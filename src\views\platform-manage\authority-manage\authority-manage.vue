<template>
  <!-- 权限管理 -->
  <ListLayout
    :has-quick-query="false"
    :has-left-panel="true"
    :aside-panel-width="asidePanelWidth"
    :aside-max-width="520"
    :has-button-group="getPermissionBtn('addAMBtn') ? true : false"
  >
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="condition">
          <el-input
            v-model="formInline.condition"
            v-trim
            v-focus
            placeholder="请输入权限名称/描述搜索"
            class="ipt-360"
            prefix-icon="el-icon-search"
            size="large"
            clearable
            @keyup.enter="onSubmit()"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="onSubmit()">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button type="primary" size="large" icon="el-icon-plus" @click="addItem" @keyup.prevent @keydown.enter.prevent
        >新增权限</el-button
      >
    </template>
    <template #page-left-side>
      <div class="tree-container">
        <div class="tree-header">
          <el-input
            v-model="filterText"
            size="small"
            placeholder="请输入类目名称"
            prefix-icon="el-icon-search"
            clearable
          />
          <el-button
            class="addTreeBtn"
            size="small"
            icon="el-icon-plus"
            @click="addTreeItem"
            @keyup.prevent
            @keydown.enter.prevent
          />
        </div>
        <div class="tree-content">
          <el-tree
            v-if="asideWidth > 80"
            ref="treeRef"
            :data="treeData"
            node-key="id"
            :props="defaultProps"
            :expand-on-click-node="false"
            highlight-current
            draggable
            :allow-drop="allowDrop"
            :filter-node-method="filterNode"
            :current-node-key="currentNodeKey"
            class="leftTree"
            @node-drop="nodeDrop"
            @filter="filterNode"
            @node-click="clickNode"
          >
            <template #default="{ node, data }">
              <span>{{ node.label }}{{ data.status === 0 ? '(已停用)' : '' }}</span>
              <el-dropdown
                v-if="data.id !== 'all'"
                trigger="hover"
                class="tree-dropdown el-icon"
                :class="node.showIcon ? 'icon-show' : ''"
                @visible-change="changeIcon(node.showIcon, node)"
              >
                <i class="el-icon-more" />
                <template v-if="getPermissionBtn('editAMBtn')" #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="editTree(node.data, node)"
                      ><i class="iconfont tes-edit" />编辑</el-dropdown-item
                    >
                    <!-- :disabled="node.data.status === 2" 删除按钮不用禁用，以红色字显示就可以 -->
                    <el-dropdown-item class="color-red" @click="delTree(node.data)"
                      ><i class="iconfont tes-delete" />删除</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-tree>
        </div>
      </div>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="auto"
      highlight-current-row
      class="dark-table base-table format-height-table no-quick-query"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column label="编号" prop="id" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="blue-color" @click="handleEdit(row)">{{ row.id || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="权限名称" prop="permissionName" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.permissionName || '--' }}
        </template>
      </el-table-column>
      <el-table-column label="描述" prop="content" :min-width="colWidth.description" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.content ? row.content : '--' }}
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" :width="colWidth.status">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="row.status == 1 ? 'success' : 'info'">{{
            row.status == 1 ? '已启用' : '已禁用'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" prop="caozuo" :width="200" fixed="right" class-name="fixed-right">
        <template #default="{ row }">
          <span v-if="getPermissionBtn('editAMBtn')" class="blue-color" @click="handleEdit(row)">资源配置</span>
          <span v-if="row.status === 0" class="blue-color" @click="handleStatus(row)">权限启用</span>
          <span
            v-if="row.status === 1 && getPermissionBtn('forbiddenAMBtn')"
            class="blue-color"
            @click="handleStatus(row)"
            >权限禁用</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getPermissionLists"
    />
    <template #other>
      <!-- 新增权限 -->
      <add
        :show="showAuthorityDialog"
        :title="addTitle"
        :data="currentData"
        :tree-ids="currentCategoryIds"
        :tree="dialogTreeData"
        @close="closeAuthorityDialog"
        @setInfo="closeAuthorityDialog"
      />
      <!-- 新增权限树弹出框 -->
      <el-dialog
        v-model="showEditDialog"
        :title="isAddTree === true ? '新增权限组' : '编辑权限组'"
        width="480px"
        :close-on-click-modal="false"
      >
        <el-form ref="formTreeRef" :model="dialogFrom" :rules="dialogRules" label-width="81px">
          <el-form-item label="分组名称：" prop="treename" style="margin-bottom: 20px">
            <el-input
              v-model.trim="dialogFrom.treename"
              autocomplete="off"
              :input="(dialogFrom.treename = dialogFrom.treename.replace(/\s+/g, ''))"
              placeholder="请输入分组名称"
            />
          </el-form-item>
          <el-form-item label="上级分组：">
            <el-cascader
              v-model="category"
              :options="dialogTreeData"
              :props="categoryProps"
              clearable
              style="width: 100%"
              @change="changeCategory"
            />
          </el-form-item>
          <el-form-item label="描述：">
            <el-input
              v-model="dialogFrom.content"
              type="textarea"
              size="small"
              rows="2"
              placeholder="请输入内容"
              maxlength="300"
            />
          </el-form-item>
          <el-form-item label="租户可见：" prop="isVisible">
            <!-- <el-switch v-model="dialogFrom.isVisible" :active-text="dialogFrom.isVisible?'可见':'不见'">可见</el-switch>  -->
            <el-radio-group v-model="dialogFrom.isVisible">
              <el-radio v-for="type in visibleOption" :key="type.name" :label="type.value">{{ type.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- <el-form-item label="状态：" prop="status">
            <el-radio-group v-model="dialogFrom.status">
              <el-radio v-for="type in statusOption" :key="type.name" :label="type.value">{{ type.name }}</el-radio>
            </el-radio-group>
          </el-form-item> -->
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="closeTreeDialog">取 消</el-button>
            <el-button type="primary" @click="editDialogSuccess">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </template>
  </ListLayout>
</template>

<script>
import { reactive, toRefs, ref, watch, onMounted, getCurrentInstance } from 'vue';
import { getPermissionBtn } from '@/utils/common';
import Pagination from '@/components/Pagination';
import Add from './add.vue';
import { formatTree, formatAllTree, formatTreeByIds, formatTreeByNames } from '@/utils/formatJson';
import {
  getPermissionTreeList,
  getPermissionList,
  enablePermission,
  updatePermissionTreeList,
  addPermissionTreeList,
  deleteTree,
  isCanDelete
} from '@/api/platform-management';
import { ElMessage, ElMessageBox } from 'element-plus';
import router from '@/router';
import ListLayout from '@/components/ListLayout';
// import { useStore } from 'vuex'
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import _ from 'lodash';

export default {
  name: 'AuthorityManage',
  components: { Pagination, Add, ListLayout },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // const store = useStore()
    // const route = useRoute()
    const otherForm = reactive({
      asidePanelWidth: 300,
      formInline: {
        condition: '',
        permissiontreeId: ''
      },
      showS: true,
      editFrom: ref(),
      listLoading: false,
      tableKey: 0,
      tableList: [],
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: null
      },
      total: 0,
      addTitle: 'add',
      showAuthorityDialog: false,
      list: [],
      content: '',
      treeData: [],
      dialogTreeData: [],
      formTreeRef: ref(),
      newTree: [],
      treeRef: ref(),
      treeTitle: '', // 选中树节点的name
      defaultProps: {
        children: 'children',
        label: 'treename'
      },
      category: [],
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'treename',
        value: 'id'
      },
      filterText: '',
      isAddTree: true,
      showEditDialog: false,
      statusOption: [
        { name: '启用', value: 1 },
        { name: '停用', value: 0 }
      ],
      visibleOption: [
        { name: '可见', value: 1 },
        { name: '不可见', value: 0 }
      ],
      currentData: null,
      dialogFrom: {
        id: '',
        treename: '',
        parentId: '0',
        content: '',
        isVisible: 1,
        status: 1
      },
      dialogRules: {
        treename: [{ required: true, message: '请输入权限组名称' }],
        parentid: [{ required: true, message: '请选择上级分组' }]
      },
      testcapability: [],
      asideWidth: 240,
      showIcon: false,
      currentNodeKey: '',
      currentCategoryIds: []
    });
    // 重置
    function reset() {
      otherForm.formInline.condition = '';
      otherForm.editFrom.resetFields();
      otherForm.listQuery.page = 1;
      otherForm.listQuery.limit = 20;
      otherForm.listQuery.orderBy = '';
      otherForm.listQuery.isAsc = false;
      proxy.getPermissionLists();
    }
    // table排序
    const sortChange = data => {
      const { prop, order } = data;
      otherForm.listQuery.orderBy = prop;
      if (order === 'ascending') {
        otherForm.listQuery.isAsc = true;
      } else if (order === 'descending') {
        otherForm.listQuery.isAsc = false;
      } else {
        otherForm.listQuery.isAsc = null;
      }
    };

    // 鼠标hover到树节点
    const mouseover = () => {
      otherForm.showIcon = true;
    };
    const mouseleave = () => {
      otherForm.showIcon = false;
    };
    // 树节点编辑
    const editTree = (data, node) => {
      otherForm.dialogTreeData = formatAllTree(data.id, otherForm.dialogTreeData);
      otherForm.showEditDialog = true;
      otherForm.isAddTree = false;
      otherForm.dialogFrom = data;
      otherForm.category = formatTreeByIds(node.parent);
    };
    // 保存树节点
    const editDialogSuccess = () => {
      otherForm.formTreeRef.validate(valid => {
        if (valid) {
          const params = {
            ...otherForm.dialogFrom,
            isPublic: 0 // 是否公共权限(0:否，1:是)
          };
          if (otherForm.isAddTree !== true) {
            updatePermissionTreeList(params).then(function (res) {
              // console.log(res)
              if (res !== false) {
                ElMessage.success('编辑成功!');
                otherForm.showEditDialog = false;
              }
              proxy.getPermissionTree();
            });
          } else {
            addPermissionTreeList(params).then(function (res) {
              // console.log(res)
              if (res !== false) {
                ElMessage.success('新增成功!');
                proxy.getPermissionTree();
                otherForm.showEditDialog = false;
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    // 关闭树的新增编辑的弹出框
    const closeTreeDialog = () => {
      otherForm.showEditDialog = false;
      otherForm.dialogFrom = {
        id: '',
        treename: '',
        parentId: '0',
        content: '',
        isVisible: 1,
        status: 1
      };
      if (otherForm.formTreeRef) {
        otherForm.formTreeRef.clearValidate();
      }
      proxy.getPermissionTree();
    };
    // 新增树节点
    const addTreeItem = () => {
      otherForm.showEditDialog = true;
      otherForm.dialogFrom = {
        id: '',
        treename: '',
        parentId: '0',
        content: '',
        isVisible: 1,
        status: 1
      };
      // console.log(formTree.value)
      if (otherForm.formTreeRef) {
        otherForm.formTreeRef.clearValidate();
      }
      otherForm.category = [];
      otherForm.isAddTree = true;
    };
    // 所属分类change
    const changeCategory = value => {
      if (value) {
        const len = value.length - 1;
        otherForm.dialogFrom.parentId = value[len];
      } else {
        otherForm.dialogFrom.parentId = '0';
      }
    };
    // 树节点删除
    const delTree = node => {
      var ids = [];
      ids.push(node.id);
      ElMessageBox({
        title: '提示',
        message: '是否删除该类目?',
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(() => {
          isCanDelete(node.id).then(res => {
            if (res.data.data) {
              deleteTree(node.id).then(function (res) {
                if (res !== false) {
                  ElMessage.success('删除成功!');
                  proxy.getPermissionTree();
                }
              });
            } else {
              ElMessage.warning('不允许删除');
            }
          });
        })
        .catch(() => {
          // ElMessage.info('已取消删除!')
        });
    };

    // 新增--打开新增权限项目弹出框
    const addItem = () => {
      otherForm.addTitle = 'add';
      otherForm.currentData = null;
      otherForm.showAuthorityDialog = true;
    };
    // 关闭-新增权限项目弹出框
    const closeAuthorityDialog = v => {
      otherForm.showAuthorityDialog = false;
      proxy.getPermissionLists();
    };
    // 编辑-操作
    const handleEdit = row => {
      router.push({
        path: '/platform-manage/authority-manage/detail',
        query: {
          id: row.id,
          permissiontreeName: row.permissiontreeName,
          permissiontreeId: row.permissiontreeId
        }
      });
    };
    // 启用禁用操作
    const handleStatus = row => {
      const statusName = row.status === 1 ? '禁用' : '启用';
      ElMessageBox({
        title: '提示',
        message: '权限' + statusName + '后，权限下资源面临不受控风险，是否继续？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(() => {
          var newStatus = row.status === 1 ? 0 : 1;
          enablePermission({ id: row.id, status: newStatus }).then(function (res) {
            if (res !== false) {
              ElMessage.success('操作成功!');
              proxy.getPermissionLists();
            }
          });
        })
        .catch(() => {
          // ElMessage.info('已取消删除!')
        });
    };

    // mounted
    onMounted(() => {});
    // 过滤树节点
    watch(
      () => otherForm.filterText,
      newValue => {
        otherForm.treeRef.filter(newValue);
      }
    );
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.treename.indexOf(value) !== -1;
    };
    // 拖拽边框
    const widthChange = m => {
      otherForm.asideWidth -= m;
      if (otherForm.asideWidth <= 80) {
        otherForm.asideWidth = 80;
      }
      if (otherForm.asideWidth >= 600) {
        otherForm.asideWidth = 600;
      }
    };

    const changeIcon = (command, node) => {
      if (!command) {
        node.showIcon = true;
      } else {
        node.showIcon = !node.showIcon;
      }
      // console.log(node.showIcon)
    };
    // 允许拖拽功能 只能同级拖拽
    const allowDrop = (draggingNode, dropNode, type) => {
      // console.log(draggingNode)
      // console.log(dropNode)
      if (draggingNode.level === dropNode.level) {
        // parentid是父节点id
        if (draggingNode.data.parentid === dropNode.data.parentid) {
          return type === 'prev' || type === 'next';
        }
      } else {
        // 不同级进行处理
        return false;
      }
    };
    // 树排序
    const nodeDrop = (before, after) => {
      // console.log(after)
      var orderList = [];
      if (after.parent.level === 0) {
        orderList = JSON.parse(JSON.stringify(after.parent.data));
        _.remove(orderList, function (n) {
          return n.id === 'all';
        });
        orderList.forEach((ol, index) => {
          ol.order = index + 1;
        });
      } else {
        orderList = after.parent.data.children;
        orderList.forEach((ol2, index) => {
          ol2.order = index + 1;
        });
      }
      // console.log(orderList)
      // updateOrderCategory(orderList).then(res => {
      //   if (res !== false) {
      //     ElMessage.success('排序成功')
      //   }
      // })
    };

    return {
      ...toRefs(otherForm),
      getPermissionBtn,
      closeTreeDialog,
      drageHeader,
      changeIcon,
      mouseleave,
      mouseover,
      widthChange,
      changeCategory,
      addTreeItem,
      filterNode,
      editDialogSuccess,
      handleStatus,
      closeAuthorityDialog,
      delTree,
      editTree,
      addItem,
      sortChange,
      reset,
      handleEdit,
      allowDrop,
      nodeDrop,
      colWidth
    };
  },
  async created() {
    await this.getPermissionTree();
    this.getPermissionLists();
    // 刷新列表
    this.bus.$on('reloadAuthorityManagementList', msg => {
      this.getPermissionLists();
    });
  },
  methods: {
    // 获取权限列表
    getPermissionLists(pdata) {
      // console.log(pdata)
      const _this = this;
      _this.listLoading = true;
      if (pdata && pdata !== undefined) {
        _this.listQuery.page = pdata.page;
        _this.listQuery.limit = pdata.limit;
      }
      const param = Object.assign(_this.formInline, _this.listQuery);
      // console.log(param)
      param.page = param.page + '';
      param.limit = param.limit + '';
      getPermissionList(param).then(response => {
        if (response !== false) {
          const data = response.data.data;
          _this.list = data.list;
          _this.total = data.totalCount;
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
      //   }
      // })
    },
    // 获取权限树接口
    getPermissionTree() {
      const _this = this;
      return new Promise(resolve => {
        getPermissionTreeList({ typeId: 0 }).then(response1 => {
          // console.log(response1)
          if (response1 !== false) {
            const data = response1.data.data;
            _this.treeData = formatTree(data);
            _this.newTree = formatTree(data);
            _this.dialogTreeData = JSON.parse(JSON.stringify(data));
            if (_this.treeData.length === 0) {
              _this.listLoading = false;
              _this.treeTitle = '';
              _this.list = [];
              _this.total = 0;
              return false;
            }
            // 在_this.otherForm.treeData树列表第一个部分添加‘全部’的根目录
            const allParam = {
              id: 'all',
              treename: '全部',
              parentId: '0'
            };
            _this.treeData.unshift(allParam);
            if (_this.formInline.categoryId === '' && _this.treeData[0].id === 'all') {
              _this.formInline.categoryId = '';
              _this.treeTitle = _this.treeData[0].name;
            }
            // 默认选中树节点
            if (_this.$route.query.permissiontreeId) {
              _this.currentNodeKey = _this.$route.query.permissiontreeId;
              _this.formInline.permissiontreeId = _this.currentNodeKey;
            }
            if (_this.currentNodeKey === '') {
              _this.currentNodeKey = _this.treeData[0].id;
              _this.currentCategoryIds = [_this.treeData[0].id];
              _this.treeTitle = _this.treeData[0].name;
              _this.$nextTick(function () {
                _this.$refs.treeRef.setCurrentKey(_this.currentNodeKey);
              });
            } else {
              _this.$nextTick(function () {
                _this.$refs.treeRef.setCurrentKey(_this.currentNodeKey);
              });
            }
            resolve(true);
          } else {
            resolve(false);
          }
        });
      });
    },
    onSubmit() {
      this.getPermissionLists();
    },
    clickNode(data, node) {
      if (data.id === 'all') {
        this.formInline.permissiontreeId = '';
        this.currentNodeKey = data.id;
      } else {
        this.formInline.permissiontreeId = data.id;
        this.currentNodeKey = data.id;
      }
      router.replace({ query: {} });
      this.treeTitle = formatTreeByNames(node)
        .filter(item => {
          return item;
        })
        .reverse()
        .join('/');
      this.currentCategoryIds = formatTreeByIds(node);
      this.getPermissionLists();
    }
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
</style>
