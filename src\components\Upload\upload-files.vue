<template>
  <div class="upload-files">
    <input ref="uploadFileRef" class="upload-files-input" multiple type="file" @change="handleClick" />
    <div v-if="!disabled" class="file-btn">
      <el-button size="small" icon="el-icon-upload" @click="uploadFiles" @keyup.prevent @keydown.enter.prevent>{{
        name
      }}</el-button>
      <div class="text">{{ tip }}</div>
    </div>
    <div class="file-list">
      <el-tag
        v-for="(file, index) in fileList"
        :key="index"
        :closable="!disabled"
        type="info"
        @close="handleClose(file, index)"
      >
        <a :href="file.url" :download="file.name">{{ file.name }}</a>
      </el-tag>
    </div>
  </div>
</template>

<script>
import { ref, reactive, toRefs, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { deleteFileById } from '@/api/attachment';

export default {
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    name: {
      type: String,
      default: '点击上传'
    },
    tip: {
      type: String,
      default: '文件大小不超过50M'
    },
    fileList: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['upload', 'deleteFile'],
  setup(props, context) {
    const datas = reactive({
      uploadFileRef: ref(),
      loading: false,
      currentFileList: []
    });

    watch(
      () => props.disabled,
      newValue => {
        console.log(newValue);
      }
    );
    // 点击按钮触发点击事件
    const uploadFiles = () => {
      datas.uploadFileRef.click();
    };
    // 选择文件
    const handleClick = e => {
      const files = e.target.files;
      const params = new FormData();
      const flag = beforeUpload(files);
      if (!flag) {
        return false;
      }
      datas.currentFileList = files;
      [...files].forEach(async file => {
        params.append('fileList', file);
      });
      e.target.value = '';
      upload(params);
    };
    // 上传
    const upload = filesData => {
      context.emit('upload', filesData);
    };
    // 过滤提示
    const beforeUpload = files => {
      var flag = 0;
      [...files].forEach(file => {
        const fileSize = file.size / 1024 / 1024 < 50;
        if (!fileSize) {
          ElMessage.error('上传附件大小不能超过50M');
          flag += 1;
        }
      });
      return flag === 0;
    };

    const handleClose = (file, index) => {
      // 调用接口删除文件
      deleteFileById(file.id).then(res => {
        if (res && res.data.code === 200) {
          context.emit('deleteFile', index);
        }
      });
    };

    return {
      ...toRefs(datas),
      beforeUpload,
      uploadFiles,
      handleClick,
      upload,
      handleClose
    };
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
.upload-files {
  .upload-files-input {
    display: none;
    z-index: -9999;
  }
  .file-btn {
    display: flex;
    align-items: center;
    .el-button {
      height: 32px;
    }
    .text {
      margin-left: 10px;
      font-size: 12px;
      color: $tes-font2;
    }
  }
  .file-list {
    display: flex;
    align-items: center;
    justify-content: left;
    flex-wrap: wrap;
    .file-item {
      margin-right: 10px;
      margin-bottom: 10px;
      &:hover {
        background: #ffffff;
        color: $tes-primary;
      }
    }
  }
}
.drop {
  border: 2px dashed #bbb;
  width: 600px;
  height: 160px;
  line-height: 160px;
  margin: 0 auto;
  font-size: 24px;
  border-radius: 5px;
  text-align: center;
  color: #bbb;
  position: relative;
}
</style>
