import { colWidth } from '@/data/tableStyle';

export function getWidth(isMinWidth, width) {
  return isMinWidth ? '' : getColWidth(width);
}

export function getMinWidth(isMinWidth, width) {
  return isMinWidth ? getColWidth(width) : '';
}

/**
 * 判断存储的colWidth是colWidth中的属性名还是数值，返回最终width数值
 * @param {string} width
 * @returns
 */
export function getColWidth(width) {
  if (isNaN(Number(width))) {
    return isNaN(Number(colWidth[width])) ? '' : colWidth[width];
  } else {
    return Number(width);
  }
}

export function formatStatus() {}

/**
 * 解析表格样式JSON字符串
 * @param {*} content
 * @returns
 */
export function parseStyleContent(content) {
  let result = { colWidth: 200 };
  try {
    result = JSON.parse(content);
  } catch {
    result = {
      colWidth: 200,
      styleType: 0,
      styleContent: {},
      isOrder: 0
    };
  }
  return result;
}

export const viewFormRules = {
  bindingMenu: [
    { required: true, message: '请输入表格编码', trigger: 'blur' },
    { required: true, message: '请输入表格编码', trigger: 'change' },
    { min: 1, max: 50, message: '表格编码长度应该在1~50个字符!', trigger: 'blur' }
  ],
  bindingMenuName: [{ required: true, message: '请选择所属资源', trigger: 'blur' }],
  viewName: [
    { required: true, message: '请输入视图名称', trigger: 'blur' },
    { required: true, message: '请输入视图名称', trigger: 'change' },
    { min: 1, max: 20, message: '视图名称长度应该在1~20个字符!', trigger: 'blur' }
  ]
};
