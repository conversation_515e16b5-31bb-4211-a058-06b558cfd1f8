import { createApp } from 'vue';
import App from './App.vue';
import Cookies from 'js-cookie';

import 'normalize.css/normalize.css';
// import config from '@/utils/config';
import router from './router';
import store from './store';
import ElementPlus from 'element-plus';
// import 'element-plus/dist/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn';
// import '@/styles/icon/iconfont.css'
import '@/styles/index.scss';
import './permission';
import './utils/error-log';
import Bus from './bus.js';

import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import defineDirectives from '@/directive/index';
import VueSignaturePad from 'vue-signature-pad';
import VueUploadComponent from 'vue-upload-component';

// import 'default-passive-events'
import WS from '@/utils/websocket';

// 连接websocket
WS.createWS();

// 简单配置
NProgress.inc(0.2);
NProgress.configure({ easing: 'ease', speed: 500, showSpinner: false });

// router.beforeEach((to, from, next) => {
//   console.log('to,from,next')
//   console.log(to)
//   console.log(from)
//   console.log(next)
//   NProgress.start()
//   next()
// })

// router.afterEach(() => {
//   NProgress.done()
// })

// 设置rem
import { setRem1 } from './utils/setRem';
import resizeManager from './utils/resizeManager';

setRem1();

// 使用统一的resize管理器，避免多个resize监听器冲突
resizeManager.register(
  'setRem',
  () => {
    setRem1();
  },
  {
    priority: 10, // 高优先级
    minInterval: 300 // 最小执行间隔300ms
  }
);

// 设置ClientId
import { ClientId, setClientId } from '@/utils/auth';
setClientId(ClientId);

import _ from 'lodash';
// import { createErrorBoundaryPlugin } from '@/utils/errorBoundary';

const app = createApp(App);

// 安装错误边界插件
// app.use(createErrorBoundaryPlugin());

// 启用页面保护器（开发环境自动启用）
if (process.env.NODE_ENV === 'development') {
  console.log('Page protector enabled for development');
}

app.config.globalProperties.bus = Bus;
app.config.globalProperties.websocket = WS.websocket;
app.config.globalProperties.getWebSocketInstance = WS.getWebSocketInstance;
// app.config.globalProperties._ = _
app.provide('_', _);
app.provide('$mittBus', Bus);
app.provide('$wsWebsocket', WS.websocket);
app.provide('$getWebSocketInstance', WS.getWebSocketInstance);
// console.log(process.env)
app.use(ElementPlus, {
  size: Cookies.get('size') || 'medium',
  locale: zhCn
});
defineDirectives(app);
// 复制
import VueClipboard from 'vue-clipboard2';
app.use(VueClipboard);
// 打印
import print from 'vue3-print-nb';
app.use(print);
app.use(store);
app.use(router);
app.use(VueSignaturePad);
app.component('DragFileUpload', VueUploadComponent);
app.mount('#app');
