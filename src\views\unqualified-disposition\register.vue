<template>
  <!-- 不良品处置详情 -->
  <DetailLayout :main-offset-top="104">
    <template #page-header>
      <div class="header-flex flex-between">
        <el-space :size="40" :spacer="spacer">
          <div class="item-column">
            <span class="item-label">处置单号</span>
            <div class="item-content">{{ sampleInfoDetail.disposalNumber || '--' }}</div>
          </div>
          <div class="item-column">
            <span class="item-label">报告编号</span>
            <div class="item-content">
              {{ sampleInfoDetail.reportNo || '--' }}
            </div>
          </div>
          <div class="item-column">
            <span class="item-label">状态</span>
            <div class="item-content">
              <span :class="fontColor[sampleInfoDetail.status]">{{ filterStatus(sampleInfoDetail.status)[1] }}</span>
            </div>
          </div>
        </el-space>
        <div v-if="!showSampleInfo" class="add-item-btn">
          <el-button v-if="getPermissionBtn('addUnqualifiedBtn')" type="primary" icon="el-icon-plus" @click="addItem"
            >添加不合格品</el-button
          >
        </div>
      </div>
    </template>
    <div class="register">
      <div class="titleHeader">不合格品登记</div>
      <div class="register-form-info info-main djxx" :class="{ isCheck: showDetail }">
        <div class="register-title">登记信息</div>
        <el-form
          ref="registerFromInfoRef"
          :inline="true"
          label-position="right"
          label-width="10rem"
          :model="formInline"
          :rules="registerFromInfoRule"
        >
          <el-row>
            <el-col :span="8" class="marginB0">
              <el-form-item label="登记日期：">
                <div v-if="showDetail">
                  {{ sampleInfoDetail.createdatetime || sampleInfoDetail.createDateTime || formatDate(new Date()) }}
                </div>
                <div v-else>{{ formatDate(new Date()) }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="marginB0">
              <el-form-item label="登记人：">
                <UserTag
                  v-if="showDetail"
                  :name="
                    getNameByid(sampleInfoDetail.createbyuserid) ||
                    getNameByid(sampleInfoDetail.createByUserId) ||
                    getNameByid(currentAccountId) ||
                    ''
                  "
                />
                <UserTag v-else :name="getNameByid(currentAccountId) || currentAccountId || '--'" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="登记地点："
                prop="address"
                :rules="{ required: !showDetail, message: '请输入登记地点', trigger: 'change' }"
              >
                <el-input
                  v-if="!showDetail"
                  v-model="formInline.address"
                  v-trim
                  v-focus
                  maxlength="100"
                  placeholder="请输入登记地点"
                  clearable
                />
                <div v-if="showDetail">{{ sampleInfoDetail.enrollmentLocation }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="责任单位："
                prop="address1"
                :rules="{ required: !showDetail, message: '请输入责任单位', trigger: 'change' }"
              >
                <el-input
                  v-if="!showDetail"
                  v-model="formInline.address1"
                  maxlength="100"
                  placeholder="请输入责任单位"
                  clearable
                />
                <div v-if="showDetail">{{ sampleInfoDetail.responsibleUnit }}</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-divider />
      </div>
      <!-- 样品信息 -->
      <register-sample-info
        v-if="showSampleInfo"
        ref="registerSIRef"
        :page-view="pageViewGroup"
        :data="sampleInfoDetail"
        @close="closeSampleInfo"
        @setData="getSampleData"
      />
      <!-- 添加不良品登记弹出框 -->
    </div>
    <add-item :show="showAddUnqulified" @close="closeAddUnqulified" @selectData="selectData" />
    <cause-analysis
      v-if="!sampleInfoDetail.disposalownerid && routeId != 0 && isResh"
      :sample-info-detail="sampleInfoDetail"
      :reason-array="reasonArray"
      @fresh="handleFresh"
    />
    <disposal-trace
      v-if="!sampleInfoDetail.disposalownerid && routeId != 0 && isResh"
      :sample-info-detail="sampleInfoDetail"
      :reason-array="reasonArray"
      @fresh="handleFresh"
    />
  </DetailLayout>
</template>

<script>
import { reactive, toRefs, ref, h, getCurrentInstance } from 'vue';
import router from '@/router/index.js';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import CauseAnalysis from './cause-analysis.vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { getLoginInfo } from '@/utils/auth';
import { ElDivider } from 'element-plus';
import { saveDefectiveProduct, getInfoByDisposalNumber, getHistoryList } from '@/api/unqualifiedDisposition';
import DetailLayout from '@/components/DetailLayout';
import UserTag from '@/components/UserTag';
// import _ from 'lodash'
import AddItem from './add-item.vue';
import RegisterSampleInfo from './sample-info';
import DisposalTrace from './disposal-trace';
import { getViewByBindingMenu } from '@/api/tableView';
import { formatViewData } from '@/utils/formatJson';

export default {
  name: 'Register',
  components: { AddItem, RegisterSampleInfo, CauseAnalysis, DisposalTrace, DetailLayout, UserTag },
  setup() {
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    const spacer = h(ElDivider, { direction: 'vertical' });
    const store = useStore().state;
    const datas = reactive({
      radioData: '全部',
      fontColor: {
        1: 'grey',
        2: 'yellow',
        3: 'blue',
        4: 'green'
      },
      reasonArray: [],
      currentAccountId: '',
      userOptions: store.common.nameList,
      currentTitle: route.params.title,
      showDetail: route.params.title === 'detail',
      pageViewGroup: {
        '1-sampleInfo': {},
        '2-sampleInfo': {},
        '3-sampleInfo': {},
        unqualifiedItems: {}
      },
      isResh: false,
      routeId: route.params.id,
      formInline: {
        param: '',
        address: '',
        address1: '',
        status: 3
      },
      sampleInfoDetail: {
        expList: [],
        imageList: []
      },
      registerFromInfoRef: ref(),
      registerFromInfoRule: {},
      registerSIRef: ref(),
      showAddUnqulified: false,
      showSampleInfo: false
    });
    if (getLoginInfo()) {
      datas.currentAccountId = getLoginInfo().accountId;
    }
    const getDetailView = async () => {
      const res = await getViewByBindingMenu('register');
      if (res) {
        datas.pageViewGroup = formatViewData(res.data.data[0].sysEmployeeListConfigList);
      }
    };
    getDetailView();
    const getHistoryDetail = processInstanceId => {
      datas.isResh = false;
      if (processInstanceId) {
        getHistoryList(processInstanceId).then(res => {
          datas.isResh = true;
          if (res.data.code === 200) {
            datas.reasonArray = [];
            res.data.data.forEach((item, index) => {
              datas.reasonArray.push({
                ...item[0],
                approvalList: item.filter(val => {
                  return val.name !== '原因分析，设置处置方案';
                })
              });
            });
          }
        });
      }
    };
    // 根据id查询处置单信息
    const getBaseInfo = () => {
      if (route.params.id !== '0') {
        getInfoByDisposalNumber(route.params.id).then(res => {
          if (res !== false) {
            datas.sampleInfoDetail = res.data.data.defectiveProductEntity;
            datas.sampleInfoDetail.expList = res.data.data.expList;
            datas.sampleInfoDetail.imageList = res.data.data.imageList;
            if (route.params.title === 'detail') {
              datas.showSampleInfo = true;
              getHistoryDetail(datas.sampleInfoDetail.processInstanceId);
            }
          }
        });
      } else if (route.params.title === 'detail') {
        datas.showSampleInfo = true;
        if (localStorage.getItem('sampleInfoDetailBLP')) {
          datas.sampleInfoDetail = JSON.parse(localStorage.getItem('sampleInfoDetailBLP'));
        }
      }
    };
    const handleFresh = () => {
      getBaseInfo();
    };
    getBaseInfo();
    // 过滤状态
    const filterStatus = status => {
      if (!status) {
        status = 1;
      }
      const param = {
        1: ['', '待分析'],
        2: ['', '待审批'],
        3: ['', '待验收'],
        4: ['success', '已完成']
      };
      return param[status];
    };
    // 添加不及格品
    const addItem = () => {
      datas.showAddUnqulified = true;
    };
    // 关闭添加不良品弹出框
    const closeAddUnqulified = () => {
      datas.showAddUnqulified = false;
    };
    // 添加不良品弹出框---选择样品
    const selectData = data => {
      datas.showSampleInfo = true;
      datas.sampleInfoDetail = data;
      datas.sampleInfoDetail.status = 1;
    };
    // 获取样品所有信息-包括 样品信息、不合格项目、图片、描述、责任人--------提交模块
    const getSampleData = data => {
      datas.sampleInfoDetail = data;
      if (route.params.title === 'add') {
        datas.sampleInfoDetail.processInstanceId = '';
        datas.sampleInfoDetail.processStartUserId = '';
        datas.sampleInfoDetail.enrollmentLocation = datas.formInline.address;
        datas.sampleInfoDetail.responsibleUnit = datas.formInline.address1;
      }
      if (!datas.sampleInfoDetail.enrollmentLocation && route.params.title === 'add') {
        datas.registerFromInfoRef.validate();
        ElMessage.warning('请输入登记地点！');
        return false;
      } else if (!datas.sampleInfoDetail.responsibleUnit && route.params.title === 'add') {
        datas.registerFromInfoRef.validate();
        ElMessage.warning('请输入责任单位！');
        return false;
      } else {
        if (proxy.$refs['registerSIRef'].onSubmit()) {
          proxy.$refs['registerSIRef']
            .onSubmit()
            .then(valid => {
              if (valid) {
                submitForm();
              } else {
                return false;
              }
            })
            .catch(res => {
              proxy.$message.closeAll();
              proxy.$message.error('请先按要求填写');
              return false;
            });
        } else {
          submitForm();
        }
      }
    };
    const submitForm = () => {
      if (datas.registerSIRef.$refs.descriptionRef && !datas.sampleInfoDetail.description) {
        // datas.registerSIRef.$refs.descriptionRef.$refs.reference.$refs.input.style = 'border: 1px solid #f56c6c;border-radius:8px;'
        ElMessage.warning('请输入不合格现象描述！');
        return false;
      } else if (!datas.sampleInfoDetail.disposalOwnerId) {
        datas.registerSIRef.$refs.disposalOwnerRef.$refs.reference.$refs.input.style =
          'border: 1px solid #f56c6c;border-radius:8px;';
        ElMessage.warning('请选择原因分析负责人！');
        return false;
      } else {
        ElMessageBox({
          title: '提交确认',
          message: '是否确认提交不合格登记？提交后不可修改',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: true,
          type: 'warning'
        }).then(() => {
          localStorage.setItem('sampleInfoDetailBLP', JSON.stringify(datas.sampleInfoDetail));
          const loading = ElLoading.service({
            lock: true,
            text: '提交中，请稍后...',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          saveDefectiveProduct(datas.sampleInfoDetail).then(res => {
            if (res !== false) {
              router.push({
                params: { ...route.params, title: 'detail', id: res.data.data.disposalNumber }
              });
              loading.close();
              ElMessage.success('提交成功');
            } else {
              loading.close();
              ElMessage.error('提交失败!');
            }
          });
        });
      }
    };
    // 取消样品信息新增内容
    const closeSampleInfo = data => {
      datas.showSampleInfo = false;
      datas.sampleInfoDetail = {
        expList: [],
        imageList: []
      };
      router.push({
        params: { ...route.params, title: 'add' }
      });
    };

    return {
      ...toRefs(datas),
      formatDate,
      spacer,
      getNameByid,
      filterStatus,
      addItem,
      closeAddUnqulified,
      selectData,
      getSampleData,
      closeSampleInfo,
      getHistoryDetail,
      handleFresh,
      getPermissionBtn
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
@import './common.scss';
:deep(.el-divider--vertical) {
  height: 40px;
}

.add-item-btn {
  float: right;
}
.blue {
  color: $tes-blue;
}
.green {
  color: $tes-green;
}
.yellow {
  color: $tes-yellow;
}
.grey {
  color: $tes-grey;
}
.textR {
  text-align: right;
}
.goback {
  margin: 0 0 10px 0;
}
.djxx {
  :deep(.el-divider--horizontal) {
    margin: 14px 0;
  }
}
.register {
  background: #fff;
  .register-header {
    height: 40px;
    .title {
      font-size: 16px;
      font-weight: bold;
      height: 32px;
      line-height: 32px;
      text-align: left;
    }
    .name {
      font-size: 12px;
      height: 25px;
      line-height: 25px;
      width: 70%;
      text-align: left;
      color: #909399;
    }
    .el-button {
      position: absolute;
      right: 0px;
    }
  }
  .register-form-info {
    .el-form-item {
      margin-right: 0px;
      // padding-left: 13px;
      color: #909399;
      text-align: left;
    }
    // .is-required {
    //   padding-left: 10px;
    // }
  }
  :deep(.el-form-item__error) {
    padding-top: 0px !important;
  }
}
</style>
