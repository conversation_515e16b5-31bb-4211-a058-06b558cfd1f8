<template>
  <div class="msg-contant" @click="openMsgBox">
    <i class="el-icon-d-arrow-left" />
    <el-drawer
      v-model="showDrawer"
      title="消息"
      direction="rtl"
      :before-close="handleClose"
      size="40%"
      destroy-on-close
      :close-on-click-modal="false"
      custom-class="msg-drawer"
    >
      <div v-for="(item, index) in messageStrList" :key="index" class="msg-box">
        <div class="time">{{ item.time }}</div>
        <div class="msg">{{ item.msg }}</div>
      </div>
      <el-empty v-if="messageStrList.length === 0" :image="emptyImg" description="暂无消息" />
      <!-- <div class="drawer-fotter">
        <el-button @click="handleClose">取消</el-button>
      </div> -->
    </el-drawer>
  </div>
</template>

<script>
import { reactive, toRefs } from 'vue';
// import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDateTime } from '@/utils/formatTime';
import { mapGetters } from 'vuex';
import { setMsgList, getMsgList } from '@/utils/auth';
import emptyImg from '@/assets/img/empty-data.png';

export default {
  name: 'Message',
  props: {
    list: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    const datas = reactive({
      showDrawer: false,
      messageStrList: []
    });

    if (getMsgList().length > 0) {
      datas.messageStrList = getMsgList();
    }

    // 关闭抽屉
    const handleClose = () => {
      datas.showDrawer = false;
    };
    // 是否打开抽屉
    const openMsgBox = () => {
      datas.showDrawer = !datas.showDrawer;
      console.log(datas.showDrawer);
    };

    return { ...toRefs(datas), emptyImg, handleClose, openMsgBox };
  },
  computed: {
    ...mapGetters(['webSocketMsg'])
  },
  watch: {
    webSocketMsg(msg) {
      if (getMsgList().length >= 100) {
        this.messageStrList = this.messageStrList.slice(1);
      }
      this.messageStrList.push({ time: formatDateTime(), msg: msg });
      setMsgList(this.messageStrList);
    }
  }
};
</script>

<style lang="scss" scoped>
.msg-contant {
  display: none;
  position: fixed;
  right: 0;
  top: 60px;
  width: 20px;
  height: 20px;
  cursor: pointer;
  z-index: 999;
  :deep(.msg-drawer) {
    .el-drawer__body {
      overflow: auto;
    }
  }
  .msg-box {
    display: flex;
    // justify-content: space-between;
    flex-wrap: wrap;
    margin: 0 20px;
    border-bottom: 1px solid $tes-border;
    .time {
      width: 100%;
      margin-right: 15px;
      color: #999999;
      text-align: left;
    }
    .msg {
      text-align: left;
    }
  }
}
.drawer-fotter {
  position: absolute;
  bottom: 0;
  width: 100%;
  margin: 10px 0px 20px 0px;
  //  border-top: 1px solid #e4e7ed;
  height: 60px;
  line-height: 100px;
  text-align: right;
  padding-right: 20px;
}
</style>
