<template>
  <el-dialog v-model="dialogShow" title="批量判定" width="500px" :close-on-click-modal="false">
    <el-form ref="ruleForm" :model="formData" label-position="top" label-width="110px" custom-class="submit-dialog">
      <el-form-item
        label="判定结论："
        prop="result"
        :rules="{
          required: true,
          message: '判定结论',
          trigger: 'change'
        }"
      >
        <el-radio-group v-model="formData.result" class="result-group" text-color="#ffffff" style="width: 100%">
          <div class="flex-group">
            <div class="flex-content">
              <el-radio class="success-radio" :label="0" border>合格</el-radio>
            </div>
          </div>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="样品进度："
        prop="result"
        :rules="{
          required: true,
          message: '请选择样品进度',
          trigger: 'change'
        }"
      >
        <el-radio-group v-model="formData.progress" class="result-group" text-color="#ffffff" style="width: 100%">
          <div class="flex-group">
            <div class="flex-content">
              <el-radio class="success-radio" :label="0" border>继续检测</el-radio>
            </div>
            <div class="flex-space" />
            <div class="flex-content">
              <el-radio class="warning-radio" :label="1" border>完成检测</el-radio>
            </div>
          </div>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.progress === 1" label="">
        <CustomPanel
          :has-margin-bottom="true"
          :panel-margin-width="0.8"
          :has-panel-header="false"
          panel-min-height="80px"
          class="tip-panel"
          style="background-color: rgb(237, 250, 255)"
        >
          <div class="tip-message">
            <el-row>
              <el-col :span="2" style="text-align: center"
                ><i class="el-icon-warning" style="color: #409eff; line-height: 2.571429rem; font-size: 1.2rem"
              /></el-col>
              <el-col :span="22"><span>提示信息</span></el-col>
            </el-row>
            <el-row>
              <el-col :span="2" />
              <el-col :span="22">
                <p class="tip-text">完成检测后，样品下的所有已分配项目将自动结束。</p>
                <p class="tip-text">如已经开始试验，则不允许结束检测!</p>
              </el-col>
            </el-row>
          </div>
        </CustomPanel>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancelDialog">取消</el-button>
      <el-button type="primary" @click="submitJudgement">确定</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { computed, reactive, watch } from 'vue';
import CustomPanel from '@/components/PageComponents/CustomPanel.vue';
export default {
  name: 'BatchJudgement',
  components: { CustomPanel },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['closeDialog', 'saveJudgement'],
  setup(props, context) {
    const formData = reactive({
      result: 0, // 0 合格， 1 不合格， 2 不判定
      progress: 0
    });
    const dialogShow = computed({
      get: () => props.dialogVisible,
      set: val => context.emit('closeDialog', val)
    });

    const cancelDialog = () => {
      context.emit('closeDialog', false);
    };

    const submitJudgement = () => {
      context.emit('saveJudgement', formData);
      context.emit('closeDialog', false);
    };

    watch(
      () => props.dialogVisible,
      newValue => {
        if (newValue) {
          formData.progress = 0;
        }
      }
    );

    return {
      formData,
      dialogShow,
      cancelDialog,
      submitJudgement
    };
  }
};
</script>
<style lang="scss" scoped>
.result-group {
  .warning-radio,
  .success-radio {
    padding: 0px;
    height: 3rem;
    line-height: 3rem;
    font-weight: 540;
    width: 100%;
    text-align: center;
    :deep(.el-radio__input) {
      display: none;
    }
  }

  .warning-radio.is-checked,
  .success-radio.is-checked {
    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #fff;
    }
  }

  .warning-radio.is-checked {
    background-color: $tes-red;
    border-color: $tes-red !important;
    box-shadow: $tes-red -1px 0px 0px 0px;
  }

  .success-radio.is-checked {
    background-color: $green;
    border-color: $green !important;
    box-shadow: $green -1px 0px 0px 0px;
  }
}
.tip-panel {
  border: 1px solid $tes-primary;
  border-radius: 10px;
  // box-shadow: 0px 0px 4px rgb(243, 73, 73);
}
.tip-panel:hover {
  box-shadow: 1px 1px 2px 1px $tes-primary4;
}

.tip-message {
  padding: 5px 10px 10px 10px;
}

.tip-text {
  margin: 0px;
  line-height: 1.25rem;
  color: $tes-font2;
}

.flex-group {
  display: flex;
  flex-direction: row;
  .flex-space {
    width: 15px;
  }
  .flex-content {
    flex: auto;
  }
}
</style>
