<template>
  <el-dialog
    v-model="dialogVisible"
    :title="ocrType === 'offline' ? '在线版OCR识别' : 'OCR识别'"
    width="900px"
    :close-on-click-modal="false"
    custom-class="imgDialog"
    @close="closeDialog"
  >
    <el-upload
      ref="uploadImg"
      class="upload-demo"
      :action="imgAction"
      :accept="'.jpg, .png'"
      multiple
      :headers="headerconfig"
      :file-list="fileList"
      :auto-upload="false"
      :show-file-list="false"
      :on-change="handleChange"
      :before-upload="beforeUpload"
    >
      <el-button
        type="primary"
        :loading="imgLoading"
        icon="el-icon-upload2"
        size="small"
        @keyup.prevent
        @keydown.enter.prevent
        >选择文件</el-button
      >
      <template #tip>
        <span class="uploadTitle el-upload__tip">只能上传 jpg/png 文件，且不超过 20M</span>
      </template>
    </el-upload>
    <el-table
      v-if="dialogVisible"
      v-loading="imgLoading"
      :data="tablePicture"
      fit
      border
      class="imgTable dark-table"
      @header-dragend="drageHeader"
    >
      <el-table-column prop="address" label="图片" :min-width="120">
        <template #default="{ row }">
          <el-image
            v-if="row.id"
            class="curson"
            style="display: flex; max-width: 120px; max-height: 40px"
            :src="row.imageUrl"
            fit="cover"
            @click="row.showViewer = true"
          >
            <template #placeholder>
              <div class="demo-image__error">
                <div class="image-slot">
                  <span class="loading">加载中...&nbsp;&nbsp;&nbsp;</span><i class="el-icon-picture-outline" />
                </div>
              </div>
            </template>
          </el-image>
          <el-image-viewer
            v-if="row.imageUrl && row.showViewer"
            :src="row.imageUrl"
            :url-list="[row.imageUrl]"
            @close="
              () => {
                row.showViewer = false;
              }
            "
          />
          <el-image
            v-if="row.imgUrl"
            class="curson"
            style="display: flex; max-width: 120px; max-height: 40px"
            :src="row.imgUrl"
            fit="cover"
            @click="row.showViewer = true"
          />
          <el-image-viewer
            v-if="row.imgUrl && row.showViewer"
            :src="row.imgUrl"
            :url-list="[row.imgUrl]"
            @close="
              () => {
                row.showViewer = false;
              }
            "
          />
        </template>
      </el-table-column>
      <el-table-column prop="date" label="识别结果" :min-width="200">
        <template #default="{ row }">
          <el-input v-model="row.ocrResult" type="text" clearable placeholder="识别结果" />
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="imgLoading" @click="closeDialog">取 消</el-button>
        <el-button :loading="imgLoading" type="primary" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >保存至粘贴板</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, getCurrentInstance, ref, watch } from 'vue';
import { getToken } from '@/utils/auth';
import { drageHeader } from '@/utils/formatTable';
import Tesseract from 'tesseract.js';
import { experimentAttachmentUploadUrl } from '@/api/uploadAction';
export default {
  name: 'DialogOCR',
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tablePicture: [],
      ocrType: '',
      imgLoading: false,
      headerconfig: {
        Authorization: getToken()
      },
      isShowBtn: true, // 是否显示确认按钮
      imgAction: experimentAttachmentUploadUrl(),
      fileList: [],
      dialogVisible: false,
      uploadImg: ref()
    });
    watch(props, newValue => {
      state.dialogVisible = newValue.dialogShow;
      if (state.dialogVisible) {
        state.tablePicture = [];
        state.ocrType = newValue.type;
      }
    });
    // 添加文件、解析图片实现在线预览
    const handleChange = (file, fileList) => {
      if (file.status === 'ready') {
        const isCanUpload = beforeUpload(file);
        if (isCanUpload) {
          state.imgLoading = true;
          const reader = new FileReader();
          reader.readAsDataURL(file.raw);
          reader.onload = function (e) {
            // 将bade64位图片保存至数组里供上面图片显示
            OcrRecognize(e, file);
          };
        }
      }
    };
    const OcrRecognize = (e, file) => {
      Tesseract.recognize(e.target.result, 'chi_sim').then(d => {
        const result = d.data.text.replaceAll(' ', '').replaceAll('\n', ';').replaceAll('|', ' ');
        if (result) {
          state.imgLoading = false;
          state.tablePicture.unshift({
            name: file.name.split('.')[0],
            imgUrl: e.target.result,
            file: [file],
            ocrResult: result
          });
        }
      });
    };
    // 图片上传之前的钩子 判断图片格式和大小
    const beforeUpload = file => {
      const fileSize = file.size / 1024 / 1024 < 20;
      var fileName = '';
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过20M');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传图片大小不能为空');
        return false;
      } else if (fileName !== 'jpg' && fileName !== 'png') {
        proxy.$message.error('只能上传jpg/png格式图片');
        return false;
      } else {
        return true;
      }
    };
    const onSubmit = () => {
      let ocrData = [];
      let newTemplateValue = [];
      state.tablePicture.forEach(item => {
        ocrData = [...new Set(ocrData), ...new Set(item.ocrResult.split(';'))];
      });
      if (localStorage.getItem('templateValue') && localStorage.getItem('templateValue') !== '[]') {
        newTemplateValue = [
          ...ocrData.filter(item => {
            return item;
          }),
          ...JSON.parse(localStorage.getItem('templateValue'))
        ];
      } else {
        newTemplateValue = [
          ...ocrData.filter(item => {
            return item;
          })
        ];
      }
      localStorage.setItem('templateValue', JSON.stringify(newTemplateValue));
      proxy.$message.success('保存至粘贴板');
      context.emit('closeDialog', true);
    };
    const closeDialog = () => {
      state.dialogVisible = false;
      context.emit('closeDialog', false);
    };
    // 清除缓存的附件，防止上传失败，二次上传不生效
    const clearFiles = (file, index) => {
      const files = JSON.parse(JSON.stringify(state.tablePicture[index].file));
      state.tablePicture[index].file = files;
      proxy.$refs['uploadImg' + index].clearFiles();
    };
    return {
      ...toRefs(state),
      drageHeader,
      handleChange,
      beforeUpload,
      closeDialog,
      clearFiles,
      onSubmit
    };
  }
};
</script>

<style lang="scss" scoped>
.imgTable {
  margin-top: 12px;
}
.redBorder {
  border: 1px solid red;
}
.el-form .pictureName {
  margin-bottom: 0 !important;
}
.red-color {
  margin-right: 0;
}
.curson {
  cursor: pointer;
}
:deep(.el-upload__tip) {
  color: #909399;
  display: inline-block;
  font-size: 14px;
  margin-left: 12px;
}
.addPicture {
  :deep(.imgDialog2 .el-upload__tip) {
    width: 250px;
    color: #909399;
    display: inline-block;
    margin-left: 12px;
    font-size: 14px;
  }
  .uploadBtn2 {
    color: $tes-primary;
    i {
      margin-right: 9.6px;
    }
  }
  .uploadBtn2.el-button--small {
    padding: 5px 16px;
    font-size: 14px;
  }
  :deep(.el-input--medium .el-input__inner) {
    height: 28px;
    line-height: 28px;
  }
  :deep(.el-input--medium) {
    line-height: 28px;
  }
  :deep(.el-input--medium .el-input__icon) {
    line-height: 28px;
  }
}
</style>
