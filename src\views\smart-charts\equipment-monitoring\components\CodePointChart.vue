<template>
  <!-- X-Bar均值图 -->
  <div class="box-top">
    <h1 class="inBlock">{{ title }}</h1>
  </div>
  <div v-loading="loading" element-loading-background="#7AD0FF" class="box-Center">
    <v-chart :option="controlChartOption" :width="'100%'" :height="'100%'" :events="controlChartEvents" />
  </div>
</template>

<script>
// import { reactive, toRefs, ref, onMounted, onBeforeUnmount, onUnmounted } from 'vue'
import { reactive, toRefs, ref, watch } from 'vue';
import VChart from '@/components/VChart';
import { cloneDeep, sum } from 'lodash';
import { formatDateTime } from '@/utils/formatTime';
import { postPointDataErrorLog } from '@/api/equipmentMonitoring';
import SPCEightCriterions from '../../func/SPCEightCriterions';
import {
  splitArray,
  getAxisInterval,
  getAverageOfArray,
  getXUCL,
  getXLCL,
  getStandardDeviation,
  getFormatArray,
  getMRArray
} from '../../func/calculate';

const chartTypeEnum = {
  Control: 1,
  Line: 2
};

export default {
  name: 'CodePointChart',
  components: { VChart },
  props: {
    title: {
      type: String,
      default: ''
    },
    chartType: {
      type: Number,
      default: chartTypeEnum.Control,
      validator(value) {
        return [chartTypeEnum.Control, chartTypeEnum.Line].includes(value);
      }
    },
    deviceId: {
      type: String,
      default: ''
    },
    deviceCodePointId: {
      type: String,
      default: ''
    },
    associatedOrderNumber: {
      type: String,
      default: ''
    },
    groupCount: {
      type: Number,
      default: 1
    },
    ucl: {
      type: String,
      default: ''
    },
    lcl: {
      type: String,
      default: ''
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  setup(props, context) {
    const state = reactive({
      subGroupCount: 2, // limited by 2 - 10
      xBarUpperLimit: null,
      xBarLowerLimit: null,
      loading: false,
      newData: [],
      intervalCount: 0
    });

    const isControlChart = props.chartType === chartTypeEnum.Control;
    let isDataZoomStop = false;
    let dataZoomLength = 0;
    let dataZoomStart = 0;
    let dataZoomEnd = 100;
    const criterionsInstance = new SPCEightCriterions();

    watch(
      () => props.data,
      data => {
        const valueList = [];
        const dateList = [];
        const groupCount = Number(props.groupCount);
        let newData = data;
        if (groupCount > 1) {
          newData = splitArray(newData, groupCount).map(items => {
            return {
              value: (sum(items.map(item => Number(item.value))) / items.length).toString(),
              date: items[items.length - 1].date
            };
          });
        }
        state.newData = newData;
        newData.forEach(item => {
          valueList.push(Number(item.value));
          dateList.push(formatDateTime(item.date, 'MM-dd\nhh:mm:ss'));
        });
        renderChart(valueList, dateList);
      }
    );

    // 控制图
    const controlChartOption = ref({
      color: ['#9FCEFF', '#00B38A', '#80D9C5', '#B3E09C', '#F2D09D', '#f9b5b5'],
      title: {
        text: ''
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#4791FF',
        extraCssText: 'text-align:left', // 设置tooltip的自定义样式
        textStyle: {
          color: '#fff',
          fontSize: 12,
          fontWeight: 400
        }
      },
      legend: {
        data: [],
        textStyle: {
          color: '#7AD0FF',
          fontSize: 12
        }
      },
      grid: {
        left: '20',
        right: '30',
        bottom: '10',
        containLabel: true
      },
      dataZoom: [
        {
          type: 'slider',
          show: false,
          rangeMode: 'value',
          // startValue: dataZoomStart < 0 ? 0 : dataZoomStart,
          // endValue: dataZoomEnd,
          zoomLock: false,
          brushSelect: false,
          textStyle: {
            color: '#ffffff'
          }
        }
      ],
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisTick: {
          show: false
        },
        axisLine: {
          lineStyle: {
            color: '#7AD0FF'
          }
        }
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: true,
          lineStyle: {
            color: '#5397CE'
          }
        },
        nameTextStyle: {
          color: '#7AD0FF',
          fontSize: '12'
        },
        axisLine: {
          lineStyle: {
            color: '#7AD0FF'
          }
        }
      },
      series: []
    });

    const controlChartEvents = {
      datazoom: params => {
        const singlePrecent = (1 / dataZoomLength) * 100;
        dataZoomStart = params.start;
        dataZoomEnd = params.end;
        if (params.end < 100 - singlePrecent * 3) {
          isDataZoomStop = true;
        } else {
          isDataZoomStop = false;
        }
      }
    };

    const renderChart = (dataSource, dateList) => {
      // 样品数量
      const dataSourceLength = dataSource.length;
      // 样本数量：单个样本内数量
      const subGroupCount = state.subGroupCount;
      // 移动极差数组
      const MRArray = getMRArray(dataSource, subGroupCount);
      // 移动平均极差
      const MRAverage = getAverageOfArray(MRArray);
      // 从小到大样本数据
      const sortedDataSource = cloneDeep(dataSource).sort((a, b) => a - b);
      // Cl
      const xClValue = Number(getAverageOfArray(sortedDataSource).toFixed(1));
      const xClArray = getFormatArray(xClValue, dataSourceLength);
      // Ucl
      const xUclValue = Number(props.ucl ? props.ucl : getXUCL(xClValue, MRAverage, subGroupCount).toFixed(1));
      const xUclArray = getFormatArray(xUclValue, dataSourceLength);
      // Lcl
      const xLclValue = Number(props.lcl ? props.lcl : getXLCL(xClValue, MRAverage, subGroupCount).toFixed(1));
      const xLclArray = getFormatArray(xLclValue, dataSourceLength);
      // 手工Ucl控制值
      let xManualUclValue = '';
      let xManualUclArray = [];
      if (props.ucl) {
        xManualUclValue = props.ucl;
        xManualUclArray = getFormatArray(xManualUclValue, dataSourceLength);
      }
      // 手工Lcl控制值
      let xManualLclValue = '';
      let xManualLclArray = [];
      if (props.lcl) {
        xManualLclValue = props.lcl;
        xManualLclArray = getFormatArray(xManualLclValue, dataSourceLength);
      }
      // 标准差 σ sigma
      const standardDeviation = dataSource.length >= 2 ? getStandardDeviation(dataSource).toFixed(1) : null;

      if (isControlChart && standardDeviation) {
        const criteriaList = criterionsInstance.getPassedCriterions({
          values: dataSource,
          UCL: xUclValue,
          CL: xClValue,
          LCL: xLclValue,
          sigma: Number(standardDeviation)
        });
        if (criteriaList) {
          console.log('criteriaList', criteriaList);
          criteriaList.forEach(item => {
            const startDateTime = state.newData[item.durationStartIndex]?.date
              ? formatDateTime(state.newData[item.durationStartIndex]?.date)
              : '';
            const endDateTime = state.newData[item.durationEndIndex]?.date
              ? formatDateTime(state.newData[item.durationEndIndex]?.date)
              : '';
            const params = {
              deviceId: props.deviceId,
              pointId: props.deviceCodePointId,
              errorTime: endDateTime,
              errorStartTime: startDateTime,
              errorEndTime: endDateTime,
              errorType: item.criterion,
              errorReason: item.description,
              productionOrderNo: props.associatedOrderNumber || 'xxxx'
            };
            postPointDataErrorLog(params);
          });
        }
      }

      const xAxisArray = dateList;

      const xbarAxisInfo = getAxisInterval(
        sortedDataSource[0],
        sortedDataSource[sortedDataSource.length - 1],
        sortedDataSource.length
      );
      const xBarInterval = xbarAxisInfo.axistInterval;
      let xBarMin = xbarAxisInfo.axisMin;
      let xBarMax = xbarAxisInfo.axisMax;
      if (xBarInterval > 0) {
        while (xBarMax <= xUclValue) {
          xBarMax += xBarInterval;
        }
        while (xBarMin >= xLclValue) {
          xBarMin -= xBarInterval;
        }
      }
      dataZoomLength = xAxisArray.length;
      if (!isDataZoomStop) {
        dataZoomEnd = 100;
      }
      if (dataZoomLength > 20) {
        controlChartOption.value.dataZoom[0].show = true;
        controlChartOption.value.dataZoom[0].start = dataZoomStart;
        controlChartOption.value.dataZoom[0].end = dataZoomEnd;
      }
      controlChartOption.value.grid.bottom = dataZoomLength > 20 ? '40' : '10';
      controlChartOption.value.xAxis.data = xAxisArray;
      controlChartOption.value.yAxis.interval = xBarInterval;
      controlChartOption.value.yAxis.min = xBarMin;
      controlChartOption.value.yAxis.max = xBarMax;
      controlChartOption.value.series = [
        {
          name: '单值X',
          type: 'line',
          symbol: 'circle',
          symbolSize: 10,
          itemStyle: {
            borderWidth: 2,
            borderColor: '#ffffff'
          },
          lineStyle: {
            width: 3,
            shadowColor: 'rgba(0, 179, 138, 0.3)',
            shadowBlur: 6,
            shadowOffsetY: 8
          },
          data: dataSource
        }
      ];

      if (isControlChart) {
        controlChartOption.value.series.push(
          ...[
            {
              name: 'UCL',
              type: 'line',
              symbol: 'none',
              data: xUclArray
            },
            {
              name: 'CL',
              type: 'line',
              symbol: 'none',
              data: xClArray
            },
            {
              name: 'LCL',
              type: 'line',
              symbol: 'none',
              data: xLclArray
            }
          ]
        );
        if (props.ucl) {
          controlChartOption.value.series.push({
            name: '手动控制上限',
            type: 'line',
            symbol: 'none',
            data: xManualUclArray
          });
        }
        if (props.lcl) {
          controlChartOption.value.series.push({
            name: '手动控制下限',
            type: 'line',
            symbol: 'none',
            data: xManualLclArray
          });
        }
        controlChartOption.value.legend.data = controlChartOption.value.series.map(item => item.name);
      }
    };

    return {
      ...toRefs(state),
      controlChartOption,
      controlChartEvents
    };
  }
};
</script>
<style lang="scss" scoped>
@import '../equipment-monitoring.scss';
</style>
