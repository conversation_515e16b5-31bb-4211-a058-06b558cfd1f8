<template>
  <span style="font-size: 12px">（{{ startNumber }}）</span>
</template>

<script>
import { reactive, toRefs, watch } from 'vue';
export default {
  name: 'DigitalScroll',
  props: {
    start: {
      type: Number,
      default: 0
    },
    end: {
      type: Number,
      default: 0
    },
    duration: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const state = reactive({
      startNumber: 0,
      endNumber: 0,
      duration: 0,
      timer: null
    });
    watch(props, newValue => {
      state.startNumber = newValue.start;
      state.endNumber = newValue.end;
      state.duration = newValue.duration;
      if (state.startNumber < state.endNumber) {
        addNumber();
      } else if (state.startNumber > state.endNumber) {
        subtractNumber();
      }
    });
    const addNumber = () => {
      state.timer = setInterval(() => {
        if (state.startNumber === state.endNumber) {
          removeTimer();
        } else {
          state.startNumber++;
        }
      }, 500);
    };
    const subtractNumber = () => {
      state.timer = setInterval(() => {
        if (state.startNumber === state.endNumber) {
          removeTimer();
        } else {
          state.startNumber--;
        }
      }, 500);
    };
    const removeTimer = () => {
      if (state.timer) {
        clearInterval(state.timer);
        state.timer = null;
      }
    };
    return {
      ...toRefs(state),
      addNumber,
      subtractNumber,
      removeTimer
    };
  }
};
</script>

<style lang="scss" scoped></style>
