<template>
  <div class="Specification">
    <div class="header-btn-group">
      <el-button
        v-if="isAdd || isEdit"
        :loading="loading"
        size="small"
        icon="el-icon-close"
        plain
        @click="handleCacle()"
        @keyup.prevent
        @keydown.enter.prevent
        >取消</el-button
      >
      <el-button
        v-if="isAdd || isEdit"
        :loading="loading"
        size="small"
        icon="el-icon-circle-check"
        type="primary"
        @click="handleSave()"
        @keyup.prevent
        @keydown.enter.prevent
        >保存</el-button
      >
      <el-button
        v-if="!isAdd && !isEdit && oldTableList.length > 0"
        :loading="loading"
        size="small"
        icon="el-icon-edit"
        plain
        type="primary"
        @click="handleAddEdit('edit')"
        @keyup.prevent
        @keydown.enter.prevent
        >编辑</el-button
      >
      <el-button
        v-if="!isEdit"
        size="small"
        icon="el-icon-plus"
        type="primary"
        @click="handleAddEdit('add')"
        @keyup.prevent
        @keydown.enter.prevent
        >新增</el-button
      >
    </div>
    <el-form ref="formRef" :model="formData" size="small">
      <el-table
        :id="`sortableList${type}`"
        ref="tableRef"
        :key="tableKey"
        v-loading="loading"
        :data="formData.tableList"
        size="medium"
        fit
        border
        class="dark-table base-table format-height-table"
        @header-dragend="drageHeader"
      >
        <el-table-column v-if="!isAdd && !isEdit" label="排序" :width="colWidth.serialNo">
          <i class="tes-move iconfont" style="font-size: 12px; cursor: move" />
        </el-table-column>
        <el-table-column label="序号" prop="index" :width="colWidth.serialNo" align="center">
          <template #default="{ $index }">
            <div class="">{{ Number($index) + 1 }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="fieldName" label="字段名称" :min-width="colWidth.name" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="(isAdd && !row.id) || (isEdit && row.id)"
              :prop="`tableList.${$index}.fieldName`"
              :rules="{ required: true, message: '请输入字段名称', trigger: 'change' }"
            >
              <el-input
                :ref="`fieldName${type}${$index}`"
                v-model="row.fieldName"
                maxlength="10"
                placeholder="请输入字段名称"
              />
            </el-form-item>
            <span v-else> {{ row.fieldName || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="isRequired" label="是否必填" :min-width="colWidth.status" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="(isAdd && !row.id) || (isEdit && row.id)"
              :prop="`tableList.${$index}.isRequired`"
              :rules="{ required: true, message: '请选择是否必填', trigger: 'change' }"
            >
              <el-select v-model="row.isRequired" size="small" placeholder="请选择是否必填">
                <el-option v-for="(val, key) in requireJSON" :key="key" :label="val" :value="Number(key)" />
              </el-select>
            </el-form-item>
            <span v-else> {{ requireJSON[row.isRequired] || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="fieldType" label="属性格式" :min-width="colWidth.name" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="(isAdd && !row.id) || (isEdit && row.id)"
              :prop="`tableList.${$index}.fieldType`"
              :rules="{ required: true, message: '请选择属性格式', trigger: 'change' }"
            >
              <el-select
                v-model="row.fieldType"
                size="small"
                placeholder="请选择"
                @change="
                  val => {
                    return handleChangeFieldType(val, $index);
                  }
                "
              >
                <el-option v-for="(val, key) in fieldTypeJSON" :key="key" :label="val" :value="key" />
              </el-select>
            </el-form-item>
            <span v-else> {{ fieldTypeJSON[row.fieldType] || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="dictionaryCode" label="关联字典" :min-width="colWidth.name" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="((isAdd && !row.id) || (isEdit && row.id)) && row.fieldType === '3'"
              :prop="`tableList.${$index}.dictionaryCode`"
              :rules="{ required: true, message: '请选择关联字典', trigger: 'change' }"
            >
              <el-select v-model="row.dictionaryCode" clearable size="small" placeholder="请选择字典">
                <el-option v-for="(val, key) in dictionaryJSON" :key="key" :label="val.name" :value="key" />
              </el-select>
            </el-form-item>
            <span v-else> {{ dictionaryJSON[row.dictionaryCode]?.name || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" :min-width="colWidth.status" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item v-if="(isAdd && !row.id) || (isEdit && row.id)" :prop="`tableList.${$index}.status`">
              <el-select v-model="row.status" size="small" placeholder="请选择">
                <el-option label="启用" :value="1" />
                <el-option label="停用" :value="0" />
              </el-select>
            </el-form-item>
            <el-tag v-else size="small" :type="statusDicClass[row.status]"> {{ statusDic[row.status] || '--' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-if="isAdd"
          prop="operation"
          label="操作"
          :min-width="colWidth.operationSingle"
          show-overflow-tooltip
          fixed="right"
        >
          <template #default="{ row, $index }">
            <span v-if="!row.id" class="blue-color" @click="handleDelete($index)">删除</span>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance, watch, nextTick } from 'vue';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { findByMaterialCategoryCode, saveOrUpdate } from '@/api/material';
import { getDictionaryList } from '@/api/dictionary';
import { drageHeader } from '@/utils/formatTable';
import { formatDate } from '@/utils/formatTime';
import { colWidth } from '@/data/tableStyle';
import Sortable from 'sortablejs';

export default {
  name: 'Specification',
  components: {},
  props: {
    type: {
      type: String,
      default: ''
    },
    activeName: {
      type: String,
      default: ''
    },
    currentData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  setup(props) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableRef1: ref(),
      tableRef2: ref(),
      currentData: {},
      oldTableList: [],
      isAdd: false,
      isEdit: false,
      formRef: ref(),
      tableKey: ref(0),
      loading: false,
      status: {
        3: { class: 'icon-tes-info', label: '待审核', value: '3' },
        5: { class: 'icon-tes-success', label: '已通过', value: '5' }
      },
      listQuery: {
        limit: 20,
        page: 1
      },
      formData: {
        tableList: []
      },
      total: 0,
      fieldTypeJSON: {
        0: '文本',
        1: '数字',
        2: '日期',
        3: '字典',
        4: '多行文本框'
      },
      requireJSON: {
        0: '否',
        1: '是'
      },
      statusDicClass: {
        1: 'success',
        0: 'info'
      },
      statusDic: {
        1: '已启用',
        0: '已停用'
      },
      dictionaryJSON: {}
    });
    watch(props, newValue => {
      state.type = props.type;
      state.currentData = props.currentData;
      if (state.type === props.activeName) {
        state.formData.tableList = [];
        state.tableKey = ref(0);
        state.isAdd = false;
        state.isEdit = false;
        getList();
        nextTick(() => {
          rowDrop();
        });
        if (state.type === '1') {
          state.fieldTypeJSON = {
            0: '文本',
            1: '数字',
            2: '日期',
            3: '字典'
          };
        } else {
          state.fieldTypeJSON = {
            0: '文本',
            1: '数字',
            2: '日期',
            3: '字典',
            4: '多行文本框'
          };
        }
      }
    });
    const getList = () => {
      state.loading = true;
      findByMaterialCategoryCode(state.currentData.code, state.type).then(res => {
        state.loading = false;
        if (res) {
          state.formData.tableList = JSON.parse(JSON.stringify(res.data.data));
          state.oldTableList = JSON.parse(JSON.stringify(res.data.data));
        }
      });
    };
    const getDictionary = () => {
      getDictionaryList({ limit: '-1', page: '1', dictionaryType: '1' }).then(res => {
        state.dictionaryJSON = {};
        res.data.data.list.forEach(item => {
          state.dictionaryJSON[item.id] = item;
        });
      });
    };
    getDictionary();
    const handleSave = () => {
      state.formRef
        .validate()
        .then(valid => {
          if (valid) {
            const fieldNameArray = state.formData.tableList.map(item => {
              return item.fieldName;
            });
            const isRequiredArray = state.formData.tableList.filter(item => {
              return item.status === 1;
            });
            if ([...new Set(fieldNameArray)].length !== state.formData.tableList.length) {
              proxy.$message.error('字段名称不能重复！');
              return false;
            }
            if (
              isRequiredArray.length > 0 &&
              !isRequiredArray.some(item => {
                return item.isRequired === 1;
              })
            ) {
              proxy.$message.error('启用状态至少有一条必填项！');
              return false;
            }
            const params = state.formData.tableList;
            params.forEach((item, index) => {
              if (!item.id) {
                item.reportKey = state.type === '1' ? `sampleField${index + 1}` : `addField${index + 1}`;
                item.order = index;
                item.configType = Number(state.type);
                item.materialCategoryCode = state.currentData.code;
                item.materialCategoryId = state.currentData.id;
              }
            });
            saveApi(params, true);
          } else {
            return false;
          }
        })
        .catch(error => {
          const fieldName = Object.keys(error)[0];
          proxy.$message.warning(error[fieldName][0].message);
          return false;
        });
    };
    const saveApi = (params, isRefresh) => {
      state.loading = true;
      saveOrUpdate({ sampleConfigList: params }).then(res => {
        state.loading = false;
        if (res) {
          state.isAdd = false;
          state.isEdit = false;
          proxy.$message.success('保存成功！');
          if (isRefresh) {
            getList();
          }
        }
      });
    };
    // 取消
    const handleCacle = () => {
      if (state.isAdd) {
        state.isAdd = false;
      } else {
        state.isEdit = false;
      }
      state.formData.tableList = JSON.parse(JSON.stringify(state.oldTableList));
    };
    const handleDelete = index => {
      state.formData.tableList.splice(index, 1);
      if (state.formData.tableList.length === state.oldTableList.length) {
        state.isAdd = false;
      }
    };
    // 行拖拽
    const rowDrop = () => {
      // 获取当前表格
      const el = document.getElementById(`sortableList${state.type}`).querySelector('.el-table__body-wrapper tbody');
      Sortable.create(el, {
        animation: 300,
        handle: '.tes-move',
        draggable: '.el-table__row',
        ghostClass: 'ghost',
        // 拖动对象移动样式
        dragClass: 'drag',
        forceFallback: true,
        onEnd({ newIndex, oldIndex }) {
          if (oldIndex !== newIndex) {
            const currRow = state.formData.tableList.splice(oldIndex, 1)[0];
            state.formData.tableList.splice(newIndex, 0, currRow);
            state.formData.tableList.forEach((value, index) => {
              value.order = index;
            });
            saveApi(state.formData.tableList);
            state.tableKey += 1;
            nextTick(() => {
              rowDrop();
            });
          }
        }
      });
    };
    // 新增、编辑
    const handleAddEdit = operateType => {
      if (state.currentData.id) {
        if (operateType === 'add') {
          state.formData.tableList.push({ fieldName: '', isRequired: 0, fieldType: '0', status: 1 });
          state.isAdd = true;
        } else {
          state.isEdit = true;
        }
      } else {
        proxy.$message.warning('请现在左侧添加类目');
      }
    };
    const reset = () => {
      state.condition = '';
      getList();
    };
    const handleChangeFieldType = (val, index) => {
      if (val !== '3') {
        state.formData.tableList[index].dictionaryName = '';
        state.formData.tableList[index].dictionaryCode = '';
      }
    };
    const onSubmit = () => {};
    return {
      ...toRefs(state),
      reset,
      rowDrop,
      handleSave,
      handleChangeFieldType,
      handleCacle,
      handleDelete,
      handleAddEdit,
      drageHeader,
      getNameByid,
      getNamesByid,
      onSubmit,
      getPermissionBtn,
      formatDate,
      getList,
      colWidth
    };
  },
  computed: {},
  created() {}
};
</script>
<style lang="scss" scoped>
.header-btn-group {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}
.el-upload__tip {
  display: inline-block;
  margin-left: 10px;
}
:deep(.el-select) {
  width: 100%;
}
:deep(.el-form .el-form-item) {
  margin-bottom: 0;
}
</style>
