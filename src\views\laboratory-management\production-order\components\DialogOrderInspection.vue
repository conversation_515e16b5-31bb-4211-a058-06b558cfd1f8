<template>
  <el-dialog
    v-model="showDialog"
    custom-class="double-dialog tiny-dialog info-add"
    :title="title"
    top="5vh"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <div class="dialog-main">
      <el-form
        ref="inspectionInfoRef"
        class="formDataInfo"
        :model="formInline"
        :rules="inspectionInfoRule"
        label-width="110px"
        label-position="top"
      >
        <el-row :gutter="60">
          <el-col :span="24">
            <el-form-item label="" prop="type">
              <el-radio-group v-model="formInline.type" size="large" class="radio-groups" @change="changeType">
                <el-radio-button v-for="item in typeOptions" :key="item.id" :label="item.id">
                  <img :src="item.icon" alt="icon" />
                  <span class="name">{{ item.name }}</span>
                  <span class="corner" />
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-space direction="vertical">
          <el-row :gutter="60">
            <el-col :span="12">
              <el-form-item label="报检人：" prop="InspectionUserId">
                <el-select
                  v-model="formInline.InspectionUserId"
                  class="owner-select"
                  placeholder="请选择报检人"
                  clearable
                  filterable
                  :filter-method="filterUserList"
                  @focus="filterUserList(null)"
                  @change="changeUser"
                >
                  <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报检日期：" prop="registerTime">
                <el-date-picker
                  v-model="formInline.registerTime"
                  type="date"
                  placeholder="请选择报检日期"
                  class="register-date"
                  @change="changeRegisterTime"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-space>
        <el-row :gutter="60">
          <el-col :span="12">
            <el-form-item label="送样数量：" prop="sampleQuantity">
              <div class="col-between">
                <el-input
                  v-model.trim="formInline.sampleQuantity"
                  type="number"
                  autocomplete="off"
                  style="width: 60%"
                  maxlength="10"
                  min="0"
                />
                <el-select
                  v-model="formInline.inspectionUnit"
                  :disabled="false"
                  placeholder="请选择单位"
                  style="width: 40%; margin-left: 10px"
                >
                  <el-option v-for="it in dirList" :key="it.code" :label="it.name" :value="it.name" />
                </el-select>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生产数量：" prop="productionQuantity">
              <div class="col-between">
                <el-input
                  v-model.trim="formInline.productionQuantity"
                  type="number"
                  autocomplete="off"
                  style="width: 60%"
                  maxlength="10"
                  min="0"
                />
                <el-select
                  v-model="formInline.inspectionUnit"
                  :disabled="false"
                  placeholder="请选择单位"
                  style="width: 40%; margin-left: 10px"
                >
                  <el-option v-for="it in dirList" :key="it.code" :label="it.name" :value="it.name" />
                </el-select>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="60">
          <el-col :span="12">
            <el-form-item label="盘号：" prop="reelNo" style="position: relative">
              <span class="scdd">
                <el-tooltip content="部分厂商对应ID号" placement="top" effect="dark">
                  <i class="iconfont tes-title" />
                </el-tooltip>
              </span>
              <el-input v-model="formInline.reelNo" placeholder="请输入盘号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-if="formInline.type === 2 || formInline.type === 3"
              label="生产工序："
              prop="productionProcedure"
            >
              <el-select
                v-model="formInline.productionProcedure"
                class="owner-select"
                placeholder="请选择生产工序"
                clearable
                filterable
                :filter-method="filterProcessList"
                @focus="filterProcessList(false)"
                @clear="filterProcessList(false)"
                @change="changeProductionProcedure"
              >
                <el-option
                  v-for="item in processList"
                  :key="item.id"
                  :label="item.no + ' - ' + item.name"
                  :value="item.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="60">
          <el-col :span="12">
            <el-form-item label="项目名称：" prop="reelNo" style="position: relative">
              <el-input v-model="formInline.projectName" placeholder="请输入项目名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物资分类：" prop="materialCode" style="position: relative">
              <el-select v-model="formInline.materialCode" placeholder="请选择物资分类" clearable filterable>
                <el-option v-for="item in materialOptions" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent>保 存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref } from 'vue';
import _ from 'lodash';
// import { ElMessageBox } from 'element-plus'
// import router from '@/router/index.js'
// import { useRoute } from 'vue-router'
// import { addInspection, updateInspection } from '@/api/inspection-application'
import { useStore } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
import { getProcessListNew } from '@/api/mas';
import { formatDate } from '@/utils/formatTime';
import { getDictionaryDetail } from '@/api/dictionary';
import { getNameByid } from '@/utils/common';
import { greaterThanZero } from '@/utils/validate';
import inspection_icon_2 from '@/assets/img/inspection_icon_2.svg';
import inspection_icon_3 from '@/assets/img/inspection_icon_3.svg';

export default {
  name: 'AddInfo',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '报检'
    },
    info: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo', 'saveInspection'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    // const lodash = inject('_')
    const store = useStore().state;
    const datas = reactive({
      currentAccountName: getNameByid(getLoginInfo().accountId),
      materialOptions: store.user.materialList,
      userOptions: store.common.nameList,
      copyUserOptions: store.common.nameList,
      showDialog: false,
      formInline: {
        InspectionUserId: '',
        registerTime: formatDate(new Date()),
        type: 2,
        inspectionUnit: '',
        reelNo: '',
        productionQuantity: '',
        productionProcedureNo: '',
        productionProcedure: '',
        sampleQuantity: ''
      },
      inspectionInfoRef: ref(),
      inspectionInfoRule: {
        InspectionUserId: [{ required: true, message: '请输入报检人' }],
        registerTime: [{ required: true, message: '请选择报检日期' }],
        materialCode: [{ required: true, message: '请选择物资分类' }],
        type: [{ required: true, message: '请输入检验类型' }],
        sampleQuantity: [
          { required: true, message: '请输入送样数量', trigger: 'blur' },
          { validator: greaterThanZero, trigger: 'change' }
        ],
        productionQuantity: [
          { required: true, message: '请输入生产数量', trigger: 'blur' },
          { validator: greaterThanZero, trigger: 'change' }
        ]
      },
      typeOptions: [
        { id: 2, name: '过程检验', icon: inspection_icon_2 },
        { id: 3, name: '完工检验', icon: inspection_icon_3 }
      ],
      processList: [],
      copyProcessList: []
    });

    watch(
      () => props.show,
      newValue => {
        if (newValue) {
          datas.showDialog = newValue;
          datas.formInline = {
            InspectionUserId: datas.currentAccountName,
            registerTime: formatDate(new Date()),
            type: 2,
            inspectionUnit: props.info.productionUnit,
            reelNo: props.info.reelNo || '',
            materialCode: props.info.materialCode || '',
            productionQuantity: props.info.productionQuantity.toString() || '',
            productionProcedureNo: props.info.productionOrderNo || '',
            productionProcedure: props.info.productionProcedure || '',
            projectName: props.info.projectName || '',
            sampleQuantity: ''
          };
        }
      },
      { deep: true }
    );

    // 确定选择
    const dialogSuccess = () => {
      datas.inspectionInfoRef.validate(valid => {
        if (valid) {
          datas.showDialog = false;
          context.emit('close', false);
          context.emit('saveInspection', datas.formInline);
          // addInspection(datas.formInline).then(res => {
          //   if (res !== false) {
          //     ElMessage.success('新增成功')
          //     datas.showDialog = false
          //     context.emit('close', false)
          //     router.push({ name: 'AddInspection', query: { id: res.data.data, flag: 2 }})
          //   }
          // })
        }
      });
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };
    // 过滤报检人
    const filterUserList = val => {
      if (val) {
        const list = [];
        datas.copyUserOptions.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        datas.userOptions = list;
      } else {
        datas.userOptions = datas.copyUserOptions;
      }
    };
    // 过滤生产工序
    const filterProcessList = val => {
      if (val) {
        const list = [];
        datas.copyProcessList.forEach(user => {
          const item = _.filter(user.name, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        datas.processList = list;
      } else {
        datas.processList = JSON.parse(JSON.stringify(datas.copyProcessList));
      }
    };

    // 报检人-change
    const changeUser = id => {
      datas.formInline.InspectionUserId = id;
    };
    // 选择报检日期
    const changeRegisterTime = time => {};
    // 检验类型-change
    const changeType = type => {
      datas.formInline.type = type;
    };
    // 生产工序-change
    const changeProductionProcedure = no => {
      datas.processList.forEach(item => {
        if (item.no === no) {
          datas.formInline.productionProcedure = item.name;
          datas.formInline.productionProcedureNo = item.no;
        }
      });
    };

    return {
      ...toRefs(datas),
      greaterThanZero,
      dialogSuccess,
      close,
      filterUserList,
      changeUser,
      changeRegisterTime,
      changeType,
      changeProductionProcedure,
      filterProcessList
    };
  },
  created() {
    this.getProcessLists();
    getDictionaryDetail(5).then(res => {
      this.dirList = res.data.data?.dictionaryoption;
    });
  },
  methods: {
    // 获取生产工序列表接口
    getProcessLists() {
      var that = this;
      var param = {
        limit: '-1',
        page: '1',
        content: ''
      };
      getProcessListNew(param).then(res => {
        if (res !== false) {
          that.processList = res.data.data.list;
          that.copyProcessList = res.data.data.list;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.col-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.formDataInfo {
  :deep(.el-input--medium .el-input__inner) {
    height: 32px;
    line-height: 32px;
  }
}
.scdd {
  position: absolute;
  left: 40px;
  top: -34px;
}
.info-add {
  .dialog-main {
    :deep(.el-space) {
      width: 100%;
      .el-space__item {
        margin: 16px 0;
        width: 100%;
      }
    }

    .radio-groups {
      display: flex;
      justify-content: space-around;
      align-items: center;
      .el-radio-button {
        border: 2px solid transparent;
      }
      .el-radio-button.is-active {
        border-color: $tes-primary3;
      }
      .el-radio-button__inner {
        img {
          width: 54px;
          height: 64px;
          margin-right: 20px;
        }
      }
      :deep(.el-radio-button__inner) {
        border-color: transparent;
        // border-radius: 8px;
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
        padding: 16px 40px;
        box-shadow: none;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        line-height: 28px;
      }
      .el-radio-button {
        box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
        // border-radius: 8px;
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
      }
      .el-radio-button__original-radio:checked + .el-radio-button__inner {
        border: 2px solid $tes-primary3 !important;
      }

      .corner {
        opacity: 0;
        position: absolute;
        top: -20px;
        right: -20px;
        width: 0;
        height: 0;
        border: 20px solid $tes-primary;
        border-bottom-color: transparent;
        border-top-color: transparent;
        border-left-color: transparent;
        transform: rotateZ(135deg);
      }
      .el-radio-button.is-active .corner {
        opacity: 1;
      }
    }
  }
  .el-input--medium .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
  .el-divider--horizontal {
    margin: 15px 0px;
  }
  .el-form-item {
    text-align: left;
    // width: 46%;
    margin-bottom: 10px;
    :deep(.el-form-item__label) {
      font-weight: normal;
      font-size: 14px;
      color: #606266;
      padding: 0px;
    }
    :deep(.el-form-item__content) {
      .el-select {
        width: 100%;
      }
      .el-date-editor.el-input {
        width: 100%;
      }
      .el-radio-group {
        .el-radio {
          margin: 0px;
        }
        .el-radio:nth-child(2) {
          margin: 0px 5px;
        }
      }
    }
  }
}
</style>
