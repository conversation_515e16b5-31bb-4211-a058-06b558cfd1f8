import store from '@/store';

const routeModules = import.meta.glob('/src/views/**/*.vue');
const routePaths = Object.keys(routeModules);

/**
 * @param {Array} value
 * @returns {Boolean}
 * @example see @/views/permission/directive.vue
 */
export default function checkPermission(value) {
  if (value && value instanceof Array && value.length > 0) {
    const roles = store.getters && store.getters.roles;
    const permissionRoles = value;

    const hasPermission = roles.some(role => {
      return permissionRoles.includes(role);
    });
    return hasPermission;
  } else {
    console.error(`need roles! Like v-permission="['admin','editor']"`);
    return false;
  }
}

function hasCurrentPermission(roles, item) {
  if (item.id && roles.length > 0) {
    const flag = roles.some(role => item.id === role.id);
    return !flag;
  } else {
    return true;
  }
}

function loadPageByRoutes(str) {
  if (str === 'Layout') {
    return () => import('../layout');
  } else {
    const index = routePaths.findIndex(ev => ev.includes(str));
    return routeModules[routePaths[index]];
  }
}

/**
 * 关于路由说明：
 * 这边菜单一共有三层，包括一级菜单，子菜单，外部菜单，和按钮，
 * 一级菜单type：1，
 * 子菜单 type：2，
 * 外部菜单 type：3，
 * 按钮 type：4
 */
export function transformMenusToRoutes(routes, menus) {
  const newRoutes = [];
  menus.forEach(menu => {
    if (hasCurrentPermission(routes, menu)) {
      const component = loadPageByRoutes(menu.component);
      const newRoute = {
        path: menu.path,
        name: menu.name,
        children: [],
        meta: {
          parentId: menu.parentId,
          id: menu.id,
          type: menu.type,
          key: menu.key,
          title: menu.title,
          icon: menu.icon,
          hidden: menu.hidden,
          visible: menu.visible,
          showOneMenu: menu.showOneMenu
        },
        component: component
      };
      if (menu.children && menu.children.length > 0) {
        newRoute.children = transformMenusToRoutes(routes, menu.children);
      }
      switch (menu.type) {
        case 1: {
          if (menu.children.length > 0) {
            newRoute.redirect = menu.children[0].path;
            newRoute.component = menu.component === 'Layout' ? loadPageByRoutes('Layout') : null;
          } else if (menu.name === 'Home' && menu.children.length === 0) {
            newRoute.path = '/';
            newRoute.redirect = menu.path;
            newRoute.component = loadPageByRoutes('Layout');
            newRoute.meta.showOneMenu = true;
            newRoute.children = [
              {
                path: menu.path,
                name: menu.name,
                children: [],
                meta: {
                  parentId: menu.parentId,
                  id: menu.id,
                  type: menu.type,
                  key: menu.key,
                  title: menu.title,
                  icon: menu.icon,
                  hidden: menu.hidden,
                  visible: menu.visible
                },
                component: component
              }
            ];
          }
          break;
        }

        case 2: {
          if (menu.hidden === 0) {
            // 如果是子菜单并且是内部菜单（hidden===0）需要关联父级
            const item = menus.filter(m => menu.menuKey === m.key);
            if (item.length > 0) {
              newRoute.meta.activeMenu = item[0].path;
              newRoute.meta.parentName = item[0].name;
            }
          }
          break;
        }

        case 3: {
          // 兼容缓存数据代码
          if (menu.path.includes('${HOST}:9130')) {
            menu.path = menu.path.replace('${HOST}:9130', '/manage');
          }
          if (menu.path.includes('${HOST}:9150')) {
            menu.path = menu.path.replace('${HOST}:9150', '/qms');
          }

          if (!menu.path.startsWith('/')) {
            newRoute.path = '/' + menu.path;
          }
          if (newRoute.path.startsWith('/manage')) {
            newRoute.path = (import.meta.env.DEV ? import.meta.env.VITE_MANAGE_ORIGIN : window.origin) + newRoute.path;
          }
          if (newRoute.path.startsWith('/qms')) {
            newRoute.path = (import.meta.env.DEV ? import.meta.env.VITE_QMS_ORIGIN : window.origin) + newRoute.path;
          }
          newRoute.component = null;
          break;
        }
      }

      newRoutes.push(newRoute);
    }
  });
  return newRoutes;
}
