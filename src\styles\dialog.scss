// 自定义复杂弹窗样式（左侧树，右侧列表，底部展示已选中）
.custom-dialog {
  margin: 0 auto;
  .el-dialog__body {
    padding: 20px 20px 0 !important;
    background: #f0f2f5;
  }
  .dialog-header {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .header-left {
      .el-input {
        width: 360px;
        margin-right: 10px;
      }
    }
  }
  .dialog-content {
    margin-bottom: 20px;
    .tree-container,
    .list-container {
      background: $background-color;
      padding: 20px;
    }
    .list-container {
      padding-bottom: 10px;
      overflow-y: auto;
      margin-left: 20px;
      .item-content {
        &:not(:last-of-type) {
          display: flex;
          align-items: baseline;
          border-bottom: 1px solid #e4e7ed;
          margin-bottom: 10px;
        }
        cursor: pointer;
        padding-bottom: 10px;
        display: flex;
        .left {
          margin-right: 10px;
        }
        .main {
          .title {
            color: $tes-font;
            font-size: 16px;
            height: 32px;
            line-height: 32px;
            margin: 0;
            padding: 0;
          }
          .title-checked {
            color: $tes-primary;
          }
          .item-list {
            border-color: none;
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            align-items: center;
            span.item-box {
              display: inline-block;
              margin: 0 20px 10px 0;
              font-size: 14px;
              // padding: 8px 0;
            }
            .el-tag {
              margin: 0 10px 10px 0;
            }
          }
        }
      }
    }
  }
  .dialog-other {
    background: $background-color;
    margin: 0 -20px -10px;
    padding: 20px 20px 0;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      height: 32px;
      label {
        font-size: 16px;
        font-weight: bold;
      }
      .el-button {
        margin-left: 10px;
      }
    }
    .select-items {
      border: 1px solid #ffffff;
      box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
      border-radius: 10px;
      padding: 10px 10px 0;
      margin-bottom: 20px;
      max-height: 120px;
      overflow-y: auto;
      .el-tag {
        margin: 0 10px 10px 0;
        max-width: 600px;
        overflow: hidden; /* 确保超出容器的内容被裁剪 */
        white-space: nowrap; /* 确保文本在一行内显示 */
        text-overflow: ellipsis; /* 超出部分显示省略号 */
      }
    }
  }
}
