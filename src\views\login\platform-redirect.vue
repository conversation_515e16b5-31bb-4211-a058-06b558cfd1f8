<script>
import { getParamDecryption } from '@/api/platform';
import { useRoute } from 'vue-router';
import { reactive, toRefs, inject } from 'vue';
// import { useStore } from 'vuex'
import store from '@/store';
import { ElMessage } from 'element-plus';
import { getLoginInfo, getLIMSConfig, getMenuList } from '@/utils/auth';
import { setCurrentIp, logoutProcess } from '@/services/loginService';

export default {
  name: 'PlatformRedirect',
  setup(props, ctx) {
    const websocket = inject('$wsWebsocket');
    const route = useRoute();
    // const store = useStore().state
    const state = reactive({
      tokenInfo: {}
    });

    const jumpToSystem = async () => {
      const platformParams = route.query.ciphertext;
      const decryptionResult = await getParamDecryption(platformParams);
      if (decryptionResult) {
        const userLoginInfo = decryptionResult.data.data;
        if (userLoginInfo) {
          if (userLoginInfo.isBind) {
            const currentLoginInfo = getLoginInfo();
            if (
              currentLoginInfo &&
              currentLoginInfo.accountId === userLoginInfo.id &&
              currentLoginInfo.tenantId === userLoginInfo.currenttenantId
            ) {
              window.location.href = `${window.location.origin}${
                userLoginInfo.targetUrl || getLIMSConfig().VITE_HOME_PAGE
              }`;
            } else {
              await logoutProcess(websocket);
              const loginResult = await store.dispatch('user/platformLogin', userLoginInfo);
              if (loginResult) {
                await setCurrentIp();
                if (userLoginInfo.targetUrl) {
                  window.location.href = `${window.location.origin}${userLoginInfo.targetUrl}`;
                } else {
                  var menuList = getMenuList();
                  var homeFlag = false;
                  var userFlag = false;
                  if (menuList && menuList.length > 0) {
                    homeFlag = menuList.some(function (menu) {
                      return menu.name === 'Home' || menu.name === 'InspectionApplication';
                    });
                    userFlag = menuList.some(function (m) {
                      return m.name === 'userManage';
                    });
                  }
                  if (homeFlag) {
                    window.location.href = window.location.origin + getLIMSConfig().VITE_HOME_PAGE;
                  } else if (userFlag) {
                    window.location.href = window.location.origin + '/user-manage/base-info';
                  } else {
                    const toPath = menuList[0].children.length > 0 ? menuList[0].children[0].path : menuList[0].path;
                    window.location.href = window.location.origin + toPath;
                  }
                }
              } else {
                ElMessage.warning('平台登录失败!');
              }
            }
          } else {
            await logoutProcess(websocket);
            sessionStorage.setItem('platformParams', platformParams);
            window.location.href = `${window.location.origin}/login`;
          }
        } else {
          ElMessage.warning(`${decryptionResult.data.message}`);
        }
      }
    };

    jumpToSystem();

    return {
      ...toRefs(state)
    };
  },
  created() {
    const hash = window.location.search.slice(1);
    console.log(`PlatformRedirect: ${hash}`);
  }
};
</script>
<template>
  <div />
</template>
