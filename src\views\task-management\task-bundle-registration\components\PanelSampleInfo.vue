<template>
  <div class="sample-about">
    <div class="sample-about-header">
      <div class="title">样品相关</div>
      <el-button
        v-if="showDetail && isInvalidated === 0"
        type="primary"
        size="small"
        icon="el-icon-plus"
        @click="addSample"
        @keyup.prevent
        @keydown.enter.prevent
        >添加样品</el-button
      >
    </div>
    <div class="content">
      <el-table
        v-if="sampleInfoDetail && sampleInfoDetail.length > 0"
        ref="tableSampleRef"
        :key="tableSampleKey"
        :data="sampleInfoDetail"
        fit
        border
        class="dark-table sample-about-table"
        @header-dragend="drageHeader"
      >
        <el-table-column type="index" label="序号" :width="70" align="center" />
        <el-table-column label="样品编号" prop="sealNum" min-width="140px" show-overflow-tooltip>
          <template #default="{ row }">
            <div>{{ row.sealNum || '--' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="样品名称" prop="sampleName" min-width="140px" show-overflow-tooltip>
          <template #default="{ row }">
            <div>{{ row.sampleName || '--' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="型号规格" prop="model" min-width="120px" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.model || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="检测依据" prop="inspectBaseOn" min-width="140px" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.inspectBaseOn || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="样品数量" prop="sampleNum" min-width="100px" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.sampleNum || '--' }}{{ filterSampleUnitToName(row.sampleUnit) || '' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="试验类型" prop="inspectType" min-width="100px" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ formatTestType(row.inspectType) || row.inspectType || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="检测项目" prop="inspectionProject" min-width="120px" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.inspectionProject || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180px" prop="caozuo" fixed="right" align="center">
          <template #default="{ row }">
            <!-- <span class="blue-color" @click="chenckSampleIA(row)">查看</span> -->
            <span v-if="showDetail && isInvalidated === 0" class="blue-color" @click="editSampleIA(row)">编辑</span>
            <span v-if="showDetail && isInvalidated === 0" class="blue-color" @click="deleteSampleIA(row)">删除</span>
            <span v-if="showDetail && isInvalidated === 0" class="blue-color" @click="copySampleInfo(row)">复制</span>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-if="!sampleInfoDetail || sampleInfoDetail.length === 0" :image="emptyImg" description="暂无数据" />
    </div>
    <DrawerAddSample
      :drawer="showDrawer"
      :title="addSampleTitle"
      :edit-data="editSampleData"
      :task-id="currentTaskId"
      :material-code="materialCode"
      @set-info="getAddSampleData"
      @close="closeAddSampleDrawer"
    />
  </div>
</template>

<script>
import { reactive, toRefs, watch } from 'vue';
import { useStore } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
// import router from '@/router/index.js'
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import DrawerAddSample from './DrawerAddSample.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
// import { getSampleUnitDict } from '@/api/login'
import { formatTestType } from '@/utils/formatIndustryTerm';
import { deleteSampleInfoById, copySampleInfoById } from '@/api/task-registration';
import { filterSampleUnitToName } from '@/utils/formatJson';
import emptyImg from '@/assets/img/empty-table.png';

export default {
  name: 'SampleAbout',
  components: { DrawerAddSample },
  props: {
    taskId: {
      type: String,
      default: ''
    },
    materialCode: {
      type: String,
      default: ''
    },
    isInvalidated: {
      type: Number,
      default: 0
    },
    showDetail: {
      type: Boolean,
      default: false
    },
    sampleInfo: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['setInfo'],
  setup(props, context) {
    const store = useStore().state;
    const datas = reactive({
      tableSampleKey: 'tableSampleKey',
      currentAccountId: getLoginInfo().accountId,
      userOptions: store.common.nameList,
      sampleInfoDetail: props.sampleInfo,
      showDrawer: false,
      addSampleTitle: '新增样品',
      isInvalidated: props.isInvalidated,
      editSampleData: {},
      currentTaskId: '',
      options: store.user.sampleUnit
    });

    watch(
      () => props.taskId,
      newValue => {
        if (newValue) {
          datas.currentTaskId = newValue;
          datas.sampleInfoDetail = props.sampleInfo;
        }
      },
      { deep: true }
    );

    watch(
      () => props.sampleInfo,
      newValue => {
        if (newValue) {
          datas.sampleInfoDetail = props.sampleInfo;
        }
      },
      { deep: true }
    );

    // 添加样品-打开新增样品弹出框
    const addSample = () => {
      // console.log(datas.sampleInfoDetail)
      datas.addSampleTitle = '新增样品';
      datas.editSampleData = {};
      datas.showDrawer = true;
      // context.emit('setInfo', datas.sampleInfoDetail)
    };
    // 关闭新增样品页面弹出框
    const closeAddSampleDrawer = value => {
      datas.showDrawer = value;
      context.emit('setInfo', 'close');
    };
    // 获取新增样品数据
    const getAddSampleData = data => {
      // console.log(data)
      // datas.sampleInfoDetail.push(data)
      context.emit('setInfo', datas.sampleInfoDetail);
    };
    // 编辑样品
    const editSampleIA = row => {
      datas.addSampleTitle = '编辑样品';
      datas.editSampleData = row;
      datas.showDrawer = true;
    };
    // 样品信息
    const chenckSampleIA = row => {
      datas.addSampleTitle = '查看样品';
      datas.editSampleData = row;
      datas.showDrawer = true;
    };

    const copySampleInfo = row => {
      ElMessageBox.prompt('样品编号不可复制，复制前请先修改编号！', '复制', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        inputPlaceholder: '请修改样品编号',
        inputValue: row.sealNum,
        closeOnClickModal: true,
        inputPattern: /\S/,
        inputErrorMessage: '请输入样品编号',
        type: 'warning'
      })
        .then(({ value }) => {
          copySampleInfoById({ id: row.id, sealNum: value }).then(res => {
            if (res !== false) {
              ElMessage.success('复制成功');
              context.emit('setInfo', 'copy');
            }
          });
        })
        .catch(() => {});
    };

    // 删除样品
    const deleteSampleIA = row => {
      // console.log(row)
      ElMessageBox({
        title: '删除样品',
        message: '是否确认删除该样品？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          deleteSampleInfoById(row.id).then(res => {
            if (res !== false) {
              // console.log(res)
              ElMessage.success('删除成功');
              context.emit('setInfo', 'delete');
            }
          });
        })
        .catch(() => {});
    };
    // 过滤单位
    const filterUnit = unitId => {
      var unitName = '';
      if (unitId && datas.options.length > 0) {
        datas.options.forEach(opt => {
          if (opt.code === unitId) {
            unitName = opt.name;
          }
        });
      }
      return unitName;
    };

    return {
      ...toRefs(datas),
      emptyImg,
      formatTestType,
      getNameByid,
      addSample,
      drageHeader,
      closeAddSampleDrawer,
      getAddSampleData,
      editSampleIA,
      deleteSampleIA,
      copySampleInfo,
      filterUnit,
      chenckSampleIA,
      getPermissionBtn,
      filterSampleUnitToName
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.sample-about {
  .sample-about-header {
    margin: 15px 0 15px 0;
    text-align: left;
    .title {
      font-size: 16px;
      font-weight: bold;
      height: 32px;
      line-height: 32px;
      text-align: left;
      display: inline-block;
      margin-right: 16px;
    }
    .el-button {
      float: right;
    }
  }
  .content {
    background: $background-color;
    text-align: left;
    position: relative;
    .sample-about-table {
      margin-bottom: 15px;
    }
  }
}
</style>
