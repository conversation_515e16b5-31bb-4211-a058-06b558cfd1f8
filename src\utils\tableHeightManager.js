/**
 * 表格高度管理工具
 * 用于优化表格高度计算，避免频繁的CSS calc重新计算导致性能问题
 */

import resizeManager from './resizeManager';

class TableHeightManager {
  constructor() {
    this.observers = new Map();
    this.cachedHeights = new Map();
    this.isResizing = false;

    // 使用统一的resize管理器，避免多个resize监听器冲突
    resizeManager.register(
      'tableHeightManager',
      () => {
        this.updateAllTableHeights();
      },
      {
        priority: 1, // 低优先级，在其他重要操作之后执行
        minInterval: 800 // 最小执行间隔800ms，避免频繁更新
      }
    );
  }

  /**
   * 注册表格元素进行高度管理
   * @param {string} id - 表格唯一标识
   * @param {HTMLElement} element - 表格元素
   * @param {Object} options - 配置选项
   */
  registerTable(id, element, options = {}) {
    const config = {
      offsetHeight: options.offsetHeight || 330, // 默认偏移高度
      minHeight: options.minHeight || 200, // 最小高度
      maxHeight: options.maxHeight || null, // 最大高度
      useRem: options.useRem || false, // 是否使用rem单位
      ...options
    };

    this.observers.set(id, {
      element,
      config,
      lastHeight: null
    });

    // 立即计算一次高度
    this.updateTableHeight(id);
  }

  /**
   * 注销表格元素
   * @param {string} id - 表格唯一标识
   */
  unregisterTable(id) {
    this.observers.delete(id);
    this.cachedHeights.delete(id);
  }

  /**
   * 计算表格高度
   * @param {Object} config - 配置选项
   * @returns {string} 计算后的高度值
   */
  calculateHeight(config) {
    const viewportHeight = window.innerHeight;
    let calculatedHeight;

    if (config.useRem) {
      // 获取当前rem值
      const remValue = parseFloat(getComputedStyle(document.documentElement).fontSize);
      const offsetInPx = config.offsetHeight * remValue;
      calculatedHeight = viewportHeight - offsetInPx;
    } else {
      calculatedHeight = viewportHeight - config.offsetHeight;
    }

    // 应用最小和最大高度限制
    if (config.minHeight && calculatedHeight < config.minHeight) {
      calculatedHeight = config.minHeight;
    }

    if (config.maxHeight && calculatedHeight > config.maxHeight) {
      calculatedHeight = config.maxHeight;
    }

    return `${calculatedHeight}px`;
  }

  /**
   * 更新单个表格高度
   * @param {string} id - 表格唯一标识
   */
  updateTableHeight(id) {
    const observer = this.observers.get(id);
    if (!observer) return;

    const { element, config } = observer;
    const newHeight = this.calculateHeight(config);

    // 只有高度发生变化时才更新DOM
    if (observer.lastHeight !== newHeight) {
      try {
        // 查找表格体包装器
        const bodyWrapper = element.querySelector('.el-table__body-wrapper');
        const fixedBodyWrapper = element.querySelector('.el-table__fixed-body-wrapper');

        if (bodyWrapper) {
          bodyWrapper.style.maxHeight = newHeight;
        }

        if (fixedBodyWrapper) {
          fixedBodyWrapper.style.maxHeight = newHeight;
        }

        observer.lastHeight = newHeight;
        this.cachedHeights.set(id, newHeight);
      } catch (error) {
        console.warn(`Failed to update table height for ${id}:`, error);
      }
    }
  }

  /**
   * 更新所有注册的表格高度
   */
  updateAllTableHeights() {
    this.observers.forEach((observer, id) => {
      this.updateTableHeight(id);
    });
  }

  /**
   * 获取缓存的表格高度
   * @param {string} id - 表格唯一标识
   * @returns {string|null} 缓存的高度值
   */
  getCachedHeight(id) {
    return this.cachedHeights.get(id) || null;
  }

  /**
   * 清除所有缓存
   */
  clearCache() {
    this.cachedHeights.clear();
  }

  /**
   * 销毁管理器
   */
  destroy() {
    resizeManager.unregister('tableHeightManager');
    this.observers.clear();
    this.cachedHeights.clear();
  }
}

// 创建全局实例
const tableHeightManager = new TableHeightManager();

export default tableHeightManager;

/**
 * Vue 3 Composition API 钩子
 * @param {string} tableId - 表格唯一标识
 * @param {Object} options - 配置选项
 * @returns {Object} 包含注册和注销函数的对象
 */
export function useTableHeight(tableId, options = {}) {
  const { onMounted, onUnmounted, ref } = require('vue');

  const tableRef = ref(null);

  onMounted(() => {
    if (tableRef.value) {
      tableHeightManager.registerTable(tableId, tableRef.value, options);
    }
  });

  onUnmounted(() => {
    tableHeightManager.unregisterTable(tableId);
  });

  return {
    tableRef,
    updateHeight: () => tableHeightManager.updateTableHeight(tableId),
    getCachedHeight: () => tableHeightManager.getCachedHeight(tableId)
  };
}

/**
 * 手动注册表格（用于非Vue组件）
 * @param {string} tableId - 表格唯一标识
 * @param {HTMLElement} element - 表格元素
 * @param {Object} options - 配置选项
 */
export function registerTable(tableId, element, options = {}) {
  tableHeightManager.registerTable(tableId, element, options);
}

/**
 * 手动注销表格
 * @param {string} tableId - 表格唯一标识
 */
export function unregisterTable(tableId) {
  tableHeightManager.unregisterTable(tableId);
}
