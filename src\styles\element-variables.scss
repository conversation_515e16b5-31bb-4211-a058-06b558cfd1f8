/**
* I think element-plus's default theme color is too light for long-term use.
* So I modified the default color and you can modify it to your liking.
**/

// @use "element-plus/theme-chalk/src/index.scss" as *;
// @forward 'element-plus/theme-chalk/src/common/var.scss' with (
//   $--colors: (
//     'primary': (
//       'base': #00B678,
//     ),
//   ),
// );

// @use "element-plus/theme-chalk/src/index.scss" as *;

$--colors: (
  'white': #ffffff,
  'black': #000000,
  'primary': (
    'base': #00b678
  ),
  'success': (
    'base': #67c23a
  ),
  'warning': (
    'base': #e6a23c
  ),
  'danger': (
    'base': #f56c6c
  ),
  'error': (
    'base': #f56c6c
  ),
  'info': (
    'base': #909399
  )
) !default;
$--color-white: #ffffff !default;

/* theme color */
// $color-primary: #00B678;
$--color-primary: #00b678 !default;
$--color-primary-light-1: mix($--color-white, $--color-primary, 10%) !default; /* 53a8ff */
$--color-primary-light-2: mix($--color-white, $--color-primary, 20%) !default; /* 66b1ff */
$--color-primary-light-3: mix($--color-white, $--color-primary, 30%) !default; /* 79bbff */
$--color-primary-light-4: mix($--color-white, $--color-primary, 40%) !default; /* 8cc5ff */
$--color-primary-light-5: mix($--color-white, $--color-primary, 50%) !default; /* a0cfff */
$--color-primary-light-6: mix($--color-white, $--color-primary, 60%) !default; /* b3d8ff */
$--color-primary-light-7: mix($--color-white, $--color-primary, 70%) !default; /* c6e2ff */
$--color-primary-light-8: mix($--color-white, $--color-primary, 80%) !default; /* d9ecff */
$--color-primary-light-9: mix($--color-white, $--color-primary, 90%) !default; /* ecf5ff */

$--color-success: #67c23a !default;
$--color-warning: #e6a23c !default;
$--color-danger: #f56c6c !default;
$--color-info: #909399 !default;

$--button-font-weight: 400;

// $--color-text-regular: #1f2d3d;

$--border-color-light: #e4e7ed;
$--border-color-lighter: #ebeef5;

$--table-border: 1px solid #dcdfe6;

/* icon font path, required */
// $--font-path: "element-plus/theme-chalk/fonts";

// @import "element-plus/theme-chalk/src/index.scss";
$--font-path: 'element-plus/theme-chalk/fonts';
@import 'element-plus/theme-chalk/src/index.scss';
