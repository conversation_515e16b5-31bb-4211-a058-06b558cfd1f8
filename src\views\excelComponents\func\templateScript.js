const { DEV, VITE_TEMPLATE_EDITOR_ORIGIN } = import.meta.env;
const templateEditorPath = (DEV ? VITE_TEMPLATE_EDITOR_ORIGIN : window.location.origin) + '/attachmentview/tes-wasm';
export function setTemplateScript(scriptRef, baseWasmUrl) {
  // public.js
  // const scriptbox = document.getElementById('scriptbox');
  // if (!scriptbox && publicJs) {
  //   const publicEle = document.createElement('script');
  //   publicEle.setAttribute('id', 'scriptbox');
  //   publicEle.type = 'text/javascript';
  //   publicEle.text = publicJs;
  //   document.head.appendChild(publicEle);
  // }

  // 模板内部代码
  const scriptEle = document.getElementById('script');
  let rawScriptString = '';
  if (scriptEle) {
    rawScriptString = scriptEle.innerHTML.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&');
    const internalEle = document.createElement('script');
    const scriptself = document.getElementsByName('scriptself');
    internalEle.setAttribute('name', 'scriptself');
    if (scriptself[0]) {
      scriptself[0].remove();
    }
    internalEle.type = 'text/javascript';
    internalEle.text = `var baseWasmUrl='${templateEditorPath}'; ${rawScriptString}`;
    if (scriptRef.value) {
      scriptRef.value.appendChild(internalEle);
    }
  }
}

export function setTemplateScript2(scriptRef, baseWasmUrl, publicJs) {
  const mathEl = document.createElement('script');
  const scr = document.getElementsByName('scriptMath');
  mathEl.setAttribute('name', 'scriptMath');
  mathEl.setAttribute('async', true);
  if (scr[0]) {
    scr[0].remove();
  }
  mathEl.type = 'text/javascript';
  mathEl.text = publicJs;
  if (scriptRef.value) {
    scriptRef.value.append(mathEl);
  }

  // 模板内部代码
  const scriptEle = document.getElementById('script');
  let rawScriptString = '';
  if (scriptEle) {
    rawScriptString = scriptEle.innerHTML.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&');
    const internalEle = document.createElement('script');
    const scriptself = document.getElementsByName('scriptself');
    internalEle.setAttribute('name', 'scriptself');
    if (scriptself[0]) {
      scriptself[0].remove();
    }
    internalEle.type = 'text/javascript';
    internalEle.text = `var baseWasmUrl='${templateEditorPath}'; ${rawScriptString}`;
    if (scriptRef.value) {
      scriptRef.value.appendChild(internalEle);
    }
  }
}
