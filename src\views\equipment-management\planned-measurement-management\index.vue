<template>
  <!-- 计量计划管理 -->
  <ListLayout :has-button-group="getPermissionBtn('addMeasurePlan') && currentStatus">
    <template #search-bar>
      <el-form ref="editFromRef" :inline="true" :model="searchForm" @submit.prevent>
        <el-form-item prop="condition">
          <el-input
            v-model="searchForm.condition"
            v-focus
            v-trim
            size="large"
            :placeholder="isShowYc ? '请输入编号/名称/计量单位' : '请输入单号'"
            class="ipt-360"
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter="getTableList"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="large" @click="getTableList" @keyup.prevent @keydown.enter.prevent
            >查询</el-button
          >
          <el-button size="large" @click="reset" @keyup.prevent @keydown.enter.prevent>重置</el-button>
          <el-button type="text" size="large" @click="search" @keyup.prevent @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
          /></el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        type="primary"
        size="large"
        icon="el-icon-plus"
        :disabled="deviceIdList.length === 0"
        @click="handleAddPlan()"
        >新增计划</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="searchFromRef" :model="searchForm" label-width="110px" label-position="right">
            <el-form-item v-if="currentStatus" label="维修标识：" prop="fixedFlag">
              <el-radio-group v-model="searchForm.fixedFlag" size="small" max="1">
                <el-radio-button style="margin-right: 4px">不限</el-radio-button>
                <el-radio-button v-for="type in repairTypes" :key="type" :label="type.code" class="label-type">
                  {{ type.name }}
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-else label="计划状态：" prop="status">
              <el-radio-group v-model="searchForm.status" size="small" max="1">
                <el-radio-button style="margin-right: 4px">不限</el-radio-button>
                <el-radio-button v-for="(val, key) in planJson" :key="key" :label="key" class="label-type">
                  {{ val }}
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="isShowYc ? '截止日期：' : '计划日期：'">
              <el-date-picker
                v-model="plannedDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="changePlannedData"
              />
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-radio-group v-model="currentStatus" size="small" style="float: left" @change="changeStatus">
        <el-radio-button :label="true">计量预测</el-radio-button>
        <el-radio-button :label="false">计量计划</el-radio-button>
      </el-radio-group>
    </template>
    <el-table
      v-show="isShowYc"
      ref="forecastRef"
      :key="tableKey"
      v-loading="tableLoading"
      :data="tableList"
      class="dark-table base-table ycTable format-height-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      fit
      border
      highlight-current-row
      height="auto"
      size="medium"
      @row-click="handleRowClick"
      @header-dragend="drageHeader"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        v-if="getPermissionBtn('addMeasurePlan')"
        type="selection"
        prop="checkbox"
        :width="colWidth.checkbox"
        align="center"
        fixed="left"
      />
      <el-table-column
        label="仪器设备编号"
        prop="deviceNumber"
        :width="colWidth.orderNo"
        align="left"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div>
            <el-tag v-if="row.fixedFlag" size="mini" effect="dark" class="title-status" type="danger">维修</el-tag>
            {{ row.deviceNumber || '--' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="仪器设备名称"
        prop="name"
        :min-width="colWidth.material"
        align="left"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="型号规格" prop="model" align="left" :min-width="colWidth.model">
        <template #default="{ row }">
          <span>{{ row.model || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上次计量日期" prop="measurementDate" :width="colWidth.datetime">
        <template #default="{ row }">
          <span>{{ row.measurementDate ? formatDate(row.measurementDate) : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="上次计量单位"
        prop="org"
        align="left"
        :width="colWidth.departmentUnit"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="nowrap">{{ row.org || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="截止有效期" prop="validEndDate" :width="colWidth.datetime">
        <template #default="{ row }">
          <span>{{ formatDate(row.validEndDate) || '--' }}</span>
          <el-tag v-if="exceedTime(row.validEndDate)" type="warning" size="small"
            >{{ exceedTime(row.validEndDate) }}天</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        label="计量周期"
        prop="measurementCycle"
        align="left"
        :width="colWidth.plate"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="nowrap">{{ row.measurementCycle || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="存放位置" prop="deviceLocation" align="left" :width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.deviceLocation || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="设备状态" prop="status" :width="colWidth.status" align="center">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="equipmentType[row.status]">{{
            equipmentJson[row.status] || '--'
          }}</el-tag>
        </template>
      </el-table-column>
    </el-table>
    <el-table
      v-show="!isShowYc"
      :key="tableKey"
      v-loading="tableLoading"
      :data="tableList"
      class="dark-table base-table format-height-table"
      fit
      border
      height="auto"
      highlight-current-row
      size="medium"
      @header-dragend="drageHeader"
    >
      <el-table-column label="计划单号" prop="planNo" :min-width="colWidth.orderNo" align="left" show-overflow-tooltip>
        <template #default="{ row }">
          <div>
            <div v-if="row.planNo" v-copy="row.planNo" class="blue-color" @click="checkRow(row)">{{ row.planNo }}</div>
            <div v-if="!row.planNo">--</div>
            <el-tag v-if="row.isRemeasured" size="small" class="title-status" type="danger">维修</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="计划日期" prop="startDate" :min-width="colWidth.dateranger">
        <template #default="{ row }">
          <span>{{ row.startDate ? formatDate(row.startDate) : '--' }}</span>
          ~
          <span>{{ row.endDate ? formatDate(row.endDate) : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="计划状态" prop="status" :width="colWidth.status" align="center">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="planType[row.status]">{{ planJson[row.status] || '--' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="进度" prop="progress" align="left" :width="colWidth.status" show-overflow-tooltip>
        <template #header>
          <span
            >进度
            <el-tooltip content="已计量数量/总数量" placement="top" effect="light">
              <i class="iconfont tes-title" />
            </el-tooltip>
          </span>
        </template>
        <template #default="{ row }">
          <div class="nowrap">{{ row.progress || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="计量负责人" prop="responsibleBy" :width="colWidth.people">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.responsibleBy) || row.responsibleBy || '--'" />
        </template>
      </el-table-column>
      <el-table-column
        label="计量单位"
        prop="org"
        align="left"
        :min-width="colWidth.departmentUnit"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="nowrap">{{ row.org || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createBy" :width="colWidth.people">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.createBy) || row.createBy || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="创建日期" prop="createTime" :width="colWidth.datetime">
        <template #default="{ row }">
          <span>{{ row.createTime ? formatDate(row.createTime) : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="colWidth.operationSingle" fixed="right" class-name="fixed-right">
        <template #default="{ row }">
          <span class="blue-color" @click="checkRow(row)">查看</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getTableList"
    />
    <template #other>
      <DialogPlan
        :dialog-visible="dialogVisible"
        :dialog-plan-type="dialogPlanType"
        :device-id-list="deviceIdList"
        @closeDialog="closeDialog"
      />
      <DialogEquipment :dialog-visible="dialogEquipment" :plan-id="planId" @closeDialog="closeEquipment" />
      <DrawerDetail
        :show-drawer="showDrawer"
        :row-type="rowType"
        :plan-id="planId"
        :plan-json="planJson"
        @closeDrawer="closeDrawer"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs } from 'vue';
import UserTag from '@/components/UserTag';
import Pagination from '@/components/Pagination';
import { getPlanList, getPredict } from '@/api/PlannedMeasurementManagement';
import { formatDate } from '@/utils/formatTime';
import DialogEquipment from './DialogEquipment.vue';
import DialogPlan from './DialogPlan.vue';
import DrawerDetail from './DrawerDetail.vue';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import ListLayout from '@/components/ListLayout';
import { colWidth } from '@/data/tableStyle';
import { getDictionary } from '@/api/user';

export default {
  name: 'PlannedMeasurementManagement',
  components: { Pagination, ListLayout, DialogPlan, DialogEquipment, DrawerDetail, UserTag },
  setup() {
    // const { proxy } = getCurrentInstance()
    const state = reactive({
      tableList: [],
      forecastRef: ref(),
      mangeList: [],
      isShowYc: true,
      currentStatus: true,
      dialogVisible: false,
      dialogEquipment: false,
      showDrawer: false,
      planId: '', // 计量计划id
      rowType: '', // 计量计划抽屉类型
      dialogPlanType: '', // 计量计划弹出窗
      total: 0,
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ],
      deviceIdList: [], // 设备id列表
      plannedDateRange: [],
      editFromRef: ref(),
      searchFromRef: ref(),
      activeName: '0',
      showS: false,
      tableLoading: false,
      tableKey: 0,
      listQuery: {
        page: 1,
        limit: 20
      },
      searchForm: {},
      repairTypes: [
        { code: true, name: '有' },
        { code: false, name: '无' }
      ],
      planJson: {}, // 计量计划状态
      planType: {
        // 计量计划状态
        Finished: 'success',
        Unfinished: 'warning',
        Uncommitted: 'info'
      },
      equipmentType: {
        Running: 'success',
        Standby: 'warning',
        Maintenance: 'default',
        Fault: 'danger',
        Scrapped: 'info'
      },
      equipmentJson: {}
    });
    const handleRowClick = row => {
      state.forecastRef.toggleRowSelection(
        row,
        !state.deviceIdList.some(item => {
          return row.id === item;
        })
      );
    };
    // 重置
    function reset() {
      state.listQuery.page = 1;
      state.listQuery.limit = 20;
      state.searchForm = {};
      state.plannedDateRange = [];
      state.deviceIdList = [];
      getTableList();
    }
    // 点击高级搜索
    const search = () => {
      state.showS = !state.showS;
      if (state.activeName === '0') {
        state.activeName = '1';
      } else {
        state.activeName = '0';
      }
    };
    // 列表查询
    const getTableList = query => {
      const params = { ...state.searchForm };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.tableLoading = true;
      if (state.currentStatus) {
        getPredict(params).then(res => {
          state.tableLoading = false;
          if (res !== false) {
            state.total = res.data.data.totalCount;
            state.tableList = res.data.data.list;
          }
        });
      } else {
        getPlanList(params).then(res => {
          state.tableLoading = false;
          if (res !== false) {
            state.total = res.data.data.totalCount;
            state.tableList = res.data.data.list;
          }
        });
      }
    };
    getTableList();
    // 关闭弹出窗
    const closeDialog = val => {
      state.dialogVisible = false;
      if (val.isRefresh) {
        getTableList(state.listQuery);
      }
    };
    const closeEquipment = val => {
      state.dialogEquipment = false;
      if (val.isRefresh) {
        getTableList(state.listQuery);
      }
    };
    const closeDrawer = val => {
      state.showDrawer = false;
      if (val.isRefresh) {
        getTableList(state.listQuery);
      }
    };
    // 列表选择
    const handleSelectionChange = val => {
      state.deviceIdList = val.map(item => {
        return item.id;
      });
    };
    // 新增计划
    const handleAddPlan = () => {
      state.dialogVisible = true;
      state.dialogPlanType = 'add';
    };
    // 高级搜索-计划日期-change
    const changePlannedData = date => {
      state.searchForm.startTime = date ? formatDate(date[0]) : '';
      state.searchForm.endTime = date ? formatDate(date[1]) : '';
    };
    // 切换tab
    const changeStatus = val => {
      state.isShowYc = val;
      state.listQuery = {
        page: 1,
        limit: 20
      };
      state.total = 0;
      state.plannedDateRange = [];
      state.searchForm.startTime = '';
      state.searchForm.endTime = '';
      getTableList();
    };
    // 获取设备状态字典
    const getPlanStatus = () => {
      getDictionary('JSJHZT').then(res => {
        if (res !== false) {
          res.data.data.dictionaryoption.forEach(item => {
            state.planJson[item.code] = item.name;
          });
        }
      });
      getDictionary(24).then(res => {
        if (res) {
          res.data.data.dictionaryoption.forEach(item => {
            state.equipmentJson[item.code] = item.name;
          });
        }
      });
    };
    getPlanStatus();
    // 计量计划->查看
    const checkRow = row => {
      state.showDrawer = true;
      state.rowType = row.status;
      state.planId = row.id;
    };
    // 计算还有多少天超期
    const exceedTime = time => {
      if (time) {
        const nowDays = new Date();
        const endTime = new Date(time);
        var residueDays;
        residueDays = Math.floor((endTime - nowDays) / (24 * 3600 * 1000));
        if (residueDays <= 30 && residueDays > 0) {
          return residueDays;
        }
      }
    };

    return {
      ...toRefs(state),
      handleRowClick,
      getPermissionBtn,
      exceedTime,
      drageHeader,
      getNameByid,
      changePlannedData,
      formatDate,
      handleAddPlan,
      closeDialog,
      closeEquipment,
      closeDrawer,
      handleSelectionChange,
      getTableList,
      search,
      checkRow,
      reset,
      colWidth,
      changeStatus,
      getPlanStatus
    };
  },
  computed: {}
};
</script>
<style lang="scss" scoped>
.btn-mg20 {
  margin-right: 20px;
}

.label-type {
  margin-bottom: 0px;
  // min-width: 200px;
}
</style>
