<template>
  <el-dialog
    v-model="showDialog"
    custom-class="custom-dialog"
    title="选择物料"
    width="60%"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <div class="dialog-header">
      <CombinationQuery
        :field-list="materialFieldList"
        field-tip="物料编号/物料名称"
        @get-single-text="getSingleText"
        @get-param-list="getParamList"
        @reset-search="reset"
      />
    </div>
    <div class="dialog-content">
      <el-row>
        <el-col :span="7">
          <div class="tree-container">
            <div class="tree-content">
              <el-tree
                ref="materialTreeRef"
                :data="tree"
                node-key="no"
                :props="defaultProps"
                default-expand-all
                :expand-on-click-node="false"
                :highlight-current="true"
                draggable
                class="leftTree"
                :current-node-key="currentNodeKey"
                @node-click="clickNode"
              >
                <template #default="{ node }">
                  <span :title="node.label">{{ node.label }}</span>
                </template>
              </el-tree>
            </div>
          </div>
        </el-col>
        <el-col :span="17">
          <div class="list-container">
            <el-table
              ref="singleTableRef"
              v-loading="loading"
              :data="newTreeDetail"
              highlight-current-row
              fit
              border
              class="dark-table base-table"
              height="91%"
              @current-change="changeRadio"
            >
              <el-table-column width="40">
                <template #default="{ row }">
                  <el-radio v-model="radioId" :label="row.id" :disabled="row.disable" @change="changeRadio(row)"
                    >&nbsp;&nbsp;&nbsp;</el-radio
                  >
                </template>
              </el-table-column>

              <template v-for="(item, index) in materialFieldList" :key="index">
                <el-table-column
                  v-if="item.checkbox && item.isHide !== 1"
                  :prop="item.field"
                  :label="item.name"
                  :sortable="Number(item.isOrder) === 1"
                  :width="item.isMinWidth ? '' : getColWidth(item.colWidth)"
                  :min-width="item.isMinWidth ? getColWidth(item.colWidth) : ''"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <div>
                      <!-- 默认字段显示 -->
                      {{ row[item.field] || '--' }}
                    </div>
                  </template>
                </el-table-column>
              </template>
            </el-table>

            <pagination :page="page" :limit="limit" :total="total" @pagination="changePage" />
          </div>
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent>确定选择</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, nextTick } from 'vue';
import { getWlItemNew } from '@/api/mas';
import { ElMessage } from 'element-plus';
import Pagination from '@/components/Pagination';
import { materialFieldList } from '../func/materialInfo';
import { getColWidth } from '@/utils/func/customTable';
import CombinationQuery from '@/components/CombinationQuery';

export default {
  name: 'AddMaterial',
  components: { Pagination, CombinationQuery },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    tree: {
      type: Object,
      default: function () {
        return {};
      }
    },
    materialNo: {
      type: String,
      default: ''
    }
  },
  emits: ['close', 'selectData'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    // const lodash = inject('_')
    const datas = reactive({
      showDialog: false,
      filterText: '',
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      singleTableRef: ref(),
      treeDetail: [],
      newTreeDetail: [],
      loading: false,
      radioId: '',
      radioData: null,
      materialTreeRef: ref(),
      limit: 20,
      page: 1,
      total: 0,
      currentNodeKey: '',
      groupNo: '',
      tableInfo: {
        tableHeader: materialFieldList,
        tableData: []
      },
      queryFieldList: [],
      queryField: '',
      queryText: '',
      isCombinationQuery: false
    });

    const changePage = value => {
      datas.limit = value.limit;
      datas.page = value.page;
      queryList();
    };

    watch(
      () => props.show,
      newValue => {
        if (newValue) {
          // console.log(props)
          datas.showDialog = newValue;
          datas.radioData = null;
          datas.radioId = '';
          if (props.show && props.tree && props.tree.length > 0) {
            datas.groupNo = props.tree[0].no;
            queryList();
            datas.currentNodeKey = props.tree[0].no;
            nextTick(() => {
              datas.materialTreeRef.setCurrentKey(datas.currentNodeKey);
            });
          }
        }
      },
      { deep: true }
    );

    // 过滤树节点
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    // 点击树节点
    const clickNode = (data, node) => {
      datas.groupNo = data.no;
      datas.page = 1;
      queryList();
    };

    // 确定选择
    const dialogSuccess = () => {
      if (!datas.radioData) {
        ElMessage.warning('请选择物料');
        return false;
      }
      datas.showDialog = false;
      context.emit('selectData', datas.radioData);
      // context.emit('close', 'add')
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };

    // 选择检测项目-radio
    const changeRadio = item => {
      if (item) {
        if (item.disable === true) {
          return false;
        }
        datas.radioId = item.id;
        datas.radioData = item;
      }
    };

    // 获取检测项目list
    const getMaterialList = (groupNo, queryParamList = []) => {
      // 获取检测项目list
      datas.loading = true;
      const defaultParams = {
        param: datas.filterText || '',
        limit: datas.limit.toString(),
        page: datas.page.toString(),
        groupNo: groupNo || '',
        tableQueryParamList: queryParamList
      };
      getWlItemNew(defaultParams).then(response => {
        datas.loading = false;
        if (response) {
          // const { list } = response.data.data
          const result = response.data.data;

          datas.treeDetail = result.list;
          datas.tableInfo.tableData = result.list;
          datas.limit = result.pageSize;
          datas.page = result.currPage;
          datas.total = result.totalCount;
          datas.newTreeDetail = result.list;
          if (props.materialNo && result.list.length > 0) {
            result.list.forEach(ml => {
              if (ml.no === props.materialNo) {
                datas.radioData = ml;
                datas.radioId = ml.id;
              }
            });
          }
        }
      });
    };

    // 查询
    const queryList = () => {
      if (datas.isCombinationQuery) {
        const tableQueryParamList = [];
        if (datas.queryField && datas.queryText) {
          tableQueryParamList.push({
            tableName: datas.queryField,
            tableValue: datas.queryText
          });
        }
        getMaterialList(datas.groupNo, tableQueryParamList);
      } else {
        getMaterialList(datas.groupNo);
      }
    };

    const switchQueryMode = () => {
      datas.isCombinationQuery = !datas.isCombinationQuery;
      if (datas.isCombinationQuery) {
        datas.filterText = '';
      } else {
        datas.queryFieldList = [];
        datas.queryField = '';
        datas.queryText = '';
      }
    };

    const getSingleText = val => {
      datas.filterText = val;
      getMaterialList(datas.groupNo);
    };

    const getParamList = paramList => {
      datas.filterText = '';
      getMaterialList(datas.groupNo, paramList);
    };

    const reset = val => {
      datas.filterText = '';
      getMaterialList(datas.groupNo);
    };

    return {
      ...toRefs(datas),
      queryList,
      dialogSuccess,
      close,
      filterNode,
      clickNode,
      changeRadio,
      changePage,
      getColWidth,
      materialFieldList,
      switchQueryMode,
      getSingleText,
      getParamList,
      reset
    };
  }
};
</script>
<style lang="scss">
@import '@/styles/dialog.scss';
</style>
<style lang="scss" scoped>
@import '@/styles/tree.scss';

.dialog-content {
  margin-bottom: 0;
}
.tree-container {
  margin-bottom: 20px;
  .tree-content {
    height: calc(100vh - 437px);
    overflow-y: auto;
  }
}
.list-container {
  padding-bottom: 0;
  margin-bottom: 20px;
  height: calc(100vh - 397px);
  overflow: hidden;
  .dark-table {
    :deep(.el-table__body-wrapper) {
      max-height: calc(100vh - 480px);
      overflow-y: auto;
      overflow-x: hidden !important;
    }
  }
}

.search-container {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex: 1 1 5rem;
  .switch-wrapper {
    width: 9rem;
    margin-right: 0.5rem;
  }
  .default-search-wrapper {
    flex: 5;
  }
  .combination-wrapper {
    flex: 5;
    width: 100%;
    display: flex;
    flex-direction: row;
    .field-input,
    .value-input {
      width: 10rem;
    }

    .add-field {
      width: 5rem;
    }
    .combination-search-wrapper {
      flex: 5;
    }
  }
  .query-btn {
    width: 5rem;
  }
}
</style>
