import request from '@/utils/request';

// 老化箱二分列表（看板）
export function sampleSplitList(data) {
  return request({
    url: '/api-device/device/deviceBox/sample/splitList',
    method: 'post',
    data
  });
}

// 老化箱设备-所有(可用于看板)
export function deviceBoxAllBox(data) {
  return request({
    url: '/api-device/device/deviceBox/allBoxList',
    method: 'post',
    data
  });
}

// 老化箱-批量记录看板状态
export function upBoxBoardStatusBatch(data) {
  return request({
    url: '/api-device/device/deviceBox/upBoxBoardStatusBatch',
    method: 'post',
    data
  });
}

// 老化箱列表-温度信息(看板)
export function boxListRdsData(data) {
  return request({
    url: '/api-device/device/deviceBox/boxListRdsData',
    method: 'post',
    data
  });
}
