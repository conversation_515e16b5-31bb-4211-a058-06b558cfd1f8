<template>
  <!-- 基本信息 -->
  <DetailLayout :has-page-header="false" :has-quick-query="false">
    <div class="left-info">
      <div class="title no-select"><span class="line-space no-select" />基本信息维护</div>
      <div class="base-info-wrapper">
        <el-form
          ref="baseInfoRef"
          label-position="right"
          :rules="rules"
          label-width="110px"
          :model="baseInfoForm"
          class="info-form no-select"
        >
          <el-form-item label="用户名：" prop="username" class="no-select">
            <span class="no-select">{{ baseInfoForm.username }}</span>
          </el-form-item>
          <el-form-item
            v-if="isEditNickName"
            label="姓名："
            class="no-select"
            prop="nickname"
            :rules="{ required: true, message: '请输入姓名', trigger: 'change' }"
          >
            <el-input
              ref="nickNameRef"
              v-model="baseInfoForm.nickname"
              v-trim
              class="no-select"
              maxlength="18"
              size="small"
            />
            <el-button class="no-select" size="small" @click="cancle('nickname')">取消</el-button>
            <el-button class="no-select" type="primary" size="small" @click="save('nickname')">保存</el-button>
          </el-form-item>
          <el-form-item v-else label="姓名：" class="no-select" prop="nickname">
            <span class="no-select">{{ baseInfoForm.nickname }}</span>
            <el-button class="no-select" circle icon="el-icon-edit" size="small" @click="handleEdit('nickname')" />
          </el-form-item>
          <el-form-item label="手机：" prop="mobile" class="no-select">
            <span v-if="!isEditMobile">{{ baseInfoForm.mobile }}</span>
            <el-input v-else ref="mobileRef" v-model="baseInfoForm.mobile" v-trim class="no-select" size="small" />
            <el-button
              v-show="!isEditMobile"
              class="no-select"
              circle
              icon="el-icon-edit"
              size="small"
              @click="handleEdit('mobile')"
            />
            <el-button v-show="isEditMobile" class="no-select" size="small" @click="cancle('mobile')">取消</el-button>
            <el-button v-show="isEditMobile" class="no-select" type="primary" size="small" @click="save('mobile')"
              >保存</el-button
            >
          </el-form-item>
          <el-form-item label="邮箱：" prop="email" class="no-select">
            <span v-if="!isEditEmail" class="no-select">{{ baseInfoForm.email }}</span>
            <el-input v-else ref="emailRef" v-model="baseInfoForm.email" class="no-select" size="small" />
            <el-button
              v-show="!isEditEmail"
              class="no-select"
              circle
              icon="el-icon-edit"
              size="small"
              @click="handleEdit('email')"
            />
            <el-button v-show="isEditEmail" class="no-select" size="small" @click="cancle('email')">取消</el-button>
            <el-button v-show="isEditEmail" class="no-select" type="primary" size="small" @click="save('email')"
              >保存</el-button
            >
          </el-form-item>
          <el-form-item label="在线签名：" prop="headImgUrl" class="no-select">
            <el-button
              class="sign-btn no-select"
              size="small"
              icon="el-icon-edit"
              circle
              @click="signatureVisible = true"
              @keyup.prevent
              @keydown.enter.prevent
            />
          </el-form-item>
          <el-form-item label="" prop="headImgUrl" class="no-select">
            <img
              class="sign-preview no-select"
              :src="baseInfoForm.headImgUrl"
              alt=""
              srcset=""
              oncontextmenu="return false"
              referrerPolicy="no-referrer"
            />
          </el-form-item>
          <el-form-item v-if="checkServerIp()" label="第三方账号绑定：" label-width="130px" class="no-select">
            <img src="@/assets/img/DingTalk.png" alt="钉钉登录" class="DingTalkImg no-select" @click="loginForDT" />
            <div v-if="dingTalkInfo?.socialUsername" class="blue-color DingTalk no-select">
              {{ dingTalkInfo?.socialUsername }}
              <span class="blue-color DingTalk no-select" @click="removingDingTalk">解除绑定</span>
            </div>
            <span v-else class="blue-color DingTalk no-select" @click="loginForDT">绑定钉钉</span>
            <!-- 微信登陆 -->
            <img src="@/assets/img/wechat.png" alt="微信登录" @click="loginForWX" />
            <div v-if="weChatInfo?.unionId" class="blue-color DingTalk no-select">
              <span>已绑定</span>
              <span class="blue-color DingTalk no-select" @click="removingWeChat">解除绑定</span>
            </div>
            <span v-else class="blue-color DingTalk no-select" @click="loginForWX">绑定微信</span>
          </el-form-item>
          <el-form-item label="登录二次验证：" label-width="130px" class="no-select">
            <el-switch
              v-model="loginAuthenticator"
              v-loading="loginAuthenticatorLoading"
              @change="handleLoginAuthenticatorChange"
            />
            <img v-if="loginAuthenticator" class="QR-code-image" :src="authenticatorQRCodeImage" alt="" />
          </el-form-item>
        </el-form>
      </div>
    </div>
  </DetailLayout>
  <code-editor :show="showCodeEditor" @close="closeCodeEditor" @set-data="getCodeEditor" />
  <DialogDingTalk :dialog="dialogDT" :goto-url="gotoUrl" @closeDialog="closeDialog" />
  <DialogWeChat :dialog="dialogWX" :page-type="'baseInfo'" @closeDialog="closeDialogWX" />
  <signature-dialog
    ref="signCanvas1"
    :dialog-visible="signatureVisible"
    @sign-img="saveSignImg"
    @close-sign-dialog="closeSignatureDialog"
  />
</template>
<script>
import { ref, reactive, toRefs, nextTick, watch } from 'vue';
import { getNameByid } from '@/utils/common';
import { getLoginInfo } from '@/utils/auth';
import { getQrCodeApi, getBandApi, dingTalkBindApi, getDingTalkApi, removeDTApi, removeWXApi } from '@/api/sysConfig';
import { useRouter, useRoute } from 'vue-router';
import Cookies from 'js-cookie';
// import _ from 'lodash'
// import { checkPermissionList } from '@/api/permission'
// import { permissionTypeList } from '@/utils/permissionList'
import CodeEditor from '@/components/CodeEditor/index1.vue';
import DetailLayout from '@/components/DetailLayout';
import {
  getuserBaseInfo,
  SysuserInfoSaveOrUpdate,
  uploadSysuser,
  isEnabledSecretAuth,
  enableSecretAuth,
  getQRCodeImage
} from '@/api/userInfo';
import { ElMessage } from 'element-plus';
import { isEmail2, isMobile2 } from '@/utils/validate';
import SignatureDialog from '@/components/SignatureDialog/index.vue';
import DialogDingTalk from '@/components/ThirdParty/DialogDingTalk.vue';
import DialogWeChat from '@/components/ThirdParty/DialogWeChat.vue';
import { checkServerIp } from '@/utils/server';

export default {
  name: 'BaseInfo',
  components: { CodeEditor, DetailLayout, SignatureDialog, DialogDingTalk, DialogWeChat },
  setup() {
    const router = useRouter();
    const route = useRoute();
    // const { proxy } = getCurrentInstance()
    const datas = reactive({
      accountId: getLoginInfo().accountId,
      gotoUrl: '',
      dialogDT: false,
      dialogWX: false,
      dingTalkInfo: {
        socialUsername: ''
      }, // 钉钉信息
      weChatInfo: {},
      baseInfoForm: {
        username: getLoginInfo().username,
        name: getNameByid(getLoginInfo().accountId),
        mobile: '',
        email: '',
        headImgUrl: '',
        attachmentRefId: ''
      },
      isEditNickName: false, // 是否能修改姓名
      nickNameRef: ref(),
      mobileRef: ref(),
      emailRef: ref(),
      isEditMobile: false,
      isEditEmail: false,
      oldBaseInfoForm: null,
      isEdit: false,
      imgRef: ref(),
      showCodeEditor: false,
      codeEditorData: '',
      baseInfoRef: ref(),
      rules: {
        email: [{ validator: isEmail2, message: '请输入正确的邮箱地址', tigger: 'blur' }],
        mobile: [{ validator: isMobile2, message: '请输入正确的手机号', tigger: 'blur' }]
      },
      signatureVisible: ref(false),
      loginAuthenticator: false,
      loginAuthenticatorLoading: false,
      authenticatorQRCodeImage: ''
    });
    watch(
      () => router.currentRoute.value.path,
      (newValue, oldValue) => {
        nextTick(() => {
          if (route.query.code && checkServerIp()) {
            getIsBand(route.query);
          }
        });
      },
      { immediate: true }
    );
    const getData = () => {
      getuserBaseInfo(datas.accountId).then(res => {
        if (res) {
          datas.baseInfoForm = res.data.data;
          datas.oldBaseInfoForm = JSON.parse(JSON.stringify(datas.baseInfoForm));
          if (import.meta.env.DEV) {
            datas.baseInfoForm.headImgUrl = res.data.data?.headImgUrl?.replace(window.location.host, '*************');
          }
        }
      });
      isEnabledSecretAuth({ username: datas.baseInfoForm.username }).then(res => {
        if (res && res.data.data) {
          datas.loginAuthenticator = true;
          setAuthenticatorQRCodeImage();
        } else {
          datas.loginAuthenticator = false;
        }
      });
    };
    getData();
    const getIsBand = query => {
      getBandApi(query).then(res => {
        if (res) {
          const data = res.data.data;
          if (!data.isBind) {
            handleBind(data);
          }
        }
      });
    };
    const getThirdParty = () => {
      getDingTalkApi({ username: JSON.parse(Cookies.get('loginInfo')).username }).then(res => {
        if (res) {
          const data = res.data.data;
          datas.dingTalkInfo = data.filter(item => {
            return item.source === 'DINGTALK';
          })[0];
          datas.weChatInfo = data.filter(item => {
            return item.source === 'WECHAT_MP';
          })[0];
        }
      });
    };
    nextTick(() => {
      if (checkServerIp()) {
        getThirdParty();
      }
    });
    const handleBind = data => {
      dingTalkBindApi({
        unionId: data.unionId,
        username: JSON.parse(Cookies.get('loginInfo')).username,
        socialId: Number(data.id)
      }).then(res => {
        if (res) {
          ElMessage.success('绑定成功');
          router.push({
            path: '/user-manage/base-info'
          });
          getThirdParty();
        }
      });
    };
    const setAuthenticatorQRCodeImage = () => {
      getQRCodeImage().then(res => {
        console.log('getQRCodeImage res', res);
        if (res && res?.data?.data?.length > 0) {
          datas.authenticatorQRCodeImage = res.data.data;
        }
      });
    };
    // 保存
    const save = flag => {
      const editFlag = {
        nickname: 'isEditNickName',
        mobile: 'isEditMobile',
        email: 'isEditEmail'
      };
      const editType = editFlag[flag];
      // datas.oldBaseInfoForm = JSON.parse(JSON.stringify(datas.baseInfoForm))
      datas.baseInfoRef.validate().then(valid => {
        if (valid) {
          SysuserInfoSaveOrUpdate(datas.baseInfoForm).then(res => {
            if (res) {
              getData();
              datas[editType] = false;
              ElMessage.success('操作成功');
            }
          });
        }
      });
    };
    // 取消
    const cancle = flag => {
      if (flag === 'nickname') {
        datas.isEditNickName = false;
      } else if (flag === 'mobile') {
        datas.isEditMobile = false;
      } else {
        datas.isEditEmail = false;
      }
      datas.baseInfoForm[flag] = datas.oldBaseInfoForm[flag];
    };
    // 更新电子签名
    const changeName = () => {
      // console.log('changeName')
      datas.imgRef.click();
    };
    // 选择图片
    const handleClick = e => {
      const files = e.target.files[0];
      // console.log(files)
      if (!files) {
        return false;
      }
      const params = new FormData();
      params.append('files', files);
      if (files.size / 1024 > 20) {
        ElMessage.error('大小不能超过20kB');
        return false;
      }
      saveSignFile(params);
    };
    const readerData = rawFile => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => {
          const data = e.target.result;
          // console.log(data)
          // const param = { imageUrl: '', checked: true }
          // param.imageUrl = data
          datas.baseInfoForm.imgUrl = data;
          resolve(data);
        };
        reader.readAsDataURL(rawFile);
      });
    };
    // 删除签名
    const deleteImg = () => {
      const posdata = JSON.parse(JSON.stringify(datas.oldBaseInfoForm));
      posdata.headImgUrl = '';
      SysuserInfoSaveOrUpdate(posdata).then(resP => {
        if (resP.data.code === 200) {
          getData();
          ElMessage.success('删除成功');
        }
      });
    };

    const saveSignImg = data => {
      const array = `${data}`.split(',');
      const mime = array[0].match(/:(.*?);/)[1];
      const bstr = window.atob(array[1]);
      let n = bstr.length;
      const u8Array = new Uint8Array(n);
      while (n--) {
        u8Array[n] = bstr.charCodeAt(n);
      }
      const blob = new File([u8Array], 'sign.png', { type: mime });
      const params = new FormData();
      params.append('files', blob);
      saveSignFile(params);
    };
    const saveSignFile = params => {
      uploadSysuser(params, callback => {}).then(res => {
        if (res !== false) {
          const posdata = JSON.parse(JSON.stringify(datas.oldBaseInfoForm));
          posdata.headImgUrl = res.data.data.url;
          posdata.attachmentRefId = res.data.data.id;
          SysuserInfoSaveOrUpdate(posdata).then(resP => {
            if (resP.data.code === 200) {
              getData();
              ElMessage.success('保存签名成功');
              datas.signatureVisible = false;
            }
          });
        } else {
          return false;
        }
      });
    };
    // 返回重置
    const handleReset = () => {
      datas.baseInfoForm = JSON.parse(JSON.stringify(datas.oldBaseInfoForm));
    };
    const closeSignatureDialog = result => {
      datas.signatureVisible = result;
    };

    // code编辑器测试
    const openCodeEditor = () => {
      datas.showCodeEditor = true;
    };
    const closeCodeEditor = () => {
      datas.showCodeEditor = false;
    };
    const getCodeEditor = value => {
      datas.codeEditorData = value;
      datas.showCodeEditor = false;
      // console.log(value)
    };
    const handleEdit = type => {
      if (type === 'nickname') {
        datas.isEditNickName = !datas.isEditNickName;
        if (datas.isEditNickName) {
          nextTick(() => {
            datas.nickNameRef.focus();
          });
        }
      } else if (type === 'mobile') {
        datas.isEditMobile = !datas.isEditMobile;
        if (datas.isEditMobile) {
          nextTick(() => {
            datas.mobileRef.focus();
          });
        }
      } else {
        datas.isEditEmail = !datas.isEditEmail;
        if (datas.isEditEmail) {
          nextTick(() => {
            datas.emailRef.focus();
          });
        }
      }
    };
    const loginForDT = () => {
      if (!datas.dingTalkInfo?.socialUsername) {
        getQrCodeApi('baseInfo').then(res => {
          datas.gotoUrl = res.data.data;
          datas.dialogDT = true;
        });
      }
    };
    const loginForWX = () => {
      datas.dialogWX = true;
    };
    const closeDialog = () => {
      datas.dialogDT = false;
    };
    const closeDialogWX = info => {
      datas.dialogWX = false;
      if (info.isRefreshLogin) {
        getThirdParty();
      }
    };
    const getQrCode = () => {};
    const removingDingTalk = () => {
      removeDTApi(datas.dingTalkInfo).then(res => {
        if (res) {
          ElMessage.success('解除绑定成功！');
          getThirdParty();
        }
      });
    };
    const removingWeChat = () => {
      removeWXApi({ socialId: datas.weChatInfo.socialId }).then(res => {
        if (res) {
          ElMessage.success('解除绑定成功！');
          getThirdParty();
        }
      });
    };
    const handleLoginAuthenticatorChange = value => {
      datas.loginAuthenticatorLoading = true;
      enableSecretAuth({ isEnabled: value ? 1 : 0 }).then(res => {
        if (res && res?.data?.data?.length > 0) {
          setAuthenticatorQRCodeImage();
        }
        datas.loginAuthenticatorLoading = false;
      });
    };
    return {
      ...toRefs(datas),
      handleReset,
      removingDingTalk,
      removingWeChat,
      getThirdParty,
      getIsBand,
      getQrCode,
      closeDialog,
      closeDialogWX,
      loginForDT,
      loginForWX,
      handleEdit,
      setAuthenticatorQRCodeImage,
      save,
      cancle,
      changeName,
      getData,
      handleClick,
      readerData,
      deleteImg,
      openCodeEditor,
      closeCodeEditor,
      getCodeEditor,
      saveSignImg,
      closeSignatureDialog,
      checkServerIp,
      handleLoginAuthenticatorChange
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.DingTalk {
  display: inline-block;
  height: 100%;
  vertical-align: top;
  padding-left: 10px;
}
.left-info {
  background: $background-color;
  padding: 20px;
  // height: calc(100vh - 128px);

  .title {
    display: flex;
    align-items: center;
    font-size: 16px;
    line-height: 24px;
    color: $tes-font;
    // margin-bottom: 20px;
    .line-space {
      width: 4px;
      height: 16px;
      margin-right: 10px;
      background: $tes-primary;
    }
  }

  .base-info-wrapper {
    height: calc(100vh - 155px);
    overflow-y: auto;
  }
  .info-form {
    width: 600px;
    margin: 0 auto;
    // max-height: calc(100vh - 220px);
    :deep(.el-input) {
      width: 70%;
    }
    :deep(.el-form-item__content) {
      text-align: left;
    }
    .el-button {
      margin-left: 10px;
      &.is-circle {
        border: none;
      }
    }
    .el-button.sign-btn {
      margin-left: 0;
    }
    .sign-preview {
      width: calc(100% - 80px);
      border-radius: 10px;
      box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    }
  }
}
.right-info {
  padding: 20px;
}

.QR-code-image {
  display: block;
  max-width: 180px;
}
</style>
