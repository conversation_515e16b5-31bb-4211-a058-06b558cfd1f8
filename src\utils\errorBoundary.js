/**
 * 错误边界处理器
 * 用于捕获和处理可能导致页面崩溃的错误
 */

class ErrorBoundary {
  constructor() {
    this.errorCount = 0;
    this.maxErrors = 10; // 最大错误数量
    this.errorResetTime = 60000; // 错误计数重置时间（1分钟）
    this.lastErrorTime = 0;
    this.isRecovering = false;

    this.setupGlobalErrorHandlers();
  }

  /**
   * 设置全局错误处理器
   */
  setupGlobalErrorHandlers() {
    // 捕获JavaScript错误
    // window.addEventListener('error', event => {
    //   this.handleError({
    //     type: 'javascript',
    //     message: event.message,
    //     filename: event.filename,
    //     lineno: event.lineno,
    //     colno: event.colno,
    //     error: event.error,
    //     stack: event.error?.stack
    //   });
    // });
    // 捕获Promise未处理的拒绝
    // window.addEventListener('unhandledrejection', event => {
    //   this.handleError({
    //     type: 'promise',
    //     message: event.reason?.message || 'Unhandled Promise Rejection',
    //     reason: event.reason,
    //     stack: event.reason?.stack
    //   });
    // });
    // 捕获资源加载错误
    // window.addEventListener(
    //   'error',
    //   event => {
    //     if (event.target !== window) {
    //       this.handleError({
    //         type: 'resource',
    //         message: `Failed to load resource: ${event.target.src || event.target.href}`,
    //         element: event.target.tagName,
    //         source: event.target.src || event.target.href
    //       });
    //     }
    //   },
    //   true
    // );
  }

  /**
   * 处理错误
   */
  handleError(errorInfo) {
    const currentTime = Date.now();

    // 重置错误计数（如果超过重置时间）
    if (currentTime - this.lastErrorTime > this.errorResetTime) {
      this.errorCount = 0;
    }

    this.errorCount++;
    this.lastErrorTime = currentTime;

    // 记录错误
    // this.logError(errorInfo);

    // 检查是否需要采取恢复措施
    if (this.errorCount >= this.maxErrors && !this.isRecovering) {
      this.attemptRecovery();
    }
  }

  /**
   * 记录错误
   */
  logError(errorInfo) {
    console.group('🚨 Error Boundary Caught Error');
    console.error('Type:', errorInfo.type);
    console.error('Message:', errorInfo.message);

    if (errorInfo.filename) {
      console.error('File:', errorInfo.filename);
      console.error('Line:', errorInfo.lineno);
      console.error('Column:', errorInfo.colno);
    }

    if (errorInfo.stack) {
      console.error('Stack:', errorInfo.stack);
    }

    if (errorInfo.element) {
      console.error('Element:', errorInfo.element);
      console.error('Source:', errorInfo.source);
    }

    console.error('Error Count:', this.errorCount);
    console.groupEnd();

    // 发送错误报告（如果需要）
    this.reportError(errorInfo);
  }

  /**
   * 尝试恢复
   */
  attemptRecovery() {
    if (this.isRecovering) return;

    this.isRecovering = true;
    console.warn('🔧 Attempting error recovery...');

    try {
      // 清理可能的内存泄漏
      this.cleanupMemoryLeaks();

      // 重置一些全局状态
      this.resetGlobalState();

      // 显示用户友好的错误提示
      this.showUserFriendlyError();

      // 延迟重置恢复状态
      setTimeout(() => {
        this.isRecovering = false;
        this.errorCount = 0;
        console.log('✅ Error recovery completed');
      }, 5000);
    } catch (recoveryError) {
      console.error('❌ Error recovery failed:', recoveryError);
      this.isRecovering = false;
    }
  }

  /**
   * 清理内存泄漏
   */
  cleanupMemoryLeaks() {
    // 清理可能的定时器
    const highestTimeoutId = setTimeout(() => {}, 0);
    for (let i = 0; i < highestTimeoutId; i++) {
      clearTimeout(i);
    }

    const highestIntervalId = setInterval(() => {}, 0);
    clearInterval(highestIntervalId);
    for (let i = 0; i < highestIntervalId; i++) {
      clearInterval(i);
    }

    // 强制垃圾回收（如果可用）
    if (window.gc) {
      try {
        window.gc();
      } catch (error) {
        // 忽略错误
      }
    }
  }

  /**
   * 重置全局状态
   */
  resetGlobalState() {
    // 清理可能的事件监听器
    try {
      // 移除可能导致问题的resize监听器
      const resizeListeners = window.getEventListeners?.('resize') || [];
      resizeListeners.forEach(listener => {
        window.removeEventListener('resize', listener.listener);
      });
    } catch (error) {
      // 忽略错误
    }
  }

  /**
   * 显示用户友好的错误提示
   */
  showUserFriendlyError() {
    // 检查是否有Element Plus可用
    // if (window.ElMessage) {
    //   window.ElMessage.warning({
    //     message: '检测到页面性能问题，正在尝试自动恢复...',
    //     duration: 3000
    //   });
    // } else {
    //   // 使用原生提示
    //   const notification = document.createElement('div');
    //   notification.style.cssText = `
    //     position: fixed;
    //     top: 20px;
    //     right: 20px;
    //     background: #f56c6c;
    //     color: white;
    //     padding: 12px 20px;
    //     border-radius: 4px;
    //     z-index: 9999;
    //     font-size: 14px;
    //     box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    //   `;
    //   notification.textContent = '检测到页面性能问题，正在尝试自动恢复...';
    //   document.body.appendChild(notification);
    //   setTimeout(() => {
    //     if (notification.parentNode) {
    //       notification.parentNode.removeChild(notification);
    //     }
    //   }, 3000);
    // }
  }

  /**
   * 报告错误（可选）
   */
  reportError(errorInfo) {
    // 这里可以添加错误报告逻辑
    // 比如发送到错误监控服务
    if (process.env.NODE_ENV === 'development') {
      // 开发环境下只记录到控制台
      return;
    }

    // 生产环境可以发送到错误监控服务
    // fetch('/api/error-report', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(errorInfo)
    // }).catch(() => {
    //   // 忽略报告错误
    // });
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    return {
      errorCount: this.errorCount,
      lastErrorTime: this.lastErrorTime,
      isRecovering: this.isRecovering
    };
  }

  /**
   * 手动触发恢复
   */
  manualRecovery() {
    this.errorCount = this.maxErrors;
    this.attemptRecovery();
  }

  /**
   * 重置错误计数
   */
  resetErrorCount() {
    this.errorCount = 0;
    this.lastErrorTime = 0;
    this.isRecovering = false;
  }
}

// 创建全局实例
const errorBoundary = new ErrorBoundary();

export default errorBoundary;

/**
 * Vue 3 错误处理插件
 */
export function createErrorBoundaryPlugin() {
  return {
    install(app) {
      // 设置Vue错误处理器
      app.config.errorHandler = (err, vm, info) => {
        errorBoundary.handleError({
          type: 'vue',
          message: err.message,
          stack: err.stack,
          componentInfo: info,
          error: err
        });
      };

      // 设置Vue警告处理器
      app.config.warnHandler = (msg, vm, trace) => {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Vue Warning:', msg, trace);
        }
      };
    }
  };
}
