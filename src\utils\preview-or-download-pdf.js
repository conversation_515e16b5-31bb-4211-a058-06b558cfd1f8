// html转pdf, 预览功能
import { getTenantConfig } from '@/utils/auth';
import $ from 'jquery';
// import { ElLoading } from 'element-plus'
import html2canvas from 'html2canvas';
import printJS from 'print-js';
/**
 * @param  ele          要生成 pdf 的DOM元素（容器）
 * @param  padfName     PDF文件生成后的文件名字
 * */

async function previewImg(ele) {
  window.scrollTo(0, 0);
  const eleW = ele.offsetWidth; // 获得该容器的宽
  const eleH = ele.offsetHeight; // 获得该容器的高
  const eleOffsetTop = ele.offsetTop; // 获得该容器到文档顶部的距离
  const eleOffsetLeft = ele.offsetLeft; // 获得该容器到文档最左的距离

  var canvas = document.createElement('canvas');
  var abs = 0;

  const win_in = document.documentElement.clientWidth || document.body.clientWidth; // 获得当前可视窗口的宽度（不包含滚动条）
  const win_out = window.innerWidth; // 获得当前窗口的宽度（包含滚动条）

  if (win_out > win_in) {
    abs = (win_out - win_in) / 2; // 获得滚动条宽度的一半
  }
  canvas.width = eleW * 2;
  canvas.height = eleH * 2;

  var context = canvas.getContext('2d');
  context.scale(2, 2);
  context.translate(-eleOffsetLeft - abs, -eleOffsetTop);
  var baseSrc = '';
  await html2canvas(ele, {
    dpi: 600,
    scale: 2, // 处理图片模糊
    useCORS: true // 允许canvas画布内 可以跨域请求外部链接图片, 允许跨域请求。
  }).then(canvas => {
    var pageData = canvas.toDataURL('image/jpeg', 1.0);
    baseSrc = pageData;
  });
  return baseSrc;
}

// 分辨率是72像素/英寸时，A4纸的尺寸的图像的像素是595×842；
// 分辨率是96像素/英寸时，A4纸的尺寸的图像的像素是794×1123；(默认)
// 分辨率是120像素/英寸时，A4纸的尺寸的图像的像素是1487×2105；
// 分辨率是150像素/英寸时，A4纸的尺寸的图像的像素是1240×1754；
// 分辨率是300像素/英寸时，A4纸的尺寸的图像的像素是2480×3508；
function getPrintPageSize(isTransverse) {
  const topBlankSpace = 50; // 打印上下留白距离
  const leftBlankSpace = 55; // 打印左右留白距离
  const A4Width = isTransverse ? 1123 : 794; // A4纸的宽度
  const A4Height = isTransverse ? 794 : 1123; // A4纸的高度
  // console.log('A4Width', A4Width)
  // console.log('A4Height', A4Height)
  return { topBlankSpace, leftBlankSpace, A4Width, A4Height };
}
// ele 打印的元素
// pageHasHead 是否分页带表头，true 带表头，false 打印分页不带表头
// isTransverse 区分横向和纵向模板 true 横向模板，false 纵向模板
// isBatch 是否是批量打印 true 是；false 不是
// currIndexPrint批量打印时候的索引
const pageHasHead = getTenantConfig()?.templatePageRepeatHead;

async function PageImg(isTransverse, isBatch, currIndexPrint) {
  const printPageSize = getPrintPageSize(isTransverse);
  const templateAll = $('.slot-temp'); // 所有元素的
  const isDesignTemplate = Boolean(templateAll.find('.template-content').length);
  const allShowRow = [];
  if (isDesignTemplate) {
    templateAll.addClass(isTransverse ? 'horizontal' : 'vertical');
    templateAll.find('.template-content > .row').each((_index, item) => {
      allShowRow.push({ dom: $(item).clone()[0], height: item.offsetHeight });
    });
  } else {
    const moduleContent = templateAll.find('.excel-content'); // 模板内容
    moduleContent.find('.el-row').each(function (index, item) {
      // 过滤掉不显示的行
      if (item.offsetHeight > 0) {
        const parent = $(item).parent().parent()[0];
        // 行嵌套行 过滤掉里面的行
        if (!$(parent).is('.el-row')) {
          allShowRow.push({ dom: $(item).clone()[0], height: item.offsetHeight });
        }
      }
    });
  }
  return new Promise((resolve, reject) => {
    resolve(
      computationalPrinting(
        allShowRow,
        printPageSize,
        isTransverse,
        pageHasHead,
        isBatch,
        currIndexPrint,
        isDesignTemplate
      )
    );
  });
}
// isBatch 是否是批量打印 true 是；false 不是
function finishPrint(ele, isTransverse, printPageSize, isBatch) {
  // 去掉打印时右边框的加粗边框
  const rows = Array.from(ele.getElementsByClassName('el-row'));
  rows.forEach(item => {
    item.lastElementChild?.classList.remove('bd-r');
    item.lastElementChild?.classList.remove('bdr');
  });
  return new Promise((resolve, reject) => {
    const hasTesting = false; // 用于调试打印模板
    if (hasTesting) debuggerPrintDom(ele, printPageSize, isTransverse);
    html2canvas(ele, {
      scale: window.devicePixelRatio * 2, // 处理图片模糊
      width: printPageSize.A4Width - printPageSize.leftBlankSpace * 2,
      background: '#FFFFFF',
      logging: true,
      x: ele.getBoundingClientRect().left,
      useCORS: true // 允许canvas画布内 可以跨域请求外部链接图片, 允许跨域请求。
    }).then(canvas => {
      const pageData = canvas.toDataURL('image/jpeg', 1.0);
      if (hasTesting) debuggerPrintImg(pageData, printPageSize);
      const focuser = setInterval(() => window.dispatchEvent(new Event('focus')), 500);
      if (!isBatch) {
        printJS({
          printable: pageData,
          // @page{margin: 0px ${printPageSize.leftBlankSpace}px; 上下margin为0是因为computationalPrinting方法已经计算了margin上下高度，左右需要设置
          style: `body { margin: 0; padding: 0; border: 0;} img { width: 100%; display: block; } @page{margin: 0px ${
            printPageSize.leftBlankSpace
          }px; size: ${isTransverse ? 'A4 landscape' : 'A4 portrait'}; overflow: hidden; }`,
          type: 'image',
          documentTitle: '',
          onPrintDialogClose: () => {
            clearInterval(focuser);
            if (!hasTesting) $('.print-box').remove();
          }
        });
      }
      clearInterval(focuser);
      console.log(ele, ele.offsetWidth);
      console.log(
        '打印内容的宽度',
        printPageSize.A4Width - printPageSize.leftBlankSpace * 2,
        '左右两边的留白：',
        printPageSize.leftBlankSpace
      );
      if (!hasTesting) {
        $('.whichPage').each(function (index, item) {
          $(item)[0].innerText = '';
        });
        $('.totalPage').each(function (index, item) {
          $(item)[0].innerText = '';
        });
        $('.print-box').remove();
      }
      resolve({ imageBase64: pageData });
    });
  });
}
// 需要分页的话，计算打印
// allShowRow 需要打印的所有行，[{dom: '行元素', height: '行高'}]
// printPageSize 打印纸张的大小和边距
// pageHasHead 分页是否每页都带表头，true 是；false 不是
// isBatch 是否是批量打印 true 是；false 不是
// currIndexPrint批量打印时候的索引
function computationalPrinting(
  allShowRow,
  printPageSize,
  isTransverse,
  pageHasHead,
  isBatch,
  currIndexPrint,
  isDesignTemplate
) {
  const templateHead = $('.pdf-header')[0]; // 模板头部 html dom 对象
  const templateFooter = $('#template-footer')[0]; // 模板尾部 html dom 对象
  window.scrollTo(0, 0);
  $('.print-box').remove();
  $('.el-form.slot-temp.box-excel').remove('.print-box');
  // 添加打印dom
  $('.slot-temp.box-excel').append('<div class="print-box"></div>');
  const printBox = $('.print-box');
  let pageNum = 1;
  let printRowHeight = 0; // 可打印的行高度
  let printPage = $(`<div class="print-page-${pageNum} print-page"></div>`);
  printBox.append(printPage);
  addPalceHolderTop(printPage, printPageSize.topBlankSpace);
  if (templateHead) {
    // 如果有表头
    printPage.append($(templateHead).clone());
    if (isDesignTemplate) {
      printPage.append(`<div class="printTemplate template-content"></div>`);
    } else {
      printPage.append(`<div class="printTemplate excel-content bdr bdl"></div>`);
    }
    printRowHeight = printPageSize.A4Height - templateHead.offsetHeight - printPageSize.topBlankSpace * 2;
  } else {
    // 如果没有表头
    if (isDesignTemplate) {
      printPage.append(`<div class="printTemplate template-content border-top"></div>`);
    } else {
      printPage.append(`<div class="printTemplate excel-content bdr bdl bdt"></div>`);
    }
    printRowHeight = printPageSize.A4Height - printPageSize.topBlankSpace * 2;
  }
  allShowRow.forEach(item => {
    if (printRowHeight < item.height) {
      // .printTemplate 样式有一个 border-top 为 1px 的高度计算
      printRowHeight = printRowHeight - 1;
      // 如果剩余的高度不够下一行，则翻页, 然后添加占位div, -1 是每页都减去 1px 来适应打印机高度机制
      addPalceHolderBottom($(`.print-page-${pageNum}`), printRowHeight + printPageSize.topBlankSpace - 1, pageNum);
      pageNum += 1;
      printPage = $(`<div class="print-page-${pageNum} print-page"></div>`);
      printBox.append(printPage);
      addPalceHolderTop(printPage, printPageSize.topBlankSpace);
      if (pageHasHead) {
        // 每页分页带表头
        if (templateHead) {
          // 有表头的情况下
          printPage.append($(templateHead).clone());
          if (isDesignTemplate) {
            printPage.append(`<div class="printTemplate template-content"></div>`);
          } else {
            printPage.append(`<div class="printTemplate excel-content bdr bdl"></div>`);
          }
          printRowHeight = printPageSize.A4Height - templateHead.offsetHeight - printPageSize.topBlankSpace * 2;
        } else {
          // 没有表头的情况下
          if (isDesignTemplate) {
            printPage.append(`<div class="printTemplate template-content border-top"></div>`);
          } else {
            printPage.append(`<div class="printTemplate excel-content bdr bdl bdt"></div>`);
          }
          printRowHeight = printPageSize.A4Height - printPageSize.topBlankSpace * 2;
        }
      } else {
        // 每页分页不带表头
        if (isDesignTemplate) {
          printPage.append(`<div class="printTemplate template-content border-top"></div>`);
        } else {
          printPage.append(`<div class="printTemplate excel-content bdr bdl bdt"></div>`);
        }
        printRowHeight = printPageSize.A4Height - printPageSize.topBlankSpace * 2;
      }
    }
    $(`.print-page-${pageNum} .printTemplate`).append($(item.dom));
    printRowHeight = printRowHeight - item.height;
  });
  if (templateFooter?.offsetHeight) {
    if (printRowHeight > templateFooter.offsetHeight) {
      printPage.append($(templateFooter).clone());
      printRowHeight = printRowHeight - templateFooter.offsetHeight;
      // 由于dom整体高度等于A4纸高度，可能跟打印机高度机制有关，所以最后一页会打印出一张空白页，减去1px
      addPalceHolderBottom(printPage, printRowHeight + printPageSize.topBlankSpace - 1, pageNum);
    } else {
      // - 1 计算同上
      addPalceHolderBottom(printPage, printRowHeight + printPageSize.topBlankSpace - 1, pageNum);
      pageNum += 1;
      printRowHeight = printPageSize.A4Height - printPageSize.topBlankSpace * 2;
      printPage = $(`<div class="print-page-${pageNum} print-page"></div>`);
      printBox.append(printPage);
      addPalceHolderTop(printPage, printPageSize.topBlankSpace);
      if (pageHasHead && templateHead) {
        printPage.append($(templateHead).clone());
        printRowHeight = printRowHeight - templateHead.offsetHeight;
      }
      printPage.append($(templateFooter).clone());
      printRowHeight = printRowHeight - templateFooter.offsetHeight;
      addPalceHolderBottom(printPage, printRowHeight + printPageSize.topBlankSpace - 1, pageNum);
    }
  } else {
    addPalceHolderBottom(printPage, printRowHeight + printPageSize.topBlankSpace - 1, pageNum);
  }
  $('.printTotalPage').each(function (index, item) {
    $(item)[0].innerText = pageNum;
  });
  if (isBatch) {
    // 中国电科院批量打印第几项共几项的问题
    $('.whichPage').each(function (index, item) {
      $(item)[0].innerText = currIndexPrint + 1;
    });
    const totalItem = getUrlParam('printlist').split(',').length;
    $('.totalPage').each(function (index, item) {
      $(item)[0].innerText = totalItem;
    });
  } else {
    // 中国电科院批量打印第几项共几项的问题
    $('.whichPage').each(function (index, item) {
      $(item)[0].innerText = 1;
    });
    $('.totalPage').each(function (index, item) {
      $(item)[0].innerText = 1;
    });
  }
  // $('.print-box')[0].style.backgroundColor = 'yellow'
  return new Promise((resolve, reject) => {
    resolve(finishPrint($('.print-box')[0], isTransverse, printPageSize, isBatch, pageNum));
  });
}

// 顶部留白占位, printPage 必须是一个jQuery对象
function addPalceHolderTop(printPage, topBlankSpace) {
  const palceHolderDiv = document.createElement('div');
  $(palceHolderDiv).addClass('topPlaceHolder');
  palceHolderDiv.style.height = `${topBlankSpace}px`;
  palceHolderDiv.style.display = 'block';
  // palceHolderDiv.style.backgroundColor = 'orange'
  printPage.append(palceHolderDiv);
}

// 底部留白占位 参数：占位长度；当前页码；要添加的元素, printPage 必须是一个jQuery对象
function addPalceHolderBottom(printPage, distance, pageNum) {
  const palceHolderDiv = document.createElement('div');
  $(palceHolderDiv).addClass('bottomPlaceHolder');
  palceHolderDiv.style.height = `${distance}px`;
  palceHolderDiv.style.display = 'block';
  palceHolderDiv.style.position = 'relative';
  // palceHolderDiv.style.backgroundColor = 'red'
  palceHolderDiv.innerHTML = `<div style="position: absolute; bottom: 30px;width: 100%; text-align: center;">${pageNum}/<span class="printTotalPage"></span></div>`;
  printPage.append(palceHolderDiv);
}
// 屏幕快照
async function PageSnapshot(ele) {
  // const printPageSize = getPrintPageSize(false);
  // 去掉打印时右边框的加粗边框
  ele.querySelectorAll('.el-row').forEach(item => {
    item.lastElementChild?.classList.remove('bd-r');
    item.lastElementChild?.classList.remove('bdr');
  });
  let url = '';
  await html2canvas(ele, {
    scale: window.devicePixelRatio * 2, // 处理图片模糊
    background: '#fff',
    useCORS: true // 允许canvas画布内 可以跨域请求外部链接图片, 允许跨域请求。
  }).then(canvas => {
    url = canvas.toDataURL('image/jpg');
    console.log(ele);
    console.log(url);
    console.log(canvas);
  });
  return url;
}
// 获取url中的参数
function getUrlParam(name) {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)'); // 构造一个含有目标参数的正则表达式对象
  var r = window.location.search.substr(1).match(reg); // 匹配目标参数
  if (r != null) return unescape(r[2]);
  return null; // 返回参数值
}

function debuggerPrintDom(ele, printPageSize, isTransverse) {
  // 添加颜色
  $('.print-box').css({ backgroundColor: 'green' });
  $('.pdf-header').css({ backgroundColor: 'red' });
  $('.topPlaceHolder').css({ backgroundColor: 'yellow' });
  $('.bottomPlaceHolder').css({ backgroundColor: 'orange' });

  let wrapper = document.getElementById('debuggerPrintDom');
  if (wrapper) {
    wrapper.appendChild($(ele).clone()[0]);
  } else {
    wrapper = document.createElement('div');
    wrapper.id = 'debuggerPrintDom';
    wrapper.style.position = 'fixed';
    wrapper.style.top = '10px';
    wrapper.style.right = '30px';
    wrapper.style.bottom = '10px';
    wrapper.style.zIndex = 100000;
    wrapper.style.padding = '0 55px 0 55px';
    wrapper.style.outline = '10px solid black';
    wrapper.style.width = isTransverse ? '1123px' : '794px';
    wrapper.style.backgroundColor = '#fff';
    wrapper.style.overflowY = 'auto';
    wrapper?.classList.add('box-excel');
    wrapper.appendChild($(ele).clone()[0]);

    for (let index = 1; index <= 10; index++) {
      const line = document.createElement('div');
      line.style.position = 'absolute';
      line.style.top = (printPageSize.A4Height - 1) * index + 'px';
      line.style.left = 0;
      line.style.zIndex = 1000;
      line.style.width = '100%';
      line.style.height = '1px';
      line.style.backgroundColor = 'blue';
      wrapper.appendChild(line);
    }
    document.body.appendChild(wrapper);
  }
}

// eslint-disable-next-line
function debuggerPrintImg(pageData, printPageSize) {
  let wrapper = document.getElementById('debuggerPrintImg');
  const imageHeight = printPageSize.A4Width - printPageSize.leftBlankSpace * 2 + 'px';
  if (wrapper) {
    const image = document.createElement('img');
    image.style.width = imageHeight;
    image.style.display = 'block';
    image.src = pageData;
    wrapper.appendChild(image);
  } else {
    wrapper = document.createElement('div');
    const image = document.createElement('img');
    image.style.width = imageHeight;
    image.src = pageData;
    wrapper.id = 'debuggerPrintImg';
    wrapper.style.position = 'fixed';
    wrapper.style.top = '10px';
    wrapper.style.bottom = '10px';
    wrapper.style.left = '30px';
    wrapper.style.zIndex = 100000;
    wrapper.style.padding = '0 55px 0 55px';
    wrapper.style.outline = '10px solid black';
    wrapper.style.backgroundColor = '#fff';
    wrapper.style.overflowY = 'auto';
    wrapper.appendChild(image);

    for (let index = 1; index <= 10; index++) {
      const line = document.createElement('div');
      line.style.position = 'absolute';
      line.style.top = (printPageSize.A4Height - 1) * index + 'px';
      line.style.left = 0;
      line.style.zIndex = 1000;
      line.style.width = '100%';
      line.style.height = '1px';
      line.style.backgroundColor = 'blue';
      wrapper.appendChild(line);
    }

    document.body.appendChild(wrapper);
  }
}

export default {
  previewImg,
  PageSnapshot,
  PageImg
};
