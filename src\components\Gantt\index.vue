<template>
  <div class="ganttChartClass">
    <!-- 顶部标题 -->
    <div class="topTitle">
      <div style="position: relative" :style="{ width: leftTitleWidth / 2 + 'px' }">
        <div style="display: inline-block">设备名称</div>
      </div>
      <div style="position: relative" :style="{ width: leftTitleWidth / 2 + 'px' }">
        <div style="display: inline-block">设备编号</div>
      </div>
      <div
        v-for="(item, i) in title"
        :key="i"
        :style="{ width: 'calc(' + '( 100% - ' + leftTitleWidth + 'px)' + ' * (1 / ' + title.length + ')' + ')' }"
      >
        {{ item }}
      </div>
    </div>
    <div v-for="(item, i) in dataAll" :key="i" class="content">
      <!-- 左侧标题 -->
      <!-- <div :style="{ width: leftTitleWidth + 'px' }" class="leftTitle">
        <div style="display:flex;width:100%;height:100%;text-align: center;">
          <div
            style="display: flex;justify-content: center;align-items: center;font-size: 16px;"
            :style="{
              width: item.children[0].subTitle === '' ? '100%' : '40%',
              paddingRight: item.children[0].subTitle === '' ? '10px' : '0px'
            }"
            class="firstTitleArea"
          >
            <div style="display:inline-block;text-align:left;">
              {{ item.title }}
            </div>
          </div>
        </div>
      </div> -->
      <div
        :style="{
          width: 'calc(' + '( 100% - ' + leftTitleWidth + 'px)' + ' * (1 / ' + title.length + ')' + ')'
        }"
      >
        <div
          v-for="(item2, i2) in item.children"
          :key="i2"
          style="width: 100%; display: flex; justify-content: flex-start"
        >
          <!-- 二级标题展示 -->
          <div style="width: 0px">
            <div
              style="
                width: 84px;
                height: 100%;
                position: relative;
                left: -84px;
                align-items: center;
                justify-content: center;
                padding: 0 5px;
                color: #585d79;
                font-size: 14px;
                font-weight: 600;
              "
              :style="{
                borderTop: 0 === i2 ? 'none' : '1px solid #FFE1C5',
                display: item2.subTitle === '' ? 'none' : 'flex'
              }"
            >
              {{ item2.subTitle }}
            </div>
          </div>

          <div style="width: 100%">
            <div v-for="(item3, i3) in item2.children" :key="i3" class="lineRow" style="display: flex; width: 100%">
              <div v-for="(ele2, index2) in item3" :key="index2" style="width: 100%; flex-shrink: 0">
                <!-- 这行是占位 -->
                <div
                  :style="{
                    width: 'calc(' + ele2.width + ' - 1px)',
                    background: lineDrawArr[index2 % 4]
                  }"
                  style="height: 100%; position: relative; visibility: hidden"
                  class="lineDraw"
                >
                  {{ ele2.name }}
                </div>

                <!-- 这行才是真正显示 -->
                <div
                  :style="{
                    width: 'calc(' + ele2.width + ' - 1px)',
                    left: ele2.left,
                    background: lineDrawArr[index2 % 4]
                  }"
                  style="display: flex; align-items: center; justify-content: center"
                  class="lineDraw"
                >
                  <div style="text-align: left; display: inline-block">
                    {{ ele2.name }}
                  </div>
                </div>
              </div>

              <div />
            </div>
          </div>
        </div>
      </div>
      <!-- <div v-for="(item5, index) in title.length-1" :key="index" :style="{ width: 'calc(' + '( 100% - ' + leftTitleWidth + 'px)' + ' * (1 / ' + title.length + ')' + ')' }" /> -->
    </div>
  </div>
</template>

<script>
import { toRefs, reactive, watch } from 'vue';

export default {
  name: 'Gantt',
  components: {},
  props: {
    type: {
      // 类型 month：月视图 day: 日视图 weeks: 周视图
      type: String,
      default: ''
    },
    date: {
      // 显示的时间范围 左右闭区间
      type: Object,
      default: () => {
        return {};
      }
    },
    data: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  setup(props, context) {
    watch(props, newValue => {
      state.dataAll = props.data;
      console.log(state.dataAll);
      state.title = formatterHeaderTitle(props.type, props.date);
    });
    // 格式化头部类型
    const formatterHeaderTitle = (type, date) => {
      const dateArray = [];
      if (type === 'month' || type === 'week') {
        for (let i = date.startDate.split('-')[2]; i <= date.endDate.split('-')[2]; i++) {
          dateArray.push(Number(i));
        }
      } else {
        for (let i = 0; i < 24; i++) {
          dateArray.push(Number(i) + ':00');
        }
      }
      return dateArray;
    };
    const state = reactive({
      leftTitleWidth: 150,
      dataAll: [
        {
          title: '1级标题',
          children: [
            {
              subTitle: '',
              children: [
                [
                  {
                    width: '50%',
                    left: '150%',
                    name: '这里是名称'
                  }
                ]
              ]
            }
          ]
        }
      ],
      title: [],
      lineDrawArr: ['#E4EFFF', '#F3F37E', '#FFDED7', '#CBEEEA']
    });
    return {
      ...toRefs(state),
      formatterHeaderTitle
    };
  }
};
</script>

<style lang="scss" scoped>
.ganttChartClass {
  box-sizing: border-box;
  overflow: hidden;
  border: 1px solid #ffe1c5;
  border-right: 0px;

  .content {
    display: flex;
    flex-wrap: wrap;

    border-top: 1px dashed #ffe1c5;

    &:nth-child(2) {
      border-top: 1px solid #ffe1c5;
    }

    .leftTitle {
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      padding: 10px;
      padding-right: 0px;
      background: #fef9f3;
      color: #252631;

      .firstTitleArea {
        position: relative;

        &:after {
          display: block;
          content: '';
          height: calc(100% + 20px);
          width: 1px;
          background: #ffe1c5;
          position: absolute;
          right: 0;
        }
      }
    }

    & > div {
      position: relative;

      // border-right: 1px solid blue;
      &::after {
        display: block;
        content: '';
        position: absolute;
        height: 100%;
        width: 1px;
        background: #ffe1c5;
        right: 0;
        top: 0;
      }

      .lineRow {
        // height: 30px;
        margin-top: 10px;
        margin-bottom: 10px;
        position: relative;

        .lineDraw {
          text-align: center;
          // line-height: 30px;
          z-index: 5;
          position: absolute;
          padding: 5px 20px;
          top: 0;
          height: 100%;

          .lineDrawText {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;

            color: #585d79;
            font-size: 14px;

            &:hover {
              cursor: pointer;
            }
          }
        }
      }
    }
  }

  .topTitle {
    display: flex;
    flex-wrap: wrap;
    background: #fef9f3;
    color: #ce8d51;

    & > div {
      display: inline-block;
      text-align: center;
      border-right: 1px solid #ffe1c5;
      height: 32px;
      line-height: 32px;
      overflow: hidden;

      .diagonalLine {
        width: 200px;
        border-bottom: 1px solid #ffe1c5;
        transform: rotate(17.9deg);
        transform-origin: 0 0;
        position: absolute;
        z-index: 5;
        top: 0;
        left: 0;
      }
    }
  }

  div {
    box-sizing: border-box;
  }
}
</style>
