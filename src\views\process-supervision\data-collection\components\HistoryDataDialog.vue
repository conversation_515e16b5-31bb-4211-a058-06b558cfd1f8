<template>
  <!--历史数据采集弹框-->
  <el-dialog
    v-model="dialogShow"
    width="70%"
    title="选择数据采集"
    top="3vh"
    custom-class="history-data-dialog"
    :close-on-click-modal="false"
  >
    <div class="table-box">
      <el-form :model="form">
        <el-form-item label="试验日期：" label-width="110px">
          <el-date-picker
            v-model="postData.startDate"
            :clearable="false"
            type="date"
            class="date-picker"
            placeholder="开始日期"
            format="YYYY/MM/DD"
            size="small"
            @change="onDateTimeChange"
          />
          <el-time-picker
            v-model="postData.startTime"
            :clearable="false"
            placeholder="开始时间"
            size="small"
            class="time-picker"
            @change="onDateTimeChange"
          />
          <span class="to">至</span>
          <!-- <el-date-picker v-model="postData.endDate" type="datetime" placeholder="结束时间" format="YYYY/MM/DD HH:mm:ss" size="small" /> -->
          <el-date-picker
            v-model="postData.endDate"
            :clearable="false"
            type="date"
            class="date-picker"
            placeholder="结束日期"
            format="YYYY/MM/DD"
            size="small"
            @change="onDateTimeChange"
          />
          <el-time-picker
            v-model="postData.endTime"
            :clearable="false"
            placeholder="结束时间"
            size="small"
            class="time-picker"
            @change="onDateTimeChange"
          />
          <el-button type="text" size="mini" style="margin-left: 10px" @click="isShowFastTime = !isShowFastTime">
            快捷时间<i class="el-icon--right" :class="[isShowFastTime ? 'el-icon-arrow-up' : 'el-icon-arrow-down']" />
          </el-button>
          <el-button type="primary" size="mini" style="margin-left: 20px" @click="handleSearch">开始查询</el-button>
          <el-button size="mini" style="margin-left: 20px" @click="handleReset">重置</el-button>
        </el-form-item>
        <el-form-item v-if="isShowFastTime" label="" label-width="110px">
          <el-radio-group v-model="form.timeRadio" size="mini" @change="changeTimeRadio">
            <el-radio-button label="30分钟内" />
            <el-radio-button label="1小时内" />
            <el-radio-button label="3小时内" />
            <el-radio-button label="8小时内" />
            <el-radio-button label="12小时内" />
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="colorList.length !== 0 && coreExistInfo.coreColourList"
          label="试验线芯："
          label-width="110px"
        >
          <el-radio-group v-model="colorRadio" @change="changeRadioColor">
            <el-radio v-for="(item, i) in colorList" :key="i" :label="i">{{ item }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="数据范围：" label-width="110px">
          <el-radio-group v-model="form.range" @change="changeDataRange">
            <el-radio :label="1">全部</el-radio>
            <el-radio :label="2">本样品</el-radio>
          </el-radio-group>
        </el-form-item> -->
      </el-form>
      <el-table
        ref="tableRef"
        :key="tableKey"
        v-loading="loading"
        :data="tableData"
        fit
        border
        class="dark-table base-table"
        size="small"
        max-height="420"
        @selection-change="selectiondataChange"
        @row-click="handleRowClick"
        @select="handleSelectRow"
      >
        <el-table-column type="selection" :width="colWidth.checkbox" />
        <el-table-column type="index" label="序号" :width="colWidth.serialNo" />
        <el-table-column prop="date" label="样品编号" :min-width="colWidth.orderNo">
          <template #default="{ row }">
            <div class="nowrap">{{ row[0].secSampleNum }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="试验时间" :min-width="colWidth.datetime">
          <template #default="{ row }">
            <div class="nowrap">{{ row[0]?.acquisitionTime }}</div>
          </template>
        </el-table-column>
        <el-table-column v-for="(item, index) in sampleDatas" :key="index" :label="item.capabilityParaName">
          <el-table-column prop="name" label="测试值" :min-width="colWidth.result">
            <template #default="{ row }">
              <div class="nowrap">{{ row[index]?.acquisitionValue }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="转换值" :min-width="colWidth.result">
            <template #default="{ row }">
              <div class="nowrap">{{ row[index]?.conversionValue }}</div>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
      <div v-if="tableData.length > 0" class="table-footer">
        <el-button size="small" @click="handleNextPage">下一页</el-button>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="small" @click="closeDialog()">取消</el-button>
        <el-button size="small" type="primary" @click="confirmSelected" @keyup.prevent @keydown.enter.prevent
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { computed, reactive, toRefs, ref, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { colWidth } from '@/data/tableStyle';
import { formatDateTime } from '@/utils/formatTime';
import { getRdsData } from '@/api/datacollection';
import { ElMessage } from 'element-plus';
import { cloneDeep } from 'lodash';

export default {
  name: 'HistoryDataDialog',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    activeSampleRow: {
      type: Object,
      default() {
        return {};
      }
    },
    activeCapabilityItem: {
      type: String,
      default: ''
    },
    wireColorList: {
      type: Array,
      default() {
        return [];
      }
    },
    wireCoreExistInfo: {
      type: Object,
      default() {
        return {};
      }
    },
    wireColorRadio: {
      type: Number,
      default: 0
    }
  },
  emits: ['close', 'confirm-selected', 'change-color'],

  setup(props, context) {
    const route = useRoute();
    const data = reactive({
      timeData: [],
      isShowFastTime: false,
      form: {
        range: 1,
        timeRadio: '30分钟内'
      },
      shortcuts: [
        {
          text: '30分钟内',
          value: (() => {
            const end = new Date();
            const start = new Date().getTime() - 3600 * 1000 * 0.5;
            return [start, end];
          })()
        },
        {
          text: '1小时内',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 1);
            return [start, end];
          })()
        },
        {
          text: '3小时内',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 3);
            return [start, end];
          })()
        },
        {
          text: '8小时内',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 8);
            return [start, end];
          })()
        },
        {
          text: '12小时内',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 12);
            return [start, end];
          })()
        }
      ],
      postData: {
        capabilityId: '', // 检测项目id
        deviceNo: route.query.deviceNumber,
        secSampleNum: '',
        startDate: '',
        startTime: '',
        endDate: '',
        endTime: '',
        timeType: 0,
        sampleId: '',
        page: 1,
        limit: 10
      },
      hasDateTimeChanged: false,
      loading: false,
      tableKey: 0,
      sampleDatas: [],
      tableData: [],
      selectiondata: [],
      tableRef: ref(null)
    });
    const activeRow = computed({
      get: () => props.activeSampleRow
    });
    const dialogShow = computed({
      get: () => props.dialogVisible,
      set: val => context.emit('close', val)
    });
    const activeItem = computed({
      get: () => props.activeCapabilityItem
    });
    const colorList = computed({
      get: () => props.wireColorList.map(item => item.coreColour)
    });
    const coreExistInfo = computed({
      get: () => props.wireCoreExistInfo
    });
    const colorRadio = computed({
      get: () => props.wireColorRadio,
      set: val => context.emit('change-color', val)
    });

    const initHistoryData = (isShowFastTime = false) => {
      if (data.postData.capabilityId !== activeItem.value || !data.hasDateTimeChanged) {
        data.hasDateTimeChanged = false;
        data.postData.capabilityId = activeItem.value;
        const endMillisecond = new Date().getTime();
        const startMillisecond = endMillisecond - 3600 * 1000 * 0.5;
        data.postData.startDate = new Date(startMillisecond);
        data.postData.startTime = new Date(startMillisecond);
        data.postData.endDate = new Date(endMillisecond);
        data.postData.endTime = new Date(endMillisecond);
        data.isShowFastTime = isShowFastTime;
        data.form.timeRadio = '30分钟内';
        data.postData.secSampleNum = '';
        data.postData.sampleId = '';
        data.postData.page = 1;
        getRdsDatalist();
      }
    };

    const getRdsDatalist = () => {
      if (!data.postData.startDate) {
        ElMessage.error('开始时间不能为空');
        return false;
      }
      if (!data.postData.endDate) {
        ElMessage.error('结束时间不能为空');
        return false;
      }
      const params = cloneDeep(data.postData);
      if (params.startDate) {
        params.startDate =
          formatDateTime(params.startDate, 'yyyy-MM-dd') + ' ' + formatDateTime(params.startTime, 'hh:mm:ss');
        delete params.startTime;
      }
      if (params.endDate) {
        params.endDate =
          formatDateTime(params.endDate, 'yyyy-MM-dd') + ' ' + formatDateTime(params.endTime, 'hh:mm:ss');
        delete params.endTime;
      }
      data.loading = true;
      getRdsData(params).then(rsp => {
        data.loading = false;
        const sampleDatas = rsp.data.data;
        if (sampleDatas.length !== 0) {
          data.sampleDatas = sampleDatas;
          const sampleDataLength = sampleDatas[0].partVos.length;
          const tableData = [];
          for (let index = 0; index < sampleDataLength; index++) {
            const tableItem = [];
            sampleDatas.forEach((sampleData, sampleIndex) => {
              const item = sampleData.partVos[index];
              item.selected = false;
              item.index = index;
              tableItem.push(item);
            });
            tableData.push(tableItem);
          }
          data.tableData = data.tableData.concat(tableData);
          data.postData.page = sampleDatas[0].currPage;
          if (data.postData.page > 1) {
            nextTick(() => {
              const tableBodyWrapper = data.tableRef.$el.querySelector('.el-table__body-wrapper');
              const row = tableBodyWrapper.querySelector(
                `.el-table__row:nth-child(${(data.postData.page - 1) * data.postData.limit + 1})`
              );
              if (tableBodyWrapper && row) {
                tableBodyWrapper.scrollTo({ top: row.offsetTop, behavior: 'smooth' });
              }
            });
          } else {
            nextTick(() => {
              data.tableKey++;
            });
          }
        } else if (params.page === 1) {
          data.sampleDatas = [];
          data.tableData = [];
        }
      });
    };
    const handleSearch = () => {
      data.tableData = [];
      data.postData.page = 1;
      getRdsDatalist();
    };
    const handleReset = () => {
      data.hasDateTimeChanged = false;
      data.sampleDatas = [];
      data.tableData = [];
      initHistoryData(data.isShowFastTime);
    };
    const handleNextPage = () => {
      data.postData.page++;
      getRdsDatalist();
    };
    const changeTimeRadio = val => {
      const endMillisecond = (data.postData.endDate || new Date()).getTime();
      let startMillisecond = 0;
      switch (val) {
        case '30分钟内':
          startMillisecond = endMillisecond - 3600 * 1000 * 0.5;
          break;
        case '1小时内':
          startMillisecond = endMillisecond - 3600 * 1000 * 1;
          break;
        case '3小时内':
          startMillisecond = endMillisecond - 3600 * 1000 * 3;
          break;
        case '8小时内':
          startMillisecond = endMillisecond - 3600 * 1000 * 8;
          break;
        case '12小时内':
          startMillisecond = endMillisecond - 3600 * 1000 * 12;
          break;
      }
      data.postData.startDate = new Date(startMillisecond);
      data.postData.startTime = new Date(startMillisecond);
      data.postData.endDate = new Date(endMillisecond);
      data.postData.endTime = new Date(endMillisecond);
      onDateTimeChange();
    };

    const onDateTimeChange = () => {
      data.hasDateTimeChanged = true;
    };

    const changeRadioColor = val => {
      data.colorRadio = val;
    };

    // 更改数据范围
    const changeDataRange = val => {
      if (val === 1) {
        data.postData.secSampleNum = '';
        data.postData.sampleId = '';
        getRdsDatalist();
      } else {
        data.postData.secSampleNum = activeRow.value.secsamplenum;
        data.postData.sampleId = activeRow.value.secsamplenum;
        getRdsDatalist();
      }
    };

    // 改变选择的数据
    const selectiondataChange = val => {
      data.selectiondata = val;
    };

    // #region dialog footer

    function closeDialog() {
      dialogShow.value = false;
    }

    function confirmSelected() {
      if (Array.prototype.toReversed) {
        context.emit('confirm-selected', data.selectiondata.toReversed());
      } else {
        context.emit('confirm-selected', cloneDeep(data.selectiondata).reverse());
      }
      closeDialog();
    }
    // #endregion

    function handleSelectRow(selection, row) {
      if (row && row.length > 0) {
        row[0].selected = !row[0].selected;
      }
    }

    function handleRowClick(row) {
      if (row && row.length > 0) {
        const rowIndex = data.tableData.findIndex(item => JSON.stringify(item) === JSON.stringify(row));
        if (rowIndex !== -1) {
          row[0].selected = !row[0].selected;
          data.tableRef.toggleRowSelection(data.tableData[rowIndex], row[0].selected);
        }
      }
    }

    return {
      ...toRefs(data),
      dialogShow,
      changeTimeRadio,
      onDateTimeChange,
      activeRow,
      activeItem,
      colorList,
      coreExistInfo,
      colorRadio,
      colWidth,
      initHistoryData,
      getRdsDatalist,
      handleSearch,
      handleReset,
      handleNextPage,
      changeRadioColor,
      changeDataRange,
      selectiondataChange,
      closeDialog,
      confirmSelected,
      handleSelectRow,
      handleRowClick
    };
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-radio-group) {
  max-height: 129px;
  overflow-y: auto;
}
.el-form :deep(.date-picker) {
  width: 120px;

  .el-input__inner {
    padding-right: 0;
  }
}

.el-form :deep(.time-picker) {
  margin-left: 10px;
  width: 100px;

  .el-input__inner {
    padding-right: 0;
  }
}

.to {
  margin: 0 16px;
}

.table-footer {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
</style>
<style lang="scss">
.history-data-dialog.el-dialog .el-dialog__body {
  max-height: 750px;
  overflow-y: auto;
}
</style>
