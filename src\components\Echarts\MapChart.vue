<template>
  <v-chart
    ref="mapChartRef"
    class="chart"
    :autoresize="true"
    :option="chartOption"
    :style="{ width: chartWidth, height: chartHeight }"
  />
</template>

<script>
import { Scatter<PERSON><PERSON>, EffectScatter<PERSON>hart, Map<PERSON>hart, <PERSON><PERSON>hart } from 'echarts/charts';
import {
  DatasetComponent,
  GridComponent,
  GeoComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  VisualMapComponent
} from 'echarts/components';
import { use, registerMap } from 'echarts/core';
import { LabelLayout, UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import { computed, ref, nextTick } from 'vue';
import VChart from 'vue-echarts';
import chinaJson from '../../../public/staticJson/china.json';

use([
  CanvasRenderer,
  DatasetComponent,
  GridComponent,
  GeoComponent,
  LabelLayout,
  MapC<PERSON>,
  <PERSON><PERSON><PERSON>,
  VisualMapComponent,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  <PERSON><PERSON>er<PERSON><PERSON>,
  EffectScatterChart,
  UniversalTransition
]);
registerMap('china', chinaJson);

export default {
  name: 'MapChart',
  components: {
    VChart
  },
  provide: {},
  props: {
    option: {
      type: Object,
      default: () => {
        return {};
      }
    },
    width: {
      type: String,
      default: '1000px'
    },
    height: {
      type: String,
      default: '1000px'
    }
  },
  setup(props, context) {
    const mapChartRef = ref(null);
    const chartOption = computed(() => {
      return props.option;
    });
    const chartWidth = computed(() => {
      return props.width;
    });
    const chartHeight = computed(() => {
      return props.height;
    });
    nextTick(() => {
      if (mapChartRef.value && mapChartRef.value.chart) {
        const mapChart = mapChartRef.value.chart;
        mapChart.on('georoam', params => {
          const option = mapChart.getOption();
          const { zoom, componentType } = params;
          if (componentType === 'series') {
            option.geo[0].center = option.series[0].center;
            if (zoom) {
              option.geo[0].zoom = option.series[0].zoom;
            }
          } else {
            option.series[0].center = option.geo[0].center;
            if (zoom) {
              option.series[0].zoom = option.geo[0].zoom;
            }
          }
          mapChart.setOption(option, true);
        });
      }
    });
    return {
      chartOption,
      chartWidth,
      chartHeight
    };
  }
};
</script>
<style scoped>
.chart {
  height: 1000px;
  width: 1000px;
}
</style>
