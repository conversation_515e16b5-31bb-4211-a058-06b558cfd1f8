import request from '@/utils/request';
import qs from 'qs';
const postHeaders = {
  'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
};
// 检测项目查询列表
export function getList(data) {
  return request({
    url: '/api-defectiveproduct/certification/list',
    method: 'post',
    data
  });
}
// 同步合格证
export function synchronizatioApi(data) {
  data = qs.stringify(data);
  return request({
    url: '/api-defectiveproduct/certification/sync',
    method: 'post',
    headers: postHeaders,
    data
  });
}
export function printersView(data) {
  data = qs.stringify(data);
  return request({
    url: `/api-defectiveproduct/certification/printers/view`,
    method: 'post',
    headers: postHeaders,
    data
  });
}
export function getPringList() {
  return request({
    url: `/api-defectiveproduct/certification/printers`,
    method: 'get'
  });
}
export function printCode(data) {
  data = qs.stringify(data);
  return request({
    url: `/api-defectiveproduct/certification/printers/print`,
    method: 'post',
    headers: postHeaders,
    data
  });
}
