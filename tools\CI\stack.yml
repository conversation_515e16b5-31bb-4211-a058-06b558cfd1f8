version: '3.7'

networks:
  overlay:
    driver: overlay
    attachable: true

services:
  web-cxist-tes:
    image: ${REGISTRY_HOST}/byzan/xlab/${IMAGE_NAME}:${TAG:-nightly}
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '100'
    networks:
      - overlay
    deploy:
      mode: replicated
      replicas: ${REPLICA:-1}
      update_config:
        failure_action: rollback
        order: start-first
    environment:
      - API_GATEWAY=${API_GATEWAY}
      - PROD=${PROD:-true}
