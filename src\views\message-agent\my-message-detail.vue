<template>
  <el-drawer
    v-model="showDrawer"
    :title="currentTitle"
    direction="rtl"
    :before-close="handleClose"
    size="38%"
    destroy-on-close
    close-on-click-modal
    custom-class="my-message-detail"
  >
    <DrawerLayout
      :has-page-header="false"
      :has-button-group="(currentDetail.todoStatus === 0 || currentDetail.todoStatus === 2) && isAgency"
    >
      <div class="drawer-wrapper">
        <el-card shadow="never">
          <div class="drawer-header">
            <div class="drawer-title">
              <span>{{ currentDetail.title }}</span>
              <el-tag size="mini" :type="tagType[currentDetail.level]" effect="dark">{{
                levelType[currentDetail.level]
              }}</el-tag>
            </div>
            <!-- 右侧按钮组 -->
            <div class="button-group">
              <el-button
                v-if="(currentDetail.todoStatus === 0 || currentDetail.todoStatus === 2) && isAgency"
                type="primary"
                size="small"
                @click="clickDetailToUrl(urlList)"
                @keyup.prevent
                @keydown.enter.prevent
                >处理</el-button
              >
            </div>
          </div>
          <el-form class="isCheck">
            <el-form-item label="发件人：">
              <UserTag :name="currentDetail.senderName" />
            </el-form-item>
            <el-form-item label="创建时间：">
              {{ formatDate(currentDetail.createTime) }}
            </el-form-item>
            <el-form-item class="content">
              {{ currentDetail.content }}
            </el-form-item>
          </el-form>
        </el-card>
        <el-form class="isCheck padLeft">
          <el-form-item label="分组：">
            {{ currentDetail.groupName }}
          </el-form-item>
          <el-form-item label="等级：">
            <el-rate
              v-model="messageLevel[currentDetail.level]"
              :colors="colors"
              :max="5"
              disabled
              :texts="['较弱', '较弱', '一般', '重要', '重要']"
              show-text
            />
          </el-form-item>
          <el-form-item label="送达方式：">
            {{ currentDetail.arrivalType || '立即送达' }}
          </el-form-item>
          <el-form-item label="有效期：">
            {{ currentDetail.usefulLife || '无限期' }}
          </el-form-item>
          <el-form-item v-if="FJList.length > 0" :label="'附件（' + FJList.length + '个）：'">
            <span class="download" @click="downloadAll(1)">全部下载</span>
            <div v-for="(list, index) in FJList" :key="index" class="enclosure-list">
              <div class="filename" @click="downloadAll(0, list)">{{ list.filename }}</div>
            </div>
          </el-form-item>
        </el-form>
        <!-- <div class="enclosure">
        </div> -->
      </div>
    </DrawerLayout>
  </el-drawer>
</template>

<script>
import { watch, reactive, toRefs } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import DrawerLayout from '@/components/DrawerLayout';
import { formatDate, formatDateTime } from '@/utils/formatTime';
import UserTag from '@/components/UserTag';
import { deleteMessage, downloadMessage } from '@/api/messageAgent';

export default {
  name: 'MyMessageDetail',
  components: { DrawerLayout, UserTag },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: function () {
        return {};
      }
    },
    isAgency: {
      type: Boolean,
      default: false
    },
    showDelete: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    // const { appContext } = getCurrentInstance()
    // const bus = appContext.config.globalProperties.bus
    const datas = reactive({
      showDrawer: false,
      currentTitle: '',
      colors: ['#99A9BF', '#F7BA2A', '#f56c6c'],
      currentDetail: {},
      messageLevel: {
        // 消息等级用在rate组件上
        0: 5,
        1: 3,
        2: 1
      },
      tagType: {
        0: 'danger',
        1: 'warning',
        2: 'success'
      },
      levelType: {
        0: '重要',
        1: '一般',
        2: '较弱'
      },
      FJList: [{ filename: '文件标题.类型1.doc' }],
      ids: [],
      isAgency: false,
      urlList: []
    });

    watch(
      () => props.drawer,
      async newValue => {
        if (newValue) {
          datas.showDrawer = newValue;
          datas.isAgency = props.isAgency;
          datas.currentDetail = props.detail.messageEntity;
          datas.FJList = props.detail.messageEntity.messagefileEntityList;
          datas.currentTitle = props.message;
          if (props.detail.messageType !== 1) {
            datas.currentTitle = '待办详情';
            datas.currentDetail = props.detail.todomessageEntity;
            datas.FJList = props.detail.todomessageEntity.messagefileEntityList;
            if (datas.currentDetail.forwardUriList && datas.currentDetail.forwardUriList.length > 0) {
              datas.currentDetail.forwardUriList.forEach(url => {
                const urlName = window.location.origin + url;
                datas.urlList.push(urlName);
              });
            }
          } else {
            datas.currentTitle = '消息详情';
          }
          datas.ids = [props.detail.id];
        }
      },
      { deep: true }
    );

    // 关闭抽屉
    const handleClose = () => {
      datas.showDrawer = false;
      context.emit('close', false);
    };

    // 过滤等级
    const filterLevel = value => {
      const p = {
        0: ['重要', '#f56c6c'],
        1: ['一般', '#67c23a'],
        2: ['较弱', '']
      };
      return p[value];
    };
    // 打印
    const print = () => {
      ElMessage.info('该功能暂未开放！');
    };
    // 删除
    const deleteThis = () => {
      ElMessageBox({
        title: '',
        message: '确认将选中消息删除？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          const params = { ids: datas.ids.join(',') };
          deleteMessage(params).then(res => {
            if (res !== false) {
              ElMessage.success('删除成功');
            }
          });
        })
        .catch(() => {});
    };
    // 处理
    const handle = () => {
      // const params = { bizIds: datas.ids.join(',') }
      // completetodoMessage(params).then(res => {
      //   if (res !== false) {
      //     console.log(res.data)
      //     bus.$emit('reloadMyAgentList', true)
      //     ElMessage.success('处理完成')
      //   }
      // })
    };
    // 跳转到新页面
    const clickDetailToUrl = list => {
      console.log(list);
      window.location.href = list[0];
    };
    // 全部下载
    const downloadAll = (flag, item) => {
      console.log(datas.FJList);
      const ids = [];
      var currentfileName = '';
      if (flag === 1 && datas.FJList.length > 0) {
        datas.FJList.forEach(list => {
          ids.push(list.id);
        });
        currentfileName = formatDateTime() + '.zip';
      } else {
        ids.push(item.id);
        currentfileName = item.filename;
      }
      const params = {
        fileIds: ids.join(',')
      };
      downloadMessage(params).then(res => {
        if (res !== false) {
          const blob = res.data;
          var fileName = res.headers.filename || currentfileName;
          var downloadElement = document.createElement('a');
          var href = window.URL.createObjectURL(blob);
          downloadElement.style.display = 'none';
          downloadElement.href = href;
          downloadElement.download = decodeURI(fileName);
          document.body.appendChild(downloadElement);
          downloadElement.click();
          document.body.removeChild(downloadElement);
          window.URL.revokeObjectURL(href);
        }
      });
    };

    return {
      ...toRefs(datas),
      handleClose,
      formatDate,
      filterLevel,
      print,
      deleteThis,
      handle,
      downloadAll,
      clickDetailToUrl
    };
  }
};
</script>

<style lang="scss" scoped>
.drawer-wrapper {
  height: 100%;
  overflow: hidden auto;
  .el-tag {
    margin-left: 10px;
  }
  .drawer-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  .drawer-title {
    line-height: 26px;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 15px;
  }
}
:deep(.isCheck .el-form-item) {
  margin-bottom: 0;
}
:deep(.el-form .el-form-item .el-form-item__label) {
  color: #909399;
}
:deep(.el-rate) {
  height: 32px;
  line-height: 32px;
}
:deep(.el-rate__icon) {
  line-height: 32px;
}
:deep(.el-rate__item) {
  height: 32px;
}
.el-card {
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
  border-radius: 10px;
  margin: 12px 20px 25px 20px;
}
.padLeft {
  padding-left: 20px;
}
.content {
  padding: 15px 0 0 0;
  margin: 10px 0 0 0;
  border-top: 1px solid #dcdfe6;
  max-height: 470px;
  min-height: 200px;
  overflow-y: auto;
  .look-url {
    color: $tes-primary;
    cursor: pointer;
  }
}
.filename {
  color: $tes-primary;
  cursor: pointer;
}
.download {
  color: $tes-primary;
  cursor: pointer;
}
.enclosure-list {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 7px 20px;
}
.messageTypeTag {
  position: relative;
  top: -2.3px;
}
.labelLeft {
  color: #909399;
}
.inlineBlock {
  display: inline-block;
}
h2 {
  font-size: 18px;
  margin: 0 0 0 10px;
}
</style>
<style lang="scss">
.my-message-detail .el-drawer__body {
  padding: 18px 20px 15px 20px;
}
</style>
