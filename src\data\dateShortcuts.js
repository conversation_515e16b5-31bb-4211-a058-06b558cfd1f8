// TODO: 实现一个获取所需日期范围快照的方法，用来调用日期快照

export const next3Months = [
  {
    text: '一月内',
    value: (() => {
      const end = new Date();
      const start = new Date();
      end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
      return [start, end];
    })()
  },
  {
    text: '三月内',
    value: (() => {
      const end = new Date();
      const start = new Date();
      end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
      return [start, end];
    })()
  }
];

export const past3Months = [
  {
    text: '过去1个月',
    value: (() => {
      const start = new Date();
      const end = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    })()
  },
  {
    text: '过去3个月',
    value: (() => {
      const start = new Date();
      const end = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    })()
  }
];
