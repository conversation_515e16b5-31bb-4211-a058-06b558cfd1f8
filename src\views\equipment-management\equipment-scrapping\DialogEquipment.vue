<template>
  <el-dialog v-model="dialogShow" title="选择设备" :close-on-click-modal="false" :width="1200" @close="handleClose">
    <div v-loading="dialogLoading">
      <el-form label-position="left" :inline="true" size="small">
        <el-row>
          <el-col :span="12">
            <el-form-item label="关键字：">
              <el-input
                v-model="condition"
                v-trim
                v-focus
                style="width: 500px"
                maxlength="100"
                placeholder="请输入编号/名称/生产厂家"
                clearable
                @keyup.enter="getTableList"
                @keyup.prevent
                @keydown.enter.prevent
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <div>
              <el-button
                type="primary"
                :loading="dialogLoading"
                size="small"
                @click="getTableList"
                @keyup.prevent
                @keydown.enter.prevent
                >查询</el-button
              >
              <el-button size="small" :loading="dialogLoading" @click="reset">重置</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <el-table
        ref="tableRef"
        :data="tableList"
        class="dark-table base-table format-height-table2"
        fit
        border
        highlight-current-row
        size="medium"
        height="auto"
        style="margin-top: 5px"
        :row-style="
          () => {
            return 'cursor: pointer';
          }
        "
        @header-dragend="drageHeader"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        @select="selectChange"
      >
        <el-table-column type="selection" prop="checkbox" :width="colWidth.checkbox" align="center" fixed="left" />
        <el-table-column
          label="仪器设备编号"
          prop="deviceNumber"
          :width="colWidth.orderNo"
          align="left"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div>
              <el-tag v-if="row.fixedFlag" size="mini" class="title-status" type="danger">维修</el-tag>
              {{ row.deviceNumber || '--' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="仪器设备名称" prop="name" :width="colWidth.orderNo" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.name || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="型号规格" prop="model" min-width="80" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.model }}</span>
          </template>
        </el-table-column>
        <el-table-column label="到货日期" prop="arrivalDate" align="left" :width="colWidth.date">
          <template #default="{ row }">
            <div class="nowrap">{{ formatDate(row.arrivalDate) || '--' }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="生产厂家"
          prop="equipmentManufactureName"
          :width="colWidth.departmentUnit"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span>{{ row.equipmentManufactureName || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="计量截止有效期" prop="validEndDate" align="left" :width="colWidth.date">
          <template #default="{ row }">
            <div>{{ formatDate(row.validEndDate) || '--' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="已使用年限" prop="measurementCycle" :width="colWidth.cycle">
          <template #default="{ row }">
            <span>{{ row.workingLife || '--' }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :page="listQuery.page"
        :limit="listQuery.limit"
        :total="total"
        @pagination="getTableList"
      />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
// getCurrentInstance
import { reactive, ref, toRefs, watch, nextTick } from 'vue';
import { useStore } from 'vuex';
import Pagination from '@/components/Pagination';
import { getLoginInfo } from '@/utils/auth';
import { getPredict } from '@/api/scrapping';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
export default {
  name: 'DialogEquipment',
  components: { Pagination },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    already: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    const store = useStore().state;
    const state = reactive({
      type: '', // 弹出窗类型
      condition: '',
      dialogLoading: false, // 弹出窗loading
      formData: {
        responsibleBy: getLoginInfo().accountId,
        planDate: []
      }, // 表单数据
      listQuery: {
        page: 1,
        limit: 20
      },
      deviceIdList: [], // 选中的设备id
      alreadyArray: [], // 已经选过的设备
      tableList: [],
      total: 0,
      dialogShow: false,
      ruleForm: ref(),
      listLoading: false,
      nameList: store.common.nameList,
      currentAccountId: getLoginInfo().accountId,
      tableRef: ref(null)
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.alreadyArray = newValue.already.map(item => {
          return item.deviceId;
        });
        getTableList();
      }
    });
    const getTableList = query => {
      const params = {
        condition: state.condition
      };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.dialogLoading = true;
      getPredict(params).then(res => {
        state.dialogLoading = false;
        if (res !== false) {
          state.total = res.data.data.totalCount;
          state.tableList = res.data.data.list;
          state.tableList.forEach(item => {
            item.workingLife = item.arrivalDate ? formatDateAlready(item.arrivalDate) : '';
            if (
              state.alreadyArray.some(value => {
                return value === item.deviceId;
              })
            ) {
              item.selected = true;
              nextTick(() => {
                state.tableRef.toggleRowSelection(item, true);
              });
            } else {
              item.selected = false;
            }
          });
        }
      });
    };
    const reset = () => {
      state.condition = '';
      state.deviceIdList = [];
      state.listQuery = {
        page: 1,
        limit: 20
      };
      state.total = 0;
      getTableList();
    };
    const onSubmit = () => {
      context.emit('closeDialog', state.deviceIdList);
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog');
    };
    const handleSelectionChange = val => {
      state.deviceIdList = val;
    };
    // 判断是否可以勾选
    const rowSelectable = (row, index) => {
      if (
        state.alreadyArray.some(item => {
          return item === row.deviceId;
        })
      ) {
        return false;
      } else {
        return true;
      }
    };
    const handleRowClick = row => {
      if (row && row.deviceId) {
        const rowIndex = state.tableList.findIndex(item => item.deviceId === row.deviceId);
        if (rowIndex !== -1) {
          row.selected = !row.selected;
          state.tableRef.toggleRowSelection(state.tableList[rowIndex], row.selected);
        }
      }
    };
    const selectChange = (selection, row) => {
      if (row && row.deviceId) {
        row.selected = !row.selected;
      }
    };
    // 计算已使用年限
    const formatDateAlready = date => {
      const ariveDate = new Date(date).getTime();
      if (ariveDate > new Date().getTime()) {
        return '--';
      }
      const days = (new Date().getTime() - ariveDate) / 1000 / 60 / 60 / 24;
      return Math.ceil(days / 365);
    };
    return {
      ...toRefs(state),
      onSubmit,
      rowSelectable,
      formatDateAlready,
      handleClose,
      handleSelectionChange,
      getTableList,
      formatDate,
      colWidth,
      drageHeader,
      reset,
      handleRowClick,
      selectChange
    };
  }
};
</script>
<style lang="scss" scoped>
::v-deep(.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 37.5rem) !important;
    overflow-y: auto;
  }
}
</style>
