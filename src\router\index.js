import { createRouter, createWebHistory } from 'vue-router';
// import Layout from '@/layout'
import { getMenuList, getLIMSConfig } from '@/utils/auth';
import { transformMenusToRoutes } from '@/utils/permission';

export const constantRoutes = [
  {
    path: '/',
    redirect: getLIMSConfig().VITE_HOME_PAGE
  },
  {
    path: '/login',
    name: 'Login',
    meta: { title: '登录', icon: '', affix: true, visible: false },
    component: () => import('@/views/login/index')
  },
  {
    path: '/auth-redirect',
    meta: { visible: false },
    component: () => import('@/views/login/auth-redirect')
  },
  {
    path: '/gotoAuth',
    meta: { visible: false },
    component: () => import('@/views/login/platform-redirect')
  },
  {
    path: '/401',
    meta: { visible: false },
    component: () => import('@/views/error-page/401')
  },
  {
    path: '/tes-home',
    name: 'TesHome',
    meta: { visible: false },
    component: () => import('@/views/home.vue')
  },
  {
    path: '/preview-report/:id/:mateType/:sampleId/:reportNo',
    name: 'ReportPreview',
    meta: { visible: false },
    component: () => import('@/components/Preview/index')
  },
  {
    path: '/preview-file',
    name: 'FilePreview',
    meta: { visible: false },
    component: () => import('@/components/PreviewPdf/index')
  },
  {
    path: '/preview-report-pdf/:id/:mateType/:sampleId/:reportNo',
    name: 'ReportPdfPreview',
    meta: { visible: false },
    component: () => import('@/components/PreviewPdf/index')
  },
  {
    path: '/add-or-update-operation-steps/:type/:capabilityName/:capabilityId/:id/:methodId',
    name: 'AddOrUpdateOperationSteps',
    meta: { visible: false },
    component: () => import('@/views/test-configuration/test-items/add-operation-steps.vue')
  },
  {
    path: '/loginMobile',
    name: 'LoginMobile',
    meta: { title: '登录', icon: '', affix: true, visible: false },
    component: () => import('@/views/login/mobile-login')
  },
  {
    path: '/uniappTemplate',
    name: 'UniappTemplate',
    meta: { title: '原始记录模板', icon: '', affix: true, visible: false },
    component: () => import('@/views/process-supervision/test-execution/uniapp-template.vue')
  }
  // {
  //   path: '/',
  //   component: Layout,
  //   redirect: '/home/<USER>',
  //   children: [
  //     {
  //       path: '/home/<USER>',
  //       id: '13018',
  //       component: () => import('@/views/home/<USER>'),
  //       name: 'Home',
  //       meta: { title: '首页', icon: 'tes-home', affix: true }
  //     }
  //   ]
  // }
  // {
  //   path: '/:pathMatch(.*)*',
  //   name: '404',
  //   component: () => import('@/views/error-page/404'),
  //   hidden: true
  // }
  // // 实验室管理
  // {
  //   path: '/labmanagement',
  //   component: Layout,
  //   alwaysShow: true,
  //   id: '29000',
  //   redirect: '/labmanagement',
  //   meta: { title: '实验室管理', icon: 'tes-sysgl' },
  //   children: [
  //     {
  //       path: 'labmanagement/samplestorage',
  //       id: '29001',
  //       component: () => import('@/views/process-supervision/sample-storage/samplestorage.vue'),
  //       name: 'samplestorage',
  //       meta: { title: '样品入库' }
  //     },
  //     {
  //       path: '/sampleinventory',
  //       id: '29002',
  //       component: () => import('@/views/process-supervision/sample-storage/sampleinventory.vue'),
  //       name: 'sampleinventory',
  //       meta: { title: '样品库存' }
  //     }
  //   ]
  // },
  // {
  //   path: '/gcdd',
  //   id: '23000',
  //   component: Layout,
  //   alwaysShow: true,
  //   name: 'ProcessSupervision',
  //   redirect: '/sample',
  //   meta: { title: '过程督导', icon: 'tes-gcdd' },
  //   children: [
  //     {
  //       path: '/inspection-application',
  //       id: '23018',
  //       component: () => import('@/views/process-supervision/inspection-application/inspection-application.vue'),
  //       name: 'InspectionApplication',
  //       meta: { title: '检验申请' }
  //     },
  //     {
  //       path: '/inspection-application/add',
  //       id: 'AddInspection',
  //       component: () => import('@/views/process-supervision/inspection-application/add-inspection.vue'),
  //       name: 'AddInspection',
  //       meta: { title: '查看检验单', activeMenu: '/inspection-application', parentName: 'InspectionApplication' }
  //     },
  //     {
  //       path: '/sample',
  //       id: '23001',
  //       component: () => import('@/views/process-supervision/sample-odrer/SampleOrder.vue'),
  //       name: 'SampleOrder',
  //       meta: { title: '样品下达' }
  //     },
  //     {
  //       path: '/sample/detail',
  //       id: '23001-1',
  //       component: () => import('@/views/process-supervision/sample-odrer/SampleOrdersDetail.vue'),
  //       name: 'SampleOrdersDetail',
  //       meta: { title: '样品详情', activeMenu: '/sample', parentName: 'SampleOrder' }
  //     },
  //     {
  //       path: '/experiment/detail',
  //       id: '204555',
  //       component: () => import('@/views/process-supervision/sample-odrer/SampleOrdersDetail.vue'),
  //       name: 'TestAllocationDetail',
  //       meta: { title: '样品详情', activeMenu: '/experiment/list', parentName: 'TestAllocation' }
  //     },
  //     {
  //       path: '/experiment/list',
  //       id: '23003',
  //       component: () => import('@/views/process-supervision/test-allocation/TestAllocation.vue'),
  //       name: 'TestAllocation',
  //       meta: { title: '检测分配' }
  //     },
  //     {
  //       path: '/allocation/:sampleId/:status/:isCurrentAccount',
  //       id: '23003-1',
  //       component: () => import('@/views/process-supervision/test-allocation/allocation.vue'),
  //       name: 'Allocation',
  //       meta: { title: '分配', activeMenu: '/experiment/list', parentName: 'TestAllocation' }
  //     },
  //     {
  //       path: '/executionlist/detail',
  //       id: '23003-2',
  //       component: () => import('@/views/process-supervision/sample-odrer/SampleOrdersDetail.vue'),
  //       name: 'executionDetail',
  //       meta: { title: '样品详情', activeMenu: '/execution/list', parentName: 'TestExecutionList' }
  //     },
  //     {
  //       path: '/execution/list',
  //       id: '23006',
  //       component: () => import('@/views/process-supervision/test-execution/TestExecutionList.vue'),
  //       name: 'TestExecutionList',
  //       meta: { title: '检测执行' }
  //     },
  //     {
  //       path: '/execution/detail',
  //       id: '23006-1',
  //       component: () => import('@/views/process-supervision/test-execution/ExecutionDetail.vue'),
  //       name: 'ExecutionDetail',
  //       meta: { title: '检测详情', activeMenu: '/execution/list', parentName: 'TestExecutionList' }
  //     },
  //     {
  //       path: '/execution/addRecord',
  //       id: '23006-2',
  //       component: () => import('@/views/process-supervision/test-execution/AddRecord.vue'),
  //       name: 'AddRecord',
  //       meta: { title: '检测详情', activeMenu: '/execution/list', parentName: 'TestExecutionList' }
  //     },
  //     {
  //       path: '/execution/printRecord',
  //       id: '23006-3',
  //       component: () => import('@/views/process-supervision/test-execution/printRecord.vue'),
  //       name: 'printRecord',
  //       meta: { title: '批量打印', activeMenu: '/execution/list', parentName: 'TestExecutionList' }
  //     },
  //     {
  //       path: '/recordReviewList',
  //       id: '23009',
  //       component: () => import('@/views/process-supervision/record-review/recordReviewList.vue'),
  //       name: 'RecordReviewList',
  //       meta: { title: '原始记录审核' }
  //     },
  //     {
  //       path: '/recordReviewDetail',
  //       id: '23010-1',
  //       component: () => import('@/views/process-supervision/test-execution/ExecutionDetail.vue'),
  //       name: 'recordReviewDetail',
  //       meta: { title: '原始记录审核详情', activeMenu: '/recordReviewList', parentName: 'RecordReviewList' }
  //     },
  //     {
  //       path: '/recordReview/addRecord',
  //       id: '11223',
  //       component: () => import('@/views/process-supervision/test-execution/AddRecord.vue'),
  //       name: 'RecordReviewAdd',
  //       meta: { title: '原始记录审核详情', activeMenu: '/recordReviewList', parentName: 'RecordReviewList' }
  //     },
  //     {
  //       path: '/recordReview/detail',
  //       id: '204556',
  //       component: () => import('@/views/process-supervision/sample-odrer/SampleOrdersDetail.vue'),
  //       name: 'recordReviewOrders',
  //       meta: { title: '样品详情', activeMenu: '/recordReviewList', parentName: 'RecordReviewList' }
  //     },
  //     {
  //       path: '/report/detail',
  //       id: '2045596',
  //       component: () => import('@/views/process-supervision/sample-odrer/SampleOrdersDetail.vue'),
  //       name: 'reportDetails',
  //       meta: { title: '样品详情', activeMenu: '/test-report', parentName: 'TestReport' }
  //     },
  //     {
  //       path: '/test-report',
  //       id: '23010',
  //       component: () => import('@/views/process-supervision/test-report/test-report.vue'),
  //       name: 'TestReport',
  //       meta: { title: '检测报告' }
  //     },
  //     {
  //       path: '/edit-report',
  //       id: '10103-edit',
  //       component: () => import('@/views/process-supervision/test-report/edit-report.vue'),
  //       name: 'EditReport',
  //       meta: { title: '检测报告编辑', activeMenu: '/test-report', parentName: 'TestReport' }
  //     },
  //     {
  //       path: '/detail-report',
  //       id: '10103-detail',
  //       component: () => import('@/views/process-supervision/test-report/test-report-detail.vue'),
  //       name: 'DetailReport',
  //       meta: { title: '检测报告详情', activeMenu: '/test-report', parentName: 'TestReport' }
  //     },
  //     {
  //       path: '/testBaseList',
  //       id: '23014',
  //       component: () => import('@/views/process-supervision/test-base/testBaseList.vue'),
  //       name: 'TestBaseList',
  //       meta: { title: '检测标准库' }
  //     },
  //     {
  //       path: '/datacollection',
  //       id: '23016',
  //       component: () => import('@/views/process-supervision/data-collection/dataCollection.vue'),
  //       name: 'Datacollection',
  //       meta: { title: '数据采集' }
  //     },
  //     {
  //       path: '/collectiondetail',
  //       id: '23017',
  //       component: () => import('@/views/process-supervision/data-collection/dataCollectionDetail.vue'),
  //       name: 'collectiondetail',
  //       meta: { title: '数据采集详情', activeMenu: '/datacollection', parentName: 'Datacollection' }
  //     }
  //
  //   ]
  // },
  // {
  //   path: '/display-analysis',
  //   component: Layout,
  //   id: '28000',
  //   alwaysShow: true,
  //   name: 'DisplayAnalysis',
  //   redirect: '/device/record',
  //   meta: { title: '展示分析', icon: 'tes-zsfx' },
  //   children: [
  //     {
  //       path: '/device/record',
  //       id: '28001',
  //       component: () => import('@/views/display-analysis/device-record.vue'),
  //       name: 'DeviceRecord',
  //       meta: { title: '设备使用记录' }
  //     },
  //     {
  //       path: '/sample/record',
  //       id: '28002',
  //       component: () => import('@/views/display-analysis/sample-record.vue'),
  //       name: 'SampleRecord',
  //       meta: { title: '样品领用记录' }
  //     }
  //   ]
  // },
  // {
  //   path: '/equipment',
  //   component: Layout,
  //   id: '24000',
  //   alwaysShow: true,
  //   meta: { title: '设备管理', icon: 'tes-Vector1' },
  //   redirect: '/equipmentList',
  //   children: [
  //     {
  //       path: 'equipmentList',
  //       id: '24001',
  //       component: () => import('@/views/instruments-equipment/equipmentList.vue'),
  //       name: 'EquipmentParameter',
  //       meta: { title: '设备台账', affix: true }
  //     }
  //   ]
  // },
  // // 试验配置
  // {
  //   path: '/syspz',
  //   component: Layout,
  //   id: '26000',
  //   alwaysShow: true,
  //   name: '444',
  //   redirect: '/capability/list',
  //   meta: { title: '试验配置', icon: 'tes-color-config' },
  //   children: [
  //     // {
  //     //   path: 'equipmentLists',
  //     //   id: '26001',
  //     //   component: () => import('@/views/instruments-equipment/equipmentList.vue'),
  //     //   name: 'Equipment',
  //     //   meta: { title: '检验策略', affix: true }
  //     // },
  //     {
  //       path: '/strategyList',
  //       id: '26001',
  //       component: () => import('@/views/inspection-strategy/strategyList.vue'),
  //       name: 'StrategyList',
  //       meta: { title: '检验策略' }
  //     },
  //     {
  //       path: '/capability/list',
  //       id: '26002',
  //       component: () => import('@/views/test-configuration/test-items/TestItem.vue'),
  //       name: 'TestItem',
  //       meta: { title: '检测项目' }
  //     },
  //     {
  //       path: '/experiment/experimentexcel',
  //       id: '26002-1',
  //       component: () => import('@/views/excelComponents/experimentExcel/experimentExcel'),
  //       name: 'ExperimentExcel',
  //       meta: { title: '检测项目模板', activeMenu: '/capability/list', parentName: 'TestItem' }
  //     },
  //     {
  //       path: '/material-classification',
  //       id: '26003',
  //       component: () => import('@/views/material-classification/MaterialClassification.vue'),
  //       name: 'MaterialClassification',
  //       meta: { title: '物资分类' }
  //     },
  //     {
  //       path: '/file-list',
  //       id: '26004',
  //       component: () => import('@/views/process-supervision/file-list/file-list.vue'),
  //       name: 'FileList',
  //       meta: { title: '文件清单' }
  //     }
  //   ]
  // },
  // {
  //   path: '/xxdb',
  //   component: Layout,
  //   id: '25000',
  //   alwaysShow: true,
  //   name: 'MessageAgent',
  //   redirect: '/my-message',
  //   meta: { title: '消息待办', icon: 'tes-xxdb' },
  //   children: [
  //     {
  //       path: '/my-message',
  //       id: '25001',
  //       component: () => import('@/views/message-agent/my-message.vue'),
  //       name: 'MyMessage',
  //       meta: { title: '我的消息' }
  //     },
  //     {
  //       path: '/my-agency',
  //       id: '25002',
  //       component: () => import('@/views/message-agent/my-agency.vue'),
  //       name: 'MyAgency',
  //       meta: { title: '我的待办' }
  //     },
  //     {
  //       path: '/message-management',
  //       id: '1321',
  //       component: () => import('@/views/Home.vue'),
  //       name: 'MessageManagement',
  //       hidden: true,
  //       meta: { title: '消息管理' }
  //     },
  //     {
  //       path: '/group-management',
  //       id: '1322',
  //       component: () => import('@/views/Home.vue'),
  //       name: 'GroupManagement',
  //       hidden: true,
  //       meta: { title: '分组管理' }
  //     }
  //   ]
  // },
  // {
  //   path: '/systemManage',
  //   component: Layout,
  //   id: '30000',
  //   alwaysShow: true,
  //   name: 'SystemManage',
  //   redirect: '/systemManage/dictionaryList',
  //   meta: { title: '系统配置', icon: 'tes-zsfx' },
  //   children: [
  //     {
  //       path: '/systemManage/dictionaryList',
  //       id: '30001',
  //       component: () => import('@/views/system-manage/dictionary/dictionaryList.vue'),
  //       name: 'Dictionary',
  //       meta: { title: '字典维护' }
  //     }
  //   ]
  // },
  // // 不良品处置
  // {
  //   path: '/unqualified',
  //   component: Layout,
  //   id: '27000',
  //   alwaysShow: true,
  //   name: 'Unqualified',
  //   redirect: '/unqualified-disposition',
  //   meta: { title: '不良品处置', icon: 'el-icon-close-notification' },
  //   children: [
  //     {
  //       path: '/unqualified-disposition',
  //       id: '27001',
  //       component: () => import('@/views/unqualified-disposition/unqualified-disposition.vue'),
  //       name: 'UnqualifiedDisposition',
  //       meta: { title: '不良品处置' }
  //     },
  //     {
  //       path: '/unqualified-disposition/register/:title/:id/:token',
  //       id: '27001-1',
  //       component: () => import('@/views/unqualified-disposition/register.vue'),
  //       name: 'Register',
  //       hidden: true,
  //       meta: { title: '不良品处置', activeMenu: '/unqualified-disposition' }
  //     }
  //   ]
  // },
  // {
  //   path: '/platformManage',
  //   component: Layout,
  //   id: 'platformManage',
  //   alwaysShow: true,
  //   name: 'PlatformManage',
  //   redirect: '/platform-manage/account-manage',
  //   meta: { title: '平台管理', icon: 'tes-component' },
  //   children: [
  //     {
  //       path: '/platform-manage/account-manage',
  //       id: 'platformManage',
  //       component: () => import('@/views/platform-manage/account-manage/account-manage.vue'),
  //       name: 'AccountManage',
  //       meta: { title: '账号管理' }
  //     },
  //     {
  //       path: '/platform-manage/login-log',
  //       id: 'platformManage',
  //       component: () => import('@/views/platform-manage/login-log.vue'),
  //       name: 'LoginLog',
  //       meta: { title: '登录日志' }
  //     },
  //     {
  //       path: '/platform-manage/tenant-manage',
  //       id: 'platformManage',
  //       component: () => import('@/views/platform-manage/tenant-manage/tenant-manage.vue'),
  //       name: 'TenantManage',
  //       meta: { title: '租户管理' }
  //     },
  //     {
  //       path: '/platform-manage/authority-manage',
  //       id: 'platformManage',
  //       component: () => import('@/views/platform-manage/authority-manage/authority-manage.vue'),
  //       name: 'AuthorityManage',
  //       meta: { title: '权限管理' }
  //     },
  //     {
  //       path: '/platform-manage/authority-manage/detail',
  //       id: 'authority-manage-detail',
  //       component: () => import('@/views/platform-manage/authority-manage/detail.vue'),
  //       name: 'AuthorityDetail',
  //       hidden: true,
  //       meta: { title: '权限详情', activeMenu: '/platform-manage/authority-manage' }
  //     },
  //     {
  //       path: '/platform-manage/resource-manage',
  //       id: 'platformManage',
  //       component: () => import('@/views/platform-manage/resource-manage/resource-manage.vue'),
  //       name: 'ResourceManage',
  //       meta: { title: '资源管理' }
  //     }
  //   ]
  // },
  // {
  //   path: '/businessManage',
  //   component: Layout,
  //   id: 'businessManage',
  //   alwaysShow: true,
  //   name: 'BusinessManage',
  //   redirect: '/business-manage/depart-management',
  //   meta: { title: '企业管理', icon: 'tes-test-exe' },
  //   children: [
  //     {
  //       path: '/business-manage/depart-management',
  //       id: 'businessManage',
  //       component: () => import('@/views/business-manage/depart-management.vue'),
  //       name: 'DepartManagement',
  //       meta: { title: '部门管理' }
  //     },
  //     {
  //       path: '/business-manage/role-management',
  //       id: 'businessManage',
  //       component: () => import('@/views/business-manage/role-management/index.vue'),
  //       name: 'RoleManagement',
  //       meta: { title: '角色管理' }
  //     },
  //     {
  //       path: '/business-manage/business-code',
  //       id: 'businessManage',
  //       component: () => import('@/views/business-manage/business-code.vue'),
  //       name: 'BusinessCode',
  //       meta: { title: '业务编码' }
  //     }
  //   ]
  // },
  // {
  //   path: '/userManage',
  //   component: Layout,
  //   id: 'userManage',
  //   alwaysShow: true,
  //   name: 'UserManage',
  //   redirect: '/user-manage/base-info',
  //   meta: { title: '个人管理', icon: 'tes-user' },
  //   children: [
  //     {
  //       path: '/user-manage/base-info',
  //       id: 'userManage',
  //       component: () => import('@/views/user-manage/base-info.vue'),
  //       name: 'BaseInfo',
  //       meta: { title: '基本信息' }
  //     },
  //     {
  //       path: '/user-manage/my-business',
  //       id: 'userManage',
  //       component: () => import('@/views/user-manage/my-business.vue'),
  //       name: 'MyBusiness',
  //       meta: { title: '我的企业' }
  //     }
  //   ]
  // }
];

// export const asyncRoutes = [
//   // 404 page must be placed at the end !!!
//   // { path: '/:pathMatch(.*)*', name: '404', redirect: '/404', hidden: true }
// ]

export const asyncRoutes = transformMenusToRoutes([], getMenuList());
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes.concat(asyncRoutes)
});
// router.beforeEach((to, from, next) => {
//   console.log('to.path' + to.path)
//   console.log('from.path' + from.path)
//   if (to.path === '/smart-charts/dataBoard' && from.path !== '/smart-charts/dataBoard') {
//     console.log('to.path' + to.path)
//     console.log('from.path' + from.path)
//     window.open(to.path, '_blank')
//     return false
//   } else if (to.path === '/smart-charts/dataBoard' && from.path === '/smart-charts/dataBoard') {
//     return false
//   } else {
//     next()
//   }
// })
// store.dispatch('permission/generateRoutes', getMenuList())
// console.log(getPermissionRouterList())
// // 由于是动态路由，这边页面刷新会导致路由失效，这边把动态路由保存到缓存中，要是更新权限，需要重新登录获取
// if (getPermissionRouterList().length === 0) {
// }
// 路由重置
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher;
}

export default router;
