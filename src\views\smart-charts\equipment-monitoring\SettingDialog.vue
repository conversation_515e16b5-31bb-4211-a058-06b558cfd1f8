<template>
  <el-dialog
    :model-value="dialogVisiable"
    title="选择设备"
    :close-on-click-modal="false"
    width="1090px"
    custom-class="DialogEquipmentSet"
    @opened="onDialogOpen"
    @close="onDialgClose"
  >
    <el-radio-group :model-value="selectViewId" size="mini" @change="handleViewChange">
      <el-radio-button v-for="(item, key) in newViewList" :key="key" :label="item.id">
        {{ item.isChanged ? '*' : '' }}{{ item.viewName }}{{ item.isDefault === 1 ? '（默认视图）' : '' }}
      </el-radio-button>
    </el-radio-group>
    <el-form
      ref="viewFormRef"
      :model="viewFormData"
      :rules="viewFormRules"
      label-width="100px"
      label-position="left"
      size="mini"
      class="view-form"
    >
      <el-row>
        <el-col :span="6">
          <el-form-item prop="viewName" label="视图名称：">
            <el-input
              v-model="viewFormData.viewName"
              maxlength="10"
              :disabled="!selectViewId"
              type="text"
              placeholder="请输入视图名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="7" style="margin-left: 10px">
          <el-checkbox v-model="viewFormData.isShow">显示</el-checkbox>
        </el-col>
      </el-row>
    </el-form>
    <el-row :gutter="20" class="tableContent">
      <el-col :span="9" class="deviceTable">
        <el-row class="deviceTableTh deviceTableTr">
          <el-col :span="11" :offset="2">设备编号</el-col>
          <el-col :span="11">设备名称</el-col>
        </el-row>
        <div v-if="deviceList.length" class="tableBody">
          <el-row
            v-for="row in deviceList"
            :key="row.id"
            class="deviceTableTr deviceTableLi cursorPointer"
            :class="{ activeRow: selectRow.deviceId === row.id }"
            @click="handleActiveRow(row)"
          >
            <el-col :span="2">
              <el-checkbox
                :model-value="hasCheckedWithCheckBox(row.id, 'device')"
                @change="val => handleChangeCheckBox(val, row, index, 'device')"
              />
            </el-col>
            <el-col :span="11">
              <div class="apostrophe">{{ row.deviceNumber || '--' }}</div>
            </el-col>
            <el-col :span="11">
              <div class="apostrophe">{{ row.name || '--' }}</div>
            </el-col>
          </el-row>
        </div>
        <div v-else class="nodeCodeList">
          <img src="@/assets/img/empty-data.png" alt="" />
          <span>暂无数据</span>
        </div>
      </el-col>
      <el-col :span="5" class="codeTable">
        <el-row class="deviceTableTh deviceTableTr">
          <el-col :span="21" :offset="2">设备码点</el-col>
        </el-row>
        <div v-if="codePointList.length" class="tableBody">
          <el-row
            v-for="(row, index) in codePointList"
            :key="row.id"
            class="deviceTableTr deviceTableLi cursorPointer"
            :class="{ activeRow: selectRow.codePointId === row.id }"
            @click="handleActiveCodePoint(row)"
          >
            <el-col :span="2">
              <el-checkbox
                :model-value="hasCheckedWithCheckBox(row.id, 'code')"
                @change="val => handleChangeCheckBox(val, row, index, 'code')"
              />
            </el-col>
            <el-col :span="21">
              <div class="apostrophe">{{ row.pointName || '--' }}</div>
            </el-col>
          </el-row>
        </div>
        <div v-else class="nodeCodeList">
          <img src="@/assets/img/empty-data.png" alt="" />
          <span>暂无数据</span>
        </div>
      </el-col>
      <el-col :span="10">
        <el-card class="box-card">
          <div class="cardTitle">码点设置</div>
          <el-form
            v-if="selectRow.codePointId"
            ref="codePointFormRef"
            :model="codePointFormData"
            :rules="codePointFormRules"
            label-position="top"
            size="small"
            label-width="auto"
          >
            <el-row>
              <el-col :span="11">
                <el-form-item prop="deviceCodePointNumber" label="码点编号：">
                  <el-input
                    v-model="codePointFormData.deviceCodePointNumber"
                    type="text"
                    disabled
                    placeholder="请输入码点编号"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="11" :offset="2">
                <el-form-item prop="deviceCodePointName" label="码点名称：">
                  <el-input
                    v-model="codePointFormData.deviceCodePointName"
                    type="text"
                    disabled
                    placeholder="请输入码点名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item prop="showType" label="展现方式：">
                  <el-select
                    v-model="codePointFormData.showType"
                    placeholder="请选择展现方式"
                    filterable
                    style="width: 100%"
                    @change="onFormDataChange"
                  >
                    <el-option :value="1" label="控制图" />
                    <el-option :value="2" label="折线图" />
                    <el-option :value="3" label="单数据" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="11" :offset="2">
                <el-form-item prop="associatedOrderNumber" label="关联订单编号：">
                  <el-input
                    v-model="codePointFormData.associatedOrderNumber"
                    type="text"
                    placeholder="请输入关联订单编号"
                    @change="onFormDataChange"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item prop="refreshRate" label="刷新频率(秒)：">
                  <el-input
                    v-model.number="codePointFormData.refreshRate"
                    type="text"
                    placeholder="请输入刷新频率(秒)"
                    @change="onFormDataChange"
                  />
                </el-form-item>
              </el-col>
              <el-col v-if="isControlChart" :span="11" :offset="2">
                <el-form-item prop="groupCount" label="分组数(个)：">
                  <el-input
                    v-model.number="codePointFormData.groupCount"
                    type="text"
                    placeholder="请输入分组数(个)"
                    @change="onFormDataChange"
                  />
                </el-form-item>
              </el-col>
              <el-col v-if="isControlChart" :span="11">
                <el-form-item prop="UCL" label="手动设置控制上线：">
                  <el-input
                    v-model="codePointFormData.UCL"
                    type="text"
                    placeholder="控制上线"
                    @change="onFormDataChange"
                  />
                </el-form-item>
              </el-col>
              <el-col v-if="isControlChart" :span="11" :offset="2">
                <el-form-item prop="LCL" label="手动设置控制下线：">
                  <el-input
                    v-model="codePointFormData.LCL"
                    type="text"
                    placeholder="控制下线"
                    @change="onFormDataChange"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div v-else class="nodeCodeList">
            <img src="@/assets/img/empty-data.png" alt="" />
            <span>暂无数据</span>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <div class="dialogBtn textRight">
      <el-button @click="handleCloseDialog(false)">取消</el-button>
      <el-button type="primary" size="small" @click="handleSaveView">保存视图</el-button>
      <el-button type="primary" size="small" @click="handleSaveViewAs">另存为视图</el-button>
    </div>
  </el-dialog>
  <el-dialog
    v-model="newViewNameDialogVisible"
    title="创建新视图"
    :close-on-click-modal="false"
    width="390px"
    custom-class="DialogEquipmentSet"
  >
    <el-form
      ref="newViewNameFormRef"
      :model="newViewNameFormData"
      :rules="newViewNameFormRules"
      label-width="100px"
      label-position="left"
      size="mini"
      class="view-form"
    >
      <el-form-item prop="viewName" label="视图名称：" required>
        <el-input
          ref="newViewNameInputRef"
          v-model="newViewNameFormData.viewName"
          type="text"
          placeholder="请输入视图名称"
        />
      </el-form-item>
    </el-form>
    <div class="dialogBtn textRight">
      <el-button size="small" @click="newViewNameDialogVisible = false">取消</el-button>
      <el-button type="primary" size="small" @click="handleSaveViewNameConfirm">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { reactive, ref, toRefs, computed, watch, nextTick, getCurrentInstance } from 'vue';
import { queryViewSettingById, saveViewSetting } from '@/api/equipmentMonitoring';
import { cloneDeep, isEqual, debounce } from 'lodash';

export default {
  name: 'SettingDialog',
  components: {},
  props: {
    dialogVisiable: {
      type: Boolean,
      default: false
    },
    currentView: {
      type: Object,
      default: () => ({})
    },
    viewList: {
      type: Array,
      default: () => []
    },
    deviceList: {
      type: Array,
      default: () => []
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      newViewList: [], // 视图列表
      codePointList: [], // 码点列表
      selectViewId: '', // 选择的视图
      selectRow: {
        // 选中的数据
        deviceId: '',
        codePointId: ''
      },
      isDefault: false,
      localShowViewId: '',
      deviceAssociatedCodePointIdMap: new Map(),
      codePointConfigMap: new Map(),
      // 视图设置表单
      viewFormData: {
        viewName: '',
        isShow: false
      },
      // 码点设置表单
      codePointFormData: {
        deviceCodePointNumber: '',
        deviceCodePointName: '',
        showType: 0,
        refreshRate: 0,
        groupCount: 0,
        UCL: '',
        LCL: ''
      },
      // 新增视图名称表单
      newViewNameFormData: {
        viewName: ''
      },
      newViewNameDialogVisible: false // 新增视图弹出窗
    });

    const viewFormRef = ref();
    const codePointFormRef = ref();
    const newViewNameFormRef = ref();
    const newViewNameInputRef = ref();

    let initNewViewList = []; // 初始化视图列表
    let initCodePointConfigMap = new Map(); // 初始化码点配置列表

    const viewFormRules = {
      viewName: { required: true, message: '请输入视图名称', trigger: 'change' }
    };
    const codePointFormRules = {
      refreshRate: { required: true, message: '请输入刷新频率(秒)', trigger: 'change' },
      groupCount: { required: true, message: '请输入分组数(个)', trigger: 'change' }
    };
    const newViewNameFormRules = {
      viewName: { required: true, message: '请输入视图名称', trigger: 'change' }
    };

    const isControlChart = computed(() => state.codePointFormData.showType === 1);

    watch(
      () => props.viewList,
      newViewList => {
        state.newViewList = newViewList.map(item => ({
          isChanged: false,
          ...item
        }));
        initNewViewList = cloneDeep(state.newViewList);
      }
    );

    const defaultFormData = {
      showType: 1,
      refreshRate: 30,
      groupCount: 1,
      UCL: '',
      LCL: ''
    };

    const onDialogOpen = () => {
      if (props.currentView.id) {
        state.localShowViewId = props.currentView.id;
        const viewItemIndex = state.newViewList.findIndex(item => item.id === props.currentView.id);
        if (viewItemIndex > -1) {
          state.newViewList.splice(viewItemIndex, 1, cloneDeep(props.currentView));
        }
        setViewData(props.currentView);
      }
    };

    const onDialgClose = () => {
      state.selectRow.deviceId = '';
      state.selectRow.codePointId = '';
      state.codePointConfigMap = new Map();
      state.deviceAssociatedCodePointIdMap = new Map();
      state.newViewList = cloneDeep(initNewViewList);
      context.emit('closeDialog', { isRefresh: false });
    };

    const setViewData = viewInfo => {
      state.selectViewId = viewInfo.id;
      state.viewFormData.viewName = viewInfo.viewName;
      state.viewFormData.isShow = viewInfo.id === state.localShowViewId;
      state.isDefault = viewInfo.isDefault === 1;
      state.codePointConfigMap = new Map();
      state.deviceAssociatedCodePointIdMap = new Map();
      if (viewInfo?.deviceList?.length > 0) {
        viewInfo.deviceList.forEach(deviceItem => {
          const codePointSelectedList = [];
          deviceItem.codePointList.forEach(item => {
            state.codePointConfigMap.set(item.deviceCodePointId, item);
            codePointSelectedList.push(item.deviceCodePointId);
          });
          state.deviceAssociatedCodePointIdMap.set(deviceItem.deviceId, codePointSelectedList);
        });
        const firstDeviceId = viewInfo.deviceList[0]?.deviceId;
        const firstCodePointId = viewInfo.deviceList[0]?.codePointList[0]?.deviceCodePointId;
        state.selectRow.deviceId = firstDeviceId;
        state.selectRow.codePointId = firstCodePointId;
        state.codePointList = props.deviceList.find(item => item.id === firstDeviceId)?.devicecodepointList || [];
        state.codePointFormData = viewInfo.deviceList[0]?.codePointList[0];
      } else {
        const firstDeviceId = props.deviceList[0].id;
        const firstCodePointId = props.deviceList[0].devicecodepointList[0].id;
        state.selectRow.deviceId = firstDeviceId;
        state.selectRow.codePointId = firstCodePointId;
        state.codePointFormData = cloneDeep(defaultFormData);
      }
      initCodePointConfigMap = cloneDeep(state.codePointConfigMap);
    };

    // 切换视图
    const handleViewChange = viewId => {
      state.selectViewId = viewId;
      getViewData(viewId);
    };

    // 通过视图Id获取视图详情
    const getViewData = viewId => {
      const viewItem = state.newViewList.find(item => item.id === viewId);
      if (isTempViewData(viewId) || viewItem.deviceList?.length > 0) {
        if (viewItem) {
          setViewData(viewItem);
        }
      } else {
        queryViewSettingById(viewId).then(res => {
          if (res) {
            const newViewItem = res.data.data;
            const viewItemIndex = state.newViewList.findIndex(item => item.id === newViewItem.id);
            if (viewItemIndex > -1) {
              state.newViewList.splice(viewItemIndex, 1, newViewItem);
            }
            setViewData(newViewItem);
          }
        });
      }
    };

    // 是否修改了数据
    const isModified = () => {
      return !isEqual(state.codePointConfigMap, initCodePointConfigMap);
    };

    // 是否为临时视图数据
    const isTempViewData = viewId => {
      return /^temp-(\d|\w)+/.test(viewId);
    };

    // 设备选中行
    const handleActiveRow = row => {
      state.selectRow.deviceId = row.id;
      state.codePointList = row.devicecodepointList;
      if (state.codePointList.length) {
        handleActiveCodePoint(state.codePointList[0]);
      } else {
        handleActiveCodePoint({});
      }
    };

    // 码点选中行
    const handleActiveCodePoint = row => {
      const codePointId = row.id;
      state.selectRow.codePointId = codePointId;
      if (state.codePointConfigMap.has(codePointId)) {
        state.codePointFormData = state.codePointConfigMap.get(codePointId);
      } else {
        state.codePointFormData = cloneDeep(defaultFormData);
        state.codePointFormData.deviceCodePointId = codePointId;
        state.codePointFormData.deviceCodePointNumber = row.pointNumber;
        state.codePointFormData.deviceCodePointName = row.pointName;
      }
    };

    // 设置已选择
    const handleChangeCheckBox = (val, row, index, type) => {
      if (type === 'device') {
        if (val) {
          // 选择设备
          state.deviceAssociatedCodePointIdMap.set(row.id, []);
        } else {
          // 删除已选中的设备
          state.deviceAssociatedCodePointIdMap.delete(row.id);
        }
      } else {
        const deviceId = state.selectRow.deviceId;
        const codePointSelect = state.deviceAssociatedCodePointIdMap.get(deviceId);
        if (val) {
          // 选择设备
          if (!hasCheckedWithCheckBox(deviceId, 'device')) {
            state.deviceAssociatedCodePointIdMap.set(deviceId, [row.id]);
          } else {
            codePointSelect.push(row.id);
            state.deviceAssociatedCodePointIdMap.set(deviceId, codePointSelect);
          }
          nextTick(onFormDataChange);
        } else {
          // 删除已选中的设备
          const findIndex = codePointSelect.findIndex(item => item === row.id);
          codePointSelect.splice(findIndex, 1);
          if (codePointSelect.length === 0) {
            state.deviceAssociatedCodePointIdMap.delete(deviceId);
          } else {
            state.deviceAssociatedCodePointIdMap.set(deviceId, codePointSelect);
          }
          state.codePointConfigMap.delete(row.id);
        }
      }
    };

    const hasCheckedWithCheckBox = (id, type) => {
      if (type === 'device') {
        return state.deviceAssociatedCodePointIdMap.has(id);
      } else {
        const codePointSelect = state.deviceAssociatedCodePointIdMap.get(state.selectRow.deviceId);
        return codePointSelect ? codePointSelect.includes(id) : false;
      }
    };

    const onFormDataChange = () => {
      state.codePointConfigMap.set(state.selectRow.codePointId, {
        deviceCodePointId: state.selectRow.codePointId,
        ...state.codePointFormData
      });
      const viewItemIndex = state.newViewList.findIndex(item => item.id === state.selectViewId);
      if (viewItemIndex > -1) {
        const viewItem = state.newViewList[viewItemIndex];
        viewItem.isChanged = isModified();
        state.newViewList.splice(viewItemIndex, 1, viewItem);
        delaySaveLocalViewData(viewItemIndex);
      }
    };

    const delaySaveLocalViewData = debounce(viewItemIndex => {
      const viewItem = state.newViewList[viewItemIndex];
      const deviceList = transformMapDataToDeviceList();
      viewItem.deviceList = deviceList;
      state.newViewList.splice(viewItemIndex, 1, viewItem);
    }, 100);

    const transformMapDataToDeviceList = () => {
      const deviceList = [];
      state.deviceAssociatedCodePointIdMap.forEach((codePointIdList, deviceId) => {
        if (codePointIdList?.length > 0) {
          const deviceItem = props.deviceList.find(item => item.id === deviceId);
          deviceList.push({
            deviceId,
            deviceName: deviceItem?.name,
            codePointList: codePointIdList.reduce((codePointList, codePointId) => {
              if (state.codePointConfigMap.has(codePointId)) {
                codePointList.push(state.codePointConfigMap.get(codePointId));
              }
              return codePointList;
            }, [])
          });
        }
      });
      return deviceList;
    };

    const viewDataModifiedConfirm = (msg = '当前页面有新添加未保存的视图，是否确认离开？') => {
      return new Promise((resolve, reject) => {
        const isChanged = state.newViewList.some(item => item.isChanged);
        if (isChanged || state.newViewList.length !== initNewViewList.length) {
          proxy
            .$confirm(msg, {
              customClass: 'is-modified-confirm',
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'warning',
              showClose: false,
              closeOnClickModal: false,
              closeOnPressEscape: false
            })
            .then(resolve.bind(null, true))
            .catch(reject.bind(null, true));
        } else {
          resolve(false);
        }
      });
    };

    // 按钮事件 - 关闭视图弹框
    const handleCloseDialog = isRefresh => {
      viewDataModifiedConfirm().then(isChanged => {
        context.emit('closeDialog', { isRefresh });
      });
    };

    // 按钮事件 - 确定视图名称后新建视图
    const handleSaveViewNameConfirm = () => {
      newViewNameFormRef.value.validate(valid => {
        if (valid) {
          const viewId = 'temp-' + Math.floor(Math.random() * 100000).toString();
          const copyDeviceList = state.newViewList.find(item => item.id === state.selectViewId)?.deviceList;
          state.newViewList.push({
            id: viewId,
            viewName: state.newViewNameFormData.viewName,
            isDefault: 0,
            isChanged: true,
            deviceList: copyDeviceList
          });
          handleViewChange(viewId);
          state.newViewNameDialogVisible = false;
        }
      });
    };

    // 按钮事件 - 保存视图
    const handleSaveView = () => {
      viewFormRef.value.validate(valid => {
        if (valid) {
          submitViewData();
        }
      });
    };

    // 按钮事件 - 另存为
    const handleSaveViewAs = () => {
      state.newViewNameDialogVisible = true;
      state.newViewNameFormData.viewName = '';
      nextTick(() => {
        newViewNameInputRef.value.focus();
      });
    };

    // 保存视图数据
    const submitViewData = () => {
      const newViewData = {
        id: isTempViewData(state.selectViewId) ? '' : state.selectViewId,
        viewName: state.viewFormData.viewName,
        isDefault: state.isDefault ? 1 : 0,
        deviceList: transformMapDataToDeviceList()
      };
      saveViewSetting(newViewData).then(res => {
        if (res) {
          let tempId = '';
          if (!newViewData.id) {
            tempId = state.selectViewId;
            newViewData.id = state.selectViewId = res.data.data.id;
          }
          newViewData.isChanged = false;
          const newViewDataIndex = state.newViewList.findIndex(item => item.id === (tempId || state.selectViewId));
          state.newViewList.splice(newViewDataIndex > -1 ? newViewDataIndex : state.newViewList.length, 1, newViewData);
          const newInitViewDataIndex = initNewViewList.findIndex(item => item.id === (tempId || state.selectViewId));
          initNewViewList.splice(
            newInitViewDataIndex > -1 ? newInitViewDataIndex : initNewViewList.length,
            1,
            cloneDeep(newViewData)
          );
          if (state.viewFormData.isShow) {
            localStorage.setItem(
              'EquipmentMonitoryingViewInfo',
              JSON.stringify({
                id: newViewData.id,
                viewName: state.viewFormData.viewName
              })
            );
          }
          handleCloseDialog(state.viewFormData.isShow);
        }
      });
    };

    return {
      ...toRefs(state),
      viewFormRef,
      codePointFormRef,
      newViewNameFormRef,
      newViewNameInputRef,
      viewFormRules,
      codePointFormRules,
      newViewNameFormRules,
      isControlChart,
      onDialogOpen,
      onDialgClose,
      handleViewChange,
      handleActiveRow,
      handleActiveCodePoint,
      handleChangeCheckBox,
      hasCheckedWithCheckBox,
      onFormDataChange,
      handleCloseDialog,
      handleSaveViewNameConfirm,
      handleSaveView,
      handleSaveViewAs
    };
  }
};
</script>
<style lang="scss" scoped>
.nodeCodeList {
  img {
    width: 90px;
    display: block;
    margin: 0 auto;
  }
  span {
    line-height: 20px;
    margin-bottom: 20px;
    display: inline-block;
    width: 100%;
    text-align: center;
    color: #fff;
  }
}
.tableBody {
  max-height: 450px;
  overflow-y: auto;
  padding: 5px;
  background-color: #3f7ab6;
}
.apostrophe {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.dialogBtn {
  margin: 30px 0 20px 0;
}
.textRight {
  text-align: right;
}

.cursorPointer {
  cursor: pointer;
}
.tableContent {
  // margin-top: 20px;
  color: #fff;
}
.deviceTableTr {
  line-height: 36px;
  margin-bottom: 10px;
  background-color: #3f7ab6;
  .el-col {
    padding: 0 10px;
  }
}
.cardTitle {
  font-size: 16px;
  line-height: 16px;
  color: #fff;
  margin-bottom: 10px;
}
.deviceTableLi {
  .el-col {
    background-color: #386eb0;
  }
  .el-col:first-child {
    text-align: center;
  }
  &:hover .el-col {
    background-color: #4280b9;
  }
  &.activeRow {
    .el-col {
      background-color: #7ad0ff;
    }
  }
}
.el-card {
  background-color: #4280b9;
  border: 0;
}
:deep(.el-card__body) {
  padding: 15px;
}
.view-form {
  margin-top: 15px;
}
:deep(.el-checkbox__label) {
  color: #fff;
}
</style>
<style lang="scss">
.DialogEquipmentSet .el-dialog__body .el-form-item__label {
  color: #fff !important;
}
.DialogEquipmentSet {
  border-radius: 10px;
  .el-button--primary,
  .el-button--primary:focus,
  .el-button--primary:hover {
    background: #245094;
    border-color: #245094;
    color: #fff;
  }
  .el-table tr {
    background-color: #fff !important;
  }
  .el-radio-button__inner {
    color: #fff;
    background-color: transparent !important;
  }
  // .el-radio-button__original-radio:checked + .el-radio-button__inner {
  //   background-color: #7AD0FF;
  //   border-color: #7AD0FF;
  //   box-shadow: 1px 0 0 0 #7AD0FF;
  // }
  .el-dialog__header {
    background: linear-gradient(90deg, #245094 0%, #3f7ab6 100%);
    border-radius: 10px 10px 0 0;
  }
  .el-dialog__title {
    color: #7ad0ff;
    font-weight: 700;
  }
  .el-dialog__body {
    background: linear-gradient(180deg, #2d59a5 0%, #4280b9 100%);
    border-radius: 0 0 10px 10px;
  }
  .el-button:hover {
    color: #000;
    border-color: #7ad0ff;
    background-color: #d4edfe;
  }
}
.is-modified-confirm {
  background: linear-gradient(180deg, #2d59a5 0%, #4280b9 100%);

  .el-message-box__message {
    color: #fff;
  }
}
</style>
