<template>
  <!-- 检测执行 项目视图详情页 -->
  <ListLayout
    :has-quick-query="false"
    :has-left-panel="true"
    :has-aside-padding="false"
    :aside-panel-width="asidePanelWidth"
    :aside-max-width="asideMaxWidth"
    :aside-min-width="asideMinWidth"
    aside-height="100%"
    main-height="100%"
  >
    <template #search-bar>
      <div class="page-title">检测项目：{{ currentCapabilityName }}</div>
    </template>
    <template #button-group>
      <el-button
        v-if="jsonData?.isower && getPermissionBtn('recordAddProject')"
        size="large"
        type="primary"
        icon="el-icon-files"
        @click="dialogRecipients = true"
        >样品领用</el-button
      >
      <el-button
        v-if="sampleList && sampleList.length !== 0"
        size="large"
        type="primary"
        icon="el-icon-collection-tag"
        @click="handleSafeguard()"
        @keyup.prevent
        @keydown.enter.prevent
        >试样分组</el-button
      >
    </template>
    <template #page-left-side>
      <div class="left-container">
        <div class="left-header">
          <div class="left-title">
            <span>检测样品</span>
            <el-popover placement="right" trigger="hover" popper-class="popover-content">
              <template #reference><i class="el-icon-info" /></template>
              <div class="content">
                <p>标识说明</p>
                <div class="popover-item"><span class="testing-icon text-copy">复</span>复测项目</div>
                <div class="popover-item"><span class="testing-icon text-origin">源</span>初始项目</div>
                <div class="popover-item"><span class="testing-icon text-change">改</span>数据修改</div>
                <div class="popover-item"><span class="testing-icon text-back">退</span>退回项目</div>
              </div>
            </el-popover>
          </div>
          <div class="urgent-wrapper">
            <el-checkbox v-model="isOwner" size="small" @change="changeOnlyOwner">仅看我的</el-checkbox>
            <el-checkbox v-model="isUrgent" size="small" @change="changeOnlyUrgent">仅看加急</el-checkbox>
          </div>
        </div>
        <el-menu
          :default-active="activeStatus?.toString()"
          class="header-menu"
          size="small"
          mode="horizontal"
          @select="switchStatus"
        >
          <el-menu-item
            v-for="(item, index) in statusList"
            :key="index"
            :index="item?.value?.toString()"
            class="header-menu-item"
          >
            {{ item.label }}
            {{ item.number }}
          </el-menu-item>
        </el-menu>
        <div class="query-wrapper">
          <SimpleQuery
            field-tip="样品编号/型号规格"
            :show-btn="false"
            @get-query-info="getQueryInfo"
            @reset-search="reset"
          />
        </div>
        <TestingList
          ref="tableRef"
          :key="tableKey"
          v-loading="dataLoading"
          :data="sampleList"
          item-type="sample"
          :style="{ flexGrow: 1 }"
          @row-click="handleRowClick"
        >
          <template #append>
            <li v-if="showLoadMore" class="load-more">
              <a href="javascript:;" class="load-more-button" @click="getMoreData()">点击加载更多</a>
            </li>
          </template>
        </TestingList>
      </div>
    </template>
    <div class="right-container">
      <div class="favorite-zone">
        <el-tooltip
          v-if="showAddRemindBtn || showUpdateRemindBtn"
          :content="showUpdateRemindBtn ? '修改提醒' : '添加提醒'"
          effect="light"
          placement="left"
        >
          <el-button
            plain
            size="small"
            :type="showUpdateRemindBtn ? 'warning' : 'primary'"
            :icon="showUpdateRemindBtn ? 'el-icon-message-solid' : 'el-icon-bell'"
            @click="handleRemind(jsonData, showUpdateRemindBtn ? 'edit' : 'add')"
            >{{ showUpdateRemindBtn ? '修改提醒' : '提醒' }}</el-button
          >
        </el-tooltip>
        <el-tooltip :content="jsonData?.isFavorite === 1 ? '取消收藏' : '添加收藏'" effect="light" placement="left">
          <el-button
            plain
            size="small"
            type="primary"
            :icon="jsonData?.isFavorite === 1 ? 'el-icon-star-on' : 'el-icon-star-off'"
            :class="jsonData?.isFavorite === 1 ? 'el-button-active' : ''"
            @click="handleFavorite(jsonData)"
            >{{ jsonData.isFavorite === 1 ? '取消收藏' : '收藏' }}</el-button
          >
        </el-tooltip>
      </div>
      <el-tabs v-model="activeName" tab-position="right" @tab-change="handleChangeTabs">
        <el-tab-pane label="原始记录" name="1">
          <div v-if="showTemplate" class="h-full">
            <first-tab-add-record :query-info="recordQueryInfo" @show-item-info="showItemInfo" />
          </div>
          <div v-else class="middle-container">
            <!-- 复测说明 -->
            <el-alert
              v-if="jsonData?.status && jsonData.retestSourceId !== '' && jsonData.retestReason && jsonData.status == 2"
              :title="'复测说明：' + jsonData?.retestReason"
              type="error"
            />
            <!-- 退回说明 -->
            <el-alert
              v-if="jsonData?.status && jsonData.isBack === 1 && jsonData.backReason && jsonData.status == 2"
              :title="'退回原因：' + jsonData?.backReason"
              type="warning"
            />
            <!-- 提醒说明 -->
            <el-alert v-if="showRemindTip" :title="remindTitle" type="success" />
            <div v-if="sampleList && sampleList.length !== 0" class="middle-content">
              <FirstTab
                v-if="!isShowRaw"
                :json-data="jsonData"
                :sample-collection-list="sampleCollectionList"
                @show-raw-template="showCurrentTemplate"
              />
              <FirstTabRaw
                v-if="isShowRaw"
                :json-data="jsonData"
                :left-ul="sampleList"
                @getdata="getdata"
                @getList="getList"
                @changeStatus="changeStatus"
                @show-raw-template="showCurrentTemplate"
                @show-item-info="showItemInfo"
              />
            </div>
            <div v-else class="empty-box">
              <el-empty :image="emptyImg" description="暂无模板" />
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="项目检验记录" name="2">
          <SecondTab :json-data="jsonData" :active-name="activeName" />
        </el-tab-pane>
        <el-tab-pane label="操作日志" name="3">
          <OperationLogs :experiment-id="jsonData?.experimentId" :active-name="activeName" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <module-safeguard
      :json-data="jsonData"
      :dialog-sheath="dialogSheath"
      @closexx="closexx"
      @save-color="saveSampleColor"
    />
    <!-- 样品领用弹出窗 -->
    <DialogRecipients
      :json-data="jsonData"
      :dialog-recipients="dialogRecipients"
      :sample-collection-list="sampleCollectionList"
      @closeRecipients="closeRecipients"
    />
    <!-- 提醒弹出窗 -->
    <el-dialog
      v-model="dialogRemind"
      :title="isAddRemind ? '新增提醒' : '编辑待提醒'"
      :close-on-click-modal="false"
      width="480px"
      custom-class="remind_dialog"
    >
      <el-form
        v-if="dialogRemind"
        ref="ruleForm"
        v-loading="dialogLoading"
        :model="formData"
        label-position="right"
        label-width="90px"
        size="small"
      >
        <el-form-item
          label="标题："
          prop="submit.title"
          :rules="{ required: true, message: '请输入标题', trigger: 'change' }"
        >
          <el-input
            v-model="formData.submit.title"
            v-trim
            size="small"
            type="text"
            maxlength="50"
            placeholder="请输入标题"
          />
        </el-form-item>
        <el-form-item
          label="试验员："
          prop="realOwnerIds"
          :rules="{
            required: true,
            message: '请选择试验员',
            trigger: 'change'
          }"
        >
          <el-select
            v-model="formData.realOwnerIds"
            clearable
            multiple
            placeholder="请选择"
            class="submit-form-item"
            style="width: 100%"
          >
            <el-option v-for="item in nameList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="等级："
          prop="submit.level"
          :rules="{ required: true, message: '请选择等级', trigger: 'change' }"
        >
          <el-select v-model="formData.submit.level" placeholder="请选择等级" size="small" style="width: 100%">
            <el-option label="较弱" :value="0" />
            <el-option label="一般" :value="1" />
            <el-option label="重要" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="截止时间："
          prop="submit.expireTime"
          :rules="{
            required: true,
            message: '请选择截止时间',
            trigger: 'change'
          }"
        >
          <el-date-picker
            v-model="formData.submit.expireTime"
            popper-class="no-atTheMoment"
            type="datetime"
            :disabled-date="disabledDate"
            clearable
            placeholder="请选择截止时间"
            size="small"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item
          label="提醒频率："
          prop="submit.remindTimeList"
          :rules="{
            required: true,
            message: '请选择截止时间',
            trigger: 'change'
          }"
        >
          <el-select
            v-model="formData.submit.remindTimeList"
            multiple
            placeholder="请选择提醒频率"
            clearable
            size="small"
            style="width: 73%"
          >
            <el-option v-for="(val, key) in scheduleList" :key="key" :label="val" :value="key" />
          </el-select>
          <el-date-picker
            ref="scheduleRef"
            v-model="scheduleTime"
            type="datetime"
            popper-class="no-atTheMoment"
            clearable
            :editable="false"
            class="scheduleTime"
            @change="changeSchedule"
          />
          <span class="blue-color cursor" style="float: right; margin-right: 0" @click="addScheduleTime"
            >添加指定时间</span
          >
        </el-form-item>
        <el-form-item label="内容：" prop="submit.content">
          <el-input
            v-model="formData.submit.content"
            type="textarea"
            maxlength="100"
            :rows="2"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button size="small" :loading="dialogLoading" @click="dialogRemind = false">取 消</el-button>
          <el-button v-if="!isAddRemind" size="small" :loading="dialogLoading" type="danger" @click="deleteRemind()"
            >删 除</el-button
          >
          <el-button size="small" :loading="dialogLoading" type="primary" @click="onSubmitRemind()">确 认</el-button>
        </span>
      </template>
    </el-dialog>
  </ListLayout>
</template>

<script>
import { reactive, toRefs, ref, onMounted, watch, getCurrentInstance, computed } from 'vue';
import { useRoute } from 'vue-router';
import TestingList from './components/TestingList';
import FirstTab from './components/FirstTab';
import FirstTabRaw from './components/FirstTabRaw';
import FirstTabAddRecord from './components/FirstTabAddRecord';
import SecondTab from './secondTab';
import OperationLogs from './operationLogs';
import { getDictionary } from '@/api/user';
import DialogRecipients from './DialogRecipients.vue';
import ModuleSafeguard from '@/components/BusinessComponents/ModuleSafeguard';
import SimpleQuery from '@/components/SimpleQuery';
import ListLayout from '@/components/ListLayout';
import { formatDateTime } from '@/utils/formatTime';
import {
  // capabilityBysamplesId,
  experimentmongodatainfo,
  TemplateIdByexperimentId,
  favoriteItem,
  unFavoriteItem,
  getItemRemindType,
  getRemindInfo,
  saveRemindInfo,
  deleteRemindApi,
  sampleByCapabilityId
} from '@/api/execution';
import { getSampleCollectionList } from '@/api/sampleItemTest';
import { getNamesByid, getPermissionBtn, getNameOptionaByid } from '@/utils/common';
import { getLoginInfo } from '@/utils/auth';
import _ from 'lodash';
import store from '@/store';
import { colWidth } from '@/data/tableStyle';
import { getItemFirstRow } from '@/services/testExecutionService';
import emptyImg from '@/assets/img/empty-template.png';
import { decryptCBC } from '@/utils/ASE';

export default {
  name: 'ItemExecutionDetail',
  components: {
    TestingList,
    FirstTab,
    SecondTab,
    OperationLogs,
    DialogRecipients,
    FirstTabRaw,
    ModuleSafeguard,
    ListLayout,
    SimpleQuery,
    FirstTabAddRecord
  },
  setup(ctx) {
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    watch(
      () => route.query,
      () => {
        // getdata()
      }
    );
    const state = reactive({
      asidePanelWidth: 400,
      asideMinWidth: 370,
      asideMaxWidth: 520,
      scheduleTime: '',
      scheduleList: {
        0: '截止时',
        15: '截止前15分钟',
        60: '截止前1小时',
        180: '截止前3小时',
        1440: '截止前1天'
      },
      sampleCollectionList: [], // 样品领用记录
      scheduleRef: ref(),
      dialogRemind: false,
      dialogLoading: false,
      isAddRemind: false,
      nameList: store.state.common.nameList,
      nameJson: {},
      disabledDate: time => {
        return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
      },
      ruleForm: ref(),
      levelJson: {}, // 级别
      levelArray: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ], // 级别
      formData: {
        realOwnerIds: [getLoginInfo().accountId],
        submit: {
          remindTimeList: []
        }
      },
      remindType: {}, // 待办状态
      remindInfo: {
        title: '',
        content: '',
        expireTime: ''
      },
      tableKey: ref(),
      tableRef: ref(),
      testipt: '',
      dialogSheath: false,
      dialogRecipients: false, // 样品领用弹出窗
      activeItem: null,
      activeStatus: route.query.status,
      accountId: getLoginInfo().accountId,
      leftList: [],
      samplesId: route.query.samplesId,
      capabilityId: route.query.capabilityId,
      avtivestatus: route.query.avtivestatus,
      orderId: '',
      isShowRaw: true, // 区分tab原始记录的两种情况
      activeName: '1', // 右侧tab的选中
      jsonData: {
        status: null
      },
      statusList: [
        { value: 2, label: '待提交', number: 0 },
        { value: 3, label: '待审核', number: 0 },
        { value: 5, label: '已通过', number: 0 }
      ],
      statusClass: {
        3: 'fu',
        4: 'yuan'
      },
      giveMeChecked: false,
      isUrgent: false,
      isOwner: true,
      allTableList: [],
      tableList: [],
      showTemplate: false,
      recordQueryInfo: {
        experimentId: '',
        samplesId: '',
        new: '',
        capabilityId: '',
        type: 'check'
      },
      sampleParams: {
        capabilityId: route.query.capabilityId,
        isAsc: true,
        isUrgent: null,
        limit: 20,
        orderBy: '',
        param: '',
        status: route.query.status || 2,
        page: 0,
        ownerId: getLoginInfo().accountId,
        tableQueryParamList: []
      },
      sampleList: [],
      pageInfo: {
        currPage: 0,
        pageSize: 0,
        totalCount: 0,
        totalPage: 0
      },
      dataLoading: false,
      firstRows: [],
      showLoadMore: true,
      currentCapabilityName: route.query.capabilityName
    });
    onMounted(() => {
      // 左侧表格的高亮行
    });
    const filterName = () => {
      state.nameList.forEach(item => {
        state.nameJson[item.id] = item.username;
      });
    };

    function getCurrentItemSamples(isFirstRowData = false) {
      return new Promise((resolve, reject) => {
        const currentParams = JSON.parse(JSON.stringify(state.sampleParams));
        state.dataLoading = true;
        if (isFirstRowData) {
          currentParams.param = route.query.sampleNo;
        }
        sampleByCapabilityId(currentParams).then(res => {
          try {
            state.dataLoading = false;
            if (res) {
              const result = res.data.data;
              if (isFirstRowData) {
                if (result.list.length > 0) {
                  if (state.isOwner && result.list[0].ownerIds.split(',').includes(state.accountId)) {
                    state.firstRows = [].concat(result.list);
                    state.sampleList = [].concat(result.list);
                  } else {
                    getCurrentItemSamples();
                  }
                }
              } else {
                if (
                  state.firstRows?.length > 0 &&
                  state.activeStatus?.toString() === route.query.status &&
                  result.list.length > 0
                ) {
                  let validRowList = [].concat(state.firstRows);
                  // 过滤仅看加急
                  if (state.sampleParams.isUrgent) {
                    validRowList = validRowList.filter(item => item.isUrgent === 1);
                  }
                  // 过滤仅看我的
                  if (state.sampleParams.ownerId) {
                    validRowList = validRowList.filter(item =>
                      item.ownerIds?.split(',').includes(state.sampleParams.ownerId)
                    );
                  }
                  // 置顶
                  state.firstRows.forEach(row => {
                    const existedIndex = result.list.findIndex(item => item.secSampleNum === row.secSampleNum);
                    if (existedIndex > -1) {
                      result.list.splice(existedIndex, 1);
                    }
                  });
                  state.sampleList = validRowList.concat(result.list);
                } else {
                  state.sampleList = result.list;
                }
              }
              state.pageInfo.currPage = isFirstRowData ? -1 : result.currPage;
              state.pageInfo.pageSize = result.pageSize;
              state.pageInfo.totalCount = result.totalCount;
              state.pageInfo.totalPage = result.totalPage;
              state.statusList[0].number = result.uncommitted;
              state.statusList[1].number = result.unconfirmed;
              state.statusList[2].number = result.passed;
              if (isFirstRowData) {
                if (state.activeStatus.toString() === route.query.status) {
                  if (
                    (route.query.status === '2' && result.uncommitted === 1) ||
                    (route.query.status === '3' && result.unconfirmed === 1) ||
                    (route.query.status === '5' && result.passed === 1)
                  ) {
                    state.showLoadMore = false;
                  } else {
                    state.showLoadMore = result.list.length > 0;
                  }
                }
              } else {
                if (result.totalPage === result.currPage || result.totalCount === 0) {
                  state.showLoadMore = false;
                } else {
                  state.showLoadMore = true;
                }
              }
              resolve(true);
            } else {
              resolve(false);
            }
          } catch {
            reject('样品列表接口异常');
          }
        });
      });
    }

    /**
     * 仅更新当前选中的数据
     */
    function updateCurrentData() {
      return new Promise((resolve, reject) => {
        const currentParams = JSON.parse(JSON.stringify(state.sampleParams));
        currentParams.param = state.jsonData.secSampleNum;
        sampleByCapabilityId(currentParams).then(res => {
          try {
            if (res) {
              const result = res.data.data;
              if (result.list.length > 0) {
                const currentData =
                  result.list.find(
                    item =>
                      item.secSampleNum === state.jsonData.secSampleNum && item.isRetest === state.jsonData.isRetest
                  ) || state.jsonData;
                currentData.isower = setIsOwner(state.jsonData.ownerIds);
                state.jsonData = currentData;
                state.activeItem = currentData;
                const sampleIndex = state.sampleList.findIndex(
                  item => item.secSampleNum === state.jsonData.secSampleNum && item.isRetest === state.jsonData.isRetest
                );
                if (sampleIndex !== -1) {
                  state.sampleList[sampleIndex] = JSON.parse(JSON.stringify(currentData));
                  state.tableRef.setCurrentRow(state.sampleList[sampleIndex]);
                  handleRowClick(state.sampleList[sampleIndex]);
                }
              }
              resolve(true);
            } else {
              resolve(false);
            }
          } catch {
            reject('更新当前样品异常');
          }
        });
      });
    }

    // 局部刷新左边列表数据
    const getList = () => {
      getCurrentItemSamples().then(res => {
        if (res) {
          getRemindType();
          changeStatus(state.activeStatus);
        }
      });
    };

    // 获取待办状态
    const getRemindType = (sampleId = state.activeItem?.samplesId, capabilityId = state.activeItem?.capabilityId) => {
      if (state.sampleList.length > 0 && sampleId && capabilityId) {
        const params = {
          sampleId,
          capabilityIds: [capabilityId]
        };
        // params.capabilityIds.push(state.activeItem?.capabilityId)
        getItemRemindType(params).then(res => {
          if (res) {
            state.remindType = res.data.data;
          }
        });
      }
    };

    // 刷新数据 getdata
    const getdata = (isFirstRowData = false) => {
      state.dataLoading = true;
      getCurrentItemSamples(isFirstRowData).then(res => {
        state.dataLoading = false;
        if (res) {
          if (state.samplesId && route.query.samplesId !== '') {
            const currentIndex = state.sampleList.findIndex(item => {
              return item.samplesId === state.samplesId;
            });
            if (state.sampleList.length > 0) {
              state.activeItem = currentIndex === -1 ? state.sampleList[0] : state.sampleList[currentIndex];
            }

            state.jsonData = state.activeItem ? JSON.parse(JSON.stringify(state.activeItem)) : getItemFirstRow();
            if (state.sampleList.length > 0) {
              handleRowClick(state.activeItem);
              state.tableRef.setCurrentRow(state.activeItem);
            }
          } else {
            if (state.avtivestatus) {
              if (state.sampleList.length !== 0) {
                state.activeItem = state.sampleList[0];
                if (state.remindType[state.jsonData.capabilityId]) {
                  getRemind(state.activeItem);
                }
                changeStatus(state.activeStatus);
              }
            } else {
              if (state.sampleList.length > 0) {
                state.activeItem = JSON.parse(JSON.stringify(state.sampleList[0]));
              } else {
                state.activeItem = null;
              }
              if (state.sampleList.length > 0) {
                handleRowClick(state.activeItem);
                state.tableRef.setCurrentRow(state.activeItem);
              }
            }
          }
        }
      });
    };

    const changeStatus = val => {
      state.activeStatus = typeof val === 'string' ? Number(val) : val;
      if (state.sampleList.length !== 0) {
        handleRowClick(state.sampleList[0]);
        state.tableRef.setCurrentRow(state.sampleList[0]);
      }
    };

    const initDictionary = () => {
      getDictionary(13).then(res => {
        if (res) {
          state.levelArray[0].group = [];
          state.levelArray[1].group = [];
          res.data.data.dictionaryoption.forEach(item => {
            if (item.status === 1) {
              state.levelArray[0].group.push(item);
            } else {
              state.levelArray[1].group.push(item);
            }
            state.levelJson[item.code] = item.name;
          });
        }
      });
    };
    // 点击下载模板
    const handleRowClick = row => {
      if (row && row.capabilityId && row.sampleId) {
        if (state.showTemplate) {
          state.activeItem = JSON.parse(JSON.stringify(row));
          state.showTemplate = false;
        }
        state.jsonData = {
          ...JSON.parse(JSON.stringify(row)),
          disabled: false,
          isower: setIsOwner(row.ownerIds)
        };
        state.capabilityId = row.capabilityId;
        if (row.experimentId) {
          downTemplate(row.experimentId);
        }
        getRemind(row);
      }
    };

    function setIsOwner(ownerIds) {
      if (ownerIds) {
        return _.indexOf(ownerIds.split(','), state.accountId) !== -1;
      } else {
        return false;
      }
    }

    const getRemind = row => {
      if (row.capabilityId && row.sampleId) {
        getRemindInfo({
          capabilityId: row.capabilityId,
          sampleId: row.sampleId
        }).then(res => {
          if (res) {
            const data = res.data.data;
            if (JSON.stringify(data) !== '{}') {
              state.formData.submit = JSON.parse(JSON.stringify(data));
              state.formData.realOwnerIds = data.testerList.map(item => {
                return item.userId;
              });
              state.remindInfo.title = data.title;
              state.remindInfo.content = data.content;
              state.remindInfo.expireTime = data.expireTime;
            }
            getRemindType(row.sampleId, row.capabilityId);
          }
        });
      }
    };
    // TemplateIdByexperimentId
    // 下载原始模板
    const downTemplate = experimentId => {
      TemplateIdByexperimentId(experimentId).then(res => {
        if (res) {
          state.isShowRaw = !!res.data.data.html;
          experimentmongodatainfo(experimentId).then(resdata => {
            if (resdata) {
              state.jsonData = {
                ...state.jsonData,
                excelHtml: decryptCBC(res.data.data.html),
                fileNo: res.data.data.fileNo,
                showType: res.data.data.showType,
                source: res.data.data.source,
                experimentData: resdata.data.data,
                templateId: resdata.data.data.templateId,
                realReviewerIdimg: resdata.data.data.reviewerSignUrl.split(','),
                realOwnerIdsimgs: resdata.data.data.ownerSignUrls.split(',')
              };
            }
          });
        }
      });
    };
    // // 获取签名
    // const getrealOwnerIdsimgs = async ids => {
    //   // if(state.isflag){
    //   state.isflag = false
    //   if (ids) {
    //     const ul = ids.split(',')
    //     const newPeopleInfoList = await store.dispatch(
    //       'common/getSignatureImg',
    //       ul
    //     )
    //     return new Promise(resolve => {
    //       resolve(newPeopleInfoList)
    //     })
    //   }
    // }
    // 线芯维护弹屏
    const handleSafeguard = () => {
      state.dialogSheath = true;
    };
    const closexx = () => {
      state.dialogSheath = false;
    };

    const saveSampleColor = colorList => {
      state.jsonData.experimentData.coreRecord.coreColourList = colorList;
      state.jsonData.experimentData.saveColorNumber = colorList.length;
    };

    const closeRecipients = val => {
      state.dialogRecipients = false;
      if (val) {
        getRecipientsList();
      }
    };
    const getRecipientsList = () => {
      getSampleCollectionList(route.query.samplesId).then(res => {
        if (res) {
          state.sampleCollectionList = res.data.data;
        }
      });
    };
    const handleChangeTabs = val => {};
    // 打开样品领用记录添加编辑弹出框
    const handleAddDialog = () => {
      state.dialogSubmit = true;
    };
    const handleRemind = (row, type) => {
      state.dialogRemind = true;
      if (type === 'add') {
        state.isAddRemind = true;
        state.formData = {
          realOwnerIds: [state.accountId],
          submit: {
            title: '【' + row.secSampleNum + '】的【' + row.capabilityName + '】等待检测',
            remindTimeList: ['15'],
            level: 1,
            capabilityName: row.capabilityName,
            capabilityId: row.capabilityId,
            sampleId: row.sampleId,
            sampleNo: row.secSampleNum
          }
        };
      } else {
        state.isAddRemind = false;
      }
    };
    const handleFavorite = row => {
      if (row.isFavorite === 0) {
        favoriteItem(row.experimentId).then(res => {
          if (res) {
            proxy.$message.success(res.data.data);
            updateCurrentData();
          }
        });
      } else {
        unFavoriteItem(row.experimentId).then(res => {
          if (res) {
            proxy.$message.success(res.data.data);
            updateCurrentData();
          }
        });
      }
    };
    const onSubmitRemind = () => {
      state.ruleForm.validate().then(valid => {
        if (valid) {
          const testerList = [];
          state.formData.realOwnerIds.forEach(item => {
            testerList.push({
              userId: item,
              userName: state.nameJson[item]
            });
          });
          const param = {
            ...state.formData.submit,
            expireTime: formatDateTime(state.formData.submit.expireTime),
            testerList: testerList
          };
          const timeDifference = new Date(param.expireTime).getTime() - new Date().getTime();
          const leave1 = timeDifference % (24 * 3600 * 1000); // 计算天数后剩余的毫秒数
          const leave2 = leave1 % (3600 * 1000); // 计算小时数后剩余的毫秒数
          const minutes = Math.floor(leave2 / (60 * 1000));
          if (minutes < 2 && state.remindInfo.expireTime !== param.expireTime) {
            proxy.$message.warning('截止时间请选择至少两分钟后');
            return false;
          }
          saveRemindInfo(param).then(res => {
            if (res) {
              proxy.$message.success(res.data.message);
              state.dialogRemind = false;
              updateCurrentData();
            }
          });
        }
      });
    };
    const deleteRemind = () => {
      proxy
        .$confirm('是否删除？删除后将不能还原。', '删除确认', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          deleteRemindApi({
            sampleId: state.formData.submit.sampleId,
            capabilityId: state.formData.submit.capabilityId
          }).then(res => {
            if (res) {
              state.dialogRemind = false;
              proxy.$message.success(res.data.message);
              getRemindType(state.formData.submit.sampleId, state.formData.submit.capabilityId);
            }
          });
        })
        .catch(() => {});
    };
    // 添加指定日期
    const addScheduleTime = () => {
      state.scheduleRef.focus();
    };
    const changeSchedule = val => {
      if (val) {
        const data = formatDateTime(val);
        if (
          !state.formData.submit.remindTimeList.some(item => {
            return item === data;
          })
        ) {
          state.formData.submit.remindTimeList.push(data);
        }
        state.scheduleTime = '';
      }
    };

    // #region 查询

    const reset = val => {
      state.sampleParams.param = '';
      // state.sampleParams.tableQueryParamList = []
      resetPageInfo();
      getdata();
    };

    const getQueryInfo = info => {
      state.sampleParams.param = info;
      resetPageInfo();
      // state.sampleParams.tableQueryParamList = info.tableQueryParamList
      getdata();
    };

    const resetPageInfo = () => {
      state.sampleParams.page = 0;
      state.sampleList = [];
      state.pageInfo.currPage = -1;
      state.pageInfo.totalPage = 1;
      state.pageInfo.totalCount = 0;
    };

    const changeOnlyOwner = val => {
      state.sampleParams.ownerId = val ? state.accountId : '';
      state.dataLoading = true;
      resetPageInfo();
      getdata();
    };

    const changeOnlyUrgent = val => {
      state.sampleParams.isUrgent = val || null;
      resetPageInfo();
      getdata();
    };

    const switchStatus = val => {
      state.activeStatus = typeof val === 'string' ? Number(val) : val;
      if (val) {
        state.sampleParams.status = val;
        resetPageInfo();
        getCurrentItemSamples().then(res => {
          if (res) {
            getRemindType();
            if (state.sampleList.length !== 0) {
              handleRowClick(state.sampleList[0]);
              state.tableRef.setCurrentRow(state.sampleList[0]);
            }
          }
        });
      }
    };

    // #endregion

    const showCurrentTemplate = type => {
      if (type) {
        state.recordQueryInfo = {
          experimentId: state.jsonData.experimentId,
          samplesId: state.jsonData.samplesId,
          new: true,
          capabilityId: state.jsonData.capabilityId,
          type: type
        };
        state.showTemplate = true;
      }
    };

    // #region 新增原始记录 FirstTabAddRecord

    const showItemInfo = val => {
      if (val) {
        if (val === 3 || val === 4) {
          // 保存提交后调整下一项
          const submitIndex = state.sampleList.findIndex(item => item.sampleId === state.jsonData.sampleId);
          if (submitIndex !== -1 && submitIndex < state.sampleList.length - 1) {
            state.activeItem = JSON.parse(JSON.stringify(state.sampleList[submitIndex + 1]));
            state.samplesId = state.activeItem?.samplesId;
          }
        } else if (val === 1) {
          const submitIndex = state.sampleList.findIndex(item => item.sampleId === state.jsonData.sampleId);
          state.activeItem = JSON.parse(JSON.stringify(state.sampleList[submitIndex]));
          state.samplesId = state.activeItem?.samplesId;
        }
        getRecipientsList();
        getdata();
        state.showTemplate = false;
      }
    };

    // #endregion

    // #region 加载更多数据
    const getMoreData = () => {
      if (state.pageInfo.currPage < state.pageInfo.totalPage) {
        state.sampleParams.page = state.pageInfo.currPage + 1;
        getRecipientsList();
        getdata();
      }
    };
    // #endregion

    // #region 立即执行的函数

    filterName();
    getRecipientsList();
    getdata(true);

    // #endregion

    const showAddRemindBtn = computed({
      get: () =>
        state.jsonData?.capabilityId &&
        !state.remindType[state.jsonData.capabilityId] &&
        state.activeStatus.toString() === '2'
    });

    const showUpdateRemindBtn = computed({
      get: () =>
        state.jsonData?.capabilityId &&
        state.remindType[state.jsonData.capabilityId] &&
        state.activeStatus.toString() === '2'
    });

    const showRemindTip = computed({
      get: () => state.jsonData?.capabilityId && state.remindType[state.jsonData.capabilityId]
    });

    const remindTitle = computed(() => {
      return `${state.remindInfo.title}提醒：${state.remindInfo.expireTime} 时截止${
        state.remindInfo.content ? '，内容：' : ''
      }${state.remindInfo.content}`;
    });

    return {
      ...toRefs(state),
      colWidth,
      getdata,
      getRemind,
      getRecipientsList,
      deleteRemind,
      addScheduleTime,
      changeSchedule,
      getRemindType,
      formatDateTime,
      handleFavorite,
      filterName,
      onSubmitRemind,
      getNameOptionaByid,
      handleRemind,
      initDictionary,
      emptyImg,
      handleSafeguard,
      handleAddDialog,
      getPermissionBtn,
      getList,
      closexx,
      closeRecipients,
      changeStatus,
      getNamesByid,
      handleChangeTabs,
      handleRowClick,
      saveSampleColor,
      reset,
      getQueryInfo,
      showCurrentTemplate,
      showItemInfo,
      switchStatus,
      changeOnlyOwner,
      changeOnlyUrgent,
      getMoreData,
      showAddRemindBtn,
      showUpdateRemindBtn,
      showRemindTip,
      remindTitle
    };
  },
  computed: {},
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
:deep(.page-list-main .main-panel) {
  height: calc(100vh - 11rem);
  padding: 0 12px 0 20px;
}

.page-main {
  position: relative;
}
.itemIcon {
  position: absolute;
  right: 40px;
  top: 20px;
  z-index: 99;
}
.cursor:hover {
  cursor: pointer;
}
.icon {
  -webkit-tap-highlight-color: transparent;
  outline: none;
  border: 0;
}
.star {
  font-size: 22px;
  margin-left: 20px;
}
.starInit {
  color: $yellow;
}
.left-container {
  display: flex;
  flex-direction: column;
  height: 100%;

  .left-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;

    .left-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: normal;
      line-height: 24px;

      .el-icon-info {
        margin-left: 6px;
        color: var(--tesPrimary);
        font-size: 18px;
        cursor: pointer;
      }
    }

    .el-button {
      padding: 7px !important;
    }
    .status-radio-wrapper {
      flex: 3;
      width: 100%;
    }
    .urgent-wrapper {
      float: right;

      .el-checkbox {
        margin-right: 10px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .header-menu {
    width: 100%;
    justify-content: space-between;

    .header-menu-item {
      width: 30%;
      height: 40px;
      color: inherit;
      font-size: 16px;
      line-height: 40px;
    }

    .header-menu-item.is-active {
      color: var(--tesPrimary);
      font-weight: 500;
    }
  }

  .query-wrapper {
    margin: 20px 20px 0;
  }

  .load-more {
    text-align: center;
    line-height: 12px;
  }

  .load-more-button {
    color: #909399;
    font-size: 12px;
  }
}

.popover-content .popover-item {
  margin-top: 8px;
}

.right-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.right-container:deep(.el-tabs) {
  height: 100%;

  .el-tabs__header {
    margin-left: 0;
    background-color: #fff;
  }

  .el-tabs__content,
  .el-tab-pane {
    height: 100%;
  }

  .el-tabs__item.is-active {
    background-color: var(--menuHover);
  }
}

.favorite-zone {
  position: absolute;
  right: 10px;
  bottom: 20px;
  z-index: 1;

  .el-button {
    display: block;
    width: 100px;
    margin: 10px 0 0 0;
    padding: 0 8px;
  }
}

.middle-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.middle-content {
  flex-grow: 1;
  overflow: hidden;
}

.el-alert {
  flex-shrink: 0;
  margin-top: 10px;

  &:first-child {
    margin-top: 0;
  }
}

.empty-box {
  .el-empty {
    padding: 40px 0;
  }
}

:deep(.el-container .el-main .page-main) {
  padding: 0;
}
</style>
<style lang="scss">
.scheduleTime.el-date-editor.el-input {
  width: 2px;
}
.no-atTheMoment .el-picker-panel {
  .el-button--text.el-picker-panel__link-btn {
    display: none;
  }
}
.scheduleTime {
  .el-input__prefix {
    display: none;
  }
  :deep(.el-input--small .el-input__inner) {
    border: 0;
  }
  .el-input__suffix {
    display: none;
  }
  .el-input__inner {
    border: 0;
    background: none;
    color: transparent;
    padding: 0;
  }
}

.tip-items {
  display: block;
  position: absolute;
  top: 5%;
  left: 6%;
  width: 1rem;
  height: 1rem;
  font-size: 0.5rem;
  line-height: 0.5rem;
  margin-left: 0.1rem;
  margin-top: 0.1rem;
}

.urgent-tip,
.review-tip,
.source-tip,
.change-tip,
.reject-tip {
  margin-right: 0.2rem;
}
.urgent-tip {
  color: $tes-tag-important;
}
.review-tip {
  color: $tes-red;
}
.source-tip {
  color: $tes-blue;
}
.change-tip {
  color: $tes-primary;
}
.reject-tip {
  color: $tes-yellow;
}

.testing-icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  margin-right: 8px;
  border-radius: 4px;
  border: 1px solid transparent;
  color: #fff;
  font-size: 12px;
  line-height: 1;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  &.text-copy {
    color: $tes-red;
    background-color: rgba($tes-red, 0.1);
    border-color: rgba($tes-red, 0.2);
  }
  &.text-origin {
    color: $tes-blue;
    background-color: rgba($tes-blue, 0.1);
    border-color: rgba($tes-blue, 0.2);
  }
  &.text-change {
    color: $panGreen;
    background-color: rgba($panGreen, 0.1);
    border-color: rgba($panGreen, 0.2);
  }
  &.text-back {
    color: $tes-yellow;
    background-color: rgba($tes-yellow, 0.1);
    border-color: rgba($tes-yellow, 0.2);
  }
}
</style>
