<template>
  <!-- 检测项目-关联仪器设备 -->
  <div class="Specification">
    <div class="header-search-group">
      <div v-loading="listLoading" class="btn-group">
        <el-button
          v-if="getPermissionBtn('addAIEBtn')"
          size="small"
          icon="el-icon-plus"
          type="default"
          @click="handleAdd()"
          @keyup.prevent
          @keydown.enter.prevent
          >新增仪器设备</el-button
        >
      </div>
    </div>
    <el-table
      ref="tableRef"
      v-loading="listLoading"
      :data="tableList"
      size="medium"
      height="auto"
      fit
      border
      highlight-current-row
      class="dark-table base-table format-height-table"
      :span-method="objectSpanMethod"
      @header-dragend="drageHeader"
    >
      <el-table-column label="序号" type="index" width="60" />
      <el-table-column label="仪器设备编号" :min-width="colWidth.name" prop="deviceNumber">
        <template #default="{ row, $index }">
          <el-select
            v-if="row.isAdd"
            v-model="row.deviceNumber"
            filterable
            size="small"
            placeholder="请选择仪器设备编号"
            :filter-method="filterDeviceList"
            clearable
            @visible-change="handleChangeVisiable"
            @clear="clearDevice($index)"
            @change="changeDevice(row, $index)"
          >
            <el-option v-for="item in deviceList" :key="item.id" :label="item.deviceNumber" :value="item.deviceNumber">
              <div style="float: left">
                {{ item.deviceNumber }}<span style="font-size: 12px">({{ item.name }})</span>
              </div>
            </el-option>
          </el-select>
          <span v-else>
            {{ row.deviceNumber }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="deviceName" label="仪器名称" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.deviceName }}
        </template>
      </el-table-column>
      <el-table-column prop="codePointName" label="码点名称" :min-width="colWidth.name">
        <template #default="{ row, $index }">
          <el-select
            v-if="row.isAdd || row.isEdit"
            v-model="row.codePointId"
            size="small"
            placeholder="请选择码点"
            clearable
            @change="changeDevicePoint(row, $index)"
            @clear="clearDevicePoint($index)"
          >
            <el-option
              v-for="item in row.devicecodepointList"
              :key="item.id"
              :label="item.pointName"
              :value="item.id"
            />
          </el-select>
          <span v-else>{{ row.pointName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="pointUnitName" label="码点单位" :width="colWidth.unit">
        <template #default="{ row }">
          {{ row.pointUnitName }}
        </template>
      </el-table-column>
      <el-table-column prop="capabilityParaName" label="关键参数" :min-width="colWidth.name">
        <template #default="{ row, $index }">
          <el-select
            v-if="row.isAdd || row.isEdit"
            v-model="row.capabilityParaId"
            size="small"
            clearable
            placeholder="请选择关键参数"
            @change="changeKeyParams(row, $index)"
            @clear="clearKeyParams($index)"
          >
            <el-option v-for="item in keyParameterList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <span v-else>{{ row.capabilityParaName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="capabilityParaUnitName" label="关键参数单位" :min-width="colWidth.unit">
        <template #default="{ row }">
          {{ row.capabilityParaUnitName }}
        </template>
      </el-table-column>
      <el-table-column prop="pointConvert" label="换算比率" :min-width="colWidth.name">
        <template #default="{ row }">
          <div v-if="row.isAdd || row.isEdit">
            <el-input v-model="row.pointConvert" size="small" style="width: 80px" />
            :
            <el-input v-model="row.paraConvert" size="small" style="width: 80px" />
          </div>
          <span v-else>{{ row.pointConvert + ' : ' + row.paraConvert }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        label="操作"
        :min-width="colWidth.operationMultiple"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="{ row, $index }">
          <span
            v-if="row.id && !isAddEditAll && getPermissionBtn('editAIEBtn')"
            class="blue-color"
            @click="editDevice(row, $index)"
            @keyup.prevent
            @keydown.enter.prevent
            >编辑</span
          >
          <span
            v-if="row.isAdd || row.isEdit"
            class="blue-color"
            @click="saveDevice(row, $index)"
            @keyup.prevent
            @keydown.enter.prevent
            >保存</span
          >
          <span
            v-if="row.id && !row.isAdd && !row.isEdit && !isAddEditAll && getPermissionBtn('delAIEBtn')"
            class="blue-color"
            @click="deleteDevice(row, $index)"
            @keyup.prevent
            @keydown.enter.prevent
            >删除</span
          >
          <span
            v-if="row.isAdd || row.isEdit"
            class="blue-color"
            @click="cancleDevice(row, $index)"
            @keyup.prevent
            @keydown.enter.prevent
            >取消</span
          >
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getCurrentInstance, reactive, ref, toRefs, watch } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { getColWidth } from '@/utils/func/customTable';
import {
  getCapabilityDevice,
  getDeviceCapabilityList,
  deleteCapabilityDevice,
  saveOrUpdateDevice,
  getDetail
} from '@/api/equipment';
import { ElMessageBox } from 'element-plus';
import { useStore } from 'vuex';
// saveOrUpdateDevice,
// deleteCapabilityDevice,
// getDetail
// import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'AssociatedEquipment',
  components: {},
  props: {
    capabilityId: {
      type: String,
      default: ''
    },
    activeName: {
      type: String,
      default: ''
    },
    keyParameter: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  setup(props) {
    const { proxy } = getCurrentInstance();
    const stores = useStore().state.user;
    const state = reactive({
      tableRef: ref(),
      deviceListAll: [], // 修改前的数据
      capabilityId: '', // 项目id
      paramsDevice: {},
      keyParameterList: [],
      units: stores.unit,
      deviceList: [],
      spanArr: [], // 合并行
      isAddEditAll: false, // 是否有新增或编辑状态的数据
      ruleForm: ref(),
      listLoading: false,
      pos: 0,
      tableList: []
    });
    watch(props, newValue => {
      if (newValue.activeName === '2') {
        state.capabilityId = props.capabilityId;
        state.keyParameterList = props.keyParameter;
        state.isAddEditAll = false;
        getList();
        getDeviceList();
      }
    });
    // 获取已选择设备列表接口
    const getList = () => {
      getCapabilityDevice(state.capabilityId).then(res => {
        if (res) {
          state.tableList = res.data.data;
          getSpanArr(state.tableList);
        }
      });
    };
    const getSpanArr = data => {
      state.spanArr = [];
      // data就是我们从后台拿到的数据
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          state.spanArr.push(1);
          state.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].deviceNumber === data[i - 1].deviceNumber) {
            state.spanArr[state.pos] += 1;
            state.spanArr.push(0);
          } else {
            state.spanArr.push(1);
            state.pos = i;
          }
        }
      }
    };
    const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
      if (columnIndex === 0 || columnIndex === 1) {
        const _row = state.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        };
      }
    };
    // 获取设备列表
    const getDeviceList = () => {
      getDeviceCapabilityList({ limit: '-1', page: '1' }).then(res => {
        if (res) {
          state.deviceList = res.data.data.list;
          state.deviceListAll = JSON.parse(JSON.stringify(res.data.data.list));
        }
      });
    };
    // 获取码点列表
    const getPointLists = () => {};
    // 新增-关联仪器设备
    const handleAdd = () => {
      if (state.isAddEditAll) {
        proxy.$message.warning('只能同时编辑一行');
        return false;
      } else {
        state.tableList.push({
          isAdd: true,
          pointConvert: 1,
          paraConvert: 1,
          devicecodepointList: []
        });
        state.isAddEditAll = true;
        state.spanArr.push(1);
      }
    };
    // 删除-关联仪器设备
    const deleteDevice = row => {
      ElMessageBox({
        title: '',
        message: '是否删除该设备？删除后不可恢复',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      }).then(() => {
        deleteCapabilityDevice(row.id).then(res => {
          if (res !== false) {
            proxy.$message.success('删除成功');
            getList(state.capabilityId);
          }
        });
      });
    };
    // 保存 关联仪器设备
    const saveDevice = (row, index) => {
      const params = JSON.parse(JSON.stringify(row));
      saveOrUpdateDevice({ ...params, capabilityId: state.capabilityId }).then(res => {
        if (res) {
          proxy.$message.success('更新成功');
          state.tableList[index].isAdd = false;
          state.tableList[index].isEdit = false;
          state.isAddEditAll = false;
          getList(state.capabilityId);
        }
      });
    };
    // 关联仪器设备 取消保存
    const cancleDevice = () => {
      getList(state.capabilityId);
      state.isAddEditAll = false;
    };
    // 关联仪器设备-编辑
    const editDevice = (row, index) => {
      state.isAddEditAll = true;
      state.tableList[index].isEdit = true;
      // 获取该设备的所有码点
      getDetail(row.deviceId).then(res => {
        if (res) {
          state.tableList[index].devicecodepointList = res.data.data.devicecodepointList;
        }
      });
    };
    const changeDevice = (row, index) => {
      const selectDeviceInfo = state.deviceList.filter(item => {
        return item.deviceNumber === row.deviceNumber;
      })[0];
      state.tableList[index].deviceName = selectDeviceInfo?.name;
      state.tableList[index].deviceId = selectDeviceInfo?.id;
      state.tableList[index].devicecodepointList = selectDeviceInfo?.devicecodepointList;
    };
    // 清除选中仪器设备
    const clearDevice = index => {
      state.tableList[index].deviceName = '';
      state.tableList[index].deviceId = '';
      state.tableList[index].devicecodepointList = [];
    };
    // 切换码点
    const changeDevicePoint = (row, index) => {
      const selectPointInfo = row.devicecodepointList.filter(item => {
        return item.id === row.codePointId;
      })[0];
      state.tableList[index].pointName = selectPointInfo?.pointName; // 	码点名称
      state.tableList[index].pointNumber = selectPointInfo?.pointNumber; // 码点编号
      state.tableList[index].pointUnitId = selectPointInfo?.pointUnit; // 码点单位Id
      state.units.forEach(unit => {
        if (unit.id === selectPointInfo?.pointUnit) {
          state.tableList[index].pointUnitName = unit.name; // 码点单位名称
        }
      });
    };
    const clearDevicePoint = index => {
      state.tableList[index].pointName = '';
      state.tableList[index].pointNumber = '';
      state.tableList[index].pointUnitName = '';
      state.tableList[index].pointUnitId = '';
    };
    // 切换关键参数
    const changeKeyParams = (row, index) => {
      const selectKeyParamsInfo = state.keyParameterList.filter(item => {
        return item.id === row.capabilityParaId;
      })[0];
      state.tableList[index].capabilityParaId = selectKeyParamsInfo?.id;
      state.tableList[index].capabilityParaUnitId = selectKeyParamsInfo?.unitid;
      state.tableList[index].capabilityParaUnitName = selectKeyParamsInfo?.unitname;
      state.tableList[index].capabilityParaName = selectKeyParamsInfo?.name;
      state.tableList[index].paraResultType = selectKeyParamsInfo?.resulttype;
    };
    const clearKeyParams = index => {
      state.tableList[index].capabilityParaId = '';
      state.tableList[index].capabilityParaUnitId = '';
      state.tableList[index].capabilityParaUnitName = '';
      state.tableList[index].capabilityParaName = '';
      state.tableList[index].paraResultType = '';
    };
    // 过虑仪器设备
    const filterDeviceList = val => {
      if (val) {
        const list = [];
        state.deviceListAll.forEach(item => {
          if (item.deviceNumber.indexOf(val) !== -1 || item.name.indexOf(val) !== -1) {
            list.push(item);
          }
        });
        state.deviceList = JSON.parse(JSON.stringify(list));
      } else {
        state.deviceList = JSON.parse(JSON.stringify(state.deviceListAll));
      }
    };
    // 还原筛选后得select下拉框
    const handleChangeVisiable = isShow => {
      if (isShow) {
        state.deviceList = JSON.parse(JSON.stringify(state.deviceListAll));
      }
    };
    return {
      ...toRefs(state),
      handleAdd,
      filterDeviceList,
      objectSpanMethod,
      changeKeyParams,
      clearKeyParams,
      changeDevice,
      clearDevice,
      handleChangeVisiable,
      changeDevicePoint,
      clearDevicePoint,
      deleteDevice,
      saveDevice,
      editDevice,
      cancleDevice,
      getPointLists,
      drageHeader,
      getNameByid,
      getNamesByid,
      getPermissionBtn,
      formatDate,
      getList,
      colWidth,
      getColWidth
    };
  },
  computed: {},
  created() {}
};
</script>
<style lang="scss" scoped>
.header-search-group {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  .btn-group {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>
