<template>
  <el-dialog
    :model-value="showDialog"
    custom-class="add-edit-account"
    :title="currentTitle"
    width="480px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <el-form
      ref="formInlineRef"
      :model="formInline"
      :rules="rules"
      size="small"
      label-position="right"
      label-width="90px"
      class="demo-ruleForm"
    >
      <el-form-item label="用户名:" prop="username">
        <el-input
          v-model="formInline.username"
          :disabled="currentTitle !== '新增账号'"
          maxlength="18"
          placeholder="请输入用户名"
        />
      </el-form-item>
      <el-form-item label="姓名:" prop="nickname">
        <el-input v-model="formInline.nickname" placeholder="请输入姓名" />
      </el-form-item>
      <el-form-item label="手机:" prop="mobile">
        <el-input v-model="formInline.mobile" maxlength="11" placeholder="请输入手机" />
      </el-form-item>
      <el-form-item label="邮箱:" prop="email">
        <el-input v-model="formInline.email" placeholder="请输入邮箱" />
      </el-form-item>
      <el-form-item label="状态:">
        <el-switch
          v-model="formInline.status"
          class="inner-switch"
          :active-value="1"
          :inactive-value="0"
          :active-text="formInline.status === 1 ? '启用' : '停用'"
          >启用</el-switch
        >
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" :loading="addEditLoading" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, toRefs, watch } from 'vue';
import { getNameByid } from '@/utils/common';
import { ElMessage } from 'element-plus';
import { addAccount, editAccount } from '@/api/platform-management';
import { isEmail2, isMobile2, isUsername } from '@/utils/validate';
// import _ from 'lodash'
// import { useRoute } from 'vue-router'
// import { formatPaginationList } from '@/utils/formatJson'

export default {
  name: 'AddOrEditAccount',
  components: {},
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: 'add'
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    const datas = reactive({
      showDialog: props.show,
      currentTitle: '新增账号',
      formInlineRef: ref(),
      addEditLoading: false,
      formInline: {
        username: '',
        nickname: '',
        mobile: '',
        email: '',
        status: 1
      },
      rules: {
        username: [
          { validator: isUsername, message: '仅支持英文字母，数字以及下划线组成', trigger: 'blur' },
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        nickname: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        email: [{ validator: isEmail2, message: '请输入正确的邮箱地址', tigger: 'blur' }],
        mobile: [{ validator: isMobile2, message: '请输入正确的手机号', tigger: 'blur' }]
      }
    });

    watch(
      () => props.show,
      newValue => {
        if (newValue) {
          datas.showDialog = newValue;
          datas.currentTitle = props.title === 'add' ? '新增账号' : '编辑账号';
          if (props.data) {
            datas.formInline = JSON.parse(JSON.stringify(props.data));
          }
        }
      },
      { deep: true }
    );

    // 确定
    const dialogSuccess = () => {
      datas.formInlineRef.validate(valid => {
        if (valid) {
          if (props.title === 'add') {
            addAccount(datas.formInline).then(res => {
              if (res !== false) {
                ElMessage.success('新增成功');
                datas.showDialog = false;
                context.emit('setInfo', datas.formInline);
              } else {
                // ElMessage.error('新增失败!')
              }
            });
          } else {
            editAccount(datas.formInline).then(res => {
              if (res !== false) {
                ElMessage.success('编辑成功');
                datas.showDialog = false;
                context.emit('setInfo', datas.formInline);
              } else {
                // ElMessage.error('编辑失败!')
              }
            });
          }
        }
      });
    };
    // 取消
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };

    return {
      ...toRefs(datas),
      close,
      getNameByid,
      dialogSuccess
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.add-edit-account {
  .search {
    width: 360px;
  }

  .add-report-search {
    margin-bottom: 15px;
  }
  .demo-ruleForm {
    .el-form-item {
      margin-bottom: 18px;
      :deep(.el-form-item__label) {
        padding-right: 15px;
      }
    }
  }
}
</style>
