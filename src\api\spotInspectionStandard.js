import request from '@/utils/request';
/**
 * 点检字段列表
 */
export function pointInspectionFieldListAll(params) {
  return request({
    url: `/api-device/device/pointInspectionField/listAll`,
    method: 'get',
    params
  });
}
/**
 * 删除点检标准
 */
export function pointInspectionStandardDelete(data) {
  return request({
    url: `/api-device/device/pointInspectionStandard/delete`,
    method: 'post',
    data
  });
}
// 保存点检字段表信息
export function pointInspectionFieldSaveBatch(data) {
  return request({
    url: '/api-device/device/pointInspectionField/saveBatch',
    method: 'post',
    data
  });
}

/**
 * 点检字段列表
 */
export function pointInspectionStandardList(params) {
  return request({
    url: `/api-device/device/pointInspectionStandard/list`,
    method: 'get',
    params
  });
}

/*
 * 根据Id查询点检标准表信息
 */
export function pointInspectionStandardInfoId(id) {
  return request({
    url: `/api-device/device/pointInspectionStandard/info/${id}`,
    method: 'get'
  });
}
// 保存点检标准表信息
export function pointInspectionStandardSave(data) {
  return request({
    url: `/api-device/device/pointInspectionStandard/save`,
    method: 'post',
    data
  });
}
