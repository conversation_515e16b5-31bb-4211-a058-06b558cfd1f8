<template>
  <div class="preview-pdf-container">
    <el-tooltip effect="light" content="返回" placement="bottom">
      <el-button icon="el-icon-back" circle class="fixed left-8 top-8 z-[9999]" @click="onBack" />
    </el-tooltip>
    <div class="w-full h-full my-0 mx-auto">
      <PDFViewer :pdf="src" :timeout="500" :page-scale="pageScale" class="flex-1" @loaded="onLoaded" />
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, onBeforeMount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import PDFViewer from '@/components/PDFViewer';
import { getAPIURL } from '@/utils/base-url';

export default {
  name: 'PreviewPdf',
  components: { PDFViewer },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const state = reactive({
      src: '',
      loading: false,
      pageScale: null
    });

    onBeforeMount(() => {
      init();
    });

    const init = async () => {
      state.loading = true;
      if (route.query.pageScale) {
        state.pageScale = Number(route.query.pageScale);
      }
      if (route.query.fileId) {
        state.src = getAPIURL() + `/api-document/document/file/preview/${route.query.fileId}`;
        return;
      } else if (route.query.fileUrl) {
        state.src = decodeURIComponent(route.query.fileUrl);
      }
    };

    const onLoaded = () => {
      state.loading = false;
    };

    const onBack = () => {
      router.push({ path: '/file-list' });
    };

    return {
      ...toRefs(state),
      onLoaded,
      onBack
    };
  }
};
</script>

<style lang="scss">
.preview-pdf-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #eff1f3;
}
</style>
