<template>
  <!-- 岗位状态 -->
  <ListLayout :has-custom-header="true" :has-custom-main="true" :main-offset-top="topHeight">
    <template #page-custom-main>
      <el-row :gutter="40" class="content">
        <el-col v-loading="loadingLeft" :span="14">
          <el-row class="module">
            <el-col :span="4" class="label">当前时间</el-col>
            <el-col :span="20" class="time">
              {{ formatDate(new Date()) }} {{ digitalClock }}
              <el-button class="fr" type="info" plain size="small" @click="dialogShow = true">设置</el-button>
              <DialogSetting :dialog-show="dialogShow" @closeDialog="handleCloseDialog" />
              <ul class="fr">
                <li>拥挤</li>
                <li>较慢</li>
                <li>良好</li>
              </ul>
            </el-col>
          </el-row>
          <el-row class="module distribution">
            <div v-for="(item, key, index) in newflowList" :key="key" class="moduleNode">
              <div v-for="(valItem, i) in item" :key="valItem.name" class="node" @click="handleEcharts(valItem)">
                <div class="title">{{ valItem.name }}</div>
                <div
                  class="number"
                  :class="{ red: valItem.status === 2, yellow: valItem.status === 1, green: valItem.status === 0 }"
                >
                  {{ valItem.value }}
                </div>
                <div v-if="i != item.length - 1" class="join">
                  <div class="join-item join-item1" />
                  <div class="join-item join-item2" />
                  <div class="join-item join-item3" />
                </div>
              </div>
              <el-divider v-if="index != moduleLength - 1" />
            </div>
          </el-row>
        </el-col>
        <el-col v-loading="loadingRight" :span="10" class="module">
          <div class="moduleTitle">{{ echartsTitle }}</div>
          <LineBarChart v-if="echartsData.length" :option="moduleColumnDiagram" :width="'100%'" :height="'100%'" />
          <div v-else style="text-align: center">
            <img src="@/assets/img/empty-chart.png" />
            <div style="color: #909399; padding: 10px; font-size: 14px">暂无数据</div>
          </div>
        </el-col>
      </el-row>
    </template>
  </ListLayout>
</template>

<script>
import { reactive, toRefs, ref } from 'vue';
import LineBarChart from '@/components/LineBarChart';
import ListLayout from '@/components/ListLayout';
import { formatDate } from '@/utils/formatTime';
import { positionStatus, queryDetail } from '@/api/positionStatus';
import DialogSetting from './components/DialogSetting';
import { formatterTips } from '../func/formatter';

export default {
  name: 'PositionStatus',
  components: {
    ListLayout,
    DialogSetting,
    LineBarChart
  },
  setup(props, context) {
    const state = reactive({
      loadingLeft: false,
      loadingRight: false,
      dialogShow: false,
      moduleLength: 0,
      echartsTitle: '',
      topHeight: 44,
      echartsData: [],
      noEcharts: ['YPRK', 'YPXD', 'BGBZ', 'JCWC'],
      allFlowList: [
        {
          module: 1,
          children: [
            {
              name: '检验申请',
              code: 'JYSQ'
            },
            {
              name: '委托登记',
              code: 'WTDJ'
            },
            {
              name: '合同评审',
              code: 'HTPS'
            },
            {
              name: '委托确认',
              code: 'WTQR'
            }
          ]
        },
        {
          module: 2,
          children: [
            {
              name: '样品入库',
              code: 'YPRK'
            },
            {
              name: '样品下达',
              code: 'YPXD'
            },
            {
              name: '检测分配',
              code: 'JCFP'
            },
            {
              name: '检测执行',
              code: 'JCZX'
            }
          ]
        },
        {
          module: 3,
          children: [
            {
              name: '报告编制',
              code: 'BGBZ'
            },
            {
              name: '报告审核',
              code: 'BGSH'
            },
            {
              name: '报告签字',
              code: 'BGQZ'
            },
            {
              name: '报告盖章',
              code: 'BGGZ'
            },
            {
              name: '报告归档',
              code: 'BGGD'
            },
            {
              name: '报告发送',
              code: 'BGFS'
            },
            {
              name: '检测完成',
              code: 'JCWC'
            }
          ]
        }
      ],
      newflowList: {}, // 过滤之后显示的岗位
      digitalClock: ''
    });
    // 数字时钟
    const getDigitalClock = () => {
      const date = new Date(); // 创建时间对象
      let Hours = date.getHours(); // 获取时
      let Min = date.getMinutes(); // 获取秒
      let Sec = date.getSeconds(); // 获取分
      Hours = Hours < 10 ? '0' + Hours : Hours;
      Min = Min < 10 ? '0' + Min : Min;
      Sec = Sec < 10 ? '0' + Sec : Sec;
      state.digitalClock = Hours + '\t:\t' + Min + '\t:\t' + Sec;
    };
    getDigitalClock();
    setInterval(getDigitalClock, 1000);
    const handleCloseDialog = isRefresh => {
      state.dialogShow = false;
      if (isRefresh) {
        getPositionStatus();
      }
    };
    const moduleColumnDiagram = ref({
      color: ['#80D9C5', '#F2D09D'],
      title: {
        text: ''
      },
      tooltip: {
        trigger: 'axis',
        extraCssText: 'text-align:left', // 设置tooltip的自定义样式
        textStyle: {
          color: '#303133'
        },
        formatter: function (params) {
          return formatterTips(params, true);
        }
      },
      grid: {
        left: '20',
        right: '20',
        bottom: '20',
        containLabel: true
      },
      toolbox: {},
      xAxis: {
        type: 'value',
        min: 0,
        show: true,
        minInterval: 1,
        boundaryGap: [0, 0.01]
      },
      yAxis: [
        {
          type: 'category',
          data: []
        }
      ],
      series: [
        {
          name: '数量',
          type: 'bar',
          barWidth: '50%',
          barMaxWidth: 50,
          data: []
        }
      ]
    });
    const getPositionStatus = () => {
      positionStatus().then(res => {
        if (res) {
          const resuleData = res.data.data;
          state.newflowList = {};
          state.allFlowList.forEach(item => {
            item.children.forEach(val => {
              val.isShow = resuleData[val.code]?.isShow;
              val.value = resuleData[val.code]?.value;
              val.status = resuleData[val.code]?.status;
              if (val.isShow) {
                if (state.newflowList[item.module]) {
                  state.newflowList[item.module].push(val);
                } else {
                  state.newflowList[item.module] = [val];
                }
              }
            });
          });
          const keys = Object.keys(state.newflowList);
          state.moduleLength = keys.length;
          handleEcharts(state.newflowList[keys[0]][0]);
        }
      });
    };
    getPositionStatus();
    // 刷新右边的echarts
    const handleEcharts = detail => {
      state.echartsTitle = detail.name;
      if (
        state.noEcharts.some(item => {
          return item === detail.code;
        })
      ) {
        moduleColumnDiagram.value.yAxis[0].data = [detail.name];
        moduleColumnDiagram.value.series[0].data = [detail.value];
        state.echartsData = [detail];
      } else {
        queryDetail(detail.code).then(res => {
          if (res) {
            state.echartsData = res.data.data;
            moduleColumnDiagram.value.yAxis[0].data = state.echartsData.map(item => item.name);
            moduleColumnDiagram.value.series[0].data = state.echartsData.map(item => item.value);
          }
        });
      }
    };
    return {
      ...toRefs(state),
      getDigitalClock,
      getPositionStatus,
      handleCloseDialog,
      moduleColumnDiagram,
      handleEcharts,
      formatDate
    };
  }
};
</script>
<style lang="scss" scoped>
.fr {
  float: right;
}
.content {
  line-height: 34px;
  text-align: left;
  padding: 0 24px;
  .module {
    background-color: $background-color;
    border-radius: 5px;
  }
  .time {
    padding: 6px 10px;
  }
  .label {
    background-color: #fafafa;
    text-align: center;
    padding: 6px;
    color: $tes-font4;
  }
  ul {
    margin: 0;
  }
}
li {
  float: right;
  list-style: none;
  padding: 0 20px;
  &:first-child {
    color: $red;
    position: relative;
    &::before {
      content: '';
      width: 10px;
      height: 10px;
      position: absolute;
      border-radius: 5px;
      top: 13px;
      left: 5px;
      background-color: $red;
    }
  }
  &:nth-child(2) {
    color: $yellow;
    position: relative;
    &::before {
      content: '';
      width: 10px;
      height: 10px;
      position: absolute;
      border-radius: 5px;
      top: 13px;
      left: 5px;
      background-color: $yellow;
    }
  }
  &:last-child {
    color: $panGreen;
    position: relative;
    &::before {
      content: '';
      width: 10px;
      height: 10px;
      position: absolute;
      border-radius: 5px;
      top: 13px;
      left: 5px;
      background-color: $panGreen;
    }
  }
}
.moduleTitle {
  font-size: 20px;
  color: $tes-font2;
  line-height: 50px;
}
.distribution {
  margin-top: 20px;
  padding: 24px;
  .moduleNode {
    width: 100%;
  }
  .node {
    width: 20%;
    margin-bottom: 24px;
    display: inline-block;
    position: relative;
    padding: 0 10px;
  }
  .title {
    font-size: 16px;
    color: $tes-font1;
    font-weight: bold;
    text-align: center;
  }
  .number {
    color: #fff;
    font-size: 20px;
    width: 80px;
    height: 80px;
    line-height: 80px;
    border-radius: 50px;
    text-align: center;
    margin: 0 auto;
    position: relative;
    // animation: heartbeat 1s infinite;
    cursor: pointer;
  }
  .red {
    background-color: $red;
  }
  .green {
    background-color: $panGreen;
  }
  .yellow {
    background-color: $yellow;
  }
}
.join {
  position: absolute;
  bottom: 35px;
  right: -15px;
  .join-item {
    float: right;
    width: 0;
    height: 0;
    border-top: 6px solid transparent;
    border-right: 6px solid transparent;
    border-left: 6px solid $border-color;
    border-bottom: 6px solid transparent;
    -webkit-animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
    animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
  }
  .join-item3 {
    -webkit-animation-delay: 0.2s;
    animation-delay: 0.2s;
  }
  .join-item2 {
    -webkit-animation-delay: 0.3s;
    animation-delay: 0.3s;
  }
  .join-item1 {
    -webkit-animation-delay: 0.4s;
    animation-delay: 0.4s;
  }
}
@-webkit-keyframes sk-cubeGridScaleDelay {
  0% {
    border-left-color: $border-color;
  }
  30% {
    border-left-color: $border-color1;
  }
  70% {
    border-left-color: $border-color2;
  }
  100% {
    border-left-color: $border-color3;
  }
}
@keyframes sk-cubeGridScaleDelay {
  0% {
    border-left-color: $border-color;
  }
  30% {
    border-left-color: $border-color1;
  }
  70% {
    border-left-color: $border-color2;
  }
  100% {
    border-left-color: $border-color3;
  }
}
@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

:deep(.el-divider--horizontal) {
  margin: 0 0 24px 0;
}
</style>
