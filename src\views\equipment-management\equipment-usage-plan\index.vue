<template>
  <!-- 设备使用计划 -->
  <ListLayout
    v-loading="loading"
    :has-button-group="false"
    :has-quick-query="false"
    :has-page-header="false"
    class="equipment-usage-plan"
  >
    <div class="calendarHeader">
      <div class="header_left">
        <h1>{{ calendarTitle }}</h1>
      </div>
      <div class="flex items-center">
        <span v-if="isShowBack" class="blue-color backToday" @click="getToday()">{{
          viewType === 'month' ? '返回当月' : viewType === 'week' ? '返回当周' : '返回当日'
        }}</span>
        <el-radio-group v-model="viewType" size="small" @change="handleChangeView">
          <el-radio-button label="month">月</el-radio-button>
          <el-radio-button label="week">周</el-radio-button>
          <el-radio-button label="day">日</el-radio-button>
        </el-radio-group>
        <el-date-picker
          v-if="viewType === 'month'"
          v-model="showMonth"
          type="month"
          size="small"
          :clearable="false"
          placeholder="请选择日期"
          style="margin-left: 10px; vertical-align: middle"
          @change="changeDate"
        />
        <el-button-group v-if="viewType === 'week'" style="margin-left: 10px">
          <el-button size="small" class="el-icon-arrow-left" @click="getPrev()">上一周</el-button>
          <el-button size="small" @click="getNext()">下一周<i class="el-icon-arrow-right" /></el-button>
        </el-button-group>
        <el-date-picker
          v-if="viewType === 'day'"
          v-model="showDate"
          type="date"
          size="small"
          :clearable="false"
          placeholder="请选择日期"
          style="margin-left: 10px; vertical-align: middle"
          @change="changeDate"
        />
        <div
          v-if="getPermissionBtn('settingKeyEquipment') || getPermissionBtn('addEquipmentUsagePlan')"
          class="separator"
        />
        <el-button
          v-if="getPermissionBtn('settingKeyEquipment')"
          size="small"
          class="el-icon-setting"
          @click="handleSetKeyPoint()"
        >
          重点设备</el-button
        >
        <el-button
          v-if="getPermissionBtn('addEquipmentUsagePlan')"
          type="primary"
          size="small"
          class="el-icon-plus"
          @click="handleAddEditPlan()"
        >
          新增</el-button
        >
      </div>
    </div>
    <div class="calendar-content">
      <div ref="fullcalendar" class="card" />
    </div>
    <!-- 新增编辑计划 -->
    <DrawerEquipmentPlan
      :drawer="drawerVisiable"
      :drawer-type="drawerType"
      :key-device-list="keyDeviceList"
      :detail-data="detailData"
      @closeDrawer="closeDrawer"
    />
    <!-- 设置重点设备 -->
    <DialogKeyEquipment :dialog-visible="dialogVisible" :device-list="keyDeviceList" @closeDialog="closeDialog" />
    <!-- 查看计划 -->
    <el-dialog
      v-model="dialogCheckShow"
      title="查看计划"
      :modal="false"
      :close-on-click-modal="true"
      :width="500"
      custom-class="equipment-usage-plan_dialog"
      @close="handleClose"
    >
      <div v-if="dialogCheckShow">
        <div class="fc_event">
          <div class="dialog_title">
            {{ formDataCheck.title || '--' }}
            <div class="dialog_icon">
              <i
                v-if="
                  formDataCheck.userIdList.some(item => {
                    return item === accountId;
                  })
                "
                class="el-icon-edit"
                @click="handleAddEditPlan(formDataCheck)"
              />
              <i
                v-if="getPermissionBtn('deleteEquipmentUsagePlan')"
                class="el-icon-delete"
                @click="handleDeletePlan()"
              />
            </div>
          </div>
        </div>
        <el-form
          ref="formRef"
          :model="formDataCheck"
          label-position="left"
          size="small"
          label-width="70px"
          class="my-form"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item prop="userIdList" label="使用人：">
                <UserTag
                  v-for="(item, index) in formDataCheck.userIdList"
                  :key="index"
                  :name="getNameByid(item) || item || '--'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="title" label="计划时间：">
                <span
                  >{{ formDataCheck.startTime }} {{ formDataCheck.startTimeMinute }} ~ {{ formDataCheck.finishTime }}
                  {{ formDataCheck.finishTimeMinute }}</span
                >
              </el-form-item>
            </el-col>
            <el-divider />
            <el-col :span="24">
              <div class="form_item_header">
                <div class="label">使用描述：</div>
                <span v-if="formDataCheck.remark" class="font-sm" @click="isShowAll = !isShowAll">{{
                  isShowAll ? '收起详情' : '展开详情'
                }}</span>
              </div>
              <div
                :class="`form_item_content ${isShowAll ? 'packDown' : 'packUp'} ${formDataCheck.remark ? '' : 'empty'}`"
                v-html="formDataCheck.remark ? formDataCheck.remark : '暂无描述'"
              />
            </el-col>
            <el-divider />
            <el-col :span="24">
              <div class="form-item_flex">
                <el-form-item prop="createTime" label="创建时间：" label-width="70px">
                  <span>{{ formatDateTime(formDataCheck.createTime) }}</span>
                </el-form-item>
                <el-form-item prop="createBy" label="创建人：" label-width="60px">
                  <UserTag :name="getNameByid(formDataCheck.createBy) || formDataCheck.createBy || '--'" />
                </el-form-item>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-dialog>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import ListLayout from '@/components/ListLayout';
import { formatDate } from '@/utils/formatTime';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { deviceListKey, devicePlanList, deleteDevicePlan, devicePlanEdit } from '@/api/equipmentUsagePlan';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { Calendar } from '@fullcalendar/core';
import resourceTimelinePlugin from '@fullcalendar/resource-timeline';
import interactionPlugin from '@fullcalendar/interaction';
import {
  formatYM,
  formatDateDay,
  formatCalendar,
  formatDateTime,
  getWeekDate,
  formatDateFilter,
  getWeekNumber
} from '@/utils/formatTime';
import { getLoginInfo } from '@/utils/auth';
import UserTag from '@/components/UserTag';
import DrawerEquipmentPlan from './components/drawer-equipment-plan.vue';
import DialogKeyEquipment from './components/dialog-key-equipment.vue';

export default {
  name: 'EquipmentUsagePlan',
  components: { ListLayout, DrawerEquipmentPlan, DialogKeyEquipment, UserTag },
  setup() {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableRef: ref(),
      calendarTitle: new Date().getFullYear() + '年' + Number(new Date().getMonth() + 1) + '月', // 日历头部显示文字
      fullcalendar: ref(),
      Tcalendar: null,
      dialogVisible: false,
      showMonth: formatYM(new Date()), // 显示月份
      showDate: new Date(),
      viewType: 'month', // 视图类型
      dialogCheckShow: false, // 查看计划弹出框
      dialogType: '', // 弹出窗类型
      drawerVisiable: false, // 添加编辑设备使用计划
      drawerType: '', // 设备使用计划类型
      ruleForm: ref(),
      isShowAll: false, // 设备使用计划是否展示全部描述
      devicePlanList: [], // 设备使用计划集合
      loading: false,
      accountId: getLoginInfo()?.accountId,
      dialogSelectEquipment: false, // 选择仪器设备弹出窗
      detailData: {}, // 传递给弹出窗的详情
      types: [],
      formDataCheck: {},
      tableList: [],
      isShowBack: false, // 是否显示返回当月当周当日按钮
      total: 0,
      statusJson: {}, // 仪器设备维修记录状态
      keyDeviceList: [] // 关键设备列表
    });
    const initCalendar = () => {
      state.Tcalendar = new Calendar(state.fullcalendar, {
        plugins: [resourceTimelinePlugin, interactionPlugin],
        initialView: 'resourceTimelineMonth',
        // aspectRatio: 2.3,
        locale: 'zh-cn',
        handleWindowResize: true,
        editable: true, // 允许编辑表格
        droppable: true,
        contentHeight: 'auto',
        resourceOrder: 'order',
        eventDurationEditable: true,
        eventResizableFromStart: true,
        selectable: getPermissionBtn('addEquipmentUsagePlan'), // 允许用户通过单击和拖动来突出显示多个日期或时间段
        firstDay: 1, // 设置一周中显示的第一天是哪天，周日是0，周一是1，类推。
        unselectAuto: true, // 当点击页面日历以外的位置时，是否自动取消当前的选中状态
        unselectCancel: '.el-drawer',
        resourceAreaColumns: [
          {
            field: 'deviceName',
            headerContent: '设备名称'
          },
          {
            field: 'deviceNumber',
            headerContent: '设备编号'
          }
        ],
        dayMaxEvents: true,
        eventClassNames: 'myclassname',
        headerToolbar: false,
        eventMaxStack: 1,
        views: {
          resourceTimelineWeek: {
            type: 'resourceTimeline',
            duration: { week: 1 },
            slotDuration: { days: 1 },
            buttonText: '周',
            slotLabelFormat: [
              // { day: 'numeric', weekday: 'short' }
              { weekday: 'short' }
            ]
          },
          resourceTimelineMonth: {
            slotLabelFormat: [
              // { day: 'numeric' }
              ({ date }) => date.day
            ]
          },
          resourceTimelineDay: {
            type: 'resourceTimeline',
            duration: { days: 1 },
            slotLabelFormat: [{ hour: 'numeric', minute: '2-digit' }]
          }
        },
        events: [],
        // schedulerLicenseKey: 'GPL-My-Project-Is-Open-Source',
        resources: state.keyDeviceList,
        resourceAreaWidth: '18%',
        eventContent: function (arg) {
          const italicEl = document.createElement('div');
          if (arg.event._def.extendedProps.startTimeMinute && state.viewType !== 'day') {
            const childTitle = document.createElement('div');
            childTitle.innerHTML = arg.event.title;
            childTitle.setAttribute('class', 'title');
            const childTime = document.createElement('div');
            childTime.innerHTML = arg.event._def.extendedProps.startTimeMinute;
            childTime.setAttribute('class', 'titleTime');
            italicEl.append(childTitle);
            italicEl.append(childTime);
            italicEl.setAttribute('class', `plan_titleTime`);
          } else {
            italicEl.setAttribute('class', `plan_title`);
            italicEl.append(arg.event.title);
          }
          return { domNodes: [italicEl] };
        },
        eventDrop: function (info) {
          // 拖拽停止时触发
          state.loading = true;
          const extendedProps = info.event._def.extendedProps;
          if (
            extendedProps?.userIdList?.some(item => {
              return item === state.accountId;
            }) ||
            extendedProps?.createBy === state.accountId
          ) {
            state.loading = false;
            handleDrap(info);
          } else {
            state.loading = false;
            proxy.$message.warning('仅限使用人和创建人修改计划！');
            info.revert();
          }
        },
        eventClick: function (info) {
          // 点击查看时触发
          handleClickEvent(info);
        },
        select: function (info) {
          // 视图选择日期触发
          if (getPermissionBtn('addEquipmentUsagePlan')) {
            handleSelectDate(info);
          }
        },
        eventResize: function (info) {
          const extendedProps = info.event._def.extendedProps;
          if (
            !extendedProps?.userIdList?.some(item => {
              return item === state.accountId;
            }) &&
            extendedProps?.createBy !== state.accountId
          ) {
            proxy.$message.warning('仅限使用人和创建人修改计划！');
            info.revert();
          } else if (
            state.viewType === 'day' &&
            (!extendedProps?.finishTimeMinute || !extendedProps?.finishTimeMinute)
          ) {
            proxy.$message.warning('日视图中全天计划不能拖拽修改时间！');
            info.revert();
          } else {
            handleEventResize(info);
          }
        }
      });
      state.Tcalendar.render();
    };
    // 获取重点设备列表
    const getDeviceKeyPoint = isEdit => {
      deviceListKey().then(res => {
        if (res) {
          state.keyDeviceList = res.data.data;
          state.keyDeviceList.forEach(item => {
            item.id = item.deviceId;
          });
          initCalendar();
          if (isEdit) {
            handleChangeView(state.viewType);
          } else {
            getDevicePlanShow();
          }
        }
      });
    };
    getDeviceKeyPoint();
    // 获取使用计划
    const getDevicePlanShow = () => {
      const typeJSON = {
        month: '2',
        week: '1',
        day: '0'
      };
      const params = {
        type: typeJSON[state.viewType]
      };
      if (params.type === '1') {
        params.date = formatDateFilter(state.calendarTitle);
      } else if (params.type === '0') {
        params.date = formatDate(state.showDate);
      } else {
        params.date = formatDate(state.showMonth);
      }
      state.loading = true;
      devicePlanList(params).then(res => {
        state.loading = false;
        if (res) {
          state.Tcalendar.getEventSources().forEach(item => {
            item.remove();
          });
          const { data } = res.data;
          state.devicePlanList = [];
          data.forEach(item => {
            state.devicePlanList.push(...item.planInfoList);
          });
          state.devicePlanList.forEach(item => {
            item.resourceId = item.deviceId;
            item.start = item.startTimeMinute ? item.startTime + ' ' + item.startTimeMinute : item.startTime;
            if (item.startTime !== item.finishTime || item.isAllDay) {
              item.end = item.finishTimeMinute
                ? formatDate(new Date(item.finishTime).getTime()) + ' ' + item.finishTimeMinute
                : formatDate(new Date(item.finishTime).getTime() + 1000 * 60 * 60 * 24);
            } else {
              item.end = item.finishTimeMinute ? item.finishTime + ' ' + item.finishTimeMinute : item.finishTime;
            }
          });
          state.Tcalendar.addEventSource(state.devicePlanList);
          state.Tcalendar.render();
        }
      });
    };
    // 上一月、周、日
    const getPrev = () => {
      state.Tcalendar.prev();
      state.calendarTitle = state.Tcalendar.view.title;
      const nowDate = formatDateFilter(state.calendarTitle);
      // 判断已经是当前周隐藏返回当前周按钮
      if (
        getWeekNumber(nowDate) === getWeekNumber(new Date()) &&
        new Date(nowDate).getFullYear() === new Date().getFullYear()
      ) {
        state.isShowBack = false;
      } else {
        state.isShowBack = true;
      }
      getDevicePlanShow();
    };
    // 下一月、周、日
    const getNext = () => {
      state.Tcalendar.next();
      state.calendarTitle = state.Tcalendar.view.title;
      const nowDate = formatDateFilter(state.calendarTitle);
      // 判断已经是当前周隐藏返回当前周按钮
      if (
        getWeekNumber(nowDate) === getWeekNumber(new Date()) &&
        new Date(nowDate).getFullYear() === new Date().getFullYear()
      ) {
        state.isShowBack = false;
      } else {
        state.isShowBack = true;
      }
      getDevicePlanShow();
    };
    // 切回当月、周、日
    const getToday = () => {
      state.Tcalendar.today();
      state.calendarTitle = state.Tcalendar.view.title;
      state.isShowBack = false;
      state.showMonth = formatYM(new Date());
      state.showDate = formatDate(new Date());
      getDevicePlanShow();
    };
    // 添加编辑使用计划
    const handleAddEditPlan = val => {
      if (!state.keyDeviceList.length) {
        proxy.$message.warning('请先添加重点设备！');
        return false;
      }
      if (val) {
        state.drawerType = 'edit';
        state.drawerVisiable = true;
        state.detailData = {
          ...val,
          deviceId: val.deviceId
        };
      } else {
        state.drawerType = 'add';
        state.drawerVisiable = true;
        state.detailData = {
          userIdList: [state.accountId],
          isAllDay: 0,
          startTime: formatDate(new Date()),
          finishTime: formatDate(new Date()),
          deviceIdList: [],
          startTimeMinute: new Date().getHours() < 23 ? new Date().getHours() + 1 + ':00' : '23:00',
          finishTimeMinute: new Date().getHours() < 22 ? new Date().getHours() + 2 + ':00' : '23:00'
        };
      }
    };
    // 删除设备维修记录
    const deleteRecord = row => {
      proxy
        .$confirm('是否删除该条维修记录？', '删除确认', {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          type: 'warning'
        })
        .then(() => {})
        .catch(() => {});
    };
    const handleChangeView = val => {
      if (val === 'month') {
        state.Tcalendar?.changeView('resourceTimelineMonth');
      } else if (val === 'week') {
        state.Tcalendar?.changeView('resourceTimelineWeek');
      } else {
        state.Tcalendar?.changeView('resourceTimelineDay');
      }
      // 切换类型之后重置日期
      getToday();
    };
    // 关闭仪器设备弹出窗
    const closeDeatilDrawer = val => {};
    // 设置重点设备
    const handleSetKeyPoint = () => {
      state.dialogVisible = true;
    };
    const closeDrawer = val => {
      state.drawerVisiable = false;
      if (val) {
        state.dialogCheckShow = false;
        getDevicePlanShow();
      }
    };
    // 关闭重点设备弹出框设置
    const closeDialog = val => {
      if (val) {
        getDeviceKeyPoint(true);
      }
      state.dialogVisible = false;
    };
    // 选择日期
    const handleSelectDate = info => {
      state.drawerType = 'add';
      const params = {
        userIdList: [state.accountId],
        startTime: formatDate(info.startStr),
        deviceIdList: [info.resource._resource.extendedProps.deviceId]
      };
      if (info.view.type === 'resourceTimelineMonth' || info.view.type === 'resourceTimelineWeek') {
        // 月视图 // 周视图
        params.isAllDay = 1;
        params.finishTime = formatDate(new Date(info.endStr).getTime() - 1000 * 60 * 60 * 24);
      } else {
        // 日视图
        params.isAllDay = 0;
        params.finishTime = formatDate(info.endStr);
        params.startTimeMinute = formatCalendar(new Date(info.startStr), 'hour');
        params.finishTimeMinute = formatCalendar(new Date(info.endStr), 'hour');
      }
      state.detailData = params;
      state.drawerVisiable = true;
    };
    // 切换月份和日期
    const changeDate = date => {
      state.Tcalendar.gotoDate(formatDate(date));
      if (state.viewType === 'month') {
        // 判断不是当前月份，显示返回当前月
        if (date.getMonth() !== new Date().getMonth() || new Date().getFullYear() !== new Date(date).getFullYear()) {
          state.isShowBack = true;
        } else {
          state.isShowBack = false;
        }
      } else {
        // 判断不是当前日期，显示返回当前日期
        if (date !== formatDate(new Date())) {
          state.isShowBack = true;
        } else {
          state.isShowBack = false;
        }
      }
      state.calendarTitle = state.Tcalendar.view.title;
      getDevicePlanShow();
    };
    // 删除计划
    const handleDeletePlan = () => {
      proxy
        .$confirm('是否确认删除', '删除确认', {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'warning'
        })
        .then(() => {
          state.loading = true;
          deleteDevicePlan([state.formDataCheck.id]).then(function (res) {
            state.loading = false;
            if (res) {
              state.dialogCheckShow = false;
              proxy.$message.success('删除成功！');
              getDevicePlanShow();
            }
          });
        })
        .catch(() => {});
    };
    // 关闭弹出框
    const handleClose = () => {
      state.dialogCheckShow = false;
    };
    // 事件点击
    const handleClickEvent = info => {
      state.dialogCheckShow = true;
      state.formDataCheck = {
        id: info.event._def.publicId,
        title: info.event._def.title,
        startTime: formatDate(info.event.startStr),
        ...info.event._def.extendedProps
      };
    };
    // 拖拽详情
    const handleDrap = info => {
      const params = {
        ...info.event.extendedProps,
        id: info.event.id,
        deviceId: info.event._def.resourceIds[0],
        title: info.event.title
      };
      params.startTime = formatCalendar(info.event.start);
      if (info.event.allDay) {
        // 全天
        params.startTimeMinute = '';
        params.finishTimeMinute = '';
        params.isAllDay = 1;
        params.finishTime = info.event.end
          ? formatCalendar(new Date(info.event.end).getTime() - 24 * 3600 * 1000)
          : formatCalendar(info.event.start);
      } else {
        // 非全天
        params.startTimeMinute = formatCalendar(info.event.start, 'hour');
        params.finishTimeMinute = formatCalendar(new Date(info.event.end), 'hour');
        params.finishTime = info.event.end
          ? formatCalendar(new Date(info.event.end))
          : formatCalendar(info.event.start);
        params.isAllDay = 0;
      }
      state.loading = true;
      devicePlanEdit(params).then(res => {
        state.loading = false;
        if (res) {
          proxy.$message.success('修改成功！');
          getDevicePlanShow();
        } else {
          getDevicePlanShow();
        }
      });
    };
    // 调整大小时触发
    const handleEventResize = info => {
      handleDrap(info);
    };
    return {
      ...toRefs(state),
      getNext,
      getPrev,
      handleEventResize,
      getWeekDate,
      handleDrap,
      handleClickEvent,
      handleClose,
      formatDateTime,
      handleDeletePlan,
      changeDate,
      handleSelectDate,
      closeDialog,
      handleAddEditPlan,
      formatDateDay,
      closeDrawer,
      handleSetKeyPoint,
      getPermissionBtn,
      handleChangeView,
      drageHeader,
      getNameByid,
      getNamesByid,
      deleteRecord,
      closeDeatilDrawer,
      getToday,
      formatDate,
      colWidth
    };
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  color: $tes-primary;
}
.calendarHeader {
  margin: 0 0 20px 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  .header_select {
    margin: 0 0 0 10px;
    display: inline-block;
    vertical-align: middle;
  }
  .separator {
    display: inline-block;
    position: relative;
    margin: 0 12px;
    &:after {
      content: '';
      position: absolute;
      top: -16px;
      left: 0;
      height: 24px;
      width: 1px;
      background: #dcdfe6;
    }
  }
}
h1 {
  font-size: 20px;
  font-weight: 500;
  line-height: 32px;
  margin: 0 0 0 0;
  text-align: left;
  vertical-align: middle;
  display: inline-block;
  color: #303133;
}
.equipment-usage-plan_dialog {
  .form-item_flex {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .el-form .el-form-item {
    margin: 0;
  }
  .fc_event {
    background: #fff;
    line-height: 22px;
    font-size: 18px;
    padding: 12px 12px 12px 18px;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    margin: 0 0 20px 0;
    position: relative;
    &:after {
      content: '';
      width: 4px;
      height: 100%;
      border-radius: 2px 0 0 2px;
      position: absolute;
      top: 0;
      left: 0;
      background-color: $tes-primary;
      border: 0;
      opacity: 1;
    }
  }

  .form_item_content.packUp {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .form_item_content.packDown {
    max-height: 15vh;
    overflow-y: auto;
  }
  .dialog_title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .dialog_icon {
    display: flex;
    gap: 6px;
    cursor: pointer;
    user-select: none;
    color: $tes-font1;
    i {
      width: 24px;
      height: 24px;
      font-size: 14px;
      padding: 5px;
      border-radius: 3px;
      &:hover {
        background: $tes-border2;
      }
    }
  }
  .form_item_header {
    line-height: 32px;
    color: #909399;
    .label {
      display: inline-block;
    }
    span {
      float: right;
      cursor: pointer;
      user-select: none;
      &:hover {
        color: $tes-primary;
      }
    }
  }
  .el-divider--horizontal {
    margin: 14px 0;
  }
  .form_item_content.empty {
    text-align: center;
    color: $tes-font2;
  }
}
</style>
<style lang="scss">
@import 'fullcalendar.scss';
.equipment-usage-plan_dialog {
  border-radius: 10px;
  border: 1px solid #dcdfe6;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
  .el-dialog__header {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    background: #f0f2f5;
  }
  .el-dialog__body {
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    background: linear-gradient(180deg, #f0f2f5 34px, #fff 35px);
    padding: 10px 20px 20px 20px;
    .el-form-item__label {
      color: #909399;
    }
  }
  .form_item_content {
    line-height: 20px;
    p,
    ul,
    li {
      margin: 0;
      padding: 0;
    }
  }
}
</style>
