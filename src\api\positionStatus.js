import request from '@/utils/request';
// 获取显示的岗位
export function positionStatus() {
  return request({
    url: '/api-orders/orders/positionStatus/query',
    method: 'get'
  });
}
// 岗位状态配置
export function queryConfList() {
  return request({
    url: '/api-orders/orders/positionStatus/queryConf',
    method: 'get'
  });
}
// 岗位状态配置
export function updateConf(data) {
  return request({
    url: '/api-orders/orders/positionStatus/updateConf',
    method: 'put',
    data
  });
}
// 岗位状态详情查询
export function queryDetail(code) {
  return request({
    url: '/api-orders/orders/positionStatus/queryDetail?code=' + code,
    method: 'get'
  });
}
