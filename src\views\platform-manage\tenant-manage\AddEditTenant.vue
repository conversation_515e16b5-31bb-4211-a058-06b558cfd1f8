/* eslint-disable vue/html-indent */
<template>
  <el-dialog
    :model-value="showDialog"
    custom-class="add-edit-tenant"
    :title="currentTitle"
    width="1000px"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <el-form
      ref="formInlineRef"
      :model="formInline"
      :rules="rules"
      label-width="120px"
      label-position="top"
      class="demo-ruleForm"
    >
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="状态:" prop="status">
            <el-switch
              v-model="formInline.status"
              class="inner-switch"
              :active-value="1"
              :inactive-value="0"
              :active-text="formInline.status === 1 ? '启用' : '停用'"
              >启用</el-switch
            >
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="水印:" prop="isWatermark">
            <el-switch
              v-model="formInline.isWatermark"
              class="inner-switch"
              :active-value="true"
              :inactive-value="false"
              :active-text="formInline.isWatermark ? '启用' : '停用'"
            >
              启用
            </el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="租户属性:" prop="clientStatus" class="seal-staff-name">
            <el-select
              v-model="formInline.clientStatus"
              style="width: 100%"
              class="owner-select"
              placeholder="请选择租户类型"
              clearable
              default-first-option
              size="small"
              @change="changeClientType1"
            >
              <el-option v-for="item in clientType1Options" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="租户类型:" prop="clientType" class="seal-staff-name">
            <el-select
              v-model="formInline.clientType"
              class="owner-select"
              style="width: 100%"
              placeholder="请选择租户类型"
              size="small"
              @change="changeClientType"
            >
              <el-option v-for="item in clientTypeOptions" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="租户名:" prop="clientName">
            <el-input v-model="formInline.clientName" size="small" placeholder="请输入租户名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="租户简称:" prop="clientShortName">
            <el-input v-model="formInline.clientShortName" size="small" maxlength="8" placeholder="请输入租户简称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="租户联系人:" prop="person">
            <el-input v-model="formInline.person" size="small" placeholder="请输入租户联系人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人电话:" prop="personMobile">
            <el-input v-model="formInline.personMobile" size="small" maxlength="11" placeholder="请输入联系人电话" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="租户管理员:" prop="managerId">
            <el-select
              v-model="formInline.managerId"
              class="owner-select"
              style="width: 100%"
              placeholder="请选择管理员"
              clearable
              size="small"
              @change="changeManager"
            >
              <el-option v-for="item in userOptions" :key="item.id" :label="item.nickname" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人邮箱:">
            <el-input v-model="formInline.personEmail" size="small" placeholder="请输入联系人邮箱" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="租户编码:" prop="clientCode">
            <el-input v-model="formInline.clientCode" size="small" maxlength="50" placeholder="请输入租户编号" />
          </el-form-item>
        </el-col>
        <el-col :span="12" />
        <el-col :span="24">
          <el-form-item label="备注:">
            <el-input
              v-model="formInline.content"
              type="textarea"
              size="small"
              rows="2"
              placeholder="请输入内容"
              maxlength="300"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" :loading="addEditLoading" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, toRefs, watch } from 'vue';
import { getNameByid } from '@/utils/common';
import { ElMessage } from 'element-plus';
import { addTenant, findManagerId } from '@/api/platform-management';
import { isEmail, isMobile } from '@/utils/validate';
import { tenantTypeOptions } from '@/data/industryTerm';
// import { useStore } from 'vuex'
// import _ from 'lodash'
// import { useRoute } from 'vue-router'
// import { formatPaginationList } from '@/utils/formatJson'

export default {
  name: 'AddOrEditTenant',
  components: {},
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: 'add'
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    // const store = useStore().state
    const datas = reactive({
      showDialog: props.show,
      currentTitle: '新增租户',
      formInlineRef: ref(),
      addEditLoading: false,
      // userOptions: store.common.nameList,
      userOptions: [],
      formInline: {
        clientStatus: '',
        clientType: 1,
        clientName: '',
        clientShortName: '',
        person: '',
        personMobile: '',
        personEmail: '',
        managerId: '',
        managerName: '',
        content: '',
        address_city: '',
        address_area: '',
        address_detail: '',
        status: 1,
        isWatermark: false,
        clientCode: ''
      },
      rules: {
        clientStatus: [{ required: true, message: '请选择租户类型', trigger: 'change' }],
        clientType: [{ required: true, message: '请选择租户类型', trigger: 'change' }],
        clientName: [{ required: true, message: '请输入租户名', trigger: 'blur' }],
        clientShortName: [{ required: true, message: '请输入租户简称', trigger: 'blur' }],
        person: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
        personMobile: [{ validator: isMobile, required: true, trigger: 'blur' }],
        managerId: [{ required: true, message: '请选择管理员', trigger: 'change' }],
        status: [{ required: true, message: '请输入姓名', trigger: 'change' }],
        personEmail: [{ validator: isEmail, required: true, trigger: 'blur' }]
        // clientCode: [{ required: true, message: '请输入租户编号', trigger: 'blur' }]
      },
      clientType1Options: [
        { id: 1, name: '公司租户' },
        { id: 2, name: '集团租户' }
      ],
      clientTypeOptions: tenantTypeOptions
    });

    watch(
      () => props.show,
      newValue => {
        if (newValue) {
          datas.showDialog = newValue;
          if (props.title === 'add') {
            datas.currentTitle = '新增租户';
          } else {
            datas.currentTitle = '编辑租户';
          }
          if (props.data) {
            datas.formInline = JSON.parse(JSON.stringify(props.data));
          }
        }
      },
      { deep: true }
    );

    // 确定
    const dialogSuccess = () => {
      datas.formInlineRef.validate(valid => {
        if (valid) {
          if (props.title === 'add') {
            datas.formInline.id = '';
          }
          addTenant(datas.formInline).then(res => {
            if (res !== false) {
              ElMessage.success('保存成功！');
              datas.showDialog = false;
              context.emit('setInfo', datas.formInline);
            } else {
              // ElMessage.error('提交失败!')
            }
          });
        }
      });
    };
    // 取消
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };
    // 选择租户类型
    const changeClientType1 = type => {
      console.log(type);
    };
    const changeClientType = type => {
      console.log(type);
    };
    // 选择管理员
    const changeManager = id => {
      console.log(id);
      datas.userOptions.forEach(data => {
        if (data.id === id) {
          datas.formInline.managerName = data.nickname;
        }
      });
      console.log(datas.formInline);
    };

    // 获取管理员
    const getmangeList = () => {
      findManagerId().then(res => {
        datas.userOptions = res.data.data;
      });
    };
    getmangeList();
    return {
      ...toRefs(datas),
      close,
      getmangeList,
      getNameByid,
      dialogSuccess,
      changeClientType1,
      changeClientType,
      changeManager
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped></style>
