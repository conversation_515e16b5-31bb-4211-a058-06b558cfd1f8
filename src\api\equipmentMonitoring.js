import request from '@/utils/request';

/**
 * 获取视图列表
 */
export function queryViewList() {
  return request({
    url: '/api-device/device/view/list',
    method: 'get'
  });
}

/**
 * 获取设备列表
 */
export function queryDeviceList() {
  return request({
    url: '/api-device/device/acquisition/monitoringList',
    method: 'get'
  });
}

/**
 * 获取单个视图设置详情
 * @param {string} viewId  // 视图id
 */
export function queryViewSettingById(viewId) {
  return request({
    url: `/api-device/device/view/detail/${viewId}`,
    method: 'get'
  });
}

/**
 * 单个视图设置保存
 * @param {string} data.id // 数据id，无id为新增，id有值为更新
 * @param {string} data.viewName // 视图名称
 * @param {array} data.deviceList // 设备列表
 * @param {string} data.deviceList[].deviceId // 所属设备Id，无id为新增，id有值为更新
 * @param {0bject[]} data.deviceList[].codePointList // 所属设备码点列表
 * @param {string} data.deviceList[].codePointList[].id // 所属设备码点配置id，无id为新增，id有值为更新
 * @param {string} data.deviceList[].codePointList[].deviceCodePointId // 所属设备码点Id
 * @param {number} data.deviceList[].codePointList[].deviceCodePointNumber // 所属码点编号
 * @param {string} data.deviceList[].codePointList[].deviceCodePointName // 所属码点名称
 * @param {number} data.deviceList[].codePointList[].showType // 展现方式（1、控制图、2、折线图、3、单数图）
 * @param {number} data.deviceList[].codePointList[].refreshRate // 刷新频率
 * @param {string} data.deviceList[].codePointList[].associatedOrderNumber // 关联订单号
 * @param {number} data.deviceList[].codePointList[].groupCount // 分组数（个）
 * @param {number} data.deviceList[].codePointList[].UCL // 控制上限
 * @param {number} data.deviceList[].codePointList[].LCL // 控制下限
 * @returns {object} 空对象
 */
export function saveViewSetting(data) {
  return request({
    url: '/api-device/device/view/save',
    method: 'post',
    data
  });
}

/**
 * 八大准则异常日志推送
 * @param {string} data.deviceId // 设备ID
 * @param {string} data.pointId // 设备ID
 * @param {string} data.errorTime // 异常时间 yyyy-MM-dd HH:mm:ss
 * @param {string} data.errorStartTime // 异常开始时间
 * @param {string} data.errorEndTime // 异常结束时间
 * @param {number} data.errorType // 异常类型
 * @param {string} data.errorReason // 异常原因
 * @param {string} data.productionOrderNo // 生产订单号
 */
export function postPointDataErrorLog(data) {
  return request({
    url: '/api-device/device/gatherPointDataErrorLog/save',
    method: 'post',
    data
  });
}
