<template>
  <el-drawer
    v-model="drawerVisiable"
    title="查看点检标准"
    direction="rtl"
    :before-close="handleClose"
    size="88%"
    destroy-on-close
    custom-class="page-drawer"
  >
    <DrawerLayout v-loading="drawerLoading" :has-left-panel="false" :main-offset-top="53" :has-button-group="false">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="ruleInfo"
        label-position="right"
        size="small"
        label-width="110px"
        class="detail-form form-height-auto"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="standardNo" label="点检标准编号：">
              {{ formData.standardNo || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="standardName" label="点检标准名称：">
              {{ formData.standardName || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="仪器设备：">
              <el-tag
                v-for="item in formData.deviceList"
                :key="item.deviceId"
                type="primary"
                effect="dark"
                style="margin-right: 5px"
              >
                {{ item.deviceName }}（{{ item.deviceNumber }}）</el-tag
              >
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="remark" label="描述：">
              {{ formData.remark || '--' }}
            </el-form-item>
          </el-col>
        </el-row>
        <h3>点检标准明细</h3>
        <el-table
          ref="tableRef"
          :data="formData.itemList"
          class="dark-table base-table format-height-table2"
          fit
          border
          size="medium"
          height="auto"
          style="margin-top: 5px"
          @header-dragend="drageHeader"
        >
          <el-table-column label="序号" type="index" :width="colWidth.serialNo" />
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :label="item.fieldName"
            :prop="item.id"
            :min-width="120"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div v-if="item.fieldType">
                <span>{{ row.tableInfo[item.id] || '--' }}</span>
              </div>
              <div v-if="item.dictionaryCode">
                <!-- 字典选择框 -->
                <span>{{ dictionaryDataAssemble[item.dictionaryCode][row.tableInfo[item.id]] || '--' }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="drawer-fotter">
        <el-button :loading="drawerLoading" @click="handleClose">取消</el-button>
      </div>
    </DrawerLayout>
  </el-drawer>
</template>
<script>
// getCurrentInstance
import { reactive, ref, toRefs, watch } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { getDictionary } from '@/api/dictionary';
import DrawerLayout from '@/components/DrawerLayout';
import { pointInspectionStandardInfoId, pointInspectionFieldListAll } from '@/api/spotInspectionStandard';
export default {
  name: 'DrawerSpotStandardCheck',
  components: { DrawerLayout },
  props: {
    drawerShow: {
      type: Boolean,
      default: false
    },
    detailInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      formData: {
        itemList: [{ tableInfo: {} }],
        deviceList: []
      },
      formRef: ref(),
      type: '',
      tableHeader: [],
      drawerLoading: false,
      dictionaryList: [],
      ruleInfo: {},
      detailInfo: {},
      drawerVisiable: false,
      dictionaryDataAssemble: {}, // 字典集合
      dictionaryCodeAll: [],
      ruleForm: ref(),
      loading: false,
      tableRef: ref(null)
    });
    watch(props, newValue => {
      state.drawerVisiable = newValue.drawerShow;
      if (state.drawerVisiable) {
        state.detailInfo = props.detailInfo;
        state.formData = {
          itemList: [{ tableInfo: {} }],
          deviceList: []
        };
        getTableHeader();
        getDetailInfo();
      }
    });
    // 获取所有字典选项
    const getDictionaryDataAssemble = dictionaryCodeArray => {
      dictionaryCodeArray.forEach(item => {
        if (item) {
          state.dictionaryDataAssemble[item] = {};
          getDictionary(item).then(res => {
            state.loading = false;
            if (res) {
              const data = res.data.data.dictionaryoption;
              data.forEach(val => {
                if (val.status === 1) {
                  state.dictionaryDataAssemble[item][val.code] = val.name;
                }
              });
            }
          });
        }
      });
    };
    // 获取表格头
    const getTableHeader = () => {
      state.drawerLoading = true;
      pointInspectionFieldListAll({ status: 1 }).then(res => {
        state.drawerLoading = false;
        if (res) {
          state.tableHeader = res.data.data;
          const params = {};
          state.tableHeader.forEach(item => {
            params[item.id] = '';
          });
          if (!state.detailInfo.id) {
            state.formData.itemList = [{ order: 0, tableInfo: params }];
          }
          state.dictionaryCodeAll = state.tableHeader.map(item => item.dictionaryCode);
          getDictionaryDataAssemble([...new Set(state.dictionaryCodeAll)]);
        }
      });
    };
    const handleAdd = () => {
      state.formData.itemList.push({
        order: state.formData.itemList.length,
        tableInfo: {}
      });
    };
    const getDetailInfo = () => {
      state.drawerLoading = true;
      pointInspectionStandardInfoId(state.detailInfo.id).then(res => {
        state.drawerLoading = false;
        if (res) {
          state.formData = res.data.data;
          // state.formData.itemList.forEach(item => {
          //   item = Object.assign(item, item.tableInfo)
          // })
        }
      });
    };

    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog');
    };
    const handleChange = (val, index, field) => {
      state.formData.itemList[index].tableInfo[field] = val;
    };
    return {
      ...toRefs(state),
      handleChange,
      getDictionaryDataAssemble,
      getTableHeader,
      handleAdd,
      handleClose,
      getDetailInfo,
      formatDate,
      colWidth,
      drageHeader
    };
  }
};
</script>
<style lang="scss" scoped>
.detail-form .el-form-item {
  margin-bottom: 0;
}

h3 {
  line-height: 1;
  display: block;
  margin-top: 20px;
}

::v-deep(.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 31.5rem) !important;
    overflow-y: auto;
  }
}
</style>
