<template>
  <component :is="type" v-bind="linkProps(to)">
    <slot />
  </component>
</template>

<script>
import { isExternal } from '@/utils/validate';
// import { reactive } from 'vue'

export default {
  name: 'AppLink',
  props: {
    to: {
      type: String,
      required: true
    }
  },
  setup(props, context) {
    // const state = reactive({
    //   platformList: []
    // })
  },
  computed: {
    isExternal() {
      return (
        isExternal(this.to) ||
        this.to === '/smart-charts/dataBoard' ||
        this.to === '/smart-charts/aging-laboratory' ||
        this.to === '/smart-charts/equipmentMonitoring'
      );
    },
    type() {
      if (this.isExternal) {
        return 'a';
      }
      return 'router-link';
    }
  },
  methods: {
    linkProps(to) {
      if (this.isExternal) {
        return {
          href: to,
          target: '_blank',
          rel: 'noopener'
        };
      }
      return {
        to: to
      };
    }
  }
};
</script>
