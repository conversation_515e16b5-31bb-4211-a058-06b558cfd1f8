<template>
  <!--新增国网项目库数据-->
  <el-dialog v-model="isshow" title="国网项目库选择" width="900px" :before-close="colseModel">
    <!--<el-dialog v-model="show" title="国网项目库选择" width="800px" :before-close="handleClose">-->
    <div class="search-box">
      <el-input
        v-model="keyword"
        size="medium"
        placeholder="输入项目编号/项目名称搜索"
        prefix-icon="el-icon-search"
        class="ipt-360"
      />
      <el-button type="primary" size="medium" class="btn-search" @click="search">查询</el-button>
    </div>
    <el-table
      ref="multipleSelection"
      :data="tablePush"
      height="400"
      tooltip-effect="dark"
      style="width: 100%"
      class="base-table"
    >
      <el-table-column label="选择" width="60">
        <template #default="scope">
          <el-checkbox v-model="scope.row.checked" />
        </template>
      </el-table-column>
      <el-table-column prop="number" label="项目编号" width="150" />
      <el-table-column prop="name" label="项目名称" width="420" />
      <el-table-column prop="testCapability" label="项目等级" width="120">
        <template #default="{ row }">
          {{ formatValue(row.testCapability) }}
        </template>
      </el-table-column>
      <el-table-column prop="address" label="状态">
        <template #default="{ row }">
          <div :class="row.status == '0' ? 'icon-tes-info' : 'icon-tes-success'">
            {{ row.status === 1 ? '启用' : '停用' }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      small
      :page="page"
      :limit="limit"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="currentChangePage"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="colseModel">取 消</el-button>
        <el-button type="primary" @click="handleData" @keyup.prevent @keydown.enter.prevent>确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { watch, reactive, toRefs } from 'vue';
import Pagination from '@/components/Pagination';
// import { saveCapability, updateCapability } from '@/api/user'
import { ElMessage } from 'element-plus';
import _ from 'lodash';
import { extcapabilitymapSave } from '@/api/user';
import { formatTestcapabilityByValue } from '@/utils/formatJson';
export default {
  name: 'ModelAdd',
  components: { Pagination },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    table: {
      type: Array,
      default: function () {
        return [];
      }
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    watch(props, newValue => {
      state.isshow = newValue.show;
      state.capabilityid = newValue.capabilityid;
      state.tabledata = newValue.table;
      state.total = newValue.table.length;
      state.capabilityid = newValue.data.capabilityid;
      if (newValue.show === false) {
        reset();
        currentChangePage({ limit: 20, page: 1 });
      }
    });
    const state = reactive({
      isshow: props.show,
      capabilityid: props.capabilityid,
      tabledata: props.table,
      multipleSelection: [],
      updateList: [],
      tablePush: [],
      total: 0,
      page: 1,
      limit: 20,
      keyword: ''
    });
    // 分页方法
    const currentChangePage = query => {
      console.log('size');
      console.log(query);
      state.tablePush = [];
      state.tabledata.forEach((item, index) => {
        if (query.limit * (query.page - 1) <= index && index <= query.limit * query.page - 1) {
          state.tablePush.push(item);
        }
      });
      return state.tablePush;
    };

    const formatValue = val => {
      return formatTestcapabilityByValue(val);
    };
    const colseModel = () => {
      reset();
      context.emit('close');
    };
    const handleData = () => {
      state.updateList = [];
      state.tabledata.forEach(item => {
        if (item.checked === true) {
          state.updateList.push(item);
        }
      });
      extcapabilitymapSave({ capabilityid: state.capabilityid, voList: state.updateList }).then(res => {
        if (res.data.code === 200) {
          ElMessage({
            type: 'success',
            message: '操作成功!'
          });
          state.isshow = false;
          context.emit('close');
        }
      });
    };
    // 搜索
    const search = () => {
      let searchList = [];
      const objList = state.tabledata;
      if (state.keyword) {
        console.log('keyword');
        console.log(state.keyword);
        searchList = _.filter(objList, function (o) {
          return o.name.includes(state.keyword) || o.number.includes(state.keyword);
        });
        state.tablePush = searchList;
        state.total = searchList.length;
      } else {
        state.total = state.tabledata.length;
        currentChangePage({ limit: 10, page: 1 });
      }
      state.page = 1;
      state.limit = 20;
    };
    const reset = () => {
      state.keyword = '';
      state.page = 1;
      state.total = 0;
      state.limit = 20;
      state.tablePush = [];
      state.updateList = [];
    };
    return {
      colseModel,
      handleData,
      currentChangePage,
      formatValue,
      reset,
      search,
      ...toRefs(state)
    };
  }
};
</script>

<style lang="scss" scoped>
.search-box {
  margin-bottom: 8px;
}
.ipt-360 {
  width: 360px;
  margin-right: 8px;
}
</style>
