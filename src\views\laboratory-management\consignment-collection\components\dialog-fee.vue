<template>
  <el-dialog
    v-model="dialogShow"
    :title="rowDetail.id ? '编辑费用' : '新增费用'"
    :close-on-click-modal="false"
    width="480px"
    @close="handleClose"
  >
    <!-- 新增委托费用 -->
    <el-form
      v-if="dialogShow"
      ref="ruleForm"
      v-loading="dialogLoading"
      :model="formData"
      label-position="right"
      label-width="100px"
      size="small"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="费用类型："
            prop="costType"
            :rules="{ required: true, message: '请选择费用类型', trigger: 'change' }"
          >
            <el-select
              v-model="formData.costType"
              placeholder="请选择费用类型"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option v-for="item in costTypeList" :key="item.code" :label="item.name" :value="item.name" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="费用描述：" prop="remark">
            <el-input v-model="formData.remark" maxlength="100" type="text" clearable placeholder="请输入费用描述" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="金额(￥)："
            prop="inspectionCost"
            :rules="{ validator: isMoney, validata: formData.inspectionCost, trigger: 'change' }"
          >
            <el-input v-model="formData.inspectionCost" type="text" maxlength="30" clearable placeholder="请输入金额" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, ref, toRefs, watch, getCurrentInstance } from 'vue';
import { saveTaskFeeInfo } from '@/api/task-registration';
import { isMoney } from '@/utils/validate';
import { useRoute } from 'vue-router';

export default {
  name: 'DialogFree',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    costType: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const route = useRoute();
    const { proxy } = getCurrentInstance();
    const state = reactive({
      dialogLoading: false, // 弹出窗loading
      formData: {}, // 表单数据
      rowDetail: {},
      costTypeList: [],
      dialogShow: false,
      ruleForm: ref()
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.rowDetail = props.detailData;
        state.costTypeList = props.costType;
        state.formData = {};
      }
    });

    const onSubmit = () => {
      proxy.$refs['ruleForm'].validate(valid => {
        if (valid) {
          state.dialogLoading = true;
          saveTaskFeeInfo({ ...state.formData, superId: route.query.id }).then(res => {
            state.dialogLoading = false;
            if (res) {
              proxy.$message.success(res.data.message);
              context.emit('closeDialog', true);
            }
          });
        } else {
          return false;
        }
      });
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog', { isRefresh: false });
    };
    const handleRadioResult = val => {
      if (val === 'Scrapped') {
        state.formData.isRemeasured = 0;
        state.formData.description = '';
      }
    };
    return { ...toRefs(state), onSubmit, isMoney, handleClose, handleRadioResult };
  }
};
</script>
<style lang="scss" scoped>
.unitClass {
  text-align: center;
  margin-left: 5px;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  background-color: rgb(245, 247, 250);
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 100%;
}
:deep(.el-select) {
  width: 100%;
}
:deep(.el-input-number--small) {
  width: 100%;
}
</style>
