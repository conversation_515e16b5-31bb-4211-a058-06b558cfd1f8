/**
 * Created by PanJia<PERSON>hen on 16/11/18.
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor'];
  return valid_map.indexOf(str.trim()) >= 0;
}

/**
 * @param {string} url
 * @returns {Boolean}
 */
export function validURL(url) {
  const reg =
    /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/;
  return reg.test(url);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validLowerCase(str) {
  const reg = /^[a-z]+$/;
  return reg.test(str);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUpperCase(str) {
  const reg = /^[A-Z]+$/;
  return reg.test(str);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validAlphabets(str) {
  const reg = /^[A-Za-z]+$/;
  return reg.test(str);
}

/**
 * @param {string} email
 * @returns {Boolean}
 */
export function validEmail(email) {
  const reg =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return reg.test(email);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function isString(str) {
  if (typeof str === 'string' || str instanceof String) {
    return true;
  }
  return false;
}

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray(arg) {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]';
  }
  return Array.isArray(arg);
}

// 验证是否整数
export function isInteger(rule, value, callback) {
  const re = /^(0|[1-9][0-9]*)$/;
  const rsCheck = re.test(value);
  if (!rsCheck) {
    callback(new Error('请输入自然数(大于等于0的整数)'));
  } else {
    callback();
  }
}

export function isInteger1(rule, value, callback) {
  const re = /^(([1-9]\d+)|([1-9]))$/;
  const rsCheck = re.test(value);
  if (value && !rsCheck) {
    callback(new Error('请输入正整数'));
  } else {
    callback();
  }
}
export function isInteger2(rule, value, callback) {
  const re = /^(([0-9]\d+)|([0-9]))$/;
  const rsCheck = re.test(value);
  if (value && !rsCheck) {
    callback(new Error('请输入整数'));
  } else {
    callback();
  }
}
// 手机号的校验
export function isMobile(rule, value, callback) {
  const mobileReg = /^((13[0-9])|(14[5,7])|(15[0-3,5-9])|(17[0,3,5-8])|(18[0-9])|166|198|199|(147))\d{8}$/;
  if (value === undefined || value === '' || value === null) {
    callback(new Error('请输入手机号'));
  } else if (mobileReg.test(value)) {
    callback();
  } else {
    callback(new Error('请输入手机号的正确格式'));
  }
}
// 邮箱的校验
export function isEmail(rule, value, callback) {
  const emailReg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
  if (value === undefined || value === '' || value === null) {
    callback(new Error('请输入邮箱'));
  } else if (emailReg.test(value)) {
    callback();
  } else {
    callback(new Error('请输入邮箱的正确格式'));
  }
}

// 英文字母，数字以及下划线组成
export function isUsername(rule, value, callback) {
  const reg = /^[_a-zA-Z0-9]+$/;
  if (value === '' || value === undefined || value == null) {
    callback(new Error('请输入用户名'));
  } else {
    if (!reg.test(value)) {
      callback(new Error('仅支持英文字母，数字以及下划线组成'));
    } else {
      callback();
    }
  }
}
// 手机号的校验不必填写
export function isMobile2(rule, value, callback) {
  const mobileReg = /^((13[0-9])|(14[5,7])|(15[0-3,5-9])|(17[0,3,5-8])|(18[0-9])|166|198|199|(147))\d{8}$/;
  if (mobileReg.test(value) || value === undefined || value === '' || value === null) {
    callback();
  } else {
    callback(new Error('请输入手机号的正确格式'));
  }
}
// 电话号或手机号的校验不必填写
export function isPhoneMobile(rule, value, callback) {
  const phone = /^(\d{3,4})?\d{7,8}$/i;
  const phone2 = /0\d{1,2}-\d{7,8}$/i;
  const phone3 = /0\d{3,4}-\d{7,8}$/i;
  const mobileReg = /^((13[0-9])|(14[5,7])|(15[0-3,5-9])|(17[0,3,5-8])|(18[0-9])|166|198|199|(147))\d{8}$/;
  if (value) {
    if (mobileReg.test(value) || phone.test(value) || phone2.test(value) || phone3.test(value)) {
      callback();
    } else {
      callback(new Error('请输入正确的手机号或座机号'));
    }
  } else {
    callback();
  }
}
// 传真号
export function isFaxNoMobile(rule, value, callback) {
  const faxNoReg = /^(?:\d{3,4}-)?\d{7,8}(?:-\d{1,6})?$/;
  if (value === undefined || value === '' || value === null) {
    callback();
  } else {
    if (faxNoReg.test(value)) {
      callback();
    } else {
      callback(new Error('请输入正确的传真号'));
    }
  }
}

// 邮箱的校验不必填写
export function isEmail2(rule, value, callback) {
  const emailReg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
  if (emailReg.test(value) || value === undefined || value === '' || value === null) {
    callback();
  } else {
    callback(new Error('请输入邮箱的正确格式'));
  }
}
// 部门编号的校验不必填写
export function isAlphanumeric(rule, value, callback) {
  const numberReg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]*$/;
  if (numberReg.test(value) || value === undefined || value === '' || value === null) {
    callback();
  } else {
    callback(new Error('只能输入字母和数字的组合'));
  }
}
// 不能输入中文 不必填
export function isZw(rule, value, callback) {
  const zwReg = /[\u4E00-\u9FA5]/g;
  if (value && zwReg.test(value)) {
    callback(new Error('不能输入中文'));
  } else {
    callback();
  }
}

// 业务编码-》起始序号
export function initialSequence(rule, value, callback) {
  const re = /^(([1-9]\d+)|([1-9]))$/;
  const rsCheck = re.test(value);
  // 规定了序号位数
  if (rule.value && re.test(rule.value)) {
    if (!rsCheck) {
      callback(new Error('请输入大于0的正确数字'));
    } else {
      const length = value.toString().length;
      if (length > Number(rule.value)) {
        callback(new Error('请输入' + Number(rule.value) + '位内的数字'));
      } else {
        callback();
      }
    }
  } else {
    callback(new Error('请先输入序号位数'));
  }
}
export function randomdigits(rule, value, callback) {
  const re = /^(([1-9]\d+)|([1-9]))$/;
  const rsCheck = re.test(value);
  if (!rsCheck) {
    callback(new Error('请输入正整数'));
  } else {
    if (value < 8 || value > 20) {
      callback(new Error('请输入8-20以内的整数'));
    } else {
      callback();
    }
  }
}

export function isDigital(rule, value, callback) {
  if (value) {
    if (Number(value) || Number(value) === 0) {
      // 是数字类型但0开头的字符串也能通过，只需要再判断一下是否是0开头的长度大于1的字符串即可
      if (value.length > 1 && value.substr(0, 1) === '0' && value.split('.').length === 1) {
        callback(new Error('请输入数字'));
      } else if (value.substr(value.length - 1, 1) === '.') {
        callback(new Error('请输入数字'));
      } else if (value.substr(0, 1) === '.') {
        callback(new Error('请输入数字'));
      } else {
        callback();
      }
    } else {
      callback(new Error('请输入数字'));
    }
  } else {
    callback(new Error('请输入数字'));
  }
}
export function isDigital2(rule, value, callback) {
  if (value) {
    if (Number(value) || Number(value) === 0) {
      // 是数字类型但0开头的字符串也能通过，只需要再判断一下是否是0开头的长度大于1的字符串即可
      if (value.length > 1 && value.substr(0, 1) === '0' && value.split('.').length === 1) {
        callback(new Error('请输入数字'));
      } else if (value.substr(value.length - 1, 1) === '.') {
        callback(new Error('请输入数字'));
      } else if (value.substr(0, 1) === '.') {
        callback(new Error('请输入数字'));
      } else {
        callback();
      }
    } else {
      callback(new Error('请输入数字'));
    }
  } else {
    callback();
  }
}
// 大于0的整数和小数
export function greaterThanZero(rule, value, callback) {
  if (value) {
    if (!isNaN(Number(value))) {
      if (Number(value) < 0) {
        callback(new Error('请输入大于0数字'));
      } else {
        callback();
      }
    } else {
      callback(new Error('请输入数字'));
    }
  } else {
    callback(new Error('请输入数字'));
  }
}
// 大于0的整数和小数
export function greaterThanZero2(rule, value, callback) {
  if (value) {
    if (!isNaN(Number(value))) {
      // 是数字类型但0开头的字符串也能通过，只需要再判断一下是否是0开头的长度大于1的字符串即可
      if (Number(value) < 0) {
        callback(new Error('请输入不小于0的数字'));
      } else {
        callback();
      }
    } else {
      callback(new Error('请输入数字'));
    }
  } else {
    callback();
  }
}
export function twoDecimalPlaces(rule, value, callback) {
  const decimalPlacesRe = /^(0|[1-9]\d{0,1})(\.\d{1,2})?$/; // 正两位小数的正则
  const positiveIntegerRe = /^(([1-9]\d+)|([1-9]))$/; // 正整数的正则
  if (value) {
    if (!decimalPlacesRe.test(value) && !positiveIntegerRe.test(value)) {
      callback(new Error('请输入两位小数以内的数字'));
    } else {
      callback();
    }
  } else {
    callback();
  }
}
export function isNumberNot(rule, value, callback) {
  if (rule.validata) {
    if (Number(rule.validata) || Number(rule.validata) === 0) {
      // 是数字类型但0开头的字符串也能通过，只需要再判断一下是否是0开头的长度大于1的字符串即可
      if (rule.validata.length > 1 && rule.validata.substr(0, 1) === '0' && rule.validata.split('.').length === 1) {
        callback(new Error('请输入数字'));
      } else if (rule.validata.substr(rule.validata.length - 1, 1) === '.') {
        callback(new Error('请输入数字'));
      } else if (rule.validata.substr(0, 1) === '.') {
        callback(new Error('请输入数字'));
      } else {
        callback();
      }
    } else {
      callback(new Error('请输入数字'));
    }
  } else {
    callback();
  }
}
export function isMoney(rule, value, callback) {
  if (value) {
    if (Number(value) || Number(value) === 0) {
      // 是数字类型但0开头的字符串也能通过，只需要再判断一下是否是0开头的长度大于1的字符串即可
      if (value.length > 1 && value.substr(0, 1) === '0' && value.split('.').length === 1) {
        callback(new Error('请输入正确金额'));
      } else if (value.substr(value.length - 1, 1) === '.') {
        callback(new Error('请输入正确金额'));
      } else if (value.substr(0, 1) === '.') {
        callback(new Error('请输入正确金额'));
      } else if (Number(value) < 0) {
        callback(new Error('请输入大于零的金额'));
      } else {
        callback();
      }
    } else {
      callback(new Error('请输入正确金额'));
    }
  } else {
    callback(new Error('请输入金额'));
  }
}
export function amountReceived(rule, value, callback) {
  if (value) {
    if (Number(value) && Number(value) > 0) {
      // 是数字类型但0开头的字符串也能通过，只需要再判断一下是否是0开头的长度大于1的字符串即可
      if (value.length > 1 && value.substr(0, 1) === '0' && value.split('.').length === 1) {
        callback(new Error('请输入数字'));
      } else if (value.substr(value.length - 1, 1) === '.') {
        callback(new Error('请输入数字'));
      } else if (value.substr(0, 1) === '.') {
        callback(new Error('请输入数字'));
      } else {
        if (Number(value) > rule.validData.receivableCost - rule.validData.receivedCost) {
          callback(new Error('收款金额不能大于未收款金额'));
        } else if (value?.split('.')[1]?.length > 2) {
          callback(new Error('请输入正确金额'));
        } else {
          callback();
        }
      }
    } else if (Number(value) <= 0) {
      callback(new Error('收款金额必须大于0'));
    } else {
      callback(new Error('请输入数字'));
    }
  } else {
    callback(new Error('请输入数字'));
  }
}
export function discountCount(rule, value, callback) {
  if (value || Number(value) === 0) {
    if (value < rule.validData.paidCost) {
      callback(new Error('收款金额不能小于已收款金额'));
    } else if (value > rule.validData.receivableCost) {
      callback(new Error('折扣后金额不能大于应收金额'));
    } else {
      callback();
    }
  } else {
    callback(new Error('请输入收款金额'));
  }
}
// 开始日期不能大于结束日期
export function durationDate(rule, value, callback) {
  let ruleValue = rule.value;
  if (typeof ruleValue === 'function') {
    ruleValue = rule.value();
  } else if (typeof ruleValue === 'object') {
    ruleValue = rule.value;
  }
  if (ruleValue.startDate && ruleValue.endDate) {
    if (new Date(ruleValue.endDate).getTime() < new Date(ruleValue.startDate).getTime()) {
      callback(new Error(rule.message));
    } else {
      callback();
    }
  } else {
    callback();
  }
}
// 非负整数可以为空
export function validateNumber(rule, value, callback) {
  const numberReg = /^\d+$|^\d+[.]?\d+$/;
  if (value === '' || value === undefined || value === null) {
    callback();
  } else {
    if (numberReg.test(value)) {
      if (value <= 0) {
        callback(new Error('请输入非负整数'));
      } else {
        callback();
      }
    } else {
      callback(new Error('请输入非负整数'));
    }
  }
}
export function restrictMaxLength(rule, value, callback) {
  if (value) {
    if (value.toString().length > rule.maxlength) {
      callback(new Error(rule.message));
    } else {
      callback();
    }
  } else {
    callback();
  }
}

export function publicIsNumber(inputStr) {
  if (inputStr !== 0 && !inputStr) {
    return false;
  }
  if (isNaN(Number(inputStr))) {
    return false;
  }
  return true;
}

// 非必填身份证号
export function idNumberRex(rule, value, callback) {
  const idNumRe1 = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/;
  const idNumRe2 = /^[1-9]\d{5}\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}$/;
  if (value) {
    if (!idNumRe1.test(value) && !idNumRe2.test(value)) {
      callback(new Error('请输入正确格式的身份证'));
    } else {
      callback();
    }
  } else {
    callback();
  }
}
