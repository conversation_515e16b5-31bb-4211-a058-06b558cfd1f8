<template>
  <!-- 打印 -->
  <el-dialog v-model="dialogShow" title="修改" width="500px" :close-on-click-modal="false">
    <el-form :model="formData" label-position="top" label-width="110px">
      <el-form-item label="特殊标识：" prop="nuclearMarker">
        <el-input v-model="formData.nuclearMarker" clearable placeholder="请输入特殊标识" />
      </el-form-item>
      <el-form-item label="生产日期：" prop="suppDate">
        <el-date-picker
          v-model="formData.suppDate"
          type="date"
          placeholder="请选择生产日期"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
          class="!w-full"
        />
      </el-form-item>
      <el-form-item label="进厂日期：" prop="wareHouseDate">
        <el-date-picker
          v-model="formData.wareHouseDate"
          type="date"
          placeholder="请选择进厂日期"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
          class="!w-full"
        />
      </el-form-item>
      <el-form-item label="寿期：" prop="validityDate">
        <el-date-picker
          v-model="formData.validityDate"
          type="date"
          placeholder="请选择寿期"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
          class="!w-full"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="cancelDialog()">取消</el-button>
      <el-button type="primary" @click="handleSubmit()">确定</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { ElMessage } from 'element-plus';
import { saveCertificatePrint } from '@/api/certificate-export';
import { reactive, watch, toRefs } from 'vue';
import { cloneDeep } from 'lodash';
export default {
  name: 'DialogBatchPrint',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    selectRow: {
      type: Array,
      default: () => []
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      selectData: {},
      printNumber: null,
      dialogLoading: false,
      nuclearMarker: [],
      dialogShow: false,
      suppDate: null,
      formData: {}
    });

    const cancelDialog = value => {
      state.dialogShow = false;
      context.emit('closeDialog', value);
    };

    const handleSubmit = async () => {
      state.dialogLoading = true;
      const params = {
        ...state.selectData,
        ...state.formData,
        nuclearMarker: state.formData.nuclearMarker.toString()
      };
      const { data } = await saveCertificatePrint({ entityList: [params] }).finally(
        () => (state.dialogLoading = false)
      );
      if (data) {
        ElMessage.success('编辑成功!');
        cancelDialog(true);
      }
    };

    watch(
      () => props.dialogVisible,
      newValue => {
        state.dialogShow = props.dialogVisible;
        if (newValue) {
          state.selectData = props.selectRow[0];
          state.formData = cloneDeep(state.selectData);
          state.formData.nuclearMarker = state.selectData.nuclearMarker
            ? state.selectData?.nuclearMarker?.split(',')
            : [];
        }
      }
    );

    return {
      ...toRefs(state),
      cancelDialog,
      handleSubmit
    };
  }
};
</script>
<style lang="scss" scoped></style>
