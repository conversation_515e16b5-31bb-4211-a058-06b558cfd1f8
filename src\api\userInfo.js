import request from '@/utils/request';

// 查询用户单个记录
export function getuserBaseInfo(id) {
  return request({
    url: '/api-user/user/sysuser/users/' + id,
    method: 'get'
  });
}
// 上传签名
export function uploadSysuser(data) {
  return request({
    url: '/api-user/user/sysuser/upload',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data
  });
}
// 新增或者是修改用户
export function SysuserInfoSaveOrUpdate(data) {
  return request({
    url: '/api-user/user/sysuser/users/saveOrUpdate',
    method: 'post',
    data
  });
}
// 获取密钥二维码
export function getQRCodeImage() {
  return request({
    url: '/api-user/user/googleAuthenticator/QRCode',
    method: 'get'
  });
}
/**
 * 开启或关闭二次验证
 * @param {number} data.isEnabled
 * @returns {string} 开启返回密钥，关闭返回空字符串
 */
export function enableSecretAuth(data) {
  return request({
    url: '/api-user/user/googleAuthenticator/enableSecretAuth',
    method: 'get',
    params: data
  });
}
/**
 * 是否开启了二次验证
 * @param {string} data.username
 * @returns {boolean}
 */
export function isEnabledSecretAuth(data) {
  return request({
    url: '/api-user/user/googleAuthenticator/isEnabledSecretAuth',
    method: 'get',
    params: data
  });
}
/**
 * 登录时验证6位动态密码（腾讯令牌小程序-扫描密钥二维码获得）
 * @param {number} data.code
 * @param {string} data.username
 * @returns {boolean}
 */
export function verifyCode(data) {
  return request({
    url: '/api-user/user/googleAuthenticator/verifyCode',
    method: 'get',
    params: data
  });
}
// 我的企业-查询列表
export function getMyBusinessList(data) {
  return request({
    url: '/api-user/user/sysclientemployee/list',
    method: 'post',
    data
  });
}
// 我的企业-同意\拒绝\退出
export function updateMyBusiness(data) {
  return request({
    url: '/api-user/user/sysclientemployee/update',
    method: 'post',
    data
  });
}
