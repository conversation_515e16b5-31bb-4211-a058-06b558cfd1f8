/* eslint-disable */

// #region 全局变量

var baseWasmUrl =
  (window.location.origin === 'http://localhost:9527' ? 'http://*************' : window.location.origin) +
  '/attachmentview/tes-wasm';
var publicI = -1;
var lengthNum = 0;
var focusDom = [];
var wasmModuleExports = null;
var Calculate1NumFormula = null;
var Calculate2NumFormula = null;
var Calculate3NumFormula = null;
var Calculate4NumFormula = null;
var Calculate5NumFormula = null;
var Calculate6NumFormula = null;
var Calculate7NumFormula = null;
var CalculateArrayNumFormula = null;
var CalculateArrayAnd1NumFormula = null;

// #endregion

// #region wasm loader

// GENERATED FILE. DO NOT EDIT.
var loader = (function (exports) {
  'use strict';

  Object.defineProperty(exports, '__esModule', {
    value: true
  });
  exports.default = void 0;
  exports.demangle = demangle;
  exports.instantiate = instantiate;
  exports.instantiateStreaming = instantiateStreaming;
  exports.instantiateSync = instantiateSync;
  // Runtime header offsets
  const ID_OFFSET = -8;
  const SIZE_OFFSET = -4; // Runtime ids

  const ARRAYBUFFER_ID = 0;
  const STRING_ID = 1; // const ARRAYBUFFERVIEW_ID = 2;
  // Runtime type information

  const ARRAYBUFFERVIEW = 1 << 0;
  const ARRAY = 1 << 1;
  const STATICARRAY = 1 << 2; // const SET = 1 << 3;
  // const MAP = 1 << 4;

  const VAL_ALIGN_OFFSET = 6; // const VAL_ALIGN = 1 << VAL_ALIGN_OFFSET;

  const VAL_SIGNED = 1 << 11;
  const VAL_FLOAT = 1 << 12; // const VAL_NULLABLE = 1 << 13;

  const VAL_MANAGED = 1 << 14; // const KEY_ALIGN_OFFSET = 15;
  // const KEY_ALIGN = 1 << KEY_ALIGN_OFFSET;
  // const KEY_SIGNED = 1 << 20;
  // const KEY_FLOAT = 1 << 21;
  // const KEY_NULLABLE = 1 << 22;
  // const KEY_MANAGED = 1 << 23;
  // Array(BufferView) layout

  const ARRAYBUFFERVIEW_BUFFER_OFFSET = 0;
  const ARRAYBUFFERVIEW_DATASTART_OFFSET = 4;
  const ARRAYBUFFERVIEW_BYTELENGTH_OFFSET = 8;
  const ARRAYBUFFERVIEW_SIZE = 12;
  const ARRAY_LENGTH_OFFSET = 12;
  const ARRAY_SIZE = 16;
  const E_NO_EXPORT_TABLE = 'Operation requires compiling with --exportTable';
  const E_NO_EXPORT_RUNTIME = 'Operation requires compiling with --exportRuntime';

  const F_NO_EXPORT_RUNTIME = () => {
    throw Error(E_NO_EXPORT_RUNTIME);
  };

  const BIGINT = typeof BigUint64Array !== 'undefined';
  const THIS = Symbol();
  const STRING_SMALLSIZE = 192; // break-even point in V8

  const STRING_CHUNKSIZE = 1024; // mitigate stack overflow

  const utf16 = new TextDecoder('utf-16le', {
    fatal: true
  }); // != wtf16

  /** polyfill for Object.hasOwn */

  Object.hasOwn =
    Object.hasOwn ||
    function (obj, prop) {
      return Object.prototype.hasOwnProperty.call(obj, prop);
    };
  /** Gets a string from memory. */

  function getStringImpl(buffer, ptr) {
    let len = new Uint32Array(buffer)[(ptr + SIZE_OFFSET) >>> 2] >>> 1;
    const wtf16 = new Uint16Array(buffer, ptr, len);
    if (len <= STRING_SMALLSIZE) return String.fromCharCode(...wtf16);

    try {
      return utf16.decode(wtf16);
    } catch {
      let str = '',
        off = 0;

      while (len - off > STRING_CHUNKSIZE) {
        str += String.fromCharCode(...wtf16.subarray(off, (off += STRING_CHUNKSIZE)));
      }

      return str + String.fromCharCode(...wtf16.subarray(off));
    }
  }
  /** Prepares the base module prior to instantiation. */

  function preInstantiate(imports) {
    const extendedExports = {};

    function getString(memory, ptr) {
      if (!memory) return '<yet unknown>';
      return getStringImpl(memory.buffer, ptr);
    } // add common imports used by stdlib for convenience

    const env = (imports.env = imports.env || {});

    env.abort =
      env.abort ||
      function abort(msg, file, line, colm) {
        const memory = extendedExports.memory || env.memory; // prefer exported, otherwise try imported

        throw Error(`abort: ${getString(memory, msg)} at ${getString(memory, file)}:${line}:${colm}`);
      };

    env.trace =
      env.trace ||
      function trace(msg, n, ...args) {
        const memory = extendedExports.memory || env.memory;
        console.log(`trace: ${getString(memory, msg)}${n ? ' ' : ''}${args.slice(0, n).join(', ')}`);
      };

    env.seed = env.seed || Date.now;
    imports.Math = imports.Math || Math;
    imports.Date = imports.Date || Date;
    return extendedExports;
  }
  /** Prepares the final module once instantiation is complete. */

  function postInstantiate(extendedExports, instance) {
    const exports = instance.exports;
    const memory = exports.memory;
    const table = exports.table;

    const __new = exports.__new || F_NO_EXPORT_RUNTIME;

    const __pin = exports.__pin || F_NO_EXPORT_RUNTIME;

    const __unpin = exports.__unpin || F_NO_EXPORT_RUNTIME;

    const __collect = exports.__collect || F_NO_EXPORT_RUNTIME;

    const __rtti_base = exports.__rtti_base;
    const getRttiCount = __rtti_base ? arr => arr[__rtti_base >>> 2] : F_NO_EXPORT_RUNTIME;
    extendedExports.__new = __new;
    extendedExports.__pin = __pin;
    extendedExports.__unpin = __unpin;
    extendedExports.__collect = __collect;
    /** Gets the runtime type info for the given id. */

    function getRttInfo(id) {
      const U32 = new Uint32Array(memory.buffer);
      if ((id >>>= 0) >= getRttiCount(U32)) throw Error(`invalid id: ${id}`);
      return U32[((__rtti_base + 4) >>> 2) + (id << 1)];
    }
    /** Gets the runtime base id for the given id. */

    function getRttBase(id) {
      const U32 = new Uint32Array(memory.buffer);
      if ((id >>>= 0) >= getRttiCount(U32)) throw Error(`invalid id: ${id}`);
      return U32[((__rtti_base + 4) >>> 2) + (id << 1) + 1];
    }
    /** Gets and validate runtime type info for the given id for array like objects */

    function getArrayInfo(id) {
      const info = getRttInfo(id);
      if (!(info & (ARRAYBUFFERVIEW | ARRAY | STATICARRAY))) throw Error(`not an array: ${id}, flags=${info}`);
      return info;
    }
    /** Gets the runtime alignment of a collection's values. */

    function getValueAlign(info) {
      return 31 - Math.clz32((info >>> VAL_ALIGN_OFFSET) & 31); // -1 if none
    }
    /** Gets the runtime alignment of a collection's keys. */
    // function getKeyAlign(info) {
    //   return 31 - Math.clz32((info >>> KEY_ALIGN_OFFSET) & 31); // -1 if none
    // }

    /** Allocates a new string in the module's memory and returns its pointer. */

    function __newString(str) {
      if (str == null) return 0;
      const length = str.length;

      const ptr = __new(length << 1, STRING_ID);

      const U16 = new Uint16Array(memory.buffer);

      for (var i = 0, p = ptr >>> 1; i < length; ++i) U16[p + i] = str.charCodeAt(i);

      return ptr;
    }

    extendedExports.__newString = __newString;
    /** Allocates a new ArrayBuffer in the module's memory and returns its pointer. */

    function __newArrayBuffer(buf) {
      if (buf == null) return 0;
      const bufview = new Uint8Array(buf);

      const ptr = __new(bufview.length, ARRAYBUFFER_ID);

      const U8 = new Uint8Array(memory.buffer);
      U8.set(bufview, ptr);
      return ptr;
    }

    extendedExports.__newArrayBuffer = __newArrayBuffer;
    /** Reads a string from the module's memory by its pointer. */

    function __getString(ptr) {
      if (!ptr) return null;
      const buffer = memory.buffer;
      const id = new Uint32Array(buffer)[(ptr + ID_OFFSET) >>> 2];
      if (id !== STRING_ID) throw Error(`not a string: ${ptr}`);
      return getStringImpl(buffer, ptr);
    }

    extendedExports.__getString = __getString;
    /** Gets the view matching the specified alignment, signedness and floatness. */

    function getView(alignLog2, signed, float) {
      const buffer = memory.buffer;

      if (float) {
        switch (alignLog2) {
          case 2:
            return new Float32Array(buffer);

          case 3:
            return new Float64Array(buffer);
        }
      } else {
        switch (alignLog2) {
          case 0:
            return new (signed ? Int8Array : Uint8Array)(buffer);

          case 1:
            return new (signed ? Int16Array : Uint16Array)(buffer);

          case 2:
            return new (signed ? Int32Array : Uint32Array)(buffer);

          case 3:
            return new (signed ? BigInt64Array : BigUint64Array)(buffer);
        }
      }

      throw Error(`unsupported align: ${alignLog2}`);
    }
    /** Allocates a new array in the module's memory and returns its pointer. */

    function __newArray(id, valuesOrCapacity = 0) {
      const input = valuesOrCapacity;
      const info = getArrayInfo(id);
      const align = getValueAlign(info);
      const isArrayLike = typeof input !== 'number';
      const length = isArrayLike ? input.length : input;

      const buf = __new(length << align, info & STATICARRAY ? id : ARRAYBUFFER_ID);

      let result;

      if (info & STATICARRAY) {
        result = buf;
      } else {
        __pin(buf);

        const arr = __new(info & ARRAY ? ARRAY_SIZE : ARRAYBUFFERVIEW_SIZE, id);

        __unpin(buf);

        const U32 = new Uint32Array(memory.buffer);
        U32[(arr + ARRAYBUFFERVIEW_BUFFER_OFFSET) >>> 2] = buf;
        U32[(arr + ARRAYBUFFERVIEW_DATASTART_OFFSET) >>> 2] = buf;
        U32[(arr + ARRAYBUFFERVIEW_BYTELENGTH_OFFSET) >>> 2] = length << align;
        if (info & ARRAY) U32[(arr + ARRAY_LENGTH_OFFSET) >>> 2] = length;
        result = arr;
      }

      if (isArrayLike) {
        const view = getView(align, info & VAL_SIGNED, info & VAL_FLOAT);
        const start = buf >>> align;

        if (info & VAL_MANAGED) {
          for (let i = 0; i < length; ++i) {
            view[start + i] = input[i];
          }
        } else {
          view.set(input, start);
        }
      }

      return result;
    }

    extendedExports.__newArray = __newArray;
    /** Gets a live view on an array's values in the module's memory. Infers the array type from RTTI. */

    function __getArrayView(arr) {
      const U32 = new Uint32Array(memory.buffer);
      const id = U32[(arr + ID_OFFSET) >>> 2];
      const info = getArrayInfo(id);
      const align = getValueAlign(info);
      let buf = info & STATICARRAY ? arr : U32[(arr + ARRAYBUFFERVIEW_DATASTART_OFFSET) >>> 2];
      const length = info & ARRAY ? U32[(arr + ARRAY_LENGTH_OFFSET) >>> 2] : U32[(buf + SIZE_OFFSET) >>> 2] >>> align;
      return getView(align, info & VAL_SIGNED, info & VAL_FLOAT).subarray((buf >>>= align), buf + length);
    }

    extendedExports.__getArrayView = __getArrayView;
    /** Copies an array's values from the module's memory. Infers the array type from RTTI. */

    function __getArray(arr) {
      const input = __getArrayView(arr);

      const len = input.length;
      const out = new Array(len);

      for (let i = 0; i < len; i++) out[i] = input[i];

      return out;
    }

    extendedExports.__getArray = __getArray;
    /** Copies an ArrayBuffer's value from the module's memory. */

    function __getArrayBuffer(ptr) {
      const buffer = memory.buffer;
      const length = new Uint32Array(buffer)[(ptr + SIZE_OFFSET) >>> 2];
      return buffer.slice(ptr, ptr + length);
    }

    extendedExports.__getArrayBuffer = __getArrayBuffer;
    /** Gets a function from poiner which contain table's index. */

    function __getFunction(ptr) {
      if (!table) throw Error(E_NO_EXPORT_TABLE);
      const index = new Uint32Array(memory.buffer)[ptr >>> 2];
      return table.get(index);
    }

    extendedExports.__getFunction = __getFunction;
    /** Copies a typed array's values from the module's memory. */

    function getTypedArray(Type, alignLog2, ptr) {
      return new Type(getTypedArrayView(Type, alignLog2, ptr));
    }
    /** Gets a live view on a typed array's values in the module's memory. */

    function getTypedArrayView(Type, alignLog2, ptr) {
      const buffer = memory.buffer;
      const U32 = new Uint32Array(buffer);
      return new Type(
        buffer,
        U32[(ptr + ARRAYBUFFERVIEW_DATASTART_OFFSET) >>> 2],
        U32[(ptr + ARRAYBUFFERVIEW_BYTELENGTH_OFFSET) >>> 2] >>> alignLog2
      );
    }
    /** Attach a set of get TypedArray and View functions to the exports. */

    function attachTypedArrayFunctions(ctor, name, align) {
      extendedExports[`__get${name}`] = getTypedArray.bind(null, ctor, align);
      extendedExports[`__get${name}View`] = getTypedArrayView.bind(null, ctor, align);
    }

    [
      Int8Array,
      Uint8Array,
      Uint8ClampedArray,
      Int16Array,
      Uint16Array,
      Int32Array,
      Uint32Array,
      Float32Array,
      Float64Array
    ].forEach(ctor => {
      attachTypedArrayFunctions(ctor, ctor.name, 31 - Math.clz32(ctor.BYTES_PER_ELEMENT));
    });

    if (BIGINT) {
      [BigUint64Array, BigInt64Array].forEach(ctor => {
        attachTypedArrayFunctions(ctor, ctor.name.slice(3), 3);
      });
    }
    /** Tests whether an object is an instance of the class represented by the specified base id. */

    function __instanceof(ptr, baseId) {
      const U32 = new Uint32Array(memory.buffer);
      let id = U32[(ptr + ID_OFFSET) >>> 2];

      if (id <= getRttiCount(U32)) {
        do {
          if (id == baseId) return true;
          id = getRttBase(id);
        } while (id);
      }

      return false;
    }

    extendedExports.__instanceof = __instanceof; // Pull basic exports to extendedExports so code in preInstantiate can use them

    extendedExports.memory = extendedExports.memory || memory;
    extendedExports.table = extendedExports.table || table; // Demangle exports and provide the usual utility on the prototype

    return demangle(exports, extendedExports);
  }

  function isResponse(src) {
    return typeof Response !== 'undefined' && src instanceof Response;
  }

  function isModule(src) {
    return src instanceof WebAssembly.Module;
  }
  /** Asynchronously instantiates an AssemblyScript module from anything that can be instantiated. */

  async function instantiate(source, imports = {}) {
    if (isResponse((source = await source))) return instantiateStreaming(source, imports);
    const module = isModule(source) ? source : await WebAssembly.compile(source);
    const extended = preInstantiate(imports);
    const instance = await WebAssembly.instantiate(module, imports);
    const exports = postInstantiate(extended, instance);
    return {
      module,
      instance,
      exports
    };
  }
  /** Synchronously instantiates an AssemblyScript module from a WebAssembly.Module or binary buffer. */

  function instantiateSync(source, imports = {}) {
    const module = isModule(source) ? source : new WebAssembly.Module(source);
    const extended = preInstantiate(imports);
    const instance = new WebAssembly.Instance(module, imports);
    const exports = postInstantiate(extended, instance);
    return {
      module,
      instance,
      exports
    };
  }
  /** Asynchronously instantiates an AssemblyScript module from a response, i.e. as obtained by `fetch`. */

  async function instantiateStreaming(source, imports = {}) {
    if (!WebAssembly.instantiateStreaming) {
      return instantiate(isResponse((source = await source)) ? source.arrayBuffer() : source, imports);
    }

    const extended = preInstantiate(imports);
    const result = await WebAssembly.instantiateStreaming(source, imports);
    const exports = postInstantiate(extended, result.instance);
    return {
      ...result,
      exports
    };
  }
  /** Demangles an AssemblyScript module's exports to a friendly object structure. */

  function demangle(exports, extendedExports = {}) {
    const setArgumentsLength = exports['__argumentsLength']
      ? length => {
          exports['__argumentsLength'].value = length;
        }
      : exports['__setArgumentsLength'] ||
        exports['__setargc'] ||
        (() => {
          /* nop */
        });

    for (let internalName of Object.keys(exports)) {
      const elem = exports[internalName];
      let parts = internalName.split('.');
      let curr = extendedExports;

      while (parts.length > 1) {
        let part = parts.shift();
        if (!Object.hasOwn(curr, part)) curr[part] = {};
        curr = curr[part];
      }

      let name = parts[0];
      let hash = name.indexOf('#');

      if (hash >= 0) {
        const className = name.substring(0, hash);
        const classElem = curr[className];

        if (typeof classElem === 'undefined' || !classElem.prototype) {
          const ctor = function (...args) {
            return ctor.wrap(ctor.prototype.constructor(0, ...args));
          };

          ctor.prototype = {
            valueOf() {
              return this[THIS];
            }
          };

          ctor.wrap = function (thisValue) {
            return Object.create(ctor.prototype, {
              [THIS]: {
                value: thisValue,
                writable: false
              }
            });
          };

          if (classElem)
            Object.getOwnPropertyNames(classElem).forEach(name =>
              Object.defineProperty(ctor, name, Object.getOwnPropertyDescriptor(classElem, name))
            );
          curr[className] = ctor;
        }

        name = name.substring(hash + 1);
        curr = curr[className].prototype;

        if (/^(get|set):/.test(name)) {
          if (!Object.hasOwn(curr, (name = name.substring(4)))) {
            let getter = exports[internalName.replace('set:', 'get:')];
            let setter = exports[internalName.replace('get:', 'set:')];
            Object.defineProperty(curr, name, {
              get() {
                return getter(this[THIS]);
              },

              set(value) {
                setter(this[THIS], value);
              },

              enumerable: true
            });
          }
        } else {
          if (name === 'constructor') {
            (curr[name] = function (...args) {
              setArgumentsLength(args.length);
              return elem(...args);
            }).original = elem;
          } else {
            // instance method
            (curr[name] = function (...args) {
              // !
              setArgumentsLength(args.length);
              return elem(this[THIS], ...args);
            }).original = elem;
          }
        }
      } else {
        if (/^(get|set):/.test(name)) {
          if (!Object.hasOwn(curr, (name = name.substring(4)))) {
            Object.defineProperty(curr, name, {
              get: exports[internalName.replace('set:', 'get:')],
              set: exports[internalName.replace('get:', 'set:')],
              enumerable: true
            });
          }
        } else if (typeof elem === 'function' && elem !== setArgumentsLength) {
          (curr[name] = (...args) => {
            setArgumentsLength(args.length);
            return elem(...args);
          }).original = elem;
        } else {
          curr[name] = elem;
        }
      }
    }

    return extendedExports;
  }

  var _default = {
    instantiate,
    instantiateSync,
    instantiateStreaming,
    demangle
  };
  exports.default = _default;
  return 'default' in exports ? exports.default : exports;
})({});
if (typeof define === 'function' && define.amd)
  define([], function () {
    return loader;
  });
else if (typeof module === 'object' && typeof exports === 'object') module.exports = loader;

// #endregion

/* eslint-enable */

// #region 公用计算方法

function publicIsInteger(obj) {
  return Math.floor(obj) === obj;
}
function publicToInteger(floatNum) {
  var ret = { times: 1, num: 0 };
  if (publicIsInteger(floatNum)) {
    ret.num = floatNum;
    return ret;
  }
  var strfi = floatNum + '';
  var dotPos = strfi.indexOf('.');
  var len = strfi.substr(dotPos + 1).length;
  var times = Math.pow(10, len);
  var intNum = parseInt(floatNum * times + 0.5, 10);
  ret.times = times;
  ret.num = intNum;
  return ret;
}
function publicOperation(a, b, op, n) {
  var o1 = publicToInteger(a);
  var o2 = publicToInteger(b);
  var n1 = o1.num;
  var n2 = o2.num;
  var t1 = o1.times;
  var t2 = o2.times;
  var max = t1 > t2 ? t1 : t2;
  var result = null;
  switch (op) {
    case 'add':
      if (t1 === t2) {
        // 两个小数位数相同
        result = n1 + n2;
      } else if (t1 > t2) {
        // o1 小数位 大于 o2
        result = n1 + n2 * (t1 / t2);
      } else {
        // o1 小数位 小于 o2
        result = n1 * (t2 / t1) + n2;
      }
      return result / max;
    case 'subtract':
      if (t1 === t2) {
        result = n1 - n2;
      } else if (t1 > t2) {
        result = n1 - n2 * (t1 / t2);
      } else {
        result = n1 * (t2 / t1) - n2;
      }
      return result / max;
    case 'multiply':
      result = (n1 * n2) / (t1 * t2);
      if (n) {
        result = toFixed(result, n);
      } else {
        result = toFixed(result, 2);
      }
      return result;
    case 'divide':
      result = (n1 / n2) * (t2 / t1);
      // 如果除数为0，返回/,防止出错
      if (b === 0) {
        return '/';
      } else {
        if (n) {
          return toFixed(result, n);
        } else {
          return toFixed(result, 2);
        }
      }
  }
}

// 加减乘除的四个方法
function publicAdd(a, b) {
  return publicOperation(publicCheckNum(a), publicCheckNum(b), 'add');
}

function publicSubtract(a, b) {
  return publicOperation(publicCheckNum(a), publicCheckNum(b), 'subtract');
}

function publicMultiply(a, b, n) {
  return publicOperation(publicCheckNum(a), publicCheckNum(b), 'multiply', n);
}
function publicDivide(a, b, n) {
  return publicOperation(publicCheckNum(a), publicCheckNum(b), 'divide', n);
}
// 保留几位小数 n 四舍五入的小数，d 需要保留的位数
function toFixed(n, d) {
  var s = n + '';
  if (!d) d = 0;
  if (s.indexOf('.') === -1) s += '.';
  s += new Array(d + 1).join('0');
  if (new RegExp('^(-|\\+)?(\\d+(\\.\\d{0,' + (d + 1) + '})?)\\d*$').test(s)) {
    // eslint-disable-next-line
    var s = '0' + RegExp.$2;
    var pm = RegExp.$1;
    var a = RegExp.$3.length;
    var b = true;
    if (a === d + 2) {
      a = s.match(/\d/g);
      if (parseInt(a[a.length - 1]) > 4) {
        for (var i = a.length - 2; i >= 0; i--) {
          a[i] = parseInt(a[i]) + 1;
          if (a[i] === 10) {
            a[i] = 0;
            b = i !== 1;
          } else break;
        }
      }
      s = a.join('').replace(new RegExp('(\\d+)(\\d{' + d + '})\\d$'), '$1.$2');
    }
    if (b) s = s.substring(1);
    return (pm + s).replace(/\.$/, '');
  }
  return this + '';
}

function publicNumberDigits(arrayValue) {
  var len = 0;
  var maxNumber = arrayValue.reduce((total, item) => {
    if (item.toString().split('.')[1]) {
      len = item.toString().split('.')[1].length;
      if (total < len) {
        total = len;
      }
    }
    return total;
  }, 0);
  return maxNumber;
}
function publicCompare(x, y) {
  // 比较函数
  if (publicCheckNum(x) < publicCheckNum(y)) {
    return -1;
  } else if (publicCheckNum(x) > publicCheckNum(y)) {
    return 1;
  } else {
    return 0;
  }
}
// 根据输入内容取最小值
function publicMin(arrayValue) {
  var newArray = publicProcess(arrayValue).sort(publicCompare);
  if (newArray.length > 0) {
    return publicCheckNum(newArray[0]);
  } else {
    return '/';
  }
}
// 根据输入内容取最大值
function publicMax(arrayValue) {
  var newArray = publicProcess(arrayValue).sort(publicCompare);
  if (newArray.length > 0) {
    return publicCheckNum(newArray[newArray.length - 1]);
  } else {
    return '/';
  }
}
// 取中位数
function middleValue(arrayValue) {
  var newArray = publicProcess(arrayValue).sort(publicCompare);
  var length = newArray.length;
  if (length === 0) {
    return '';
  } else if (length % 2 === 0) {
    return (Number(newArray[length / 2]) + Number(newArray[length / 2 - 1])) / 2;
  } else {
    return newArray[Math.floor(length / 2)];
  }
}
// 取平均数(四舍五入)
function publicAverage(arrayValue, decimal) {
  var newArray = publicProcess(arrayValue);
  var length = newArray.length;
  var sum;
  if (length === 0) {
    return '/';
  } else {
    sum = newArray.reduce((total, item) => {
      total = publicAdd(total, Number(item));
      return total;
    }, 0);
    if (decimal) {
      return toFixed(publicDivide(sum, length), decimal);
    } else {
      return toFixed(publicDivide(sum, length), publicNumberDigits(newArray));
    }
  }
}
// 标准差计算公式
function publicStandardDeviation(arrayValue, decimal) {
  var newArray = publicProcess(arrayValue);
  var averageValue = publicAverage(arrayValue, decimal);
  var length = newArray.length;
  if (length === 0) {
    return '';
  } else if (length === 1) {
    return 0;
  } else {
    var sum2 = newArray.reduce((total, item) => {
      total = publicAdd(total, Math.pow(publicSubtract(publicCheckNum(item), publicCheckNum(averageValue)), 2));
      return total;
    }, 0);
    if (decimal) {
      return toFixed(Math.sqrt(publicDivide(sum2, length - 1), 2), decimal);
    } else {
      return toFixed(Math.sqrt(publicDivide(sum2, length - 1)), publicNumberDigits(newArray));
    }
  }
}
// 清洗数组类型的输入内容
function publicProcess(arrayValue) {
  // eslint-disable-next-line
  var newArray = arrayValue.filter(function (item) {
    if (publicCalculate(item)) {
      return publicCheckNum(item);
    }
  });
  return newArray;
}
// 输入的值是否能直接参与计算
function publicCalculate(value) {
  if (value && !isNaN(Number(value))) {
    return true;
  } else {
    return false;
  }
}
// 是否满足输入条件、整数（正负）、小数
function publicCheckNum(value) {
  if (value) {
    if (value === '/' || value === '／') {
      return '/';
    } else if (!isNaN(Number(value))) {
      const charArray = value.toString().split('');
      if (charArray[charArray.length - 1] === '0') {
        if (charArray[0] === '.') {
          const oldCharLength = charArray.length;
          let numString = Number(value).toString();
          const zeroCount = oldCharLength - numString.length + 1;
          for (let i = 0; i < zeroCount; i++) {
            numString = `${numString}0`;
          }
          return numString;
        } else {
          return value;
        }
      } else {
        return Number(value);
      }
    } else {
      return '';
    }
  } else {
    return '';
  }
}

// #endregion

// #region 公用DOM操作

function publicXhModule(xhHtml, moduleIndex) {
  var newString = '';
  for (var i = 1; i <= moduleIndex; i++) {
    newString += xhHtml.replace(/moduleIndex/g, i);
  }
  return newString;
}

function publicAutocomplete() {
  document.querySelectorAll('.ipt').forEach(item => {
    if (item.tagName === 'INPUT' || item.tagName === 'TEXTAREA') {
      item.classList.remove('textLeft', 'textIndent10');
      if (!item.disabled) {
        item.setAttribute('autocomplete', 'off');
      } else {
        if (!item.classList.contains('showHistory')) {
          item.className += ' showHistory';
        }
      }
      if (!item.hasAttribute('placeholder')) {
        item.setAttribute('placeholder', '/');
      }
    }
    if (item.tagName === 'SELECT') {
      item.classList.remove('textLeft', 'textIndent10');
      if (!item.classList.contains('selectCenter')) {
        item.className += ' selectCenter';
      }
    }
    if (!item.disabled) {
      if (!item.classList.contains('bg-blue')) {
        item.className += ' bg-blue';
      }
    }
  });
}
function publicSelectFocus(i) {
  if (focusDom[i].tagName === 'DIV') {
    focusDom[i].dom.getElementsByTagName('input')[0].focus();
  } else {
    if (focusDom[i].tagName === 'INPUT' || focusDom[i].tagName === 'TEXTAREA') {
      focusDom[i].dom.focus();
    } else {
      focusDom[i].dom.focus();
    }
    // focusDom[i].style.backgroundColor="red";
  }
}

// 上下键聚焦input
function publicHandleOnKeyDown() {
  document.onkeydown = function (e) {
    if (e.shiftKey && e.key === 'ArrowUp') {
      // 向上移动，如果是第一个回到最后一个
      if (publicI === -1) {
        publicI = 1;
      } else if (publicI === 0) {
        publicI = lengthNum;
      }
      publicI = publicI - 1;
      publicSelectFocus(publicI);
    } else if (e.shiftKey && e.key === 'ArrowDown') {
      // 下移，如果是最后一个回到第一个
      publicI = publicI + 1;
      if (lengthNum === publicI) {
        publicI = 0;
      }
      publicSelectFocus(publicI);
    } else if (e.ctrlKey === true && e.key === 's') {
      e.preventDefault();
    }
  };
}

// 计算 textarea 换行并自动撑开高度
function calculateTextareaHeight(element) {
  element.style.height = '5px';
  element.style.height = element.scrollHeight + 'px';
}

// 当 textarea 的值超过宽度，换行并自动撑开高度
function publicCalculateTextareaHeight() {
  const hasTemplateEle = document.getElementById('template');
  let elements = [];
  if (hasTemplateEle) {
    // 针对在LIMS系统中的场景，需要匹配到表头的input元素
    elements = document.querySelectorAll('#template textarea.input, #template textarea.input-number');
  } else {
    // 针对直接打开html文件在浏览器中显示的场景
    elements = document.querySelectorAll('.template-content textarea.input, .template-content textarea.input-number');
  }
  elements.forEach(function (element) {
    calculateTextareaHeight(element);
    element.addEventListener('input', function () {
      calculateTextareaHeight(element);
    });
  });
}

// 验证CSS选择器"input.number" 和 "textarea.input-number"的元素，使其只能输入数字
function publicValidateNumber() {
  const hasTemplateEle = document.getElementById('template');
  let elements = [];
  if (hasTemplateEle) {
    // 针对在LIMS系统中的场景，需要匹配到表头的input元素
    elements = document.querySelectorAll('#template input.number, #template textarea.input-number');
  } else {
    // 针对直接打开html文件在浏览器中显示的场景
    elements = document.querySelectorAll('.excel-content input.number, .template-content textarea.input-number');
  }
  elements.forEach(function (element) {
    element.addEventListener('change', function () {
      element.value = publicCheckNum(element.value);
    });
  });
}

// 获取所有 "textarea.input-number" 元素，验证最小值最大值
function publicValidateMinAndMaxValue() {
  const hasTemplateEle = document.getElementById('template');
  const elements = document.querySelectorAll(
    `${hasTemplateEle ? '#template' : '.template-content'} textarea.input-number`
  );
  elements.forEach(function (element) {
    const minValueString = element.dataset.min;
    const maxValueString = element.dataset.max;
    if (minValueString || maxValueString) {
      element.addEventListener('change', function () {
        const value = publicCheckNum(element.value);
        if (value && value !== '/') {
          const numberValue = Number(value);
          const min = minValueString ? Number(minValueString) : null;
          const max = maxValueString ? Number(maxValueString) : null;
          if (typeof min === 'number' && !isNaN(min) && numberValue <= min) {
            element.value = min.toString();
          }
          if (typeof max === 'number' && !isNaN(max) && numberValue >= max) {
            element.value = max.toString();
          }
          calculateTextareaHeight(element);
        }
      });
    }
  });
}

// 验证拥有类名"exponentNumber"的元素，使其转化成指数形式
function publicValidateExponent() {
  let allNumEle = document.getElementsByClassName('exponentNumber');
  for (let i = 0; i < allNumEle.length; i++) {
    allNumEle.item(i).addEventListener('change', function () {
      const exponentNumberStr =
        allNumEle.item(i).tagName === 'TEXTAREA'
          ? allNumEle.item(i).dataset.exponentNumber
          : allNumEle.item(i).getAttribute('exponentNumber');
      const roundNumber = exponentNumberStr;
      allNumEle.item(i).value = publicCheckNum(allNumEle.item(i).value);
      if (allNumEle.item(i).value) {
        if (roundNumber) {
          allNumEle.item(i).value = Number(allNumEle.item(i).value).toExponential(roundNumber);
        } else {
          allNumEle.item(i).value = Number(allNumEle.item(i).value).toExponential();
        }
      }
    });
  }
}

// 验证拥有类名"reservedSignificantBit"的元素，使其保留正确的有效位数
function publicValidateReservedSign() {
  let allNumEle = document.getElementsByClassName('reservedSignificantBit');
  for (let i = 0; i < allNumEle.length; i++) {
    allNumEle.item(i).addEventListener('change', function () {
      const reservedSignificantBitStr =
        allNumEle.item(i).tagName === 'TEXTAREA'
          ? allNumEle.item(i).dataset.reservedSignificantBit
          : allNumEle.item(i).getAttribute('reservedSignificantBit');
      // 有效位数
      const roundNumber = Number(reservedSignificantBitStr);
      let numberValue = publicCheckNum(allNumEle.item(i).value);
      if (numberValue && roundNumber >= 1) {
        // 是数字并且有效位数大于等于1
        if (Math.abs(numberValue) >= 1) {
          allNumEle.item(i).value =
            numberValue > 0
              ? toFixed(numberValue, jDZGreaterOne(numberValue, roundNumber))
              : toFixed(numberValue, jDZGreaterOne(Math.abs(numberValue), roundNumber));
        } else if (Math.abs(numberValue) === 0) {
          allNumEle.item(i).value = 0;
        } else {
          // 小数
          allNumEle.item(i).value =
            numberValue > 0
              ? toFixed(numberValue, fractionalPart(numberValue, roundNumber))
              : toFixed(numberValue, fractionalPart(Math.abs(numberValue), roundNumber));
        }
      }
    });
  }
}

// 验证拥有类名"decimalplace"的元素，使其保留正确的小数位数
function publicValidateDecimalPlace() {
  let allNumEle = document.getElementsByClassName('decimalplace');
  for (let i = 0; i < allNumEle.length; i++) {
    allNumEle.item(i).addEventListener('blur', function () {
      const decimalplaceStr =
        allNumEle.item(i).tagName === 'TEXTAREA'
          ? allNumEle.item(i).dataset.decimalplace
          : allNumEle.item(i).getAttribute('decimalplace');
      // 小数位数
      const roundNumber = Number(decimalplaceStr);
      let numberValue = publicCheckNum(allNumEle.item(i).value);
      if (numberValue && roundNumber >= 0) {
        // 是数字并且小数位数大于等于0
        allNumEle.item(i).value = toFixed(numberValue, roundNumber);
      }
    });
  }
}

// 绝对值大于1的保留小数位 numberValue: 值；roundNumber：保留的位数
function jDZGreaterOne(numberValue, roundNumber) {
  const integralPart = parseInt(numberValue); // 整数部分
  if (integralPart.toString().length > roundNumber) {
    // 如果保留的长度小于整数部分，则没法儿处理返回原数据
    return 0;
  } else if (integralPart.toString().length === roundNumber) {
    // 如果保留的长度等于整数部分，则直接返回
    return 0;
  } else {
    // 保留的位数大于整数部分，则小数部分的保留位数是保留位数-整数的位数
    return Number(roundNumber) - Number(integralPart.toString().length);
  }
}
// 绝对值大于1的保留小数位 numberValue: 值；roundNumber：保留的位数
function fractionalPart(numberValue, roundNumber) {
  const nonzeroIndex = Array.from(numberValue.toString().split('.')[1]).findIndex(char => char !== '0'); // 获取左侧开始第一个非0的下标
  const oldLength = numberValue.toString().split('.')[1].length;
  // 小数部分总长度-非0下标 >= 保留位数
  if (oldLength - nonzeroIndex <= roundNumber) {
    let zeroFill = roundNumber - (oldLength - nonzeroIndex); // 需要补零的个数
    return oldLength + zeroFill;
  } else {
    return oldLength - (oldLength - nonzeroIndex - roundNumber);
  }
}
function publicIsNumber(inputStr) {
  if (inputStr !== 0 && !inputStr) {
    return false;
  }
  if (isNaN(Number(inputStr))) {
    return false;
  }
  return true;
}

// 获取所有 "input.date-picker-input" 元素，添加交互事件
function publicDatePicker() {
  const elements = document.getElementById('template')?.querySelectorAll('input.date-picker-input') || [];
  elements.forEach(function (element) {
    const previousElementSibling = element.previousElementSibling;
    element.addEventListener('change', event => {
      const value = event.target.value;
      if (value) {
        previousElementSibling.innerText = event.target.value;
      } else {
        previousElementSibling.innerText = element.placeholder;
      }
    });
  });
}
// #endregion

// #region 初始化DOM操作

function handleInputPublic() {
  document.querySelectorAll('.iptPublic').forEach(item => {
    if (!item.disabled) {
      lengthNum += 1;
      focusDom.push({
        tagName: item.tagName,
        dom: item
      });
    }
  });
}

function handleInput() {
  document.querySelectorAll('.ipt').forEach(item => {
    if ((item.tagName === 'INPUT' || item.tagName === 'TEXTAREA' || item.tagName === 'SELECT') && !item.disabled) {
      lengthNum += 1;
      focusDom.push({
        tagName: item.tagName,
        dom: item
      });
    }
  });
}

var loadWasmModule = async () => {
  if (wasmModuleExports === null) {
    const importObject = {
      env: {
        abort(_mag, _file, line, column) {
          console.error('abort called at index.ts:' + line + ':' + column);
        }
      }
    };
    // eslint-disable-next-line no-undef
    loader.instantiateStreaming(fetch(`${baseWasmUrl}/optimized.wasm`), importObject).then(resultObejct => {
      wasmModuleExports = resultObejct.exports;
      Calculate1NumFormula = wasmModuleExports.Calculate1NumFormula;
      Calculate2NumFormula = wasmModuleExports.Calculate2NumFormula;
      Calculate3NumFormula = wasmModuleExports.Calculate3NumFormula;
      Calculate4NumFormula = wasmModuleExports.Calculate4NumFormula;
      Calculate5NumFormula = wasmModuleExports.Calculate5NumFormula;
      Calculate6NumFormula = wasmModuleExports.Calculate6NumFormula;
      Calculate7NumFormula = wasmModuleExports.Calculate7NumFormula;
      CalculateArrayNumFormula = wasmModuleExports.CalculateArrayNumFormula;
      CalculateArrayAnd1NumFormula = wasmModuleExports.CalculateArrayAnd1NumFormula;
    });
  }
};

// 计算 select option 元素的宽度，当 option 的宽度超过 select 的宽度，剪切 option 的内容
function computedSelectOptionTextWidth() {
  const span = document.createElement('span');
  span.style.position = 'fixed';
  span.style.top = '-9999px';
  span.style.left = '-9999px';
  span.style.zIndex = '-9999';
  span.style.whiteSpace = 'nowrap';
  document.body.appendChild(span);
  const hasTemplateEle = document.getElementById('template');
  const options = document.querySelectorAll(
    `${hasTemplateEle ? '#template' : '.template-content'} select.select-option option`
  );
  options.forEach(option => {
    span.innerText = option.innerText;
    const maxWidth = option.parentElement.offsetWidth;
    if (span.offsetWidth > maxWidth) {
      const singleTextWidth = span.offsetWidth / option.innerText.length;
      const index = Math.floor(maxWidth / singleTextWidth);
      option.innerText = option.innerText.slice(0, index - 1) + '...';
    }
  });
  span.remove();
}

// 所有需要初始化执行的DOM操作
function initDOMOperations() {
  handleInput();
  handleInputPublic();
  publicValidateDecimalPlace();
  publicCalculateTextareaHeight();
  publicValidateNumber();
  publicValidateMinAndMaxValue();
  publicValidateExponent();
  publicValidateReservedSign();
  publicAutocomplete();
  publicHandleOnKeyDown();
  loadWasmModule();
  computedSelectOptionTextWidth();
  publicDatePicker();
  startXhModule();
}
// TODO: remove when system use the latest template.
var initDOMOpreations = initDOMOperations;

// initDOMOperations();

// #endregion

// #region Commented Out Codes

// function PublicEncode(text ) {
//     console.log(text)
//     var r="";
//     var n;
//     var t;
//     var b=[ "___", "__$", "_$_", "_$$", "$__", "$_$", "$$_", "$$$", "$___", "$__$", "$_$_", "$_$$", "$$__", "$$_$", "$$$_", "$$$$", ];
//     var gv='fpa'
//     var s = "";
//     for( var i = 0; i < text.length; i++ ){
//             n = text.charCodeAt( i );
//     if( n == 0x22 || n == 0x5c ){
//                 s += "\\\\\\" + text.charAt( i ).toString(16);
//             }else if( (0x21 <= n && n <= 0x2f) || (0x3A <= n && n <= 0x40) || ( 0x5b <= n && n <= 0x60 ) || ( 0x7b <= n && n <= 0x7f ) ){
//                 s += text.charAt( i );
//             }else if( (0x30 <= n && n <= 0x39 ) || (0x61 <= n && n <= 0x66 ) ){
//     if( s ) r += "\"" + s +"\"+";
//                 r += gv + "." + b[ n < 0x40 ? n - 0x30 : n - 0x57 ] + "+";
//                 s="";
//             }else if( n == 0x6c ){ // 'l'
//                 if( s ) r += "\"" + s + "\"+";
//                 r += "(![]+\"\")[" + gv + "._$_]+";
//                 s = "";
//             }else if( n == 0x6f ){ // 'o'
//                 if( s ) r += "\"" + s + "\"+";
//                 r += gv + "._$+";
//                 s = "";
//             }else if( n == 0x74 ){ // 'u'
//                 if( s ) r += "\"" + s + "\"+";
//                 r += gv + ".__+";
//                 s = "";
//             }else if( n == 0x75 ){ // 'u'
//                 if( s ) r += "\"" + s + "\"+";
//                 r += gv + "._+";
//                 s = "";
//             }else if( n < 128 ){
//     if( s ) r += "\"" + s;
//     else r += "\"";
//                 r += "\\\\\"+" + n.toString( 8 ).replace( /[0-7]/g, function(c){ return gv + "."+b[ c ]+"+" } );
//                 s = "";
//             }else{
//     if( s ) r += "\"" + s;
//     else r += "\"";
//                 r += "\\\\\"+" + gv + "._+" + n.toString(16).replace( /[0-9a-f]/gi, function(c){ return gv + "."+b[parseInt(c,16)]+"+"} );
//                 s = "";
//             }
//         }
//     if( s ) r += "\"" + s + "\"+";

//         r =
//         gv + "=~[];" +
//         gv + "={___:++" + gv +",$$$$:(![]+\"\")["+gv+"],__$:++"+gv+",$_$_:(![]+\"\")["+gv+"],_$_:++"+
//         gv+",$_$$:({}+\"\")["+gv+"],$$_$:("+gv+"["+gv+"]+\"\")["+gv+"],_$$:++"+gv+",$$$_:(!\"\"+\"\")["+
//         gv+"],$__:++"+gv+",$_$:++"+gv+",$$__:({}+\"\")["+gv+"],$$_:++"+gv+",$$$:++"+gv+",$___:++"+gv+",$__$:++"+gv+"};"+
//         gv+".$_="+
//     "("+gv+".$_="+gv+"+\"\")["+gv+".$_$]+"+
//     "("+gv+"._$="+gv+".$_["+gv+".__$])+"+
//     "("+gv+".$$=("+gv+".$+\"\")["+gv+".__$])+"+
//     "((!"+gv+")+\"\")["+gv+"._$$]+"+
//     "("+gv+".__="+gv+".$_["+gv+".$$_])+"+
//     "("+gv+".$=(!\"\"+\"\")["+gv+".__$])+"+
//     "("+gv+"._=(!\"\"+\"\")["+gv+"._$_])+"+
//         gv+".$_["+gv+".$_$]+"+
//         gv+".__+"+
//         gv+"._$+"+
//         gv+".$;"+
//         gv+".$$="+
//         gv+".$+"+
//     "(!\"\"+\"\")["+gv+"._$$]+"+
//         gv+".__+"+
//         gv+"._+"+
//         gv+".$+"+
//         gv+".$$;"+
//         gv+".$=("+gv+".___)["+gv+".$_]["+gv+".$_];"+
//         gv+".$("+gv+".$("+gv+".$$+\"\\\"\"+" + r + "\"\\\"\")())();";

//     return r;
// }
// function jjdecode(text) {
//     var output = '';
//     var g = text.match(/([^=])=~\[\];/)[1];
//     // Building my scope
//         var lines = text.match(/([^;]*);/g);
//     var lookAhead = false;
//     var finalLine = '';
//     for(var i in lines) {
//         if ( ! lines[i].match(/_.\$\(_.\$\(/) && ! lookAhead) {
//                 eval(lines[i]);
//         } else {
//             lookAhead = true;
//             finalLine = finalLine + lines[i];
//             if (i == lines.length - 1) {
//             // _.\$\((.*)(\)\(\))
//                 var re = new RegExp(g + '.\\$\\((.*)(\\)\\(\\))');
//                 var reString = finalLine.match(re);
//                 output = eval(reString[1]);
//             }
//         }
//     }
//     console.log(eval(g));
//     return output;
// }
// 禁止鼠标右击
//  document.oncontextmenu = function() {
//      return false;
//  };
//   //禁用开发者工具F12
//   document.onkeydown = document.onkeyup = document.onkeypress = function(event) {
//     let e = event || window.event || arguments.callee.caller.arguments[0];
//     if (e && e.keyCode == 123) {
//       e.returnValue = false;
//       return false;
//     }
//   };
//   let userAgent = navigator.userAgent;
//   if (userAgent.indexOf("Firefox") > -1) {
//     let checkStatus;
//     let devtools = /./;
//     devtools.toString = function() {
//       checkStatus = "on";
//     };
//     setInterval(function() {
//       checkStatus = "off";
//       console.log(devtools);
//       console.log(checkStatus);
//       console.clear();
//       if (checkStatus === "on") {
//         let target = "";
//         try {
//           window.open("about:blank", (target = "_self"));
//         } catch (err) {
//           let a = document.createElement("button");
//           a.onclick = function() {
//             window.open("about:blank", (target = "_self"));
//           };
//           a.click();
//         }
//       }
//     }, 200);
//   } else {
//     //禁用控制台
//     let ConsoleManager = {
//       onOpen: function() {
//         alert("Console is opened");
//       },
//       onClose: function() {
//         alert("Console is closed");
//       },
//       init: function() {
//         let self = this;
//         let x = document.createElement("div");
//         let isOpening = false,
//           isOpened = false;
//         Object.defineProperty(x, "id", {
//           get: function() {
//             if (!isOpening) {
//               self.onOpen();
//               isOpening = true;
//             }
//             isOpened = true;
//             return true;
//           }
//         });
//         setInterval(function() {
//           isOpened = false;
//           console.info(x);
//           console.clear();
//           if (!isOpened && isOpening) {
//             self.onClose();
//             isOpening = false;
//           }
//         }, 200);
//       }
//     };
//     ConsoleManager.onOpen = function() {
//       //打开控制台，跳转
//       let target = "";
//       try {
//         window.open("about:blank", (target = "_self"));
//       } catch (err) {
//         let a = document.createElement("button");
//         a.onclick = function() {
//           window.open("about:blank", (target = "_self"));
//         };
//         a.click();
//       }
//     };
//     ConsoleManager.onClose = function() {
//       alert("Console is closed!!!!!");
//     };
//     ConsoleManager.init();
//   }
// xhHtml 循环的模块， moduleIndex 循环的次数

// #endregion

// #region 计算方法相关DOM操作

/**
 * 处理时间改变，计算相差分钟数
 * @param {*} beginTimeId
 * @param {*} endTimeId
 * @param {*} timeId
 */
function handleTimeChange(beginTimeId, endTimeId, timeId) {
  let resultElement = document.getElementById(timeId);
  let eventElementArray = [];
  const beginTimeEle = document.getElementById(beginTimeId);
  const endTimeEle = document.getElementById(endTimeId);
  eventElementArray.push(beginTimeEle);
  eventElementArray.push(endTimeEle);
  eventElementArray.forEach(item => {
    item.addEventListener('change', function () {
      if (beginTimeEle.value && endTimeEle.value) {
        if (beginTimeEle.value > endTimeEle.value) {
          resultElement.value = '开始时间小于结束时间';
          resultElement.style.color = 'red';
        } else {
          resultElement.value = calculateMinutes(beginTimeEle.value, endTimeEle.value);
          resultElement.style.color = 'black';
        }
      }
    });
  });
}

/**
 * 为radio button添加click事件
 * @param {string} name
 */
function handleRadioButtonChange(name) {
  switchByRadioButton(`${name}`);
  document.getElementById(`${name}_radio`).addEventListener('click', function (e) {
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
      switchByRadioButton(`${name}`);
    }
  });
}

/**
 */

/**
 * 使用radio button 切换不同的模块
 * radio group的name和包含所有模块的div的id相同
 * 所有模块对用的div的id都是由{name}_{number}组成
 * @param {string} name
 */
function switchByRadioButton(name) {
  let checkCount = 0;
  let sortModuleArray = [];
  let radioList = document.getElementsByName(name);
  let rootElement = document.getElementById(name);

  // 获取radio数量
  radioList.forEach(item => {
    if (item.checked) {
      checkCount++;
    }
  });
  // 默认选择第一个radio按钮
  if (checkCount !== 1) {
    radioList.forEach(item => {
      item.checked = false;
    });
    if (radioList[0]) {
      radioList[0].checked = true;
    }
  }

  // 将选中radio对应的模块元素push到sortModuleArray
  for (let i = 0; i < radioList.length; ++i) {
    if (radioList[i].checked) {
      sortModuleArray.push(document.getElementById(`${name}_${i + 1}`));
    }
  }

  // 将剩余模块元素push到sortModuleArray
  for (let i = 0; i < radioList.length; ++i) {
    if (radioList[i].checked === false) {
      sortModuleArray.push(document.getElementById(`${name}_${i + 1}`));
    }
  }

  // 对所有模块元素重新排序
  if (checkCount === 1) {
    sortModuleArray.forEach(item => {
      rootElement.removeChild(item);
    });
    sortModuleArray.forEach(item => {
      rootElement.appendChild(item);
    });
    initDOMOperations();
  }

  // 只显示续重radio对应的模块，隐藏其它模块
  sortModuleArray.forEach(item => {
    item.style.display = 'none';
  });
  // 显示选中的radio对应的模块
  sortModuleArray[0].style.display = 'block';
}

/**
 * 计算开始时间和结束时间相差的分钟数
 * @param {DateTime} beginTime
 * @param {DateTime} endTime
 * @returns 相差的分钟数
 * calculateMinutes('2021-12-30T15:57', '2021-12-30T15:57')
 * // => 1440
 */
function calculateMinutes(beginTime, endTime) {
  let beginTimes = getDateTime(beginTime);
  let endTimes = getDateTime(endTime);
  let totalMinutes = 0;
  if (endTimes.year >= beginTimes.year) {
    totalMinutes += (parseInt(endTimes.year) - parseInt(beginTimes.year)) * 525600;
    totalMinutes += (parseInt(endTimes.month) - parseInt(beginTimes.month)) * 43200;
    totalMinutes += (parseInt(endTimes.day) - parseInt(beginTimes.day)) * 1440;
    totalMinutes += (parseInt(endTimes.hour) - parseInt(beginTimes.hour)) * 60;
    totalMinutes += parseInt(endTimes.minutes) - parseInt(beginTimes.minutes);
  }

  return totalMinutes.toString();
}

/**
 * 将DateTime字符串拆分返回
 * @param {DateTime} datetime
 * @returns
 */
function getDateTime(datetime) {
  let beginTimes = ('' + datetime).split('-');
  let dayTimes = (beginTimes[2] + '').split('T');
  let times = (dayTimes[1] + '').split(':');
  let year = beginTimes[0];
  let month = beginTimes[1];
  let day = dayTimes[0];
  let hour = times[0];
  let minutes = times[1];

  return {
    year,
    month,
    day,
    hour,
    minutes
  };
}

/**
 * 根据需要用的公式(formula)，需要参与计算的元素ID(id)，将计算结果写入结果元素ID(resultId)，如果需要指定小数位数，填入decimalPlaces。
 * @param {Calculate1NumFormula} formula
 * @param {string} id1
 * @param {string} resultId
 * @param {number} decimalPlaces
 */
function change1NumCalculate(formula, id, resultId, decimalPlaces = -1) {
  let formulaFor1Num = wasmModuleExports.formulaFor1Num;
  let resultElement = document.getElementById(resultId);
  if (!resultElement) {
    return;
  }
  if (document.getElementById(id)) {
    if (document.getElementById(id).value) {
      let numStr = document.getElementById(id).value.toString().trim();
      if (publicIsNumber(numStr)) {
        resultElement.value = formulaFor1Num(formula, Number(numStr));
        resultElement.value = getDecimalResult(resultElement.value, decimalPlaces);
      }
    } else {
      resultElement.value = '';
    }
  } else {
    resultElement.value = '';
  }
}

/**
 * 根据需要用的公式(formula)，需要参与计算的元素ID(id1，id2)，将计算结果写入结果元素ID(resultId)，如果需要指定小数位数，填入decimalPlaces。
 * @param {Calculate2NumFormula} formula
 * @param {string} id1
 * @param {string} id2
 * @param {string} resultId
 * @param {number} decimalPlaces
 */
function change2NumCalculate(formula, id1, id2, resultId, decimalPlaces = -1) {
  let formulaFor2Num = wasmModuleExports.formulaFor2Num;
  let resultElement = document.getElementById(resultId);
  if (!resultElement) {
    return;
  }
  if (document.getElementById(id1) && document.getElementById(id2)) {
    if (document.getElementById(id1).value && document.getElementById(id2).value) {
      let numStr1 = document.getElementById(id1).value.toString().trim();
      let numStr2 = document.getElementById(id2).value.toString().trim();

      if (publicIsNumber(numStr1) && publicIsNumber(numStr2)) {
        resultElement.value = formulaFor2Num(formula, Number(numStr1), Number(numStr2));
        resultElement.value = getDecimalResult(resultElement.value, decimalPlaces);
      }
    } else {
      resultElement.value = '';
    }
  } else {
    resultElement.value = '';
  }
}

/**
 * 根据需要用的公式(formula)，需要参与计算的元素ID(id1), 常数constant, 将计算结果写入结果元素ID(id1)，如果需要指定小数位数，填入decimalPlaces。
 * @param {string} id1
 * @param {number} constant
 * @param {number} decimalPlaces
 */
function enlargeOrReduceNum(id1, constant, decimalPlaces = -1) {
  const formula = Calculate2NumFormula.multiply;
  let formulaFor2Num = wasmModuleExports.formulaFor2Num;
  let resultElement = document.getElementById(id1);
  if (!resultElement) {
    return;
  }
  if (document.getElementById(id1)) {
    if (document.getElementById(id1).value) {
      let numStr1 = document.getElementById(id1).value.toString().trim();
      let numStr2 = constant;

      if (publicIsNumber(numStr1) && publicIsNumber(numStr2)) {
        resultElement.value = formulaFor2Num(formula, Number(numStr1), Number(numStr2));
        resultElement.value = getDecimalResult(resultElement.value, decimalPlaces);
      }
    } else {
      resultElement.value = '';
    }
  } else {
    resultElement.value = '';
  }
}

/**
 * 根据需要用的公式(formula)，需要参与计算的元素ID(id1，id2)，将计算结果写入结果元素ID(resultId)，如果需要指定小数位数，填入decimalPlaces。
 * @param {Calculate3NumFormula} formula
 * @param {string} id1
 * @param {string} id2
 * @param {string} id3
 * @param {string} resultId
 * @param {number} decimalPlaces
 */
function change3NumCalculate(formula, id1, id2, id3, resultId, decimalPlaces = -1) {
  let formulaFor3Num = wasmModuleExports.formulaFor3Num;
  let resultElement = document.getElementById(resultId);
  if (!resultElement) {
    return;
  }
  if (document.getElementById(id1) && document.getElementById(id2) && document.getElementById(id3)) {
    if (
      document.getElementById(id1).value &&
      document.getElementById(id2).value &&
      document.getElementById(id3).value
    ) {
      let numStr1 = document.getElementById(id1).value.toString().trim();
      let numStr2 = document.getElementById(id2).value.toString().trim();
      let numStr3 = document.getElementById(id3).value.toString().trim();

      if (publicIsNumber(numStr1) && publicIsNumber(numStr2) && publicIsNumber(numStr3)) {
        let num1 = Number(numStr1);
        let num2 = Number(numStr2);
        let num3 = Number(numStr3);
        if (formula === Calculate3NumFormula.thermogravimetryTubeSurfaceArea) {
          num1 = num1 <= 2 ? Number(toFixed(num1, 2)) : Number(toFixed(num1, 1));
          num2 = num2 <= 0.4 ? Number(toFixed(num2, 2)) : Number(toFixed(num2, 1));
          num3 = Number(toFixed(num3, 1));
        }
        if (formula === Calculate3NumFormula.appliedLoad) {
          num2 = Number(toFixed(num2, 1));
          num3 = Number(toFixed(num3, 1));
        }

        resultElement.value = formulaFor3Num(formula, num1, num2, num3);
        resultElement.value = getDecimalResult(resultElement.value, decimalPlaces);
      } else {
        resultElement.value = '';
      }
    }
  } else {
    resultElement.value = '';
  }
}
/**
 * 限制输入框输入内容
 * @param {string} inputId 限制的输入框id
 * @param {string} regExp 要匹配的正则
 * @param {string} alertMessage 不合法时的提示语
 */
function limitInput(inputId, regExp, alertMessage) {
  const valueInput = document.getElementById(`${inputId}`).value;
  var reg = new RegExp(regExp);
  if (valueInput != null && valueInput != '' && !reg.test(valueInput)) {
    document.getElementById(`${inputId}`).value = '';
    document.getElementById(`${inputId}`).focus();
    setTimeout(() => {
      document.getElementById(`${inputId}`).value = valueInput;
    }, 1000);
    alert(alertMessage);
  }
}
/**
 * 比较数据。
 * @param {string} maxId 最大值的id
 * @param {string} mixId 最小值的id
 * @param {string} maxContainId 最大值是否包含的id
 * @param {string} minContainId 最小值是否包含的id
 * @param {array} compareIds 参与比较的id数组
 */
function compareArrayNum(maxId, mixId, maxContainId, minContainId, compareIds) {
  let maxValue = Number(document.getElementById(maxId).value); // 最大值
  let minValue = Number(document.getElementById(mixId).value); // 最小值
  let maxContainValue = document.getElementById(maxContainId).value; // 是否包含最大值
  let mixContainValue = document.getElementById(minContainId).value; // 是否包含最小值
  const resultArray = [];
  for (var i = 0; i < compareIds.length; i++) {
    const compareValue = Number(document.getElementById(compareIds[i]).value); // 比较的值
    resultArray.push(compareNum(maxValue, minValue, maxContainValue, mixContainValue, compareValue));
  }
  if (
    resultArray.some(item => {
      return item === false;
    })
  ) {
    return false;
  } else {
    return true;
  }
}
function compareResult(array) {
  if (
    array.some(item => {
      return item === false;
    })
  ) {
    return false;
  } else {
    return true;
  }
}
/**
 * 比较两个数据。
 * @param {string} maxValue 最大值
 * @param {string} minValue 最小值
 * @param {string} maxContainValue 最大值包含
 * @param {string} mixContainValue 最小值包含
 * @param {string} compareValue 参与比较的值
 */
function compareNum(maxValue, minValue, maxContainValue, mixContainValue, compareValue) {
  maxContainValue = maxContainValue === 'true' || maxContainValue === true;
  mixContainValue = mixContainValue === 'true' || mixContainValue === true;
  if (!publicIsNumber(compareValue)) {
    return;
  }
  if (maxValue && minValue) {
    // 既有最大值又有最小值
    if (maxContainValue && mixContainValue) {
      // 如果既包含最大值又包含最小值
      if (compareValue <= maxValue && compareValue >= minValue) {
        return true;
      } else {
        return false;
      }
    } else if (maxContainValue && !mixContainValue) {
      // 只包含最大值不包含最小值
      if (compareValue <= maxValue && compareValue > minValue) {
        return true;
      } else {
        return false;
      }
    } else if (!maxContainValue && mixContainValue) {
      // 只包含最小值，不包含最大值
      if (compareValue < maxValue && compareValue >= minValue) {
        return true;
      } else {
        return false;
      }
    } else {
      // 都不包含
      if (compareValue < maxValue && compareValue > minValue) {
        return true;
      } else {
        return false;
      }
    }
  } else if (maxValue && !minValue) {
    // 只有最大值没有最小值
    if (maxContainValue) {
      // 如果包含最大值
      if (compareValue <= maxValue) {
        return true;
      } else {
        return false;
      }
    } else {
      if (compareValue < maxValue) {
        return true;
      } else {
        return false;
      }
    }
  } else {
    // 只有最小值没有最大值
    if (mixContainValue) {
      // 如果包含最小值
      if (compareValue >= minValue) {
        return true;
      } else {
        return false;
      }
    } else {
      if (compareValue > minValue) {
        return true;
      } else {
        return false;
      }
    }
  }
}
/**
 * 根据需要用的公式(formula)，需要参与计算的元素ID(id1，id2)，将计算结果写入结果元素ID(resultId)，如果需要指定小数位数，填入decimalPlaces。
 * @param {Calculate4NumFormula} formula
 * @param {string} id1
 * @param {string} id2
 * @param {string} id3
 * @param {string} id4
 * @param {string} resultId
 * @param {number} decimalPlaces
 */
function change4NumCalculate(formula, id1, id2, id3, id4, resultId, decimalPlaces = -1) {
  let formulaFor4Num = wasmModuleExports.formulaFor4Num;
  let resultElement = document.getElementById(resultId);
  if (!resultElement) {
    return;
  }
  if (
    document.getElementById(id1) &&
    document.getElementById(id2) &&
    document.getElementById(id3) &&
    document.getElementById(id4)
  ) {
    if (
      document.getElementById(id1).value &&
      document.getElementById(id2).value &&
      document.getElementById(id3).value &&
      document.getElementById(id4).value
    ) {
      let numStr1 = document.getElementById(id1).value.toString().trim();
      let numStr2 = document.getElementById(id2).value.toString().trim();
      let numStr3 = document.getElementById(id3).value.toString().trim();
      let numStr4 = document.getElementById(id4).value.toString().trim();

      if (publicIsNumber(numStr1) && publicIsNumber(numStr2) && publicIsNumber(numStr3) && publicIsNumber(numStr4)) {
        resultElement.value = formulaFor4Num(
          formula,
          Number(numStr1),
          Number(numStr2),
          Number(numStr3),
          Number(numStr4)
        );
        resultElement.value = getDecimalResult(resultElement.value, decimalPlaces);
      }
    } else {
      resultElement.value = '';
    }
  } else {
    resultElement.value = '';
  }
}

/**
 * 根据需要用的公式(formula)，需要参与计算的元素ID(id1，id2)，将计算结果写入结果元素ID(resultId)，如果需要指定小数位数，填入decimalPlaces。
 * @param {Calculate6NumFormula} formula
 * @param {string} id1
 * @param {string} id2
 * @param {string} id3
 * @param {string} id4
 * @param {string} resultId
 * @param {number} decimalPlaces
 */
function change6NumCalculate(formula, id1, id2, id3, id4, id5, id6, resultId, decimalPlaces = -1) {
  let formulaFor6Num = wasmModuleExports.formulaFor6Num;
  let resultElement = document.getElementById(resultId);
  if (!resultElement) {
    return;
  }
  if (
    document.getElementById(id1) &&
    document.getElementById(id2) &&
    document.getElementById(id3) &&
    document.getElementById(id4) &&
    document.getElementById(id5) &&
    document.getElementById(id6)
  ) {
    if (
      document.getElementById(id1).value &&
      document.getElementById(id2).value &&
      document.getElementById(id3).value &&
      document.getElementById(id4).value &&
      document.getElementById(id5).value &&
      document.getElementById(id6).value
    ) {
      let numStr1 = document.getElementById(id1).value.toString().trim();
      let numStr2 = document.getElementById(id2).value.toString().trim();
      let numStr3 = document.getElementById(id3).value.toString().trim();
      let numStr4 = document.getElementById(id4).value.toString().trim();
      let numStr5 = document.getElementById(id5).value.toString().trim();
      let numStr6 = document.getElementById(id6).value.toString().trim();
      if (
        publicIsNumber(numStr1) &&
        publicIsNumber(numStr2) &&
        publicIsNumber(numStr3) &&
        publicIsNumber(numStr4) &&
        publicIsNumber(numStr5) &&
        publicIsNumber(numStr6)
      ) {
        resultElement.value = formulaFor6Num(
          formula,
          Number(numStr1),
          Number(numStr2),
          Number(numStr3),
          Number(numStr4),
          Number(numStr5),
          Number(numStr6)
        );
        resultElement.value = getDecimalResult(resultElement.value, decimalPlaces);
      }
    }
  } else {
    resultElement.value = '';
  }
}

/**
 * 根据需要用的公式(formula)，需要参与计算的元素ID(id1，id2)，将计算结果写入结果元素ID(resultId)，如果需要指定小数位数，填入decimalPlaces。
 * @param {Calculate7NumFormula} formula
 * @param {string} id1
 * @param {string} id2
 * @param {string} id3
 * @param {string} id4
 * @param {string} id5
 * @param {string} id6
 * @param {string} id7
 * @param {string} resultId
 * @param {number} decimalPlaces
 */
function change7NumCalculate(formula, id1, id2, id3, id4, id5, id6, id7, resultId, decimalPlaces = -1) {
  let formulaFor7Num = wasmModuleExports.formulaFor7Num;
  let resultElement = document.getElementById(resultId);
  if (!resultElement) {
    return;
  }
  if (
    document.getElementById(id1) &&
    document.getElementById(id2) &&
    document.getElementById(id3) &&
    document.getElementById(id4) &&
    document.getElementById(id5) &&
    document.getElementById(id6) &&
    document.getElementById(id7)
  ) {
    if (
      document.getElementById(id1).value &&
      document.getElementById(id2).value &&
      document.getElementById(id3).value &&
      document.getElementById(id4).value &&
      document.getElementById(id5).value &&
      document.getElementById(id6).value &&
      document.getElementById(id7).value
    ) {
      let numStr1 = document.getElementById(id1).value.toString().trim();
      let numStr2 = document.getElementById(id2).value.toString().trim();
      let numStr3 = document.getElementById(id3).value.toString().trim();
      let numStr4 = document.getElementById(id4).value.toString().trim();
      let numStr5 = document.getElementById(id5).value.toString().trim();
      let numStr6 = document.getElementById(id6).value.toString().trim();
      let numStr7 = document.getElementById(id7).value.toString().trim();

      if (
        publicIsNumber(numStr1) &&
        publicIsNumber(numStr2) &&
        publicIsNumber(numStr3) &&
        publicIsNumber(numStr4) &&
        publicIsNumber(numStr5) &&
        publicIsNumber(numStr6) &&
        publicIsNumber(numStr7)
      ) {
        resultElement.value = formulaFor7Num(
          formula,
          Number(numStr1),
          Number(numStr2),
          Number(numStr3),
          Number(numStr4),
          Number(numStr5),
          Number(numStr6),
          Number(numStr7)
        );
        resultElement.value = getDecimalResult(resultElement.value, decimalPlaces);
      }
    }
  } else {
    resultElement.value = '';
  }
}
/**
 * 根据需要用的公式(formula)，需要参与计算的元素ID(id1，id2)，将计算结果写入结果元素ID(resultId)，如果需要指定小数位数，填入decimalPlaces。
 * @param {Calculate5NumFormula} formula
 * @param {string} id1
 * @param {string} id2
 * @param {string} id3
 * @param {string} id4
 * @param {string} resultId
 * @param {number} decimalPlaces
 */
function change5NumCalculate(formula, id1, id2, id3, id4, id5, resultId, decimalPlaces = -1) {
  let formulaFor5Num = wasmModuleExports.formulaFor5Num;
  let resultElement = document.getElementById(resultId);
  if (!resultElement) {
    return;
  }
  if (
    document.getElementById(id1) &&
    document.getElementById(id2) &&
    document.getElementById(id3) &&
    document.getElementById(id4) &&
    document.getElementById(id5)
  ) {
    if (
      document.getElementById(id1).value &&
      document.getElementById(id2).value &&
      document.getElementById(id3).value &&
      document.getElementById(id4).value &&
      document.getElementById(id5).value
    ) {
      let numStr1 = document.getElementById(id1).value.toString().trim();
      let numStr2 = document.getElementById(id2).value.toString().trim();
      let numStr3 = document.getElementById(id3).value.toString().trim();
      let numStr4 = document.getElementById(id4).value.toString().trim();
      let numStr5 = document.getElementById(id5).value.toString().trim();

      if (
        publicIsNumber(numStr1) &&
        publicIsNumber(numStr2) &&
        publicIsNumber(numStr3) &&
        publicIsNumber(numStr4) &&
        publicIsNumber(numStr5)
      ) {
        resultElement.value = formulaFor5Num(
          formula,
          Number(numStr1),
          Number(numStr2),
          Number(numStr3),
          Number(numStr4),
          Number(numStr5)
        );
        resultElement.value = getDecimalResult(resultElement.value, decimalPlaces);
      }
    } else {
      resultElement.value = '';
    }
  } else {
    resultElement.value = '';
  }
}

/**
 * 根据需要用的公式(formula)，需要参与计算的元素ID(id1，id2)，将计算结果写入结果元素ID(resultId)，如果需要指定小数位数，填入可选参数decimalPlaces。
 * @param {CalculateArrayNumFormula} formula
 * @param {Array<string>} paramsArray 老模板id集合;模板引擎定义的模板入参值集合
 * @param {string} resultId
 * @param {number} decimalPlaces
 */
function changeArrayNumCalculate(formula, paramsArray, resultId, decimalPlaces = -1) {
  const { __pin, __unpin, __newArray, __getArray, __getArrayView } = wasmModuleExports;
  let formulaForArrayNum = wasmModuleExports.formulaForArrayNum;
  let float64ArrayID = wasmModuleExports.Float64Array_ID;
  let elementArray = [];
  if (resultId) {
    // 老模板
    paramsArray.forEach(elementId => {
      elementArray.push(document.getElementById(elementId));
    });
    if (elementArray.length > 0) {
      let numArray = [];
      elementArray.forEach(element => {
        if (element) {
          if (publicIsNumber(element.value)) {
            numArray.push(Number(element.value));
          }
        }
      });
      let resultElement = document.getElementById(resultId);
      if (numArray.length > 0) {
        const numArrayPtr = __pin(__newArray(float64ArrayID, numArray));
        resultElement.value = formulaForArrayNum(formula, numArrayPtr);
        resultElement.value = getDecimalResult(resultElement.value, decimalPlaces);
        __unpin(numArrayPtr);
      } else {
        resultElement.value = '';
      }
    }
  } else {
    // 模板引擎定义的模板,直接返回结果值
    if (paramsArray.length > 0) {
      const numArrayPtr = __pin(__newArray(float64ArrayID, paramsArray));
      __unpin(numArrayPtr);
      return formulaForArrayNum(formula, numArrayPtr);
    } else {
      return;
    }
  }
}

/**
 * 根据需要用的公式(formula)，需要参与计算的元素ID(id1，id2)，将计算结果写入结果元素ID(resultId)，如果需要指定小数位数，填入可选参数decimalPlaces。
 * @param {CalculateArrayAnd1NumFormula} formula
 * @param {Array<string>} idArray
 * @param {string} resultId
 * @param {number} decimalPlaces
 */
function changeArrayAnd1NumCalculate(formula, idArray, id1, resultId, decimalPlaces = -1) {
  const { __pin, __unpin, __newArray, __getArray, __getArrayView } = wasmModuleExports;
  let formulaForArrayAnd1Num = wasmModuleExports.formulaForArrayAnd1Num;
  let float64ArrayID = wasmModuleExports.Float64Array_ID;
  let numStr1;
  if (document.getElementById(id1)) {
    numStr1 = document.getElementById(id1).value.toString().trim();
  }
  let resultElement = document.getElementById(resultId);
  if (!resultElement) {
    return;
  }
  let elementArray = [];
  idArray.forEach(elementId => {
    elementArray.push(document.getElementById(elementId));
  });
  if (elementArray.length > 1) {
    let numArray = [];
    elementArray.forEach(element => {
      if (element) {
        if (publicIsNumber(element.value)) {
          numArray.push(Number(element.value));
        }
      }
    });
    if (numArray.length > 0 && numStr1) {
      const numArrayPtr = __pin(__newArray(float64ArrayID, numArray));
      resultElement.value = formulaForArrayAnd1Num(formula, numArrayPtr, Number(numStr1));
      resultElement.value = getDecimalResult(resultElement.value, decimalPlaces);
      __unpin(numArrayPtr);
    } else {
      resultElement.value = '';
    }
  }
}

/**
 * 判断小数位保留
 * @param {string} result
 * @param {number} decimalPlaces
 * @returns
 */
function getDecimalResult(result, decimalPlaces) {
  if (result === '-**********' || !publicIsNumber(result)) {
    return '';
  } else {
    if (result.includes('e')) {
      return result;
    }
    if (decimalPlaces === -1) {
      let splictArray = result.toString().split('.');
      if (splictArray.length === 2 && splictArray[1].length > 10) {
        return toFixed(result, 10);
      }
      return result;
    } else {
      return toFixed(result, decimalPlaces);
    }
  }
}

function changeDecimalPlaces(elementId, decimalPlaces) {
  let element = document.getElementById(elementId);
  const originValue = element.value.toString().trim();
  element.value = getDecimalResult(originValue, decimalPlaces);
}
function changeExponentNumber(elementId, number) {
  let element = document.getElementById(elementId);
  element.value = Number(element.value).toExponential(number);
}
function reservedSignificantBit(id, bitNum) {
  let resultElement = document.getElementById(id);
  if (!resultElement) {
    return;
  }
  if (bitNum < 1) {
    return;
  }
  let numStr = document.getElementById(id).value.toString().trim();
  if (document.getElementById(id)) {
    if (document.getElementById(id).value && publicIsNumber(numStr)) {
      let numberValue = publicCheckNum(numStr);
      if (numberValue && bitNum >= 1) {
        // 是数字并且有效位数大于等于1
        if (Math.abs(numberValue) >= 1) {
          resultElement.value =
            numberValue > 0
              ? toFixed(numberValue, jDZGreaterOne(numberValue, bitNum))
              : toFixed(numberValue, jDZGreaterOne(Math.abs(numberValue), bitNum));
        } else if (Math.abs(numberValue) === 0) {
          resultElement.value = 0;
        } else {
          // 小数
          resultElement.value =
            numberValue > 0
              ? toFixed(numberValue, fractionalPart(numberValue, bitNum))
              : toFixed(numberValue, fractionalPart(Math.abs(numberValue), bitNum));
        }
      }
    } else {
      resultElement.value = '';
    }
  } else {
    resultElement.value = '';
  }
}

function changePrecision(elementId, precision) {
  let element = document.getElementById(elementId);
  const originValue = element.value.toString().trim();
  if (publicIsNumber(originValue)) {
    element.value = Number(originValue).toPrecision(precision);
  }
}

/**
 * 设置单向数据绑定，将input元素的值写入到result元素
 * @param {string} inputId
 * @param {string} resultId
 */
function setDataBinding(inputId, resultId) {
  const inputElement = document.getElementById(inputId);
  const resultElement = document.getElementById(resultId);

  inputElement.addEventListener('input', function (e) {
    resultElement.value = publicIsNumber(e.target.value) ? e.target.value : '';
  });
}

/**
 * 引入MathJax
 */
function importMathJax() {
  if (!window.MathJax) {
    const body = document.querySelector('body');
    let script = document.createElement('script');
    script.setAttribute('id', 'MathJax-script');
    script.setAttribute('type', 'text/javascript');
    script.setAttribute('src', `${baseWasmUrl}/tes-wasm/tex-mml-chtml.js`);
    script.async = true;
    document.getElementsByTagName('head')[0].appendChild(script);
  }
}
// 根据入参id获取值
function getValueByParamId(paramId) {
  if (document.getElementById(paramId)) {
    if (document.getElementById(paramId).value) {
      return document.getElementById(paramId).value.toString().trim();
    } else {
      return;
    }
  } else {
    return;
  }
}
// 根据结果值id, 往结果值id里面塞值
function setValueByTargetId(resultId, value) {
  let resultElement = document.getElementById(resultId);
  if (!resultElement) {
    return;
  }
  resultElement.value = value;
  // 小数位数保留
  if (resultElement.hasAttribute('data-decimalplace') && resultElement.getAttribute('data-decimalplace') !== 'null') {
    changeDecimalPlaces(resultId, Number(resultElement.getAttribute('data-decimalplace')));
  }
  // 有效位数保留
  if (
    resultElement.hasAttribute('data-reservedSignificantBit') &&
    resultElement.getAttribute('data-reservedSignificantBit') !== 'null'
  ) {
    reservedSignificantBit(resultId, Number(resultElement.getAttribute('data-reservedSignificantBit')));
  }
  // 科学计数法
  if (
    resultElement.hasAttribute('data-exponentNumber') &&
    resultElement.getAttribute('data-exponentNumber') !== 'null'
  ) {
    changeExponentNumber(resultId, Number(resultElement.getAttribute('data-exponentNumber')));
  }
  document.getElementById(resultId).dispatchEvent(new Event('change'));
}

/**
 * 手动渲染公式 返回 Promise
 * @param el 需要渲染的DOM元素或集合
 * @returns Promise
 */
function renderByMathjax(el) {
  setTimeout(() => {
    // mathjax初始化后才会注入version
    if (window.MathJax) {
      if (!window.MathJax.version) {
        return;
      }
    }

    if (el && !Array.isArray(el)) {
      el = [el];
    }

    return new Promise((resolve, reject) => {
      window.MathJax.typesetPromise(el)
        .then(() => {
          // eslint-disable-next-line no-void
          resolve(void 0);
        })
        .catch(err => reject(err));
    });
  }, 100);
}
// 求和
function SUM() {
  return changeArrayNumCalculate(
    CalculateArrayNumFormula.sum,
    Array.from(arguments).filter(item => item !== undefined),
    false
  );
}
// 平均值
function AVERAGE() {
  return changeArrayNumCalculate(
    CalculateArrayNumFormula.average,
    Array.from(arguments).filter(item => item !== undefined),
    false
  );
}
// 最大值
function MAX() {
  return changeArrayNumCalculate(
    CalculateArrayNumFormula.maximum,
    Array.from(arguments).filter(item => item !== undefined),
    false
  );
}
// 最小值
function MIN() {
  return changeArrayNumCalculate(
    CalculateArrayNumFormula.minimum,
    Array.from(arguments).filter(item => item !== undefined),
    false
  );
}
// 乘积
function PRODUCT() {
  return changeArrayNumCalculate(
    CalculateArrayNumFormula.product,
    Array.from(arguments).filter(item => item !== undefined),
    false
  );
}
// 相除
function DIVIDE() {
  return changeArrayNumCalculate(
    CalculateArrayNumFormula.divide,
    Array.from(arguments).filter(item => item !== undefined),
    false
  );
}
const startXhModule = (length, columnNum) => {
  if (length) {
    if (document.getElementById('xhmoduleIndex')) {
      document.getElementById('xhmoduleIndex').value = length;
    }
    for (var i = 0; i < document.getElementsByClassName('appendXhmodule').length; i++) {
      const xhDom = document.getElementsByClassName('xhModule')[i].innerHTML; // 大的循环体
      const columnXhDom = document.getElementsByClassName('xhModule')[i].querySelector('.columnRow').innerHTML; // 列的循环体
      let columnAll = ''; // 这个循环体的所有列

      for (var index = 1; index <= length; index++) {
        columnAll += getNewColumnString(columnXhDom, index);
        if (index % columnNum === 0 || index === length) {
          let moduleIndex = 0;
          if (index === length) {
            moduleIndex = Math.floor(index / columnNum + 1);
          } else {
            moduleIndex = Math.floor(index / columnNum);
          }
          document
            .getElementsByClassName('appendXhmodule')
            [i].insertAdjacentHTML('beforeend', getNewModuleString(xhDom, moduleIndex));
          document
            .getElementsByClassName('appendXhmodule')
            [i].querySelector('.pdf-page-' + moduleIndex)
            .querySelector('.columnXhModule').innerHTML = columnAll;
          columnAll = '';
        }
      }
    }
  } else {
    for (var j = 0; j < document.getElementsByClassName('appendXhmodule').length; j++) {
      document.getElementsByClassName('appendXhmodule')[j].innerHTML = '';
    }
  }
};
const getNewModuleString = (xhString, index) => {
  var newString =
    '<div class="newXhModule pdf-page pdf-page-' + index + '">' + xhString.replace(/moduleIndex/g, index) + '</div>';
  return newString;
};
const getNewColumnString = (xhString, index) => {
  var newString = '<div class="newXhColumn columnRow bd-r">' + xhString.replace(/moduleIndex/g, index) + '</div>';
  return newString;
};
