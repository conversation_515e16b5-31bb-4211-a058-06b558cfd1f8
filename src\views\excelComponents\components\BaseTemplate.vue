<template>
  <el-form>
    <!--头部-->
    <div v-if="templateStyle !== 0 && !isOnlyFooter && !isDogTagType" class="pdf-header">
      <!-- 简洁模式 -->
      <div v-if="templateStyle == 1">
        <h2 class="template-title">{{ templateHeaderName }}</h2>
        <h2 class="template-title">原始记录</h2>
        <h3 class="english-title">Original Record</h3>
        <div class="bd">
          <el-row class="moduleBh" style="margin-bottom: 2px">
            <el-col :span="12">
              <div>样品编号</div>
            </el-col>
            <el-col :span="12">
              <div>型号规格</div>
            </el-col>
          </el-row>
          <el-row class="moduleBh">
            <el-col :span="12">
              <div>Sample No：{{ sampleNum }}</div>
            </el-col>
            <el-col :span="12">
              <div>
                Type & Size ：
                <span v-html="filterProdType(modelSpecification)" />
              </div>
            </el-col>
          </el-row>
          <div v-if="!transverse" class="header">
            <el-row class="bd-b">
              <el-col :span="6" class="bd-r">试验场所：</el-col>
              <el-col :span="11" class="bd-r">
                <slot name="test-site" />
              </el-col>
              <el-col :span="7" style="text-align: right; padding-right: 5px">{{ fileNo }}</el-col>
            </el-row>
            <el-row class="bd-b">
              <el-col :span="6" class="bd-r">检测项目：</el-col>
              <el-col :span="18" class="templateHeaderItem">
                <el-input v-model="capabilityItemName" class="iptPublic" :readonly="true" />
              </el-col>
            </el-row>
            <slot v-if="!isDogTagType" name="test-device" />
          </div>
          <div v-if="transverse" class="header">
            <el-row class="bd-b">
              <el-col :span="4" class="bd-r">试验场所：</el-col>
              <el-col :span="12" class="bd-r">
                <slot name="test-site" />
              </el-col>
              <el-col :span="8" style="text-align: right; padding-right: 5px">{{ fileNo }}</el-col>
            </el-row>
            <el-row class="bd-b">
              <el-col :span="4" class="bd-r">检测项目：</el-col>
              <el-col :span="20" class="bd-r templateHeaderItem">
                <el-input v-model="capabilityItemName" class="iptPublic" :readonly="true" />
              </el-col>
            </el-row>
            <slot name="test-device" />
          </div>
        </div>
      </div>
      <!-- 标准模式 -->
      <div v-else-if="templateStyle == 2">
        <h2 class="template-title">{{ templateHeaderName }}</h2>
        <h2 class="template-title">{{ capabilityItemName || 'XXXX' }} 原始记录</h2>
        <div class="bd">
          <el-row class="moduleBh" style="margin-bottom: 2px">
            <el-col :span="12">
              <div>样品编号</div>
            </el-col>
            <el-col :span="12">
              <div>型号规格</div>
            </el-col>
          </el-row>
          <el-row class="moduleBh">
            <el-col :span="12">
              <div>Sample No：{{ sampleNum }}</div>
            </el-col>
            <el-col :span="12">
              <div>
                Type & Size ：
                <span v-html="filterProdType(modelSpecification)" />
              </div>
            </el-col>
          </el-row>
          <div v-if="!transverse" class="header">
            <el-row class="bd-b">
              <el-col :span="6" class="bd-r">试验场所：</el-col>
              <el-col :span="11" class="bd-r">
                <slot name="test-site" />
              </el-col>
              <el-col :span="7" style="text-align: right; padding-right: 5px">{{ fileNo }}</el-col>
            </el-row>
            <el-row class="bd-b">
              <el-col :span="6" class="bd-r">检测项目：</el-col>
              <el-col :span="18" class="templateHeaderItem">
                <el-input v-model="capabilityItemName" class="iptPublic" :readonly="true" />
              </el-col>
            </el-row>
            <el-row class="bd-b">
              <el-col :span="6" class="bd-r textareaLable">试验方法：</el-col>
              <el-col :span="18">
                <slot name="test-method" />
              </el-col>
            </el-row>
            <el-row class="bd-b">
              <el-col :span="6" class="bd-r textareaLable">检测标准：</el-col>
              <el-col :span="18">
                <slot name="test-standard" />
              </el-col>
            </el-row>
            <el-row class="bd-b">
              <el-col :span="6" class="bd-r">环境温度(℃)：</el-col>
              <el-col :span="6" class="bd-r">
                <slot name="test-temperature" />
              </el-col>
              <el-col :span="5" class="bd-r">环境湿度(RH%)：</el-col>
              <el-col :span="7">
                <slot name="test-humidness" />
              </el-col>
            </el-row>
            <slot v-if="!isDogTagType" name="test-device" />
          </div>
          <div v-if="transverse" class="header">
            <el-row class="bd-b">
              <el-col :span="4" class="bd-r">试验场所：</el-col>
              <el-col :span="12" class="bd-r">
                <slot name="test-site" />
              </el-col>
              <el-col :span="8" style="text-align: right; padding-right: 5px">{{ fileNo }}</el-col>
            </el-row>
            <el-row class="bd-b">
              <el-col :span="4" class="bd-r">检测项目：</el-col>
              <el-col :span="20" class="bd-r templateHeaderItem">
                <el-input v-model="capabilityItemName" class="iptPublic" :readonly="true" />
              </el-col>
              <el-col :span="4" class="bd-r textareaLable">试验方法：</el-col>
              <el-col :span="8">
                <slot name="test-method" />
              </el-col>
            </el-row>
            <el-row class="bd-b">
              <el-col :span="4" class="bd-r">检测标准：</el-col>
              <el-col :span="8" class="bd-r">
                <slot name="test-standard" />
              </el-col>
              <el-col :span="4" class="bd-r">环境温度(℃)：</el-col>
              <el-col :span="2" class="bd-r">
                <slot name="test-temperature" />
              </el-col>
              <el-col :span="4" class="bd-r">环境湿度(RH%)：</el-col>
              <el-col :span="2">
                <slot name="test-humidness" />
              </el-col>
            </el-row>
            <slot name="test-device" />
          </div>
        </div>
      </div>
      <!-- 中国电科院样式 -->
      <div v-else-if="templateStyle == 3">
        <h2 class="template-title">{{ capabilityItemName || 'XXXX' }} 检测原始记录</h2>
        <el-row>
          <el-col :span="12">
            <div style="margin-top: 27px; text-align: left">文件代号：{{ fileNo }}</div>
          </el-col>
          <el-col :span="12" class="template-pagination">
            共<span class="totalPage" />项 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 第<span
              class="whichPage"
            />项
          </el-col>
        </el-row>
        <div class="bd">
          <el-row class="moduleBh" style="margin-bottom: 2px">
            <el-col :span="12">
              <div>样品编号： {{ sampleNum }}</div>
            </el-col>
            <el-col :span="12">
              <div>型号规格：<span v-html="filterProdType(modelSpecification)" /></div>
            </el-col>
          </el-row>
          <div v-if="!transverse" class="header">
            <el-row class="bd-b">
              <el-col :span="6" class="bd-r">检测地点：</el-col>
              <el-col :span="18">
                <slot name="test-site" />
              </el-col>
            </el-row>
            <el-row class="bd-b">
              <el-col :span="6" class="bd-r">检测时间：</el-col>
              <el-col :span="18">
                <slot name="test-time" />
              </el-col>
            </el-row>

            <el-row class="bd-b">
              <el-col :span="6" class="bd-r textareaLable">检测方法：</el-col>
              <el-col :span="18">
                <slot name="test-method" />
              </el-col>
            </el-row>
            <el-row class="bd-b">
              <el-col :span="6" class="bd-r textareaLable">判定依据：</el-col>
              <el-col :span="18">
                <slot name="test-standard" />
              </el-col>
            </el-row>
            <el-row class="bd-b">
              <el-col :span="6" class="bd-r">室温(℃)：</el-col>
              <el-col :span="6" class="bd-r">
                <slot name="test-temperature" />
              </el-col>
              <el-col :span="5" class="bd-r">相对湿度(%)：</el-col>
              <el-col :span="7">
                <slot name="test-humidness" />
              </el-col>
            </el-row>
            <slot v-if="!isDogTagType" name="test-device" />
          </div>
          <div v-if="transverse" class="header">
            <el-row class="bd-b">
              <el-col :span="4" class="bd-r">检测地点：</el-col>
              <el-col :span="8" class="bd-r">
                <slot name="test-site" />
              </el-col>
              <el-col :span="4" class="bd-r">文件代号：</el-col>
              <el-col :span="8" style="text-align: right; padding-right: 5px">{{ fileNo }}</el-col>
            </el-row>
            <el-row class="bd-b">
              <el-col :span="4" class="bd-r">检测依据：</el-col>
              <el-col :span="20">
                <slot name="test-standard" />
              </el-col>
            </el-row>
            <el-row class="bd-b">
              <el-col :span="4" class="bd-r">室温（℃）：</el-col>
              <el-col :span="8" class="bd-r">
                <slot name="test-temperature" />
              </el-col>
              <el-col :span="4" class="bd-r">相对湿度(%)：</el-col>
              <el-col :span="8">
                <slot name="test-humidness" />
              </el-col>
            </el-row>
            <slot name="test-device" />
          </div>
        </div>
      </div>
      <!--久隆-->
      <div v-else-if="templateStyle == 4">
        <h2 class="template-title">上海久隆电力（集团）有限公司变压器修试分公司检测中心油化检测室</h2>
        <h2 class="template-title">原始记录</h2>
        <h3 class="english-title">Original Record</h3>
        <div class="bd header">
          <el-row class="bd-b">
            <el-col :span="5" class="bd-r"> 样品名称： </el-col>
            <el-col :span="8" class="bd-r">
              <slot name="test-sampleName" />
            </el-col>
            <el-col :span="5" class="bd-r"> 样品编号： </el-col>
            <el-col :span="6"><slot name="test-sampleNum" /></el-col>
          </el-row>
          <el-row class="bd-b">
            <el-col :span="5" class="bd-r"> 收样日期： </el-col>
            <el-col :span="8" class="bd-r">
              <slot name="test-sample-receiving-date" />
            </el-col>
            <el-col :span="5" class="bd-r"> 样品状态： </el-col>
            <el-col :span="6">
              <slot name="test-sampleStatus" />
            </el-col>
          </el-row>
          <el-row class="bd-b">
            <el-col :span="5" class="bd-r">标准方法：</el-col>
            <el-col :span="8" class="bd-r">
              <slot name="test-method" />
            </el-col>
            <el-col :span="5" class="bd-r">检测地点：</el-col>
            <el-col :span="6">
              <slot name="test-site" />
            </el-col>
          </el-row>
          <el-row class="bd-b">
            <el-col :span="5" class="bd-r">环境温度（℃）：</el-col>
            <el-col :span="8" class="bd-r">
              <slot name="test-temperature" />
            </el-col>
            <el-col :span="5" class="bd-r">相对湿度（RH%）：</el-col>
            <el-col :span="6">
              <slot name="test-humidness" />
            </el-col>
          </el-row>
        </div>
      </div>
      <!-- 八益 -->
      <div v-else-if="templateStyle == 5">
        <h2 class="template-title">{{ templateHeaderName }}</h2>
        <h2 class="template-title">{{ getTenantConfig()?.clientNameEn }}</h2>
        <div class="relative">
          <div class="font-bold text-center pt-1">
            <div style="font-size: 17px">{{ capabilityItemName || 'XXXX' }}原始记录</div>
            <div style="font-size: 17px">Primary Record of {{ englishName }}</div>
          </div>
          <div class="font-bold absolute right-0 bottom-0">NO: {{ fileNo }}</div>
        </div>
        <div class="bd header">
          <el-row class="bd-b">
            <el-col :span="6" class="bd-r"> 样品编号 Sample No：</el-col>
            <el-col :span="6" class="bd-r flex items-center">
              {{ sampleNum }}
            </el-col>
            <el-col :span="6" class="bd-r"> 批次号 batch no： </el-col>
            <el-col :span="6" class="flex items-center">
              {{ batchNo }}
            </el-col>
          </el-row>
          <el-row class="bd-b">
            <el-col :span="6" class="bd-r"> 样品名称 Sample Name：</el-col>
            <el-col :span="18" class="flex items-center">
              {{ sampleName }}
            </el-col>
          </el-row>
          <slot name="test-device" />
        </div>
      </div>
    </div>
    <div id="template-body" :class="{ bdt: templateStyle === 0 || isDogTagType }">
      <slot />
    </div>
    <div v-if="(!isOnlyHeader || isOnlyFooter) && !isDogTagType" id="template-footer">
      <div v-if="templateTail === 1" class="footer">
        <el-row class="">
          <el-col :span="2" class=""> 检·测： </el-col>
          <el-col :span="6" class="" style="text-align: left">
            <slot name="tester" />
            <slot name="checker" />
          </el-col>
          <el-col :span="2" class=""> 记·录： </el-col>
          <el-col :span="6" class="" style="text-align: left">
            <slot name="tester" />
          </el-col>
          <el-col :span="2" class=""> 校·核： </el-col>
          <el-col :span="6" class="" style="text-align: left">
            <slot name="checker" />
          </el-col>
        </el-row>
      </div>
      <!-- 八益 -->
      <div v-else-if="templateTail === 2" class="txt-l footer">
        <el-row class="">
          <el-col :span="6" class=""> 试验员 Tester： </el-col>
          <el-col :span="6" class="">
            <slot name="tester" />
          </el-col>
          <el-col :span="6" class=""> 审批人 Approver： </el-col>
          <el-col :span="6" class="">
            <slot name="checker" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6" class=""> 试验日期 Test Date： </el-col>
          <el-col :span="6" class="">
            <slot name="test-date" />
          </el-col>
          <el-col :span="6" class=""> 审批日期 Approval Date： </el-col>
          <el-col :span="6" class="">
            <slot name="check-date" />
          </el-col>
        </el-row>
      </div>
      <div v-else class="txt-l footer">
        <el-row class="">
          <el-col :span="3" class=""> 试验员： </el-col>
          <el-col :span="9" class="">
            <slot name="tester" />
          </el-col>
          <el-col :span="3" class=""> 校核人： </el-col>
          <el-col :span="9" class="">
            <slot name="checker" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="3" class=""> 试验日期： </el-col>
          <el-col :span="9" class="">
            <slot name="test-date" />
          </el-col>
          <el-col :span="3" class=""> 校核日期： </el-col>
          <el-col :span="9" class="">
            <slot name="check-date" />
          </el-col>
        </el-row>
      </div>
    </div>
  </el-form>
</template>
<script>
import { computed, reactive, toRefs } from 'vue';
import store from '@/store';
import { getTenantConfig } from '@/utils/auth';

import { getPermissionBtn } from '@/utils/common';
// import SvgIcon from '@/components/SvgIcon'
// import { formatWebsocketTime } from '@/utils/formatTime'
// import { getLoginInfo } from '@/utils/auth'

export default {
  name: 'BaseTemplate',
  components: {},
  props: {
    sampleNum: {
      type: String,
      default: ''
    },
    sampleName: {
      type: String,
      default: ''
    },
    batchNo: {
      type: String,
      default: ''
    },
    englishName: {
      type: String,
      default: ''
    },
    modelSpecification: {
      type: Array,
      default: () => {
        return [];
      }
    },
    fileNo: {
      type: String,
      default: ''
    },
    capabilityName: {
      type: String,
      default: ''
    },
    isOnlyHeader: {
      type: Boolean,
      default: false
    },
    isOnlyFooter: {
      type: Boolean,
      default: false
    },
    isTransverse: {
      type: Boolean,
      default: false
    },
    isTransverseFooter: {
      type: Boolean,
      default: false
    },
    templateTailStyle: {
      type: Number,
      default: 0
    },
    headType: {
      type: Number,
      default: 2
    },
    isDogTag: {
      type: Boolean,
      default: false
    }
  },
  setup(props, ctx) {
    const state = reactive({
      voiceFlag: false
    });
    const templateHeaderName = computed({
      get: () => store.state.user.tenantInfo.clientFullName
    });
    const templateTail = computed({
      // 表尾样式
      get: () => props.templateTailStyle
    });
    const capabilityItemName = computed({
      get: () => props.capabilityName
    });
    const isHeader = computed({
      get: () => props.isOnlyHeader
    });
    const isFooter = computed({
      get: () => props.isOnlyFooter
    });
    const templateStyle = computed({
      // 表头样式
      get: () => props.headType
    });
    const transverse = computed({
      get: () => props.isTransverse
    });
    const transverseFooter = computed({
      get: () => props.isTransverseFooter
    });
    const isDogTagType = computed({
      get: () => props.isDogTag
    });
    const filterProdType = val => {
      var title = '';
      if (val.length) {
        val.forEach(item => {
          title = title + `${item}<br>`;
        });
      } else {
        title = val;
      }
      return title;
    };

    return {
      ...toRefs(state),
      getPermissionBtn,
      filterProdType,
      templateHeaderName,
      getTenantConfig,
      capabilityItemName,
      templateTail,
      transverseFooter,
      templateStyle,
      transverse,
      isHeader,
      isFooter,
      isDogTagType
    };
  }
};
</script>
<style scoped lang="scss">
.textareaLable {
  display: flex;
  align-items: center;
}
.templateHeaderItem {
  text-indent: 0;
  :deep(.el-input__wrapper) {
    box-shadow: none;
    height: 25px;
  }
}
@import '@/styles/mixin.scss';

.bd {
  text-align: left;
  // width: 680px;
  margin: 10px auto 0;
}
// .isTransverse {
//   width: 1123px;
//   .bd {
//     width: 1009px;
//   }
// }
.pdf-header {
  h2,
  h3 {
    text-align: center;
  }
  .bd {
    margin: 0 auto;
  }
}
.moduleBh {
  div {
    line-height: 20px !important;
    height: 20px !important;
  }
  .header_size {
    height: auto !important;
  }
}
.header {
  border: 1px solid #000;
  line-height: 1.5;
  text-indent: 10px;
  margin-top: 15px;
  border-bottom: none;
}

.template-title {
  font-weight: bold;
  font-size: 1.5em;
  line-height: normal;
}
.template-pagination {
  font-size: 1rem;
  text-align: right;
  margin-top: 27px;
  line-height: normal;
}
.english-title {
  font-size: 1em;
  margin-top: 0px;
  line-height: normal;
}
.page-number {
  position: absolute;
  bottom: -30px;
  width: 50px;
  left: 50%;
  margin-left: -25px;
  text-align: center;
}
.whichPage {
  display: inline-block;
  padding: 0 10px;
}
.totalPage {
  display: inline-block;
  padding: 0 10px;
}
:deep(.el-input--medium .el-input__icon) {
  line-height: 25px;
}
</style>
