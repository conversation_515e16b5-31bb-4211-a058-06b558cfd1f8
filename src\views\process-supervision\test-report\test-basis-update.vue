<template>
  <el-dialog
    :model-value="showDialog"
    custom-class="test-basis-update"
    title="判定标准更新"
    width="70%"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :before-close="close"
  >
    <el-row class="add-report-search">
      <el-col :span="24"> 以下项目判定标准已更新，是否重新判定？ </el-col>
    </el-row>
    <el-table
      ref="testBasisUpdateRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      size="medium"
      max-height="calc(100vh - 26.5rem)"
      class="dark-table allocation-table base-table format-height-table"
      @header-dragend="drageHeader"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" :selectable="selectable" />
      <el-table-column label="项目名称" prop="expName" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.expName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="原检测依据" prop="standardProduct" min-width="120px" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.standardProduct">{{ row.standardProduct }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="新检测依据" prop="firstStandardProduct" min-width="120px" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.firstStandardProduct || '--' }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination v-show="total>0" small :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getStandarInfoList" /> -->
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取消,不更新</el-button>
        <el-button type="primary" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent>更新选中项</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, toRefs, watch, getCurrentInstance } from 'vue';
// import { ElMessage } from 'element-plus'
// import Pagination from '@/components/Pagination'
import { drageHeader } from '@/utils/formatTable';
import { getStandardInfoById, updateStandardInfoByIds } from '@/api/testReport';
import { getCurrentReportInfo } from '@/utils/auth';
// import _ from 'lodash'

export default {
  name: 'TestBasisUpdate',
  components: {},
  props: {
    show: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const testBasisUpdateRef = ref(null);
    const currentInfo = getCurrentReportInfo();
    const datas = reactive({
      showDialog: props.show,
      listLoading: false,
      tableKey: 0,
      list: [],
      selectList: [],
      newList: [],
      param: { editReportdetailInfoRequestList: [] }
    });

    watch(
      () => props.show,
      async newValue => {
        console.log(newValue);
        if (newValue) {
          await proxy.getStandarInfoList(currentInfo.sampleId);
          datas.showDialog = newValue;
        }
      },
      { deep: true }
    );

    // 确定选择
    const dialogSuccess = async () => {
      console.log(datas.param);
      await proxy.updateStandardInfoByIds();
      datas.showDialog = false;
      context.emit('setInfo', true);
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      context.emit('setInfo', false);
    };
    // 列表选择事件
    const handleSelectionChange = val => {
      console.log(val);
      datas.selectList = val;
      datas.param.editReportdetailInfoRequestList = [];
      if (val.length > 0) {
        val.forEach(v => {
          datas.param.editReportdetailInfoRequestList.push({ id: v.id });
        });
      }
    };
    // 是否禁用checkbox
    const selectable = (row, index) => {
      if (row.disable) {
        return false;
      } else {
        return true;
      }
    };

    return {
      ...toRefs(datas),
      close,
      dialogSuccess,
      drageHeader,
      handleSelectionChange,
      selectable,
      testBasisUpdateRef
    };
  },
  created() {
    // this.getStandarInfoList()
  },
  methods: {
    getStandarInfoList(sampleId) {
      const that = this;
      that.listLoading = true;
      const param = {
        reportStage: getCurrentReportInfo().reportStage,
        sampleId: sampleId
      };
      // param.sampleId = '578932719061712896'
      getStandardInfoById(param).then(res => {
        if (res !== false) {
          that.list = res.data.data.reportdetailexpinfoEntityList;
          if (that.list && that.list.length > 0) {
            that.$refs.testBasisUpdateRef.toggleAllSelection(true);
          }
        }
        that.listLoading = false;
      });
    },
    updateStandardInfoByIds() {
      return updateStandardInfoByIds(this.param).then(res => {
        if (res !== false) {
          console.log('updateStandardInfoByIds');
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.test-basis-update {
  .search {
    width: 360px;
  }
  .add-report-search {
    margin-bottom: 15px;
  }
}
</style>
