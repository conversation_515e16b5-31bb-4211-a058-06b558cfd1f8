import { colWidth } from '@/data/tableStyle';

/**
 * 属性说明
 * field: 字段名
 * label: 字段显示名
 * colwidth: 列宽(可以为tableStyle中的值，也可是数字)
 * fieldType: 列的类型。 0: 默认类型
 * isNotQuery: 列是否可以为非查询字段， 0: 不是，1: 是
 * routeType: 路由调整类型 0:检验申请-申请单号
 * styleContent: 样式字段解析
 * order: 排序字段，排序顺序
 * checkbox: 当前视图中是否显示
 * isHide: 是否为隐藏列
 * isOrder: 是否需要排序
 */

/**
 * 列的类型type
 * 0: 默认显示
 * 1: 带链接的列
 * 2: 显示带单位的生产数量的列
 * 3: 用标签显示枚举的列
 */

export const orderFieldList = [
  {
    field: 'salesOrderItemNo',
    label: '销售订单行号',
    colWidth: colWidth.productionOrderNo,
    fieldType: 'text',
    isNotQuery: 0,
    routefieldType: 'text',
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isMinWidth: true,
    isOrder: 1
  },
  {
    field: 'factory',
    label: '工厂编号',
    colWidth: colWidth.productionOrderNo,
    fieldType: 'text',
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isMinWidth: true
  },
  {
    field: 'materialNo',
    label: '物料编号',
    colWidth: colWidth.material,
    fieldType: 'text',
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 1,
    isMinWidth: true
  },
  {
    field: 'materialGroupNo',
    label: '物料分组',
    colWidth: colWidth.description,
    fieldType: 'text',
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 1,
    isMinWidth: true
  },
  {
    field: 'materialDesc',
    label: '物料名称',
    colWidth: colWidth.description,
    fieldType: 'text',
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isMinWidth: true
  },
  {
    field: 'customerModel',
    label: '客户型号',
    colWidth: colWidth.materialGroup,
    fieldType: 'text',
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isMinWidth: true
  },
  {
    field: 'customerSpecifications',
    label: '客户规格',
    colWidth: colWidth.process,
    fieldType: 'text',
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isMinWidth: false
  },
  {
    field: 'customerVoltage',
    label: '客户电压',
    colWidth: colWidth.name,
    fieldType: 'text',
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isMinWidth: false
  },
  {
    field: 'companyModel',
    label: '公司型号规格',
    colWidth: colWidth.productionQuantity,
    fieldType: 'text',
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isMinWidth: false
  },
  {
    field: 'model',
    label: '物料特征值-型号',
    colWidth: colWidth.status,
    fieldType: 'text',
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isFixed: true,
    isMinWidth: false
  },
  {
    field: 'specifications',
    label: '物料特征值-规格',
    colWidth: colWidth.status,
    fieldType: 'text',
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isFixed: true,
    isMinWidth: false
  },
  {
    field: 'voltageLevel',
    label: '物料特征值-电压等级',
    colWidth: colWidth.status,
    fieldType: 'text',
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isFixed: true,
    isMinWidth: false
  },
  {
    field: 'standard',
    label: '物料特征值-标准',
    colWidth: colWidth.status,
    fieldType: 'text',
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isFixed: true,
    isMinWidth: false
  },
  {
    field: 'color',
    label: '物料特征值-颜色',
    colWidth: colWidth.status,
    fieldType: 'text',
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isFixed: true,
    isMinWidth: false
  },
  {
    field: 'nuclearMarker',
    label: '特殊标识',
    colWidth: colWidth.status,
    fieldType: 'text',
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isFixed: true,
    isMinWidth: false
  },
  {
    field: 'segmentLengthTxt',
    label: '生产段长描述',
    colWidth: colWidth.status,
    fieldType: 'text',
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isFixed: true,
    isMinWidth: false
  },
  {
    field: 'printRequirement',
    label: '印字要求',
    colWidth: colWidth.status,
    fieldType: 'text',
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isFixed: true,
    isMinWidth: false
  },
  {
    field: 'orderQuantity',
    label: '销售订单数量',
    colWidth: colWidth.status,
    fieldType: 'text',
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isFixed: true,
    isMinWidth: false
  },
  {
    field: 'quantityUnit',
    label: '数量单位',
    colWidth: colWidth.status,
    fieldType: 'text',
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isFixed: true,
    isMinWidth: false
  },
  {
    field: 'plannedCompletionDate',
    label: '计划完成日期',
    colWidth: colWidth.status,
    fieldType: 'date',
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isFixed: true,
    isMinWidth: false
  },
  {
    field: 'remark',
    label: '备注',
    colWidth: colWidth.status,
    fieldType: 'text',
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isFixed: true,
    isMinWidth: false
  },
  {
    field: 'shippingDate',
    label: '发货日期',
    colWidth: colWidth.status,
    fieldType: 'date',
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isFixed: true,
    isMinWidth: false
  },
  {
    field: 'isDeleted',
    label: '行项目删除标识',
    colWidth: colWidth.status,
    fieldType: 'custom',
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isFixed: true,
    isMinWidth: false
  }
];

export const orderTagList = [
  {
    field: 'productStatus',
    dataMap: {
      0: ['warning', '作废'],
      1: ['success', '正常']
    }
  },
  {
    field: 'isDeleted',
    dataMap: {
      0: ['warning', '未删除'],
      1: ['success', '已删除']
    }
  },
  {
    field: 'submitStatus',
    dataMap: {
      0: ['warning', '待送检'],
      1: ['success', '已送检']
    }
  }
];

export function handleTag(field, value) {
  if (field && orderTagList.length > 0) {
    const enumIndex = orderTagList.findIndex(item => item.field === field);
    if (enumIndex === -1) {
      return ['', value];
    } else {
      return orderTagList[enumIndex].dataMap[value] || ['', value];
    }
  } else {
    return ['', value];
  }
}
