#!/bin/bash

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

echo -e "RUNNING: $ cd $SCRIPT_DIR"
cd $SCRIPT_DIR
if [ $? -ne 0 ]
then
    echo -e "ERROR RUNNING: \"$ cd $SCRIPT_DIR\""
    exit 1
fi

STACK_NAME=cx-main
IMAGE_NAME=web-cxist-tes
IMAGE_TAG=nightly
REGISTRY_HOST=""
TARGET_API_GATEWAY=""
TARGET_PROD=""
IMAGE_PREFIX=xlab

while [[ $# -gt 0 ]]
do
key="$1"
case $key in
    --registry)
    REGISTRY_HOST="$2"
    shift
    shift
    ;;
    --stack)
    STACK_NAME="$2"
    shift
    shift
    ;;
    --name)
    IMAGE_NAME="$2"
    shift
    shift
    ;;
    --tag)
    IMAGE_TAG="$2"
    shift
    shift
    ;;
    --prod)
    TARGET_PROD="$2"
    shift
    shift
    ;;
    --api-gateway)
    TARGET_API_GATEWAY="$2"
    shift
    shift
    ;;
    *)
    shift
    ;;
esac
done

if [ -z $STACK_NAME ]
then
    STACK_NAME=cx-main
fi

DOCKER_SERVICE_ID="$(sudo docker service inspect $STACK_NAME'_'$IMAGE_NAME -f '{{.ID}}')"
if [ ! $? -ne 0 ]; then
    echo -e "docker service update --force --image hub-internal.cxist.com:5000/byzan/$IMAGE_PREFIX/$IMAGE_NAME:$IMAGE_TAG $STACK_NAME"_"$IMAGE_NAME"
    docker service update --force --image hub-internal.cxist.com:5000/byzan/$IMAGE_PREFIX/$IMAGE_NAME:$IMAGE_TAG $STACK_NAME'_'$IMAGE_NAME
else
    echo -e "\nRUNNING: \"$ env \
        PROD=$TARGET_PROD \
        API_GATEWAY=$TARGET_API_GATEWAY \
        TAG=$IMAGE_TAG \
        IMAGE_NAME=$IMAGE_NAME \
        REGISTRY_HOST=$REGISTRY_HOST \
        docker stack up -c ./stack.yml $STACK_NAME\""

    env \
        PROD=$TARGET_PROD \
        API_GATEWAY=$TARGET_API_GATEWAY \
        TAG=$IMAGE_TAG \
        IMAGE_NAME=$IMAGE_NAME \
        REGISTRY_HOST=$REGISTRY_HOST \
        docker stack up -c ./stack.yml $STACK_NAME
    if [ $? -ne 0 ]
    then
        echo -e "ERROR RUNNING: \"env \
            PROD=$TARGET_PROD \
            API_GATEWAY=$TARGET_API_GATEWAY \
            TAG=$IMAGE_TAG \
            IMAGE_NAME=$IMAGE_NAME \
            REGISTRY_HOST=$REGISTRY_HOST \
            docker stack up -c ./stack.yml $STACK_NAME\""
        exit 1
    fi
fi
