<template>
  <Editor :id="id" v-model="myValue" :init="initOptions" :disabled="disabled" tag-name="div" />
  <!-- <textarea id="mytextarea">Hello, World!</textarea> -->
  <div ref="htmlDocRef" style="display: none" />
</template>
<script>
import { reactive, onMounted, watch, toRefs, ref } from 'vue';
import Editor from '@tinymce/tinymce-vue';
import tinymce from 'tinymce/tinymce'; // tinymce默认hidden，不引入则不显示编辑器
import 'tinymce/themes/silver'; // 编辑器主题，不引入则报错
import 'tinymce/icons/default/icons'; // 引入编辑器图标icon，不引入则不显示对应图标
// 引入编辑器插件
import 'tinymce/plugins/advlist'; // 高级列表
import 'tinymce/plugins/autolink'; // 自动链接
import 'tinymce/plugins/code'; // 源码
import 'tinymce/plugins/colorpicker'; // 颜色选择器
import 'tinymce/plugins/charmap'; // 特殊字符
import 'tinymce/plugins/fullscreen'; // 全屏
import 'tinymce/plugins/hr'; // 水平分割线
import 'tinymce/plugins/image'; // 插入上传图片插件
import 'tinymce/plugins/imagetools';
import 'tinymce/plugins/insertdatetime'; // 插入日期时间
import 'tinymce/plugins/link'; // 超链接
import 'tinymce/plugins/lists'; // 列表插件
import 'tinymce/plugins/media'; // 插入视频插件
import 'tinymce/plugins/paste'; // 粘贴插件
import 'tinymce/plugins/preview'; // 预览
import 'tinymce/plugins/table'; // 插入表格插件
import 'tinymce/plugins/textcolor'; // 文字颜色
import 'tinymce/plugins/textpattern'; // 快速排版
import 'tinymce/plugins/template'; // 内容模板
import 'tinymce/plugins/wordcount'; // 字数统计插件
import { uploadMoreMethod } from '@/api/testItem';
import { renderAsync } from 'docx-preview';

export default {
  name: 'MarkdownEditor',
  components: { Editor },
  props: {
    value: {
      type: String,
      default: ''
    },
    height: {
      type: [Number, String],
      default: 500
    },
    disabled: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: 'mytextarea'
    },
    readonly: {
      type: Boolean,
      default: false
    },
    plugins: {
      type: [String, Array],
      default:
        'preview autolink fullscreen image link media template code table charmap hr insertdatetime advlist lists wordcount textpattern'
    },
    toolbar: {
      type: [String, Array, Boolean],
      default: () => {
        return [
          'formatselect | forecolor backcolor | fontselect | fontsizeselect | bullist numlist | outdent indent lineheight | undo redo',
          'bold italic underline strikethrough subscript superscript removeformat charmap hr selectall | alignleft aligncenter alignright alignjustify | table image link pagebreak | code preview fullscreen'
        ];
      }
    }
  },
  emits: ['getContent'],
  setup(props, context) {
    const datas = reactive({
      htmlDocRef: ref(),
      myValue: props.value,
      id: props.id,
      initOptions: {
        selector: `#${props.id}`,
        language_url: '/tinymce/langs/zh_CN.js', // 语言包的路径
        language: 'zh_CN', // 语言
        relative_urls: false,
        remove_script_host: false,
        skin_url: '/tinymce/skins/ui/oxide', // skin路径
        height: props.height, // 编辑器高度
        branding: false, // 是否禁用“Powered by TinyMCE”
        menubar: false, // 隐藏菜单栏
        // image_dimensions: false, // 去除宽高属性
        plugins: props.plugins,
        toolbar: props.toolbar,
        // paste_convert_word_fake_lists: false, // 插入word文档需要该属性
        // paste_webkit_styles: 'all',
        // paste_merge_formats: true,
        // nonbreaking_force_tab: false,
        // paste_auto_cleanup_on_paste: false,
        fontsize_formats: '12px 14px 16px 18px 24px 32px', // 字体大小
        font_formats:
          'Times New Roman;MiSans;苹方=PingFang SC,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;',
        style_formats: [
          { title: 'Bold text', inline: 'b' },
          { title: 'Red text', inline: 'span', styles: { color: '#ff0000' } },
          { title: 'Red header', block: 'h1', styles: { color: '#ff0000' } },
          { title: 'Example 1', inline: 'span', classes: 'example1' },
          { title: 'Example 2', inline: 'span', classes: 'example2' },
          { title: '首行缩进', block: 'p', styles: { 'text-indent': '2em' } },
          { title: 'Table styles' },
          { title: 'Table row 1', selector: 'tr', classes: 'tablerow1' }
        ],
        style_formats_merge: true,
        style_formats_autohide: true,
        color_cols: 10,
        custom_colors: true,
        color_map: [
          '000000',
          '',
          '252523',
          '',
          '595958',
          '',
          '8c8c8b',
          '',
          'bfbfbe',
          '',
          'dadad9',
          '',
          'e9e9e8',
          '',
          'f5f5f5',
          '',
          'f9f9f8',
          '',
          'ffffff',
          '',
          'f44d4e',
          '',
          'f67a44',
          '',
          'f8aa3e',
          '',
          'fbec3c',
          '',
          '72d13c',
          '',
          '43cfc9',
          '',
          '40a9fe',
          '',
          '597ef7',
          '',
          '9354de',
          '',
          'f659ac',
          ''
        ],
        readonly: true,
        // 图片上传
        images_upload_handler: (blobInfo, success, failure) => {
          const formdata = new FormData();
          formdata.append('fileList', blobInfo.blob());
          // debugger
          // var imgSrc = 'data:image/jpg;base64,' + blobInfo.base64()
          uploadMoreMethod(formdata, callback => {
            // console.log(callback)
          }).then(res => {
            if (res) {
              success(res.data.data[0].url);
            }
          });
          // 将返回来的数据放在succsss回调中
        },
        // 文件上传
        file_picker_callback: function (callback, value, meta) {
          // console.log(value, meta)
          // var reader = new FileReader()
          // reader.onload = function() {
          //   var id = 'blobid' + (new Date()).getTime()
          //   var blobCache = tinymce.activeEditor.editorUpload.blobCache
          //   var base64 = reader.result.split(',')[1]
          //   var blobInfo = blobCache.create(id, file, base64)
          //   blobCache.add(blobInfo)
          //   callback(blobInfo.blobUri(), { title: file.name })
          // }
          // reader.readAsDataURL(file)
          // 文件分类
          var filetype = '';
          if (meta.filetype === 'file') {
            filetype = '.pdf, .txt, .zip, .rar, .7z, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .mp3, .mp4';
          }
          // 模拟出一个input用于添加本地文件
          var input = document.createElement('input');
          input.setAttribute('type', 'file');
          input.setAttribute('accept', filetype);

          input.onchange = function () {
            var file = this.files[0];
            var reader = new FileReader();
            reader.onload = function () {
              var id = 'blobid' + new Date().getTime();
              var blobCache = tinymce.activeEditor.editorUpload.blobCache;
              var base64 = reader.result.split(',')[1];
              var blobInfo = blobCache.create(id, file, base64);
              // 实现word展示
              if (file.name.indexOf('.doc') !== -1) {
                renderAsync(blobInfo.blob(), datas.htmlDocRef).then(res => {
                  datas.myValue = datas.htmlDocRef.innerHTML;
                });
              }
              blobCache.add(blobInfo);
              callback(blobInfo.blobUri(), {
                title: file.name
              });
            };
            reader.readAsDataURL(file);
            // var formdata = new FormData()
            // formdata.append('file', file)
            // 调用上传接口
            // axops.post('xxxxx',formdata).then(res=>{})
            // 返回地址 //将返回的数据放在callback()里面
            // callback("http://baidu.com", { title: file.name })
          };
          input.click();
        }
      }
    });

    watch(
      () => props.value,
      newValue => {
        if (typeof newValue === 'string') {
          datas.myValue = newValue;
        }
      },
      { deep: true }
    );

    // 监听富文本中的数据变化
    watch(
      () => datas.myValue,
      newValue => {
        context.emit('getContent', newValue);
      }
    );

    // 在onMounted中初始化编辑器
    onMounted(() => {
      tinymce.init({});
    });

    return { ...toRefs(datas) };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss">
.tinymce {
  width: 850px !important;
}
.tox-tinymce {
  border-color: #dcdfe6 !important;
  border-radius: 4px !important;
}
.tox:not([dir='rtl']) .tox-toolbar__group:not(:last-of-type) {
  border-right-color: #dcdfe6 !important;
}
.tox-tinymce-aux {
  z-index: 9999 !important;
}
.tox .tox-dialog-wrap__backdrop {
  display: none;
}
.tox .tox-dialog {
  box-shadow: none;
}
.tox-dialog tox-dialog--width-lg {
  z-index: 9999 !important;
}
.tox .tox-statusbar {
  border-top-color: #dcdfe6 !important;
}
</style>
