<template>
  <!-- 检测执行 样品 -->
  <ListComponent page-type="SampleViewList" />
</template>

<script>
import { reactive, toRefs } from 'vue';
import ListComponent from './components/ListComponent.vue';

export default {
  name: 'SampleViewList',
  components: { ListComponent },
  setup() {
    const state = reactive({});
    return {
      ...toRefs(state)
    };
  },
  computed: {},
  created() {}
};
</script>
<style lang="scss" scoped></style>
