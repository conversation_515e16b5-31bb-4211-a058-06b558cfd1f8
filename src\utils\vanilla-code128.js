const Code128 = (() => {
  const patterns = [
    '212222',
    '222122',
    '222221',
    '121223',
    '121322',
    '131222',
    '122213',
    '122312',
    '132212',
    '221213',
    '221312',
    '231212',
    '112232',
    '122132',
    '122231',
    '113222',
    '123122',
    '123221',
    '223211',
    '221132',
    '221231',
    '213212',
    '223112',
    '312131',
    '311222',
    '321122',
    '321221',
    '312212',
    '322112',
    '322211',
    '212123',
    '212321',
    '232121',
    '111323',
    '131123',
    '131321',
    '112313',
    '132113',
    '132311',
    '211313',
    '231113',
    '231311',
    '112133',
    '112331',
    '132131',
    '113123',
    '113321',
    '133121',
    '313121',
    '211331',
    '231131',
    '213113',
    '213311',
    '213131',
    '311123',
    '311321',
    '331121',
    '312113',
    '312311',
    '332111',
    '314111',
    '221411',
    '431111',
    '111224',
    '111422',
    '121124',
    '121421',
    '141122',
    '141221',
    '112214',
    '112412',
    '122114',
    '122411',
    '142112',
    '142211',
    '241211',
    '221114',
    '413111',
    '241112',
    '134111',
    '111242',
    '121142',
    '121241',
    '114212',
    '124112',
    '124211',
    '411212',
    '421112',
    '421211',
    '212141',
    '214121',
    '412121',
    '111143',
    '111341',
    '131141',
    '114113',
    '114311',
    '411113',
    '411311',
    '113141',
    '114131',
    '311141',
    '411131',
    '211412',
    '211214',
    '211232',
    '2331112'
  ];

  const patternModules = patterns.map(p => p.split('').map(ch => parseInt(ch, 10)));
  const START_B = 104;
  const STOP = 106;

  function codeForCharB(ch) {
    const code = ch.charCodeAt(0) - 32;
    if (code < 0 || code > 95) throw new Error(`Unsupported char for Code128-B: "${ch}"`);
    return code;
  }

  function encodeToCodes(text) {
    const codes = [START_B];
    for (let i = 0; i < text.length; i++) codes.push(codeForCharB(text[i]));
    let checksum = START_B;
    for (let i = 0; i < text.length; i++) checksum += (i + 1) * codes[i + 1];
    codes.push(checksum % 103);
    codes.push(STOP);
    return codes;
  }

  function codesToModules(codes) {
    const modules = [];
    for (let i = 0; i < codes.length; i++) {
      const code = codes[i];
      if (code === STOP) modules.push(...patternModules[STOP]);
      else modules.push(...patternModules[code]);
    }
    return modules;
  }

  function drawModulesToCanvas(modules, text, opts = {}) {
    const height = opts.height || 50;
    const moduleWidth = opts.width || 2;
    const margin = typeof opts.margin === 'number' ? opts.margin : 10;
    const bg = opts.background || '#ffffff';
    const lineColor = opts.lineColor || '#000000';
    const displayValue = opts.displayValue !== undefined ? opts.displayValue : true;
    const font = opts.font || '16px sans-serif';

    const totalModules = modules.reduce((s, n) => s + n, 0);
    const textHeight = displayValue ? parseInt((font.match(/\d+/) || [16])[0], 10) + 6 : 0;

    // ✅ canvas 总高度包含上下 margin
    const canvasWidth = Math.ceil(totalModules * moduleWidth + margin * 2);
    const canvasHeight = Math.ceil(margin + height + (displayValue ? textHeight + 2 : 0) + margin);

    const canvas = document.createElement('canvas');
    canvas.width = canvasWidth;
    canvas.height = canvasHeight;
    const ctx = canvas.getContext('2d');

    ctx.fillStyle = bg;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // ✅ 条码从 marginTop 绘制
    let x = margin;
    const y = margin; // top margin
    ctx.fillStyle = lineColor;
    let isBar = true;
    for (let i = 0; i < modules.length; i++) {
      const w = modules[i] * moduleWidth;
      if (isBar) ctx.fillRect(x, y, w, height);
      x += w;
      isBar = !isBar;
    }

    // ✅ 显示文字
    if (displayValue) {
      ctx.font = font;
      ctx.fillStyle = lineColor;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'top';
      const textX = canvasWidth / 2;
      const textY = y + height + 2; // 条码下方 + 2px 间距
      ctx.fillText(text, textX, textY);
    }

    return canvas;
  }

  return {
    encodeToCodes,
    codesToModules,
    drawModulesToCanvas
  };
})();

export default class VanillaCode128 {
  static renderToCanvas(text, options = {}) {
    if (!text && text !== '0') throw new Error('text is required to generate barcode');
    const codes = Code128.encodeToCodes(String(text));
    const modules = Code128.codesToModules(codes);
    return Code128.drawModulesToCanvas(modules, text, options);
  }

  static toDataURL(text, options = {}) {
    return new Promise((resolve, reject) => {
      try {
        const canvas = VanillaCode128.renderToCanvas(text, options);
        resolve(canvas.toDataURL('image/png'));
      } catch (err) {
        reject(err);
      }
    });
  }

  static toImageElement(text, options = {}) {
    const canvas = VanillaCode128.renderToCanvas(text, options);
    const img = document.createElement('img');
    img.src = canvas.toDataURL('image/png');
    return img;
  }
}
