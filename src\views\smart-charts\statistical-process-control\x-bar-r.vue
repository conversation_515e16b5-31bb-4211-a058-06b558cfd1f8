<template>
  <!-- SPC控制图 -->
  <ListLayout :has-custom-header="true" :has-custom-main="true" :main-offset-top="topHeight">
    <template #page-custom-header>
      <CollapseHeader ref="collapse" @set-main-offset="setMainOffset" @show-collapse="showCollapse">
        <template #title>
          <div v-if="showCondition" class="page-title page-comple">
            <span v-if="searchForm.inspectionTypeName">{{ searchForm.inspectionTypeName }}</span>
            <span v-if="searchForm.materialClassificationName" class="space-line" />
            <span>{{ searchForm.materialClassificationName }}</span>
            <span v-if="searchForm.voltageLevelName" class="space-line" />
            <span v-if="searchForm.voltageLevelName">{{ searchForm.voltageLevelName }}</span>
            <span v-if="searchForm.materialGroupName" class="space-line" />
            <span v-if="searchForm.materialGroupName">{{ searchForm.materialGroupName }}</span>
            <span v-if="searchForm.workingProcedureName" class="space-line" />
            <span v-if="searchForm.workingProcedureName">{{ searchForm.workingProcedureName }}</span>
            <span v-if="searchForm.itemKeyParam" class="space-line" />
            <span>{{ searchForm.itemKeyParam }}</span>
            <span v-if="searchForm.key">&nbsp;&nbsp;&nbsp;&nbsp;{{ `模糊搜索：${searchForm.key}` }}</span>
          </div>
          <div v-else class="page-title">选择查询条件生成控制图</div>
        </template>

        <template #collapse>
          <div class="collapse-group">
            <div class="left-form-group">
              <el-form
                ref="editFrom"
                label-width="110px"
                label-position="top"
                :inline="true"
                :model="searchForm"
                @submit.prevent
              >
                <el-row :gutter="16">
                  <el-col :span="4">
                    <el-form-item label="检验类型：" prop="inspectionType">
                      <el-select
                        v-model="searchForm.inspectionType"
                        class="chart-query-select"
                        placeholder="选择检验类型"
                        size="small"
                        clearable
                        @change="changeInspectionType"
                      >
                        <el-option
                          v-for="item in inspectionTypeOptions"
                          :key="item.id"
                          :label="item.name"
                          :value="item.code"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item
                      label="物资分类："
                      prop="materialClassification"
                      :rules="{
                        required: true,
                        message: '请选择物资分类',
                        trigger: 'change'
                      }"
                    >
                      <el-select
                        v-model="searchForm.materialClassification"
                        class="chart-query-select"
                        placeholder="选择物资分类"
                        size="small"
                        clearable
                        @change="changeMaterialClassification"
                      >
                        <el-option
                          v-for="item in materialClassifications"
                          :key="item.id"
                          :label="item.name"
                          :value="item.code"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item label="电压等级：" prop="voltageLevel">
                      <el-select
                        v-model="searchForm.voltageLevel"
                        class="chart-query-select"
                        placeholder="选择电压等级"
                        size="small"
                        clearable
                        @change="changeVoltageLevel"
                      >
                        <el-option
                          v-for="item in voltageLevelOptions"
                          :key="item.id"
                          :label="item.name"
                          :value="item.code"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item label="物料分组：" prop="materialGroupName">
                      <el-tag v-if="searchForm.materialGroupName" closable @close="deleteMaterialgroup">
                        {{ searchForm.materialGroupName }}
                      </el-tag>
                      <el-button
                        v-else
                        size="small"
                        icon="el-icon-plus"
                        @click="handleMaterialgroup"
                        @keyup.prevent
                        @keydown.enter.prevent
                        >选择物料分组</el-button
                      >
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item label="工序：">
                      <el-tag v-if="searchForm.workingProcedureName" size="small" closable @close="deleteProcess">
                        <span class="query-tag">{{ searchForm.workingProcedureName }}</span>
                      </el-tag>
                      <el-button
                        v-else
                        size="small"
                        icon="el-icon-plus"
                        @click="handleProcess"
                        @keyup.prevent
                        @keydown.enter.prevent
                        >选择工序</el-button
                      >
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item
                      label="关键参数："
                      prop="itemKeyParam"
                      :rules="{
                        required: true,
                        message: '请选择关键参数',
                        trigger: 'change'
                      }"
                    >
                      <el-tag v-if="searchForm.itemKeyParam" size="small" closable @close="deleteKeyParam()">
                        <span class="query-tag">{{ searchForm.itemKeyParam }}</span>
                      </el-tag>
                      <el-button
                        v-else
                        size="small"
                        icon="el-icon-plus"
                        @click="selectItems()"
                        @keyup.prevent
                        @keydown.enter.prevent
                        >选择关键参数</el-button
                      >
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item
                      label="时间范围："
                      class="date-range"
                      prop="submitTime"
                      :rules="{
                        required: true,
                        message: '请选择时间范围',
                        trigger: 'change'
                      }"
                    >
                      <el-date-picker
                        v-model="searchForm.submitTime"
                        type="daterange"
                        size="small"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :shortcuts="shortcuts"
                        @change="handleDatePicker"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item
                      label="子组大小："
                      prop="subGroupCount"
                      :rules="{
                        required: true,
                        message: '请选择子组大小',
                        trigger: 'change'
                      }"
                    >
                      <el-input-number
                        v-model="searchForm.subGroupCount"
                        size="small"
                        controls-position="right"
                        :min="1"
                        :max="10"
                        @change="changeSubGroup"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item
                      label="样品数："
                      prop="sampleCapacity"
                      :rules="{
                        required: true,
                        message: '请输入样品数',
                        trigger: 'change'
                      }"
                    >
                      <el-input-number
                        v-model="searchForm.sampleCapacity"
                        size="small"
                        controls-position="right"
                        :min="1"
                        :max="1000"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="模糊搜索：" prop="key">
                      <el-input
                        ref="inputKeyRef"
                        v-model="searchForm.key"
                        size="small"
                        placeholder="请输入生产订单/物料编号/物料分组/样品名称/型号规格"
                        controls-position="right"
                        clearable
                        :min="2"
                        :max="10"
                        @change="changeSearchKey"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            <div class="right-button-group">
              <el-button
                key="sample"
                type="text"
                size="small"
                @click="renderSampleData()"
                @keyup.prevent
                @keydown.enter.prevent
              >
                <span class="el-icon-data-analysis" />
              </el-button>
              <el-button key="cancel" size="small" @click="cancelRender()" @keyup.prevent @keydown.enter.prevent
                >取消</el-button
              >
              <el-button
                :loading="renderLoading"
                type="primary"
                size="small"
                @click="renderSpcData()"
                @keyup.prevent
                @keydown.enter.prevent
                >生成图表</el-button
              >
            </div>
          </div>
        </template>
      </CollapseHeader>
    </template>

    <template #page-custom-main>
      <el-row v-if="showSingleChart">
        <el-col :span="24">
          <CustomPanel :has-margin-right="true" :has-margin-left="true">
            <template #panel-title>
              <div class="panel-header-left">
                <span class="title">实时控制图</span>
              </div>
            </template>
            <template #panel-content>
              <v-chart v-if="showSingleChart" :option="realTimeChartOption" :width="'100%'" :height="'40vh'" />
              <el-empty v-else :image="emptyImg" description="暂无图表" />
            </template>
          </CustomPanel>
        </el-col>
      </el-row>
      <el-row v-else>
        <el-col :span="12">
          <CustomPanel :has-margin-left="true">
            <template #panel-title>
              <div class="panel-header-flex">
                <div class="panel-header-left">
                  <span class="title">X-Bar均值图</span>
                  <div class="standard-controller">
                    <span class="standard-label">标准范围：</span>
                    <el-input v-model="xBarLowerLimit" size="small" placeholder="下限" :disabled="limitDisabled" />
                    <span class="standard-space">-</span>
                    <el-input v-model="xBarUpperLimit" size="small" placeholder="上限" :disabled="limitDisabled" />
                  </div>
                </div>
                <el-popover placement="bottom" :width="400" trigger="hover">
                  <template #reference>
                    <i class="el-icon-info" />
                  </template>
                  <el-alert
                    v-for="(item, index) in warningReferenceList"
                    :key="index"
                    class="chart-alert"
                    :title="item"
                    type="warning"
                    :closable="false"
                  />
                </el-popover>
              </div>
            </template>
            <template #panel-content>
              <v-chart v-if="showChart" :option="xBarChartOption" :width="'100%'" :height="'40vh'" />
              <el-empty v-else :image="emptyImg" description="暂无图表" />
            </template>
          </CustomPanel>
        </el-col>
        <el-col :span="12">
          <CustomPanel :has-margin-right="true" :has-margin-left="true">
            <template #panel-title>
              <div class="panel-header-flex">
                <div class="panel-header-left">
                  <span class="title">R极差图</span>
                  <div class="standard-controller">
                    <span class="standard-label">标准范围：</span>
                    <el-input v-model="rangeLowerLimit" size="small" placeholder="下限" :disabled="limitDisabled" />
                    <span class="standard-space">-</span>
                    <el-input v-model="rangeUpperLimit" size="small" placeholder="上限" :disabled="limitDisabled" />
                  </div>
                </div>
                <el-popover placement="left-start" :width="400" trigger="hover">
                  <template #reference>
                    <i class="el-icon-info" />
                  </template>
                  <el-alert
                    v-for="(item, index) in warningReferenceList"
                    :key="index"
                    class="chart-alert"
                    :title="item"
                    type="warning"
                    :closable="false"
                  />
                </el-popover>
              </div>
            </template>
            <template #panel-content>
              <v-chart v-if="showChart" :option="rangeChartOption" :width="'100%'" :height="'40vh'" />
              <el-empty v-else :image="emptyImg" description="暂无图表" />
            </template>
          </CustomPanel>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <CustomPanel
            :has-margin-top="true"
            :has-margin-right="true"
            :has-margin-bottom="true"
            :has-margin-left="true"
            class="custom-panel-sm"
          >
            <template #panel-title>
              <div class="panel-header-left">
                <span class="title">属性</span>
              </div>
            </template>
            <template #panel-content>
              <el-descriptions v-if="showChart || showSingleChart" :column="6" border>
                <el-descriptions-item v-for="(item, index) in paramTable" :key="index" label-align="left">
                  <template #label>
                    <div v-if="item.label === 'CPK等级'" class="cell-item">
                      <el-tooltip placement="top">
                        <template #content>
                          Cpk等级评定<br />
                          A+, Cpk &gt;= 1.67<br />
                          A, 1.33&lt;= Cpk &lt; 1.67<br />
                          B, 1.0&lt;= Cpk &lt; 1.33<br />
                          C, 0.67&lt;= Cpk &lt; 1.0<br />
                          D, Cpk &lt;= 0.67<br />
                        </template>
                        <div style="width: 100%; height: 100%">
                          {{ item.label }}
                        </div>
                      </el-tooltip>
                    </div>
                    <div v-else class="cell-item">
                      {{ item.label }}
                    </div>
                  </template>
                  {{ item.value }}
                </el-descriptions-item>
              </el-descriptions>
              <div v-else class="no-data">
                <img src="@/assets/img/empty-data.png" class="nodata-img" alt="no-data" />
              </div>
            </template>
          </CustomPanel>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <CustomPanel class="last-panel" :has-margin-right="true" :has-margin-bottom="true" :has-margin-left="true">
            <template #panel-title>
              <div class="panel-header-left">
                <span class="title">样本数据</span>
              </div>
            </template>
            <template #panel-content>
              <el-table v-if="showChart || showSingleChart" :data="sampleTable" class="dark-table" max-height="500">
                <el-table-column label="序号" :width="100" align="center">
                  <template #default="scope">{{
                    scope.$index + 1 < 10 ? '0' + (scope.$index + 1) : scope.$index + 1
                  }}</template>
                </el-table-column>
                <template v-if="showSingleChart">
                  <el-table-column label="样品日期" align="center">
                    <template #default="{ row }">{{ row.finishedDate }}</template>
                  </el-table-column>
                  <el-table-column label="样品名称" align="center">
                    <template #default="{ row }">{{ row.sampleName }}</template>
                  </el-table-column>
                  <el-table-column label="样品值" align="center">
                    <template #default="{ row }">{{ row.capabilityParaValue }}</template>
                  </el-table-column>
                </template>
                <template v-else>
                  <el-table-column v-for="n in sampleTable[0].sample.length" :key="n" :label="'#' + n" align="right">
                    <template #default="{ row }">{{ row.sample[n - 1] }}</template>
                  </el-table-column>
                  <el-table-column label="平均值 x-bar" align="right">
                    <template #default="{ row }">{{
                      isNaN(Number(row.sampleAverage)) ? '-' : Number(row.sampleAverage).toExponential(6)
                    }}</template>
                  </el-table-column>
                  <el-table-column label="极差 R" align="right">
                    <template #default="{ row }">{{ row.sampleRange.toExponential() }}</template>
                  </el-table-column>
                </template>
              </el-table>
              <el-empty v-else :image="emptyTable" description="暂无数据" />
            </template>
          </CustomPanel>
        </el-col>
      </el-row>

      <el-row>
        <el-col>
          <div style="height: 60px; width: 100%" />
        </el-col>
      </el-row>
    </template>
    <template #other>
      <SelectKeyParam
        :show="showItemDialog"
        :tree="treeData"
        :data="oldSelectedKeyParam"
        @close="closeDialog"
        @selected-data="selectedKeyParam"
      />
      <process
        :dialog-visiable="dialogProcess"
        :is-add="isAdd"
        :detail-data="searchForm"
        @selectRow="getProcess"
        @closeDialog="closeProcess"
      />
      <MaterialGroup
        :dialog-visiable="dialogMaterialgroup"
        :detail-data="searchForm"
        :is-add="true"
        @selectRow="getMaterialgroup"
        @closeDialog="closeMaterialgroup"
      />
      <BottomPanel class="bottom-panel">
        <template #panel-content>
          <div style="text-align: right">
            <el-button
              :loading="downloadLoading"
              type="primary"
              size="small"
              @click="exportToExcel()"
              @keyup.prevent
              @keydown.enter.prevent
              >数据导出</el-button
            >
          </div>
        </template>
      </BottomPanel>
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, watch, nextTick, computed, onMounted, getCurrentInstance } from 'vue';
import VChart from '@/components/VChart';
import ListLayout from '@/components/ListLayout';
import { formatDate, formatDateTime } from '@/utils/formatTime';
import { formatTree } from '@/utils/formatJson';
import { getCapabilityTree } from '@/api/user';
import { getDictionary } from '@/api/user';
import { getKeyParamData } from '@/api/order';
import { InspectionTypeAll } from '@/data/industryTerm';
import SPCEightCriterions from '../func/SPCEightCriterions';
import {
  getAxisInterval,
  splitArray,
  sort2DArray,
  getRangeValue,
  getAverageOfArray,
  getSampleNumAverageArray,
  getRangeLCL,
  getRangeUCL,
  getXBarUCL,
  getXBarLCL,
  getFormatArray,
  getStandardDeviation,
  getCapabilityOfAccuracy,
  getCapabilityOfPrecision,
  getCpk,
  getCpkLevel,
  getMedian,
  getModeValue,
  getVarianceValue
} from '../func/calculate';
import { allWeightSet } from '../data/testData';
import { past3Months } from '@/data/dateShortcuts';
import { useStore } from 'vuex';
import SelectKeyParam from '@/components/BusinessComponents/SelectKeyParam';
import Process from '@/components/BusinessComponents/Process';
import MaterialGroup from '@/components/BusinessComponents/MaterialGroup';
import CollapseHeader from '@/views/smart-charts/components/CollapseHeader';
import BottomPanel from '@/components/PageComponents/BottomPanel';
import CustomPanel from '@/components/PageComponents/CustomPanel';
import { ElMessage } from 'element-plus';
import { publicIsNumber } from '@/utils/validate';
import { getNameByid } from '@/utils/common';
import { singleChartTooltipFormatter, SPCChartTooltipFormatter } from './tooltip-formatter';
import emptyImg from '@/assets/img/empty-chart.png';
import emptyTable from '@/assets/img/empty-table.png';
import { getPageRequestParam, setPageRequestParam } from '@/utils/auth';

export default {
  name: 'XBarR',
  components: {
    ListLayout,
    VChart,
    SelectKeyParam,
    Process,
    MaterialGroup,
    CollapseHeader,
    BottomPanel,
    CustomPanel
  },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const store = useStore();
    const processData = reactive({
      dialogProcess: false,
      isAdd: false
    });
    const data = reactive({
      searchForm: {
        // 检验类型
        inspectionType: '',
        inspectionTypeName: '全部',
        // 物资分类
        materialClassification: store.state.user.materialList[0]?.code,
        materialClassificationName: store.state.user.materialList[0]?.name,
        // 电压等级
        voltageLevel: '',
        voltageLevelName: '全部',
        // 物料分组
        materialGroupId: '',
        materialGroupName: '',
        materialGroupNo: '',
        // 工序
        workingProcedureId: '',
        workingProcedureName: '',
        workingProcedureCode: '',
        // 关键参数
        itemKeyParam: '',
        capabilityId: '',
        capabilityParamId: '',
        capabilityParamName: '',
        templateKey: '',
        // 时间范围
        submitTime: [formatDate(new Date().getTime() - 3600 * 1000 * 24 * 90), formatDate(new Date())],
        startTime: formatDate(new Date().getTime() - 3600 * 1000 * 24 * 90),
        endTime: formatDate(new Date()),
        // 子组大小
        subGroupCount: 5,
        // 样本数
        sampleCapacity: 1000,
        // 模糊搜索
        key: ''
      },
      showPanel: false,
      activePanelName: '0',
      submitTime: '',
      showItemDialog: false,
      oldSelectedKeyParam: {},
      treeData: [],
      capabilityKeyParam: {},
      shortcuts: past3Months,
      inspectionTypeOptions: InspectionTypeAll,
      materialGroupOptions: MaterialGroup,
      voltageLevelOptions: [],
      dialogMaterialgroup: false,
      topHeight: 85,
      showChart: false,
      showSingleChart: false,
      showCondition: false,
      xBarLowerLimit: null,
      xBarUpperLimit: null,
      rangeLowerLimit: null,
      rangeUpperLimit: null,
      limitDisabled: true,
      downloadLoading: false,
      renderLoading: false,
      cpk: null,
      warningReferenceList: Object.keys(SPCEightCriterions.criterions).map(
        criterion => `${criterion}. ${SPCEightCriterions.criterions[criterion]}`
      )
    });
    const sampleTable = ref([]);
    const paramTable = ref([]);

    // #region 计算和渲染图表
    const realTimeChartOption = ref({
      title: {
        text: '示例散点图'
      },
      xAxis: {},
      yAxis: {},
      series: [
        {
          data: [],
          type: 'scatter'
        }
      ]
    });

    const xBarChartOption = ref({
      title: {
        text: '示例折线图'
      },
      xAxis: {
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    });

    const rangeChartOption = ref({
      title: {
        text: '示例折线图'
      },
      xAxis: {
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    });

    function renderChart(dataSource = allWeightSet, isShowSingleChart) {
      if (isShowSingleChart) {
        const sampleData = dataSource;
        const sampleValueArray = [];
        const scatterData = [];
        const valueList = [];
        sampleData
          .sort((a, b) => Number(a.capabilityParaValue) - Number(b.capabilityParaValue))
          .forEach((item, index) => {
            const capabilityParaValue = Number(item.capabilityParaValue);
            sampleValueArray.unshift({
              capabilityParaValue: capabilityParaValue,
              finishedDate: item.finishedDate,
              sampleName: item.sampleName
            });
            scatterData.push({
              name: item.sampleName,
              date: item.finishedDate,
              value: [capabilityParaValue, capabilityParaValue]
            });
            valueList.push(capabilityParaValue);
          });
        // 平均数
        const averageValue = getAverageOfArray(valueList);
        // 最大值
        const maxValue = valueList[valueList.length - 1];
        // 最小值
        const minValue = valueList[0];
        // 极差R
        const rangeValue = maxValue - minValue;
        // 众数
        const mostValue = getModeValue(valueList);
        // 中位数
        const medianValue = getMedian(valueList);
        // 方差
        const varianceValue = getVarianceValue(valueList, averageValue);
        // 标准差 σ sigma
        const standardDeviation = getStandardDeviation(valueList);
        const axisInfo = getAxisInterval(valueList[0], valueList[valueList.length - 1], valueList.length);
        const axisMax = parseInt(axisInfo.axisMax * 1.1);
        const axisMin = parseInt(axisInfo.axisMin * 1.1);
        const axistInterval = axisInfo.axistInterval;
        realTimeChartOption.value = {
          title: {
            text: ''
          },
          tooltip: {
            trigger: 'axis',
            extraCssText: 'text-align:left', // 设置tooltip的自定义样式
            textStyle: {
              color: '#303133'
            },
            formatter: params => {
              return singleChartTooltipFormatter(params);
            }
          },
          grid: {
            left: '20',
            right: '20',
            bottom: '10',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            interval: axistInterval,
            splitLine: {
              lineStyle: {
                color: ['#ebeef5']
              }
            },
            min: axisMin,
            max: axisMax
          },
          yAxis: {
            type: 'value',
            interval: axistInterval,
            splitLine: {
              lineStyle: {
                color: ['#ebeef5']
              }
            },
            axisLabel: {
              formatter: function (value) {
                return value.toExponential();
              }
            },
            min: axisMin,
            max: axisMax
          },
          series: [
            {
              name: '数据源',
              type: 'scatter',
              symbolSize: 8,
              data: scatterData
            }
          ]
        };
        paramTable.value = [
          { label: '样本数', value: dataSource.length },
          { label: '子组大小n', value: data.searchForm.subGroupCount },
          { label: '子组m', value: dataSource.length },
          { label: '样本总均值CL(X)', value: averageValue ? parseFloat(averageValue.toExponential(6)) : '' },
          { label: '样本极差CL(R)', value: rangeValue ? parseFloat(rangeValue.toExponential(6)) : '' },
          { label: '最大值Max', value: maxValue ? parseFloat(maxValue.toExponential(6)) : '' },
          { label: '最小值Min', value: minValue ? parseFloat(minValue.toExponential(6)) : '' },
          { label: '众数', value: mostValue ? parseFloat(mostValue.toExponential(6)) : '' },
          { label: '中位数', value: medianValue ? parseFloat(medianValue.toExponential(6)) : '' },
          { label: '方差值', value: varianceValue ? parseFloat(varianceValue.toExponential(6)) : '' },
          { label: '标准差σ', value: standardDeviation ? standardDeviation.toExponential(1) : '' },
          { label: '', value: '' }
        ];
        sampleTable.value = sampleValueArray.sort(
          (a, b) => new Date(b.finishedDate).getTime() - new Date(a.finishedDate).getTime()
        );
      } else {
        const sampleData = splitArray(dataSource, data.searchForm.subGroupCount);
        const groupCount =
          (dataSource.length - (dataSource.length % data.searchForm.subGroupCount)) / data.searchForm.subGroupCount;
        const xAxisArray = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'];
        if (groupCount > 10) {
          for (let i = 11; i <= groupCount; i++) {
            xAxisArray.push(i.toString());
          }
        }

        // 范围数平均值
        const newSet = JSON.parse(JSON.stringify(sampleData));
        const sortedArray = sort2DArray(newSet);
        const rangeValueArray = getRangeValue(sortedArray);
        const rAverage = getAverageOfArray(rangeValueArray);
        // 样品平均值
        const sampleNumAverageArray = getSampleNumAverageArray(newSet);
        const xAverageAverage = getAverageOfArray(sampleNumAverageArray);

        const xUclValue = Number(getXBarUCL(xAverageAverage, rAverage, 5).toFixed(1));
        const xUclArray = getFormatArray(xUclValue, groupCount);
        let xUpperArray = JSON.parse(JSON.stringify(xUclArray));
        const xClValue = xAverageAverage;
        const xClArray = getFormatArray(xClValue, groupCount);
        const xLclValue = Number(getXBarLCL(xAverageAverage, rAverage, 5).toFixed(1));
        const xLclArray = getFormatArray(xLclValue, groupCount);
        let xLowerArray = JSON.parse(JSON.stringify(xLclArray));
        if (publicIsNumber(data.xBarUpperLimit)) {
          xUpperArray = getFormatArray(Number(data.xBarUpperLimit), groupCount);
        } else {
          data.xBarUpperLimit = xUpperArray[0];
        }
        if (publicIsNumber(data.xBarLowerLimit)) {
          xLowerArray = getFormatArray(Number(data.xBarLowerLimit), groupCount);
        } else {
          data.xBarLowerLimit = xLowerArray[0];
        }

        const rUclValue = Number(getRangeUCL(rAverage, 5).toFixed(1));
        const rUclArray = getFormatArray(rUclValue, groupCount);
        let rangeUpperArray = JSON.parse(JSON.stringify(rUclArray));
        const rClValue = rAverage;
        const rClArray = getFormatArray(rClValue, groupCount);
        const rLclValue = Number(getRangeLCL(rAverage, 5).toFixed(1));
        const rLclArray = getFormatArray(rLclValue, groupCount);
        let rangeLowerArray = JSON.parse(JSON.stringify(rLclArray));
        if (publicIsNumber(data.rangeUpperLimit)) {
          rangeUpperArray = getFormatArray(Number(data.rangeUpperLimit), groupCount);
        } else {
          data.rangeUpperLimit = rangeUpperArray[0];
        }
        if (publicIsNumber(data.rangeLowerLimit)) {
          rangeLowerArray = getFormatArray(Number(data.rangeLowerLimit), groupCount);
        } else {
          data.rangeLowerLimit = rangeLowerArray[0];
        }

        let allSampleArray = [];
        sampleTable.value = [];
        paramTable.value = [];

        let xbarIntervalCount = sampleNumAverageArray.length;
        const sortedXBarArray = JSON.parse(JSON.stringify(sampleNumAverageArray)).sort((a, b) => a - b);
        let xbarAxisInfo = getAxisInterval(
          sortedXBarArray[0],
          sortedXBarArray[sortedXBarArray.length - 1],
          xbarIntervalCount
        );
        let xBarMin = xbarAxisInfo.axisMin;
        let xBarMax = xbarAxisInfo.axisMax;
        let xBarInterval = xbarAxisInfo.axistInterval;
        xbarIntervalCount = xbarAxisInfo.intervalCount;
        if (xBarInterval > 0) {
          while (xBarMax < xUclValue || xBarMax < xUpperArray[0]) {
            xBarMax += xBarInterval;
          }
          while (xBarMin > xLowerArray[0]) {
            xBarMin -= xBarInterval;
          }
        }
        xbarAxisInfo = getAxisInterval(xBarMin, xBarMax, xbarIntervalCount);
        xBarMin = xbarAxisInfo.axisMin;
        xBarMax = xbarAxisInfo.axisMax;
        xBarInterval = xbarAxisInfo.axistInterval;
        xbarIntervalCount = xbarAxisInfo.intervalCount;

        let rangeIntervalCount = rangeValueArray.length;
        const sortedRangeArray = JSON.parse(JSON.stringify(rangeValueArray)).sort((a, b) => a - b);
        let axisInfo = getAxisInterval(
          sortedRangeArray[0],
          sortedRangeArray[sortedRangeArray.length - 1],
          rangeIntervalCount
        );
        let rangeMin = axisInfo.axisMin;
        let rangeMax = axisInfo.axisMax;
        let rangeInterval = axisInfo.axistInterval;
        rangeIntervalCount = axisInfo.intervalCount;
        if (xBarInterval > 0) {
          while (rangeMax < rUclValue || rangeMax < rangeUpperArray[0]) {
            rangeMax += rangeInterval;
          }
          while (rangeMin > rangeLowerArray[0]) {
            rangeMin -= rangeInterval;
          }
        }
        axisInfo = getAxisInterval(rangeMin, rangeMax, rangeIntervalCount);
        rangeMin = axisInfo.axisMin;
        rangeMax = axisInfo.axisMax;
        rangeInterval = axisInfo.axistInterval;
        rangeIntervalCount = axisInfo.intervalCount;

        for (let i = 0; i < groupCount; i++) {
          sampleTable.value.push({
            sample: dataSource[i],
            sampleAverage: sampleNumAverageArray[i],
            sampleRange: rangeValueArray[i]
          });
          allSampleArray = allSampleArray.concat(dataSource[i]);
        }
        // 标准差 σ sigma
        const standardDeviation = getStandardDeviation(allSampleArray).toFixed(1);
        if (publicIsNumber(data.xBarLowerLimit) && publicIsNumber(data.xBarUpperLimit)) {
          const ca = getCapabilityOfAccuracy(
            xAverageAverage,
            getAverageOfArray([Number(data.xBarLowerLimit), Number(data.xBarUpperLimit)]),
            Number(data.xBarUpperLimit),
            Number(data.xBarLowerLimit)
          );
          const cp = getCapabilityOfPrecision(
            Number(data.xBarUpperLimit),
            Number(data.xBarLowerLimit),
            Number(standardDeviation)
          );
          data.cpk = getCpk(cp, ca).toFixed(6);
        }
        const XBarCriteriaList = new SPCEightCriterions().getPassedCriterions({
          values: sampleNumAverageArray,
          UCL: xUclValue,
          CL: xClValue,
          LCL: xLclValue,
          sigma: Number(standardDeviation)
        });
        const rangeCriteriaList = new SPCEightCriterions().getPassedCriterions({
          values: rangeValueArray,
          UCL: rUclValue,
          CL: rClValue,
          LCL: rLclValue,
          sigma: Number(standardDeviation)
        });
        let XBarWarningList = [];
        let rangeWarningList = [];
        if (XBarCriteriaList) {
          XBarWarningList = sampleNumAverageArray.map((value, index) =>
            XBarCriteriaList.filter(item => item.durationStartIndex <= index && item.durationEndIndex >= index)
          );
        }
        if (rangeCriteriaList) {
          rangeWarningList = sampleNumAverageArray.map((value, index) =>
            rangeCriteriaList.filter(item => item.durationStartIndex <= index && item.durationEndIndex >= index)
          );
        }
        const cpkLevel = getCpkLevel(Number(data.cpk)) ?? '无';
        const operationList = [
          { label: '样本数', value: dataSource.length.toExponential() },
          { label: '子组大小n', value: Number(data.searchForm.subGroupCount).toExponential() },
          { label: '子组m', value: Number(groupCount).toExponential() },
          { label: '样本总均值CL(X)', value: Number(xClValue).toExponential(6) },
          { label: '平均样本极差CL(R)', value: Number(rClValue).toExponential(6) },
          { label: 'CPK值', value: Number(data.cpk).toExponential() },
          { label: 'UCL(X)', value: Number(xUclValue).toExponential() },
          { label: 'LCL(X)', value: Number(xLclValue).toExponential() },
          { label: 'UCL(R)', value: Number(rUclValue).toExponential() },
          { label: 'LCL(R)', value: Number(rLclValue).toExponential() },
          { label: '标准差σ', value: Number(standardDeviation).toExponential() },
          { label: 'CPK等级', value: cpkLevel }
        ];

        paramTable.value = operationList;
        const seriseXBarChartData = sampleNumAverageArray.map((item, index) => {
          return {
            value: item,
            itemStyle: XBarWarningList[index]?.length ? { color: 'red' } : { color: '#00b38a' } // 拐点颜色
          };
        });
        xBarChartOption.value = {
          color: ['#9FCEFF', '#00B38A', '#80D9C5', '#B3E09C', '#F2D09D', '#f9b5b5'],
          title: {
            text: ''
          },
          tooltip: {
            trigger: 'axis',
            extraCssText: 'text-align:left', // 设置tooltip的自定义样式
            textStyle: {
              color: '#303133'
            },
            formatter: params => {
              return SPCChartTooltipFormatter(params, XBarWarningList);
            }
          },
          legend: {
            data: ['UCL', '均值X', 'CL', 'LCL', '标准下限', '标准上限']
          },
          grid: {
            left: '20',
            right: '20',
            bottom: '10',
            containLabel: true
          },
          toolbox: {
            // feature: {
            //   saveAsImage: {}
            // }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            axisTick: {
              show: false
            },
            data: xAxisArray
          },
          yAxis: {
            type: 'value',
            interval: xBarInterval,
            splitLine: {
              lineStyle: {
                color: ['#ebeef5']
              }
            },
            axisLabel: {
              formatter: function (value) {
                return value.toExponential();
              }
            },
            min: xBarMin,
            max: xBarMax
          },
          series: [
            {
              name: 'UCL',
              type: 'line',
              symbol: 'none',
              smooth: false,
              data: xUclArray
            },
            {
              name: '均值X',
              type: 'line',
              symbol: 'circle',
              symbolSize: 10,
              itemStyle: {
                borderWidth: 2,
                borderColor: '#ffffff'
              },
              lineStyle: {
                width: 3,
                shadowColor: 'rgba(0, 179, 138, 0.3)',
                shadowBlur: 6,
                shadowOffsetY: 8
              },
              data: seriseXBarChartData
            },
            {
              name: 'CL',
              type: 'line',
              symbol: 'none',
              data: xClArray
            },
            {
              name: 'LCL',
              type: 'line',
              symbol: 'none',
              data: xLclArray
            },
            {
              name: '标准下限',
              type: 'line',
              symbol: 'none',
              data: xLowerArray
            },
            {
              name: '标准上限',
              type: 'line',
              symbol: 'none',
              data: xUpperArray
            }
          ]
        };
        const seriseRangeChartData = rangeValueArray.map((item, index) => {
          return {
            value: item,
            itemStyle: rangeWarningList[index]?.length ? { color: 'red' } : { color: '#00b38a' } // 拐点颜色
          };
        });
        rangeChartOption.value = {
          color: ['#9FCEFF', '#00B38A', '#80D9C5', '#B3E09C', '#F2D09D', '#f9b5b5'],
          title: {
            text: ''
          },
          tooltip: {
            trigger: 'axis',
            extraCssText: 'text-align:left', // 设置tooltip的自定义样式
            textStyle: {
              color: '#303133'
            },
            formatter: params => {
              return SPCChartTooltipFormatter(params, rangeWarningList);
            }
          },
          legend: {
            data: ['UCL', '极差R', 'CL', 'LCL', '标准下限', '标准上限']
          },
          grid: {
            left: '20',
            right: '20',
            bottom: '10',
            containLabel: true
          },
          toolbox: {
            // feature: {
            //   saveAsImage: {}
            // }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            axisTick: {
              show: false
            },
            data: xAxisArray
          },
          yAxis: {
            type: 'value',
            interval: rangeInterval,
            axisLabel: {
              formatter: function (value) {
                return value.toExponential();
              }
            },
            splitLine: {
              lineStyle: {
                color: ['#ebeef5']
              }
            },
            min: rangeMin,
            max: rangeMax
          },
          series: [
            {
              name: 'UCL',
              type: 'line',
              symbol: 'none',
              data: rUclArray
            },
            {
              name: '极差R',
              type: 'line',
              symbol: 'circle',
              symbolSize: 10,
              itemStyle: {
                borderWidth: 2,
                borderColor: '#ffffff'
              },
              lineStyle: {
                width: 3,
                shadowColor: 'rgba(0, 179, 138, 0.3)',
                shadowBlur: 6,
                shadowOffsetY: 8
              },
              data: seriseRangeChartData
            },
            {
              name: 'CL',
              type: 'line',
              symbol: 'none',
              data: rClArray
            },
            {
              name: 'LCL',
              type: 'line',
              symbol: 'none',
              data: rLclArray
            },
            {
              name: '标准下限',
              type: 'line',
              symbol: 'none',
              data: rangeLowerArray
            },
            {
              name: '标准上限',
              type: 'line',
              symbol: 'none',
              data: rangeUpperArray
            }
          ]
        };
      }
    }

    // #endregion

    // #region 查询条件表单
    const setMainOffset = height => {
      data.topHeight = height;
    };

    const showCollapse = value => {
      data.limitDisabled = !value;
      if (value) {
        data.xBarLowerLimit = '';
        data.xBarUpperLimit = '';
        data.rangeLowerLimit = '';
        data.rangeUpperLimit = '';
      }
    };

    const getVoltageLevel = () => {
      getDictionary(2).then(res => {
        data.voltageLevelOptions = [{ code: '', name: '全部', status: 1 }].concat(res.data.data?.dictionaryoption);
      });
    };

    const materialClassifications = computed({
      get: () => store.state.user.materialList
    });

    const closeProcess = value => {
      processData.dialogProcess = false;
    };
    const closeMaterialgroup = value => {
      data.dialogMaterialgroup = false;
    };

    const deleteProcess = () => {
      data.searchForm.workingProcedureId = '';
      data.searchForm.workingProcedureName = '';
      data.searchForm.workingProcedureCode = '';
    };

    const deleteKeyParam = () => {
      data.searchForm.itemKeyParam = '';
      data.searchForm.capabilityId = '';
      data.searchForm.capabilityParamId = '';
      data.searchForm.capabilityParamName = '';
      data.searchForm.templateKey = '';
    };

    const deleteMaterialgroup = () => {
      data.searchForm.materialGroupId = '';
      data.searchForm.materialGroupNo = '';
      data.searchForm.materialGroupName = '';
    };

    const handleProcess = () => {
      processData.dialogProcess = true;
    };

    const handleMaterialgroup = () => {
      data.dialogMaterialgroup = true;
    };

    const handleDatePicker = value => {
      if (value) {
        data.expired = false;
        data.searchForm.startTime = formatDate(value[0]);
        data.searchForm.endTime = formatDate(value[1]);
      } else {
        data.searchForm.startTime = '';
        data.searchForm.endTime = '';
      }
    };

    const getProcess = value => {
      processData.dialogProcess = false;
      if (data.searchForm.workingProcedureId && value.workingProcedureId !== data.formData.workingProcedureId) {
        data.searchForm.workingProcedureId = '';
        data.searchForm.workingProcedureName = '';
        data.searchForm.workingProcedureCode = '';
      }
      data.searchForm = { ...data.searchForm, ...value };
    };

    const getMaterialgroup = value => {
      data.dialogMaterialgroup = false;
      if (data.searchForm.materialGroupId && value.materialGroupId !== data.formData.materialGroupId) {
        data.searchForm.materialGroupId = '';
        data.searchForm.materialGroupName = '';
        data.searchForm.materialGroupNo = '';
      }
      data.searchForm = { ...data.searchForm, ...value };
    };

    // 选择关键参数
    const selectItems = () => {
      data.showItemDialog = true;
    };
    // 关闭关键参数弹出框
    const closeDialog = () => {
      data.showItemDialog = false;
    };

    // 获取关键参数
    const selectedKeyParam = val => {
      data.capabilityKeyParam = val;
      data.searchForm.capabilityId = data.capabilityKeyParam.capabilityid;
      data.searchForm.capabilityParamId = data.capabilityKeyParam.id;
      data.searchForm.capabilityParamName = data.capabilityKeyParam.name;
      data.searchForm.templateKey = data.capabilityKeyParam.templatekey;
      data.searchForm.itemKeyParam = data.capabilityKeyParam.name
        ? `${data.capabilityKeyParam.name}-${data.capabilityKeyParam.templatekey}`
        : '';
      data.oldSelectedKeyParam = JSON.parse(JSON.stringify(data.capabilityKeyParam));
    };

    const getTree = mateType => {
      getCapabilityTree(mateType).then(response => {
        if (response.data.code === 200) {
          const result = response.data.data;
          data.treeData = formatTree(result);
          data.treeData.unshift({
            id: '-1',
            parentId: '0',
            materialCategoryCode:
              result.length > 0 ? result[0].materialCategoryCode : data.searchForm.materialClassification,
            name: '全部',
            order: 0,
            status: 2
          });
        }
      });
    };

    const changeSubGroup = value => {
      data.searchForm.subGroupCount = value;
    };

    const changeSearchKey = value => {
      data.searchForm.key = value;
    };

    const getInspectionTypeList = () => {
      getDictionary('JYLX').then(res => {
        data.inspectionTypeOptions = [];
        res.data.data?.dictionaryoption.forEach(item => {
          if (item.status === 1) {
            data.inspectionTypeOptions.push(item);
          }
        });
        data.inspectionTypeOptions.unshift({ id: '', code: '', name: '全部', status: 1 });
      });
    };

    const changeInspectionType = value => {
      data.searchForm.inspectionTypeName = getInspectionTypeName(value);
    };

    function getInspectionTypeName(code) {
      const inspectionIndex = data.inspectionTypeOptions.findIndex(item => item.code === code);
      return inspectionIndex === -1 ? '' : data.inspectionTypeOptions[inspectionIndex].name;
    }

    const changeMaterialClassification = value => {
      if (value) {
        materialClassifications.value.forEach(item => {
          if (item.code === value) {
            data.searchForm.materialClassificationName = item.name;
          }
        });
      } else {
        data.searchForm.materialClassificationName = '';
      }
    };

    const changeVoltageLevel = value => {
      data.searchForm.voltageLevelName = getVoltageLevelName(value);
    };

    function getVoltageLevelName(code) {
      const voltageIndex = data.voltageLevelOptions.findIndex(item => item.code === code);
      return voltageIndex === -1 ? '' : data.voltageLevelOptions[voltageIndex].name;
    }

    function getSearchFormParams() {
      if (!data.searchForm.materialClassification) {
        ElMessage.warning({
          message: '请先选择物资分类',
          type: 'warning'
        });
        return false;
      }
      if (!data.searchForm.capabilityId) {
        ElMessage.warning({
          message: '请先选择关键参数',
          type: 'warning'
        });
        return false;
      }
      if (!data.searchForm.startTime || !data.searchForm.endTime) {
        ElMessage.warning({
          message: '请先选择时间范围',
          type: 'warning'
        });
        return false;
      }
      return {
        capabilityId: data.searchForm.capabilityId,
        capabilityParaId: data.searchForm.capabilityParamId,
        capabilityParaName: data.searchForm.capabilityParamName,
        startDate: data.searchForm.startTime,
        endDate: data.searchForm.endTime,
        mateType: data.searchForm.materialClassification,
        materialGroupNo: data.searchForm.materialGroupNo,
        productionProcedureNo: data.searchForm.workingProcedureCode,
        templateKey: data.searchForm.templateKey,
        type: data.searchForm.inspectionType,
        voltName: data.searchForm.voltageLevel,
        sampleCapacity: data.searchForm.sampleCapacity,
        key: data.searchForm.key
      };
    }

    const setQueryParams = () => {
      const localStorageParams = getPageRequestParam() ? JSON.parse(getPageRequestParam()) : {};
      localStorageParams['StatisticalProcessControl'] = data.searchForm;
      setPageRequestParam(localStorageParams);
    };
    const renderSpcData = () => {
      const keyParamPostBody = getSearchFormParams();
      if (!keyParamPostBody) {
        return;
      }
      data.showCondition = true;
      const keyParamValueArray = [];
      data.renderLoading = true;
      getKeyParamData(keyParamPostBody)
        .then(res => {
          setQueryParams();
          if (res.data.code === 200 && res.data.data.length > 0) {
            res.data.data.forEach(item => {
              if (!isNaN(Number(item.capabilityParaValue))) {
                keyParamValueArray.push(Number(item.capabilityParaValue));
              }
            });
            if (keyParamValueArray.length < data.searchForm.subGroupCount) {
              ElMessage.warning({
                message: `该关键参数的历史数据数量为${keyParamValueArray.length}, 小于子组数${data.searchForm.subGroupCount}!`,
                type: 'warning'
              });
              return;
            } else {
              const isSubGroupCountEqualOne = data.searchForm.subGroupCount === 1;
              if (isSubGroupCountEqualOne) {
                data.showSingleChart = true;
                data.showChart = false;
              } else {
                data.showSingleChart = false;
                data.showChart = true;
              }
              proxy.$refs.collapse.handleCollapse();
              renderChart(isSubGroupCountEqualOne ? res.data.data : keyParamValueArray, isSubGroupCountEqualOne);
            }
          } else {
            ElMessage.warning({
              message: '查找不到该关键参数的历史数据',
              type: 'warning'
            });
          }
        })
        .catch(err => {
          ElMessage.error({
            message: `${err.message}`,
            type: 'error'
          });
        })
        .finally(() => {
          data.renderLoading = false;
        });
    };

    const renderSampleData = () => {
      data.showChart = true;
      data.showCondition = false;
      proxy.$refs.collapse.handleCollapse();
      renderChart();
      // const XBarCriteriaInstance = new SPCEightCriterions()
      // // const XBarTestArray = [110, 37, 38, 3, 37, 35, 38, 33, 39, 19, 24, 21, 29, 25, 27, 10, 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9, 11, 11.1, 11.2, 11.3, 11.4]
      // const XBarTestArray = []
      // const XBarTestArray1 = [110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110, 110] // 1
      // // const XBarTestArray1 = [76, 77, 78, 26, 80, 81, 82, 26, 78, 76, 77, 78] // 3
      // let index = 0
      // const timer = setInterval(() => {
      //   if (XBarTestArray1[index]) {
      //     XBarTestArray.push(XBarTestArray1[index])
      //     // eslint-disable-next-line
      //     const XBarCriteriaList = XBarCriteriaInstance.getPassedCriterions({ values: XBarTestArray, UCL: 100, CL: 50, LCL: 0, sigma: 25 })
      //     if (XBarCriteriaList) {
      //       console.log('XBarCriteriaList', XBarCriteriaList)
      //     }
      //   } else {
      //     clearInterval(timer)
      //   }
      //   index++
      // }, 1000)
    };

    const cancelRender = () => {
      data.showChart = false;
      data.showCondition = false;
      proxy.$refs.collapse.handleCollapse();
    };

    watch(
      () => data.showItemDialog,
      newValue => {
        data.showDialog = newValue;
        if (newValue) {
          data.addList = JSON.parse(JSON.stringify(data.capabilityKeyParam));
          nextTick(() => {
            if (data.searchForm.materialClassification) {
              getTree(data.searchForm.materialClassification);
            } else {
              ElMessage.warning({
                message: '请先选择物资分类！',
                type: 'warning'
              });
            }
          });
        }
      },
      { deep: true }
    );

    const exportToExcel = () => {
      const keyParamPostBody = getSearchFormParams();
      if (!keyParamPostBody) {
        return;
      }
      data.downloadLoading = true;
      getKeyParamData(keyParamPostBody)
        .then(res => {
          if (res.data.code === 200) {
            const result = res.data.data;
            if (result.length > 0) {
              const workSheets = [];
              import('@/utils/Export2Excel').then(excel => {
                if (result.length > 0) {
                  for (let i = 0; i < result.length; i++) {
                    result[i].ownerName = `${getNameByid(result[i].ownerId)}`;
                    result[i].voltageLevelName = `${result[i].voltName ? getVoltageLevelName(result[i].voltName) : ''}`;
                  }
                  const sampleHeader = [
                    '样品编号',
                    '物料分组',
                    '试验日期',
                    '物料编号',
                    '样品名称',
                    '规格型号',
                    '电压等级',
                    '批次',
                    '盘号',
                    '实验负责人',
                    '检验对象',
                    '对象位置',
                    '对象名称',
                    '项目',
                    '关键参数',
                    '线芯分组',
                    '关键参数实际值'
                  ];
                  const sampleFilterVal = [
                    'secSampleNum',
                    'materialGroup',
                    'startDate',
                    'materialNo',
                    'sampleName',
                    'prodType',
                    'voltageLevelName',
                    'batchNo',
                    'reelNo',
                    'ownerName',
                    'experimentObject',
                    'objectPosition',
                    'objectName',
                    'capabilityName',
                    'capabilityParaName',
                    'color',
                    'capabilityParaValue'
                  ];
                  const sampleExcelData = formatExcelData(sampleFilterVal, result);
                  const sampleSheet = excel.getWorkSheet({
                    header: sampleHeader,
                    data: sampleExcelData,
                    wsName: `样品信息`
                  });
                  workSheets.push(sampleSheet);
                }
                excel.exportMultiSheetExcel(workSheets, `LIMS-SPC控制图 ${formatDateTime()}`);
              });
            } else {
              ElMessage.warning({
                message: '暂无可导出的的数据!',
                type: 'warning'
              });
              return;
            }
          }
        })
        .catch(err => {
          ElMessage.error({
            message: `${err.message}`,
            type: 'error'
          });
        })
        .finally(() => {
          data.downloadLoading = false;
        });
    };

    function formatExcelData(filterVal, jsonData) {
      return jsonData.map(v =>
        filterVal.map(j => {
          return v[j];
        })
      );
    }

    onMounted(() => {
      getVoltageLevel();
      getInspectionTypeList();
      const localStorageParams = getPageRequestParam() ? JSON.parse(getPageRequestParam()) : undefined;
      if (localStorageParams?.['StatisticalProcessControl']) {
        data.searchForm = localStorageParams['StatisticalProcessControl'];
        renderSpcData();
      }
    });
    return {
      ...toRefs(data),
      ...toRefs(processData),
      paramTable,
      sampleTable,
      realTimeChartOption,
      rangeChartOption,
      xBarChartOption,
      materialClassifications,
      renderChart,
      handleDatePicker,
      selectItems,
      closeDialog,
      selectedKeyParam,
      getTree,
      getProcess,
      closeProcess,
      deleteProcess,
      handleProcess,
      deleteMaterialgroup,
      handleMaterialgroup,
      getMaterialgroup,
      closeMaterialgroup,
      getVoltageLevel,
      deleteKeyParam,
      changeSubGroup,
      changeSearchKey,
      setMainOffset,
      showCollapse,
      renderSpcData,
      changeInspectionType,
      changeMaterialClassification,
      changeVoltageLevel,
      renderSampleData,
      cancelRender,
      emptyImg,
      emptyTable,
      exportToExcel
    };
  }
};
</script>
<style lang="scss" scoped>
.panel-header-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.el-icon-info {
  color: var(--tesPrimary);
  font-size: 18px;
  cursor: pointer;
}
.chart-alert {
  text-align: left;
  line-height: 1;
  padding-left: 0;
}
.last-panel {
  margin: 0 24px 12px !important;
}
.bottom-panel .panel-content {
  padding: 10px 24px !important;
}
</style>
