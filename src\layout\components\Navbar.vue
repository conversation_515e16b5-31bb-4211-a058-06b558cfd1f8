<template>
  <div class="navbar">
    <hamburger
      id="hamburger-container"
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />
    <div class="right-menu">
      <div class="message-main">
        <el-badge :is-dot="isDot" :value="messageNum" :max="99" class="item">
          <el-button :is-dot="isDot1" size="mini" icon="el-icon-bell" circle @click="messageInfo(0)" />
        </el-badge>
        <el-badge :value="messageNum1" :max="99" class="item">
          <el-button size="mini" icon="el-icon-alarm-clock" circle @click="messageInfo(1)" />
        </el-badge>
      </div>
      <el-divider class="divider" direction="vertical" />
      <span class="user-name"
        ><span class="name-icon">{{ accountStr }}</span
        >{{ getNameByid(accountId) }}</span
      >
      <el-divider class="divider" direction="vertical" />
      <div style="float: right" @click="logout">退出</div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
import { getNameByid } from '@/utils/common';
import { reactive, toRefs } from 'vue';
// import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger';
// import ErrorLog from '@/components/ErrorLog'
// import Screenfull from '@/components/Screenfull'
// import SizeSelect from '@/components/SizeSelect'
// import Search from '@/components/HeaderSearch'
import { makePy } from '@/utils/chinese-to-str';
// import { ElMessage } from 'element-plus'
import { getMessageNum } from '@/api/messageAgent';
import router from '@/router';

export default {
  components: {
    Hamburger
  },
  setup() {
    const datas = reactive({
      accountId: '',
      accountStr: '',
      messageNum: 0,
      messageNum1: 0,
      isDot: false,
      isDot1: false
    });
    datas.accountId = getLoginInfo().accountId;
    const name = getLoginInfo().username;
    datas.accountStr = makePy(name.charAt(0))[0].toUpperCase();
    // console.log(datas.accountStr)
    // 开多个浏览器需要实时更新对应数据
    var dataInterval = setInterval(() => {
      if (getLoginInfo() === null) {
        clearInterval(dataInterval);
      }
      if (datas.accountId && getLoginInfo() && datas.accountId !== getLoginInfo().accountId) {
        // console.log(window)
        window.location.reload();
      }
    }, 5000);

    const messageInfo = flag => {
      // ElMessage.warning('该功能待开放')
      if (flag === 0) {
        router.push({ name: 'MyMessage' });
      } else {
        router.push({ name: 'MyAgency' });
      }
    };

    return { ...toRefs(datas), getNameByid, messageInfo };
  },
  computed: {
    ...mapGetters(['sidebar', 'avatar', 'device', 'name'])
  },
  created() {
    this.getMessageNums();
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar');
    },
    async logout() {
      // console.log(this.$store)
      this.$router.push(`/login`);
      await this.$store.dispatch('user/logout');
    },
    getMessageNums() {
      var that = this;
      getMessageNum().then(res => {
        if (res !== false) {
          // console.log(res.data.data)
          const numList = res.data.data;
          that.messageNum = numList.notReadMessageNum;
          that.messageNum1 = numList.notHandleTodoNum;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 40px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.04);

  .hamburger-container {
    line-height: 40px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 40px;
    margin-right: 20px;
    font-size: 14px;
    display: flex;
    align-items: center;
    &:focus {
      outline: none;
    }
    .user-avatar:hover {
      cursor: pointer;
      color: $tes-primary;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
          span {
            font-size: 12px;
          }
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
    .message-main {
      color: #b5bbc0;
      float: right;
      .item {
        margin-right: 30px;
        .el-button {
          i {
            font-size: 15px;
          }
        }
        :deep(.el-badge__content.is-dot) {
          right: 10px;
          top: 8px;
          background: $tes-red;
        }
        :deep(.el-badge__content.is-fixed) {
          top: 8px;
          background: $tes-red;
          line-height: 15px;
        }
      }
    }
    .divider {
      float: right;
    }
  }
  .user-name {
    height: 40px;
    line-height: 40px;
    float: right;
    display: flex;
    align-items: center;
    .name-icon {
      height: 26px;
      width: 26px;
      display: block;
      float: left;
      line-height: 27px;
      background: linear-gradient(270deg, #1b87e2 1.51%, #48acff 100%);
      border-radius: 20px;
      color: #ffffff;
      margin-right: 10px;
    }
  }
}
</style>
