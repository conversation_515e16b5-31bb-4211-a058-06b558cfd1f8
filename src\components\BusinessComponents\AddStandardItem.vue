<template>
  <el-dialog
    v-model="showDialog"
    custom-class="custom-dialog"
    title="添加标准项目"
    width="60%"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <div class="dialog-header">
      <div class="header-left">
        <span style="margin-right: 5px">产品标准:</span> <span>{{ latestStandardProduct.productName }}</span>
      </div>
      <div class="header-right">
        <el-button size="small" @click="selectNone(newTreeDetail)" @keyup.prevent @keydown.enter.prevent
          >反选</el-button
        >
        <el-button size="small" @click="selectAll(newTreeDetail)" @keyup.prevent @keydown.enter.prevent>全选</el-button>
      </div>
    </div>
    <div class="dialog-content">
      <el-row>
        <el-col :span="6">
          <div class="tree-container">
            <div class="tree-content">
              <el-tree
                ref="treeRef"
                :data="tree"
                node-key="id"
                :props="defaultProps"
                default-expand-all
                :expand-on-click-node="false"
                :highlight-current="true"
                draggable
                class="leftTree"
                @node-click="clickNode"
              >
                <template #default="{ node }">
                  <span>{{ node.label }}</span>
                </template>
              </el-tree>
            </div>
          </div>
        </el-col>
        <el-col :span="18">
          <div v-loading="loading" class="list-container">
            <el-row
              v-for="(item, index) in newTreeDetail"
              :key="index"
              class="item-content"
              style="width: 100%"
              @click="changeCheckBox(item)"
            >
              <el-col :span="1">
                <div class="left">
                  <el-checkbox v-model="item.checked" :disabled="item.disabled" @change="changeCheckBox(item, 1)" />
                </div>
              </el-col>
              <el-col :span="1">
                <div :key="index" style="line-height: 100%">
                  <span>{{ index + 1 }}</span>
                </div>
              </el-col>
              <el-col :span="22">
                <div class="main">
                  <div class="title" :class="{ 'title-checked': item.checked }">
                    {{ item.name }}
                  </div>
                  <div class="item-list">
                    <el-tag
                      v-for="(list, index1) in item.capabilityparaVoList"
                      :key="index1"
                      :type="item.checked ? 'primary' : 'info'"
                      >{{ list.name }}</el-tag
                    >
                  </div>
                </div>
              </el-col>
              <!-- <el-col :span="1">
                <div style="float: right;">
                  <i
                    v-if="item.standardProductVersionId"
                    style="color: green;"
                    class="el-icon--right el-icon-document-checked"
                  /><i
                    v-else
                    style="color: red;"
                    class="el-icon--right el-icon-document-delete"
                  />
                </div>
              </el-col> -->
            </el-row>
            <el-empty v-if="newTreeDetail.length === 0" :image="emptyImg" description="暂无数据" />
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="dialog-other">
      <div class="title">
        <label>已选项目</label>
        <el-button v-if="tags.length > 0" size="small" icon="el-icon-delete" @click="clear">清空</el-button>
      </div>
      <div v-if="oldTags.length > 0 || tags.length > 0" class="select-items">
        <el-tag
          v-for="tag in oldTags"
          :key="tag.name || tag.sourceName"
          :closable="tag.closable"
          size="small"
          @close="closeTag(tag)"
        >
          {{ tag.name || tag.sourceName }}
        </el-tag>
        <el-tag v-for="tag in tags" :key="tag.name || tag.sourceName" closable size="small" @close="closeTag(tag)">
          {{ tag.name || tag.sourceName }}
        </el-tag>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button key="cancel" @click="close">取 消</el-button>
        <el-button key="return" @click="close">上一步</el-button>
        <el-button type="primary" @click="dialogSuccess">确定选择</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, inject } from 'vue';
import { getItemList } from '@/api/testBase';
import _ from 'lodash';
import emptyImg from '@/assets/img/empty-data.png';
// import { ElLoading } from 'element-plus'
// import { useRoute } from 'vue-router'
// import { formatPaginationList } from '@/utils/formatJson'

export default {
  name: 'AddStandardItem',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    },
    tree: {
      type: Object,
      default: function () {
        return {};
      }
    },
    latestStandardProduct: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'selectData'],
  setup(props, context) {
    const lodash = inject('_');
    const datas = reactive({
      showDialog: props.show,
      filterText: '',
      pageType: '',
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      tags: [],
      oldTags: [],
      treeDetail: [],
      newTreeDetail: [],
      loading: false,
      isRefresh: true
    });

    watch(props, newValue => {
      datas.showDialog = newValue.show;
      if (datas.showDialog) {
        datas.filterText = '';
      }
      if (datas.isRefresh === false) {
        return false;
      }
      datas.tags = [];
      datas.oldTags = JSON.parse(JSON.stringify(newValue.data));
      if (newValue.show && newValue.tree && newValue.tree.length > 0 && newValue.latestStandardProduct) {
        getStandardItemList(newValue.tree[0].id, newValue.latestStandardProduct.id);
        datas.isRefresh = false;
      }
    });

    // 过滤树节点
    const treeRef = ref(null);
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    // 点击树节点
    const clickNode = (data, node) => {
      getStandardItemList(data.id, props.latestStandardProduct.id);
    };

    // 确定选择
    const dialogSuccess = () => {
      context.emit('selectData', datas.tags);
      datas.showDialog = false;
      datas.isRefresh = true;
      context.emit('close', false);
    };

    // 取消选择
    const close = () => {
      datas.showDialog = false;
      datas.isRefresh = true;
      // context.emit('close', false)
    };
    // changeCheckBox
    const changeCheckBox = (item, flag) => {
      if (item.disabled) {
        return false;
      }
      item.checked = !item.checked;
      if (item.checked) {
        datas.tags.push(item);
      } else {
        lodash.remove(datas.tags, n => {
          return item.id === n.id;
        });
      }
    };
    // 关闭tags
    const closeTag = tag => {
      datas.tags.splice(datas.tags.indexOf(tag), 1);
      datas.newTreeDetail.forEach(item => {
        const hasitem = _.filter(datas.oldTags, res => {
          res.closable = false;
          return res.id === item.id || item.id === res.sourceid || res.capabilityId === item.id;
        });
        // 判断是否已经勾选过但未曾确认选择
        const hasitem2 = _.filter(datas.tags, res => {
          res.closable = false;
          return res.id === item.id || item.id === res.sourceid || res.capabilityId === item.id;
        });
        if (hasitem.length === 1) {
          item.checked = true;
          item.disabled = true;
        } else {
          if (hasitem2.length === 1) {
            item.checked = true;
          } else {
            item.checked = false;
          }
        }
      });
    };
    // 清空
    const clear = () => {
      datas.tags = [];
      if (datas.newTreeDetail && datas.newTreeDetail.length > 0) {
        datas.newTreeDetail.forEach(list => {
          const hasitem = _.filter(datas.oldTags, res => {
            res.closable = false;
            return res.id === list.id || list.id === res.sourceid || res.capabilityId === list.id;
          });
          if (hasitem.length === 1) {
            list.checked = true;
            list.disabled = true;
          } else {
            list.checked = false;
            datas.tags = [];
          }
        });
      }
    };

    function getStandardItemList(standardId, versionId) {
      // 获取产品标准下的检测项目
      datas.loading = true;
      getItemList({ projectCategoryId: standardId, standardProductVersionId: versionId }).then(res => {
        datas.loading = false;
        if (res.data.code === 200) {
          const capabilityItems = res.data.data;
          datas.treeDetail = [];
          capabilityItems.forEach(item => {
            const keyParams = [];
            item.capabilityparaEntityList.forEach(val => {
              if (val.applylabel.includes('2')) {
                keyParams.push(val);
              }
            });
            datas.treeDetail.push({ ...item, capabilityparaVoList: keyParams });
          });
          datas.newTreeDetail = datas.treeDetail;
          checkSelectedItems();
        }
      });
    }

    function checkSelectedItems() {
      datas.newTreeDetail.forEach(item => {
        const hasitem = _.filter(datas.oldTags, res => {
          res.closable = false;
          return res.id === item.id || item.id === res.sourceId || res.capabilityId === item.id;
        });
        // 判断是否已经勾选过但未曾确认选择
        const hasitem2 = _.filter(datas.tags, res => {
          res.closable = false;
          return res.id === item.id || item.id === res.sourceId || res.capabilityId === item.id;
        });
        if (hasitem.length === 1) {
          item.checked = true;
          item.disabled = true;
        } else {
          if (hasitem2.length === 1) {
            item.checked = true;
          } else {
            item.checked = false;
          }
        }
      });
    }

    // 全选
    const selectAll = arr => {
      if (arr && arr.length > 0) {
        arr.forEach(item => {
          item.checked = true;
        });
        datas.tags = datas.tags.concat(arr);
        datas.tags = lodash.uniqBy(datas.tags, 'id');
      }
    };

    // 反选
    const selectNone = arr => {
      if (arr && arr.length > 0) {
        arr.forEach(item => {
          item.checked = !item.checked;
          if (item.checked === false) {
            _.pull(datas.tags, item);
          } else {
            datas.tags.push(item);
          }
        });
      }
    };

    return {
      ...toRefs(datas),
      emptyImg,
      dialogSuccess,
      close,
      filterNode,
      clickNode,
      treeRef,
      closeTag,
      clear,
      changeCheckBox,
      getStandardItemList,
      selectAll,
      selectNone
    };
  },
  methods: {}
};
</script>
<style lang="scss">
@import '@/styles/dialog.scss';
</style>
<style lang="scss" scoped>
@import '@/styles/tree.scss';

.dialog-content {
  .tree-container {
    .tree-content {
      height: calc(100vh - 540px);
      overflow-y: auto;
      padding-left: 0;
    }
  }
  .list-container {
    height: calc(100vh - 500px);
    overflow-y: auto;
  }
}
</style>
