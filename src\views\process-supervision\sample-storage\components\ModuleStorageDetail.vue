<template>
  <el-dialog v-model="detailVisible" title="样品记录" :width="920" :before-close="closeDialog">
    <el-row style="line-height: 30px">
      <el-col :span="8">委托编号：{{ operateDetail.presentationCode }}</el-col>
      <el-col :span="12">样品名称：{{ operateDetail.mateName }}</el-col>
      <el-col :span="4" style="display: flex; align-items: center"
        >样品二维码：<QRCodeTrigger :value="qrCodeData"
      /></el-col>
    </el-row>
    <el-timeline v-if="operateDetail.list.length" class="opticalRecording">
      <el-timeline-item v-for="item in operateDetail.list" :key="item.id" placement="top" center>
        <div class="content">
          <div class="top">
            <span class="type">{{ typeJson[item.type] }}<span v-if="item.type === 1">（补样）</span></span>
            <span class="time">{{ item.createTime }}</span>
          </div>
          <el-row>
            <el-col :span="8"
              >{{ typeJson[item.type] }}日期：<span class="value">{{ item.operateDate || '--' }}</span></el-col
            >
            <el-col :span="8"
              >{{ typeJson[item.type] }}数量：<span class="value"
                >{{ item.quantity || '--' }}<span>{{ dictionaryJson[item.unitName] }}</span></span
              ></el-col
            >
            <el-col :span="8">
              <div v-if="item.type === 4 || item.type === 1 || item.type === 0">
                规格长度：<span class="value">{{ item.specLen || '--' }}</span>
              </div>
              <div v-else>
                {{ typeJson[item.type] }}人：<span class="value">{{ getNameByid(item.userId) || '--' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row v-if="item.type === 5">
            <el-col :span="8">
              处理方式：<span class="value">{{ item.processingManner || '--' }}</span>
            </el-col>
            <el-col :span="16">
              处理说明：<span class="value">{{ item.description || '--' }}</span>
            </el-col>
          </el-row>
          <el-row v-if="item.type === 4">
            <el-col :span="8">
              回库状态：<span class="value">{{ item.status || '--' }}</span>
            </el-col>
            <el-col :span="16">
              存放地：<span class="value">{{ item.storageLocation || '--' }}</span>
            </el-col>
            <el-col :span="24">
              回库说明：<span class="value">{{ item.description || '--' }}</span>
            </el-col>
          </el-row>
          <el-row v-if="item.type === 3">
            <el-col :span="8">
              检测机构：<span class="value">{{ item.orgName || '--' }}</span>
            </el-col>
            <el-col :span="8">
              物流信息：<span class="value">{{ item.description || '--' }}</span>
            </el-col>
          </el-row>
          <el-row v-if="item.type === 0 || item.type === 1">
            <el-col :span="8">
              批次：<span class="value">{{ item.batchNo || '--' }}</span>
            </el-col>
            <el-col :span="8">
              盘号：<span class="value">{{ item.reelNo || '--' }}</span>
            </el-col>
            <el-col :span="8">
              入库状态：<span class="value">{{ item.status || '--' }}</span>
            </el-col>
            <el-col :span="8">
              存放地：<span class="value">{{ item.storageLocation || '--' }}</span>
            </el-col>
            <el-col :span="16">
              入库说明：<span class="value">{{ item.description || '--' }}</span>
            </el-col>
            <el-col :span="24">
              图片：
              <div v-if="item.fileLabelList.length > 0" style="display: inline-block">
                <div v-for="val in item.fileLabelList" :key="val.fileId" style="display: inline-block">
                  {{ val.fileName }}
                  <span v-if="val.labelInfoList.length">
                    （{{
                      val.labelInfoList
                        .map(label => {
                          return label.labelName;
                        })
                        .toString()
                    }}）
                  </span>
                </div>
              </div>
              <span v-else>--</span>
            </el-col>
          </el-row>
        </div>
      </el-timeline-item>
    </el-timeline>
    <div v-else style="width: 100%; text-align: center; line-height: 22px; padding-bottom: 40px">
      <img src="@/assets/img/empty-data.png" alt="" />
      <div>暂无数据</div>
    </div>
    <!-- 二维码弹出框 -->
    <QRCodePopup title="样品二维码" />
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, computed } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { filterProcessMode, filterSampleUnitToName } from '@/utils/formatJson';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { warehousinginfo } from '@/api/samplestorage';
// import { ElMessage } from 'element-plus'
import { QRCodePopup, QRCodeTrigger } from '@/components/QRCodePopup';

export default {
  name: 'ModuleStorageDetail',
  components: { QRCodePopup, QRCodeTrigger },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: function () {
        return {};
      }
    },
    dictionary: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, ctx) {
    // const { proxy } = getCurrentInstance()
    const state = reactive({
      detailVisible: false,
      operateDetail: {
        list: []
      },
      dictionaryJson: {},
      typeJson: {
        0: '入库',
        1: '入库',
        2: '领用',
        3: '借出',
        4: '回库',
        5: '处理'
      },
      loading: false
    });
    const qrCodeData = computed(() => {
      return {
        type: 'sample',
        id: state.detail.sampleId,
        orderId: state.detail.orderId
      };
    });
    watch(
      () => props.visible,
      newValue => {
        state.detailVisible = props.visible;
        if (props.visible) {
          state.operateDetail = {
            list: []
          };
          state.detail = props.detail;
          state.dictionaryJson = props.dictionary;
          initList();
        }
      }
    );
    const initList = () => {
      warehousinginfo({ sampleId: state.detail.sampleId, internalId: state.detail.id }).then(res => {
        if (res) {
          state.operateDetail = res.data.data;
        }
      });
    };
    // 关闭弹框
    const closeDialog = i => {
      state.detailVisible = false;
      ctx.emit('close');
    };
    return {
      ...toRefs(state),
      qrCodeData,
      getPermissionBtn,
      closeDialog,
      filterProcessMode,
      formatDate,
      filterSampleUnitToName,
      getNameByid
    };
  }
};
</script>

<style scoped lang="scss">
.opticalRecording {
  height: 600px;
  overflow-y: auto;
  padding: 10px 20px 0 20px;
  margin-bottom: 20px;
}
.content {
  line-height: 24px;
  background-color: #f4f4f5;
  border-radius: 5px;
  padding: 12px;
}

:deep(.el-timeline-item__node) {
  background-color: $tes-primary;
}
:deep(.el-timeline-item__tail) {
  border-left-color: $tes-primary1;
}
.top {
  position: absolute;
  top: 0;
  left: 28px;
  .type {
    color: $tes-primary;
    margin-right: 10px;
  }
  .time {
    color: #9d9d9d;
    font-size: 13px;
  }
}
:deep(.el-timeline-item__timestamp.is-top) {
  margin-bottom: 20px;
}
</style>
