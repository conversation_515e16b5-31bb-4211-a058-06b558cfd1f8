<!-- 请假管理 -->
<template>
  <el-dialog v-model="dialogShow" title="请假管理" :close-on-click-modal="false" width="900px" @close="handleClose">
    <div class="holidayTop">
      <el-radio-group v-model="status" size="small" @change="initTable">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button :label="0">请假中</el-radio-button>
        <el-radio-button :label="1">已结束</el-radio-button>
        <el-radio-button :label="2">已取消</el-radio-button>
      </el-radio-group>
      <el-button class="fr" type="primary" icon="el-icon-plus" size="small" @click="dialogAdd = true">新增</el-button>
    </div>
    <el-table
      ref="tableRef"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      height="auto"
      size="medium"
      highlight-current-row
      class="dark-table format-height-table base-table"
    >
      <el-table-column label="请假人" prop="secSampleNum" :width="colWidth.person" show-overflow-tooltip>
        <template #default="{ row }">
          <UserTag v-if="row.name" :name="row.name || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="开始时间" prop="startTime" :min-width="colWidth.datetime" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.startTime">{{ row.startTime }}</div>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" prop="endTime" :min-width="colWidth.datetime" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.endTime">{{ row.endTime }}</div>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" :width="colWidth.datetime" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.createTime">{{ formatDate(row.createTime) }}</div>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" :width="colWidth.status" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag :type="tagType[row.status]">{{ statusJson[row.status] || '--' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isShowOperate"
        label="操作"
        fixed="right"
        :width="colWidth.operationSingle"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <span v-if="row.status === 0" class="blue-color" @click="cancelHoliday(row)">取消请假</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :page="page" :limit="limit" :total="total" @pagination="initTable" />
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose">取 消</el-button>
        <!-- <el-button type="primary" :loading="dialogLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent>确 认</el-button> -->
      </span>
    </template>
    <DialogAddLeave :dialog-visible="dialogAdd" @closeDialog="handleCloseAdd" />
  </el-dialog>
</template>
<script>
import { reactive, ref, toRefs, watch, getCurrentInstance } from 'vue';
import UserTag from '@/components/UserTag';
import { colWidth } from '@/data/tableStyle';
import { getNameByid } from '@/utils/common';
import DialogAddLeave from './DialogAddLeave';
import Pagination from '@/components/Pagination';
import { employeeLeave } from '@/api/dutyTime';
import { formatDate } from '@/utils/formatTime';
import { saveEmployeeLeave } from '@/api/dutyTime';
export default {
  name: 'DialogLeave',
  components: { UserTag, DialogAddLeave, Pagination },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      dialogLoading: false, // 弹出窗loading
      status: '',
      total: 0,
      page: 1,
      limit: 20,
      statusJson: {
        0: '请假中',
        1: '已结束',
        2: '已取消'
      },
      tagType: {
        0: 'warning',
        1: 'success',
        2: 'info'
      },
      dialogShow: false,
      isShowOperate: false, // 是否显示操作列
      dialogAdd: false, // 新增请假信息
      tableData: [],
      tableLoading: false,
      isRefresh: false,
      ruleForm: ref(),
      listLoading: false
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.status = '';
        state.isShowOperate = false;
        state.total = 0;
        state.page = 1;
        state.isRefresh = false;
        state.limit = 20;
        initTable();
      }
    });
    const initTable = query => {
      const params = {
        status: state.status
      };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.page = query.page;
        state.limit = query.limit;
      } else {
        state.page = 1;
        params.page = '1';
        params.limit = state.limit.toString();
      }
      state.tableLoading = true;
      employeeLeave(params).then(res => {
        state.tableLoading = false;
        if (res) {
          state.total = res.data.data.totalCount;
          state.tableData = res.data.data.list;
          state.isShowOperate = state.tableData.some(item => {
            return item.status === 0;
          });
        }
      });
    };
    const onSubmit = () => {};
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog', state.isRefresh);
    };
    // 取消请假
    const cancelHoliday = row => {
      saveEmployeeLeave({ id: row.id, startTime: row.startTime, endTime: row.endTime, status: 2 }).then(res => {
        if (res) {
          proxy.$message.success(res.data.message);
          state.isRefresh = true;
          initTable();
        }
      });
    };
    const handleCloseAdd = isRefresh => {
      if (isRefresh) {
        initTable();
        state.isRefresh = true;
      }
      state.dialogAdd = false;
    };
    return {
      ...toRefs(state),
      initTable,
      onSubmit,
      formatDate,
      handleCloseAdd,
      handleClose,
      cancelHoliday,
      getNameByid,
      colWidth
    };
  }
};
</script>
<style lang="scss" scoped>
.holidayTop {
  margin-bottom: 15px;
}
</style>
