<template>
  <el-dialog v-model="dialogShow" title="入库登记" :close-on-click-modal="false" width="480px" @close="handleClose">
    <el-form
      v-if="dialogShow"
      ref="ruleForm"
      v-loading="dialogLoading"
      :model="formData"
      label-position="right"
      label-width="110px"
      size="small"
    >
      <el-form-item label="辅料编号：" prop="no">
        {{ formData.no || '--' }}
      </el-form-item>
      <el-form-item label="辅料名称：" prop="name">
        {{ formData.name || '--' }}
      </el-form-item>
      <el-form-item
        label="登记人："
        prop="userId"
        :rules="{ required: true, message: '请选择登记人', trigger: 'blur' }"
      >
        <el-select v-model="formData.userId" clearable filterable placeholder="请选择登记人">
          <el-option v-for="item in nameList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item
        label="登记日期："
        prop="recordDate"
        :rules="{ required: true, message: '请选择登记日期', trigger: 'blur' }"
      >
        <el-date-picker v-model="formData.recordDate" type="date" placeholder="请选择登记日期" />
      </el-form-item>
      <el-form-item
        label="入库数量："
        prop="num"
        :rules="{ required: true, message: '请输入入库数量', trigger: 'blur' }"
      >
        <el-row>
          <el-col :span="20">
            <el-input-number
              v-model="formData.num"
              :min="0"
              maxlength="18"
              controls-position="right"
              placeholder="请输入入库数量"
            />
          </el-col>
          <el-col :span="4">
            <div class="unitClass">{{ unitAllJson[formData.unit] }}</div>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item
        label="有效期至："
        prop="expireDate"
        :rules="{ required: true, message: '请选择有效期至', trigger: 'blur' }"
      >
        <el-date-picker v-model="formData.expireDate" type="date" placeholder="请选择有效期至" />
      </el-form-item>
      <el-form-item label="批号：" prop="batchNo">
        <el-input v-model="formData.batchNo" type="text" placeholder="请输入批号" />
      </el-form-item>
      <el-form-item label="供应商：" prop="provider">
        <el-input v-model="formData.provider" type="text" placeholder="请输入供应商" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, ref, toRefs, watch, getCurrentInstance } from 'vue';
import { useStore } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
import { entryStorage } from '@/api/standingBook';
import { formatDate } from '@/utils/formatTime';
export default {
  name: 'DialogBeput',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    unitJson: {
      type: Object,
      default: function () {
        return {};
      }
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const state = reactive({
      type: '', // 弹出窗类型
      dialogLoading: false, // 弹出窗loading
      formData: {}, // 表单数据
      dialogShow: false,
      unitAllJson: {},
      ruleForm: ref(),
      listLoading: false,
      nameList: store.common.nameList,
      currentAccountId: getLoginInfo().accountId
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.unitAllJson = props.unitJson;
        state.formData = {
          recordDate: formatDate(new Date()),
          expireDate: formatDate(new Date()),
          userId: state.currentAccountId,
          name: props.detailData.name,
          no: props.detailData.no,
          productId: props.detailData.id,
          unit: props.detailData.unit
        };
      }
    });
    const onSubmit = () => {
      proxy.$refs['ruleForm'].validate(valid => {
        if (valid) {
          state.dialogLoading = true;
          entryStorage({
            ...state.formData,
            expireDate: formatDate(state.formData.expireDate),
            recordDate: formatDate(state.formData.recordDate)
          }).then(res => {
            state.dialogLoading = false;
            if (res) {
              proxy.$message.success(res.data.message);
              context.emit('closeDialog', { isRefresh: true });
            }
          });
        } else {
          return false;
        }
      });
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog', { isRefresh: false });
    };
    const handleRadioResult = val => {
      if (val === 'Scrapped') {
        state.formData.isRemeasured = 0;
        state.formData.description = '';
      }
    };
    return { ...toRefs(state), onSubmit, handleClose, handleRadioResult };
  }
};
</script>
<style lang="scss" scoped>
.unitClass {
  text-align: center;
  margin-left: 5px;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  background-color: rgb(245, 247, 250);
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 100%;
}
:deep(.el-select) {
  width: 100%;
}
:deep(.el-input-number--small) {
  width: 100%;
}
</style>
