<template>
  <!-- 光纤库存列表 -->
  <ListLayout :has-left-panel="false" :aside-panel-width="asidePanelWidth" :aside-max-width="520">
    <template #search-bar>
      <div class="searchInput">
        <el-input
          v-model="searchForm.queryStr"
          v-trim
          v-focus
          class="ipt-360"
          placeholder="请输入搜索条件"
          clearable
          size="large"
          @keyup.enter="getTableList"
        >
          <template #prefix>
            <i class="el-input__icon el-icon-search" />
          </template>
        </el-input>
        <el-button type="primary" size="large" @click="getTableList">查询</el-button>
        <el-button size="large" @click="reset">重置</el-button>
        <el-button
          v-if="status !== 'DR'"
          class="searchBtn"
          size="large"
          type="text"
          @click="search"
          @keyup.prevent
          @keydown.enter.prevent
        >
          高级搜索
          <i class="el-icon--right" :class="[showPanel ? 'el-icon-arrow-up' : 'el-icon-arrow-down']" />
        </el-button>
      </div>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('importOpticalfiberInventory')"
        class="fr"
        type="primary"
        size="large"
        @click="handleImport()"
        @keyup.prevent
        @keydown.enter.prevent
        >导入</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-if="status !== 'DR'" v-model="activePanelName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="searchRef" :model="searchForm" class="searchLeft">
            <el-form-item :label="`${formLabel[status]}：`">
              <el-date-picker
                v-model="searchTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="handleDatePicker"
              />
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-radio-group v-model="status" size="small" @change="changeTab">
        <el-radio-button label="DR">导入</el-radio-button>
        <el-radio-button label="COLOR">着色</el-radio-button>
        <el-radio-button label="SWITCH_REEL">倒盘</el-radio-button>
        <el-radio-button label="RETURN_WAREHOUSE">回仓</el-radio-button>
      </el-radio-group>
    </template>
    <div>
      <el-scrollbar>
        <el-table
          ref="tableRef"
          :key="tableKey"
          v-loading="tableLoading"
          :data="tableData"
          fit
          border
          height="auto"
          size="medium"
          highlight-current-row
          class="dark-table format-height-table base-table"
          @header-dragend="drageHeader"
          @sort-change="sortChange"
        >
          <el-table-column
            v-for="item in tableHeader"
            :key="item.props"
            :label="item.name"
            :prop="item.props"
            :sortable="item.sortable"
            :width="colWidth.model"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div>{{ row[item.props] || '--' }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            :width="colWidth.operationMultiples"
            class-name="fixed-right"
            prop="caozuo"
            fixed="right"
          >
            <template #default="{ row }">
              <span v-if="getPermissionBtn('Coloring')" class="blue-color" @click="handleOperate('COLOR', row)">
                着色</span
              >
              <span
                v-if="getPermissionBtn('ReverseDish')"
                class="blue-color"
                @click="handleOperate('SWITCH_REEL', row)"
              >
                倒盘</span
              >
              <span
                v-if="getPermissionBtn('ReturnToWarehouse')"
                class="blue-color"
                @click="handleOperate('RETURN_WAREHOUSE', row)"
              >
                回仓</span
              >
              <span class="blue-color" @click="handleViewRecord('COLOR', row)"> 记录</span>
            </template>
          </el-table-column>
        </el-table>
      </el-scrollbar>
      <pagination :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getTableList" />
    </div>

    <el-dialog
      v-model="dialogImport"
      title="数据导入"
      :close-on-click-modal="false"
      width="680px"
      @close="dialogImport = false"
    >
      <div class="title">为确保数据导入的准确性，请按照以下规则进行导入</div>
      <div class="bt">下载表单模板</div>
      <ul class="uploadRules">
        <li>
          请按照<span class="blue-color" @click="downLoadFile('光纤导入模板.xls')">光纤导入模板.xlsx</span
          >在模板内录入数据
        </li>
        <li>只导入第一张工作表（sheet1）</li>
        <li>请勿修改表格标题，防止导入失败</li>
      </ul>
      <div v-if="dialogImport">
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="headerconfig"
          :auto-upload="false"
          :limit="1"
          :accept="fileAcceptExcel"
          :on-exceed="handleExceed"
          :before-upload="beforeUpload"
          :on-success="handleFileSuccess"
        >
          <el-button size="small" type="primary" plain>选择上传文件</el-button>
        </el-upload>
      </div>
      <ul class="uploadRules">
        <li>请上传*.xls，*.xlsx格式文件；</li>
        <li>目前一次性最多上传5000条数据；</li>
        <li>文件大小不超过10M；</li>
      </ul>
      <template #footer>
        <span class="dialog-footer">
          <el-button :loading="dialogLoading" @click="dialogImport = false">取 消</el-button>
          <el-button type="primary" @click="submitUpload">确认上传</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 着色、倒盘、回仓 -->
    <DialogFormThree
      :type="dialogType"
      :detail-data="rowDetail"
      :unit-list="unitList"
      :dialog-visible="dialogForm"
      @closeDialog="closedialogForm"
    />
    <!-- 着色、倒盘、回仓记录 -->
    <DialogThreeRecord
      :detail-data="rowDetail"
      :status="status"
      :dialog-visible="dialogThreeRecord"
      @closeDialog="closeDialog2"
    />
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import Pagination from '@/components/Pagination';
import DialogFormThree from './DialogFormThree';
import DialogThreeRecord from './DialogThreeRecord';
import { commonTableHeader, fiberInventory, fiberOperationList } from '@/api/opticalfiberInventory';
import { getDictionary } from '@/api/user';
import { fiberInventoryReportUploadUrl } from '@/api/uploadAction';
import { colWidth } from '@/data/tableStyle';
import ListLayout from '@/components/ListLayout';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { getToken } from '@/utils/auth';
import { fileAcceptExcel } from '@/utils/fileAccept';

export default {
  name: 'OpticalfiberInventory',
  components: { ListLayout, Pagination, DialogFormThree, DialogThreeRecord },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // const store = useStore()
    // const route = useRoute()
    const state = reactive({
      asidePanelWidth: 300,
      dialogForm: false,
      showPanel: false,
      status: 'DR',
      searchTime: '',
      tableHeaderJson: {
        DR: 'IMPORT',
        COLOR: 'PUT_COLOUR',
        SWITCH_REEL: 'INVERTED_PLATE',
        RETURN_WAREHOUSE: 'RETURN_WAREHOUSE'
      },
      formLabel: {
        COLOR: '着色日期',
        SWITCH_REEL: '倒盘日期',
        RETURN_WAREHOUSE: '回仓日期'
      },
      dialogThreeRecord: false,
      searchRef: ref(null),
      shortcuts: [
        {
          text: '一月内',
          value: (() => {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        },
        {
          text: '三月内',
          value: (() => {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
            return [start, end];
          })()
        }
      ],
      dialogType: '',
      rowDetail: {},
      isAdd: false,
      searchForm: {},
      detailData: {}, // 传递给详情页的内容
      tableData: [],
      isDesc: '',
      uploadRef: ref(),
      dialogImport: false,
      dialogLoading: false,
      tableLoading: false, // 表格加载的loading
      isEdit: true, // 详情页的类型
      tableHeader: [], // 表头
      tableKey: ref(0),
      activePanelName: '0',
      uploadAction: fiberInventoryReportUploadUrl(),
      headerconfig: {
        Authorization: getToken()
      },
      unitList: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ],
      unitJson: {},
      total: 0,
      listQuery: {
        page: 1,
        limit: 20
      }
    });
    const getTableHeader = () => {
      state.tableLoading = true;
      commonTableHeader(state.tableHeaderJson[state.status]).then(res => {
        state.tableLoading = false;
        if (res) {
          state.tableHeader = res.data.data.columnDetail;
          getTableList();
        }
      });
    };
    getTableHeader();
    const changeTab = () => {
      getTableHeader();
    };
    const getTableList = query => {
      const params = state.searchForm;
      if (state.isDesc !== '') {
        params.isDesc = state.isDesc;
      }
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoading = true;
      if (state.status === 'DR') {
        fiberInventory(params).then(res => {
          state.listLoading = false;
          if (res) {
            state.total = res.data.data.totalCount;
            state.tableData = res.data.data.list;
          }
        });
      } else {
        params.operationType = state.status;
        fiberOperationList(params).then(res => {
          state.tableLoading = false;
          if (res) {
            state.total = res.data.data.totalCount;
            state.tableData = res.data.data.list;
          }
        });
      }
    };
    const reset = () => {
      state.searchForm = {};
      state.searchTime = '';
      state.isDesc = '';
      state.listQuery.page = 1;
      state.listQuery.limit = 20;
      getTableList();
    };
    // 获取库存单位
    const getDictionaryList = () => {
      state.unitJson = {};
      getDictionary('QSP').then(res => {
        res.data.data.dictionaryoption.forEach(item => {
          state.unitJson[item.code] = item.name;
          if (item.status === 1) {
            state.unitList[0].group.push(item);
          } else {
            state.unitList[1].group.push(item);
          }
        });
      });
    };
    getDictionaryList();
    const handleBePut = row => {
      state.dialogForm = true;
      state.rowDetail = row;
    };
    // 出库
    const handleDelivery = row => {
      state.dialogThreeRecord = true;
      state.rowDetail = row;
    };
    const closedialogForm = val => {
      state.dialogForm = false;
      if (val.isRefresh) {
        getTableList();
      }
    };
    // 列表排序
    const sortChange = column => {
      state.searchForm.sort = column.prop;
      if (column.order === 'descending') {
        state.searchForm.order = 'desc';
      } else {
        state.searchForm.order = 'asc';
      }
      getTableList();
    };
    const closeDialog2 = val => {
      state.dialogThreeRecord = false;
      if (val.isRefresh) {
        getTableList();
      }
    };
    const closeDrawer = value => {
      if (value.isRefresh) {
        getTableList();
      }
    };
    const handleViewRecord = (type, row) => {
      state.dialogThreeRecord = true;
      state.dialogType = type;
      state.rowDetail = row;
    };
    const handleOperate = (type, row) => {
      state.dialogForm = true;
      state.dialogType = type;
      state.rowDetail = row;
    };
    // 导入
    const handleImport = () => {
      state.dialogImport = true;
    };
    // 上传文件的限制
    const beforeUpload = file => {
      var fileName = '';
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过10M');
        return false;
      } else if (fileName !== 'xls' && fileName !== 'xlsx') {
        proxy.$message.error('仅支持.xls，.xlsx文件扩展名');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };
    // 上传附件
    const submitUpload = () => {
      state.uploadRef.submit();
    };
    // 下载附件
    const downLoadFile = () => {
      const a = document.createElement('a');
      a.href = '/staticFile/光纤导入模板.xls';
      a.download = '光纤导入模板.xls';
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      a.remove();
    };
    const search = () => {
      state.showPanel = !state.showPanel;
      if (state.activePanelName === '0') {
        state.activePanelName = '1';
      } else {
        state.activePanelName = '0';
      }
    };
    const handleDatePicker = value => {
      if (value) {
        state.searchForm.beginOperationDate = formatDate(value[0]);
        state.searchForm.endOperationDate = formatDate(value[1]);
      } else {
        state.searchForm.beginOperationDate = '';
        state.searchForm.endOperationDate = '';
      }
    };
    // 上传成功的钩子
    const handleFileSuccess = (res, file) => {
      if (res.code === 200) {
        proxy.$message.success(res.message);
        state.dialogImport = false;
        getTableList();
      } else {
        proxy.$message.error(res.message);
      }
    };
    const handleExceed = files => {
      state.uploadRef.clearFiles(['success', 'fail', 'ready']);
      state.uploadRef.handleStart(files[0]);
    };
    return {
      ...toRefs(state),
      sortChange,
      search,
      handleDatePicker,
      beforeUpload,
      handleExceed,
      downLoadFile,
      submitUpload,
      handleFileSuccess,
      handleImport,
      getTableHeader,
      getTableList,
      changeTab,
      handleOperate,
      closedialogForm,
      handleViewRecord,
      getDictionaryList,
      closeDialog2,
      handleBePut,
      handleDelivery,
      closeDrawer,
      getPermissionBtn,
      formatDate,
      getNameByid,
      drageHeader,
      reset,
      colWidth,
      fileAcceptExcel
    };
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
.searchInput {
  display: flex;
  .el-input {
    width: 360px;
    margin-right: 10px;
  }
}
.uploadRules {
  padding: 5px 10px;
  background: #f0f2f5;
  margin: 14px 0;
  li {
    list-style: none;
    line-height: 22px;
    font-size: 13px;
  }
  .blue-color {
    margin-right: 0;
  }
}
.title {
  background-color: #f0f2f5;
  line-height: 30px;
  padding: 0 10px;
  margin-bottom: 15px;
}
.bt {
  font-size: 15px;
  line-height: 16px;
  font-weight: bold;
  // padding: 0 10px;
}
</style>
