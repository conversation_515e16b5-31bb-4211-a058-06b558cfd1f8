import request from '@/utils/request';

/**
 * 根据委托登记Id查询收款记录
 * @param {string} entrustId
 */
export function entrustCostCollectionList(entrustId) {
  return request({
    url: `/api-diplomat/diplomat/entrustCostCollection/listItem/${entrustId}`,
    method: 'get'
  });
}

// 保存委托费用收款信息
export function entrustCostCollectionSave(data) {
  return request({
    url: `/api-diplomat/diplomat/entrustCostCollection/save`,
    method: 'post',
    data
  });
}
// 保存委托费用收款信息
export function entrustCostCollectionInvalid(id) {
  return request({
    url: `/api-diplomat/diplomat/entrustCostCollection/invalid/${id}`,
    method: 'post'
  });
}

// 保存委托费用调价信息
export function entrustCostAdjustSave(data) {
  return request({
    url: `/api-diplomat/diplomat/entrustCostAdjust/save`,
    method: 'post',
    data
  });
}
