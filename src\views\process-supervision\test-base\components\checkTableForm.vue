<template>
  <el-form
    ref="formRef"
    v-loading="formLoading"
    :model="formDataAll"
    :rules="rules"
    label-position="right"
    label-width="110px"
    class="formAllCheck"
    hide-required-asterisk
  >
    <el-container style="height: 100%">
      <el-header style="height: auto; padding: 0">
        <div class="itemDetail">
          <div class="itemName">{{ formDataAll.name }}</div>
          <div class="itemName itemNameEng">{{ formDataAll.englishname }}</div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="试验方法：" prop="empiricalApproach">
                {{
                  formDataAll.empiricalApproach && formDataAll.empiricalApproach.length > 0
                    ? formDataAll.empiricalApproach.toString()
                    : '--'
                }}
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item label="项目分类：" prop="empirical" class="item">
                <el-cascader
                  v-model="formDataAll.projectCategoryId"
                  disabled
                  :options="itemTreeData"
                  :props="categoryProps"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item label="试验要求：" label-width="110px" prop="requirement">
                {{ formDataAll.requirement || '--' }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检测依据：" label-width="110px" prop="capabilityBasisId">
                <el-select
                  v-if="formDataAll.capabilityBasisId"
                  v-model="formDataAll.capabilityBasisId"
                  disabled
                  placeholder="请选择检测依据"
                  style="width: 100%"
                >
                  <el-option v-for="(val, key) in basisListJSON" :key="key" :label="val.basisName" :value="key" />
                </el-select>
                <span v-else> -- </span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="更新人：" prop="lastUpdateByUserId">
                <UserTag :name="getNameByid(formDataAll.lastUpdateByUserId) || getNameByid(accountId)" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="更新时间：" prop="lastupdatedatetime">
                {{ formDataAll.lastupdatedatetime || formatDate(new Date()) }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-header>
      <el-main style="padding: 0">
        <div v-if="formDataAll.custList.length === 0" class="noData">
          <el-empty :image="emptyImg" description="暂无数据" />
        </div>
        <div v-else class="gjcs">
          <div
            v-for="(row, i) in formDataAll.custList"
            :key="i + Math.random()"
            :class="'formDataClass rowType' + row.resulttype"
          >
            <div class="top">
              <div class="top-title">
                <span class="xh">{{ i + 1 }}</span>
                <span class="sonItemName">{{ row.name }}</span>
                <span class="sonItemName sonItemEngName">{{ row.englishname || '--' }}</span>
              </div>
              <div class="tag-group">
                <el-tag size="small" :type="row.resulttype == 1 ? 'primary' : 'warning'" class="fr">{{
                  typeOfData[row.resulttype]
                }}</el-tag>
                <el-tag v-if="row.okflag2" type="danger" size="small" class="fr">不判定</el-tag>
              </div>
            </div>
            <el-row>
              <el-col v-if="row.resulttype == 1" :span="12">
                <el-form-item label="类型：">
                  <span :class="{ 'blue-color': row.standardmathtype, 'orange-color': !row.standardmathtype }">{{
                    row.standardmathtype == 0 ? '非标准' : '标准项' || '--'
                  }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单位：">
                  {{ row.unitname || '--' }}
                </el-form-item>
              </el-col>
              <el-col v-if="row.resulttype == 1 && row.standardmathtype != 1" :span="12">
                <el-form-item label="约束小数位："> {{ row.smallnumber || '--' }} 位 </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="row.resulttype == 1 && row.standardmathtype !== 0">
              <el-col :span="12">
                <el-form-item label="判定标准：">
                  <span v-if="(row.minNum || row.minNum == 0) && !row.maxNum && row.maxNum !== 0"
                    >结果值{{ '&gt;' }}{{ row.minselected ? '=' + row.minNum : '' + row.minNum }}</span
                  >
                  <span v-else-if="!row.minNum && row.minNum != 0 && (row.maxNum || row.maxNum == 0)"
                    >结果值{{ '&lt;' }}{{ row.maxseleced ? '=' + row.maxNum : '' + row.maxNum }}</span
                  >
                  <span v-else-if="(row.minNum || row.minNum == 0) && (row.maxNum || row.maxNum == 0)">
                    {{ row.minNum }}
                    {{ '&lt;' }}
                    {{ row.minselected ? '=' : '' }}
                    结果值
                    {{ '&lt;' }}
                    {{ row.maxseleced ? '=' + row.maxNum : '' + row.maxNum }}
                  </span>
                  <span v-else>--</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="约束小数位："> {{ row.smallnumber || '--' }} 位 </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="row.resulttype == 1 && row.standardmathtype == 0">
              <el-col :span="24">
                <el-form-item :prop="'custList.' + i + '.javascriptmath'" label="表达式：">
                  <div v-if="showCode" class="code-textarea">
                    <el-input v-model.trim="row.javascriptmath" type="textarea" autosize readonly />
                  </div>
                  <div v-else class="code-textarea">
                    <el-input v-model.trim="row.javascriptmath" type="textarea" :rows="4" readonly />
                  </div>
                  <el-button
                    size="small"
                    type="text"
                    :icon="showCode ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                    @click="showCode = !showCode"
                    >{{ showCode ? '收起' : '展开' }}</el-button
                  >
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="row.resulttype == 2">
              <el-col :span="24">
                <el-form-item :prop="'custList.' + i + '.qualifiedOption'" label="合格选项：">
                  {{
                    row.qualifiedOption
                      .map(item => {
                        return selectJson[item];
                      })
                      .toString() || '--'
                  }}
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :prop="'custList.' + i + '.noQualifiedOption'" label="不合格项：">
                  {{
                    row.noQualifiedOption
                      .map(item => {
                        return selectJson[item];
                      })
                      .toString() || '--'
                  }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="技术要求：" :prop="'custList.' + i + '.requirement'">
                  <div class="textDetail">
                    {{ row.requirement || '--' }}
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-main>
    </el-container>
  </el-form>
</template>

<script>
import { reactive, toRefs, watch } from 'vue';
import { getNameByid } from '@/utils/common';
import { getDictionary } from '@/api/user';
import UserTag from '@/components/UserTag';
import { getLoginInfo } from '@/utils/auth';
import { formatDate } from '@/utils/formatTime';
import { findByFeats } from '@/api/testBase';
import { useRoute } from 'vue-router';
import emptyImg from '@/assets/img/empty-data.png';
export default {
  name: 'DetailItems',
  components: { UserTag },
  props: {
    treeData: {
      type: Array,
      default: function () {
        return [];
      }
    },
    unitJson: {
      type: Object,
      default: function () {
        return {};
      }
    },
    rowDetail: {
      type: Object,
      default: function () {
        return {};
      }
    },
    custList: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['detail'],
  setup(props, context) {
    const route = useRoute();
    const state = reactive({
      unitJson: props.unitJson,
      accountId: getLoginInfo().accountId, // 当前登录人的id
      basisListJSON: {}, // 检测依据列表
      formLoading: false,
      rules: {},
      formDataAll: {
        custList: []
      },
      showCode: false,
      itemTreeData: [], // 项目分类树结构
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      selectJson: {},
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      typeOfData: {
        1: '数值型',
        2: '枚举型',
        3: '字符串',
        4: '日期型',
        5: '自定义枚举'
      },
      tableList: []
    });
    watch(props, newValue => {
      state.formLoading = true;
      state.formDataAll = JSON.parse(JSON.stringify(props.rowDetail));
      state.itemTreeData = props.treeData;
      getSpecifications(state.formDataAll);
      formatParams();
      setTimeout(() => {
        state.formLoading = false;
      }, 10);
    });
    // 获取匹配的检测依据
    const getSpecifications = selectItem => {
      const params = {
        capabilityId: selectItem.capabilityId,
        specIdValueMap: JSON.parse(route.query.specifications)
      };
      findByFeats(params).then(res => {
        const response = res.data.data;
        response.forEach(item => {
          state.basisListJSON[item.basisId] = item;
        });
      });
    };
    const formatParams = () => {
      state.formDataAll.custList.forEach((row, index) => {
        if (row.custlabel && row.resulttype === '2') {
          row.selectOption = getSelectDictionary(row, index);
          // row.noQualifiedOption = row.noQualifiedOption === '' ? [] : row.noQualifiedOption.split(',')
          // row.qualifiedOption = row.qualifiedOption === '' ? [] : row.qualifiedOption.split(',')
        }
        if (Number(row.smallnumber) || row.smallnumber === '0') {
          row.smallnumber = Number(row.smallnumber);
        } else {
          row.smallnumber = undefined;
        }
        row.resultoption = row.custlabel === '' || row.custlabel === undefined ? [] : row.custlabel.split(',');
        row.okflag2 = !row.okflag;
        if (!row.qualifiedOption) {
          row.qualifiedOption = [];
        }
        if (!row.noQualifiedOption) {
          row.noQualifiedOption = [];
        }
      });
    };
    // 获取合格不合格选项
    const getSelectDictionary = (row, index) => {
      getDictionary(row.custlabel).then(res => {
        if (res) {
          res.data.data.dictionaryoption.forEach(item => {
            state.selectJson[item.code] = item.name;
          });
        }
      });
    };
    return {
      ...toRefs(state),
      emptyImg,
      formatDate,
      getNameByid,
      getSelectDictionary
    };
  }
};
</script>

<style lang="scss" scoped>
@import '../baseDetailCommom.scss';
:deep(.el-tag--medium) {
  height: 24px;
  line-height: 24px;
}
.pdClass {
  position: absolute;
  right: 11px;
}
.red {
  color: #f56c6c;
}
.textDetail {
  word-wrap: break-word;
}
:deep(.el-input.is-disabled .el-input__inner) {
  background: $background-color;
  border: 0;
  color: #606266;
  padding: 0;
}
:deep(.el-input__suffix) {
  display: none;
}
:deep(.el-form-item--medium .el-form-item__content) {
  line-height: 36px;
}

.code-textarea {
  :deep(.el-textarea__inner) {
    border: none;
    padding: 0;
  }
}
.item {
  :deep(.el-form-item__content) {
    margin-left: 110px !important;
  }
  :deep(.el-form-item__label) {
    width: 110px !important;
  }
}
</style>
