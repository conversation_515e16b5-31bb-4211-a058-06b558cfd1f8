<template>
  <div class="operation-steps-contant">
    <el-button
      v-if="getPermissionBtn('addTestMethods')"
      class="add-btn"
      size="small"
      icon="el-icon-plus"
      @click="addPerson"
      @keyup.prevent
      @keydown.enter.prevent
      >新增</el-button
    >
    <el-table
      :data="tableData"
      fit
      border
      highlight-current-row
      height="auto"
      size="medium"
      class="base-table format-height-table2 dark-table operation"
    >
      <el-table-column type="index" label="序号" width="70" align="center" />
      <el-table-column prop="nickname" label="姓名" min-width="120" show-overflow-tooltip>
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.nickname) || row.nickname || '--'" />
        </template>
      </el-table-column>
      <el-table-column prop="mobile" label="手机号" min-width="120">
        <template #default="{ row }">
          <span>{{ row.mobile || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="email" label="邮箱" min-width="200">
        <template #default="{ row }">
          <span>{{ row.email || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="departmentName" label="部门" min-width="130">
        <template #default="{ row }">
          <span>{{ row.departmentName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="beOnTheJob" label="在职状态" width="120">
        <template #default="{ row }">
          <span>{{ jobType[row.beOnTheJob] || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="getPermissionBtn('checkTestMethods') || getPermissionBtn('editTestMethods')"
        prop="status"
        label="操作"
        width="140"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <span class="blue-color" @click="deletePerson(row)" @keyup.prevent @keydown.enter.prevent>删除</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <DialogPerson :dialog-show="dialogVisible" :already-exist="alreadyExist" @closeDialog="closeDialog" />
</template>
<script>
import { reactive, toRefs, getCurrentInstance, watch } from 'vue';
// import { useRoute } from 'vue-router'
import { formatDate } from '@/utils/formatTime';
import { getNameByid, getNamesByid, getPermissionBtn } from '@/utils/common';
import { getPersionList, deletePersonApi, savePerson } from '@/api/testItem';
import UserTag from '@/components/UserTag';
import DialogPerson from '@/components/DialogPerson';

export default {
  name: 'ProjectPersonnel',
  components: { UserTag, DialogPerson },
  props: {
    detailData: {
      type: Object,
      default: function () {}
    },
    activeName: {
      type: String,
      default: ''
    }
  },
  emits: ['close'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableData: [],
      dialogVisible: false,
      jobType: {
        1: '在职',
        0: '离职',
        2: '外部用户'
      },
      alreadyExist: [], // 已经存在的人员
      detailData: props.detailData
    });
    watch(props, newValue => {
      if (newValue.activeName === '6') {
        state.detailData = props.detailData;
        getPersionLists();
      }
    });
    // 获取列表
    const getPersionLists = () => {
      getPersionList(state.detailData.id).then(res => {
        if (res) {
          // console.log(res.data.data)
          state.tableData = res.data.data;
          state.alreadyExist = state.tableData.map(item => {
            return item.employeeId;
          });
        }
      });
    };
    // 新增试验人员
    const addPerson = () => {
      state.dialogVisible = true;
    };
    // 删除试验员
    const deletePerson = row => {
      deletePersonApi(row.id).then(res => {
        proxy.$message.success(res.data.message);
        getPersionLists();
      });
    };
    // 获取所选人员
    const closeDialog = data => {
      state.dialogVisible = false;
      if (data.isRefresh) {
        const params = {
          capabilityEmployeeEntityList: []
        };
        data.selected.forEach(item => {
          params.capabilityEmployeeEntityList.push({
            employeeId: item.id,
            userId: item.userId,
            capabilityId: state.detailData.id
          });
        });
        savePerson(params).then(res => {
          if (res) {
            proxy.$message.success(res.data.message);
            getPersionLists();
          }
        });
      }
    };
    return {
      ...toRefs(state),
      formatDate,
      closeDialog,
      getPermissionBtn,
      deletePerson,
      getNameByid,
      getNamesByid,
      addPerson,
      getPersionLists
    };
  }
};
</script>
<style lang="scss" scoped>
.operation-steps-contant {
  .add-btn {
    float: left;
    margin-bottom: 10px;
  }
  :deep(.format-height-table2) {
    .el-table__body-wrapper {
      max-height: calc(100vh - 33.5rem) !important;
      overflow-x: hidden !important;
      overflow-y: auto;
    }
  }
}
</style>
