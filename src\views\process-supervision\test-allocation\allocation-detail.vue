<template>
  <!-- 检测分配详情页 -->
  <DetailLayout :main-offset-top="95">
    <template #page-header>
      <div class="flex-header">
        <div class="flex-fixed">
          <span>样品编号：</span>
          <el-tooltip v-if="topForm.secSampleNum" :content="topForm.secSampleNum" placement="top" effect="light">
            <span
              v-if="topForm.secSampleNum"
              id="sample-no"
              v-copy="topForm.secSampleNum"
              class="sampleNo"
              @click="handleSampleOrdersDetail"
              >{{ topForm.secSampleNum }}</span
            >
          </el-tooltip>
        </div>
        <div class="flex-auto">
          <span>样品名称：</span>
          <span>{{ topForm.mateName }}</span>
        </div>
        <div class="flex-auto">
          <span>判定标准：</span>
          <el-tag
            v-if="topForm.sampleStandardEntityList.length > 0"
            id="default-standard"
            closable
            class="standard-link"
            @close="deleteStandard(topForm.sampleStandardEntityList[topForm.sampleStandardEntityList.length - 1])"
            @click="jumpToStandard('default')"
          >
            {{
              topForm.sampleStandardEntityList[topForm.sampleStandardEntityList.length - 1]?.standardProductName
            }}&nbsp;v{{
              topForm.sampleStandardEntityList[topForm.sampleStandardEntityList.length - 1]?.standardProductVersion
            }} </el-tag
          >&nbsp;
          <span v-else>&nbsp;</span>
          <el-popover
            v-if="topForm.sampleStandardEntityList.length > 0"
            placement="bottom"
            :width="300"
            trigger="click"
          >
            <template #reference>
              <span id="standard-popover" class="ml-2"
                >共{{ topForm.sampleStandardEntityList.length }}项
                <i class="el-icon--right" :class="['el-icon-arrow-down']" />
              </span>
            </template>
            <div v-if="topForm.sampleStandardEntityList.length > 0">
              <el-table
                id="popover-table"
                :data="topForm.sampleStandardEntityList"
                :show-header="false"
                size="small"
                fit
                border
                class="standard-link"
              >
                <el-table-column prop="standardProductName" :min-width="200" show-overflow-tooltip>
                  <template #default="{ row }">
                    <span :key="row.standardProductName" @click="jumpToStandard(row.standardId)"
                      >{{ row.standardProductName }}&nbsp;v{{ row.standardProductVersion }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column prop="id" :width="50">
                  <template #default="{ row }">
                    <span :key="row.id" class="delete-standard" @click="deleteStandard(row)">&nbsp;x&nbsp;</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-popover>
          <span v-else>&nbsp;</span>
        </div>
      </div>

      <div class="flex-header">
        <div class="flex-fixed">
          <span>试验时间：</span>
          <span>{{ topForm.startDate }} ~ {{ topForm.finishedDate }}</span>
        </div>
        <div class="flex-auto">
          <span>型号规格：</span>
          <span>{{ topForm.prodType }}</span>
        </div>
        <div class="flex-auto">
          <span>试验说明：</span>
          <span>{{ topForm.assignedRemark }}</span>
        </div>
      </div>
    </template>

    <el-row>
      <el-col :span="16">
        <CustomPanel :has-margin-right="true" :has-margin-bottom="true" panel-height="100%" panel-min-height="660px">
          <template #panel-title>
            <div v-if="!readOnly">
              <el-row class="panel-flex" :gutter="20">
                <el-col :span="3">
                  <el-button
                    v-if="getPermissionBtn('deleteBatch')"
                    size="small"
                    :disabled="readOnly"
                    @click="removeSelect"
                    @keyup.prevent
                    @keydown.enter.prevent
                    >批量删除</el-button
                  >
                </el-col>
                <el-col :span="3">
                  <el-button
                    v-if="getPermissionBtn('allocationBatch')"
                    size="small"
                    :disabled="readOnly"
                    @click="selectUser"
                    @keyup.prevent
                    @keydown.enter.prevent
                    >批量分配</el-button
                  >
                </el-col>
                <el-col :span="6" />
                <el-col :span="3">
                  <el-button
                    v-if="getPermissionBtn('externalEntrustBtn')"
                    size="small"
                    icon="el-icon-plus"
                    :disabled="readOnly"
                    @click="showExternalDialog()"
                    @keyup.prevent
                    @keydown.enter.prevent
                    >委外登记</el-button
                  >
                </el-col>
                <el-col :span="3">
                  <el-button
                    v-if="getPermissionBtn('addItem')"
                    size="small"
                    icon="el-icon-plus"
                    :disabled="readOnly"
                    @click="showStandardDialog"
                    @keyup.prevent
                    @keydown.enter.prevent
                    >标准项目</el-button
                  >
                </el-col>
                <el-col :span="3">
                  <el-button
                    v-if="getPermissionBtn('addItem')"
                    size="small"
                    icon="el-icon-document-copy"
                    :disabled="readOnly"
                    @click="openQuickAllocationDialog"
                    @keyup.prevent
                    @keydown.enter.prevent
                    >快速分配</el-button
                  >
                </el-col>
                <el-col :span="3">
                  <el-button
                    v-if="getPermissionBtn('addItem')"
                    size="small"
                    icon="el-icon-plus"
                    :disabled="readOnly"
                    @click="addItem"
                    @keyup.prevent
                    @keydown.enter.prevent
                    >添加项目</el-button
                  >
                </el-col>
              </el-row>
            </div>
          </template>
          <template #panel-content>
            <el-table
              id="item-table"
              ref="tableRef"
              :key="tableKey"
              v-loading="listLoading"
              :data="list"
              :row-key="setRowKey"
              fit
              border
              highlight-current-row
              style="width: 100%; height: auto"
              class="dark-table allocation-table"
              @header-dragend="drageHeader"
              @sort-change="sortChange"
              @selection-change="handleSelectionChange"
              @row-click="handleRowClick"
              @current-change="setCurrentChange"
            >
              <el-table-column v-if="!readOnly" label="" :width="readOnly ? 10 : 55">
                <i v-if="!readOnly" class="tes-move iconfont" style="font-size: 12px; cursor: move" />
              </el-table-column>
              <el-table-column v-if="!readOnly" type="selection" :width="55" :selectable="isEditable" />
              <el-table-column label="序号" width="50">
                <template #default="{ row }">
                  <div>
                    {{ row.order + 1 }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="检测项目" prop="name" :min-width="140" show-overflow-tooltip>
                <template #default="{ row }">
                  <div
                    v-copy="row.sourceName"
                    class="item nowrap"
                    style="cursor: pointer"
                    @mouseenter="enterPopver(row)"
                    @mouseleave="leavePopver(row)"
                  >
                    <div class="blue-color" @click.stop="showCapabilityItemDetail(row)">
                      <span v-if="row.retestSourceId" class="custom-icon text-copy">复</span>
                      <span v-if="row.isretest === 1" class="custom-icon text-origin">源</span>
                      {{ row.sourceName }}
                      <span v-if="row.englishname" class="englishname">{{ '(' + row.englishname + ')' }}</span>
                    </div>
                  </div>
                  <el-popover
                    v-if="row.remark"
                    :visible="row.showPopver"
                    placement="top-start"
                    :title="row.sourceName"
                    trigger="click"
                  >
                    <div class="item-desc word-break">项目描述: {{ row.remark || '--' }}</div>
                    <template #reference>
                      <div />
                    </template>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column label="试验员" prop="status" :min-width="180" show-overflow-tooltip>
                <template #default="{ row }">
                  <el-select
                    v-if="isEditable(row)"
                    v-model="row.ownerIds"
                    class="owner-select auto-width"
                    placeholder="请选择"
                    clearable
                    multiple
                    filterable
                    size="small"
                    @change="changeOwner(row.ownerIds, row)"
                  >
                    <el-option
                      v-for="item in userOptions"
                      :key="item.id"
                      :label="item.nickname"
                      :value="item.id"
                      :disabled="item.status === -2"
                    />
                  </el-select>
                  <span v-else> {{ getTestersName(row.ownerIds) }} </span>
                </template>
              </el-table-column>
              <el-table-column label="日期要求" prop="status" :width="375">
                <template #default="{ row }">
                  <el-date-picker
                    v-if="isEditable(row)"
                    v-model="row.dateRange"
                    type="daterange"
                    size="small"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="changeDate(row.dateRange, row)"
                  />
                  <span v-else>{{ row.startDateTime }} ~ {{ row.finishDateTime }}</span>
                </template>
              </el-table-column>
              <el-table-column v-if="topForm.type == 1" label="上次试验日期" prop="status" :width="150">
                <template #header>
                  <span
                    >上次试验日期
                    <el-tooltip content="相同供应商的物料对应检验项目上次试验的日期" placement="top" effect="light">
                      <i class="iconfont tes-title" />
                    </el-tooltip>
                  </span>
                </template>
                <template #default="{ row }">
                  <span>{{ row.lastExperimentDate || '--' }}</span>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </CustomPanel>
      </el-col>
      <el-col :span="8">
        <CustomPanel :has-margin-bottom="true" panel-height="100%" panel-min-height="660px">
          <template #panel-title>
            <div v-show="list.length > 0" class="panel-header-left">
              <span class="capability-name">{{ capabilityItemDetail.capabilityItemName }}</span>
              <span class="capability-engname">{{ capabilityItemDetail.englishname }}</span>
            </div>
          </template>
          <template #panel-content>
            <el-form
              v-show="list.length > 0"
              id="keyForm"
              :model="capabilityItemDetail"
              label-width="80px"
              label-position="left"
            >
              <CustomPanel
                :has-margin-bottom="true"
                :panel-margin-width="0.8"
                :has-panel-header="false"
                panel-min-height="100px"
              >
                <el-form-item label="试验方法：" class="contentRight">
                  <span v-if="isDisabled" class="testMethods">{{
                    capabilityItemDetail.testMethods.map(item => item.name).join(', ') || '暂无'
                  }}</span>
                  <el-select
                    v-else
                    :key="keyParamsKey"
                    v-model="capabilityItemDetail.selectedMethods"
                    size="small"
                    multiple
                    clearable
                    collapse-tags
                    placeholder="请选择试验方法"
                    style="width: 100%"
                    @change="handleChangeMethod"
                  >
                    <el-option
                      v-for="item in capabilityItemDetail.testMethods"
                      :key="item.id"
                      :label="item.name"
                      :value="item.name"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="试验要求：" placeholder="请输入试验要求" class="contentRight">
                  <span v-if="isDisabled">{{ capabilityItemDetail.testRequirement || '暂无' }}</span>
                  <el-input
                    v-else
                    v-model="capabilityItemDetail.testRequirement"
                    placeholder="请输入试验要求"
                    size="small"
                    type="textarea"
                    :autosize="{ minRows: 1, maxRows: 3 }"
                    style="width: 100%"
                    @change="changeTestRequirement()"
                  />
                </el-form-item>
                <el-form-item label="判定标准：" class="contentRight">
                  <span v-if="isDisabled">{{ capabilityItemDetail.selectedStandardName || '暂无' }}</span>
                  <el-select
                    v-else
                    v-model="capabilityItemDetail.selectedStandardName"
                    collapse-tags
                    clearable
                    placeholder="请选择判定标准"
                    style="width: 100%"
                    size="small"
                    @change="changeItemStandard()"
                  >
                    <el-option
                      v-for="item in topForm.sampleStandardEntityList"
                      :key="item.standardProductName"
                      :label="item.standardProductName"
                      :value="item.standardProductName"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="检测依据：" class="contentRight">
                  <span v-if="isDisabled">{{
                    capabilityItemDetail.capabilityStandardBasisIds
                      .map(item => {
                        return detectionBasisList[item];
                      })
                      .toString() || '暂无'
                  }}</span>
                  <el-select
                    v-else
                    v-model="capabilityItemDetail.capabilityStandardBasisIds"
                    clearable
                    multiple
                    placeholder="请选择检测依据"
                    style="width: 100%"
                    size="small"
                    @change="changeItemBasisId"
                  >
                    <el-option v-for="(val, key) in detectionBasisList" :key="key" :label="val" :value="key" />
                  </el-select>
                </el-form-item>
              </CustomPanel>
              <div v-if="capabilityItemDetail.keyParams.length > 0" id="key-container" :key="keyParamsKey">
                <div v-for="(item, index) in capabilityItemDetail.keyParams" :key="index" class="key-panel">
                  <CustomPanel
                    v-if="item.operationType !== 3"
                    :has-margin-bottom="true"
                    :panel-margin-width="0.8"
                    :has-panel-header="false"
                    panel-min-height="100px"
                    class="key-params-panel"
                  >
                    <el-row>
                      <el-col :span="22">
                        <el-form-item label="关键参数:" style="padding: 10px 0px 0px 20px">
                          <el-tooltip :content="item.keyParamName" placement="top" effect="light">
                            <span class="key-param-name">{{ item.keyParamName }}</span>
                          </el-tooltip>
                          <el-tooltip :content="item.englishname" placement="top" effect="light">
                            <span class="key-param-eng-name">{{ item.englishname }}</span>
                          </el-tooltip>
                        </el-form-item>
                      </el-col>
                      <el-col :span="2">
                        <div class="key-param-order">
                          {{ item.order + 1 }}
                        </div>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="24">
                        <el-form-item label="技术要求:" style="padding: 0px 0px 5px 20px">
                          {{ item.standard || '--' }}
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="22">
                        <el-form-item class="unit-item" label="单位:" style="padding: 0px 0px 0px 20px">
                          {{ item.unit || '--' }}
                        </el-form-item>
                      </el-col>
                      <el-col :span="2">
                        <el-button
                          v-if="!isDisabled"
                          :key="item.keyParamName"
                          circle
                          icon="el-icon-delete"
                          style="border-color: #ffffff"
                          size="small"
                          @click.stop.prevent="deleteKeyParam(item)"
                        />
                      </el-col>
                    </el-row>
                  </CustomPanel>
                </div>
              </div>
            </el-form>
          </template>
        </CustomPanel>
      </el-col>
    </el-row>

    <el-row>
      <el-col>
        <div style="height: 60px; width: 100%" />
      </el-col>
    </el-row>

    <!-- 底部执行栏 -->
    <div class="bottom-btn">
      <el-button v-if="readOnly && ownerView && topForm.isInvalidated !== 1" type="primary" @click="editAllocation"
        >编辑</el-button
      >
      <el-button v-if="!readOnly" @click="cancelAllocation">取消</el-button>
      <el-button v-if="!readOnly" @click="viewAllocation">查看</el-button>
      <el-button
        v-if="getPermissionBtn('allocationSave') && !readOnly"
        type="primary"
        :disabled="readOnly === true"
        :loading="saveLoading"
        @click="allocationItem()"
        @keyup.prevent
        @keydown.enter.prevent
        >保存</el-button
      >
    </div>

    <template #other>
      <AddInspectionItem
        :show="showAdd"
        type="testBase"
        :material-category-code="topForm.mateType"
        :data="addList"
        :allocation="true"
        @close="closeDialog"
        @select-data="selectedItems"
      />
      <QuickAllocation
        :dialog-visiable="showQuickAllocation"
        @close-dialog="closeQuickAllocationDialog"
        @select-row="selectQuickItem"
      />
      <JudgementStandard
        :show="showJudgementStandard"
        :tree="standardTreeData"
        :standard-model="standardModelInfo"
        @close="closeStandardDialog"
        @select-data="selectStandardData"
        @show-standard-item="showStandardItemDialog"
      />
      <AddStandardItem
        :show="showAddStandard"
        :tree="productTreeData"
        :latest-standard-product="latestStandardProduct"
        :data="addStandardItemList"
        @close="closeStandardItemDialog"
        @select-data="selectStandardItems"
      />
      <el-dialog
        v-model="showSelectDialog"
        title="选择试验员"
        width="480px"
        :close-on-click-modal="false"
        @close="showSelectDialog = false"
      >
        <el-form
          ref="selectformRef"
          :model="selectFrom"
          :rules="dialogRules"
          label-position="right"
          label-width="110px"
          class="form-dialog"
        >
          <el-form-item label="试验日期：" prop="dateRange">
            <el-date-picker
              v-model="selectFrom.dateRange"
              type="daterange"
              size="small"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="试验员：" prop="ownerIds">
            <el-select
              v-model="selectFrom.ownerIds"
              size="small"
              placeholder="请选择"
              clearable
              filterable
              multiple
              style="width: 100%"
            >
              <el-option
                v-for="item in userOptions"
                :key="item.id"
                :label="item.nickname"
                :value="item.id"
                :disabled="item.status === -2"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showSelectDialog = false">取 消</el-button>
            <el-button type="primary" @click="selectDialogSuccess">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <el-dialog
        v-model="showExternalRegDialog"
        title="委外登记"
        width="480px"
        :close-on-click-modal="false"
        @close="showExternalRegDialog = false"
      >
        <el-form
          ref="externalRegFormRef"
          :model="externalRegForm"
          :rules="externalRegRules"
          label-position="right"
          label-width="110px"
          class="form-dialog"
        >
          <el-form-item label="委托日期：" prop="entrustDate">
            <el-date-picker v-model="externalRegForm.entrustDate" size="small" style="width: 100%" />
          </el-form-item>

          <el-form-item label="跟进人：" prop="followUpId">
            <el-select
              v-model="externalRegForm.followUpId"
              size="small"
              placeholder="请选择"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="item in userOptions"
                :key="item.id"
                :label="item.nickname"
                :value="item.id"
                :disabled="item.status === -2"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="完成日期：" prop="dateRange">
            <el-date-picker
              v-model="externalRegForm.dateRange"
              type="daterange"
              size="small"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="委托机构：" prop="experimentPlatform">
            <el-select
              v-model="externalRegForm.organizationId"
              size="small"
              placeholder="请选择"
              clearable
              filterable
              style="width: 100%"
              @change="changEntrustOrg(externalRegForm.organizationId)"
            >
              <el-option
                v-for="item in platformOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
                :disabled="item.status === -2"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showExternalRegDialog = false">取 消</el-button>
            <el-button type="primary" :loading="externalLoading" @click="externalRegDialogSuccess">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </template>
  </DetailLayout>
</template>

<script>
import { reactive, toRefs, ref, onMounted, watch, inject, nextTick, computed } from 'vue';
import { useRoute } from 'vue-router';
import router from '@/router/index.js';
import AddInspectionItem from '@/components/BusinessComponents/AddInspectionItem';
import AddStandardItem from '@/components/BusinessComponents/AddStandardItem';
import { formatDate } from '@/utils/formatTime';
import { formatTree } from '@/utils/formatJson';
import { getNameByid, getNamesByid, getPermissionBtn } from '@/utils/common';
import { getDistributionInfoById, saveDistributionInfo } from '@/api/allocation';
import { getCapabilityTree } from '@/api/user';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useStore } from 'vuex';
import { drageHeader } from '@/utils/formatTable';
import _ from 'lodash';
import DetailLayout from '@/components/DetailLayout';
import { addAllocationMsg } from '@/api/messageAgent';
import { getLoginInfo } from '@/utils/auth';
import { getRowDataUserList } from '@/api/user';
import { getQuickAllocationInspectionItem } from '@/api/order';
import QuickAllocation from './components/QuickAllocation.vue';
import CustomPanel from '@/components/PageComponents/CustomPanel.vue';
import JudgementStandard from '@/components/BusinessComponents/JudgementStandard';
import Sortable from 'sortablejs';
import { getStandardItemSort, getStandardProductVersion } from '@/api/capability';
import { getSampleInfoById } from '@/api/task-registration';
import { getTree, getItemList } from '@/api/testBase';
import { saveExternalEntrustReg } from '@/api/task-management';
import { getList } from '@/api/testingApparatus';
import { getCapabilityStandardBasisList } from '@/api/capability';
import { getMethodList } from '@/api/testItem';
import qs from 'qs';

export default {
  name: 'AllocationDetail',
  components: {
    AddInspectionItem,
    DetailLayout,
    QuickAllocation,
    CustomPanel,
    JudgementStandard,
    AddStandardItem
  },
  setup() {
    // const { proxy } = getCurrentInstance()
    const mittBus = inject('$mittBus');
    const route = useRoute();
    const store = useStore().state;
    const tableRef = ref(null);
    const selectformRef = ref(null);
    const externalRegFormRef = ref(null);
    const datas = reactive({
      currentAccountId: getLoginInfo().accountId,
      readOnly: false,
      ownerView: false,
      topForm: {
        secSampleNum: '',
        mateName: '',
        prodType: '',
        startDate: '',
        finishedDate: '',
        orderId: '',
        sampleStandardEntityList: [
          {
            standardProductName: 'test1'
          },
          {
            standardProductName: 'test2'
          }
        ]
      },
      detectionBasisList: {}, // 检测依据下拉数据源
      detailParam: {
        sampleId: route.params.sampleId
      },
      saveParam: null,
      checkAllocation: false,
      showCheckAllocation: false,
      listLoading: false,
      tableKey: 0,
      total: 0,
      list: [],
      oldList: [],
      addList: [],
      delList: [],
      selectList: [],
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        sort: 'DESC'
      },
      showAdd: false,
      treeData: [],
      userOptions: [],
      platformOptions: [],
      materialList: store.user.materialList,
      showSelectDialog: false,
      selectFrom: {
        dateRange: [new Date(), new Date()],
        ownerIds: getLoginInfo().accountId
      },
      externalRegForm: {
        entrustDate: new Date(),
        dateRange: [new Date(), new Date()],
        followUpId: getLoginInfo().accountId,
        organizationId: null,
        organizationName: null,
        priority: null,
        startDate: null,
        finishDate: null
      },
      experimentOwnerList: [],
      dialogRules: {
        dateRange: [{ required: true, message: '请选择试验日期', trigger: 'blur' }],
        ownerIds: [{ required: true, message: '请选择试验员', trigger: 'blur' }]
      },
      externalRegRules: {
        dateRange: [{ required: true, message: '请选择试验日期', trigger: 'blur' }]
      },
      listByGW: [],
      isOpenAll: false,
      type: 'info',
      showPopverData: {},
      saveLoading: false,
      showQuickAllocation: false,
      quickAllocationData: {},
      quickAllocationInspectionItems: [],
      capabilityItemDetail: {
        sourceId: '',
        capabilityItemName: '',
        selectedStandardName: '',
        selectedMethods: [],
        testMethods: [],
        testRequirement: '',
        keyParams: [],
        capabilityStandardBasisIds: []
      },
      capabilityItemForm: {
        testMethods: []
      },
      showJudgementStandard: false,
      standardTreeData: [],
      standardAddList: [],
      standardOldList: [],
      currentRowKey: '',
      showAddStandard: false,
      standardModelInfo: {},
      addStandardItemList: [],
      productTreeData: [],
      latestStandardProduct: {},
      selectedStandardProduct: {},
      keyParamsKey: 0,
      showExternalRegDialog: false,
      externalLoading: false
    });
    const handleChangeMethod = val => {
      datas.capabilityItemDetail.selectedMethods.forEach(item => {
        if (datas.capabilityItemDetail.testMethods.findIndex(x => x.name === item) === -1) {
          datas.capabilityItemDetail.testMethods.push({ name: item });
        }
      });
      datas.list.forEach(item => {
        if (item.sourceId === datas.capabilityItemDetail.sourceId) {
          item.method = datas.capabilityItemDetail.selectedMethods.join(',');
        }
      });
    };
    const getSampleInfoByIdInfo = () => {
      getSampleInfoById(route.params.sampleId).then(res => {
        if (res) {
          const { data } = res.data;
          datas.standardModelInfo = data;
        }
      });
    };
    if (route.query.isContracts) {
      // 是委托
      getSampleInfoByIdInfo();
    }
    // #region 标题栏功能

    // 点击样品编号跳转到样品详情页面
    const handleSampleOrdersDetail = () => {
      router.push({
        path: '/allocation/smaple/detail',
        query: {
          orderId: datas.topForm.orderId,
          sampleId: datas.topForm.sampleId
        }
      });
    };
    // 获取试验方法列表
    const getMethodListById = sourceId => {
      getMethodList(sourceId).then(res => {
        if (res !== false) {
          datas.capabilityItemDetail.testMethods = res.data.data;
        }
      });
    };
    // 获取检测依据
    const getDetectionBasisList = sourceId => {
      datas.detectionBasisList = {};
      getCapabilityStandardBasisList(sourceId).then(res => {
        if (res) {
          res.data.data.forEach(item => {
            datas.detectionBasisList[item.id] = item.basisName;
          });
        }
      });
    };
    // 删除判断标准
    function deleteStandard(row) {
      const findStandardIndex = datas.standardAddList.findIndex(item => item.id === row.id);
      if (findStandardIndex === -1) {
        row.operationType = 3;
        datas.standardAddList.push(row);
      } else {
        datas.standardAddList[findStandardIndex].operationType = 3;
      }
      datas.topForm.sampleStandardEntityList = datas.topForm.sampleStandardEntityList.filter(
        item => item.id !== row.id
      );
    }

    // 跳转到标准库
    function jumpToStandard(standardId) {
      const sampleStandard =
        standardId === 'default'
          ? datas.topForm.sampleStandardEntityList[datas.topForm.sampleStandardEntityList.length - 1]
          : datas.topForm.sampleStandardEntityList.find(x => x.standardId === standardId);
      router.push({
        path: '/testBaseList',
        query: {
          checkTreeId: sampleStandard.standardId,
          materialCategoryCode: datas.topForm.mateType,
          standardProductName: sampleStandard.standardProductName
        }
      });
    }
    // #endregion

    // #region 批量分配/删除

    // 选择批量分配-点击
    const selectUser = () => {
      var flag = false;
      datas.list.forEach(item => {
        if (item.checkbox === true) {
          flag = true;
        }
      });
      if (flag === false) {
        ElMessage.warning('请选择需要分配的检测项目！');
        return false;
      }
      datas.showSelectDialog = !datas.showSelectDialog;
      datas.selectFrom = {
        dateRange: [new Date(), new Date()],
        ownerIds: [getLoginInfo().accountId],
        startTime: formatDate(new Date()),
        endTime: formatDate(new Date())
      };
      // datas.tableKey += 1
    };
    // 选择批量删除
    const removeSelect = () => {
      var flag = false;
      datas.list.forEach(item => {
        if (item.checkbox === true) {
          flag = true;
          item.operationType = 3;
          datas.delList.push(item);
        }
      });
      if (flag === false) {
        ElMessage.warning('请选择需要删除的检测项目！');
        return false;
      }
      ElMessageBox({
        title: '提示',
        message: '是否确认删除项目？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          _.pullAll(datas.list, datas.delList);
          datas.list.forEach((item, index) => {
            item.order = index;
          });
          datas.addList = datas.list;

          ElMessage.success('删除成功');
        })
        .catch(() => {});
    };
    // 选择试验员-确定
    const selectDialogSuccess = () => {
      selectformRef.value.validate(valid => {
        if (valid) {
          const range = datas.selectFrom.dateRange;
          datas.selectFrom.startTime = range ? formatDate(range[0]) : '';
          datas.selectFrom.endTime = range ? formatDate(range[1]) : '';
          datas.list.forEach(item => {
            if (item.checkbox === true) {
              item.startDateTime = datas.selectFrom.startTime;
              item.finishDateTime = datas.selectFrom.endTime;
              item.ownerIds = datas.selectFrom.ownerIds;
              item.ownerId = datas.selectFrom.ownerIds ? datas.selectFrom.ownerIds.join(',') : [];
              item.dateRange = range;
            }
          });
          datas.showSelectDialog = false;
        }
      });
    };

    // #endregion

    // #region 添加标准项目 包含判断标准对话框，选择标准项目对话框

    const showStandardDialog = () => {
      getStandardTree(datas.topForm.mateType).then(() => {
        datas.showJudgementStandard = true;
      });
    };

    const closeStandardDialog = () => {
      datas.showJudgementStandard = false;
    };

    const selectStandardData = data => {
      const result = addCapabilityStandard(data);
      if (result.success) {
        ElMessage.success(result.message);
      } else {
        ElMessage.warning(result.message);
      }
    };

    function addCapabilityStandard(data) {
      const newStandardEntity = {
        id: data.id,
        judgmentName: `${getJudgmentName(data.standardCategoryId)} ${data.productName}`,
        sampleId: route.params.sampleId,
        standardId: data.standardCategoryId,
        standardProductId: data.id,
        standardProductName: data.productName,
        standardProductVersion: data.version,
        operationType: 1
      };
      let result = {
        success: true,
        message: `已成功添加标准${newStandardEntity.judgmentName}`
      };
      if (
        datas.topForm.sampleStandardEntityList.findIndex(
          item => item.standardProductId === newStandardEntity.standardProductId
        ) !== -1
      ) {
        result = {
          success: false,
          message: `标准 ${newStandardEntity.standardProductName} 已存在，请勿重复添加!`
        };
      } else {
        datas.topForm.sampleStandardEntityList.push(newStandardEntity);
        setItemKeyParamRequirements(newStandardEntity);
        const findStandardIndex = datas.standardAddList.findIndex(
          item =>
            item.standardProductId === newStandardEntity.standardProductId &&
            Number(item.standardProductVersion) === newStandardEntity.version
        );
        if (findStandardIndex === -1) {
          datas.standardAddList.push(newStandardEntity);
        } else {
          datas.standardAddList.splice(findStandardIndex, 1);
        }
      }
      return result;
    }

    function setItemKeyParamRequirements(standardProduct) {
      if (datas.list.length > 0) {
        getStandardProductVersion(standardProduct.id).then(res => {
          if (res && res.data.code === 200) {
            const versionList = res.data.data.versionList;
            if (versionList.length > 0) {
              const lastVersion = versionList[versionList.length - 1];
              getItemList({
                projectCategoryId: '',
                standardProductVersionId: lastVersion.id
              }).then(res => {
                if (res.data.code === 200) {
                  const capabilityItems = res.data.data;
                  // const capabilityArray = []
                  capabilityItems.forEach(item => {
                    const index = datas.list.findIndex(obj => obj.sourceId === item.capabilityId);
                    if (index !== -1) {
                      const paramList = datas.list[index].childList;
                      datas.list[index].selectedStandardName = standardProduct.standardProductName;
                      item.custList.forEach(ele => {
                        const paramIndex = paramList.findIndex(param => param.sourceName === ele.name);
                        if (paramIndex !== -1) {
                          datas.list[index].childList[paramIndex].standardRequirement = ele.requirement;
                          datas.list[index].childList[paramIndex].sourceUnit = ele.unitname;
                          datas.list[index].childList[paramIndex].operationType = 2;
                        }
                      });
                    }
                  });
                  showCapabilityItemDetail(datas.list[0]);
                }
              });
            }
          }
        });
      }
    }

    // 改变单个项目的判定标准
    function changeItemStandard() {
      const standardIndex = datas.topForm.sampleStandardEntityList.findIndex(
        item => item.standardProductName === datas.capabilityItemDetail.selectedStandardName
      );
      const index = datas.list.findIndex(item => item.sourceId === datas.capabilityItemDetail.sourceId);
      datas.list[index].selectedStandardName = datas.capabilityItemDetail.selectedStandardName;
      if (standardIndex !== -1) {
        const selectedStandard = datas.topForm.sampleStandardEntityList[standardIndex];
        getStandardProductVersion(selectedStandard.standardProductId).then(res => {
          if (res && res.data.code === 200) {
            const versionList = res.data.data.versionList;
            if (versionList.length > 0) {
              const lastVersion = versionList[versionList.length - 1];
              getItemList({
                projectCategoryId: '',
                standardProductVersionId: lastVersion.id
              }).then(res => {
                if (res.data.code === 200) {
                  const capabilityItems = res.data.data;
                  const standardItemIndex = capabilityItems.findIndex(
                    item => item.capabilityId === datas.capabilityItemDetail.sourceId
                  );
                  if (standardItemIndex !== -1) {
                    const paramList = datas.list[index].childList;
                    capabilityItems[standardItemIndex].custList.forEach(ele => {
                      const paramIndex = paramList.findIndex(param => param.sourceName === ele.name);
                      if (paramIndex !== -1) {
                        datas.list[index].childList[paramIndex].standardRequirement = ele.requirement;
                        datas.list[index].childList[paramIndex].sourceUnit = ele.unitname;
                        datas.list[index].childList[paramIndex].operationType = 2;
                      }
                    });
                    showCapabilityItemDetail(datas.list[index]);
                    // ElMessage.success(`根据标准${datas.capabilityItemDetail.selectedStandardName}已更新检测项目${datas.capabilityItemDetail.capabilityItemName}`)
                  }
                }
              });
            }
          }
        });
      }
    }

    // 获取标准名
    function getJudgmentName(standardCategoryId) {
      let resultName = '';
      const standardTreeList = datas.standardTreeData;
      standardTreeList.shift();
      standardTreeList.forEach(item => {
        if (JSON.stringify(item).indexOf(standardCategoryId) !== -1) {
          const standardList = readTree([item]);
          standardList.forEach(ele => {
            if (ele.id === standardCategoryId) {
              standardList.forEach(obj => {
                if (obj.id === ele.parentId) {
                  resultName = obj.code;
                }
              });
            }
          });
        }
      });
      return resultName;
    }

    function readTree(node, resultList = []) {
      if (node && node.length > 0) {
        let count = 0;
        node.forEach(ele => {
          resultList.push({
            id: ele.id,
            parentId: ele.parentId,
            code: ele.code,
            type: ele.standardType
          });
          if (ele.children && ele.children.length > 0) {
            readTree(ele.children, resultList);
          } else {
            count++;
          }
        });
        if (count === node.length) {
          return resultList;
        }
      }
      return resultList;
    }

    // 获取判定标准库左侧树
    async function getStandardTree(code) {
      await getTree(code).then(res => {
        const data = res.data.data;
        datas.standardTreeData = formatTree(data);
        datas.standardTreeData = [
          {
            id: '',
            isDeleted: false,
            parentId: '0',
            name: '全部',
            code: '全部',
            materialCategoryCode: datas.topForm.mateType ?? '',
            order: 0
          }
        ].concat(datas.standardTreeData);
      });
    }

    function getDistributionInfo() {
      datas.listLoading = true;
      datas.list = [];
      getDistributionInfoById(datas.detailParam).then(res => {
        if (res !== false && res.data.code === 200) {
          const { data } = res.data;
          datas.topForm = data;
          checkReadOnly(datas.topForm.ownerId);
          datas.standardOldList = JSON.parse(JSON.stringify(data.sampleStandardEntityList));
          datas.topForm.startDate = formatDate(data.startDate);
          datas.topForm.finishedDate = formatDate(data.finishedDate);
          // datas.list = data.taskSampleExperimentOrders
          datas.saveParam = datas.topForm;
          const taskSampleExperimentOrders = data.taskSampleExperimentOrders;
          const experimentCapabilityList = data.experimentCapabilityList;
          if (experimentCapabilityList && experimentCapabilityList.length > 0) {
            experimentCapabilityList.forEach(ex => {
              ex.status = ex.experimentStatus;
              if (ex.method) {
                ex.method1 = ex.method.split(',');
                ex.methods = [].concat(ex.method1);
              }
              if (ex.startDateTime && ex.finishDateTime) {
                ex.dateRange = [];
                ex.dateRange.push(ex.startDateTime);
                ex.dateRange.push(ex.finishDateTime);
              } else {
                ex.dateRange = [];
              }
              ex.checkbox = false;
              ex.showPopver = false;
              ex.operationType = 2;
              if (ex.ownerId) {
                ex.ownerIds = ex.ownerId.split(',');
              }
            });
            datas.addList = experimentCapabilityList;
            datas.list = experimentCapabilityList;
          }
          if (taskSampleExperimentOrders && taskSampleExperimentOrders.length > 0) {
            taskSampleExperimentOrders.forEach((ele, index) => {
              ele.sourceIdStr = ele.id;
              ele.sourceNumber = ele.number;
              ele.sourceparentIdStr = ele.capabilityid;
              ele.sourceCapabilityDescription = ele.description;
              ele.sourceName = ele.name;
              ele.sourceCapabilityType = 'Internal'; // 'External' 目前只有内部项目，不展示外部项目
              ele.experimentCategoryIdStr = ele.categoryId;
              ele.checkbox = false;
              ele.operationType = 2;
              ele.status = 1;
              if (ele.method) {
                ele.method1 = ele.method.split(',');
                ele.methods = JSON.parse(ele.method1);
              }
              if (ele.capabilityparaVoList && ele.capabilityparaVoList.length > 0) {
                ele.capabilityparaVoList.forEach(clist => {
                  clist.sourceparentIdStr = clist.capabilityid;
                  clist.sourceIdStr = clist.id;
                  clist.experimentCategoryIdStr = clist.categoryId;
                });
              }
              ele.childList = ele.capabilityparaVoList;

              datas.listLoading = false;
            });
            datas.list = taskSampleExperimentOrders;
            datas.addList = taskSampleExperimentOrders;
          } else {
            datas.listLoading = false;
          }

          // 初始化右边的关键参数面板
          if (datas.list.length > 0) {
            showCapabilityItemDetail(datas.list[0]);
          }

          // 初始化判断标准
          // datas.standardList = datas.topForm.sampleStandardEntityList

          // 保存历史数据
          datas.oldList = JSON.parse(JSON.stringify(datas.list));
          mittBus.reloadAllocation = false;
          if (data.mateType) {
            getCapabilityTree(data.mateType).then(response => {
              if (response !== false) {
                const data1 = response.data.data;
                datas.treeData = formatTree(data1);
                const allParam = {
                  id: '-1',
                  name: '全部',
                  materialCategoryCode: data.mateType,
                  categoryId: ''
                };
                datas.treeData.unshift(allParam);
              }
            });
          }
        } else {
          ElMessage.error(res.data);
          datas.listLoading = false;
        }
      });
    }

    function closeStandardItemDialog() {
      datas.showAddStandard = false;
    }

    function selectStandardItems(standardCapabilityItems) {
      if (standardCapabilityItems) {
        let repeatCount = 0;
        const addItemList = [];
        datas.showJudgementStandard = false;
        datas.showAddStandard = false;
        const addStandardResult = addCapabilityStandard(datas.selectedStandardProduct);
        standardCapabilityItems.forEach(item => {
          item.sourceCapabilityType = 'Internal';
          item.sourceId = item.capabilityId;
          item.sourceIdStr = item.capabilityId;
          item.sourceNumber = item.sourcenumber;
          item.sourceName = item.name;
          item.method = item.empiricalApproach.join(',');
          item.number = item.sourcenumber;
          item.operationType = 1;
          item.status = 1;
          item.experimentStatus = 1;
          item.experimentCategoryId = item.projectCategoryId;
          item.dateRange = [];
          item.finishDateTime = '';
          item.startDateTime = '';
          item.childList = item.custList.filter(ele =>
            item.capabilityparaVoList.find(para => para.id === ele.capabilityParaId)
          );
          item.childList.forEach((obj, index) => {
            obj.sourceId = obj.capabilityParaId;
            obj.sourceName = obj.name;
            obj.sourceUnit = obj.unitname;
            obj.standardRequirement = obj.requirement;
            obj.order = index;
          });
          const index = datas.list.findIndex(ele => ele.sourceId === item.sourceId);
          if (index === -1) {
            addItemList.push(item);
          } else {
            datas.list[index].sourceName = item.name;
            datas.list[index].englishname = item.englishname;
            datas.list[index].childList.forEach((child, childIndex) => {
              const experimentId = child.experimentId;
              const newChild = item.childList.filter(childItem => childItem.sourceId == child.sourceId)[0];
              newChild.experimentId = experimentId;
              datas.list[index].childList.splice(childIndex, 1, { ...newChild }); // 替换整个对象
            });
            if (datas.list[index].sourceId === datas.capabilityItemDetail.sourceId) {
              showCapabilityItemDetail(datas.list[index]);
            }
            repeatCount++;
          }
        });
        selectedItems(addItemList);
        ElMessage.success(
          `${addStandardResult.message}\t|\t有${repeatCount}个检测项目已存在，成功添加${addItemList.length}个标准项目!`
        );
      }
    }

    function showStandardItemDialog(standardProduct) {
      datas.selectedStandardProduct = standardProduct;
      getStandardProductVersion(standardProduct.id).then(res => {
        if (res && res.data.code === 200) {
          const versionList = res.data.data.versionList;
          if (versionList.length > 0) {
            const lastVersion = versionList[versionList.length - 1];
            lastVersion.productName = standardProduct.productName;
            datas.latestStandardProduct = lastVersion;
            getProductTree(lastVersion.id).then(() => {
              datas.showAddStandard = true;
            });
          }
        }
      });
    }

    async function getProductTree(productVersionId) {
      await getStandardItemSort(productVersionId).then(res => {
        const data = res.data.data;
        datas.productTreeData = formatTree(data);
        datas.productTreeData = [
          {
            id: '',
            isDeleted: false,
            parentId: '0',
            name: '全部',
            code: '全部',
            order: 0
          }
        ].concat(datas.productTreeData);
      });
    }

    // #endregion

    // #region 快速分配对话框

    // 打开快速分配对话框
    const openQuickAllocationDialog = () => {
      datas.showQuickAllocation = true;
    };

    // 关闭快速分配对话框
    const closeQuickAllocationDialog = () => {
      datas.showQuickAllocation = false;
    };

    // 确认选择的历史已分配样品
    const submitQuickAllocationDialog = () => {
      datas.showQuickAllocation = false;
    };

    const selectQuickItem = row => {
      datas.quickAllocationData = row;
      // 根据周期时间设置日期要求
      let startDate = new Date();
      let endDate = new Date();
      endDate.setTime(startDate.getTime() + 3600 * 1000 * 24 * (row.cycleTime >= 1 ? row.cycleTime - 1 : 0));
      startDate = formatDate(startDate);
      endDate = formatDate(endDate);
      let newCount = 0;
      let repeatCount = 0;
      getQuickAllocationInspectionItem(row.samplesId).then(res => {
        if (res.data.code === 200) {
          const result = res.data.data;
          datas.quickAllocationInspectionItems = JSON.parse(JSON.stringify(result.experimentCapabilityList));
          datas.topForm.sampleStandardEntityList = result.sampleStandardEntityList;
          let newItems = result.experimentCapabilityList
            ? JSON.parse(JSON.stringify(result.experimentCapabilityList))
            : [];
          datas.quickAllocationInspectionItems.forEach(historyItem => {
            datas.list.forEach(item => {
              if (item.sourceId === historyItem.sourceId || item.sourceIdStr === historyItem.sourceId) {
                item.method = historyItem.method;
                item.method1 = historyItem.method ? historyItem.method.split(',') : [];
                item.methods = [].concat(item.method1);
                item.requirement = historyItem.requirement;
                item.ownerId = historyItem.ownerId;
                item.ownerIds = historyItem.ownerId ? historyItem.ownerId.split(',') : [];
                item.dateRange = [startDate, endDate];
                item.startDateTime = startDate;
                item.finishDateTime = endDate;
                repeatCount++;
                newItems = newItems.filter(newItem => newItem.sourceId !== historyItem.sourceId);
              }
            });
          });
          newCount = newItems.length;
          const newItemList = [];
          newItems.forEach(item => {
            item.childList.forEach((child, index) => {
              child.id = child.sourceId;
              child.sourceIdStr = child.sourceId;
              child.name = child.sourceName;
              child.name1 = '';
              child.number = child.sourceNumber;
              child.capabilityid = child.experimentId;
              child.order = index;
              child.experimentId = '';
            });
            const newItemData = {
              method: item.method,
              method1: item.method ? item.method.split(',') : [],
              methods: [].concat(item.method1),
              requirement: item.requirement,
              ownerId: item.ownerId,
              ownerIds: item.ownerId ? item.ownerId.split(',') : [],
              dateRange: [],
              startDateTime: item.startDateTime,
              finishDateTime: item.finishDateTime,
              capabilityparaVoList: item.childList,
              categoryid: item.experimentCategoryId,
              checked: true,
              childList: item.childList,
              englishName: '',
              experimentCategoryId: item.experimentCategoryId,
              experimentCategoryIdStr: item.experimentCategoryId,
              id: item.experimentId,
              name: item.sourceName,
              newcapabilityparaVoList: item.childList,
              number: item.sourceNumber,
              operationType: 1,
              parentid: '0',
              remark: '',
              sourcecapabilitydescription: '',
              sourcecapabilitytype: 'Internal',
              sourceCapabilityType: 'Internal',
              sourceIdStr: item.sourceId,
              sourceId: item.sourceId,
              sourceName: item.sourceName,
              sourceNumber: item.sourceNumber,
              sourceparentIdStr: '0',
              status: 1,
              experimentStatus: 1,
              order: datas.list.length + newItemList.length
            };
            if (item.startDateTime) {
              newItemData.dateRange = [item.startDateTime, item.finishDateTime];
            }
            newItemList.push(newItemData);
          });

          addCapabilityItems(newItemList);

          ElMessage.success(`新增${newCount}个项目，有${repeatCount}个项目已存在，已更新数据！`);
          repeatCount = 0;
        }
      });
    };
    // #endregion

    // #region 添加检测项目对话框

    // 新增项目
    const addItem = () => {
      datas.showAdd = true;
    };
    // 关闭新增项目
    const closeDialog = value => {
      datas.showAdd = value;
    };
    // 获取新增的项目
    const selectedItems = data => {
      if (data.length > 0) {
        for (let i = 0; i < data.length; i++) {
          data[i].methods = data[i].method ? data[i].method.split(',') : [];
          data[i].method1 = data[i].methods;
          data[i].experimentCategoryId = data[i].experimentCategoryIdStr;
          data[i].status = 1;
          data[i].experimentStatus = 1;
          data[i].sourceId = data[i].sourceIdStr;
          data[i].sourceNumber = data[i].sourcenumber;
          data[i].order = datas.list.length + i;
          data[i].childList.forEach((item, index) => {
            item.order = index;
            item.sourceId = item.sourceIdStr;
          });
          data[i].dateRange = [new Date(), new Date()];
          changeDate(data[i].dateRange, data[i]);
        }
        addCapabilityItems(data);
      }
    };
    // #endregion

    // #region 表格相关功能

    function getTestersName(testerIds) {
      const testersName = [];
      testerIds?.forEach(id => {
        const userIndex = datas.userOptions.findIndex(item => item.id === id);
        if (userIndex !== -1) {
          testersName.push(datas.userOptions[userIndex].nickname);
        }
      });
      return testersName.join(', ');
    }

    function setRowKey(row) {
      return row.sourceId;
    }

    // 未分配项目-checkbox-change
    const changeCheckAllocation = value => {
      if (value) {
        datas.list.forEach(item => {
          if (item.checkbox === true) {
            item.showCheckAllocation = true;
          }
        });
      } else {
        datas.list.forEach(item => {
          if (item.checkbox === true) {
            item.showCheckAllocation = false;
          }
        });
      }
    };

    // 日期要求-change
    const changeDate = (date, row) => {
      row.startDateTime = date ? formatDate(date[0]) : '';
      row.finishDateTime = date ? formatDate(date[1]) : '';
    };
    // 试验方法-change
    const changeMethod = (method, row) => {
      row.methods = row.methods ? row.methods : [];
      row.methods = _.uniq(row.methods.concat(method));
      row.method = JSON.stringify(method);
    };
    // 试验员-change
    const changeOwner = (owner, row) => {
      row.ownerId = owner.join(',');
    };

    // hover 检测项目
    const enterPopver = row => {
      row.showPopver = true;
    };

    // leave 检测项目
    const leavePopver = row => {
      row.showPopver = false;
    };

    // 移除
    const remove = row => {
      row.operationType = 3;
      datas.delList.push(row);
      _.remove(datas.list, n => {
        return n.id === row.id;
      });
    };

    // showRow 改变table一行的样式
    const showRow = ({ row }) => {
      let styleJson = {};
      if (row.showCheckAllocation) {
        styleJson = {
          display: 'none'
        };
      } else {
        styleJson = {
          display: 'table-row'
        };
      }
      return styleJson;
    };

    // 排序
    const sortChange = data => {
      // const { prop, order } = data
    };
    // 检测项目行是否可编辑
    const isEditable = row => {
      return !datas.readOnly && (row.status === 1 || row.status === 2);
    };

    // 选择检测项目
    const handleSelectionChange = val => {
      if (val.length > 0) {
        datas.type = 'primary';
      } else {
        datas.type = 'info';
      }
      datas.selectList = val;
      datas.list.forEach(item => {
        const hasitem = _.includes(val, item);
        item.checkbox = hasitem;
      });
    };

    const handleRowClick = row => {
      tableRef.value.toggleRowSelection(row, !row.checkbox);
      datas.list.forEach(item => {
        if (item.experimentId === row.experimentId) {
          item.checkbox = true;
        }
      });
    };

    const setCurrentChange = row => {
      datas.currentRowKey = row.sourceId;
    };

    // 行拖拽
    function rowDrop() {
      // 获取当前表格
      const tbody = document.getElementById('item-table').querySelector('.el-table__body-wrapper tbody');
      Sortable.create(tbody, {
        animation: 150,
        handle: '.tes-move',
        draggable: '.el-table__row',
        ghostClass: 'ghost',
        // 拖动对象移动样式
        dragClass: 'drag',
        forceFallback: true,
        onChoose(evt) {},
        onEnd(evt) {
          if (evt.oldIndex !== evt.newIndex) {
            // 移除原来的数据
            const currRow = datas.list.splice(evt.oldIndex, 1)[0];
            // 移除原来的数据并插入新的数据
            datas.list.splice(evt.newIndex, 0, currRow);
            datas.list.forEach((value, index) => {
              value.order = index;
            });
          }
          datas.tableKey += 1;
        }
      });
    }

    // 行拖拽
    function keyDivDrop() {
      // 获取当前表格
      if (document.getElementById('key-container')) {
        const tbody = document.getElementById('key-container');
        Sortable.create(tbody, {
          animation: 150,
          handle: '.key-panel',
          draggable: '.key-panel',
          ghostClass: 'ghost',
          // 拖动对象移动样式
          dragClass: 'drag',
          forceFallback: true,
          onChoose(evt) {},
          onEnd(evt) {
            if (evt.oldIndex !== evt.newIndex) {
              changeKeyParamOrder(evt.oldIndex, evt.newIndex);
              datas.keyParamsKey += 1;
            }
            // 消除元素拖拽事件对子元素click事件的影响
            if (document.getElementsByClassName('key-panel').length > 0) {
              document.getElementsByClassName('key-panel')[0].click();
            }
          }
        });
      }
    }

    function changeKeyParamOrder(oldIndex, newIndex) {
      // 移除原来的数据
      const currRow = datas.capabilityItemDetail.keyParams.splice(oldIndex, 1)[0];
      // 移除原来的数据并插入新的数据
      datas.capabilityItemDetail.keyParams.splice(newIndex, 0, currRow);
      datas.capabilityItemDetail.keyParams.forEach((value, index) => {
        value.order = index;
      });
    }

    function changelistKeyParamOrder() {
      datas.list.forEach(item => {
        if (item.sourceId === datas.capabilityItemDetail.sourceId) {
          item.childList.forEach(ele => {
            ele.order = datas.capabilityItemDetail.keyParams.findIndex(obj => obj.keyParamName === ele.sourceName);
          });
        }
      });
    }

    function addCapabilityItems(newItems) {
      datas.addList = datas.addList.concat(newItems);
      datas.list = datas.list.concat(newItems);
      // 刷新右侧面板
      if (datas.list.length > 0) {
        showCapabilityItemDetail(datas.list[datas.list.length - 1]);
      }
    }

    // #endregion

    // #region 右边关键参数面板

    // 显示右侧关键参数面板
    const showCapabilityItemDetail = row => {
      if (row) {
        datas.capabilityItemDetail.sourceId = row.sourceId;
        datas.capabilityItemDetail.capabilityItemName = row.sourceName;
        datas.capabilityItemDetail.englishname = row.englishname;
        datas.capabilityItemDetail.selectedStandardName = row.selectedStandardName || '';
        datas.capabilityItemDetail.testRequirement = row.requirement;
        datas.capabilityItemDetail.status = row.experimentStatus;
        datas.capabilityItemDetail.testMethods = [];
        datas.capabilityItemDetail.selectedMethods = [];
        datas.capabilityItemDetail.keyParams = [];
        if (row.capabilityStandardBasisIds) {
          datas.capabilityItemDetail.capabilityStandardBasisIds = row.capabilityStandardBasisIds.split(',');
        } else {
          datas.capabilityItemDetail.capabilityStandardBasisIds = [];
        }
        if (row.method) {
          // datas.capabilityItemDetail.testMethods = [].concat(
          //   row.method.split(',')
          // )
          datas.capabilityItemDetail.selectedMethods = [].concat(row.method.split(','));
        }
        if (row.childList.length > 0) {
          row.childList.forEach(item => {
            datas.capabilityItemDetail.keyParams.push({
              keyParamName: item.sourceName,
              unit: item.sourceUnit,
              standard: item.standardRequirement,
              order: item.order,
              englishname: item.englishname,
              operationType: item.operationType || 1
            });
          });
        }
        datas.keyParamsKey += 1;
        getDetectionBasisList(row.sourceId);
        getMethodListById(row.sourceId);
      }
    };

    // 修改检测依据
    const changeItemBasisId = val => {
      datas.list.forEach(item => {
        if (item.sourceId === datas.capabilityItemDetail.sourceId) {
          item.capabilityStandardBasisIds = val.toString();
        }
      });
    };

    // 修改检测项目的试验要求
    const changeTestRequirement = () => {
      datas.list.forEach(item => {
        if (item.sourceId === datas.capabilityItemDetail.sourceId) {
          item.requirement = datas.capabilityItemDetail.testRequirement;
        }
      });
    };

    // 删除关键参数
    const deleteKeyParam = currentKeyParam => {
      if (currentKeyParam) {
        ElMessageBox({
          title: '提示',
          message: `是否要删除关键参数: ${currentKeyParam.keyParamName}?`,
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'info'
        })
          .then(() => {
            currentKeyParam.operationType = 3;
            changeKeyParamOrder(currentKeyParam.order, datas.capabilityItemDetail.keyParams.length);
            changelistKeyParamOrder();
            datas.list.forEach(item => {
              if (item.sourceId === datas.capabilityItemDetail.sourceId) {
                item.childList.forEach(ele => {
                  if (ele.sourceName === currentKeyParam.keyParamName) {
                    ele.operationType = 3;
                  }
                });
              }
            });
            return true;
          })
          .catch(() => {
            return false;
          });
      } else {
        return true;
      }
    };

    // #endregion

    // #region 底部执行栏
    // 取消分配
    const cancelAllocation = () => {
      router.push({ name: 'TestAllocation' });
    };
    const editAllocation = () => {
      router.push({
        name: 'Allocation',
        params: {
          sampleId: route.params.sampleId,
          status: route.params.status,
          id: route.params.id,
          isView: 0
        }
      });
    };

    const viewAllocation = () => {
      router.push({
        name: 'Allocation',
        params: {
          sampleId: route.params.sampleId,
          status: route.params.status,
          id: route.params.id,
          isView: 1
        }
      });
    };

    // 分配
    const allocationItem = (isExternal = false) => {
      const saveParam = {
        experimentCapabilityList: datas.list.concat(datas.delList),
        mateType: datas.topForm.mateType,
        sampleId: route.params.sampleId,
        sampleStandardEntityList: isExternal ? [] : datas.standardAddList,
        sampleStatus: datas.topForm.status,
        prodType: datas.topForm.prodType
      };

      var flag = 0;
      saveParam.experimentCapabilityList.forEach(row => {
        if (saveParam.sampleStatus === 1 || row.operationType === 1) {
          if (row.operationType !== 3 && !isExternal) {
            row.operationType = 1;
            if ((!row.startDateTime && row.ownerId) || (row.startDateTime && !row.ownerId)) {
              flag = 1;
            }
          }
        }
        if (isExternal && row.checkbox) {
          row.ownerIds = [];
          changeOwner([], row);
          changeDate([], row);
        }
      });
      datas.topForm.sampleStandardEntityList = [];
      if (flag === 1) {
        ElMessage.warning('试验员和日期要求必须同时填写');
        return false;
      }
      datas.saveLoading = true;
      datas.externalLoading = true;
      saveDistributionInfo(saveParam).then(res => {
        if (res) {
          // const { data } = res.data
          if (saveParam.sampleStatus > 1) {
            replacePathParams({ ...route.params, status: saveParam.sampleStatus });
          } else {
            replacePathParams({ ...route.params, status: 2 });
          }
          datas.standardAddList = [];
          datas.delList = [];
          // addMessage(saveParam)
          if (isExternal) {
            saveExternalEntrustRegList();
          } else {
            ElMessage.success('保存成功!');
            // addMessage(saveParam)
            getDistributionInfo();
          }
          // router.push({ name: 'TestAllocation' })
        }
        if (!isExternal) {
          datas.saveLoading = false;
          datas.externalLoading = false;
        }
      });
    };

    const replacePathParams = params => {
      const query = qs.stringify(route.query);
      history.replaceState(
        null,
        route.path,
        `/allocation/${params.sampleId}/${params.status}/${params.isView}${query ? `?${query}` : ''}`
      );
    };

    // #endregion

    // #region 委外登记

    const showExternalDialog = () => {
      const regList = datas.list.filter(item => item.checkbox);
      if (regList.length === 0) {
        ElMessage.warning('请选择需要委外登记的检测项目！');
        return false;
      } else {
        const assignedList = regList.filter(item => item.experimentStatus !== 1);
        if (assignedList.length > 0) {
          const assignedNameList = [];
          assignedList.forEach(item => {
            assignedNameList.push(item.sourceName);
          });
          ElMessage.warning(
            `检测项目:${assignedNameList.join()}已分配，无法委外登记！(可先删除再新增项目进行委外登记)`
          );
          return false;
        } else {
          datas.showExternalRegDialog = true;
        }
      }
    };

    // 确认委外登记
    const externalRegDialogSuccess = () => {
      externalRegFormRef.value.validate(valid => {
        if (valid) {
          allocationItem(true);
        }
      });
    };

    function saveExternalEntrustRegList() {
      const experimentEntrustList = [];
      const range = datas.externalRegForm.dateRange;
      const startDate = range ? formatDate(range[0]) : '';
      const finishDate = range ? formatDate(range[1]) : '';
      datas.list.forEach(item => {
        if (item.checkbox === true) {
          experimentEntrustList.push({
            capabilityId: item.sourceId,
            capabilityName: item.sourceName,
            childList: item.childList,
            entrustDate: formatDate(datas.externalRegForm.entrustDate),
            entrustStatus: 0,
            experimentId: item.experimentId,
            finishDate,
            followUpId: datas.externalRegForm.followUpId,
            id: '',
            organizationId: datas.externalRegForm.organizationId,
            organizationName: datas.externalRegForm.organizationName,
            sampleId: datas.topForm.sampleId,
            secSampleNum: datas.topForm.secSampleNum,
            startDate,
            orderId: datas.topForm.orderId,
            order: item.order
          });
          item.ownerIds = [];
          item.dateRange = [];
        }
      });
      saveExternalEntrustReg({ experimentEntrustList }).then(res => {
        if (res) {
          getDistributionInfo();
          // router.push({ name: 'TestAllocation' })
          ElMessage.success('委外登记成功！');
        }
        datas.saveLoading = false;
        datas.externalLoading = false;
      });
      datas.showExternalRegDialog = false;
    }

    // change entrus org
    function changEntrustOrg(orgId) {
      const orgIndex = datas.platformOptions.findIndex(item => item.id === orgId);
      datas.externalRegForm.organizationName = orgIndex === -1 ? '' : datas.platformOptions[orgIndex].name;
    }

    // #endregion

    // #region 额外功能

    // 添加消息
    const addMessage = list => {
      var eventCode = '';
      if (list.status === 2) {
        eventCode = 'M006';
      }
      const capabilityIdArray = [];
      const projectNameArray = [];
      const startdatetimeArr = [];
      const finishdatetimeArr = [];
      const ownerIdArr = [];
      const ownerNameArr = [];
      if (list.experimentCapabilityList.length > 0) {
        list.experimentCapabilityList.forEach(exp => {
          if (exp.operationType !== 3) {
            if (exp.ownerIds && exp.ownerIds.length > 0) {
              ownerIdArr.push(exp.ownerIds.join('|'));
              ownerNameArr.push(getNamesByid(exp.ownerId).join('|'));
            } else {
              ownerIdArr.push(0);
              ownerNameArr.push(0);
            }
            if (exp.sourceId) {
              capabilityIdArray.push(exp.sourceId);
            } else {
              capabilityIdArray.push(exp.id);
            }
            projectNameArray.push(exp.sourceName);
            if (exp.startDateTime) {
              startdatetimeArr.push(exp.startDateTime);
              finishdatetimeArr.push(exp.finishDateTime);
            } else {
              startdatetimeArr.push(0);
              finishdatetimeArr.push(0);
            }
          }
        });
      }
      const params = {
        eventCode: eventCode,
        senderName: getNameByid(datas.currentAccountId),
        receiverIdArray: ownerIdArr.join(','),
        receiverNameArray: ownerNameArr.join(','),
        todoStartTimeArray: startdatetimeArr.join(','),
        todoEndTimeArray: finishdatetimeArr.join(','),
        c_ids: capabilityIdArray.join(','),
        c_b_sampleNoArray: datas.topForm.secSampleNum,
        c_b_samplesIdArray: list.sampleId,
        c_b_projectNameArray: projectNameArray.join(','),
        c_b_capabilityIdArray: capabilityIdArray.join(',')
      };
      addAllocationMsg(params).then(res => {
        // if (res !== false) {
        // }
      });
    };

    // 判断数据是否更新
    function isChangeDatas(newList, oldList) {
      if (newList.length !== oldList.length) {
        mittBus.reloadAllocation = true;
      } else {
        newList.forEach((list, index) => {
          if (
            list.order !== oldList[index].order ||
            list.method !== oldList[index].method ||
            list.ownerId !== oldList[index].ownerId ||
            list.startDateTime !== oldList[index].startDateTime ||
            list.requirement !== oldList[index].requirement
          ) {
            mittBus.reloadAllocation = true;
            return false;
          }
        });
      }
    }

    // #endregion

    // #region 立即执行的方法

    // 判定是否只读, 只有分配到当前账号的负责人才能编辑，否则只读
    function checkReadOnly(ownerId) {
      if (ownerId === datas.currentAccountId) {
        datas.ownerView = true;
        if (route.params.isView === '1') {
          datas.readOnly = true;
        } else {
          datas.readOnly = false;
        }
      } else {
        datas.readOnly = true;
        datas.ownerView = false;
      }
    }

    // 获取具有检测执行/检测详情权限的人员
    getRowDataUserList({}).then(res => {
      if (res.data.code === 200) {
        datas.userOptions = res.data.data;
      }
    });

    mittBus.$on('saveAllocation', msg => {
      allocationItem();
      mittBus.reloadAllocation = false;
    });

    function getOrgList() {
      const params = {
        condition: '',
        orderBy: '',
        category: 0,
        page: '1',
        limit: '-1'
      };
      getList(params).then(res => {
        datas.platformOptions = [];
        res.data.data.list.forEach(item => {
          datas.platformOptions.push({ id: item.id, no: item.no, name: item.name });
        });
      });
    }
    getOrgList();

    // #endregion

    const isDisabled = computed({
      get: () => (datas.capabilityItemDetail.status !== 1 && datas.capabilityItemDetail.status !== 2) || datas.readOnly
    });

    watch(
      () => datas.list,
      (newValue, oldValue) => {
        isChangeDatas(newValue, datas.oldList);
      },
      {
        deep: true,
        immediate: false
      }
    );

    watch(
      () => datas.tableKey,
      (newValue, oldValue) => {
        nextTick(() => {
          rowDrop();
          if (datas.selectList.length > 0) {
            datas.selectList.forEach(item => {
              tableRef.value.toggleRowSelection(item, true);
            });
          }
        });
      }
    );

    watch(
      () => datas.keyParamsKey,
      (newValue, oldValue) => {
        nextTick(() => {
          keyDivDrop();
          datas.list.forEach(item => {
            if (item.sourceId === datas.capabilityItemDetail.sourceId) {
              item.childList.forEach(ele => {
                ele.order = datas.capabilityItemDetail.keyParams.findIndex(obj => obj.keyParamName === ele.sourceName);
              });
            }
          });
        });
      }
    );

    onMounted(() => {
      rowDrop();
      getDistributionInfo();
    });

    return {
      ...toRefs(datas),
      addMessage,
      getPermissionBtn,
      getDetectionBasisList,
      enterPopver,
      leavePopver,
      getNamesByid,
      drageHeader,
      isEditable,
      showRow,
      removeSelect,
      changeOwner,
      changeMethod,
      changeDate,
      changeCheckAllocation,
      tableRef,
      selectUser,
      selectformRef,
      selectDialogSuccess,
      remove,
      handleSelectionChange,
      sortChange,
      addItem,
      handleChangeMethod,
      closeDialog,
      selectedItems,
      allocationItem,
      cancelAllocation,
      editAllocation,
      openQuickAllocationDialog,
      closeQuickAllocationDialog,
      submitQuickAllocationDialog,
      selectQuickItem,
      handleRowClick,
      showCapabilityItemDetail,
      changeItemBasisId,
      changeTestRequirement,
      closeStandardDialog,
      selectStandardData,
      showStandardDialog,
      setRowKey,
      setCurrentChange,
      deleteStandard,
      closeStandardItemDialog,
      selectStandardItems,
      showStandardItemDialog,
      jumpToStandard,
      handleSampleOrdersDetail,
      deleteKeyParam,
      isDisabled,
      viewAllocation,
      getTestersName,
      changeItemStandard,
      showExternalDialog,
      changEntrustOrg,
      externalRegDialogSuccess,
      saveExternalEntrustRegList,
      externalRegFormRef
    };
  }
};
</script>
<style lang="scss" scoped>
.fr {
  float: right;
}
.englishname {
  font-size: 12px;
  transform: scale(0.8);
}
.allocation-header {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  text-align: left;
}
.allocation-btn {
  margin-bottom: 20px;
  text-align: right;
  .select-btn {
    border: 1px solid #dcdfe6;
    height: 32px;
    line-height: 32px;
    border-radius: 4px;
    padding: 0px 5px;
  }
  .select-btn:hover {
    background: #e6f1fb;
    color: #006bc5;
  }
}
.textR {
  text-align: right;
}
.item-desc {
  height: 25px;
  color: #606266;
}
.bottom-btn {
  text-align: right;
  padding-right: 20px;
  position: fixed;
  left: 0;
  bottom: 0;
  height: 4rem;
  line-height: 4rem;
  background: $background-color;
  width: 100%;
  z-index: 1000;
}
.allocation-table {
  :deep(.el-table__body-wrapper) {
    tbody {
      .ghost {
        background-color: #cce4f8 !important;
      }
      .drag {
        background: #e9e9eb !important;
        background-image: linear-gradient(#e9e9eb, #ffffff) !important;
      }
    }
  }
  .item {
    display: flex;
    align-items: center;
    .custom-icon {
      padding: 2px 5px;
      margin-right: 10px;
    }
  }
}
.el-select {
  position: relative;
  .el-select__tags {
    position: inherit;
    transform: translateY(0);
    padding: 3px 0;
    min-height: 28px;
  }
  .el-select__tags ~ .el-input {
    height: 100%;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    .el-input__inner {
      min-height: 20px;
      height: 100% !important;
    }
  }
  .el-select__input.is-mini {
    min-height: 20px;
  }
}
.form-dialog.el-form .el-form-item {
  margin-bottom: 18px;
}
.testMethods {
  width: 100%;
  display: inline-block;
  word-wrap: break-word;
}

:deep(.contentRight .el-form-item__label) {
  font-size: 1rem;
  font-weight: 550;
  color: #303133 !important;
}

.capability-name {
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
  color: $menuActiveText;
  text-align: left;
  max-width: 252px;
}
.capability-engname {
  font-size: 12px;
  line-height: 24px;
  color: $menuActiveText;
  margin-left: 10px;
  text-align: left;
  max-width: 202px;
}

#sample-no {
  cursor: pointer;
  color: $menuActiveText;
}

#standard-popover {
  background: #f4f4f5;
  border: 1px solid #e9e9eb;
  border-radius: 4px;
}

.standard-item.el-tag {
  width: 95%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  background-color: transparent;
}

#popover-table.el-table {
  color: red;
  :deep(.el-table__row) {
    td {
      padding: 0px !important;
      color: $menuActiveText;
    }
  }
}

.delete-standard:hover {
  border-radius: 50%;
  cursor: pointer;
  background: $tes-primary;
  color: #ffffff;
}

.standard-link:hover {
  cursor: pointer;
}

.key-params-panel {
  border: 1px solid #dcdfe6;
  border-radius: 10px;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
}
.key-params-panel:hover {
  border: 1px solid $tes-primary;
  cursor: move;
  box-shadow: 1px 1px 2px 1px $tes-primary4;
}
.key-param-name {
  color: $tes-primary;
  font-weight: bold;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  display: inline-block;
  max-width: 192px;
}
.key-param-eng-name {
  color: $tes-primary;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  display: inline-block;
  margin-left: 10px;
  max-width: 142px;
  transform: scale(0.8);
}

.key-param-order {
  padding: 0px;
  margin-top: 0.8rem;
  margin-right: 0rem;
  margin-left: 0.5rem;
  width: 1.5rem;
  height: 1.5rem;
  line-height: 1.5rem;
  text-align: center;
  border-radius: 50% 50%;
  font-size: 0.8rem;
  color: #909399;
  background-color: #f0f2f5;
}

.flex-header {
  display: flex;
  flex-direction: row;
  height: 2rem;
  line-height: 2rem;
  .flex-fixed {
    overflow: auto;
    text-align: left;
    width: 20rem;
    display: flex;
    flex-direction: row;
  }
  .sampleNo {
    white-space: nowrap;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .flex-auto {
    overflow: auto;
    text-align: left;
    flex: 1;
  }
}
</style>
