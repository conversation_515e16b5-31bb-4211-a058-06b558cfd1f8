<template>
  <!-- 委托收款列表 -->
  <ListLayout :has-button-group="false">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="param">
          <div style="width: 42vw">
            <CombinationQuery
              :field-list="tableColumns"
              field-tip="委托编号/委托方/缴款方"
              @get-query-info="getQueryInfo"
              @reset-search="reset"
            />
          </div>
        </el-form-item>
        <el-form-item style="margin-left: 0px">
          <el-button size="large" type="text" class="searchBtn" @click="search" @keyup.prevent @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
          /></el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" :model="searchForm" label-width="110px" label-position="right">
            <el-form-item label="登记日期：">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="changeBZTime"
              />
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="16">
          <el-radio-group v-model="formInline.settleStatus" size="small" @change="changeRadio">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button :label="0">未结清</el-radio-button>
            <el-radio-button :label="1">已结清</el-radio-button>
          </el-radio-group>
        </el-col>
        <el-col :span="8" style="text-align: right">
          <TableColumnView binding-menu="ConsignmentCollection" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="tableList"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="item.fieldType === fieldTypesEnum.Link">
              <span
                v-if="row[item.fieldKey]"
                v-copy="row[item.fieldKey]"
                class="nowrap blue-color"
                @click="checkRegistrationDetail(row)"
                >{{ row[item.fieldKey] }}</span
              >
              <span v-else>--</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Person">
              <UserTag :name="row.regUserName || '--'" />
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Status">
              <el-tag size="small" effect="dark" :type="filterStatus[row[item.fieldKey]][0]">{{
                filterStatus[row[item.fieldKey]][1]
              }}</el-tag>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Date">
              <span>{{ formatDate(row[item.fieldKey] || '--') }}</span>
            </template>
            <span v-else>{{ row[item.fieldKey] || '--' }}</span>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" :width="colWidth.operationMultiple" fixed="right" class-name="fixed-right">
        <template #default="{ row }">
          <span class="blue-color" @click="handleCheckDetail(row)">查看</span>
          <span
            v-if="getPermissionBtn('CommissionPriceAdjustment')"
            class="blue-color"
            @click="handlePriceAdjustment(row)"
            >调价</span
          >
          <span
            v-if="getPermissionBtn('ReceiptAndPayment') && row.settleStatus === 0"
            class="blue-color"
            @click="handleCollection(row)"
            >收款</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
    <template #other>
      <!-- 委托调价 -->
      <DrawerAdjustPrice :drawer-visible="drawerVisible" :detail-info="detailInfo" @closeDrawer="closeDialog" />
      <!-- 收付款 -->
      <DrawerCollection
        :drawer-show="drawerShow"
        :drawer-type="drawerType"
        :detail-info="detailInfo"
        @closeDrawer="closeDrawerCollection"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs } from 'vue';
import Pagination from '@/components/Pagination';
import UserTag from '@/components/UserTag';
import { getTaskRegistrationList } from '@/api/task-registration';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { useStore } from 'vuex';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import ListLayout from '@/components/ListLayout';
import { colWidth } from '@/data/tableStyle';
import CombinationQuery from '@/components/CombinationQuery';
import TableColumnView from '@/components/TableColumnView';
import router from '@/router/index.js';
import DrawerAdjustPrice from './components/drawer-adjust-price.vue';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';
import DrawerCollection from './components/drawer-collection.vue';

export default {
  name: 'ConsignmentCollection',
  components: {
    Pagination,
    UserTag,
    ListLayout,
    CombinationQuery,
    TableColumnView,
    DrawerAdjustPrice,
    DrawerCollection
  },
  setup() {
    const store = useStore().state;
    const state = reactive({
      currentAccountId: getLoginInfo().accountId,
      activeName: '0',
      showS: false,
      drawerShow: false,
      detailInfo: {},
      drawerType: '', // 收款弹出窗类型
      drawerVisible: false,
      editFrom: ref(),
      filterStatus: {
        0: ['warning', '未结清'],
        1: ['success', '已结清']
      },
      type: 'info',
      formInline: {
        tableQueryParamList: []
      },
      searchForm: {
        dateRange: []
      },
      types: store.user.materialList,
      userOptions: store.common.nameList,
      copyUserOptions: store.common.nameList,
      tableColumns: [],
      tableList: [],
      content: '',
      radioData: '全部',
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: ''
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ],
      tableRef: ref(),
      showAddInfoDialog: false
    });
    const getList = query => {
      const params = { ...state.formInline, status: 3 };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.tableLoading = true;
      getTaskRegistrationList(params).then(res => {
        state.tableLoading = false;
        if (res !== false) {
          state.total = res.data.data.totalCount;
          state.tableList = res.data.data.list;
        }
      });
    };
    getList();
    // 重置
    function reset() {
      state.editFrom.resetFields();
      state.formInline = {
        tableQueryParamList: []
      };
      state.radioData = '全部';
      state.searchForm = {
        dateRange: []
      };
      state.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: null
      };
      getList();
    }
    // 打开 高级搜索
    const search = () => {
      state.showS = !state.showS;
      if (state.activeName === '0') {
        state.activeName = '1';
      } else {
        state.activeName = '0';
      }
    };
    // 排序
    const sortChange = data => {
      const { prop, order } = data;
      state.listQuery.orderBy = prop;
      if (order === 'ascending') {
        state.listQuery.isAsc = true;
      } else if (order === 'descending') {
        state.listQuery.isAsc = false;
      } else {
        state.listQuery.isAsc = null;
      }
    };

    // 点击审批
    const reviewTask = row => {};

    // 查看
    const iaDetail = row => {};
    // 切换tab
    const changeRadio = value => {
      getList();
    };

    // 高级搜索-登记日期-change
    const changeBZTime = date => {
      state.formInline.startRegDate = date ? formatDate(date[0]) : '';
      state.formInline.endRegDate = date ? formatDate(date[1]) : '';
    };

    // #region 组合查询

    const getQueryInfo = info => {
      state.formInline.param = info.param;
      state.formInline.tableQueryParamList = info.tableQueryParamList;
      getList();
    };

    // #endregion

    const onUpdateColumns = columns => {
      state.tableKey = state.tableKey + 1;
      state.tableColumns = columns;
    };
    // 查看费用详情
    const handleCheckDetail = row => {
      router.push({ path: '/laboratoryManagement/consignmentCollection/detail', query: row });
    };
    // 查看委托编号详情
    const checkRegistrationDetail = row => {
      router.push({ path: '/laboratoryManagement/consignment-detail', query: { id: row.id, flag: 1 } });
    };
    // 委托调价
    const handlePriceAdjustment = row => {
      state.drawerVisible = true;
      state.detailInfo = row;
    };
    const closeDialog = val => {
      state.drawerVisible = false;
      if (val) {
        getList();
      }
    };
    const handleCollection = row => {
      state.drawerShow = true;
      state.detailInfo = row;
      state.drawerType = 'add';
    };
    // 关闭收付款
    const closeDrawerCollection = val => {
      state.drawerShow = false;
      if (val) {
        getList();
      }
    };

    return {
      closeDialog,
      handleCollection,
      closeDrawerCollection,
      handlePriceAdjustment,
      handleCheckDetail,
      checkRegistrationDetail,
      getList,
      reviewTask,
      drageHeader,
      formatDate,
      changeBZTime,
      getNameByid,
      changeRadio,
      sortChange,
      ...toRefs(state),
      search,
      reset,
      getPermissionBtn,
      iaDetail,
      colWidth,
      getQueryInfo,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  }
};
</script>
<style lang="scss" scoped>
.el-form {
  .el-form-item {
    margin-bottom: 0;
  }
}

.small-dialog {
  .dialog-main {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    img {
      width: 78%;
      margin-bottom: 20px;
    }
  }
}
.blue-color {
  color: $tes-primary;
  cursor: pointer;
}
</style>
