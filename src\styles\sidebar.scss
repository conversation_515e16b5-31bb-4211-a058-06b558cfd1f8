#app {
  .main-container {
    min-height: 100%;
    transition: margin-left 0.28s;
    margin-left: $sideBarWidth;
    position: relative;
    background: $background-color2;
  }

  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth !important;
    background-color: $menuBg;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 100px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .sub-el-icon {
      margin-right: 8px;
      margin-left: -2px;
      color: currentColor;
      font-style: normal;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }
    .el-sub-menu.is-opened {
      .nest-menu {
        .el-menu-item {
          color: $tes-font3 !important;
          background: #162334 !important;
          &:hover {
            color: #fff !important;
          }
        }
        .el-menu-item.is-active {
          color: #fff !important;
          background: $menuActiveText !important;
        }
      }
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-sub-menu__title {
      display: flex;
      align-items: center;
      color: $tes-font3 !important;
      &:hover {
        // background-color: $menuHover !important;
      }
      i.iconfont {
        color: $tes-font3;
        font-size: 16px;
      }
      i.el-sub-menu__icon-arrow {
        color: $tes-font3;
      }
    }
    .el-sub-menu i,
    .el-sub-menu:hover i {
      // color: $tes-font1 !important;
    }

    .el-sub-menu.is-active {
      .el-sub-menu__title,
      .el-sub-menu__title i.iconfont {
        color: $subMenuActiveText;
      }
    }

    .is-active > .el-sub-menu__title {
      color: $subMenuActiveText !important;
    }

    .submenu-title-noDropdown.is-active {
      background: $menuActiveText !important;
      // &:hover{
      //   i.iconfont {
      //     color: $subMenuActiveText !important;
      //   }
      // }
    }
    .submenu-title-noDropdown.is-active i.iconfont {
      color: $subMenuActiveText !important;
    }

    & .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      min-width: $sideBarWidth !important;
      line-height: inherit;
      display: flex;
      flex-direction: column;
      justify-content: center;
      &.is-active,
      &:hover {
        // background-color: $subMenuHover !important;
        color: $subMenuActiveText !important;
      }
    }
    // 菜单打开状态，hover不变化
    .textAL {
      .el-sub-menu {
        .nest-menu {
          a {
            .is-active:hover {
              // color: $menuActiveText !important;
            }
          }
        }
      }
    }

    & .el-sub-menu .is-active {
      color: $menuActiveText !important;
    }

    .is-active {
      // background-color: $menuActiveText !important;
      // color: $menuText !important;
      &:hover {
        // color: $menuText !important;
      }
    }
    .show-title > .is-active {
      color: $menuText !important;
      border-right: 3px solid $tes-primary3;
    }
    .is-opened > .el-sub-menu__title {
      i {
        // color: $menuText;
      }
    }

    .nest-menu {
      .show-title {
        .el-menu-item {
          span {
            margin-left: 8px;
          }
        }
      }
    }
    .textAC {
      .show-title {
        .el-menu-item {
          padding-left: 15px !important;
          span {
            margin-left: 15px;
          }
        }
      }
    }
  }
  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }

    .main-container {
      margin-left: 54px;
    }

    .submenu-title-noDropdown {
      justify-content: center;
      padding: 0 !important;
      &:hover {
        color: $subMenuActiveText !important;
      }
      .iconfont {
        margin-right: 0 !important;
      }

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }
      }
    }

    .el-sub-menu {
      overflow: hidden;

      & > .el-sub-menu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 5px;
        }

        .el-sub-menu__icon-arrow {
          // display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-sub-menu > .el-sub-menu__title > span,
      .submenu-title-noDropdown > span {
        height: 0;
        width: 0;
        overflow: hidden;
        visibility: hidden;
        display: inline-block;
      }

      .el-sub-menu {
        .el-menu-item,
        .el-sub-menu__title {
          justify-content: center;
          .iconfont {
            margin: 0 !important;
          }
        }
      }
    }
  }

  .el-menu--vertical {
    .el-menu {
      background: $menuBg !important;
    }
  }

  .el-menu--collapse .el-menu .el-sub-menu {
    min-width: $sideBarWidth !important;
  }

  .el-menu--collapse {
    // .el-sub-menu:hover {
    //   background-color: #303133;
    // }
    .el-sub-menu i,
    .el-sub-menu:hover i {
      // color: $tes-font1 !important;
    }
    // .el-sub-menu.is-active i,
    // .el-sub-menu.is-active:hover i,
    // .el-menu-item.is-active i {
    //   color: $menuActiveText !important;
    // }
    .is-active > .el-sub-menu__title,
    .is-active.submenu-title-noDropdown {
      color: $subMenuActiveText !important;

      i {
        color: $subMenuActiveText !important;
      }
    }
    .el-sub-menu__title {
      .el-sub-menu__icon-arrow {
        display: none;
      }
    }
  }
  .el-menu-item * {
    word-break: break-word;
    white-space: normal;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform 0.28s;
      width: $sideBarWidth !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
  // 收起菜单样式
  .close-sidebar-content {
    .el-sub-menu:hover {
      background: $subMenuBg;
    }
    // .is-active > .el-sub-menu__title {
    //   color: $menuActiveText !important;
    //   i {
    //     color: #ffffff;
    //   }
    // }
    // .is-active:hover {
    //   // background-color: $subMenuHover !important;
    //   i {
    //     color: #303133 !important;
    //   }
    // }
  }
}

// when menu collapsed
.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 16px;
    }
    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }
  }

  .nest-menu .el-sub-menu > .el-sub-menu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      // background-color: $menuHover !important;
    }
  }
  .nest-menu .el-menu-item {
    height: 40px;
    line-height: 40px;
    &.is-active {
      color: $menuText !important;
      background-color: $menuActiveText !important;
    }
  }

  // the scroll bar appears when the subMenu is too long
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
