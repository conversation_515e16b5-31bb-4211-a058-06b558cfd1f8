import request from '@/utils/request';

// 任务分配-任务视图列表
export function getTaskViewList(data) {
  return request({
    url: '/api-orders/metering/sample/pageList',
    method: 'post',
    data
  });
}

// 任务分配-子任务视图列表
export function getSubTaskViewList(data) {
  return request({
    url: '/api-orders/metering/experiment/experimentPageList',
    method: 'post',
    data
  });
}

/**
 * 任务分配-检测项目委外登记保存或修改
 * http://192.168.69.72:8800/doc.html#/orders/%E6%A3%80%E6%B5%8B%E9%A1%B9%E7%9B%AE%E5%A7%94%E5%A4%96%E7%99%BB%E8%AE%B0/saveUsingPOST_7
 * @param {*} data
 * @returns
 */
export function saveExternalEntrustReg(data) {
  return request({
    url: '/api-orders/orders/experimententrust/saveOrUpdate',
    method: 'post',
    data
  });
}

// 任务分配-检测项目委外登记列表
export function getExternalEntrustList(data) {
  return request({
    url: '/api-orders/orders/experimententrust/list',
    method: 'post',
    data
  });
}

// 任务分配-检测项目委外登记信息
export function getExternalInfoByExperimentId(experimentId) {
  return request({
    url: `/api-orders/orders/experimententrust/info/${experimentId}`,
    method: 'get'
  });
}
