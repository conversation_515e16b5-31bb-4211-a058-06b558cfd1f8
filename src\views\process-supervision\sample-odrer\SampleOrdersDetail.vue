<template>
  <!-- 样品详情 -->
  <DetailLayout :main-offset-top="105">
    <template #page-header>
      <div class="header-flex flex-start">
        <el-space :size="40" :spacer="spacer">
          <div class="item-column">
            <span class="item-label">样品编号</span>
            <div class="item-content" style="position: relative">
              <span v-if="samplesDetails.isUrgent" class="urgent2">急</span>{{ samplesDetails.secSampleNum }}
            </div>
          </div>
          <div class="item-column">
            <span class="item-label">试验负责人</span>
            <div class="item-content">
              <span class="iconfont tes-user1" />{{
                samplesDetails.ownerId ? getNameByid(samplesDetails.ownerId) : '--'
              }}
            </div>
          </div>
          <div class="item-column">
            <span class="item-label">进度</span>
            <div class="item-content">
              <div v-show="samplesDetails.status === 0" class="warning"><i class="tes-clock iconfont" />样品下达</div>
              <div v-show="samplesDetails.status === 1 || samplesDetails.status === 2" class="warning">
                <i class="tes-jiance iconfont" />检测中
              </div>
              <div v-show="samplesDetails.status === 3" class="primary"><i class="tes-Vector iconfont" />报告审批</div>
              <div v-show="samplesDetails.status === 4" class="success"><i class="tes-success iconfont" />检测完成</div>
            </div>
          </div>
          <div class="item-column">
            <span class="item-label">二维码</span>
            <div class="item-content">
              <QRCodeTrigger :value="qrCodeData" />
            </div>
          </div>
        </el-space>
      </div>
    </template>
    <el-collapse v-model="activeNames" class="collapse-wrap" @change="handleChange">
      <el-collapse-item name="1">
        <template #title>
          <div class="collapse-header-title">样品信息</div>
        </template>
        <div class="collapse-content">
          <!-- 检测信息-->
          <div v-if="openView['detectionInfo']" class="collapse-item">
            <div class="collapse-content-title" style="position: relative">
              <span class="line-space" />检测信息
              <div style="position: absolute; right: 0">
                <el-tag
                  v-if="sampleStandardInfo?.standardProductName"
                  type="primary"
                  closable
                  class="mr-2"
                  @close="handleCloseStandard"
                  >{{ sampleStandardInfo.standardProductName }}</el-tag
                >
                <el-button
                  v-else
                  size="small"
                  icon="el-icon-plus"
                  @click="handleStandard"
                  @keyup.prevent
                  @keydown.enter.prevent
                  >标准</el-button
                >
                <el-button size="small" icon="el-icon-edit" @click="handleEdit" @keyup.prevent @keydown.enter.prevent
                  >编辑</el-button
                >
              </div>
            </div>
            <el-row :gutter="20" class="collapse-item">
              <el-col
                v-for="viewItem in pageViewGroup[`detectionInfo`]"
                :key="viewItem.fieldKey"
                :span="Number(viewItem.columnWidth)"
                ><span class="title">{{ viewItem.fieldName }}：</span>
                <span class="txt">
                  <template v-if="viewItem.fieldType == 'text'">
                    {{ samplesDetails.certificatePrintEntity?.[viewItem.fieldKey] || '--' }}
                  </template>
                </span>
                <!-- 文本 -->
              </el-col>
            </el-row>
            <el-divider />
          </div>
          <!-- 样品信息-->
          <div v-if="openView['sampleInfo']" class="collapse-item">
            <div class="collapse-content-title"><span class="line-space" />样品信息</div>
            <el-row :gutter="20" class="collapse-item">
              <el-col v-for="item in pageViewGroup[`sampleInfo`]" :key="item.fieldKey" :span="Number(item.columnWidth)">
                <span class="title">{{ item.fieldName }}：</span>
                <!-- 文本 -->
                <template v-if="item.fieldType == 'text'">
                  <span class="txt">
                    {{ samplesDetails[item.fieldKey] || '--' }}
                  </span>
                </template>
                <!-- 自定义 -->
                <template v-if="item.fieldType == 'custom'">
                  <span v-if="item.fieldKey == 'type'" class="txt">
                    {{ samplesDetails.type ? dictionaryTypeJson[samplesDetails.type.toString()] : '--' }}
                  </span>
                  <span v-else-if="item.fieldKey == 'orderCode'" class="txt">
                    {{ samplesDetails.ordersEntity?.orderCode || '--' }}
                  </span>
                  <span v-else-if="item.fieldKey == 'thirdNo'" class="txt">
                    {{ samplesDetails.ordersEntity?.thirdNo || '--' }}
                  </span>
                  <span v-else-if="item.fieldKey == 'sampleNum'" class="txt">
                    {{ samplesDetails.sampleNum }}
                    {{ filterSampleUnitToName(samplesDetails.sampleUnit) || samplesDetails.sampleUnit }}
                  </span>
                  <el-popover
                    v-else-if="item.fieldKey == 'prodType'"
                    placement="top"
                    trigger="hover"
                    :content="samplesDetails[item.fieldKey]"
                  >
                    <template #reference>
                      <span class="txt">
                        {{ samplesDetails[item.fieldKey] || '--' }}
                      </span>
                    </template>
                  </el-popover>
                  <span v-else-if="item.fieldKey == 'thirdType'" class="txt">
                    {{ samplesDetails.ordersEntity ? filterComeFrom(samplesDetails.ordersEntity.thirdType) : '--' }}
                  </span>
                  <span v-else-if="item.fieldKey == 'voltName'" class="txt">
                    {{ dictionaryJson[samplesDetails[item.fieldKey]] || '--' }}
                  </span>
                </template>
              </el-col>
            </el-row>
            <el-divider />
          </div>
          <!--登记信息-->
          <div v-if="openView['registInfo']" class="collapse-item">
            <div class="collapse-content-title"><span class="line-space" />登记信息</div>
            <el-row :gutter="20" class="collapse-item">
              <el-col v-for="item in pageViewGroup[`registInfo`]" :key="item.fieldKey" :span="Number(item.columnWidth)">
                <span class="title">{{ item.fieldName }}：</span>
                <!-- 文本 -->
                <template v-if="item.fieldType == 'text'">
                  <span class="txt">
                    {{ samplesDetails[item.fieldKey] || '--' }}
                  </span>
                </template>
                <!-- 日期 -->
                <template v-if="item.fieldType == 'date'">
                  {{ samplesDetails[item.fieldKey] ? formatDateTime(samplesDetails[item.fieldKey]) : '--' }}
                </template>
                <!-- 人员 -->
                <template v-if="item.fieldType == 'person'">
                  <UserTag :name="samplesDetails[item.fieldKey] ? getNameByid(samplesDetails[item.fieldKey]) : '--'" />
                </template>
              </el-col>
            </el-row>
            <el-divider />
          </div>
          <!-- 铭牌信息-->
          <div v-if="tenantInfo.type === 0 && openView['nameplateInfo']" class="collapse-item">
            <div class="collapse-content-title"><span class="line-space" />铭牌信息</div>
            <el-row :gutter="20" class="collapse-item">
              <el-col
                v-for="item in pageViewGroup[`nameplateInfo`]"
                :key="item.fieldKey"
                :span="Number(item.columnWidth)"
              >
                <span class="title">{{ item.fieldName }}：</span>
                <span class="txt">
                  <template v-if="item.fieldType == 'person'">
                    <UserTag :name="samplesDetails[item.fieldKey] ? samplesDetails[item.fieldKey] : '--'" />
                  </template>
                  <template v-if="item.fieldType == 'text'">
                    {{ samplesDetails[item.fieldKey] || '--' }}
                  </template>
                  <template v-if="item.fieldType == 'date'">
                    {{ samplesDetails[item.fieldKey] ? formatDateTime(samplesDetails[item.fieldKey]) : '--' }}
                  </template>
                </span>
              </el-col>
            </el-row>
            <el-divider />
          </div>
          <!-- 封样信息-->
          <div v-if="tenantInfo.type === 0 && openView['sealingSampleInfo']" class="collapse-item">
            <div class="collapse-content-title"><span class="line-space" />封样信息</div>
            <el-row :gutter="20" class="collapse-item">
              <el-col
                v-for="item in pageViewGroup[`sealingSampleInfo`]"
                :key="item.fieldKey"
                :span="Number(item.columnWidth)"
              >
                <span class="title">{{ item.fieldName }}：</span>
                <span class="txt">
                  <template v-if="item.fieldType == 'person'">
                    <UserTag :name="samplesDetails[item.fieldKey] ? samplesDetails[item.fieldKey] : '--'" />
                  </template>
                  <template v-if="item.fieldType == 'text'">
                    {{ samplesDetails[item.fieldKey] || '--' }}
                  </template>
                  <template v-if="item.fieldType == 'date'">
                    {{ samplesDetails[item.fieldKey] ? formatDateTime(samplesDetails[item.fieldKey]) : '--' }}
                  </template>
                </span>
              </el-col>
            </el-row>
            <el-divider />
          </div>
          <!-- 采购入库信息-->
          <div
            v-if="(samplesDetails.type === 1 || samplesDetails.type === 8) && openView['procureInfo']"
            class="collapse-item"
          >
            <div class="collapse-content-title"><span class="line-space" />采购入库信息</div>
            <el-row :gutter="20" class="collapse-item">
              <el-col
                v-for="item in pageViewGroup[`procureInfo`]"
                :key="item.fieldKey"
                :span="Number(item.columnWidth)"
              >
                <span class="title">{{ item.fieldName }}：</span>
                <span class="txt">
                  <template v-if="item.fieldType == 'person'">
                    <UserTag :name="samplesDetails[item.fieldKey] ? samplesDetails[item.fieldKey] : '--'" />
                  </template>
                  <template v-if="item.fieldType == 'text'">
                    {{ samplesDetails[item.fieldKey] || '--' }}
                  </template>
                  <template v-if="item.fieldType == 'date'">
                    {{ samplesDetails[item.fieldKey] ? formatDateTime(samplesDetails[item.fieldKey]) : '--' }}
                  </template>
                  <template v-if="item.fieldType == 'custom'">
                    {{ samplesDetails.inputWarehouseQuantity ? samplesDetails.inputWarehouseQuantity : '--' }}
                    {{ filterSampleUnitToName(samplesDetails.inputWarehouseUnit) || samplesDetails.inputWarehouseUnit }}
                  </template>
                </span>
              </el-col>
            </el-row>
          </div>
          <!-- 生产信息-->
          <div
            v-if="
              (samplesDetails.type === 2 ||
                samplesDetails.type === 3 ||
                samplesDetails.type === 4 ||
                samplesDetails.type === 5 ||
                samplesDetails.type === 6 ||
                samplesDetails.type === 7) &&
              openView[`${samplesDetails.type}-productInfo`]
            "
            class="collapse-item"
          >
            <div class="collapse-content-title"><span class="line-space" />生产信息</div>
            <el-row class="collapse-item">
              <el-col
                v-for="item in pageViewGroup[`${samplesDetails.type}-productInfo`]"
                :key="item.fieldKey"
                :span="Number(item.columnWidth)"
              >
                <span class="title">{{ item.fieldName }}：</span>
                <!-- 文本 -->
                <template v-if="item.fieldType == 'text'">
                  <span class="txt">
                    {{ samplesDetails[item.fieldKey] || '--' }}
                  </span>
                </template>
                <!-- 日期 -->
                <template v-if="item.fieldType == 'date'">
                  {{ samplesDetails[item.fieldKey] ? formatDateTime(samplesDetails[item.fieldKey]) : '--' }}
                </template>
                <!-- 人员 -->
                <template v-if="item.fieldType == 'person'">
                  <UserTag :name="samplesDetails[item.fieldKey] ? getNameByid(samplesDetails[item.fieldKey]) : '--'" />
                </template>
                <!-- 自定义 -->
                <template v-if="item.fieldType == 'custom'">
                  <span v-if="item.fieldKey == 'productionQuantity'" class="txt">
                    {{ samplesDetails.productionQuantity ? samplesDetails.productionQuantity : '--' }}
                    {{ filterSampleUnitToName(samplesDetails.productionUnit) }}
                  </span>
                  <span v-if="item.fieldKey == 'productionProcedureNo'" class="txt">
                    {{ samplesDetails.productionProcedureNo ? samplesDetails.productionProcedureNo + '-' : ''
                    }}{{ samplesDetails.productionProcedure ? samplesDetails.productionProcedure : '--' }}
                  </span>
                  <span v-if="item.fieldKey == 'salesOrderNo' && samplesDetails.type == 3" class="txt">
                    {{ samplesDetails.ordersEntity.salesOrderNo ? samplesDetails.ordersEntity.salesOrderNo : '--' }}
                  </span>
                  <span v-if="item.fieldKey == 'salesItemNo' && samplesDetails.type == 3" class="txt">
                    {{ samplesDetails.ordersEntity.salesItemNo ? samplesDetails.ordersEntity.salesItemNo : '--' }}
                  </span>
                  <span v-if="item.fieldKey == 'projectName' && samplesDetails.type == 3" class="txt">
                    {{ samplesDetails.ordersEntity.projectName ? samplesDetails.ordersEntity.projectName : '--' }}
                  </span>
                </template>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-collapse-item>
      <!--委托项目-->
      <el-collapse-item v-if="tenantInfo.type === 2" name="2">
        <template #title>
          <div class="collapse-header-title">委托项目</div>
        </template>
        <div class="collapse-content">
          <div class="collapse-top">
            <el-row :gutter="20" class="collapse-item">
              <el-col :span="8">
                <span class="title">检测依据：</span>
                <span class="txt">{{ samplesDetails.standard ? samplesDetails.standard : '--' }}</span>
              </el-col>
              <el-col :span="8">
                <span class="title">检测类别：</span>
                <span class="txt">
                  {{
                    samplesDetails.experimentType === 1
                      ? '型式实验'
                      : samplesDetails.experimentType === 2
                      ? '部分性能'
                      : '--'
                  }}</span
                >
              </el-col>
              <el-col :span="8">
                <span class="title">执行策略：</span>
                <span class="txt">{{ samplesDetails.testLevel ? samplesDetails.testLevel : '--' }}</span>
              </el-col>
            </el-row>
          </div>
          <el-table :data="samplesDetails.taskSampleExperimentOrders" fit border class="dark-table base-table">
            <el-table-column type="index" label="序号" :width="colWidth.serialNo" />
            <el-table-column prop="itemName" label="检测项目" />
          </el-table>
        </div>
      </el-collapse-item>
      <!--检测任务-->
      <el-collapse-item name="9">
        <template #title>
          <div class="collapse-header-title">检测任务</div>
        </template>
        <el-empty
          v-if="
            capabilityList.length === 0 &&
            !samplesDetails.name &&
            !samplesDetails.startDate &&
            !samplesDetails.assignedTime
          "
          :image="emptyImg"
          description="暂无数据"
        />
        <div v-else class="collapse-content">
          <div class="collapse-top">
            <el-row :gutter="20" class="collapse-item">
              <el-col :span="4">
                <span class="title">试验负责人：</span>
                <span class="txt">
                  <UserTag :name="samplesDetails.ownerId ? getNameByid(samplesDetails.ownerId) : '--'" />
                </span>
              </el-col>
              <el-col :span="7">
                <span class="title">试验时间：</span>
                <span v-if="samplesDetails.startDate !== ''" class="txt">
                  {{ formatDateTime(samplesDetails.startDate) }} ~
                  {{ formatDateTime(samplesDetails.finishedDate) }}</span
                >
                <span v-else class="txt"> --</span>
              </el-col>
              <el-col :span="6">
                <span class="title">下达时间：</span>
                <span class="txt">{{
                  samplesDetails.assignedTime ? formatDateTime(samplesDetails.assignedTime) : '--'
                }}</span>
              </el-col>
              <el-col :span="6">
                <span class="title">检验策略：</span>
                <span class="txt">{{ strategyName || '--' }}</span>
              </el-col>
            </el-row>
          </div>
          <el-table
            v-if="openView['detectionTask-table']"
            :data="capabilityList"
            fit
            border
            class="dark-table base-table"
          >
            <el-table-column type="index" label="序号" width="50px" />
            <el-table-column
              v-for="item in pageViewGroup['detectionTask-table']"
              :key="item.fieldKey"
              :prop="item.fieldKey"
              :width="item.isMinWidth ? '' : item.columnWidth"
              :min-width="item.isMinWidth ? item.columnWidth : ''"
              :label="item.fieldName"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <template v-if="item.fieldType == 'text'">
                  {{ row[item.fieldKey] || '--' }}
                </template>
                <template v-if="item.fieldType == 'date'">
                  {{ formatDateTime(row[item.fieldKey]) || '--' }}
                </template>
                <template v-if="item.fieldType == 'status'">
                  <el-tag v-if="row.status === 1" size="small" effect="dark" type="warning">未分配</el-tag>
                  <el-tag v-if="row.status === 2" size="small" effect="dark" type="warning">待提交</el-tag>
                  <el-tag v-if="row.status === 3" size="small" effect="dark" type="warning">待审核</el-tag>
                  <el-tag v-if="row.status === 5" size="small" effect="dark" type="success">已通过</el-tag>
                  <el-tag v-if="row.status === -5" size="small" effect="dark" type="info">已删除</el-tag>
                </template>
                <template v-if="item.fieldType == 'person'">
                  <div v-if="row[item.fieldKey]">
                    <UserTag
                      v-for="(userItem, index) in getNamesByid(row[item.fieldKey])"
                      :key="index"
                      :name="userItem"
                    />
                  </div>
                  <span v-else> --</span>
                </template>
                <template v-if="item.fieldType == 'custom'">
                  <div v-if="item.fieldKey == 'capabilityName'" class="capability-name">
                    <div class="name-icons">
                      <el-tooltip v-if="row?.retestSourceId !== ''" placement="top-start" effect="light">
                        <template #content> 复测说明：{{ row.retestReason }} </template>
                        <span class="custom-icon text-copy">复</span>
                      </el-tooltip>
                      <span v-if="row.isRetest === 1" class="custom-icon text-origin">源</span>
                      <span v-if="row.isEditMore > 0" class="custom-icon text-change">改</span>
                      <el-tooltip v-if="row.isBack === 1" placement="top-start" effect="light">
                        <template #content> 退回原因：{{ row.backReason }} </template>
                        <span class="custom-icon text-back">退</span>
                      </el-tooltip>
                    </div>
                    <span>{{ row.capabilityName || '--' }}</span>
                  </div>
                  <div v-if="item.fieldKey == 'startDateTime'">
                    <span v-if="row.startDateTime" class=""
                      >{{ formatDateTime(row.startDateTime) }} ~ {{ formatDateTime(row.finishDateTime) }}</span
                    >
                    <span v-else> - </span>
                  </div>
                  <template v-if="item.fieldKey == 'reviewDateTime'">
                    <div v-if="row.status !== 2">
                      {{ formatDateTime(row.reviewDateTime) || '--' }}
                    </div>
                    <span v-else> --</span>
                  </template>
                </template>
              </template>
            </el-table-column>
            <el-table-column prop="itemName" label="操作" :width="150">
              <template #default="{ row }">
                <span v-if="row.status === 5 || row.status === 3" class="blue-color" @click="goPdf(row)"
                  >查看原始记录</span
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-collapse-item>
      <!--检测报告-->
      <el-collapse-item v-if="openView['reportInfo']" name="10" class="ru-ku">
        <template #title>
          <div class="collapse-header-title">检测报告</div>
        </template>
        <div class="collapse-content">
          <el-table :data="reportList" fit border class="dark-table base-table">
            <el-table-column type="index" label="序号" :width="70" />
            <el-table-column
              v-for="item in pageViewGroup['reportInfo']"
              :key="item.fieldKey"
              :prop="item.fieldKey"
              :width="item.isMinWidth ? '' : item.columnWidth"
              :min-width="item.isMinWidth ? item.columnWidth : ''"
              :label="item.fieldName"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <template v-if="item.fieldType == 'text'">
                  {{ row[item.fieldKey] }}
                </template>
                <template v-if="item.fieldType == 'date'">
                  <span>{{ formatDateTime(row[item.fieldKey]) }}</span>
                </template>
                <template v-if="item.fieldType == 'person'">
                  <UserTag :name="getNameByid(row[item.fieldKey])" />
                </template>
                <template v-if="item.fieldType == 'status'">
                  <el-tag v-if="row.examineStatus === 1" size="small" effect="dark" type="info">待提交</el-tag>
                  <el-tag v-if="row.examineStatus === 2" size="small" effect="dark" type="warning">待审核</el-tag>
                  <el-tag v-if="row.examineStatus === 3" size="small" effect="dark" type="warning">待签字</el-tag>
                  <el-tag v-if="row.examineStatus === 4" size="small" effect="dark" type="warning">待盖章</el-tag>
                  <el-tag v-if="row.examineStatus === 5" size="small" effect="dark" type="warning">待发送</el-tag>
                  <el-tag v-if="row.examineStatus === 6" size="small" effect="dark" type="success">已发送</el-tag>
                </template>
                <template v-if="item.fieldType == 'custom'">
                  <template v-if="item.fieldKey == 'result'"> {{ resultJson[row.result] || '--' }} </template>
                  <template v-if="item.fieldKey == 'examineByUserId'">
                    <div v-if="row.examineByUserId">
                      <UserTag
                        v-for="rowItem in row.examineByUserId.split(',')"
                        :key="rowItem"
                        :name="getNameByid(rowItem)"
                      />
                    </div>
                    <UserTag v-else name="--" />
                  </template>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-collapse-item>
      <!-- 不良品处置-->
      <el-collapse-item v-if="showUnqualified && openView['unqualifiedInfo']" name="5" class="ru-ku">
        <template #title>
          <div class="collapse-header-title">不良品处置记录</div>
        </template>
        <div class="collapse-content">
          <el-table :data="unqualifiedInfoList" fit border class="dark-table base-table">
            <el-table-column type="index" label="序号" :width="70" />
            <el-table-column
              v-for="item in pageViewGroup['unqualifiedInfo']"
              :key="item.fieldKey"
              :prop="item.fieldKey"
              :width="item.isMinWidth ? '' : item.columnWidth"
              :min-width="item.isMinWidth ? item.columnWidth : ''"
              :label="item.fieldName"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <template v-if="item.fieldType == 'text'">
                  {{ row[item.fieldKey] }}
                </template>
                <template v-if="item.fieldType == 'link'">
                  <span class="blue-color" @click="goOut(row)">
                    {{ row[item.fieldKey] }}
                  </span>
                </template>
                <template v-if="item.fieldType == 'date'">
                  <span>{{ formatDateTime(row[item.fieldKey]) }}</span>
                </template>
                <template v-if="item.fieldType == 'person'">
                  <UserTag :name="getNameByid(row[item.fieldKey])" />
                </template>
                <template v-if="item.fieldType == 'status'">
                  <el-tag v-if="row.status === 1" size="small" effect="dark" type="warning">待分析</el-tag>
                  <el-tag v-if="row.status === 2" size="small" effect="dark" type="warning">待评审</el-tag>
                  <el-tag v-if="row.status === 3" size="small" effect="dark" type="warning">待处置</el-tag>
                  <el-tag v-if="row.status === 4" size="small" effect="dark" type="success">已完成</el-tag>
                </template>
                <template v-if="item.fieldType == 'custom'">
                  <div v-if="row.expList.length !== 0">
                    <div v-for="(it, i) in row.expList" :key="i">
                      {{ it.expName }}
                    </div>
                  </div>
                  <span v-else> --</span>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-collapse-item>
      <!--入库信息-->
      <el-collapse-item v-if="showRuKuInfo && openView['warehousingInfo']" name="3" class="ru-ku-box">
        <template #title>
          <div class="collapse-header-title">入库信息</div>
        </template>
        <el-empty v-if="warehousingInfoList?.length === 0" description="暂无数据" :image="emptyImg" />
        <div v-else>
          <!-- warehousingInfo -->
          <div v-for="(item, ii) in warehousingInfoList" :key="ii" class="collapse-content">
            <el-row :gutter="20" class="collapse-item">
              <el-col
                v-for="viewItem in pageViewGroup['warehousingInfo']"
                :key="viewItem.fieldKey"
                :span="Number(viewItem.columnWidth)"
              >
                <span class="title">{{ viewItem.fieldName }}：</span>
                <span class="txt">
                  <template v-if="viewItem.fieldType == 'date'">
                    {{ item[viewItem.fieldKey] ? formatDateTime(item[viewItem.fieldKey]) : '--' }}
                  </template>
                  <template v-if="viewItem.fieldType == 'person'">
                    <UserTag :name="item[viewItem.fieldKey] ? getNameByid(item[viewItem.fieldKey]) : '--'" />
                  </template>
                  <template v-if="viewItem.fieldType == 'text'">
                    {{ item[viewItem.fieldKey] || '--' }}
                  </template>
                  <template v-if="viewItem.fieldType == 'custom'">
                    <template v-if="viewItem.fieldKey == 'inputWarehouseQuantity'">
                      {{ item[viewItem.fieldKey] || '--' }}
                      {{ item.inputWarehouseUnit ? filterSampleUnitToName(item.inputWarehouseUnit) : '-' }}
                    </template>
                    <template v-if="viewItem.fieldKey == 'sampleTemplateFileList'">
                      <div v-if="item[viewItem.fieldKey].length > 0">
                        <div
                          v-for="val in item[viewItem.fieldKey]"
                          :key="val.fileId"
                          class="blue-color"
                          @click="down(val.url)"
                        >
                          <i class="el-icon-paperclip" />
                          {{ val?.fileName }}
                          <span v-if="val.labelList.length">
                            （{{
                              val.labelList
                                .map(label => {
                                  return label.labelName;
                                })
                                .toString()
                            }}）
                          </span>
                        </div>
                      </div>
                      <span v-else>--</span>
                    </template>
                  </template>
                </span>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
    <!-- 二维码弹出框 -->
    <QRCodePopup title="样品二维码" />
    <!-- 检测信息编辑 -->
    <DialogDetection
      :dialog="isEditDetectionInfo"
      :page-view="pageViewGroup['detectionInfo']"
      :info="samplesDetails.certificatePrintEntity"
      @close="handleCloseDialog"
    />
    <ChooseStandard
      :show="dialogStandard"
      :material-code="samplesDetails.mateType"
      :already-select="sampleStandardInfo"
      @close="closeDialogStandard"
    />
  </DetailLayout>
</template>

<script>
import { h, ref, reactive, toRefs, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import {
  findBySampleIdblp,
  capabilityBysampleId,
  findBySampleId,
  samplesDetails,
  checkTabMenu,
  samplestandardSave,
  findBySampleIdStandard
} from '@/api/order';
// import { getName } from '@/api/login'
import { formatDate, formatDateTime } from '@/utils/formatTime';
import { getNameByid, getNamesByid } from '@/utils/common';
import { getStrategy } from '@/api/strategy';
import router from '@/router';
import { getLoginInfo } from '@/utils/auth';
import { mapGetters } from 'vuex';
import { filterSampleUnitToName } from '@/utils/formatJson';
import { getDictionary } from '@/api/user';
import { ElDivider } from 'element-plus';
import DetailLayout from '@/components/DetailLayout';
import UserTag from '@/components/UserTag';
import { colWidth } from '@/data/tableStyle';
import { QRCodePopup, QRCodeTrigger } from '@/components/QRCodePopup';
import emptyImg from '@/assets/img/empty-data.png';
import { getViewByBindingMenu } from '@/api/tableView';
import { formatViewData } from '@/utils/formatJson';
import DialogDetection from './components/DialogDetection.vue';
import ChooseStandard from '@/components/BusinessComponents/ChooseStandard.vue';
import { ElMessage } from 'element-plus';
// tenantInfo.type
// type === 0, // 第二方企业
// type === 1, // 工厂侧实验室
// type === 2, // 第三方检测机构
export default {
  name: 'RecordSampleDetail',
  components: { DetailLayout, UserTag, QRCodePopup, QRCodeTrigger, DialogDetection, ChooseStandard },
  setup() {
    const spacer = h(ElDivider, { direction: 'vertical' });
    const route = useRoute();
    const state = reactive({
      sampleId: route.query.sampleId,
      reportList: [], // 检测报告列表
      resultJson: {
        0: '合格',
        1: '不合格',
        2: '不判定'
      },
      orderId: route.query.orderId,
      samplesDetails: {
        certificatePrintEntity: {}
      },
      isEditDetectionInfo: false,
      dictionaryJson: {}, // 电压等级字典json
      dictionaryTypeJson: {},
      warehousingInfoList: [],
      unqualifiedInfoList: [],
      strategyJson: {},
      capabilityList: [],
      voltageList: [],
      logininfo: getLoginInfo(),
      strategyName: '',
      tabMenuPermission: {},
      sampleStandardInfo: {},
      openView: {},
      showUnqualified: false,
      dialogStandard: false,
      selectStandard: {},
      pageViewGroup: {
        sampleInfo: {},
        registInfo: {},
        unqualifiedInfo: {},
        '2-productInfo': {},
        '3-productInfo': {},
        '4-productInfo': {},
        '5-productInfo': {},
        '6-productInfo': {},
        '7-productInfo': {},
        reportInfo: {},
        procureInfo: {},
        sealingSampleInfo: {},
        'detectionTask-table': {},
        detectionInfo: {},
        warehousingInfo: {}
      },
      showRuKuInfo: false
    });
    const qrCodeData = computed(() => {
      return {
        type: 'sample',
        id: state.sampleId,
        orderId: route.query.orderId
      };
    });
    const tableData = reactive({
      list: []
    });
    const handleEdit = () => {
      state.isEditDetectionInfo = true;
    };
    const handleStandard = () => {
      state.dialogStandard = true;
    };
    const handleCloseDialog = isRefresh => {
      state.isEditDetectionInfo = false;
      if (isRefresh) {
        getdata();
      }
    };
    const getStandardInfo = async () => {
      state.detailLoading = true;
      const response = await findBySampleIdStandard(state.sampleId).finally(() => {
        state.detailLoading = false;
      });
      if (response) {
        state.sampleStandardInfo = response.data.data[0];
      }
    };
    onMounted(() => {
      getStandardInfo();
    });
    const getDetailView = async () => {
      state.detailLoading = true;
      const res = await getViewByBindingMenu('recordSampleDetail');
      state.detailLoading = false;
      if (res) {
        state.pageViewGroup = formatViewData(res.data.data[0].sysEmployeeListConfigList);
      }
    };
    getDetailView();
    const getDictionaryList = () => {
      getDictionary('recordSampleDetail').then(res => {
        if (res) {
          res.data.data.dictionaryoption.forEach(item => {
            if (item.status == 1) {
              state.openView[item.code] = true;
            }
          });
        }
      });
      getDictionary(2).then(res => {
        if (res) {
          res.data.data.dictionaryoption.forEach(item => {
            state.dictionaryJson[item.code] = item.name;
          });
        }
      });
      getDictionary('JYLX').then(res => {
        if (res) {
          res.data.data.dictionaryoption.forEach(item => {
            state.dictionaryTypeJson[item.code] = item.name;
          });
        }
      });
    };
    getDictionaryList();
    const getStrategyName = id => {
      if (id) {
        getStrategy(id).then(res => {
          if (res) {
            if (res.data.data.inspectionName) {
              state.strategyName = res.data.data.inspectionName;
            }
          }
        });
      }
    };
    // 获取选择的标准
    const closeDialogStandard = async val => {
      state.dialogStandard = false;
      if (val) {
        const params = {
          id: val.id,
          judgmentName: val.judgmentName,
          sampleId: state.sampleId,
          standardProductId: val.id,
          standardProductName: val.productName,
          standardProductVersion: val.version,
          standardCategoryId: val.standardCategoryId,
          operationType: 1,
          standardId: val.standardCategoryId
        };
        state.detailLoading = true;
        const response = await samplestandardSave({ sampleStandards: [params] }).finally(() => {
          state.detailLoading = false;
        });
        if (response) {
          ElMessage.success('保存成功！');
          getStandardInfo();
        }
      }
    };
    // 删除tag
    const handleCloseStandard = async () => {
      const params = {
        ...state.sampleStandardInfo,
        operationType: 3
      };
      state.detailLoading = true;
      const response = await samplestandardSave({ sampleStandards: [params] }).finally(() => {
        state.detailLoading = false;
      });
      if (response) {
        ElMessage.success('删除成功！');
        getStandardInfo();
      }
    };
    const getdata = () => {
      const postdata = {
        sampleId: route.query.sampleId,
        orderId: route.query.orderId
      };
      samplesDetails(postdata).then(resdata => {
        if (resdata.data.code === 200) {
          state.samplesDetails = resdata.data.data;
          if (JSON.stringify(state.samplesDetails?.certificatePrintEntity) === '{}') {
            state.samplesDetails.certificatePrintEntity.sampleId = state.sampleId;
          }
          state.warehousingInfoList = resdata.data.data.warehousingInfoList || [];
          getStrategyName(state.samplesDetails.inspectionStrategyId);
          state.samplesDetails.warehouseInfo = resdata.data.data?.warehousingInfoEntity;
          if (state.samplesDetails.thirdType === 0) {
            state.samplesDetails.thirdTypeName = 'ERP';
          } else if (state.samplesDetails.thirdType === 1) {
            state.samplesDetails.thirdTypeName = 'MES';
          } else if (state.samplesDetails.thirdType === 2) {
            state.samplesDetails.thirdTypeName = '自建';
          } else {
            state.samplesDetails.thirdTypeName = '--';
          }
          // if (state.samplesDetails.ownerId) {
          //   var paramData = {
          //     limit: '999999',
          //     page: '1',
          //     condition: state.samplesDetails.ownerId
          //   }
          //   getName(paramData).then(thisres => {
          //     state.samplesDetails.name = thisres.data.data[0].name
          //   })
          // }
          if (state.samplesDetails.voltageLevel) {
            getDictionary(2).then(res => {
              state.voltageList = res.data.data.dictionaryoption;
              state.voltageList.forEach(item => {
                if (item.code === state.samplesDetails.voltageLevel) {
                  state.samplesDetails.voltageLevelName = item.name;
                }
              });
            });
          }
          // 获取tab权限
          getCheckTabMenu();
        }
      });
      capabilityBysampleId(route.query.sampleId).then(resp => {
        if (resp.data.data.length !== 0) {
          state.capabilityList = resp.data.data;
        }
      });
      findBySampleIdblp(route.query.sampleId).then(res => {
        state.unqualifiedInfoList = res.data.data;
      });
      findBySampleId(route.query.sampleId).then(res => {
        state.reportList = res.data.data;
      });
    };
    getdata();
    const activeNames = ref(['1', '2', '3', '4', '5', '9', '10']);
    const handleChange = val => {};

    const handleSelectionChange = val => {};
    const goOut = row => {
      router.push({
        name: 'Register',
        params: {
          title: 'detail',
          id: row.disposalNumber,
          token: 0
        }
      });
    };
    const down = url => {
      window.open(url);
      // window.location.href= id
    };
    const goPdf = row => {
      router.push({
        path: '/execution/addRecord/sample',
        query: {
          type: 'check',
          experimentId: row.experimentId,
          samplesId: route.query.sampleId,
          capabilityId: row.capabilityId
        }
      });
    };
    // 过滤来源
    const filterComeFrom = id => {
      if (id === 0) {
        return 'ERP';
      } else if (id === 1) {
        return 'MES';
      } else if (id === 2) {
        return 'LIMS/自建';
      } else {
        return '--';
      }
    };
    //
    const getCheckTabMenu = () => {
      const param = ['unqualified-disposition', 'sampleinventory'];
      checkTabMenu(param).then(res => {
        if (res !== false) {
          state.tabMenuPermission = res.data.data;
          state.showUnqualified = state.tabMenuPermission['unqualified-disposition'];
          state.showRuKuInfo = state.tabMenuPermission['sampleinventory'];
        }
      });
    };

    return {
      ...toRefs(state),
      qrCodeData,
      handleCloseStandard,
      closeDialogStandard,
      handleEdit,
      handleStandard,
      handleCloseDialog,
      getStrategyName,
      getdata,
      emptyImg,
      filterSampleUnitToName,
      getDictionaryList,
      down,
      activeNames,
      handleChange,
      goOut,
      formatDate,
      formatDateTime,
      tableData,
      goPdf,
      getNameByid,
      getNamesByid,
      handleSelectionChange,
      filterComeFrom,
      spacer,
      colWidth,
      getCheckTabMenu
    };
  },
  computed: {
    ...mapGetters(['tenantInfo'])
  },
  created() {}
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--vertical) {
  height: 40px;
}
.capability-name {
  display: flex;
  align-items: center;
}
.urgent2 {
  font-size: 12px;
  position: absolute;
  right: -35px;
  top: 0px;
  color: #f56c6c;
  background-color: #fef0f0;
  border: 1px solid #fde2e2;
  border-radius: 2px;
  padding: 2px 3px;
  display: inline-block;
  line-height: 12px;
  z-index: 999;
}
</style>
