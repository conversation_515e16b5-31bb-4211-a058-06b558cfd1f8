<template>
  <el-dialog
    v-model="showDialog"
    custom-class="tiny-dialog info-add"
    :title="title"
    top="3vh"
    width="920px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <div v-loading="loading" class="dialog-main">
      <el-form
        ref="inspectionInfoRef"
        class="formDataInfo"
        :model="formInline"
        label-width="110px"
        label-position="top"
      >
        <el-row :gutter="60">
          <el-col :span="24">
            <el-form-item label="" prop="type">
              <el-radio-group v-model="formInline.type" class="radio-groups" :disabled="isEdit" @change="changeType">
                <el-radio-button v-for="(val, key) in dictionaryAll['JYLX'].enable" :key="key" :label="key">
                  {{ val }}
                  <span class="corner" />
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-space direction="vertical">
          <el-row :gutter="60">
            <el-col
              v-for="viewItem in pageViewGroup[`${formInline.type}-basicInfo-dialog`]"
              :key="viewItem.fieldKey"
              :span="Number(viewItem.columnWidth)"
            >
              <el-form-item
                :label="`${viewItem.fieldName}：`"
                :prop="viewItem.fieldKey"
                style="position: relative"
                :rules="[
                  {
                    required: viewItem.isRequired == 1,
                    message: formatViewRequirePrompt(viewItem.fieldType, viewItem.fieldName),
                    trigger: 'change'
                  },
                  { ...inspectionInfoRule[viewItem.fieldKey] }
                ]"
              >
                <template v-if="viewItem.fieldType == 'text'">
                  <el-input
                    v-model="formInline[viewItem.fieldKey]"
                    :maxlength="100"
                    :placeholder="`请输入${viewItem.fieldName}`"
                    clearable
                  />
                </template>
                <template v-if="viewItem.fieldType == 'person'">
                  <el-select
                    v-model="formInline[viewItem.fieldKey]"
                    class="owner-select"
                    :placeholder="`请选择${viewItem.fieldName}`"
                    clearable
                    filterable
                    :filter-method="filterUserList"
                    @focus="filterUserList(null)"
                    @change="changeUser"
                  >
                    <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select>
                </template>
                <template v-if="viewItem.fieldType == 'date'">
                  <template v-if="viewItem.fieldKey == 'registerTime'">
                    <el-date-picker
                      v-model="formInline[viewItem.fieldKey]"
                      type="datetime"
                      :placeholder="`请选择${viewItem.fieldName}`"
                      @change="
                        val => {
                          return handleChangeDate(val, viewItem.fieldKey);
                        }
                      "
                    />
                  </template>
                  <template v-else>
                    <el-date-picker
                      v-model="formInline[viewItem.fieldKey]"
                      type="date"
                      :placeholder="`请选择${viewItem.fieldName}`"
                      @change="
                        val => {
                          return handleChangeDate(val, viewItem.fieldKey);
                        }
                      "
                    />
                  </template>
                </template>
                <template v-if="viewItem.fieldType == 'custom'">
                  <template v-if="viewItem.fieldKey == 'productionOrderNo'">
                    <span class="scdd">
                      <el-tooltip content="部分厂商对应生产指令号/生产工单" placement="top" effect="dark">
                        <i class="iconfont tes-title" />
                      </el-tooltip>
                    </span>
                    <el-input
                      v-model="formInline[viewItem.fieldKey]"
                      maxlength="100"
                      :placeholder="`请输入${viewItem.fieldName}`"
                      clearable
                    />
                  </template>
                  <template v-if="viewItem.fieldKey == 'productionProcedureNo'">
                    <el-select
                      v-model="formInline.productionProcedureNo"
                      class="owner-select"
                      :placeholder="`请选择${viewItem.fieldName}`"
                      clearable
                      filterable
                      :filter-method="filterProcessList"
                      @focus="filterProcessList(null)"
                      @change="changeProductionProcedure"
                    >
                      <el-option
                        v-for="item in processList"
                        :key="item.id"
                        :label="item.no + ' - ' + item.name"
                        :value="item.no"
                      />
                    </el-select>
                  </template>
                  <template v-if="viewItem.fieldKey == 'isFirstOutput'">
                    <el-radio-group v-model="formInline.isFirstOutput">
                      <el-radio label="是" size="large">是</el-radio>
                      <el-radio label="否" size="large">否</el-radio>
                    </el-radio-group>
                  </template>
                </template>
              </el-form-item>
            </el-col>
          </el-row>
        </el-space>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button v-loading="loading" @click="showDialog = false">取 消</el-button>
        <el-button v-loading="loading" type="primary" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent
          >保 存</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref } from 'vue';
import _ from 'lodash';
import { ElMessage, ElMessageBox } from 'element-plus';
import router from '@/router/index.js';
// import { useRoute } from 'vue-router'
import { addInspection, updateInspection } from '@/api/inspection-application';
import { useStore } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
import { getProcessListNew } from '@/api/mas';
import { formatDateTime, formatDate } from '@/utils/formatTime';
import { greaterThanZero2 } from '@/utils/validate';
import { formatViewRequirePrompt } from '@/utils/formatJson';

export default {
  name: 'AddInfo',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '新增检验单'
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    pageView: {
      type: Object,
      default: function () {
        return {};
      }
    },
    dictionary: {
      type: Object,
      default: function () {
        return {};
      }
    },
    info: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    // const lodash = inject('_')
    const store = useStore().state;
    const datas = reactive({
      currentAccountId: getLoginInfo().accountId,
      userOptions: store.common.nameList,
      loading: false,
      copyUserOptions: store.common.nameList,
      showDialog: false,
      dictionaryAll: {
        JYLX: {
          enable: {},
          all: {}
        }
      },
      formInline: {
        registerUserId: getLoginInfo().accountId,
        registerDepartment: '',
        registerTime: formatDateTime(new Date()),
        type: 1,
        inputWarehouseNo: '',
        wareHouseNo: '',
        wareHouseName: '',
        wareHousePerson: '',
        inputWarehouseDate: '',
        applyStatus: 1
      },
      inspectionInfo: {
        list: []
      },
      inspectionInfoRef: ref(),
      inspectionInfoRule: {
        sampleLength: { validator: greaterThanZero2, tigger: 'change' },
        segment: { validator: greaterThanZero2, tigger: 'change' }
      },
      processList: [],
      pageViewGroup: {
        '1-basicInfo-dialog': {},
        '2-basicInfo-dialog': {},
        '3-basicInfo-dialog': {},
        '4-basicInfo-dialog': {},
        '5-basicInfo-dialog': {},
        '6-basicInfo-dialog': {},
        '7-basicInfo-dialog': {},
        '8-basicInfo-dialog': {}
      },
      copyProcessList: [],
      showEdit: false
    });

    watch(
      () => props.show,
      newValue => {
        if (newValue) {
          datas.showDialog = newValue;
          datas.showEdit = props.isEdit;
          datas.pageViewGroup = props.pageView;
          datas.dictionaryAll = props.dictionary;
          if (props.isEdit) {
            datas.formInline = JSON.parse(JSON.stringify(props.info));
          } else {
            datas.formInline = {
              registerUserId: getLoginInfo().accountId,
              registerTime: formatDateTime(new Date()),
              type: 1,
              applyStatus: 1
            };
          }
        }
      },
      { deep: true }
    );

    // 确定选择
    const dialogSuccess = () => {
      datas.inspectionInfoRef.validate(valid => {
        if (valid) {
          if (datas.showEdit) {
            datas.loading = true;
            updateInspection(datas.formInline).then(res => {
              datas.loading = false;
              if (res) {
                datas.showDialog = false;
                context.emit('close', 'update');
                ElMessage.success('更新成功');
              }
            });
          } else {
            ElMessageBox({
              title: '提交',
              message: '是否确认提交？提交后检验类型不可修改',
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              showCancelButton: true,
              closeOnClickModal: true,
              type: 'warning'
            })
              .then(() => {
                datas.loading = true;
                addInspection(datas.formInline).then(res => {
                  datas.loading = false;
                  if (res) {
                    ElMessage.success('新增成功');
                    datas.showDialog = false;
                    context.emit('close', false);
                    router.push({ name: 'AddInspection', query: { id: res.data.data?.id, flag: 2 } });
                  }
                });
              })
              .catch(() => {});
          }
        } else {
          console.log(valid);
        }
      });
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };
    // 过滤登记人
    const filterUserList = val => {
      if (val) {
        const list = [];
        datas.copyUserOptions.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        datas.userOptions = list;
      } else {
        datas.userOptions = datas.copyUserOptions;
      }
    };
    // 过滤生产工序
    const filterProcessList = val => {
      if (val) {
        const list = [];
        datas.copyProcessList.forEach(user => {
          const item = _.filter(user.name, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        datas.processList = list;
      } else {
        datas.processList = datas.copyProcessList;
      }
    };
    // 登记人-change
    const changeUser = id => {
      datas.formInline.registerUserId = id;
    };
    // 检验类型-change
    const changeType = type => {
      datas.formInline = {
        registerUserId: getLoginInfo().accountId,
        registerTime: formatDateTime(new Date()),
        type: type,
        applyStatus: 1
      };
    };
    // 生产工序-change
    const changeProductionProcedure = no => {
      // console.log(no)
      datas.processList.forEach(item => {
        if (item.no === no) {
          datas.formInline.productionProcedure = item.name;
          datas.formInline.productionProcedureNo = item.no;
        }
      });
    };
    const handleChangeDate = (val, field) => {
      if (val) {
        if (field == 'registerTime') {
          datas.formInline[field] = formatDateTime(val);
        } else {
          datas.formInline[field] = formatDate(val);
        }
      } else {
        datas.formInline[field] = '';
      }
    };
    return {
      ...toRefs(datas),
      dialogSuccess,
      handleChangeDate,
      formatViewRequirePrompt,
      greaterThanZero2,
      close,
      filterUserList,
      changeUser,
      changeType,
      changeProductionProcedure,
      filterProcessList
    };
  },
  created() {
    this.getProcessLists();
  },
  methods: {
    // 获取生产工序列表接口
    getProcessLists() {
      var that = this;
      var param = {
        limit: '-1',
        page: '1',
        content: ''
      };
      getProcessListNew(param).then(res => {
        if (res !== false) {
          that.processList = res.data.data.list;
          that.copyProcessList = res.data.data.list;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.formDataInfo {
  max-height: 640px;
  overflow-y: auto;
  overflow-x: hidden;
  :deep(.el-input--medium .el-input__inner) {
    height: 32px;
    line-height: 32px;
  }
}
.scdd {
  position: absolute;
  left: 70px;
  top: -34px;
}
.info-add {
  .dialog-main {
    :deep(.el-space) {
      width: 100%;
      .el-space__item {
        margin: 16px 0;
        width: 100%;
      }
    }
  }
  .el-input--medium .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
  .el-divider--horizontal {
    margin: 15px 0px;
  }
  .el-form-item {
    text-align: left;
    // width: 46%;
    margin-bottom: 10px;
    :deep(.el-form-item__label) {
      font-weight: normal;
      font-size: 14px;
      color: #606266;
      padding: 0px;
    }
    :deep(.el-form-item__content) {
      .el-select {
        width: 100%;
      }
      .el-date-editor.el-input {
        width: 100%;
      }
      .el-radio-group {
        .el-radio {
          margin: 0px;
        }
        .el-radio:nth-child(2) {
          margin: 0px 5px;
        }
      }
    }
  }
}
</style>
