import request from '@/utils/request';

// 查询表头控制配置信息
export function commonTableHeader(param) {
  return request({
    url: `/api-diplomat/diplomat/commontableheader/info?module=${param}`,
    method: 'get'
  });
}
// 光纤库存列表
export function fiberInventory(data) {
  return request({
    url: `/api-diplomat/diplomat/fiber-inventory/list`,
    method: 'post',
    data
  });
}
// 保存着色、倒盘、回仓
export function fiberOperationSave(data) {
  return request({
    url: `/api-diplomat/diplomat/fiber-operation/save`,
    method: 'post',
    data
  });
}
// 着色、倒盘、回仓记录列表
export function fiberOperationList(data) {
  return request({
    url: `/api-diplomat/diplomat/fiber-operation/list`,
    method: 'post',
    data
  });
}

// 着色、倒盘、回仓记录列表
export function operationRecordList(mainDataId) {
  return request({
    url: `/api-diplomat/opticalFiberInventoryOperationHis/getList?mainDataId=` + mainDataId,
    method: 'get'
  });
}
