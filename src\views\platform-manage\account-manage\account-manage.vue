<template>
  <!-- 账号管理 -->
  <ListLayout
    class="account-management"
    :has-search-panel="false"
    :has-quick-query="false"
    :has-button-group="getPermissionBtn('addAccountBtn') ? true : false"
  >
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="condition">
          <el-input
            v-model="formInline.condition"
            v-trim
            v-focus
            placeholder="请输入用户名/姓名/手机搜索"
            class="ipt-360"
            prefix-icon="el-icon-search"
            size="large"
            clearable
            @keyup.enter="onSubmit"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="onSubmit">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button type="primary" icon="el-icon-plus" size="large" @click="addAccount">新增账号</el-button>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      size="medium"
      height="auto"
      class="dark-table base-table format-height-table no-quick-query"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column label="用户名" prop="username" :min-width="colWidth.name" sortable>
        <template #default="{ row }">
          <div>{{ row.username || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="姓名" prop="nickname" :width="colWidth.people" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <UserTag width="120px" :name="row.nickname || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="手机" prop="mobile" :min-width="colWidth.phone">
        <template #default="{ row }">
          <span>{{ row.mobile || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="邮箱" prop="email" :min-width="colWidth.email">
        <template #default="{ row }">
          <span>{{ row.email || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" :width="colWidth.status">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="row.status === 1 ? 'success' : 'info'">
            {{ row.status === 1 ? '已启用' : '已停用' || '--' }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        v-if="getPermissionBtn('repwdAccountBtn') || getPermissionBtn('editAccountBtn')"
        label="操作"
        prop="caozuo"
        :width="colWidth.operationMultiple"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <span v-if="getPermissionBtn('editAccountBtn')" class="blue-color" @click="handleAM(row, 1)">编辑</span>
          <span v-if="getPermissionBtn('repwdAccountBtn')" class="blue-color" @click="handleAM(row, 2)">密码重置</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
    <template #other>
      <add-edit-account
        :show="showDialog"
        :title="dialogTitle"
        :data="dialogData"
        @close="closeDialog"
        @setInfo="setInfo"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, toRefs, getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getPermissionBtn } from '@/utils/common';
import ListLayout from '@/components/ListLayout';
import { getLoginInfo } from '@/utils/auth';
// import _ from 'lodash'
// import { checkPermissionList } from '@/api/permission'
// import { permissionTypeList } from '@/utils/permissionList'
import { getAccountList, resetPassword } from '@/api/platform-management';
import Pagination from '@/components/Pagination';
import UserTag from '@/components/UserTag';
import { drageHeader } from '@/utils/formatTable';
import AddEditAccount from './addEditAccount.vue';
import { colWidth } from '@/data/tableStyle';

export default {
  name: 'AccountManagement',
  components: { Pagination, AddEditAccount, ListLayout, UserTag },
  setup() {
    const { proxy } = getCurrentInstance();
    // const store = useStore().state
    const datas = reactive({
      accountId: getLoginInfo().accountId,
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: null
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      list: [],
      formInline: {
        condition: ''
      },
      showDialog: false,
      dialogTitle: '',
      dialogData: null
    });
    // 查询
    function onSubmit() {
      proxy.getList();
    }
    // 重置
    function reset() {
      datas.formInline = {
        condition: ''
      };
      datas.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: null
      };
      proxy.getList();
    }
    // table排序
    const sortChange = data => {
      const { prop, order } = data;
      datas.listQuery.orderBy = prop;
      if (order === 'ascending') {
        datas.listQuery.isAsc = true;
      } else if (order === 'descending') {
        datas.listQuery.isAsc = false;
      } else {
        datas.listQuery.isAsc = null;
      }
    };
    // 新增账号
    const addAccount = () => {
      datas.dialogTitle = 'add';
      datas.showDialog = true;
      datas.dialogData = { status: 1 };
    };
    // 关闭弹出框
    const closeDialog = v => {
      datas.showDialog = false;
    };
    // 弹出框-确认
    const setInfo = data => {
      datas.showDialog = false;
      proxy.getList();
    };
    // 操作
    const handleAM = (row, flag) => {
      if (flag === 1) {
        // 编辑
        datas.dialogTitle = 'edit';
        datas.showDialog = true;
        datas.dialogData = row;
      } else if (flag === 2) {
        // 重置密码
        ElMessageBox({
          title: '密码重置',
          message: '密码将重置为初始密码CX@lims，请提醒用户及时修改新密码。',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: true,
          type: 'warning'
        })
          .then(() => {
            resetPassword(row.id).then(res => {
              if (res !== false) {
                ElMessage.success('重置成功！');
              }
            });
          })
          .catch(() => {});
      }
    };

    return {
      ...toRefs(datas),
      onSubmit,
      reset,
      sortChange,
      handleAM,
      drageHeader,
      addAccount,
      closeDialog,
      setInfo,
      getPermissionBtn,
      colWidth
    };
  },
  created() {
    this.getList();
    // 刷新列表
    this.bus.$on('accountManagementList', msg => {
      this.getList();
    });
  },
  methods: {
    getList(data) {
      const _this = this;
      // _this.listLoading = true
      if (data && data !== undefined) {
        _this.listQuery.page = data.page;
        _this.listQuery.limit = data.limit;
      }
      const param = Object.assign(_this.formInline, _this.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      getAccountList(param).then(res => {
        if (res !== false) {
          const { data } = res.data;
          _this.list = data.list;
          _this.total = data.totalCount;
          // this.getAllList()
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
    }
  }
};
</script>
<style lang="scss" scoped></style>
