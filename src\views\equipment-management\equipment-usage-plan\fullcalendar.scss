.equipment-usage-plan {
  .calendar-content {
    max-height: calc(100vh - 172px);
    overflow-y: auto;
    padding: 0 0 20px 0;
  }
  .fc-timeline-slot-frame,
  th .fc-datagrid-cell-frame {
    height: 32px !important;
    line-height: 32px !important;
    background-color: #f5f7fa;
    font-size: 12px;
    color: #303133;
    font-weight: 500;
  }
  .fc-timeline-lane-frame {
    height: 56px !important;
    // line-height: 56px;
  }
  .fc-datagrid-cell-frame {
    height: 56px !important;
    line-height: 56px;
  }
  .fc .fc-datagrid-cell-cushion {
    padding: 0 8px;
    text-align: left;
  }
  .fc-theme-standard .fc-scrollgrid {
    border: 0;
  }
  .fc-datagrid-body.fc-scrollgrid-sync-table {
    tr:nth-child(even) {
      background-color: #f6f9fc;
    }
  }
  .fc-scrollgrid-sync-table {
    tr:nth-child(even) {
      background-color: #f6f9fc;
    }
  }
  .fc-theme-standard th {
    // border-left: 1px solid #EBEEF5;
    border-left: 0;
    border-bottom: 1px solid #ebeef5;
    border-right: 0;
    // &:first-child {
    //   border-left: 0;
    // }
    &.fc-day-today .fc-timeline-slot-frame {
      font-weight: normal;
      border-radius: 3px;
      background: $tes-primary;
      color: #fff;
    }
  }
  .fc .fc-timeline-header-row-chrono .fc-timeline-slot-frame {
    justify-content: center;
  }
  .fc-theme-standard td {
    border: 1px solid #ebeef5;
    &.fc-day-today {
      position: relative;
    }
    &.fc-day-today::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      right: 0;
      height: 100%;
      border-left: 1px dashed $tes-primary;
      z-index: -1;
    }
    &.fc-timeline-slot-minor.fc-slot-today:nth-of-type(even) {
      border-left-color: transparent;
    }
  }
  .fc-theme-standard td:first-child {
    border-left: 0;
  }
  .fc-theme-standard td:last-child {
    border-right: 0;
    border-left: 0;
  }
  .fc-theme-standard td {
    border-top: 0;
    border-bottom: 0;
    // border-right: 0;
  }
  .fc .fc-resource-timeline-divider {
    width: 1px;
    border-color: #ebeef5;
    // display: none;
  }
  .fc .fc-timeline-body {
    height: auto;
    min-height: auto;
  }
  .fc .fc-timeline-overlap-enabled .fc-timeline-lane-frame .fc-timeline-events {
    padding-bottom: 0;
  }
  .fc-direction-ltr .fc-timeline-event.fc-event-end {
    height: 40px;
  }
  .plan_title {
    line-height: 40px;
    max-width: 86%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
  }
  .plan_titleTime {
    div {
      line-height: 20px;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .title {
      color: #3d3d3d;
      font-size: 14px;
    }
    .titleTime {
      color: #999;
      font-size: 12px;
    }
  }
  .fc-event {
    position: relative;
    line-height: 20px;
    padding: 6px 8px;
    background-color: $tes-primary2;
    border: 0;
    &:after {
      content: '';
      width: 3px;
      height: 100%;
      border-radius: 2px 0 0 2px;
      position: absolute;
      top: 0;
      left: 0;
      background-color: $tes-primary;
      border: 0;
      opacity: 1;
    }
    .plan_title {
      color: #303133;
    }
  }
  .fc-timeline-more-link-inner {
    padding: 0;
    line-height: 12px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .fc-theme-standard .fc-popover-header {
    background-color: #fff;
    padding: 20px 20px 14px 20px;
  }
  .fc .fc-popover-title {
    color: $tes-primary;
    font-size: 24px;
  }
  .fc-scrollgrid-section.fc-scrollgrid-section-body.fc-scrollgrid-section-liquid {
    td {
      border-right: 0;
    }
  }
  .fc-scrollgrid-section.fc-scrollgrid-section-body {
    > th:nth-of-type(1),
    > td:nth-of-type(1) {
      border-right: 0;
    }
  }
  .fc .fc-more-popover .fc-popover-body {
    padding: 0 20px 16px 20px;
    // div {
    //   margin-bottom: 5px;
    // }
  }
  .fc-license-message {
    display: none;
  }
  .fc-popover-title {
    max-width: 92%;
    line-height: 24px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .fc-timeline-more-link {
    background-color: transparent;
    color: $tes-primary;
  }
}
