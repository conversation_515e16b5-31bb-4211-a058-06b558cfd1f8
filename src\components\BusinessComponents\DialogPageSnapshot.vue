<template>
  <!-- 屏幕截图弹出窗 -->
  <el-dialog
    v-model="dialogShow"
    custom-class="customSnapshotDialog"
    title="调整截图"
    width="1300px"
    top="3vh"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <div v-loading="loading" class="boardBox">
      <div id="tui-image-editor" />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="loading" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" @click="handleCanvas2Img">确认上传</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { conversion } from '@/utils/file';
import { uploadFilePicture } from '@/api/sysConfig';
import { ElMessage } from 'element-plus';
// import ImageEditor from 'tui-image-editor';
// import 'tui-image-editor/dist/tui-image-editor.css';
// import 'tui-color-picker/dist/tui-color-picker.css';

export default {
  name: 'DialogPageSnapshot',
  components: {},
  props: {
    dialogVisiable: {
      type: Boolean,
      default: false
    },
    imgUrl: {
      type: String,
      default: ''
    },
    parameter: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    const state = reactive({
      dialogShow: false,
      params: {}, // 上传时额外的参数
      imgBaseUrl: '',
      loading: false,
      // 创建的画布对象
      instance: null,
      ImageEditor: null,
      localeCN: {
        Crop: '裁剪',
        Draw: '涂鸦',
        Text: '添加文本',
        Undo: '上一步',
        Redo: '下一步',
        Reset: '重置',
        Apply: '确定',
        Cancel: '取消',
        Custom: '自定义',
        Square: '正方形',
        Free: '曲线',
        Straight: '直线',
        Color: '颜色',
        Range: '粗细/角度',
        Bold: '加粗',
        Italic: '斜体',
        Underline: '下划线',
        Left: '左对齐',
        Center: '居中',
        Right: '右对齐',
        'Flip X': 'X 轴',
        'Flip Y': 'Y 轴',
        Flip: '镜像',
        Rotate: '旋转',
        ZoomIn: '放大',
        ZoomOut: '缩小',
        Hand: '拖动',
        'Text size': '字体大小'
      },
      customTheme: {
        // image 坐上角度图片
        'common.bi.image': '', // 在这里换上你喜欢的logo图片
        'common.bisize.width': '0px',
        'common.bisize.height': '0px',
        'common.backgroundImage': 'none',
        'common.backgroundColor': '#f3f4f6',
        'common.border': 'none',

        // header
        'header.backgroundImage': 'none',
        'header.backgroundColor': 'transparent',
        'header.border': '0px',

        // load button
        'loadButton.backgroundColor': '#fff',
        'loadButton.border': '1px solid #ddd',
        'loadButton.color': '#222',
        'loadButton.fontFamily': 'NotoSans, sans-serif',
        'loadButton.fontSize': '12px',
        'loadButton.display': 'none', // 可以直接隐藏掉

        // download button
        'downloadButton.backgroundColor': '#fdba3b',
        'downloadButton.border': '1px solid #fdba3b',
        'downloadButton.color': '#fff',
        'downloadButton.fontFamily': 'NotoSans, sans-serif',
        'downloadButton.fontSize': '12px',
        'downloadButton.display': 'none', // 可以直接隐藏掉

        // icons default
        'menu.normalIcon.color': '#8a8a8a',
        'menu.activeIcon.color': '#555555',
        'menu.disabledIcon.color': '#434343',
        'menu.hoverIcon.color': '#e9e9e9',
        'submenu.normalIcon.color': '#8a8a8a',
        'submenu.activeIcon.color': '#e9e9e9',

        'menu.iconSize.width': '18px',
        'menu.iconSize.height': '18px',
        'submenu.iconSize.width': '20px',
        'submenu.iconSize.height': '20px',
        // 'submenu.backgroundColor': '#ddd',

        // submenu primary color
        // 'submenu.backgroundColor': 'currentcolor',
        'submenu.partition.color': '#858585',

        // submenu labels
        'submenu.normalLabel.color': '#858585',
        'submenu.normalLabel.fontWeight': 'lighter',
        'submenu.activeLabel.color': '#fff',
        'submenu.activeLabel.fontWeight': 'lighter',

        // checkbox style
        'checkbox.border': '1px solid #ccc',
        'checkbox.backgroundColor': '#fff',

        // rango style
        'range.pointer.color': '#fff',
        'range.bar.color': '#666',
        'range.subbar.color': '#d1d1d1',

        'range.disabledPointer.color': '#414141',
        'range.disabledBar.color': '#282828',
        'range.disabledSubbar.color': '#414141',

        'range.value.color': '#fff',
        'range.value.fontWeight': 'lighter',
        'range.value.fontSize': '11px',
        'range.value.border': '1px solid #353535',
        'range.value.backgroundColor': '#151515',
        'range.title.color': '#fff',
        'range.title.fontWeight': 'lighter',

        // colorpicker style
        // 'colorpicker.button.border': '1px solid #1e1e1e',
        'colorpicker.title.color': '#fff'
      }
    });

    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisiable;
      if (state.dialogShow) {
        state.refPage = newValue.refPage;
        state.imgBaseUrl = newValue.imgUrl;
        state.params = newValue.parameter;
        if (state.imgBaseUrl) {
          nextTick(() => {
            // 获取到当前屏幕的宽高，用于判断当前是大屏幕还是小屏幕==》进而确定的那个要渲染哪个init（画布大小不一样）
            // 如果不压缩图片大小，太大的图片出现拖动，手机端无法操作，所以要根据不同屏幕大小渲染不同大小的画布
            if (document.documentElement.clientWidth <= 1200) {
              initMini();
            } else {
              // 页面加载好，就调用这个方法来创建图片编辑器
              init();
            }
          });
        }
      }
    });

    // 由于 tui-image-editor 所依赖的 fabric.js 中有 node-canvas 插件，该插件在gitlab上构建的时候极慢，而且还容易失败，造成 npm error node-pre-gyp ERR
    // 所以这里由 npm install 方式改为本地 cdn 方式加载该插件，插件代码在 public/tui-image-editor 文件夹中维护。
    const tuiImageEditorUrls = [
      { type: 'text/javascript', src: '/tui-image-editor/tui-color-picker.min.js' },
      {
        type: 'text/javascript',
        src: '/tui-image-editor/tui-image-editor.min.js',
        onload: function () {
          state.loading = false;
          state.ImageEditor = tui.ImageEditor;
        }
      },
      { rel: 'stylesheet', href: '/tui-image-editor/tui-image-editor.min.css' }
    ];

    onMounted(() => {
      loadTuiImageEditor();
    });

    onUnmounted(() => {
      unloadTuiImageEditor();
    });

    const loadTuiImageEditor = () => {
      state.loading = true;
      tuiImageEditorUrls.forEach(item => {
        if (item.type === 'text/javascript') {
          const script = document.createElement('script');
          Object.assign(script, item);
          document.head.appendChild(script);
        } else if (item.rel === 'stylesheet') {
          const link = document.createElement('link');
          Object.assign(link, item);
          document.head.appendChild(link);
        }
      });
    };

    const unloadTuiImageEditor = () => {
      state.loading = false;
      tuiImageEditorUrls.forEach(item => {
        if (item.type === 'text/javascript') {
          const script = document.querySelector(`script[type='text/javascript'][src='${item.src}']`);
          if (script) {
            script.remove();
          }
        } else if (item.ref === 'stylesheet') {
          const link = document.querySelector(`link[ref='stylesheet'][href='${item.href}']`);
          if (link) {
            link.remove();
          }
        }
      });
    };

    const init = () => {
      // 创建tui-image-editor组件实例，后续操作需要用到this.instance这个对象
      state.instance = new state.ImageEditor(document.querySelector('#tui-image-editor'), {
        includeUI: {
          // 默认加载的图片
          loadImage: {
            // 图片路径
            path: state.imgBaseUrl,
            // 图片的名字，可以省略
            name: 'image'
          },
          // 默认开启绘图的功能,小屏幕情况下，直接打开菜单，会占用较大屏幕空间，不美观
          initMenu: 'draw',
          // 支持的菜单
          menu: [
            'crop', // 裁切
            'draw', // 添加绘画
            'text', // 添加文本
            'rotate', // 旋转
            'flip' // 翻转
            // 'shape', // 添加形状
            // 'icon', // 添加图标
            // 'mask', // 添加覆盖
            // 'filter' // 添加滤镜
          ],
          // 菜单位置在下面
          menuBarPosition: 'bottom',
          // 汉化
          locale: state.localeCN,
          // 自定义样式（隐藏默认顶部栏目、按钮颜色。。。）
          theme: state.customTheme
        },
        // 设置画布的最大宽高，能自动等比例缩放大图片到指定的宽高内
        // TODO：可以监听当前页面的缩放，动态修改画布最大宽高以防止图片过大
        cssMaxWidth: 941,
        cssMaxHeight: 1440
      });
      // 清除自定义样式造成的一条边框线
      document.getElementsByClassName('tui-image-editor-main')[0].style.top = 0;
      // 你也可以指定那个菜单隐藏，留几个有用的菜单
      // document.querySelector('[tooltip-content="Undo"]').style.display = 'none'// 上一步
      // document.querySelector('[tooltip-content="Redo"]').style.display = 'none' // 下一步
      // document.querySelector('[tooltip-content="Reset"]').style.display = 'none' // 完全重新编辑
      // document.querySelector('[tooltip-content="ZoomIn"]').style.display = 'none' // 放大
      // document.querySelector('[tooltip-content="ZoomOut"]').style.display = 'none' // 缩小
      // document.querySelector('[tooltip-content="Hand"]').style.display = 'none' // 拖动界面
      document.querySelector('[tooltip-content="History"]').style.display = 'none';
      document.querySelector('[tooltip-content="Delete"]').style.display = 'none'; // 删除选中编辑内容
      document.querySelector('[tooltip-content="DeleteAll"]').style.display = 'none'; // 清空
      // 隐藏分割线
      document.querySelectorAll('.tui-image-editor-icpartition').forEach(item => {
        item.style.display = 'none';
      });
    };
    // 创建图片编辑器 ==>小屏幕
    const initMini = () => {
      // 创建tui-image-editor组件实例，后续操作需要用到this.instance这个对象
      state.instance = new state.ImageEditor(document.querySelector('#tui-image-editor'), {
        includeUI: {
          // 默认加载的图片
          loadImage: {
            // 图片路径
            path: state.imgBaseUrl,
            // 图片的名字，可以省略
            name: 'image'
          },
          // 默认开启绘图的功能,小屏幕情况下，直接打开菜单，会占用较大屏幕空间，不美观
          initMenu: 'draw',
          // 支持的菜单
          menu: [
            'crop', // 裁切
            'draw', // 添加绘画
            'text' // 添加文本
          ],
          // 菜单位置在下面
          menuBarPosition: 'bottom',
          // 汉化
          locale: state.localeCN,
          // 自定义样式（隐藏默认顶部栏目、按钮颜色。。。）
          theme: state.customTheme
        },
        // 设置画布的最大宽高，能自动等比例缩放大图片到指定的宽高内
        // !设置小图宽高，自动压缩图片，防止过大出现滚动，导致无法操作
        cssMaxWidth: 941,
        cssMaxHeight: 1440
      });
      // 清除自定义样式造成的一条边框线
      document.getElementsByClassName('tui-image-editor-main')[0].style.top = 0;
      // 设置图片编辑其余距离底部90px（就不会被底部展开的工具栏遮挡住了）===>无效
      // document.getElementsByClassName('tui-image-editor-wrap')[0].style.bottom = 90

      //! 修改图片编辑器的顶部导航栏
      // document.querySelector('[tooltip-content="Undo"]').style.display = 'none'// 上一步
      document.querySelector('[tooltip-content="History"]').style.display = 'none';
      document.querySelector('[tooltip-content="Delete"]').style.display = 'none'; // 删除选中编辑内容
      document.querySelector('[tooltip-content="DeleteAll"]').style.display = 'none'; // 清空
      // 隐藏分割线
      document.querySelectorAll('.tui-image-editor-icpartition').forEach(item => {
        item.style.display = 'none';
      });
    };
    // 对图片插入左下角用户名和日期文字===》当点击保存按钮时触发
    const addUserNameText = base64String => {
      const fileUrl = conversion(base64String);
      // ?new Date().getTime()==>获取当前时间戳，并通过changeDate方法转换成好看的格式，存在data中，方便绘制图片的时候印上去
      const drawADate = changeDate(new Date().getTime());
      // console.log('转化base64为文件类型：==》', fileUrl)
      //! tui-image-editor组件的官方方法，用于获取当前图片的宽高
      state.instance.loadImageFromFile(fileUrl).then(result => {
        // TODO：改成获取userName
        state.instance
          .addText('张三' + drawADate, {
            styles: {
              fill: '#2196F3',
              fontSize: 22,
              fontWeight: 'bold'
            },
            position: {
              x: 10,
              y: result.newHeight - 30
            }
          })
          .then(objectProps => {
            console.log('左下角文字加上了');
          });
      });
    };
    /** 保存编辑后图片 */
    const handleCanvas2Img = () => {
      // 调用组件官方方法，获取整个编辑后图片的base64数据
      const base64String = state.instance.toDataURL();
      state.loading = true;
      setTimeout(() => {
        var formData = new FormData();
        formData.append('relevancyKey', state.params.relevancyKey);
        formData.append('file', conversion(base64String));
        uploadFilePicture(formData).then(res => {
          state.loading = false;
          if (res) {
            context.emit('closeDialog', true);
            ElMessage.success({
              message: '上传成功',
              type: 'success'
            });
          }
        });
      }, 700);
    };
    // ? this.instance.loadImageFromFile这个官方的方法会返回图片的宽高，但是传入的必须时file类型的文件
    // 转换时间格式
    const changeDate = originVal => {
      const dt = new Date(originVal);
      // 年
      const y = dt.getFullYear();
      // dt.getMonth() + 1 就是获取日期+1变成1-12月，
      // ! 通过.padStart(2, '0')，当字符串长度不足两位时，用'0'来填充在首位至2位
      // ! 因为是针对字符串的，所以在dt.getMonth() + 1 + '' 转为字符串
      // 月
      const m = (dt.getMonth() + 1 + '').padStart(2, '0');
      // 日
      const d = (dt.getDate() + '').padStart(2, '0');
      // 时
      const hh = (dt.getHours() + '').padStart(2, '0');
      // 分
      const mm = (dt.getMinutes() + '').padStart(2, '0');
      // 秒
      const ss = (dt.getSeconds() + '').padStart(2, '0');
      // 返回过滤后的时间格式  yyyy-mm-dd hh:mm:ss
      return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
    };
    const handleClose = () => {
      state.dialogShow = false;
      context.emit('closeDialog');
    };

    return { ...toRefs(state), handleClose, handleCanvas2Img, changeDate, addUserNameText, initMini, init };
  }
};
</script>
<style lang="scss">
@import '@/styles/dialog.scss';
</style>
<style lang="scss" scoped>
// :deep(.tui-image-editor) {
//   height: 1800px !important;
//   width: 1177px !important;
// }
// :deep(.tui-image-editor-canvas-container) {
//   max-width: 1177px !important;
//   max-height: 1800px !important;
// }
// :deep(.lower-canvas) {
//   max-width: 1177px !important;
//   max-height: 1800px !important;
// }
// :deep(.upper-canvas) {
//   max-width: 1177px !important;
//   max-height: 1800px !important;
// }
.boardBox {
  width: 100%;
  height: 79.5vh;
  background: #f9f9f9;
}

// 弹窗的关闭按钮
.closeBigBtn {
  position: absolute;
  left: 150px;
  top: 12px;
}
</style>

<style lang="scss">
.tui-image-editor-container .tui-image-editor-controls {
  height: 40px;
  background-color: #ddd;
}
.tui-image-editor-container .tui-image-editor-wrap {
  padding: 6px 0;
  position: initial;
  flex: 1;
}
.tui-image-editor-container .tui-image-editor-main {
  display: flex;
  flex-direction: column-reverse;
}
.tui-image-editor-container .tui-image-editor-main-container {
  height: calc(100% - 40px);
}
.tui-image-editor-container .color-picker-value {
  width: 22px;
  height: 22px;
}
.tui-image-editor {
  top: 0 !important;
}
.tui-image-editor-submenu {
  border-radius: 10px 10px 0 0 !important;
}
.tui-image-editor-container .tui-image-editor-partition > div {
  height: 40px;
}
/* 设置弹窗页面的主体内容的内边距，这样绘图区域会更大 */
// .el-dialog__body {
//   padding: 30px 5px;
// }

// /* 设置弹窗中的图床距离顶部距离 */
// .el-dialog__header {
//   margin-bottom: 25px;
// }
.tui-image-editor-submenu-item {
  padding: 8px 0 8px 0 !important;
}
.tui-image-editor-container {
  background-color: transparent;
}
.tui-image-editor-container li {
  line-height: initial;
}
.tui-image-editor-container .svg_ic-submenu {
  display: inline-block;
  // width: 27px !important;
  // height: 27px !important;
}
/* 强制压缩菜单的高度 ，减少占用屏幕的空间*/
.tui-image-editor-container .tui-image-editor-submenu {
  height: auto !important;
  position: sticky !important;
}

.tui-image-editor-container.bottom .tui-image-editor-submenu > div {
  padding: 0 !important;
}

/* 顶部工具栏定位 */
.tui-image-editor-container .tui-image-editor-header {
  top: 0;
}
.tui-image-editor-container .tui-image-editor-help-menu {
  border-radius: 10px 10px 0 0;
}
.tui-image-editor-container .tui-image-editor-help-menu.top {
  top: -32px;
  background-color: #f0f2f5;
  height: auto;
}
/* 取消超出部分隐藏，否则因为顶部工具栏已经超出去了，会显示不出来
.tui-image-editor-container {
  overflow: auto;
} */
/* 顶部工具栏定位 */
.tui-image-editor-container {
  overflow: visible;
}
</style>
