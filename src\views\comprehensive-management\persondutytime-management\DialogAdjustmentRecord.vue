<!-- 请假管理 -->
<template>
  <el-dialog
    v-model="dialogShow"
    title="调整记录"
    :close-on-click-modal="false"
    custom-class="custom-dialog-no"
    top="5vh"
    width="800px"
    @close="handleClose"
  >
    <el-table
      ref="tableRef"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      height="auto"
      size="medium"
      highlight-current-row
      class="dark-table format-height-table base-table"
    >
      <el-table-column label="姓名" prop="secSampleNum" :min-width="colWidth.person" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.names">
            <UserTag v-for="(item, index) in row.names.split(',')" :key="index" :name="item" />
          </div>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="调整操作" prop="startTime" :width="colWidth.datetime" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ operaTypeJson[row.operateType] || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="调整时间" prop="createTime" :min-width="colWidth.datetime" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.createTime || '--' }}
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :page="page" :limit="limit" :total="total" @pagination="initTable" />
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose">取 消</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, toRefs, watch } from 'vue';
import UserTag from '@/components/UserTag';
import { colWidth } from '@/data/tableStyle';
import { getNameByid } from '@/utils/common';
import Pagination from '@/components/Pagination';
import { formatDate } from '@/utils/formatTime';
import { initRecordList } from '@/api/dutyTime';
export default {
  name: 'DialogAdjustmentRecord',
  components: { UserTag, Pagination },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      dialogLoading: false, // 弹出窗loading
      status: '',
      total: 0,
      page: 1,
      limit: 20,
      operaTypeJson: {
        0: '移除',
        1: '新增'
      },
      dialogShow: false,
      tableData: [],
      tableLoading: false
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.total = 0;
        state.page = 1;
        state.limit = 20;
        initTable();
      }
    });
    const initTable = query => {
      const params = {
        status: state.status
      };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.page = query.page;
        state.limit = query.limit;
      } else {
        state.page = 1;
        params.page = '1';
        params.limit = state.limit.toString();
      }
      state.tableLoading = true;
      initRecordList(params).then(res => {
        state.tableLoading = false;
        if (res) {
          state.total = res.data.data.totalCount;
          state.tableData = res.data.data.list;
        }
      });
    };
    const onSubmit = () => {};
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog');
      state.dialogShow = false;
    };
    return { ...toRefs(state), initTable, onSubmit, formatDate, handleClose, getNameByid, colWidth };
  }
};
</script>
<style lang="scss" scoped>
.holidayTop {
  margin-bottom: 15px;
}
</style>
<style lang="scss">
.custom-dialog-no {
  .el-dialog__body {
    background-color: #fff !important;
  }
}
</style>
