import { loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import svgLoader from 'vite-svg-loader';
import { resolve } from 'path';
import eslint from 'vite-plugin-eslint';
// import px2rem from 'postcss-pxtorem';
import inject from '@rollup/plugin-inject';
// import vueDevTools from 'vite-plugin-vue-devtools';

/** 当前执行node命令时文件夹的地址（工作目录） */
const root = process.cwd();

export default ({ mode }) => {
  const { VITE_PORT, VITE_MINIO_GATEWAY, VITE_SOCKET_GATEWAY } = loadEnv(mode, root);
  return {
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
        vue: resolve(__dirname, './node_modules/vue/dist/vue.esm-bundler.js')
      },
      extensions: ['.js', '.vue', '.json']
    },
    define: {
      jQuery: 'window.jQuery',
      tui: 'window.tui'
    },
    // 服务端渲染
    server: {
      // 是否开启 https
      https: false,
      // 端口号
      port: Number(VITE_PORT),
      open: true,
      host: '0.0.0.0',
      hmr: {
        overlay: true
      },
      proxy: {
        '^/api/.*': {
          target: 'http://*************',
          changeOrigin: true
          // rewrite: path => path.replace(/^\/api/, '')
        },
        '^/FlashTable/': {
          target: 'http://*************:30020',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/flashtable/, '')
        },
        '^/minioview/.*': {
          target: 'http://*************',
          changeOrigin: true
          // rewrite: path => path.replace(/^\/api/, '')
        },
        '^/message/.*': {
          target: 'ws://*************',
          changeOrigin: true,
          ws: true
        }
      }
    },
    build: {
      commonjsOptions: { transformMixedEsModules: true }, // Change
      sourcemap: false,
      // 消除打包大小超过500kb警告
      chunkSizeWarningLimit: 4000,
      rollupOptions: {
        input: {
          index: resolve(__dirname, 'index.html')
        },
        // 静态资源分类打包
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]'
        }
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/styles/variables.scss";`
        }
      }
      // postcss: {
      //   plugins: [px2rem({ remUnit: 14 })]
      // }
    },
    optimizeDeps: {
      include: ['jquery']
    },
    plugins: [
      vue(),
      // vue开发工具：可以查看组件树，快速定位问题组件，吃CPU，需要时再使用
      // vueDevTools(),
      inject({
        $: 'jquery',
        jQuery: 'jquery'
      }),
      svgLoader({
        defaultImport: 'url'
      }),
      eslint({
        include: ['src/**/*.js', 'src/**/*.vue'],
        failOnError: false
      })
    ]
  };
};
