<template>
  <div ref="containerRef" class="virtual-table-container" @scroll.passive="handleScroll">
    <!-- 表头 -->
    <div ref="headerRef" class="virtual-table-header">
      <div class="virtual-table-row">
        <div
          v-for="column in columns"
          :key="column.key"
          class="virtual-table-cell"
          :style="{
            width: column.width + 'px',
            position: column.fixed ? 'sticky' : 'static',
            left: column.fixed === 'left' ? getFixedPosition(column, 'left') + 'px' : 'auto',
            right: column.fixed === 'right' ? getFixedPosition(column, 'right') + 'px' : 'auto',
            zIndex: column.fixed ? 2 : 1
          }"
        >
          {{ column.title }}
        </div>
      </div>
    </div>

    <!-- 表格内容 -->
    <div ref="bodyRef" class="virtual-table-body" :style="{ height: totalHeight + 'px' }">
      <div class="virtual-table-content" :style="{ transform: `translateY(${offsetY}px)` }">
        <div
          v-for="row in visibleData"
          :key="getRowKey(row)"
          class="virtual-table-row"
          :style="{ height: rowHeight + 'px' }"
        >
          <div
            v-for="column in columns"
            :key="column.key"
            class="virtual-table-cell"
            :style="{
              width: column.width + 'px',
              position: column.fixed ? 'sticky' : 'static',
              left: column.fixed === 'left' ? getFixedPosition(column, 'left') + 'px' : 'auto',
              right: column.fixed === 'right' ? getFixedPosition(column, 'right') + 'px' : 'auto',
              zIndex: column.fixed ? 2 : 1
            }"
          >
            <slot v-if="column.slot" :name="column.slot" :row="row" :column="column" />
            <template v-else>
              {{ row[column.key] || '--' }}
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- 固定列阴影 -->
    <div v-if="showLeftShadow" class="fixed-shadow fixed-shadow-left" :style="{ height: visibleHeight + 'px' }" />
    <div v-if="showRightShadow" class="fixed-shadow fixed-shadow-right" :style="{ height: visibleHeight + 'px' }" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  columns: {
    type: Array,
    required: true,
    validator: cols => cols.every(col => col.key && col.title && typeof col.width === 'number')
  },
  rowHeight: {
    type: Number,
    default: 48
  },
  bufferSize: {
    type: Number,
    default: 5
  },
  rowKey: {
    type: [String, Function],
    default: 'id'
  }
});

// const emit = defineEmits(['row-click']);

// 获取行唯一标识
const getRowKey = row => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(row);
  }
  return row[props.rowKey] || row.id;
};

// DOM 引用
const containerRef = ref(null);
const headerRef = ref(null);
const bodyRef = ref(null);

// 滚动状态
const scrollTop = ref(0);
const scrollLeft = ref(0);

// 计算属性
const totalHeight = computed(() => props.data.length * props.rowHeight);
const visibleHeight = computed(() => containerRef.value?.clientHeight || 0);
const startIndex = computed(() => Math.max(0, Math.floor(scrollTop.value / props.rowHeight) - props.bufferSize));
const endIndex = computed(() =>
  Math.min(
    props.data.length - 1,
    startIndex.value + Math.ceil(visibleHeight.value / props.rowHeight) + props.bufferSize * 2
  )
);
const offsetY = computed(() => startIndex.value * props.rowHeight);
const visibleData = computed(() => props.data.slice(startIndex.value, endIndex.value + 1));

// 固定列阴影
const showLeftShadow = computed(() => scrollLeft.value > 0);
const showRightShadow = computed(() => {
  if (!containerRef.value) return false;
  return scrollLeft.value + containerRef.value.clientWidth < containerRef.value.scrollWidth;
});

// 获取固定列位置
const getFixedPosition = (column, direction) => {
  if (!column.fixed || column.fixed !== direction) return 0;

  const fixedColumns = props.columns.filter(col => col.fixed === direction);
  const index = fixedColumns.findIndex(col => col.key === column.key);

  let position = 0;
  for (let i = 0; i < index; i++) {
    position += fixedColumns[i].width;
  }

  return position;
};

// 处理滚动
const handleScroll = e => {
  if (!containerRef.value) return;

  scrollTop.value = containerRef.value.scrollTop;
  scrollLeft.value = containerRef.value.scrollLeft;

  // 同步表头水平滚动
  if (headerRef.value) {
    headerRef.value.scrollLeft = scrollLeft.value;
  }
};

// 初始化
onMounted(() => {
  if (containerRef.value) {
    containerRef.value.addEventListener('scroll', handleScroll);
  }
});

onUnmounted(() => {
  if (containerRef.value) {
    containerRef.value.removeEventListener('scroll', handleScroll);
  }
});
</script>

<style scoped lang="scss">
.virtual-table-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.virtual-table-header {
  position: sticky;
  top: 0;
  z-index: 3;
  background-color: #f5f7fa;
  // overflow: hidden;
  border-bottom: 1px solid #ebeef5;
  .virtual-table-row .virtual-table-cell {
    background-color: #f5f7fa;
  }
}

.virtual-table-body {
  position: relative;
  // overflow: hidden;
}

.virtual-table-row {
  display: flex;
  box-sizing: border-box;
  width: 100%;
}

.virtual-table-cell {
  flex-shrink: 0;
  padding: 12px;
  box-sizing: border-box;
  border-bottom: 1px solid #ebeef5;
  background-color: #fff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 固定列阴影 */
.fixed-shadow {
  position: absolute;
  top: 0;
  width: 6px;
  pointer-events: none;
  z-index: 2;
}

.fixed-shadow-left {
  left: 0;
  // background: linear-gradient(to right, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0));
}

.fixed-shadow-right {
  right: 0;
  // background: linear-gradient(to left, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0));
}
</style>
