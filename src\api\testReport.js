import request from '@/utils/request';

// 检测报告列表【查询】
export function getReportList(data) {
  return request({
    url: '/api-orders/orders/report/list',
    method: 'post',
    data
  });
}
// 新增报告
export function addReportList(data) {
  return request({
    url: '/api-orders/orders/report/save',
    method: 'post',
    data
  });
}
// 删除报告
export function deleteReportList(data) {
  return request({
    url: '/api-orders/orders/report/delete',
    method: 'post',
    data
  });
}
// 检测报告详情-样品信息
export function getSampleInfoByReportId(reportId) {
  return request({
    url: `/api-orders/orders/report/info/${reportId}`,
    method: 'get'
  });
}
// 保存报告-样品信息
export function updateSampleInfoReport(data) {
  return request({
    url: '/api-orders/orders/report/update',
    method: 'post',
    data
  });
}
// 查询设备列表
export function getDeviceList(data) {
  return request({
    url: '/api-orders/orders/reportdetaildeviceusageinfo/selectList',
    method: 'post',
    data
  });
}
// 保存设备列表
export function saveDeviceList(data) {
  return request({
    url: '/api-orders/orders/reportdetaildeviceusageinfo/saveBatch',
    method: 'post',
    data
  });
}
// 查询图片信息
export function getReportImgList(data) {
  return request({
    url: '/api-orders/orders/reportimage/list',
    method: 'post',
    data
  });
}
// 保存图片信息
export function saveReportImgList(data) {
  return request({
    url: '/api-orders/orders/reportimage/save',
    method: 'post',
    data
  });
}
// 上传图片
export function uploadReportImg(data, callback) {
  return request({
    url: '/api-orders/orders/reportimage/saveImage',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: function (progressEvent) {
      // 原生获取上传进度的事件
      callback(progressEvent);
    },
    data
  });
}
// 图片删除
export function delReportImgList(data) {
  return request({
    url: '/api-orders/orders/reportimage/delete',
    method: 'post',
    data
  });
}
// 根据id查询检测标准信息
export function getStandardInfoById(data) {
  return request({
    url: '/api-orders/orders/reportdetailexpinfo/info',
    method: 'post',
    data
  });
}
// 检测标准--更新选中项
export function updateStandardInfoByIds(data) {
  return request({
    url: '/api-orders/orders/reportdetailexpinfo/update',
    method: 'post',
    data
  });
}
// 检测结果界面信息
export function getResultByReportId(reportId) {
  return request({
    url: `/api-orders/orders/reportdetailresultinfo/info/${reportId}`,
    method: 'get'
  });
}
// 保存检测结果界面信息
export function saveResultByIds(data) {
  return request({
    url: '/api-orders/orders/reportdetailresultinfo/save',
    method: 'post',
    data
  });
}
// 检测报告-第二步-获取检测结果汇总-首次进入
export function getReportstep2first(data) {
  return request({
    url: '/api-orders/orders/reportdetailexpinfo/reportstep2first',
    method: 'post',
    data
  });
}
// 检测报告-第二步-获取检测结果汇总-二次进入
export function getReportstep2second(data) {
  return request({
    url: '/api-orders/orders/reportdetailexpinfo/reportstep2second',
    method: 'post',
    data
  });
}
// 检测报告-第二步-判定标准选择下拉框切换数据
export function getSelectChanged(data) {
  return request({
    url: '/api-orders/orders/reportdetailexpinfo/selectchanged',
    method: 'post',
    data
  });
}
// 检测报告-第二步-保存检测结果汇总-首次进入
export function saveReportExpInfo(data) {
  return request({
    url: '/api-orders/orders/reportdetailexpinfo/save',
    method: 'post',
    data
  });
}
// 检测报告-第二步-保存检测结果汇总-二次进入
export function saveReportExpInfo2(data) {
  return request({
    url: '/api-orders/orders/reportdetailexpinfo/updateList',
    method: 'post',
    data
  });
}
// 检测报告-第二步-获取检测结果汇总
export function getReportExpInfo(data) {
  return request({
    url: '/api-orders/orders/reportdetailexpinfo/getReportDetailExpInfo',
    method: 'post',
    data
  });
}
// 根据reportid查询所有详情信息
export function getAllReportDetail(data) {
  return request({
    url: '/api-orders/orders/report/reportDetailAllInfo',
    method: 'post',
    data
  });
}
// 提交流程审批
export function processSubmit(data) {
  return request({
    url: '/api-orders/orders/report/processSubmit',
    method: 'post',
    data
  });
}
// 执行流程审批
export function processExecute(data) {
  return request({
    url: '/api-orders/orders/report/processExecute',
    method: 'post',
    data
  });
}
// 查询流程审批历史记录
export function getProcessHistory(processInstanceId) {
  return request({
    url: `/api-orders/orders/report/processHistory/${processInstanceId}`,
    method: 'get'
  });
}
// 下载报告
export function exportWord(reportId, categoryCode, sampleId) {
  return request({
    url: `/api-orders/orders/exportword/exportword/${reportId}/${categoryCode}/${sampleId}`,
    responseType: 'blob',
    headers: { 'Content-Type': 'application/octet-stream; charset=utf-8' },
    method: 'get'
  });
}
// 流程定义的查询
export function getProcessList(tenantType) {
  return request({
    url: `/api-orders/orders/report/findAllUserTaskName/${tenantType}`,
    method: 'get'
  });
}

/**
 * 检测报告-第二步-检测项目重置
 * @param {{"reportId": "", "sampleId": ""}} data
 * @returns
 */
export function resetReportCapability(data) {
  return request({
    url: '/api-orders/orders/reportdetailexpinfo/reportCapabilityReset',
    method: 'post',
    data
  });
}

/**
 * 根据id下载附件
 * @param id
 * @returns
 */
export function downloadById(id) {
  return request({
    url: `/api-orders/orders/attachment/fileDownloadById?id=${id}`,
    responseType: 'blob',
    method: 'get'
  });
}

/**
 * 附件信息删除
 * @param id
 * @returns
 */
export function deleteId(id) {
  return request({
    url: `/api-orders/orders/attachment/delete/${id}`,
    method: 'post'
  });
}

/**
 * 检测报告word批量导出
 * @param data
 * @returns
 */
export function batchExportReportWord(data) {
  return request({
    url: `/api-orders/orders/report/batchExportReportWord`,
    method: 'post',
    data
  });
}

/** 检测项目数据回传给中天mes */
export function experimentDataReturn(reportId) {
  return request({
    url: `/api-orders/orders/report/experimentDataReturn/${reportId}`,
    method: 'get'
  });
}

/** 查询具备“检测报告”【检测报告编辑】按钮资源权限的人员数据 */
export function listReportEdit(data) {
  return request({
    url: `/api-user/user/sysuser/users/listReportEdit`,
    method: 'post',
    data
  });
}
