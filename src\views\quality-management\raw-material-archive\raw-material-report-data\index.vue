<template>
  <div class="h-full flex flex-col overflow-hidden">
    <div class="flex justify-end mb-4">
      <el-button
        type="primary"
        :loading="state.exportRawMaterialListLoading"
        @click="exportRawMaterialList"
        @keyup.prevent
        @keydown.enter.prevent
        >导出原材料清单</el-button
      >
      <el-button
        :loading="state.exportReportLoading"
        type="primary"
        @click="onBatchExportReport"
        @keyup.prevent
        @keydown.enter.prevent
      >
        批量导出报告</el-button
      >
      <el-button
        :loading="state.exportOriginalRecordLoading"
        type="primary"
        @click="onExportOriginalRecord()"
        @keyup.prevent
        @keydown.enter.prevent
        >导出原始记录</el-button
      >
      <el-button
        :loading="state.batchExportWarrantyLoading"
        type="primary"
        @click="batchExportWarranty"
        @keyup.prevent
        @keydown.enter.prevent
      >
        批量导出质保书</el-button
      >
    </div>
    <div class="flex flex-col flex-1 overflow-hidden">
      <el-table
        ref="tableRef"
        key="id"
        v-loading="state.loading"
        :data="state.list"
        fit
        border
        height="auto"
        size="medium"
        highlight-current-row
        class="dark-table format-height-table base-table"
        :row-style="
          () => {
            return 'cursor: pointer';
          }
        "
        @header-dragend="drageHeader"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" fixed="left" prop="checkbox" :width="colWidth.checkbox" align="center" />
        <el-table-column
          v-for="item in columns"
          :key="item.field"
          :label="item.title"
          :prop="item.field"
          :min-width="colWidth.orderNo"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="item.field === 'latestArchivalReportEntity.reportNo'">
              {{ row.latestArchivalReportEntity?.reportNo }}
            </template>
            <template v-else-if="item.field === 'type'">
              {{ InspectionTypeConstMapDesc[row.type] }}
            </template>
            <template v-else-if="item.field === 'inboundLengthInputWarehouseUnit'">
              {{ row.inboundLength
              }}{{ row.inputWarehouseUnit ? state.dictionary['5'].all[row.inputWarehouseUnit] : '--' }}
            </template>
            <template v-else-if="item.field === 'warrantyCertificateList'">
              <div v-if="row.warrantyCertificateList?.length">
                <div class="flex flex-col gap-1">
                  <div
                    v-for="warranty in row.warrantyCertificateList"
                    :key="warranty.name"
                    class="cursor-pointer text-primary"
                    @click.stop="onDownloadWarrantyCertificate(warranty)"
                  >
                    {{ warranty.name }}
                  </div>
                </div>
              </div>
              <span v-else>--</span>
            </template>

            <template v-else-if="item.field === 'latestArchivalReportEntity.reportFormationByUserId'">
              <UserTag :name="getNameByid(row.latestArchivalReportEntity?.reportFormationByUserId) || '--'" />
            </template>
            <template v-else-if="item.field === 'latestArchivalReportEntity.result'">
              <div>{{ row.latestArchivalReportEntity?.result }}</div>
            </template>
            <template v-else-if="item.field === 'latestArchivalReportEntity.fileUrl'">
              <div
                v-if="row.latestArchivalReportEntity?.fileUrl"
                class="cursor-pointer text-primary"
                @click="onDownloadFile(row)"
              >
                {{ row.latestArchivalReportEntity?.reportNo ? `${row.latestArchivalReportEntity?.reportNo}.pdf` : '' }}
              </div>
              <span v-else>--</span>
            </template>
            <div v-else>{{ row[item.field] || '--' }}</div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :page="state.pagination.page"
        :limit="state.pagination.limit"
        :total="state.total"
        @pagination="handleQuery"
      />
    </div>
  </div>
</template>

<script setup>
import Pagination from '@/components/Pagination';
import { colWidth } from '@/data/tableStyle';
import { drageHeader } from '@/utils/formatTable';
import { reactive, ref, onMounted } from 'vue';
import { columns } from './column';
import { getNameByid } from '@/utils/common';
import { getDictionary } from '@/api/user';
import { querySampleArchival, downloadZip, exportProductionOrderRawMaterialRecord } from '@/api/order-sample-archival';
import { InspectionTypeConst, InspectionTypeConstMapDesc } from '@/const/inspection-type-const';
import UserTag from '@/components/UserTag';
import { ElMessage } from 'element-plus';
import { saveAs } from 'file-saver';
import { downloadFileWithFetch } from '@/utils/file';

const emits = defineEmits(['onGetNotFoundBatchNoList']);
defineExpose({ query });

const state = reactive({
  selected: [],
  list: [],
  loading: false,
  pagination: {
    page: 1,
    limit: 20
  },
  total: 0,
  dictionary: {
    5: {
      enable: {},
      all: {}
    }
  },
  exportRawMaterialListLoading: false,
  exportReportLoading: false,
  exportOriginalRecordLoading: false,
  batchExportWarrantyLoading: false
});

const tableRef = ref();

onMounted(() => {
  handleQuery(state.pagination);
  getDictionaryList();
});

const getDictionaryList = () => {
  Object.keys(state.dictionary).forEach(async item => {
    const response = await getDictionary(item);
    if (response) {
      state.dictionary[item] = { enable: {}, all: {} };
      response.data.data.dictionaryoption.forEach(optionItem => {
        if (optionItem.status == 1) {
          state.dictionary[item].enable[optionItem.code] = optionItem.name;
        }
        state.dictionary[item].all[optionItem.code] = optionItem.name;
      });
    }
  });
};

const handleSelectionChange = val => {
  state.selected = val;
};

const handleRowClick = row => {
  tableRef.value.toggleRowSelection(
    row,
    !state.selected.some(item => {
      return row.sampleId === item.sampleId;
    })
  );
};

const onDownloadFile = data => {
  downloadFileWithFetch(data.latestArchivalReportEntity.fileUrl, `${data.latestArchivalReportEntity?.reportNo}.pdf`);
  ElMessage.success('下载成功');
};

const onDownloadWarrantyCertificate = async warranty => {
  downloadFileWithFetch(warranty.fileUrl, warranty.name);
  ElMessage.success('下载成功');
};

const exportRawMaterialList = async () => {
  if (!Array.isArray(state.selected) || state.selected.length === 0) {
    ElMessage.warning('请选择原材料清单');
    return;
  }

  try {
    state.exportRawMaterialListLoading = true;
    const { data } = await exportProductionOrderRawMaterialRecord({
      batchNoList: state.selected.map(item => item.batchNo)
    });

    state.exportRawMaterialListLoading = false;
    if (!data.data?.fileUrl) {
      ElMessage.warning('导出原材料投料清单失败');
      return;
    }
    downloadFileWithFetch(data.data.fileUrl, `${state.selected[0].salesOrderNo}.xlsx`);
    ElMessage.success('下载成功');
  } catch (_ex) {
    state.exportRawMaterialListLoading = false;
  }
};
const onBatchExportReport = async () => {
  if (!Array.isArray(state.selected) || state.selected.length === 0) {
    ElMessage.warning('请选择导出报告');
    return;
  }

  // 2.检测报告：按照销售订单+样品编号+报告编号；
  const urls = state.selected
    .map(item => ({
      fileUrl: item.latestArchivalReportEntity?.fileUrl,
      fileName: `${item.salesOrderNo}-${item.secSampleNum}-${item.latestArchivalReportEntity?.reportNo || ''}.pdf`
    }))
    .filter(x => x.fileUrl);
  if (!Array.isArray(urls) || urls.length === 0) {
    ElMessage.warning('导出报告文件地址不能为空');
    return;
  }

  state.exportReportLoading = true;
  const { data } = await downloadZip(urls);
  saveAs(data, '导出报告.zip');
  state.exportReportLoading = false;
  ElMessage.success('下载成功');
};

const onExportOriginalRecord = async () => {
  if (!Array.isArray(state.selected) || state.selected.length === 0) {
    ElMessage.warning('请选择导出原始记录');
    return;
  }

  // 按照销售订单+样品编号+检测项目名称；

  const urls = state.selected
    .flatMap(item =>
      item.archivalExperimentEntityList.map(entity => ({
        fileName: `${item.salesOrderNo}-${item.secSampleNum || ''}-${entity.sourceName || ''}.pdf`,
        fileUrl: entity.fileUrl
      }))
    )
    .filter(x => x.fileUrl);

  if (!Array.isArray(urls) || urls.length === 0) {
    ElMessage.warning('导出原始记录文件地址不能为空');
    return;
  }
  state.exportOriginalRecordLoading = true;
  const { data } = await downloadZip(urls);
  saveAs(data, '原始记录.zip');
  state.exportOriginalRecordLoading = false;
  ElMessage.success('下载成功');
};

const batchExportWarranty = async () => {
  if (!Array.isArray(state.selected) || state.selected.length === 0) {
    ElMessage.warning('请选择导出质保书');
    return;
  }

  const urls = state.selected
    .flatMap(item =>
      item.warrantyCertificateList?.map(x => ({
        fileUrl: x.fileUrl,
        fileName: x.name
      }))
    )
    ?.filter(x => x.fileUrl);
  if (!Array.isArray(urls) || urls.length === 0) {
    ElMessage.warning('导出质保书文件地址不能为空');
    return;
  }
  state.batchExportWarrantyLoading = true;
  const { data } = await downloadZip(urls).finally(() => (state.batchExportWarrantyLoading = false));
  saveAs(data, '质保书文件.zip');
  ElMessage.success('下载成功');
};

async function query(params = {}) {
  state.pagination.page = 1;
  state.pagination.limit = 20;
  handleQuery({ ...params, ...state.pagination });
}

const handleQuery = async (params = {}) => {
  state.loading = true;
  const { data } = await querySampleArchival({
    ...params,
    sampleType: InspectionTypeConst.RAW_MATERIAL_INSPECTION
  }).finally(() => (state.loading = false));
  const { notFoundBatchNoList, pageUtils: { currPage, pageSize, totalCount, list } = {} } = data.data;
  state.pagination.limit = pageSize;
  state.pagination.page = currPage;
  state.total = totalCount;
  state.list = list;
  emits('onGetNotFoundBatchNoList', notFoundBatchNoList);
};
</script>

<style lang="scss" scoped></style>
