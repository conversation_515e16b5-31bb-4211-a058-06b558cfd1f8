<template>
  <!-- 角色管理 -->
  <ListLayout
    :has-page-header="false"
    :has-quick-query="false"
    :has-left-panel="true"
    :aside-panel-width="asidePanelWidth"
  >
    <template #page-left-side>
      <div class="tree-container">
        <div class="tree-header">
          <el-input v-model="filterText" size="small" placeholder="请输入角色名称" prefix-icon="el-icon-search" />
          <el-button
            v-if="asideWidth > 195 && getPermissionBtn('addRoleTreeBtn')"
            class="addTreeBtn"
            size="small"
            icon="el-icon-plus"
            @click="addTreeItem"
            @keyup.prevent
            @keydown.enter.prevent
          />
        </div>
        <div class="tree-content">
          <el-tree
            ref="treeRef"
            :data="treeData"
            node-key="id"
            :props="defaultProps"
            default-expand-all
            :expand-on-click-node="false"
            :highlight-current="true"
            draggable
            :filter-node-method="filterNode"
            :current-node-key="currentNodeKey"
            class="leftTree"
            @node-click="clickNode"
            @filter="filterNode"
          >
            <template #default="{ node, data }">
              <span>{{ node.label }}</span>
              <span v-if="data.status === 0" class="treeState disabled">(已停用)</span>
              <el-dropdown
                v-if="data.isadmin !== 1"
                class="tree-dropdown el-icon"
                trigger="hover"
                :class="node.showIcon ? 'icon-show' : ''"
                @visible-change="changeIcon(node.showIcon, node)"
              >
                <i class="el-icon-more" />
                <template v-if="getPermissionBtn('editRoleTreeBtn') || getPermissionBtn('delRoleTreeBtn')" #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="getPermissionBtn('editRoleTreeBtn')" @click="editTree(node.data, node)"
                      ><i class="iconfont tes-edit" />编辑</el-dropdown-item
                    >
                    <el-dropdown-item
                      v-if="getPermissionBtn('delRoleTreeBtn')"
                      class="color-red"
                      @click="delTree(node.data)"
                      ><i class="iconfont tes-delete" />删除</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-tree>
        </div>
      </div>
    </template>
    <el-tabs v-model="activeName" class="tab-box">
      <el-tab-pane label="权限设置" name="first" style="height: 100%">
        <Permissions :role-data="roleData" />
      </el-tab-pane>
      <el-tab-pane label="成员" name="second">
        <Members :role-data="roleData" :active-name="activeName" />
      </el-tab-pane>
    </el-tabs>
  </ListLayout>
  <!-- 添加树节点弹出框 -->
  <el-dialog
    v-model="roleDialog"
    :title="isAddTree === true ? '新增角色' : '编辑角色'"
    width="480px"
    :close-on-click-modal="false"
  >
    <el-form
      v-if="roleDialog"
      ref="formTree"
      v-loading="dialogLoading"
      :model="dialogFrom"
      :rules="dialogRules"
      label-position="right"
      label-width="110px"
      size="small"
    >
      <el-form-item label="状态：" prop="status">
        <el-radio-group v-model="dialogFrom.status" size="small">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="编号："
        prop="code"
        :label-width="formLabelWidth"
        :rules="{ required: true, message: '请输入编号', trigger: 'change' }"
      >
        <el-input v-model="dialogFrom.code" autocomplete="off" placeholder="请输入编号" maxlength="20" />
      </el-form-item>
      <el-form-item
        label="角色名称："
        prop="name"
        :label-width="formLabelWidth"
        :rules="{ required: true, message: '请输入角色名称', trigger: 'change' }"
      >
        <el-input v-model="dialogFrom.name" autocomplete="off" placeholder="请输入角色名称" maxlength="20" />
      </el-form-item>
      <el-form-item label="描述：" prop="content" :label-width="formLabelWidth">
        <el-input v-model="dialogFrom.content" :rows="2" autocomplete="off" type="textarea" placeholder="请输入描述" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="editDialogSuccess">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, ref, watch, getCurrentInstance, nextTick } from 'vue';
import Members from './components/member.vue';
import Permissions from './components/permission.vue';
import { getRoleTree, saveRoleTree, updateRoleTree, deleteRoleTree } from '@/api/roleManage';
import { ElMessage, ElMessageBox } from 'element-plus';
import { formatAllTree, formatTree } from '@/utils/formatJson';
import { getMemberList } from '@/api/roleManage';
import { getPermissionBtn } from '@/utils/common';
import ListLayout from '@/components/ListLayout';

export default {
  name: 'RoleManage',
  components: { Members, Permissions, ListLayout },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      asidePanelWidth: 300,
      asideWidth: 240,
      dialogTreeData: {},
      filterText: '',
      filterPMText: '',
      treeData: [],
      formTree: ref(),
      treeRef: ref(),
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      dialogFrom: {
        name: '',
        parentid: ''
      },
      dialogPMFrom: {
        name: '',
        productmodel: '',
        categoryid: '',
        categorycode: ''
      },
      activeName: 'first',
      treeLoading: false, // 角色管理树loading
      formData: {
        categoryid: 0,
        detectioncycle: 0,
        circulationmode: '',
        isretention: '',
        issamplepreparation: ''
      },
      currentParentForm: {},
      currentFormData: {},
      roleData: {},
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      showEdit: true,
      isAddPM: false,
      roleDialog: false,
      showEditPMDialog: false,
      dialogLoading: false,
      isAddTree: true,
      dialogRules: {},
      formLabelWidth: '120px',
      showIcon: false,
      titlePM: '',
      listPM: [],
      listQuery: {
        page: 1,
        limit: 20,
        categoryId: '',
        productModel: ''
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      currentNodeKey: ''
    });

    // 拖拽边框
    const widthChange = m => {
      state.asideWidth -= m;
      if (state.asideWidth <= 80) {
        state.asideWidth = 80;
      }
      if (state.asideWidth >= 600) {
        state.asideWidth = 600;
      }
    };
    // 过滤树节点
    watch(
      () => state.filterText,
      newValue => {
        state.treeRef.filter(newValue);
      }
    );
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    // 角色管理树列表接口
    const getTreeList = () => {
      state.treeLoading = true;
      getRoleTree({}).then(function (res) {
        state.treeLoading = false;
        if (res) {
          const data = res.data.data;
          state.treeData = formatTree(data);
          if (!state.roleData.id) {
            state.roleData = data[0];
          }
          if (state.roleData.id) {
            nextTick(() => {
              state.treeRef.setCurrentKey(state.roleData.id, true);
            });
          }
        }
      });
    };
    getTreeList();
    const loadNode = (node, resolve) => {
      if (!node.data.children || node.data.children.length === 0) {
        delete node.data['children'];
        node.data.leaf = true;
        // node.isLeaf = true
      }
      return resolve([]);
    };
    // 鼠标hover到树节点
    const mouseover = () => {
      state.showIcon = true;
    };
    const mouseleave = () => {
      state.showIcon = false;
    };
    // 新增树节点
    const addTreeItem = () => {
      state.roleDialog = true;
      state.dialogFrom = {
        status: 1
      };
      state.isAddTree = true;
    };
    // 树节点编辑
    const editTree = (data, node) => {
      state.treeData = formatAllTree(data.id, state.treeData);
      state.roleDialog = true;
      state.isAddTree = false;
      state.dialogFrom = JSON.parse(JSON.stringify(data));
    };
    // 保存树节点
    const editDialogSuccess = () => {
      proxy.$refs['formTree'].validate(valid => {
        if (valid) {
          if (state.isAddTree !== true) {
            state.dialogLoading = true;
            updateRoleTree(state.dialogFrom).then(function (res) {
              state.dialogLoading = false;
              if (res !== false && res.data.code === 200) {
                ElMessage.success('编辑成功!');
                state.roleDialog = false;
                getTreeList();
              }
            });
          } else {
            state.dialogLoading = true;
            saveRoleTree(state.dialogFrom).then(function (res) {
              state.dialogLoading = false;
              if (res) {
                ElMessage.success('新增成功!');
                state.roleDialog = false;
                getTreeList();
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    // 树节点删除
    const delTree = node => {
      var ids = [];
      ids.push(node.id);
      ElMessageBox({
        title: '提示',
        message: '是否删除该角色?',
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(() => {
          getMemberList({ roleId: node.id, isadmin: node.isadmin, page: '1', limit: '20' }).then(res => {
            if (res) {
              if (res.data.data.totalCount) {
                ElMessage.error('此角色下存在成员，请转移到其他角色下后再进行删除!');
              } else {
                deleteRoleTree(ids).then(function (res) {
                  if (res) {
                    ElMessage.success('删除成功!');
                    if (state.roleData.id === node.id) {
                      state.roleData.id = '';
                    }
                    getTreeList();
                  }
                });
              }
            }
          });
        })
        .catch(() => {});
    };
    // 编辑
    const edit = () => {
      state.showEdit = false;
    };
    // 取消树节点编辑
    const cancel = () => {
      state.roleDialog = false;
    };

    const changeIcon = (command, node) => {
      if (!command) {
        node.showIcon = true;
      } else {
        node.showIcon = !node.showIcon;
      }
    };
    const clickNode = (data, node) => {
      state.roleData = data;
    };
    return {
      ...toRefs(state),
      getPermissionBtn,
      clickNode,
      getTreeList,
      widthChange,
      filterNode,
      addTreeItem,
      edit,
      editDialogSuccess,
      editTree,
      delTree,
      cancel,
      mouseover,
      mouseleave,
      loadNode,
      changeIcon
    };
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
// 左侧区域高度
.tree-container .tree-content {
  height: calc(100vh - 170px);
}

.tab-box {
  :deep(.el-tabs__header) {
    margin-bottom: 20px;
  }
  :deep(.el-tabs__content) {
    height: calc(100vh - 185px);
  }
}
// .tab-box{
//   :deep(.el-tabs__nav-scroll){
//     background: #FFFFFF;
//     border-radius: 8px;
//     padding:3px 1rem 0;
//   }
//   :deep(.el-tabs__header) {
//     margin-bottom: 1rem;
//     border-radius: 8px;
//     background: #ffffff;
//     padding: 0 8px 4px;
//   }
// }
// .el-tree .el-tree-node__content .treeState {
//   font-size: 12px;
//   color: #909399;
//   display: inline-block;
//   margin-left: 5px;
// }
.color-red {
  color: $red;
}
.material-classification {
  .base-form {
    background: #ffffff;
    border-radius: 4px;
    margin-top: 15px;
    padding: 21px 0 7px 0;
    .el-form-item {
      :deep(.el-form-item__content) {
        text-align: left;
      }
    }
  }
}
</style>
