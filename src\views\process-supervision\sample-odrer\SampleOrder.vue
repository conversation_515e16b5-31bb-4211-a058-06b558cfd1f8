<template>
  <!-- 样品下达 -->
  <ListLayout :has-button-group="getPermissionBtn('sampleOrderBatch') ? true : false">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" @submit.prevent>
        <el-form-item prop="param">
          <div style="width: 42vw">
            <CombinationQuery
              :field-list="searchFieldList"
              :field-tip="searchTips"
              @get-single-text="getSingleText"
              @get-param-list="getParamList"
              @reset-search="reset"
            />
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="text" size="large" @click="search" @keyup.prevent @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
          /></el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        type="primary"
        size="large"
        icon="el-icon-document-copy"
        :disabled="multipleSelection.length === 0"
        @click="handleMultipleOrder()"
        >批量下达</el-button
      >
      <el-button
        v-if="formInline.status == 1 && getPermissionBtn('sampleArchive')"
        type="primary"
        size="large"
        icon="el-icon-document-copy"
        :disabled="multipleSelection.length === 0"
        @click="handleArchiving()"
        >归档</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="searchFrom" :model="formInline" label-width="120px" label-position="right" size="small">
            <el-form-item :label="tenantInfo.type === 0 ? '来样日期：' : '登记日期：'">
              <el-date-picker
                v-model="warehouseDateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="changeRuku"
              />
            </el-form-item>
            <el-form-item label="下达日期：">
              <el-date-picker
                v-model="orderDateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="changeXaida"
              />
            </el-form-item>
            <el-form-item label="试验负责人：" prop="sealStaffName" class="seal-staff-name">
              <el-select
                v-model="formInline.ownerId"
                placeholder="请选择"
                size="small"
                clearable
                filterable
                :filter-method="filterUserList"
                @focus="filterUserList(null)"
              >
                <el-option v-for="item in nameList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="16" class="flex gap-2">
          <el-radio-group v-model="formInline.status" size="small" style="float: left" @change="getList()">
            <el-radio-button label=" ">全部</el-radio-button>
            <el-radio-button label="0">待下达</el-radio-button>
            <el-radio-button label="1">已下达</el-radio-button>
            <el-radio-button label="2">已作废</el-radio-button>
          </el-radio-group>
          <el-select
            v-if="getPermissionBtn('inspectionType')"
            v-model="formInline.type"
            class="owner-select"
            filterable
            placeholder="请选择检验类型"
            size="small"
            clearable
            @change="getList()"
          >
            <el-option v-for="(val, key) in dictionaryAll['JYLX'].enable" :key="key" :label="val" :value="key" />
          </el-select>
          <el-select
            v-if="getPermissionBtn('registerDepartment')"
            v-model="formInline.registerDepartment"
            class="owner-select"
            filterable
            placeholder="请选择送检部门"
            size="small"
            clearable
            @change="getList()"
          >
            <el-option v-for="(val, key) in dictionaryAll['JHDW']?.enable" :key="key" :label="val" :value="val" />
          </el-select>
        </el-col>
        <el-col :span="8" style="text-align: right">
          <TableColumnView binding-menu="SampleOrder" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="multipleSelectionRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="tableList"
      class="dark-table base-table format-height-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      fit
      border
      height="auto"
      highlight-current-row
      size="medium"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" prop="checkbox" :width="colWidth.checkbox" align="center" fixed="left" />

      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="item.fieldType === fieldTypesEnum.Link">
              <div
                v-if="item.fieldKey === 'secSampleNum' && row[item.fieldKey]"
                v-copy="row[item.fieldKey]"
                class="blue nowrap"
                @click.stop="handleDetail(row)"
              >
                {{ row[item.fieldKey] ? row[item.fieldKey] : '--' }}
              </div>
              <span
                v-else-if="item.fieldKey === 'presentationCode' && row[item.fieldKey]"
                v-copy="row[item.fieldKey]"
                class="nowrap blue-color"
                @click.stop="jumpApplicationDetail(row)"
              >
                {{ row[item.fieldKey] ? row[item.fieldKey] : '--' }}
              </span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Person">
              <UserTag :name="getNameByid(row[item.fieldKey]) || '--'" />
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Status">
              <el-tag
                v-if="item.fieldKey === 'status'"
                size="small"
                :type="row[item.fieldKey] == '0' ? 'warning' : 'success'"
              >
                {{ row[item.fieldKey] == '0' ? '待下达' : '已下达' }}
              </el-tag>

              <span v-else-if="item.fieldKey === 'reportType'">{{
                row.reportType.toString() === '0'
                  ? '合格'
                  : row.reportType.toString() === '1'
                  ? '不合格'
                  : row.reportType.toString() === '2'
                  ? '不判定'
                  : '--'
              }}</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Date">
              <span>{{ row[item.fieldKey] ? formatDateTime(row[item.fieldKey]) : '--' }}</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Custom">
              <!-- 检验对象 -->
              <div v-if="item.fieldKey === 'inputWarehouseNo,productionProcedureNo'" class="nowrap">
                <span>{{
                  row.type === 2
                    ? row.registerDepartment + row.materialNo + '号变' || '--'
                    : dictionaryAll['equipment'].all[row.productionProcedureNo] || row.productionProcedureNo || '--'
                }}</span>
              </div>
              <!--工厂 1，8 原材料属性，其余生产 -->
              <div v-else-if="item.fieldKey === 'inputWarehouseNo,productionOrderNo,factory'" class="nowrap">
                <span>{{ [1, 8].includes(row.type) ? row.inputWarehouseNo : row.productionOrderNo }}</span>
              </div>
              <!--对象位置 -->
              <span v-else-if="item.fieldKey === 'productionProcedure,productionStation,wareHouseName'">{{
                [1, 8].includes(row.type)
                  ? row.wareHouseName || '--'
                  : (row.productionProcedure || '--') + '-' + (row.productionStation || '--')
              }}</span>
              <!--对象名称 -->
              <span v-else-if="item.fieldKey === 'customerName,supplierName'">{{
                [1, 8].includes(row.type) ? row.supplierName || '--' : row.customerName || '--'
              }}</span>
              <span v-else-if="item.fieldKey === 'type'">{{
                dictionaryAll['JYLX'].all[row[item.fieldKey]] || '--'
              }}</span>
              <template v-else-if="item.fieldKey === 'sampleNum'">
                <div v-if="row.sampleNum" class="nowrap">
                  {{ row.sampleNum }}{{ filterSampleUnitToName(row.sampleUnit) || row.sampleUnit }}
                </div>
                <div v-else class="nowrap">--</div>
              </template>
            </template>
            <template v-else-if="item.fieldKey === 'reportNo'">
              <div v-if="row.reportNo" class="nowrap blue" @click="onRedirectReportDetail(row)">
                {{ row.reportNo }}
              </div>
              <div v-else class="nowrap">--</div>
            </template>
            <span v-else>{{ row[item.fieldKey] || '--' }}</span>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" :width="colWidth.operation" prop="caozuo" fixed="right" class-name="fixed-right">
        <template #default="scope">
          <span class="blue-color" @click.stop="handleDetail(scope.row)"> 查看 </span>
          <span
            v-if="getPermissionBtn('sampleOrderBtn') && scope.row.isInvalidated !== 1"
            class="blue-color"
            @click.stop="handleOrder(scope.row)"
          >
            下达
          </span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="formInline.page"
      :limit="formInline.limit"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getList"
    />
    <template #other>
      <ModelOrder :is-show="dialogFormVisible" :sample-info="samplesInfo" @setInfo="getSInfo" @close="closeModel()" />
      <!-- 手动入库 -->
      <module-storage
        module-title="批量入库"
        :visible="dialogStorageVisible"
        :lists="fastInbound[1]"
        @close="closeStorage"
      />
      <DialogArchive
        :dialog-visible="dialogArchiveVisiable"
        :select-archive="selectArchive"
        @closeDialog="closeDialog"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, onMounted, toRefs } from 'vue';
import router from '@/router/index.js';
import Pagination from '@/components/Pagination';
import { samplesList } from '@/api/order';
import { ElMessage, ElMessageBox } from 'element-plus';
import { formatDate, formatDateTime } from '@/utils/formatTime';
import ModelOrder from '@/views/process-supervision/sample-odrer/ModuleOrder';
import ModuleStorage from '@/views/process-supervision/sample-storage/components/ModuleStorage';
import { materialCategoryList } from '@/api/material';
import UserTag from '@/components/UserTag';
import _ from 'lodash';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { checkPermissionList } from '@/api/permission';
import ListLayout from '@/components/ListLayout';
import { addByTemp, withDrawTodoById } from '@/api/messageAgent';
import { getLoginInfo } from '@/utils/auth';
import { mapGetters, useStore } from 'vuex';
import { formatTenantId, filterSampleUnitToName } from '@/utils/formatJson';
import { colWidth } from '@/data/tableStyle';
import { getInspectionList } from '@/api/inspection-application';
import { getTaskRegistrationList } from '@/api/task-registration';
import CombinationQuery from '@/components/CombinationQuery';
import DialogArchive from './components/DialogArchive';
import TableColumnView from '@/components/TableColumnView';
import { getDictionary } from '@/api/user';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';
import { addWarehousings } from '@/api/samplestorage';
import { useRoute } from 'vue-router';

export default {
  name: 'SampleOrder',
  components: {
    Pagination,
    ModelOrder,
    ListLayout,
    UserTag,
    CombinationQuery,
    DialogArchive,
    TableColumnView,
    ModuleStorage
  },
  setup() {
    // const _ = inject('_')
    const route = useRoute();
    const store = useStore();
    const state = reactive({
      tableColumns: [],
      tableList: [],
      mangeList: [],
      dialogStorageVisible: false, // 手动入库
      searchTips: '',
      samplesInfo: [],
      currentAccountId: getLoginInfo().accountId,
      currentTenantId: getLoginInfo().tenantId,
      nameList: store.state.common.nameList,
      copyNameList: store.state.common.nameList,
      dialogArchiveVisiable: false, // 归档
      fastInbound: { 0: [], 1: [], 2: [] }, // 快速入库类型
      selectArchive: {},
      dictionaryAll: {
        JYLX: {
          enable: {},
          all: {}
        },
        equipment: {
          enable: {},
          all: {}
        },
        JHDW: {
          enable: {},
          all: {}
        }
      },
      sampleAssignListAssignBatch: false,
      multipleSelection: [],
      multipleSelectionRef: ref(),
      dialogFormVisible: false,
      total: 0,
      warehouseDateRange: [],
      formInline: {
        page: 1,
        limit: 20,
        tableQueryParamList: [],
        param: '',
        status: '0',
        ownerId: '',
        startTime: '',
        assignedStartTime: '',
        // 送检部门
        registerDepartment: ''
      },
      orderDateRange: [],
      searchFieldList: [],
      currentSampleOrder: null,
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ]
    });
    const editFrom = ref('');
    const searchFrom = ref(null);
    const activeName = ref('0');
    const showS = ref(false);
    const otherForm = reactive({
      types: [],
      userOptions: [],
      list: [],
      content: ''
    });
    const activeTabsName = ref('first');
    const listLoading = ref(false);
    const tableKey = ref(0);
    function onSubmit() {
      getList();
    }
    function reset() {
      editFrom.value.resetFields();
      searchFrom.value.resetFields();
      state.formInline = {
        page: 1,
        limit: 20,
        tableQueryParamList: [],
        param: '',
        status: '0',
        ownerId: '',
        startTime: '',
        assignedStartTime: ''
      };
      state.multipleSelection = [];
      // state.total = 0
      state.warehouseDateRange = [];
      state.orderDateRange = [];
      getList();
    }
    const search = () => {
      showS.value = !showS.value;
      if (activeName.value === '0') {
        activeName.value = '1';
      } else {
        activeName.value = '0';
      }
    };
    materialCategoryList('').then(resdata => {
      const materialListByP = resdata.data.data;
      otherForm.types = [];
      materialListByP.forEach(m => {
        if (m.parentId === '0') {
          otherForm.types.push(m);
        }
      });
    });
    const getDicList = () => {
      Object.keys(state.dictionaryAll).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          state.dictionaryAll[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              state.dictionaryAll[item].enable[optionItem.code] = optionItem.name;
            }
            state.dictionaryAll[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    getDicList();
    // 试验负责人
    checkPermissionList('allocation').then(res => {
      state.mangeList = res.data.data;
    });
    const getList = query => {
      if (query) {
        state.formInline.page = query.page;
        state.formInline.limit = query.limit;
      }
      listLoading.value = true;
      const searchdata = JSON.parse(JSON.stringify(state.formInline));
      if (searchdata.status === '2') {
        searchdata.isInvalidated = '1';
        searchdata.status = '';
      } else {
        searchdata.isInvalidated = '0';
      }
      searchdata.page = JSON.stringify(searchdata.page);
      searchdata.limit = JSON.stringify(searchdata.limit);
      samplesList(searchdata)
        .then(res => {
          if (res) {
            state.total = res.data.data.totalCount;
            state.tableList = res.data.data.list;
          }
        })
        .finally(() => (listLoading.value = false));
    };
    const sortChange = data => {};

    const handleSelectionChange = val => {
      state.multipleSelection = val;
    };
    const handleRowClick = row => {
      if (row && row.id) {
        const rowIndex = state.tableList.findIndex(item => item.id === row.id);
        if (rowIndex !== -1) {
          row.checkbox = !row.checkbox;
          state.multipleSelectionRef.toggleRowSelection(state.tableList[rowIndex], row.checkbox);
        }
      }
    };
    const getSingleText = val => {
      state.formInline.param = val;
      state.formInline.tableQueryParamList = [];
      getList();
    };

    const getParamList = paramList => {
      state.formInline.tableQueryParamList = paramList;
      state.formInline.param = '';
      getList();
    };
    // 处理批量下达操作
    const handleMultipleOrder = () => {
      state.dialogFormVisible = true;
      state.samplesInfo = JSON.parse(JSON.stringify(state.multipleSelection));
    };
    // 下达
    const handleOrder = row => {
      state.currentSampleOrder = row;
      state.dialogFormVisible = true;
      state.samplesInfo = JSON.parse(JSON.stringify([row]));
    };
    // 归档
    const handleArchiving = () => {
      if (state.multipleSelection.length > 1) {
        ElMessage('一次只能归档一个数据~');
      } else {
        if (state.multipleSelection[0].status === '0') {
          ElMessage('只能归档已下达的数据~');
          return false;
        }
        ElMessageBox({
          title: '提示',
          message: '是否确认归档',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'info'
        })
          .then(() => {
            state.dialogArchiveVisiable = true;
            state.selectArchive = state.multipleSelection[0];
          })
          .catch(() => {});
      }
    };
    const closeDialog = () => {
      state.dialogArchiveVisiable = false;
    };
    const changeStatus = val => {
      getList();
    };
    const statusFilterName = status => {
      const statusNames = {
        0: 'success',
        1: 'info'
      };
      return statusNames[status];
    };
    const closeModel = () => {
      state.dialogFormVisible = false;
      state.samplesInfo = [];
    };
    // 关闭手动入库
    const closeStorage = () => {
      state.dialogStorageVisible = false;
      state.samplesInfo = [];
      getList();
    };
    // 快速入库
    const setWarehouseEntry = () => {
      state.fastInbound = { 0: [], 1: [], 2: [] };
      state.samplesInfo.forEach(item => {
        state.fastInbound[item.isFastInbound || 0].push({
          ...item,
          localWarehousingTime: formatDate(new Date()),
          inputWarehouseQuantity: item.sampleQuantity,
          prodType: '完好',
          sampleWarehousingStatus: '完好'
        });
      });
      if (state.fastInbound[2].length) {
        // 自动入库
        addWarehousings(state.fastInbound[2]).then(res => {
          state.samplesInfo = [];
          if (res) {
            ElMessage.success('自动入库成功');
            getList();
          }
        });
      }
      if (state.fastInbound[1].length) {
        // 手动入库 延迟打开弹出框，否则下达成功的提示语看不到
        setTimeout(() => {
          state.dialogStorageVisible = true;
        }, 1000);
      }
      if (!state.fastInbound[2].length && !state.fastInbound[1].length) {
        state.samplesInfo = [];
        getList();
      }
    };
    // 样品下达弹出框-确定接口调用成功后，发待办消息给试验员
    const getSInfo = list => {
      // 操作入库
      setWarehouseEntry();
      const params = {
        eventCode: 'M002',
        receiverType: '1',
        senderName: getNameByid(state.currentAccountId),
        receiverIds: list.formdata[0].ownerId,
        receiverNames: getNameByid(list.formdata[0].ownerId),
        todoStartTime: list.formdata[0].startDate,
        todoEndTime: list.formdata[0].finishedDate,
        c_ids: '',
        c_b_samplesIdArray: '',
        c_b_sampleNoArray: ''
      };
      // 直接下达
      if (
        list.idsStatus0 === '' &&
        list.nos0 === '' &&
        list.idsStatus1 === '' &&
        list.nos1 === '' &&
        list.idsStatus2 === '' &&
        list.nos2 === ''
      ) {
        params.c_ids = state.currentSampleOrder.sampleId;
        params.c_b_sampleNoArray = state.currentSampleOrder.secSampleNum;
        params.c_b_samplesIdArray = state.currentSampleOrder.sampleId;
        if (state.currentSampleOrder.status === 0) {
          // 未下达的待办
          params.eventCode = 'M002';
          // 添加消息待办
          addByTemp(params).then(res => {
            if (res !== false) {
              // console.log(res.data)
            }
          });
        } else if (state.currentSampleOrder.status === 1) {
          // 已经下达，未分配
          // 修改下達信息，修改試驗負責人,先撤原来的待办，然后再重新下达新待办
          // 发新消息给新的试验负责人
          params.eventCode = 'M002';
          addByTemp(params).then(res1 => {
            if (res1 !== false) {
              // console.log(res1.data)
            }
          });
          // 撤销原来负责人
          withDrawTodoById({ c_ids: state.currentSampleOrder.sampleId }).then(res => {
            if (res !== false) {
              params.eventCode = 'M003';
              params.receiverIds = state.currentSampleOrder.ownerId;
              params.receiverNames = getNameByid(state.currentSampleOrder.ownerId);
              addByTemp(params).then(res2 => {
                if (res2 !== false) {
                  // console.log(res2.data)
                }
              });
            }
          });
        } else if (state.currentSampleOrder.status === 2) {
          // 已经下达，已经分配
          params.eventCode = 'M004';
          // 添加消息待办
          addByTemp(params).then(res => {
            if (res !== false) {
              // console.log(res.data)
            }
          });
        }
      } else {
        // 批量下达，3种情况
        // 已经下达，未分配的需要先撤回之前的待办，然后重新下达新的待办
        if (list.idsStatus1 && list.nos1) {
          // 修改下達信息，已下达未分配，修改試驗負責人
          withDrawTodoById({ c_ids: list.idsSample1 }).then(res => {
            if (res !== false) {
              // 先发消息给新的试验负责人
              params.eventCode = 'M002';
              params.c_ids = list.idsSample1;
              params.c_b_sampleNoArray = list.nos1;
              params.c_b_samplesIdArray = list.idsSample1;
              addByTemp(params).then(res1 => {
                if (res1 !== false) {
                  // console.log(res1.data)
                }
              });
              // 撤销老负责人
              params.eventCode = 'M003';
              params.receiverIds = state.currentSampleOrder.ownerId;
              params.receiverNames = getNameByid(state.currentSampleOrder.ownerId);
              addByTemp(params).then(res1 => {
                if (res1 !== false) {
                  // console.log(res1.data)
                }
              });
            }
          });
        }
        // 未下达的直接，发待办
        if (list.idsStatus0 && list.nos0) {
          params.eventCode = 'M002';
          params.c_ids = list.idsSample0;
          params.c_b_sampleNoArray = list.nos0;
          params.c_b_samplesIdArray = list.idsSample0;
          addByTemp(params).then(res1 => {
            if (res1 !== false) {
              // console.log(res1.data)
            }
          });
        }
        // 已下达，已经分配
        if (list.idsStatus2 && list.nos2) {
          params.eventCode = 'M004';
          params.c_ids = list.idsSample2;
          params.c_b_sampleNoArray = list.nos2;
          params.c_b_samplesIdArray = list.idsSample2;
          addByTemp(params).then(res1 => {
            if (res1 !== false) {
              // console.log(res1.data)
            }
          });
        }
      }
    };
    /** 缓存查询状态 */
    function setCurrentStatus() {
      const currentState = {
        formInline: state.formInline
      };
      sessionStorage.setItem(route.name, JSON.stringify(currentState));
    }
    function consumeCurrentStatus() {
      const currentSorage = sessionStorage.getItem(route.name);
      console.log(currentSorage);
      if (currentSorage) {
        const currentState = JSON.parse(currentSorage);
        state.formInline = currentState.formInline;
      }
      getList();
    }

    // 跳转样品详情
    const handleDetail = row => {
      console.log(state.formInline);
      setCurrentStatus();
      router.push({
        path: '/sample/detail',
        query: {
          orderId: row.orderId,
          sampleId: row.sampleId
        }
      });
    };

    const inputValue = data => {
      // console.log(data)
      if (typeof data === 'string') {
        otherForm.content = data;
      } else {
        otherForm.content = '';
      }
    };
    // 高级搜索-来样日期-change
    const changeRuku = date => {
      state.formInline.startTime = date ? formatDateTime(date[0]) : '';
      state.formInline.endTime = date ? formatDateTime(date[1]) : '';
    };
    // 高级搜索-下达日期-change
    const changeXaida = date => {
      state.formInline.assignedStartTime = date ? formatDateTime(date[0]) : '';
      state.formInline.assignedEndTime = date ? formatDateTime(date[1]) : '';
    };

    // 过滤试验员
    const filterUserList = val => {
      if (val) {
        const list = [];
        state.copyNameList.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        state.nameList = list;
      } else {
        state.nameList = state.copyNameList;
      }
    };

    // 查看申请详情
    const jumpApplicationDetail = row => {
      if (row.type === 10 || row.type === 11 || row.type === 12) {
        getTaskRegistrationList({
          condition: `${row.presentationCode}`
        }).then(res => {
          if (res && res.status === 200) {
            if (res.data.data.list.length > 0) {
              router.push({
                name: 'SampleOrderRegistration',
                query: { id: res.data.data.list.find(item => item.entrustNo === row.presentationCode)?.id, flag: 1 }
              });
            }
          }
        });
      } else {
        getInspectionList({ param: `${row.presentationCode}` }).then(res => {
          if (res && res.status === 200) {
            if (res.data.data.list.length > 0) {
              router.push({
                name: 'SampleOrderApplication',
                query: { id: res.data.data.list[0].id, flag: 1 }
              });
            }
          }
        });
      }
    };

    // 更新表格字段
    const onUpdateColumns = columns => {
      tableKey.value = tableKey.value + 1;
      state.tableColumns = columns;
      state.searchFieldList = state.tableColumns.filter(item => {
        return item.isQuery == 1;
      });
      state.searchTips = state.searchFieldList.map(item => item.fieldName).join('/');
    };

    const onRedirectReportDetail = rowData => {
      router.push({
        path: '/detail-report',
        query: {
          reportId: rowData.reportId,
          sampleId: rowData.sampleId,
          reportStage: 6
        }
      });
    };

    onMounted(() => {
      consumeCurrentStatus();
    });

    return {
      ...toRefs(state),
      getPermissionBtn,
      getSingleText,
      getParamList,
      filterUserList,
      drageHeader,
      getNameByid,
      changeXaida,
      closeStorage,
      changeRuku,
      formatDateTime,
      changeStatus,
      formatDate,
      closeModel,
      getSInfo,
      inputValue,
      activeTabsName,
      closeDialog,
      handleSelectionChange,
      handleRowClick,
      handleMultipleOrder,
      handleArchiving,
      statusFilterName,
      handleOrder,
      handleDetail,
      sortChange,
      getList,
      tableKey,
      listLoading,
      showS,
      editFrom,
      searchFrom,
      otherForm,
      search,
      activeName,
      onSubmit,
      reset,
      formatTenantId,
      filterSampleUnitToName,
      colWidth,
      jumpApplicationDetail,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum,
      onRedirectReportDetail
    };
  },
  computed: {
    ...mapGetters(['tenantGroup', 'tenantInfo'])
  },
  created() {}
};
</script>
<style lang="scss" scoped>
.btn-mg20 {
  margin-right: 20px;
}

.label-type {
  margin-bottom: 0px;
}
</style>
