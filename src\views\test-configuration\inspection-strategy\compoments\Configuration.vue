<template>
  <!-- 检验策略 - 检验项目配置 -->
  <div class="testMaterial">
    <div class="flex-bc">
      <div class="searchInput">
        <el-input
          v-model="itemconfigcondition"
          v-trim
          v-focus
          placeholder="请输入编号/名称进行搜索"
          clearable
          size="small"
          @keyup.enter="getTableList"
          @clear="getTableList"
        >
          <template #prefix>
            <i class="el-input__icon el-icon-search" />
          </template>
        </el-input>
        <el-button type="primary" size="small" @click="getTableList" @keyup.prevent @keydown.enter.prevent
          >查询</el-button
        >
        <el-button size="small" @click="reset" @keyup.prevent @keydown.enter.prevent>重置</el-button>
      </div>
      <div v-if="getPermissionBtn('addProject')" class="searchRight fr">
        <el-button
          v-if="!isEditParameter && getPermissionBtn('ExportPolicy')"
          :loading="downloadLoading"
          type="text"
          size="small"
          @click="handleReload()"
          @keyup.prevent
          @keydown.enter.prevent
          >获取最新检测项目</el-button
        >
        <el-button
          v-if="!isEditParameter && getPermissionBtn('ExportPolicy')"
          :loading="downloadLoading"
          type="primary"
          size="small"
          icon="el-icon-download"
          @click="exportToExcel()"
          @keyup.prevent
          @keydown.enter.prevent
          >导出标准</el-button
        >
        <el-button
          v-if="!isEditParameter"
          :loading="downloadLoading"
          type="primary"
          size="small"
          icon="el-icon-edit"
          @click="isEditParameter = !isEditParameter"
          @keyup.prevent
          @keydown.enter.prevent
          >编辑参数</el-button
        >
        <el-button
          v-if="isEditParameter"
          :loading="tableLoading"
          type="info"
          size="small"
          icon="el-icon-cancle"
          @click="cancelEdit()"
          @keyup.prevent
          @keydown.enter.prevent
          >取消</el-button
        >
        <el-button
          v-if="isEditParameter"
          :loading="tableLoading"
          type="primary"
          size="small"
          icon="el-icon-save"
          @click="saveParameter()"
          @keyup.prevent
          @keydown.enter.prevent
          >保存</el-button
        >
        <el-button
          v-if="!isEditParameter"
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="addProject"
          @keyup.prevent
          @keydown.enter.prevent
          >添加项目</el-button
        >
      </div>
    </div>
    <el-table
      id="sortableList"
      ref="tableRef"
      :key="tableKey"
      v-loading="tableLoading"
      row-key="itemConfigId"
      :data="tableData"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
      @sort-change="handleSortChange"
    >
      <el-table-column v-if="!isEditParameter" label="排序" :min-width="50">
        <i class="tes-move iconfont" style="font-size: 12px; cursor: move" />
      </el-table-column>
      <el-table-column
        label="检测项目编号"
        prop="inspectionProjectCode"
        :min-width="170"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div>{{ row.inspectionProjectCode }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="检测项目名称"
        prop="inspectionProjectName"
        :min-width="colWidth.name"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row, $index }">
          <div>{{ row.inspectionProjectName }}</div>
          <el-tag
            v-for="(item, i) in row.capabilityParaVos"
            :key="item.paraId + Math.random()"
            effect="dark"
            size="mini"
            class="parameter"
            :closable="isEditParameter"
            @close="handleDeleteParameter($index, i)"
            >{{ item.paraName }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        label="项目描述"
        prop="projectDescription"
        :min-width="colWidth.description"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.projectDescription || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" :min-width="colWidth.status">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="statusDicClass[row.status]"> {{ statusDic[row.status] }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        v-if="getPermissionBtn('deleteProject') && !isEditParameter"
        label="操作"
        :min-width="colWidth.operationSingle"
        prop="caozuo"
      >
        <template #default="{ row }">
          <span class="blue-color" @click="handleDelete(row)">删除</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getTableList" />
    <!-- 添加项目 -->
    <AddInspectionItems
      :show="showAdd"
      :data="alreadyList"
      type="strategyList"
      :material-select-code="currentData.code"
      :can-select-material="false"
      @close="closeDialog"
      @selectData="selectData"
    />
  </div>
</template>

<script>
import { reactive, ref, watch, onMounted, getCurrentInstance, toRefs, nextTick } from 'vue';
import {
  deleteTestItems,
  getMaterialList,
  getAllConfiguration,
  addTestItems,
  updateOrderItemApi,
  inspectionReload
} from '@/api/strategy';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import AddInspectionItems from '@/components/BusinessComponents/AddInspectionItems';
import { getCapabilityTree } from '@/api/user';
import { formatTree } from '@/utils/formatJson';
import { formatDate, formatDateTime } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import Pagination from '@/components/Pagination';
import { colWidth } from '@/data/tableStyle';
import { exportStandardExcel } from '@/api/testBase';
import { ElMessage } from 'element-plus';
import Sortable from 'sortablejs';

export default {
  name: 'Configuration',
  components: { Pagination, AddInspectionItems },
  props: {
    treeId: {
      type: String,
      default: ''
    },
    treeLength: {
      type: Number,
      default: 0
    },
    activeName: {
      type: String,
      default: ''
    },
    currentData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // const store = useStore()
    const state = reactive({
      activeName: '',
      isEditParameter: false,
      showAdd: false,
      treeData: [],
      statusDicClass: {
        1: 'success',
        0: 'info'
      },
      statusDic: {
        0: '已停用',
        1: '已启用'
      },
      isAdd: false,
      isAsc: '',
      orderBy: '',
      detailData: {}, // 传递给详情页的内容
      tableData: [],
      currentData: props.currentData,
      alreadyList: [], // 已经添加过的数据
      itemconfigcondition: '', // 模糊查询关键字
      total: 0,
      versionId: '', // 当前版本id
      tableLoading: false, // 表格加载的loading
      checkTreeId: '', // 选中的左侧树节点的id
      treeLength: 0, // 树节点的长度
      listQuery: {
        page: 1,
        limit: 20
      },
      downloadLoading: false
    });
    const tableKey = ref(0);
    watch(props, newValue => {
      state.treeLength = props.treeLength;
      state.currentData = props.currentData;
      state.checkTreeId = props.treeId;
      if (state.checkTreeId) {
        if (props.activeName === 'second') {
          tableKey.value = 0;
          getTableList();
          getTermTree();
        }
      } else {
        state.tableData = [];
        state.alreadyList = [];
      }
    });
    const reset = () => {
      state.itemconfigcondition = '';
      state.listQuery.page = 1;
      state.listQuery.limit = 20;
      getTableList();
    };
    // getTree()
    // 列表排序
    const handleSortChange = data => {
      const { prop, order } = data;
      state.orderBy = prop;
      if (order === 'ascending') {
        state.isAsc = 'true';
      } else if (order === 'descending') {
        state.isAsc = 'false';
      } else {
        state.isAsc = '';
      }
      getTableList();
    };

    // 编辑、查看详情
    const handleDetail = (row, isEdit) => {};
    // 删除
    const handleDelete = (row, type) => {
      proxy
        .$confirm('是否删除当前检验项目？', '确认删除', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          // 删除产品
          deleteTestItems({
            itemConfigId: row.itemConfigId,
            strategyId: state.checkTreeId,
            versionId: state.versionId
          }).then(res => {
            if (res) {
              getTableList();
              // proxy.$message.closeAll()
              proxy.$message.success('删除成功');
            }
          });
        })
        .catch(() => {});
    };

    // mounted
    onMounted(() => {});
    // 获取添加项目的信息
    const getTermTree = () => {
      getCapabilityTree(state.currentData.code).then(res => {
        if (res.data.code === 200) {
          const data = res.data.data;
          state.treeData = formatTree(data);
          state.treeData.unshift({
            id: '-1',
            parentId: '0',
            materialCategoryCode: data.length > 0 ? data[0].materialCategoryCode : '266013',
            name: '全部',
            order: 0,
            status: 2
          });
        }
      });
    };
    // 添加项目
    const addProject = () => {
      if (state.treeLength === 0) {
        proxy.$message.closeAll();
        proxy.$message.warning('请先在左侧添加类目');
      } else {
        state.showAdd = true;
      }
    };
    // 获取新增的项目
    const selectData = data => {
      state.showAdd = false;
      var addArray = [];
      data.forEach((item, index) => {
        item.capabilityParaEntityList = [];
        item.capabilityparaVoList.forEach(val => {
          item.capabilityParaEntityList.push({
            capabilityId: val.capabilityid,
            paraName: val.name,
            paraNumber: val.number,
            paraId: val.id
          });
        });
        addArray.push({
          inspectionProjectCode: item.number,
          inspectionProjectName: item.name,
          projectDescription: item.remark,
          inspectionProjectId: item.capabilityId,
          capabilityParaVos: item.capabilityParaEntityList,
          order: state.tableData.length + index
        });
      });
      var params = {
        strategyId: state.checkTreeId,
        versionId: state.versionId,
        items: addArray
      };
      if (data.length > 0) {
        state.tableLoading = true;
        addTestItems(params).then(res => {
          state.tableLoading = false;
          if (res) {
            proxy.$message.success('添加成功');
            getTableList();
          }
        });
      }
    };
    const closeDialog = () => {
      state.showAdd = false;
    };
    const getConfiguration = () => {
      getAllConfiguration({
        strategyId: state.checkTreeId,
        versionId: state.versionId
      }).then(res => {
        state.alreadyList = [];
        res.data.data.forEach(item => {
          state.alreadyList.push({
            number: item.inspectionProjectCode,
            name: item.inspectionProjectName,
            remark: item.projectDescription,
            id: item.inspectionProjectId
          });
        });
      });
    };
    // 刷新检验策略配置的检测项目
    const handleReload = () => {
      state.tableLoading = true;
      inspectionReload({ strategyId: state.checkTreeId, versionId: state.versionId }).then(res => {
        state.tableLoading = false;
        if (res) {
          getTableList();
        }
      });
    };
    // #region 导出检验项目

    const exportToExcel = () => {
      const excelParamPostBody = {
        strategyId: state.checkTreeId,
        versionId: state.versionId
      };
      if (!excelParamPostBody) {
        return;
      }
      state.downloadLoading = true;
      exportStandardExcel(excelParamPostBody)
        .then(res => {
          if (res.data.code === 200) {
            const result = res.data.data;
            if (result.length > 0) {
              const workSheets = [];
              import('@/utils/Export2Excel').then(excel => {
                if (result.length > 0) {
                  for (let i = 0; i < result.length; i++) {
                    result[i].ownerName = `${getNameByid(result[i].ownerId)}`;
                    result[i].voltageLevelName = `${result[i].voltName || ''}`;
                  }
                  const sampleHeader = [
                    '项目分类',
                    '项目Id',
                    '项目编号',
                    '项目名称',
                    '试验方法',
                    '关键参数Id',
                    '关键参数名称',
                    '关键参数编号',
                    '关键参数字段类型',
                    '关键参数单位',
                    '关键参数昵称',
                    '是否有标准要求',
                    '关键参数标准要求',
                    '约束小数位'
                  ];
                  const sampleFilterVal = [
                    'capabilityCategory',
                    'capabilityId',
                    'capabilityNumber',
                    'capabilityName',
                    'testMethod',
                    'paraId',
                    'paraName',
                    'paraNumber',
                    'paraResultType',
                    'paraUnitName',
                    'paraNickname',
                    'standardRequirement',
                    'requirement',
                    'decimalPlace'
                  ];
                  const sampleExcelData = formatExcelData(sampleFilterVal, result);
                  const sampleSheet = excel.getWorkSheet({
                    header: sampleHeader,
                    data: sampleExcelData,
                    wsName: `检验项目`
                  });
                  workSheets.push(sampleSheet);
                }
                excel.exportMultiSheetExcel(workSheets, `LIMS-检验项目_${formatDateTime()}`);
              });
            } else {
              ElMessage.warning({
                message: '暂无可导出的的数据!',
                type: 'warning'
              });
              return;
            }
          }
        })
        .catch(err => {
          ElMessage.error({
            message: `${err.message}`,
            type: 'error'
          });
        })
        .finally(() => {
          state.downloadLoading = false;
        });
    };

    function formatExcelData(filterVal, jsonData) {
      return jsonData.map(v =>
        filterVal.map(j => {
          if (j === 'standardRequirement' && !v[j]) {
            return '是';
          } else {
            return v[j];
          }
        })
      );
    }
    const getTableList = query => {
      if (state.treeLength === 0) {
        proxy.$message.warning('请先在左侧添加分类');
        return false;
      }
      const params = {
        itemConfigCondition: state.itemconfigcondition,
        strategyId: state.checkTreeId,
        isAsc: state.isAsc,
        orderBy: state.orderBy,
        tabIndex: '1'
      };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        params.page = state.listQuery.page.toString();
        params.limit = state.listQuery.limit.toString();
      }
      state.tableLoading = true;
      getMaterialList(params).then(res => {
        state.tableLoading = false;
        if (res) {
          state.tableData = res.data.data.itemConfigInfoList.list;
          state.tableData.forEach((item, index) => {
            item.order = index;
          });
          state.total = res.data.data.itemConfigInfoList.totalCount;
          if (state.tableData.length) {
            nextTick(() => {
              rowDrop();
            });
          }
          state.versionId = res.data.data.versionInfo.versionList[0].versionId;
          getConfiguration();
        }
      });
    };
    // 行拖拽
    const rowDrop = () => {
      // 获取当前表格
      const el = document.getElementById('sortableList').querySelector('.el-table__body-wrapper tbody');
      Sortable.create(el, {
        animation: 300,
        handle: '.tes-move',
        draggable: '.el-table__row',
        ghostClass: 'ghost',
        // 拖动对象移动样式
        dragClass: 'drag',
        forceFallback: true,
        onEnd({ newIndex, oldIndex }) {
          if (oldIndex !== newIndex) {
            const currRow = state.tableData.splice(oldIndex, 1)[0];
            state.tableData.splice(newIndex, 0, currRow);
            state.tableData.forEach((value, index) => {
              value.order = index;
            });
            tableKey.value += 1;
            updateOrder(state.tableData);
          }
        }
      });
    };
    const updateOrder = tableData => {
      state.listLoading = true;
      updateOrderItemApi({ orderVo: tableData }).then(res => {
        state.listLoading = false;
        if (res) {
          proxy.$message.success(res.data.message);
          getTableList();
        }
      });
    };
    // 保存参数编辑
    const saveParameter = () => {
      state.tableLoading = true;
      addTestItems({
        strategyId: state.checkTreeId,
        versionId: state.versionId,
        items: state.tableData
      }).then(res => {
        state.tableLoading = false;
        if (res) {
          proxy.$message.success(res.data.message);
          state.isEditParameter = false;
          getTableList();
        }
      });
    };
    // 取消参数编辑
    const cancelEdit = () => {
      state.isEditParameter = false;
      getTableList();
    };
    // 删除关键参数
    const handleDeleteParameter = (index, i) => {
      state.tableData[index].capabilityParaVos.splice(i, 1);
    };
    return {
      ...toRefs(state),
      closeDialog,
      handleReload,
      saveParameter,
      handleDeleteParameter,
      cancelEdit,
      rowDrop,
      getTableList,
      getTermTree,
      selectData,
      addProject,
      getConfiguration,
      getPermissionBtn,
      formatDate,
      getNameByid,
      drageHeader,
      handleDelete,
      handleSortChange,
      handleDetail,
      tableKey,
      reset,
      colWidth,
      exportToExcel
    };
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
.testMaterial {
  text-align: left;
}
.parameter {
  margin-right: 10px;
}
.el-icon--right {
  margin-left: 2px;
}
.blue-color:last-child {
  margin-right: 10px;
}

.flex-bc {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.searchInput {
  display: inline-block;
  .el-input {
    margin-right: 8px;
    width: 300px;
  }
}
.searchLeft {
  display: inline-block;
  .el-form-item {
    margin-bottom: 0;
  }
  :deep(.el-form-item--medium .el-form-item__label) {
    line-height: 32px;
  }
}
.searchRight {
  margin-right: 0;
}
</style>
