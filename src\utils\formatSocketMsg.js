// 对实时语音的消息进行过滤转换
export function formatterSocketMsg(msg, nodeIsNumber, oldValue) {
  const receiveMsg = JSON.parse(msg).text;
  if (nodeIsNumber) {
    const filterMsg = numberDigit(filterNumber(receiveMsg)) || Number(receiveMsg);
    return filterMsg || oldValue;
  } else {
    // 不是数字框，直接返回
    return receiveMsg;
  }
}

var map = {
  点: '.',
  负: '-',

  零: 0,
  幺: 1,
  吆: 1,
  一: 1,
  壹: 1,

  二: 2,
  贰: 2,
  两: 2,

  三: 3,
  叁: 3,

  四: 4,
  肆: 4,

  五: 5,
  伍: 5,

  六: 6,
  陆: 6,

  七: 7,
  柒: 7,

  八: 8,
  捌: 8,

  九: 9,
  玖: 9,

  十: 10,
  拾: 10,

  百: 100,
  佰: 100,

  千: 1000,
  仟: 1000,
  万: 10000,
  十万: 100000,
  百万: 1000000,
  千万: 10000000,
  亿: 100000000
};
const numberMap = {
  零: 0,
  一: 1,
  壹: 1,
  幺: 1,
  吆: 1,

  二: 2,
  贰: 2,
  两: 2,

  三: 3,
  叁: 3,

  四: 4,
  肆: 4,

  五: 5,
  伍: 5,

  六: 6,
  陆: 6,

  七: 7,
  柒: 7,

  八: 8,
  捌: 8,

  九: 9,
  玖: 9
};
function filterNumber(msg) {
  let newMsg = '';
  for (let i = 0; i < msg.length; i++) {
    if (map[msg[i]] || map[msg[i]] === 0) {
      newMsg += msg[i];
    }
  }
  return newMsg;
}
// 解析失败返回空，成功返回转换后的数字，不支持负数
export function numberDigit(message) {
  const fuIndex = message.indexOf('负');
  const dointIndex = message.indexOf('点');
  const isFu = fuIndex === 0; // 是负数
  const isDoint = dointIndex !== -1; // 是小数
  let zs = ''; // 整数部分
  let xs = ''; // 小数部分
  let filterMsg = '';
  if (isFu && isDoint) {
    // 是负数是小数
    zs = message.slice(1, dointIndex);
    xs = message.slice(dointIndex + 1, message.length);
    filterMsg = transformNumber(true, zs, xs);
  } else if (isFu && !isDoint) {
    // 是负数不是小数
    zs = message.slice(1, message.length);
    filterMsg = transformNumber(true, zs);
  } else if (!isFu && isDoint) {
    // 不是负数是小数
    zs = message.slice(0, dointIndex);
    xs = message.slice(dointIndex + 1, message.length);
    filterMsg = transformNumber(false, zs, xs);
  } else if (!isFu && !isDoint) {
    // 不是负数也不是小数
    zs = message;
    filterMsg = transformNumber(false, zs, xs);
  }
  return filterMsg;
}
// num1 小数点之前的数字；num2小数点之后的数字 isFs:true 负数， false 正数
export function transformNumber(isFs, num1, num2) {
  let filterNum1 = '';
  let filterNum2 = '';
  if (num1.match(/十/g) || num1.match(/百/g) || num1.match(/千/g) || num1.match(/万/g) || num1.match(/亿/g)) {
    filterNum1 = quantityUnit(num1);
  } else {
    filterNum1 = figureNumber(num1);
  }
  const num2New = figureNumber(num2);
  filterNum2 = num2New ? `.${num2New}` : '';
  if (isFs) {
    return `-${filterNum1}${filterNum2}`;
  } else {
    return `${filterNum1}${filterNum2}`;
  }
}

// 正整数含个十百千转阿拉伯数字
export function quantityUnit(chinese_number) {
  var rest;
  var len = chinese_number.length;
  if (len === 0) return -1;
  if (len === 1) return map[chinese_number] <= 10 ? map[chinese_number] : -1;
  var summary = 0;
  if (map[chinese_number[0]] === 10) {
    chinese_number = '一' + chinese_number;
    len++;
  }
  if (len >= 3 && map[chinese_number[len - 1]] < 10) {
    var last_second_num = map[chinese_number[len - 2]];
    if (
      last_second_num === 100 ||
      last_second_num === 1000 ||
      last_second_num === 10000 ||
      last_second_num === 100000000
    ) {
      for (var key in map) {
        if (map[key] === last_second_num / 10) {
          chinese_number += key;
          len += key.length;
          break;
        }
      }
    }
  }
  if (chinese_number.match(/亿/g) && chinese_number.match(/亿/g).length > 1) return -1;
  var splited = chinese_number.split('亿');
  if (splited.length === 2) {
    rest = splited[1] === '' ? 0 : numberDigit(splited[1]);
    return summary + numberDigit(splited[0]) * 100000000 + rest;
  }
  splited = chinese_number.split('万');
  if (splited.length === 2) {
    rest = splited[1] === '' ? 0 : numberDigit(splited[1]);
    return summary + numberDigit(splited[0]) * 10000 + rest;
  }
  var i = 0;
  while (i < len) {
    var first_char_num = map[chinese_number[i]];
    var second_char_num = map[chinese_number[i + 1]];
    if (second_char_num > 9) {
      summary += first_char_num * second_char_num;
    }
    i++;
    if (i === len) {
      summary += first_char_num <= 9 ? first_char_num : 0;
    }
  }
  return summary;
}
// 不含个十百千万的直接转阿拉伯数字的
export function figureNumber(number) {
  let newNumber = '';
  for (const i in number) {
    if (numberMap[number[i]] || numberMap[number[i]] === 0) {
      newNumber += numberMap[number[i]];
    }
  }
  return newNumber;
}
