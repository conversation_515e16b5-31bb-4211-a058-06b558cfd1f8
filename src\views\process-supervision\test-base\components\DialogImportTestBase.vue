<template>
  <!-- 标准导入 dialog -->
  <el-dialog
    v-model="dialogImport"
    title="数据导入"
    top="5vh"
    :close-on-click-modal="false"
    :show-close="!dialogLoading"
    width="1080px"
    @close="handleClose()"
  >
    <div
      v-if="dialogImport"
      v-loading="dialogLoading"
      class="flex gap-4"
      element-loading-text="数据检验导入中，请稍后..."
      element-loading-background="rgba(255, 255, 255, 0.2)"
    >
      <div class="tree-container">
        <div class="tree-header">
          <div class="header-select">
            <span class="icon el-icon-menu" />
            <el-select
              v-model="materialCode"
              filterable
              size="small"
              class="topSelect"
              placeholder="请选择物资分类"
              @change="onSelectMaterial"
            >
              <el-option v-for="val in materialList" :key="val.value" :label="val.name" :value="val.code" />
            </el-select>
          </div>
          <div class="header-input-button">
            <el-input
              v-model="projectName"
              size="small"
              placeholder="请输入类目名称"
              prefix-icon="el-icon-search"
              clearable
            />
          </div>
        </div>
        <div class="tree-content">
          <el-tree
            ref="treeRef"
            v-loading="treeLoading"
            show-checkbox
            default-expand-all
            :data="treeData"
            node-key="id"
            :props="defaultProps"
            :expand-on-click-node="false"
            highlight-current
            :filter-node-method="filterNode"
            :current-node-key="currentNodeKey"
            class="leftTree"
            @filter="filterNode"
            @node-click="onClickTreeNode"
          >
            <template #default="{ node }">
              <span>{{ node.label }}</span>
            </template>
          </el-tree>
        </div>
      </div>
      <div class="flex-1">
        <div class="title">为确保数据导入的准确性，请按照以下规则进行导入</div>
        <ul class="uploadRules">
          <li>请按照<span class="blue-color" @click="downLoadFile">标准导入模板.xlsx</span>在模板内录入数据</li>
          <li>只导入第一张工作表（sheet1）；</li>
          <li>请上传*.xls，*.xlsx格式文件；</li>
          <li>目前一次性最多上传5000条数据；</li>
          <li>文件大小不超过10M；</li>
        </ul>
        <div class="uploadArea">
          <el-upload
            ref="uploadRef"
            :action="uploadAction"
            :headers="headerconfig"
            :data="uploadParams"
            :auto-upload="false"
            :limit="1"
            :accept="fileAcceptExcel"
            :on-exceed="handleExceed"
            :before-upload="beforeUpload"
            :on-success="handleFileSuccess"
            :on-change="onChangeFile"
          >
            <el-button size="small" type="primary" plain>选择上传文件</el-button>
          </el-upload>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button :disabled="dialogLoading" @click="handleClose()">取 消</el-button>
        <el-button type="primary" :disabled="dialogLoading" @click="handleSubmit()">确认上传</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, onMounted, ref } from 'vue';
import { getToken } from '@/utils/auth';
import { testBaseStandardcategoryImport } from '@/api/uploadAction';
import { ElMessage } from 'element-plus';
import { standardcategoryExport } from '@/api/testBase';
import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import { useRoute } from 'vue-router';
import { getCapabilityTree } from '@/api/user';
import { useStore } from 'vuex';
import { formatTree } from '@/utils/formatJson';
import { fileAcceptExcel } from '@/utils/fileAccept';

export default {
  name: 'DialogImportTestBase',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detailData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    specifyList: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['close'],
  setup(props, ctx) {
    const route = useRoute();
    const store = useStore();
    const state = reactive({
      dialogImport: false,
      uploadRef: ref(),
      uploadParams: {
        materialCategoryCode: '', // 物资分类编码
        standardCategoryId: '', // 左侧分类ID
        capabilityCategoryIdList: []
      },
      headerconfig: {
        Authorization: getToken()
      },
      specifyList: [],
      uploadAction: testBaseStandardcategoryImport(), // 附件上传地址
      title: '导入标准',
      treeRef: ref(),
      dialogLoading: false,
      treeData: [],
      materialList: store.state.user.materialList,
      materialCode: store.state.user.materialList[0]?.code,
      projectName: '',
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      currentNodeKey: '',
      currentSelectMaterial: store.state.user.materialList[0],
      treeLoading: false,
      fileName: ''
    });

    const downLoadFile = async () => {
      if (state.specifyList.length) {
        handleDownTemplate();
      } else {
        standardcategoryExport(state.uploadParams).then(res => {
          state.tableLoading = false;
          const blob = new Blob([res.data], { type: '' });
          const blobUrl = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.download = `${route.query.productName}.xlsx`;
          a.href = blobUrl;
          a.click();
          ElMessage.success('下载附件成功');
        });
      }
    };
    const handleDownTemplate = async () => {
      // 特征值
      const characteristic = state.specifyList;
      // 创建工作簿
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Sheet1');

      // 计算关键列位置
      const charaStartCol = 2; // B列开始
      const charaEndCol = charaStartCol + characteristic.length - 1;
      const exampleCol = charaEndCol + 1;

      const totalColumns = 1 + characteristic.length; // A + 特征列 + 关键参数列 + 示例列

      // 设置所有列宽为25
      const columns = Array(totalColumns).fill({ width: 25 });
      worksheet.columns = columns;
      // 第一行
      const row1 = worksheet.getRow(1);
      row1.getCell(1).value = '检测标准'; // A1

      // 合并关键参数特征值单元格
      if (characteristic.length > 1) {
        worksheet.mergeCells(1, charaStartCol, 1, charaEndCol);
      }
      row1.getCell(charaStartCol).value = '关键参数的特征值(需与物资分类中的规格保持一致)';

      // XX项目下关键参数
      row1.getCell(exampleCol).value = 'XX项目下关键参数';

      // 示例说明
      row1.getCell(exampleCol + 1).value = '示例说明';

      // 第二行
      const row2 = worksheet.getRow(2);
      row2.getCell(exampleCol).value = '';
      row2.getCell(exampleCol + 1).value = '该行输入检测项目,需与系统中保持一致';

      // 第三行
      const row3 = worksheet.getRow(3);
      row3.getCell(1).value = '检测依据'; // A3

      // 添加特征值名称
      characteristic.forEach((item, index) => {
        row3.getCell(charaStartCol + index).value = item.specificationName;
      });

      // 添加关键参数名称
      row3.getCell(exampleCol).value = '';

      row3.getCell(exampleCol + 1).value = '该行输入关键参数，需与系统中保持一致';

      // 第四行
      const row4 = worksheet.getRow(4);
      row4.getCell(exampleCol + 1).value = '该行输入判定方式，参考批注说明';
      // 第五行
      const row5 = worksheet.getRow(5);
      // 测试示例5
      row5.getCell(exampleCol + 1).value = '该行输入关键参数的单位，默认从系统中导出';

      // 第六行
      const row6 = worksheet.getRow(6);
      row6.getCell(exampleCol + 1).value = '该行输入一些特殊说明的备注，仅做导入时备注说明，不导入系统';

      const boldBorder = {
        top: { style: 'thin' }, // 上边框加粗
        left: { style: 'thin' }, // 左边框加粗
        bottom: { style: 'thin' }, // 下边框加粗
        right: { style: 'thin' } // 右边框加粗
      };
      // 设置整个工作表内容水平居中
      worksheet.eachRow(row => {
        row.border = boldBorder;
        row.alignment = { horizontal: 'center', wrapText: true, vertical: 'middle' };
      });
      // 第一行斜体
      worksheet.getRow(1).font = {
        italic: true,
        color: { argb: 'ffff0000' },
        bold: true // 可选：加粗
      };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFCCCCCC' } // 灰色
      };
      worksheet.getRow(2).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFCCCCCC' } // 灰色
      };
      // 最后一列斜体
      const lastColumn = worksheet.columnCount;
      // 最后一列宽度45
      const lastColumnStyle = {
        alignment: {
          wrapText: true,
          vertical: 'middle',
          horizontal: 'left'
        },
        font: {
          italic: true
        },
        fill: {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'ffff00' } // 黄色
        },
        border: boldBorder
      };
      worksheet.getColumn(lastColumn).width = 45;
      worksheet.getColumn(lastColumn - 1).width = 35;
      row1.getCell(exampleCol + 1).style = lastColumnStyle;
      row2.getCell(exampleCol + 1).style = {
        ...lastColumnStyle,
        font: { italic: true, color: { argb: 'ffff0000' }, bold: true }
      };
      row3.getCell(exampleCol + 1).style = lastColumnStyle;
      row4.getCell(exampleCol + 1).style = lastColumnStyle;
      row5.getCell(exampleCol + 1).style = lastColumnStyle;
      row6.getCell(exampleCol + 1).style = lastColumnStyle;

      // 生成Excel文件
      const buffer = await workbook.xlsx.writeBuffer();
      saveAs(
        new Blob([buffer], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }),
        `检测标准导入模板.xlsx`
      );
    };
    const handleSubmit = () => {
      const selectedProjectIds = state.treeRef.getCheckedKeys(false);
      if (!Array.isArray(selectedProjectIds) || selectedProjectIds.length === 0) {
        ElMessage.warning('检测项目不能为空');
        return;
      }
      if (!state.fileName) {
        ElMessage.warning('上传文件不能为空');
        return;
      }
      state.fileName = '';
      state.uploadParams.capabilityCategoryIdList = selectedProjectIds;
      state.dialogLoading = true;
      state.uploadRef.submit();
    };

    // #endregion

    watch(
      () => props.visible,
      newValue => {
        if (props.visible) {
          state.dialogImport = true;
          state.specifyList = props.specifyList;
          state.uploadParams = props.detailData;
        }
      },
      { deep: true }
    );

    watch(
      () => state.projectName,
      newValue => {
        state.treeRef.filter(newValue);
      }
    );
    const handleClose = () => {
      state.dialogImport = false;
      state.projectName = '';
      state.fileName = '';
      ctx.emit('close', false);
    };
    // 上传文件的限制
    const beforeUpload = file => {
      var fileName = '';
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        ElMessage.error('上传附件大小不能超过10M');
        return false;
      } else if (fileName !== 'xls' && fileName !== 'xlsx') {
        ElMessage.error('仅支持.xls，.xlsx文件扩展名');
        return false;
      } else if (file.size === 0) {
        ElMessage.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };
    const handleExceed = files => {
      state.uploadRef.clearFiles(['success', 'fail', 'ready']);
      state.uploadRef.handleStart(files[0]);
    };
    // 上传成功的钩子
    const handleFileSuccess = (res, file) => {
      state.dialogLoading = false;
      if (res.code === 200) {
        ElMessage.success(res.message);
      } else {
        ElMessage.error(res.message);
      }
      state.dialogImport = false;
      state.projectName = '';
      state.fileName = '';
      ctx.emit('close', true);
    };

    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };

    const onClickTreeNode = (data, node) => {
      //
    };

    const handleQueryCapabilityTree = () => {
      state.treeLoading = true;
      getCapabilityTree(state.currentSelectMaterial?.code)
        .then(response1 => {
          const data = response1.data.data;
          state.treeData = formatTree(data);
        })
        .finally(() => (state.treeLoading = false));
    };

    // 选择物资-更多里面的物资
    const onSelectMaterial = async val => {
      state.currentSelectMaterial = store.state.user.materialList.filter(item => item.code === val)[0];
      state.currentNodeKey = '';
      handleQueryCapabilityTree();
    };

    const onChangeFile = file => {
      var fileName = '';
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        ElMessage.error('上传附件大小不能超过10M');
      } else if (fileName !== 'xls' && fileName !== 'xlsx') {
        ElMessage.error('仅支持.xls，.xlsx文件扩展名');
      } else if (file.size === 0) {
        ElMessage.error('上传附件大小不能为空');
      } else {
        state.fileName = file.name;
      }
    };
    onMounted(() => {
      handleQueryCapabilityTree();
    });

    return {
      ...toRefs(state),
      downLoadFile,
      handleFileSuccess,
      handleExceed,
      beforeUpload,
      handleClose,
      handleSubmit,
      filterNode,
      onClickTreeNode,
      onSelectMaterial,
      onChangeFile,
      fileAcceptExcel
    };
  }
};
</script>

<style scoped lang="scss">
@import '@/styles/tree.scss';
.tree-container {
  height: 65vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .tree-header {
    flex-direction: column;
    .header-select {
      display: flex;
      width: 100%;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 10px;
      :deep(.el-select .el-input__inner) {
        padding-left: 0;
        font-size: 16px;
        color: #303133;
        border: none;
      }
      .icon {
        font-size: 16px;
        margin-right: 10px;
      }
      .el-select {
        width: 100%;
      }
    }
    .header-input-button {
      display: flex;
      width: 100%;
      justify-content: flex-start;
      align-items: center;
    }
  }
  .tree-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}
.uploadRules {
  padding: 5px 10px;
  background: #f0f2f5;
  margin: 14px 0;
  li {
    list-style: none;
    line-height: 22px;
    font-size: 13px;
  }
  .blue-color {
    margin-right: 0;
  }
}
</style>
