<template>
  <!-- 辅料台账列表 -->
  <ListLayout
    :has-left-panel="true"
    :has-quick-query="false"
    :aside-panel-width="asidePanelWidth"
    :aside-max-width="520"
  >
    <template #search-bar>
      <div class="searchInput">
        <el-input
          v-model="searchForm.condition"
          v-trim
          v-focus
          class="ipt-360"
          placeholder="请输入编号/名称/型号规格"
          clearable
          size="large"
          @keyup.enter="getTableList"
        >
          <template #prefix>
            <i class="el-input__icon el-icon-search" />
          </template>
        </el-input>
        <el-button type="primary" size="large" @click="getTableList">查询</el-button>
        <el-button size="large" @click="reset">重置</el-button>
      </div>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('addMaterialsStanding')"
        class="fr"
        type="primary"
        size="large"
        icon="el-icon-plus"
        @click="handleStanding('add')"
        @keyup.prevent
        @keydown.enter.prevent
        >新增辅料</el-button
      >
    </template>
    <template #page-left-side>
      <div class="tree-container">
        <div class="tree-header">
          <el-input
            v-model="otherForm.filterText"
            size="small"
            placeholder="请输入分类名称"
            prefix-icon="el-icon-search"
          />
          <el-button
            v-if="getPermissionBtn('addMaterialsClassify')"
            size="small"
            icon="el-icon-plus"
            class="addTreeBtn"
            @click="addTreeItem"
            @keyup.prevent
            @keydown.enter.prevent
          />
        </div>
        <div class="tree-content">
          <el-tree
            ref="refTree"
            :data="otherForm.treeData"
            node-key="id"
            :props="otherForm.defaultProps"
            :expand-on-click-node="false"
            :highlight-current="true"
            :filter-node-method="filterNode"
            class="leftTree"
            draggable
            :allow-drop="allowDrop"
            @node-drop="nodeDrop"
            @filter="filterNode"
            @node-click="clickNode"
          >
            <template #default="{ node, data }">
              <span class="tree-node">{{ node.label }}</span>
              <el-dropdown
                v-if="
                  (getPermissionBtn('editMaterialsClassify') || getPermissionBtn('deleteMaterialsClassify')) &&
                  data.id !== 'all'
                "
                trigger="hover"
                :class="node.showIcon ? 'icon-show' : ''"
                class="tree-dropdown el-icon"
                @visible-change="changeIcon(node.showIcon, node)"
              >
                <i class="el-icon-more" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-if="getPermissionBtn('editMaterialsClassify')"
                      @click="editTree(node.data, node)"
                      ><i class="iconfont tes-edit" />编辑</el-dropdown-item
                    >
                    <el-dropdown-item
                      v-if="getPermissionBtn('deleteMaterialsClassify')"
                      class="color-red"
                      @click="delTree(node.data)"
                      ><i class="iconfont tes-delete" />删除</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-tree>
        </div>
      </div>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      height="auto"
      :size="otherForm.tableSize"
      highlight-current-row
      class="dark-table format-height-table base-table no-quick-query"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column label="辅料编号" prop="no" :min-width="colWidth.orderNo" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <div v-copy="row.no" class="blue-color" @click="handleStanding('check', row)">{{ row.no || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="辅料名称" prop="name" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.name || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="型号规格" prop="model" :width="colWidth.model" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.model || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="辅料描述" prop="remark" :min-width="colWidth.remark" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.remark || '--' }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="库存数量"
        prop="num"
        :width="colWidth.name"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span v-if="row.num" class="nowrap">{{ row.num + unitJson[row.unit] }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column> -->
      <el-table-column
        v-if="
          getPermissionBtn('editMaterialsStanding') ||
          getPermissionBtn('deleteMaterialsStanding') ||
          getPermissionBtn('inStorage') ||
          getPermissionBtn('outStorage')
        "
        label="操作"
        :width="colWidth.operation"
        class-name="fixed-right"
        fixed="right"
        prop="caozuo"
      >
        <template #default="{ row }">
          <span
            v-if="getPermissionBtn('editMaterialsStanding')"
            class="blue-color"
            @click="handleStanding('edit', row)"
          >
            编辑</span
          >
          <el-dropdown
            v-if="
              getPermissionBtn('deleteMaterialsStanding') ||
              getPermissionBtn('inStorage') ||
              getPermissionBtn('outStorage')
            "
            trigger="hover"
          >
            <span class="blue-color" style="margin-left: 15px"><span class="el-icon-more" /></span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-if="getPermissionBtn('inStorage')" class="blue-color" @click="handleBePut(row)"
                  >入库</el-dropdown-item
                >
                <el-dropdown-item v-if="getPermissionBtn('outStorage')" class="blue-color" @click="handleDelivery(row)"
                  >出库</el-dropdown-item
                >
                <el-dropdown-item
                  v-if="getPermissionBtn('deleteMaterialsStanding')"
                  class="blue-color"
                  @click="handleDeleteStanding(row)"
                  >删除</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getTableList" />
    <DrawerStandingBook
      :drawer="unitVisiable"
      :unit-list="unitList"
      :unit-json="unitJson"
      :detail-data="detailData"
      :tree-data="otherForm.dialogTreeData"
      :type="drawerType"
      @close="closeDrawer"
    />
    <el-dialog
      v-model="showEditDialog"
      :title="otherForm.isAddTree === true ? '添加分类' : '编辑分类'"
      width="480px"
      :close-on-click-modal="false"
    >
      <el-form
        v-if="showEditDialog"
        ref="formTree"
        :model="dialogFrom"
        :rules="otherForm.dialogRules"
        label-position="right"
        label-width="120px"
      >
        <el-form-item
          label="分类名称："
          prop="name"
          :rules="{
            required: true,
            message: '请输入分类名称',
            trigger: 'change'
          }"
        >
          <el-input v-model.trim="dialogFrom.name" autocomplete="off" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="父级分类：">
          <el-cascader
            v-model="dialogFrom.parentId"
            :options="otherForm.dialogTreeData"
            :props="otherForm.categoryProps"
            clearable
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false">取 消</el-button>
          <el-button v-loading.fullscreen.lock="dialogLoading" type="primary" @click="handleEditTree">确 认</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 入库登记 -->
    <DialogBeput
      :detail-data="rowDetail"
      :unit-json="unitJson"
      :dialog-visible="dialogBePut"
      @closeDialog="closeDialogBeput"
    />
    <!-- 出库登记 -->
    <DialogDelivery
      :detail-data="rowDetail"
      :unit-json="unitJson"
      :dialog-visible="dialogDelivery"
      @closeDialog="closeDialog2"
    />
  </ListLayout>
</template>

<script>
import { reactive, ref, watch, onMounted, getCurrentInstance, toRefs } from 'vue';
import Pagination from '@/components/Pagination';
import { formatTree, formatAllTree, formatTreeByIds, formatTreeByNames } from '@/utils/formatJson';
import DrawerStandingBook from './DrawerStandingBook';
import DialogBeput from './DialogBeput';
import DialogDelivery from './DialogDelivery';
import {
  getList,
  getTree,
  deleteTreeNode,
  addUpdateTreeNode,
  deleteMaterialApi,
  updateOrderTree
} from '@/api/standingBook';
import { getDictionary } from '@/api/user';
import { colWidth } from '@/data/tableStyle';
import ListLayout from '@/components/ListLayout';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import router from '@/router/index.js';
import _ from 'lodash';

export default {
  name: 'StandingBook',
  components: { ListLayout, Pagination, DrawerStandingBook, DialogBeput, DialogDelivery },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // const store = useStore()
    // const route = useRoute()
    const state = reactive({
      asidePanelWidth: 300,
      drawerType: '', // 抽屉类型
      dialogBePut: false,
      dialogDelivery: false,
      rowDetail: {},
      showPanel: false,
      activePanelName: '0',
      rowId: '', // 行数据id
      unitVisiable: false,
      dialogLoading: false,
      isAdd: false,
      searchForm: {},
      detailData: {}, // 传递给详情页的内容
      tableData: [],
      total: 0,
      tableLoading: false, // 表格加载的loading
      detailDrawer: false,
      isEdit: true, // 详情页的类型
      dialogFrom: {}, // 操作树节点的弹窗表格
      treeTitle: '', // 选中树节点的name
      checkTreeId: '', // 选中的左侧树节点的id
      unitList: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ],
      unitJson: {},
      listQuery: {
        page: 1,
        limit: 20
      }
    });
    const editFrom = ref(null);
    const activeName = ref('0');
    const showS = ref(true);

    const otherForm = reactive({
      tableList: [],
      list: [],
      treeData: [],
      dialogTreeData: [],
      editData: {},
      newTree: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      category: [],
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      tabsMoreData: [],
      filterText: '',
      isAddTree: true,
      dialogRules: {},
      tableSize: 'medium',
      showIcon: false
    });

    const tableKey = ref(0);
    const reset = () => {
      state.searchForm = {};
      state.listQuery.page = 1;
      state.listQuery.limit = 20;
      proxy.getTableList();
    };
    // 树节点编辑
    const showEditDialog = ref(false);
    const editTree = (data, node) => {
      otherForm.dialogTreeData = formatAllTree(data.id, otherForm.dialogTreeData);
      showEditDialog.value = true;
      otherForm.isAddTree = false;
      state.dialogFrom = JSON.parse(JSON.stringify(data));
      otherForm.category = formatTreeByIds(node.parent);
    };
    const formTree = ref(null);
    // 新增、编辑时保存树节点
    const handleEditTree = () => {
      formTree.value.validate(valid => {
        if (valid) {
          var parentId = state.dialogFrom.parentId;
          if (state.dialogFrom.parentId instanceof Array) {
            if (state.dialogFrom.parentId.length > 0) {
              parentId = state.dialogFrom.parentId[state.dialogFrom.parentId.length - 1].toString();
            } else {
              parentId = '';
            }
          }
          const params = {
            ...state.dialogFrom,
            parentId: parentId
          };
          state.dialogLoading = true;
          addUpdateTreeNode(params).then(function (res) {
            state.dialogLoading = false;
            if (res) {
              showEditDialog.value = false;
              proxy.$message.success(res.data.message);
              proxy.getLeftTree();
            }
          });
        } else {
          return false;
        }
      });
    };
    // 获取库存单位
    const getDictionaryList = () => {
      state.unitJson = {};
      getDictionary('5').then(res => {
        res.data.data.dictionaryoption.forEach(item => {
          state.unitJson[item.code] = item.name;
          if (item.status === 1) {
            state.unitList[0].group.push(item);
          } else {
            state.unitList[1].group.push(item);
          }
        });
      });
    };
    getDictionaryList();
    // 新增树节点
    const addTreeItem = () => {
      otherForm.dialogTreeData = formatAllTree('', otherForm.dialogTreeData);
      showEditDialog.value = true;
      otherForm.category = [];
      otherForm.isAddTree = true;
      state.dialogFrom = {};
    };
    // 树节点删除
    const delTree = node => {
      proxy
        .$confirm('是否删除该类目', '提示', {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'warning'
        })
        .then(() => {
          deleteTreeNode(node.id).then(function (res) {
            if (res) {
              proxy.$message.success('删除成功!');
              if (node.id === state.checkTreeId) {
                state.checkTreeId = '';
              }
              proxy.getLeftTree();
            } else {
              proxy.$message.error(res.data.data.message);
            }
          });
        })
        .catch(() => {});
    };
    const handleBePut = row => {
      state.dialogBePut = true;
      state.rowDetail = row;
    };
    // 出库
    const handleDelivery = row => {
      state.dialogDelivery = true;
      state.rowDetail = row;
    };
    const closeDialogBeput = val => {
      state.dialogBePut = false;
      if (val.isRefresh) {
        proxy.getTableList();
      }
    };
    // 列表排序
    const sortChange = column => {
      state.searchForm.orderBy = column.prop;
      state.searchForm.isAsc = !state.searchForm.isAsc;
      proxy.getTableList();
    };
    const closeDialog2 = val => {
      state.dialogDelivery = false;
      if (val.isRefresh) {
        proxy.getTableList();
      }
    };
    const handleStanding = (type, row) => {
      if (otherForm.treeData.length === 0) {
        proxy.$message.closeAll();
        proxy.$message.warning('请先在左侧添加分类');
        return false;
      }
      if (type === 'add') {
        state.detailData = {
          categoryId: state.checkTreeId // 当前节点的标准分类
        };
      } else {
        state.detailData = row;
      }
      state.drawerType = type;
      state.unitVisiable = true;
    };
    // 删除辅料
    const handleDeleteStanding = row => {
      proxy
        .$confirm('是否删除该辅料？', '提示', {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          // 删除产品
          state.tableLoading = true;
          deleteMaterialApi(row.id).then(res => {
            state.tableLoading = false;
            if (res) {
              proxy.$message.success(res.data.message);
              proxy.getTableList();
            }
          });
        })
        .catch(() => {});
    };
    // 编辑、查看详情
    const handleDetail = (row, isEdit) => {
      // 带给详情页的产品信息
      state.rowId = row.id;
      state.detailData = {
        productDetail: row, // 产品详情
        checkTreeId: state.checkTreeId, // 当前节点的标准分类
        standardTree: otherForm.treeData // 所有标准分类树节点
      };
      state.detailDrawer = true;
      state.isEdit = isEdit;
    };
    const closeDeatilDrawer = val => {
      state.detailDrawer = false;
      // 由于设备使用记录跳转到详情页面，要是关闭页面会重新刷新列表，因此会再次打开详情页面，这边添加传值来做判断，同时在关闭详情页面的时候为了防止刷新，这边路由会替换到原先模块的路由
      proxy.getTableList(null, false);
      router.replace({ query: {} });
    };
    // mounted
    onMounted(() => {});
    // 拖拽
    // 过滤树节点
    const refTree = ref(null);
    watch(
      () => otherForm.filterText,
      newValue => {
        refTree.value.filter(newValue);
      }
    );
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    // 选择物资
    const changeIcon = (command, node) => {
      if (!command) {
        node.showIcon = true;
      } else {
        node.showIcon = !node.showIcon;
      }
    };
    const closeDrawer = value => {
      if (value.isRefresh) {
        proxy.getTableList();
      }
      state.unitVisiable = false;
    };
    // 允许拖拽功能 只能同级拖拽
    const allowDrop = (draggingNode, dropNode, type) => {
      if (draggingNode.level === dropNode.level) {
        // parentid是父节点id
        if (draggingNode.data.parentId === dropNode.data.parentId) {
          return type === 'prev' || type === 'next';
        }
      } else {
        // 不同级进行处理
        return false;
      }
    };
    // 树排序
    const nodeDrop = (before, after) => {
      // console.log(after)
      var orderList = [];
      if (after.parent.level === 0) {
        orderList = JSON.parse(JSON.stringify(after.parent.data));
        _.remove(orderList, function (n) {
          return n.id === 'all';
        });
        orderList.forEach((ol, index) => {
          ol.order = index + 1;
        });
      } else {
        orderList = after.parent.data.children;
        orderList.forEach((ol2, index) => {
          ol2.order = index + 1;
        });
      }
      updateOrderTree(orderList).then(res => {
        if (res !== false) {
          proxy.$message.success('排序成功');
        }
      });
    };
    return {
      ...toRefs(state),
      allowDrop,
      sortChange,
      closeDialogBeput,
      getDictionaryList,
      closeDialog2,
      handleBePut,
      handleDelivery,
      handleDeleteStanding,
      nodeDrop,
      handleStanding,
      closeDrawer,
      getPermissionBtn,
      formatDate,
      changeIcon,
      getNameByid,
      drageHeader,
      formTree,
      addTreeItem,
      refTree,
      filterNode,
      handleEditTree,
      showEditDialog,
      delTree,
      editTree,
      closeDeatilDrawer,
      handleDetail,
      tableKey,
      showS,
      editFrom,
      otherForm,
      activeName,
      reset,
      colWidth
    };
  },
  created() {
    this.getLeftTree();
  },
  methods: {
    // 获取左侧列表树接口
    getLeftTree() {
      const vm = this;
      vm.tableLoading = true;
      getTree().then(res => {
        vm.tableLoading = false;
        if (res) {
          const data = res.data.data;
          if (data.length > 0) {
            vm.otherForm.treeData = JSON.parse(JSON.stringify(formatTree(data)));
            const all = { id: 'all', name: '全部' };
            vm.otherForm.treeData.unshift(all);
          } else {
            vm.otherForm.treeData = formatTree(data);
          }
          // vm.otherForm.treeData = formatTree(data)
          vm.otherForm.dialogTreeData = data;
          vm.$nextTick(() => {
            // 默认高亮选中节点
            if (!vm.checkTreeId) {
              // 判断第一次加载时默认选中第一个
              if (data.length > 0) {
                vm.$refs.refTree.setCurrentKey(vm.otherForm.treeData[0].id, true);
                vm.checkTreeId = vm.otherForm.treeData[0].id;
                vm.treeTitle = vm.otherForm.treeData[0].name;
                vm.getTableList();
              } else {
                vm.treeTitle = '';
                vm.tableData = [];
              }
            } else {
              vm.$refs.refTree.setCurrentKey(vm.checkTreeId, true);
            }
          });
        }
      });
    },
    // 获取当前节点的产品列表
    getTableList(query, flag) {
      const vm = this;
      if (vm.otherForm.treeData.length === 0 && !vm.$route.query.checkTreeId) {
        vm.$message.closeAll();
        vm.$message.warning('请先在左侧添加分类');
        return false;
      }
      const params = {
        ...vm.searchForm,
        categoryId: vm.checkTreeId === 'all' ? '' : vm.checkTreeId
      };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
      } else {
        params.page = vm.listQuery.page.toString();
        params.limit = vm.listQuery.limit.toString();
      }
      vm.tableLoading = true;
      getList(params).then(res => {
        vm.tableLoading = false;
        if (res && res.data.code === 200) {
          vm.tableData = res.data.data.list;
          vm.total = res.data.data.totalCount;
          // 自动打开详情页
          if (vm.$route.query.checkTreeId && flag !== false) {
            vm.handleDetail(vm.tableData[0]);
          }
        }
      });
    },
    getTree(type) {
      getTree(type).then(response => {
        if (response !== false) {
          const data = response.data.data;
          this.otherForm.treeData = formatTree(data);
          this.otherForm.newTree = formatTree(data);
          this.otherForm.dialogTreeData = data;
        }
      });
    },
    clickNode(data, node) {
      this.treeTitle = formatTreeByNames(node).join('/');
      if (this.checkTreeId !== data.id) {
        this.checkTreeId = data.id;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
.searchInput {
  display: flex;
  .el-input {
    width: 360px;
    margin-right: 10px;
  }
}
</style>
