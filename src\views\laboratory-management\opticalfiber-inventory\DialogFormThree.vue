<template>
  <el-dialog
    v-model="dialogShow"
    :title="titleJson[dialogType]"
    :close-on-click-modal="false"
    width="680px"
    @close="handleClose"
  >
    <el-form
      v-if="dialogShow"
      ref="ruleForm"
      v-loading="dialogLoading"
      :model="formData"
      label-position="top"
      label-width="160px"
      size="small"
    >
      <el-row>
        <el-col :span="11">
          <el-form-item
            :label="`${formLabel.operationWorker[dialogType]}：`"
            prop="operationWorker"
            :rules="{ required: true, message: '请选择着色人', trigger: 'blur' }"
          >
            <el-select v-model="formData.operationWorker" clearable filterable placeholder="请选择着色人">
              <el-option v-for="item in nameList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="11" :offset="2">
          <el-form-item
            :label="`${formLabel.operationDate[dialogType]}：`"
            prop="operationDate"
            :rules="{ required: true, message: '请选择着色日期', trigger: 'blur' }"
          >
            <el-date-picker v-model="formData.operationDate" type="date" placeholder="请选择着色日期" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="dialogType === 'COLOR'">
        <el-col :span="11">
          <el-form-item
            label="机台："
            prop="machinePlatform"
            :rules="{ required: true, message: '请输入机台', trigger: 'blur' }"
          >
            <el-input
              v-model="formData.machinePlatform"
              type="text"
              clearable
              maxlength="30"
              placeholder="请输入机台"
            />
          </el-form-item>
        </el-col>
        <el-col :span="11" :offset="2">
          <el-form-item
            label="着色纤色谱："
            prop="chromatography"
            :rules="{ required: true, message: '请选择着色纤色谱', trigger: 'blur' }"
          >
            <el-select v-model="formData.chromatography" clearable filterable placeholder="请选择着色纤色谱">
              <el-option-group v-for="item in unitAllList" :key="item.label" :label="item.label">
                <el-option
                  v-for="val in item.group"
                  :key="val.id"
                  :label="val.name"
                  :value="val.code"
                  :disabled="val.status !== 1"
                >
                  <span style="float: left">{{ val.name }}</span>
                  <span v-if="val.status !== 1" class="fr" style="color: red">已停用</span>
                </el-option>
              </el-option-group>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="长度（实际测量）：" prop="actualLength">
            <el-input-number
              v-model="formData.actualLength"
              type="text"
              clearable
              placeholder="请输入长度（实际测量）"
              :min="0"
              controls-position="right"
            />
          </el-form-item>
        </el-col>
        <el-col :span="11" :offset="2">
          <el-form-item label="测试衰减1 LOSS1310：" prop="inspectionAttenuation1">
            <el-input v-model="formData.inspectionAttenuation1" type="text" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="测试衰减2 LOSS1550：" prop="inspectionAttenuation2">
            <el-input v-model="formData.inspectionAttenuation2" type="text" clearable />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, ref, toRefs, watch, getCurrentInstance } from 'vue';
import { useStore } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
import { fiberOperationSave } from '@/api/opticalfiberInventory';
import { formatDate } from '@/utils/formatTime';
export default {
  name: 'DialogFormThree',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    unitList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    type: {
      type: String,
      default: ''
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const state = reactive({
      dialogType: '', // 弹出窗类型
      titleJson: {
        COLOR: '光纤着色',
        SWITCH_REEL: '光纤倒盘',
        RETURN_WAREHOUSE: '光纤回仓'
      },
      dialogLoading: false, // 弹出窗loading
      formData: {}, // 表单数据
      rowDetail: {},
      dialogShow: false,
      unitAllList: [],
      ruleForm: ref(),
      listLoading: false,
      formLabel: {
        operationWorker: {
          COLOR: '着色人',
          SWITCH_REEL: '倒盘人',
          RETURN_WAREHOUSE: '回仓人'
        },
        operationDate: {
          COLOR: '着色日期',
          SWITCH_REEL: '倒盘日期',
          RETURN_WAREHOUSE: '回仓日期'
        }
      },
      nameList: store.common.nameList
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.dialogType = newValue.type;
        state.unitAllList = newValue.unitList;
        state.rowDetail = props.detailData;
        state.formData = {
          operationDate: formatDate(new Date()),
          operationWorker: getLoginInfo().accountId,
          actualLength: state.rowDetail.actualLength,
          inspectionAttenuation1: state.rowDetail.inspectionAttenuation1,
          inspectionAttenuation2: state.rowDetail.inspectionAttenuation2,
          mainDataId: state.rowDetail.id,
          operationType: newValue.type
        };
      }
    });
    const onSubmit = () => {
      proxy.$refs['ruleForm'].validate(valid => {
        if (valid) {
          state.dialogLoading = true;
          fiberOperationSave(state.formData).then(res => {
            state.dialogLoading = false;
            if (res) {
              proxy.$message.success(res.data.message);
              context.emit('closeDialog', { isRefresh: true });
            }
          });
        } else {
          return false;
        }
      });
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog', { isRefresh: false });
    };
    const handleRadioResult = val => {
      if (val === 'Scrapped') {
        state.formData.isRemeasured = 0;
        state.formData.description = '';
      }
    };
    return { ...toRefs(state), onSubmit, handleClose, handleRadioResult };
  }
};
</script>
<style lang="scss" scoped>
.unitClass {
  text-align: center;
  margin-left: 5px;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  background-color: rgb(245, 247, 250);
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 100%;
}
:deep(.el-select) {
  width: 100%;
}
:deep(.el-input-number--small) {
  width: 100%;
}
</style>
