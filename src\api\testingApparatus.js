import request from '@/utils/request';
// 客户管理

// 查询列表
export function getList(data) {
  return request({
    url: `/api-diplomat/diplomat/inspection_org/list`,
    method: 'post',
    data
  });
}
// 保存机构信息
export function saveOrganization(data) {
  return request({
    url: `/api-diplomat/diplomat/inspection_org/save`,
    method: 'post',
    data
  });
}
// 获取租户列表
export function getListTenant(data) {
  return request({
    url: `/api-user/user/oauthinformation/list`,
    method: 'post',
    data
  });
}

// 提交关联的租户
export function submitTenant(data) {
  return request({
    url: `/api-diplomat/diplomat/inspection_org_tenant/save`,
    method: 'post',
    data
  });
}
// 查询客户信息
export function getDetail(id) {
  return request({
    url: `/api-diplomat/diplomat/inspection_org/info/${id}`,
    method: 'get'
  });
}
// 发票列表
export function getInvoiceList(orgId) {
  return request({
    url: `/api-diplomat/diplomat/inspection_org_invoice/info/${orgId}`,
    method: 'get'
  });
}
// 新增发票
export function saveInvoiceApi(data) {
  return request({
    url: `/api-diplomat/diplomat/inspection_org_invoice/save`,
    method: 'post',
    data
  });
}
// 地址列表
export function getAddress(orgId) {
  return request({
    url: `/api-diplomat/diplomat/inspection_org_address/list/${orgId}`,
    method: 'get'
  });
}
// 新增地址
export function saveAddressApi({ data: data, id: id }) {
  return request({
    url: `/api-diplomat/diplomat/inspection_org_address/save/${id}`,
    method: 'post',
    data
  });
}

// 删除地址
export function deleteAddressApi(id) {
  return request({
    url: `/api-diplomat/diplomat/inspection_org_address/delete/${id}`,
    method: 'get'
  });
}
// 联系人列表
export function getContacts(orgId) {
  return request({
    url: `/api-diplomat/diplomat/inspection_org_contacts/list/${orgId}`,
    method: 'get'
  });
}
// 新增联系人
export function saveContactsApi({ data: data, id: id }) {
  return request({
    url: `/api-diplomat/diplomat/inspection_org_contacts/save/${id}`,
    method: 'post',
    data
  });
}

// 删除联系人
export function deleteContactsApi(id) {
  return request({
    url: `/api-diplomat/diplomat/inspection_org_contacts/delete/${id}`,
    method: 'get'
  });
}
// 下载附件
export function downLoadFileApi(data) {
  return request({
    url: `/api-diplomat/diplomat/inspection_org_attachment/download`,
    method: 'post',
    responseType: 'blob',
    data
  });
}
// 删除附件
export function deleteUploadFile(id) {
  return request({
    url: `/api-diplomat/diplomat/inspection_org_attachment/delete/${id}`,
    method: 'get'
  });
}
// 附件列表
export function getFileList(orgId) {
  return request({
    url: `/api-diplomat/diplomat/inspection_org_attachment/list/${orgId}`,
    method: 'get'
  });
}
