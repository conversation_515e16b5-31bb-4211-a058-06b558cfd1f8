<template>
  <el-drawer
    v-model="showDrawer"
    :title="titles[drawerType]"
    direction="rtl"
    :before-close="handleClose"
    :size="750"
    destroy-on-close
    :close-on-click-modal="false"
    custom-class="page-drawer"
  >
    <DrawerLayout v-loading="drawerLoading" :has-button-group="false" :has-page-header="false">
      <el-form
        ref="formRef"
        :inline="true"
        :model="formData"
        label-width="100px"
        :class="{ checkForm: isCheck }"
        class="form-height-auto"
        :label-position="isCheck ? 'left' : 'top'"
      >
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item
              label="所属分类："
              prop="categoryId"
              :rules="{ required: !isCheck, message: '请选择所属分类', trigger: 'change' }"
            >
              <el-cascader
                v-model="formData.categoryId"
                :disabled="isCheck"
                :options="dialogTreeData"
                :props="categoryProps"
                style="width: 100%"
                @change="changeCategory"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="辅料编号："
              prop="no"
              :rules="{ required: !isCheck, message: '请输入辅料编号', trigger: 'change' }"
            >
              <el-input
                v-if="!isCheck"
                v-model.trim="formData.no"
                maxlength="100"
                placeholder="请输入辅料编号"
                @change="handleModify"
              />
              <span v-else>{{ formData.no || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="辅料名称："
              prop="name"
              :rules="{ required: !isCheck, message: '请输入辅料名称', trigger: 'change' }"
            >
              <el-input
                v-if="!isCheck"
                v-model.trim="formData.name"
                maxlength="50"
                placeholder="请输入辅料名称"
                @change="handleModify"
              />
              <span v-else>{{ formData.name || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="型号规格："
              prop="model"
              :rules="{ required: !isCheck, message: '请输入型号规格', trigger: 'change' }"
            >
              <el-input
                v-if="!isCheck"
                v-model.trim="formData.model"
                maxlength="100"
                placeholder="请输入型号规格"
                @change="handleModify"
              />
              <span v-else>{{ formData.model || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="库存单位："
              prop="unit"
              :rules="{ required: !isCheck, message: '请选择库存单位', trigger: 'change' }"
            >
              <el-select
                v-if="!isCheck"
                v-model="formData.unit"
                placeholder="请选择库存单位"
                filterable
                style="width: 100%"
                @change="handleModify"
              >
                <el-option-group v-for="item in unitList" :key="item.label" :label="item.label">
                  <el-option
                    v-for="val in item.group"
                    :key="val.id"
                    :label="val.name"
                    :value="val.code"
                    :disabled="val.status !== 1"
                  >
                    <span style="float: left">{{ val.name }}</span>
                    <span v-if="val.status !== 1" class="fr" style="color: red">已停用</span>
                  </el-option>
                </el-option-group>
              </el-select>
              <span v-else>{{ unitAllJson[formData.unit] || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="CAS：" prop="cas">
              <el-input
                v-if="!isCheck"
                v-model="formData.cas"
                maxlength="100"
                placeholder="请输入CAS号"
                @change="handleModify"
              />
              <span v-else>{{ formData.cas || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="辅料描述：" prop="remark">
              <el-input
                v-if="!isCheck"
                v-model="formData.remark"
                type="textarea"
                :maxlength="300"
                :rows="2"
                placeholder="请输入辅料描述"
                @change="handleModify"
              />
              <span v-else>{{ formData.remark || '--' }}</span>
            </el-form-item>
          </el-col>
          <div class="safetyTitle">安全说明</div>
          <el-row class="safetyContent">
            <el-col v-for="(val, key) in safetyInfo" :key="key" :span="24">
              <el-form-item :label="val.name + '：'" :prop="'safetyInstruction.' + key">
                <el-checkbox-group v-if="!isCheck" v-model="formData.safetyInstruction[key]">
                  <el-checkbox v-for="(i, k) in val.dictionary" :key="k" :label="k">{{ i }}</el-checkbox>
                </el-checkbox-group>
                <div v-else>
                  <div v-if="formData.safetyInstruction[key].length > 0">
                    <span v-for="value in formData.safetyInstruction[key]" :key="value">{{
                      safetyInfo[key].dictionary[value]
                    }}</span>
                  </div>
                  <span v-else>--</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="易制爆：" prop="safetyInstruction.easyMakeBomb">
                <el-radio-group v-if="!isCheck" v-model="formData.safetyInstruction.easyMakeBomb" size="small">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
                <span v-else>{{ formData.safetyInstruction.easyMakeBomb ? '是' : '否' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="易制毒：" prop="safetyInstruction.easyMakePoison">
                <el-radio-group v-if="!isCheck" v-model="formData.safetyInstruction.easyMakePoison" size="small">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
                <span v-else>{{ formData.safetyInstruction.easyMakePoison ? '是' : '否' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-row>
      </el-form>
      <div v-if="!isCheck" class="drawer-fotter">
        <el-button type="primary" :loading="drawerLoading" @click="onSubmit">确认</el-button>
        <el-button :loading="drawerLoading" @click="handleClose">取消</el-button>
      </div>
    </DrawerLayout>
  </el-drawer>
</template>

<script>
import { ref, watch, reactive, getCurrentInstance, toRefs } from 'vue';
import { updateAddMaterialApi } from '@/api/standingBook';
import { getDictionary } from '@/api/user';
import DrawerLayout from '@/components/DrawerLayout';
import store from '@/store';

export default {
  name: 'DrawerUnit',
  components: { DrawerLayout },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      required: true
    },
    treeData: {
      type: Array,
      default: function () {
        return [];
      }
    },
    unitList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    unitJson: {
      type: Object,
      default: function () {
        return {};
      }
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // 抽屉事件
    const showDrawer = ref(props.drawer);
    const state = reactive({
      drawerType: '',
      titles: {
        add: '新增辅料',
        edit: '编辑辅料',
        check: '查看辅料'
      },
      isModified: false,
      drawerLoading: false,
      unitAllJson: {},
      isCheck: true,
      userList: store.state.common.nameList,
      options: {
        1: '年',
        2: '月'
      },
      dialogTreeData: [],
      unitList: [],
      detailData: {}, // 详情数据
      formRef: ref(),
      safetyInfo: {
        storageList: {
          name: '存储',
          props: '',
          dictionary: {}
        },
        useList: {
          name: '使用',
          props: '',
          dictionary: {}
        },
        dangerousList: {
          name: '危险性',
          props: '',
          dictionary: {}
        }
      },
      formData: {
        safetyInstruction: {
          storageList: [],
          useList: [],
          dangerousList: [],
          easyMakeBomb: false,
          easyMakePoison: false
        }
      },
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      }
    });
    // 关闭抽屉
    const handleClose = () => {
      if (state.isModified) {
        proxy
          .$confirm('确认离开当前页面吗？离开后数据不可恢复', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false
          })
          .then(() => {
            showDrawer.value = false;
            state.isModified = false;
            context.emit('close', { isRefresh: false, isShow: false });
          })
          .catch(() => {});
      } else {
        showDrawer.value = false;
        context.emit('close', { isRefresh: false, isShow: false });
      }
    };
    watch(props, newValue => {
      showDrawer.value = newValue.drawer;
      if (showDrawer.value) {
        state.drawerType = props.type;
        state.detailData = props.detailData;
        state.dialogTreeData = props.treeData;
        state.isModified = false;
        state.unitList = props.unitList;
        state.unitAllJson = props.unitJson;
        getDictionaryList();
        if (state.drawerType === 'add') {
          initDetail(props);
          state.isCheck = false;
        } else {
          if (state.drawerType === 'check') {
            state.isCheck = true;
          } else {
            state.isCheck = false;
          }
          getDetail(props);
        }
      }
    });
    const handleModify = value => {
      state.isModified = true;
    };
    const getDictionaryList = () => {
      Object.keys(state.safetyInfo).forEach(item => {
        getDictionary(item).then(res => {
          if (res) {
            res.data.data.dictionaryoption.forEach(val => {
              if (val.status === 1) {
                state.safetyInfo[item].dictionary[val.code] = val.name;
              }
            });
          }
        });
      });
    };
    const initDetail = newData => {
      state.formData = {
        safetyInstruction: {
          storageList: [],
          useList: [],
          dangerousList: [],
          easyMakeBomb: false,
          easyMakePoison: false
        },
        categoryId: state.detailData.categoryId === 'all' ? '' : newData.detailData.categoryId
      };
    };
    const getDetail = newData => {
      // const data = newData.detailData
      state.formData = JSON.parse(JSON.stringify(newData.detailData));
      // state.safetyInfo.map(item => item.props).forEach(val => {
      //   state.formData.safetyInstruction[val] = []
      // })
    };
    // 确认新增编辑
    const onSubmit = () => {
      proxy.$refs['formRef'].validate(valid => {
        if (valid) {
          state.drawerLoading = true;
          updateAddMaterialApi(state.formData).then(res => {
            state.drawerLoading = false;
            if (res) {
              proxy.$message.success(res.data.message);
              context.emit('close', { isRefresh: true, isShow: false });
              state.isModified = false;
            }
          });
        } else {
          return false;
        }
      });
    };
    // 所属分类change
    const changeCategory = value => {
      const len = value.length - 1;
      state.formData.categoryId = value[len];
      state.isModified = true;
    };
    return {
      ...toRefs(state),
      getDetail,
      changeCategory,
      getDictionaryList,
      handleModify,
      onSubmit,
      handleClose,
      showDrawer,
      initDetail
    };
  }
};
</script>

<style lang="scss" scoped>
.checkForm {
  .el-form-item {
    margin-bottom: 0;
  }
  .safetyTitle {
    margin: 20px 0 10px 0;
  }
}
// :deep(.el-checkbox-group) {
//   // height: 32px;
// }
.safetyTitle {
  line-height: 18px;
  padding: 5px 0 10px 20px;
  display: inline-block;
  font-size: 17px;
  &::before {
    content: ' ';
    background-color: $tes-primary;
  }
}
:deep(.el-input.is-disabled .el-input__inner) {
  border: 0;
  padding: 0;
  background-color: #fff;
  color: #303133;
  cursor: text;
}
.safetyContent {
  background-color: #f0f2f5;
  padding: 10px 10px 5px 20px;
  border-radius: 5px;
  margin: 0 auto;
  width: 94%;
}
:deep(.is-disabled .el-input__suffix) {
  display: none;
}
</style>
