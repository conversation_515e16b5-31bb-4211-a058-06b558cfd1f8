const beforeUnloadListener = event => {
  event.preventDefault();
  event.returnValue = 'Are you sure you want to exit?';
  return event.returnValue;
};

// 添加浏览器关闭提示
export function addBrowserClosePrompt() {
  addEventListener('beforeunload', beforeUnloadListener, { capture: true });
}

// 删除浏览器关闭提示
export function removeBrowserClosePrompt() {
  removeEventListener('beforeunload', beforeUnloadListener, { capture: true });
}
