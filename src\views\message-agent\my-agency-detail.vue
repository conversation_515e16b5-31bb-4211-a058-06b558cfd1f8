<template>
  <!-- 我的待办详情 -->
  <el-row class="page-title"> {{ messageDetail.title }} </el-row>
</template>

<script>
import { reactive, toRefs, onMounted } from 'vue';
// import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router';
import { getMessageInfo } from '@/api/messageAgent';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';

export default {
  name: 'MyAgencyDetail',
  components: {},
  setup() {
    const route = useRoute();
    // const { proxy } = getCurrentInstance();
    const state = reactive({
      messageDetail: {}
    });
    const initDetail = () => {
      getMessageInfo({ id: route.query.id, messageType: '2' }).then(res => {
        if (res !== false) {
          state.messageDetail = res.data.data?.todomessageEntity;
          console.log(state.messageDetail);
        }
      });
    };
    // 关闭详情抽屉
    const closeDetail = v => {};
    onMounted(() => {
      initDetail();
    });
    return {
      ...toRefs(state),
      formatDate,
      getNameByid,
      getPermissionBtn,
      closeDetail
    };
  }
};
</script>
<style lang="scss" scoped>
.page-title {
  font-size: 20px;
  padding: 0 20px;
  color: #303133;
  background-color: #fff;
}
</style>
