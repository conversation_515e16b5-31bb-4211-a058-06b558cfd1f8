import request from '@/utils/request';
// 检验策略树结构数据
export function getTree(data) {
  return request({
    url: `/api-capabilitystd/capability/inspectionstrategy/listTree`,
    method: 'post',
    data
  });
}
// 新增分类树节点
export function addTreeNode(data) {
  return request({
    url: '/api-capabilitystd/capability/inspectionstrategy/add',
    method: 'post',
    data
  });
}
// 删除分类树节点
export function deleteTreeNode(data) {
  return request({
    url: `/api-capabilitystd/capability/inspectionstrategy/delete`,
    method: 'post',
    data
  });
}
// 更新分类列表树节点
export function updateTreeNode(data) {
  return request({
    url: '/api-capabilitystd/capability/inspectionstrategy/edit',
    method: 'post',
    data
  });
}
// 策略配置、检验项目配置列表
export function getMaterialList(data) {
  return request({
    url: '/api-capabilitystd/capability/inspectionstrategy/info',
    method: 'post',
    data
  });
}
// 检验项目配置列表全
export function getAllConfiguration(data) {
  return request({
    url: '/api-capabilitystd/capability/inspectionstrategy/itemconfig/selecteditems',
    method: 'post',
    data
  });
}
// 添加策列配置
export function addMaterial(data) {
  return request({
    url: `/api-capabilitystd/capability/inspectionstrategy/config/save`,
    method: 'post',
    data
  });
}
// 删除策列配置
export function deleteMaterial(data) {
  return request({
    url: `/api-capabilitystd/capability/inspectionstrategy/config/delete`,
    method: 'post',
    data
  });
}
// 添加检验项目配置
export function addTestItems(data) {
  return request({
    url: '/api-capabilitystd/capability/inspectionstrategy/itemconfig/save',
    method: 'post',
    data
  });
}
// 添加检验项目配置
export function deleteTestItems(data) {
  return request({
    url: `/api-capabilitystd/capability/inspectionstrategy/itemconfig/delete`,
    method: 'post',
    data
  });
}
// 添加检验项目配置
export function updateTreeOrder(data) {
  return request({
    url: `/api-capabilitystd/capability/inspectionstrategy/updateOrder`,
    method: 'post',
    data
  });
}
// 根据id匹配策略
export function getStrategy(id) {
  return request({
    url: `/api-capabilitystd/capability/inspectionstrategy/getById/${id}`,
    method: 'get'
  });
}
// 策略匹配检测项目排序
export function updateOrderItemApi(data) {
  return request({
    url: `/api-capabilitystd/capability/inspectionstrategy/itemconfig/updateOrder`,
    method: 'post',
    data
  });
}
// 刷新检验策略配置的检测项目
export function inspectionReload(data) {
  return request({
    url: `/api-capabilitystd/capability/inspectionstrategy/itemconfig/inspectionReload`,
    method: 'post',
    data
  });
}
