<template>
  <!-- 原始记录审核 -->
  <el-table
    ref="tableRef"
    :key="tableKey"
    v-loading="listLoading"
    :data="tableList"
    size="medium"
    fit
    border
    class="dark-table base-table format-height-table downTable"
  >
    <el-table-column type="index" label="序号" :width="colWidth.checkbox" align="center" fixed="left" />
    <el-table-column label="报告编号" prop="reportNo" :min-width="colWidth.orderNo" show-overflow-tooltip>
      <template #default="{ row }">
        {{ row.reportNo || '--' }}
      </template>
    </el-table-column>
    <el-table-column label="批次" prop="batchNo" :min-width="colWidth.name" show-overflow-tooltip>
      <template #default="{ row }">
        {{ row.batchNo || '--' }}
      </template>
    </el-table-column>
    <el-table-column label="盘号" prop="reelNo" :min-width="colWidth.model" show-overflow-tooltip>
      <template #default="{ row }">
        {{ row.reelNo || '--' }}
      </template>
    </el-table-column>
  </el-table>
  <pagination v-show="total > 0" :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getList" />
</template>

<script>
import { reactive, ref, toRefs, watch } from 'vue';
import Pagination from '@/components/Pagination';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { nonconformingList } from '@/api/order';
import { colWidth } from '@/data/tableStyle';
import { unqualifiedTableList } from '../data/testData';
export default {
  name: 'DownTable',
  components: { Pagination },
  props: {
    capabilityId: {
      type: String,
      default: ''
    },
    searchInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  setup(props) {
    // const _ = inject('_')
    const state = reactive({
      tableRef: ref(),
      listLoading: false,
      listQuery: {
        limit: 20,
        page: 1
      },
      tableList: [],
      total: 0
    });
    const getList = query => {
      if (props.capabilityId) {
        const params = {
          capabilityParaId: props.capabilityId,
          ...props.searchInfo
        };
        if (query && query.page) {
          params.page = query.page.toString();
          params.limit = query.limit.toString();
        } else {
          state.listQuery.page = 1;
          params.page = '1';
          params.limit = state.listQuery.limit.toString();
        }
        state.listLoading = true;
        nonconformingList(params).then(res => {
          state.listLoading = false;
          if (res) {
            state.total = res.data.data.totalCount;
            state.tableList = res.data.data.list;
          }
        });
      } else {
        state.total = unqualifiedTableList.totalCount;
        state.tableList = unqualifiedTableList.list;
      }
    };
    watch(
      props,
      newValue => {
        getList();
      },
      { immediate: true }
    );

    const tableKey = ref(0);
    return {
      ...toRefs(state),
      getPermissionBtn,
      getNameByid,
      getNamesByid,
      getList,
      tableKey,
      colWidth
    };
  }
};
</script>
<style lang="scss">
.downTable {
  .el-table__body-wrapper {
    max-height: calc(100vh - 590px) !important;
    overflow-y: auto;
  }
  .el-table__fixed-body-wrapper {
    max-height: calc(100vh - 590px);
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
}
</style>
