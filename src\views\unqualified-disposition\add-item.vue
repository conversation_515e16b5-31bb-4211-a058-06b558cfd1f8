<template>
  <el-dialog
    :model-value="showDialog"
    custom-class="add-unqulified"
    title="选择样品"
    width="70%"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <el-row class="add-report-search">
      <el-col :span="24">
        <el-input
          ref="inputRef"
          v-model="filterText"
          v-trim
          class="search"
          size="small"
          placeholder="请输入申请单号"
          prefix-icon="el-icon-search"
          clearable
          maxlength="100"
          @keyup.enter="searchItem(filterText)"
        />
        <el-button type="primary" size="small" style="margin-left: 15px" @click="searchItem(filterText)"
          >查询</el-button
        >
      </el-col>
    </el-row>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      size="medium"
      class="dark-table base-table add-report-table"
      @header-dragend="drageHeader"
      @row-click="handleRowClick"
    >
      <el-table-column label="选择" prop="select" :width="colWidth.checkbox">
        <template #default="{ row }">
          <el-radio v-model="row.radio" :label="row.reportNo" @change="changeRadio">{{ '' }}</el-radio>
        </template>
      </el-table-column>
      <el-table-column label="样品编号" prop="no" :width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.secSampleNum || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="检验报告编号" prop="reportId" :width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.reportNo">{{ row.reportNo }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="检验类型" prop="type" :width="colWidth.typeGroup" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ filterTypes(row.type) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="检验对象" prop="inspectionApplyThirdNo" :width="colWidth.objectNo" show-overflow-tooltip>
        <template #default="{ row }">
          <!-- <span>{{ row.type === 1? row.inputWarehouseNo : row.inspectionApplyThirdNo || '--' }}</span> -->
          <span>{{ row.type === 1 ? row.inputWarehouseNo || '--' : row.productionOrderNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="对象位置" prop="productionStation" :min-width="colWidth.name">
        <template #default="{ row }">
          <span>{{
            row.type === 1
              ? row.wareHouseName
              : (row.productionProcedure || '--') + '-' + (row.productionStation || '--')
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="盘号" prop="status" :width="colWidth.plate" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.reelNo || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="物料编号" prop="materialNo" :width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.materialNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="样品名称" prop="materialDesc" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.materialDesc || row.sampleName || '--' }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      small
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getUnqualifiedReport"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="dialogSuccess">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, toRefs, watch, getCurrentInstance, nextTick } from 'vue';
import { getNameByid } from '@/utils/common';
import { ElMessage } from 'element-plus';
import Pagination from '@/components/Pagination';
import { drageHeader } from '@/utils/formatTable';
import {
  findUnqualifiedReport,
  findReportCapability,
  getReportList,
  getInspectionList,
  checkNo
} from '@/api/unqualifiedDisposition';
import { getDictionaryDetail } from '@/api/dictionary';
// import { formatDate } from '@/utils/formatTime'
import _ from 'lodash';
// import { ElLoading } from 'element-plus'
// import { useRoute } from 'vue-router'
// import { formatPaginationList } from '@/utils/formatJson'
import { colWidth } from '@/data/tableStyle';

export default {
  name: 'AddUnqulified',
  components: { Pagination },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'selectData'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const tableRef = ref(null);
    const datas = reactive({
      showDialog: props.show,
      filterText: '',
      inputRef: ref(),
      listLoading: false,
      tableKey: 0,
      list: [],
      total: 0,
      listQuery: {
        page: 1,
        limit: 10
      },
      formInline: {
        keyword: ''
      },
      selectItem: null,
      newList: [],
      sampleUnitList: []
    });

    watch(
      () => props.show,
      async newValue => {
        // console.log(newValue)
        if (newValue) {
          await proxy.getUnqualifiedReport();
          datas.showDialog = newValue;
          nextTick(() => {
            datas.inputRef.focus();
          });
        }
      },
      { deep: true }
    );

    // 过滤检验类型
    const filterTypes = type => {
      // 检验类型 采购入库=1，过程检验=2，完工检验=3
      const classMap = {
        1: '采购入库',
        2: '过程检验',
        3: '完工检验',
        10: '委托试验',
        11: '型式试验',
        12: '能力验证'
      };
      return classMap[type];
    };
    // 过滤样品单位
    const filterUnit = id => {
      var name = '';
      if (datas.sampleUnitList.length > 0) {
        datas.sampleUnitList.forEach(ul => {
          if (ul.id === id) {
            name = ul.name;
          }
        });
      }
      return name;
    };

    // 查询
    const searchItem = value => {
      datas.formInline.keyword = value;
      proxy.getUnqualifiedReport();
    };

    // 确定选择
    const dialogSuccess = () => {
      if (!datas.selectItem) {
        ElMessage.warning('请勾选样品');
        return false;
      }
      checkNo(datas.selectItem.id).then(res => {
        if (res !== false) {
          // console.log(res.data.data)
          if (res.data.data === false) {
            context.emit('selectData', datas.selectItem);
            context.emit('close', false);
            datas.showDialog = false;
          } else {
            ElMessage.warning('样品已登记');
          }
        }
      });
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      datas.listQuery = {
        page: 1,
        limit: 10
      };
      datas.filterText = '';
      context.emit('close', false);
    };
    // 列表选择事件
    const changeRadio = reportNo => {
      // console.log(reportNo)
      datas.list.forEach(async item => {
        if (item.reportNo === reportNo) {
          // item.radio = true
          item.reportId = item.id;
          item.sampleUnitName = filterUnit(item.sampleUnit);
          item.expList = await proxy.getKeyWordsList(item.id);
          datas.selectItem = item;
        } else {
          item.radio = false;
        }
      });
      // console.log(datas.list)
    };
    const handleRowClick = row => {
      datas.list.forEach(item => {
        if (item.reportNo === row.reportNo) {
          item.radio = row.reportNo;
        }
      });
      changeRadio(row.reportNo);
    };

    return {
      ...toRefs(datas),
      close,
      handleRowClick,
      getNameByid,
      dialogSuccess,
      drageHeader,
      changeRadio,
      filterTypes,
      searchItem,
      tableRef,
      filterUnit,
      colWidth
    };
  },
  created() {
    this.getSampleUnits();
  },
  methods: {
    // 获取不合格报告列表tes的
    getUnqualifiedReport(value) {
      const that = this;
      that.listLoading = true;
      that.formInline.keyword = that.filterText;
      if (value && value !== undefined) {
        that.listQuery.page = value.page;
        that.listQuery.limit = value.limit;
      }
      const param = Object.assign(that.formInline, that.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      // console.log(param)
      findUnqualifiedReport(param).then(res => {
        if (res !== false) {
          // console.log(res.data.data.list)
          that.list = res.data.data.list;
          that.total = res.data.data.totalCount;
          that.list.forEach(item => {
            if (!item.radio) {
              item.radio = false;
            }
          });
          that.listLoading = false;
        }
      });
    },
    // 获取不合格报告列表lims的,lims做的太脑残被咱取缔了，这个方法放在这边，给后人当笑话看。。。。。。。
    getReportLists(value) {
      const that = this;
      that.listLoading = true;
      const param = {
        limit: 10,
        offset: 0,
        assignedToMe: false,
        keyword: that.filterText,
        sourceType: 'inspection',
        reportResult: 'FAIL'
      };
      if (value && value !== undefined) {
        that.listQuery.page = value.page;
        that.listQuery.limit = value.limit;
      }
      param.limit = that.listQuery.limit;
      param.offset = (that.listQuery.page - 1) * that.listQuery.limit;
      getReportList(param).then(async res => {
        // console.log(res.data.items)
        if (res !== false) {
          that.list = res.data.items;
          if (that.list.length > 0) {
            const hasitems = _.filter(that.list, res2 => {
              return res2.reportDetailSampleInfo;
            });
            that.list = hasitems;
            const params = {
              limit: that.total,
              inspectionIds: []
            };
            that.list.forEach(item => {
              params.inspectionIds.push(item.sampleId);
              if (!item.radio) {
                item.radio = false;
              }
              item.reportDetailSampleInfoId = item.reportDetailSampleInfo ? item.reportDetailSampleInfo.id : {};
              item = Object.assign(item, item.reportDetailSampleInfo);
            });
            // console.log(params)
            await getInspectionList(params).then(res1 => {
              if (res1 !== false) {
                // console.log(res1.data.items)
                const inspectionLists = res1.data.items;
                if (inspectionLists.length > 0 && that.list.length > 0) {
                  that.list.forEach(item => {
                    const hasitem = _.filter(inspectionLists, res2 => {
                      return item.sampleId === res2.id;
                    });
                    if (hasitem.length > 0) {
                      item.currentId = item.id;
                      item = Object.assign(item, hasitem[0]);
                    }
                  });
                }
                // console.log(that.list)
              }
            });
          }
        }
        that.listLoading = false;
      });
    },
    getSampleUnits() {
      // getSampleUnit().then(res => {
      //   if (res !== false) {
      //     this.sampleUnitList = res.data.items
      //   }
      // })
      getDictionaryDetail(5).then(res => {
        this.sampleUnitList = res.data.data?.dictionaryoption;
      });
    },
    getKeyWordsList(reportId) {
      return new Promise((resolve, reject) => {
        findReportCapability(reportId)
          .then(res2 => {
            if (res2 !== false) {
              // console.log(res2.data.data)
              resolve(res2.data.data);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.add-unqulified {
  .search {
    width: 360px;
  }

  .add-report-search {
    margin-bottom: 15px;
  }

  .add-report-table {
    :deep(.el-table thead th) {
      background-color: #f6f6f6;
    }

    :deep(.el-table__body-wrapper) {
      max-height: calc(100vh - 28.5rem);
      overflow-y: auto;
    }
  }
  .el-table thead th {
    background: #f6f6f6;
  }
  .el-dialog__body {
    padding-bottom: 0;
  }
}
</style>
