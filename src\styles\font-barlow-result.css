/* Generated By cn-font-split@4.15.1 https://www.npmjs.com/package/cn-font-split
CreateTime: Fri, 21 Jun 2024 10:18:20 GMT;
Origin File Name Table:
copyright: Copyright 2017 The Barlow Project Authors (https://github.com/jpt/barlow)
fontFamily: Barlow
fontSubfamily: Regular
uniqueID: 1.101;TRBY;Barlow-SemiBold
fullName: Barlow
version: Version 1.101
postScriptName: Barlow-SemiBold
manufacturer: <PERSON>
designer: <PERSON>
manufacturerURL: https://tribby.com/
designerURL: https://tribby.com/
license: This Font Software is licensed under the SIL Open Font License, Version 1.1. This license is available with a FAQ at: http://scripts.sil.org/OFL
licenseURL: http://scripts.sil.org/OFL
preferredFamily: Barlow
preferredSubfamily: SemiBold
 */

@font-face {
  font-family: 'Barlow';
  src: local('Barlow'), url('../assets/font/Barlow-SemiBold/e67b54153ff34a14aa42d812d208e2ab.woff2') format('woff2');
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  unicode-range: U+d, U+20-7e, U+a0-ff, U+131, U+152-153, U+2c6, U+2da, U+2dc, U+304, U+308, U+2013-2014, U+2018-201a,
    U+201c-201e, U+2020-2022, U+2026, U+2030, U+2039-203a, U+2044, U+2074-2079, U+20ac, U+2122, U+2212, U+2215;
}
@font-face {
  font-family: 'Barlow';
  src: local('Barlow'), url('../assets/font/Barlow-SemiBold/054de73c731564cd1349a3f77681c8ed.woff2') format('woff2');
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  unicode-range: U+100-107, U+10a-113, U+116-11b, U+11e-123, U+126-127, U+12a-12b, U+12e-130, U+133, U+136-137,
    U+139-13e, U+141-148, U+14a-14d, U+150-151, U+154-15b, U+15e-167, U+16a-16b, U+16e-17e, U+192, U+1cd-1ce, U+218-21b,
    U+228-229, U+1e80-1e85, U+1ef2-1ef3, U+20a3, U+20ba, U+20bd, U+2113;
}
@font-face {
  font-family: 'Barlow';
  src: local('Barlow'), url('../assets/font/Barlow-SemiBold/d5b6d8a34d42c71dedf10afb92d04478.woff2') format('woff2');
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  unicode-range: U+300-301, U+303;
}
@font-face {
  font-family: 'Barlow';
  src: local('Barlow'), url('../assets/font/Barlow-SemiBold/0a671301a843d8c5664e6cac59b63fdf.woff2') format('woff2');
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  unicode-range: U+394, U+3a9, U+3bc, U+3c0;
}
@font-face {
  font-family: 'Barlow';
  src: local('Barlow'), url('../assets/font/Barlow-SemiBold/9868bda6f4548507264f62393ac87457.woff2') format('woff2');
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  unicode-range: U+2c7, U+2c9, U+2d8-2d9, U+2db, U+2dd, U+302, U+306-307, U+30a-30c, U+312-313, U+326-328, U+335-338,
    U+2126, U+212e, U+215b-215e, U+2202, U+2206, U+220f, U+2211, U+2219-221a, U+221e, U+222b, U+2248, U+2260,
    U+2264-2265, U+25ca;
}
