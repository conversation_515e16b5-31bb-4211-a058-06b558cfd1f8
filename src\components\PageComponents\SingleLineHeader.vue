<template>
  <div class="full-item" style="border-top: 1px solid #dcdfe6">
    <div class="left-form-group">
      <slot name="left-form-group" />
    </div>
    <div class="right-button-group">
      <slot name="right-button-group" />
    </div>
  </div>
</template>
<script>
import { reactive, toRefs } from 'vue';

export default {
  name: 'CollapseHeader',
  setup(props, context) {
    const data = reactive({});

    return {
      ...toRefs(data)
    };
  }
};
</script>

<style lang="scss" scoped>
.full-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding-top: 8px;
  .left-form-group {
    flex: 2;
  }
  .right-button-group {
    flex: 1;
    height: inherit;
    top: 30px;
    text-align: right;
    margin-bottom: 18px;
  }
  :slotted(.el-form) {
    .el-form-item,
    .el-form-item__content {
      width: 100%;
    }
    .el-select.el-select--small,
    .el-button.el-button--small,
    .el-range-editor--small,
    .el-input-number--small,
    .el-tag {
      width: 100%;
    }
    .el-form-item__content {
      display: flex;
      align-items: center;
    }

    .el-button {
      font-weight: normal;
      border-radius: 4px;
      border-color: #dcdfe6;
      &:hover {
        border-color: #b3e8dc;
      }
    }
    .el-tag {
      height: 32px;
      line-height: 32px;
      text-overflow: ellipsis;
      max-width: 100%;
      word-break: break-all;
      overflow: hidden;
      border-color: transparent;
      .el-tag__close {
        font-size: 16px;
        position: absolute;
        right: 6px;
        top: 8px;
      }
    }
  }
}
</style>
