<!-- 样品回库 -->
<template>
  <div>
    <el-dialog
      v-model="detailVisible"
      title="样品回库"
      :width="720"
      :before-close="closeDialog"
      :close-on-click-modal="false"
    >
      <el-form
        v-if="detailVisible"
        ref="formRef"
        v-loading="loading"
        :model="formData"
        size="small"
        label-position="top"
        label-width="90px"
        class="form"
      >
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item :label="tenantType === 1 ? '申请单号：' : '委托编号：'">
              <span>{{ formData.presentationCode || '--' }}</span>
            </el-form-item></el-col
          >
          <el-col :span="12">
            <el-form-item label="样品名称：">
              <span class="name-txt">{{ formData.mateName || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="回库日期："
              prop="operateTime"
              :rules="{ required: true, message: '请选择回库日期', trigger: 'blur' }"
            >
              <el-date-picker
                v-model="formData.operateTime"
                style="width: 100%"
                type="date"
                placeholder="选择回库日期"
              /> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item
              label="回库数量："
              prop="quantity"
              :rules="[
                { required: true, message: '请输入回库数量', trigger: 'blur' },
                { validator: validateNumber, trigger: 'change' }
              ]"
            >
              <el-input
                v-model="formData.quantity"
                v-trim
                type="number"
                autocomplete="off"
                maxlength="18"
                placeholder="请输入回库数量"
                style="width: 50%"
              />
              <el-select
                v-model="formData.unitName"
                placeholder="请选择数量单位"
                style="width: 48%; float: right"
                disabled
              >
                <el-option-group v-for="it in dirList" :key="it.label" :label="it.label">
                  <el-option
                    v-for="val in it.group"
                    :key="val.id"
                    :label="val.name"
                    :value="val.code"
                    :disabled="val.status !== 1"
                  >
                    <span style="float: left">{{ val.name }}</span>
                    <span v-if="val.status !== 1" class="fr" style="color: red">已停用</span>
                  </el-option>
                </el-option-group>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="回库状态：" prop="returnStatus">
              <el-radio-group v-model="formData.returnStatus">
                <el-radio :label="0">完好</el-radio>
                <el-radio :label="1">其他</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格长度：" prop="specLen">
              <el-radio-group v-model="formData.specLen">
                <el-radio :label="0">完好</el-radio>
                <el-radio :label="1">其他</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="存放地：" prop="storagePlace">
              <el-input
                v-model="formData.storagePlace"
                type="textarea"
                placeholder="请输入存放地"
                :rows="2"
                autocomplete="off"
              /> </el-form-item
          ></el-col>
          <el-col :span="24">
            <el-form-item label="回库说明：" prop="returnInfo">
              <el-input
                v-model="formData.returnInfo"
                placeholder="请输入回库说明"
                type="textarea"
                :rows="2"
                autocomplete="off"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog(false)">取 消</el-button>
          <el-button type="primary" @click="onSumbit()">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { reactive, toRefs, watch, ref, getCurrentInstance } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getDictionaryDetail } from '@/api/dictionary';
import { filterProcessMode, filterSampleUnitToName } from '@/utils/formatJson';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { warehousingReturn } from '@/api/samplestorage';
import { useStore } from 'vuex';
import { validateNumber } from '@/utils/validate';

export default {
  name: 'ModuleReturnStorage',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, ctx) {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const state = reactive({
      formData: {},
      tenantType: store.user.tenantInfo.type,
      dirList: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ],
      formRef: ref(),
      detailVisible: false,
      loading: false
    });
    watch(
      () => props.visible,
      newValue => {
        if (props.visible) {
          state.detailVisible = props.visible;
          if (state.detailVisible) {
            getDictionaryList();
            state.formData = {
              presentationCode: props.detail.presentationCode,
              mateName: props.detail.mateName,
              sampleId: props.detail.sampleId,
              operateTime: new Date(),
              unitName: props.detail.sampleUnit,
              returnStatus: 0,
              specLen: 0
            };
          }
        }
      }
    );
    // 关闭弹框
    const closeDialog = () => {
      state.detailVisible = false;
      ctx.emit('close');
    };
    const onSumbit = () => {
      proxy.$refs['formRef'].validate(valid => {
        if (valid) {
          state.loading = true;
          warehousingReturn(state.formData).then(res => {
            state.loading = false;
            if (res) {
              ctx.emit('close', true);
              state.detailVisible = false;
              proxy.$message.success(res.data.message);
            }
          });
        } else {
          return false;
        }
      });
    };
    // 回库数量
    const getDictionaryList = () => {
      getDictionaryDetail(5).then(res => {
        res.data.data.dictionaryoption.forEach(item => {
          if (item.status === 1) {
            state.dirList[0].group.push(item);
          } else {
            state.dirList[1].group.push(item);
          }
        });
      });
    };
    return {
      ...toRefs(state),
      getPermissionBtn,
      getDictionaryList,
      closeDialog,
      validateNumber,
      filterProcessMode,
      formatDate,
      filterSampleUnitToName,
      getNameByid,
      onSumbit
    };
  }
};
</script>

<style scoped lang="scss">
.file-upload-input {
  display: none;
  z-index: -9999;
}
.tips,
.flies {
  margin: 20px 0;
}
.tips {
  margin-left: 10px;
}
.btn-del {
  color: $tes-primary;
  margin-left: 5px;
  cursor: pointer;
}
// .line-item{
//   //border-bottom: 1px solid #CCC;
//   margin-bottom: 14px;
//   margin-right: 15px;
//   .el-form-item{
//     margin-bottom: 2px;
//   }
//   &:last-child{
//     border-bottom: 1px solid #fff;
//     margin-bottom: 4px;
//   }
// }
// .form{
//   max-height: 65vh;
//   overflow-y: auto;
//   position: relative;
//   // border: 1px solid #EBEEF5;
//   padding-top:10px;
//   border-radius: 4px;
//   margin-bottom: 10px;
//   margin-right: 10px;
//   margin-left: 5px;
//   .name-txt{
//     width: 90%;
//     display: inline-block;
//     overflow: hidden;
//     text-overflow:ellipsis;
//     white-space: nowrap;
//   }
//   .btn-edit{
//     position: absolute;
//     right: 15px;
//     top: 10px;
//     font-size: 20px;
//     cursor: pointer;
//     padding: 5px;
//     z-index: 8888;
//   }
// }
.details-info-box {
  overflow: hidden;
  border: 1px solid #bbb;
  border-radius: 4px;
  padding: 10px 16px;
}
.details-info-box-row {
  overflow: hidden;
  line-height: 1.2;
  .el-col {
    margin: 8px 0;
  }
}
</style>
