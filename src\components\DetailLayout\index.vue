<template>
  <!-- 详情页通用框架 -->
  <div class="page-detail-wrapper">
    <!-- 页头 固定区域 -->
    <div v-if="hasPageHeader" class="page-header">
      <slot name="page-header" />
    </div>
    <!-- 主体 页面滚动区域 -->
    <div
      class="page-detail-main"
      :style="{
        top: hasPageHeader ? `${mainOffsetTop}px` : '20px',
        height: hasFullMainHeight ? `calc(100% - ${hasPageHeader ? mainOffsetTop : 20}px)` : null
      }"
    >
      <!-- 主体区域 -->
      <slot />
    </div>
    <slot name="other" />
  </div>
</template>
<script>
export default {
  name: 'DetailLayout',
  props: {
    hasPageHeader: {
      type: Boolean,
      default: true
    },
    mainOffsetTop: {
      type: Number,
      default: 84
    },
    hasFullMainHeight: {
      type: Boolean,
      default: true
    }
  }
};
</script>

<style lang="scss" scoped>
// detail page
.page-detail-wrapper {
  height: inherit;
  overflow: hidden auto;
  .page-header {
    border-bottom: 1px solid #f0f2f5;
    background: $background-color;
    padding: 12px 24px;
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    z-index: 1000;
  }
  .page-detail-main {
    position: relative;
    width: calc(100% - 48px);
    margin: 0 24px;
    background: $background-color2;
    // padding: 20px 24px;
  }
}
</style>
