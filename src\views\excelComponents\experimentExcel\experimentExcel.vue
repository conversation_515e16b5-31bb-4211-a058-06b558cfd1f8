<template>
  <DetailLayout :main-offset-top="146">
    <template v-if="showHtml" #page-header>
      <div class="header-flex flex-between">
        <div class="page-title">
          <span class="title">版本号：</span>
          <el-button
            v-for="(item, index) in versionList"
            :key="index"
            :type="excelid === item.id ? 'primary' : null"
            round
            size="small"
            @click="changeCapability(item)"
          >
            {{ item.version }}
          </el-button>
          <el-dropdown
            v-if="unreleasedTemplates.length > 0"
            trigger="click"
            class="ml-2.5"
            @command="onUnreleasedTemplateClick"
          >
            <el-button size="small" round>草稿<i class="el-icon-arrow-down ml-1" /></el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="item in unreleasedTemplates"
                  :key="item.designTemplateId"
                  :command="item.designTemplateId"
                  >{{ item.version }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <div class="btn-group">
          <el-button size="large" icon="el-icon-back" @click="back">返回列表</el-button>
          <el-button v-if="getPermissionBtn('templatePrint')" size="large" icon="el-icon-printer" @click="toImg()"
            >打印</el-button
          >
          <el-button
            v-if="getPermissionBtn('newTemplateAdd') || getPermissionBtn('templateAdd')"
            size="large"
            icon="el-icon-edit"
            @click="handleUpload()"
            >编辑模板</el-button
          >
          <el-button
            v-if="getPermissionBtn('newTemplateAdd') || getPermissionBtn('templateAdd')"
            size="large"
            icon="el-icon-plus"
            @click="handleUpload(true)"
            >创建模板</el-button
          >
        </div>
      </div>
      <div class="page-header-group">
        <el-row :gutter="20">
          <el-col :span="12" class="item">
            <span class="title">检测项目：</span>
            <span class="txt ellipsis">
              {{ capabilityNumber + '-' + capabilityName }}
            </span>
          </el-col>
          <el-col :span="12" class="item">
            <span class="title">版本描述：</span>
            <span class="txt ellipsis">
              {{ excelinfo.description || '--' }}
            </span>
          </el-col>
          <el-col :span="6">
            <span class="title">默认当前模板：</span>
            <span class="txt">
              <el-switch
                v-model="excelinfo.isDefault"
                :disabled="excelinfo.status === 0"
                class="inner-switch"
                :active-text="excelinfo.isDefault ? '是' : '否'"
                @change="handleOff(value)"
              />
            </span>
          </el-col>
          <!-- <el-col :span="6">
            <span class="title">状态：</span>
            <span class="txt">
              <el-tag size="small" :type="excelinfo.status === 1 ? 'success' : 'info'">{{
                excelinfo.status === 1 ? '已启用' : '已禁用'
              }}</el-tag>
            </span>
          </el-col> -->
          <el-col :span="6">
            <span class="title">模板中动态分组数：</span>
            <span class="txt">
              {{ excelinfo.coreNumber || '--' }}
            </span>
          </el-col>
          <el-col :span="6">
            <span class="title">单个分组中试件个数：</span>
            <span class="txt">
              {{ excelinfo.groupNumber || '--' }}
            </span>
          </el-col>
        </el-row>
      </div>
    </template>
    <div class="moban">
      <div v-if="showHtml">
        <base-excel :json-data="excelinfo">
          <template #default>
            <div ref="excel" v-html="html" />
          </template>
        </base-excel>
      </div>
      <el-empty v-else :image="emptyImg" description="暂无模板" class="box-empty">
        <el-button
          v-if="getPermissionBtn('newTemplateAdd') || getPermissionBtn('templateAdd')"
          icon="el-icon-plus"
          type="primary"
          @click="handleUpload(true)"
          >创建模板</el-button
        >
        <el-dropdown
          v-if="unreleasedTemplates.length > 0 && getPermissionBtn('newTemplateAdd')"
          trigger="click"
          class="ml-2.5"
          @command="onUnreleasedTemplateClick"
        >
          <el-button>草稿<i class="el-icon-arrow-down ml-1" /></el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="item in unreleasedTemplates"
                :key="item.designTemplateId"
                :command="item.designTemplateId"
                >{{ item.version }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-empty>
    </div>
    <!-- 上传附件版创建编辑模板 -->
    <DialogTemplateUpload
      :dialog-visiable="dialogTemplateUpload"
      :is-add="isadd"
      :form-info="excelinfo"
      @closeDialog="closeUploadDialog"
    />
    <!-- 模板引擎创建编辑弹窗 -->
    <DialogTemplateOnline
      :dialog-visiable="dialogTemplateOnline"
      :is-add="isadd"
      :form-info="excelinfo"
      :released-templates="releasedTemplates"
      :unreleased-templates="unreleasedTemplates"
      @closeDialog="closeOnlineDialog"
    />
  </DetailLayout>
</template>

<script>
import { reactive, toRefs, ref, nextTick, computed } from 'vue';
import { getToken } from '@/utils/auth';
import { useRoute } from 'vue-router';
// import store from '@/store'
import { getPermissionBtn } from '@/utils/common';
import { decryptCBC } from '@/utils/ASE';
import {
  capabilitytemplate,
  downloadExcel,
  getByCapabilityId,
  updatecapabilitytemplate,
  queryReleasedTemplates,
  queryUnreleasedTemplates
} from '@/api/excel';
import { ElMessage } from 'element-plus';
import baseExcel from '@/views/excelComponents/baseExcel';
import router from '@/router';
import pdf from '@/utils/preview-or-download-pdf';
import DetailLayout from '@/components/DetailLayout';
import { getXhModuleNumber } from '@/views/excelComponents/func/dynamicCore';
import { getLoginInfo } from '@/utils/auth';
import DialogTemplateOnline from './dialog-template-online.vue';
import DialogTemplateUpload from './dialog-template-upload.vue';
import emptyImg from '@/assets/img/empty-template.png';

export default {
  name: 'ExperimentExcel',
  components: {
    baseExcel,
    DetailLayout,
    DialogTemplateUpload,
    DialogTemplateOnline
  },
  setup(props, { refs, root }) {
    // const { appContext } = getCurrentInstance()
    const route = useRoute();
    const uploadRef = ref();
    const excel = ref(null);
    const state = reactive({
      isadd: false,
      isCopy: false, // 是否是复制状态
      dialogSingleItem: false,
      capabilityId: route.query.capabilityId,
      capabilityName: route.query.capabilityName,
      capabilityNumber: route.query.capabilityNumber,
      excelinfo: {},
      id: '',
      excelid: '',
      html: '',
      versionList: [],
      headerconfig: {
        Authorization: getToken()
      },
      dialogTemplateUpload: false,
      dialogTemplateOnline: false, // 在线设计模板弹出框
      releasedTemplates: [],
      unreleasedTemplates: []
    });
    const getExceldata = () => {
      getByCapabilityId(state.capabilityId).then(res => {
        if (res.data.code === 200 && res.data) {
          state.versionList = res.data.data;
          if (state.versionList.length !== 0) {
            const _index = state.versionList.length - 1;
            const findIndex = state.versionList.findIndex(item => item.isDefault === 1);
            const defaultIndex = findIndex === -1 ? _index : findIndex;
            changeCapability(state.versionList[defaultIndex]);
          }
        }
      });
    };
    getExceldata();
    const getExcelInfo = () => {
      if (state.excelid) {
        capabilitytemplate(state.excelid).then(resp => {
          if (resp) {
            state.excelinfo = resp.data.data;
            state.excelinfo.capabilityName = state.capabilityName;
            state.excelinfo.isDefault = state.excelinfo.isDefault === 1;
            const postexcelinfo = {
              capabilityTemplateId: resp.data.data.capabilityId,
              version: resp.data.data.version
            };
            downloadExcel(postexcelinfo).then(respo => {
              if (respo) {
                state.html = respo.data.data ? decryptCBC(respo.data.data.html) : '';
                nextTick(() => {
                  getXhModuleNumber(route.name, state.excelinfo.coreNumber, null);
                });
              }
            });
          }
        });
      }
    };
    const getReleasedTemplates = () => {
      queryReleasedTemplates(state.capabilityId).then(response => {
        if (response) {
          const data = response.data.data;
          if (data && data?.length > 0) {
            state.releasedTemplates = data;
          }
        }
      });
    };
    getReleasedTemplates();
    const getUnreleasedTemplates = () => {
      queryUnreleasedTemplates(state.capabilityId).then(response => {
        if (response) {
          const data = response.data.data;
          if (data && data?.length > 0) {
            state.unreleasedTemplates = data;
          }
        }
      });
    };
    getUnreleasedTemplates();
    const changeCapability = val => {
      state.excelid = val.id;
      getExcelInfo();
    };
    // 启用
    const handleOff = val => {
      const changeInfo = JSON.parse(JSON.stringify(state.excelinfo));
      changeInfo.isDefault = val === false ? 0 : 1;
      updatecapabilitytemplate(changeInfo).then(data => {});
      // }
    };
    // 转图片打印
    const toImg = () => {
      // 转图片打印
      pdf.PageImg(state.excelinfo?.showType);
    };
    // 创建编辑模板
    const handleUpload = isAdd => {
      state.isadd = isAdd;
      if (getPermissionBtn('newTemplateAdd')) {
        // 模板引擎
        state.dialogTemplateOnline = true;
      } else if (getPermissionBtn('templateAdd')) {
        // 附件上传版
        state.dialogTemplateUpload = true;
      }
    };
    const handleHtmlUpload = file => {
      if (file.type !== 'text/html') {
        ElMessage.error('请选择正确的HTML模板文件');
        return false;
      }
    };
    const back = () => {
      router.push({
        path: '/testConfiguration/testItems',
        query: {
          categoryId: route.query.categoryId,
          code: route.query.code,
          tenantId: getLoginInfo().tenantId
        }
      });
    };

    const showHtml = computed(() => {
      return state.html.toString().length > 0;
    });

    const onUnreleasedTemplateClick = designTemplateId => {
      gotoDesignTemplatePage('update', { designTemplateId });
    };

    const gotoDesignTemplatePage = (type, query) => {
      const { DEV, VITE_TEMPLATE_EDITOR, VITE_TEMPLATE_EDITOR_ORIGIN } = import.meta.env;
      const templateEditorPagePath = router.resolve({
        path: `${VITE_TEMPLATE_EDITOR}/design/${type}`,
        query: {
          capabilityId: route.query.capabilityId,
          capabilityName: route.query.capabilityName,
          capabilityNumber: route.query.capabilityNumber,
          version: '',
          fileNo: '',
          description: '',
          ...query
        }
      }).href;
      window.location.href = (DEV ? VITE_TEMPLATE_EDITOR_ORIGIN : '') + templateEditorPagePath;
    };
    const closeOnlineDialog = () => {
      state.dialogTemplateOnline = false;
    };
    const closeUploadDialog = isRefresh => {
      state.dialogTemplateUpload = false;
      if (isRefresh) {
        if (!state.isadd) {
          getExcelInfo();
        } else {
          getExceldata();
        }
      }
    };
    return {
      ...toRefs(state),
      showHtml,
      emptyImg,
      closeOnlineDialog,
      closeUploadDialog,
      handleUpload,
      excel,
      handleHtmlUpload,
      getExceldata,
      onUnreleasedTemplateClick,
      changeCapability,
      getExcelInfo,
      handleOff,
      getPermissionBtn,
      toImg,
      back,
      uploadRef
    };
  }
};
</script>

<style lang="scss" scoped>
.header-flex {
  .page-title {
    max-width: 60%;
    overflow: auto;
    .title {
      display: inline-block;
      width: 80px;
      min-width: 80px;
    }
  }
  .btn-group {
    max-width: 680px;
  }
}
.page-header-group {
  padding-top: 10px;
  .el-col {
    text-align: left;
    margin: 4px 0;
    .title {
      color: $tes-font2;
    }
  }
  .item {
    display: flex;
    align-items: center;
    .txt {
      max-width: calc(100% - 90px);
    }
  }
}
</style>
