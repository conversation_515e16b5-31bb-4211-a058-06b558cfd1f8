<template>
  <!-- 老化箱管理 -->
  <ListLayout :has-button-group="getPermissionBtn('AgingAddDevice')" :has-search-panel="false" :has-quick-query="false">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" size="large" class="sample-order-form" @submit.prevent>
        <el-form-item prop="condition">
          <el-input
            v-model="formInline.condition"
            v-trim
            v-focus
            size="medium"
            placeholder="请输入设备编号/设备名称"
            class="ipt-360"
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter="onSubmit"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="large" @click="onSubmit">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button type="primary" size="large" icon="el-icon-plus" @click="handleAddDevice('AgingAddDevice')"
        >添加设备</el-button
      >
    </template>
    <div v-loading="listLoading" class="datacollection-list">
      <el-row v-if="tableList.length !== 0" :gutter="24">
        <el-col v-for="(item, index) in tableList" :key="index" :xs="12" :sm="8" :md="6" :lg="6" :xl="6">
          <div class="grid-content collection-content" @click="handlePush(item)">
            <div class="content-bd">
              <div class="item-r">
                <div class="item-title" :class="statusList[item.status]">
                  <div>
                    <span class="iconfont tes-Vector1 icon-status" />
                    <span class="title-c">{{ item.boxName }}</span>
                    <el-tag v-if="item.status === 'Standby'" size="small" class="title-status" type="warning"
                      >待机</el-tag
                    >
                    <el-tag v-if="item.status === 'Running'" size="small" class="title-status" type="success"
                      >运行</el-tag
                    >
                    <el-tag v-if="item.status === 'Maintenance'" size="small" class="title-status">检修</el-tag>
                    <el-tag v-if="item.status === 'Fault'" size="small" class="title-status" type="danger">故障</el-tag>
                  </div>
                  <el-icon :size="16" class="icon" @click.stop="handleDelete(item.id)">
                    <i class="el-icon-delete" />
                  </el-icon>
                </div>
                <div class="item-txt"><span class="label-txt">设备编号：</span>{{ item.deviceNumber }}</div>
                <div class="item-txt"><span class="label-txt">已放置样品数：</span>{{ item.sampleSum || '--' }}</div>
              </div>
            </div>
            <div class="content-ft">
              <el-row>
                <el-col :span="11">
                  <el-button class="bg-btn btn-hover" size="small" @click.stop="handleRapidSampling(item)">
                    <i class="el-icon-tickets" @keyup.prevent @keydown.enter.prevent />快速取样</el-button
                  >
                </el-col>
                <el-col :span="2">
                  <el-divider direction="vertical" />
                </el-col>
                <el-col :span="11">
                  <el-button class="bg-btn btn-hover" size="small" @click.stop="handleLayout(item)">
                    <i class="el-icon-pie-chart" /> 快速放样</el-button
                  >
                </el-col>
              </el-row>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-empty v-else class="empty" :image="emptyImg" description="暂无数据" />
    </div>
    <template #other>
      <!-- 添加重点设备 -->
      <AddMultipleEquipment
        :show="dialogSelectEquipment"
        :select-info="tableList"
        @select-data="handleSelectData"
        @close="handleCloseDialog"
      />
      <!-- 快速取样 -->
      <DialogRapidSampling
        :dialog-visible="dialogSampling"
        :select-info="selectInfo"
        @closeDialog="closeSamplingDialog"
      />
      <!-- 快速放样 -->
      <DialogQuickLayout :dialog-visible="dialogLayout" :box-id="selectInfo.id" @closeDialog="closeLayoutDialog" />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, toRefs } from 'vue';
import ListLayout from '@/components/ListLayout';
import { formatDate } from '@/utils/formatTime';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import AddMultipleEquipment from '@/components/BusinessComponents/AddMultipleEquipment';
import { getDeviceBoxList, deviceBoxAddBatch, deleteBatch } from '@/api/aging-chamber';
import emptyImg from '@/assets/img/empty-data.png';
import DialogRapidSampling from './components/DialogRapidSampling.vue';
import DialogQuickLayout from './components/DialogQuickLayout.vue';
import router from '@/router/index.js';
import { ElMessage, ElMessageBox } from 'element-plus';

export default {
  name: 'AgingChamber',
  components: { ListLayout, AddMultipleEquipment, DialogRapidSampling, DialogQuickLayout },
  setup() {
    // const _ = inject('_')
    const state = reactive({
      tableList: [],
      listLoading: false,
      dialogSampling: false,
      dialogLayout: false,
      detailData: [],
      selectInfo: {}, // 选中的设备
      dialogSelectEquipment: false,
      formInline: {
        page: '1',
        limit: '-1'
      },
      statusList: {
        Standby: 'warning',
        Running: 'success',
        Maintenance: '',
        Fault: 'danger',
        Scrapped: 'info'
      }
    });

    const getList = () => {
      state.listLoading = true;
      getDeviceBoxList(state.formInline)
        .then(res => {
          if (res.data.code === 200) {
            state.tableList = [];
            res.data.data.list.forEach(item => {
              state.tableList.push(item);
            });
          }
        })
        .finally(() => (state.listLoading = false));
    };
    getList();

    function onSubmit() {
      getList();
    }

    function reset() {
      state.formInline = {
        page: '1',
        limit: '-1'
      };
      getList();
    }

    const handleRapidSampling = item => {
      state.dialogSampling = true;
      state.selectInfo = item;
    };
    const handleCloseDialog = () => {
      state.dialogSelectEquipment = false;
    };
    const handleSelectData = async val => {
      if (val?.length) {
        const deviceIdList = val.map((item, index) => {
          return item.id;
        });
        state.listLoading = true;
        const { data } = await deviceBoxAddBatch(deviceIdList).finally(() => (state.listLoading = false));
        if (data) {
          ElMessage.success('添加设备成功！');
          getList();
        }
      }
      state.dialogSelectEquipment = false;
    };
    const search = () => {};
    // 快速放样
    const handleLayout = row => {
      state.dialogLayout = true;
      state.selectInfo = row;
    };
    // 添加设备
    const handleAddDevice = () => {
      state.dialogSelectEquipment = true;
    };
    const closeSamplingDialog = isRefresh => {
      state.dialogSampling = false;
      if (isRefresh) {
        getList();
      }
    };
    const closeLayoutDialog = isRefresh => {
      state.dialogLayout = false;
      if (isRefresh) {
        getList();
      }
    };
    // 详情页
    const handlePush = item => {
      router.push({
        name: 'AgingChamberDetail',
        query: {
          boxId: item.id,
          boxName: item.boxName,
          deviceId: item.deviceId,
          deviceNumber: item.deviceNumber,
          boxStatus: item.boxStatus
        }
      });
    };
    /** 删除设备 */
    const handleDelete = id => {
      ElMessageBox({
        title: '删除',
        message: '是否确认删除？',
        confirmButtonText: '确定',
        showCancelButton: false,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(async () => {
          state.listLoading = true;
          const { data } = await deleteBatch([id]).finally((state.listLoading = false));
          if (data) {
            ElMessage.success('删除成功！');
            getList();
          }
        })
        .catch(() => {});
    };
    return {
      ...toRefs(state),
      handleCloseDialog,
      handleDelete,
      handlePush,
      closeSamplingDialog,
      closeLayoutDialog,
      emptyImg,
      handleRapidSampling,
      handleSelectData,
      handleLayout,
      getPermissionBtn,
      handleAddDevice,
      drageHeader,
      getNameByid,
      formatDate,
      getList,
      search,
      onSubmit,
      reset
    };
  },
  computed: {},
  created() {}
};
</script>
<style scoped lang="scss">
.datacollection {
  padding: 16px 24px 0;
  box-sizing: border-box;

  .sample-order-form {
    text-align: left;
  }
}

.datacollection-list {
  overflow-y: auto;
  overflow-x: hidden;
  padding-top: 4px;
  .el-row {
    margin-bottom: 20px;
    margin-right: 0 !important;
    margin-left: 0 !important;
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.collection-content {
  background: $background-color;
  border-radius: 8px;
  margin-bottom: 24px;
  padding: 16px 20px 4px;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.12);
  cursor: pointer;
  overflow: hidden;
  .content-ft {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    .el-row {
      align-items: center;
    }
    .el-col {
      display: flex;
      &:first-of-type {
        justify-content: flex-end;
      }
      &:last-of-type {
        justify-content: flex-start;
      }
    }
  }

  .content-bd {
    overflow: hidden;
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 10px;
    .item-r {
      font-size: 14px;
      .item-title.info {
        .icon-status {
          background: $tes-grey;
        }
      }
      .item-title.success {
        .icon-status {
          background: $tes-green;
        }
      }
      .item-title.warning {
        .icon-status {
          background: $tes-yellow;
        }
      }
      .item-title.danger {
        .icon-status {
          background: $tes-red;
        }
      }
      .item-title {
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        justify-items: center;
        align-items: center;

        & > span {
          display: inline-block;
        }
        .title-c {
          font-size: 18px;
          color: #303133;
          overflow: hidden;
          font-weight: bold;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: left;
          flex: auto;
        }
        .icon-status {
          width: 20px;
          height: 20px;
          border-radius: 2px;
          font-size: 12px;
          margin-right: 10px;
          background: $tes-primary;
          color: #fff;
          display: inline-flex;
          justify-content: center;
          align-items: center;
        }
      }
      .item-txt {
        width: 100%;
        margin-top: 6px;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: left;
        display: block;
        overflow: hidden;
        color: #303133;
        .label-txt {
          display: inline-block;
          width: 88px;
          color: #909399;
        }
        .label-space {
          color: #909399;
        }
      }

      & > div {
        line-height: 1.4;
        text-align: left;
        color: #606266;
      }
    }
  }
}
.btn-right {
  border-radius: 8px;
}
.btn-hover {
  cursor: pointer;
  font-size: 14px;
  i {
    margin-right: 2px;
  }
}
.btn-hover:hover,
.btn-hover:focus {
  background-color: #fff !important;
  color: $tes-primary;
}
.empty {
  margin-top: 15vh;
}
.bg-btn {
  padding: 0;
  font-weight: normal;
  border: none;
}

.pop-box {
  padding: 10px;
  .pop-title {
    color: #909399;
    margin-bottom: 4px;
  }
}
.icon {
  color: $tes-primary;
  cursor: pointer;
}

:deep(.page-list-main .el-container .el-main) {
  margin: 0 14px;
  background: transparent;
}

:deep(.el-container .el-main .page-main) {
  padding: 0;
  background: transparent;
}
</style>
