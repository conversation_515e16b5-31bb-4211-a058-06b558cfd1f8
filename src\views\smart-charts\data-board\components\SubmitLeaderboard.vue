<template>
  <!-- 近15天提交项目排行榜 -->
  <!-- 近15天提交报告排行榜 -->
  <div class="box-top">
    <h1 class="inBlock">{{ titleJson[pageType] }}</h1>
  </div>
  <div class="box-Center">
    <div class="topTime textRight">{{ formatDate(recentlyFifteen) }} ~ {{ formatDate(new Date()) }}</div>
    <ul v-loading="loading" element-loading-background="#7AD0FF" class="phb">
      <li v-for="(item, index) in leaderboard" :key="Object.keys(item)[0]">
        <img v-if="index === 0" src="@/assets/img/dataBoard/goldMedal.png" alt="" />
        <img v-if="index === 1" src="@/assets/img/dataBoard/silverMedal.png" alt="" />
        <img v-if="index === 2" src="@/assets/img/dataBoard/bronzeMedal.png" alt="" />
        <span class="name">{{ getNameByid(Object.keys(item)[0]) }}</span>
        <el-progress
          :percentage="getProportion(firstNumber, Object.values(item)[0])"
          :format="() => Object.values(item)[0]"
        />
      </li>
    </ul>
  </div>
</template>

<script>
import { reactive, toRefs, onBeforeUnmount, watch } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getNameByid } from '@/utils/common';
import { getSubmitReportData, getfindSubmitCapabilityData } from '@/api/dataBoard';

export default {
  name: 'DataBoard',
  components: {},
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  setup(props, context) {
    const state = reactive({
      recentlyFifteen: new Date().getTime() - 3600 * 1000 * 24 * 14, // 最近15天
      titleJson: {
        submitProject: '近15天提交项目排行榜',
        submitReport: '近15天提交报告排行榜'
      },
      pageType: props.type,
      loading: false, // 提交项目排行榜loading
      firstNumber: 0, // 提交报告排行榜最高数字
      leaderboard: [], // 提交报告排行榜列表
      timer: null
    });
    watch(props, newValue => {
      state.pageType = props.type;
    });
    onBeforeUnmount(() => {
      removeTimer();
    });
    const setTimeCenterTop = () => {
      state.timeCenterTop = setInterval(() => {
        getSubmitList();
      }, 10000);
    };
    // 近15天提交项目排行榜
    const getSubmitList = isFirst => {
      if (state.pageType === 'submitProject') {
        state.loading = true;
        getfindSubmitCapabilityData().then(res => {
          state.loading = false;
          if (res) {
            const data = res.data.data;
            state.leaderboard = data;
            if (data.length > 0) {
              state.firstNumber = Object.values(data[0])[0];
            }
          }
        });
      } else {
        state.loading = true;
        getSubmitReportData().then(res => {
          state.loading = false;
          if (res) {
            const data = res.data.data;
            state.leaderboard = data;
            if (data.length > 0) {
              state.firstNumber = Object.values(data[0])[0];
            }
          }
        });
      }
      if (isFirst) {
        setTimeCenterTop();
      }
    };
    getSubmitList(true);
    const removeTimer = () => {
      if (state.timeCenterTop) {
        clearInterval(state.timeCenterTop);
        state.timeCenterTop = null;
      }
    };

    // 计算百分占比
    const getProportion = (cardinal, comparisonVal) => {
      return (comparisonVal / cardinal) * 100;
    };
    return {
      ...toRefs(state),
      getNameByid,
      setTimeCenterTop,
      removeTimer,
      getProportion,
      formatDate,
      getSubmitList
    };
  }
};
</script>
<style lang="scss" scoped>
@import '../data-board.scss';
:deep(.el-progress__text) {
  min-width: 0;
  width: auto;
  float: right;
  text-align: right;
  font-weight: 700;
}
:deep(.el-progress-bar) {
  display: inline-block;
  width: 80%;
}
:deep(.el-progress) {
  width: 82%;
  display: inline-block;
  position: absolute;
  top: 3px;
}
:deep(.el-progress-bar__outer) {
  background-color: transparent;
}
</style>
