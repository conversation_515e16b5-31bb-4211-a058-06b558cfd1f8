<template>
  <ul class="testing-list">
    <li
      v-for="(row, index) in data"
      :key="index"
      :class="'testing-list__item' + (selectedIndex === index ? ' active' : '')"
      @click="handleClickRow(row, index)"
    >
      <div v-if="isProjectItemType" class="testing-list__sample-item">
        <span v-if="row.retestSourceId !== ''" class="testing-icon text-copy">复</span>
        <span v-if="row.isRetest === 1" class="testing-icon text-origin">源</span>
        <span v-if="row.isEditMore > 0" class="testing-icon text-change">改</span>
        <span v-if="row.isBack === 1" class="testing-icon text-back">退</span>
        <span v-if="row.isManual === 1" class="testing-icon text-info">增</span>
        <span v-if="row.isSpecial === 1" class="testing-icon text-copy">特</span>
        <span>{{ row.capabilityName }}</span>
      </div>
      <div v-if="isSampleItemType" class="testing-list__project-item">
        <div class="testing-icon-container">
          <span v-if="row.retestSourceId !== ''" class="testing-icon text-copy">复</span>
          <span v-if="row.isRetest === 1" class="testing-icon text-origin">源</span>
          <span v-if="row.isEditMore > 0" class="testing-icon text-change">改</span>
          <span v-if="row.isBack === 1" class="testing-icon text-back">退</span>
          <!-- <span v-if="row.isManual === 1" class="testing-icon text-info">手</span>
          <span v-if="row.isSpecial === 1" class="testing-icon text-copy">特</span> -->
        </div>
        <div class="testing-list__project-row">
          <span class="testing-list__project-label">样品编号 : </span>
          <span class="testing-list__project-num">{{ row.secSampleNum }}</span>
        </div>
        <div class="testing-list__project-row">
          <span class="testing-list__project-label">型号规格 :</span>
          <span class="testing-list__project-type">{{ row.prodType || '--' }}</span>
        </div>
      </div>
    </li>
    <slot name="append" />
  </ul>
</template>

<script>
import { reactive, toRefs, computed, watch } from 'vue';

export default {
  name: 'TestingList',
  props: {
    itemType: {
      type: String,
      required: true,
      validator(value) {
        return ['sample', 'project'].includes(value) > -1;
      }
    },
    data: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['row-click'],
  setup(props, context) {
    const state = reactive({
      selectedIndex: -1,
      currentRow: {}
    });
    const isSampleItemType = computed(() => props.itemType === 'sample');
    const isProjectItemType = computed(() => props.itemType === 'project');

    watch(
      () => props.data,
      newData => {
        if (state.currentRow.capabilityId) {
          const index = newData.findIndex(item => item.capabilityId === state.currentRow.capabilityId);
          if (index > -1) {
            state.selectedIndex = index;
          }
        }
      }
    );

    const handleClickRow = (row, index) => {
      state.selectedIndex = index;
      context.emit('row-click', row, index);
    };

    const setCurrentRow = row => {
      state.currentRow = row;
      const index = props.data.findIndex(item => item.capabilityId === row.capabilityId);
      if (index > -1) {
        state.selectedIndex = index;
      }
    };

    return {
      ...toRefs(state),
      isSampleItemType,
      isProjectItemType,
      handleClickRow,
      setCurrentRow
    };
  }
};
</script>

<style lang="scss" scoped>
.testing-list {
  padding: 20px;
  overflow-y: auto;
  position: relative;
}

.testing-list__item {
  margin-bottom: 10px;
  border: 1px solid transparent;
  border-radius: 8px;
  background-color: #ffffff;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.12);
  text-align: left;
  transition: all 0.5s ease;
  cursor: pointer;

  &:last-child {
    margin-bottom: 0;
  }

  &.active,
  &:hover {
    // border: 1px solid var(--tesPrimary);
    box-shadow: 0px 0px 4px 0px var(--tesPrimary);
    background-color: var(--menuHover);
  }
}

.testing-list__sample-item {
  padding: 8px 16px;
  line-height: 1.5;
}

.testing-list__project-item {
  padding: 12px 16px;
  position: relative;
}

.testing-list__project-row {
  display: flex;
}

.testing-list__project-label {
  flex-shrink: 0;
  color: #909399;
  font-size: 12px;
}

.testing-list__project-num,
.testing-list__project-type {
  flex-grow: 1;
  margin-left: 10px;
  font-size: 14px;
}

.testing-list__project-num {
  color: var(--tesPrimary);
  font-weight: 500;
}

.testing-list__project-type {
  color: #303133;
}

.testing-icon-container {
  position: absolute;
  top: 12px;
  right: 16px;
}
</style>
