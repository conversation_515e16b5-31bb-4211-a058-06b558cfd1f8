<template>
  <!-- 老化箱放样记录 -->
  <ListLayout :has-button-group="true">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="condition">
          <el-input
            v-model="formInline.condition"
            v-trim
            v-focus
            :placeholder="`请输入${fieldTips}`"
            class="ipt-360"
            prefix-icon="el-icon-search"
            size="large"
            clearable
            @keyup.enter="getList()"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="getList()">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button class="fr" type="primary" size="large" :loading="listLoading" @click="exportExcel()"
        ><span class="iconfont tes-task-issued" @keyup.prevent @keydown.enter.prevent /> 导出</el-button
      >
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="16">
          <el-radio-group v-model="formInline.status" size="small" @change="changeRadio">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button :label="0">进行中</el-radio-button>
            <el-radio-button :label="1">已取出</el-radio-button>
          </el-radio-group>
        </el-col>
        <el-col :span="8" style="text-align: right">
          <TableColumnView binding-menu="AgingChamberList" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="tableList"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="item.fieldType === fieldTypesEnum.Person">
              <UserTag :name="getNameByid(row[item.fieldKey]) || row[item.fieldKey] || '--'" />
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Date">
              <span>{{ formatDate(row[item.fieldKey] || '--') }}</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Status">
              <el-tag size="small" effect="dark" :type="statusJSON[item.fieldKey][row[item.fieldKey]].type">{{
                statusJSON[item.fieldKey][row[item.fieldKey]].label
              }}</el-tag>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Custom">
              <div v-if="item.fieldKey == 'fzzq'">
                <span v-if="row.periodHour">{{ row.periodHour }}小时</span>
                <span v-if="row.periodMinute">{{ row.periodMinute }}分钟</span>
              </div>
              <div v-if="item.fieldKey == 'fzsj'">
                <div>{{ row.startDateTime }} ~ {{ row.endDateTime }}</div>
              </div>
            </template>
            <span v-else>{{ row[item.fieldKey] || '--' }}</span>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
  </ListLayout>
</template>

<script>
import { onMounted, reactive, ref, toRefs } from 'vue';
import Pagination from '@/components/Pagination';
import UserTag from '@/components/UserTag';
import { sampleDetailList } from '@/api/aging-chamber';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import ListLayout from '@/components/ListLayout';
import { colWidth } from '@/data/tableStyle';
import TableColumnView from '@/components/TableColumnView';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';

export default {
  name: 'AgingChamberList',
  components: {
    Pagination,
    UserTag,
    ListLayout,
    TableColumnView
  },
  setup() {
    const state = reactive({
      editFrom: ref(),
      statusJSON: {
        sampleStatus: {
          0: { type: 'warning', label: '进行中' },
          1: { type: 'success', label: '已取出' }
        }
      },
      type: 'info',
      formInline: {
        tableQueryParamList: []
      },
      searchForm: {
        dateRange: []
      },
      tableColumns: [],
      searchFieldList: [],
      tableAll: [],
      fieldTips: '',
      tableList: [],
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: ''
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      tableRef: ref()
    });
    const getList = query => {
      const params = { ...state.formInline };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.tableLoading = true;
      sampleDetailList(params).then(res => {
        state.tableLoading = false;
        if (res !== false) {
          state.total = res.data.data.totalCount;
          state.tableList = res.data.data.list;
        }
      });
    };
    getList();
    const getListAll = async () => {
      state.listLoading = true;
      const { data } = await sampleDetailList({ limit: '-1', page: '1' }).finally((state.listLoading = false));
      if (data) {
        state.tableAll = data.data.list;
      }
    };
    onMounted(() => {
      getListAll();
      getList();
    });
    // 重置
    function reset() {
      state.formInline = { params: '' };
      state.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: null
      };
      getList();
    }
    // 排序
    const sortChange = data => {
      const { prop, order } = data;
      state.listQuery.orderBy = prop;
      if (order === 'ascending') {
        state.listQuery.isAsc = true;
      } else if (order === 'descending') {
        state.listQuery.isAsc = false;
      } else {
        state.listQuery.isAsc = null;
      }
    };
    // 切换tab
    const changeRadio = value => {
      getList();
    };
    // 导出
    const exportExcel = () => {
      var tHeader = [];
      var filterVal = [];
      var fileName = '老化箱放样记录';
      tHeader = state.tableColumns.map(item => item.fieldName);
      filterVal = state.tableColumns.map(item => item.fieldKey);
      state.listLoading = true;
      import('@/utils/Export2Excel').then(excel => {
        const data = formatJson(filterVal, state.tableAll);
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: fileName,
          autoWidth: true,
          bookType: 'xlsx'
        });
        state.listLoading = false;
      });
    };
    const formatJson = (filterVal, jsonData) => {
      return jsonData.map(v =>
        filterVal.map(j => {
          if (j === 'createBy') {
            return getNameByid(v[j]) || v[j];
          } else if (j === 'sampleStatus') {
            return state.statusJSON['sampleStatus'][v[j]].label || v[j];
          } else if (j === 'fzsj') {
            return `${v.startDateTime} ~ ${v.endDateTime}`;
          } else if (j === 'fzzq') {
            return `${v.periodHour || 0}小时 ${v.periodMinute || 0}分钟`;
          } else {
            return v[j];
          }
        })
      );
    };
    const onUpdateColumns = columns => {
      state.tableKey = state.tableKey + 1;
      state.tableColumns = columns;
      state.searchFieldList = columns.filter(item => {
        return item.isQuery == 1;
      });
      state.fieldTips = state.searchFieldList.map(item => item.fieldName).join('/');
    };

    return {
      getList,
      drageHeader,
      exportExcel,
      formatDate,
      getNameByid,
      changeRadio,
      sortChange,
      ...toRefs(state),
      reset,
      getPermissionBtn,
      colWidth,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  }
};
</script>
<style lang="scss" scoped>
.el-form {
  .el-form-item {
    margin-bottom: 0;
  }
}

.small-dialog {
  .dialog-main {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    img {
      width: 78%;
      margin-bottom: 20px;
    }
  }
}
.blue-color {
  color: $tes-primary;
  cursor: pointer;
}
</style>
