<template>
  <div v-loading="loading" class="templatePdf">
    <div class="moban-wrap">
      <div ref="excel" class="boxTransverse">
        <print-excel
          ref="ItemRef"
          :curr-index="currIndex"
          :json-data="jsonDataListItem"
          @handleData="handleData"
          @setImg="setImg"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, ref } from 'vue';
import { useRoute } from 'vue-router';
import { archivalExperimentFileUpload } from '@/api/order';
import printExcel from '@/views/excelComponents/printExcel';
import { batchPrint } from '@/api/excel';
import { decryptCBC } from '@/utils/ASE';

export default {
  name: 'TemplatePdf',
  components: { printExcel },
  setup(props, ctx) {
    const route = useRoute();
    const state = reactive({
      experimentId: route.query.experimentId,
      showType: 1,
      loading: true,
      experimentData: {},
      i: 0,
      currIndex: 0,
      sampleId: route.query.sampleId, // 样品ID
      printList: route.query.printlist.split(','),
      imgList: [],
      printListAt: [],
      pdfFile: [],
      jsonDataListItem: {},
      jsonDataList: [],
      dataValue: {
        dialogSubmit: false
      },
      url: '',
      dialogSubmit: false,
      formData: {
        realOwnerIds: [],
        reviewerId: '',
        date: []
      },
      deviceList: []
    });

    // 保存
    const excel = ref(null);
    const ItemRef = ref(null);
    const handleData = thisValue => {
      state.experimentData = thisValue;
    };
    const setImg = detailInfo => {
      state.imgList.push(detailInfo);
      translatePdf(detailInfo, state.currIndex);
      if (state.imgList.length !== state.jsonDataList.length) {
        state.currIndex += 1;
        state.jsonDataListItem = state.jsonDataList[state.currIndex];
      }
    };
    const translatePdf = async (detailInfo, index) => {
      const file = base64ToFile(detailInfo.imageBase64, `${state.printList[index]}`);
      const params = new FormData();
      params.append('file', file);
      params.append('experimentId', `${state.printList[index]}`);
      params.append('sampleId', state.sampleId);
      archivalExperimentFileUpload(params).then(res => {
        if (res) {
          state.pdfFile.push(true);
        } else {
          state.pdfFile.push(false);
        }
        printNew();
      });
    };
    const base64ToFile = (base64, fileName) => {
      // 将base64按照 , 进行分割 将前缀  与后续内容分隔开
      const data = base64.split(',');
      // 利用正则表达式 从前缀中获取图片的类型信息（image/png、image/jpeg、image/webp等）
      const type = data[0].match(/:(.*?);/)[1];
      // 从图片的类型信息中 获取具体的文件格式后缀（png、jpeg、webp）
      const suffix = type.split('/')[1];
      // 使用atob()对base64数据进行解码  结果是一个文件数据流 以字符串的格式输出
      const bstr = window.atob(data[1]);
      // 获取解码结果字符串的长度
      let n = bstr.length;
      // 根据解码结果字符串的长度创建一个等长的整形数字数组
      // 但在创建时 所有元素初始值都为 0
      const u8arr = new Uint8Array(n);
      // 将整形数组的每个元素填充为解码结果字符串对应位置字符的UTF-16 编码单元
      while (n--) {
        // charCodeAt()：获取给定索引处字符对应的 UTF-16 代码单元
        u8arr[n] = bstr.charCodeAt(n);
      }
      // 利用构造函数创建File文件对象
      // new File(bits, name, options)
      const file = new File([u8arr], `${fileName}.${suffix}`, {
        type: type
      });
      // 将File文件对象返回给方法的调用者
      return file;
    };
    const printus = async list => {
      state.jsonDataListItem = list[state.currIndex];
      state.loading = false;
      state.dialogPrint = false;
    };
    // 获取数据
    const getAllList = () => {
      return new Promise((resolve, reject) => {
        batchPrint(state.printList).then(res => {
          if (res !== false) {
            state.printListAt = res.data.data;
            state.imgList = [];
            var len = state.printList.length;
            state.printListAt.forEach(async (item, index) => {
              const jsonDataitem = { ...item.value };
              jsonDataitem.excelHtml = decryptCBC(item.html);
              jsonDataitem.showType = item.showType;
              jsonDataitem.index = item.index;
              jsonDataitem.experimentData = item.value;
              jsonDataitem.realReviewerIdimg = jsonDataitem.experimentData.reviewerSignUrl?.split(',');
              jsonDataitem.realOwnerIdsimgs = jsonDataitem.experimentData.ownerSignUrls?.split(',');
              state.jsonDataList.push(jsonDataitem);
              if (index === len - 1) {
                resolve(state.jsonDataList);
              }
            });
          }
        });
      });
    };
    // 打印
    const printNew = () => {
      if (state.pdfFile.length === state.printList.length) {
        window.open('about:blank', '_self').close();
      }
    };
    return {
      ...toRefs(state),
      excel,
      ItemRef,
      printNew,
      handleData,
      base64ToFile,
      setImg,
      getAllList,
      printus,
      translatePdf
    };
  },
  computed: {},
  async created() {
    const list = await this.getAllList();
    this.printus(list);
  }
};
</script>

<style lang="scss" scoped>
.templatePdf {
  margin: 0 1rem;
  padding: 10px 14px 10px 1rem;

  :deep(.el-table__body-wrapper) {
    margin-bottom: 0;
  }
  .boxTransverse {
    margin: 10px auto;
    width: 1123px !important;
  }
}

.moban-wrap {
  width: 100%;
  height: calc(100vh - 202px);
  margin-top: 30px;
  overflow-y: auto;
  text-align: center;
}

.print-box-show {
  width: 684px !important;
  margin: 0 auto;
  background-color: #fff;
  box-shadow: 0 0.285714rem 0.857143rem 0.285714rem rgba(0, 0, 0, 0.08),
    0 0.142857rem 0.285714rem -0.142857rem rgba(0, 0, 0, 0.16);
  filter: drop-shadow(0 0.214286rem 0.571429rem rgba(0, 0, 0, 0.12));
}
.box {
  width: 684px !important;
  margin: 0 auto;
  background-color: #fff;
  box-shadow: 0 0.285714rem 0.857143rem 0.285714rem rgba(0, 0, 0, 0.08),
    0 0.142857rem 0.285714rem -0.142857rem rgba(0, 0, 0, 0.16);
  filter: drop-shadow(0 0.214286rem 0.571429rem rgba(0, 0, 0, 0.12));
  // height: 1123px;
  display: block;
}
</style>
