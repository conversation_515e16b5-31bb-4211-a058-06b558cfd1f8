<template>
  <el-drawer
    v-model="showDrawer"
    :title="titles"
    direction="rtl"
    :before-close="handleClose"
    size="50%"
    destroy-on-close
    :close-on-click-modal="false"
    custom-class="page-drawer"
  >
    <el-form
      ref="formRef"
      v-loading="drawerLoading"
      :model="formInline"
      class="formDataSample"
      :inline="true"
      :rules="addRules"
      :label-width="labelWidth"
      :label-position="position"
    >
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="样品名称：" prop="sampleName">
            <el-input v-if="!isCheck" v-model="formInline.sampleName" size="small" />
            <span v-else> {{ formInline.sampleName || '--' }} </span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="样品数量：" prop="sampleNum">
            <el-input-number
              v-if="!isCheck"
              v-model="formInline.sampleNum"
              controls-position="right"
              :step="1"
              :min="0"
              size="small"
              style="width: 60%"
            />
            <span v-else> {{ formInline.sampleNum || '--' }} </span>
            <el-select
              v-if="!isCheck"
              v-model="formInline.sampleUnit"
              placeholder="请选择"
              size="small"
              style="width: 40%; padding-left: 8px"
            >
              <el-option-group v-for="item in optionsArray" :key="item.label" :label="item.label">
                <el-option
                  v-for="val in item.group"
                  :key="val.id"
                  :label="val.name"
                  :value="val.code"
                  :disabled="val.status !== 1"
                >
                  <span style="float: left">{{ val.name }}</span>
                  <span v-if="val.status !== 1" class="fr" style="color: #f56c6c">已停用</span>
                </el-option>
              </el-option-group>
            </el-select>
            <span v-else>{{ filterUnit(formInline.sampleUnit) }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="型号规格：" prop="model">
            <el-input v-if="!isCheck" v-model="formInline.model" size="small" />
            <span v-else> {{ formInline.model || '--' }} </span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="存放地：">
            <el-select
              v-if="!isCheck"
              v-model="formInline.storage"
              placeholder="请选择"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option v-for="item in voltageList" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
            <div v-else class="nowrap">
              {{ filterStorage(formInline.storage) || '--' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检测依据：" prop="inspectBaseOn">
            <el-input v-if="!isCheck" v-model="formInline.inspectBaseOn" size="small" />
            <span v-else> {{ formInline.inspectBaseOn || '--' }} </span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检测费用(￥)：" prop="inspectCost">
            <el-input-number
              v-if="!isCheck"
              v-model="formInline.inspectCost"
              controls-position="right"
              :step="1"
              :min="0"
              :precision="2"
              style="width: 100%"
              size="small"
            />
            <span v-else> {{ formInline.inspectCost || '--' }} </span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="试验类型：" prop="inspectType">
            <el-radio-group v-model="formInline.inspectType">
              <el-radio :label="0" size="large">型式试验</el-radio>
              <el-radio :label="1" size="large">全性能</el-radio>
              <el-radio :label="2" size="large">部分性能</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col v-if="tenantType === 0" :span="12">
          <el-form-item label="样品等级：" prop="sampleGrade">
            <el-radio-group v-model="formInline.sampleGrade" @change="changStrategyTestItems">
              <el-radio label="0" size="large">A级</el-radio>
              <el-radio label="1" size="large">B级</el-radio>
              <el-radio label="2" size="large">C级</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col v-else :span="12" />
        <el-col v-if="tenantType === 0" :span="24">
          <el-form-item label="等级对应检测项目：" prop="testItems">
            <el-table :data="testItems" fit border size="small" class="dark-table base-table add-process-table">
              <el-table-column type="index" label="序号" :width="50" align="center" />
              <el-table-column label="项目编号" property="externalCapabilityCode" min-width="50" />
              <el-table-column label="项目名称" property="externalCapabilityName" min-width="100" />
            </el-table>
          </el-form-item>
        </el-col>

        <el-col v-if="tenantType !== 0" :span="24">
          <el-form-item label="检测项目：" prop="inspectionProject">
            <el-input v-if="!isCheck" v-model="formInline.inspectionProject" placeholder="请输入检测项目" class="" />
            <div v-else class="nowrap">
              {{ formInline.inspectionProject || '--' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注：" prop="remark">
            <el-input
              v-if="!isCheck"
              v-model="formInline.remark"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
              maxlength="300"
              show-word-limit
            />
            <div v-else class="nowrap">{{ formInline.remark || '--' }}</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div v-if="!isCheck" class="drawer-footer">
      <el-button type="primary" :loading="drawerLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
        >保存</el-button
      >
      <el-button :loading="drawerLoading" @click="handleClose">取消</el-button>
    </div>
  </el-drawer>
</template>

<script>
import { ref, watch, reactive, toRefs, computed } from 'vue';
import {
  saveTaskSampleInfo,
  getSgTestItemByStrategy,
  getSavedTestItemsBySampleId,
  saveTaskSampleTestItems
} from '@/api/task-registration';
import { ElMessage } from 'element-plus';
import { getDictionary } from '@/api/dictionary';
import { useStore } from 'vuex';
// import { useRoute } from 'vue-router'

export default {
  name: 'DrawerAddSample',
  components: {},
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    taskId: {
      type: String,
      default: ''
    },
    materialCode: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      required: true
    },
    editData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    // const { appContext } = getCurrentInstance()
    // const bus = appContext.config.globalProperties.bus
    // console.log(appContext)
    // console.log(bus)
    // const route = useRoute()
    const store = useStore().state;
    const datas = reactive({
      showDrawer: false,
      titles: '新增样品',
      formRef: ref(),
      formInline: {
        sampleName: '',
        sampleNum: 0,
        sampleUnit: '',
        model: '',
        storage: '',
        inspectBaseOn: '',
        inspectCost: 0,
        inspectType: 0,
        inspectionProject: '',
        remark: '',
        sampleGrade: '0'
      },
      testItems: [],
      position: 'top',
      labelWidth: '110px',
      drawerLoading: false,
      addRules: {
        sampleName: [{ required: true, message: '请输入样品名称' }],
        model: [{ required: true, message: '请输入型号规格' }],
        sampleNum: [{ required: true, message: '请输入样品数量' }],
        sampleUnit: [{ required: true, message: '请选择样品单位' }]
      },
      options: [],
      defaultValue: '', // 单位字典项默认code
      defaultVolt: '', // 电压等级的默认字典值
      optionsArray: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ],
      isCheck: false, // 是否是查看页面
      materialList: store.user.materialList,
      voltageList: []
    });

    // console.log(datas.materialList)

    watch(
      () => props.drawer,
      newValue => {
        // console.log(props)
        // console.log(newValue)
        if (newValue) {
          datas.showDrawer = newValue;
          datas.titles = props.title;
          if (datas.titles === '查看样品') {
            datas.isCheck = true;
            datas.position = 'left';
            datas.labelWidth = 'auto';
            datas.formInline = props.editData;
          } else if (datas.titles === '编辑样品') {
            const currentSample = props.editData;
            currentSample.inspectCost = Number(currentSample.inspectCost);
            datas.formInline = props.editData;
          } else {
            datas.formInline = {
              sampleName: '',
              sampleNum: 0,
              sampleUnit: '',
              model: '',
              storage: '',
              inspectBaseOn: '',
              inspectCost: 0,
              inspectType: 0,
              inspectionProject: '',
              sampleGrade: '0',
              remark: ''
            };
            if (!datas.formInline.sampleUnit) {
              datas.formInline.sampleUnit = datas.defaultValue;
            }
            if (!datas.formInline.sampleNum) {
              datas.formInline.productionUnit = datas.sampleNum;
            }
            if (!datas.formInline.storage) {
              datas.formInline.storage = datas.defaultVolt;
            }
            datas.isCheck = false;
            datas.position = 'top';
            datas.labelWidth = '110px';
          }
          getGradeTestItems();
        }
      },
      { deep: true }
    );

    // 过滤单位
    const filterUnit = unitId => {
      var unitName = '';
      if (unitId && datas.options.length > 0) {
        datas.options.forEach(opt => {
          if (opt.code === unitId) {
            unitName = opt.name;
          }
        });
      }
      return unitName;
    };

    // 过滤电压等级
    const filterStorage = code => {
      var voltageName = '';
      if (code && datas.voltageList.length > 0) {
        datas.voltageList.forEach(item => {
          if (item.code === code) {
            voltageName = item.name;
          }
        });
      }
      return voltageName;
    };

    // 确认新增
    const onSubmit = () => {
      if (!datas.formInline.sampleName) {
        ElMessage.warning('请输入样品名称');
        return false;
      }
      datas.formRef.validate(valid => {
        if (valid) {
          datas.formInline.superId = props.taskId;
          datas.drawerLoading = true;
          saveTaskSampleInfo(datas.formInline).then(function (res) {
            datas.drawerLoading = false;
            saveTestItems();
            if (res !== false) {
              getGradeTestItems();
              context.emit('setInfo', datas.formInline);
              context.emit('close', false);
              ElMessage.success(`${props.title === '新增样品' ? '新增' : '编辑'}成功`);
              datas.showDrawer = false;
            }
          });
        } else {
          return false;
        }
      });
    };
    // 关闭抽屉
    const handleClose = () => {
      datas.showDrawer = false;
      context.emit('close', false);
    };

    const changStrategyTestItems = value => {
      getSgTestItemByStrategy(props.materialCode, value).then(res => {
        if (res) {
          // console.log(res.data.data)
          datas.testItems = res.data.data;
        }
      });
    };

    const getSavedTestItems = sampleId => {
      if (tenantType.value === 0) {
        getSavedTestItemsBySampleId(sampleId).then(res => {
          if (res) {
            if (res.data.data && res.data.data.length > 0) {
              datas.testItems = res.data.data;
            }
            return res.data.data.length > 0;
          }
        });
      }
    };

    const saveTestItems = () => {
      if (tenantType.value === 0) {
        const itemList = [];
        datas.testItems.forEach(item => {
          itemList.push({
            entrustRegSampleId: datas.formInline.id,
            externalCapabilityCode: item.externalCapabilityCode,
            externalCapabilityId: item.externalCapabilityId,
            externalCapabilityName: item.externalCapabilityName,
            id: ''
          });
        });

        saveTaskSampleTestItems({ entityList: itemList }).then(res => {
          if (res) {
            getSavedTestItems(datas.formInline.id);
          }
        });
      }
    };

    const getGradeTestItems = () => {
      if (tenantType.value === 0) {
        if (!getSavedTestItems(datas.formInline.id)) {
          changStrategyTestItems(datas.formInline.sampleGrade);
        }
      }
    };

    const tenantType = computed({
      get: () => store.user.tenantInfo.type
    });

    return {
      ...toRefs(datas),
      tenantType,
      onSubmit,
      handleClose,
      filterUnit,
      filterStorage,
      changStrategyTestItems,
      getSavedTestItems,
      saveTestItems
    };
  },
  created() {
    this.getSampleUnits();
    this.getVoltage();
  },
  methods: {
    getSampleUnits() {
      // 从tes字典里面读取样品单位
      const that = this;
      getDictionary(5).then(res => {
        // console.log(res.data.items)
        that.optionsArray[0].group = [];
        that.optionsArray[1].group = [];
        res.data.data.dictionaryoption.forEach(item => {
          if (item.status === 1) {
            that.optionsArray[0].group.push(item);
          } else {
            that.optionsArray[1].group.push(item);
          }
        });
        if (that.optionsArray[0].group.length > 0) {
          that.defaultValue = that.optionsArray[0].group[0].code;
        }
        that.options = res.data.data.dictionaryoption;
      });
    },
    getVoltage() {
      var that = this;
      getDictionary('YPCFD').then(res => {
        that.voltageList = res.data.data.dictionaryoption;
        that.defaultVolt = that.voltageList.filter(item => item.status === 1)[0].code;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.formDataSample {
  :deep(.el-input--medium .el-input__inner) {
    height: 32px;
    line-height: 32px;
  }
  :deep(.el-form-item) {
    margin-right: 0;
  }
  :deep(.el-form-item__content) {
    display: block;
  }
}
</style>
<style lang="scss">
.page-drawer .el-drawer__header {
  padding: 20px 40px 0px !important;
}
.page-drawer .el-drawer__body {
  padding: 10px 40px !important;
  margin-bottom: 50px;
}
</style>
