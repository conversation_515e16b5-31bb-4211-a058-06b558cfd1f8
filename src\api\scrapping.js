import request from '@/utils/request';

// 设备报废
export function addPlan(data) {
  return request({
    url: '/api-device/device/measure_plan/add',
    method: 'post',
    data
  });
}
// 设备报废列表页
export function getScrappingList(data) {
  return request({
    url: '/api-device/device/device_scrap_apply/list',
    method: 'post',
    data
  });
}
// 设备报废详情
export function getScrappingDetail(id) {
  return request({
    url: `/api-device/device/device_scrap_apply/info/${id}`,
    method: 'get'
  });
}
// 设备报废-》获取所有任务节点名称
export function findAllUserTaskName() {
  return request({
    url: `/api-device/device/device_scrap_apply/findAllUserTaskName`,
    method: 'get'
  });
}
// 设备报废-》保存设备报废申请表信息
export function saveInfoApi(data) {
  return request({
    url: `/api-device/device/device_scrap_apply/save`,
    method: 'post',
    data
  });
}
// 设备报废-》删除报废信息
export function deleteApply(id) {
  return request({
    url: `/api-device/device/device_scrap_apply/delete/${id}`,
    method: 'delete'
  });
}
// 设备报废-》查询流程审批历史记录
export function processHistoryApi(id) {
  return request({
    url: `/api-device/device/device_scrap_apply/processHistory/${id}`,
    method: 'get'
  });
}

// 设备报废-》提交设备报废申请表信息
export function submitInfoApi(data) {
  return request({
    url: `/api-device/device/device_scrap_apply/commit`,
    method: 'post',
    data
  });
}

// 设备报废-》提交申请信息
export function processExecuteApi(data) {
  return request({
    url: `/api-device/device/device_scrap_apply/processExecute`,
    method: 'post',
    data
  });
}
// 设备报废-》设备列表
export function getPredict(data) {
  return request({
    url: '/api-device/device/device_scrap_apply/list_device',
    method: 'post',
    data
  });
}
