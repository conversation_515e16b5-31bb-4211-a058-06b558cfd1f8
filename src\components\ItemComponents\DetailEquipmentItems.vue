<template>
  <!-- 可检项目 -->
  <div v-loading="tableLoading" class="textLeft">
    <el-button class="add-btn" size="small" icon="el-icon-plus" @click="handleAdd" @keyup.prevent @keydown.enter.prevent
      >新增</el-button
    >
  </div>
  <el-table
    :data="tableList"
    fit
    border
    highlight-current-row
    size="medium"
    class="detail-table dark-table drawer-height-table"
    @header-dragend="drageHeader"
  >
    <el-table-column type="index" label="序号" :width="70" align="center" />
    <el-table-column prop="number" label="项目编号" :min-width="140">
      <template #default="{ row }">
        <div>{{ row.number || '--' }}</div>
      </template>
    </el-table-column>
    <el-table-column prop="name" label="项目名称" :min-width="140">
      <template #default="{ row }">
        <div>{{ row.name || '--' }}</div>
      </template>
    </el-table-column>
    <el-table-column prop="fixedResult" label="所属分类" :min-width="140">
      <template #default="{ row }">
        <div>{{ row.categoryName || '--' }}</div>
      </template>
    </el-table-column>
    <el-table-column
      v-if="getPermissionBtn('deleteMaintainRecord')"
      class-name="fixed-right"
      prop="status"
      label="操作"
      width="120"
    >
      <template #default="{ row }">
        <span v-if="getPermissionBtn('deleteMaintainRecord')" class="blue-color" @click="handleDelete(row)">删除</span>
      </template>
    </el-table-column>
  </el-table>
  <!-- 新增项目弹出窗 -->
  <AddInspectionItems
    :show="showAdd"
    :data="alreadyList"
    :material-select-code="''"
    :can-select-material="true"
    @close="closeDialog"
    @selectData="selectData"
  />
</template>

<script>
import { reactive, toRefs, watch, getCurrentInstance } from 'vue';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import AddInspectionItems from '@/components/BusinessComponents/AddInspectionItems';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { deleteDeviceItem, addItemDevice } from '@/api/equipment';
import { savePerson, deletePersonApi } from '@/api/testItem';

export default {
  name: 'DetailEquipmentItems',
  components: { AddInspectionItems },
  props: {
    id: {
      type: String,
      default: ''
    },
    list: {
      type: Array,
      default: function () {
        return [];
      }
    },
    type: {
      type: String,
      default: ''
    },
    userId: {
      type: String,
      default: ''
    }
  },
  emits: ['handleResh'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableList: [],
      treeData: [],
      alreadyList: [],
      showAdd: false,
      dialogVisible: false, // 弹窗的隐藏与显示
      dialogType: '', // 弹窗类型
      detailId: '',
      tableLoading: false
    });
    watch(props, newValue => {
      state.tableList = props.list;
      state.detailId = props.id;
      state.alreadyList = state.tableList;
      state.dialogType = props.type;
    });
    // 获取新增的项目
    const selectData = data => {
      state.showAdd = false;
      const params = data;
      if (params) {
        if (state.dialogType === 'equipment') {
          // 设备台账页面
          params.forEach(item => {
            item.deviceId = state.detailId;
          });
          addItemDevice({ capabilityDeviceEntityList: params }).then(res => {
            if (res) {
              proxy.$message.success(res.data.message);
              context.emit('handleResh');
            }
          });
        } else {
          // 部门管理页面
          params.forEach(item => {
            item.employeeId = state.detailId;
            item.userId = props.userId;
          });
          savePerson({ capabilityEmployeeEntityList: params }).then(res => {
            if (res) {
              proxy.$message.success(res.data.message);
              context.emit('handleResh');
            }
          });
        }
      }
    };
    const closeDialog = value => {
      state.showAdd = value;
    };
    // 新增
    const handleAdd = () => {
      state.showAdd = true;
    };
    // 删除
    const handleDelete = row => {
      proxy
        .$confirm(`是否删除${row.name}？`, {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          showClose: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        })
        .then(() => {
          state.tableLoading = true;
          if (state.dialogType === 'equipment') {
            deleteDeviceItem(row.id).then(res => {
              state.tableLoading = false;
              if (res) {
                proxy.$message.success(res.data.message);
                context.emit('handleResh');
              }
            });
          } else if (state.dialogType === 'depart') {
            deletePersonApi(row.id).then(res => {
              state.tableLoading = false;
              if (res) {
                proxy.$message.success(res.data.message);
                context.emit('handleResh');
              }
            });
          }
        });
    };
    return {
      ...toRefs(state),
      handleAdd,
      selectData,
      closeDialog,
      handleDelete,
      getPermissionBtn,
      drageHeader,
      formatDate,
      getNameByid
    };
  }
};
</script>

<style lang="scss" scoped>
.textLeft {
  text-align: left;
}
.el-select {
  width: 100%;
}
.add-btn {
  margin-bottom: 20px;
}
</style>
