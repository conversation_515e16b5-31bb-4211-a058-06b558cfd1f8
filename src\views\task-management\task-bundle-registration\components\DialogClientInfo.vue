<template>
  <el-dialog
    v-model="showDialog"
    custom-class="double-dialog tiny-dialog info-add"
    :title="title"
    top="5vh"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <div class="dialog-main">
      <el-form
        ref="taskInfoRef"
        class="formDataInfo"
        :model="formInline"
        :rules="taskInfoRule"
        size="small"
        label-width="90px"
        label-position="right"
      >
        <!--委托方-->
        <el-space direction="vertical">
          <el-row :gutter="60">
            <el-col :span="12">
              <el-form-item label="委托方：" prop="entrustCustomerId">
                <el-select
                  v-model="formInline.entrustCustomerId"
                  class="owner-select"
                  placeholder="请选择委托方"
                  clearable
                  filterable
                  @change="changeEntrustCustomer"
                >
                  <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" />
            <el-col :span="12">
              <el-form-item label="联系人：" prop="entrustContactsId">
                <el-select
                  v-model="formInline.entrustContactsId"
                  class="owner-select"
                  placeholder="请选择联系人"
                  clearable
                  filterable
                  @change="changeEntrustContacts"
                >
                  <el-option v-for="item in entrustContactsList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电话：" prop="entrustContactsPhone">
                <el-input v-model="formInline.entrustContactsPhone" placeholder="输入电话" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司电话：" prop="entrustPhone">
                <el-input v-model="formInline.entrustPhone" placeholder="输入公司电话" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司地址：" prop="entrustAddress">
                <el-select
                  v-model="formInline.entrustAddress"
                  class="owner-select"
                  placeholder="请选择公司地址"
                  clearable
                  filterable
                  @change="changeEntrustAddress"
                >
                  <el-option
                    v-for="item in entrustAddressList"
                    :key="item.id"
                    :label="item.exactAddress"
                    :value="item.exactAddress"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-space>
        <!--缴款方-->
        <el-space direction="vertical">
          <el-row :gutter="60">
            <el-col :span="12">
              <el-form-item label="缴款方：" prop="payerCustomerId">
                <el-select
                  v-model="formInline.payerCustomerId"
                  class="owner-select"
                  placeholder="请选择缴款方"
                  clearable
                  filterable
                  @change="changePayerCustomer"
                >
                  <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" />
            <el-col :span="12">
              <el-form-item label="公司税号：" prop="payerTaxNo">
                <el-input v-model="formInline.payerTaxNo" placeholder="输入公司税号" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电话：" prop="payerPhone">
                <el-input v-model="formInline.payerPhone" placeholder="输入电话" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户行：" prop="payerOpeningBank">
                <el-input v-model="formInline.payerOpeningBank" placeholder="输入开户行" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="账号：" prop="payerAcctNo">
                <el-input v-model="formInline.payerAcctNo" placeholder="输入账号" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="地址：" prop="payerAddress">
                <el-input v-model="formInline.payerAddress" placeholder="输入地址" clearable />
              </el-form-item>
            </el-col>
          </el-row>
        </el-space>
        <!--发票邮寄-->
        <el-space direction="vertical">
          <el-row :gutter="60">
            <el-col :span="12">
              <el-form-item label="发票邮寄：" prop="invoiceCustomerId">
                <el-select
                  v-model="formInline.invoiceCustomerId"
                  class="owner-select"
                  placeholder="请选择客户"
                  clearable
                  filterable
                  @change="changeInvoiceCustomer"
                >
                  <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" />
            <el-col :span="12">
              <el-form-item label="联系人：" prop="invoiceContactsId">
                <el-select
                  v-model="formInline.invoiceContactsId"
                  class="owner-select"
                  placeholder="请选择联系人"
                  clearable
                  filterable
                  @change="changeInvoiceContacts"
                >
                  <el-option v-for="item in invoiceContactsList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电话：" prop="invoicePhone">
                <el-input v-model="formInline.invoicePhone" placeholder="输入电话" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="地址：" prop="invoiceAddress">
                <el-select
                  v-model="formInline.invoiceAddress"
                  class="owner-select"
                  placeholder="请选择公司地址"
                  clearable
                  filterable
                  @change="changeInvoiceAddress"
                >
                  <el-option
                    v-for="item in invoiceAddressList"
                    :key="item.id"
                    :label="item.exactAddress"
                    :value="item.exactAddress"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-space>
        <!--报告邮寄-->
        <el-space direction="vertical">
          <el-row :gutter="60">
            <el-col :span="12">
              <el-form-item label="报告邮寄：" prop="reportCustomerId">
                <el-select
                  v-model="formInline.reportCustomerId"
                  class="owner-select"
                  placeholder="请选择客户"
                  clearable
                  filterable
                  @change="changeReportCustomer"
                >
                  <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" />
            <el-col :span="12">
              <el-form-item label="联系人：" prop="reportContactsId">
                <el-select
                  v-model="formInline.reportContactsId"
                  class="owner-select"
                  placeholder="请选择联系人"
                  clearable
                  filterable
                  @change="changeReportContacts"
                >
                  <el-option v-for="item in reportContactsList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电话：" prop="reportPhone">
                <el-input v-model="formInline.reportPhone" placeholder="输入电话" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="地址：" prop="reportAddress">
                <el-select
                  v-model="formInline.reportAddress"
                  class="owner-select"
                  placeholder="请选择公司地址"
                  clearable
                  filterable
                  @change="changeReportAddress"
                >
                  <el-option
                    v-for="item in reportAddressList"
                    :key="item.id"
                    :label="item.exactAddress"
                    :value="item.exactAddress"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-space>
        <!--生产商-->
        <el-space direction="vertical">
          <el-row :gutter="60">
            <el-col :span="12">
              <el-form-item label="生产商：" prop="producerCustomerId">
                <el-select
                  v-model="formInline.producerCustomerId"
                  class="owner-select"
                  placeholder="请选择客户"
                  clearable
                  filterable
                  @change="changeProducerCustomer"
                >
                  <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" />
            <el-col :span="12">
              <el-form-item label="联系人：" prop="producerContactsId">
                <el-select
                  v-model="formInline.producerContactsId"
                  class="owner-select"
                  placeholder="请选择联系人"
                  clearable
                  filterable
                  @change="changeProducerContacts"
                >
                  <el-option v-for="item in producerContactsList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电话：" prop="producerPhone">
                <el-input v-model="formInline.producerPhone" placeholder="输入电话" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="地址：" prop="producerAddress">
                <el-select
                  v-model="formInline.producerAddress"
                  class="owner-select"
                  placeholder="请选择公司地址"
                  clearable
                  filterable
                  @change="changePayerAddress"
                >
                  <el-option
                    v-for="item in producerAddressList"
                    :key="item.id"
                    :label="item.exactAddress"
                    :value="item.exactAddress"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-space>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent>确 认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref } from 'vue';
import { ElMessage } from 'element-plus';
import router from '@/router/index.js';
// import { useRoute } from 'vue-router'
import {
  getClientContacts,
  getInvoiceInfo,
  getClientAddress,
  saveTaskCustomerInfo,
  getTaskCustomerInfo
} from '@/api/task-registration';
import { getList } from '@/api/customerManagement';
import { useStore } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
// import { formatDate } from '@/utils/formatTime'
import { taskType } from '@/data/industryTerm';

export default {
  name: 'DialogTaskRegistration',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '新增客户信息'
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    // console.log(proxy)
    // const lodash = inject('_')
    const store = useStore().state;
    const datas = reactive({
      currentAccountId: getLoginInfo().accountId,
      userOptions: [],
      copyUserOptions: [],
      entrustContactsList: [],
      entrustAddressList: [],
      payerContactsList: [],
      invoiceContactsList: [],
      invoiceAddressList: [],
      producerContactsList: [],
      producerAddressList: [],
      reportContactsList: [],
      reportAddressList: [],
      addressOptions: store.user.materialList,
      copyaddressOptions: store.user.materialList,
      showDialog: false,
      formInline: {
        entrustAddress: '',
        entrustContactsId: '',
        entrustContactsName: '',
        entrustContactsPhone: '',
        entrustCustomerId: '',
        entrustCustomerName: '',
        entrustPhone: '',
        invoiceAddress: '',
        invoiceContactsId: '',
        invoiceContactsName: '',
        invoiceContactsPhone: '',
        invoiceCustomerId: '',
        invoiceCustomerName: '',
        invoicePhone: '',
        payerAcctNo: '',
        payerAddress: '',
        payerContactsId: '',
        payerContactsName: '',
        payerContactsPhone: '',
        payerCustomerId: '',
        payerCustomerName: '',
        payerOpeningBank: '',
        payerTaxNo: '',
        payerPhone: '',
        producerAddress: '',
        producerContactsId: '',
        producerContactsName: '',
        producerContactsPhone: '',
        producerCustomerId: '',
        producerCustomerName: '',
        producerPhone: '',
        reportAddress: '',
        reportContactsId: '',
        reportContactsName: '',
        reportContactsPhone: '',
        reportCustomerId: '',
        reportCustomerName: '',
        reportPhone: '',
        superId: ''
      },
      taskInfoRef: ref(),
      taskInfoRule: {
        entrustCustomerId: [{ required: true, message: '请选择委托方' }]
      },
      typeOptions: taskType,
      showEdit: false
    });

    watch(
      () => props.show,
      newValue => {
        if (newValue) {
          // console.log(props)
          datas.showDialog = newValue;
          datas.showEdit = props.isEdit;
          if (props.isEdit) {
            datas.formInline = {
              entrustAddress: props.info.entrust?.address || '',
              entrustContactsId: props.info.entrust?.contactsId || '',
              entrustContactsName: props.info.entrust?.contactsName || '',
              entrustContactsPhone: props.info.entrust?.contactsPhone || '',
              entrustCustomerId: props.info.entrust?.customerId || '',
              entrustCustomerName: props.info.entrust?.customerName || '',
              entrustPhone: props.info.entrust?.contactsPhone || '',
              invoiceAddress: props.info.invoice?.address || '',
              invoiceContactsId: props.info.invoice?.contactsId || '',
              invoiceContactsName: props.info.invoice?.contactsName || '',
              invoiceContactsPhone: props.info.invoice?.contactsPhone || '',
              invoiceCustomerId: props.info.invoice?.customerId || '',
              invoiceCustomerName: props.info.invoice?.customerName || '',
              invoicePhone: props.info.invoice?.phone || '',
              payerAcctNo: props.info.payer?.acctNo || '',
              payerAddress: props.info.payer?.address || '',
              payerContactsId: props.info.payer?.contactsId || '',
              payerContactsName: props.info.payer?.contactsName || '',
              payerContactsPhone: props.info.payer?.contactsPhone || '',
              payerCustomerId: props.info.payer?.customerId || '',
              payerCustomerName: props.info.payer?.customerName || '',
              payerOpeningBank: props.info.payer?.openingBank || '',
              payerTaxNo: props.info.payer?.taxNo || '',
              payerPhone: props.info.payer?.phone || '',
              producerAddress: props.info.producer?.address || '',
              producerContactsId: props.info.producer?.contactsId || '',
              producerContactsName: props.info.producer?.contactsName || '',
              producerContactsPhone: props.info.producer?.contactsPhone || '',
              producerCustomerId: props.info.producer?.customerId || '',
              producerCustomerName: props.info.producer?.customerName || '',
              producerPhone: props.info.producer?.phone || '',
              reportAddress: props.info.report?.address || '',
              reportContactsId: props.info.report?.contactsId || '',
              reportContactsName: props.info.report?.contactsName || '',
              reportContactsPhone: props.info.report?.contactsPhone || '',
              reportCustomerId: props.info.report?.customerId || '',
              reportCustomerName: props.info.report?.customerName || '',
              reportPhone: props.info.report?.phone || '',
              superId: props.info.superId
            };
          }
          datas.formInline.superId = props.info.superId;
          setDropList();
        }
      },
      { deep: true }
    );

    function setDropList() {
      if (datas.formInline.entrustContactsId) {
        getContactList(datas.formInline.entrustCustomerId).then(res => {
          datas.entrustContactsList = res;
        });
      }
      if (datas.formInline.entrustAddress) {
        getAddressList(datas.formInline.entrustCustomerId).then(res => {
          datas.entrustAddressList = res;
        });
      }
      if (datas.formInline.invoiceContactsId) {
        getContactList(datas.formInline.invoiceCustomerId).then(res => {
          datas.invoiceContactsList = res;
        });
      }
      if (datas.formInline.invoiceAddress) {
        getAddressList(datas.formInline.invoiceCustomerId).then(res => {
          datas.invoiceAddressList = res;
        });
      }
      if (datas.formInline.reportContactsId) {
        getContactList(datas.formInline.reportCustomerId).then(res => {
          datas.reportContactsList = res;
        });
      }
      if (datas.formInline.reportAddress) {
        getAddressList(datas.formInline.reportCustomerId).then(res => {
          datas.reportAddressList = res;
        });
      }
      if (datas.formInline.producerContactsId) {
        getContactList(datas.formInline.producerCustomerId).then(res => {
          datas.producerContactsList = res;
        });
      }
      if (datas.formInline.producerAddress) {
        getAddressList(datas.formInline.producerCustomerId).then(res => {
          datas.producerAddressList = res;
        });
      }
    }

    // 确定选择
    const dialogSuccess = () => {
      datas.taskInfoRef.validate(valid => {
        if (valid) {
          const xInfo = datas.formInline;
          const entrustParams = {
            contactsId: xInfo.entrustContactsId,
            contactsName: xInfo.entrustContactsName,
            contactsPhone: xInfo.entrustContactsPhone,
            customerId: xInfo.entrustCustomerId,
            customerName: xInfo.entrustCustomerName,
            address: xInfo.entrustAddress,
            phone: xInfo.entrustPhone
          };
          const payerParams = {
            contactsId: xInfo.payerContactsId,
            contactsName: xInfo.payerContactsName,
            contactsPhone: xInfo.payerContactsPhone,
            openingBank: xInfo.payerOpeningBank,
            customerId: xInfo.payerCustomerId,
            customerName: xInfo.payerCustomerName,
            taxNo: xInfo.payerTaxNo,
            acctNo: xInfo.payerAcctNo,
            phone: xInfo.payerPhone,
            address: xInfo.payerAddress
          };
          const invoiceParams = {
            contactsId: xInfo.invoiceContactsId,
            contactsName: xInfo.invoiceContactsName,
            contactsPhone: xInfo.invoiceContactsPhone,
            customerId: xInfo.invoiceCustomerId,
            customerName: xInfo.invoiceCustomerName,
            address: xInfo.invoiceAddress,
            phone: xInfo.invoicePhone
          };
          const producerParams = {
            contactsId: xInfo.producerContactsId,
            contactsName: xInfo.producerContactsName,
            contactsPhone: xInfo.producerContactsPhone,
            customerId: xInfo.producerCustomerId,
            customerName: xInfo.producerCustomerName,
            address: xInfo.producerAddress,
            phone: xInfo.producerPhone
          };
          const reportParams = {
            contactsId: xInfo.reportContactsId,
            contactsName: xInfo.reportContactsName,
            contactsPhone: xInfo.reportContactsPhone,
            customerId: xInfo.reportCustomerId,
            customerName: xInfo.reportContactsName,
            address: xInfo.reportAddress,
            phone: xInfo.reportPhone
          };
          const customerParams = {
            entrust: entrustParams,
            invoice: invoiceParams,
            payer: payerParams,
            report: reportParams,
            producer: producerParams,
            superId: xInfo.superId
          };
          if (datas.showEdit) {
            saveTaskCustomerInfo(customerParams).then(res => {
              if (res !== false) {
                datas.showDialog = false;
                context.emit('close', customerParams);
                ElMessage.success('更新成功');
              }
            });
          } else {
            saveTaskCustomerInfo(customerParams).then(res => {
              if (res !== false) {
                ElMessage.success('新增成功');
                datas.showDialog = false;
                // context.emit('setInfo', true)
                context.emit('close', customerParams);
                router.push({
                  name: 'TaskRegistrationDetail',
                  query: { id: res.data.data, flag: 2 }
                });
              }
            });
          }
        }
      });
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };

    // #region 委托方

    const changeEntrustCustomer = id => {
      const userIndex = datas.userOptions.findIndex(item => item.id === id);
      if (userIndex !== -1) {
        getContactList(id).then(res => {
          datas.entrustContactsList = res;
          const contactsIndex = datas.entrustContactsList.findIndex(
            item => item.id === datas.userOptions[userIndex].defaultContactsId
          );
          datas.formInline.entrustContactsId = datas.userOptions[userIndex].defaultContactsId;
          datas.formInline.entrustContactsName = datas.userOptions[userIndex].defaultContacts;
          datas.formInline.entrustContactsPhone =
            contactsIndex === -1 ? '' : datas.entrustContactsList[contactsIndex].phone;
          datas.formInline.entrustCustomerId = datas.userOptions[userIndex].id;
          datas.formInline.entrustCustomerName = datas.userOptions[userIndex].name;
          datas.formInline.entrustPhone = datas.userOptions[userIndex].phone;
          getAddressList(id).then(res => {
            datas.entrustAddressList = res;
            datas.formInline.entrustAddress = datas.userOptions[userIndex].defaultAddress;
          });
        });
      }
    };

    const changeEntrustContacts = id => {
      const contactsIndex = datas.entrustContactsList.findIndex(item => item.id === id);
      if (contactsIndex !== -1) {
        const xContacts = datas.entrustContactsList[contactsIndex];
        datas.formInline.entrustContactsId = xContacts.id;
        datas.formInline.entrustContactsName = xContacts.name;
        datas.formInline.entrustContactsPhone = xContacts.phone;
      }
    };

    const changeEntrustAddress = address => {
      // console.log(address)
    };

    // #endregion

    // #region 缴款方

    const changePayerCustomer = id => {
      const userIndex = datas.userOptions.findIndex(item => item.id === id);
      if (userIndex !== -1) {
        getContactList(id).then(res => {
          datas.payerContactsList = res;
          const contactsIndex = datas.payerContactsList.findIndex(
            item => item.id === datas.userOptions[userIndex].defaultContactsId
          );
          datas.formInline.payerContactsId = datas.userOptions[userIndex].defaultContactsId;
          datas.formInline.payerContactsName = datas.userOptions[userIndex].defaultContacts;
          datas.formInline.payerContactsPhone =
            contactsIndex === -1 ? '' : datas.payerContactsList[contactsIndex].phone;
          datas.formInline.payerCustomerId = datas.userOptions[userIndex].id;
          datas.formInline.payerCustomerName = datas.userOptions[userIndex].name;
          getCurrentInvoiceInfo(id).then(res => {
            datas.formInline.payerAcctNo = res.acctNo;
            datas.formInline.payerTaxNo = res.taxNo;
            datas.formInline.payerAddress = res.exactAddress;
            datas.formInline.payerOpeningBank = res.openingBank;
            datas.formInline.payerPhone = res.phone;
          });
        });
      }
    };

    const changePayerContacts = id => {
      const contactsIndex = datas.payerContactsList.findIndex(item => item.id === id);
      if (contactsIndex !== -1) {
        const xContacts = datas.payerContactsList[contactsIndex];
        datas.formInline.payerContactsId = xContacts.id;
        datas.formInline.payerContactsName = xContacts.name;
        datas.formInline.payerContactsPhone = xContacts.phone;
      }
    };

    const changePayerAddress = address => {
      // const addressIndex = datas.copyaddressOptions.findIndex(item => item.address === address)
    };

    // #endregion

    // #region 发票邮寄

    const changeInvoiceCustomer = id => {
      const userIndex = datas.userOptions.findIndex(item => item.id === id);
      if (userIndex !== -1) {
        getContactList(id).then(res => {
          datas.invoiceContactsList = res;
          const contactsIndex = datas.invoiceContactsList.findIndex(
            item => item.id === datas.userOptions[userIndex].defaultContactsId
          );
          datas.formInline.invoiceContactsId = datas.userOptions[userIndex].defaultContactsId;
          datas.formInline.invoiceContactsName = datas.userOptions[userIndex].defaultContacts;
          datas.formInline.invoiceContactsPhone =
            contactsIndex === -1 ? '' : datas.invoiceContactsList[contactsIndex].phone;
          datas.formInline.invoiceCustomerId = datas.userOptions[userIndex].id;
          datas.formInline.invoiceCustomerName = datas.userOptions[userIndex].name;
          datas.formInline.invoicePhone = datas.userOptions[userIndex].phone;
          getAddressList(id).then(res => {
            datas.invoiceAddressList = res;
            datas.formInline.invoiceAddress = datas.userOptions[userIndex].defaultAddress;
          });
        });
      }
    };

    const changeInvoiceContacts = id => {
      const contactsIndex = datas.invoiceContactsList.findIndex(item => item.id === id);
      if (contactsIndex !== -1) {
        const xContacts = datas.invoiceContactsList[contactsIndex];
        datas.formInline.invoiceContactsId = xContacts.id;
        datas.formInline.invoiceContactsName = xContacts.name;
        datas.formInline.invoiceContactsPhone = xContacts.phone;
      }
    };

    const changeInvoiceAddress = address => {
      // console.log(address)
    };

    // #endregion

    // #region 报告邮寄

    const changeReportCustomer = id => {
      const userIndex = datas.userOptions.findIndex(item => item.id === id);
      if (userIndex !== -1) {
        getContactList(id).then(res => {
          datas.reportContactsList = res;
          const contactsIndex = datas.reportContactsList.findIndex(
            item => item.id === datas.userOptions[userIndex].defaultContactsId
          );
          datas.formInline.reportContactsId = datas.userOptions[userIndex].defaultContactsId;
          datas.formInline.reportContactsName = datas.userOptions[userIndex].defaultContacts;
          datas.formInline.reportContactsPhone =
            contactsIndex === -1 ? '' : datas.reportContactsList[contactsIndex].phone;
          datas.formInline.reportCustomerId = datas.userOptions[userIndex].id;
          datas.formInline.reportCustomerName = datas.userOptions[userIndex].name;
          datas.formInline.reportPhone = datas.userOptions[userIndex].phone;
          getAddressList(id).then(res => {
            datas.reportAddressList = res;
            datas.formInline.reportAddress = datas.userOptions[userIndex].defaultAddress;
          });
        });
      }
    };

    const changeReportContacts = id => {
      const contactsIndex = datas.reportContactsList.findIndex(item => item.id === id);
      if (contactsIndex !== -1) {
        const xContacts = datas.reportContactsList[contactsIndex];
        datas.formInline.reportContactsId = xContacts.id;
        datas.formInline.reportContactsName = xContacts.name;
        datas.formInline.reportContactsPhone = xContacts.phone;
      }
    };

    const changeReportAddress = address => {
      // console.log(address)
    };

    // #endregion

    // #region 生产商

    const changeProducerCustomer = id => {
      const userIndex = datas.userOptions.findIndex(item => item.id === id);
      if (userIndex !== -1) {
        getContactList(id).then(res => {
          datas.producerContactsList = res;
          const contactsIndex = datas.producerContactsList.findIndex(
            item => item.id === datas.userOptions[userIndex].defaultContactsId
          );
          datas.formInline.producerContactsId = datas.userOptions[userIndex].defaultContactsId;
          datas.formInline.producerContactsName = datas.userOptions[userIndex].defaultContacts;
          datas.formInline.producerContactsPhone =
            contactsIndex === -1 ? '' : datas.producerContactsList[contactsIndex].phone;
          datas.formInline.producerCustomerId = datas.userOptions[userIndex].id;
          datas.formInline.producerCustomerName = datas.userOptions[userIndex].name;
          datas.formInline.producerPhone = datas.userOptions[userIndex].phone;
          getAddressList(id).then(res => {
            datas.producerAddressList = res;
            datas.formInline.producerAddress = datas.userOptions[userIndex].defaultAddress;
          });
        });
      }
    };

    const changeProducerContacts = id => {
      const contactsIndex = datas.producerContactsList.findIndex(item => item.id === id);
      if (contactsIndex !== -1) {
        const xContacts = datas.producerContactsList[contactsIndex];
        datas.formInline.producerContactsId = xContacts.id;
        datas.formInline.producerContactsName = xContacts.name;
        datas.formInline.producerContactsPhone = xContacts.phone;
      }
    };

    const changeProducerAddress = address => {
      // console.log(address)
    };

    // #endregion

    // #region 获取客户信息

    function getContactList(customerId) {
      return new Promise(resolve => {
        let result = [];
        getClientContacts(customerId).then(res => {
          if (res.data.code === 200) {
            result = res.data.data;
          }
          resolve(result);
        });
      });
    }

    function getAddressList(customerId) {
      return new Promise(resolve => {
        let result = [];
        getClientAddress(customerId).then(res => {
          if (res.data.code === 200) {
            result = res.data.data;
          }
          resolve(result);
        });
      });
    }

    function getCurrentInvoiceInfo(customerId) {
      return new Promise(resolve => {
        let result = [];
        getInvoiceInfo(customerId).then(res => {
          if (res.data.code === 200) {
            result = res.data.data;
          }
          resolve(result);
        });
      });
    }

    function getAllClients() {
      const clientParams = {
        condition: '',
        isDesc: false,
        page: '1',
        limit: '-1',
        orderBy: '',
        isAsc: ''
      };
      getList(clientParams).then(res => {
        if (res.data.code === 200) {
          datas.userOptions = res.data.data.list;
          datas.copyUserOptions = JSON.parse(JSON.stringify(res.data.data.list));
        }
      });
    }

    function getCustomerInfo(taskId) {
      getTaskCustomerInfo(taskId).then(res => {});
    }

    // #endregion

    getAllClients();
    return {
      ...toRefs(datas),
      dialogSuccess,
      close,
      changeEntrustCustomer,
      changeEntrustContacts,
      changeEntrustAddress,
      changeProducerCustomer,
      changeProducerContacts,
      changeProducerAddress,
      changeReportCustomer,
      changeReportContacts,
      changeReportAddress,
      changeInvoiceCustomer,
      changeInvoiceContacts,
      changeInvoiceAddress,
      changePayerCustomer,
      changePayerContacts,
      changePayerAddress,
      getCustomerInfo
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
#sample-recycle {
  :deep(.el-radio__label) {
    margin-right: 10px;
  }
}

.formDataInfo {
  :deep(.el-input--medium .el-input__inner) {
    height: 32px;
    line-height: 32px;
  }
}
.scdd {
  position: absolute;
  left: 70px;
  top: -34px;
}
.info-add {
  .dialog-main {
    :deep(.el-space) {
      width: 100%;
      .el-space__item {
        margin: 2px 0;
        width: 100%;
      }
    }
  }
  .el-input--medium .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
  .el-divider--horizontal {
    margin: 15px 0px;
  }
  .el-form-item {
    text-align: left;
    // width: 46%;
    margin-bottom: 10px;
    :deep(.el-form-item__label) {
      font-weight: normal;
      font-size: 14px;
      color: #606266;
      padding: 0px;
    }
    :deep(.el-form-item__content) {
      .el-select {
        width: 100%;
      }
      .el-date-editor.el-input {
        width: 100%;
      }
      .el-radio-group {
        .el-radio {
          margin: 0px;
        }
        .el-radio:nth-child(2) {
          margin: 0px 5px;
        }
      }
    }
  }
}
</style>
