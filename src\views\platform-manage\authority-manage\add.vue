<template>
  <el-dialog
    :model-value="showDialog"
    custom-class="add-edit-account"
    :title="currentTitle"
    width="480px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <el-form
      ref="formInlineRef"
      :model="formInline"
      :rules="rules"
      label-position="right"
      label-width="120px"
      class="demo-ruleForm"
    >
      <!-- <el-form-item label="编号:" prop="id">
        <el-input v-model="formInline.id" disabled />
      </el-form-item> -->
      <el-form-item label="权限名称：" prop="permissionName">
        <el-input ref="inputRef" v-model="formInline.permissionName" v-trim placeholder="请输入权限名称" />
      </el-form-item>
      <el-form-item label="权限分组：" prop="permissiontreeId">
        <!-- <el-input v-model="formInline.permissiontreeId" /> -->
        <el-cascader
          v-model="permissiontreeIds"
          :options="treeData"
          :props="categoryProps"
          clearable
          style="width: 100%"
          @change="changeCategory"
        />
      </el-form-item>
      <el-form-item label="描述：">
        <el-input
          v-model="formInline.content"
          type="textarea"
          size="small"
          rows="2"
          placeholder="请输入内容"
          maxlength="300"
        />
      </el-form-item>
      <el-form-item label="状态：">
        <!-- <el-switch v-model="formInline.status" :active-text="formInline.status?'启用':'停用'">启用</el-switch> -->
        <el-radio-group v-model="formInline.status">
          <el-radio v-for="type in statusOption" :key="type.name" :label="type.value">{{ type.name }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" :loading="addEditLoading" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent
          >保 存</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ref, reactive, toRefs, watch, nextTick } from 'vue';
import { getNameByid } from '@/utils/common';
import { ElMessage } from 'element-plus';
import { addPermission, editPermission } from '@/api/platform-management';
import _ from 'lodash';
// import { useRoute } from 'vue-router'
import { formatAuthorityTree } from '@/utils/formatJson';

export default {
  name: 'AddOrEditAuthority',
  components: {},
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: 'add'
    },
    tree: {
      type: Array,
      default: function () {
        return [];
      }
    },
    treeIds: {
      type: Array,
      default: function () {
        return [];
      }
    },
    data: {
      type: Object,
      default: function () {
        return null;
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    const datas = reactive({
      showDialog: props.show,
      currentTitle: '新增权限',
      formInlineRef: ref(),
      inputRef: ref(),
      addEditLoading: false,
      permissiontreeIds: [],
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'treename',
        value: 'id'
      },
      formInline: {
        permissionName: '',
        permissiontreeId: '',
        content: '',
        status: 1
      },
      statusOption: [
        { name: '启用', value: 1 },
        { name: '停用', value: 0 }
      ],
      rules: {
        permissionName: [{ required: true, message: '请输入权限名称', trigger: 'blur' }],
        permissiontreeId: [{ required: true, message: '请选择权限分组', trigger: 'change' }]
      },
      treeData: []
    });

    watch(
      () => props.show,
      newValue => {
        // console.log(props)
        if (newValue) {
          datas.showDialog = newValue;
          datas.currentTitle = props.title === 'add' ? '新增权限' : '编辑权限';
          datas.treeData = props.tree;
          if (props.data) {
            datas.formInline = props.data;
            var items = _.filter(props.tree, function (t) {
              return t.id === datas.formInline.permissiontreeId;
            });
            if (items.length > 0) {
              datas.permissiontreeIds = [
                datas.formInline.permissiontreeId[datas.formInline.permissiontreeId.length - 1]
              ];
            } else {
              const pids = formatAuthorityTree(props.tree, datas.formInline.permissiontreeId, []);
              // console.log(pids)
              datas.permissiontreeIds = pids;
            }
          } else {
            datas.formInline = {
              permissionName: '',
              permissiontreeId: '',
              content: '',
              status: 1
            };
            datas.permissiontreeIds = [];
            if (props.treeIds && props.treeIds.length > 0) {
              datas.permissiontreeIds = props.treeIds;
              datas.formInline.permissiontreeId = props.treeIds[props.treeIds.length - 1];
            }
          }
          nextTick(() => {
            datas.inputRef.focus();
          });
        }
      },
      { deep: true }
    );

    // 确定
    const dialogSuccess = () => {
      datas.formInlineRef.validate(valid => {
        if (valid) {
          if (props.title === 'add') {
            addPermission(datas.formInline).then(res => {
              if (res !== false) {
                ElMessage.success('新增权限成功');
                datas.showDialog = false;
                context.emit('setInfo', datas.formInline);
              } else {
                ElMessage.error('新增权限失败!');
              }
            });
          } else {
            editPermission(datas.formInline).then(res => {
              if (res !== false) {
                ElMessage.success('编辑权限成功');
                datas.showDialog = false;
                context.emit('setInfo', datas.formInline);
              } else {
                ElMessage.error('编辑权限失败!');
              }
            });
          }
        }
      });
    };
    // 取消
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };
    // 过滤
    const filterTreeName = ids => {
      var name = '';
      if (ids.length > 0) {
        var items = _.filter(datas.treeData, function (t) {
          return t.id === ids[0];
        });
        if (items.length > 0 && ids.length === 1) {
          name = items[0].treename;
        } else {
          var items1 = _.filter(items[0].children, function (c) {
            return c.id === ids[1];
          });
          if (items1.length > 0) {
            name = items1[0].treename;
          }
        }
      }
      return name;
    };
    // 所属分类change
    const changeCategory = value => {
      // console.log(value)
      datas.permissiontreeIds = value;
      if (value) {
        const len = value.length - 1;
        datas.formInline.permissiontreeId = value[len];
        datas.formInline.permissiontreeName = filterTreeName(value);
        // console.log(datas.formInline)
      }
    };

    return {
      ...toRefs(datas),
      close,
      getNameByid,
      dialogSuccess,
      changeCategory
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.demo-ruleForm {
  :deep(.el-form-item:not(:last-of-type)) {
    margin-bottom: 20px;
  }
}
</style>
