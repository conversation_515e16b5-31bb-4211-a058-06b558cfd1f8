import request from '@/utils/request';

/**
 * 生产订单明细列表查询
 * submitStatus 送检状态 0 待送检 1 已送检
 * http://*************:8800/doc.html#/diplomat/%E7%94%9F%E4%BA%A7%E8%AE%A2%E5%8D%95%E6%98%8E%E7%BB%86/listUsingPOST_5
 * @param {{
 *  "limit": "20", "page": "1", "isAsc": true, "key": "", "orderBy": "", "submitStatus": "0"
 * }} data
 * @returns
 */
export function getProductionOrderDetailList(data) {
  return request({
    url: '/api-diplomat/diplomat/productionorderdetail/list',
    method: 'post',
    data
  });
}

/**
 * 根据生产订单明细Id获取送检记录
 * http://*************:8800/doc.html#/diplomat/%E7%94%9F%E4%BA%A7%E8%AE%A2%E5%8D%95%E6%98%8E%E7%BB%86%E5%92%8C%E7%94%B3%E8%AF%B7%E5%8D%95%E5%85%B3%E7%B3%BB/findRecordUsingGET
 * @param {productionOrderDetailId} productionOrderDetailId
 * @returns
 */
export function getInspectionRecordList(productionOrderDetailId) {
  return request({
    url: `/api-diplomat/diplomat/productionorderdetail/recordList/${productionOrderDetailId}`,
    method: 'get'
  });
}

/**
 * 生产订单明细送检
 * http://*************:8800/doc.html#/diplomat/%E7%94%9F%E4%BA%A7%E8%AE%A2%E5%8D%95%E6%98%8E%E7%BB%86%E5%92%8C%E7%94%B3%E8%AF%B7%E5%8D%95%E5%85%B3%E7%B3%BB/saveUsingPOST_8
 * @param {{}} data
 * @returns
 */
export function saveInspectionRecord(data) {
  return request({
    url: '/api-diplomat/diplomat/productionorderdetail/submit',
    method: 'post',
    data
  });
}

// #region 暂未使用

/**
 * 生产订单信息列表
 * http://*************:8800/doc.html#/diplomat/%E7%94%9F%E4%BA%A7%E8%AE%A2%E5%8D%95/listUsingGET
 * @param {*} data
 * @returns
 */
export function getProductionOrderList(data) {
  return request({
    url: '/api-diplomat/production-order/list',
    method: ''
  });
}

/**
 * 根据Id查询生产订单信息
 * http://*************:8800/doc.html#/diplomat/%E7%94%9F%E4%BA%A7%E8%AE%A2%E5%8D%95/listUsingGET
 * @param { string } id
 * @returns
 */
export function getProductionOrderInfoById(id) {
  return request({
    url: `/api-diplomat/production-order/info/${id}`,
    method: 'get'
  });
}

/**
 * 保存生产订单信息
 * @param {*} data
 * @returns
 */
export function saveProductionOrder(data) {
  return request({
    url: '/api-diplomat/production-order/save',
    method: 'post',
    data
  });
}

/**
 * 修改生产订单信息
 * @param {*} data
 * @returns
 */
export function updateProductionOrder(data) {
  return request({
    url: '/api-diplomat/production-order/update',
    method: 'post',
    data
  });
}

/**
 * 删除生产订单及明细
 * @param {*} data
 * @returns
 */
export function deleteProductionOrder(data) {
  return request({
    url: '/api-diplomat/production-order/delete',
    method: 'post',
    data
  });
}

// #endregion
