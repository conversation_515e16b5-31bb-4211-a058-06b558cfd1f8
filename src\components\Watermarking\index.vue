<template>
  <div style="position: relative">
    <!-- <img :src="imgSrc" alt=""> -->
    <canvas
      id="canvas"
      :height="height"
      :width="width"
      oncontextmenu="return false"
      referrerPolicy="no-referrer"
      style="position: absolute"
    />
    <slot />
  </div>
</template>

<script>
import { nextTick, reactive, toRefs, watchEffect } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getLoginInfo } from '@/utils/auth';

export default {
  name: 'Watermarking',
  props: {
    width: {
      type: Number,
      default: 100
    },
    height: {
      type: Number,
      default: 100
    }
  },
  setup(props, context) {
    const state = reactive({
      width: 100,
      height: 100
    });
    watchEffect(() => {
      state.width = props.width;
      state.height = props.height;
    });
    const initCanvas = () => {
      const ctx = document.querySelector('#canvas').getContext('2d');
      const img = new Image();
      ctx.font = `italic 10px Vedana`;
      ctx.fillStyle = 'rgba(200, 200, 200, 0.40)';
      ctx.textBaseline = 'Middle';
      // ctx.translate(20, 20)
      // 以左上角为坐标原点 开始绘制图像
      ctx.drawImage(img, 0, 0, state.width, state.height);
      // ctx.fillText(getLoginInfo().nickname, state.width * 0.70, state.height * 0.75) // 在图片上添加字体
      // ctx.fillText(getLoginInfo().username, state.width * 1.33, state.height * 0.75)
      // ctx.fillText(formatDate(new Date()), state.width * 2.06, state.height * 0.75)
      ctx.fillText(getLoginInfo().nickname, state.width * 0.15, state.height * 0.5); // 在图片上添加字体
      ctx.fillText(getLoginInfo().username, state.width * 0.35, state.height * 0.5);
      ctx.fillText(formatDate(new Date()), state.width * 0.75, state.height * 0.5);
    };
    nextTick(() => {
      initCanvas();
    });
    return {
      ...toRefs(state),
      initCanvas
    };
  }
};
</script>

<style lang="scss" scoped></style>
