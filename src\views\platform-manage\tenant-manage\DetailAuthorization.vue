<template>
  <!-- 租户管理授权页面 -->
  <ListLayout :has-page-header="false" :has-quick-query="false" :has-left-panel="false">
    <div v-loading="listLoading" class="permission">
      <el-row style="height: 100%">
        <el-col :span="20" style="height: 100%; position: relative">
          <el-tree
            ref="treeRef"
            class="treeClass allHeight"
            :class="{ canEditClass: canEdit }"
            node-key="id"
            :default-checked-keys="havSelected"
            default-expand-all
            :data="formList"
            show-checkbox
            :props="defaultProps"
          >
            <template #default="{ data }">
              <span :class="{ isPenultimate: data.isPenultimate, isChecked: checkNodeId === data.id }"
                >{{ data.treename }}{{ data.status === 0 ? '(已停用)' : '' }}</span
              >
            </template>
          </el-tree>
          <div v-if="canEdit" class="textRight btnGroup">
            <el-button size="small" @click="getList">取 消</el-button>
            <el-button size="small" type="primary" @click="onSubmit()">保 存</el-button>
          </div>
        </el-col>
        <el-col :span="4" style="padding: 0 0 0 15px; height: 100%; overflow-x: auto">
          <div class="allItem">
            <div
              v-for="(item, index) in tableData"
              :key="index"
              class="scrollItem"
              :class="{ 'is-current': currentIndex === index }"
              @click="scrollJump(index, item)"
            >
              {{ item.name }}
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </ListLayout>
</template>

<script>
import { reactive, toRefs, ref, nextTick, getCurrentInstance } from 'vue';
// import DrageHandle from '@/components/DragHandle/handle.vue'
// import { getRoleTree } from '@/api/roleManage'
// import { ElMessage } from 'element-plus'
// import { formatTree } from '@/utils/formatJson'
// import { getMemberList } from '@/api/roleManage'
import { useRoute } from 'vue-router';
import { getPermissionBtn } from '@/utils/common';
import ListLayout from '@/components/ListLayout';
import { getDetailList, savePermissionTenant } from '@/api/tenantPermission';
import $ from 'jquery';

export default {
  name: 'DetailAuthorization',
  components: { ListLayout },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    const state = reactive({
      tableData: [],
      currentIndex: 0,
      checkNodeId: '',
      tableRef: ref(),
      formList: [],
      canEdit: getPermissionBtn('tenantAuthorizeBtn'),
      listLoading: false,
      havSelected: [], // 已经选中的节点
      filterText: '',
      filterPMText: '',
      treeData: [],
      formTree: ref(),
      treeRef: ref(),
      defaultProps: {
        children: 'permissions',
        label: 'name'
      },
      treeLoading: false, // 角色管理树loading
      roleData: {},
      dialogLoading: false,
      isAddTree: true,
      dialogRules: {},
      tableKey: 0,
      total: 0,
      currentNodeKey: '',
      scrollInfo: {
        scrollTop: 0,
        dom: ''
      }
    });
    const customNodeClass = data => {
      if (data.isPenultimate) {
        return 'is-penultimate';
      }
      return null;
    };

    // 向下滑动
    const smoothDown = (distance, step, total) => {
      if (distance < total) {
        distance += step;
        state.scrollInfo.dom.scrollTop = distance;
        // console.log(distance, total)
        setTimeout(function () {
          smoothDown(distance, step, total);
        }, 10);
      } else {
        state.scrollInfo.dom.scrollTop = total;
      }
    };
    // 向上滑动
    const smoothUp = (distance, step, total) => {
      if (distance > total) {
        distance -= step;
        state.scrollInfo.dom.scrollTop = distance;
        setTimeout(function () {
          smoothUp(distance, step, total);
        }, 10);
      } else {
        state.scrollInfo.dom.scrollTop = total;
      }
    };
    // 用class添加锚点,实现定位
    const scrollJump = (index, item) => {
      state.currentIndex = index;
      // const tableDom = document.getElementsByClassName('treeClass')[0]
      const jump = $('.treeClass').children();
      var total = 0;
      // 该节点之前的总共的高度
      for (var i = 0; i < index; i++) {
        total = total + jump[i].offsetHeight;
      }
      const distance = state.scrollInfo.scrollTop;
      // 平滑滚动，时长500ms，每10ms一跳，共50跳
      var step = total / 50;
      if (total > distance) {
        smoothDown(distance, step, total);
      } else {
        const newTotal = distance - total;
        step = newTotal / 50;
        smoothUp(distance, step, total);
      }
      state.checkNodeId = item.id;
    };
    // 获取滚动高度
    const onScroll = e => {
      state.scrollInfo.scrollTop = e.target.scrollTop;
    };

    const getList = () => {
      state.listLoading = true;
      // state.activeNames = state.formList.map(item => { return item.parentId })
      getDetailList(route.query.strId).then(res => {
        state.listLoading = false;
        if (res) {
          const data = res.data.data;
          state.tableData = [];
          state.havSelected = [];
          data.forEach(item => {
            item.level = 1;
            state.tableData.push({ name: item.treename, id: item.id });
            if (item.status === 0 || !state.canEdit) {
              item.disabled = true;
            }
            item.permissions.forEach(value => {
              if (value.status === 0 || !state.canEdit) {
                value.disabled = true;
              }
              if (value.permissionName) {
                value.level = 2;
                value.parentId = item.id;
                value.id = value.permissionId;
                value.treename = value.permissionName;
                value.isPenultimate = true;
                if (value.selectflag !== 0) {
                  state.havSelected.push(value.id);
                }
              }
            });
          });
          state.formList = data;
          nextTick(() => {
            var newdom = document.getElementsByClassName('isPenultimate');
            for (var i = 0; i < newdom.length; i++) {
              newdom[i].parentElement.style.width = '30%';
              newdom[i].parentElement.style.float = 'left';
            }
            state.scrollInfo.dom = document.getElementsByClassName('treeClass')[0];
            state.scrollInfo.dom.addEventListener('scroll', onScroll);
          });
        }
      });
    };
    getList();
    // 权限表格点击行展开和收缩
    const handleRowExpend = row => {
      state.tableRef.toggleRowExpansion(row);
    };
    // 保存
    const onSubmit = () => {
      const getselected = state.treeRef.getCheckedNodes();
      var selected = [];
      getselected.forEach(item => {
        if (item.permissionName) {
          selected.push(item);
        }
      });
      state.listLoading = true;
      savePermissionTenant({ oauthPermissionSaveVoList: selected }).then(res => {
        state.listLoading = false;
        if (res) {
          proxy.$message.success('权限调整成功!');
          getList();
        }
      });
    };
    return {
      ...toRefs(state),
      getPermissionBtn,
      onSubmit,
      customNodeClass,
      handleRowExpend,
      getList,
      scrollJump,
      onScroll
    };
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
// 左侧区域高度
.permission {
  position: relative;
}
.textLeft {
  text-align: left;
}
.btnGroup {
  position: absolute;
  bottom: -40px;
  right: 10px;
}
:deep(.el-card__body) {
  padding: 10px;
}
:deep(.el-collapse-item__wrap) {
  border-bottom: 0.6rem solid #ebeef5 !important;
}
:deep(.el-collapse-item__header) {
  font-size: 16px;
  padding: 0 10px;
  border-bottom: 0.1rem solid #ebeef5 !important;
  line-height: 37px;
  height: 37px;
}
:deep(.el-collapse-item__content) {
  padding: 0;
}
:deep(.el-card) {
  border: 0;
  &:hover {
    background: $tes-primary2;
  }
}
:deep(.el-table td) {
  padding: 8px 0;
}
:deep(.el-table .el-table__expanded-cell) {
  border-bottom: 0.4rem solid #ebeef5;
}
:deep(.tableLeft.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background: #fff;
  cursor: pointer;
}
:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  cursor: pointer;
}
.isChecked {
  display: inline-block;
  animation-name: mymove;
  animation-duration: 2s;
  animation-delay: 0.5s;
  -webkit-animation-name: mymove;
  -webkit-animation-duration: 2s;
}
@keyframes mymove {
  0% {
    background: none;
    font-size: 14px;
  }
  50% {
    background-color: $tes-primary2;
    width: 100%;
    color: $tes-primary;
    text-align: left;
    padding-left: 10px;
    font-size: 16px;
  }
  100% {
    background: none;
    font-size: 14px;
  }
}
.textRight {
  text-align: right;
}
.allHeight,
.allItem {
  height: calc(100vh - 156px);
  overflow-y: auto;
  // height: 100%;
}
.canEditClass {
  height: calc(100vh - 196px);
}
.scrollItem {
  line-height: 22px;
  text-align: left;
  font-size: 14px;
  padding: 2px 2px 2px 8px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  border-left: 4px solid transparent;
  &:hover {
    cursor: pointer;
    background-color: $tes-primary2;
    color: $tes-primary;
  }
  &.is-current {
    background-color: $tes-primary2;
    color: $tes-primary;
    border-left-color: $tes-primary;
  }
}
.allItem {
  position: relative;
}
:deep(.el-card) {
  border-radius: 0;
}
.btnGroup {
  margin-top: 10px;
}
.isPenultimate:parent {
  color: red !important;
}
</style>
