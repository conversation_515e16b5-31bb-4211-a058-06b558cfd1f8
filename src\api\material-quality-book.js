import request from '@/utils/request';

// 原材料质保书列表
export function inspectionrawmaterialquality(data) {
  return request({
    url: '/api-diplomat/diplomat/inspectionrawmaterialquality/list',
    method: 'post',
    data
  });
}

// 原材料质保书详情
export function inspectionrawmaterialqualityInfo(id) {
  return request({
    url: `/api-diplomat/diplomat/inspectionrawmaterialquality/info/${id}`,
    method: 'get'
  });
}

// 原材料质保书提交
export function inspectionrawmaterialqualitySubmit(data) {
  return request({
    url: `/api-diplomat/diplomat/inspectionrawmaterialquality/submitExpValue`,
    method: 'post',
    data
  });
}

// 原材料质保书保存
export function inspectionrawmaterialqualitySave(data) {
  return request({
    url: `/api-diplomat/diplomat/inspectionrawmaterialquality/saveExpValue`,
    method: 'post',
    data
  });
}

// 原材料质保书附件上传
export function inspectionrawmaterialqualityUpload(data) {
  return request({
    url: `/api-diplomat/diplomat/inspectionrawmaterialquality/fileUpload`,
    headers: { 'Content-type': 'multipart/form-data' },
    method: 'post',
    data
  });
}

// 原材料质保书附件下载
export function inspectionrawmaterialqualityDown(id) {
  return request({
    url: `/api-diplomat/diplomat/inspectionrawmaterialquality/download/${id}`,
    responseType: 'blob',
    method: 'get'
  });
}

// 原材料质保书附件删除
export function inspectionrawmaterialqualityDeleteFile(id) {
  return request({
    url: `/api-diplomat/diplomat/inspectionrawmaterialquality/deleteAttachment/${id}`,
    method: 'get'
  });
}

// 原材料质保书删除检测项目
export function inspectionrawmaterialqualityDeleteItem(id) {
  return request({
    url: `/api-diplomat/diplomat/inspectionrawmaterialquality/deleteDetailById/${id}`,
    method: 'get'
  });
}
