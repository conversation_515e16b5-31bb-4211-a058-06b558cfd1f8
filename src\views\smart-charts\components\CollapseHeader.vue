<template>
  <div class="full-item" :style="{ paddingBottom: headerPadding + 'px' }">
    <div class="page-title" style="text-align: left">
      <slot name="title" />
    </div>
    <div class="right-group">
      <el-button
        class="searchBtn"
        size="large"
        type="text"
        @click="handleCollapse"
        @keyup.prevent
        @keydown.enter.prevent
      >
        {{ actionWord }}查询条件
        <i class="el-icon--right" :class="[show ? 'el-icon-arrow-up' : 'el-icon-arrow-down']" />
      </el-button>
    </div>
  </div>

  <el-collapse class="collapse-transition" style="border: 0px">
    <div
      v-show="show"
      class="query-container"
      style="border: 0px solid #dcdfe6; border-width: 1px 0px 0px"
      :style="{ paddingTop: headerPadding + 'px' }"
    >
      <slot name="collapse" />
    </div>
  </el-collapse>
</template>
<script>
import { reactive, toRefs } from 'vue';

export default {
  name: 'CollapseHeader',
  emits: ['setMainOffset', 'showCollapse'],
  setup(props, context) {
    const data = reactive({
      show: false,
      topHeight: 75,
      headerPadding: 0,
      actionWord: '展开'
    });

    const handleCollapse = () => {
      data.show = !data.show;
      data.topHeight = data.show ? 270 : 75;
      context.emit('setMainOffset', data.topHeight);
      context.emit('showCollapse', data.show);
      data.headerPadding = data.show ? 20 : 0;
      data.actionWord = data.show ? '收起' : '展开';
    };
    return {
      ...toRefs(data),
      handleCollapse
    };
  }
};
</script>

<style lang="scss" scoped>
.full-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  :slotted(.page-comple) {
    .space-line {
      display: inline-block;
      width: 24px;
      height: 2px;
      background: #b3e8dc;
      margin: 0 10px;
      position: relative;
      &::after {
        position: absolute;
        content: '';
        right: -2px;
        top: -2px;
        width: 6px;
        height: 6px;
        background: #b3e8dc;
        border-radius: 50%;
      }
    }
  }
}

.query-container {
  :slotted(.collapse-group) {
    display: flex;
    justify-content: space-between;
    .left-form-group {
      flex: 1;
    }
    .right-button-group {
      width: 280px;
      text-align: right;
    }
  }
  :slotted(.el-form) {
    .el-form-item,
    .el-form-item__content {
      width: 100%;
    }
    .el-form-item__content {
      display: flex;
      align-items: center;
    }
    .el-select.el-select--small,
    .el-button.el-button--small,
    .el-range-editor--small,
    .el-input-number--small,
    .el-tag {
      width: 100%;
    }
    .el-button {
      font-weight: normal;
      border-radius: 4px;
      border-color: #dcdfe6;
      &:hover {
        border-color: #b3e8dc;
      }
    }
    .el-tag {
      height: 32px;
      line-height: 32px;
      text-overflow: ellipsis;
      max-width: 100%;
      word-break: break-all;
      overflow: hidden;
      border-color: transparent;
      .el-tag__close {
        font-size: 16px;
        position: absolute;
        right: 6px;
        top: 8px;
      }
    }
  }
}
</style>
