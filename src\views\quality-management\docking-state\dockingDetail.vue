<template>
  <!-- SDCC国网对接详情 -->
  <DetailLayout>
    <template #page-header>
      <div class="header-flex flex-between">
        <div class="page-title">生产订单号：{{ detailData.no || '--' }}</div>
        <el-button class="fr" icon="el-icon-back" @click="goBack()">返回列表</el-button>
      </div>
    </template>
    <el-collapse v-model="activeName2" class="collapse-wrap">
      <el-collapse-item name="1">
        <template #title>
          <div class="collapse-header-title">生产订单信息</div>
        </template>
        <el-form
          ref="inspectionInfoRef"
          :inline="true"
          :model="formInline"
          class="productionOrderClass"
          label-width="130px"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="物资类型：" prop="materialSdccTypeName">
                <div>{{ detailData.materialSdccTypeName || '--' }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="客户名称：" prop="customerName">
                {{ getNameByid(detailData.customerName) || detailData.customerName || '--' }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="销售订单号：" prop="salesOrderNo">
                <div class="nowrap">{{ detailData.salesOrderNo || '--' }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="销售订单行项目号：" prop="salesOrderItemNo">
                <div>{{ detailData.salesOrderItemNo || '--' }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="项目名称：" prop="projectName">
                <div class="nowrap">{{ detailData.projectName || '--' }}</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-item>
    </el-collapse>
    <div v-loading="detailLoading" class="panelContent">
      <!-- <div class="uploadSdcc">
        <el-button
          class="add-btn"
          size="small"
          type="warning"
          @click="uploadSdcc"
          @keyup.prevent
          @keydown.enter.prevent
        >上传SDCC</el-button>
      </div> -->
      <el-tabs v-model="activeName" class="marginTop" :before-leave="beforeLeave" @tab-change="tabsClick">
        <el-tab-pane v-for="item in tabsList" :key="item.id" :label="item.name" :name="item.code">
          <FinishedProducts
            v-if="item.code === 'END_PRODUCT' && activeName == item.code"
            :unit-list="unitListArray"
            :external-list="item.externalCapabilityVoList"
            @isModify="
              val => {
                isModifyValue = val;
              }
            "
          />
          <InspectionProcess
            v-if="item.code === 'PROGRESS' && activeName == item.code"
            :unit-list="unitListArray2"
            :external-list="item.externalCapabilityVoList"
            @isModify="
              val => {
                isModifyValue = val;
              }
            "
          />
          <RawMaterials
            v-if="item.code === 'RAW_MATERIAL' && activeName == item.code"
            :unit-list="unitListArray2"
            :external-list="item.externalCapabilityVoList"
            @isModify="
              val => {
                isModifyValue = val;
              }
            "
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- 提交成功弹出框 -->
    <!-- 编辑检验单 -->
  </DetailLayout>
</template>

<script>
import { reactive, toRefs, ref, h } from 'vue';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { ElDivider } from 'element-plus';
import { formatDate } from '@/utils/formatTime';
// import { useRoute } from 'vue-router'
import { getLoginInfo } from '@/utils/auth';
import DetailLayout from '@/components/DetailLayout';
import FinishedProducts from './components/FinishedProducts';
import InspectionProcess from './components/InspectionProcess';
import RawMaterials from './components/RawMaterials';
import { ElMessageBox } from 'element-plus';
import { getTabsApi } from '@/api/sdcc';
import { getDictionary } from '@/api/user';
import router from '@/router';

export default {
  name: 'DockingDetail',
  components: { DetailLayout, FinishedProducts, InspectionProcess, RawMaterials },
  setup() {
    // const { proxy } = getCurrentInstance()
    // const route = useRoute()
    const spacer = h(ElDivider, { direction: 'vertical' });
    const state = reactive({
      leftRow: {}, // 左侧选中的数据
      tabsList: [],
      unitListArray: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ], // 字典单位数组类型
      unitListArray2: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ], // 字典单位数组类型
      detailLoading: false,
      externalCapabilityFirst: [], // 成品检验检测项目关键参数
      currentAccountId: getLoginInfo().accountId,
      isModifyValue: false,
      showS: false,
      activeName: '',
      activeName2: '1',
      showDetail: true,
      showEdit: false,
      formInline: {},
      detailData: JSON.parse(localStorage.getItem('productionOrderInfo')),
      inspectionInfoRef: ref()
    });
    // 获取检验单信息
    const getTab = () => {
      state.detailLoading = true;
      getTabsApi(state.detailData.materialSdccType).then(res => {
        state.detailLoading = false;
        if (res) {
          state.tabsList = res.data.data;
          if (state.tabsList.length > 0) {
            state.activeName = state.tabsList[0].code;
            // 成品检验
            const cpjyData = state.tabsList.filter(item => {
              return item.code === 'END_PRODUCT';
            })[0];
            cpjyData.externalCapabilityVoList.forEach(item => {
              item.externalCapabilityMapList.forEach(val => {
                state.externalCapabilityFirst.push(val);
              });
            });
          }
        }
      });
    };
    getTab();
    // 高级搜索
    const showDetail = () => {
      state.showS = !state.showS;
      if (state.activeName2 === '0') {
        state.activeName2 = '1';
      } else {
        state.activeName2 = '0';
      }
    };
    const tabsClick = val => {
      state.activeName = val;
    };
    // 获取的左侧数据
    const getLeftRow = data => {
      state.leftRow = data;
    };
    // 获取单位
    const getUnitList = () => {
      getDictionary(5).then(res => {
        if (res) {
          state.unitListArray[0].group = [];
          state.unitListArray[1].group = [];
          res.data.data.dictionaryoption.forEach(item => {
            if (item.status === 1) {
              state.unitListArray[0].group.push(item);
            } else {
              state.unitListArray[1].group.push(item);
            }
          });
        }
      });
      getDictionary(13).then(res => {
        if (res) {
          state.unitListArray2[0].group = [];
          state.unitListArray2[1].group = [];
          res.data.data.dictionaryoption.forEach(item => {
            if (item.status === 1) {
              state.unitListArray2[0].group.push(item);
            } else {
              state.unitListArray2[1].group.push(item);
            }
          });
        }
      });
    };
    getUnitList();
    // 返回列表
    const goBack = () => {
      router.go(-1);
    };
    // 切换标签
    const beforeLeave = async () => {
      if (state.isModifyValue) {
        await new Promise((resolve, reject) => {
          ElMessageBox({
            title: '提示',
            message: '当前页面数据未保存，是否确认离开？',
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            showCancelButton: true,
            closeOnClickModal: false,
            type: 'info'
          })
            .then(() => {
              state.isModifyValue = false;
              resolve();
            })
            .catch(() => {
              reject();
            });
        });
        // return false
      }
    };
    return {
      ...toRefs(state),
      formatDate,
      beforeLeave,
      getUnitList,
      goBack,
      spacer,
      showDetail,
      getLeftRow,
      tabsClick,
      getNameByid,
      getTab,
      getPermissionBtn
    };
  }
};
</script>
<style lang="scss" scoped>
.detail-collapse {
  border: 0;
  :deep(.el-collapse-item__header) {
    display: none;
  }
  :deep(.el-collapse-item__content) {
    // background-color: #fff;
    padding-bottom: 0;
  }
  :deep(.el-form .el-form-item) {
    margin-bottom: 0;
    text-align: left;
  }
  :deep(.el-collapse-item__wrap) {
    border: 0;
    padding-bottom: 20px;
    background-color: transparent;
  }
}
.productionOrderClass {
  text-align: left;
  background-color: $background-color;
  margin-top: 10px;
  :deep(.el-form-item) {
    margin: 0;
  }
}
.panelContent {
  background-color: $background-color;
  margin-top: 20px;
  padding: 10px 20px;
}
.fr {
  float: right;
}
.textLeft {
  text-align: left;
}
:deep(.el-tabs__header) {
  margin-bottom: 10px;
}
.uploadSdcc {
  position: absolute;
  // top: 0;
  right: 20px;
  z-index: 999;
}
</style>
