import { colWidth } from '@/data/tableStyle';

/**
 * 属性说明
 * field: 字段名
 * name: 字段显示名
 * colwidth: 列宽(可以为tableStyle中的值，也可是数字)
 * type: 列的类型。 0: 默认类型
 * isNotQuery: 列是否可以为非查询字段， 0: 不是，1: 是
 * styleContent: 样式字段解析
 * order: 排序字段，排序顺序
 * checkbox: 当前视图中是否显示
 * isHide: 是否为隐藏列
 * isOrder: 是否需要排序
 */

export const imageTagFieldList = [
  {
    field: 'labelKey',
    name: '图片key',
    colWidth: colWidth.name,
    type: 0,
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0
  },
  {
    field: 'labelName',
    name: 'KEY名称',
    colWidth: colWidth.name,
    type: 0,
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isMinWidth: true
  }
];
