<template>
  <div v-loading="logLoading" class="operationLogs">
    <el-empty v-if="logList.length === 0" :image="emptyImg" description="暂无数据" />
    <el-timeline>
      <el-timeline-item v-for="item in logList" :key="item.id" :timestamp="item.createTime" placement="top" center>
        <el-card>
          <div class="content">{{ item.operation }}</div>
        </el-card>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script>
import { reactive, toRefs, watch } from 'vue';
import { getOperationLogs } from '@/api/execution';
import { getNameByid } from '@/utils/common';
import emptyImg from '@/assets/img/empty-data.png';
export default {
  name: 'OperationLogs',
  props: {
    experimentId: {
      type: String,
      default: ''
    },
    activeName: {
      type: String,
      default: ''
    }
  },
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    const state = reactive({
      experimentId: '',
      logLoading: false,
      emptyImg,
      logList: [] // 操作记录
    });
    watch(props, () => {
      state.experimentId = props.experimentId;
      if (state.experimentId && props.activeName === '3') {
        console.log(1111);
        getList();
      }
    });
    const getList = () => {
      state.logLoading = true;
      getOperationLogs(state.experimentId).then(res => {
        state.logLoading = false;
        if (res) {
          state.logList = res.data.data;
        }
      });
    };
    return { ...toRefs(state), props, getList, getNameByid };
  },
  computed: {},
  created() {}
};
</script>

<style lang="scss" scoped>
.operationLogs {
  width: 80%;
  text-align: left;
  margin: 30px auto 0 auto;
}
.content {
  font-size: 15px;
}
:deep(.el-timeline-item__timestamp) {
  font-size: 14px;
}
:deep(.el-card__body) {
  padding: 10px;
}
:deep(.el-timeline-item__node) {
  background-color: $tes-primary;
}
</style>
