<template>
  <!-- 我的企业 -->
  <ListLayout :has-search-panel="false" :has-quick-query="false" :has-button-group="false">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="condition">
          <el-input
            v-model="formInline.condition"
            v-trim
            v-focus
            placeholder="请输入企业名称"
            class="ipt-360"
            size="large"
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter="onSubmit"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="onSubmit">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table base-table format-height-table no-quick-query"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column label="企业简称" prop="clientShortName" :width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.clientShortName || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="企业名称" prop="clientName" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.clientName || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="企业联系人" prop="person" :min-width="colWidth.people" show-overflow-tooltip>
        <template #default="{ row }">
          <UserTag :name="row.person || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="企业联系号码" prop="personMobile" :min-width="colWidth.phone">
        <template #default="{ row }">
          <span>{{ row.personMobile || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="加入状态" prop="status" :width="colWidth.status" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag v-if="row.status === 1" size="small" effect="dark" type="warning">{{
            filterStatus(row.status) || '--'
          }}</el-tag>
          <el-tag v-if="row.status === 2" size="small" effect="dark" type="error">{{
            filterStatus(row.status) || '--'
          }}</el-tag>
          <el-tag v-if="row.status === 3" size="small" effect="dark" type="success">{{
            filterStatus(row.status) || '--'
          }}</el-tag>
          <el-tag v-if="row.status === 4" size="small" effect="dark" type="info">{{
            filterStatus(row.status) || '--'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="colWidth.operation" prop="caozuo" fixed="right" class-name="fixed-right">
        <template #default="{ row }">
          <span v-if="row.status === 1" class="blue-color" @click="handleBusiness(row, 1)">同意</span>
          <span v-if="row.status === 1" class="blue-color" @click="handleBusiness(row, 2)">拒绝</span>
          <span v-if="row.status === 3" class="blue-color" @click="handleBusiness(row, 3)">退出</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
  </ListLayout>
</template>

<script>
import { reactive, toRefs, getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
// import { getNameByid, getPermissionBtn } from '@/utils/common'
// import { useStore } from 'vuex'
import { getLoginInfo } from '@/utils/auth';
import ListLayout from '@/components/ListLayout';
import Pagination from '@/components/Pagination';
import UserTag from '@/components/UserTag';
import { drageHeader } from '@/utils/formatTable';
import { getMyBusinessList, updateMyBusiness } from '@/api/userInfo';
import { colWidth } from '@/data/tableStyle';

export default {
  name: 'MyBusiness',
  components: { Pagination, ListLayout, UserTag },
  setup() {
    const { proxy } = getCurrentInstance();
    // const store = useStore().state
    const datas = reactive({
      accountId: getLoginInfo().accountId,
      listQuery: {
        page: 1,
        limit: 20
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      list: [],
      formInline: {
        condition: ''
      }
    });
    // 查询
    function onSubmit() {
      proxy.getList();
    }
    // 重置
    function reset() {
      datas.formInline = {
        condition: ''
      };
      datas.listQuery = {
        page: 1,
        limit: 20
      };
      proxy.getList();
    }
    // table排序
    const sortChange = data => {
      const { prop, order } = data;
      datas.listQuery.orderBy = prop;
      if (order === 'ascending') {
        datas.listQuery.isAsc = true;
      } else if (order === 'descending') {
        datas.listQuery.isAsc = false;
      } else {
        datas.listQuery.isAsc = null;
      }
    };
    // 过滤进入状态
    const filterStatus = status => {
      const map = {
        1: '待确认',
        2: '已拒绝',
        3: '已同意',
        4: '已退出'
      };
      return map[status];
    };
    // 操作
    const handleBusiness = (row, flag) => {
      var params = {
        id: row.id,
        status: 1 // "status": 1待确认 2拒绝 3同意 4已退出
      };
      if (flag === 1) {
        // 同意
        ElMessageBox({
          title: '',
          message: '同意后将加入该企业，是否确认继续？',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: true,
          type: 'warning'
        })
          .then(() => {
            params.status = 3;
            updateMyBusiness(params).then(res => {
              if (res !== false) {
                ElMessage.success('已同意！');
                proxy.getList();
              }
            });
          })
          .catch(() => {});
      } else if (flag === 2) {
        // 拒绝
        ElMessageBox({
          title: '',
          message: '拒绝后将无法加入企业，是否确认继续？',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: true,
          type: 'warning'
        })
          .then(() => {
            params.status = 2;
            updateMyBusiness(params).then(res => {
              if (res !== false) {
                ElMessage.success('已拒绝！');
                proxy.getList();
              }
            });
          })
          .catch(() => {});
      } else {
        // 退出
        ElMessageBox({
          title: '',
          message: '退出后将无法在重新登录该企业，是否确认继续？',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: true,
          type: 'warning'
        })
          .then(() => {
            params.status = 4;
            updateMyBusiness(params).then(res => {
              if (res !== false) {
                ElMessage.success('退出成功！');
                proxy.getList();
              }
            });
          })
          .catch(() => {});
      }
    };

    return { ...toRefs(datas), onSubmit, reset, sortChange, handleBusiness, drageHeader, filterStatus, colWidth };
  },
  created() {
    this.getList();
    // 刷新列表
    this.bus.$on('myBusinessList', msg => {
      this.getList();
    });
  },
  methods: {
    getList(data) {
      const _this = this;
      // _this.listLoading = true
      if (data && data !== undefined) {
        _this.listQuery.page = data.page;
        _this.listQuery.limit = data.limit;
      }
      const param = Object.assign(_this.formInline, _this.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      getMyBusinessList(param).then(res => {
        if (res !== false) {
          const { data } = res.data;
          _this.list = data.list;
          _this.total = data.totalCount;
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
    }
  }
};
</script>
<style lang="scss" scoped></style>
