<template>
  <!-- 近30日用户待办处理情况 -->
  <div class="UserToDo">
    <div class="top-agency">
      <div class="title">近30日用户待办处理情况</div>
    </div>
    <div class="echartsContent">
      <LineBarPieChart v-if="showChart" :option="barLineOption" :width="'100%'" height="600px" />
      <el-empty v-else :image="emptyImg" />
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, ref } from 'vue';
import LineBarPieChart from '@/components/LineBarPieChart';
import { getBarLineApi } from '@/api/home';
import emptyImg from '@/assets/img/empty-chart.png';

export default {
  name: 'UserToDo',
  components: { LineBarPieChart },
  props: {},
  emits: ['close', 'setInfo'],
  setup(props, context) {
    // const { appContext } = getCurrentInstance()
    const state = reactive({
      barLineJson: [],
      barJson: [],
      showChart: true
    });
    const barLineOption = ref({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999'
          }
        },
        formatter: params => {
          var text = `<div>${params[0].name}</div>`;
          var textTime = '';
          params.forEach(item => {
            if (item.value !== 0) {
              if (item.seriesName !== '处理待办数') {
                textTime = formatterMinue(item.value);
                text += `<div class="echartsFormatter">${item.marker}<span class="label">${item.seriesName}</span>：<span class="value">${textTime}</span></div> `;
              } else {
                text += `<div class="echartsFormatter">${item.marker}<span class="label">${item.seriesName}</span>：<span class="value">${item.value}项</span></div> `;
              }
            }
          });
          return text;
        }
      },
      toolbox: {
        feature: {
          dataView: { show: false, readOnly: false },
          magicType: { show: false, type: ['line', 'bar'] },
          restore: { show: false },
          saveAsImage: { show: true, title: '保存为图片' }
        },
        top: '8%',
        right: '3%'
      },
      grid: {
        width: 'auto',
        top: '23%'
      },
      legend: {
        data: ['处理待办数', '平均用时', '最短用时', '最长用时'],
        top: '8%',
        itemGap: 20
      },
      xAxis: [
        {
          type: 'category',
          data: [],
          axisLabel: {
            interval: 0
          },
          axisPointer: {
            type: 'shadow'
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '最长用时',
          alignTicks: true,
          axisLabel: {
            formatter: '{value} min'
          }
        },
        {
          type: 'value',
          name: '处理待办数',
          alignTicks: true,
          axisLabel: {
            formatter: '{value} 项'
          }
        }
      ],
      series: [
        {
          name: '处理待办数',
          type: 'bar',
          yAxisIndex: 1,
          data: [],
          barWidth: '50%',
          barMaxWidth: 50,
          itemStyle: {
            color: '#9FCEFF',
            borderColor: '#409EFF',
            borderWidth: 1,
            borderType: 'solid'
          }
        },
        {
          name: '平均用时',
          type: 'line',
          data: [],
          symbolSize: 8, // 设定实心点的大小
          lineStyle: {
            // 阴影部分
            shadowOffsetX: 0, // 折线的X偏移
            shadowOffsetY: 7, // 折线的Y偏移
            shadowBlur: 6, // 折线模糊
            shadowColor: 'rgba(199, 231, 202, 1)' // 折线颜色
          },
          itemStyle: {
            borderWidth: 2,
            color: '#67C23A',
            shadowOffsetX: 0, // 折线的X偏移
            shadowOffsetY: 7, // 折线的Y偏移
            shadowBlur: 6, // 折线模糊
            shadowColor: 'rgba(199, 231, 202, 1)'
          },
          emphasis: {
            itemStyle: {
              color: '#D1EDC4',
              borderColor: '#67C23A'
            }
          }
        },
        {
          name: '最短用时',
          type: 'line',
          data: [],
          symbolSize: 8, // 设定实心点的大小
          lineStyle: {
            // 阴影部分
            shadowOffsetX: 0, // 折线的X偏移
            shadowOffsetY: 7, // 折线的Y偏移
            shadowBlur: 6, // 折线模糊
            shadowColor: 'rgba(199, 213, 231, 1)' // 折线颜色
          },
          itemStyle: {
            borderWidth: 2,
            color: '#409EFF',
            shadowOffsetX: 0, // 折线的X偏移
            shadowOffsetY: 7, // 折线的Y偏移
            shadowBlur: 6, // 折线模糊
            shadowColor: 'rgba(199, 213, 231, 1)'
          },
          emphasis: {
            itemStyle: {
              color: '#D1EDC4',
              borderColor: '#409EFF'
            }
          }
        },
        {
          name: '最长用时',
          type: 'line',
          data: [],
          symbolSize: 8, // 设定实心点的大小
          lineStyle: {
            // 阴影部分
            shadowOffsetX: 0, // 折线的X偏移
            shadowOffsetY: 7, // 折线的Y偏移
            shadowBlur: 6, // 折线模糊
            shadowColor: 'rgba(231, 199, 199, 1)' // 折线颜色
          },
          itemStyle: {
            borderWidth: 2,
            color: '#F56C6C',
            shadowOffsetX: 0, // 折线的X偏移
            shadowOffsetY: 7, // 折线的Y偏移
            shadowBlur: 6, // 折线模糊
            shadowColor: 'rgba(231, 199, 199, 1)'
          },
          emphasis: {
            itemStyle: {
              color: '#D1EDC4',
              borderColor: '#F56C6C'
            }
          }
        }
      ]
    });
    // 近30日用户待办处理情况
    const getBarLineOption = () => {
      getBarLineApi().then(res => {
        if (res) {
          state.barLineJson = res.data.data;
          barLineOption.value.xAxis[0].data = state.barLineJson.map(item => {
            return item.userName;
          });
          barLineOption.value.series[0].data = state.barLineJson.map(item => {
            return item.todoNum;
          });
          barLineOption.value.series[1].data = state.barLineJson.map(item => {
            return item.avgTime;
          });
          barLineOption.value.series[2].data = state.barLineJson.map(item => {
            return item.minTime;
          });
          barLineOption.value.series[3].data = state.barLineJson.map(item => {
            return item.maxTime;
          });
        } else {
          state.showChart = false;
        }
      });
    };
    getBarLineOption();
    const formatterMinue = value => {
      var textTime = '';
      if (value !== 0) {
        // 分钟数
        var minuteNumber = Number(value % 60);
        // 小时数
        var hourNumber = parseInt(value / 60);
        // 天数
        var days = parseInt(hourNumber / 24);
        // 周数
        var weeks = parseInt(hourNumber / 24 / 7);
        if (hourNumber < 24 && hourNumber > 0) {
          // 不满一天
          textTime = minuteNumber ? `${hourNumber}小时 ${minuteNumber}分钟` : `${hourNumber}小时`;
        } else if (hourNumber >= 24 && hourNumber < 168) {
          // 满一天不满一周
          textTime = `${days}天 ${parseInt(hourNumber % 24)}小时 ${minuteNumber}分钟`;
        } else if (hourNumber >= 168) {
          // 满一周
          textTime = `${weeks}周 ${parseInt((hourNumber % 168) / 24)}天 ${parseInt(
            (hourNumber % 168) % 24
          )}小时 ${minuteNumber}分钟`;
        } else {
          // 不满一小时
          textTime = `${minuteNumber}分钟`;
        }
      }
      return textTime;
    };
    return { ...toRefs(state), emptyImg, barLineOption, getBarLineOption, formatterMinue };
  }
};
</script>
<style lang="scss">
.echartsContent {
  background-color: $background-color;
}
.echartsFormatter {
  text-align: left;
  .value {
    float: right;
  }
}
</style>
