<template>
  <!-- 检验策略 -->
  <ListLayout
    :has-page-header="false"
    :has-quick-query="false"
    :has-left-panel="true"
    :aside-max-width="520"
    :aside-panel-width="300"
  >
    <template #page-left-side>
      <div class="tree-container">
        <div class="tree-header">
          <div class="header-select">
            <span class="icon el-icon-menu" />
            <el-select
              v-model="materialCode"
              filterable
              size="small"
              class="topSelect"
              placeholder="请选择物资分类"
              @change="clickMaterial"
            >
              <el-option v-for="val in tabsData" :key="val.value" :label="val.name" :value="val.code" />
            </el-select>
          </div>
          <div class="header-input-button">
            <el-input
              v-model="otherForm.filterText"
              size="small"
              maxlength="100"
              placeholder="请输入类目名称进行搜索"
              prefix-icon="el-icon-search"
            />
            <el-button
              v-if="getPermissionBtn('addStrategyTreeBtn')"
              size="small"
              icon="el-icon-plus"
              class="addTreeBtn"
              @click="addTreeItem"
              @keyup.prevent
              @keydown.enter.prevent
            />
          </div>
        </div>
        <div class="tree-content">
          <el-tree
            ref="refTree"
            :data="otherForm.treeData"
            node-key="id"
            :props="otherForm.defaultProps"
            :expand-on-click-node="false"
            :highlight-current="true"
            :filter-node-method="filterNode"
            class="leftTree"
            draggable
            :allow-drop="allowDrop"
            @node-drop="nodeDrop"
            @filter="filterNode"
            @node-click="clickNode"
          >
            <template #default="{ node }">
              <span>{{ node.label }}</span>
              <el-dropdown
                v-if="getPermissionBtn('editStrategyTreeBtn') || getPermissionBtn('delStrategyTreeBtn')"
                trigger="hover"
                :class="node.showIcon ? 'icon-show' : ''"
                class="tree-dropdown el-icon"
                @visible-change="changeIcon(node.showIcon, node)"
              >
                <i class="el-icon-more" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="getPermissionBtn('editStrategyTreeBtn')" @click="editTree(node.data, node)"
                      ><i class="iconfont tes-edit" />编辑</el-dropdown-item
                    >
                    <el-dropdown-item
                      v-if="getPermissionBtn('delStrategyTreeBtn')"
                      class="color-red"
                      @click="delTree(node.data)"
                      ><i class="iconfont tes-delete" />删除</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-tree>
        </div>
      </div>
    </template>
    <el-tabs v-model="activeName" class="tab-box" @tab-click="changeTab">
      <el-tab-pane label="策略配置" name="first">
        <Material
          :tree-id="checkTreeId"
          :tree-length="treeLength"
          :tree-title="treeTitle"
          :current-data="currentTabsData"
          :active-name="activeName"
        />
      </el-tab-pane>
      <el-tab-pane label="检验项目配置" name="second">
        <Configuration
          :tree-id="checkTreeId"
          :tree-length="treeLength"
          :current-data="currentTabsData"
          :active-name="activeName"
        />
      </el-tab-pane>
    </el-tabs>
    <!-- 详情抽屉 -->
    <el-dialog
      v-model="showEditDialog"
      :title="otherForm.isAddTree === true ? '新增类目' : '编辑类目'"
      width="480px"
      :close-on-click-modal="false"
    >
      <el-form
        v-if="showEditDialog"
        ref="formTree"
        :model="dialogFrom"
        :rules="otherForm.dialogRules"
        label-position="right"
        label-width="90px"
      >
        <el-form-item
          label="类目名称："
          prop="inspectionName"
          :rules="{ required: true, message: '请输入类目名称', trigger: 'change' }"
        >
          <el-input
            ref="inputRefDialog"
            v-model.trim="dialogFrom.inspectionName"
            maxlength="50"
            autocomplete="off"
            placeholder="请输入类目名称"
            @keyup.enter="handleEditTree"
            @keyup.prevent
            @keydown.enter.prevent
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false">取 消</el-button>
          <el-button type="primary" @click="handleEditTree" @keyup.prevent @keydown.enter.prevent>确 认</el-button>
        </span>
      </template>
    </el-dialog>
  </ListLayout>
</template>

<script>
import { reactive, ref, watch, getCurrentInstance, toRefs, nextTick } from 'vue';
import { formatTree, formatAllTree, formatTreeByIds } from '@/utils/formatJson';
import { getTree, deleteTreeNode, addTreeNode, updateTreeNode, updateTreeOrder } from '@/api/strategy';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { useRoute } from 'vue-router';
import { useStore } from 'vuex';
import Material from './compoments/Material.vue';
import Configuration from './compoments/Configuration.vue';
import ListLayout from '@/components/ListLayout';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import _ from 'lodash';

export default {
  name: 'StrategyList',
  components: { Material, Configuration, ListLayout },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    const store = useStore();
    // const store = useStore()
    const state = reactive({
      expired: false, // 已过期
      inputRefDialog: ref(),
      materialCode: store.state.user.materialList[0]?.code || '',
      currentTabsData: store.state.user.materialList[0],
      treeLength: 0,
      activeName: route.query.activeName || 'first',
      activeName2: route.query.activeName || 'first',
      tableDataCl: [], // 策略配置表单
      submitTime: '', // 筛选时间段
      searchForm: {}, // 查询数据
      rowId: '', // 行数据id
      unitVisiable: false,
      tabsData: store.state.user.materialList,
      isAdd: false,
      detailData: {}, // 传递给详情页的内容
      tableData: [],
      copyData: {}, // 复制产品的详情
      param: '', // 模糊查询关键字
      total: 0,
      tableLoading: false, // 表格加载的loading
      detailDrawer: false,
      productTitle: '添加产品', // 产品弹出框标题
      isEdit: true, // 详情页的类型
      dialogFrom: {}, // 操作树节点的弹窗表格
      treeTitle: '', // 选中树节点的name
      checkTreeId: '', // 选中的左侧树节点的id
      listQuery: {
        page: 1,
        limit: 20
      }
    });
    const editFrom = ref(null);

    const otherForm = reactive({
      tableList: [],
      list: [],
      treeData: [],
      dialogTreeData: [],
      editData: {},
      defaultProps: {
        children: 'children',
        label: 'inspectionName'
      },
      category: [],
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      filterText: '',
      isAddTree: true,
      dialogRules: {},
      asideWidth: 240,
      tableSize: 'medium',
      showIcon: false
    });
    const tableKey = ref(0);
    // 树节点编辑
    const showEditDialog = ref(false);
    const editTree = (data, node) => {
      otherForm.dialogTreeData = formatAllTree(data.id, otherForm.dialogTreeData);
      showEditDialog.value = true;
      otherForm.isAddTree = false;
      state.dialogFrom = JSON.parse(JSON.stringify(data));
      otherForm.category = formatTreeByIds(node.parent);
      nextTick(() => {
        state.inputRefDialog.focus();
      });
    };
    // 选择物资
    const clickMaterial = val => {
      state.currentTabsData = store.state.user.materialList.filter(item => item.code === val)[0];
      state.checkTreeId = '';
      proxy.getLeftTree();
    };
    const formTree = ref(null);
    // 新增、编辑时保存树节点
    const handleEditTree = () => {
      formTree.value.validate(valid => {
        if (valid) {
          const params = {
            ...state.dialogFrom,
            parentId: '0',
            categoryCode: state.currentTabsData.code,
            categoryId: state.currentTabsData.id
          };
          if (otherForm.isAddTree) {
            addTreeNode(params).then(function (res) {
              if (res.data.code === 200) {
                showEditDialog.value = false;
                proxy.$message.success('新增成功!');
                proxy.getLeftTree();
              }
            });
          } else {
            updateTreeNode(params).then(function (res) {
              if (res.data.code === 200) {
                showEditDialog.value = false;
                proxy.$message.success('编辑成功!');
                proxy.getLeftTree();
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    // 新增树节点
    const addTreeItem = () => {
      otherForm.dialogTreeData = formatAllTree('', otherForm.dialogTreeData);
      showEditDialog.value = true;
      nextTick(() => {
        state.inputRefDialog.focus();
      });
      otherForm.category = [];
      otherForm.isAddTree = true;
      state.dialogFrom = {};
    };
    // 树节点删除
    const delTree = node => {
      proxy
        .$confirm('是否删除该类目', '提示', {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'warning'
        })
        .then(() => {
          deleteTreeNode({ ids: node.id }).then(function (res) {
            if (res.data.code === 200) {
              proxy.$message.success('删除成功!');
              if (node.id === state.checkTreeId) {
                state.checkTreeId = '';
              }
              proxy.getLeftTree();
            } else {
              proxy.$message.error(res.data.data.message);
            }
          });
        })
        .catch(() => {});
    };
    // 拖拽
    // 过滤树节点
    const refTree = ref(null);
    watch(
      () => otherForm.filterText,
      newValue => {
        refTree.value.filter(newValue);
      }
    );
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.inspectionName.indexOf(value) !== -1;
    };
    const changeTab = value => {
      if (state.activeName2 !== value.props.name) {
        state.activeName2 = value.props.name;
      }
    };
    // 拖拽边框
    const widthChange = m => {
      otherForm.asideWidth -= m;
      if (otherForm.asideWidth <= 80) {
        otherForm.asideWidth = 80;
      }
      if (otherForm.asideWidth >= 600) {
        otherForm.asideWidth = 600;
      }
    };
    const changeIcon = (command, node) => {
      if (!command) {
        node.showIcon = true;
      } else {
        node.showIcon = !node.showIcon;
      }
    };
    // 允许拖拽功能 只能同级拖拽
    const allowDrop = (draggingNode, dropNode, type) => {
      if (draggingNode.level === dropNode.level) {
        // parentid是父节点id
        if (draggingNode.data.parentId === dropNode.data.parentId) {
          return type === 'prev' || type === 'next';
        }
      } else {
        // 不同级进行处理
        return false;
      }
    };
    // 树排序
    const nodeDrop = (before, after) => {
      // console.log(after)
      var orderList = [];
      if (after.parent.level === 0) {
        orderList = JSON.parse(JSON.stringify(after.parent.data));
        _.remove(orderList, function (n) {
          return n.id === 'all';
        });
        orderList.forEach((ol, index) => {
          ol.order = index + 1;
        });
      } else {
        orderList = after.parent.data.children;
        orderList.forEach((ol2, index) => {
          ol2.order = index + 1;
        });
      }
      updateTreeOrder(orderList).then(res => {
        if (res !== false) {
          proxy.$message.success('排序成功');
        }
      });
    };
    return {
      ...toRefs(state),
      allowDrop,
      nodeDrop,
      getPermissionBtn,
      changeIcon,
      clickMaterial,
      changeTab,
      formatDate,
      getNameByid,
      drageHeader,
      formTree,
      widthChange,
      addTreeItem,
      refTree,
      filterNode,
      handleEditTree,
      showEditDialog,
      delTree,
      editTree,
      tableKey,
      editFrom,
      otherForm
    };
  },
  created() {
    this.getLeftTree();
  },
  methods: {
    // 获取左侧列表树接口
    getLeftTree() {
      const vm = this;
      vm.tableLoading = true;
      if (vm.currentTabsData) {
        getTree({ categoryCode: vm.currentTabsData.code, categoryId: vm.currentTabsData.id }).then(res => {
          vm.tableLoading = false;
          if (res) {
            const data = res.data.data;
            vm.otherForm.treeData = formatTree(data);
            vm.treeLength = vm.otherForm.treeData.length;
            vm.otherForm.dialogTreeData = data;
            if (!vm.checkTreeId) {
              // 判断第一次加载时默认选中第一个
              if (data.length > 0) {
                vm.checkTreeId = data[0].id;
                vm.treeTitle = data[0].inspectionName;
                vm.$nextTick(() => {
                  vm.$refs.refTree.setCurrentKey(data[0].id, true);
                });
              } else {
                vm.treeTitle = '';
                vm.tableData = [];
              }
            } else {
              vm.$nextTick(() => {
                vm.$refs.refTree.setCurrentKey(vm.checkTreeId, true);
              });
            }
          }
        });
      }
    },
    clickNode(data, node) {
      this.treeTitle = data.inspectionName;
      if (this.checkTreeId !== data.id) {
        this.checkTreeId = data.id;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
.tree-container {
  .tree-header {
    flex-direction: column;
    .header-select {
      display: flex;
      width: 100%;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 10px;
      :deep(.el-select .el-input__inner) {
        padding-left: 0;
        font-size: 16px;
        color: #303133;
        border: none;
      }
      .icon {
        font-size: 16px;
        margin-right: 10px;
      }
      .el-select {
        width: 100%;
      }
    }
    .header-input-button {
      display: flex;
      width: 100%;
      justify-content: flex-start;
      align-items: center;
    }
  }
  .tree-content {
    height: calc(100vh - 210px);
  }
}
.tab-box {
  :deep(.el-tabs__header) {
    margin-bottom: 20px;
  }
}

.blue-color:last-child {
  margin-right: 10px;
}
</style>
