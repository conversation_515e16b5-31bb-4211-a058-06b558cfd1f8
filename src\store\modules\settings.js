// import variables from '@/styles/element-variables.module.scss'
import color from '@/styles/variables.module.scss';
import defaultSettings from '@/settings';

const { showSettings, tagsView } = defaultSettings;

const state = {
  theme: color.theme,
  menuBg: color.menuBg,
  tesPrimary: color.tesPrimary,
  backgroundColor: color.backgroundColor,
  showSettings: showSettings,
  tagsView: tagsView,
  fixedHeader: true,
  sidebarLogo: true,
  sidebarMode: 'vertical',
  combinationQueryObject: {
    addMaterial: false
  }
};

const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    // eslint-disable-next-line no-prototype-builtins
    if (state.hasOwnProperty(key)) {
      state[key] = value;
      // console.log(state)
    }
  },
  SET_COMBINATION_QUERY: ({ key, value }) => {
    if (state.combinationQueryObject.hasOwnProperty(key)) {
      state.combinationQueryObject[key] = value;
    }
  }
};

const actions = {
  changeSetting({ commit }, data) {
    commit('CHANGE_SETTING', data);
  },
  changeCombinationQuery({ commit }, data) {
    commit('SET_COMBINATION_QUERY', data);
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
