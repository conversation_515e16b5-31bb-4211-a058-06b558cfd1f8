<template>
  <el-input v-if="focus" v-trim v-focus :size="size" :placeholder="placeholder" :prefix-icon="prefixIcon" clearable />
  <el-input v-else v-trim :size="size" :placeholder="placeholder" :prefix-icon="prefixIcon" clearable />
</template>

<script>
export default {
  name: 'UserTag',
  props: {
    size: {
      type: String,
      default: ''
    },
    focus: {
      type: Boolean,
      default: false
    },
    inputValue: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入'
    },
    prefixIcon: {
      type: String,
      default: ''
    }
  }
};
</script>

<style lang="scss" scoped></style>
