// 按钮key值
export const permissionBtnList = [
  {
    id: '131223',
    btnCname: '模板引用新增',
    utype: 'tes',
    umenu: '1318',
    btnName: 'reportAudit'
  },
  {
    id: '131223',
    btnCname: '模板引用新增',
    utype: 'tes',
    umenu: '1318',
    btnName: 'mbRefAdd'
  },
  {
    id: '131222',
    btnCname: '模板引用保存',
    utype: 'tes',
    umenu: '1318',
    btnName: 'mbRefSave'
  },
  {
    id: '131221',
    btnCname: '模板引用删除',
    utype: 'tes',
    umenu: '1318',
    btnName: 'mbRefDelete'
  },
  {
    id: '131220',
    btnCname: '模板引用编辑',
    utype: 'tes',
    umenu: '1318',
    btnName: 'mbRefEdit'
  },
  {
    id: '131219',
    btnCname: '关键参数删除',
    utype: 'tes',
    umenu: '1318',
    btnName: 'keyDelete'
  },
  {
    id: '131813',
    btnCname: '新增物资树分类',
    utype: 'tes',
    umenu: '1318',
    btnName: 'materialTreeAdd'
  },
  {
    id: '130111',
    btnCname: '下达',
    utype: 'tes',
    umenu: '1301',
    btnName: 'sampleOrder'
  },
  {
    id: '130112',
    btnCname: '批量下达',
    utype: 'tes',
    umenu: '1301',
    btnName: 'sampleOrderBatch'
  },
  {
    id: '130311',
    btnCname: '检测分配',
    utype: 'tes',
    umenu: '1303',
    btnName: 'allocation'
  },
  {
    id: '130411',
    btnCname: '添加项目',
    utype: 'tes',
    umenu: '1304',
    btnName: 'addItem'
  },
  {
    id: '130412',
    btnCname: '批量删除',
    utype: 'tes',
    umenu: '1304',
    btnName: 'deleteBatch'
  },
  {
    id: '130413',
    btnCname: '批量分配',
    utype: 'tes',
    umenu: '1304',
    btnName: 'allocationBatch'
  },
  {
    id: '130414',
    btnCname: '保存',
    utype: 'tes',
    umenu: '1304',
    btnName: 'allocationSave'
  },
  {
    id: '130611',
    btnCname: '查看详情',
    utype: 'tes',
    umenu: '1306',
    btnName: 'detailBtn'
  },
  {
    id: '130711',
    btnCname: '编辑',
    utype: 'tes',
    umenu: '1307',
    btnName: 'executeEdit'
  },
  {
    id: '130712',
    btnCname: '保存提交',
    utype: 'tes',
    umenu: '1307',
    btnName: 'executeSumbit'
  },
  {
    id: '130713',
    btnCname: '提交',
    utype: 'tes',
    umenu: '1307',
    btnName: 'executeSave'
  },
  {
    id: '130714',
    btnCname: '审核',
    utype: 'tes',
    umenu: '1307',
    btnName: 'executeAudit'
  },
  {
    id: '130715',
    btnCname: '退回',
    utype: 'tes',
    umenu: '1307',
    btnName: 'executeBack'
  },
  {
    id: '130716',
    btnCname: '复测',
    utype: 'tes',
    umenu: '1307',
    btnName: 'executeAgain'
  },
  {
    id: '130717',
    btnCname: '设备管理',
    utype: 'tes',
    umenu: '1307',
    btnName: 'device'
  },
  {
    id: '130718',
    btnCname: '添加样品领用',
    utype: 'tes',
    umenu: '1307',
    btnName: 'recordAdd'
  },
  {
    id: '130719',
    btnCname: '编辑样品领用记录',
    utype: 'tes',
    umenu: '1307',
    btnName: 'recordEdit'
  },
  {
    id: '130720',
    btnCname: '删除样品领用记录',
    utype: 'tes',
    umenu: '1307',
    btnName: 'recordDelete'
  },
  {
    id: '130721',
    btnCname: '添加原始记录模板',
    utype: 'tes',
    umenu: '1307',
    btnName: 'templateAdd'
  },
  {
    id: '130722',
    btnCname: '上传实验图片',
    utype: 'tes',
    umenu: '1307',
    btnName: 'photoAdd'
  },
  {
    id: '130723',
    btnCname: '打印',
    utype: 'tes',
    umenu: '1307',
    btnName: 'printBtn'
  },
  {
    id: '130724',
    btnCname: '批量打印',
    utype: 'tes',
    umenu: '1307',
    btnName: 'batchPrint'
  },
  {
    id: '130911',
    btnCname: '审核',
    utype: 'tes',
    umenu: '1309',
    btnName: 'recordAudit'
  },
  {
    id: '131011',
    btnCname: '新增',
    utype: 'tes',
    umenu: '1310',
    btnName: 'reportAdd'
  },
  {
    id: '131012',
    btnCname: '编辑',
    utype: 'tes',
    umenu: '1310',
    btnName: 'reportEdit'
  },
  {
    id: '131013',
    btnCname: '提交',
    utype: 'tes',
    umenu: '1310',
    btnName: 'reportSumbit'
  },
  {
    id: '131014',
    btnCname: '下载',
    utype: 'tes',
    umenu: '1310',
    btnName: 'downDocx'
  },
  {
    id: '131015',
    btnCname: '删除',
    utype: 'tes',
    umenu: '1310',
    btnName: 'reportDelete'
  },
  {
    id: '131111',
    btnCname: '下一步',
    utype: 'tes',
    umenu: '1311',
    btnName: 'reportNext'
  },
  {
    id: '131112',
    btnCname: '保存',
    utype: 'tes',
    umenu: '1311',
    btnName: 'reportSave'
  },
  {
    id: '131113',
    btnCname: '上传图片',
    utype: 'tes',
    umenu: '1311',
    btnName: 'photoUp'
  },
  {
    id: '131211',
    btnCname: '添加项目',
    utype: 'tes',
    umenu: '1312',
    btnName: 'itemAdd'
  },
  {
    id: '131212',
    btnCname: '模板查看',
    utype: 'tes',
    umenu: '1312',
    btnName: 'templateDetail'
  },
  {
    id: '131213',
    btnCname: '新增项目库',
    utype: 'tes',
    umenu: '1312',
    btnName: 'libraryAdd'
  },
  {
    id: '131214',
    btnCname: '编辑项目库',
    utype: 'tes',
    umenu: '1312',
    btnName: 'libraryEdit'
  },
  {
    id: '131215',
    btnCname: '项目编辑',
    utype: 'tes',
    umenu: '1312',
    btnName: 'itemEdit'
  },
  {
    id: '131216',
    btnCname: '关键参数新增',
    utype: 'tes',
    umenu: '1312',
    btnName: 'keyAdd'
  },
  {
    id: '131217',
    btnCname: '关键参数启用禁用',
    utype: 'tes',
    umenu: '1312',
    btnName: 'keyStatusChange'
  },
  {
    id: '131218',
    btnCname: '关键参数编辑',
    utype: 'tes',
    umenu: '1312',
    btnName: 'keyEdit'
  },
  {
    id: '131311',
    btnCname: '新增树分类',
    utype: 'tes',
    umenu: '1313',
    btnName: 'itemTreeAdd'
  },
  {
    id: '131411',
    btnCname: '查看模板',
    utype: 'tes',
    umenu: '1314',
    btnName: 'templateLook'
  },
  {
    id: '131412',
    btnCname: '新建模板',
    utype: 'tes',
    umenu: '1314',
    btnName: 'templateAdd'
  },
  {
    id: '131413',
    btnCname: '切换模板',
    utype: 'tes',
    umenu: '1314',
    btnName: 'templatechange'
  },
  {
    id: '131414',
    btnCname: '打印模板',
    utype: 'tes',
    umenu: '1314',
    btnName: 'templatePrint'
  },
  {
    id: '131511',
    btnCname: '新增树分类',
    utype: 'tes',
    umenu: '1315',
    btnName: 'gistTreeAdd'
  },
  {
    id: '131512',
    btnCname: '添加产品',
    utype: 'tes',
    umenu: '1315',
    btnName: 'gistAdd'
  },
  {
    id: '131513',
    btnCname: '编辑产品',
    utype: 'tes',
    umenu: '1315',
    btnName: 'gistEdit'
  },
  {
    id: '131514',
    btnCname: '编辑项目库',
    utype: 'tes',
    umenu: '1315',
    btnName: 'libraryEdit'
  },
  {
    id: '131515',
    btnCname: '启禁用',
    utype: 'tes',
    umenu: '1315',
    btnName: 'gistStatusChange'
  },
  {
    id: '131516',
    btnCname: '删除',
    utype: 'tes',
    umenu: '1315',
    btnName: 'gistDelete'
  },
  {
    id: '131517',
    btnCname: '复制',
    utype: 'tes',
    umenu: '1315',
    btnName: 'gistCopy'
  },
  {
    id: '131611',
    btnCname: '编辑',
    utype: 'tes',
    umenu: '1316',
    btnName: 'settingEdit'
  },
  {
    id: '131711',
    btnCname: '新增',
    utype: 'tes',
    umenu: '1317',
    btnName: 'productAdd'
  },
  {
    id: '131712',
    btnCname: '编辑',
    utype: 'tes',
    umenu: '1317',
    btnName: 'productEdit'
  },
  {
    id: '131713',
    btnCname: '删除',
    utype: 'tes',
    umenu: '1317',
    btnName: 'productDelete'
  },
  {
    id: '131811',
    btnCname: '上传报告',
    utype: 'tes',
    umenu: '1318',
    btnName: 'reportUpload'
  },
  {
    id: '131812',
    btnCname: '启禁用',
    utype: 'tes',
    umenu: '1318',
    btnName: 'reportStatusChange'
  }
];
// 系统权限分组编码
export const permissionTypeList = {
  taskReview: 'allocation',
  taskConfirm: 'allocation',
  experimentReviewOwner: 'allocation',
  experimentOwner: 'allocation',
  sampleOwner: 'allocation',
  reportSubmit: 'allocation',
  reportReview: 'allocation',
  reportSign: 'allocation',
  reportChop: 'allocation',
  reportArchive: 'allocation',
  reportSend: 'allocation',
  deviceOwner: 'allocation',
  measurementOwner: 'allocation'
};
