// base64转成文件流
export function conversion(imgBase64, fileName) {
  var arr = imgBase64.split(',');
  var mime = arr[0].match(/:(.*?);/)[1];
  var bstr = window.atob(arr[1]);
  var n = bstr.length;
  var u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], fileName, { type: mime });
}

export function downloadFileByUrl(fileUrl, fileName) {
  const a = document.createElement('a');
  if (fileName) {
    a.download = fileName;
  }
  a.style.display = 'none';
  a.href = fileUrl;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

export async function downloadFileWithFetch(url, filename) {
  try {
    // 获取文件数据
    const response = await fetch(url);
    const blob = await response.blob();

    // 创建对象URL
    const blobUrl = window.URL.createObjectURL(blob);

    // 创建隐藏的<a>标签
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = blobUrl;
    link.download = filename;

    // 添加到DOM并触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    window.URL.revokeObjectURL(blobUrl);
    document.body.removeChild(link);
  } catch (error) {
    console.error('下载文件时出错:', error);
  }
}
