import request from '@/utils/request';
import qs from 'qs';
const postHeaders = {
  'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
};

// 租户授权详情列
export function getDetailList(strId) {
  return request({
    url: `/api-user/user/oauthinformation/selectAllPermission/${strId}`,
    method: 'get'
  });
}
// 租户授权保存
export function savePermissionTenant(data) {
  return request({
    url: `/api-user/user/oauthinformation/saveOrUpdatePermission`,
    method: 'post',
    data
  });
}
// 基础配置 获取信息
export function getSettingInfo(tenantId) {
  return request({
    url: `/api-user/user/oauthinformation/config/${tenantId}`,
    method: 'get'
  });
}
// 设置系统名称
export function configName(data) {
  const datas = qs.stringify(data);
  return request({
    url: `/api-user/user/oauthinformation/config/name/${data.tenantId}`,
    method: 'post',
    headers: postHeaders,
    data: datas
  });
}
// 设置模板表头样式
export function configStyle(data) {
  const datas = qs.stringify(data);
  return request({
    url: `/api-user/user/oauthinformation/config/style/${data.tenantId}`,
    method: 'post',
    headers: postHeaders,
    data: datas
  });
}
// 更新模板分页是否重复模板头
export function pageRepeatHead(data) {
  const datas = qs.stringify(data);
  return request({
    url: `/api-user/user/oauthinformation/config/pageRepeatHead/${data.tenantId}`,
    method: 'post',
    headers: postHeaders,
    data: datas
  });
}
// 删除导航栏展开logo
export function deleteLogo(tenantId) {
  return request({
    url: `/api-user/user/oauthinformation/config/logo/normal/delete/${tenantId}`,
    method: 'get'
  });
}
