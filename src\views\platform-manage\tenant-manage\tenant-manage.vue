<template>
  <!-- 租户管理 -->
  <ListLayout
    class="tenant-manage"
    :has-search-panel="false"
    :has-quick-query="false"
    :has-button-group="getPermissionBtn('addTenantBtn') ? true : false"
  >
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="condition">
          <el-input
            v-model="formInline.key"
            v-trim
            v-focus
            placeholder="请输入租户简称/租户名/联系人/电话"
            class="ipt-360"
            prefix-icon="el-icon-search"
            size="large"
            clearable
            @keyup.enter="onSubmit"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="onSubmit">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button type="primary" icon="el-icon-plus" size="large" @click="addAccount">新增租户</el-button>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      size="medium"
      height="auto"
      class="dark-table base-table format-height-table no-quick-query"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column label="租户简称" prop="clientShortName" :width="colWidth.name" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.clientShortName || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="租户名" prop="clientName" :min-width="colWidth.name" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.clientName || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="租户类型" prop="clientType" :width="colWidth.name" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <div>
            {{ row.clientType === 1 ? '工厂实验室' : (row.clientType === 2 ? '第三方实验室' : '第二方企业') || '--' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="联系人" prop="person" :width="colWidth.people" show-overflow-tooltip>
        <template #default="{ row }">
          <UserTag :name="row.person || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="联系电话" prop="personMobile" :width="colWidth.datetime" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.personMobile || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="管理员" prop="managerName" :width="colWidth.people" show-overflow-tooltip>
        <template #default="{ row }">
          <UserTag width="120px" :name="row.managerName || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" :width="colWidth.status" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="row.status === 0 ? 'info' : 'success'">{{
            row.status === 0 ? '已停用' : '已启用' || '--'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="水印" prop="isWatermark" :width="colWidth.status" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="row.isWatermark ? 'success' : 'info'">{{
            row.isWatermark ? '已启用' : '已停用' || '--'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="租户编号" prop="clientCode" :min-width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.clientCode || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="
          getPermissionBtn('tenantAuthorizeBtn') ||
          getPermissionBtn('editTenantBtn') ||
          getPermissionBtn('basicConfigurationBtn')
        "
        label="操作"
        :width="colWidth.operationMultiple"
        prop="caozuo"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <span v-if="getPermissionBtn('editTenantBtn')" class="blue-color" @click="handleAM(row)">编辑</span>
          <span v-if="getPermissionBtn('tenantAuthorizeBtn')" class="blue-color" @click="handleSQ(row)">授权</span>
          <span v-if="getPermissionBtn('basicConfigurationBtn')" class="blue-color" @click="handleBasic(row)"
            >基础配置</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
    <template #other>
      <add-edit-tenant
        :show="showDialog"
        :title="dialogTitle"
        :data="dialogData"
        @close="closeDialog"
        @setInfo="setInfo"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, toRefs, getCurrentInstance } from 'vue';
// import { ElMessage, ElMessageBox } from 'element-plus'
import ListLayout from '@/components/ListLayout';
// import { ElMessage, ElMessageBox } from 'element-plus'
import { getPermissionBtn } from '@/utils/common';
// import { useStore } from 'vuex'
import { getLoginInfo } from '@/utils/auth';
// import _ from 'lodash'
// import { checkPermissionList } from '@/api/permission'
// import { permissionTypeList } from '@/utils/permissionList'
import Pagination from '@/components/Pagination';
import router from '@/router/index.js';
import UserTag from '@/components/UserTag';
import { drageHeader } from '@/utils/formatTable';
import { getTenantList } from '@/api/platform-management';
import AddEditTenant from './AddEditTenant.vue';
import { colWidth } from '@/data/tableStyle';

export default {
  name: 'TenantManage',
  components: { Pagination, AddEditTenant, ListLayout, UserTag },
  setup() {
    const { proxy } = getCurrentInstance();
    // const store = useStore().state
    const datas = reactive({
      accountId: getLoginInfo().accountId,
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: ''
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      list: [],
      formInline: {
        key: ''
      },
      showDialog: false,
      dialogTitle: '',
      dialogData: null
    });
    // 查询
    function onSubmit() {
      proxy.getList();
    }
    // 重置
    function reset() {
      console.log('reset');
      datas.formInline = {
        key: ''
      };
      datas.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: ''
      };
      proxy.getList();
    }
    // table排序
    const sortChange = data => {
      const { prop, order } = data;
      datas.listQuery.orderBy = prop;
      if (order === 'ascending') {
        datas.listQuery.isAsc = true;
      } else if (order === 'descending') {
        datas.listQuery.isAsc = false;
      } else {
        datas.listQuery.isAsc = '';
      }
    };
    // 编辑操作
    const handleAM = row => {
      datas.dialogTitle = 'edit';
      datas.showDialog = true;
      datas.dialogData = row;
    };
    // 新增租户
    const addAccount = () => {
      datas.dialogTitle = 'add';
      datas.showDialog = true;
      datas.dialogData = {
        status: 1,
        clientType: 1,
        isWatermark: false
      };
    };
    // 关闭弹出框
    const closeDialog = v => {
      datas.showDialog = false;
    };
    // 弹出框-确认
    const setInfo = data => {
      datas.showDialog = false;
      proxy.getList();
    };
    // 授权
    const handleSQ = row => {
      router.push({
        path: '/platform-manage/tenant-manage/detail',
        query: {
          strId: row.strId
        }
      });
    };
    // 基础配置
    const handleBasic = row => {
      router.push({
        path: '/platform-manage/tenant-manage/DetailBasic',
        query: {
          strId: row.strId,
          clientShortName: row.clientShortName
        }
      });
    };

    return {
      ...toRefs(datas),
      onSubmit,
      reset,
      sortChange,
      getPermissionBtn,
      handleAM,
      handleSQ,
      handleBasic,
      drageHeader,
      addAccount,
      closeDialog,
      setInfo,
      colWidth
    };
  },
  created() {
    this.getList();
    // 刷新列表
    this.bus.$on('tenantManageList', msg => {
      this.getList();
    });
  },
  methods: {
    getList(data) {
      const _this = this;
      // _this.listLoading = true
      if (data && data !== undefined) {
        _this.listQuery.page = data.page;
        _this.listQuery.limit = data.limit;
      }
      const param = Object.assign(_this.formInline, _this.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      getTenantList(param).then(res => {
        if (res !== false) {
          const { data } = res.data;
          _this.list = data.list;
          _this.total = data.totalCount;

          // this.getAllList()
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
    }
  }
};
</script>
<style lang="scss" scoped></style>
