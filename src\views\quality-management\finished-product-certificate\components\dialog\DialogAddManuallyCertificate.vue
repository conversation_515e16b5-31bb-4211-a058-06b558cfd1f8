<template>
  <el-dialog
    v-model="dialogShow"
    top="5vh"
    destroy-on-close
    title="复制合格证"
    width="950px"
    :close-on-click-modal="false"
    @close="cancelDialog()"
  >
    <el-form ref="ruleForm" :model="formData" label-position="right" class="form-class overflow-y-auto pr-2">
      <el-row>
        <el-col v-for="field in pageViewAll" :key="field.fieldKey" :span="12">
          <el-form-item
            :label="`${field.fieldName}：`"
            :label-width="`${labelWidth[field.fieldKey] || 110}px`"
            :prop="field.fieldKey"
          >
            {{ formData[field.fieldKey] }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-row :gutter="10">
            <el-col :span="20">
              <el-form-item
                label="生产段长："
                prop="realReelLength"
                label-width=" 110px"
                :rules="{
                  required: true,
                  message: '请输入生产段长',
                  trigger: 'change'
                }"
              >
                <el-input-number
                  v-model="formData.realReelLength"
                  class="!w-full"
                  :min="0"
                  controls-position="right"
                  placeholder="请输入生产段长"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4" class="text-center">
              <el-select
                v-model="formData.inputWarehouseUnit"
                filterable
                disabled
                :placeholder="`请选择`"
                class="w-full"
              >
                <el-option v-for="(val, key) in dictionaryAll['5']?.enable" :key="key" :label="val" :value="key" />
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="盘数："
            prop="reelNum"
            label-width=" 110px"
            :rules="{
              required: true,
              message: '请输入盘数',
              trigger: 'change'
            }"
          >
            <el-input-number
              v-model="formData.reelNum"
              class="!w-full"
              :min="0"
              controls-position="right"
              placeholder="请输入盘数"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button :loading="dialogLoading" @click="cancelDialog()">取消</el-button>
      <el-button :loading="dialogLoading" type="primary" @click="handleSubmit()">确定</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, watch, ref, toRefs } from 'vue';
import { certificateprintItemCopy } from '@/api/certificate-export';
import { ElMessage } from 'element-plus';
export default {
  name: 'DialogAddManuallyCertificate',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dictionary: {
      type: Object,
      default: () => ({})
    },
    salesOrderItem: {
      type: Object,
      default: () => ({})
    },
    salesOrder: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      formData: {
        smallSerialNumber: []
      },
      dictionaryAll: {
        5: {
          all: {},
          enable: {}
        }
      },
      pageViewAll: [
        {
          fieldKey: 'projectName',
          fieldName: '项目名称',
          readonly: true
        },
        {
          fieldKey: 'materialNo',
          fieldName: '物料编号',
          readonly: true
        },
        {
          fieldKey: 'materialDesc',
          fieldName: '物料描述',
          readonly: true
        },
        {
          fieldKey: 'salesOrderCode',
          fieldName: '销售订单号',
          readonly: true
        },
        {
          fieldKey: 'salesOrderItemNo',
          fieldName: '销售行号',
          readonly: true
        },
        {
          fieldKey: 'productionOrderNo',
          fieldName: '生产订单号',
          readonly: true
        },
        {
          fieldKey: 'nuclearMarker',
          fieldName: '特殊标识',
          readonly: true
        }
      ],
      dialogLoading: false,
      labelWidth: {
        //  allowedOperatingTemperature: 150,
        // suppDate: 150,
        // reelDesc: 150
      },

      dialogShow: false,
      ruleForm: ref()
    });

    const cancelDialog = value => {
      state.dialogShow = false;
      context.emit('closeDialog', value);
    };

    watch(
      () => props.dialogVisible,
      newValue => {
        state.dialogShow = newValue;
        if (newValue) {
          state.formData = { ...props.salesOrderItem, ...props.salesOrder };
        }

        if (isNaN(parseFloat(props.salesOrderItem.realReelLength))) {
          state.formData.realReelLength = undefined;
        }
        if (isNaN(parseFloat(props.salesOrderItem.reelNum))) {
          state.formData.reelNum = undefined;
        }

        state.dictionaryAll = props.dictionary || {
          5: {
            all: {},
            enable: {}
          }
        };
      }
    );
    const handleSubmit = async () => {
      state.ruleForm.validate().then(async valid => {
        if (valid) {
          state.dialogLoading = true;
          const { data } = await certificateprintItemCopy({
            reelNum: state.formData.reelNum,
            realReelLength: state.formData.realReelLength,
            id: props.salesOrderItem.id
          }).finally(() => (state.dialogLoading = false));
          if (data) {
            ElMessage.success('合格证复制成功');
            cancelDialog(true);
          }
        }
      });
    };

    return {
      ...toRefs(state),
      cancelDialog,
      handleSubmit
    };
  }
};
</script>
<style lang="scss" scoped>
.form-class {
  max-height: 600px;
}
:deep(.el-input-group__append) {
  padding: 0 !important;
}
</style>
