<template>
  <el-dialog
    :model-value="showDialog"
    custom-class="add-report"
    title="选择样品"
    width="1000px"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <el-row class="add-report-search">
      <el-col :span="24">
        <el-input
          v-model="filterText"
          v-trim
          class="search"
          size="small"
          placeholder="请输入编号/样品名称"
          prefix-icon="el-icon-search"
          clearable
          @keyup.enter="searchItem(filterText)"
        />
        <el-button type="primary" size="small" style="margin-left: 15px" @click="searchItem(filterText)"
          >查询</el-button
        >
        <el-button size="small" @click="reset">重置</el-button>
      </el-col>
    </el-row>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="calc(100vh - 400px)"
      size="medium"
      class="dark-table base-table add-report-table"
      @header-dragend="drageHeader"
      @row-click="handleRowClick"
    >
      <el-table-column :width="50" align="center">
        <template #default="{ row }">
          <el-radio v-model="currentId" :label="row.id" @change="changeRedio(row)">&nbsp;</el-radio>
        </template>
      </el-table-column>
      <el-table-column label="样品编号" prop="secSampleNum" :min-width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.secSampleNum || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="tenantInfo.type === 1 ? '申请单号' : '委托编号'"
        prop="presentationCode"
        :width="colWidth.orderNo"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span class="nowrap">{{ row.presentationCode ? row.presentationCode : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="样品名称" prop="mateName" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.mateName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="试验负责人" prop="user" :width="colWidth.person">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.ownerId) || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="样品状态" prop="status" :width="colWidth.status" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="filterStatus(row.status)[0]">
            {{ filterStatus(row.status)[1] }}</el-tag
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      small
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getSampleOrderList"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button
          type="primary"
          :loading="addReportLoading"
          @click="dialogSuccess"
          @keyup.prevent
          @keydown.enter.prevent
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, toRefs, watch, getCurrentInstance } from 'vue';
import { getNameByid } from '@/utils/common';
import { samplesList } from '@/api/order';
// import { GetInventorySampleBySampleId } from '@/api/login'
import { ElMessage } from 'element-plus';
import Pagination from '@/components/Pagination';
import UserTag from '@/components/UserTag';
import { drageHeader } from '@/utils/formatTable';
import { mapGetters } from 'vuex';
// import { formatDate } from '@/utils/formatTime'
// import { getWarehousinginfo } from '@/api/samplestorage'
import { colWidth } from '@/data/tableStyle';
// import _ from 'lodash'
// import { ElLoading } from 'element-plus'
// import { useRoute } from 'vue-router'
// import { formatPaginationList } from '@/utils/formatJson'

export default {
  name: 'DialogSpecimen',
  components: { Pagination, UserTag },
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const tableRef = ref(null);
    const state = reactive({
      showDialog: false,
      filterText: '',
      currentId: '',
      listLoading: false,
      selectData: {}, // 选择的数据
      tableKey: 0,
      list: [],
      total: 0,
      listQuery: {
        page: 1,
        limit: 20,
        param: ''
      },
      newList: [],
      addReportLoading: false
    });

    watch(props, newValue => {
      state.showDialog = props.dialogShow;
      if (state.showDialog) {
        state.currentId = '';
        state.filterText = '';
        state.selectData = {};
        state.listQuery = {
          page: 1,
          limit: 20,
          param: ''
        };
        proxy.getSampleOrderList();
      }
    });

    // 过滤样品状态颜色
    const filterStatus = status => {
      status = status.toString();
      const classMap = {
        0: ['info', '待下达'],
        1: ['warning', '检测中'],
        2: ['warning', '检测中'],
        3: ['warning', '检测中'],
        4: ['success', '检测完成']
      };
      return classMap[status];
    };

    // 查询
    const searchItem = value => {
      state.listQuery.param = value;
      proxy.getSampleOrderList();
    };
    // 重置列表
    const reset = () => {
      state.currentId = '';
      state.filterText = '';
      state.selectData = {};
      state.listQuery = {
        page: 1,
        limit: 20,
        param: ''
      };
      proxy.getSampleOrderList();
    };
    // 确定选择
    const dialogSuccess = () => {
      if (state.selectData.sampleNo) {
        context.emit('closeDialog', { selectedData: state.selectData });
      } else {
        ElMessage.warning('请勾选样品');
      }
    };
    // 取消选择
    const close = () => {
      state.showDialog = false;
      state.listQuery = {
        page: 1,
        limit: 20,
        param: ''
      };
      state.filterText = '';
      context.emit('closeDialog');
    };
    // 表格行点击
    const handleRowClick = row => {
      state.currentId = row.id;
      state.selectData = {
        orderId: row.orderId,
        sampleId: row.sampleId,
        sampleNo: row.secSampleNum
      };
    };
    // 选择的样品行
    const changeRedio = row => {
      state.selectData = {
        orderId: row.orderId,
        sampleId: row.sampleId,
        sampleNo: row.secSampleNum
      };
    };

    return {
      ...toRefs(state),
      close,
      reset,
      changeRedio,
      getNameByid,
      dialogSuccess,
      drageHeader,
      handleRowClick,
      filterStatus,
      searchItem,
      tableRef,
      colWidth
    };
  },
  computed: {
    ...mapGetters(['tenantInfo'])
  },
  created() {
    // this.getSampleOrderList()
  },
  methods: {
    getSampleOrderList(value) {
      const that = this;
      that.listLoading = true;
      if (value && value !== undefined) {
        that.listQuery.page = value.page;
        that.listQuery.limit = value.limit;
      }
      const param = JSON.parse(JSON.stringify(that.listQuery));
      param.page = param.page + '';
      param.limit = param.limit + '';
      param.status = '1';
      samplesList(param).then(res => {
        if (res) {
          that.list = res.data.data.list;
          that.total = res.data.data.totalCount;
          if (that.list.length > 0) {
            that.list.forEach((order, index) => {
              order.selected = false;
              order.sampleName = order.mateName;
              order.presentationCorporation = '';
            });
          }
        }
        that.listLoading = false;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.add-report {
  .search {
    width: 360px;
  }

  .add-report-search {
    margin-bottom: 15px;
  }
}
</style>
<style lang="scss">
.add-report {
  .el-table thead th {
    background: #f6f6f6;
  }
  .el-dialog__body {
    padding-bottom: 0;
  }
}
</style>
