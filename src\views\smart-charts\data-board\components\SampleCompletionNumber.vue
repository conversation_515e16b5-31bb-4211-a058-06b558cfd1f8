<template>
  <!-- 近15天样品完成数 -->
  <div class="box-top">
    <h1 class="inBlock">近15天样品完成数</h1>
    <div class="top-right inBlock">{{ formatDate(recentlyFifteen) }} ~ {{ formatDate(new Date()) }}</div>
  </div>
  <div v-loading="sampleCompletLoading" element-loading-background="#7AD0FF" class="box-Center">
    <LineBarPieChart :option="xBarChartOption" :width="'100%'" :height="'100%'" />
  </div>
</template>

<script>
import { reactive, ref, toRefs, onBeforeUnmount } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getNameByid } from '@/utils/common';
import LineBarPieChart from '@/components/LineBarPieChart';
import { mapGetters, mapState } from 'vuex';
import { getFinishList } from '@/api/dataBoard';
import { formatterTips } from '../../func/formatter';

export default {
  name: 'SampleCompletionNumber',
  components: { LineBarPieChart },
  setup(props, context) {
    const state = reactive({
      recentlyFifteen: new Date().getTime() - 3600 * 1000 * 24 * 14, // 最近15天
      sampleCompletJson: {}, // 近15天样品完成数
      sampleCompletLoading: false, // 近15天样品完成loading
      timer: null // 定时器
    });
    // 折线统计图
    const xBarChartOption = ref({
      tooltip: {
        show: true,
        backgroundColor: '#4791FF',
        borderColor: 'transparent',
        formatter: params => {
          return formatterTips(params);
        },
        textStyle: {
          color: '#fff',
          fontSize: 12,
          fontWeight: 400
        },
        trigger: 'axis'
      },
      grid: {
        width: 'auto',
        left: '10%',
        right: '2%',
        bottom: '14.5%'
      },
      legend: {
        type: 'plain',
        show: true,
        right: 18,
        top: 18,
        textStyle: {
          color: '#7AD0FF',
          fontSize: 12
        }
      },
      xAxis: {
        type: 'category',
        data: Object.keys(state.sampleCompletJson),
        axisLine: {
          lineStyle: {
            color: '#7AD0FF'
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '单位：个',
        splitLine: {
          show: true,
          lineStyle: {
            color: '#5397CE'
          }
        },
        nameTextStyle: {
          color: '#7AD0FF',
          fontSize: '12'
        },
        axisLine: {
          lineStyle: {
            color: '#7AD0FF'
          }
        }
      },
      series: [
        {
          name: '样品数',
          data: Object.values(state.sampleCompletJson),
          type: 'line',
          legendHoverLink: true,
          symbolSize: 7,
          borderColor: '#7CEFF6',
          symbol: 'circle',
          color: '#3A71B1',
          animation: true,
          lineStyle: {
            color: '#7CEFF6'
          },
          emphasis: {
            scale: true
          },
          itemStyle: {
            color: '#3A71B1',
            borderWidth: 2,
            borderColor: '#7CEFF6'
          },
          areaStyle: {
            color: {
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 1,
                  color: '#7AD0FF'
                },
                {
                  offset: 0,
                  color: '#7CF6C3' // 100% 处的颜色
                }
              ]
            }
          }
        }
      ]
    });
    onBeforeUnmount(() => {});
    // 近15天样品完成数
    const getSampleFinish = isFirst => {
      state.sampleCompletLoading = true;
      getFinishList().then(res => {
        state.sampleCompletLoading = false;
        if (res) {
          state.sampleCompletJson = res.data.data;
          xBarChartOption.value.xAxis.data = formatDates(Object.keys(state.sampleCompletJson));
          xBarChartOption.value.series[0].data = Object.values(state.sampleCompletJson);
          if (isFirst) {
            setTime();
          }
        }
      });
    };
    getSampleFinish(true);
    const setTime = () => {
      state.timer = setInterval(() => {
        getSampleFinish();
      }, 10000);
    };
    const removeTimer = () => {
      if (state.timer) {
        clearInterval(state.timer);
        state.timer = null;
      }
    };

    // 过滤时间数组，去掉年份以/拼接
    const formatDates = array => {
      var newArray = [];
      newArray = array.map(item => {
        return item.substring(5).replace('-', '/');
      });
      return newArray;
    };

    return {
      ...toRefs(state),
      getNameByid,
      setTime,
      removeTimer,
      formatDate,
      formatDates,
      getSampleFinish,
      xBarChartOption
    };
  },
  computed: {
    ...mapGetters(['sidebar', 'tenantGroup']),
    ...mapState(['tenantInfo'])
  }
};
</script>
<style lang="scss" scoped>
@import '../data-board.scss';
</style>
