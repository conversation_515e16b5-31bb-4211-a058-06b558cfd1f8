import request from '@/utils/request';
// 设备管理-》设备维修记录

// 查询列表
export function recordList(data) {
  return request({
    url: `/api-device/device/fixed_record/query`,
    method: 'post',
    data
  });
}
// 新增维修记录
export function addRecordApi(data) {
  return request({
    url: `/api-device/device/fixed_record/save`,
    method: 'post',
    data
  });
}
// 编辑维修记录
export function editRecordApi(data) {
  return request({
    url: `/api-device/device/fixed_record/update`,
    method: 'post',
    data
  });
}
// 删除维修记录
export function deleteRecordApi(id) {
  return request({
    url: `/api-device/device/fixed_record/delete/${id}`,
    method: 'delete'
  });
}
// 保存设备附件信息
export function saveDeviceFile(data) {
  return request({
    url: `/api-device/device/device/saveAttachment`,
    method: 'post',
    data
  });
}
// 获取设备附件信息
export function findAttachment(id) {
  return request({
    url: `/api-device/device/device/findAttachment/${id}`,
    method: 'get'
  });
}
// 附件信息删除
export function attachmentDelete(id) {
  return request({
    url: `/api-device/attachment/delete/${id}`,
    method: 'post'
  });
}
// 获取设备附件信息
export function updateAttachment(data) {
  return request({
    url: `/api-device/attachment/updateAttachment`,
    method: 'post',
    data
  });
}
// 根据附件id下载附件
export function downloadById(id) {
  return request({
    url: `/api-device/attachment/downloadById/${id}`,
    method: 'get',
    responseType: 'blob'
  });
}
