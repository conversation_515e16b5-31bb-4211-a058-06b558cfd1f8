<template>
  <div @click="toggleClick">
    <i class="iconfont hamburger" :class="isActive ? 'is-active tes-lists' : 'tes-lists-expand'" />
  </div>
</template>

<script>
import { defineComponent } from 'vue';

export default defineComponent({
  name: '<PERSON><PERSON>',
  props: {
    isActive: {
      type: Boolean,
      default: false
    }
  },
  emits: ['toggleClick'],
  setup(props, { emit }) {
    const toggleClick = () => {
      emit('toggleClick');
    };
    return {
      toggleClick
    };
  }
});
</script>

<style lang="scss" scoped>
.hamburger {
  font-size: 24px;
  color: #909399;
}

.hamburger:hover {
  color: $tes-primary;
}
</style>
