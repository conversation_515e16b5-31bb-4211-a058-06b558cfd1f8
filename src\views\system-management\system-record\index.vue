<template>
  <!-- 体系记录 -->
  <ListLayout>
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="param">
          <div style="width: 42vw">
            <CombinationQuery
              :field-list="searchFieldList"
              :field-tip="fieldTip"
              @get-single-text="getSingleText"
              @get-param-list="getParamList"
              @reset-search="reset"
            />
          </div>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('UploadSystemRecord')"
        size="large"
        icon="el-icon-upload"
        type="primary"
        :loading="listLoading"
        :disabled="selectList.length == 0"
        @click="handleUploadPDF()"
        @keyup.prevent
        @keydown.enter.prevent
        >导出文件</el-button
      >
      <el-button
        v-if="getPermissionBtn('AddSystemRecord')"
        size="large"
        icon="el-icon-plus"
        type="primary"
        :loading="listLoading"
        @click="handleAddEditTemplate('add')"
        @keyup.prevent
        @keydown.enter.prevent
        >新建记录</el-button
      >
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="16">
          <el-radio-group v-model="formInline.status" size="small" @change="getList()">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button :label="1">待提交</el-radio-button>
            <el-radio-button :label="2">待审批</el-radio-button>
            <el-radio-button :label="3">待批准</el-radio-button>
            <el-radio-button :label="4">已完成</el-radio-button>
          </el-radio-group>
        </el-col>
        <el-col :span="8" style="text-align: right">
          <TableColumnView binding-menu="SystemRecord" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="tableList"
      size="medium"
      fit
      border
      height="auto"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      class="dark-table test-item-table base-table format-height-table"
      @header-dragend="drageHeader"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column v-if="getPermissionBtn('UploadSystemRecord')" type="selection" width="55" />
      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="item.fieldType === fieldTypesEnum.Person">
              <UserTag :name="getNameByid(row[item.fieldKey]) || row[item.fieldKey] || '--'" />
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Date">
              <span>{{ row[item.fieldKey] == '' ? '--' : formatDate(row[item.fieldKey]) }}</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Custom">
              <div v-if="item.fieldKey === 'designTemplateType'" class="nowrap">
                {{ templateTypeJSON[row.designTemplateType] || row.designTemplateType || '--' }}
              </div>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Status">
              <el-tag size="small" effect="dark" :type="statusJSON[row.status]?.type">{{
                statusJSON[row.status]?.label
              }}</el-tag>
            </template>
            <span v-else>{{ row[item.fieldKey] || '--' }}</span>
          </template>
        </el-table-column>
      </template>
      <el-table-column
        v-if="
          getPermissionBtn('CheckSystemRecord') ||
          getPermissionBtn('SubmitSystemRecord') ||
          getPermissionBtn('ApproveSystemRecord') ||
          getPermissionBtn('ScheduleSystemRecord') ||
          getPermissionBtn('DeleteSystemRecord')
        "
        label="操作"
        :min-width="colWidth.operationMultiple"
        class-name="fixed-right"
        fixed="right"
      >
        <template #default="{ row }">
          <span
            v-if="getPermissionBtn('CheckSystemRecord')"
            class="blue-color"
            @click.stop="handleAddEditTemplate('check', row)"
            >查看</span
          >
          <span
            v-if="getPermissionBtn('SubmitSystemRecord') && row.status == 1 && row.createBy == currentAccountId"
            class="blue-color"
            @click.stop="handleSubmit(row)"
            >提交</span
          >
          <span
            v-if="getPermissionBtn('ApproveSystemRecord') && (row.status == 2 || row.status == 3)"
            class="orange-color"
            @click.stop="handleApprove(row, '1')"
            >审批</span
          >
          <span v-if="getPermissionBtn('ScheduleSystemRecord')" class="blue-color" @click.stop="handleApprove(row, '2')"
            >进度</span
          >
          <span
            v-if="getPermissionBtn('DeleteSystemRecord') && row.status == 1"
            class="red-color"
            @click.stop="handleDelete(row)"
            >删除</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
    <template #other>
      <DrawerApprove
        :drawer-visible="drawerApprove"
        :type="approveType"
        :detail-info="selectRow"
        @closeDrawer="closeDrawer"
      />
      <div class="flash-table">
        <FlashTablePdf ref="flashViewRef" :option="templateOption" @receive="handleReceive" />
      </div>
    </template>
  </ListLayout>
</template>

<script>
// Basic
import { reactive, ref, toRefs } from 'vue';
import router from '@/router/index.js';
import { useStore } from 'vuex';
import { mapGetters } from 'vuex';
import { ElMessage, ElMessageBox } from 'element-plus';
// Utils
import { getLoginInfo } from '@/utils/auth';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
// Api
import { qualitySystemList, deleteQualitysystem, processSubmit, processExecute } from '@/api/systemRecord';
import { documentPlanCategoryList } from '@/api/planManagement';
// Data
import { colWidth } from '@/data/tableStyle';
import { COMMAND } from '@/data/flashTable';
// components
import UserTag from '@/components/UserTag';
import Pagination from '@/components/Pagination';
import ListLayout from '@/components/ListLayout';
import CombinationQuery from '@/components/CombinationQuery';
import FlashTablePdf from '@/components/FlashTable/gen-pdf.vue';
import TableColumnView from '@/components/TableColumnView';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';
import DrawerApprove from './components/DrawerApprove';
export default {
  name: 'SystemRecord',
  components: { Pagination, ListLayout, UserTag, CombinationQuery, TableColumnView, DrawerApprove, FlashTablePdf },
  setup() {
    const store = useStore().state;
    const state = reactive({
      tableRef: ref(),
      currentAccountId: getLoginInfo().accountId,
      templateOption: {},
      activeName: '0',
      dialogSubmit: false,
      drawerApprove: false,
      approveType: '1',
      ruleForm: ref(),
      flashViewRef: ref(),
      searchFieldList: [],
      listLoading: false,
      fieldTip: '',
      dialogType: '',
      selectRow: {},
      selectList: [],
      showS: false,
      statusJSON: {
        1: { type: 'info', label: '待提交' },
        2: { type: 'primary', label: '待审批' },
        3: { type: 'warning', label: '待批准' },
        4: { type: 'success', label: '已完成' }
      },
      userOptions: store.common.nameList,
      templateTypeJSON: {},
      listQuery: {
        limit: 20,
        page: 1
      },
      formData: {},
      formInline: {
        status: '',
        param: '',
        tableQueryParamList: []
      },
      tableColumns: [],
      tableList: [],
      nameList: store.common.nameList,
      dialogFormVisible: false,
      total: 0
    });
    const tableKey = ref(0);
    const getList = query => {
      const params = { ...state.formInline };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoading = true;
      qualitySystemList(params).then(res => {
        state.listLoading = false;
        if (res) {
          state.total = res.data.data.totalCount;
          state.tableList = res.data.data.list;
          state.listQuery.page = Number(params.page);
        }
      });
    };
    getList();
    const initType = async () => {
      state.listLoading = true;
      const response = await documentPlanCategoryList({ page: '1', limit: '-1' }).finally((state.listLoading = false));
      if (response) {
        state.templateTypeJSON = {};
        response.data.data.forEach(item => {
          state.templateTypeJSON[item.code] = item.name;
        });
      }
    };
    initType();
    const reset = () => {
      state.formInline = {
        param: '',
        status: '',
        tableQueryParamList: []
      };
      getList();
    };
    const handleAddEditTemplate = (type, row) => {
      router.push({
        path: '/systemManagement/systemRecordDetail',
        query: {
          type: type,
          id: row?.id
        }
      });
    };
    const handleSubmit = row => {
      ElMessageBox({
        title: '提交确认',
        message: '是否确认提交？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      }).then(async () => {
        state.listLoading = true;
        const response = row.processInstanceId
          ? await processExecute({
              isAssent: 1,
              processInstanceId: row.processInstanceId,
              businessKey: row.id
            }).finally((state.listLoading = false))
          : await processSubmit(row.id).finally((state.listLoading = false));
        if (response) {
          ElMessage.success('提交成功！');
          getList();
        }
      });
    };
    const onSubmit = () => {};
    const getSingleText = val => {
      state.formInline.param = val;
      state.formInline.tableQueryParamList = [];
      getList();
    };

    const getParamList = paramList => {
      state.formInline.tableQueryParamList = paramList;
      state.formInline.param = '';
      getList();
    };
    // 高级筛选
    const advancedSearch = () => {
      state.showS = !state.showS;
      if (state.activeName === '0') {
        state.activeName = '1';
      } else {
        state.activeName = '0';
      }
    };
    const onUpdateColumns = columns => {
      tableKey.value = tableKey.value + 1;
      state.tableColumns = columns;
      state.searchFieldList = columns.filter(item => {
        return item.isQuery == 1;
      });
      state.fieldTip = state.searchFieldList.map(item => item.fieldName).join('/');
    };
    const closeDrawer = isRefresh => {
      state.drawerApprove = false;
      if (isRefresh) {
        getList();
      }
    };
    // 审批、进度
    const handleApprove = (row, type) => {
      if (!row.processInstanceId) {
        ElMessage.warning('暂无进度');
        return false;
      }
      state.selectRow = JSON.parse(JSON.stringify(row));
      state.drawerApprove = true;
      state.approveType = type;
    };
    const handleRowClick = row => {
      if (
        !state.selectList.some(item => {
          return item === row.id;
        })
      ) {
        state.tableRef.toggleRowSelection(row);
      }
    };
    // 删除
    const handleDelete = row => {
      ElMessageBox({
        title: '删除确认',
        message: '是否确认删除？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      }).then(async () => {
        state.listLoading = true;
        const response = await deleteQualitysystem(row.id).finally((state.listLoading = false));
        if (response) {
          ElMessage.success(response.data.message);
          getList();
        }
      });
    };
    const handleReceive = res => {
      switch (res.command) {
        case COMMAND.GENERATE_PDF: //
          openPdf(res);
          break;
        case COMMAND.SUBMIT_CALLBACK: // 提交成功返回数据id
          break;
      }
    };
    const openPdf = res => {
      state.listLoading = false;
      state.tableRef.clearSelection();
      // 基于垂直方向A4纸张的多张合为一张的图片数据
      const verticalA4ImageBlob = res.data.data.verticalA4ImageBlob;
      if (verticalA4ImageBlob) {
        const url = URL.createObjectURL(verticalA4ImageBlob);
        window.open(url);
      }

      // 基于水平方向A4纸张的多张合为一张的图片数据
      const horizontalA4ImageBlob = res.data.data.horizontalA4ImageBlob;
      if (horizontalA4ImageBlob) {
        const url2 = URL.createObjectURL(horizontalA4ImageBlob);
        window.open(url2);
      }
    };
    const handleUploadPDF = () => {
      const mockData = [];
      state.selectList.forEach(item => {
        mockData.push({ id: item.designTemplateId, version: item.designTemplateVersion, dataId: item.templateDataId });
      });
      if (mockData.length) {
        state.listLoading = true;
        state.flashViewRef.getPDF(mockData);
      }
    };
    const handleSelectionChange = val => {
      state.selectList = val;
    };
    return {
      ...toRefs(state),
      getSingleText,
      handleReceive,
      handleRowClick,
      handleDelete,
      handleApprove,
      closeDrawer,
      getParamList,
      handleSelectionChange,
      getPermissionBtn,
      handleSubmit,
      drageHeader,
      getNameByid,
      getNamesByid,
      handleUploadPDF,
      handleAddEditTemplate,
      onSubmit,
      reset,
      formatDate,
      getList,
      tableKey,
      advancedSearch,
      colWidth,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  },
  computed: {
    ...mapGetters(['tenantGroup', 'tenantInfo'])
  }
};
</script>
<style lang="scss" scoped>
.btn-mg20 {
  margin-right: 20px;
}

.margin-right {
  margin-right: 4px;
}
.fr {
  float: right;
}

.flash-table {
  // visibility: hidden;
  left: -999px;
  position: absolute;
}
:deep(.el-radio.is-bordered + .el-radio.is-bordered) {
  margin-left: 0;
}
</style>
