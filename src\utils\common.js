import store from '@/store';
import Decimal from 'decimal.js';
export function getNameByid(id) {
  const nameList = store.state.common.allNameList;
  if (Object.keys(nameList).length > 0) {
    const person = nameList[id];
    if (person) {
      return person.name;
    } else {
      return id;
    }
  } else {
    return id;
  }
}
export function getIdByName(name) {
  const nameList = store.state.common.nameList;
  if (nameList.length > 0) {
    const person = nameList.find(item => item.name === name);
    if (person) {
      return person.id;
    } else {
      return '';
    }
  } else {
    return '';
  }
}
export function getUserNameByid(id) {
  const nameList = store.state.common.allNameList;
  if (Object.keys(nameList).length > 0) {
    const person = nameList[id];
    if (person) {
      return person.username;
    } else {
      return id;
    }
  } else {
    return id;
  }
}

export function getNamesByid(ids) {
  if (ids !== '' && ids) {
    const nameList = store.state.common.allNameList;
    const idList = ids.split(',');
    if (Object.keys(nameList).length === 0) {
      store.dispatch('common/setNameList');
    } else {
      const findnameList = [];
      idList.forEach(obj => {
        const person = nameList[obj];
        person ? findnameList.push(person.name) : findnameList.push(obj);
      });
      return findnameList;
    }
  } else {
    return [];
  }
}

export function getNamesByIds(ids) {
  if (ids !== '' && ids) {
    const nameList = store.state.common.allNameList;
    const idList = ids.split(',');
    if (Object.keys(nameList).length === 0) {
      store.dispatch('common/setNameList');
    } else {
      var findnameList = [];
      idList.forEach(obj => {
        const person = nameList[obj];
        // findnameList.push(person.name)
        person ? findnameList.push(person.name) : findnameList.push(obj);
        // if (person && idList.length > 1) {
        //   findnameList = findnameList + person.name + ';'
        // } else {
        //   findnameList = person.name
        // }
      });
      return findnameList.join('; ');
    }
  } else {
    return '';
  }
}

export function getLimsNamesByIds(ids) {
  if (ids !== '' && ids) {
    const nameList = store.state.common.allNameList;
    const idList = ids.split(',');
    if (Object.keys(nameList).length === 0) {
      store.dispatch('common/setNameList');
    } else {
      var findnameList = [];
      idList.forEach(obj => {
        const person = nameList[obj];
        if (person) {
          findnameList.push(person.name);
        } else {
          findnameList.push(obj);
        }
      });
      return findnameList.join(';');
    }
  } else {
    return '';
  }
}

export function getNameOptionaByid(ids) {
  if (ids !== '' && ids) {
    const nameList = store.state.common.allNameList;
    const idList = ids.split(',');
    if (Object.keys(nameList).length === 0) {
      store.dispatch('common/setNameList');
    } else {
      const findnameList = [];
      idList.forEach(obj => {
        const person = nameList[obj];
        person ? findnameList.push(person) : findnameList.push(obj);
      });
      return findnameList;
    }
  }
}
// 获取对象属性个数
export function attributeCount(obj) {
  var count = 0;
  for (var i in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, i)) {
      count++;
    }
  }
  return count;
}
// 获取按钮权限
export function getPermissionBtn(key) {
  const btnlist = localStorage.getItem('permissionBtnlist');
  if (btnlist) {
    const reuslt = JSON.parse(btnlist)[key];
    return reuslt;
  }
  return true;
}

// 根据Decimal.js加减乘除计算封装,传参转换后只能是数字类型,且不能超过最大安全整数，否则返回NaN
// 示例 calculator(912).add(9.00719).toNumber();
export function calculator(value) {
  try {
    if ((Number(value) || value === 0) && !isNaN(value) && Math.abs(value) <= Number.MAX_SAFE_INTEGER) {
      const decimal = new Decimal(value);
      return {
        add(num) {
          return decimal.add(num);
        },
        subtract(num) {
          return decimal.sub(num);
        },
        multiply(num) {
          return decimal.mul(num);
        },
        divide(num) {
          return decimal.div(num);
        }
      };
    } else {
      console('请输入合理数字！');
      const decimal = new Decimal(NaN);
      return decimal;
    }
  } catch (error) {
    console.error(error);
  }
}

/** 获取随机颜色 */
export function generateRandomColor(id) {
  if (id) {
    let hash = 0;
    for (let i = 0; i < id.length; i++) {
      hash = id.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = '#';
    for (let i = 0; i < 3; i++) {
      const value = (hash >> (i * 8)) & 0xff;
      color += `00${value.toString(16)}`.substr(-2);
    }
    // 移除十六进制颜色中的'#'
    color = color.replace('#', '');

    // 解析RGB
    const r = parseInt(color.substring(0, 2), 16);
    const g = parseInt(color.substring(2, 4), 16);
    const b = parseInt(color.substring(4, 6), 16);
    return `rgba(${r}, ${g}, ${b}, 0.8)`;
  }
}
