<template>
  <vue-chart
    class="chart"
    :option="chartOption"
    :autoresize="true"
    :style="{ width: chartWidth, height: chartHeight }"
    v-on="events"
  />
</template>

<script>
import { defaultOption } from './demo';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { <PERSON><PERSON><PERSON>, Bar<PERSON>hart, Pie<PERSON>hart, Scatter<PERSON>hart, Tree<PERSON>hart } from 'echarts/charts';
import {
  GridComponent,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  ToolboxComponent,
  DataZoomComponent,
  MarkPointComponent
} from 'echarts/components';
import { UniversalTransition } from 'echarts/features';
import Vue<PERSON><PERSON>, { THEME_KEY } from 'vue-echarts';
import { ref, computed } from 'vue';

use([
  CanvasRenderer,
  GridComponent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TitleComponent,
  Toolt<PERSON>Component,
  <PERSON>Z<PERSON>Component,
  MarkPointComponent,
  LegendComponent,
  UniversalTransition,
  ToolboxComponent
]);

export default {
  name: '<PERSON><PERSON>',
  components: {
    VueC<PERSON>
  },
  provide: {
    [THEME_KEY]: 'light'
  },
  props: {
    option: {
      type: Object,
      default: function () {
        return defaultOption;
      }
    },
    events: {
      type: Object,
      default: () => ({})
    },
    width: {
      type: String,
      default: '600px'
    },
    height: {
      type: String,
      default: '400px'
    }
  },
  setup(props, context) {
    const lineOption = ref(props.option);
    const chartOption = computed({
      get: () => props.option
    });
    const chartWidth = computed({
      get: () => props.width
    });
    const chartHeight = computed({
      get: () => props.height
    });
    return {
      lineOption,
      chartOption,
      chartWidth,
      chartHeight
    };
  }
};
</script>

<style scoped>
.chart {
  height: 200px;
  width: 300px;
}
</style>
