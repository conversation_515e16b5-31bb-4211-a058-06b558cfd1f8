<!-- 新增编辑公告 -->
<template>
  <el-drawer
    v-model="showDrawer"
    title="截图记录"
    direction="rtl"
    :before-close="handleClose"
    :size="600"
    destroy-on-close
    custom-class="DrawerSnapshot"
  >
    <DrawerLayout v-loading="drawerLoading" :has-button-group="false" :has-page-header="false">
      <div v-if="pictureHistory.length > 0" class="pictureList form-height-auto">
        <div v-for="(item, index) in pictureHistory" :key="item.id" class="pictureItem">
          <el-row>
            <el-col :span="12">
              <span class="name-icon">{{ getAbbreviation(getUserNameByid(item.createBy)) }}</span>
              <span class="">{{ getNameByid(item.createBy) || item.createBy }}</span>
            </el-col>
            <el-col :span="12" class="timeText">
              {{ item.createTime }}
              <i
                v-if="ownerId === item.createBy"
                class="iconfont tes-delete"
                style="font-size: 16px; cursor: pointer"
                @click="handleDelete(item)"
              />
            </el-col>
            <el-col :span="24" class="imgContent">
              <el-image :src="item.url" :zoom-rate="1" :preview-src-list="srcList" :initial-index="index" fit="cover" />
            </el-col>
          </el-row>
        </div>
      </div>
      <div v-else class="form-height-auto">
        <img src="@/assets/img/empty-img.png" alt="" />
      </div>
      <div class="drawer-fotter">
        <el-button :loading="drawerLoading" @click="handleClose">关闭</el-button>
      </div>
    </DrawerLayout>
  </el-drawer>
</template>

<script>
import { ref, watch, reactive, toRefs } from 'vue';
import { getPictureHis, deletePicture } from '@/api/sysConfig';
import DrawerLayout from '@/components/DrawerLayout';
import { getNameByid, getUserNameByid } from '@/utils/common';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getLoginInfo } from '@/utils/auth';
import { makePy } from '@/utils/chinese-to-str';
// import { formatDateTime } from '@/utils/formatTime'
import store from '@/store';

export default {
  name: 'DrawerSnapshot',
  components: { DrawerLayout },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    parameter: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    // 抽屉事件
    const showDrawer = ref(props.drawer);
    const state = reactive({
      drawerLoading: false,
      params: {},
      userList: store.state.common.nameList,
      ownerId: getLoginInfo().accountId,
      srcList: [],
      pictureHistory: [],
      detailData: {}
    });
    watch(props, newValue => {
      showDrawer.value = newValue.drawer;
      if (showDrawer.value) {
        state.params = newValue.parameter;
        state.drawerLoading = false;
        state.srcList = [];
        initDetail();
      }
    });
    const initDetail = () => {
      state.drawerLoading = true;
      getPictureHis(state.params).then(res => {
        state.drawerLoading = false;
        if (res) {
          state.pictureHistory = res.data.data;
          state.srcList = state.pictureHistory.map(item => {
            return item.url;
          });
        }
      });
    };
    // 关闭抽屉
    const handleClose = () => {
      showDrawer.value = false;
      context.emit('close', false);
    };
    // 简称
    const getAbbreviation = name => {
      return makePy(name.charAt(0))[0].toUpperCase();
    };
    const handleDelete = item => {
      ElMessageBox({
        title: '提示',
        message: '是否确认删除？',
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(() => {
          state.drawerLoading = true;
          deletePicture(item.id).then(res => {
            state.drawerLoading = false;
            if (res) {
              state.drawerLoading = false;
              ElMessage.success('删除成功');
              initDetail();
            }
          });
        })
        .catch(() => {});
    };
    // 确认新增
    return {
      ...toRefs(state),
      getNameByid,
      getLoginInfo,
      handleDelete,
      getUserNameByid,
      initDetail,
      handleClose,
      showDrawer,
      getAbbreviation
    };
  }
};
</script>

<style lang="scss" scoped>
.imgContent {
  text-align: center;
  background-color: lightgray;
  padding: 15px;
  margin-top: 5px;
  border-radius: 6px;
  :deep(.el-image) {
    overflow-y: auto;
    max-width: 100%;
    max-height: 280px;
  }
  :deep(.el-image__inner) {
    max-width: 100%;
    max-height: 280px;
  }
}
.pictureItem {
  margin-bottom: 15px;
  padding-right: 10px;
  &:last-child {
    margin-bottom: 0;
  }
}
.name-icon {
  height: 24px;
  width: 24px;
  line-height: 24px;
  background: $tes-primary;
  border-radius: 50%;
  color: #fff;
  margin-right: 8px;
  display: inline-block;
  text-align: center;
}
.timeText {
  text-align: right;
  color: #999;
}
.pictureList {
  text-align: left;
}
.tes-delete {
  color: $tes-primary;
  margin-left: 10px;
  cursor: pointer;
}
</style>
