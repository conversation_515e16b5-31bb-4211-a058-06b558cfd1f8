import { getAPIURL } from '@/utils/base-url';

const dingTalkServerIps = ['69.72', '69.111', '69.117', 'swms.cxist.com'];

export const QmsServerIps = [];

export const devServerIps = ['69.72', 'localhost'];

export function checkServerIp(serverIpList = dingTalkServerIps) {
  const serverIp = getAPIURL();
  let result = false;
  if (serverIp) {
    serverIpList.forEach(item => {
      if (serverIp.includes(item)) {
        result = true;
      }
    });
  }
  return result;
}

export function checkDevServerIp(serverIpList = devServerIps) {
  let result = false;
  serverIpList.forEach(item => {
    if (window.location.hostname.includes(item)) {
      result = true;
    }
  });
  return result;
}
