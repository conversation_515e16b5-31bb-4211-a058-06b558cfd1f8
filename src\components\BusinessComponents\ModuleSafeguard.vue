<template>
  <!-- eslint-disable -->
  <el-dialog
    v-model="dialogSheathShow"
    title="试样分组维护"
    :close-on-click-modal="false"
    width="500px"
    custom-class="sampleGroupMaintenance_dialog"
    :before-close="closedialogxx"
  >
    <div v-if="showCoreTip">
      <p><span style="color: #a9a9a9">此样品暂未维护试样分组，请先维护试样分组后再添加模板</span></p>
      <p>&nbsp;</p>
    </div>
    <el-form ref="ruleForm" :model="formData" class="form-sheath" label-position="top" label-width="110px">
      <el-form-item v-if="isCanAdd && isHengTong" label="快速批量分组：" prop="plfz">
        <el-row>
          <el-col :span="8">
            <el-input-number
              v-model.number="formData.plfz"
              :min="0"
              controls-position="right"
              placeholder="开始"
              size="mini"
            />
          </el-col>
          <el-col :span="3" style="text-align: center"> - </el-col>
          <el-col :span="8">
            <el-input-number
              v-model.number="formData.plfz2"
              :min="0"
              controls-position="right"
              placeholder="结束"
              size="mini"
            />
          </el-col>
          <el-col :span="5" style="text-align: right">
            <el-button type="text" size="mini" @click="handleGeneration()">快速生成</el-button>
          </el-col>
        </el-row>
      </el-form-item>
      <div v-if="radio === '2'">
        <div style="line-height: 36px">
          试样分组：<span v-if="isCanAdd" class="blue-color fr" @click="handleAddGroup()">增加分组</span>
        </div>
        <el-table
          ref="tableRef"
          v-loading="listLoading"
          :data="formData.detailInfo.coreColourList"
          size="medium"
          fit
          border
          height="auto"
          class="dark-table base-table format-height-table format-height-table2"
          @header-dragend="drageHeader"
          @row-click="handleRowClick"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            v-if="!isCanAdd"
            type="selection"
            :width="colWidth.checkbox"
            align="center"
            :selectable="selectable"
          />
          <el-table-column label="分组" prop="phone" align="left" fixed :min-width="105">
            <template #default="{ row, $index }">
              <span v-if="row.id">{{ row.coreColour || '--' }}</span>
              <el-form-item v-else :prop="`detailInfo.coreColourList.${$index}.coreColour`" style="margin-bottom: 0">
                <el-select v-model="row.coreColour" clearable allow-create filterable size="mini" placeholder="选择">
                  <el-option-group v-for="item in colorGroupList" :key="item.label" :label="item.label">
                    <el-option v-for="val in item.group" :key="val.name" :label="val.name" :value="val.name">
                      <span style="float: left">{{ val.name }}</span>
                      <span v-if="val.status !== 1" class="fr" style="color: red">已停用</span>
                    </el-option>
                  </el-option-group>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            v-for="coreKeyItem in formData.detailInfo.coreKeyList"
            :key="coreKeyItem.coreKey"
            :label="coreKeyItem.name"
            :prop="coreKeyItem.coreKey"
            :width="100"
            show-overflow-tooltip
          >
            <template #default="{ row, $index }">
              <span v-if="row.id">{{ row.groupMap[coreKeyItem.coreKey] || '--' }}</span>
              <el-form-item
                v-else-if="coreKeyItem.status === 0"
                :prop="`detailInfo.coreColourList.${$index}.groupMap${coreKeyItem.coreKey}`"
                style="margin-bottom: 0"
              >
                <el-input
                  v-model="row.groupMap[coreKeyItem.coreKey]"
                  size="mini"
                  :placeholder="`${coreKeyItem.name}`"
                />
              </el-form-item>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!formData.detailInfo.coreColourList[formData.detailInfo.coreColourList.length - 1]?.id && isCanAdd"
            label="操作"
            fixed="right"
            :width="60"
          >
            <template #default="{ row, $index }">
              <span v-if="!row.id" class="blue" @click="handleDelete(row, $index)">删除</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-if="radio === '1'">
        <el-form-item
          label="试样分组"
          prop="coreColourList"
          :rules="{ required: true, message: '请选择试样分组', trigger: 'change' }"
          style="margin-bottom: 0"
        >
          <el-select
            v-loading="listLoading"
            v-model="formData.coreColourList"
            :allow-create="!isCapability"
            multiple
            filterable
            clearable
            placeholder="请选择"
            :disabled="info.disabled"
          >
            <el-option v-for="val in colorGroupList[0].group" :key="val.name" :label="val.name" :value="val.name">
              <span style="float: left">{{ val.name }}</span>
            </el-option>
            <el-option
              v-for="val in colorGroupList[1].group"
              :key="val.name"
              :label="val.name"
              :value="val.name"
              disabled
            >
              <span style="float: left">{{ val.name }}</span>
              <span v-if="val.status !== 1" class="fr" style="color: red">已停用</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-button v-if="isHengTong" type="text" size="mini" @click="handleColorBatchImport" style="margin-top: 10px"
          >批量导入</el-button
        >
      </div>
      <el-form-item v-if="info.sheathColour" label="操作用户：" class="span-item">
        <span>{{ getNameByid(info.createBy) }}</span>
      </el-form-item>
      <el-form-item v-if="info.sheathColour" label="操作时间：" class="span-item">
        <span>{{ info.createTime }}</span>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closedialogxx()">取 消</el-button>
        <el-button
          :loading="submitLoading"
          type="primary"
          @click="handleSubmitxx(1)"
          @keyup.prevent
          @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
    <el-dialog
      v-model="colorBatchImportDialogVisible"
      title="试样分组批量导入"
      :close-on-click-modal="false"
      width="400px"
      @close="onColorBatchImportDialogClose"
    >
      <ul class="example-rules">
        <li>格式: 井号(#)、英文逗号(,)或者换行分隔</li>
        <li>例子如下:</li>
        <li>#红#橙#黄#绿#青#蓝#紫</li>
        <li>红,橙,黄,绿,青,蓝,紫</li>
      </ul>
      <el-input v-model="colorInputString" type="textarea" placeholder="请输入内容" :rows="5" @input="onTextareInput" />
      <div v-if="newCoreColourList.length || parseLoading" v-loading="parseLoading" class="preview">
        <p class="preview-title">预览</p>
        <el-tag type="info" v-for="item in newCoreColourList" :key="item">{{ item }}</el-tag>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleColorBatchImportDialogCancel">取 消</el-button>
          <el-button
            :loading="parseLoading"
            type="primary"
            @keyup.prevent
            @keydown.enter.prevent
            @click="handleColorBatchImportDialogSave"
          >
            确 认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </el-dialog>
</template>
<script>
import { reactive, toRefs, ref, computed, watch, getCurrentInstance } from 'vue';
// import { formatDate } from '@/utils/formatTime'
import store from '@/store';
// import router from '@/router/index.js'
// import { useRoute } from 'vue-router'
import {
  getInfoBySampleId,
  getInfoBySampleCapabilityId,
  saveCapabilitySampleGroupInfo,
  experimentcorerecordsave
} from '@/api/execution';
import { ElMessage } from 'element-plus';
import { getLoginInfo } from '@/utils/auth';
import { getNameByid } from '@/utils/common';
import { useRoute } from 'vue-router';
import { drageHeader } from '@/utils/formatTable';
import { getDictionary } from '@/api/user';
import { colWidth } from '@/data/tableStyle';
import { getCoreColorId, getCoreColorByNameId, getHaveSaved } from '@/utils/func/sampleGroup';
import { throttle } from 'lodash';

export default {
  name: 'ModuleSafeguard',
  props: {
    dataValue: {
      type: Object,
      default: function () {
        return {};
      }
    },
    dialogSheath: {
      type: Boolean,
      default: function () {
        return false;
      }
    },
    jsonData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    showTip: {
      type: Boolean,
      default: function () {
        return false;
      }
    },
    isCapability: {
      type: Boolean,
      default: function () {
        return false;
      }
    }
  },
  emits: ['closexx', 'sumbitData', 'saveColor'],
  setup(props, ctx) {
    const route = useRoute();
    watch(props, newValue => {
      state.dataValue = newValue.dataValue;
      state.jsonData = newValue.jsonData;
      state.dialogSheathShow = newValue.dialogSheath;
      if (newValue.dialogSheath) {
        state.coreKeyMap = {};
        getInfo();
        if (!props.isCapability) {
          getcolorList();
        }
        state.formData.plfz = 0;
        state.formData.plfz2 = 0;
        state.formData.radio = '1';
        state.pageName = route.name;
      }
    });
    const ruleForm = ref('');
    const state = reactive({
      colorGroupList: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ],
      info: {
        disabled: false
      },
      radio: '1',
      initialData: {
        coreColourList: [],
        coreKeyList: []
      }, // 试样分组数据源
      pageName: '',
      listLoading: false,
      tableRef: ref(),
      tableSelected: [], // 已经选中的试样分组
      alreadySelectedAll: [], // 以前选中过的数据
      isCanAdd: !props.isCapability, // 用于已经保存过的试样分组，不能再进行新增，只能在已保存里面选择
      dialogSheathShow: props.dialogSheath,
      dialogSubmit: props.dataValue.dialogSubmit,
      dataValue: props.dataValue,
      jsonData: props.jsonData,
      showCoreTip: props.showTip,
      excelInfo: {},
      formData: {
        coreColourList: [],
        detailInfo: {
          coreColourList: [], // 已选中的线芯颜色
          coreKeyList: [] // 试样分组动态列
        }
      },
      coreKeyMap: {}, // 试样分组动态列仅用于新增时候赋值
      userList: store.state.common.nameList,
      capabilityInfo: {},
      colorBatchImportDialogVisible: false,
      colorInputString: '',
      parseLoading: false,
      newCoreColourList: [], // 批量新增试样分组
      submitLoading: false
    });
    const isHengTong = computed(() => store?.state?.user?.tenantInfo?.clientCode === 'hengtong001');
    let parseTimer = null;
    // 提交审核事件
    const { appContext } = getCurrentInstance();
    const bus = appContext.config.globalProperties.bus;
    bus.$on('excelData', allDate => {
      state.excelInfo.experimentData = allDate;
    });
    // 获取线芯维护的信息
    const getInfo = () => {
      getInfoBySampleId(state.jsonData.samplesId).then(res => {
        state.formData.detailInfo = JSON.parse(JSON.stringify(res.data.data));
        state.initialData = JSON.parse(JSON.stringify(res.data.data));
        state.formData.detailInfo.coreKeyList.forEach(item => {
          state.coreKeyMap[item.coreKey] = '';
        });
        if (state.formData.detailInfo.coreKeyList.length) {
          if (
            state.formData.detailInfo.coreKeyList.some(item => {
              return item.status === 0;
            })
          ) {
            state.radio = '2';
          } else {
            state.radio = '1';
          }
        } else {
          state.radio = '1';
        }
        if (props.isCapability) {
          if (state.initialData.coreColourList && state.initialData.coreColourList.length > 0) {
            setCapabilityGroup(state.initialData);
          }
        } else {
          setSampleGroup(state.initialData);
        }
      });
    };
    // 获取编辑页试样分组已经再次保存过的数据
    const setCapabilityGroup = () => {
      state.listLoading = true;
      getInfoBySampleCapabilityId({
        sampleId: state.jsonData.samplesId,
        capabilityId: state.jsonData.capabilityId
      }).then(res => {
        state.listLoading = false;
        const response = res.data.data;
        state.capabilityInfo = response;
        state.formData.coreColourList = [];
        state.colorGroupList[0].group = [];
        state.colorGroupList[1].group = [];
        state.initialData.coreColourList.forEach(item => {
          state.colorGroupList[0].group.push({
            name: item.coreColour,
            disabled: getHaveSaved(item.coreColourSuffix, response.coreColourList),
            status: 1
          });
        });
        state.capabilityInfo.coreColourList.forEach(item => {
          item.id = getCoreColorByNameId(item.coreColour, state.initialData.coreColourList);
          state.formData.coreColourList.push(item.coreColour);
        });
        state.colorGroupList[0].group.forEach(item => {
          item.disabled = state.formData.coreColourList.some(val => {
            return item.name === val;
          });
        });
        // if (state.capabilityInfo.capabilityId) {
        // } else {
        //   state.formData.coreColourList = []
        // }
        // if (response.coreColourList.length < state.formData.detailInfo.coreColourList.length) {
        // } else {
        //   state.capabilityInfo.coreColourList = []
        //   state.formData.coreColourList = []
        // }
      });
    };

    const setSampleGroup = info => {
      state.formData.coreColourList = [];
      info.coreColourList.forEach(item => {
        state.formData.coreColourList.push(item.coreColour);
      });
      state.initialData.disabled = false;
      state.formData.coreColourList.forEach(item => {
        if (
          !state.colorGroupList[0].group.some(element => {
            return element.name === item;
          }) &&
          !state.colorGroupList[1].group.some(element => {
            return element.name === item;
          })
        ) {
          state.colorGroupList[0].group.push({ name: item, disabled: true, status: 1 });
        }
      });
      state.colorGroupList[0].group.forEach(item => {
        item.disabled = state.formData.coreColourList.some(val => {
          return item.name === val;
        });
      });
      state.colorGroupList[1].group.forEach(item => {
        item.disabled = state.formData.coreColourList.some(val => {
          return item.name === val;
        });
      });
    };

    const handleSubmitxx = throttle(
      () => {
        if (state.radio === '1') {
          ruleForm.value
            .validate()
            .then(valid => {
              if (valid && state.formData.coreColourList.length > 0) {
                const postdata = {
                  sampleId: state.jsonData.samplesId,
                  coreColourList: [],
                  capabilityId: ''
                };
                if (props.isCapability) {
                  state.formData.coreColourList.forEach(item => {
                    postdata.coreColourList.push({
                      coreColour: item,
                      coreColourSuffix: getCoreColourSuffix(item),
                      id: getCoreColorByNameId(item, state.capabilityInfo.coreColourList)
                    });
                  });
                  postdata.capabilityId = state.jsonData.capabilityId;
                  onSubmit(postdata);
                } else {
                  state.formData.coreColourList.forEach(item => {
                    postdata.coreColourList.push({
                      coreColour: item.toString(),
                      id: getCoreColorByNameId(item, state.initialData.coreColourList)
                    });
                  });
                  postdata.capabilityId = '';
                  onSubmit(postdata);
                }
              }
            })
            .catch();
        } else {
          ruleForm.value
            .validate()
            .then(valid => {
              if (valid && state.formData.detailInfo.coreColourList.length > 0) {
                if (props.isCapability) {
                  const params = {
                    sampleId: state.jsonData.samplesId,
                    coreKeyList: state.formData.detailInfo.coreKeyList,
                    capabilityId: state.jsonData.capabilityId
                  };
                  params.coreColourList = [];
                  state.tableSelected.forEach(item => {
                    if (!item.alreadySelected) {
                      params.coreColourList.push({ ...item, id: '' });
                    } else {
                      params.coreColourList.push({
                        ...item,
                        id: getCoreColorId(item.coreColourSuffix, state.alreadySelectedAll)
                      });
                    }
                  });
                  onSubmit(params);
                } else {
                  state.formData.detailInfo.capabilityId = '';
                  state.formData.detailInfo.sampleId = state.jsonData.samplesId;
                  onSubmit(state.formData.detailInfo);
                }
              }
            })
            .catch();
        }
      },
      1000,
      { trailing: false }
    );
    // 提交试样分组
    const onSubmit = params => {
      if (props.isCapability) {
        state.submitLoading = true;
        saveCapabilitySampleGroupInfo(params)
          .then(res => {
            if (res.data.code === 200) {
              ctx.emit('saveColor', params.coreColourList);
              ElMessage.success({
                message: '维护成功',
                type: 'success'
              });
              closedialogxx(true);
            }
          })
          .finally(() => (state.submitLoading = false));
      } else {
        state.submitLoading = true;
        experimentcorerecordsave(params)
          .then(res => {
            if (res.data.code === 200) {
              ElMessage.success({
                message: '维护成功',
                type: 'success'
              });
              closedialogxx(true);
            }
          })
          .finally(() => (state.submitLoading = false));
      }
    };
    function getCoreColourSuffix(sampleColor) {
      let sampleColorSuffix = '';
      state.initialData.coreColourList.forEach(item => {
        if (item.coreColour === sampleColor) {
          sampleColorSuffix = item.coreColourSuffix;
        }
      });
      return sampleColorSuffix;
    }

    // 表格勾选框的禁用规则
    const selectable = (row, index) => {
      if (!row.alreadySelected) {
        return true;
      } else {
        // 已经选择过，不能再次选择
        return false;
      }
    };
    // 获取线芯颜色字典选项
    const getcolorList = () => {
      state.listLoading = true;
      getDictionary('XXYS').then(res => {
        state.listLoading = false;
        if (res) {
          state.colorGroupList[0].group = [];
          state.colorGroupList[1].group = [];
          res.data.data.dictionaryoption.forEach(item => {
            if (item.status === 1) {
              state.colorGroupList[0].group.push(item);
            } else {
              state.colorGroupList[1].group.push(item);
            }
          });
        }
      });
    };
    // 关闭弹屏
    const closedialogxx = isRefresh => {
      ruleForm.value.resetFields();
      state.formData.detailInfo = {
        coreColourList: [],
        coreKeyList: []
      };
      ctx.emit('closexx', isRefresh);
    };
    // 快速生成试样分组-仅限数字类型
    const handleGeneration = () => {
      if (state.formData.plfz !== '' && state.formData.plfz2 !== '') {
        if (state.formData.plfz > state.formData.plfz2) {
          ElMessage.warning({
            message: '开始数字不能大于结束数字'
          });
        } else {
          const reg = new RegExp('^[+]?\\d+$');
          if (reg.test(state.formData.plfz) && reg.test(state.formData.plfz2)) {
            for (let i = state.formData.plfz; i <= state.formData.plfz2; i++) {
              if (state.radio === '1') {
                const isCz1 = state.formData.coreColourList.some(item => {
                  if (Number(item)) {
                    return Number(i) === Number(item);
                  }
                });
                if (!isCz1) {
                  state.formData.coreColourList.push(i);
                }
              } else {
                const isCz2 = state.formData.detailInfo.coreColourList.some(item => {
                  if (Number(item)) {
                    return Number(i) === Number(item);
                  }
                });
                if (!isCz2) {
                  handleAddGroup(i);
                }
              }
            }
          } else {
            ElMessage.warning({
              message: '只能输入正整数'
            });
          }
        }
      } else {
        ElMessage.warning({
          message: '开始数字或结束数字不能为空'
        });
      }
    };
    // 增加试样分组
    const handleAddGroup = val => {
      state.formData.detailInfo.coreColourList.push({
        coreColour: val || '',
        groupMap: JSON.parse(JSON.stringify(state.coreKeyMap))
      });
    };
    // 删除新增的试样分组
    const handleDelete = (row, index) => {
      state.formData.detailInfo.coreColourList.splice(index, 1);
    };
    // 试样分组再次选择
    const handleSelectionChange = val => {
      state.tableSelected = val;
    };
    // 行点击
    const handleRowClick = row => {
      if (!state.isCanAdd && !row.alreadySelected) {
        // 未选中过
        const rowIndex = state.formData.detailInfo.coreColourList.findIndex(
          item => item.coreColourSuffix === row.coreColourSuffix
        );
        if (rowIndex !== -1) {
          row.selected = !row.selected;
          state.tableRef.toggleRowSelection(row, row.selected);
        }
      }
    };
    // 试样分组批量导入弹框 - 打开按钮
    const handleColorBatchImport = () => {
      state.colorBatchImportDialogVisible = true;
    };
    // 试样分组批量导入弹框 - 重置变量
    const resetColorBatchImportDialogVar = () => {
      clearTimeout(parseTimer);
      parseTimer = null;
      state.parseLoading = false;
      state.colorInputString = '';
      state.newCoreColourList = [];
    };
    // 试样分组批量导入弹框 - 关闭事件
    const onColorBatchImportDialogClose = () => {
      resetColorBatchImportDialogVar();
    };
    // 解析输入的试验分组字符串
    const onTextareInput = value => {
      if (!value) {
        resetColorBatchImportDialogVar();
        return;
      }
      if (parseTimer) {
        clearTimeout(parseTimer);
      }
      state.parseLoading = true;
      parseTimer = setTimeout(() => {
        state.parseLoading = false;
        const newCoreColourList = value.split(/#|,|\n/);
        state.newCoreColourList = newCoreColourList.reduce((accumulator, currentItem) => {
          const newItem = currentItem.trim();
          if (newItem.length > 0 && accumulator.indexOf(newItem) === -1) {
            accumulator.push(newItem);
          }
          return accumulator;
        }, []);
      }, 500);
    };
    // 试样分组批量导入弹框 - 取消按钮
    const handleColorBatchImportDialogCancel = () => {
      state.colorBatchImportDialogVisible = false;
    };
    // 试样分组批量导入弹框 - 确定按钮
    const handleColorBatchImportDialogSave = () => {
      state.newCoreColourList.forEach(item => {
        if (state.formData.coreColourList.indexOf(item) === -1) {
          state.formData.coreColourList.push(item);
        }
      });
      handleColorBatchImportDialogCancel();
    };

    return {
      ...toRefs(state),
      isHengTong,
      ruleForm,
      onSubmit,
      getCoreColorId,
      selectable,
      colWidth,
      handleSelectionChange,
      handleRowClick,
      handleDelete,
      drageHeader,
      handleAddGroup,
      handleGeneration,
      getNameByid,
      getLoginInfo,
      handleSubmitxx,
      closedialogxx,
      handleColorBatchImport,
      onColorBatchImportDialogClose,
      onTextareInput,
      handleColorBatchImportDialogCancel,
      handleColorBatchImportDialogSave
    };
  }
};
</script>
<style lang="scss">
.sampleGroupMaintenance_dialog {
  .el-select .el-select__tags > span {
    max-height: 141px;
    overflow-y: auto;
  }
  .format-height-table2.el-table .el-table__body-wrapper {
    max-height: calc(100vh - 564px);
  }

  .colorRow {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }
  .addRowButton {
    position: absolute;
    right: 0;
    top: 0;
    margin: 0;
  }
  .addColumn {
    position: absolute;
    top: -20px;
    right: 0;
  }
  .span-item {
    overflow: hidden;
    padding-bottom: 20px;
    .el-form-item {
      width: 50%;
      float: left;
    }
  }
  .form-sheath {
    .el-select {
      width: 100%;
    }
    .el-select__tags {
      width: 100%;
    }
    .el-input-number--mini {
      width: 100%;
    }
  }
}

.example-rules {
  padding: 5px 10px;
  background: var(--backgroundColor2);
  margin: 14px 0;
  li {
    list-style: none;
    line-height: 22px;
    font-size: 13px;
  }
  .blue-color {
    margin-right: 0;
  }
}

.preview {
  margin: 10px 0;

  .preview-title {
    margin-left: 6px;
  }

  .el-tag {
    box-sizing: border-box;
    border-color: transparent;
    margin: 2px 0 2px 6px;
    background-color: var(--backgroundColor2);
  }
}
</style>
