<template>
  <div>
    <div v-if="isValid" class="btn-group">
      <el-button
        v-if="formData.tableList.length == 0"
        :loading="tableLoading"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="addInvoice"
        @keyup.prevent
        @keydown.enter.prevent
        >新增</el-button
      >
      <el-button
        v-if="formData.tableList.length > 0 && !isEdit"
        :loading="tableLoading"
        icon="el-icon-edit"
        size="small"
        @click="isEdit = true"
        @keyup.prevent
        @keydown.enter.prevent
        >编辑</el-button
      >
      <el-button
        v-if="isEdit"
        size="small"
        :loading="tableLoading"
        type="primary"
        @click="saveInvoice"
        @keyup.prevent
        @keydown.enter.prevent
        >保存</el-button
      >
      <el-button
        v-if="isEdit"
        size="small"
        :loading="tableLoading"
        @click="calceInvoice"
        @keyup.prevent
        @keydown.enter.prevent
        >取消</el-button
      >
    </div>
    <el-form ref="ruleFormTable" :model="formData">
      <el-table
        v-loading="tableLoading"
        :data="formData.tableList"
        fit
        border
        height="auto"
        highlight-current-row
        class="detail-table dark-table base-table"
      >
        <el-table-column prop="taxNo" label="公司税号" :width="ColumnWidth.Char11" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <span v-if="!isEdit"> {{ row.taxNo || '--' }}</span>
            <el-form-item
              v-else
              :prop="`tableList.${$index}.taxNo`"
              :rules="{ required: true, message: '请输入公司税号', trigger: 'change' }"
              style="margin: 0px"
            >
              <el-input v-model="row.taxNo" placeholder="请输入公司税号" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="openingBank" label="开户行" :min-width="colWidth.model" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <span v-if="!isEdit"> {{ row.openingBank }}</span>
            <el-form-item v-else :prop="`tableList.${$index}.openingBank`" style="margin: 0px">
              <el-input v-model="row.openingBank" maxlength="100" placeholder="请输入开户行" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="acctNo" label="账号" :min-width="colWidth.model" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <span v-if="!isEdit"> {{ row.acctNo || '--' }}</span>
            <el-form-item v-else :prop="`tableList.${$index}.acctNo`" style="margin: 0px">
              <el-input v-model="row.acctNo" maxlength="100" placeholder="请输入账号" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="paymentMethod"
          label="付款方式"
          :min-width="colWidth.customerInvoiceStatus"
          show-overflow-tooltip
        >
          <template #default="{ row, $index }">
            <span v-if="!isEdit"> {{ paymentMethodTypeJSON[row.paymentMethod] || '--' }}</span>
            <el-form-item v-else :prop="`tableList.${$index}.paymentMethod`" style="margin: 0px">
              <el-select
                v-model="row.paymentMethod"
                clearable
                size="small"
                class="topSelect"
                placeholder="请选择付款方式"
              >
                <el-option v-for="(val, key) in paymentMethodTypeJSON" :key="key" :label="val" :value="Number(key)" />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="电话" :width="ColumnWidth.Char8">
          <template #default="{ row, $index }">
            <span v-if="!isEdit"> {{ row.phone || '--' }}</span>
            <el-form-item
              v-else
              :prop="`tableList.${$index}.phone`"
              :rules="[{ validator: isPhoneMobile, tigger: 'blur' }]"
              style="margin: 0px"
            >
              <el-input v-model="row.phone" placeholder="请输入详细电话" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="invoiceType"
          label="发票类型"
          :min-width="colWidth.customerInvoiceStatus"
          show-overflow-tooltip
        >
          <template #default="{ row, $index }">
            <span v-if="!isEdit"> {{ invoiceTypeJSON[row.invoiceType] || '--' }}</span>
            <el-form-item v-else :prop="`tableList.${$index}.invoiceType`" style="margin: 0px">
              <el-select
                v-model="row.invoiceType"
                clearable
                size="small"
                class="topSelect"
                placeholder="请选择发票类型"
              >
                <el-option v-for="(val, key) in invoiceTypeJSON" :key="key" :label="val" :value="Number(key)" />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="regionState" label="发票地址" :width="colWidth.address" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <span v-if="!isEdit"> {{ row.regionState.length ? row.regionState.toString() : '--' }}</span>
            <el-form-item v-else :prop="`tableList.${$index}.regionState`" style="margin: 0px">
              <el-cascader
                v-model="row.regionState"
                placeholder="请输入发票地址"
                :props="cascaderProps"
                :options="provice"
                filterable
                clearable
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="exactAddress" label="详细地址" :min-width="160" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <span v-if="!isEdit"> {{ row.exactAddress || '--' }}</span>
            <el-form-item v-else :prop="`tableList.${$index}.exactAddress`" style="margin: 0px">
              <el-input v-model="row.exactAddress" maxlength="120" placeholder="请输入详细地址" />
            </el-form-item>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch, getCurrentInstance } from 'vue';
import { isPhoneMobile } from '@/utils/validate';
import { getInvoiceList, saveInvoiceApi } from '@/api/customerManagement';
import provice from '@/data/administrativeDivisionsOfChina';
import { colWidth, ColumnWidth } from '@/data/tableStyle';

export default {
  name: 'CustomerInvoice',
  props: {
    activeName: {
      type: String,
      default: ''
    },
    isValid: {
      type: Boolean,
      default: false
    },
    detailId: {
      type: String,
      default: ''
    }
  },
  emits: ['isHaveRevised'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      infoId: '', // 详情id
      isValid: false,
      tableLoading: false,
      isEdit: false,
      isAdd: false,
      ruleFormTable: ref(),
      formData: {
        tableList: []
      },
      invoiceTypeJSON: {
        0: '普票',
        1: '专票'
      },
      paymentMethodTypeJSON: {
        0: '现金',
        1: '转账'
      },
      oldTableList: [],
      provice: [],
      cascaderProps: {
        expandTrigger: 'hover',
        children: 'children',
        label: 'name',
        value: 'name'
      }
    });
    watch(props, newValue => {
      if (newValue.activeName === '3' && props.detailId) {
        state.isValid = props.isValid;
        state.provice = provice;
        state.infoId = props.detailId;
        getList();
      }
    });
    const getList = () => {
      getInvoiceList(state.infoId).then(res => {
        state.tableLoading = false;
        if (res) {
          state.isEdit = false;
          state.isAdd = false;
          state.formData.tableList = res.data.data.id ? [JSON.parse(JSON.stringify(res.data.data))] : [];
          state.oldTableList = res.data.data.id ? [JSON.parse(JSON.stringify(res.data.data))] : [];
        }
      });
    };
    // 保存发票
    const saveInvoice = () => {
      state.ruleFormTable
        .validate()
        .then(valid => {
          if (valid) {
            const params = JSON.parse(JSON.stringify(state.formData.tableList[0]));
            state.tableLoading = true;
            saveInvoiceApi({ customerId: state.infoId, ...params }).then(res => {
              state.tableLoading = false;
              if (res) {
                context.emit('isHaveRevised', true);
                proxy.$message.success(res.data.message);
                getList('contacts');
              }
            });
          } else {
            return false;
          }
        })
        .catch(error => {
          const fieldName = Object.keys(error)[0];
          proxy.$message.warning(error[fieldName][0].message);
          return false;
        });
    };
    // 取消保存
    const calceInvoice = () => {
      state.isEdit = false;
      if (state.oldTableList.length > 0) {
        state.formData.tableList = [JSON.parse(JSON.stringify(state.oldTableList[0]))];
      } else {
        state.formData.tableList = [];
      }
    };
    // 新增发票
    const addInvoice = () => {
      state.isEdit = true;
      state.formData.tableList.push({
        taxNo: ''
      });
    };
    return {
      ...toRefs(state),
      getList,
      isPhoneMobile,
      colWidth,
      ColumnWidth,
      addInvoice,
      calceInvoice,
      saveInvoice
    };
  }
};
</script>

<style scoped lang="scss">
.btn-group {
  margin-bottom: 16px;
}
</style>
