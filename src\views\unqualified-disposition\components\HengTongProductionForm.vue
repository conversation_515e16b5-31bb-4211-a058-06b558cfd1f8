<template>
  <div class="form-wrapper">
    <el-form
      ref="inspectionInfoRef"
      class="formDataInfo"
      :model="formInline"
      :rules="formRule"
      label-width="10rem"
      label-position="right"
    >
      <el-row>
        <el-col :span="8">
          <el-form-item label="区域：" prop="region">
            <span v-if="showDetail">{{ detailForm.regionName }}</span>
            <el-select
              v-else
              v-model="formInline.region"
              class="owner-select"
              placeholder="请选择区域"
              clearable
              filterable
            >
              <el-option v-for="item in regionOptionList" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="缺陷分类：" prop="flawClassify">
            <span v-if="showDetail">{{ formInline.flawClassify }}</span>
            <el-input v-else v-model="formInline.flawClassify" placeholder="请输入缺陷分类" maxlength="100" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="产品类别：" prop="productClassify">
            <span v-if="showDetail">{{ detailForm.productClassifyName }}</span>
            <el-select
              v-else
              v-model="formInline.productClassify"
              class="owner-select"
              placeholder="请选择产品类别"
              clearable
              filterable
            >
              <el-option v-for="item in productClassifyList" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="质量等级：" prop="qualityLevel">
            <span v-if="showDetail">{{ detailForm.qualityLevelName }}</span>
            <el-select
              v-else
              v-model="formInline.qualityLevel"
              class="owner-select"
              placeholder="请选择质量等级"
              clearable
              filterable
            >
              <el-option v-for="item in qualityLevelList" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="生产人：" prop="productionPersonnel">
            <span v-if="showDetail">{{ formInline.productionPersonnel }}</span>
            <el-input
              v-else
              v-model="formInline.productionPersonnel"
              placeholder="请输入生产人"
              maxlength="100"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="生产日期：" prop="productionDate">
            <span v-if="showDetail">{{ formatDate(formInline.productionDate) }}</span>
            <el-date-picker
              v-else
              v-model="formInline.productionDate"
              type="date"
              placeholder="请选择生产日期"
              class="register-date"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="来料机台：" prop="incomingStation">
            <span v-if="showDetail">{{ formInline.incomingStation }}</span>
            <el-input
              v-else
              v-model="formInline.incomingStation"
              placeholder="请输入来料机台"
              clearable
              maxlength="100"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="合同单位：" prop="contractOffice">
            <span v-if="showDetail">{{ formInline.contractOffice }}</span>
            <el-input
              v-else
              v-model="formInline.contractOffice"
              placeholder="请输入合同单位"
              clearable
              maxlength="100"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="合同订单号：" prop="contractOrderNo">
            <span v-if="showDetail">{{ formInline.contractOrderNo }}</span>
            <el-input
              v-else
              v-model="formInline.contractOrderNo"
              placeholder="请输入合同订单号"
              clearable
              maxlength="100"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="合同分类：" prop="contractClassify">
            <span v-if="showDetail">{{ detailForm.contractClassifyName }}</span>
            <el-select
              v-else
              v-model="formInline.contractClassify"
              class="owner-select"
              placeholder="请选择合同分类"
              clearable
              filterable
            >
              <el-option v-for="item in contractClassifyList" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="16" />
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注：" prop="remark">
            <span v-if="showDetail">{{ formInline.remark }}</span>
            <el-input
              v-else
              v-model="formInline.remark"
              placeholder="请输入备注"
              :rows="3"
              type="textarea"
              clearable
              maxlength="300"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="不合格现象描述：" prop="unqualifiedDescription">
            <span v-if="showDetail">{{ formInline.unqualifiedDescription }}</span>
            <el-input
              v-else
              v-model="formInline.unqualifiedDescription"
              placeholder="请输入不合格现象描述"
              :rows="3"
              maxlength="300"
              type="textarea"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="纠正措施：" prop="correctiveAction">
            <span v-if="showDetail">{{ formInline.correctiveAction }}</span>
            <el-input
              v-else
              v-model="formInline.correctiveAction"
              placeholder="请输入纠正措施"
              :rows="3"
              type="textarea"
              maxlength="300"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { reactive, toRefs, ref, onMounted, watch, getCurrentInstance } from 'vue';
// import { ElMessage, ElMessageBox } from 'element-plus'
// import router from '@/router/index.js'
// import { useRoute } from 'vue-router'
import { useStore } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
import { formatDate } from '@/utils/formatTime';
import { getDictionary } from '@/api/user';
import { regionCode, qualityLevelCode, contractClassifyCode, productCategoryCode } from '@/data/dictionaryCode';
import { formatCodeByDicList } from '@/utils/formatDictionary';
// import { objectMapper } from '@/utils/objectMapper'

export default {
  name: 'HengTongProductionForm',
  props: {
    showDetail: {
      type: Boolean,
      default: function () {
        return false;
      }
    },
    initInfo: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['setInfo'],
  setup(props, context) {
    // const lodash = inject('_')
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const datas = reactive({
      currentAccountId: getLoginInfo().accountId,
      userOptions: store.common.nameList,
      copyUserOptions: store.common.nameList,
      formInline: {
        region: '',
        // 缺陷分类 字典code
        flawClassify: '',
        productClassify: '',
        qualityLevel: '',
        productionPersonnel: '',
        productionDate: '',
        incomingStation: '',
        contractOffice: '',
        contractOrderNo: '',
        contractClassify: '',
        remark: '',
        unqualifiedDescription: '',
        correctiveAction: ''
      },
      detailForm: {
        regionName: '',
        qualityLevelName: '',
        contractClassifyName: '',
        productClassifyName: ''
      },
      inspectionInfoRef: ref(),
      formRule: {
        region: [{ required: true, message: '请选择区域', trigger: 'change' }],
        flawClassify: [{ required: true, message: '请输入缺陷分类', trigger: 'blur' }],
        productClassify: [{ required: true, message: '请选择产品类别', trigger: 'change' }],
        qualityLevel: [{ required: true, message: '请选择质量等级', trigger: 'change' }],
        productionPersonnel: [{ required: true, message: '请输入生产人', trigger: 'blur' }],
        productionDate: [{ required: true, message: '请选择生产日期', trigger: 'change' }],
        incomingStation: [{ required: true, message: '请输入来料机台', trigger: 'blur' }],
        contractClassify: [{ required: true, message: '请选择合同分类', trigger: 'change' }],
        unqualifiedDescription: [{ required: true, message: '请输入不合格现象描述', trigger: 'blur' }]
      },
      regionOptionList: [],
      qualityLevelList: [],
      contractClassifyList: [],
      productClassifyList: []
    });

    // Object.assign(datas.formInline, props.initInfo)

    const changeproductionDate = val => {
      // console.log(val)
    };

    const getAllDicList = () => {
      Object.assign(datas.formInline, props.initInfo);
      getDictionary(regionCode).then(res => {
        datas.regionOptionList = res.data.data?.dictionaryoption;
        datas.detailForm.regionName = formatCodeByDicList(datas.formInline.region, datas.regionOptionList);
      });
      getDictionary(qualityLevelCode).then(res => {
        datas.qualityLevelList = res.data.data?.dictionaryoption;
        datas.detailForm.qualityLevelName = formatCodeByDicList(datas.formInline.qualityLevel, datas.qualityLevelList);
      });
      getDictionary(contractClassifyCode).then(res => {
        datas.contractClassifyList = res.data.data?.dictionaryoption;
        datas.detailForm.contractClassifyName = formatCodeByDicList(
          datas.formInline.contractClassify,
          datas.contractClassifyList
        );
      });
      getDictionary(productCategoryCode).then(res => {
        datas.productClassifyList = res.data.data?.dictionaryoption;
        datas.detailForm.productClassifyName = formatCodeByDicList(
          datas.formInline.productClassify,
          datas.productClassifyList
        );
      });
    };

    const changeFormData = val => {
      // context.emit('setInfo', datas.formInline)
    };
    const onSubmit = () => {
      if (proxy.$refs['inspectionInfoRef']) {
        return proxy.$refs['inspectionInfoRef'].validate();
      }
    };

    watch(
      () => datas.formInline,
      newValue => {
        if (newValue) {
          context.emit('setInfo', newValue);
        }
      },
      { deep: true }
    );

    // watch(props, (newValue) => {
    //   if (newValue?.initInfo) {
    //     Object.assign(datas.formInline, newValue.initInfo)
    //   }
    // })

    onMounted(() => {
      getAllDicList();
    });

    return {
      ...toRefs(datas),
      onSubmit,
      changeproductionDate,
      changeFormData,
      formatDate
    };
  }
};
</script>
<style lang="scss" scoped>
.formDataInfo {
  :deep(.el-input--medium .el-input__inner) {
    height: 32px;
    line-height: 32px;
  }
}

.form-wrapper {
  :deep(.el-space) {
    width: 100%;
    .el-space__item {
      margin: 16px 0;
      width: 100%;
    }
  }

  .radio-group {
    display: flex;
    justify-content: space-between;
    .el-radio-button__inner {
      img {
        width: 54px;
        height: 64px;
        margin-right: 10px;
      }
    }
    :deep(.el-radio-button__inner) {
      border: 2px solid transparent;
      border-radius: 8px;
      padding: 16px 48px;
      box-shadow: none;
      display: flex;
      justify-content: center;
      align-items: center;
      .name {
        color: #303133;
        font-size: 20px;
        line-height: 28px;
      }
    }
    .el-radio-button {
      box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
      border-radius: 8px;
    }
  }
}
.form-wrapper {
  .el-input--medium .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
  .el-divider--horizontal {
    margin: 15px 0px;
  }
  .el-form-item {
    text-align: left;
    // width: 46%;
    margin-bottom: 15px;
    :deep(.el-form-item__label) {
      font-weight: normal;
      font-size: 14px;
      color: #606266;
      padding: 0px;
    }
    :deep(.el-form-item__content) {
      .el-select {
        width: 100%;
      }
      .el-date-editor.el-input {
        width: 100%;
      }
      .el-radio-group {
        .el-radio {
          margin: 0px;
        }
        .el-radio:nth-child(2) {
          margin: 0px 5px;
        }
      }
    }
  }
}
</style>
