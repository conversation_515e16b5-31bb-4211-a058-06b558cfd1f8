<template>
  <!-- 检验申请详情 -->
  <DetailLayout>
    <template #page-header>
      <div class="header-flex flex-between">
        <div class="page-title">
          编号：{{ taskInfo.entrustNo ? taskInfo.entrustNo : '保存基本信息后生成' }}
          <el-tag size="small" :type="filterStatus(taskInfo.status)[0]">{{ filterStatus(taskInfo.status)[1] }}</el-tag>
        </div>
        <div class="btn-group">
          <el-button
            v-if="(taskInfo.status === 0 || taskInfo.status === 4) && isEdit"
            size="small"
            type="primary"
            @click="submitTask"
            @keyup.prevent
            @keydown.enter.prevent
            >{{ taskInfo.status === 4 ? '重新提交' : '提交委托' }}</el-button
          >
        </div>
      </div>
    </template>
    <el-collapse v-model="activeNames" class="collapse-wrap">
      <el-collapse-item name="1">
        <template #title>
          <div class="collapse-header-title">
            <div class="title">基本信息</div>
          </div>
        </template>
        <div class="panel-header">
          <el-button
            v-if="isEdit"
            size="small"
            icon="el-icon-edit"
            @click="editInfo"
            @keyup.prevent
            @keydown.enter.prevent
            >编辑</el-button
          >
        </div>
        <div class="panel-content">
          <el-form ref="taskInfoRef" :inline="true" :model="formInline" label-width="110px">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="委托类型：" prop="entrustType">
                  <div>{{ filterType(taskInfo.entrustType) }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="登记人：" prop="registerUserId">
                  <UserTag :name="taskInfo.regUserName || getNameByid(taskInfo.regUserId) || '--'" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="登记日期：" prop="regDate">
                  <div class="nowrap">
                    {{ formatDate(taskInfo.regDate) || '--' }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="截止日期：" prop="endDate">
                  <div class="nowrap">
                    {{ formatDate(taskInfo.endDate) || '--' }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="服务类型：" prop="type">
                  <div>
                    {{ serviceTypeJSON[taskInfo.serviceType] }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="样品回收：" prop="type">
                  <div>
                    {{ taskInfo.sampleRecycle === 0 ? '否' : '是' }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="报告语言：" prop="type">
                  <div>
                    {{ taskReportInfo() }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="盖章范围：" prop="inputWarehouseNo">
                  <div class="nowrap">
                    {{ `${taskInfo.isCnas ? 'CNAS' : ''} ${taskInfo.isCma ? 'CMA' : ''}` }}
                  </div>
                </el-form-item></el-col
              >
              <el-col :span="16">
                <el-form-item label="备注：" prop="remark">
                  <div class="nowrap">{{ taskInfo.remark || '--' }}</div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-collapse-item>
      <el-collapse-item name="2">
        <!--样品相关 -->
        <template #title>
          <div class="collapse-header-title">客户信息</div>
        </template>
        <div class="panel-header">
          <el-button
            v-if="isEdit"
            size="small"
            icon="el-icon-edit"
            @click="editClientInfo"
            @keyup.prevent
            @keydown.enter.prevent
            >编辑</el-button
          >
        </div>
        <div class="collapse-content">
          <!--委托方-->
          <div class="collapse-item">
            <div class="collapse-content-title"><span class="line-space" />委托方信息</div>
            <el-row :gutter="20" class="collapse-item">
              <el-col :span="24">
                <span class="title">委托方：</span>
                <span class="txt">
                  {{ clientInfo.entrust.customerName ? clientInfo.entrust.customerName : '--' }}
                </span>
              </el-col>
              <el-col :span="8">
                <span class="title">联系人：</span>
                <span class="txt">
                  {{ clientInfo.entrust.contactsName ? clientInfo.entrust.contactsName : '--' }}
                </span>
              </el-col>
              <el-col :span="8">
                <span class="title">电话：</span>
                <span class="txt">
                  {{ clientInfo.entrust.contactsPhone ? clientInfo.entrust.contactsPhone : '--' }}
                </span>
              </el-col>
              <el-col :span="8">
                <span class="title">公司电话：</span>
                <span class="txt">
                  {{ clientInfo.entrust.phone || '--' }}
                </span>
              </el-col>
              <el-col :span="24">
                <span class="title">公司地址：</span>
                <span class="txt">
                  {{ clientInfo.entrust.address ? clientInfo.entrust.address : '--' }}
                </span>
              </el-col>
            </el-row>
            <el-divider />
          </div>
          <!--缴款方-->
          <div class="collapse-item">
            <div class="collapse-content-title"><span class="line-space" />缴款方信息</div>
            <el-row :gutter="20" class="collapse-item">
              <el-col :span="24">
                <span class="title">缴款方：</span>
                <span class="txt">
                  {{ clientInfo.payer.customerName ? clientInfo.payer.customerName : '--' }}
                </span>
              </el-col>
              <el-col :span="8">
                <span class="title">公司税号：</span>
                <span class="txt">
                  {{ clientInfo.payer.taxNo ? clientInfo.payer.taxNo : '--' }}
                </span>
              </el-col>
              <el-col :span="8">
                <span class="title">电话：</span>
                <span class="txt">
                  {{ clientInfo.payer.phone ? clientInfo.payer.phone : '--' }}
                </span>
              </el-col>
              <el-col :span="8">
                <span class="title">开户行：</span>
                <span class="txt">
                  {{ clientInfo.payer.openingBank ? clientInfo.payer.openingBank : '--' }}
                </span>
              </el-col>
              <el-col :span="16">
                <span class="title">地址：</span>
                <span class="txt">
                  {{ clientInfo.payer.address ? clientInfo.payer.address : '--' }}
                </span>
              </el-col>
            </el-row>
            <el-divider />
          </div>
          <!--发票邮寄信息-->
          <div class="collapse-item">
            <div class="collapse-content-title"><span class="line-space" />发票邮寄信息</div>
            <el-row :gutter="20" class="collapse-item">
              <el-col :span="24">
                <span class="title">发票邮寄抬头：</span>
                <span class="txt">
                  {{ clientInfo.invoice.customerName ? clientInfo.invoice.customerName : '--' }}
                </span>
              </el-col>
              <el-col :span="8">
                <span class="title">联系人：</span>
                <span class="txt">
                  {{ clientInfo.invoice.contactsName ? clientInfo.invoice.contactsName : '--' }}
                </span>
              </el-col>
              <el-col :span="8">
                <span class="title">电话：</span>
                <span class="txt">
                  {{ clientInfo.invoice.contactsPhone ? clientInfo.invoice.contactsPhone : '--' }}
                </span>
              </el-col>
              <el-col :span="24">
                <span class="title">邮寄地址：</span>
                <span class="txt">
                  {{ clientInfo.invoice.address ? clientInfo.invoice.address : '--' }}
                </span>
              </el-col>
            </el-row>
            <el-divider />
          </div>
          <!--报告邮寄信息-->
          <div class="collapse-item">
            <div class="collapse-content-title"><span class="line-space" />报告邮寄信息</div>
            <el-row :gutter="20" class="collapse-item">
              <el-col :span="24">
                <span class="title">报告邮寄抬头：</span>
                <span class="txt">
                  {{ clientInfo.report.customerName ? clientInfo.report.customerName : '--' }}
                </span>
              </el-col>
              <el-col :span="8">
                <span class="title">联系人：</span>
                <span class="txt">
                  {{ clientInfo.report.contactsName ? clientInfo.report.contactsName : '--' }}
                </span>
              </el-col>
              <el-col :span="8">
                <span class="title">电话：</span>
                <span class="txt">
                  {{ clientInfo.report.contactsPhone ? clientInfo.report.contactsPhone : '--' }}
                </span>
              </el-col>
              <el-col :span="24">
                <span class="title">地址：</span>
                <span class="txt">
                  {{ clientInfo.report.address ? clientInfo.report.address : '--' }}
                </span>
              </el-col>
            </el-row>
            <el-divider />
          </div>
          <!--生产商信息-->
          <div class="collapse-item">
            <div class="collapse-content-title"><span class="line-space" />生产商信息</div>
            <el-row :gutter="20" class="collapse-item">
              <el-col :span="24">
                <span class="title">生产商：</span>
                <span class="txt">
                  {{ clientInfo.producer.customerName ? clientInfo.producer.customerName : '--' }}
                </span>
              </el-col>
              <el-col :span="8">
                <span class="title">联系人：</span>
                <span class="txt">
                  {{ clientInfo.producer.contactsName ? clientInfo.producer.contactsName : '--' }}
                </span>
              </el-col>
              <el-col :span="8">
                <span class="title">电话：</span>
                <span class="txt">
                  {{ clientInfo.producer.contactsPhone ? clientInfo.producer.contactsPhone : '--' }}
                </span>
              </el-col>
              <el-col :span="24">
                <span class="title">地址：</span>
                <span class="txt">
                  {{ clientInfo.producer.address ? clientInfo.producer.address : '--' }}
                </span>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-collapse-item>
      <el-collapse-item name="3">
        <!-- 样品相关 -->
        <template #title>
          <div class="collapse-header-title">样品信息</div>
        </template>
        <PanelSampleInfo
          :is-invalidated="taskInfo.isInvalidated"
          :task-id="formInline.id"
          :material-code="formInline.materialCode"
          :show-detail="isEdit"
          :sample-info="sampleInfoList"
          @set-info="getSampleInfoList"
        />
      </el-collapse-item>
      <el-collapse-item name="4">
        <!-- 费用信息 -->
        <template #title>
          <div class="collapse-header-title">费用信息</div>
        </template>
        <PanelFeeInfo
          :task-id="formInline.id"
          :show-detail="isEdit"
          :fee-info-data="feeInfoData"
          @set-info="getFeeInfoList"
        />
      </el-collapse-item>
      <el-collapse-item name="5">
        <!-- 费用信息 -->
        <template #title>
          <div class="collapse-header-title">附件信息</div>
        </template>
        <PanelAttachInfo
          :task-id="formInline.id"
          :show-detail="isEdit"
          :attach-info="attachInfoList"
          @set-info="getAttachInfoList"
        />
      </el-collapse-item>
      <el-collapse-item name="6">
        <!-- 费用信息 -->
        <template #title>
          <div class="collapse-header-title">技术评审</div>
        </template>
        <PanelReviewInfo
          :task-id="formInline.id"
          :show-detail="isEdit"
          :process-id="formInline.processInstanceId"
          :task-status="taskInfo.status"
          :review-info="reviewInfoList"
          @set-info="getReviewInfo"
        />
      </el-collapse-item>
    </el-collapse>

    <template #other>
      <!-- 编辑委托登记 -->
      <DialogTaskRegistration
        :show="showEditInfoDialog"
        title="编辑基本信息"
        :is-edit="true"
        :info="taskInfo"
        @close="closeEditInfo"
      />
      <!-- 编辑客户信息 -->
      <DialogClientInfo
        :show="showEditClientInfoDialog"
        title="编辑客户信息"
        :is-edit="true"
        :info="clientInfo"
        @close="closeEditClientInfo"
      />
    </template>
  </DetailLayout>
</template>

<script>
import { reactive, toRefs, ref, computed, watch } from 'vue';
// import router from '@/router/index.js'
// import { ElMessage, ElMessageBox } from 'element-plus'
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { getLoginInfo } from '@/utils/auth';
// import { drageHeader } from '@/utils/formatTable'
import _ from 'lodash';
import DetailLayout from '@/components/DetailLayout';
import UserTag from '@/components/UserTag';
import {
  getTaskRegistrationInfo,
  getTaskCustomerInfo,
  getTaskSampleList,
  getFeeInfoListByTaskId,
  getAttachmentListByTaskId,
  getTechnicalReviewList,
  submitTaskRegistrationInfo,
  saveTechnicalReviewInfo
} from '@/api/task-registration';
import { formatTaskType } from '@/utils/formatIndustryTerm';
import DialogTaskRegistration from './components/DialogTaskRegistration.vue';
import DialogClientInfo from './components/DialogClientInfo.vue';
import PanelSampleInfo from './components/PanelSampleInfo.vue';
import PanelFeeInfo from './components/PanelFeeInfo.vue';
import PanelAttachInfo from './components/PanelAttachInfo.vue';
import PanelReviewInfo from './components/PanelReviewInfo.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { filterStatus } from './func/format';

export default {
  name: 'TaskRegistrationDetail',
  components: {
    DetailLayout,
    PanelSampleInfo,
    UserTag,
    PanelFeeInfo,
    PanelAttachInfo,
    PanelReviewInfo,
    DialogTaskRegistration,
    DialogClientInfo
  },
  setup() {
    // const { proxy } = getCurrentInstance()
    const route = useRoute();
    // console.log(route.query)
    const store = useStore().state;
    const datas = reactive({
      activeNames: route.query.flag === '3' ? ['1', '6'] : ['1', '2', '3', '4', '5', '6'],
      clientInfo: {
        entrust: {},
        invoice: {},
        payer: {},
        producer: {},
        report: {},
        superId: ''
      },
      sampleInfoList: [],
      feeInfoData: [],
      attachInfoList: [],
      reviewInfoList: [],
      currentAccountId: getLoginInfo().accountId,
      userOptions: store.common.nameList,
      copyUserOptions: store.common.nameList,
      showDetail: route.query.id !== '0',
      formInline: {
        registerUserId: '',
        registerDepartment: '',
        registerTime: '',
        type: 1,
        inputWarehouseNo: '',
        wareHouseNo: '',
        wareHouseName: '',
        wareHousePerson: '',
        inputWarehouseDate: '',
        applyStatus: 1
      },
      serviceTypeJSON: {
        0: '标准服务',
        1: '加急服务',
        2: '特急服务'
      },
      taskInfo: {},
      taskInfoRef: ref(),
      taskInfoRule: {
        registerUserId: [{ required: true, message: '请输入登记人' }],
        registerTime: [{ required: true, message: '请选择登记日期' }],
        type: [{ required: true, message: '请输入检验类型' }]
      },
      typeOptions: [
        { id: 1, name: '采购入库' },
        { id: 2, name: '过程检验' },
        { id: 3, name: '完工检验' }
      ],
      showEditInfoDialog: false,
      showEditClientInfoDialog: false,
      backUrl: '/inspection-application'
    });

    // 获取委托登记信息
    const getTaskRegInfo = flag => {
      getTaskRegistrationInfo(route.query.id).then(res => {
        if (res !== false) {
          datas.taskInfo = res.data.data;
          datas.formInline = datas.taskInfo;
          if (datas.taskInfo.id) {
            getTaskCustomerInfo(datas.taskInfo.id).then(response => {
              if (response && response.data.data) {
                const result = response.data.data;
                if (result.entrust?.id) {
                  datas.clientInfo.entrust = result.entrust;
                }
                if (result.invoice?.id) {
                  datas.clientInfo.invoice = result.invoice;
                }
                if (result.payer?.id) {
                  datas.clientInfo.payer = result.payer;
                }
                if (result.report?.id) {
                  datas.clientInfo.report = result.report;
                }
                if (result.producer?.id) {
                  datas.clientInfo.producer = result.producer;
                }
              }
              datas.clientInfo.superId = datas.taskInfo.id;
            });
          }
          getSampleInfoList();
          getAttachInfoList();
          getReviewList();
        }
      });
    };

    // 判断是否是新增, 要是有id调用详情接口
    getTaskRegInfo();
    // 过滤检验类型
    const filterType = type => {
      // 检验类型 1、采购入库 2、过程检验 3、完工检验
      return formatTaskType(type);
    };
    // 过滤登记人
    const filterUserList = val => {
      if (val) {
        const list = [];
        datas.copyUserOptions.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        datas.userOptions = list;
      } else {
        datas.userOptions = datas.copyUserOptions;
      }
    };
    // 登记人-change
    const changeUser = id => {
      datas.formInline.registerUserId = id;
    };
    // 选择登记日期
    const changeRegisterTime = time => {
      // console.log(time)
    };
    // 检验类型-change
    const changeType = type => {
      datas.formInline.type = type;
    };
    // 生产工序-change
    const changeProductionProcedure = name => {
      // console.log(name)
      datas.formInline.productionProcedure = name;
    };
    // 选择入库日期
    const changeInputWarehouseDate = time => {
      // console.log(time)
    };
    // 获取样品相关列表
    const getSampleInfoList = list => {
      if (datas.taskInfo.id) {
        getTaskSampleList(datas.taskInfo.id).then(resp => {
          if (resp && resp.data.data) {
            datas.sampleInfoList = resp.data.data;
            getFeeInfoList();
          }
        });
      }
    };

    const getFeeInfoList = list => {
      if (datas.taskInfo.id) {
        getFeeInfoListByTaskId(datas.taskInfo.id).then(resp => {
          if (resp && resp.data.data) {
            datas.feeInfoData = resp.data.data;
          }
        });
      }
    };

    const getAttachInfoList = list => {
      if (datas.taskInfo.id) {
        getAttachmentListByTaskId(datas.taskInfo.id).then(resp => {
          if (resp && resp.data.data) {
            datas.attachInfoList = resp.data.data;
          }
        });
      }
    };

    const getReviewList = list => {
      if (datas.taskInfo.processInstanceId) {
        getTechnicalReviewList(datas.taskInfo.processInstanceId).then(resp => {
          if (resp && resp.data.data) {
            const processList = resp.data.data;
            datas.reviewInfoList = [];
            processList.forEach(result => {
              datas.reviewInfoList.push({
                approveBy: result.assignee,
                approveDetail: result.processParameter.opinion,
                approveResult: result.processParameter.isAssent,
                approveType: result.processParameter.approveType,
                devAbility: result.processParameter.devAbility,
                envAbility: result.processParameter.envAbility,
                staffAbility: result.processParameter.staffAbility,
                endTime: result.endTime,
                startTime: result.startTime,
                nodeName: result.name,
                startUser: result.startUser,
                processInstanceId: result.processInstanceId
              });
            });
          }
        });
      }
    };

    function getReviewInfo(value) {
      getTaskRegistrationInfo(route.query.id).then(res => {
        if (res !== false) {
          datas.taskInfo = res.data.data;
          datas.formInline = datas.taskInfo;
          getReviewList();
        }
      });
    }

    // 编辑
    const editInfo = () => {
      // console.log(datas.taskInfo)
      datas.showEditInfoDialog = true;
    };

    // 编辑弹出框-关闭
    const closeEditInfo = v => {
      // console.log(v)
      datas.showEditInfoDialog = false;
      getTaskRegInfo();
    };

    // #region 客户信息

    // 编辑
    const editClientInfo = () => {
      // console.log(datas.taskInfo)
      datas.showEditClientInfoDialog = true;
    };

    // 编辑弹出框-关闭
    const closeEditClientInfo = clientInfo => {
      // console.log(v)
      if (clientInfo && clientInfo.superId) {
        datas.clientInfo = clientInfo;
      }
      datas.showEditClientInfoDialog = false;
      // getClientInfo()
    };

    // #endregion

    // #region 样品信息

    // #endregion

    // #region 费用信息

    // #endregion

    // #region 附件信息

    // #endregion

    // #region 技术评审

    // #endregion

    function submitTask() {
      ElMessageBox({
        title: '提交委托',
        message: '是否提交该委托？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          if (datas.taskInfo.status === 0) {
            submitTaskRegistrationInfo(datas.formInline.id).then(res => {
              if (res !== false) {
                getTaskRegInfo();
                ElMessage.success('提交成功');
                datas.activeNames = ['1', '6'];
              }
            });
          } else {
            const resultParams = {
              devAbility: '',
              envAbility: '',
              staffAbility: '',
              isAssent: '1',
              opinion: ''
            };
            const executeParams = {
              businessKey: datas.taskInfo.id,
              processInstanceId: datas.taskInfo.processInstanceId,
              processParameter: resultParams
            };
            saveTechnicalReviewInfo(executeParams).then(res => {
              if (res !== false) {
                getTaskRegInfo();
                ElMessage.success('提交成功');
                datas.activeNames = ['1', '6'];
              }
            });
          }
        })
        .catch(() => {});
    }

    const isEdit = computed({
      get: () =>
        (route.query.flag === '2' || route.query.flag === '3') &&
        getPermissionBtn('TaskEditBtn') &&
        (datas.taskInfo.status === 0 || datas.taskInfo.status === 4)
    });

    const taskReportInfo = () => {
      let result = '';
      if (!datas.taskInfo.reportCn && !datas.taskInfo.reportEn && !datas.taskInfo.reportBilingual) {
        result = '只需结果，不需报告,';
      } else {
        if (datas.taskInfo.reportCn) {
          result += `中文报告${datas.taskInfo.reportCnNum}份,`;
        }
        if (datas.taskInfo.reportEn) {
          result += `英文报告${datas.taskInfo.reportEnNum}份,`;
        }
        if (datas.taskInfo.reportBilingual) {
          result += `中英文报告${datas.taskInfo.reportBilingualNum}份,`;
        }
      }
      return result ? result.substring(0, result.length - 1) : '--';
    };

    watch(
      () => datas.taskInfo.status,
      newValue => {
        if (newValue === 1 || newValue === 2) {
          datas.activeNames = ['1', '6'];
        }
      },
      { deep: true }
    );

    return {
      ...toRefs(datas),
      isEdit,
      taskReportInfo,
      formatDate,
      getNameByid,
      filterStatus,
      filterUserList,
      changeRegisterTime,
      filterType,
      getTaskRegInfo,
      editInfo,
      changeType,
      changeInputWarehouseDate,
      changeUser,
      changeProductionProcedure,
      getSampleInfoList,
      getFeeInfoList,
      getAttachInfoList,
      closeEditInfo,
      getPermissionBtn,
      editClientInfo,
      closeEditClientInfo,
      getReviewList,
      getReviewInfo,
      submitTask
    };
  },
  async created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.panel-header {
  margin-top: 0.5rem;
  height: 1rem;
  .el-button {
    float: right;
  }
}
</style>
