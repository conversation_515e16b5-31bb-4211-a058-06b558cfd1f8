<template>
    <!-- 原始记录审核 -->
    <ListLayout>
        <template #search-bar>
            <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
                <el-form-item prop="param">
                    <div style="width: 35vw">
                        <CombinationQuery
                            :field-list="searchFieldList"
                            :field-tip="fieldTips"
                            @get-single-text="getSingleText"
                            @get-param-list="getParamList"
                            @reset-search="reset"
                        />
                    </div>
                </el-form-item>
            </el-form>
        </template>
        <template #button-group>
            <el-button
                v-if="getPermissionBtn('BatchWarehousing')"
                size="large"
                icon="el-icon-document-checked"
                type="primary"
                :disabled="!selectRow.length"
                @click="handleWarehouse(selectRow, true)"
                @keyup.prevent
                @keydown.enter.prevent
                >更新入库状态</el-button
            >
            <el-button
                v-if="getPermissionBtn('BatchPrintConformity')"
                size="large"
                icon="el-icon-document-checked"
                type="primary"
                :disabled="!selectRow.length"
                @click="handleUpdatePrintStatus(selectRow)"
                @keyup.prevent
                @keydown.enter.prevent
                >更新合格证状态</el-button
            >
            <el-button
                v-if="getPermissionBtn('BatchDetermination')"
                size="large"
                icon="el-icon-document-checked"
                type="primary"
                :disabled="!selectRow.length"
                @click="handleDetermin"
                @keyup.prevent
                @keydown.enter.prevent
                >批量判定</el-button
            >
            <el-button
                v-if="getPermissionBtn('BatchWarehousing')"
                size="large"
                icon="el-icon-document-checked"
                type="primary"
                :disabled="!selectRow.length"
                @click="handleWarehouse(selectRow)"
                @keyup.prevent
                @keydown.enter.prevent
                >批量入库</el-button
            >
            <el-button
                v-if="getPermissionBtn('BatchPrintConformity')"
                size="large"
                icon="el-icon-document-checked"
                type="primary"
                :disabled="!selectRow.length"
                @click="handlePrint(selectRow)"
                @keyup.prevent
                @keydown.enter.prevent
                >批量打印合格证</el-button
            >
        </template>
        <template #radio-content>
            <el-row>
                <el-col :span="16" class="flex gap-6">
                    <el-radio-group v-model="radioValue" size="small" @change="handleChangeRadio">
                        <el-radio-button label="">全部</el-radio-button>
                        <el-radio-button v-for="(val, key) in radioData" :key="key" :label="key">{{ val }}</el-radio-button>
                    </el-radio-group>
                </el-col>
                <el-col :span="8" style="text-align: right">
                    <TableColumnView binding-menu="RawCertificate" @columns="onUpdateColumns" />
                </el-col>
            </el-row>
        </template>
        <el-table
            ref="tableRef"
            :key="tableKey"
            v-loading="listLoading"
            :data="tableList"
            size="medium"
            fit
            border
            height="auto"
            class="dark-table test-item-table base-table format-height-table"
            :row-style="
                () => {
                    return 'cursor: pointer';
                }
            "
            @header-dragend="drageHeader"
            @row-click="handleRowClick"
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" :width="colWidth.checkbox" align="center" fixed="left" />
            <template v-for="(item, index) in tableColumns" :key="index">
                <el-table-column
                    :prop="item.fieldKey"
                    :label="item.fieldName"
                    :sortable="Number(item.isSortable) === 1"
                    :width="item.isMinWidth ? '' : item.columnWidth"
                    :min-width="item.isMinWidth ? item.columnWidth : ''"
                    show-overflow-tooltip
                >
                    <template #default="{ row }">
                        <template v-if="item.fieldType === fieldTypesEnum.Person">
                            <template v-if="row[item.fieldKey]">
                                <UserTag v-for="ownerId in row[item.fieldKey].split(',')" :key="ownerId" :name="getNameByid(ownerId) || ownerId || '--'" />
                            </template>
                            <span v-else>--</span>
                        </template>
                        <template v-else-if="item.fieldType === fieldTypesEnum.Link">
                            <template v-if="item.fieldKey == 'no'">
                                <span v-if="row[item.fieldKey]" v-copy="row.no" class="nowrap blue-color" @click.stop="iaDetail(row)">{{ row.no }}</span>
                                <span v-else>--</span>
                            </template>
                            <template v-else-if="item.fieldKey == 'secSampleNum'">
                                <span v-if="row[item.fieldKey]" v-copy="row.secSampleNum" class="nowrap blue-color" @click.stop="handleSampleDetail(row)">{{
                                    row.secSampleNum || "--"
                                }}</span>
                                <span v-else>--</span>
                            </template>
                            <template v-else-if="item.fieldKey == 'inspectionCode'">
                                <span v-if="row[item.fieldKey]" v-copy="row.inspectionCode" class="nowrap blue-color" @click.stop="handleReportDetail(row)">{{
                                    row.inspectionCode || "--"
                                }}</span>
                                <span v-else>--</span>
                            </template>
                        </template>
                        <template v-else-if="item.fieldType === fieldTypesEnum.Status">
                            <el-tag v-if="row[item.fieldKey] !== ''" size="small" effect="dark" :type="statusJson[item.fieldKey][row[item.fieldKey]]?.type">{{
                                statusJson[item.fieldKey][row[item.fieldKey]]?.label
                            }}</el-tag>
                            <span v-else>--</span>
                        </template>
                        <template v-else-if="item.fieldType === fieldTypesEnum.Date">
                            <span>{{ row[item.fieldKey] == "" ? "--" : formatDate(row[item.fieldKey]) }}</span>
                        </template>
                        <template v-else-if="item.fieldType === fieldTypesEnum.Custom">
                            <div v-if="item.fieldKey === 'nuclearMarker'">
                                <span v-if="row.nuclearMarker">
                                    {{ row.nuclearMarker }}
                                </span>
                                <span v-else>--</span>
                            </div>
                            <div v-else-if="item.fieldKey == 'reportType'">
                                {{ reportTypeJSON[row[item.fieldKey]] || row[item.fieldKey] }}
                            </div>
                            <div v-else-if="item.fieldKey == 'mateType'">
                                {{ materialCategoryAll[row[item.fieldKey]]?.name || row[item.fieldKey] }}
                            </div>
                            <div v-else-if="item.fieldKey == 'decideType'">
                                {{ dictionary["HGZSCPD"].all[row[item.fieldKey]] || row[item.fieldKey] || "--" }}
                            </div>
                        </template>
                        <template v-else-if="item.fieldKey === 'mateNameGroup'">
                            <span>{{ row.materialGroup == "" ? "--" : row.materialGroupId }}</span>
                        </template>
                        <template v-else-if="item.fieldKey === 'inputWarehouse'">
                            <span>{{ row.wareHouseName == "" ? "--" : row.wareHouseNo }}</span>
                        </template>
                        <template v-else-if="item.fieldKey === 'inputWarehouseUnit'">
                            <span>{{ dictionary["5"].all[row.inputWarehouseUnit] || "--" }}</span>
                        </template>
                        <span v-else>{{ row[item.fieldKey] || "--" }}</span>
                    </template>
                </el-table-column>
            </template>
            <el-table-column label="操作" fixed="right" :min-width="colWidth.operation">
                <template #default="{ row }">
                    <span v-if="getPermissionBtn('RawCertificateEdit')" class="blue-color" @click.stop="handleEdit([row])">编辑</span>
                    <span v-if="row.thirdDataReturnStatus === thirdDataReturnStatusConst.unInStorage" class="blue-color" @click.stop="handleWarehouse([row])"
                        >入库</span
                    >
                </template>
            </el-table-column>
            <el-table-column label="打印" fixed="right" :min-width="colWidth.operationSingle">
                <template #default="{ row }">
                    <span class="blue-color" @click.stop="handlePrint([row])">合格证</span>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getList" />
        <template #other>
            <!-- 批量判定 -->
            <DialogBatchDetermin :dialog-visible="dialogVisible" :select-row="selectRow" :dictionary="dictionary" @closeDialog="closeDialog" />
            <!-- 批量完成 -->
            <DialogBatchCompletion :un-list="unFinishedSampleList" :dialog-visible="showUnFinishedDialog" />
            <!-- 合格证打印 -->
            <DialogBatchPrint :dialog-visible="dialogPrint" :select-row="rowData" @closeDialog="closeDialogPrint" />
            <!-- 编辑特殊标识 -->
            <DialogBatchEdit :dialog-visible="dialogEdit" :select-row="rowData" @closeDialog="closeDialogEdit" />

            <el-dialog v-model="batchInStorageDialogVisible" title="批量导入结果" width="40%" :before-close="onCloseBatchInStorageDialog">
                <div class="flex gap-5 mb-2">
                    <div>入库总数量：{{ batchInStorageResult.total }}</div>
                    <div>入库成功数：{{ batchInStorageResult.successTotal }}</div>
                    <div>入库失败数：{{ batchInStorageResult.errorTotal }}</div>
                </div>
                <div class="mb-2">入库失败信息：</div>
                <div v-if="batchInStorageResult.sapErrorMessage.length > 0">
                    <div v-for="(item, index) in batchInStorageResult.sapErrorMessage" :key="index">{{ item }}</div>
                </div>
                <template #footer>
                    <el-button type="primary" @click="onCloseBatchInStorageDialog()"> 确定 </el-button>
                </template>
            </el-dialog>
        </template>
        <el-dialog v-model="batchWareHouseDialogVisible" title="批量入库" width="35%" :before-close="onCloseBatchWareHouseDialog">
            <div>
                <span class="mb-2">入库时间</span>
                <el-date-picker
                    v-model="wareHouseDate"
                    type="date"
                    :clearable="false"
                    placeholder="请选择入库时间"
                    style="width: 100%"
                    value-format="YYYY-MM-DD"
                />
            </div>
            <template #footer>
                <el-button @click="onCloseBatchWareHouseDialog()"> 取消 </el-button>
                <el-button type="primary" @click="onConfirmBatchWareHouse(selectRow)"> 确定 </el-button>
            </template>
        </el-dialog>
    </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, onMounted, nextTick } from "vue";
import router from "@/router/index.js";
import { ElMessage, ElMessageBox } from "element-plus";
import Pagination from "@/components/Pagination";
import ListLayout from "@/components/ListLayout";
import { formatDate } from "@/utils/formatTime";
import UserTag from "@/components/UserTag";
import { getNamesByid, getNameByid, getPermissionBtn } from "@/utils/common";
import { getCertificateprintList, rawDataReturn, updateInputWarehouseStatus, updatePrintStatus } from "@/api/raw-certificate";
import { drageHeader } from "@/utils/formatTable";
import { colWidth } from "@/data/tableStyle";
import CombinationQuery from "@/components/CombinationQuery";
import TableColumnView from "@/components/TableColumnView";
import { fieldTypesEnum, columnFixedTypesEnum } from "@/components/TableColumnView/enum";
import { getDictionary } from "@/api/user";
import DialogBatchDetermin from "./components/DialogBatchDetermin.vue";
import DialogBatchCompletion from "./components/DialogBatchCompletion.vue";
import DialogBatchPrint from "./components/DialogBatchPrint.vue";
import DialogBatchEdit from "./components/DialogBatchEdit.vue";
import { materialCategoryList } from "@/api/material";
import { saveBatchFinish } from "@/api/order";
import { saveCertificatePrint } from "@/api/certificate-export";
import { debounce } from "lodash";
export default {
    name: "RawCertificate",
    components: {
        Pagination,
        ListLayout,
        UserTag,
        CombinationQuery,
        TableColumnView,
        DialogBatchDetermin,
        DialogBatchPrint,
        DialogBatchEdit,
        DialogBatchCompletion,
    },
    setup() {
        const thirdDataReturnStatusConst = {
            /**
             * 0:未入库
             */
            unInStorage: "0",
            /**
             * 1:入库
             */
            inStorage: "1",
        };
        const state = reactive({
            tableRef: ref(),
            fieldTips: "",
            dialogVisible: false,
            materialCategoryAll: {},
            materialList: [],
            dialogPrint: false,
            showUnFinishedDialog: false,
            dialogEdit: false,
            searchFieldList: [],
            rowData: [],
            unFinishedSampleList: [],
            listLoading: false,
            radioData: {
                1: "未打印",
                2: "已打印",
                3: "待入库",
                4: "已入库",
            },
            statusJson: {
                printStatus: {
                    0: { type: "warning", label: "未打印" },
                    1: { type: "success", label: "已打印" },
                },
                thirdDataReturnStatus: {
                    0: { type: "warning", label: "待入库" },
                    1: { type: "success", label: "已入库" },
                },
                decideMethod: {
                    0: { type: "info", label: "空" },
                    1: { type: "primary", label: "手动" },
                    2: { type: "success", label: "自动" },
                },
                reportType: {
                    0: { type: "success", label: "合格" },
                    1: { type: "error", label: "不合格" },
                    2: { type: "info", label: "不判定" },
                },
            },

            listQuery: {
                limit: 20,
                page: 1,
            },
            radioValue: "",
            formInline: {
                param: "",
                mateType: "",
                certificateType: 1,
                tableQueryParamList: [],
            },
            dictionary: {
                HGZSCPD: {
                    enable: {},
                    all: {},
                },
                5: {
                    enable: {},
                    all: {},
                },
            },
            tableColumns: [],
            reportTypeJSON: {
                0: "合格",
                1: "不合格",
                2: "不判定",
            },
            tableList: [],
            oldRow: {}, // 右侧选中的行数据（修改之前）
            tableLeft: [],
            selectRow: [], // 表格选中的值
            dialogFormVisible: false,
            total: 0,
            batchInStorageDialogVisible: false,
            batchInStorageResult: {
                total: 0,
                errorTotal: 0,
                successTotal: 0,
                sapErrorMessage: [],
            },
            wareHouseDate: formatDate(new Date()),
            batchWareHouseDialogVisible: false,
            isUpdateInputWarehouseStatus: false,
            isBatchDetermination: false,
        });
        // 要是从待办那边跳转过来，需要添加过滤条件，暂时过滤样品编号
        const winUrl = decodeURI(window.location.href);
        const urlList = winUrl.split("?");
        const sampleSecNo = urlList[1];
        if (sampleSecNo) {
            state.formInline.key = sampleSecNo;
        }
        const tableKey = ref(0);
        const tableKeyLeft = ref(0);
        const getList = (query) => {
            const params = { ...state.formInline };
            if (query && query.page) {
                params.page = query.page.toString();
                params.limit = query.limit.toString();
                state.listQuery.page = query.page;
                state.listQuery.limit = query.limit;
            } else {
                state.listQuery.page = 1;
                params.page = "1";
                params.limit = state.listQuery.limit.toString();
            }
            state.listLoading = true;
            getCertificateprintList(params).then((res) => {
                state.listLoading = false;
                if (res) {
                    state.total = res.data.data.totalCount;
                    state.tableList = res.data.data.list;
                    state.listQuery.page = Number(params.page);
                    console.log(state.selectRow);
                }
            });
        };
        const getDictionaryList = () => {
            Object.keys(state.dictionary).forEach(async (item) => {
                const response = await getDictionary(item);
                if (response) {
                    state.dictionary[item] = { enable: {}, all: {} };
                    response.data.data.dictionaryoption.forEach((optionItem) => {
                        if (optionItem.status == 1) {
                            state.dictionary[item].enable[optionItem.code] = optionItem.name;
                        }
                        state.dictionary[item].all[optionItem.code] = optionItem.name;
                    });
                }
            });
        };

        // 切换核电标志
        const handleChangeRow = async (val, row) => {
            const params = {
                ...row,
                nuclearMarker: val.toString(),
            };
            state.listLoading = true;
            const { data } = await saveCertificatePrint({ entityList: [params] }).finally((state.listLoading = false));
            if (data) {
                ElMessage.success("编辑成功!");
                getList();
            }
        };

        const handleSizeChange = (val) => {
            state.listQuery.limit = val;
            getList();
        };
        const reset = () => {
            state.formInline = {
                param: "",
                certificateType: 1,
                mateType: state.materialList[0]?.code,
                tableQueryParamList: [],
            };
            getList();
        };
        const handleSelectionChange = (val) => {
            if (state.isBatchDetermination) {
                state.isBatchDetermination = false;
                nextTick(() => {
                    setTimeout(() => {
                        state.selectRow.forEach((row) => state.tableRef.toggleRowSelection(row, true));
                    }, 300);
                });
                return;
            }
            state.selectRow = val;
            console.log("handleSelectionChange", val);
        };
        const handleAudit = () => {};
        /** 批量判定 */
        const handleDetermin = () => {
            state.isBatchDetermination = true;
            state.dialogVisible = true;
        };

        const handleRowClick = (row) => {
            state.tableRef.toggleRowSelection(
                row,
                !state.selectRow.some((item) => {
                    return row.id === item.id;
                })
            );
        };
        const getSingleText = (val) => {
            state.formInline.param = val;
            state.formInline.tableQueryParamList = [];
            getList();
        };

        const getParamList = (paramList) => {
            state.formInline.tableQueryParamList = paramList;
            state.formInline.param = "";
            getList();
        };
        // 打印
        const handlePrint = (row) => {
            state.dialogPrint = true;
            state.rowData = row;
        };
        // 编辑
        const handleEdit = (row) => {
            state.dialogEdit = true;
            state.rowData = row;
        };

        const handleChangeRadio = (val) => {
            if (val == 1) {
                state.formInline.printStatus = 0;
                delete state.formInline.thirdDataReturnStatus;
            } else if (val == 2) {
                state.formInline.printStatus = 1;
                delete state.formInline.thirdDataReturnStatus;
            } else if (val == 3) {
                state.formInline.thirdDataReturnStatus = 0;
                delete state.formInline.printStatus;
            } else if (val == 4) {
                state.formInline.thirdDataReturnStatus = 1;
                delete state.formInline.printStatus;
            } else {
                delete state.formInline.printStatus;
                delete state.formInline.thirdDataReturnStatus;
            }
            getList();
        };
        // 保存
        const handleSave = (row) => {
            console.log(row);
        };
        // 取消保存
        const handleCancle = (index) => {
            state.tableList[index] = JSON.parse(JSON.stringify(state.oldRow));
            state.tableList[index].isEdit = false;
        };

        const onUpdateColumns = (columns) => {
            tableKey.value = tableKey.value + 1;
            state.tableColumns = columns;
            state.searchFieldList = columns.filter((item) => {
                return item.isQuery == 1;
            });
            state.fieldTips = state.searchFieldList.map((item) => item.fieldName).join("/");
        };
        const closeDialog = (value) => {
            state.dialogVisible = false;
            if (value) {
                getList(state.listQuery);
                if (value.progress == 1) {
                    submitBatchFinish();
                }
            }
        };

        const closeDialogPrint = (isRefresh) => {
            state.dialogPrint = false;
            if (isRefresh) {
                getList();
            }
        };

        const closeDialogEdit = (isRefresh) => {
            state.dialogEdit = false;
            if (isRefresh) {
                getList();
            }
        };

        const submitBatchFinish = (event = false) => {
            const params = {
                sampleBatchFinishVoList: [],
            };
            state.selectRow.forEach((item) => {
                params.sampleBatchFinishVoList.push({
                    sampleId: item.sampleId,
                    secSampleNum: item.secSampleNum,
                    status: item.status,
                });
            });
            saveBatchFinish(params).then((res) => {
                if (res.data.code === 200) {
                    if (res.data.data && res.data.data.length > 0) {
                        if (event) {
                            ElMessage.success("部分样品已完成检测,无法完成的样品见提示框!");
                        } else {
                            ElMessage.success("所选样品已全部判定合格,部分样品已完成检测,无法完成的样品见提示框!");
                        }
                        state.unFinishedSampleList = res.data.data;
                        state.showUnFinishedDialog = true;
                    } else {
                        if (event) {
                            ElMessage.success("所选样品已全部完成检测!");
                        } else {
                            ElMessage.success("所选样品已全部判定合格并完成检测!");
                        }
                    }
                } else {
                    ElMessage.error(`${res.data.message}`);
                }
            });
        };

        const getMaterialcategory = () => {
            materialCategoryList("").then((response) => {
                state.materialList = response.data.data;
                state.materialCategoryAll = {};
                state.materialList.forEach((item) => {
                    if (item.parentId === "0") {
                        state.materialCategoryAll[item.code] = item;
                    }
                });
                state.formInline.mateType = state.materialList[0]?.code;
                getList();
            });
        };
        // 查看
        const iaDetail = (row) => {
            if (row.no) {
                router.push({ name: "RawApplication", query: { id: row.orderId, flag: 1 } });
            }
        };
        // 样品编号
        const handleSampleDetail = (row) => {
            router.push({ name: "RawSampleDetail", query: { orderId: row.orderId, sampleId: row.sampleId } });
        };
        // 报告编号
        const handleReportDetail = (row) => {
            router.push({
                name: "RawProductTestReport",
                query: { reportId: row.reportId, sampleId: row.sampleId, reportStage: 6 },
            });
        };

        const onCloseBatchInStorageDialog = () => {
            state.batchInStorageDialogVisible = false;
            if (state.batchInStorageResult.successTotal > 0) {
                getList();
            }
        };

        // 入库
        const handleWarehouse = async (rowArray, isUpdateInputWarehouseStatus = false) => {
            state.isUpdateInputWarehouseStatus = isUpdateInputWarehouseStatus;
            if (rowArray.some((x) => x.thirdDataReturnStatus === thirdDataReturnStatusConst.inStorage)) {
                ElMessage.warning("已入库的批次，不能重复入库，请重新选择");
                return;
            }
            state.batchWareHouseDialogVisible = true;
            state.wareHouseDate = formatDate(new Date());
        };

        const onCloseBatchWareHouseDialog = () => {
            state.batchWareHouseDialogVisible = false;
        };

        const handleUpdatePrintStatus = async (rowArray) => {
            const confirmRes = await ElMessageBox.confirm("打印状态更新成功后，将无法撤回，是否继续？", "确认更新打印合格证状态", {
                distinguishCancelAndClose: true,
                confirmButtonText: "确认",
                cancelButtonText: "取消",
            }).catch(() => "cancel");
            if (confirmRes === "cancel") {
                return;
            }
            await updatePrintStatus(rowArray.map((x) => x.id));
            ElMessage.success("打印状态更新成功");
            state.listLoading = true;
            getList();
        };

        /** 更新入库状态 */
        const handleUpdateInputWarehouseStatus = async (rowArray) => {
            if (rowArray.some((x) => x.thirdDataReturnStatus === thirdDataReturnStatusConst.inStorage)) {
                ElMessage.warning("已入库的批次，不能重复入库，请重新选择");
                return;
            }
            await updateInputWarehouseStatus({
                certificatePrintIdList: rowArray.map((x) => x.id),
                wareHouseDate: formatDate(new Date()),
            }).finally(() => {
                state.listLoading = false;
                state.batchWareHouseDialogVisible = false;
            });
            ElMessage.success("入库成功");
            state.listLoading = true;
            getList();
        };

        const onConfirmBatchWareHouse = async (rowArray) => {
            if (!state.wareHouseDate) {
                ElMessage.warning("请选择入库日期");
                return;
            }

            if (state.isUpdateInputWarehouseStatus) {
                await handleUpdateInputWarehouseStatus(rowArray);
                return;
            }

            const { data } = await rawDataReturn({
                list: rowArray.map((item) => ({ ...item, wareHouseDate: state.wareHouseDate })),
            }).finally(() => {
                state.listLoading = false;
                state.batchWareHouseDialogVisible = false;
            });
            if (data.code === 200) {
                const { errorTotal } = data.data;
                if (errorTotal > 0) {
                    state.batchInStorageDialogVisible = true;
                    state.batchInStorageResult = data.data;
                    return;
                }
                ElMessage.success("入库成功");
                state.listLoading = true;
                getList();
            }
        };

        onMounted(() => {
            getDictionaryList();
            getMaterialcategory();
        });
        return {
            ...toRefs(state),
            handlePrint,
            handleSampleDetail,
            iaDetail,
            handleReportDetail,
            closeDialogPrint,
            handleChangeRadio,
            closeDialogEdit,
            handleChangeRow,
            closeDialog,
            handleEdit,
            handleSave,
            handleCancle,
            handleWarehouse,
            getSingleText,
            getParamList,
            handleRowClick,
            getPermissionBtn,
            drageHeader,
            getNameByid,
            handleSizeChange,
            getNamesByid,
            handleAudit,
            handleDetermin,
            reset,
            handleSelectionChange,
            formatDate,
            getList,
            tableKey,
            tableKeyLeft,
            colWidth,
            onUpdateColumns,
            fieldTypesEnum,
            columnFixedTypesEnum,
            thirdDataReturnStatusConst,
            onCloseBatchInStorageDialog,
            onCloseBatchWareHouseDialog,
            onConfirmBatchWareHouse,
            handleUpdateInputWarehouseStatus,
            handleUpdatePrintStatus,
        };
    },
};
</script>
<style lang="scss" scoped>
.btn-mg20 {
    margin-right: 20px;
}

.margin-right {
    margin-right: 4px;
}
.fr {
    float: right;
}

:deep(.el-radio.is-bordered + .el-radio.is-bordered) {
    margin-left: 0;
}
</style>
