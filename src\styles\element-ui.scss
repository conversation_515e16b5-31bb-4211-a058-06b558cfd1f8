// cover some element-ui styles
.el-breadcrumb {
  font-size: 12px;
}
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
  color: #606266;
  font-style: normal;
  font-weight: normal;
}

.el-button {
  font-family: 'MiSans';
}

.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 4px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    min-width: 60px;
  }
}

.el-button--default {
  color: #303133;

  &.el-button--primary {
    color: #ffffff;
  }
}

.el-button--small:not(.is-circle) {
  padding: 7px 15px !important;
  font-size: 14px !important;
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 4px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// lot-content dialog height styles
.lot-dialog.el-dialog {
  .el-dialog__body {
    max-height: calc(100vh - 222px);
    overflow-y: auto;
  }
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

// input -small styles
.el-input--small {
  font-size: 14px !important;
}
.el-textarea .el-input__count {
  color: #c0c4cc;
}

// drawer 抽屉样式
.el-drawer {
  .el-drawer__header {
    padding-left: 20px;
    margin-bottom: 0;

    span {
      display: inline-block;
      text-align: left;
      font-size: 18px;
      line-height: 26px;
      color: #303133;
    }
  }

  .el-drawer__body {
    padding: 33px 40px 20px 40px;
    // max-height: calc(100vh - 120px);
    overflow-y: auto;

    .el-form--inline .el-form-item {
      display: block;
      text-align: left;
    }
    .full-item {
      .el-form-item__content {
        width: 100%;
      }
    }
  }

  .el-descriptions {
    margin-bottom: 20px;
    // 抽屉中的描述列表
    .el-descriptions__body {
      filter: drop-shadow(0px 0px 12px rgba(0, 0, 0, 0.12));
      -webkit-filter: drop-shadow(0px 0px 12px rgba(0, 0, 0, 0.12));

      .el-descriptions__label {
        background: #f5f7fa;
        color: #606266;
        width: 110px;
        font-weight: normal;
      }
    }
  }

  .drawer-footer,
  .drawer-fotter {
    // position: absolute;
    // bottom: 0;
    display: flex;
    width: 100%;
    background: $background-color;
    padding: 10px 0 20px;
    z-index: 999;
  }
}

.search-collapse {
  .el-collapse-item {
    :deep(.el-collapse-item__wrap) {
      background: none;
      border: none;
    }
    :deep(.el-collapse-item__content) {
      margin-bottom: 20px;
      padding: 20px 20px 10px;
      background: #ffffff;
    }
    :deep(.el-collapse-item__header) {
      display: none;
    }
  }
  :deep(.el-form-item__content) {
    text-align: left;
  }
  :deep(.el-form-item__label) {
    line-height: 36px;
  }
  :deep(.el-form-item__content) {
    margin-bottom: 18px;
  }
  :deep(.el-radio__input) {
    display: none;
  }
  :deep(.el-radio-button--mini .el-radio-button__inner) {
    padding-left: 10px;
  }
  .el-radio-group {
    // margin-top: 7px;
    :deep(.el-radio-button.is-active) {
      .el-radio-button__inner {
        color: $tes-primary;
        box-shadow: none;
      }
    }
    .el-radio-button {
      &:first-child {
        background: none;
      }
    }
    :deep(.el-radio-button__inner) {
      border: 0;
      font-size: 14px;
      background: none;
      padding: 5px;
    }
    .date-radio {
      display: inline-block;
      margin-left: 10px;
    }
    :deep(.label-type) {
      margin-right: 4px;
    }
  }
}

.el-dialog {
  .el-dialog__header {
    height: 56px;

    .el-dialog__title {
      float: left;
    }
  }
  .el-dialog__body {
    padding: 5px 20px 10px 20px;
  }
  // 悬浮表格上显示滚动条
}
.el-dialog:hover {
  ::-webkit-scrollbar-thumb {
    background: #d4d7de;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* Track */
::-webkit-scrollbar-track {
  -webkit-border-radius: 8px;
  border-radius: 8px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  -webkit-border-radius: 8px;
  border-radius: 8px;
}

::-webkit-scrollbar-thumb:hover {
  background: #cdd0d6;
}

.el-form {
  .el-form-item {
    margin-bottom: 18px;
    .el-form-item__label {
      font-weight: normal;
      font-size: 14px;
      color: #606266;
      padding: 0px;
    }

    .ipt-360 {
      width: 360px;
    }
  }
  &.el-form-small {
    .el-form-item {
      margin-bottom: 18px;
    }
  }

  // 表单中input高度 默认32px
  &.default-form {
    :deep(.el-input--medium .el-input__inner) {
      height: 32px;
      line-height: 32px;
    }
  }
}

// message styles
.el-message.el-message--success,
.el-message.el-message--warning,
.el-message.el-message--error,
.el-message.el-message--info {
  background: #fff;
  border: none;
  box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
}

.el-dialog {
  .el-dialog__body {
    text-align: left;

    .el-form-item__label {
      font-weight: normal;
      font-size: 14px;
      color: #606266;
      padding: 0px;
    }
  }
}

.el-dialog.double-dialog {
  width: 875px !important;
}

.el-dialog.normal-dialog {
  width: 720px !important;
}

.el-dialog.small-dialog {
  width: 480px !important;
}

.el-dialog.double-dialog,
.el-dialog.normal-dialog,
.el-dialog.small-dialog {
  .el-dialog__header {
    display: flex;
    align-items: center;

    .el-dialog__headerbtn {
      top: auto;
    }
  }

  .el-dialog__body {
    max-height: calc(100vh - 200px);
    overflow-y: auto;

    .el-form-item__label {
      color: #303133;
      line-height: 32px;
    }

    // form-item required point position
    // .el-form--label-top {
    //   .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
    //     content: "";
    //     color: transparent;
    //     margin-right: 0;
    //   }
    //   .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:after {
    //     content: "*";
    //     color: #f56c6c;
    //     margin-left: 4px;
    //   }
    // }
  }

  .el-dialog__footer {
    .el-button {
      padding: 5px 16px;
      font-size: 14px;
    }
  }
}

.el-dialog.tiny-dialog {
  .el-dialog__body {
    padding: 20px 40px;
  }
}

.el-dialog.ample-dialog {
  .el-dialog__body {
    padding: 30px 20px 48px;
  }
}

.el-tree {
  padding: 0;

  .el-tree-node__content {
    height: 28px;
    line-height: 28px;

    span {
      font-weight: normal;
      font-size: 14px;
      // color: #606266;
    }
    .el-tree-node__expand-icon {
      font-size: 12px;
    }

    .el-icon {
      display: none;
    }

    .icon-show {
      display: inline-block;
      background: #dcdfe6;
      border-radius: 2px;
    }

    .icon-hide {
      display: none;
    }
  }

  .el-tree-node__content:hover .el-icon {
    display: inline-block;
  }

  .el-tree-node__content:hover .el-icon:hover {
    display: inline-block;
    background: #dcdfe6;
    border-radius: 2px;
  }

  .el-tree-node:focus > .el-tree-node__content {
    background-color: #ebf4fc !important;
  }
}

.el-switch.inner-switch {
  width: 80px;

  .el-switch__core {
    min-width: 54px;
  }

  .el-switch__label {
    margin-left: -35px;
    z-index: 1;
    color: #ffffff;
    line-height: 20px;
  }

  .el-switch__label.is-active {
    margin-left: -48px;
    z-index: 1;
    color: #ffffff;
    line-height: 20px;
  }
}

.blue-color {
  color: $tes-primary;
  cursor: pointer;
  margin-right: 12px;
}

.red-color {
  color: #f56c6c;
  cursor: pointer;
  margin-right: 12px;
}

.green-color {
  color: #67c23a;
  cursor: pointer;
  margin-right: 12px;
}

.orange-color {
  color: #e6a23c;
  cursor: pointer;
  margin-right: 12px;
}

.blue {
  color: $tes-primary;
  cursor: pointer;
}

// 标题中需要用到的line样式
.title-line {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 10px;
  background: $tes-primary;
}

.text-label {
  display: inline-block;
  color: $tes-font1;
}

//表格里状态的点
.base-table {
  .icon-tes-info:before,
  .icon-tes-success:before,
  .icon-tes-wait:before,
  .icon-tes-error:before,
  .icon-tes-blue:before,
  .icon-tes-green:before {
    display: inline-block;
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 4px;
    background: #e6a23c;
    margin-right: 8px;
    margin-bottom: 1px;
  }

  .icon-tes-success:before {
    background: #67c23a;
  }

  .icon-tes-wait:before {
    background: #909399;
  }

  .icon-tes-error:before {
    background: #f56c6c;
  }

  .icon-tes-blue:before {
    background: #409eff;
  }

  .icon-tes-green:before {
    background: #00b38a;
  }
}

// 复、源、退、变、外 icon样式
.custom-icon {
  margin-right: 8px;
  padding: 0 5px;
  border-radius: 3px;
  color: #fff;
  font-size: 12px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  &.text-copy {
    background: $tes-red;
  }
  &.text-origin {
    background: $tes-blue;
  }
  &.text-change {
    background: $tes-primary;
  }
  &.text-back {
    background: $tes-yellow;
  }
  &.text-info {
    background: $tes-grey;
  }
  &.text-green {
    background: $tes-green;
  }
}

// readio/checkbox is-bordered 样式重置
.el-radio.is-bordered.is-checked:not(.is-disabled),
.el-checkbox.is-bordered.is-checked:not(.is-disabled) {
  background: $tes-primary2;
  border-color: $tes-primary1 !important;
}

.radio-content {
  overflow: hidden;
  width: 100%;
  text-align: left;
  margin: 1rem 0;

  .el-radio-group {
    display: inline-block;
    line-height: 1;
    vertical-align: middle;
    font-size: 0;
  }

  .el-radio-button__original-radio:checked + .el-radio-button__inner {
    background-color: $tes-primary2;
    border-color: $tes-primary3;
    color: $tes-primary;
  }

  .el-radio-button--small .el-radio-button__inner {
    font-size: 14px !important;
    padding: 8px 15px;
  }

  .el-radio-button__original-radio:checked + .el-radio-button__inner {
    box-shadow: -0.067rem 0 0 0 $tes-primary1;
  }

  .el-button--medium {
    padding: 9px 20px;
    font-size: 14px;
  }

  .only-me {
    margin-left: 15px;
    vertical-align: middle;
  }
}

.dialog-style {
  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 0px 0px 20px 0px;

    .dialog-footer {
      padding: 0px 20px;
    }
  }
}

// tabview styles
.el-tabs__nav-wrap::after {
  height: 1px;
}

.main-page {
  padding: 16px 24px;
  box-sizing: border-box;
}

.detail-table {
  thead {
    th {
      background: #f6f6f6;
    }
  }
}

// empty styles
.el-empty {
  padding: 25px 0 40px;
}

.el-popper.is-light.calendar-popover {
  background: #ffffff;
  border: none;
  box-shadow: 0px 16px 48px 16px rgba(0, 0, 0, 0.08), 0px 8px 16px -8px rgba(0, 0, 0, 0.16);
  filter: drop-shadow(0px 12px 32px rgba(0, 0, 0, 0.12));
  -webkit-filter: drop-shadow(0px 12px 32px rgba(0, 0, 0, 0.12));
}
.tes-task-issued {
  font-size: 14px;
}
.el-message-box__errormsg {
  line-height: 18px;
}
.accredit_voice {
  color: $tes-primary;
  margin-left: 10px;
}
.el-select-dropdown__item {
  max-width: 500px;
  text-overflow: clip !important;
  white-space: normal !important;
  height: auto !important;
}

// .el-button {
//   background-color: $background-color;
// }
