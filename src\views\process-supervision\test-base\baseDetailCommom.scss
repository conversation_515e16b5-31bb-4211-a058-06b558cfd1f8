.inline {
  display: inline-block;
  :deep(.el-form-item__content) {
    display: inline-block;
  }
}
.itemFormClass {
  width: 40%;
  display: inline-block;
}
.itemEngFormClass {
  margin-left: 10px;
}
.formAllCheck {
  height: 100%;
  ::v-deep(.el-form-item--medium .el-form-item__content) {
    line-height: 32px;
  }
  ::v-deep(.el-form-item--medium .el-form-item__label) {
    height: 32px;
    line-height: 32px;
  }
  .el-form-item {
    margin-bottom: 0;
  }
  .gjcs {
    overflow-y: auto;
  }
  .code-textarea .el-textarea {
    border: none;
  }
}
.formAll {
  height: 100%;
  :deep(.el-input--medium .el-input__inner) {
    height: 32px;
    line-height: 32px;
  }
  :deep(.el-form-item--medium .el-form-item__label) {
    line-height: 32px;
  }
  :deep(.el-form-item--medium .el-form-item__content) {
    line-height: 32px;
  }
  :deep(.el-input-number--medium) {
    line-height: 32px;
  }
  :deep(.el-input--medium) {
    line-height: 32px;
  }
  :deep(.el-input--medium .el-input__icon) {
    line-height: 32px;
  }
  .itemName {
    margin-bottom: 12px;
  }
}
.itemDetail {
  margin: 0 12px 12px 12px;
  padding: 10px 20px;
  border-radius: 10px;
  background: $background-color;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}
.itemName {
  font-weight: 700;
  font-size: 16px;
  line-height: 40px;
  color: $tes-primary;
  display: inline-block;
}
.itemNameEng {
  margin-left: 10px;
  font-size: 12px;
}
.itemEnName {
  font-size: 12px;
  margin-left: 10px;
}
.itemInfo {
  margin: 10px 15px 10px 20px !important;
}
.formDataClass {
  position: relative;
  background: $background-color;
  border: 2px solid transparent;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.12);
  border-radius: 10px;
  margin: 12px;
  padding: 20px 20px 10px;
  min-width: 500px;
  &:first-child {
    margin-top: 0;
  }
  &:last-child {
    margin-bottom: 0;
  }
  &:last-of-type {
    padding-bottom: 0;
  }
  .top {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    .top-title {
      display: flex;
      align-items: center;
    }
    .tag-group {
      .el-tag {
        margin-left: 10px;
      }
    }
  }

  &:hover {
    border-color: $tes-primary3;
    .xh {
      color: $tes-primary;
      background: $tes-primary2;
      border: 1px solid $tes-tagBorder-weaker;
    }
    .sonItemName {
      color: $menuActiveText;
    }
  }
  .top {
    padding: 0 0 14px 0;
    margin-bottom: 14px;
    border-bottom: 1px solid #dcdfe6;
  }
}
.addKeyParameter {
  background: $background-color;
  margin: 10px 0 0 0;
  height: 35px;
  line-height: 31px;
  border: 2px solid transparent;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  &:hover {
    background-color: $tes-primary2;
    border: 2px solid $tes-primary3;
  }
}
.sonItemName {
  display: inline-block;
  background: $background-color;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #303133;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 300px;
}
.sonItemEngName {
  font-size: 12px;
  margin-left: 10px;
}
.xh {
  display: flex;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid #e9e9eb;
  justify-content: center;
  align-items: center;
  background: #f4f4f5;
  color: $tes-font2;
  margin-right: 12px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
}
.code-textarea {
  margin-top: 10px;
}
