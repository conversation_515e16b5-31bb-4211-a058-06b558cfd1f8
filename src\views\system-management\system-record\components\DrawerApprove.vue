<template>
  <el-drawer
    v-model="showDrawer"
    title="审批流程"
    direction="rtl"
    :before-close="handleClose"
    :size="600"
    destroy-on-close
    custom-class="DrawerSnapshot"
  >
    <DrawerLayout v-loading="drawerLoading" :has-button-group="false" :has-page-header="false">
      <el-timeline v-if="processList.length" class="text-left pl-1 form-height-auto">
        <el-timeline-item
          v-for="(item, index) in processList"
          :key="item.processInstanceId"
          :color="item.endTime ? (item.isAssent == 1 || item.name == '提交流程' ? '#67C23A' : '#F56C6C') : ''"
        >
          <div class="flex justify-between">
            <span>{{ item.name }}</span>
            <span class="end-time">{{ item.endTime }}</span>
          </div>
          <span class="title">{{ item.assignee ? getNameByid(item.assignee) : '--' }}</span>
          <span v-if="item.endTime && item.name != '提交流程' && item.name != '重新提交'" class="title"
            >（{{ item.isAssent == 1 ? '同意' : '拒绝' }}）</span
          >
          <span v-else-if="item.name != '提交流程' && item.name != '重新提交'" class="title"
            >（{{ item.name }}中）</span
          >
          <div v-if="item.opinion" class="option-content">{{ item.opinion }}</div>
          <div v-if="isEdit && index == processList.length - 1" :model="formData">
            <div class="flex items-center">
              <span>结论：</span>
              <el-radio-group v-model="formData.isAssent" class="radioGroup">
                <el-radio :label="1">同意</el-radio>
                <el-radio :label="0">拒绝</el-radio>
              </el-radio-group>
            </div>
            <el-input v-model="formData.opinion" type="textarea" :rows="3" maxlength="200" />
          </div>
        </el-timeline-item>
      </el-timeline>
      <!-- 暂无数据 -->
      <div v-else class="form-height-auto">
        <img src="@/assets/img/empty-img.png" alt="" />
      </div>
      <div class="drawer-fotter">
        <el-button :loading="drawerLoading" @click="handleClose">关闭</el-button>
        <el-button v-if="isEdit" type="primary" :loading="drawerLoading" @click="handleSave">保存</el-button>
      </div>
    </DrawerLayout>
  </el-drawer>
</template>
<script>
// getCurrentInstance
import { reactive, toRefs, watch } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import DrawerLayout from '@/components/DrawerLayout';
import { colWidth } from '@/data/tableStyle';
import { ElMessage, ElMessageBox } from 'element-plus';
// Api
import { systemProcessHistory, processExecute } from '@/api/systemRecord';
import { getNameByid } from '@/utils/common';
export default {
  name: 'DrawerApprove',
  components: { DrawerLayout },
  props: {
    drawerVisible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    detailInfo: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDrawer'],
  setup(props, context) {
    const state = reactive({
      drawerType: '', // 弹出窗类型
      currentAccountId: getLoginInfo().accountId,
      drawerLoading: false, // loading
      formData: {}, // 表单数据
      processList: [],
      showDrawer: false,
      isEdit: false // 是否可以编辑
    });
    watch(props, newValue => {
      state.showDrawer = newValue.drawerVisible;
      if (state.showDrawer) {
        state.drawerType = props.type;
        state.formData = {
          isAssent: 1,
          businessKey: props.detailInfo.id,
          processInstanceId: props.detailInfo.processInstanceId
        };
        state.processList = [];
        getProcessHistory();
        if (props.type == '1') {
          // 审批
        } else {
          // 进度
        }
      }
    });
    const getProcessHistory = async () => {
      state.drawerLoading = true;
      const response = await systemProcessHistory(props.detailInfo.processInstanceId).finally(
        (state.drawerLoading = false)
      );
      if (response) {
        state.processList = response.data.data;
        if (
          props.type == '1' &&
          !state.processList[state.processList.length - 1].endTime &&
          state.processList[state.processList.length - 1].assignee == state.currentAccountId
        ) {
          state.isEdit = true;
        } else {
          state.isEdit = false;
        }
      }
    };
    const handleSave = () => {
      ElMessageBox({
        title: '审批',
        message: '是否确认审批？',
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(async () => {
          state.drawerLoading = true;
          const response = await processExecute(state.formData).finally((state.drawerLoading = false));
          if (response) {
            ElMessage.success('审批成功！');
            state.showDrawer = false;
            context.emit('closeDrawer', true);
          }
        })
        .catch(() => {});
    };
    // 关闭
    const handleClose = () => {
      context.emit('closeDrawer');
    };
    return {
      ...toRefs(state),
      getNameByid,
      handleSave,
      handleClose,
      getProcessHistory,
      formatDate,
      colWidth,
      drageHeader
    };
  }
};
</script>
<style lang="scss" scoped>
.title,
.end-time {
  color: #999;
  font-size: 12px;
}
.option-content {
  color: #999;
  background-color: #f6f6f6;
  padding: 5px 10px;
  margin-top: 5px;
  border-radius: 3px;
}
</style>
