<template>
  <!-- 试验方案列表、检测分配列表 -->
  <ListLayout>
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="sample-order-form" @submit.prevent>
        <el-form-item prop="param">
          <div style="width: 38vw">
            <CombinationQuery
              :field-list="tableColumns"
              :field-tip="fieldTips"
              @get-single-text="getSingleText"
              @get-param-list="getParamList"
              @reset-search="reset"
            />
          </div>
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button size="large" type="text" class="searchBtn" @click="search" @keyup.prevent @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']" />
          </el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <div class="btn-group">
        <el-button
          v-if="getPermissionBtn('CopySample')"
          type="primary"
          size="large"
          icon="el-icon-document-copy"
          :disabled="multipleSelection.length === 0"
          @click="handleCopySample()"
          >复制样品</el-button
        >
        <el-button
          v-if="radioData === '待同步' && getPermissionBtn('batchJudgement')"
          type="primary"
          size="large"
          icon="el-icon-refresh"
          :disabled="multipleSelection.length === 0"
          @click="batchJudgement()"
          >批量判定</el-button
        >
        <el-button
          v-if="radioData === '待同步' && getPermissionBtn('ResultReturn')"
          type="primary"
          size="large"
          icon="el-icon-refresh"
          :disabled="multipleSelection.length === 0"
          @click="resultBack()"
          >结论回传</el-button
        >
        <el-button
          v-if="showBatchFinish && getPermissionBtn('batchFinish')"
          type="primary"
          size="large"
          icon="el-icon-refresh"
          :disabled="multipleSelection.length === 0"
          @click="batchFinish()"
          >批量完成</el-button
        >
        <el-button
          v-if="radioData === '待认领' && getPermissionBtn('allocationBatchClaim')"
          type="primary"
          size="large"
          icon="el-icon-document"
          :disabled="multipleSelection.length === 0"
          @click="multipleGet()"
          >批量认领</el-button
        >
        <el-button
          v-if="getPermissionBtn('allocationExport')"
          type="primary"
          size="large"
          :loading="listLoading"
          @click="exportExcel()"
          ><span class="iconfont tes-task-issued" @keyup.prevent @keydown.enter.prevent />导出</el-button
        >
        <el-button
          v-if="getPermissionBtn('allocationExport')"
          type="primary"
          size="large"
          :loading="listLoading"
          @click="handleImport()"
          ><span class="el-icon-upload" @keyup.prevent @keydown.enter.prevent />导入</el-button
        >
      </div>
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" :model="searchForm" label-width="110px" label-position="right">
            <!-- <el-form-item label="物资分类：">
              <el-radio-group v-model="searchForm.prodType" max="1">
                <el-radio-button style="margin-right: 4px" @change="changeProdType('all')">不限</el-radio-button>
                <el-radio-button
                  v-for="item in types"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                  @change="changeProdType(item)"
                />
              </el-radio-group>
            </el-form-item> -->
            <el-form-item :label="tenantInfo.type === 0 ? '来样日期：' : '登记日期：'">
              <el-date-picker
                v-model="searchForm.rukuDateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="changeRuku"
              />
            </el-form-item>
            <el-form-item label="下达日期：">
              <el-date-picker
                v-model="searchForm.xiadaDateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="changeXaida"
              />
            </el-form-item>
            <el-form-item label="试验负责人：" class="seal-staff-name">
              <el-select
                v-model="formInline.ownerId"
                class="owner-select"
                placeholder="请选择"
                clearable
                filterable
                size="small"
                :filter-method="filterUserList"
                @focus="filterUserList(null)"
              >
                <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="来源：" class="seal-staff-name">
              <el-select
                v-model="formInline.thirdType"
                class="owner-select"
                placeholder="请选择"
                clearable
                size="small"
              >
                <el-option v-for="item in thirdTypeOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="检测结果：" class="seal-staff-name">
              <el-select
                v-model="formInline.reportType"
                class="owner-select"
                placeholder="请选择"
                clearable
                size="small"
              >
                <el-option label="合格" value="0" />
                <el-option label="不合格" value="1" />
                <el-option label="不判定" value="2" />
                <el-option label="空" value="3" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="23" class="flex gap-3">
          <el-radio-group v-model="radioData" size="small" @change="changeRadio">
            <el-radio-button label="全部" />
            <el-radio-button label="待认领">
              待认领<span v-if="tableNumber.unClaimCount" class="listNumber">{{
                '（' + tableNumber.unClaimCount + '）'
              }}</span>
            </el-radio-button>
            <el-radio-button label="待分配">
              待分配
              <span v-if="tableNumber.unAssignmentCount" class="listNumber">{{
                '（' + tableNumber.unAssignmentCount + '）'
              }}</span>
            </el-radio-button>
            <el-radio-button
              label="已分配"
              :class="{
                isHaveNew: isHaveNew
              }"
            >
              已分配
              <Digitalscroll
                :start="Number(oldTableNumber.assignmentCount)"
                :end="Number(tableNumber.assignmentCount)"
              />
            </el-radio-button>
            <el-radio-button v-if="getPermissionBtn('overdueUndistributed')" label="超期待分配">
              超期待分配<span v-if="tableNumber.extendedCount" class="listNumber">{{
                '（' + tableNumber.extendedCount + '）'
              }}</span>
            </el-radio-button>
            <el-radio-button label="已完成" />
            <el-radio-button v-if="getPermissionBtn('ResultReturn')" label="待同步">
              待同步<span v-if="tableNumber.unDataReturnCount" class="listNumber">{{
                '（' + tableNumber.unDataReturnCount + '）'
              }}</span>
            </el-radio-button>
            <el-radio-button v-if="getPermissionBtn('ResultReturn')" label="已同步" />
            <el-radio-button v-if="getPermissionBtn('allocationCancelBtn')" label="已作废" />
          </el-radio-group>
          <el-checkbox v-model="giveMeChecked" @change="giveMeChange">仅看我的</el-checkbox>
          <el-select
            v-if="getPermissionBtn('inspectionType')"
            v-model="formInline.type"
            placeholder="请选择检验类型"
            class="w-[160px]"
            size="small"
            clearable
            filterable
            @change="getList()"
          >
            <el-option v-for="(val, key) in dictionary['JYLX'].enable" :key="key" :label="val" :value="Number(key)" />
          </el-select>
          <el-select
            v-if="getPermissionBtn('registerDepartment')"
            v-model="formInline.registerDepartment"
            class="w-[160px]"
            filterable
            placeholder="请选择送检部门"
            size="small"
            clearable
            @change="getList()"
          >
            <el-option v-for="(val, key) in dictionary['JHDW']?.enable" :key="key" :label="val" :value="val" />
          </el-select>
        </el-col>
        <el-col :span="1" style="text-align: right">
          <TableColumnView binding-menu="test-allocation" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table allocation-table base-table format-height-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      @header-dragend="drageHeader"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column
        type="selection"
        :width="colWidth.checkbox"
        align="center"
        fixed="left"
        :selectable="selectable"
      />

      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template v-if="item.fieldKey === 'schedule'" #header>
            <span
              >完成情况
              <el-tooltip content="待提交/待审核/已通过" placement="top" effect="light">
                <i class="iconfont tes-title" />
              </el-tooltip>
            </span>
          </template>
          <template #default="{ row }">
            <template v-if="item.fieldType === fieldTypesEnum.Link">
              <template v-if="item.fieldKey === 'secSampleNum'">
                <span class="little-tag">
                  <span v-if="row.isUrgent" class="urgent">急</span>
                  <span v-if="row.isSpecial === '1'" class="special">特</span>
                </span>
                <span
                  v-if="row.secSampleNum"
                  v-copy="row.secSampleNum"
                  class="blue-color"
                  @click="handleSampleOrdersDetail(row)"
                >
                  {{ row.secSampleNum }}</span
                >
                <span v-else> {{ '--' }} </span>
              </template>
              <span
                v-else-if="item.fieldKey === 'presentationCode' && (row.presentationCode || row.no)"
                v-copy="row.presentationCode || row.no"
                class="nowrap blue-color"
                @click.stop="jumpApplicationDetail(row)"
              >
                {{ row.presentationCode || row.no || '--' }}
              </span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Person">
              <UserTag :name="getNameByid(row[item.fieldKey]) || '--'" />
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Status">
              <el-tag
                v-if="item.fieldKey === 'status'"
                size="small"
                effect="dark"
                :type="row.status === 2 || row.status === 4 ? 'success' : 'warning'"
              >
                {{ filterStatus(row.status) }}
              </el-tag>
              <el-tag
                v-else-if="item.fieldKey === 'overdue'"
                size="small"
                effect="dark"
                :type="row.overdue ? 'danger' : 'success'"
                >{{ row.overdue ? '超期' : '正常' }}</el-tag
              >
              <span v-else-if="item.fieldKey === 'reportType'"
                >{{
                  row.reportType.toString() === '0'
                    ? '合格'
                    : row.reportType.toString() === '1'
                    ? '不合格'
                    : row.reportType.toString() === '2'
                    ? '不判定'
                    : '--'
                }}{{ row.reportType.toString() === '1' ? '(' + row.disposalType + ')' + row.disposalNo : '' }}</span
              >
              <span v-else-if="item.fieldKey === 'thirdSyncStatus'">{{
                row.thirdSyncStatus === 0 ? '待同步' : row.thirdSyncStatus === 1 ? '已同步' : '--'
              }}</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Date">
              <template v-if="item.fieldKey === 'startDate,finishedDate'">
                <span>{{ formatDateTime(row.startDate) || '--' }}</span>
                ~
                <span>{{ formatDateTime(row.finishedDate) || '--' }}</span>
              </template>
              <span v-else>{{ row[item.fieldKey] ? formatDateTime(row[item.fieldKey]) : '--' }}</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Custom">
              <span v-if="item.fieldKey === 'thirdType'">{{
                row.thirdType === 0 ? 'ERP' : row.thirdType === 1 ? 'MES' : '自建'
              }}</span>
              <span v-if="item.fieldKey === 'hth'">{{ row.salesOrderNo || row.purchaseNo || '--' }}</span>
              <span v-else-if="item.fieldKey === 'type'">{{ dictionary['JYLX'].all[row[item.fieldKey]] }}</span>
              <!--检验对象 -->
              <!--工厂 1，8 原材料属性，其余生产 -->
              <div v-else-if="item.fieldKey === 'inputWarehouseNo,productionOrderNo'" class="nowrap">
                {{ [1, 8].includes(row.type) ? row.inputWarehouseNo || '--' : row.productionOrderNo || '--' }}
              </div>
              <!--对象位置 -->
              <template v-else-if="item.fieldKey === 'productionProcedure,productionStation,wareHouseName'">
                <div v-if="[1, 8].includes(row.type)" class="nowrap">
                  {{ row.wareHouseName }}
                </div>
                <div v-else class="nowrap">{{ row.productionProcedure }}{{ row.productionStation }}</div>
              </template>
              <!--对象名称 -->
              <template v-else-if="item.fieldKey === 'customerName,supplierName'">
                <div v-if="[1, 8].includes(row.type)" class="nowrap">{{ row.supplierName }}</div>
                <div v-else class="nowrap">{{ row.customerName }}</div>
              </template>
              <template v-else-if="item.fieldKey === 'sampleNum'">
                <div v-if="row.sampleNum" class="nowrap">
                  {{ row.sampleNum }}{{ filterSampleUnitToName(row.sampleUnit) || row.unitName }}
                </div>
                <div v-else class="nowrap">--</div>
              </template>
            </template>
            <template v-else-if="item.fieldKey === 'reportNo'">
              <div
                v-if="row.reportNo"
                v-copy="row.reportNo"
                class="blue-color nowrap"
                @click="onRedirectReportDetail(row)"
              >
                {{ row.reportNo }}
              </div>
              <div v-else class="nowrap">--</div>
            </template>
            <span v-else>{{ row[item.fieldKey] || '--' }}</span>
          </template>
        </el-table-column>
      </template>
      <el-table-column
        label="操作"
        :width="colWidth.operationMultiple"
        prop="caozuo"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="scope">
          <div v-if="formInline.isInvalidated === '1'">
            <span
              v-if="getPermissionBtn('allocationDetail')"
              class="blue-color"
              @click.stop="handleDetail(scope.row, true)"
              >查看</span
            >
            <span
              v-if="getPermissionBtn('allocationCancelBtn')"
              class="blue-color"
              @click.stop="handleRestore(scope.row)"
              >还原</span
            >
          </div>
          <div v-else>
            <span
              v-if="getPermissionBtn('allocationBtn') && scope.row.ownerId === accountId"
              class="blue-color"
              @click.stop="handleDetail(scope.row)"
              >分配</span
            >
            <span
              v-if="getPermissionBtn('allocationDetail')"
              class="blue-color"
              @click.stop="handleDetail(scope.row, true)"
              >查看</span
            >
            <span
              v-if="getPermissionBtn('allocationClaim') && scope.row.status === 0"
              class="blue-color"
              @click.stop="handleClaim(scope.row)"
              >认领</span
            >
            <span
              v-if="getPermissionBtn('allocationCancelBtn')"
              class="blue-color"
              @click.stop="handleCancel(scope.row)"
              >作废</span
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row>
      <el-col :span="10" style="text-align: left; padding-top: 10px">
        <el-button type="primary" size="small" :disabled="multipleSelection.length === 0" @click="handleUrgent()"
          >加急</el-button
        >
        <el-button size="small" :disabled="multipleSelection.length === 0" @click="handleUrgent('cancle')"
          >取消加急</el-button
        >
        <template v-if="getPermissionBtn('specialSample')">
          <el-button type="primary" size="small" :disabled="multipleSelection.length === 0" @click="handleSpecialSample"
            >特殊送样</el-button
          >
          <el-button size="small" :disabled="multipleSelection.length === 0" @click="handleCancelSpecialSample"
            >取消送样</el-button
          >
        </template>
      </el-col>
      <el-col :span="14">
        <pagination
          v-show="total > 0"
          :page="listQuery.page"
          :limit="listQuery.limit"
          :total="total"
          @pagination="getList"
        />
      </el-col>
    </el-row>
    <template #other>
      <!-- 认领、批量认领 -->
      <claim
        :is-show="claimDialogVisible"
        :samples-info="samplesInfo"
        :mange-list="mangeList"
        :multiple-selection="multipleSelection"
        @setInfo="getClaimSInfo"
        @close="closeClaimModel()"
      />
      <BatchJudgement
        :dialog-visible="showJudgementDialog"
        @close-dialog="closeJudgementDialog"
        @save-judgement="saveJudgement"
      />
      <el-dialog v-model="showFinishDialog" title="确认完成" width="500px" :close-on-click-modal="false">
        <CustomPanel
          :has-margin-bottom="true"
          :panel-margin-width="0.8"
          :has-panel-header="false"
          panel-min-height="80px"
          class="tip-panel"
          style="background-color: rgb(237, 250, 255)"
        >
          <div class="tip-message">
            <el-row>
              <el-col :span="2" style="text-align: center"><i class="el-icon-warning info-icon" style="" /></el-col>
              <el-col :span="22"><span style="line-height: 2.571429rem; font-size: 1.2rem">提示信息</span></el-col>
            </el-row>
            <el-row>
              <el-col :span="2" />
              <el-col :span="22">
                <p class="tip-text">完成检测后，样品下的所有已分配项目将自动结束。</p>
                <p class="tip-text">如已经开始试验，则不允许结束检测。</p>
                <p class="tip-text">是否确认继续完成样品检测？</p>
              </el-col>
            </el-row>
          </div>
        </CustomPanel>
        <template #footer>
          <el-button @click="cancelFinishDialog">取消</el-button>
          <el-button type="primary" @click="submitBatchFinish">确定</el-button>
        </template>
      </el-dialog>
      <el-dialog v-model="showUnFinishedDialog" title="未完成检测样品" width="500px" :close-on-click-modal="false">
        <CustomPanel
          :has-margin-bottom="true"
          :panel-margin-width="0.8"
          :has-panel-header="false"
          panel-min-height="80px"
          class="tip-panel"
          style="background-color: rgb(237, 250, 255)"
        >
          <div class="tip-message">
            <el-row>
              <el-col :span="2" style="text-align: center"><i class="el-icon-warning warning-icon" style="" /></el-col>
              <el-col :span="22"
                ><span style="line-height: 2.571429rem; font-size: 0.9rem"
                  >以下样品因已有检测数据或报告数据不能完成检测</span
                ></el-col
              >
            </el-row>
            <el-row>
              <el-col :span="2" />
              <el-col :span="22">
                <el-tag v-for="sample in unFinishedSampleList" :key="sample" type="info" effect="dark">
                  {{ sample }}
                </el-tag>
              </el-col>
            </el-row>
          </div>
        </CustomPanel>
        <template #footer>
          <el-button type="primary" @click="submitUnfinishTip">确定</el-button>
        </template>
      </el-dialog>
      <DialogImportAllocation :dialog-visible="dialogImport" @close-dialog="closeDialogImport" />
      <!-- 复制样品 -->
      <DialogCopySample
        :dictionary="dictionary"
        :sample-id="multipleSelection[0]?.sampleId"
        :dialog-visible="dialogCopySample"
        @close-dialog="closeDialogCopy"
      />
      <!-- 手动入库 -->
      <module-storage
        module-title="批量入库"
        :visible="dialogStorageVisible"
        :lists="fastInbound[1]"
        @close="closeStorage"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance, inject, computed } from 'vue';
import router from '@/router/index.js';
import { useRoute } from 'vue-router';
import ListLayout from '@/components/ListLayout';
import Pagination from '@/components/Pagination';
import UserTag from '@/components/UserTag';
import {
  getDistributionList,
  claimSample,
  cancelSample,
  restoreSample,
  getNumberApi,
  unUrgentApi,
  urgentApi,
  specialSampleApi,
  cancelSpecialSampleApi
} from '@/api/allocation';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate, formatDateTime } from '@/utils/formatTime';
import { mapGetters, useStore } from 'vuex';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import _ from 'lodash';
import { checkPermissionList } from '@/api/permission';
import { permissionTypeList } from '@/utils/permissionList';
import Claim from './components/claim';
import { thirdReportReturn } from '@/api/inspection-application';
import { filterSampleUnitToName } from '@/utils/formatJson';
import { colWidth } from '@/data/tableStyle';
import { getInspectionList } from '@/api/inspection-application';
import { getTaskRegistrationList } from '@/api/task-registration';
import BatchJudgement from './components/BatchJudgement';
import { saveBatchJudgement, saveBatchFinish } from '@/api/order';
import Digitalscroll from '@/components/DigitalScroll';
// import { getStrategy } from '@/api/strategy'
import CustomPanel from '@/components/PageComponents/CustomPanel.vue';
import CombinationQuery from '@/components/CombinationQuery';
import TableColumnView from '@/components/TableColumnView';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';
import ModuleStorage from '@/views/process-supervision/sample-storage/components/ModuleStorage';
import DialogImportAllocation from './components/dialog-import.vue';
import DialogCopySample from './components/dialog-copy-sample.vue';
import { getDictionary } from '@/api/user';
import { addWarehousings } from '@/api/samplestorage';

export default {
  name: 'TestAllocation',
  components: {
    ListLayout,
    Pagination,
    Claim,
    DialogImportAllocation,
    UserTag,
    BatchJudgement,
    Digitalscroll,
    CustomPanel,
    ModuleStorage,
    DialogCopySample,
    CombinationQuery,
    TableColumnView
  },
  setup() {
    const { proxy } = getCurrentInstance();
    const mittBus = inject('$mittBus');
    const store = useStore().state;
    const route = useRoute();
    const editFrom = ref(null);
    const otherForm = reactive({
      accountId: getLoginInfo().accountId,
      searchFieldList: [],
      fastInbound: { 0: [], 1: [], 2: [] }, // 快速入库类型
      dialogStorageVisible: false, // 手动入库
      fieldTips: '',
      isHaveNew: false,
      dialogCopySample: false,
      tableRef: ref(),
      allList: [],
      giveMeChecked: false,
      dialogImport: false,
      activeName: '0',
      showS: false,
      mangeList: [],
      dictionary: {
        JYLX: {
          enable: {},
          all: {}
        },
        JHDW: {
          enable: {},
          all: {}
        }
      },
      tenantType: store.user.tenantInfo.type,
      formInline: {
        param: '',
        assignedEndTime: '',
        tableQueryParamList: [],
        assignedStartTime: '',
        startTime: '',
        endTime: '',
        ownerId: '',
        registerDepartment: '',
        prodType: '',
        status: '0',
        thirdType: '',
        isInvalidated: '0'
      },
      searchForm: {
        prodType: '',
        rukuDateRange: '',
        xiadaDateRange: ''
      },
      types: store.user.materialList,
      userOptions: store.common.nameList,
      copyUserOptions: store.common.nameList,
      tableNumber: {}, // 各个状态的数据
      oldTableNumber: {}, // 各个状态的数据
      tableColumns: [],
      list: [],
      content: '',
      radioData: '待认领',
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        sort: 'DESC'
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ],
      claimDialogVisible: false,
      samplesInfo: {},
      multipleSelection: [],
      thirdTypeOptions: [
        { id: 0, name: 'ERP' },
        { id: 1, name: 'MES' },
        { id: 2, name: '自建' }
      ],
      showJudgementDialog: false,
      showFinishDialog: false,
      unFinishedSampleList: [],
      showUnFinishedDialog: false
    });

    if (route.query.status) {
      switch (route.query.status) {
        case '1':
          otherForm.radioData = '待分配';
          otherForm.formInline.ownerId = otherForm.accountId;
          otherForm.giveMeChecked = true;
          otherForm.formInline.thirdSyncStatus = '';
          break;
      }
      otherForm.formInline.status = route.query.status;
    }
    const getDictionaryList = () => {
      Object.keys(otherForm.dictionary).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          otherForm.dictionary[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              otherForm.dictionary[item].enable[optionItem.code] = optionItem.name;
            }
            otherForm.dictionary[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    getDictionaryList();
    function onSubmit() {
      getList();
    }
    // 关闭手动入库
    const closeStorage = () => {
      otherForm.dialogStorageVisible = false;
      otherForm.multipleSelection = [];
      getNumber(true);
      getList();
    };
    // const initFieldList = () => {
    //   otherForm.searchFieldList = [
    //     {
    //       field: 'secSampleNum',
    //       name: '样品编号'
    //     },
    //     {
    //       field: 'presentationCode',
    //       name: store.user.tenantInfo.type === 1 ? '申请单号' : '委托编号'
    //     },
    //     {
    //       field: 'mateName',
    //       name: '样品名称'
    //     },
    //     {
    //       field: 'prodType',
    //       name: '型号规格'
    //     },
    //     {
    //       field: 'thirdNo',
    //       name: '第三方单号'
    //     },
    //     {
    //       field: 'inspectionStrategyName',
    //       name: '检验策略'
    //     }
    //   ];
    //   let addArray = [];
    //   if (otherForm.tenantType === 1) {
    //     addArray = [
    //       {
    //         field: 'materialGroup',
    //         name: '物料分组'
    //       },
    //       {
    //         field: 'materialNo',
    //         name: '物料编号'
    //       },
    //       {
    //         field: 'batchNo',
    //         name: '批次'
    //       },
    //       {
    //         field: 'reelNo',
    //         name: '盘号'
    //       }
    //     ];
    //   } else if (otherForm.tenantType === 0) {
    //     addArray = [
    //       {
    //         field: 'mateTypeStr',
    //         name: '物资分类'
    //       }
    //     ];
    //   }
    //   otherForm.searchFieldList = otherForm.searchFieldList.concat(addArray);
    // };
    // initFieldList();
    function reset() {
      // console.log('reset')
      editFrom.value.resetFields();
      otherForm.formInline = {
        param: '',
        tableQueryParamList: [],
        assignedEndTime: '',
        assignedStartTime: '',
        startTime: '',
        endTime: '',
        ownerId: '',
        registerDepartment: '',
        mate_type: '',
        status: '0',
        thirdType: '',
        thirdSyncStatus: '',
        isInvalidated: '0'
      };
      otherForm.radioData = '待认领';
      otherForm.searchForm = {
        prodType: '',
        rukuDateRange: '',
        xiadaDateRange: ''
      };
      otherForm.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        sort: 'DESC'
      };
      getList();
    }

    const search = () => {
      otherForm.showS = !otherForm.showS;
      if (otherForm.activeName === '0') {
        otherForm.activeName = '1';
      } else {
        otherForm.activeName = '0';
      }
    };

    // console.log(store.common.nameList)

    const sortChange = data => {
      const { prop, order } = data;
      // console.log(prop)
      // console.log(order)
      otherForm.listQuery.orderBy = prop;
      if (order === 'ascending') {
        otherForm.listQuery.isAsc = true;
      } else if (order === 'descending') {
        otherForm.listQuery.isAsc = false;
      } else {
        otherForm.listQuery.isAsc = null;
      }
      getList();
    };
    // 导入
    const handleImport = () => {
      otherForm.dialogImport = true;
    };
    // 关闭弹出窗
    const closeDialogImport = val => {
      otherForm.dialogImport = false;
      if (val) {
        getList();
      }
    };
    const closeDialogCopy = val => {
      otherForm.dialogCopySample = false;
      if (val) {
        getList();
      }
    };
    // 导出
    const exportExcel = () => {
      otherForm.listLoading = true;
      getDistributionList({ ...otherForm.formInline, limit: '-1', page: '1' }).then(res => {
        otherForm.listLoading = false;
        if (res) {
          otherForm.allList = [];
          res.data.data.list.forEach(row => {
            // 申请单号' : '委托编号
            row.presentationCode = row.presentationCode || row.no || '';
            // '来样日期' : '登记日期'
            row.createTime = formatDate(row.createTime);
            // 下达日期
            row.assignedTime = formatDate(row.assignedTime);
            // 试验负责人
            row.ownerId = getNameByid(row.ownerId) ? getNameByid(row.ownerId) : row.ownerId;
            // 来源
            row.thirdType = row.thirdType === 0 ? 'ERP' : row.thirdType === 1 ? 'MES' : '自建';
            // 是否超期
            row.overdue = row.overdue ? '超期' : '正常';
            // 合同号
            row.hth = row.salesOrderNo || row.purchaseNo;
            row.type = otherForm.dictionary['JYLX'].all[row.type];
            // 状态
            row.status = filterStatus(row.status);
            // 试验日期
            row['startDate,finishedDate'] = formatDate(row.startDate) + '~' + formatDate(row.finishedDate);
            // 检验对象
            row['inputWarehouseNo,productionOrderNo'] =
              row.type === 1 ? row.inputWarehouseNo || '--' : row.productionOrderNo;
            // 检验结果
            if (row.reportType === '0') {
              row.reportType = '合格';
            } else if (row.reportType == '2') {
              row.reportType = '不判定';
            } else if (row.reportType === '1') {
              row.reportType = '不合格' + '(' + row.disposalType + ')';
            } else {
              row.reportType = row.disposalNo;
            }
            // 对象位置
            row['productionProcedure,productionStation,wareHouseName'] =
              row.type == 1 ? row.wareHouseName : row.productionProcedure + row.productionStation;

            row['customerName,supplierName'] = row.type == 1 ? row.supplierName : row.customerName;

            // 样品数量
            row.sampleNum = row.sampleNum + filterSampleUnitToName(row.sampleUnit) || row.unitName;

            // 同步状态
            row.thirdSyncStatus = row.thirdSyncStatus === 0 ? '待同步' : row.thirdSyncStatus === 1 ? '已同步' : '';
            otherForm.allList.push(row);
          });
          var tHeader = otherForm.tableColumns.map(item => {
            return item.fieldName;
          });
          var filterVal = otherForm.tableColumns.map(item => {
            return item.fieldKey;
          });
          export2Excel(tHeader, filterVal);
        }
      });
    };
    const export2Excel = (tHeader, filterVal) => {
      otherForm.listLoading = true;
      var fileName = '检测分配';
      import('@/utils/Export2Excel').then(excel => {
        const data = formatJson(filterVal, otherForm.allList);
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: fileName,
          autoWidth: true,
          bookType: 'xlsx'
        });
        otherForm.listLoading = false;
        proxy.$message.success('导出成功！');
      });
    };
    const formatJson = (filterVal, jsonData) => {
      return jsonData.map(v =>
        filterVal.map(j => {
          return v[j];
        })
      );
    };
    const handleSelectionChange = val => {
      // console.log(val)
      otherForm.multipleSelection = val;
    };
    // 是否禁用checkbox
    const selectable = (row, index) => {
      // if (row.examineByUserId !== otherForm.currentAccountId && row.examineStatus !== 1) {
      //   return false
      // } else {
      //   return true
      // }
      return true;
    };
    const handleCopySample = () => {
      if (otherForm.multipleSelection.length > 1) {
        ElMessage.warning('一次只能复制一个样品');
        return;
      }
      otherForm.dialogCopySample = true;
    };
    // 分配点击  查看点击
    const handleDetail = (row, readOnly = false) => {
      const isContracts =
        store.common.bussinessCodeList
          .filter(item => item.name.toString().includes('委托'))
          .findIndex(item => row.presentationCode.toString().startsWith(item.rule.toString().substring(0, 2))) !== -1;
      router.push({
        name: 'Allocation',
        params: {
          sampleId: row.sampleId,
          status: row.status,
          id: row.id,
          isView: readOnly ? 1 : 0
        },
        query: {
          presentationCode: row.presentationCode,
          isContracts: isContracts
        }
      });
    };
    // 点击样品编号跳转到样品详情页面
    const handleSampleOrdersDetail = row => {
      router.push({
        path: '/experiment/detail',
        query: {
          orderId: row.orderId,
          sampleId: row.sampleId
        }
      });
    };

    const handleEdit = row => {
      // console.log(row)
    };

    const inputValue = data => {
      // console.log(data)
      if (typeof data === 'string') {
        otherForm.content = data;
      } else {
        otherForm.content = '';
      }
    };

    // 过滤状态
    const filterStatus = status => {
      const map = {
        0: '待认领',
        1: '待分配',
        2: '已分配',
        3: '报告审批',
        4: '已完成',
        11: '超期待分配',
        21: '超期已分配',
        22: '检测中',
        待同步: '待同步',
        已同步: '已同步'
      };
      return map[status];
    };

    const batchFinishWhiteList = ['待认领', '待分配', '已分配', '超期待分配', '超期已分配', '待同步', '已同步'];

    const showBatchFinish = computed(() => batchFinishWhiteList.includes(otherForm.radioData));

    // 切换tab
    const changeRadio = value => {
      switch (value) {
        case '待认领':
          setQuickQuery('0', '', '0');
          break;
        case '待分配':
          setQuickQuery('1', '', '0');
          break;
        case '已分配':
          setQuickQuery('2', '', '0');
          break;
        case '超期待分配':
          setQuickQuery('11', '', '0');
          break;
        case '超期已分配':
          setQuickQuery('21', '', '0');
          break;
        case '已完成':
          setQuickQuery('4', '', '0');
          break;
        case '待同步':
          setQuickQuery('', '0', '0');
          break;
        case '已同步':
          setQuickQuery('', '1', '0');
          break;
        case '已作废':
          setQuickQuery('', '', '1');
          break;
        default:
          setQuickQuery('', '', '0');
          break;
      }
      getList();
      getNumber(true);
      // if (value === '待分配') {
      // } else if (value === '已分配') {
      //   getNumber(false, 'assignmentCount')
      // } else {
      //   getNumber(true)
      // }
    };

    /**
     * 设置快捷查询的查询条件
     */
    function setQuickQuery(status = '', thirdSyncStatus = '', isInvalidated = '0') {
      if (status === '0') {
        otherForm.formInline.ownerId = '';
        otherForm.giveMeChecked = false;
      } else {
        otherForm.formInline.ownerId = otherForm.accountId;
        if (otherForm.radioData === '全部') {
          otherForm.giveMeChecked = false;
          otherForm.formInline.ownerId = '';
        } else {
          otherForm.formInline.ownerId = otherForm.accountId;
          otherForm.giveMeChecked = true;
        }
      }
      if (status === '2') {
        otherForm.isHaveNew = false;
      }
      otherForm.formInline.status = status;
      otherForm.formInline.thirdSyncStatus = thirdSyncStatus;
      otherForm.formInline.isInvalidated = isInvalidated;
    }

    // 高级搜索-物资分类-change
    const changeProdType = type => {
      // console.log(type)
      if (type === 'all') {
        otherForm.formInline.mateType = '';
      } else {
        otherForm.formInline.mateType = type.code;
      }
    };
    // 高级搜索-入库日期-change
    const changeRuku = date => {
      otherForm.formInline.startTime = date ? formatDateTime(date[0]) : '';
      otherForm.formInline.endTime = date ? formatDateTime(date[1]) : '';
    };
    // 高级搜索-下达日期-change
    const changeXaida = date => {
      otherForm.formInline.assignedStartTime = date ? formatDateTime(date[0]) : '';
      otherForm.formInline.assignedEndTime = date ? formatDateTime(date[1]) : '';
    };
    // 过滤试验员
    const filterUserList = val => {
      if (val) {
        const list = [];
        otherForm.copyUserOptions.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        otherForm.userOptions = list;
      } else {
        otherForm.userOptions = otherForm.copyUserOptions;
      }
    };
    // 结果回传
    const resultBack = () => {
      ElMessageBox({
        title: '同步确认',
        message: '检测结果同步后将不可撤回，是否确认同步?',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          const loading = ElLoading.service({
            lock: true,
            text: '数据同步中，请稍后...',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          var params = {
            thirdDataReturnVoList: []
          };
          otherForm.multipleSelection.forEach(ms => {
            const p = {
              disposalNo: ms.disposalNo,
              disposalProcessId: ms.disposalProcessId,
              disposalType: ms.disposalType,
              disposalTypeCode: ms.disposalTypeCode,
              reportProcessId: ms.reportProcessId,
              reportType: ms.reportType,
              sampleId: ms.sampleId,
              secSampleNum: ms.secSampleNum,
              thirdNo: ms.thirdNo,
              thirdType: ms.thirdType
            };
            params.thirdDataReturnVoList.push(p);
          });
          thirdReportReturn(params).then(res => {
            if (res !== false) {
              // console.log(res)
              loading.close();
              ElMessage.success('同步成功!');
            } else {
              loading.close();
              ElMessage.error('同步失败!');
            }
          });
        })
        .catch(() => {});
    };
    // 获取各个状态的数量
    const getNumber = (isAll, type) => {
      getNumberApi(otherForm.formInline).then(res => {
        if (res) {
          otherForm.tableNumber = res.data.data;
          if (isAll) {
            otherForm.oldTableNumber = JSON.parse(JSON.stringify(otherForm.tableNumber));
          } else {
            otherForm.oldTableNumber[type] = otherForm.tableNumber[type];
          }
        }
      });
    };
    getNumber(true);
    // #region 批量判定
    // 批量判定
    const batchJudgement = () => {
      otherForm.showJudgementDialog = true;
    };

    const closeJudgementDialog = value => {
      otherForm.showJudgementDialog = value;
    };

    const saveJudgement = batchFormData => {
      const params = {
        reportType: 0,
        sampleIdList: []
      };
      otherForm.multipleSelection.forEach(item => {
        params.sampleIdList.push(item.sampleId);
      });
      params.reportType = batchFormData.result;
      saveBatchJudgement(params).then(res => {
        if (res.data.code === 200) {
          if (batchFormData.progress === 1) {
            submitBatchFinish();
          } else {
            getList();
            ElMessage.success('批量判定成功!');
          }
        } else {
          ElMessage.error(`${res.data.message}`);
        }
      });
    };

    // #endregion

    // #region 批量认领

    // 批量认领
    const multipleGet = () => {
      // console.log('multipleGet')
      otherForm.claimDialogVisible = true;
    };
    // 认领
    const handleClaim = row => {
      // console.log(row)
      otherForm.samplesInfo = row;
      otherForm.multipleSelection = [];
      otherForm.multipleSelection.push(row);
      otherForm.claimDialogVisible = true;
    };
    // 获取认领弹出框数据
    const getClaimSInfo = data => {
      // console.log(data)
      var params = {
        samplesVoList: []
      };
      if (data.formdata.length > 0) {
        data.formdata.forEach(d => {
          const p = {
            id: d.id,
            status: d.status,
            ownerId: d.ownerId,
            assignedTime: formatDate(new Date()),
            assignedRemark: d.assignedRemark,
            startDate: d.startDate,
            finishedDate: d.finishedDate
          };
          params.samplesVoList.push(p);
        });
      }
      claimSamples(params);
    };
    // 取消认领弹出框
    const closeClaimModel = data => {
      // console.log(data)
      otherForm.claimDialogVisible = false;
    };
    // 快速入库
    const setWarehouseEntry = () => {
      otherForm.fastInbound = { 0: [], 1: [], 2: [] };
      otherForm.multipleSelection.forEach(item => {
        otherForm.fastInbound[item.isFastInbound || 0].push({
          ...item,
          localWarehousingTime: formatDate(new Date()),
          inputWarehouseQuantity: item.sampleQuantity,
          prodType: '完好',
          sampleWarehousingStatus: '完好'
        });
      });
      if (otherForm.fastInbound[2].length) {
        // 自动入库
        addWarehousings(otherForm.fastInbound[2]).then(res => {
          otherForm.multipleSelection = [];
          if (res) {
            ElMessage.success('自动入库成功');
            getNumber(true);
            getList();
          }
        });
      }
      if (otherForm.fastInbound[1].length) {
        // 手动入库 延迟打开弹出框，否则下达成功的提示语看不到
        setTimeout(() => {
          otherForm.dialogStorageVisible = true;
        }, 1000);
      }
      if (!otherForm.fastInbound[2].length && !otherForm.fastInbound[1].length) {
        otherForm.multipleSelection = [];
        getNumber(true);
        getList();
      }
    };
    // 认领接口
    const claimSamples = param => {
      claimSample(param).then(res => {
        if (res !== false) {
          // console.log(res)
          ElMessage.success(res.data.message);
          otherForm.claimDialogVisible = false;
          otherForm.isHaveNew = true;
          // 操作入库
          setWarehouseEntry();
          getNumber(true);
          getList();
        }
      });
    };

    // 查看申请详情
    const jumpApplicationDetail = row => {
      if (row.type === 10 || row.type === 11 || row.type === 12) {
        getTaskRegistrationList({
          condition: `${row.presentationCode}`
        }).then(res => {
          if (res && res.status === 200) {
            if (res.data.data.list.length > 0) {
              router.push({
                name: 'TestAllocationRegistration',
                query: { id: res.data.data.list.find(item => item.entrustNo === row.presentationCode)?.id, flag: 1 }
              });
            }
          }
        });
      } else {
        getInspectionList({ param: `${row.presentationCode}` }).then(res => {
          if (res && res.status === 200) {
            if (res.data.data.list.length > 0) {
              router.push({
                name: 'TestAllocationApplication',
                query: { id: res.data.data.list[0].id, flag: 1 }
              });
            }
          }
        });
      }
    };

    const handleCancel = row => {
      if (row.sampleId) {
        cancelSample(row.sampleId).then(res => {
          if (res.data.code === 200) {
            ElMessage.success(`样品 ${row.secSampleNum} 已成功作废！`);
            getList();
          }
        });
      }
    };
    // 仅看我的
    const giveMeChange = check => {
      if (check) {
        otherForm.formInline.ownerId = otherForm.accountId;
      } else {
        otherForm.formInline.ownerId = '';
      }
      getNumber(true);
      getList();
    };
    const handleRestore = row => {
      if (row.sampleId) {
        restoreSample(row.sampleId).then(res => {
          if (res.data.code === 200) {
            ElMessage.success(`样品 ${row.secSampleNum} 已成功还原！`);
            getList();
          }
        });
      }
    };
    const handleRowClick = row => {
      if (
        otherForm.multipleSelection.some(item => {
          return item.secSampleNum === row.secSampleNum;
        })
      ) {
        otherForm.multipleSelection = otherForm.multipleSelection.filter(item => {
          return item.secSampleNum !== row.secSampleNum;
        });
      } else {
        otherForm.multipleSelection.push(row);
      }
      otherForm.tableRef.toggleRowSelection(row);
    };
    // 加急、取消加急
    const handleUrgent = type => {
      const params = otherForm.multipleSelection.map(item => {
        return item.sampleId;
      });
      if (type) {
        // 取消加急
        unUrgentApi(params).then(res => {
          if (res) {
            proxy.$message.success(res.data.data);
            otherForm.multipleSelection = [];
            getList();
          }
        });
      } else {
        // 加急
        urgentApi(params).then(res => {
          if (res) {
            proxy.$message.success(res.data.data);
            otherForm.multipleSelection = [];
            getList();
          }
        });
      }
    };

    /**
     * @description: 特殊送样
     * @return {*}
     */
    function handleSpecialSample() {
      const ids = otherForm.multipleSelection.map(item => {
        return item.sampleId;
      });
      specialSampleApi(ids).then(res => {
        if (res) {
          proxy.$message.success('操作成功');
          otherForm.multipleSelection = [];
          getList();
        }
      });
    }

    /**
     * @description: 取消送样
     * @return {*}
     */
    function handleCancelSpecialSample() {
      const ids = otherForm.multipleSelection.map(item => {
        return item.sampleId;
      });
      cancelSpecialSampleApi(ids).then(res => {
        if (res) {
          proxy.$message.success('操作成功');
          otherForm.multipleSelection = [];
          getList();
        }
      });
    }

    // #endregion 批量认领

    // #region 批量完成

    const batchFinish = () => {
      otherForm.showFinishDialog = true;
    };

    const cancelFinishDialog = () => {
      otherForm.showFinishDialog = false;
    };

    const submitBatchFinish = (event = false) => {
      const params = {
        sampleBatchFinishVoList: []
      };
      otherForm.multipleSelection.forEach(item => {
        params.sampleBatchFinishVoList.push({
          sampleId: item.sampleId,
          secSampleNum: item.secSampleNum,
          status: item.status
        });
      });
      saveBatchFinish(params).then(res => {
        if (res.data.code === 200) {
          otherForm.showFinishDialog = false;
          getList();
          if (res.data.data && res.data.data.length > 0) {
            if (event) {
              ElMessage.success('部分样品已完成检测,无法完成的样品见提示框!');
            } else {
              ElMessage.success('所选样品已全部判定合格,部分样品已完成检测,无法完成的样品见提示框!');
            }
            otherForm.unFinishedSampleList = res.data.data;
            otherForm.showUnFinishedDialog = true;
          } else {
            if (event) {
              ElMessage.success('所选样品已全部完成检测!');
            } else {
              ElMessage.success('所选样品已全部判定合格并完成检测!');
            }
          }
        } else {
          ElMessage.error(`${res.data.message}`);
        }
      });
    };

    const submitUnfinishTip = () => {
      otherForm.showUnFinishedDialog = false;
    };
    const getSingleText = val => {
      otherForm.formInline.param = val;
      otherForm.formInline.tableQueryParamList = [];
      getList();
    };

    const getParamList = paramList => {
      otherForm.formInline.tableQueryParamList = paramList;
      otherForm.formInline.param = '';
      getList();
    };

    // #endregion

    const getList = data => {
      otherForm.listLoading = true;
      if (data && data !== undefined) {
        otherForm.listQuery.page = data.page;
        otherForm.listQuery.limit = data.limit;
      }
      const param = Object.assign(otherForm.formInline, otherForm.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      getDistributionList(param).then(res => {
        if (res) {
          const { data } = res.data;
          otherForm.list = data.list;
          otherForm.list.forEach(item => {
            // if (item.inspectionStrategyId) {
            //   getStrategy(item.inspectionStrategyId).then(res => {
            //     if (res) {
            //       item.inspectionStrategyName = res.data.data.inspectionName
            //     }
            //   })
            // }
          });
          otherForm.total = data.totalCount;
        }
        otherForm.listLoading = false;
      });
    };

    const getNameList = () => {
      checkPermissionList(permissionTypeList.sampleOwner).then(res => {
        otherForm.mangeList = res.data.data;
      });
    };

    getList();
    getNameList();
    // 刷新列表
    mittBus.$on('reloadTestAllocationList', msg => {
      getList();
    });
    otherForm.copyUserOptions = JSON.parse(JSON.stringify(otherForm.userOptions));

    // 更新表格字段
    const onUpdateColumns = columns => {
      otherForm.tableKey = otherForm.tableKey + 1;
      otherForm.tableColumns = columns;
      otherForm.searchFieldList = columns.filter(item => {
        return item.isQuery == 1;
      });
      otherForm.fieldTips = '查询内容';
    };

    const onRedirectReportDetail = rowData => {
      router.push({
        path: '/detail-report',
        query: {
          reportId: rowData.reportId,
          sampleId: rowData.sampleId,
          reportStage: 6
        }
      });
    };
    return {
      filterUserList,
      closeStorage,
      handleCopySample,
      getSingleText,
      getParamList,
      handleUrgent,
      handleRowClick,
      getNumber,
      formatJson,
      giveMeChange,
      closeDialogImport,
      closeDialogCopy,
      handleImport,
      exportExcel,
      export2Excel,
      getPermissionBtn,
      drageHeader,
      handleSampleOrdersDetail,
      formatDate,
      changeXaida,
      changeRuku,
      formatDateTime,
      changeProdType,
      getNameByid,
      changeRadio,
      inputValue,
      handleSelectionChange,
      selectable,
      handleEdit,
      handleDetail,
      handleClaim,
      claimSamples,
      sortChange,
      editFrom,
      ...toRefs(otherForm),
      search,
      onSubmit,
      reset,
      resultBack,
      multipleGet,
      getClaimSInfo,
      closeClaimModel,
      filterStatus,
      filterSampleUnitToName,
      colWidth,
      jumpApplicationDetail,
      handleCancel,
      handleRestore,
      batchJudgement,
      closeJudgementDialog,
      saveJudgement,
      cancelFinishDialog,
      submitBatchFinish,
      batchFinish,
      submitUnfinishTip,
      getNameList,
      getList,
      showBatchFinish,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum,
      handleSpecialSample,
      handleCancelSpecialSample,
      onRedirectReportDetail
    };
  },
  computed: {
    ...mapGetters(['tenantGroup', 'tenantInfo'])
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.el-button {
  .el-icon-upload,
  .iconfont {
    margin-right: 5px;
  }
}
.listNumber {
  font-size: 12px;
}
.isHaveNew {
  &:after {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 5px;
    position: absolute;
    top: 3px;
    right: 4px;
    background: #f56c6c;
  }
}
.page-wrapper {
  .sample-order-form {
    text-align: left;

    .el-form-item {
      margin-bottom: 0px;
    }

    .searchBtn {
      border: 0;
      background: none;
      color: $tes-primary;
      padding: 0;
    }
  }

  .allocation-table {
    .blue-color {
      color: $tes-primary;
      cursor: pointer;
    }
  }
}

.tip-panel {
  border: 1px solid $tes-primary;
  border-radius: 10px;
  .info-icon {
    color: #409eff;
    line-height: 2.571429rem;
    font-size: 0.9rem;
  }
  .warning-icon {
    color: orange;
    line-height: 2.571429rem;
    font-size: 0.9rem;
  }
}
.tip-panel:hover {
  box-shadow: 1px 1px 2px 1px $tes-primary4;
}

.tip-message {
  padding: 5px 10px 10px 10px;
}

.tip-text {
  margin: 0px;
  line-height: 1.25rem;
  color: black;
}

#flex-group {
  display: flex;
  flex-direction: row;
  #flex-space {
    width: 15px;
  }
  .flex-content {
    flex: auto;
  }
}

.little-tag {
  position: absolute;
  left: -11px;
  top: 2px;
  z-index: 10;
}
</style>
