<template>
  <el-drawer
    v-model="showDrawer"
    :title="titles"
    direction="rtl"
    :before-close="handleClose"
    size="50%"
    destroy-on-close
    :close-on-click-modal="false"
    custom-class="page-drawer"
    @opened="handleOpened"
  >
    <el-form
      ref="formRef"
      :model="formInline"
      class="formDataSample"
      :inline="true"
      :rules="addRules"
      :label-width="labelWidth"
      :label-position="position"
    >
      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="标题：" prop="title">
            <el-input ref="inputRef" v-model="formInline.title" v-trim placeholder="请输入标题" maxlength="30" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="系统版本：" prop="version">
            <el-input v-model="formInline.version" placeholder="请输入版本号" maxlength="30" />
          </el-form-item>
        </el-col>
        <el-col v-loading="loadContant" :span="24">
          <el-form-item label="发布内容：" prop="content">
            <!-- <el-input v-model="formInline.content" placeholder="请输入内容" /> -->
            <MarkdownEditor :value="formInline.content" @getContent="getContent" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="drawer-fotter">
      <el-button type="primary" @click="onSubmit" @keyup.prevent @keydown.enter.prevent>保存</el-button>
      <el-button @click="handleClose">取消</el-button>
    </div>
  </el-drawer>
</template>

<script>
import { ref, watch, reactive, toRefs } from 'vue';
import { saveOrUpdateLogInfo, getLogInfoById } from '@/api/logInfo';
import { ElMessage } from 'element-plus';
import { getLoginInfo } from '@/utils/auth';
import { formatDate } from '@/utils/formatTime';
// import { useStore } from 'vuex'
// import { useRoute } from 'vue-router'
import MarkdownEditor from '@/components/MarkdownEditor/index.vue';

export default {
  name: 'AddLog',
  components: { MarkdownEditor },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      required: true
    },
    editData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    // const { appContext } = getCurrentInstance()
    // console.log(appContext)
    // const store = useStore().state
    const datas = reactive({
      currentAccountId: getLoginInfo().accountId,
      inputRef: ref(),
      showDrawer: false,
      titles: '新增更新日志',
      formRef: ref(),
      formInline: {
        title: '',
        version: '',
        content: ''
      },
      position: 'top',
      labelWidth: '110px',
      addRules: {
        title: [{ required: true, message: '请输入标题' }],
        version: [{ required: true, message: '请输入版本号' }]
      },
      loadContant: false
    });

    watch(
      () => props.drawer,
      newValue => {
        // console.log(props)
        // console.log(newValue)
        if (newValue) {
          datas.showDrawer = newValue;
          datas.titles = props.title === 'add' ? '新增日志' : '更新日志';
          if (props.title === 'add') {
            datas.formInline = {
              title: '',
              version: '',
              content: ''
            };
          } else {
            datas.formInline = props.editData;
            getDetailById(datas.formInline.id);
          }
        }
      },
      { deep: true }
    );

    // 获取正文内容
    const getContent = value => {
      // console.log(value)
      datas.formInline.content = value;
    };
    // 根据id查询详情
    const getDetailById = id => {
      datas.loadContant = true;
      getLogInfoById(id).then(res => {
        if (res !== false) {
          // console.log(res.data.data)
          datas.formInline = res.data.data;
        }
        datas.loadContant = false;
      });
    };

    // 确认新增
    const onSubmit = () => {
      datas.formRef.validate(valid => {
        if (valid) {
          if (props.title === 'add') {
            datas.formInline.createBy = datas.currentAccountId;
            datas.formInline.createTime = formatDate(new Date());
            saveOrUpdateLogInfo(datas.formInline).then(function (res) {
              // console.log(res)
              if (res !== false) {
                context.emit('setInfo', datas.formInline);
                context.emit('close', false);
                ElMessage.success('新增成功');
                datas.showDrawer = false;
              }
            });
          } else {
            saveOrUpdateLogInfo(datas.formInline).then(function (res) {
              // console.log(res)
              if (res !== false) {
                context.emit('setInfo', datas.formInline);
                context.emit('close', false);
                ElMessage.success('编辑成功');
                datas.showDrawer = false;
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    // 关闭抽屉
    const handleClose = () => {
      datas.showDrawer = false;
      context.emit('close', false);
    };

    const handleOpened = () => {
      if (datas.inputRef) {
        datas.inputRef.focus();
      }
    };

    return {
      ...toRefs(datas),
      onSubmit,
      handleClose,
      handleOpened,
      getContent,
      getDetailById
    };
  },
  created() {},
  methods: {}
};
</script>

<style lang="scss" scoped>
.formDataSample {
  :deep(.el-input--medium .el-input__inner) {
    height: 32px;
    line-height: 32px;
  }
  :deep(.el-form-item) {
    margin-right: 0;
  }
  :deep(.el-form-item__content) {
    display: block;
  }
}
</style>
