<template>
  <el-drawer
    v-model="drawerShow"
    :title="category === 0 ? '机构详情' : '分包商详情'"
    direction="rtl"
    :before-close="handleClose"
    size="88%"
    destroy-on-close
    custom-class="page-drawer customerManagement"
  >
    <DrawerLayout
      v-loading="detailLoading"
      :has-left-panel="false"
      :main-offset-top="53"
      :has-button-group="
        (category === 0 && getPermissionBtn('editOrganization')) ||
        (category === 1 && getPermissionBtn('editSubcontractor'))
      "
    >
      <template #drawer-title>
        <span class="title">{{ detailData.name }}</span>
        <el-tag v-if="detailData.isValid" size="mini">生效</el-tag>
        <el-tag v-else type="info" size="mini">作废</el-tag>
      </template>
      <template #button-group>
        <el-button size="small" @click="editDetail" @keyup.prevent @keydown.enter.prevent>编辑机构信息</el-button>
      </template>
      <el-form class="isCheck" label-position="right" label-width="130px">
        <el-row>
          <el-col :span="8">
            <el-form-item :label="category === 1 ? '分包商编号：' : '机构编号：'" prop="no">
              <div class="formValue">{{ detailData.no || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col v-if="category === 0" :span="8">
            <el-form-item label="检测机构属性：" prop="type">
              {{ typeJson[detailData.type] || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="英文名：" prop="enName">
              <div class="formValue">{{ detailData.enName || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="公司传真：" prop="faxNo">
              <div class="formValue">{{ detailData.faxNo || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="公司电话：" prop="phone">
              <div class="formValue">{{ detailData.phone || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="已获得认可：" prop="reckoningWay">
              <span v-if="detailData.isCma">CMA </span>
              <span v-if="detailData.isCnas">CNAS </span>
              <span v-if="detailData.isStamp">检测专用章</span>
              <span v-if="!detailData.isCma && !detailData.isCnas && !detailData.isStamp">--</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="经营范围：" prop="bizScope">
              <div class="formValue">{{ detailData.bizScope || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注：" prop="remark">
              <div class="formValue">{{ detailData.remark || '--' }}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs v-model="activeName" class="marginTop">
        <el-tab-pane label="地址" name="1" style="text-align: left">
          <div
            v-if="
              detailData.isValid &&
              ((category === 1 && getPermissionBtn('addEditAddressSub')) ||
                (category === 0 && getPermissionBtn('addEditAddressOrg')))
            "
            class="btn-group"
          >
            <el-button
              :loading="tableLoading"
              icon="el-icon-plus"
              type="primary"
              size="small"
              @click="addAddress"
              @keyup.prevent
              @keydown.enter.prevent
              >新增</el-button
            >
            <el-button
              v-if="tableAddress.length > 0 && !isEditAddress && !isAddress"
              icon="el-icon-edit"
              :loading="tableLoading"
              size="small"
              @click="isEditAddress = true"
              @keyup.prevent
              @keydown.enter.prevent
              >编辑</el-button
            >
            <el-button
              v-if="isEditAddress || isAddress"
              size="small"
              :loading="tableLoading"
              type="primary"
              @click="saveAddress"
              @keyup.prevent
              @keydown.enter.prevent
              >保存</el-button
            >
            <el-button
              v-if="isEditAddress || isAddress"
              size="small"
              :loading="tableLoading"
              @click="calceAddress"
              @keyup.prevent
              @keydown.enter.prevent
              >取消</el-button
            >
          </div>
          <el-table
            v-loading="tableLoading"
            :data="tableAddress"
            fit
            border
            height="auto"
            highlight-current-row
            class="detail-table dark-table base-table format-height-table2"
          >
            <el-table-column label="序号" :width="colWidth.serialNo" align="center">
              <template #default="{ $index }">
                <span> {{ $index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="regionState" label="省份/地区" show-overflow-tooltip>
              <template #default="{ row }">
                <div v-if="!isEditAddress && row.id">
                  {{ row.regionState ? row.regionState.toString() : row.regionState }}
                </div>
                <el-cascader
                  v-else
                  v-model="row.regionState"
                  placeholder="请选择省份/地区"
                  :props="cascaderProps"
                  :options="provice"
                  popper-class="province"
                  filterable
                  clearable
                />
              </template>
            </el-table-column>
            <el-table-column prop="exactAddress" label="详细地址" show-overflow-tooltip>
              <template #default="{ row }">
                <div v-if="!isEditAddress && row.id">{{ row.exactAddress }}</div>
                <el-input v-else v-model="row.exactAddress" maxlength="120" placeholder="请输入详细地址" />
              </template>
            </el-table-column>
            <el-table-column prop="isDefault" label="默认地址" width="180">
              <template #default="{ row }">
                <el-tag v-if="!isEditAddress && row.id" :type="row.isDefault ? 'success' : 'info'" size="small">
                  {{ row.isDefault ? '是' : '否' }}
                </el-tag>
                <el-switch
                  v-else
                  v-model="row.isDefault"
                  class="inner-switch"
                  :active-text="row.isDefault ? '是' : '否'"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-if="
                detailData.isValid &&
                ((category === 1 && getPermissionBtn('addEditAddressSub')) ||
                  (category === 0 && getPermissionBtn('addEditAddressOrg')))
              "
              label="操作"
              :width="colWidth.operationSingle"
            >
              <template #default="{ row, $index }">
                <span class="blue-color" @click="deleteAddress(row, $index)">删除</span>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="联系人" name="2" style="text-align: left">
          <div
            v-if="
              detailData.isValid &&
              ((category === 1 && getPermissionBtn('addEditContactsSub')) ||
                (category === 0 && getPermissionBtn('addEditContactsOrg')))
            "
            class="btn-group"
          >
            <el-button
              :loading="tableLoading"
              icon="el-icon-plus"
              type="primary"
              size="small"
              @click="addContacts"
              @keyup.prevent
              @keydown.enter.prevent
              >新增</el-button
            >
            <el-button
              v-if="tableContacts.length > 0 && !isEditContacts && !isAddContacts"
              icon="el-icon-edit"
              :loading="tableLoading"
              size="small"
              @click="isEditContacts = true"
              @keyup.prevent
              @keydown.enter.prevent
              >编辑</el-button
            >
            <el-button
              v-if="isEditContacts || isAddContacts"
              size="small"
              :loading="tableLoading"
              type="primary"
              @click="saveContacts"
              @keyup.prevent
              @keydown.enter.prevent
              >保存</el-button
            >
            <el-button
              v-if="isEditContacts || isAddContacts"
              size="small"
              :loading="tableLoading"
              @click="calceContacts"
              @keyup.prevent
              @keydown.enter.prevent
              >取消</el-button
            >
          </div>
          <el-table
            v-loading="tableLoading"
            :data="tableContacts"
            fit
            border
            height="auto"
            highlight-current-row
            class="detail-table dark-table base-table format-height-table2"
          >
            <el-table-column label="序号" :width="colWidth.serialNo" align="center">
              <template #default="{ $index }">
                <span> {{ $index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="姓名" show-overflow-tooltip>
              <template #default="{ row, $index }">
                <div v-if="!isEditContacts && row.id">{{ row.name || '--' }}</div>
                <el-form v-else :ref="'name' + $index" :model="row" style="margin: 0px" :rules="contactsRules">
                  <el-form-item prop="name" style="margin: 0px">
                    <el-input v-model="row.name" maxlength="100" placeholder="请输入姓名" />
                  </el-form-item>
                </el-form>
              </template>
            </el-table-column>
            <el-table-column prop="job" label="职务" show-overflow-tooltip>
              <template #default="{ row }">
                <div v-if="!isEditContacts && row.id">{{ row.job || '--' }}</div>
                <el-input v-else v-model="row.job" maxlength="100" placeholder="请输入职务" />
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="电话" show-overflow-tooltip>
              <template #default="{ row, $index }">
                <div v-if="!isEditContacts && row.id">{{ row.phone || '--' }}</div>
                <el-form v-else :ref="'phone' + $index" :model="row" style="margin: 0px" :rules="contactsRules">
                  <el-form-item prop="phone" style="margin: 0px">
                    <el-input v-model="row.phone" placeholder="请输入电话" />
                  </el-form-item>
                </el-form>
              </template>
            </el-table-column>
            <el-table-column prop="email" label="邮箱" show-overflow-tooltip>
              <template #default="{ row, $index }">
                <div v-if="!isEditContacts && row.id">{{ row.email }}</div>
                <el-form v-else :ref="'email' + $index" :model="row" style="margin: 0px" :rules="contactsRules">
                  <el-form-item prop="email" style="margin: 0px">
                    <el-input v-model="row.email" placeholder="请输入邮箱" />
                  </el-form-item>
                </el-form>
              </template>
            </el-table-column>
            <el-table-column prop="isDefault" label="默认联系人" width="180">
              <template #default="{ row }">
                <el-tag v-if="!isEditContacts && row.id" :type="row.isDefault ? 'success' : 'info'" size="small">
                  {{ row.isDefault ? '是' : '否' }}
                </el-tag>
                <el-switch
                  v-else
                  v-model="row.isDefault"
                  class="inner-switch"
                  :active-text="row.isDefault ? '是' : '否'"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-if="
                detailData.isValid &&
                ((category === 1 && getPermissionBtn('addEditContactsSub')) ||
                  (category === 0 && getPermissionBtn('addEditContactsOrg')))
              "
              label="操作"
              :width="colWidth.operationSingle"
            >
              <template #default="{ row, $index }">
                <span class="blue-color" @click="deleteContacts(row, $index)">删除</span>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane v-if="category === 0" label="发票" name="3" style="text-align: left">
          <div v-if="detailData.isValid && getPermissionBtn('addEditInvoiceOrg')" class="btn-group">
            <el-button
              v-if="tableInvoice.length == 0"
              :loading="tableLoading"
              type="primary"
              icon="el-icon-plus"
              size="small"
              @click="addInvoice"
              @keyup.prevent
              @keydown.enter.prevent
              >新增</el-button
            >
            <el-button
              v-if="tableInvoice.length > 0 && !isEditInvoice"
              :loading="tableLoading"
              icon="el-icon-edit"
              size="small"
              @click="isEditInvoice = true"
              @keyup.prevent
              @keydown.enter.prevent
              >编辑</el-button
            >
            <el-button
              v-if="isEditInvoice"
              size="small"
              :loading="tableLoading"
              type="primary"
              @click="saveInvoice"
              @keyup.prevent
              @keydown.enter.prevent
              >保存</el-button
            >
            <el-button
              v-if="isEditInvoice"
              size="small"
              :loading="tableLoading"
              @click="calceInvoice"
              @keyup.prevent
              @keydown.enter.prevent
              >取消</el-button
            >
          </div>
          <el-table
            v-loading="tableLoading"
            :data="tableInvoice"
            fit
            border
            hegiht="auto"
            highlight-current-row
            class="detail-table dark-table base-table"
          >
            <el-table-column prop="templatekey" label="公司税号" :width="colWidth.taxNo" show-overflow-tooltip>
              <template #default="{ row, $index }">
                <span v-if="!isEditInvoice"> {{ row.taxNo || '--' }}</span>
                <el-form v-else :ref="'taxNo' + $index" :model="row" style="margin: 0px" :rules="contactsRules">
                  <el-form-item prop="taxNo" style="margin: 0px">
                    <el-input v-model="row.taxNo" placeholder="请输入公司税号" />
                  </el-form-item>
                </el-form>
              </template>
            </el-table-column>
            <el-table-column prop="openingBank" label="开户行" show-overflow-tooltip>
              <template #default="{ row }">
                <span v-if="!isEditInvoice"> {{ row.openingBank }}</span>
                <el-input v-else v-model="row.openingBank" placeholder="请输入开户行" />
              </template>
            </el-table-column>
            <el-table-column prop="acctNo" label="账号" show-overflow-tooltip>
              <template #default="{ row }">
                <span v-if="!isEditInvoice"> {{ row.acctNo || '--' }}</span>
                <el-input v-else v-model="row.acctNo" placeholder="请输入账号" />
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="电话" :width="colWidth.telephone">
              <template #default="{ row, $index }">
                <span v-if="!isEditInvoice"> {{ row.phone || '--' }}</span>
                <el-form v-else :ref="'phone' + $index" :model="row" style="margin: 0px" :rules="contactsRules">
                  <el-form-item prop="phone" style="margin: 0px">
                    <el-input v-model="row.phone" placeholder="请输详细电话" />
                  </el-form-item>
                </el-form>
              </template>
            </el-table-column>
            <el-table-column prop="resulttype" label="发票地址" :width="colWidth.address" show-overflow-tooltip>
              <template #default="{ row }">
                <span v-if="!isEditInvoice"> {{ row.regionState ? row.regionState.toString() : row.regionState }}</span>
                <el-cascader
                  v-else
                  v-model="row.regionState"
                  placeholder="请输入发票地址"
                  :props="cascaderProps"
                  :options="provice"
                  filterable
                  clearable
                />
              </template>
            </el-table-column>
            <el-table-column prop="exactAddress" label="详细地址" :min-width="160" show-overflow-tooltip>
              <template #default="{ row }">
                <span v-if="!isEditInvoice"> {{ row.exactAddress || '--' }}</span>
                <el-input v-else v-model="row.exactAddress" maxlength="120" placeholder="请输入详细地址" />
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="附件" name="4" style="text-align: left">
          <div
            v-if="
              detailData.isValid &&
              ((category === 1 && getPermissionBtn('addEditFileSub')) ||
                (category === 0 && getPermissionBtn('addEditFileOrg')))
            "
          >
            <el-upload
              :action="fileAction"
              :show-file-list="false"
              :headers="headerconfig"
              :before-upload="beforeUpload"
              :on-success="
                (res, file, files) => {
                  return handleSuccess(res, file, files);
                }
              "
              :auto-upload="true"
            >
              <el-button
                class="add-btn"
                size="small"
                icon="el-icon-plus"
                type="primary"
                @keyup.prevent
                @keydown.enter.prevent
                >上传文件</el-button
              >
              <span class="uploadSupplement">文件大小不能超过20M</span>
            </el-upload>
          </div>
          <el-table
            :data="tableFileList"
            fit
            border
            highlight-current-row
            size="medium"
            max-height="440px"
            class="detail-table format-height-table2 dark-table"
            @header-dragend="drageHeader"
          >
            <el-table-column type="index" label="序号" :width="colWidth.serialNo" align="center" />
            <el-table-column prop="name" label="附件" :min-width="colWidth.name" show-overflow-tooltip>
              <template #default="{ row }">
                <span class="blue-color" @click="downLoadFile(row)">{{ row.name || '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="createBy" label="上传人" :min-width="colWidth.person" show-overflow-tooltip>
              <template #default="{ row }">
                <UserTag :name="getNameByid(row.createBy) || '--'" />
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="上传时间" :width="colWidth.date">
              <template #default="{ row }">
                <span>{{ formatDate(row.createTime) || '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="
                detailData.isValid &&
                ((category === 1 && getPermissionBtn('addEditFileSub')) ||
                  (category === 0 && getPermissionBtn('addEditFileOrg')))
              "
              fixed="right"
              class-name="fixed-right"
              prop="status"
              label="操作"
              :width="colWidth.operation"
            >
              <template #default="{ row, $index }">
                <span class="blue-color deleteBtn" @click="handleDelete(row, $index)">删除</span>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <!-- 编辑客户信息 -->
      <DrawerCustomerInfo
        :drawer="customerDrawer"
        :detail-data="detailData"
        :drawer-type="'edit'"
        @close="closeCustomerDrawer"
      />
    </DrawerLayout>
  </el-drawer>
</template>
<script>
import { watch, reactive, toRefs, ref, getCurrentInstance } from 'vue';
import { getLoginInfo } from '@/utils/auth';
import {
  getDetail,
  getInvoiceList,
  saveInvoiceApi,
  getAddress,
  saveAddressApi,
  deleteAddressApi,
  getContacts,
  saveContactsApi,
  deleteContactsApi,
  deleteUploadFile,
  downLoadFileApi,
  getFileList
} from '@/api/testingApparatus';
import { inspectionOrgAttachmentUploadUrl } from '@/api/uploadAction';
import DrawerLayout from '@/components/DrawerLayout';
import DrawerCustomerInfo from './DrawerOrganizationInfo.vue';
import { getPermissionBtn } from '@/utils/common';
import { getNameByid } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { formatDate, addMonth } from '@/utils/formatTime';
import { colWidth } from '@/data/tableStyle';
import provice from '@/data/administrativeDivisionsOfChina';
import { isPhoneMobile, isEmail2 } from '@/utils/validate';
import { getToken } from '@/utils/auth';
import UserTag from '@/components/UserTag';
import { useRoute } from 'vue-router';
export default {
  name: 'ApparatusDrawer',
  components: { DrawerLayout, DrawerCustomerInfo, UserTag },
  props: {
    detailDrawer: {
      type: Boolean,
      default: false
    },
    drawerType: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    }
  },
  emits: ['closeDrawer'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    const state = reactive({
      drawerShow: false,
      activeName: '1',
      ruleForm: ref(),
      tableFileList: [], // 附件列表
      headerconfig: {
        Authorization: getToken()
      },
      fileAction: inspectionOrgAttachmentUploadUrl(),
      formRef: ref(),
      tableInvoice: [],
      oldTableInvoice: [],
      contactsRules: {
        phone: { validator: isPhoneMobile, tigger: 'blur' },
        email: { validator: isEmail2, tigger: 'blur' },
        name: { required: true, tigger: 'blur' },
        taxNo: { required: true, tigger: 'blur' }
      },
      tableAddress: [],
      typeJson: {
        0: '内部',
        1: '外部',
        2: '检储配'
      },
      oldTableAddress: [],
      tableContacts: [],
      oldTableContacts: [],
      customerDrawer: false,
      detailInfoId: '',
      detailLoading: false,
      provice: [],
      detailData: {},
      cascaderProps: {
        expandTrigger: 'hover',
        children: 'children',
        label: 'name',
        value: 'name'
      },
      isEditInvoice: false, // 发票列表是否是编辑状态
      isEditAddress: false, // 地址
      isEditContacts: false, // 联系人
      isAddress: false,
      isAddContacts: false,
      tableLoading: false,
      accountId: getLoginInfo().accountId, // 当前登录人的id
      listLoading: false,
      tableSize: 'medium',
      title: '',
      category: route.name === 'SubcontractorManagement' ? 1 : 0 // 1 分包商管理页面， 0 检测机构页面
    });
    const handleClose = () => {
      context.emit('closeDrawer', { isRefresh: true });
    };
    watch(props, newValue => {
      state.drawerShow = props.detailDrawer;
      if (state.drawerShow) {
        state.provice = provice;
        state.detailInfoId = props.id;
        state.fileAction = inspectionOrgAttachmentUploadUrl(state.detailInfoId);
        state.isEditInvoice = false;
        state.isEditAddress = false;
        state.isEditContacts = false;
        state.tableLoading = false;
        state.isAddress = false;
        state.isAddContacts = false;
        state.activeName = '1';
        initDetail();
        getList('invoice');
        getList('address');
        getList('contacts');
        getList('file');
      }
    });

    // 查询详情
    const initDetail = () => {
      state.detailLoading = true;
      getDetail(state.detailInfoId).then(res => {
        state.detailLoading = false;
        if (res) {
          state.detailData = res.data.data;
        }
      });
    };
    // 编辑客户信息
    const editDetail = () => {
      state.customerDrawer = true;
    };
    const closeCustomerDrawer = val => {
      state.customerDrawer = false;
      if (val.isRefresh) {
        initDetail(props.id);
      }
    };
    const getList = type => {
      if (type === 'invoice') {
        // 获取发票列表
        state.tableLoading = true;
        getInvoiceList(state.detailInfoId).then(res => {
          state.tableLoading = false;
          if (res) {
            state.isEditInvoice = false;
            state.tableInvoice = res.data.data.id ? [JSON.parse(JSON.stringify(res.data.data))] : [];
            state.oldTableInvoice = res.data.data.id ? [JSON.parse(JSON.stringify(res.data.data))] : [];
          }
        });
      } else if (type === 'address') {
        state.tableLoading = true;
        getAddress(state.detailInfoId).then(res => {
          state.tableLoading = false;
          if (res) {
            state.isEditAddress = false;
            state.isAddress = false;
            state.tableAddress = JSON.parse(JSON.stringify(res.data.data));
            state.oldTableAddress = JSON.parse(JSON.stringify(res.data.data));
          }
        });
      } else if (type === 'contacts') {
        state.tableLoading = true;
        getContacts(state.detailInfoId).then(res => {
          state.tableLoading = false;
          if (res) {
            state.isEditContacts = false;
            state.isAddContacts = false;
            state.tableContacts = JSON.parse(JSON.stringify(res.data.data));
            state.oldTableContacts = JSON.parse(JSON.stringify(res.data.data));
          }
        });
      } else if (type === 'file') {
        state.tableLoading = true;
        getFileList(state.detailInfoId).then(res => {
          state.tableLoading = false;
          if (res) {
            state.tableFileList = res.data.data;
          }
        });
      }
    };
    // 保存地址
    const saveAddress = () => {
      if (
        state.tableAddress.some(item => {
          return !item.regionState || item.regionState.length === 0;
        })
      ) {
        proxy.$message.error('省份/地区不能为空');
      } else {
        state.tableLoading = true;
        saveAddressApi({ data: state.tableAddress, id: state.detailInfoId }).then(res => {
          state.tableLoading = false;
          if (res) {
            proxy.$message.success(res.data.message);
            getList('address');
          }
        });
      }
    };
    // 删除地址
    const deleteAddress = (row, index) => {
      if (row.id) {
        state.tableLoading = true;
        deleteAddressApi(row.id).then(res => {
          state.tableLoading = false;
          if (res) {
            proxy.$message.success(res.data.message);
            state.tableAddress.splice(index, 1);
            state.oldTableAddress.splice(
              state.oldTableAddress.findIndex(item => item.id === row.id),
              1
            );
          }
        });
      } else {
        state.tableAddress.splice(index, 1);
      }
    };
    // 保存联系人
    const saveContacts = () => {
      const validateArry = [];
      for (const i in proxy.$refs) {
        if (proxy.$refs[i] !== null) {
          proxy.$refs[i].validate(valid => {
            if (!valid) {
              if (i.indexOf('name') >= 0 && validateArry.indexOf('name') === -1) {
                validateArry.push('name');
              } else if (i.indexOf('phone') >= 0 && validateArry.indexOf('phone') === -1) {
                validateArry.push('phone');
              } else if (i.indexOf('email') >= 0 && validateArry.indexOf('email') === -1) {
                validateArry.push('email');
              }
            }
          });
        }
      }
      if (validateArry.length === 0) {
        state.tableLoading = true;
        saveContactsApi({ data: state.tableContacts, id: state.detailInfoId }).then(res => {
          state.tableLoading = false;
          if (res) {
            proxy.$message.success(res.data.message);
            getList('contacts');
          }
        });
      } else {
        var messageError = '';
        if (
          validateArry.some(item => {
            return item === 'name';
          })
        ) {
          messageError = '<div style="line-height: 20px;">请输入姓名</div>';
        }
        if (
          validateArry.some(item => {
            return item === 'phone';
          })
        ) {
          messageError += '<div style="line-height: 20px;">请输入正确的电话</div>';
        }
        if (
          validateArry.some(item => {
            return item === 'email';
          })
        ) {
          messageError += '<div style="line-height: 20px;">请输入正确的邮箱</div>';
        }
        proxy.$message.error({
          dangerouslyUseHTMLString: true,
          message: messageError
        });
      }
    };
    // 删除联系人
    const deleteContacts = (row, index) => {
      if (row.id) {
        state.tableLoading = true;
        deleteContactsApi(row.id).then(res => {
          state.tableLoading = false;
          if (res) {
            proxy.$message.success(res.data.message);
            state.tableContacts.splice(index, 1);
            state.oldTableContacts.splice(
              state.oldTableContacts.findIndex(item => item.id === row.id),
              1
            );
          }
        });
      } else {
        state.tableContacts.splice(index, 1);
      }
    };
    // 新增发票
    const addInvoice = () => {
      state.isEditInvoice = true;
      state.tableInvoice.push({
        taxNo: ''
      });
    };
    // 新增地址
    const addAddress = () => {
      state.isAddress = true;
      state.tableAddress.push({
        isDefault: true,
        regionState: [],
        superId: state.detailInfoId
      });
    };
    // 新增联系人
    const addContacts = () => {
      state.isAddContacts = true;
      state.tableContacts.push({
        isDefault: true,
        name: '',
        superId: state.detailInfoId
      });
    };
    // 保存发票
    const saveInvoice = () => {
      const validateArry = [];
      for (const i in proxy.$refs) {
        if (proxy.$refs[i] !== null) {
          proxy.$refs[i].validate(valid => {
            if (!valid) {
              if (i.indexOf('taxNo') >= 0 && validateArry.indexOf('taxNo') === -1) {
                validateArry.push('taxNo');
              } else if (i.indexOf('phone') >= 0 && validateArry.indexOf('phone') === -1) {
                validateArry.push('phone');
              }
            }
          });
        }
      }
      if (validateArry.length === 0) {
        const params = JSON.parse(JSON.stringify(state.tableInvoice[0]));
        state.tableLoading = true;
        saveInvoiceApi({ superId: state.detailInfoId, ...params }).then(res => {
          state.tableLoading = false;
          if (res) {
            proxy.$message.success(res.data.message);
            getList('invoice');
          }
        });
      } else {
        var messageError = '';
        if (
          validateArry.some(item => {
            return item === 'taxNo';
          })
        ) {
          messageError = '<div style="line-height: 20px;">请输入公司税号</div>';
        }
        if (
          validateArry.some(item => {
            return item === 'phone';
          })
        ) {
          messageError += '<div style="line-height: 20px;">请输入正确的电话</div>';
        }
        proxy.$message.error({
          dangerouslyUseHTMLString: true,
          message: messageError
        });
      }
    };
    // 取消保存
    const calceInvoice = () => {
      state.isEditInvoice = false;
      if (state.oldTableInvoice.length > 0) {
        state.tableInvoice = [JSON.parse(JSON.stringify(state.oldTableInvoice[0]))];
      } else {
        state.tableInvoice = [];
      }
    };
    // 取消保存
    const calceAddress = () => {
      state.isEditAddress = false;
      state.isAddress = false;
      if (state.oldTableAddress.length > 0) {
        state.tableAddress = JSON.parse(JSON.stringify(state.oldTableAddress));
      } else {
        state.tableAddress = [];
      }
    };
    // 取消保存
    const calceContacts = () => {
      state.isEditContacts = false;
      state.isAddContacts = false;
      if (state.oldTableContacts.length > 0) {
        state.tableContacts = JSON.parse(JSON.stringify(state.oldTableContacts));
      } else {
        state.tableContacts = [];
      }
    };
    // 下载附件
    const downLoadFile = row => {
      downLoadFileApi(row).then(res => {
        const blob = new Blob([res.data], { type: '' });
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = `${row.name}.${row.remoteName.split('.')[1]}`;
        a.href = blobUrl;
        a.click();
        proxy.$message.success('下载附件成功');
      });
    };
    // 上传成功的钩子
    const handleSuccess = (res, file, files) => {
      if (res.code === 200) {
        getList('file');
      } else {
        proxy.$message.error(res.message);
      }
    };
    // 上传文件的限制
    const beforeUpload = file => {
      const fileSize = file.size / 1024 / 1024 < 20;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过20M');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };
    // 删除附件
    const handleDelete = row => {
      proxy
        .$confirm('是否确认删除附件', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          showClose: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        })
        .then(() => {
          state.tableLoading = true;
          deleteUploadFile(row.id).then(res => {
            state.tableLoading = false;
            proxy.$message.success(res.data.message);
            if (res) {
              getList('file');
            }
          });
        })
        .catch(() => {});
    };
    return {
      ...toRefs(state),
      editDetail,
      handleDelete,
      beforeUpload,
      handleSuccess,
      downLoadFile,
      addContacts,
      saveContacts,
      deleteContacts,
      calceContacts,
      deleteAddress,
      getList,
      addInvoice,
      addAddress,
      saveInvoice,
      saveAddress,
      calceInvoice,
      calceAddress,
      closeCustomerDrawer,
      drageHeader,
      colWidth,
      initDetail,
      getPermissionBtn,
      getNameByid,
      formatDate,
      addMonth,
      handleClose
    };
  }
};
</script>

<style lang="scss" scoped>
.btn-group {
  margin-bottom: 16px;
}
.title {
  display: inline-block;
  max-width: 800px;
  word-wrap: break-word;
  text-align: left;
}
.formValue {
  word-wrap: break-word;
}
.required::before {
  content: '*';
  color: $red;
  position: absolute;
  left: 0;
  top: 0;
}
.isCheck {
  margin-top: 12px;
  padding: 10px 20px;
  background: #f5f7fa;
  border-radius: 3px;
}
:deep(.el-range-editor--small.el-input__inner) {
  width: 100%;
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 100%;
}
:deep(.el-upload-list__item.is-success.focusing .el-icon-close-tip) {
  display: none !important;
}
.add-btn {
  margin-bottom: 20px;
}
.footerBtn {
  position: fixed;
  bottom: 20px;
  right: 40px;
}
:deep(.el-cascader) {
  width: 100%;
}
:deep(.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 34rem) !important;
    overflow-y: auto;
    overflow-x: hidden !important;
  }
}
.marginTop {
  margin-top: 35px;
}
.uploadSupplement {
  display: inline-block;
  margin-left: 10px;
  font-size: 12px;
  color: #909399;
}
</style>
<style lang="scss">
.province .el-popper__arrow {
  opacity: 0;
}
</style>
