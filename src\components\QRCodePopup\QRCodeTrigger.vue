<template>
  <!-- 二维码图标按钮 -->
  <SvgIcon icon-class="qrcode" class="qrcode-trigger" :width="width" :height="height" @click="onClick" />
</template>

<script>
import { getCurrentInstance } from 'vue';
import SvgIcon from '@/components/SvgIcon';

export default {
  name: 'QRCodeTrigger',
  components: { SvgIcon },
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    width: {
      type: Number,
      default: 24
    },
    height: {
      type: Number,
      default: 24
    }
  },
  setup(props) {
    const { appContext } = getCurrentInstance();
    const bus = appContext.config.globalProperties.bus;
    const onClick = () => {
      bus.$emit('showQRCodePopup', props.value);
    };
    return {
      onClick
    };
  }
};
</script>

<style lang="scss" scoped>
.qrcode-trigger {
  cursor: pointer;
}
</style>
