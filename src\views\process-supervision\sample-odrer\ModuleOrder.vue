<template>
  <!--  样品下达操作人员选项弹窗-->
  <el-dialog :ref="dialogForm" v-model="dialogFormVisible" title="样品下达" width="500px" @close="reset()">
    <el-form ref="refForm" :model="formData" :rules="rules" label-position="right" label-width="100px" size="small">
      <el-form-item label="试验负责人：" prop="ownerId">
        <el-select v-model="formData.ownerId" placeholder="请选择" clearable filterable>
          <el-option
            v-for="(item, index) in nameList"
            :key="index"
            :label="item.nickname"
            :value="item.id"
            :disabled="item.status === -2"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="试验日期：" prop="finishedDate">
        <el-date-picker
          v-model="timeRange"
          type="datetimerange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleTimerange"
        />
      </el-form-item>
      <el-form-item label="试验说明：">
        <el-input v-model="formData.assignedRemark" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="reset()">取 消</el-button>
        <el-button type="primary" @click="handleSubmit()">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, ref, watch } from 'vue';
import { formatDateTime } from '@/utils/formatTime';
import { ElMessage } from 'element-plus';
import { samplesOrder } from '@/api/order';
import { getLoginInfo } from '@/utils/auth';
// import { getName } from '@/api/login'
// import { reportAudit } from '@/api/permission'
import { getAllocationUserList } from '@/api/user';
// import store from '@/store'
export default {
  name: 'ModelOrder',
  props: {
    isShow: {
      type: Boolean,
      default: function () {
        return false;
      }
    },
    sampleInfo: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['setInfo', 'close'],
  setup(props, context) {
    const dialogForm = ref();
    const refForm = ref();
    const state = reactive({
      nameList: [],
      timeRange: [formatDateTime(new Date()), formatDateTime(new Date())],
      allocationNameList: [],
      dialogFormVisible: props.isShow,
      samplesInfo: [],
      formData: {
        assignedRemark: '',
        finishedDate: '',
        startDate: '',
        id: '',
        ownerId: getLoginInfo().accountId,
        status: 1
      },
      rules: {
        finishedDate: [
          {
            required: true,
            trigger: 'change',
            message: '请输入'
          }
        ],
        ownerId: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入'
          }
        ]
      }
    });
    getAllocationUserList({}).then(res => {
      if (res.data.code === 200) {
        state.nameList = res.data.data;
      }
    });
    watch(props, newValue => {
      state.dialogFormVisible = newValue.isShow;
      if (state.dialogFormVisible) {
        state.samplesInfo = newValue.sampleInfo;
        state.timeRange = [formatDateTime(new Date()), formatDateTime(new Date())];
        state.formData = {
          ownerId: getLoginInfo().accountId,
          startDate: formatDateTime(new Date()),
          finishedDate: formatDateTime(new Date())
        };
      }
    });

    const handleTimerange = () => {
      if (state.timeRange && state.timeRange.length === 2) {
        state.formData.startDate = state.timeRange[0];
        state.formData.finishedDate = state.timeRange[1];
        state.formData.startDate = formatDateTime(state.formData.startDate);
        state.formData.finishedDate = formatDateTime(state.formData.finishedDate);
      } else {
        state.timeRange = [];
        state.formData.startDate = '';
        state.formData.finishedDate = '';
      }
    };
    // 提交表单
    const handleSubmit = () => {
      const postdata = [];
      const idsStatus0 = [];
      const sampleNoList0 = [];
      const sampleIds0 = [];
      const idsStatus1 = [];
      const sampleNoList1 = [];
      const sampleIds1 = [];
      const idsStatus2 = [];
      const sampleNoList2 = [];
      const sampleIds2 = [];
      if (state.samplesInfo.length > 1) {
        // 批量
        state.samplesInfo.forEach(item => {
          const postdateitem = JSON.parse(JSON.stringify(state.formData));
          postdateitem.id = item.id;
          postdateitem.prodType = item.prodType;
          postdateitem.status = 1;
          postdata.push(postdateitem);
          if (item.status === 0) {
            idsStatus0.push(item.id);
            sampleNoList0.push(item.secSampleNum);
            sampleIds0.push(item.sampleId);
          } else if (item.status === 1) {
            idsStatus1.push(item.id);
            sampleNoList1.push(item.secSampleNum);
            sampleIds1.push(item.sampleId);
          } else if (item.status === 2) {
            idsStatus2.push(item.id);
            sampleNoList2.push(item.secSampleNum);
            sampleIds2.push(item.sampleId);
          }
        });
      } else {
        state.formData = {
          ...state.formData,
          assignedRemark: state.formData.assignedRemark,
          status: 1,
          assignedTime: state.samplesInfo[0].assignedTime,
          id: state.samplesInfo[0].id,
          prodType: state.samplesInfo[0].prodType
        };
        postdata[0] = JSON.parse(JSON.stringify(state.formData));
      }

      refForm.value.validate(valid => {
        if (valid) {
          // ElMessage.error('请上传模板')
          const formdata = JSON.parse(JSON.stringify(postdata));
          samplesOrder({ samplesVoList: formdata }).then(res => {
            if (res.data.code === 200) {
              ElMessage.success({
                message: res.data.message,
                type: 'success'
              });
              const p = {
                nos0: sampleNoList0.join(','),
                idsStatus0: idsStatus0.join(','),
                idsSample0: sampleIds0.join(','),
                nos1: sampleNoList1.join(','),
                idsStatus1: idsStatus1.join(','),
                idsSample1: sampleIds1.join(','),
                nos2: sampleNoList2.join(','),
                idsStatus2: idsStatus2.join(','),
                idsSample2: sampleIds2.join(','),
                formdata: formdata
              };
              context.emit('setInfo', p);
              // getExceldata()
              reset();
            }
          });
        }
      });
    };
    const reset = () => {
      refForm.value.clearValidate();
      state.formData = {
        assignedRemark: '',
        finishedDate: '',
        startDate: '',
        id: '',
        ownerId: '',
        status: 1
      };
      state.samplesInfo = [];
      state.timeRange = [];
      state.dialogFormVisible = false;
      context.emit('close');
    };
    return {
      ...toRefs(state),
      dialogForm,
      formatDateTime,
      handleTimerange,
      handleSubmit,
      reset,
      refForm,
      props
    };
  }
};
</script>

<style scoped lang="scss">
.el-select {
  width: 100%;
}
:deep(.el-range-editor.el-input__inner) {
  width: 100% !important;
}
</style>
