<template>
  <div class="home">
    <el-row :gutter="20">
      <el-col :span="12" :offset="6">
        <el-input v-model="inputData" size="mini" placeholder="请输入1~13" style="width: 400px" @input="changeUrl" />
        <el-button type="primary" size="mini" @click="clickData">click</el-button>
      </el-col>
    </el-row>
    <div style="text-align: center; height: 50px; line-height: 50px; width: 100%">{{ newData }}</div>
    <div style="text-align: center; height: 50px; line-height: 50px; width: 100%">{{ urlData }}</div>
    <div v-for="(name, index) in nameList" :key="index" style="height: 20px; line-height: 20px">
      {{ index + 1 + ':' + name }}
    </div>
  </div>
</template>

<script>
// @ is an alias to /src
// import HelloWorld from '@/components/HelloWorld.vue'
// import { setup } from 'vue-class-component'
import { getCurrentInstance, reactive, toRefs } from 'vue';
import router from '@/router/index.js';
import { constantRoutes } from '@/router';
// import { useRoute } from 'vue-router'

export default {
  name: 'Home',
  components: {},
  setup() {
    const { proxy } = getCurrentInstance();
    proxy.$message('message');
    const datas = reactive({
      inputData: '',
      newData: 'home',
      urlData: window.location.origin + '/home',
      nameList: [
        '样品下达',
        '检测执行',
        '检测分配',
        '检测项目模板',
        '原始记录审核',
        '检测报告',
        '检测项目',
        '检测依据',
        '物资分类',
        '展示分析',
        '我的消息',
        '我的待办',
        '不良品'
      ]
    });
    const changeUrl = value => {
      switch (value) {
        case '1':
          datas.newData = constantRoutes[6].children[0].meta.title;
          datas.urlData = window.location.origin + constantRoutes[6].children[0].path;
          break;
        case '2':
          datas.newData = constantRoutes[6].children[6].meta.title;
          datas.urlData = window.location.origin + constantRoutes[6].children[6].path;
          break;
        case '3':
          datas.newData = constantRoutes[6].children[3].meta.title;
          datas.urlData = window.location.origin + constantRoutes[6].children[3].path;
          break;
        case '4':
          datas.newData = constantRoutes[6].children[10].meta.title;
          datas.urlData = window.location.origin + constantRoutes[6].children[10].path;
          break;
        case '5':
          datas.newData = constantRoutes[6].children[11].meta.title;
          datas.urlData = window.location.origin + constantRoutes[6].children[11].path;
          break;
        case '6':
          datas.newData = constantRoutes[6].children[16].meta.title;
          datas.urlData = window.location.origin + constantRoutes[6].children[16].path;
          break;
        case '7':
          datas.newData = constantRoutes[6].children[19].meta.title;
          datas.urlData = window.location.origin + constantRoutes[6].children[19].path;
          break;
        case '8':
          datas.newData = constantRoutes[6].children[20].meta.title;
          datas.urlData = window.location.origin + constantRoutes[6].children[20].path;
          break;
        case '9':
          datas.newData = constantRoutes[6].children[21].meta.title;
          datas.urlData = window.location.origin + constantRoutes[6].children[21].path;
          break;
        case '10':
          datas.newData = constantRoutes[7].children[0].meta.title;
          datas.urlData = window.location.origin + constantRoutes[7].children[0].path;
          break;
        case '11':
          datas.newData = constantRoutes[8].children[0].meta.title;
          datas.urlData = window.location.origin + constantRoutes[8].children[0].path;
          break;
        case '12':
          datas.newData = constantRoutes[8].children[1].meta.title;
          datas.urlData = window.location.origin + constantRoutes[8].children[1].path;
          break;
        case '13':
          datas.newData = constantRoutes[9].children[0].meta.title;
          datas.urlData = window.location.origin + constantRoutes[9].children[0].path;
          break;
        default:
          datas.newData = 'home';
      }
    };
    function clickData() {
      switch (datas.inputData) {
        case '1':
          router.push({ name: 'SampleOrder' });
          break;
        case '2':
          router.push({ name: 'TestExecutionList' });
          break;
        case '3':
          router.push({ name: 'TestAllocation' });
          break;
        case '4':
          router.push({ name: 'ExperimentExcel' });
          break;
        case '5':
          router.push({ name: 'RecordReviewList' });
          break;
        case '6':
          router.push({ name: 'TestReport' });
          break;
        case '7':
          router.push({ name: 'TestItem' });
          break;
        case '8':
          router.push({ name: 'TestBaseList' });
          break;
        case '9':
          router.push({ name: 'MaterialClassification' });
          break;
        case '10':
          router.push({ name: 'ZSFX' });
          break;
        case '11':
          router.push({ name: 'MyMessage' });
          break;
        case '12':
          router.push({ name: 'MyAgency' });
          break;
        case '13':
          router.push({ name: 'UnqualifiedDisposition' });
          break;
        default:
          router.push({ path: '/home' });
          break;
      }
    }
    return {
      ...toRefs(datas),
      clickData,
      changeUrl
    };
  },
  created() {
    this.$message('this is home  !');
  }
};
</script>
