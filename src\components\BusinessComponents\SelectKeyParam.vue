<template>
  <!-- 正态分布图 - 选择关键参数 -->
  <!-- spc控制图 - 选择关键参数 -->
  <el-dialog
    :model-value="show"
    custom-class="custom-dialog"
    title="选择关键参数"
    width="60%"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
    @opened="handleOpened"
  >
    <div class="dialog-header">
      <div class="header-left">
        <el-input
          ref="inputRef"
          v-model="filterText"
          class="search"
          size="small"
          placeholder="请输入类目名称"
          prefix-icon="el-icon-search"
          clearable
          @keyup.enter="searchItem(filterText)"
          @clear="filterClear()"
        />
        <el-button type="primary" size="small" @click="searchItem(filterText)">查询</el-button>
      </div>
    </div>
    <div class="dialog-content">
      <el-row>
        <el-col :span="6">
          <div class="tree-container">
            <div class="tree-content">
              <el-tree
                ref="treeRef"
                :data="tree"
                node-key="id"
                :props="defaultProps"
                default-expand-all
                :expand-on-click-node="false"
                :highlight-current="true"
                draggable
                class="leftTree"
                @node-click="clickNode"
              >
                <template #default="{ node }">
                  <span>{{ node.label }}</span>
                </template>
              </el-tree>
            </div>
          </div>
        </el-col>
        <el-col :span="18">
          <div v-loading="loading" class="list-container">
            <el-radio-group v-model="selectedKeyParamName">
              <div v-for="(item, index) in newTreeDetail" :key="index" class="item-content">
                <div class="main">
                  <div class="title">{{ item.name }}</div>
                  <div class="item-list">
                    <span v-for="(list, index1) in item.capabilityparaVoList" :key="index1" class="item-box">
                      <span v-if="list.resulttype === '数值型'">
                        <el-radio :label="item.name + '--' + list.name" @change="changeSelectedKey(list)">{{
                          list.name
                        }}</el-radio>
                      </span>
                      <!-- <el-checkbox v-if="list.resulttype === '数值型'" v-model="list.checked" @change="changeItems(list)" /> -->
                      <span v-else>{{ list.name }}</span>
                    </span>
                  </div>
                </div>
              </div>
            </el-radio-group>
            <el-empty v-if="treeDetail.length === 0" :image="emptyImg" description="暂无数据" />
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="dialog-other">
      <el-row>
        <el-col :span="24">
          <div class="title">
            <label>选中的关键参数</label>
            <!-- <el-button size="small" @click="clear">清空</el-button> -->
          </div>
          <div v-if="selectedKeyParam.name" class="select-items">
            <el-tag :key="selectedKeyParam.name" type="info">
              {{ selectedKeyParamName }}
            </el-tag>
          </div>
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="dialogSuccess">确定选择</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, getCurrentInstance, inject, nextTick } from 'vue';
import { getCapabilityUplist } from '@/api/user';
import _ from 'lodash';
import emptyImg from '@/assets/img/empty-data.png';
// import { ElLoading } from 'element-plus'
// import { useRoute } from 'vue-router'
// import { formatPaginationList } from '@/utils/formatJson'

export default {
  name: 'SelectKeyParam',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    },
    tree: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'selectedData'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const lodash = inject('_');
    const detailList = ref(null);
    const datas = reactive({
      showDialog: props.show,
      filterText: '',
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      tags: [],
      inputRef: ref(),
      oldTags: [],
      treeDetail: [],
      newTreeDetail: [],
      loading: false,
      isRefresh: true,
      capabilityPara: [],
      selectedKeyParamName: '',
      selectedKeyParam: {}
    });

    watch(
      () => props.show,
      newValue => {
        // console.log(props)
        datas.showDialog = newValue;
        if (datas.isRefresh === false) {
          return false;
        }
        if (datas.showDialog) {
          nextTick(() => {
            datas.inputRef.focus();
          });
        }
        datas.tags = JSON.parse(JSON.stringify(props.data));
        datas.oldTags = JSON.parse(JSON.stringify(props.data));
        datas.oldTags = lodash.uniqBy(datas.oldTags, 'name');
        // if (newValue && props.tree && props.tree.length > 0) {
        //   proxy.getCapabilityList(props.tree[0].id, props.tree[0].materialCategoryCode)
        //   datas.isRefresh = false
        // }
      },
      { deep: true }
    );

    // 过滤树节点
    const treeRef = ref(null);
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    // 点击树节点
    const clickNode = (data, node) => {
      // console.log(node)
      // console.log(data.id)
      proxy.getCapabilityList(data.id, data.materialCategoryCode);
    };

    // 确定选择
    const dialogSuccess = () => {
      context.emit('selectedData', datas.selectedKeyParam);
      datas.showDialog = false;
      datas.isRefresh = true;
      context.emit('close', false);
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      datas.isRefresh = true;
      datas.selectedKeyParam = {};
      datas.selectedKeyParamName = '';
      context.emit('close', false);
    };
    // 全选
    const selectAll = arr => {
      // console.log('selectAll')
      if (arr && arr.length > 0) {
        datas.tags = [];
        arr.forEach(item => {
          const hasitem = _.filter(datas.oldTags, res => {
            res.closable = false;
            return res.id === item.id || item.id === res.sourceId;
          });
          // console.log(hasitem)
          if (hasitem.length >= 1) {
            item.checked = true;
            item.disabled = true;
          } else {
            item.checked = true;
            filterItem(item);
          }
          // datas.tags.push(item)
        });
      }
    };
    // 反选
    const selectNone = arr => {
      if (arr && arr.length > 0) {
        datas.tags = [];
        arr.forEach(item => {
          // console.log(item.checked)
          const hasitem = _.filter(datas.oldTags, res => {
            res.closable = false;
            return res.id === item.id || item.id === res.sourceId;
          });
          // console.log(hasitem)
          if (hasitem.length >= 1) {
            item.checked = true;
            item.disabled = true;
          } else if (item.checked) {
            item.checked = false;
          } else {
            item.checked = true;
            datas.tags.push(item);
          }
        });
      }
    };
    // changeCheckBox
    const changeCheckBox = (item, flag) => {
      if (item.disabled) {
        return false;
      }
      // console.log(item)
      item.checked = !item.checked;
      if (item.checked) {
        filterItem(item);
      } else {
        // lodash.remove(datas.tags, n => {
        //   return item.id === n.id
        // })
        datas.tags = [];
      }
    };
    // 数据过滤
    const filterItem = item => {
      item.sourceCapabilityType = 'Internal';
      item.newcapabilityparaVoList = [];
      if (item.capabilityparaVoList && item.capabilityparaVoList.length > 0) {
        item.capabilityparaVoList.forEach(cap => {
          const newItem1 = {
            sourceIdStr: cap.id,
            sourceNumber: cap.number,
            sourceparentIdStr: cap.capabilityid,
            sourceCapabilityDescription: cap.description,
            sourceName: cap.name,
            sourceCapabilityType: cap.sourceCapabilityType,
            experimentCategoryIdStr: cap.categoryid
          };
          const allItems = Object.assign(cap, newItem1);
          item.newcapabilityparaVoList.push(allItems);
        });
      }
      const newItem = {
        sourceIdStr: item.id,
        sourceNumber: item.number,
        sourceparentIdStr: item.capabilityid,
        sourceCapabilityDescription: item.description,
        sourceName: item.name,
        sourceCapabilityType: item.sourceCapabilityType,
        experimentCategoryIdStr: item.categoryid,
        parentid: '0',
        operationType: 1,
        status: 1,
        childList: item.newcapabilityparaVoList
      };
      const param = Object.assign(item, newItem);
      // datas.tags.push(param)
      datas.tags = param;
      datas.newTreeDetail.forEach(tree => {
        // if (tree.capabilityparaVoList && tree.capabilityparaVoList.length > 0) {
        //   tree.capabilityparaVoList.forEach(cpl => {
        //     cpl.checked = false
        //   })
        // }
        if (tree.id === param.id) {
          tree.checked = true;
          datas.capabilityPara = [];
        } else {
          tree.checked = false;
        }
      });
    };
    // 关闭tags
    const closeTag = tag => {
      // console.log(tag)
      datas.tags.splice(datas.tags.indexOf(tag), 1);
      datas.newTreeDetail.forEach(tree => {
        if (tree.id === tag.id) {
          tree.checked = false;
        }
      });
    };
    // 清空
    const clear = () => {
      datas.tags = [];
      if (datas.newTreeDetail && datas.newTreeDetail.length > 0) {
        datas.newTreeDetail.forEach(list => {
          const hasitem = _.filter(datas.oldTags, res => {
            res.closable = false;
            return res.id === list.id || list.id === res.sourceId;
          });
          // console.log(hasitem)
          if (hasitem.length >= 1) {
            list.checked = true;
            list.disabled = true;
          } else {
            list.checked = false;
            datas.tags = [];
          }
        });
      }
    };
    // searchItem
    const searchItem = value => {
      if (value) {
        datas.newTreeDetail = datas.treeDetail.filter(item => {
          return JSON.stringify(item).indexOf(value) !== -1;
        });
      } else {
        datas.newTreeDetail = datas.treeDetail;
      }
    };

    const filterClear = () => {
      datas.newTreeDetail = datas.treeDetail;
    };

    const changeItems = list => {
      // console.log(list)
      if (list.checked) {
        datas.tags.push(list);
      } else {
        lodash.remove(datas.tags, n => {
          return list.id === n.id;
        });
      }
      // console.log(datas.tags)
    };

    const changeSelectedKey = selectedKey => {
      // console.log(selectedKey)
      datas.selectedKeyParam = selectedKey;
      // console.log('selected raido group:')
      // console.log(datas.selectedKeyParamName)
    };

    const handleOpened = () => {
      if (props.tree && props.tree.length > 0) {
        proxy.getCapabilityList(props.tree[0].id, props.tree[0].materialCategoryCode);
        datas.isRefresh = false;
      }
    };

    return {
      ...toRefs(datas),
      emptyImg,
      searchItem,
      dialogSuccess,
      close,
      filterNode,
      selectAll,
      selectNone,
      clickNode,
      treeRef,
      closeTag,
      clear,
      changeCheckBox,
      detailList,
      filterItem,
      changeItems,
      changeSelectedKey,
      filterClear,
      handleOpened
    };
  },
  methods: {
    getCapabilityList(id, materialCategoryCode) {
      // 获取检测项目list
      const _this = this;
      _this.loading = true;
      getCapabilityUplist(id, materialCategoryCode).then(response => {
        // console.log(response.data.data)
        // console.log(_this.oldTags)
        _this.loading = false;
        if (response !== false && response.data.code === 200) {
          const { data } = response.data;
          _this.treeDetail = data;
          _this.newTreeDetail = data;
          // _this.newTreeDetail.forEach(item => {
          //   if (item.capabilityparaVoList.length > 0) {
          //     item.capabilityparaVoList.forEach(cpl => {
          //       const hasitem = _.filter(_this.oldTags, res => {
          //         return res.id === cpl.id
          //       })
          //       // console.log(hasitem)
          //       if (hasitem.length >= 1) {
          //         cpl.checked = true
          //       } else {
          //         cpl.checked = false
          //       }
          //     })
          //   }
          // })
          if (_this.filterText) {
            _this.searchItem(_this.filterText);
          }
        } else {
          setTimeout(() => {
            _this.loading = false;
          }, 1.5 * 500);
        }
      });
    }
  }
};
</script>
<style lang="scss">
@import '@/styles/dialog.scss';
</style>
<style lang="scss" scoped>
@import '@/styles/tree.scss';

.dialog-content {
  margin-bottom: 0;
  padding-bottom: 20px;
  .tree-container {
    .tree-content {
      height: calc(100vh - 540px);
      overflow-y: auto;
      padding-left: 0;
    }
  }
  .list-container {
    height: calc(100vh - 500px);
    overflow-y: auto;
    .el-radio-group {
      width: 100%;
    }
    .item-content {
      padding-bottom: 0 !important;
      .main {
        width: 100%;
        .title {
          padding-bottom: 8px;
        }
        .item-list {
          width: 100%;
          border-left: none;
          .item-box {
            height: 26px;
            margin-right: 20px !important;
            display: flex !important;
            align-items: center;
            margin-bottom: 0 !important;
          }
        }
      }
    }
  }
}
</style>
