@font-face {
  font-family: 'iconfont'; /* Project id 2599441 */
  src: url('iconfont.woff2?t=1710827406873') format('woff2'), url('iconfont.woff?t=1710827406873') format('woff'),
    url('iconfont.ttf?t=1710827406873') format('truetype');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.tes-live-fill:before {
  content: '\e67b';
}

.tes-money-circle-fill:before {
  content: '\e67c';
}

.tes-exchange-fill:before {
  content: '\e67d';
}

.tes-flag-fill:before {
  content: '\e67e';
}

.tes-hammer-fill:before {
  content: '\e67f';
}

.tes-flask-fill:before {
  content: '\e680';
}

.tes-layout-fill:before {
  content: '\e681';
}

.tes-file-mark-fill:before {
  content: '\e682';
}

.tes-home-fill:before {
  content: '\e683';
}

.tes-cloud-fill:before {
  content: '\e684';
}

.tes-contacts-fill:before {
  content: '\e685';
}

.tes-compass-fill:before {
  content: '\e686';
}

.tes-book-fill:before {
  content: '\e687';
}

.tes-file-list-fill:before {
  content: '\e688';
}

.tes-book-read-fill:before {
  content: '\e689';
}

.tes-calendar-check-fill:before {
  content: '\e68a';
}

.tes-bar-h-fill:before {
  content: '\e68b';
}

.tes-bar-fill:before {
  content: '\e66b';
}

.tes-shield-check-fill:before {
  content: '\e66c';
}

.tes-account-fill:before {
  content: '\e66d';
}

.tes-umbrella-fill:before {
  content: '\e66e';
}

.tes-pushpin-fill:before {
  content: '\e66f';
}

.tes-water-flash-fill:before {
  content: '\e670';
}

.tes-settings-fill:before {
  content: '\e671';
}

.tes-discuss-fill:before {
  content: '\e672';
}

.tes-pie-fill:before {
  content: '\e673';
}

.tes-auction-fill:before {
  content: '\e674';
}

.tes-sidebar-fill:before {
  content: '\e675';
}

.tes-alarm-fill:before {
  content: '\e676';
}

.tes-hotel-fill:before {
  content: '\e677';
}

.tes-reserved-fill:before {
  content: '\e678';
}

.tes-movie-fill:before {
  content: '\e679';
}

.tes-todo-fill:before {
  content: '\e67a';
}

.tes-todo:before {
  content: '\e66a';
}

.tes-charts:before {
  content: '\e669';
}

.tes-platform:before {
  content: '\e659';
}

.tes-histogram:before {
  content: '\e65a';
}

.tes-user1:before {
  content: '\e65b';
}

.tes-message1:before {
  content: '\e65e';
}

.tes-company:before {
  content: '\e65f';
}

.tes-nut:before {
  content: '\e660';
}

.tes-menu-flower:before {
  content: '\e661';
}

.tes-measuring-bottle:before {
  content: '\e663';
}

.tes-sample:before {
  content: '\e664';
}

.tes-home1:before {
  content: '\e665';
}

.tes-shield:before {
  content: '\e666';
}

.tes-server:before {
  content: '\e667';
}

.tes-Vector1:before {
  content: '\e658';
}

.tes-detail:before {
  content: '\e656';
}

.tes-shujucaiji1:before {
  content: '\e657';
}

.tes-icon-toolBar-comment:before {
  content: '\e655';
}

.tes-icon-toolBar-collect:before {
  content: '\e65c';
}

.tes-icon-toolBar-more:before {
  content: '\e65d';
}

.tes-icon-tool-pic:before {
  content: '\e662';
}

.tes-color-config:before {
  content: '\e654';
}

.tes-remain-num:before {
  content: '\e653';
}

.tes-new-add:before {
  content: '\e652';
}

.tes-week-num:before {
  content: '\e651';
}

.tes-yesterday-num:before {
  content: '\e650';
}

.tes-report-file:before {
  content: '\e64f';
}

.tes-report-send:before {
  content: '\e64e';
}

.tes-report-seal:before {
  content: '\e64d';
}

.tes-report-sign:before {
  content: '\e64c';
}

.tes-report-approval:before {
  content: '\e64b';
}

.tes-report-bianzhi:before {
  content: '\e64a';
}

.tes-record-review:before {
  content: '\e649';
}

.tes-test-exe:before {
  content: '\e648';
}

.tes-test-allocation:before {
  content: '\e647';
}

.tes-task-issued:before {
  content: '\e646';
}

.tes-task-manage:before {
  content: '\e645';
}

.tes-wen-juan-diao-cha:before {
  content: '\e644';
}

.tes-back-top:before {
  content: '\e643';
}

.tes-fault:before {
  content: '\e642';
}

.tes-success:before {
  content: '\e641';
}

.tes-batch-order:before {
  content: '\e640';
}

.tes-delete:before {
  content: '\e63f';
}

.tes-lists:before {
  content: '\e63e';
}

.tes-lists-expand:before {
  content: '\e668';
}

.tes-add:before {
  content: '\e63d';
}

.tes-icon-wrapper:before {
  content: '\e63c';
}

.tes-title:before {
  content: '\e63b';
}

.tes-move:before {
  content: '\e63a';
}

.tes-Vector:before {
  content: '\e600';
}

.tes-jiance:before {
  content: '\e639';
}

.tes-warning:before {
  content: '\e638';
}

.tes-clock:before {
  content: '\e637';
}

.tes-home:before {
  content: '\e636';
}

.tes-drag-icon:before {
  content: '\e634';
}

.tes-filter-select:before {
  content: '\e635';
}

.tes-zsfx:before {
  content: '\e630';
}

.tes-xxdb:before {
  content: '\e631';
}

.tes-gcdd:before {
  content: '\e632';
}

.tes-sysgl:before {
  content: '\e633';
}

.tes-dashboard:before {
  content: '\e607';
}

.tes-documentation:before {
  content: '\e608';
}

.tes-404:before {
  content: '\e609';
}

.tes-example:before {
  content: '\e60a';
}

.tes-guide:before {
  content: '\e60b';
}

.tes-email:before {
  content: '\e60c';
}

.tes-link:before {
  content: '\e60d';
}

.tes-international:before {
  content: '\e60e';
}

.tes-drag:before {
  content: '\e60f';
}

.tes-exit-fullscreen:before {
  content: '\e610';
}

.tes-education:before {
  content: '\e611';
}

.tes-fullscreen:before {
  content: '\e612';
}

.tes-message:before {
  content: '\e613';
}

.tes-eye:before {
  content: '\e614';
}

.tes-cxtes:before {
  content: '\e615';
}

.tes-eye-open:before {
  content: '\e616';
}

.tes-people:before {
  content: '\e617';
}

.tes-money:before {
  content: '\e618';
}

.tes-form:before {
  content: '\e619';
}

.tes-password:before {
  content: '\e61a';
}

.tes-tree-table:before {
  content: '\e61b';
}

.tes-user:before {
  content: '\e61c';
}

.tes-icon:before {
  content: '\e61d';
}

.tes-tab:before {
  content: '\e61e';
}

.tes-skill:before {
  content: '\e61f';
}

.tes-search:before {
  content: '\e620';
}

.tes-shopping:before {
  content: '\e621';
}

.tes-nested:before {
  content: '\e622';
}

.tes-zip:before {
  content: '\e623';
}

.tes-list:before {
  content: '\e624';
}

.tes-tree:before {
  content: '\e625';
}

.tes-size:before {
  content: '\e626';
}

.tes-theme:before {
  content: '\e627';
}

.tes-language:before {
  content: '\e628';
}

.tes-peoples:before {
  content: '\e629';
}

.tes-wechat:before {
  content: '\e62a';
}

.tes-table:before {
  content: '\e62b';
}

.tes-pdf:before {
  content: '\e62c';
}

.tes-qq:before {
  content: '\e62d';
}

.tes-star:before {
  content: '\e62e';
}

.tes-lock:before {
  content: '\e62f';
}

.tes-component:before {
  content: '\e601';
}

.tes-chart:before {
  content: '\e602';
}

.tes-clipboard:before {
  content: '\e603';
}

.tes-excel:before {
  content: '\e604';
}

.tes-edit:before {
  content: '\e605';
}

.tes-bug:before {
  content: '\e606';
}
