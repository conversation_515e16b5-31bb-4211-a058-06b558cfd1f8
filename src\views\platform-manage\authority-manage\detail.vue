<template>
  <!-- 权限管理 详情 -->
  <DetailLayout :main-offset-top="120">
    <template #page-header>
      <div class="header-flex flex-between">
        <div class="page-title">
          {{ detailData.permissionName }}
          <el-tag size="small" :type="detailData.status === 1 ? 'success' : 'info'">{{
            detailData.status === 1 ? '已启用' : '已停用'
          }}</el-tag>
        </div>
        <div class="btn-group">
          <el-button
            v-if="detailData.status === 1"
            icon="el-icon-remove"
            size="large"
            @click="forbiddenDetial(detailData)"
            @keyup.prevent
            @keydown.enter.prevent
            >禁用权限</el-button
          >
          <el-button
            type="primary"
            icon="el-icon-edit"
            size="large"
            @click="editDetial"
            @keyup.prevent
            @keydown.enter.prevent
            >编辑权限</el-button
          >
        </div>
      </div>
      <el-descriptions :model="detailData">
        <el-descriptions-item label="所属分组：">
          {{ detailData.permissiontreeName || detailData.permissiontreeId }}
        </el-descriptions-item>
        <el-descriptions-item label="描述：">
          {{ detailData.content || '--' }}
        </el-descriptions-item>
      </el-descriptions>
    </template>
    <div class="page-main">
      <div class="panel-headline">
        <div class="title">资源配置</div>
        <div class="btn-group">
          <el-button @click="closeDetail">取 消</el-button>
          <el-button type="primary" @click="saveDetail">保 存</el-button>
        </div>
      </div>
      <div class="panel-content">
        <el-table
          ref="tableRef"
          :key="tableKey"
          v-loading="listLoading"
          :data="list"
          fit
          border
          height="auto"
          :default-expand-all="false"
          row-key="id"
          :tree-props="treeProps"
          class="dark-table base-table self-table"
        >
          <el-table-column label="资源KEY" prop="key" show-overflow-tooltip>
            <template #default="{ row }">
              <div>{{ row.key || '--' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="资源名称" prop="title" :width="200" show-overflow-tooltip>
            <template #default="{ row }">
              <div>{{ row.title || '--' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="资源类型" prop="type" :width="180">
            <template #default="{ row }">
              <span>{{ filterType(row.type) || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column :width="70">
            <template #default="{ row }">
              <el-checkbox
                v-model="row.selected"
                :true-label="1"
                :false-label="0"
                @change="handleSelectionChange(row)"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- <div class="footer-btn">
        <el-button @click="closeDetail">取 消</el-button>
        <el-button type="primary" @click="saveDetail">保 存</el-button>
      </div> -->
    </div>
    <!-- 编辑权限 -->
    <edit
      :show="showAuthorityDialog"
      :data="detailData"
      :title="addTitle"
      :tree="dialogTreeData"
      @close="closeAuthorityDialog"
      @setInfo="closeAuthorityDialog"
    />
  </DetailLayout>
</template>

<script>
import { reactive, toRefs, getCurrentInstance, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
// import { getNameByid, getPermissionBtn } from '@/utils/common'
// import { useStore } from 'vuex'
import { getLoginInfo } from '@/utils/auth';
import _ from 'lodash';
// import { checkPermissionList } from '@/api/permission'
// import { permissionTypeList } from '@/utils/permissionList'
import { useRoute } from 'vue-router';
import router from '@/router';
import Edit from './add.vue';
import DetailLayout from '@/components/DetailLayout';
import { drageHeader } from '@/utils/formatTable';
import {
  detailPermission,
  enablePermission,
  getMenuList,
  editMenuPermission,
  getPermissionTreeList
} from '@/api/platform-management';

export default {
  name: 'AuthorityDetial',
  components: { Edit, DetailLayout },
  setup() {
    const { proxy } = getCurrentInstance();
    // const store = useStore().state
    const route = useRoute();
    const datas = reactive({
      accountId: getLoginInfo().accountId,
      menuIds: [],
      listLoading: false,
      list: [],
      tableKey: 0,
      detailData: {},
      showAuthorityDialog: false,
      addTitle: 'edit',
      activeName: '1',
      dialogTreeData: [],
      treeProps: {
        children: 'children',
        hasChildren: true // 'hasChildren'
      },
      isFirst: false,
      currentSelectList: [],
      tableRef: ref()
    });

    // 编辑
    const editDetial = () => {
      datas.showAuthorityDialog = true;
    };
    // 禁用
    const forbiddenDetial = (row, flag) => {
      ElMessageBox({
        title: '',
        message: '权限禁用后，权限下资源面临不受控风险，是否继续？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          enablePermission({ id: row.id, status: 0 }).then(res => {
            if (res !== false) {
              ElMessage.success('禁用成功！');
              datas.detailData.status = 0;
            }
          });
        })
        .catch(() => {});
    };
    // 关闭编辑弹出框
    const closeAuthorityDialog = data => {
      datas.showAuthorityDialog = false;
      if (data) {
        router.push({
          query: {
            ...route.query,
            permissiontreeName: data.permissiontreeName,
            permissiontreeId: data.permissiontreeId
          }
        });
      }
      proxy.getDetailList(route.query);
    };
    // 取消详情
    const closeDetail = () => {
      router.push({
        path: '/platform-manage/authority-manage',
        query: {
          permissiontreeName: route.query.permissiontreeName,
          permissiontreeId: route.query.permissiontreeId
        }
      });
    };
    // 保存详情
    const saveDetail = () => {
      const params = {
        permissionId: datas.detailData.id,
        selectedMenuId: datas.menuIds,
        isPublic: 0 // 是否公共权限(0:否，1:是)
      };
      editMenuPermission(params).then(emp => {
        if (emp !== false) {
          router.push({
            path: '/platform-manage/authority-manage',
            query: {
              permissiontreeName: route.query.permissiontreeName,
              permissiontreeId: route.query.permissiontreeId
            }
          });
        }
      });
    };
    // 选择资源
    const handleSelectionChange = item => {
      if (item) {
        if (item.selected === 1) {
          datas.menuIds.push(item.id);
          // 结合产品要求，取消子父相关功能
          // filterUpdateMenus(item.children, true)
        } else {
          const newItems = _.filter(datas.menuIds, n => {
            return n !== item.id;
          });
          datas.menuIds = newItems;
          // filterUpdateMenus(item.children, false)
        }
      }
      datas.menuIds = _.uniq(datas.menuIds);
    };
    // 修改权限过滤
    const filterUpdateMenus = (items, flag) => {
      if (items && items.length > 0) {
        items.forEach(m => {
          // datas.tableRef.toggleRowSelection(m, true)
          if (flag) {
            datas.menuIds.push(m.id);
            m.selected = 1;
          } else {
            const newItems = _.filter(datas.menuIds, n => {
              return n !== m.id;
            });
            m.selected = 0;
            datas.menuIds = newItems;
          }
          if (m.children && m.children.length > 0) {
            filterUpdateMenus(m.children, flag);
          }
        });
      }
    };

    // 过滤这样类型
    const filterType = type => {
      // 1：一级菜单 2：子菜单 3：页面 4：按钮/表单/字段
      const map = {
        1: '一级菜单',
        2: '子菜单',
        3: '页面',
        4: '按钮/表单/字段'
      };
      return map[type];
    };
    // 过滤勾选权限
    const filterMenus = menus => {
      datas.isFirst = true;
      if (menus && menus.length > 0) {
        menus.forEach(m => {
          m.hasChildren = false;
          if (m.selected === 1) {
            datas.menuIds.push(m.id);
            // datas.tableRef.toggleRowSelection(m, true)
          }
          if (m.children && m.children.length > 0) {
            m.hasChildren = true;
            filterMenus(m.children);
          }
        });
      }
      return menus;
    };

    return {
      ...toRefs(datas),
      forbiddenDetial,
      drageHeader,
      closeAuthorityDialog,
      editDetial,
      closeDetail,
      saveDetail,
      handleSelectionChange,
      filterType,
      filterMenus,
      filterUpdateMenus
    };
  },
  created() {
    this.getDetailList(this.$route.query);
    this.getList(this.$route.query.id);
    this.getTree();
  },
  methods: {
    getDetailList(param) {
      const _this = this;
      detailPermission(param).then(res => {
        if (res !== false) {
          const { data } = res.data;
          _this.detailData = data;
          _this.detailData.permissiontreeName = _this.$route.query.permissiontreeName;
        }
      });
    },
    getList(permissionId) {
      var that = this;
      var param = {
        permissionId: permissionId
      };
      getMenuList(param).then(res => {
        if (res !== false) {
          const { data } = res.data;
          that.list = data;
          that.$nextTick(() => {
            // that.isFirst = false
            that.filterMenus(data);
          });
        }
      });
    },
    getTree() {
      var _this = this;
      getPermissionTreeList({ typeId: 0 }).then(response1 => {
        if (response1 !== false) {
          const data = response1.data.data;
          _this.dialogTreeData = data;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.self-table.el-table) {
  .el-table__body {
    .el-table__row {
      td:first-child {
        .cell {
          display: flex;
        }
      }
    }
  }
  .el-table__body-wrapper {
    max-height: calc(100vh - 330px);
    overflow-y: auto;
  }
}

.panel-headline {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.footer-btn {
  text-align: right;
  margin-top: 20px;
}
</style>
