<template>
  <!-- 试验管理-委外管理 -->
  <ListLayout :has-search-panel="false">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" @submit.prevent>
        <el-form-item prop="param">
          <div style="width: 42vw">
            <CombinationQuery
              :field-list="listColumns"
              field-tip="样品编号/委外编号/委托机构"
              @get-query-info="getQueryInfo"
              @reset-search="reset"
            />
          </div>
        </el-form-item>
      </el-form>
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="16">
          <el-radio-group v-model="radioData" size="small" @change="changeRadio">
            <el-radio-button label="全部" />
            <el-radio-button label="待上传" />
            <el-radio-button label="已上传" />
            <el-radio-button label="已作废" />
          </el-radio-group>
        </el-col>
        <el-col :span="8">
          <TableColumnView binding-menu="OutsourcingManagement" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>

    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table format-height-table base-table"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <template v-for="(item, index) in listColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : getColWidth(item.columnWidth)"
          :min-width="item.isMinWidth ? getColWidth(item.columnWidth) : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <TableColumnContentRenderer
              :column="item"
              :row="row"
              format="yyyy-MM-dd"
              :status="filterUploadStatus(row[item.fieldKey])"
              @link-click="handleLinkJump"
            />
          </template>
        </el-table-column>
      </template>

      <el-table-column
        label="操作"
        :min-width="colWidth.operationSingle"
        prop="operation"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <div class="operation-wrapper">
            <span
              v-if="row.entrustStatus === 1 || row.entrustStatus === 2"
              class="blue-color margin-right-10"
              @click="handleLinkJump('check', row)"
              >查看</span
            >
            <span v-else class="blue-color margin-right-10" @click="handleLinkJump('edit', row)">编辑</span>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <Pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />

    <template #other>
      <!--证书上传-->
      <!-- <dialog-upload
        :visible="certificateDialogVisible"
        :lists="lists"
        @close="close"
      /> -->
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance, computed } from 'vue';
import router from '@/router/index.js';
import Pagination from '@/components/Pagination';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { mapGetters, useStore } from 'vuex';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import _ from 'lodash';
import { checkPermissionList } from '@/api/permission';
import { permissionTypeList } from '@/utils/permissionList';
import { filterSampleUnitToName } from '@/utils/formatJson';
import ListLayout from '@/components/ListLayout';
import { colWidth } from '@/data/tableStyle';
import { getColWidth } from '@/utils/func/customTable';
import { getExternalEntrustList } from '@/api/task-management';
import { ElMessageBox } from 'element-plus';
import CombinationQuery from '@/components/CombinationQuery';
import TableColumnView from '@/components/TableColumnView';
import TableColumnContentRenderer from '@/components/TableColumnView/TableColumnContentRenderer';

export default {
  name: 'OutsourcingManagement',
  components: { Pagination, ListLayout, CombinationQuery, TableColumnView, TableColumnContentRenderer },
  setup() {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const editFrom = ref(null);
    const otherForm = reactive({
      accountId: getLoginInfo().accountId,
      multipleSelection: [],
      tableRef: ref(),
      activeName: '0',
      certificateDialogVisible: false,
      showS: false,
      mangeList: [],
      lists: [],
      formInline: {
        param: '',
        tableQueryParamList: [],
        assignedEndTime: '',
        assignedStartTime: '',
        startTime: '',
        endTime: '',
        ownerId: '',
        prodType: '',
        entrustStatus: ''
      },
      searchForm: {
        prodType: '',
        ownerId: '',
        rukuDateRange: '',
        xiadaDateRange: ''
      },
      types: store.user.materialList,
      userOptions: store.common.nameList,
      copyUserOptions: store.common.nameList,
      listColumns: [],
      list: [],
      content: '',
      radioData: '全部',
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        sort: 'DESC'
      },
      tableKey: 0,
      listLoading: false,
      total: 0
    });
    function onSubmit() {
      proxy.getList();
    }

    function reset() {
      editFrom.value.resetFields();
      otherForm.formInline = {
        param: '',
        tableQueryParamList: [],
        assignedEndTime: '',
        assignedStartTime: '',
        startTime: '',
        endTime: '',
        ownerId: '',
        mate_type: '',
        entrustStatus: ''
      };
      otherForm.radioData = '全部';
      otherForm.searchForm = {
        prodType: '',
        ownerId: '',
        rukuDateRange: '',
        xiadaDateRange: ''
      };
      otherForm.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        sort: 'DESC'
      };
      proxy.getList();
    }

    const search = () => {
      otherForm.showS = !otherForm.showS;
      if (otherForm.activeName === '0') {
        otherForm.activeName = '1';
      } else {
        otherForm.activeName = '0';
      }
    };

    const sortChange = data => {
      const { prop, order } = data;
      otherForm.listQuery.orderBy = prop;
      if (order === 'ascending') {
        otherForm.listQuery.isAsc = true;
      } else if (order === 'descending') {
        otherForm.listQuery.isAsc = false;
      } else {
        otherForm.listQuery.isAsc = null;
      }
    };

    const inputValue = data => {
      if (typeof data === 'string') {
        otherForm.content = data;
      } else {
        otherForm.content = '';
      }
    };
    // 过滤试验员
    const filterUserList = val => {
      if (val) {
        const list = [];
        otherForm.copyUserOptions.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        otherForm.userOptions = list;
      } else {
        otherForm.userOptions = otherForm.copyUserOptions;
      }
    };

    // 选择入库数据
    const handleSelectionChange = val => {
      otherForm.multipleSelection = val;
      // if (val.length > 0) {
      //   val.forEach((v, i) => {
      //     if (i === 10) {
      //       ElMessage.warning('最多选择10条数据')
      //       otherForm.tableRef.toggleRowSelection(v, false)
      //     }
      //   })
      // }
    };

    // 上传委托证书
    const handleUpload = row => {
      if (row.entrustStatus === 1) {
        ElMessageBox({
          title: '重复上传证书',
          message: '是否要重复上传(会覆盖已上传的证书文件）?',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'warning'
        })
          .then(() => {
            hanleOpenDialog(row);
          })
          .catch(() => {
            // ElMessage.info('已取消删除!')
          });
      } else {
        hanleOpenDialog(row);
      }
    };

    function hanleOpenDialog(row) {
      const itt = JSON.parse(JSON.stringify(row));
      otherForm.lists = [];
      otherForm.lists.push(itt);
      otherForm.certificateDialogVisible = true;
    }

    const close = refreshList => {
      if (refreshList) {
        proxy.getList();
      }
      otherForm.certificateDialogVisible = false;
      otherForm.lists = [];
    };

    const handleLinkJump = (fieldKey, row) => {
      switch (fieldKey) {
        // 查看样品详情
        case 'secSampleNum': {
          router.push({
            name: 'OutsourcingManagementSampleDetail',
            query: {
              orderId: row.orderId,
              sampleId: row.sampleId
            }
          });
          break;
        }

        case 'edit': {
          router.push({
            name: 'OutsourcingManagementEdit',
            query: {
              id: row.id
            }
          });
          break;
        }

        case 'code':
        case 'check': {
          router.push({
            name: 'OutsourcingManagementDetail',
            query: {
              id: row.id
            }
          });
          break;
        }
      }
    };

    const tenantType = computed({
      get: () => store.user.tenantInfo.type
    });

    // #region 上传功能

    const showUploadDialog = () => {};

    // #endregion

    // #region 特殊样式列

    function handleRouteJump(row, styleContent) {
      const queryParams = {};
      styleContent.params.forEach(item => {
        if (item.name) {
          if (row[item.code] && (item.value === 0 || !item.value)) {
            queryParams[item.name] = row[item.code];
          } else {
            queryParams[item.name] = item.value;
          }
        } else {
          if (row[item.code] && (item.value === 0 || !item.value)) {
            queryParams[item.code] = row[item.code];
          } else {
            queryParams[item.code] = item.value;
          }
        }
      });
      router.push({
        path: styleContent.path,
        query: queryParams
      });
    }

    // 过滤上传状态
    const filterUploadStatus = status => {
      // 上传状态  0.待上传 1.已上传 2.已作废
      if (status === 0 || status === 1 || status === 2) {
        const classMap = {
          0: ['warning', '待上传'],
          1: ['success', '已上传'],
          2: ['info', '已作废']
        };
        return {
          type: classMap[status][0],
          content: classMap[status][1]
        };
      } else {
        return {
          type: 'info',
          content: '--'
        };
      }
    };

    // #endregion

    const changeRadio = value => {
      const param = {
        全部: '',
        待上传: 0,
        已上传: 1,
        已作废: 2
      };
      otherForm.formInline.entrustStatus = param[value].toString();
      proxy.getList();
    };

    const getQueryInfo = info => {
      otherForm.formInline.param = info.param;
      otherForm.formInline.tableQueryParamList = info.tableQueryParamList;
      proxy.getList();
    };

    const onUpdateColumns = columns => {
      otherForm.tableKey = otherForm.tableKey + 1;
      otherForm.listColumns = columns;
    };

    return {
      filterUserList,
      getPermissionBtn,
      drageHeader,
      handleUpload,
      formatDate,
      filterSampleUnitToName,
      getNameByid,
      inputValue,
      handleSelectionChange,
      close,
      sortChange,
      editFrom,
      ...toRefs(otherForm),
      search,
      onSubmit,
      reset,
      colWidth,
      tenantType,
      getColWidth,
      handleLinkJump,
      showUploadDialog,
      handleRouteJump,
      filterUploadStatus,
      changeRadio,
      getQueryInfo,
      onUpdateColumns
    };
  },
  computed: {
    ...mapGetters(['tenantGroup'])
  },
  created() {
    this.getList();
    this.getnamelist();
    // 刷新列表
    this.bus.$on('reloadTestAllocationList', msg => {
      this.getList();
    });
    this.copyUserOptions = JSON.parse(JSON.stringify(this.userOptions));
  },
  methods: {
    getList(data) {
      const _this = this;
      _this.listLoading = true;
      if (data && data !== undefined) {
        _this.listQuery.page = data.page;
        _this.listQuery.limit = data.limit;
      }
      _this.formInline.param = _this.formInline.param.trim();
      const param = Object.assign(_this.formInline, _this.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      getExternalEntrustList(param).then(res => {
        if (res !== false && res.data.code === 200) {
          const { data } = res.data;
          _this.list = data.list;
          _this.total = data.totalCount;
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
    },
    getnamelist() {
      checkPermissionList(permissionTypeList.sampleOwner).then(res => {
        this.mangeList = res.data.data;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.blue-color {
  color: $tes-primary;
  cursor: pointer;
}
::v-deep(.el-table__body td.is-center .cell) {
  justify-content: center;
}
</style>
