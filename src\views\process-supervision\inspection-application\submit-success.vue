<template>
  <el-dialog
    v-model="showDialog"
    custom-class="submit-success"
    width="400px"
    :show-close="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <el-result :icon="icon" :title="title" :sub-title="subTitle">
      <template #extra>
        <el-button type="primary" size="small" @click="back">{{ backTitle }}</el-button>
      </template>
    </el-result>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch } from 'vue';
import router from '@/router/index.js';
// import _ from 'lodash'
// import { ElMessage, ElMessageBox } from 'element-plus'
// import { useRoute } from 'vue-router'
// import { useStore } from 'vuex'
// import { getLoginInfo } from '@/utils/auth'

export default {
  name: 'SubmitSuccess',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    icon: {
      type: String,
      default: 'success' // success / warning / info / error
    },
    title: {
      type: String,
      default: '提交成功'
    },
    subTitle: {
      type: String,
      default: '申请单已提交'
    },
    backTitle: {
      type: String,
      default: '返回列表'
    },
    backUrl: {
      type: String,
      default: ''
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    // const lodash = inject('_')
    // const store = useStore().state
    const datas = reactive({
      showDialog: false,
      url: ''
    });

    watch(
      () => props.show,
      newValue => {
        if (newValue) {
          datas.showDialog = newValue;
          datas.url = props.backUrl;
        }
      },
      { deep: true }
    );

    // 确定选择
    const dialogSuccess = () => {
      datas.showDialog = false;
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };

    const back = () => {
      if (datas.url) {
        router.push(datas.url);
      } else {
        history.go(-1);
      }
    };

    return { ...toRefs(datas), dialogSuccess, close, back };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss">
.submit-success {
  .el-dialog__header {
    padding: 0;
  }
  .el-dialog__body {
    padding: 0;
    .el-result {
      padding: 15px;
    }
  }
}
</style>
