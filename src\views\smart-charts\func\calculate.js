import _ from 'lodash';
import { mathAdd, mathDivide, mathMultiply, mathSubtract, mathToInteger } from '@/utils/calc/basicMath';

// #region Common Function

/**
 *
 * @param {*} count 横坐标轴label总数
 * @param {*} powNumber 指数
 * @param {*} dividingNumber 横坐标不用设置间隔的分界数
 */
export function getAxisLabelInterval(count, powNumber = 0.25, dividingNumber = 10) {
  return count > dividingNumber ? Math.ceil(Math.pow(count, powNumber)) : 0;
}

/**
 * 计算坐标轴间距，最小值，最大值
 * @param {*} min 最小值
 * @param {*} max 最大值
 * @param {*} count 数量
 * @param {*} splitNumber 分隔数, 默认为10
 * @returns
 */
export function getAxisInterval(min, max, count, splitNumber = 10) {
  let axisMin = Math.round(Number(min));
  let axisMax = Math.round(Number(max));
  if (min === max) {
    return {
      axisMax: axisMax,
      axisMin: axisMin,
      intervalCount: 1,
      axistInterval: axisMax === axisMin ? Math.ceil(Math.sqrt(Math.abs(axisMax) + 1)) : axisMax - axisMin
    };
  } else {
    let intervalCount = count > splitNumber ? splitNumber : count;
    let axistInterval = Math.ceil((max - min) / intervalCount);
    axisMax = axisMin + axistInterval * intervalCount;

    if (max < 1 && max > -1) {
      const fixAxisMax = Number(Number(max).toFixed(5));
      const fixAxisMin = Number(Number(min).toFixed(5));
      const maxToInteger = mathToInteger(Number(fixAxisMax));
      const tempMin = Math.floor(maxToInteger.magnification * Number(fixAxisMin));
      const tempInterval = Math.ceil((maxToInteger.enlargedNumber - tempMin) / intervalCount);
      axisMin = Math.floor(tempMin / maxToInteger.magnification);
      axistInterval = tempInterval / maxToInteger.magnification;
      axisMax = axisMin + axistInterval * intervalCount;
      if (intervalCount > 0) {
        while (axisMax < fixAxisMax) {
          axisMax = mathAdd(axisMax, axistInterval);
          intervalCount++;
        }
      }
    }
    return {
      axisMax,
      axisMin,
      intervalCount,
      axistInterval
    };
  }
}

export function splitArray(rawArray, subGroupCount) {
  const resultArray = [];
  let index = 0;
  if (rawArray.length > subGroupCount) {
    const divisibleNumber = rawArray.length - (rawArray.length % subGroupCount);
    while (index < divisibleNumber) {
      const subArray = [];
      for (let i = index; i < index + subGroupCount; i++) {
        subArray.push(rawArray[i < rawArray.length ? i : rawArray.length - 1]);
      }
      resultArray.push(subArray);
      index += subGroupCount;
    }
    return resultArray;
  } else {
    return [rawArray];
  }
}

/**
 * 给二维数组内的数组排序
 * @param rawArray
 * @returns
 */
export function sort2DArray(rawArray) {
  const sortedArray = [];
  for (let i = 0; i < rawArray.length; i++) {
    sortedArray.push(rawArray[i].sort((a, b) => a - b));
  }
  return sortedArray;
}

/**
 * 计算范围值，数组的最大值减去最小值 (数组已从小到大排序)
 * @param sortedArray
 * @returns
 */
export function getRangeValue(sortedArray) {
  const rangeValueArray = [];
  for (let i = 0; i < sortedArray.length; i++) {
    rangeValueArray.push(mathSubtract(sortedArray[i][sortedArray[i].length - 1], sortedArray[i][0]));
  }
  return rangeValueArray;
}

export function getSampleNumAverageArray(sortedArray) {
  const averageArray = [];
  for (let i = 0; i < sortedArray.length; i++) {
    averageArray.push(getAverageOfArray(sortedArray[i]));
  }
  return averageArray;
}

export function getAverageOfArray(array) {
  return _.sum(array) / array.length;
}

export function getSumOfArray(array) {
  return _.sum(array);
}

export function getFormatArray(constNum, length) {
  const result = [];
  for (let i = 0; i < length; i++) {
    result.push(constNum);
  }
  return result;
}

export function getStandardDeviation(array, average = null) {
  var avg = average ?? _.sum(array) / array.length;
  return Math.sqrt(_.sum(_.map(array, i => Math.pow(i - avg, 2))) / (array.length - 1));
}

export function getMedian(sortedArray) {
  if (sortedArray.length % 2 === 0) {
    return _.sum([(sortedArray.length - 2) / 2, sortedArray.length / 2]) / 2;
  } else {
    return sortedArray[(sortedArray.length - 1) / 2];
  }
}

export function getModeValue(array) {
  if (!array.length) return 0;
  if (array.length === 1) return array[0];
  let mostValue = 0;
  let maxNum = 0;
  array.reduce((accumulator, currentNum) => {
    accumulator[currentNum] ? (accumulator[currentNum] += 1) : (accumulator[currentNum] = 1);
    if (accumulator[currentNum] > maxNum) {
      maxNum = accumulator[currentNum];
      mostValue = currentNum;
    }
    return accumulator;
  }, {});
  return mostValue;
}

export function getVarianceValue(array, average = null) {
  const avg = average ?? _.sum(array) / array.length;
  return Math.sqrt(
    array.reduce((accumulator, currentNum) => accumulator + Math.pow(avg - currentNum, 2), 0) / array.length
  );
}

// #endregion

// #region SPC Function

export function getXBarUCL(xAverageAverage, rAverage, n) {
  const controlChart = getControlChart(n);
  return mathAdd(xAverageAverage, mathMultiply(controlChart.A2, rAverage));
}

export function getRangeUCL(rangeAverage, n) {
  const controlChart = getControlChart(n);
  return mathMultiply(controlChart.D4, rangeAverage);
}

export function getXBarLCL(xAverageAverage, rAverage, n) {
  const controlChart = getControlChart(n);
  return mathSubtract(xAverageAverage, mathMultiply(controlChart.A2, rAverage));
}

export function getRangeLCL(rangeAverage, n) {
  const controlChart = getControlChart(n);
  return mathMultiply(controlChart.D3, rangeAverage);
}

export function getXUCL(xAverage, rAverage, n) {
  const controlChart = getControlChart(n);
  return mathAdd(xAverage, mathMultiply(controlChart.E2, rAverage));
}

export function getXLCL(xAverage, rAverage, n) {
  const controlChart = getControlChart(n);
  return mathSubtract(xAverage, mathMultiply(controlChart.E2, rAverage));
}

function getControlChart(sampleCount) {
  const result = {
    A2: -1,
    A3: -1,
    D3: -1,
    D4: -1,
    E2: -1
  };
  switch (sampleCount) {
    case 2:
      result.A2 = 1.88;
      result.A3 = 2.659;
      result.D3 = 0;
      result.D4 = 3.267;
      result.E2 = 2.66;
      break;
    case 3:
      result.A2 = 1.023;
      result.A3 = 1.954;
      result.D3 = 0;
      result.D4 = 2.575;
      result.E2 = 1.772;
      break;
    case 4:
      result.A2 = 0.729;
      result.A3 = 1.628;
      result.D3 = 0;
      result.D4 = 2.282;
      result.E2 = 1.457;
      break;
    case 5:
      result.A2 = 0.577;
      result.A3 = 1.427;
      result.D3 = 0;
      result.D4 = 2.114;
      result.E2 = 1.29;
      break;
    case 6:
      result.A2 = 0.483;
      result.A3 = 1.287;
      result.D3 = 0;
      result.D4 = 2.004;
      result.E2 = 1.184;
      break;
    case 7:
      result.A2 = 0.419;
      result.A3 = 1.182;
      result.D3 = 0.076;
      result.D4 = 1.924;
      result.E2 = 1.109;
      break;
    case 8:
      result.A2 = 0.373;
      result.A3 = 1.099;
      result.D3 = 0.136;
      result.D4 = 1.864;
      result.E2 = 2.847;
      break;
    case 9:
      result.A2 = 0.337;
      result.A3 = 1.032;
      result.D3 = 0.184;
      result.D4 = 1.816;
      result.E2 = 1.01;
      break;
    case 10:
      result.A2 = 0.308;
      result.A3 = 1.032;
      result.D3 = 0.223;
      result.D4 = 1.777;
      result.E2 = 0.975;
      break;
    default:
      result.A2 = -1;
      result.A3 = -1;
      result.D3 = -1;
      result.D4 = -1;
      result.E2 = -1;
      break;
  }

  return result;
}

// #endregion

// #region NormalDistribution Fuction

// 正态分布密度函数(cumulative = false)
export function getNormalDistribution(x, mean, stdDev) {
  return Math.exp(-0.5 * Math.pow((x - mean) / stdDev, 2)) / (Math.sqrt(2 * Math.PI) * stdDev);
}

// 分组数
export function getNormDistGroup(count, min, max) {
  const idealGroup = Math.ceil(Math.sqrt(count));
  const groupDistance = mathDivide(mathSubtract(max, min), idealGroup);
  const minRange = mathSubtract(min, mathDivide(groupDistance, 2));
  let maxRange = minRange + mathMultiply(idealGroup, groupDistance);
  let groupCount = idealGroup + 1;
  while (maxRange < max) {
    maxRange = mathAdd(maxRange, groupDistance);
    groupCount++;
  }
  return {
    idealGroup,
    groupDistance,
    minRange,
    groupCount
  };
}

// 获取分组数组
export function getNormDistRangeArray(minRange, groupDistance, groupCount) {
  const rangeArray = [];
  for (let i = 0; i < groupCount; i++) {
    rangeArray.push(mathAdd(minRange, mathMultiply(groupDistance, i)));
  }
  return rangeArray;
}

// 获取频数数组
export function getNormDistFreqArray(array, minRange, groupDistance, groupCount) {
  const freqArray = Array(groupCount).fill(0);
  let index = 0;
  for (let i = 0; i < array.length; i++) {
    if (array[i] <= minRange) {
      freqArray[0] = freqArray[0] + 1;
    } else {
      index = Math.ceil((array[i] - minRange) / groupDistance);
      freqArray[index] = freqArray[index] + 1;
    }
  }
  return freqArray;
}

export function getNormDistArray(rangeArray, mean, stdDev) {
  const normDistArray = Array(rangeArray.length);
  for (let i = 0; i < rangeArray.length; i++) {
    normDistArray[i] = getNormalDistribution(rangeArray[i], mean, stdDev).toFixed(6);
  }

  return normDistArray;
}

// #endregion

// #region CPK值计算方法

// Cpk = Cp(1-|Ca|)
// Ca(Capability of Accuracy): 制程准确度; 反应的是位置关系即集中趋势
// Cp(Capability of Precision): 制程精密度,反应的是散步关系即离散趋势
// 计算Cpk时，取样数据至少应有30组数据，而且数据要具有一定代表性

// Ca计算公式 Ca = (xAverage - C) / (T/2)
// xAverage是样品的平均值, C是规格中心, T是规格公差(规格上限减下限), sigma是标准差

/**
 * Ca(制程准确度)的计算公式
 * @param {*} xAverage 样品平均值
 * @param {number} centerLine 规格中心
 * @param {number} usl 规格上限
 * @param {number} lsl 规格下限
 * @returns Ca 制程准确度
 */
export function getCapabilityOfAccuracy(xAverage, centerLine, usl, lsl) {
  return (xAverage - centerLine) / ((usl - lsl) / 2);
}

// Cp(制程精密度)的计算公式

// 1.只有规格上限和规格中心 Cpu = (USl - xAverage) / 3 sigma
export function getUpperCapabilityOfPrecision(xAverage, usl, sigma) {
  return (usl - xAverage) / (3 * sigma);
}

// 2.只有规格下限和规格中心 Cpl = (xAverage - LSL) / 3 sigma
export function getLowerCapabilityOfPrecision(xAverage, lsl, sigma) {
  return (xAverage - lsl) / (3 * sigma);
}

// 3.双边规格和规格中心 Cpu = (USI - xAverage) / 3 sigma
export function getCapabilityOfPrecision(usl, lsl, sigma) {
  return (usl - lsl) / (3 * sigma);
}

// Cpk = Cp(1-|Ca|)

/**
 * 根据Cp(制程精密度)和Ca(制程准确度)计算Cpk的值
 * @param {number} cp
 * @param {number} ca
 * @returns
 */
export function getCpk(cp, ca) {
  return cp * (1 - Math.abs(ca));
}

/**
 * 获取cpk评级
 * @param {number} cpk
 * @returns cpk level
 */
export function getCpkLevel(cpk) {
  if (cpk >= 1.67) {
    return 'A+';
  } else if (cpk < 1.67 && cpk >= 1.33) {
    return 'A';
  } else if (cpk < 1.33 && cpk >= 1.0) {
    return 'B';
  } else if (cpk < 1.0 && cpk >= 0.67) {
    return 'C';
  } else {
    return 'D';
  }
}

// #endregion

// #region 单值移动极差图（X- MR）计算方法

/**
 * 获取分组内移动极差数组
 * @param {*} rawArray
 * @param {*} subGroupCount
 * @returns
 */
export function getMRArray(rawArray, subGroupCount = 2) {
  let endToEndArray = [];
  let index = 0;
  if (rawArray.length > subGroupCount) {
    // 例如原数组[1, 2, 3, 4, 5, 6]
    // subGroupCount为2的时候：[[1, 2], [2, 3], [3, 4], [4, 5], [5, 6]]
    // subGroupCount为3的时候：[[1, 2, 3], [3, 4, 5], [5, 6]]
    while (index < rawArray.length) {
      const subArray = [];
      for (let i = index; i < index + subGroupCount; i++) {
        subArray.push(rawArray[i < rawArray.length ? i : rawArray.length - 1]);
      }
      endToEndArray.push(subArray);
      index = index + subGroupCount - 1;
    }
  } else if (rawArray.length > 0 && rawArray.length <= subGroupCount) {
    endToEndArray = [rawArray];
  } else {
    endToEndArray = [];
  }
  return endToEndArray.length > 0 ? getRangeValue(sort2DArray(endToEndArray)) : 0;
}

// #endregion
