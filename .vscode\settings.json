{"editor.tabSize": 2, "editor.guides.bracketPairs": "active", "files.autoSave": "off", "git.confirmSync": false, "workbench.startupEditor": "newUntitledFile", "editor.suggestSelection": "first", "editor.acceptSuggestionOnCommitCharacter": false, "css.lint.propertyIgnoredDueToDisplay": "ignore", "editor.quickSuggestions": {"other": true, "comments": true, "strings": true}, "files.associations": {"editor.snippetSuggestions": "top"}, "editor.formatOnType": false, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}