import { colWidth } from '@/data/tableStyle';

/**
 * 属性说明
 * field: 字段名
 * name: 字段显示名
 * colwidth: 列宽(可以为tableStyle中的值，也可是数字)
 * type: 列的类型。 0: 默认类型
 * isNotQuery: 列是否可以为非查询字段， 0: 不是，1: 是
 * routeType: 路由调整类型 0:检验申请-申请单号
 * styleContent: 样式字段解析
 * order: 排序字段，排序顺序
 * checkbox: 当前视图中是否显示
 * isHide: 是否为隐藏列
 * isOrder: 是否需要排序
 */

/**
 * 列的类型type
 * 0: 默认显示
 * 1: 带链接的列
 * 2: 显示带单位的生产数量的列
 * 3: 用标签显示枚举的列
 */

export const orderFieldList = [
  {
    field: 'productionOrderNo',
    name: '生产订单号',
    colWidth: colWidth.productionOrderNo,
    type: 0,
    isNotQuery: 0,
    routeType: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isMinWidth: true,
    isOrder: 1
  },
  {
    field: 'parentNo',
    name: '生产制令号',
    colWidth: colWidth.productionOrderNo,
    type: 0,
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isMinWidth: true
  },
  {
    field: 'materialNo',
    name: '物料编号',
    colWidth: colWidth.material,
    type: 0,
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 1,
    isMinWidth: true
  },
  {
    field: 'materialDesc',
    name: '物料名称',
    colWidth: colWidth.description,
    type: 0,
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 1,
    isMinWidth: true
  },
  {
    field: 'projectName',
    name: '项目名称',
    colWidth: colWidth.description,
    type: 0,
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isMinWidth: true
  },
  {
    field: 'materialGroup',
    name: '物料分组',
    colWidth: colWidth.materialGroup,
    type: 0,
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isMinWidth: true
  },
  {
    field: 'productionProcedure',
    name: '生产工序',
    colWidth: colWidth.process,
    type: 0,
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isMinWidth: false
  },
  {
    field: 'productionStation',
    name: '生产机台',
    colWidth: colWidth.name,
    type: 0,
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isMinWidth: false
  },
  {
    field: 'productionQuantity',
    name: '生产数量',
    colWidth: colWidth.productionQuantity,
    type: 2,
    isNotQuery: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isMinWidth: false
  },
  {
    field: 'submitStatus',
    name: '送检状态',
    colWidth: colWidth.status,
    type: 3,
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isFixed: true,
    isMinWidth: false
  },
  {
    field: 'productStatus',
    name: '生产状态',
    colWidth: colWidth.status,
    type: 3,
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 0,
    isFixed: true,
    isMinWidth: false
  }
];

export const orderTagList = [
  {
    field: 'productStatus',
    dataMap: {
      0: ['warning', '作废'],
      1: ['success', '正常']
    }
  },
  {
    field: 'submitStatus',
    dataMap: {
      0: ['warning', '待送检'],
      1: ['success', '已送检']
    }
  }
];

export function handleTag(field, value) {
  if (field && orderTagList.length > 0) {
    const enumIndex = orderTagList.findIndex(item => item.field === field);
    if (enumIndex === -1) {
      return ['', value];
    } else {
      return orderTagList[enumIndex].dataMap[value] || ['', value];
    }
  } else {
    return ['', value];
  }
}
