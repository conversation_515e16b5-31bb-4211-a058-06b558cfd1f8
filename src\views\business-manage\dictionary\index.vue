<template>
  <!-- 字典维护 -->
  <ListLayout :has-quick-query="true" :has-button-group="getPermissionBtn('addDictionary') ? true : false">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="code">
          <el-input
            v-model="formInline.condition"
            v-trim
            v-focus
            placeholder="请输入编号/名称/描述"
            class="ipt-360"
            prefix-icon="el-icon-search"
            size="large"
            clearable
            @keyup.enter="getList"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="getList">查询</el-button>
          <el-button size="large" @click="reset"> 重置 </el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        type="primary"
        icon="el-icon-plus"
        size="large"
        @click="handleAdd"
        @keyup.prevent
        @keydown.enter.prevent
        >新增字典</el-button
      >
    </template>
    <template #radio-content>
      <el-radio-group v-model="dictionaryType" size="small" style="float: left" @change="changeStatus">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button v-for="(value, key, index) in dictionaryTypeJSON" :key="index" :label="key">{{
          value
        }}</el-radio-button>
      </el-radio-group>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="tableList"
      size="medium"
      fit
      border
      height="auto"
      style="width: auto"
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column label="字典编号" prop="code" :width="colWidth.name" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.code || '' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="字典名称" prop="name" :width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap blue" @click="handleCheck(row)">{{ row.name || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="字典类型" prop="dictionaryType" :width="colWidth.name">
        <template #default="{ row }">
          <div class="nowrap">{{ dictionaryTypeJSON[row.dictionaryType] || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="字典描述" prop="description" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.description || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="getPermissionBtn('checkDictionary') || getPermissionBtn('editDictionary')"
        label="操作"
        prop="cz"
        :width="140"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <span v-if="getPermissionBtn('checkDictionary')" class="blue-color" @click="handleCheck(row)">查看</span>
          <span v-if="getPermissionBtn('editDictionary')" class="blue-color" @click="handleEdit(row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
    <template #other>
      <Dictionarydrawer
        :type="drawerType"
        :dictionary-type="dictionaryType"
        :drawer="drawerVisiable"
        :row-detail="rowDetail"
        @close="closeDrawer"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs } from 'vue';
import store from '@/store';
import Pagination from '@/components/Pagination';
import Dictionarydrawer from './dictionary-drawer.vue';
import ListLayout from '@/components/ListLayout';
import { formatDate } from '@/utils/formatTime';
// import { getDay, getMonthDay } from '@/utils/getDay'
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { getDictionaryList } from '@/api/dictionary';
import { getDictionary } from '@/api/user';
import { drageHeader } from '@/utils/formatTable';
import {} from '@/api/messageAgent';
import { getLoginInfo } from '@/utils/auth';
import { colWidth } from '@/data/tableStyle';

export default {
  name: 'DictionaryList',
  components: { Dictionarydrawer, Pagination, ListLayout },
  setup() {
    // const _ = inject('_')
    // const { proxy } = getCurrentInstance()
    const state = reactive({
      tableRef: ref(),
      drawerType: '', // 抽屉类型
      drawerVisiable: false, // 抽屉的显示和隐藏
      listLoading: false,
      formInline: {
        condition: '' // 搜索的关键字
      },
      orderby: 'desc',
      listQuery: {
        limit: 20,
        page: 1
      },
      dictionaryType: '',
      dictionaryTypeJSON: {},
      tableList: [],
      nameList: store.state.common.nameList,
      tableSelected: [], // 表格选中的值
      dialogFormVisible: false,
      total: 0,
      rowDetail: {}, // 选中的行数据
      currentAccountId: getLoginInfo().accountId
    });

    const tableKey = ref(0);
    const getList = query => {
      const params = {
        condition: state.formInline.condition.trim(),
        orderby: state.orderby,
        dictionaryType: state.dictionaryType
      };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        params.page = state.listQuery.page.toString();
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoading = true;
      getDictionaryList(params).then(res => {
        state.listLoading = false;
        if (res) {
          state.total = res.data.data.totalCount;
          state.tableList = res.data.data.list;
        }
      });
    };
    const getDictionType = () => {
      getDictionary(1).then(res => {
        if (res) {
          res.data.data.dictionaryoption.forEach(item => {
            state.dictionaryTypeJSON[item.code] = item.name;
          });
        } else {
          state.dictionaryTypeJSON = {
            1: '业务字典',
            2: '系统字典',
            3: '模板字典'
          };
        }
        getList();
      });
    };
    getDictionType();
    const reset = () => {
      state.formInline.condition = '';
      state.listQuery = {
        limit: 20,
        page: 1
      };
      getList();
    };
    const sortChange = data => {
      const { order } = data;
      if (order === 'ascending') {
        state.orderby = 'asc';
      } else if (order === 'descending') {
        state.orderby = 'desc';
      }
      getList();
    };
    const handleSizeChange = val => {
      state.listQuery.limit = val;
      getList();
    };
    const closeDrawer = value => {
      state.drawerVisiable = false;
      if (value.isResh) {
        getList();
      }
    };
    // 查看
    const handleCheck = row => {
      state.drawerType = 'check';
      if (getPermissionBtn('checkDictionary')) {
        state.drawerVisiable = true;
      }
      state.rowDetail = row;
    };
    // 编辑
    const handleEdit = row => {
      state.drawerType = 'edit';
      state.drawerVisiable = true;
      state.rowDetail = row;
    };
    // 新增
    const handleAdd = () => {
      state.drawerType = 'add';
      state.drawerVisiable = true;
    };
    const changeStatus = value => {
      state.dictionaryType = value;
      getList();
    };
    return {
      ...toRefs(state),
      getPermissionBtn,
      changeStatus,
      reset,
      sortChange,
      closeDrawer,
      handleAdd,
      drageHeader,
      getNameByid,
      handleSizeChange,
      getNamesByid,
      handleCheck,
      handleEdit,
      formatDate,
      getList,
      tableKey,
      colWidth
    };
  },
  computed: {},
  created() {}
};
</script>
<style lang="scss" scoped></style>
