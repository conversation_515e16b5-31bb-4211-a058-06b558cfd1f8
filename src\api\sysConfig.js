import request from '@/utils/request';
// import qs from 'qs'
// const Headers = {
//   'Content-Type': 'application/json; charset=utf-8'
// }
const formHeaders = {
  'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
};

// 获取当前登录用户的配置信息
export function getSysConfigInfo(data) {
  return request({
    url: '/api-user/user/sysconfig/info',
    method: 'post',
    data
  });
}
// 保存当前登录用户的配置信息
export function saveSysConfigInfo(data) {
  return request({
    url: '/api-user/user/sysconfig/save',
    method: 'post',
    data
  });
}
// 登出
export function loginOutApi(id) {
  return request({
    url: `/api-user/user/sysloginlog/logout/${id}`,
    method: 'post'
  });
}
// 统计在线时长
export function heartApi(id) {
  return request({
    url: `/api-user/user/sysloginlog/heart/${id}`,
    method: 'post'
  });
}
// 获取二维码
export function getQrCodeApi(type) {
  return request({
    url: `/api-uaa/social/login/dingtalk?req_type=${type}`,
    method: 'get'
  });
}
// 判断是否绑定
export function getBandApi(query) {
  return request({
    url: `/api-uaa/social/dingtalk/callback?code=${query.code}&state=${query.state}`,
    method: 'get'
  });
}
// 绑定钉钉
export function dingTalkBindApi(data) {
  return request({
    url: `/api-uaa/social/bind/account`,
    method: 'post',
    data
  });
}
// 获取绑定的钉钉
export function getDingTalkApi(data) {
  return request({
    url: `/api-uaa/social/socialSys/query`,
    method: 'post',
    data
  });
}
// 解除绑定钉钉
export function removeDTApi(data) {
  return request({
    url: `/api-uaa/social/unBind/account`,
    method: 'post',
    data
  });
}
// 解除绑定微信
export function removeWXApi(data) {
  return request({
    url: `/api-uaa/social/wechatmp/unBind`,
    method: 'post',
    data
  });
}
export function getTokenApi(data) {
  return request({
    url: `/api-uaa/oauth/token`,
    method: 'post',
    data
  });
}

// 签名记录
export function getSignHis(data) {
  return request({
    url: `/api-user/user/sysuser/users/getSignHis`,
    method: 'get',
    headers: formHeaders,
    params: data
  });
}
// 上传系统截图
export function uploadFilePicture(data) {
  return request({
    url: `/api-orders/screenshotRecord/save`,
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data
  });
}

// 上传系统截图
export function getPictureHis(data) {
  return request({
    url: `/api-orders/screenshotRecord/findByRelevancyKey`,
    method: 'post',
    data
  });
}
// 是否有未查看的截图
export function screenshotRecord(data) {
  return request({
    url: `/api-orders/screenshotRecord/hasNotSeen`,
    headers: formHeaders,
    method: 'get',
    params: data
  });
}
// 删除记录
export function deletePicture(id) {
  return request({
    url: `/api-orders/screenshotRecord/delete?ids=${id}`,
    method: 'delete'
  });
}
// chat 获取 conversation_id
export function conversations(data) {
  return request({
    url: `http://*************/api/conversations`,
    method: 'get',
    data
  });
}
