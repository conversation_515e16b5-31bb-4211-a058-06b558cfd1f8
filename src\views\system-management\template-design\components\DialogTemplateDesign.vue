<template>
  <!-- 新增模板、编辑模板 -->
  <el-dialog
    v-model="dialogShow"
    :title="dialogTitle[dialogType]"
    :close-on-click-modal="false"
    :width="500"
    @close="handleClose"
  >
    <el-form
      v-if="dialogShow"
      ref="formRef"
      v-loading="dialogLoading"
      :model="formData"
      label-position="right"
      label-width="100px"
    >
      <!-- <el-form-item
        label="模板编号："
        prop="templateNum"
        :rules="{ required: true, message: '请输入模板编号', trigger: 'change' }"
      >
        <el-input v-model="formData.templateNum" maxlength="100" placeholder="请输入模板编号" clearable />
      </el-form-item> -->
      <el-form-item
        label="模板名称："
        prop="name"
        :rules="{ required: true, message: '请输入模板名称', trigger: 'change' }"
      >
        <el-input v-model="formData.name" maxlength="100" placeholder="请输入模板名称" clearable />
      </el-form-item>
      <el-form-item
        label="类型："
        prop="templateType"
        :rules="{ required: true, message: '请选择类型', trigger: 'change' }"
      >
        <el-select v-model="formData.templateType" placeholder="请选择类型" style="width: 100%">
          <el-option v-for="(val, key) in templateTypeJSON" :key="key" :label="val" :value="key" />
        </el-select>
      </el-form-item>
      <div class="template-online">
        <SvgIcon icon-class="template-online-edit" :width="218" :height="74" />
        <div class="text">
          <span class="text-title">在线设计</span>
          <span>可视化界面，轻松编辑报告模板</span>
        </div>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
// getCurrentInstance
import router from '@/router/index.js';
import { reactive, ref, toRefs, watch } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import SvgIcon from '@/components/SvgIcon';
// Api
import { saveOrUpdate } from '@/api/templateDesign';
export default {
  name: 'DialogTemplateDesign',
  components: { SvgIcon },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    templateType: {
      type: Object,
      default: function () {
        return {};
      }
    },
    detailInfo: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      dialogType: '', // 弹出窗类型
      dialogTitle: {
        create: '新增模板',
        update: '编辑模板',
        check: '查看模板'
      },
      dialogLoading: false, // 弹出窗loading
      templateTypeJSON: {}, // 模板类型
      formData: {}, // 表单数据
      dialogShow: false,
      formRef: ref()
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.dialogType = props.type;
        state.templateTypeJSON = props.templateType;
        if (props.type == 'add') {
          state.formData = {};
        } else {
          state.formData = JSON.parse(JSON.stringify(props.detailInfo));
        }
      }
    });
    const onSubmit = () => {
      state.formRef.validate(async valid => {
        if (valid) {
          state.dialogLoading = true;
          const reponse = await saveOrUpdate(state.formData).finally((state.dialogLoading = false));
          if (reponse) {
            if (state.dialogType === 'create') {
              state.dialogShow = false;
              router.push({
                path: '/systemManagement/templateDesignDetail',
                query: {
                  type: state.dialogType,
                  id: reponse.data.data.id
                }
              });
            } else {
              context.emit('closeDialog', true);
            }
          }
        } else {
          return false;
        }
      });
      // context.emit('closeDialog');
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog');
    };
    return {
      ...toRefs(state),
      onSubmit,
      handleClose,
      formatDate,
      colWidth,
      drageHeader
    };
  }
};
</script>
<style lang="scss" scoped>
.template-online {
  padding: 0 32px 0 0;
  display: flex;
  flex-direction: row;
  position: relative;
  justify-content: flex-end;
  border-radius: 8px;
  overflow: hidden;
  user-select: none;
  border: 1px solid #97d8b8;
  background: linear-gradient(90deg, #97d8b8 0%, #c7eedb 45%, #eef9f2 100%);
  .svg-icon {
    position: absolute;
    top: -60px;
    left: 5px;
  }
  .text {
    font-size: 12px;
    gap: 8px;
    padding: 18px 0;
    display: flex;
    align-items: flex-end;
    flex-direction: column;
  }
  .text-title {
    font-size: 18px;
    font-weight: 600;
    color: #006644;
  }
  .text {
    color: #61be9d;
  }
}
</style>
