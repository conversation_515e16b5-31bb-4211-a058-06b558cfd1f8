<template>
  <div class="tes-home-header-contant">
    <div class="left-header">
      <Logo v-if="sysConfigInfo.layout === 1" :collapse="false" />
      <!-- <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" /> -->
      <ProductServe :show="sysConfigInfo.layout === 1" />
      <breadcrumb id="breadcrumb-container" class="breadcrumb-container" />
    </div>
    <div class="right-header">
      <span class="icon-ai" @click="handleIntelligent">
        <img v-if="getPermissionBtn('IntelligentAI')" src="@/assets/img/icon-ai.svg" alt="" />
      </span>
      <el-select
        v-model="tenantId"
        size="small"
        class="mr-5"
        placeholder="请选择租户"
        popper-class="select-tenant"
        @change="changeTenantId"
      >
        <el-option v-for="item in tenantGroupList" :key="item.tenantId" :label="item.clientName" :value="item.tenantId">
          <span class="el-icon-office-building">&nbsp;</span>
          <span>{{ item.clientName }}</span>
        </el-option>
      </el-select>
      <div v-if="getPermissionBtn('BusinessTour')" class="svg-config" @click="handleGuideFlow">
        <SvgIcon icon-class="shape" :width="16" :height="16" />
      </div>
      <el-popover
        v-if="getPermissionBtn('ThemeSetting')"
        v-model:visible="visible"
        placement="bottom"
        :width="320"
        :offset="24"
        trigger="click"
        popper-class="color-config-popover"
      >
        <template #reference>
          <div class="svg-config">
            <span class="icon iconfont tes-settings-fill" />
          </div>
        </template>
        <template #default>
          <div class="content-config">
            <div class="theme-config">
              <p>主题色</p>
              <div class="content">
                <div
                  v-for="(color, index) in themeColors"
                  :key="color"
                  :style="{ background: color.color }"
                  class="color-list"
                  @click="selectColor(color, index)"
                >
                  <span v-if="index === sysConfigInfo.themecolor" class="icon el-icon-check" />
                </div>
              </div>
            </div>
            <el-divider />
            <div class="theme-config">
              <p>背景色</p>
              <div class="content">
                <div
                  v-for="colorItem in colorList"
                  :key="colorItem"
                  :style="{ background: colorItem }"
                  class="color-list colorBackground"
                  @click="handleChangeColor(colorItem)"
                >
                  <span v-if="colorItem === sysConfigInfo.backgroundColor" class="icon el-icon-check" />
                </div>
              </div>
            </div>
            <el-divider />
            <div class="theme-config">
              <p>导航模式</p>
              <el-radio-group v-model="sysConfigInfo.layout" class="radio-config" @change="selectMenuConfig">
                <el-radio-button v-for="item in menuConfig" :key="item.id" :label="item.id">
                  <img :src="item.icon" alt="icon" />
                  <span class="label el-icon-check" />
                  <div class="item-text">{{ item.value }}</div>
                </el-radio-button>
              </el-radio-group>
            </div>
            <el-divider />
            <div class="theme-config theme-font">
              <p>字体大小</p>
              <el-slider
                v-model="sysConfigInfo.fontsize"
                show-stops
                :min="12"
                :max="18"
                :step="2"
                :show-tooltip="false"
                :marks="fontSizeMarks"
                @change="selectFontSizeConfig"
              />
            </div>
            <div class="config-btn">
              <el-button size="small" plain @click="saveSysConfigInfos">应用设置</el-button>
            </div>
          </div>
        </template>
      </el-popover>
      <div class="message-main">
        <div v-if="getPermissionBtn('delMaeeageBtn')">
          <el-badge :value="messageNum" :max="99" is-dot class="item" :class="messageNum === 0 ? 'noNumber' : ''">
            <el-button icon="icon iconfont tes-message1" circle @click="messageInfo(0)" />
          </el-badge>
        </div>
        <div v-if="getPermissionBtn('handleAgencyBtn')">
          <el-badge :value="messageNum1" :max="99" is-dot class="item" :class="messageNum1 === 0 ? 'noNumber' : ''">
            <el-button icon="icon iconfont tes-calendar-check-fill" circle @click="messageInfo(1)" />
          </el-badge>
        </div>
      </div>
      <el-divider direction="vertical" />
      <el-dropdown trigger="click" @command="handleCommand">
        <div class="user-name">
          <span class="name-icon no-select">{{ accountStr }}</span>
          <span class="name-txt no-select">{{ nickname || accountId }}</span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item v-if="getPermissionBtn('ChangePassword')" command="1">修改密码</el-dropdown-item>
            <el-dropdown-item command="2">登 出</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <!-- 修改密码-弹出框 -->
    <el-dialog
      v-model="submitPWDDialog"
      title="修改密码"
      width="480px"
      :close-on-click-modal="false"
      @close="submitPWDDialog = false"
    >
      <el-form ref="pwdRef" :model="pwdFrom" :rules="pwdrules" size="small" label-position="right" label-width="120px">
        <el-form-item label="原密码：" prop="oldpwd">
          <el-input v-model="pwdFrom.oldpwd" type="password" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="新密码：" prop="newpwd">
          <el-input v-model="pwdFrom.newpwd" type="password" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="确认密码：" prop="submitpwd">
          <el-input v-model="pwdFrom.submitpwd" type="password" placeholder="请输入" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="canclePWD">取 消</el-button>
          <el-button type="primary" @click="submitPWDSuccess">保 存</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 消息框 -->
    <msg />
  </div>
  <DialogIntelligentService :dialog-visiable="dialogService" @closeDialog="closeServiceDialog" />
  <DrawerGuideFlowService :drawer-visible="drawerGuideFlowVisible" @closeDrawer="closeGuideFlowDrawer" />
</template>

<script>
import { getCurrentInstance, reactive, toRefs, ref, inject, nextTick } from 'vue';
import { getLoginId, setLoginId, getLoginInfo, setHasWebSocket, setSysConfig } from '@/utils/auth';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { makePy } from '@/utils/chinese-to-str';
import Breadcrumb from '@/components/Breadcrumb';
import { ElMessage, ElNotification } from 'element-plus';
import { getMessageNum } from '@/api/messageAgent';
import router from '@/router';
import { mapGetters, mapState } from 'vuex';
import { setThemeColor, setBackgroundColor, setBackgroundColor2, getLIMSConfig } from '@/utils/auth';
import { changePWD } from '@/api/login';
// import Hamburger from '@/components/Hamburger'
import store from '@/store';
import { formatTenantId } from '@/utils/formatJson';
import Msg from '@/components/Message';
import SvgIcon from '@/components/SvgIcon';
import { getSysConfigInfo, saveSysConfigInfo, loginOutApi, heartApi } from '@/api/sysConfig';
import { setRem1 } from '@/utils/setRem';
// import TopSidebar from '@/layout/components/Sidebar/TopSidebar.vue'
import ProductServe from './ProductServe.vue';
import Logo from '@/layout/components/Sidebar/Logo.vue';
import Watermark from '@/utils/watermark';
// import { saveSysLoginLog } from '@/api/login-log'
import { getPlatformConfigList, getPlatformUrlParams } from '@/api/platform';
import DialogIntelligentService from '@/components/ThirdParty/DialogIntelligentService.vue';
import DrawerGuideFlowService from '@/components/ThirdParty/DrawerGuideFlow.vue';
import packageJSON from 'element-plus/package.json';
import icon_menu_on from '@/assets/img/icon_menu_on.png';
import icon_menu_off from '@/assets/img/icon_menu_off.png';
import tinycolor from 'tinycolor2';

export default {
  name: 'TesHeader',
  components: {
    Msg,
    Breadcrumb,
    SvgIcon,
    ProductServe,
    Logo,
    DialogIntelligentService,
    DrawerGuideFlowService
  },
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const websocket = inject('$wsWebsocket');
    // 密码输入检验
    const validatePassword = (rule, value, callback) => {
      var re = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^]{6,16}$/;
      var result = re.test(datas.pwdFrom.newpwd);
      if (!value) {
        callback(new Error('请输入新密码'));
      } else if (datas.pwdFrom.newpwd === datas.pwdFrom.oldpwd) {
        callback(new Error('新密码与原密码不能一样，请重新输入新密码'));
      } else if (!result) {
        callback(new Error('新密码需同时包含大小写、数字和6位以上，请重新输入新密码'));
      } else {
        callback();
      }
    };
    const validateSubmitPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入确认密码'));
      } else if (datas.pwdFrom.newpwd !== datas.pwdFrom.submitpwd) {
        callback(new Error('与新密码不一致，请重新输入新密码'));
      } else {
        callback();
      }
    };

    const datas = reactive({
      accountId: '',
      sliderValue: 0,
      dialogService: false,
      drawerGuideFlowVisible: false,
      accountStr: '',
      messageNum: 0,
      messageNum1: 0,
      visible: false,
      timer: null, // 统计在线时长定时器
      isDot: false,
      isDot1: false,
      tenantId: getLoginInfo().tenantId,
      nickname: getLoginInfo().nickname,
      tenantGroupList: JSON.parse(localStorage.getItem('tenantGroup')),
      submitText: '',
      chalk: '',
      theme: '#00b678',
      themeFlag: true,
      themeColors: [
        {
          color: '#00b678',
          tesBoder: '#80D9C5',
          bg: '#E6F8F4',
          menuHover: '#E6F8F4',
          secondary: '#4DCAAE',
          light: '#B3E9DC'
        },
        {
          color: '#0077DB',
          tesBoder: '#80BBED',
          bg: '#E5F1FB',
          menuHover: '#E5F1FB',
          secondary: '#4DA0E6',
          light: '#B3D6F4'
        },
        {
          color: '#00AE66',
          tesBoder: '#80D7B3',
          bg: '#E5F7F0',
          menuHover: '#E5F7F0',
          secondary: '#4DC694',
          light: '#B3E7D1'
        },
        {
          color: '#5588EE',
          tesBoder: '#AAC3F7',
          bg: '#EEF3FD',
          menuHover: '#EEF3FD',
          secondary: '#88ACF3',
          light: '#CCDBFA'
        }
      ],
      currentColorIndex: 0,
      menuConfig: [
        { id: 0, value: '侧边', icon: icon_menu_on },
        { id: 1, value: '顶部', icon: icon_menu_off }
      ],
      currentMenuIndex: 0,
      fontSizeMarks: {
        12: '较小',
        14: '标准',
        16: '较大',
        18: '大'
      },
      colorList: ['#fff', '#F0F2F5', '#CDD0D6'],
      colorListJson: {
        '#fff': '#f0f2f5',
        '#F0F2F5': '#d8d9dc',
        '#CDD0D6': '#b8bbc0'
      },
      colorSelect: ref(),
      fontSizeConfig: [
        { id: 0, label: '标准', value: 14 },
        { id: 1, label: '较大', value: 16 },
        { id: 2, label: '最大', value: 18 }
      ],
      currentFontSizeIndex: 0,
      version: packageJSON.version,
      popoverWidth: 160,
      submitPWDDialog: false,
      pwdFrom: {
        oldpwd: '',
        newpwd: '',
        submitpwd: ''
      },
      pwdrules: {
        oldpwd: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
        newpwd: [{ required: true, trigger: 'blur', validator: validatePassword }],
        submitpwd: [{ required: true, trigger: 'blur', validator: validateSubmitPassword }]
      },
      pwdRef: ref(),
      messageStrList: [],
      sysConfigInfo: {
        layout: 0,
        themecolor: 0,
        fontsize: 14,
        backgroundColor: '#fff',
        backgroundColor2: '#f0f2f5'
      },
      platformList: []
    });
    datas.accountId = getLoginInfo().accountId;
    const name = getLoginInfo().username;
    datas.accountStr = makePy(name.charAt(0))[0].toUpperCase();
    store.commit('user/SET_TENANT_INFO', formatTenantId(datas.tenantId));
    // console.log('formatTenantId(datas.tenantId)', formatTenantId(datas.tenantId))
    // store.state.user.tenantInfo = formatTenantId(datas.tenantId)
    // proxy.tenantInfo = formatTenantId(datas.tenantId)

    const messageInfo = flag => {
      if (flag === 0) {
        router.push({ name: 'MyMessage', query: { status: 0 } });
      } else {
        router.push({ name: 'MyAgency', query: { status: 0 } });
      }
    };
    // 退出登录
    const handleCommand = type => {
      if (type === '2') {
        // 登出关闭websocket
        if (websocket) {
          setHasWebSocket(false);
          websocket.close();
        }
        Watermark.remove();
        loginOut();
      } else {
        datas.submitPWDDialog = true;
        datas.pwdFrom = {
          oldpwd: '',
          newpwd: '',
          submitpwd: ''
        };
        // ElMessage.info('该功能待开放！')
      }
    };
    const removeTimer = () => {
      if (datas.timer) {
        clearInterval(datas.timer);
        datas.timer = null;
      }
    };
    const loginOut = () => {
      const loginId = getLoginId();
      if (loginId) {
        loginOutApi(loginId).then(res => {
          if (res) {
            setLoginId('');
            removeTimer();
            proxy.logout();
          }
        });
      } else {
        removeTimer();
        proxy.logout();
      }
    };
    const loginHeart = () => {
      const loginId = getLoginId();
      if (loginId) {
        datas.timer = setInterval(() => {
          heartApi(loginId).then(res => {});
        }, 600000);
      } else {
        removeTimer();
      }
    };
    loginHeart();
    // 选择颜色
    const selectColor = (color, index) => {
      datas.currentColorIndex = index;
      datas.sysConfigInfo.themecolor = index;
      setThemeColor(color);
      datas.theme = color.color;
      // proxy.themeChange(color.color)
      proxy.menuBgChange(color);
    };
    // 选择菜单配置
    const selectMenuConfig = menu => {
      // datas.currentMenuIndex = index
      datas.sysConfigInfo.layout = menu;
      store.dispatch('settings/changeSetting', {
        key: 'sidebarMode',
        value: menu === 0 ? 'vertical' : 'horizontal'
      });
    };
    // 选择字体大小
    const selectFontSizeConfig = font => {
      // datas.currentFontSizeIndex = font
      datas.sysConfigInfo.fontsize = font;
      setRem1(font);
    };
    // 租户切换
    const changeTenantId = async id => {
      // const loginId = getLoginId()
      const flag = await store.dispatch('user/changeTenant', id);
      const flag2 = await store.dispatch('permission/getcheckPermissionBtn');
      const flag3 = await store.dispatch('permission/getLIMSConfig');
      if (flag && flag2 && flag3) {
        router.push(getLIMSConfig().VITE_HOME_PAGE);
        setTimeout(() => {
          window.location.reload();
        }, 500);
        loginHeart();
      }
    };
    // 修改密码-保存
    const submitPWDSuccess = () => {
      datas.pwdRef.validate(valid => {
        if (valid) {
          const param = {
            id: datas.accountId,
            newPassword: datas.pwdFrom.newpwd,
            oldPassword: datas.pwdFrom.oldpwd
          };
          changePWD(param).then(res => {
            // console.log(res)
            if (res !== false) {
              datas.submitPWDDialog = false;
              datas.pwdRef.clearValidate();
              ElMessage.success('密码修改成功,请重新登录');
              loginOut();
            } else {
              ElMessage.error('帐号密码不正确');
            }
          });
        }
      });
    };
    // 修改密码-取消
    const canclePWD = () => {
      datas.submitPWDDialog = false;
      datas.pwdRef.clearValidate();
    };

    const getPlatformList = () => {
      getPlatformConfigList().then(res => {
        if (res) {
          datas.platformList = res.data.data;
        } else {
          ElMessage.warning('获取平台列表失败!');
        }
      });
    };

    const jumpToPlatform = platform => {
      if (platform) {
        getPlatformUrlParams(platform.id).then(res => {
          if (res) {
            const { url, ciphertext } = res.data.data;
            if (url && ciphertext) {
              const params = encodeURIComponent(ciphertext);
              const fullUrl =
                url.substring(url.length - 1) === '/'
                  ? `${url.substring(0, url.length - 1)}?ciphertext=${params}`
                  : `${url}?ciphertext=${params}`;
              window.open(fullUrl, '_blank');
            }
          } else {
            ElMessage.warning('获取平台跳转参数失败!');
          }
        });
      }
    };
    // 打开智能聊天
    const handleIntelligent = () => {
      datas.dialogService = true;
    };

    // 打开引导流程
    const handleGuideFlow = () => {
      datas.drawerGuideFlowVisible = true;
    };
    const handleChangeColor = value => {
      datas.sysConfigInfo.backgroundColor = value;
      datas.sysConfigInfo.backgroundColor2 = datas.colorListJson[value];
      setBackgroundColor(value);
      setBackgroundColor2(datas.colorListJson[value]);
      setSysConfig(datas.sysConfigInfo);
      document.getElementsByTagName('body')[0].style.setProperty('--backgroundColor', value || '#fff');
      document
        .getElementsByTagName('body')[0]
        .style.setProperty('--backgroundColor2', datas.colorListJson[value] || '#fff');
      // setRem1(value)
      nextTick(() => {
        let dom = datas.colorSelect;
        if (dom) {
          dom = dom.$el.children[0];
          const inputDom = dom.querySelectorAll('.el-input__inner');
          const icon = dom.querySelectorAll('.el-input__icon');
          inputDom[0].style['background-color'] = value || '#fff';
          icon[0].style['color'] = 'black';
        }
      });
    };
    // 关闭智能聊天
    const closeServiceDialog = () => {
      datas.dialogService = false;
    };

    // 关闭引导流程
    const closeGuideFlowDrawer = () => {
      datas.drawerGuideFlowVisible = false;
    };

    return {
      ...toRefs(datas),
      messageInfo,
      handleChangeColor,
      getNameByid,
      getPermissionBtn,
      handleCommand,
      loginOut,
      selectColor,
      selectMenuConfig,
      loginHeart,
      removeTimer,
      changeTenantId,
      handleIntelligent,
      handleGuideFlow,
      closeServiceDialog,
      closeGuideFlowDrawer,
      submitPWDSuccess,
      canclePWD,
      validatePassword,
      selectFontSizeConfig,
      jumpToPlatform,
      getPlatformList
    };
  },
  computed: {
    ...mapGetters(['sidebar', 'tenantGroup', 'webSocketMsg']),
    ...mapState(['tenantInfo'])
  },
  watch: {
    webSocketMsg(msg) {
      // 后台消息提醒类型乱七八糟，有header里面cmd 0, 1, 2, 3,还有33,还有不带cmd，后台提供不了都代表什么，为防止吵架暂且搁置
      this.messageStrList.push(msg);
      if (
        JSON.stringify(msg)?.indexOf('read') === -1 &&
        JSON.stringify(msg)?.indexOf('deleted') === -1 &&
        JSON.stringify(msg)?.indexOf('completed') === -1 &&
        JSON.stringify(msg)?.indexOf('todo') === -1 &&
        JSON.stringify(msg)?.indexOf('{"cmd":33}') === -1
      ) {
        ElNotification({
          title: '新消息',
          dangerouslyUseHTMLString: true,
          message: '<div>' + msg + '</div>',
          type: 'info'
        });
      }
      if (JSON.stringify(msg).indexOf('{"cmd":33}') === -1) {
        this.getMessageNums();
      }
    },
    theme(val, oldVal) {
      var that = this;
      if (!oldVal) {
        oldVal = '#409EFF';
      }
      if (typeof val !== 'string') return;
      const themeCluster = this.getThemeCluster(val.replace('#', ''));
      const originalCluster = this.getThemeCluster('409EFF');

      const getHandler = (variable, id) => {
        return () => {
          const newStyle = that.updateStyle(that.chalk, originalCluster, themeCluster);

          let styleTag = document.getElementById(id);
          if (!styleTag) {
            styleTag = document.createElement('style');
            styleTag.setAttribute('id', id);
            document.head.appendChild(styleTag);
          }
          styleTag.innerText = newStyle;
        };
      };

      const chalkHandler = getHandler('chalk', 'chalk-style');
      if (!this.chalk) {
        const url = `https://unpkg.com/element-plus@${this.version}/theme-chalk/index.css`;
        this.getCSSString(url, chalkHandler, 'chalk');
      } else {
        chalkHandler();
      }
    }
  },
  created() {
    this.getMessageNums();
    this.bus.$on('reloadMessageNums', msg => {
      this.getMessageNums();
    });
    this.getSysConfigInfos(); // 加载系统配置
  },
  methods: {
    getMessageNums() {
      var that = this;
      getMessageNum().then(res => {
        if (res !== false) {
          // console.log(res.data.data)
          const numList = res.data.data;
          that.messageNum = numList.notReadMessageNum;
          that.messageNum1 = numList.notHandleTodoNum;
        }
      });
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar');
    },
    async logout() {
      await this.$store.dispatch('user/resetToken');
    },
    // 主题切换功能
    themeChange(val) {
      // this.$store.dispatch('settings/changeSetting', {
      //   key: 'theme',
      //   value: val
      // })
      // this.$store.dispatch('settings/changeSetting', {
      //   key: 'tesPrimary',
      //   value: val
      // })
      // if (document.getElementById('chalk-style')) {
      //   document.head.removeChild(document.getElementById('chalk-style'))
      // }
      // this.chalk = ''
      // this.theme = ''
      const oldVal = val;
      if (typeof val !== 'string') return;
      const themeCluster = this.getThemeCluster(val.replace('#', ''));
      const originalCluster = this.getThemeCluster(oldVal.replace('#', ''));
      // console.log(themeCluster, originalCluster)

      const getHandler = (variable, id) => {
        return () => {
          const originalCluster = this.getThemeCluster(val.replace('#', ''));
          const newStyle = this.updateStyle(this[variable], originalCluster, themeCluster);

          let styleTag = document.getElementById(id);
          if (!styleTag) {
            styleTag = document.createElement('style');
            styleTag.setAttribute('id', id);
            document.head.appendChild(styleTag);
          }
          styleTag.innerText = newStyle;
          // if (this.themeFlag) {
          //   this.themeFlag = false
          // }
        };
      };

      const chalkHandler = getHandler('chalk', 'chalk-style');
      if (!this.chalk) {
        const url = `https://unpkg.com/element-plus@${this.version}/theme-chalk/index.css`;
        this.getCSSString(url, chalkHandler, 'chalk');
      } else {
        chalkHandler();
      }

      const styles = [].slice.call(document.querySelectorAll('style')).filter(style => {
        const text = style.innerText;
        return new RegExp(oldVal, 'i').test(text) && !/Chalk Variables/.test(text);
      });
      styles.forEach(style => {
        const { innerText } = style;
        if (typeof innerText !== 'string') return;
        style.innerText = this.updateStyle(innerText, originalCluster, themeCluster);
      });
    },
    updateStyle(style, oldCluster, newCluster) {
      // console.log(style)
      // console.log('old', oldCluster);
      // console.log('new', newCluster);
      let newStyle1 = style;
      oldCluster.forEach((color, index) => {
        // console.log(color, index)
        newStyle1 = newStyle1.replace(new RegExp(color, 'ig'), newCluster[index]);
      });
      return newStyle1;
    },
    getCSSString(url, callback, variable) {
      var that = this;
      const xhr = new XMLHttpRequest();
      xhr.onreadystatechange = () => {
        if (xhr.readyState === 4 && xhr.status === 200) {
          that.chalk = xhr.responseText.replace(/@font-face{[^}]+}/, '');
          callback();
        }
      };
      xhr.open('GET', url);
      xhr.send();
      // return new Promise(resolve => {
      //   const xhr = new XMLHttpRequest()
      //   xhr.onreadystatechange = () => {
      //     if (xhr.readyState === 4 && xhr.status === 200) {
      //       this[variable] = xhr.responseText.replace(/@font-face{[^}]+}/, '')
      //       resolve()
      //     }
      //   }
      //   xhr.open('GET', url)
      //   xhr.send()
      // })
    },
    getThemeCluster(theme) {
      const tintColor = (color, tint) => {
        let red = parseInt(color.slice(0, 2), 16);
        let green = parseInt(color.slice(2, 4), 16);
        let blue = parseInt(color.slice(4, 6), 16);

        if (tint === 0) {
          // when primary color is in its rgb space
          return [red, green, blue].join(',');
        } else {
          red += Math.round(tint * (255 - red));
          green += Math.round(tint * (255 - green));
          blue += Math.round(tint * (255 - blue));

          red = red.toString(16);
          green = green.toString(16);
          blue = blue.toString(16);

          return `#${red}${green}${blue}`;
        }
      };

      const shadeColor = (color, shade) => {
        let red = parseInt(color.slice(0, 2), 16);
        let green = parseInt(color.slice(2, 4), 16);
        let blue = parseInt(color.slice(4, 6), 16);

        red = Math.round((1 - shade) * red);
        green = Math.round((1 - shade) * green);
        blue = Math.round((1 - shade) * blue);

        red = red.toString(16);
        green = green.toString(16);
        blue = blue.toString(16);

        return `#${red}${green}${blue}`;
      };

      const darkenColor = (color, level) => {
        const tinyColor = tinycolor(color);
        const hsl = tinyColor.toHsl();
        const colorH = hsl['h'];
        const colorS = hsl['s'] * 100;
        const colorL = hsl['l'] * 100;
        const darken = colorL - level;
        const darkColor = tinycolor('hsl(' + colorH + ', ' + colorS + '%, ' + darken + '%)');
        const darkHex = darkColor.toHexString();
        return darkHex;
      };

      const clusters = [theme];
      for (let i = 0; i <= 9; i++) {
        clusters.push(tintColor(theme, Number((i / 10).toFixed(2))));
      }
      clusters.push(darkenColor(theme, 10));
      clusters.push(shadeColor(theme, 0.1));
      return clusters;
    },
    // 切换部分字体颜色、背景色、边框色
    menuBgChange(val) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'menuBg',
        value: val.color
      });
      document.getElementsByTagName('body')[0].style.setProperty('--tesPrimary', val.color);
      document.getElementsByTagName('body')[0].style.setProperty('--tesPrimary1', val.tesBoder);
      document.getElementsByTagName('body')[0].style.setProperty('--tesPrimary2', val.bg);
      document.getElementsByTagName('body')[0].style.setProperty('--tesPrimary3', val.secondary);
      document.getElementsByTagName('body')[0].style.setProperty('--tesPrimary4', val.light);
      document.getElementsByTagName('body')[0].style.setProperty('--menuHover', val.menuHover);
    },
    scrollMenu() {
      const el = this.$refs['topSidebarScrollRef'].wrap;
      // console.log(el.offsetWidth, el.scrollWidth, el.scrollLeft)
      if (el.scrollWidth - el.offsetWidth <= el.screenLeft) {
        el.scrollLeft = el.scrollWidth - el.offsetWidth;
      } else {
        el.scrollLeft = el.scrollLeft + 100;
      }
    },
    getSysConfigInfos() {
      var that = this;
      getSysConfigInfo({}).then(res => {
        if (res) {
          if (res.data.data.themecolor || res.data.data.themecolor === 0) {
            that.sysConfigInfo = res.data.data;
            that.selectColor(that.themeColors[that.sysConfigInfo.themecolor], that.sysConfigInfo.themecolor);
          } else {
            that.sysConfigInfo.themecolor = 0;
            that.selectColor(that.themeColors[that.sysConfigInfo.themecolor], that.sysConfigInfo.themecolor);
          }
          that.$store.dispatch('settings/changeSetting', {
            key: 'sidebarMode',
            value: that.sysConfigInfo.layout === 0 ? 'vertical' : 'horizontal'
          });
          setRem1(that.sysConfigInfo.fontsize);
          setSysConfig(res.data.data);
          that.handleChangeColor(that.sysConfigInfo.backgroundColor);
        } else {
          that.handleChangeColor('#fff');
        }
      });
    },
    saveSysConfigInfos() {
      var vm = this;
      saveSysConfigInfo(this.sysConfigInfo).then(res => {
        if (res !== false) {
          vm.visible = false;
          this.getSysConfigInfos();
          ElMessage.success('系统配置成功！');
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.colorSelect {
  .el-select-dropdown__item.selected {
    background-color: $menuHover;
  }
}
.platform-wrapper {
  margin-left: 0.5rem;
  .el-tag {
    cursor: pointer;
  }
}

.tes-home-header-contant {
  height: 48px;
  line-height: normal;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left-header {
    display: flex;
    align-items: center;
    justify-content: center;
    :deep(.sidebar-logo-container) {
      background: #fff;
      justify-content: center;
    }
    :deep(.header-search-btn) {
      height: 48px;
      border: 0;
      color: $tes-font1;
      padding: 0 8px;
      border-radius: 0;
      i {
        padding-right: 8px;
      }
    }
    .searchBtn {
      height: 48px;
      border: 0;
      color: #fff;
      padding: 0 8px;
      background: none;
      border-radius: 0;
      &.active,
      &:hover {
        background: #000;
      }
      i {
        padding-right: 8px;
      }
    }
    .hamburger-container {
      line-height: 48px;
      height: 48px;
      float: left;
      cursor: pointer;
      transition: all 0.3s;
      -webkit-tap-highlight-color: transparent;

      &:hover {
        background: rgba(0, 0, 0, 0.025);
      }
    }
  }
  .center-header {
    display: inline-flex;
    width: calc(100% - 400px);
    .el-icon-arrow-left,
    .el-icon-arrow-right {
      // position: absolute;
      left: 0px;
      line-height: 48px;
      color: #fff;
      font-size: 20px;
      cursor: pointer;
    }
  }
  .right-header {
    display: flex;
    align-items: center;
    justify-content: center;
    .icon-ai {
      display: inline-flex;
      cursor: pointer;
      img {
        width: 28px;
        transition: all 100ms ease-in;
      }
      &:hover {
        img {
          width: 30px;
        }
      }
    }
    :deep(.el-select) {
      margin-left: 20px;
      .el-input {
        height: auto;
      }
      .el-input__suffix {
        display: flex;
        align-items: center;
      }
    }
    .svg-config {
      cursor: pointer;
      padding: 10px;
      border-radius: 4px;
      margin: 0;
      background: none;
      display: flex;
      align-items: center;
      span {
        font-size: 16px;
        color: $tes-font1;
      }
      &.ml-20 {
        margin-left: 20px;
      }
      &:hover {
        background: var(--backgroundColor2);
      }
    }
    .message-main {
      display: flex;
      flex-direction: row;
      margin-right: 10px;
      .item {
        border-radius: 4px;
        &:hover {
          background: var(--backgroundColor2);
        }
        :deep(.el-button) {
          background: none;
          border: 0px;
          i {
            color: $tes-font1;
            font-size: 16px;
          }
        }
        :deep(.el-badge__content.is-fixed) {
          top: 10px;
          right: 14px;
          // border: none;
          background: $tes-red;
        }
      }
      .noNumber {
        :deep(.el-badge__content.is-fixed) {
          display: none;
        }
      }
    }
    .user-name {
      display: flex;
      justify-content: center;
      align-items: center;
      // width: 90px;
      height: 48px;
      margin: 0 20px 0 10px;
      color: $tes-font2;
      cursor: pointer;
      &:hover {
        .name-txt {
          color: $tes-font1;
        }
      }
      .name-icon {
        height: 24px;
        width: 24px;
        line-height: 24px;
        background: $tes-primary;
        border-radius: 50%;
        color: #fff;
        margin-right: 8px;
      }
    }
  }
  :deep(.el-scrollbar) {
    overflow-y: hidden !important;
    .scrollbar-wrapper-top {
      .el-scrollbar__view {
        padding: 0 15px;
        .el-menu.el-menu--horizontal {
          display: flex;
          border: 0px;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.color-config-popover {
  .content-config {
    padding: 20px;
    .theme-config {
      margin-bottom: 20px;
      p {
        line-height: 22px;
        margin: 0 0 10px 0;
        color: $tes-font;
      }
      .content {
        display: flex;
        width: 100%;
        align-items: center;
      }
      .color-list {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        color: #fff;
        cursor: pointer;
        &:not(:last-of-type) {
          margin-right: 12px;
        }
        .icon {
          display: inline-block;
          font-size: 20px;
        }
      }
      .colorBackground {
        border: 1px solid $tes-primary;
        .icon {
          color: $tes-primary;
        }
      }
      .is-selected {
        box-shadow: 0 0 4px 6px rgba(0, 0, 0, 0.3);
      }
      .menu-config-list {
        display: inline-block;
        cursor: pointer;
        margin-right: 0.5rem;
        &.is-select {
          color: $tes-primary;
        }
      }
      &.flex-between {
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 20px;
      }
      &.theme-font {
        padding-bottom: 20px;
        width: 90%;
        margin: 0 auto 40px;
        .el-slider {
          .el-slider__stop {
            outline: 1px solid $tes-primary;
          }
          .el-slider__marks-text {
            margin-top: 20px;
            font-size: 12px;
            color: $tes-font2;
          }
        }
      }
    }
    .config-btn {
      .el-button {
        width: 100%;
      }
    }
  }
  .el-radio-button__original-radio:checked + .el-radio-button__inner,
  .el-radio-button__inner {
    background-color: #fff !important;
    border: 0;
    box-shadow: none;
  }
}
</style>
<style lang="scss">
.radio-config {
  display: flex !important;
  justify-content: flex-start;
  .el-radio-button__inner {
    padding: 0 !important;
    background: transparent;
    border: none;
    .label {
      display: none;
    }
    .item-text {
      text-align: left;
      color: $tes-font2;
      padding: 8px 0 0 10px;
      font-size: 12px;
    }
  }
  .el-radio-button__inner {
    margin-right: 10px;
  }
  .el-radio-button:first-child .el-radio-button__inner {
    border-left: transparent;
  }
  .is-active {
    .label {
      display: inline-block;
      position: absolute;
      left: 40%;
      top: 25%;
      z-index: 1000;
      color: #303133;
      font-size: 16px;
    }
  }
}
</style>
