<template>
  <!-- 我的消息 -->
  <ListLayout class="my-message">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="condition">
          <el-input
            v-model="formInline.condition"
            v-trim
            v-focus
            size="large"
            placeholder="请输入标题或内容"
            class="ipt-360"
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter="onSubmit"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0px">
          <el-button type="primary" size="large" @click="onSubmit">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
          <el-button class="searchBtn" size="large" type="text" @click="search" @keyup.prevent @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
          /></el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" :model="searchForm" label-width="110px" label-position="right">
            <el-form-item label="等级：">
              <el-radio-group v-model="searchForm.level" @change="changelevel">
                <el-radio lable="">不限</el-radio>
                <el-radio v-for="item in levelOptions" :key="item.id" :label="item.id">{{ item.name }}</el-radio>
              </el-radio-group>
              <!-- <el-select v-model="searchForm.level" class="owner-select" placeholder="请选择" size="small" clearable @change="changelevel">
                <el-option v-for="item in levelOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select> -->
            </el-form-item>
            <el-form-item label="分组：">
              <el-select
                v-model="searchForm.groupId"
                class="owner-select"
                placeholder="请选择"
                size="small"
                clearable
                @change="changecategoryName"
              >
                <el-option
                  v-for="item in categoryNameOptions"
                  :key="item.id"
                  :label="item.groupName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间：">
              <el-date-picker
                v-model="searchForm.createDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                @change="changeCreateTime(searchForm.createDateRange)"
              />
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-radio-group v-model="radioData" size="small" @change="changeRadio">
        <el-radio-button label="全部" />
        <el-radio-button label="未读" />
        <el-radio-button label="已读" />
      </el-radio-group>
    </template>
    <template #button-group>
      <el-button
        size="large"
        icon="el-icon-delete"
        :disabled="disableRead"
        @click="batchDelete"
        @keyup.prevent
        @keydown.enter.prevent
        >删除</el-button
      >
      <el-button
        v-if="radioData !== '已读'"
        type="primary"
        size="large"
        icon="el-icon-reading"
        :disabled="disableRead"
        @click="batchRead()"
        @keyup.prevent
        @keydown.enter.prevent
        >标记为已读</el-button
      >
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table message-table base-table format-height-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      @header-dragend="drageHeader"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <!-- 测试提bug，缩小会出现几个黑点，宽度设为80px, -->
      <el-table-column type="selection" width="80px" align="center" fixed="left" />
      <el-table-column label="标题" prop="title" width="200px" show-overflow-tooltip>
        <template #default="{ row }">
          <span :class="row.readStatus === 0 ? 'no-read-weight' : ''">{{ row.title || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="内容" prop="content" min-width="200px" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.content" :class="row.readStatus === 0 ? 'no-read-weight' : ''">{{ row.content }}</span>
          <span v-else>--</span>
          <!-- <span class="look-msg" @click="editDetail(row)">>>查看消息</span> -->
        </template>
      </el-table-column>
      <el-table-column label="等级" prop="level" width="100px">
        <template #default="{ row }">
          <el-tag v-if="row.level" size="small" effect="dark" :type="levelStyleClass[row.level]">
            {{ filterLevel(row.level)[0] }}</el-tag
          >
          <!-- <span v-if="row.level" :style="{color: filterLevel(row.level)[1]}">{{ filterLevel(row.level)[0] }}</span> -->
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="类型" prop="categoryName" min-width="140px" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.categoryName" :class="row.readStatus === 0 ? 'no-read-weight' : ''">{{
            row.categoryName
          }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="readStatus" width="100px" show-overflow-tooltip>
        <template #default="{ row }">
          <span :class="row.readStatus === 0 ? 'no-read-weight' : ''">{{
            filterReadStatus(row.readStatus) || '--'
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发件人" prop="senderName" width="140px" show-overflow-tooltip>
        <template #default="{ row }">
          <span :class="row.readStatus === 0 ? 'no-read-weight' : ''">{{ row.senderName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="140px" sortable>
        <template #default="{ row }">
          <span v-if="row.createTime" :class="row.readStatus === 0 ? 'no-read-weight' : ''">{{
            formatDate(row.createTime)
          }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="140px" prop="caozuo" fixed="right" class-name="fixed-right">
        <template #default="scope">
          <span class="blue-color" @click.stop="editDetail(scope.row)">详情</span>
          <span v-if="getPermissionBtn('delMaeeageBtn')" class="blue-color" @click.stop="removeMessage(scope.row)"
            >删除</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      small
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
    <template #other>
      <!-- 消息详情 -->
      <my-message-detail
        :drawer="showMessageDrawer"
        :detail="messageDetail"
        :is-agency="false"
        :show-delete="showDelete"
        @close="closeDetail"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import Pagination from '@/components/Pagination';
import { getMessageList, messagegroupListNum, deleteMessage, getMessageInfo, readMessage } from '@/api/messageAgent';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import ListLayout from '@/components/ListLayout';
// import { useStore } from 'vuex'
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import MyMessageDetail from './my-message-detail.vue';
import _ from 'lodash';

export default {
  name: 'MyMessage',
  components: { Pagination, MyMessageDetail, ListLayout },
  setup() {
    const { proxy } = getCurrentInstance();
    // const store = useStore().state
    // console.log(store)
    const editFrom = ref(null);
    const datas = reactive({
      currentAccountId: getLoginInfo().accountId,
      activeName: '0',
      tableRef: ref(),
      showS: false,
      list: [],
      ids: [],
      formInline: {
        messageType: '1',
        groupId: '',
        condition: '',
        level: '',
        startTime: null,
        endTime: null,
        readStatus: 'all'
      },
      searchForm: {
        level: '',
        categoryName: '',
        groupId: '',
        createDateRange: []
      },
      listQuery: {
        page: 1,
        limit: 20
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      radioData: '全部',
      categoryNameOptions: [],
      categoryNametree: [],
      levelOptions: [
        { id: 0, name: '重要' },
        { id: 1, name: '一般' },
        { id: 2, name: '较弱' }
      ],
      showMessageDrawer: false,
      messageDetail: {},
      selectList: [],
      disableRead: true,
      showDelete: true,
      levelStyleClass: {
        0: 'danger',
        1: 'warning',
        2: 'success'
      }
    });
    // 查询
    function onSubmit() {
      proxy.getList();
    }
    // 重置
    function reset() {
      datas.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        sort: 'DESC'
      };
      datas.searchForm = {
        level: '',
        categoryName: '',
        groupId: '',
        createDateRange: []
      };
      datas.formInline = {
        messageType: '1',
        groupId: '',
        condition: '',
        level: '',
        startTime: null,
        endTime: null,
        readStatus: 'all'
      };
      datas.radioData = '全部';
      proxy.getList();
    }
    // 高级搜索
    const search = () => {
      datas.showS = !datas.showS;
      if (datas.activeName === '0') {
        datas.activeName = '1';
      } else {
        datas.activeName = '0';
      }
    };

    const sortChange = data => {
      // const { prop, order } = data
      // console.log(prop)
      // console.log(order)
    };
    // 选择checkbox
    const handleSelectionChange = val => {
      datas.selectList = val;
      if (val.length > 0) {
        datas.disableRead = false;
      } else {
        datas.disableRead = true;
      }
    };
    const handleRowClick = row => {
      datas.tableRef.toggleRowSelection(
        row,
        !datas.selectList.some(item => {
          return row.id === item.id;
        })
      );
    };
    // 点击详情，查看消息--打开详情抽屉
    const editDetail = row => {
      // console.log(row)
      getMessageInfo({ id: row.id, messageType: '1' }).then(res => {
        if (res !== false) {
          // console.log(res.data.data)
          datas.showMessageDrawer = true;
          datas.messageDetail = res.data.data;
          batchRead(row);
        }
      });
    };
    // 关闭详情抽屉
    const closeDetail = v => {
      datas.showMessageDrawer = v;
    };
    // 删除
    const removeMessage = (row, flag) => {
      if (flag === 1) {
        datas.ids = row;
      } else {
        datas.ids = [row.id];
      }
      ElMessageBox({
        title: '',
        message: '确认将选中消息删除？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          const params = { ids: datas.ids.join(',') };
          deleteMessage(params).then(res => {
            if (res !== false) {
              ElMessage.success('删除成功');
              proxy.getList();
            }
          });
        })
        .catch(() => {});
    };
    // 过滤等级
    const filterLevel = value => {
      const p = {
        0: ['重要', '#f56c6c'],
        1: ['一般', '#67c23a'],
        2: ['较弱', '']
      };
      return p[value];
    };
    // 过滤状态
    const filterReadStatus = value => {
      const p = {
        0: '未读',
        1: '已读',
        all: '全部'
      };
      return p[value];
    };
    // 切换tab
    const changeRadio = value => {
      const param = {
        全部: 'all',
        已读: '1',
        未读: '0'
      };
      datas.formInline.readStatus = param[value];
      proxy.getList();
    };
    // 切换等级
    const changelevel = v => {
      datas.formInline.level = v + '';
    };
    // 切换分组
    const changecategoryName = v => {
      datas.formInline.groupId = v + '';
    };
    // 切换时间
    const changeCreateTime = date => {
      datas.formInline.startTime = date ? formatDate(date[0]) : '';
      datas.formInline.endTime = date ? formatDate(date[1]) : '';
    };
    // 批量-标记为已读
    const batchRead = row => {
      var ids = [];
      if (row) {
        ids = [row.id];
        const params = { ids: ids.join(',') };
        readMessage(params).then(res => {
          if (res !== false) {
            // ElMessage.success('标记成功')
            proxy.getList();
          }
        });
      } else {
        datas.selectList.forEach(list => {
          ids.push(list.id);
        });
        ElMessageBox({
          title: '',
          message: '确认将选中消息标为已读？',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: true,
          type: 'warning'
        })
          .then(() => {
            const params = { ids: ids.join(',') };
            readMessage(params).then(res => {
              if (res !== false) {
                ElMessage.success('标记成功');
                proxy.getList();
              }
            });
          })
          .catch(() => {});
      }
    };
    // 批量删除
    const batchDelete = () => {
      const ids = [];
      datas.selectList.forEach(list => {
        ids.push(list.id);
      });
      removeMessage(ids, 1);
    };

    return {
      editDetail,
      removeMessage,
      drageHeader,
      formatDate,
      getNameByid,
      changeRadio,
      filterLevel,
      handleSelectionChange,
      handleRowClick,
      sortChange,
      editFrom,
      ...toRefs(datas),
      filterReadStatus,
      getPermissionBtn,
      search,
      onSubmit,
      reset,
      changelevel,
      changecategoryName,
      changeCreateTime,
      closeDetail,
      batchRead,
      batchDelete
    };
  },
  created() {
    const status = this.$route.query.status;
    if (status) {
      const radioData = this.filterReadStatus(status);
      if (radioData) {
        this.radioData = radioData;
        this.formInline.readStatus = status;
      }
    }
    this.getList();
    this.messagegroupLists();
    // 刷新列表
    this.bus.$on('reloadMymessageList', msg => {
      this.getList();
    });
  },
  methods: {
    // 获取消息列表
    getList(data) {
      const _this = this;
      _this.listLoading = true;
      if (data && data !== undefined) {
        _this.listQuery.page = data.page;
        _this.listQuery.limit = data.limit;
      }
      const param = Object.assign(_this.formInline, _this.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      // console.log(param)
      // 列表接口
      getMessageList(param).then(res => {
        // console.log(res.data)
        if (res !== false) {
          const { data } = res.data;
          _this.list = data.list;
          _this.total = data.totalCount;
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
    },
    // 获取分组列表
    messagegroupLists() {
      var that = this;
      const params = {
        messageType: '1'
      };
      that.categoryNameOptions = [];
      messagegroupListNum(params).then(res => {
        if (res !== false) {
          // console.log(res.data.data)
          const allList = res.data.data;
          const list = res.data.data;
          var categoryNametree = [];
          if (allList.length > 0) {
            allList.forEach(opt => {
              if (opt.level === 2) {
                that.categoryNameOptions.push(opt);
              }
              if (opt.parent_id === 0) {
                categoryNametree.push(opt);
              }
              const hasitem = _.filter(list, optl => {
                res.closable = false;
                return optl.parent_id === opt.id;
              });
              if (hasitem.length > 0) {
                opt.childrens = hasitem;
              } else {
                opt.childrens = [];
              }
            });
            const newList = [];
            categoryNametree.forEach((tree, index) => {
              if (tree.childrens.length > 0) {
                tree.childrens.forEach((child, cindex) => {
                  newList.push({ list: [tree] });
                  newList[cindex].list.push(child);
                  newList[cindex].list = newList[cindex].list.concat(child.childrens);
                });
              }
            });
            that.categoryNametree = newList;
            // console.log(newList)
          }
          // console.log(that.categoryNametree)
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.my-message {
  height: inherit;
  overflow: hidden auto;
  :deep(.el-form-item--medium .el-form-item__content) {
    line-height: 32px;
  }
  :deep(.el-form-item__label) {
    line-height: 32px;
  }
  .message-list {
    width: 100%;
    min-height: 30px;
    .list-contant {
      height: 30px;
      line-height: 30px;
      float: left;
      width: 100%;
      margin-bottom: 10px;
      .item {
        float: left;
        width: 111px;
        border-radius: 4px;
        border: 1px solid #909399;
        margin: 0px 10px 10px 0px;
      }
      .item1 {
        height: 30px;
        float: left;
        width: 111px;
        margin-right: 10px;
      }
    }
  }
  .message-table {
    width: auto;
    .el-table__body-wrapper {
      .look-msg {
        color: $tes-primary;
        cursor: pointer;
      }
    }
  }
  .no-read-weight {
    font-weight: bold;
    color: $tes-font1;
  }
}
</style>
