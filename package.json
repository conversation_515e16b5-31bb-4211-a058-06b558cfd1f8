{"name": "cxist-tes", "private": true, "version": "3.0.0", "scripts": {"dev": "vite", "serve": "npm run dev", "dev:debug": "npm run dev -- --debug", "build": "rimraf dist && vite build", "clean": "rimraf node_modules && rimraf .eslintcache", "lint:eslint": "eslint --max-warnings 0 \"src/**/*.{vue,js}\"", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint:fix": "npm run lint:eslint -- --fix && npm run lint:prettier", "lint": "npm run lint:eslint", "prepare": "husky install"}, "dependencies": {"@cxist/luckysheet": "^2.1.31", "@dcloudio/uni-webview-js": "^0.0.3", "@element-plus/icons": "^0.0.11", "@fullcalendar/core": "^6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.10", "@fullcalendar/list": "^6.1.10", "@fullcalendar/resource": "^6.1.10", "@fullcalendar/resource-timeline": "^6.1.10", "@fullcalendar/timegrid": "^6.1.10", "@fullcalendar/vue3": "^6.1.10", "@tinymce/tinymce-vue": "^5.0.0", "axios": "^0.21.1", "clipboard": "^2.0.8", "codemirror": "^5.65.3", "core-js": "^3.6.5", "decimal.js": "^10.4.3", "default-passive-events": "^2.0.0", "driver.js": "^0.9.8", "dropzone": "^5.9.2", "echarts": "^5.3.2", "element-plus": "1.1.0-beta.10", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "fuse.js": "^6.4.6", "html2canvas": "1.1.2", "idb": "^7.0.1", "jquery": "^2.2.4", "js-cookie": "^2.2.1", "jspdf": "^2.3.1", "jszip": "^3.6.0", "lodash": "^4.17.21", "luckyexcel": "^1.0.1", "mammoth": "^1.4.17", "mathjax": "^4.0.0-alpha.1", "mitt": "^3.0.0", "moment": "^2.30.1", "normalize.css": "7.0.0", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.0", "pdf-viewer": "file:deps\\pdf-viewer.tar.gz", "postcss-pxtorem": "6.1.0", "print-js": "^1.6.0", "qrcode": "^1.5.4", "qrcode.vue": "^3.4.0", "qs": "^6.13.0", "recorder-core": "^1.2.23070100", "screenfull": "^5.1.0", "script-loader": "^0.7.2", "sortablejs": "^1.14.0", "tesseract.js": "^5.0.5", "tinycolor2": "^1.6.0", "tinymce": "^5.5.1", "vue": "^3.2.0", "vue-clipboard2": "^0.3.3", "vue-count-to": "^1.0.13", "vue-echarts": "^6.0.3", "vue-ganttastic": "^0.9.34", "vue-print-nb": "^1.7.4", "vue-router": "^4.0.0-0", "vue-signature-pad": "^3.0.2", "vue-splitpane": "^1.0.6", "vue-sse": "^2.5.2", "vue-upload-component": "^2.8.23", "vue-virtual-scroller": "^2.0.0-beta.8", "vue3-print-nb": "^0.1.4", "vuedraggable": "^2.24.3", "vuex": "^4.0.0-0", "whatwg-fetch": "^3.6.2", "xlsx": "^0.17.0"}, "devDependencies": {"@babel/core": "^7.24.5", "@babel/eslint-parser": "^7.17.0", "@babel/preset-env": "^7.24.5", "@rollup/plugin-inject": "5.0.5", "@vitejs/plugin-vue": "^5.0.4", "@vue/compiler-sfc": "^3.2.0", "@vue/eslint-config-standard": "^5.1.2", "@vue/test-utils": "^2.0.0-0", "autoprefixer": "^9.5.1", "babel-jest": "^27.0.1", "babel-plugin-dynamic-import-node": "^2.3.3", "chai": "^4.1.2", "chalk": "^4.1.1", "chokidar": "^3.5.1", "connect": "^3.7.0", "crypto-js": "^4.2.0", "css-unicode-loader": "^1.0.3", "docx-preview": "0.1.11", "eslint": "^8.43.0", "eslint-config-prettier": "^8.4.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^8.6.0", "husky": "^7.0.4", "lint-staged": "^11.0.0", "mockjs": "^1.1.0", "plop": "^2.7.4", "postcss": "^8.4.38", "prettier": "^2.8.8", "sass": "1.39.0", "sass-loader": "10.1.1", "serve-static": "^1.14.1", "svgo": "^2.3.0", "tailwindcss": "^3.4.3", "vite": "^5.2.0", "vite-plugin-eslint": "^1.8.1", "vite-svg-loader": "^5.1.0", "vue-printjs": "^1.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}