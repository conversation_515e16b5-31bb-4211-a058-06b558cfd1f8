<template>
  <!-- 检测项目-检测设备 -->
  <div class="Specification">
    <div class="header-search-group">
      <div v-loading="listLoading" class="btn-group">
        <el-button
          size="small"
          icon="el-icon-plus"
          type="default"
          @click="handleAdd()"
          @keyup.prevent
          @keydown.enter.prevent
          >新增</el-button
        >
      </div>
    </div>
    <el-form ref="ruleFormTable" :model="formData">
      <el-table
        ref="tableRef"
        v-loading="listLoading"
        :data="formData.tableList"
        size="medium"
        fit
        border
        height="auto"
        highlight-current-row
        class="dark-table base-table"
        @header-dragend="drageHeader"
      >
        <el-table-column prop="templateDataField" label="业务数据" :min-width="colWidth.name">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isEdit"
              :prop="`tableList.${$index}.templateDataField`"
              :rules="{ required: true, message: '请输入业务数据', trigger: 'change' }"
              style="margin: 0px"
            >
              <el-input
                v-model="row.templateDataField"
                placeholder="请输入业务数据"
                maxlength="100"
                @keyup.prevent
                @keydown.enter.prevent
              />
            </el-form-item>
            <span v-else> {{ row.templateDataField || '--' }} </span>
          </template>
        </el-table-column>
        <el-table-column
          label="关联Id"
          prop="templateFiledId"
          show-overflow-tooltip
          align="left"
          :min-width="colWidth.name"
        >
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isEdit"
              :prop="`tableList.${$index}.templateFiledId`"
              :rules="{ required: true, message: '请输入关联Id', trigger: 'change' }"
              style="margin: 0px"
            >
              <el-input
                v-model="row.templateFiledId"
                placeholder="请输入关联Id"
                maxlength="100"
                @keyup.prevent
                @keydown.enter.prevent
              />
            </el-form-item>
            <span v-else> {{ row.templateFiledId || '--' }} </span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" show-overflow-tooltip align="left" :min-width="colWidth.name">
          <template #default="{ row, $index }">
            <el-form-item v-if="row.isEdit" :prop="`tableList.${$index}.remark`" style="margin: 0px">
              <el-input
                v-model="row.remark"
                placeholder="请输入备注"
                maxlength="300"
                @keyup.prevent
                @keydown.enter.prevent
              />
            </el-form-item>
            <span v-else> {{ row.remark || '--' }} </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" :width="colWidth.operation">
          <template #default="{ row, $index }">
            <span v-if="row.id && !row.isEdit" class="blue-color" @click="handleEdit(row, $index)">编辑</span>
            <span v-if="row.id && !row.isEdit" class="blue-color" @click="handleDelete(row, $index)">删除</span>
            <span v-if="row.isEdit" class="blue-color" @click="handleSaveEdit(row, $index)">保存</span>
            <span v-if="row.isEdit" class="blue-color" @click="handleCancle(row, $index)">取消</span>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>

<script>
import { getCurrentInstance, reactive, ref, toRefs, watch } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { getColWidth } from '@/utils/func/customTable';
import { findByCapabilityIdParamer, saveParamer, deleteParamer } from '@/api/test-report-template';
import { ElMessageBox, ElMessage } from 'element-plus';

export default {
  name: 'DataRelationship',
  components: {},
  props: {
    capabilityId: {
      type: String,
      default: ''
    },
    activeName: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableRef: ref(),
      detailItemId: '', // 项目id
      isEdit: false, // 是否是编辑状态 true 是
      ruleForm: ref(),
      ruleFormTable: ref(),
      listLoading: false,
      formData: {
        tableList: []
      }
    });

    watch(props, newValue => {
      if (newValue.activeName == '11') {
        state.detailItemId = props.capabilityId;
        state.isEdit = false;
        getList();
      }
    });

    const getList = () => {
      state.listLoading = true;
      findByCapabilityIdParamer({ capabilityId: state.detailItemId }).then(res => {
        state.listLoading = false;
        if (res) {
          state.formData.tableList = res.data.data;
        }
      });
    };

    // 保存编辑
    const handleSaveEdit = row => {
      state.ruleFormTable
        .validate()
        .then(valid => {
          if (valid) {
            const templateFiledId = [...new Set(state.formData.tableList.map(item => item.templateFiledId))];
            const templateDataField = [...new Set(state.formData.tableList.map(item => item.templateDataField))];
            const params = JSON.parse(JSON.stringify(row));
            delete params.isEdit;
            if (
              templateFiledId.length != state.formData.tableList.length ||
              templateDataField.length != state.formData.tableList.length
            ) {
              ElMessage.warning('一个业务数据只能关联一个ID！');
              return false;
            }
            onSubmitApi(params);
          } else {
            return false;
          }
        })
        .catch(error => {
          const fieldName = Object.keys(error)[0];
          proxy.$message.warning(error[fieldName][0].message);
          return false;
        });
    };

    const onSubmitApi = params => {
      state.listLoading = true;
      saveParamer(params).then(res => {
        state.listLoading = false;
        if (res) {
          proxy.$message.success(res.data.message);
          state.isEdit = false;
          getList();
        }
      });
    };

    // 新增
    const handleAdd = () => {
      if (state.isEdit) {
        ElMessage.warning('只能同时编辑一行');
        return;
      }
      state.formData.tableList.push({
        capabilityId: state.detailItemId,
        isEdit: true
      });
      state.isEdit = true;
    };

    // 编辑表格
    const handleEdit = (row, index) => {
      if (state.isEdit) {
        ElMessage.warning('只能同时编辑一行');
        return;
      }
      state.isEdit = true;
      state.formData.tableList[index].isEdit = true;
    };

    // 取消修改
    const handleCancelEdit = () => {
      state.isEdit = false;
      getList();
    };

    // 删除新增的数据
    const handleDelete = (row, index) => {
      if (row.id) {
        ElMessageBox({
          title: '提示',
          message: '是否确认删除？',
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'warning'
        })
          .then(() => {
            state.listLoading = true;
            deleteParamer([row.id]).then(res => {
              state.listLoading = false;
              if (res) {
                ElMessage.success('删除成功');
                state.formData.tableList.splice(index, 1);
              }
            });
          })
          .catch(() => {});
      } else {
        state.formData.tableList.splice(index, 1);
      }
    };
    const handleCancle = (row, index) => {
      state.formData.tableList[index].isEdit = false;
      state.isEdit = false;
      if (row.id) {
        getList();
      } else {
        state.formData.tableList.splice(index, 1);
      }
    };

    return {
      ...toRefs(state),
      handleAdd,
      handleCancle,
      handleSaveEdit,
      handleDelete,
      drageHeader,
      getNameByid,
      getNamesByid,
      getPermissionBtn,
      formatDate,
      colWidth,
      handleEdit,
      handleCancelEdit,
      getColWidth
    };
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-upload__tip) {
  display: inline;
  margin-left: 5px;
}
.header-search-group {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  .btn-group {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

.base-table.dark-table :deep(.el-table__body-wrapper) {
  max-height: 100% !important;
}
</style>
