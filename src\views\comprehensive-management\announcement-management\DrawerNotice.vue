<!-- 新增编辑公告 -->
<template>
  <el-drawer
    v-model="showDrawer"
    :title="titleType[drawerType]"
    direction="rtl"
    :before-close="handleClose"
    :size="750"
    destroy-on-close
    :close-on-click-modal="false"
    custom-class="DrawerNotice"
    @opened="handleOpened"
  >
    <DrawerLayout v-loading="drawerLoading" :has-button-group="false" :has-page-header="false">
      <el-form
        ref="formRef"
        :inline="true"
        :model="formData"
        :label-width="isCheck ? '70px' : '110px'"
        :label-position="isCheck ? 'left' : 'top'"
        class="form-height-auto"
      >
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item
              label="公告标题："
              prop="title"
              :rules="{ required: !isCheck, message: '请输入公告标题', trigger: 'change' }"
            >
              <span v-if="isCheck">{{ formData.title }}</span>
              <el-input
                v-else
                ref="inputRef"
                v-model.trim="formData.title"
                maxlength="100"
                placeholder="请输入公告标题"
                @change="handleModify"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否置顶：" prop="title">
              <span v-if="isCheck">{{ formData.isTop ? '是' : '否' }}</span>
              <el-switch
                v-else
                v-model="formData.isTop"
                class="inner-switch"
                :active-text="formData.isTop ? '是' : '否'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="发布人："
              prop="publisherId"
              :rules="{ required: !isCheck, message: '请选择发布人', trigger: 'change' }"
            >
              <span v-if="isCheck">{{ formData.publisherName }}</span>
              <el-select
                v-else
                v-model="formData.publisherId"
                placeholder="请选择发布人"
                clearable
                filterable
                style="width: 100%"
                @change="handleEditUser"
              >
                <el-option v-for="item in userList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="下架日期："
              prop="abrogateTime"
              :rules="{ required: !isCheck, message: '请选择下架日期', trigger: 'change' }"
            >
              <span v-if="isCheck">{{ formData.abrogateTime }}</span>
              <el-date-picker
                v-else
                v-model="formData.abrogateTime"
                type="datetime"
                placeholder="请选择下架日期"
                @change="handleEditDate"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="发布内容：" prop="content" label-position="top" style="margin-bottom: 0">
              <MarkdownEditor v-if="!isCheck" :value="formData.content" @getContent="getContent" />
            </el-form-item>
            <div v-if="isCheck" class="publishContent" v-html="formData.content" />
          </el-col>
        </el-row>
      </el-form>

      <div class="drawer-fotter">
        <el-button v-if="!isCheck" type="primary" :loading="drawerLoading" @click="onSubmit('release')">发布</el-button>
        <el-button v-if="!isCheck" type="warning" :loading="drawerLoading" @click="onSubmit('save')">保存</el-button>
        <el-button :loading="drawerLoading" @click="handleClose">取消</el-button>
      </div>
    </DrawerLayout>
  </el-drawer>
</template>

<script>
import { ref, watch, reactive, getCurrentInstance, toRefs } from 'vue';
import { releaseNotice, saveNotice } from '@/api/announcement';
import DrawerLayout from '@/components/DrawerLayout';
import MarkdownEditor from '@/components/MarkdownEditor/index.vue';
import { formatDateTime } from '@/utils/formatTime';
import store from '@/store';

export default {
  name: 'DrawerUnit',
  components: { DrawerLayout, MarkdownEditor },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    detailInfo: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // 抽屉事件
    const showDrawer = ref(props.drawer);
    const state = reactive({
      drawerType: '',
      titleType: {
        add: '新增公告',
        edit: '编辑公告',
        check: '查看公告'
      },
      isModified: false,
      inputRef: ref(),
      isCheck: true,
      drawerLoading: false,
      userList: store.state.common.nameList,
      formRef: ref(),
      formData: {}
    });
    watch(props, newValue => {
      showDrawer.value = newValue.drawer;
      if (showDrawer.value) {
        state.drawerType = props.type;
        state.formData = JSON.parse(JSON.stringify(props.detailInfo));
        state.isModified = false;
        state.isCheck = true;
        if (props.type === 'add') {
          state.formData = {};
          state.isCheck = false;
        } else if (props.type === 'edit') {
          state.isCheck = false;
        }
      }
    });
    const handleOpened = () => {
      if (state.inputRef && !state.isCheck) {
        state.inputRef.focus();
      }
    };
    // 关闭抽屉
    const handleClose = () => {
      if (state.isModified) {
        proxy
          .$confirm('确认离开当前页面吗？离开后数据不可恢复', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false
          })
          .then(() => {
            showDrawer.value = false;
            state.isModified = false;
            context.emit('close', false);
          })
          .catch(() => {});
      } else {
        showDrawer.value = false;
        context.emit('close', false);
      }
    };
    const handleModify = () => {
      state.isModified = true;
    };
    // 获取正文内容
    const getContent = value => {
      state.formData.content = value;
      handleModify();
    };
    // 确认新增
    const onSubmit = type => {
      proxy.$refs['formRef'].validate(valid => {
        if (valid) {
          if (type === 'release') {
            // 发布
            proxy
              .$confirm('是否确认发布', '确认发布', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning'
              })
              .then(() => {
                state.drawerLoading = true;
                releaseNotice(state.formData).then(res => {
                  state.drawerLoading = false;
                  if (res) {
                    state.isModified = false;
                    context.emit('close', true);
                    proxy.$message.success(res.data.message);
                  }
                });
              })
              .catch(() => {});
          } else {
            state.drawerLoading = true;
            saveNotice(state.formData).then(res => {
              state.drawerLoading = false;
              if (res) {
                state.isModified = false;
                context.emit('close', true);
                proxy.$message.success(res.data.message);
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    // 编辑时间日期
    const handleEditDate = value => {
      state.formData.abrogateTime = formatDateTime(value);
      handleModify();
    };
    // 获取发布人姓名
    const handleEditUser = value => {
      state.formData.publisherName = state.userList.filter(item => {
        return item.id === value;
      })[0].name;
      handleModify();
    };
    return {
      ...toRefs(state),
      handleEditUser,
      handleEditDate,
      getContent,
      handleModify,
      handleOpened,
      onSubmit,
      handleClose,
      showDrawer
    };
  }
};
</script>

<style lang="scss" scoped>
.publishContent {
  background-color: #eff1f4;
  padding: 10px;
  border-radius: 5px;
  margin-top: 10px;
  line-height: initial;
  text-align: left;
}

:deep(.el-input--medium) {
  line-height: 1;
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 100%;
}
</style>
<style lang="scss">
.DrawerNotice {
  .publishContent {
    p,
    div,
    h1,
    h2,
    h3,
    h4,
    h6,
    h5 {
      margin: 0;
      padding: 0;
    }
  }
}
</style>
