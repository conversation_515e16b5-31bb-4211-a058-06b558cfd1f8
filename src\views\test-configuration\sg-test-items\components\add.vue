<template>
  <el-drawer
    v-model="showDrawer"
    :title="titles"
    direction="rtl"
    :before-close="handleClose"
    :size="750"
    destroy-on-close
    :close-on-click-modal="false"
    custom-class="add-item"
  >
    <DrawerLayout :has-button-group="false" :has-page-header="false">
      <el-form
        ref="form"
        :model="formInline"
        :inline="true"
        :rules="otherDatas.addRules"
        label-width="110px"
        label-position="top"
      >
        <el-row :gutter="40">
          <el-col :span="24">
            <el-form-item label="状态:">
              <el-switch
                v-model="otherDatas.status"
                class="inner-switch"
                :active-text="otherDatas.status ? '启用' : '停用'"
                >启用</el-switch
              >
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="所属分类：" prop="categoryId">
              <el-cascader
                v-model="otherDatas.category"
                :options="otherDatas.newTree"
                :props="otherDatas.categoryProps"
                style="width: 100%"
                @change="changeCategory"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目编号：" prop="code">
              <el-input
                v-model="formInline.code"
                placeholder=""
                :input="(formInline.code = formInline.code.replace(/\s+/g, ''))"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目名称：" prop="name">
              <el-input
                v-model="formInline.name"
                placeholder=""
                maxlength="30"
                :input="(formInline.name = formInline.name.replace(/\s+/g, ''))"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联工序：">
              <el-select
                v-model="formInline.workingProcedureCode"
                placeholder="请选择"
                clearable
                filterable
                style="width: 100%"
                :filter-method="filterProcessList"
                @focus="filterProcessList(false)"
                @clear="clearProcess()"
                @change="changeProductionProcedure"
              >
                <el-option v-for="item in otherDatas.processList" :key="item.id" :label="item.name" :value="item.no" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目等级：">
              <el-checkbox-group v-model="otherDatas.testcapability" @change="changeTestcapability">
                <el-checkbox v-for="(val, key) in otherDatas.testcapabilitys" :key="key" :label="key">{{
                  val
                }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="基准价(¥)：">
              <el-input v-model="formInline.price" placeholder="请输入价格(元)" @input="clearNoNum(formInline.price)" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="盖章范围：" @change="changesealRange">
              <el-checkbox-group v-model="otherDatas.sealRange">
                <el-checkbox v-for="(val, key) in otherDatas.sealScopeOf" :key="key" :label="key">{{ val }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col> -->
          <el-col :span="24">
            <el-form-item label="项目描述：">
              <el-input
                v-model="formInline.remark"
                type="textarea"
                :rows="5"
                placeholder="请输入内容"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="drawer-footer">
        <el-button type="primary" @click="onSubmit" @keyup.prevent @keydown.enter.prevent>确认</el-button>
        <el-button @click="handleClose">取消</el-button>
      </div>
    </DrawerLayout>
  </el-drawer>
</template>

<script>
import { ref, watch, reactive, inject } from 'vue';
import { saveExternalCapabilityInfo } from '@/api/sg-capabilitystd';
import { ElMessage, ElMessageBox } from 'element-plus';
import DrawerLayout from '@/components/DrawerLayout';
import { getProcessListNew } from '@/api/mas';
import _ from 'lodash';

export default {
  name: 'ADD',
  components: { DrawerLayout },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      required: true
    },
    editData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    categoryIds: {
      type: Object,
      default: function () {
        return {};
      }
    },
    tree: {
      type: Object,
      default: function () {
        return {};
      }
    },
    itemLevel: {
      type: Object,
      default: function () {
        return {};
      }
    },
    sealScopeOf: {
      type: Object,
      default: function () {
        return {};
      }
    },
    materialClassification: {
      type: Object,
      default: function () {
        return {
          code: '',
          id: ''
        };
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    const mittBus = inject('$mittBus');
    // console.log(appContext)
    // console.log(bus)
    // 抽屉事件
    const showDrawer = ref(props.drawer);
    const titles = ref(props.title);
    const form = ref(null);
    // 关闭抽屉
    const handleClose = () => {
      const oldData = otherDatas.oldFormInline;
      if (
        formInline.categoryId !== oldData.categoryId ||
        formInline.description !== oldData.description ||
        formInline.name !== oldData.name ||
        formInline.englishName !== oldData.englishName ||
        formInline.code !== oldData.code ||
        formInline.price !== oldData.price ||
        formInline.remark !== oldData.remark ||
        formInline.sealRange !== oldData.sealRange ||
        formInline.status !== oldData.status ||
        formInline.grade !== oldData.grade ||
        formInline.workHours !== oldData.workHours
      ) {
        ElMessageBox({
          title: '提示',
          message: '当前页面数据未保存，是否确认离开？',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'info'
        })
          .then(() => {
            showDrawer.value = false;
            context.emit('close', false);
            return true;
          })
          .catch(() => {
            return false;
          });
      } else {
        showDrawer.value = false;
        context.emit('close', false);
      }
    };
    // formInline
    const formInline = reactive({
      allCategoryId: '',
      categoryId: '',
      code: '',
      description: '',
      englishName: '',
      grade: '',
      id: '',
      materialClassificationCode: '',
      materialClassificationId: '',
      method: '',
      name: '',
      price: 0,
      remark: '',
      sealRange: '',
      status: 0,
      workHours: '',
      workingProcedureCode: '',
      workingProcedureId: '',
      workingProcedureName: ''
    });
    const otherDatas = reactive({
      oldFormInline: {},
      categoryOptions: [],
      newTree: [],
      categoryProps: {
        children: 'children',
        label: 'name',
        value: 'id',
        checkStrictly: true
      },
      status: true,
      sealRange: [],
      method: '',
      category: [],
      testcapability: [],
      testcapabilitys: {},
      sealScopeOf: {},
      processList: [],
      copyProcessList: [],
      tableData: [{ id: '12345', name: '', ename: '', dw: '', szlx: '', cssz: 1, cz: true }],
      addRules: {
        categoryId: [{ required: true, message: '请选择所属分类', trigger: 'change' }],
        name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入项目编号', trigger: 'blur' }]
      },
      methodtags: ['111'],
      input1: ''
    });

    watch(props, newValue => {
      showDrawer.value = newValue.drawer;
      if (showDrawer.value) {
        otherDatas.testcapabilitys = props.itemLevel;
        otherDatas.sealScopeOf = props.sealScopeOf;
      }
      titles.value = props.title;
      otherDatas.newTree = props.tree.slice(1);
      formInline.materialClassificationCode = newValue.materialClassification.code;
      formInline.materialClassificationId = newValue.materialClassification.id;
      if (props.title !== '新增项目') {
        otherDatas.status = newValue.editData.status === 1;
        otherDatas.category = newValue.editData.categoryId;
        formInline.code = newValue.editData.code;
        formInline.name = newValue.editData.name;
        formInline.englishName = newValue.editData.englishName;
        formInline.workHours = newValue.editData.workHours;
        formInline.price = newValue.editData.price;
        formInline.categoryId = newValue.editData.categoryId;
        formInline.allCategoryId = newValue.editData.allCategoryId;
        formInline.materialClassificationCode = newValue.editData.materialClassificationCode;
        formInline.materialClassificationId = newValue.editData.materialClassificationId;
        formInline.remark = newValue.editData.remark;
        formInline.id = newValue.editData.id;
        formInline.workingProcedureCode = newValue.editData.workingProcedureCode;
        formInline.workingProcedureId = newValue.editData.workingProcedureId;
        formInline.workingProcedureName = newValue.editData.workingProcedureName;
        if (newValue.editData.grade) {
          otherDatas.testcapability = newValue.editData.grade.split(',');
        }
        if (newValue.editData.sealRange) {
          otherDatas.sealRange = JSON.parse(newValue.editData.sealRange);
        }
        // formInline.description = newValue.editData.description
        // if (newValue.editData.description) {
        //   showDes.value = true
        // }
      } else {
        formInline.code = '';
        formInline.name = '';
        formInline.englishName = '';
        formInline.workHours = '';
        formInline.price = 0;
        formInline.description = '';
        formInline.remark = '';
        showDes.value = false;
        otherDatas.method = '';
        otherDatas.testcapability = [];
        otherDatas.sealRange = [];
        otherDatas.category = props.categoryIds === '0' ? '' : props.categoryIds;
        formInline.categoryId =
          props.categoryIds[props.categoryIds.length - 1] === '0'
            ? ''
            : props.categoryIds[props.categoryIds.length - 1];
        formInline.allCategoryId = props.categoryIds.toString();
      }
      otherDatas.oldFormInline = JSON.parse(JSON.stringify(formInline));
    });
    // 确认新增
    const onSubmit = () => {
      form.value.validate(valid => {
        // console.log(valid)
        if (valid) {
          // formInline.price = parseFloat(formInline.price)
          if (otherDatas.status) {
            formInline.status = 1;
          } else {
            formInline.status = 0;
          }
          if (otherDatas.testcapability.length > 0) {
            formInline.grade = otherDatas.testcapability.toString();
          }
          if (props.title === '新增项目') {
            saveExternalCapabilityInfo(formInline).then(function (res) {
              if (res.status === 200 && res.data.code === 200) {
                // const datas = { show: true, detail: formInline }
                // context.emit('setDetail', datas)
                mittBus.$emit('reloadList', true);
                context.emit('close', false);
                ElMessage.success('新增成功');
              }
            });
          } else {
            saveExternalCapabilityInfo(formInline).then(function (res) {
              // console.log(res)
              if (res.status === 200 && res.data.code === 200) {
                // const datas = { show: true, detail: formInline }
                // context.emit('setDetail', datas)
                mittBus.$emit('reloadList', true);
                mittBus.$emit('reloadDetail', formInline);
                context.emit('close', false);
                ElMessage.success('编辑成功');
              }
            });
          }
        } else {
          return false;
        }
      });
    };

    // 添加项目描述
    const showDes = ref(false);
    const addItemDis = () => {
      showDes.value = !showDes.value;
    };
    // 所属分类change
    const changeCategory = value => {
      const len = value.length - 1;
      formInline.categoryId = value[len];
      formInline.allCategoryId = value.toString();
    };
    // 检测项目change
    const changeTestcapability = value => {
      formInline.grade = JSON.stringify(value);
    };
    // 盖章范围
    const changesealRange = () => {
      formInline.sealRange = JSON.stringify(otherDatas.sealRange);
    };
    // 基准价过滤
    const clearNoNum = obj => {
      if (obj) {
        // 先把非数字的都替换掉，除了数字和.
        obj = obj.replace(/[^\d.]/g, '');
        // 必须保证第一个为数字而不是.
        obj = obj.replace(/^\./g, '');
        // 保证只有出现一个.而没有多个.
        obj = obj.replace(/\.{2,}/g, '.');
        // 保证.只出现一次，而不能出现两次以上
        obj = obj.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
      }
      formInline.price = obj;
    };

    // 获取生产工序列表接口
    function getProcessLists() {
      var param = {
        limit: '-1',
        page: '1',
        content: ''
      };
      getProcessListNew(param).then(res => {
        if (res !== false) {
          otherDatas.processList = res.data.data.list;
          otherDatas.copyProcessList = res.data.data.list;
        }
      });
    }

    // 过滤生产工序
    function filterProcessList(val) {
      if (val) {
        const list = [];
        otherDatas.copyProcessList.forEach(user => {
          const item = _.filter(user.name, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        otherDatas.processList = list;
      } else {
        otherDatas.processList = JSON.parse(JSON.stringify(otherDatas.copyProcessList));
      }
    }

    function clearProcess() {
      formInline.workingProcedureCode = '';
      formInline.workingProcedureId = '';
      formInline.workingProcedureName = '';
    }

    // 生产工序-change
    const changeProductionProcedure = no => {
      // console.log(no)
      otherDatas.processList.forEach(item => {
        if (item.no === no) {
          formInline.workingProcedureId = item.id;
          formInline.workingProcedureCode = item.no;
          formInline.workingProcedureName = item.name;
        }
      });
    };

    getProcessLists();

    return {
      clearNoNum,
      form,
      changesealRange,
      changeTestcapability,
      showDes,
      changeCategory,
      addItemDis,
      otherDatas,
      onSubmit,
      formInline,
      handleClose,
      showDrawer,
      titles,
      filterProcessList,
      changeProductionProcedure,
      clearProcess
    };
  }
};
</script>

<style lang="scss" scoped>
.add-item {
  :deep(.el-form-item--medium .el-form-item__content) {
    line-height: 36px;
  }
  .item-status {
    width: 100%;
    margin-left: 18px;
    :deep(.el-form-item__content) {
      display: inline-block;
      margin-left: 10px;
    }
  }
  .remove-bg {
    background: none;
    color: $tes-primary;
    border: 0px;
    padding: 0px;
    margin: 0px 0px 0px 28px;
    font-weight: 500;
    font-size: 14px;
    float: left;
  }
  .allow-create-input {
    width: 100%;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .el-tag {
      margin: 0px 2px;
    }
    :deep(.el-input) {
      width: 20%;
      border: 0px;
      input {
        border: 0px;
      }
    }
  }
}
.drawer-fotter {
  position: absolute;
  bottom: 30px;
  right: 52px;
  //  border-top: 1px solid #e4e7ed;
  text-align: right;
}
</style>
