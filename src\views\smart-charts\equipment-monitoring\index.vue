<template>
  <!-- 设备监控数据看板 -->
  <div class="dataBoard">
    <div class="dataPageTop">
      <SvgIcon ref="voice" class="logo" :icon-class="'cxistLogo'" :width="37" :height="37" @click="voiceBroadcast(2)" />
      <img class="topHeader" src="@/assets/img/dataBoard/topHeader.png" alt="" />
      <img class="logoRight" :src="isQms ? qmsLogo : logo_title" />
      <span class="dataPageTitle">{{ clientName }}</span>
      <div class="topRight">
        <div class="time inBlock">
          <img src="@/assets/img/dataBoard/calendar.png" alt="" />{{ formatDate(new Date()) }}
        </div>
        <div class="time inBlock"><img src="@/assets/img/dataBoard/timepiece.png" alt="" />{{ digitalClock }}</div>
        <i class="el-icon-setting" style="font-size: 21px" @click="handleSetting()" />
      </div>
    </div>
    <div class="dataBoardContent">
      <el-row class="dataBoardTop">
        <el-col v-for="item in chartDataList" :key="item.id" :span="12" class="componentItem">
          <CodePointChart
            v-if="item.showType != 3"
            :title="`${item.deviceName || ''}-${item.deviceCodePointName || ''}-${
              item.showType == 1 ? '控制图' : '折线图'
            }`"
            :chart-type="item.showType"
            :device-id="item.deviceId"
            :device-code-point-id="item.deviceCodePointId"
            :associated-order-number="item.associatedOrderNumber"
            :group-count="item.groupCount"
            :ucl="item.UCL"
            :lcl="item.LCL"
            :data="item.data"
          />
          <SingleData
            v-else
            :title="`${item.deviceName || ''}-${item.deviceCodePointName || ''}-单数监控图`"
            :data="item.data"
          />
        </el-col>
        <!-- <el-col :span="18" class="componentItem">
          <CodePointChart title="XXX设备XXX码点折线图" chart-type="line" />
        </el-col>
        <el-col :span="6" class="componentItem">
          <SingleData type="submitReport" />
        </el-col> -->
      </el-row>
    </div>
    <SettingDialog
      :dialog-visiable="dialogShow"
      :current-view="currentView"
      :view-list="viewList"
      :device-list="deviceList"
      @closeDialog="closeDialog"
    />
  </div>
</template>

<script>
import { nextTick, reactive, ref, toRefs, computed, onMounted, onBeforeUnmount, inject } from 'vue';
import axios from 'axios';
import { getLoginInfo } from '@/utils/auth';
import { formatDate } from '@/utils/formatTime';
import { getNameByid } from '@/utils/common';
import { mapGetters, mapState, useStore } from 'vuex';
import CodePointChart from './components/CodePointChart';
import SingleData from './components/SingleData';
import SettingDialog from './SettingDialog';
import SvgIcon from '@/components/SvgIcon';
import { getTenantConfig } from '@/utils/auth';
import { checkServerIp, QmsServerIps } from '@/utils/server';
import { queryViewList, queryDeviceList, queryViewSettingById } from '@/api/equipmentMonitoring';
import logoTitle from '@/assets/img/logo-title.svg';
import qmsLogo from '@/assets/img/logo-title-qms.svg';

export default {
  name: 'EquipmentMonitoring',
  components: {
    CodePointChart,
    SvgIcon,
    SingleData,
    SettingDialog
  },
  setup() {
    const store = useStore();
    const state = reactive({
      dialogShow: false,
      typesettingData: [],
      digitalClock: '', // 数字时钟
      tenantId: getLoginInfo().tenantId,
      logo_title: getTenantConfig().normalLogo ? getTenantConfig().normalLogo : logoTitle,
      qmsLogo,
      tenantGroupList: JSON.parse(localStorage.getItem('tenantGroup')),
      histogramJson: {}, // 未来15天待见样品预测
      msg: '',
      timer: null, // 定时器
      failList: [], // 不合格样品集合
      failLoading: false,
      dvtime: 10, // 轮训时间
      voice: ref(),
      clientName: '',
      currentView: {},
      viewList: [],
      deviceList: [],
      chartDataList: []
    });

    let isWebSocketStarted = false;
    const defaultViewId = 'temp-default';

    const isQms = computed({
      get: () => checkServerIp(QmsServerIps)
    });

    const getWebSocketInstance = inject('$getWebSocketInstance');
    let webSocket = null;
    let webSocketTimer = null;

    onMounted(() => {
      document.getElementsByTagName('body')[0].style.setProperty('--tesPrimary', store.state.settings.theme);
      initWebSocket();
      state.clientName = state.tenantGroupList.find(item => item.clientId === state.tenantId)?.clientName;
    });

    onBeforeUnmount(() => {
      if (webSocket) {
        closeWebSocket();
      } else {
        clearInterval(webSocketTimer);
      }
    });

    // 初始化 WebSocket
    const initWebSocket = () => {
      webSocket = getWebSocketInstance();
      if (!webSocket) {
        webSocketTimer = setInterval(() => {
          webSocket = getWebSocketInstance();
          if (webSocket) {
            clearInterval(webSocketTimer);
            getDashboardconfig();
          }
        }, 1000);
      } else {
        getDashboardconfig();
      }
    };

    // 获取看板配置信息
    const getDashboardconfig = () => {
      let viewInfo = localStorage.getItem('EquipmentMonitoryingViewInfo');
      let viewId = '';
      if (viewInfo) {
        viewInfo = JSON.parse(viewInfo);
        viewId = viewInfo.id;
      }
      const apis = [queryDeviceList(), queryViewList()];
      if (viewId && viewId !== defaultViewId) {
        apis.push(queryViewSettingById(viewId));
      }
      axios.all(apis).then(
        axios.spread((deviceListResponse, viewListResponse, viewDetailResponse) => {
          if (deviceListResponse) {
            state.deviceList = deviceListResponse.data.data;
          }
          if (viewDetailResponse) {
            const currentView = viewDetailResponse.data.data;
            state.currentView = currentView;
            state.clientName = currentView.viewName;
            if (isWebSocketStarted) {
              closeWebSocket();
            }
            startWebSocket(currentView.id);
            setChartDataList(currentView);
          }
          if (viewListResponse) {
            state.viewList = viewListResponse.data.data;
            const defaultView = state.viewList.find(item => item.isDefault === 1);
            if (!defaultView) {
              setLocalDefaultViewInfo();
            }
          } else {
            setLocalDefaultViewInfo();
          }
          // if (currentView) {
          //   state.currentView = currentView
          // } else {
          //   state.currentView = state.viewList.find(item => item.isDefault === 1)
          // }
        })
      );
    };

    // 开始 WebSocket 数据监听
    const startWebSocket = viewId => {
      const params = {
        header: {
          cmd: 3
        },
        body: {
          viewId,
          tenantId: state.tenantId,
          optType: 0
        }
      };
      isWebSocketStarted = true;
      webSocket.send(JSON.stringify(params));
      webSocket.addEventListener('message', onWebSocketMessage);
    };

    // 监听 WebSocket 数据
    const onWebSocketMessage = event => {
      // console.log('event', event)
      const dataString = event.data;
      if (dataString.startsWith('{"header":{"cmd":3}')) {
        const data = JSON.parse(dataString);
        const codePointNumber = data.body.codePoint;
        const codePointItemIndex = state.chartDataList.findIndex(
          item => item.deviceCodePointNumber === codePointNumber
        );
        if (codePointItemIndex > -1) {
          const codePointItem = state.chartDataList[codePointItemIndex];
          codePointItem.data = codePointItem.data.concat(data.body.items);
          state.chartDataList.splice(codePointItemIndex, 1, codePointItem);
        }
      }
    };

    // 关闭 WebSocket 数据监听
    const closeWebSocket = () => {
      const params = {
        header: {
          cmd: 3
        },
        body: {
          viewId: state.currentView.id,
          tenantId: state.tenantId,
          optType: -1
        }
      };
      isWebSocketStarted = false;
      webSocket.send(JSON.stringify(params));
      webSocket.removeEventListener('message', onWebSocketMessage);
    };

    const setChartDataList = viewData => {
      if (viewData && viewData.deviceList?.length) {
        state.chartDataList = [];
        viewData.deviceList.forEach(deviceItem => {
          deviceItem.codePointList.forEach(item => {
            state.chartDataList.push({
              ...item,
              deviceId: deviceItem.deviceId,
              deviceName: deviceItem.deviceName,
              data: []
            });
          });
        });
        console.log('state.chartDataList', state.chartDataList);
      }
    };

    const setLocalDefaultViewInfo = () => {
      const deviceList = [];
      const codePointChartTypes = [1, 2, 3];
      let codePointChartIndex = 0;
      for (let index = 0; index < state.deviceList.length; index++) {
        const deviceItem = state.deviceList[index];
        const newDeviceItem = {
          deviceId: deviceItem.id,
          codePointList: []
        };
        for (let codePointIndex = 0; codePointIndex < deviceItem.devicecodepointList.length; codePointIndex++) {
          const codePointItem = deviceItem.devicecodepointList[codePointIndex];
          newDeviceItem.codePointList.push({
            deviceCodePointId: codePointItem.id,
            deviceCodePointNumber: codePointItem.pointNumber,
            deviceCodePointName: codePointItem.pointName,
            showType: codePointChartTypes[codePointChartIndex],
            refreshRate: 30,
            associatedOrderNumber: '',
            groupCount: 1,
            UCL: '',
            LCL: ''
          });
          codePointChartIndex++;
          if (codePointChartIndex >= codePointChartTypes.length) {
            break;
          }
        }
        deviceList.push(newDeviceItem);
        if (codePointChartIndex >= codePointChartTypes.length) {
          break;
        }
      }
      const localDefaultView = {
        isLocalConfig: true,
        isDefault: 1,
        id: defaultViewId,
        viewName: state.clientName,
        deviceList
      };
      state.currentView = localDefaultView;
      state.viewList.unshift(localDefaultView);
    };

    const closeDrawer = isRefresh => {
      if (isRefresh) {
        getDashboardconfig();
      }
      state.showDrawer = false;
    };
    // 数字时钟
    const getDigitalClock = () => {
      const date = new Date(); // 创建时间对象
      let Hours = date.getHours(); // 获取时
      let Min = date.getMinutes(); // 获取秒
      let Sec = date.getSeconds(); // 获取分
      Hours = Hours < 10 ? '0' + Hours : Hours;
      Min = Min < 10 ? '0' + Min : Min;
      Sec = Sec < 10 ? '0' + Sec : Sec;
      state.digitalClock = Hours + '\t:\t' + Min + '\t:\t' + Sec;
    };
    getDigitalClock();
    setInterval(getDigitalClock, 1000);
    const voiceBroadcast = volume => {
      const localAudio = window.speechSynthesis;
      const audioService = new SpeechSynthesisUtterance();
      const voices = localAudio.getVoices().filter(item => {
        return item.lang === 'zh-CN';
      });
      // console.log(voices)
      localAudio.cancel();
      audioService.text = '';
      audioService.text = state.msg || '测试语音';
      audioService.lang = 'zh-CN';
      audioService.volume = 1;
      audioService.pitch = 1.5;
      audioService.rate = 0.8;
      audioService.voice = voices[0];
      if (state.typesettingData.voiceBroadcastFlag) {
        localAudio.speak(audioService);
      }
    };

    const closeDialog = ({ isRefresh }) => {
      state.dialogShow = false;
      if (isRefresh) {
        getDashboardconfig();
      }
    };

    const handleVoice = () => {
      nextTick(() => {
        state.voice.click();
      });
    };

    const handleSetting = () => {
      state.dialogShow = true;
    };

    return {
      ...toRefs(state),
      isQms,
      handleSetting,
      getDashboardconfig,
      closeDialog,
      closeDrawer,
      getNameByid,
      voiceBroadcast,
      handleVoice,
      formatDate
    };
  },
  computed: {
    ...mapGetters(['sidebar', 'tenantGroup']),
    ...mapState(['tenantInfo'])
  }
};
</script>
<style lang="scss" scoped>
@import 'equipment-monitoring.scss';
</style>
