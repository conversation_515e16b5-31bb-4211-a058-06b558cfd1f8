<template>
  <!-- 样品领用记录 -->
  <ListLayout :has-quick-query="false">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="param">
          <el-input
            v-model="formInline.param"
            v-trim
            v-focus
            placeholder="请输入编号"
            class="ipt-360"
            prefix-icon="el-icon-search"
            clearable
            size="large"
            @keyup.enter="onSubmit"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="onSubmit">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
          <el-button class="searchBtn" type="text" @click="search" @keyup.prevent @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']" />
          </el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button type="primary" size="large" @click="exportExcel()"
        ><span class="iconfont tes-task-issued" @keyup.prevent @keydown.enter.prevent /> 导出</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" :model="searchForm" label-width="110px" label-position="right">
            <el-form-item label="领用日期：">
              <el-date-picker
                v-model="searchForm.rukuDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="changeRuku"
              />
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table no-quick-query base-table format-height-table"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column label="样品编号" prop="secSampleNum" :width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-copy="row.secSampleNum" class="blue" @click="handleDeviceDetail(row)">
            {{ row.secSampleNum || '--' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="样品名称" prop="sampleName" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.sampleName">{{ row.sampleName }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="型号规格" prop="prodType" :min-width="colWidth.model" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.prodType || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="入库日期" prop="localWarehousingTime" :width="colWidth.date">
        <template #default="{ row }">
          <span>{{ formatDate(row.localWarehousingTime) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="存放地" prop="storageLocation" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.storageLocation || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="领用日期" prop="receiveDate" :width="colWidth.date">
        <template #default="{ row }">
          <span>{{ formatDate(row.receiveDate) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="领用数量" prop="receiveNum" :width="colWidth.amount">
        <template #default="{ row }"> {{ row.receiveNum }} {{ filterSampleUnitToName(row.sampleUnitId) }} </template>
      </el-table-column>
      <el-table-column label="领用人" prop="receiveOwnerId" :width="colWidth.person">
        <template #default="{ row }">
          <UserTag v-if="row.receiveOwnerId" :name="getNameByid(row.receiveOwnerId) || row.receiveOwnerId || '--'" />
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import router from '@/router/index.js';
import Pagination from '@/components/Pagination';
import { sampleRecordList } from '@/api/equipment';
import { ElMessage } from 'element-plus';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import ListLayout from '@/components/ListLayout';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import UserTag from '@/components/UserTag';
import { checkPermissionList } from '@/api/permission';
// import { permissionTypeList } from '@/utils/permissionList'
// import { parseTime } from '@/utils'
import { filterSampleUnitToName } from '@/utils/formatJson';
import { colWidth } from '@/data/tableStyle';

export default {
  name: 'SampleRecord',
  components: { Pagination, ListLayout, UserTag },
  setup() {
    const { proxy } = getCurrentInstance();
    // const store = useStore().state
    const editFrom = ref(null);
    const otherForm = reactive({
      accountId: getLoginInfo().accountId,
      showS: false,
      activeName: '0',
      mangeList: [],
      formInline: {
        param: '',
        startTime: '',
        endTime: ''
      },
      searchForm: {
        rukuDateRange: ''
      },
      list: [],
      content: '',
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        sort: 'DESC'
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ]
    });

    function onSubmit() {
      proxy.getList();
    }

    function reset() {
      editFrom.value.resetFields();
      otherForm.formInline = {
        param: '',
        startTime: '',
        endTime: ''
      };
      otherForm.searchForm = {
        prodType: '',
        rukuDateRange: ''
      };
      otherForm.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        sort: 'DESC'
      };
      proxy.getList();
    }

    const search = () => {
      otherForm.showS = !otherForm.showS;
      if (otherForm.activeName === '0') {
        otherForm.activeName = '1';
      } else {
        otherForm.activeName = '0';
      }
    };

    const sortChange = data => {
      const { prop, order } = data;
      otherForm.listQuery.orderBy = prop;
      if (order === 'ascending') {
        otherForm.listQuery.isAsc = true;
      } else if (order === 'descending') {
        otherForm.listQuery.isAsc = false;
      } else {
        otherForm.listQuery.isAsc = null;
      }
    };

    // 点击仪器编号跳转到仪器详情页面
    const handleDeviceDetail = row => {
      router.push({
        path: '/sample/record/detail',
        query: {
          orderId: row.orderId,
          sampleId: row.sampleId
        }
      });
    };

    const inputValue = data => {
      if (typeof data === 'string') {
        otherForm.content = data;
      } else {
        otherForm.content = '';
      }
    };

    // 高级搜索-入库日期-change
    const changeRuku = date => {
      otherForm.formInline.startTime = date ? formatDate(date[0]) : '';
      otherForm.formInline.endTime = date ? formatDate(date[1]) : '';
    };
    // 导出
    const exportExcel = () => {
      proxy.export2Excel();
      ElMessage.success('导出成功！');
    };

    return {
      getPermissionBtn,
      drageHeader,
      handleDeviceDetail,
      formatDate,
      changeRuku,
      getNameByid,
      inputValue,
      sortChange,
      editFrom,
      ...toRefs(otherForm),
      search,
      onSubmit,
      reset,
      exportExcel,
      filterSampleUnitToName,
      colWidth
    };
  },
  created() {
    this.getList();
    // 刷新列表
    this.bus.$on('reloadDeviceRecordList', msg => {
      this.getList();
    });
  },
  methods: {
    getList(data) {
      const _this = this;
      _this.listLoading = true;
      if (data && data !== undefined) {
        _this.listQuery.page = data.page;
        _this.listQuery.limit = data.limit;
      }
      const param = Object.assign(_this.formInline, _this.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      sampleRecordList(param).then(res => {
        if (res !== false && res.data.code === 200) {
          const { data } = res.data;
          _this.list = data.list;
          _this.total = data.totalCount;
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
    },
    getnamelist() {
      checkPermissionList('allocation').then(res => {
        this.mangeList = res.data.data;
      });
    },
    export2Excel() {
      var type = 1;
      var tHeader = [];
      var filterVal = [];
      var fileName = '模板';
      var that = this;
      that.expLoading = true;
      if (type === 1) {
        fileName = '样品领用记录';
        tHeader = ['样品编号', '样品名称', '型号规格', '入库日期', '存放地', '领用日期', '领用数量', '领用人'];
        filterVal = [
          'secSampleNum',
          'sampleName',
          'prodType',
          'localWarehousingTime',
          'storageLocation',
          'receiveDate',
          'receiveNum',
          'receiveOwnerId'
        ];
      }
      import('@/utils/Export2Excel').then(excel => {
        const data = this.formatJson(filterVal, that.list);
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: fileName,
          autoWidth: true,
          bookType: 'xlsx'
        });
        that.expLoading = false;
      });
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v =>
        filterVal.map(j => {
          if (j === 'receiveOwnerId') {
            return getNameByid(v[j]);
          } else if (j === 'receiveNum' && filterSampleUnitToName(v['sampleUnitId'])) {
            return v[j] + filterSampleUnitToName(v['sampleUnitId']);
          } else {
            return v[j];
          }
        })
      );
    }
  }
};
</script>
<style lang="scss" scoped></style>
