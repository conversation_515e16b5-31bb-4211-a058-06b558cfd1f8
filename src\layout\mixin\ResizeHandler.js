import store from '@/store';
import resizeManager from '@/utils/resizeManager';

const { body } = document;
const WIDTH = 992; // refer to Bootstrap's responsive design

export default {
  watch: {
    $route(route) {
      if (this.device === 'mobile' && this.sidebar.opened) {
        store.dispatch('app/closeSideBar', { withoutAnimation: false });
      }
    }
  },
  beforeMount() {
    // 使用统一的resize管理器，避免多个resize监听器冲突
    const resizeHandlerId = `resize-handler-${this.$.uid || Math.random()}`;
    this.$_resizeHandlerId = resizeHandlerId;

    resizeManager.register(
      resizeHandlerId,
      () => {
        this.$_resizeHandler();
      },
      {
        priority: 5, // 中等优先级
        minInterval: 500 // 最小执行间隔500ms
      }
    );
  },
  beforeUnmount() {
    if (this.$_resizeHandlerId) {
      resizeManager.unregister(this.$_resizeHandlerId);
    }
  },
  mounted() {
    const isMobile = this.$_isMobile();
    if (isMobile) {
      store.dispatch('app/toggleDevice', 'mobile');
      store.dispatch('app/closeSideBar', { withoutAnimation: true });
    }
  },
  methods: {
    // use $_ for mixins properties
    // https://vuejs.org/v2/style-guide/index.html#Private-property-names-essential
    $_isMobile() {
      const rect = body.getBoundingClientRect();
      return rect.width - 1 < WIDTH;
    },
    $_resizeHandler() {
      if (!document.hidden) {
        const isMobile = this.$_isMobile();
        store.dispatch('app/toggleDevice', isMobile ? 'mobile' : 'desktop');

        if (isMobile) {
          store.dispatch('app/closeSideBar', { withoutAnimation: true });
        }
      }
    }
  }
};
