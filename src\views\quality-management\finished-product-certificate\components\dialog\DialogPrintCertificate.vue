<template>
  <!-- 合格证打印、批量打印合格证 -->
  <el-dialog
    v-model="dialogShow"
    top="5vh"
    title="合格证打印"
    width="450px"
    :close-on-click-modal="false"
    @close="cancelDialog()"
  >
    <el-form
      v-if="dialogShow"
      ref="printFormRef"
      v-loading="dialogLoading"
      :model="formData"
      label-position="top"
      label-width="120px"
      class="overflow-y-auto pr-2 dialog-form-content"
    >
      <el-form-item
        label="合格证模板"
        prop="printTemplateId"
        :rules="{
          required: true,
          message: '请选择合格证模板',
          trigger: 'change'
        }"
      >
        <el-select
          v-model="formData.printTemplateId"
          filterable
          size="small"
          clearable
          placeholder="请选择合格证模板"
          class="w-full"
        >
          <el-option v-for="item in certificateTemplates" :key="item.id" :label="item.name" :value="item.id">
            <span style="float: left">{{ item.name }}</span>
            <span
              style="
                float: right;
                display: inline-block;
                color: #909399;
                font-size: 13px;
                max-width: 200px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              "
              >{{ item.description }}</span
            >
          </el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item
        label="打印份数"
        prop="printNumber"
        :rules="{
          required: true,
          message: '请输入打印份数',
          trigger: 'change'
        }"
      >
        <el-input-number
          v-model.number="formData.printNumber"
          :min="1"
          :step="1"
          controls-position="right"
          placeholder="请输入打印份数"
          style="width: 100%"
        />
      </el-form-item> -->
      <el-form-item
        label="小盘起始序列号"
        prop="smallReelStartSerialNumber"
        :rules="{
          required: false,
          message: '小盘起始序列号',
          trigger: 'change'
        }"
      >
        <el-row>
          <el-col :span="10">
            <el-input-number
              v-model.number="formData.smallReelStartSerialNumber"
              :min="1"
              :step="1"
              controls-position="right"
              placeholder="起"
              style="width: 100%"
            />
          </el-col>
          <el-col :span="4" class="text-center">~</el-col>
          <el-col :span="10">
            <el-input-number
              v-model.number="formData.smallReelEndSerialNumber"
              :min="1"
              :step="1"
              controls-position="right"
              placeholder="止"
              style="width: 100%"
            />
          </el-col>
        </el-row>
      </el-form-item>
      <div v-if="capabilityVoList.length" class="pb-1">合格证是否打印检测项目</div>
      <el-table
        v-if="capabilityVoList.length"
        ref="multipleTableRef"
        :data="capabilityVoList"
        row-key="id"
        size="small"
        class="dark-table base-table"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" :min-width="55" />
        <el-table-column label="检测项目" :min-width="120">
          <template #default="{ row }">{{ row.capabilityName }}</template>
        </el-table-column>
      </el-table>
    </el-form>
    <template #footer>
      <el-button :loading="dialogLoading" @click="cancelDialog()">取消</el-button>
      <el-button :loading="dialogLoading" type="primary" @click="handleSubmit()">确定</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, watch, ref, toRefs } from 'vue';
// import UserTag from '@/components/UserTag';
import { certificateprintItem } from '@/api/certificate-export';
import { getNameByid } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import router from '@/router';
import { ElMessage } from 'element-plus';
// import { ElMessage } from 'element-plus';
// import { certificateDecide } from '@/api/raw-certificate';
export default {
  name: 'DialogEditCertificate',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    selectRow: {
      type: Object,
      default: () => ({})
    },
    dictionary: {
      type: Object,
      default: () => ({})
    },
    certificateTemplates: {
      type: Array,
      default: () => []
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      formData: {
        printNumber: 1
      },
      dictionaryAll: {
        JHDW: {
          all: {},
          enable: {}
        }
      },
      printFormRef: ref(),
      selectCertificate: [],
      capabilityVoList: [],
      pageViewAll: {},
      dialogLoading: false,
      labelWidth: {
        allowedOperatingTemperature: 200,
        suppDate: 150,
        reelDesc: 150
      },

      dialogShow: false,
      ruleForm: ref()
    });

    const cancelDialog = value => {
      state.dialogShow = false;
      context.emit('closeDialog', value);
    };

    const submitJudgement = () => {};

    watch(
      () => props.dialogVisible,
      newValue => {
        state.dialogShow = newValue;
        if (newValue) {
          state.formData = {
            printNumber: 1
          };
          state.capabilityVoList = [];
          state.selectCertificate = props.selectRow;
          state.dictionaryAll = props.dictionary;
          initFindFinishedProduct();
        }
      }
    );
    const initFindFinishedProduct = async () => {
      if (state.selectCertificate.length == 1) {
        state.dialogLoading = true;
        const { data } = await certificateprintItem(state.selectCertificate[0].sampleId).finally(
          (state.dialogLoading = false)
        );
        state.capabilityVoList = data.data.capabilityVoList;
      }
    };
    const handleSubmit = async () => {
      state.printFormRef
        .validate()
        .then(async valid => {
          if (valid) {
            const templateItem = props.certificateTemplates.find(item => item.id === state.formData.printTemplateId);
            console.log(templateItem);
            if (!templateItem) {
              ElMessage.warning('未找到模板！');
              return;
            }
            const fileUrl = templateItem.fileUrl;

            const ids = state.selectCertificate.map(item => item.sampleId);

            const certificatePrintIds = state.selectCertificate.map(item => item.id);

            await router.push({
              path: '/qualityManagement/online-excel-batch-print',
              query: {
                ids: JSON.stringify(ids),
                certificatePrintIds: JSON.stringify(certificatePrintIds),
                paperSize: templateItem.paperSize,
                paperPrintWidth: templateItem.paperPrintWidth,
                paperPrintHeight: templateItem.paperPrintHeight,
                paperOrientation: templateItem.paperPrintOrientation,
                smallSerialNumber: JSON.stringify([
                  state.formData.smallReelStartSerialNumber,
                  state.formData.smallReelEndSerialNumber
                ]),
                url: import.meta.env.DEV ? fileUrl.replace(window.location.host, '*************') : fileUrl
              }
            });
          } else {
            return false;
          }
        })
        .catch(() => {});
    };
    const handleSelectionChange = selectItem => {
      console.log(selectItem);
    };

    return {
      ...toRefs(state),
      cancelDialog,
      handleSelectionChange,
      handleSubmit,
      getNameByid,
      formatDate,
      submitJudgement
    };
  }
};
</script>
<style lang="scss" scoped>
.dialog-form-content {
  max-height: 700px;
}
</style>
