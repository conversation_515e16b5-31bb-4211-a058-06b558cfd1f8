import request from '@/utils/request';

/**
 * 获取视图列表【查询】
 * @param {string} data.isFixedView
 * @param {string} data.page
 * @param {string} data.limit
 * @returns {array} list
 */
export function getViewListByType(data) {
  return request({
    url: '/api-user/user/sysemployeelistview/listByType',
    method: 'post',
    data
  });
}

/**
 * 通过视图id来获取视图
 * @param {string} id
 * @returns {object}
 */
export function getViewByViewId(id) {
  return request({
    url: `/api-user/user/sysemployeelistview/getInfoById/${id}`,
    method: 'get'
  });
}

/**
 * 通过用户id来获取创建的视图
 * @param {string} userId
 * @returns {object}
 */
export function getViewByBindingMenu(bindingMenu) {
  return request({
    url: `/api-user/user/sysemployeelistview/findInfoByBindingMenu/${bindingMenu}`,
    method: 'get'
  });
}

/**
 * 保存视图
 * @param {string} data.userId
 * @param {string} data.bindingMenu
 * @param {string} data.bindingMenuName
 * @param {string} data.id
 * @param {string} data.viewName
 * @param {string} data.isFixedView
 * @param {string} data.isDefault
 * @param {array} data.sysEmployeeListConfigList
 * @returns {object}
 */
export function saveOrUpdateView(data) {
  return request({
    url: `/api-user/user/sysemployeelistview/saveOrUpdate`,
    method: 'post',
    data
  });
}

/**
 * 重置用户视图
 * @param {string} id
 */
export function setDefaultViewById(id) {
  return request({
    url: `/api-user/user/sysemployeelistview/setDefaultView/${id}`,
    method: 'get'
  });
}

/**
 * 重置用户视图
 * @param {strng} id
 */
export function resetViewById(id) {
  return request({
    url: `/api-user/user/sysemployeelistview/reset/${id}`,
    method: 'get'
  });
}

/**
 * 删除用户视图
 * @param {strng} id
 */
export function deleteViewById(id) {
  return request({
    url: `/api-user/user/sysemployeelistview/delete/${id}`,
    method: 'delete'
  });
}
