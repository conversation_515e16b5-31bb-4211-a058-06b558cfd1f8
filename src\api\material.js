import request from '@/utils/request';

// 物资分类查询列表 /{typeId}}
export function getMaterialcategoryTree(typeId) {
  return request({
    url: `/api-material/material/materialcategory/listTree/${typeId}`,
    method: 'get'
  });
}
// 保存物资分类信息
export function saveMaterialcategory(data) {
  return request({
    url: '/api-material/material/materialcategory/save',
    method: 'post',
    data
  });
}
// 更新物资分类
export function updateMaterialcategory(data) {
  return request({
    url: '/api-material/material/materialcategory/update',
    method: 'put',
    data
  });
}
// 删除物资分类信息
export function deleteMaterialcategory(data) {
  return request({
    url: '/api-material/material/materialcategory/delete',
    method: 'post',
    data
  });
}
// 根据Id查询物资分类信息
export function materialcategoryInfo(id) {
  return request({
    url: `/api-material/material/materialcategory/info/${id}`,
    method: 'get'
  });
}
// 根据物资分类id查询详情信息
export function materialCategorydetailInfo(categoryId) {
  return request({
    url: `/api-material/material/categorydetail/info/${categoryId}`,
    method: 'get'
  });
}
// 更新物资分类的详情
export function updateMaterialcategoryDetailInfo(data) {
  return request({
    url: '/api-material/material/categorydetail/update',
    method: 'put',
    data
  });
}
// 删除物资详情信息
export function deleteMaterialcategoryDetailInfo(data) {
  return request({
    url: '/api-material/material/categorydetail/delete',
    method: 'post',
    data
  });
}
// material查询列表 16类物资dictionaryId:10
export function materialCategoryList(dictionaryId) {
  return request({
    url: `/api-material/material/materialcategory/list/${dictionaryId}`,
    method: 'get'
  });
}
// 产品型号接口列表【查询】
export function getProductModelList(data) {
  return request({
    url: '/api-material/material/categoryproductmodel/list',
    method: 'post',
    data
  });
}
// 修改产品型号接口
export function updateProductModel(data) {
  return request({
    url: '/api-material/material/categoryproductmodel/update',
    method: 'post',
    data
  });
}
// 新增产品型号信息
export function saveProductModel(data) {
  return request({
    url: '/api-material/material/categoryproductmodel/save',
    method: 'post',
    data
  });
}
// 报告模板列表
export function initReportList(data) {
  return request({
    url: '/api-material/material/categoryreporttemplate/list',
    method: 'post',
    data
  });
}
// 启用、停用报告模板
export function changeStatus(data) {
  return request({
    url: '/api-material/material/categoryreporttemplate/updateStatusSwitch',
    method: 'post',
    data
  });
}
// 删除报告模板
export function deleteReport(data) {
  return request({
    url: '/api-material/material/categoryreporttemplate/delete',
    method: 'post',
    data
  });
}
// 上传报告模板
export function addRepore(data) {
  return request({
    url: '/api-material/material/categoryreporttemplate/save',
    method: 'post',
    data
  });
}
// 报告模板编辑
export function editRepore(data) {
  return request({
    url: '/api-material/material/categoryreporttemplate/update',
    method: 'post',
    data
  });
}
// 下载报告
export function downLoad(data) {
  return request({
    url: `/api-material/material/categoryreporttemplate/download`,
    method: 'post',
    data
  });
}
// 合格证模板列表
export function certificateList(data) {
  return request({
    url: '/api-material/categoryCertificateTemplate/list',
    method: 'post',
    data
  });
}

// 删除合格证模板
export function deleteCertificate(data) {
  return request({
    url: '/api-material/categoryCertificateTemplate/delete',
    method: 'post',
    data
  });
}

// 新增合格证模板
export function addCertificate(data) {
  return request({
    url: '/api-material/categoryCertificateTemplate/save',
    method: 'post',
    data
  });
}

// 合格证模板编辑
export function editCertificate(data) {
  return request({
    url: '/api-material/categoryCertificateTemplate/update',
    method: 'post',
    data
  });
}
// 合格证模板启用停用
export function changeCertificateStatus(data) {
  return request({
    url: '/api-material/categoryCertificateTemplate/updateStatusSwitch',
    method: 'post',
    data
  });
}

// 合格证模板下载
export function downLoadCertificate(data) {
  return request({
    url: '/api-material/categoryCertificateTemplate/download',
    method: 'post',
    data
  });
}

// 产品规格列表
export function specificationList(data) {
  return request({
    url: '/api-material/material/categoryproductspecification/list',
    method: 'post',
    data
  });
}
// 产品规格--新增
export function addSpecify(data) {
  return request({
    url: '/api-material/material/categoryproductspecification/add',
    method: 'post',
    data
  });
}
// 产品规格--编辑
export function editSpecify(data) {
  return request({
    url: '/api-material/material/categoryproductspecification/edit',
    method: 'post',
    data
  });
}
// 产品规格--生成编号
export function getNumber(data) {
  return request({
    url: '/api-material/material/categoryproductspecification/getcode',
    method: 'post',
    data
  });
}
// 产品规格排序
export function updateOrderApi(data) {
  return request({
    url: '/api-material/material/categoryproductspecification/updateOrder',
    method: 'post',
    data
  });
}

/**
 * 保存或更新物资分类图片标签信息
 * @param {*} data
 * @returns
 */
export function saveMaterialImageLabel(data) {
  return request({
    url: '/api-material/material/matetialimagelabel/saveOrUpdate',
    method: 'post',
    data
  });
}

/**
 * 根据物资分类Id查询检测项目图片标签信息列表
 * @param {string} materialCategoryId
 * @returns
 */
export function getMaterialImageLabel(materialCategoryId) {
  return request({
    url: `/api-material/material/matetialimagelabel/findByMaterialCategoryId/${materialCategoryId}`,
    method: 'get'
  });
}

/**
 * 删除物资分类图片标签信息
 * @param {string} id
 * @returns
 */
export function deleteMaterialImageLabel(id) {
  return request({
    url: `/api-material/material/matetialimagelabel/delete/${id}`,
    method: 'delete'
  });
}

/**
 * 根据物资分类Code查询物资分类图片标签信息
 * @param {*} materialCategoryCode
 * @returns
 */
export function getImgLabelListByMaterialCode(materialCategoryCode) {
  return request({
    url: `/api-material/material/matetialimagelabel/findByMaterialCategoryCode/${materialCategoryCode}`,
    method: 'get'
  });
}

/**
 * 根据物资分类编号查询样品补充(小样)字段配置信息
 * @param {*} materialCategoryCode
 * * @param {*} configType
 * @returns
 */
export function findByMaterialCategoryCode(materialCategoryCode, configType) {
  return request({
    url: `/api-material/material/samplesupplementaryconfig/findByMaterialCategoryCode/${materialCategoryCode}/${configType}`,
    method: 'get'
  });
}

/**
 * 修改样品补充(小样)字段配置信息
 * @param {*} materialCategoryCode
 * * @param {*} configType
 * @returns
 */
export function saveOrUpdate(data) {
  return request({
    url: `/api-material/material/samplesupplementaryconfig/saveOrUpdate`,
    method: 'post',
    data
  });
}

/**
 * 成品合格证销售订单
 * * @param {*} status
 * @returns
 */
export function getSaleCertification(data) {
  return request({
    url: `/api-diplomat/diplomat/sales_order_detail/queryPageByCertification`,
    method: 'post',
    data
  });
}
