import request from '@/utils/request';

/**
 * 销售订单明细列表查询
 * submitStatus 送检状态 0 待送检 1 已送检
 * @param {{
 *  "limit": "20", "page": "1", "isAsc": true, "key": "", "orderBy": "", "submitStatus": "0"
 * }} data
 * @returns
 */
export function getSalesOrderList(data) {
  return request({
    url: '/api-diplomat/diplomat/sales_order_detail/list',
    method: 'post',
    data
  });
}

/**
 * 根据销售订单明细Id获取送检记录
 * @param {productionOrderDetailId} salesOrderDetailId
 * @returns
 */
export function getInspectionRecordList(salesOrderDetailId) {
  return request({
    url: `/api-diplomat/diplomat/productionorderdetail/recordList/${salesOrderDetailId}`,
    method: 'get'
  });
}

/**
 * 销售订单明细送检
 * @param {{}} data
 * @returns
 */
export function saveInspectionRecord(data) {
  return request({
    url: '/api-diplomat/diplomat/sales_order_detail/submit',
    method: 'post',
    data
  });
}

// #endregion
