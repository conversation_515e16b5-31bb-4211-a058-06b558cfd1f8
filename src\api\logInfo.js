import request from '@/utils/request';

// 所有日志信息查看
export function getAllLogInfo() {
  return request({
    url: `/api-user/user/releaselog/allView`,
    method: 'get'
  });
}
// 根据Id获取日志信息
export function getLogInfoById(id) {
  return request({
    url: `/api-user/user/releaselog/info/${id}`,
    method: 'get'
  });
}
// 日志列表查询列表
export function getLogInfoList(data) {
  return request({
    url: '/api-user/user/releaselog/list',
    method: 'post',
    data
  });
}
// 保存或修改日志信息
export function saveOrUpdateLogInfo(data) {
  return request({
    url: '/api-user/user/releaselog/saveOrUpdate',
    method: 'post',
    data
  });
}
// 删除日志信息
export function deleteLogInfo(data) {
  return request({
    url: '/api-user/user/releaselog/delete',
    method: 'delete',
    data
  });
}
