<template>
  <!-- 租户管理基础配置页面 -->
  <DetailLayout>
    <template #page-header>
      <div class="header-flex flex-between">
        <div class="page-title">
          <span class="title">{{ clientShortName }}</span>
        </div>
        <div class="btn-group">
          <el-button @click="goBack">返回列表</el-button>
        </div>
      </div>
    </template>
    <div v-loading="detailLoading" class="page-main">
      <div class="module">
        <div class="title">系统名称</div>
        <div class="content">
          <span v-if="!isEditSystemName">{{ detailInfo.systemName }}</span>
          <div v-if="isEditSystemName" class="inlineBlock inputSystem">
            <el-input v-model="detailInfo.systemName" placeholder="请输入系统名称" />
          </div>
          <div class="inlineBlock btnGroup">
            <el-button v-if="isEditSystemName" size="small" @click="handleCancal('systemName')">取消</el-button>
            <el-button v-if="isEditSystemName" type="primary" size="small" @click="onSaveSystemName">保存</el-button>
            <el-button v-if="!isEditSystemName" type="primary" size="small" @click="isEditSystemName = true"
              >编辑</el-button
            >
          </div>
        </div>
      </div>
      <div class="module">
        <div class="title">导航栏LOGO设置</div>
        <div class="content logoSetting">
          <div class="inlineBlock">
            <el-upload
              ref="uploadRef"
              :action="imgAction"
              :headers="headerconfig"
              :accept="'.jpg, .jpeg, .svg, .png'"
              :auto-upload="false"
              :show-file-list="false"
              :on-success="handleFileSuccess"
              :on-change="handleChange"
              :before-upload="beforeUpload"
            >
              <template #trigger>
                <img v-if="detailInfo.normalLogo" :src="detailInfo.normalLogo" class="avatar" />
                <img v-if="!detailInfo.normalLogo" :src="logo_title" class="sidebar-title" />
              </template>
              <div class="inlineBlock">
                <div class="btnGroup">
                  <el-button size="small" @click="restoreDefault">恢复默认</el-button>
                  <el-button type="primary" size="small" @click="submitUpload">更新</el-button>
                </div>
              </div>
              <template #tip>
                <div class="uploadTitle">建议上传100*20的图片（点击图片上传）</div>
              </template>
            </el-upload>
          </div>
        </div>
      </div>
      <div class="module">
        <div class="title">模板分页是否重复模板头</div>
        <div class="content">
          <el-radio-group v-model="detailInfo.templatePageRepeatHead" @change="changeTemplatePageHeader">
            <el-radio :label="true">重复模板头</el-radio>
            <el-radio :label="false">不重复模板头</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="module">
        <div class="title">原始记录模板表头样式</div>
        <div class="content">
          <el-radio-group v-model="detailInfo.templateStyle" @change="changeTemplate('templateStyle')">
            <el-radio :label="2">标准模式</el-radio>
            <el-radio :label="1">简洁模式</el-radio>
            <el-radio :label="0">无表头</el-radio>
            <el-radio :label="3">中国电科院</el-radio>
          </el-radio-group>
          <el-button
            size="mini"
            type="primary"
            style="float: right"
            @click="handleChangeHeader()"
            v-text="isTransverse === 1 ? '预览纵向模板表头' : '预览横向模板表头'"
          />
          <BaseTemplate
            v-if="detailInfo.templateStyle !== 0"
            :is-only-header="true"
            :is-transverse="isTransverse === 1"
            :head-type="detailInfo.templateStyle"
          />
        </div>
      </div>
      <div class="module">
        <div class="title">原始记录模板表尾样式</div>
        <div class="content">
          <el-radio-group v-model="detailInfo.templateTailStyle" @change="changeTemplate('templateTailStyle')">
            <el-radio :label="0">标准模式</el-radio>
            <el-radio :label="1">中国电科院</el-radio>
          </el-radio-group>
          <el-button
            size="mini"
            type="primary"
            style="float: right"
            @click="handleChangeFooter()"
            v-text="isTransverseFooter === 1 ? '预览纵向模板表尾' : '预览横向模板表尾'"
          />
          <BaseTemplate
            :is-only-footer="true"
            :is-transverse-footer="isTransverseFooter === 1"
            :template-tail-style="detailInfo.templateTailStyle"
          />
        </div>
      </div>
    </div>
  </DetailLayout>
</template>

<script>
import { reactive, toRefs, getCurrentInstance, ref, onMounted, onUnmounted } from 'vue';
import DetailLayout from '@/components/DetailLayout';
import BaseTemplate from '@/views/excelComponents/components/BaseTemplate';
import { getSettingInfo, configName, configStyle, deleteLogo, pageRepeatHead } from '@/api/tenantPermission';
import { oauthinformationConfigLogoUploadUrl } from '@/api/uploadAction';
import { getLoginInfo, setTenantConfig, getTenantConfig, getToken } from '@/utils/auth';
// import '@/views/excelComponents/commonPart/excelcommon.scss';
// import '@/views/excelComponents/excelstyle.scss';
// import '@/views/excelComponents/module.scss';
import router from '@/router/index.js';
import { useRoute } from 'vue-router';
import logoTitle from '@/assets/img/logo-title.svg';
import emptyImg from '@/assets/img/empty-img.png';
import { mounted, unmounted } from '@/views/excelComponents/template-style-loader';

export default {
  name: 'DetailBasic',
  components: { DetailLayout, BaseTemplate },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    const state = reactive({
      tenantId: route.query.strId,
      clientShortName: route.query.clientShortName,
      isTransverse: 0,
      isTransverseFooter: 0,
      emptyImg,
      imgAction: oauthinformationConfigLogoUploadUrl(route.query.strId),
      detailInfo: {},
      detailLoading: false,
      uploadRef: ref(),
      oldDetailInfo: {},
      isEditSystemName: false,
      headerconfig: {
        Authorization: getToken()
      }
    });

    onMounted(() => {
      mounted();
    });

    onUnmounted(() => {
      unmounted();
    });

    // 获取配置信息
    const getInformation = type => {
      state.detailLoading = true;
      getSettingInfo(state.tenantId).then(res => {
        state.detailLoading = false;
        if (res) {
          if (type) {
            state.detailInfo[type] = res.data.data[type];
            state.oldDetailInfo[type] = JSON.parse(JSON.stringify(res.data.data[type]));
            if (state.tenantId === getLoginInfo().tenantId) {
              const tenantConfig = getTenantConfig();
              tenantConfig[type] = state.detailInfo[type];
              setTenantConfig(tenantConfig);
            }
          } else {
            state.detailInfo = res.data.data;
            state.oldDetailInfo = JSON.parse(JSON.stringify(res.data.data));
          }
        }
      });
    };
    getInformation();
    const goBack = () => {
      router.go(-1);
    };
    const beforeUpload = file => {
      var fileName = '';
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      const fileSize = file.size / 1024 / 1024 < 20;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过20M');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else if (fileName !== 'png' && fileName !== 'svg' && fileName !== 'jpeg' && fileName !== 'jpg') {
        proxy.$message.error('请上传图片类型的文件');
        return false;
      } else {
        return true;
      }
    };
    const handleFileSuccess = res => {
      if (res.code === 200) {
        if (state.tenantId === getLoginInfo().tenantId) {
          proxy.$message.success('更新成功，刷新页面立即生效');
        } else {
          proxy.$message.success('上传成功');
        }
        getInformation('normalLogo');
      } else {
        proxy.$message.error(res.data);
      }
    };
    const onSaveSystemName = () => {
      if (!state.detailInfo.systemName) {
        proxy.$message.error('请输入系统名称');
        return false;
      }
      state.detailLoading = true;
      configName({ tenantId: state.tenantId, systemName: state.detailInfo.systemName }).then(res => {
        state.detailLoading = false;
        if (res) {
          state.isEditSystemName = false;
          proxy.$message.success(res.data.message);
          getInformation('systemName');
        }
      });
    };
    const handleCancal = type => {
      state.isEditSystemName = false;
      state.detailInfo[type] = state.oldDetailInfo[type];
    };
    // 切换模板表头 切换模板表头
    const changeTemplate = type => {
      state.detailLoading = true;
      configStyle({
        tenantId: state.tenantId,
        templateTailStyle: state.detailInfo.templateTailStyle,
        templateStyle: state.detailInfo.templateStyle
      }).then(res => {
        state.detailLoading = false;
        if (res) {
          proxy.$message.success(res.data.message);
          getInformation(type);
        }
      });
    };
    const changeTemplatePageHeader = () => {
      pageRepeatHead({
        tenantId: state.tenantId,
        templatePageRepeatHead: state.detailInfo.templatePageRepeatHead
      }).then(res => {
        state.detailLoading = false;
        if (res) {
          proxy.$message.success(res.data.message);
          getInformation('templatePageRepeatHead');
        }
      });
    };

    const submitUpload = () => {
      state.uploadRef.submit();
    };
    const handleChange = file => {
      const url = window.URL.createObjectURL(file.raw);
      state.detailInfo.normalLogo = url;
    };
    // 恢复默认
    const restoreDefault = () => {
      deleteLogo(state.tenantId).then(res => {
        if (res) {
          if (state.tenantId === getLoginInfo().tenantId) {
            proxy.$message.success('更新成功，刷新页面立即生效');
          } else {
            proxy.$message.success(res.data.message);
          }
          getInformation('normalLogo');
        }
      });
    };
    const handleChangeHeader = () => {
      if (state.isTransverse) {
        state.isTransverse = 0;
      } else {
        state.isTransverse = 1;
      }
    };
    const handleChangeFooter = () => {
      if (state.isTransverseFooter) {
        state.isTransverseFooter = 0;
      } else {
        state.isTransverseFooter = 1;
      }
    };
    return {
      ...toRefs(state),
      getInformation,
      changeTemplatePageHeader,
      restoreDefault,
      handleChange,
      handleChangeHeader,
      handleChangeFooter,
      submitUpload,
      beforeUpload,
      handleCancal,
      handleFileSuccess,
      onSaveSystemName,
      changeTemplate,
      goBack,
      logo_title: logoTitle
    };
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/mixin.scss';

.inlineBlock {
  display: inline-block;
}
.btnGroup {
  margin-left: 30px;
}
.inputSystem {
  width: 30%;
}
// :deep(.el-upload) {
//   border: 1px dashed gray;
//   width: 100px;
//   height: 40px;
//   line-height: 40px;
//   text-align: center;
//   vertical-align: middle;
// }
i {
  font-size: 16px;
}
:deep(.el-empty) {
  padding: 0;
}
.module {
  text-align: left;
  .title {
    line-height: 20px;
    font-size: 16px;
    text-align: left;
    font-weight: 400;
    position: relative;
    padding: 0 12px;
    &:before {
      content: ' ';
      width: 4px;
      height: 16px;
      background: #00b38a;
      position: absolute;
      left: 0;
      top: 2px;
    }
  }
  .content {
    line-height: 20px;
    background-color: #f0f2f5;
    padding: 10px;
    margin: 20px 0;
  }
}
:deep(.el-empty__description) {
  display: none;
}
.logoSetting {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.uploadTitle {
  margin: 1px 0 0 0;
  font-size: 12px;
  color: #808080;
}
:deep(.el-empty__image) {
  width: 100px;
}
</style>
