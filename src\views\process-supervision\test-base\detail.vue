<template>
  <!-- 检测标准库详情页 -->
  <ListLayout
    :has-page-header="true"
    :has-quick-query="false"
    :has-left-panel="true"
    :aside-panel-width="400"
    :main-offset-top="76"
    :has-bottom-panel="true"
  >
    <template #search-bar>
      <div class="page-title">
        产品名称：
        <el-tooltip :content="detailData.productName" effect="light" placement="bottom">
          <span class="title">{{ detailData.productName }}</span>
        </el-tooltip>
        <el-tag v-if="versionList.length > 0">{{ 'V' + lastVersion.version }}</el-tag>
        <span v-if="isDraft" class="draftClass">（草稿）</span>
      </div>
    </template>
    <template #button-group>
      <div v-if="!isEditDetail && versionList.length > 1" class="historyList">
        <span>历史版本：</span>
        <div class="btnGroup">
          <el-scrollbar>
            <el-radio-group
              v-model="radioVersion"
              size="mini"
              class="scrollbar-flex-content"
              @change="handleChangeVersion"
            >
              <el-radio-button v-for="item in versionList" :key="item.id" :label="item.id">
                {{ item.version }}
              </el-radio-button>
            </el-radio-group>
          </el-scrollbar>
        </div>
      </div>
      <el-button size="small" :loading="listLoading" @click="handleClose">返回列表</el-button>
      <el-button v-if="getPermissionBtn('derivedStandard')" size="small" :loading="listLoading" @click="downloadBase()"
        >导出标准</el-button
      >
      <el-button
        v-if="isEditDetail && getPermissionBtn('importStandard')"
        size="small"
        :loading="listLoading"
        @click="openUploadDialog"
        >导入标准</el-button
      >
      <el-button
        v-if="isEditDetail && isDraft"
        icon="iconfont tes-delete"
        type="danger"
        size="small"
        :loading="listLoading"
        @click="deleteDraft"
      >
        删除草稿
      </el-button>
      <el-button
        v-if="isEditDetail && !listLoading && isModified"
        type="primary"
        size="small"
        :loading="listLoading"
        @click="onSubmitTable(false)"
        >保 存</el-button
      >
      <el-button
        v-if="isEditDetail && !listLoading && (isDraft || (!isDraft && isModified))"
        type="primary"
        size="small"
        :loading="listLoading"
        @click="onSubmitTable(true)"
        >发 布</el-button
      >
    </template>
    <template #page-left-side>
      <el-table
        v-if="isShow"
        id="sortableList"
        ref="tableRef"
        :data="tableList"
        fit
        border
        height="auto"
        :size="tableSize"
        highlight-current-row
        class="detailTable base-table format-height-table dark-table"
        :row-style="
          () => {
            return 'cursor: pointer';
          }
        "
        @row-click="clickRow"
      >
        <el-table-column label="项目/子项目" prop="name" :min-width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="nowrap">
              <span v-if="isEditDetail" class="sortingIcon tes-move iconfont" style="font-size: 10px; cursor: move" />
              {{ row.name }}
              <span style="font-size: 12px">（{{ row.sourcenumber }}）</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column width="70" align="center">
          <template #header>
            <el-tooltip v-if="isEditDetail" content="添加项目" effect="light" placement="top-end">
              <el-button
                v-if="isEditDetail"
                size="mini"
                icon="el-icon-plus"
                class="btnAddItem"
                round
                type="primary"
                @click="addProject"
                @keyup.prevent
                @keydown.enter.prevent
              />
            </el-tooltip>
          </template>
          <template #default="{ row }">
            <i v-if="isEditDetail" class="blue-color iconfont tes-delete" @click.stop="handleDelete(row)" />
          </template>
        </el-table-column>
      </el-table>
    </template>
    <el-container v-loading="listLoading" class="detail-wrapper">
      <!-- <el-aside class="tableAside" style="width: 400px; margin-bottom: 0; position: relative">
      </el-aside> -->
      <el-container>
        <el-main class="gjcsMain">
          <TableForm
            v-if="tableList.length > 0 && isEditDetail"
            ref="tableForm"
            :tree-data="itemTreeData"
            :row-detail="rowDetail"
            :material-code="detailData.materialCategoryCode"
            :table-index="rowDetail.order"
            :unit-list="unitListArray"
            @detail="getRowDetail"
          />
          <CheckTableForm
            v-if="tableList.length > 0 && !isEditDetail"
            ref="tableFormCheck"
            :tree-data="itemTreeData"
            :row-detail="rowDetail"
            :unit-json="unitJson"
            :table-index="rowDetail.order"
          />
          <div v-if="tableList.length === 0" class="noData">
            <el-empty :image="emptyImg" description="暂无数据" />
          </div>
        </el-main>
      </el-container>
    </el-container>

    <template #other>
      <AddInspectionItem
        :show="showAdd"
        type="testBase"
        :material-category-code="detailData.materialCategoryCode"
        :data="alreadyList"
        @close="closeDialog"
        @selectData="selectData"
      />
      <el-dialog
        v-model="sortDialog"
        :title="isAddTree === true ? '添加类目' : '编辑类目'"
        width="480px"
        :close-on-click-modal="false"
      >
        <el-form
          v-if="sortDialog"
          ref="formTree"
          :model="formDataDialog"
          :rules="dialogRules"
          label-position="right"
          label-width="120px"
          size="small"
        >
          <el-form-item
            label="类目名称："
            prop="name"
            :rules="{ required: true, message: '请输入类目名称', trigger: 'change' }"
          >
            <el-input v-model.trim="formDataDialog.name" autocomplete="off" placeholder="请输入类目名称" />
          </el-form-item>
          <el-form-item label="父级分类：">
            <el-cascader
              v-model="formDataDialog.parentId"
              :options="dialogTreeData"
              :props="categoryProps"
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="sortDialog = false">取 消</el-button>
            <el-button type="primary" @click="isCanEditTree">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <DialogUploadStandard
        :visible="uploadDialogVisible"
        :upload-params="{
          materialCategoryCode: detailData.materialCategoryCode,
          standardCategoryId: detailData.checkTreeId
        }"
        :lists="standardList"
        @close="closeUploadDialog"
      />
      <!-- 导入 -->
      <DialogImportTestBase
        :visible="dialogImport"
        :detail-data="{
          materialCategoryCode: detailData.materialCategoryCode,
          standardCategoryId: detailData.checkTreeId,
          standardProductIdList: [detailData.productId]
        }"
        :specify-list="[]"
        @close="handleCloseImport"
      />
    </template>
  </ListLayout>
</template>
<script>
import { ref, watch, reactive, getCurrentInstance, toRefs, nextTick } from 'vue';
import { getLoginInfo } from '@/utils/auth';
import {
  getItemSort,
  addItemTree,
  updateItemTree,
  deleteItemTree,
  getItemList,
  canDeleteSort,
  updateTable,
  issueTable,
  updateOrderTree2,
  getVersionApi,
  deleteDraftApi,
  findByFeats,
  standardcategoryExport
} from '@/api/testBase';
import { getDictionary } from '@/api/user';
import { getCapabilityTree } from '@/api/user';
import { formatTree, formatAllTree } from '@/utils/formatJson';
import AddInspectionItem from '@/components/BusinessComponents/AddInspectionItem';
import { getNameByid } from '@/utils/common';
import { useRoute } from 'vue-router';
import TableForm from './components/tableForm';
import CheckTableForm from './components/checkTableForm';
import { formatDate } from '@/utils/formatTime';
import router from '@/router/index.js';
import ListLayout from '@/components/ListLayout';
import Sortable from 'sortablejs';
import DialogUploadStandard from './components/DialogUploadStandard.vue';
import { getPermissionBtn } from '@/utils/common';
import _ from 'lodash';
import emptyImg from '@/assets/img/empty-data.png';
import DialogImportTestBase from './components/DialogImportTestBase.vue';
export default {
  name: 'DetailBase',
  components: { AddInspectionItem, TableForm, CheckTableForm, ListLayout, DialogUploadStandard, DialogImportTestBase },
  props: {},
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    const state = reactive({
      asideWidth: 240,
      dialogImport: false,
      isShow: true,
      versionList: [],
      rowDetail: {},
      radioVersion: '',
      typeOfData: {
        数值型: 1,
        枚举型: 2,
        字符串: 3,
        日期型: 4,
        自定义枚举: 5
      },
      typeOfData2: {
        1: '数值型',
        2: '枚举型',
        3: '字符串',
        4: '日期型',
        5: '自定义枚举'
      },
      oldRowIndex: '',
      isRefresh: true,
      deletedList: [], // 删除的数据id
      accountId: getLoginInfo().accountId, // 当前登录人的id
      listLoading: false,
      isDraft: false, // 是否是草稿状态
      unitJson: {}, // 字典单位
      unitListArray: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ], // 字典单位数组类型
      sortDialog: false, // 项目类目弹出框
      isModified: false, // 是否修改过， true修改过，false没有修改过
      checkTreeId: '', // 选中树的节点id
      checkNodeOrder: '', // 选中的树的order
      filterText: '',
      leftTreeData: [],
      treeData: [],
      alreadyList: [], // 已经添加过的数据
      isAddTree: false,
      formDataDialog: {}, // 树节点编辑表单
      dialogTreeData: [],
      itemTreeData: [], // 项目分类的树
      dialogRules: {},
      dialogSort: false, // 项目分类弹出框
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      showAdd: false,
      tableSize: 'medium',
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      tableList: [],
      detailData: {},
      lastVersion: {}, // 版本列表最后一个的信息
      versionId: '', // 版本id
      isEditDetail: false,
      tableRef: ref(null),
      ruleForm: ref(0),
      formTree: ref(null),
      leftTreeRef: ref(null),
      uploadDialogVisible: false,
      standardList: []
    });
    watch(
      () => state.filterText,
      newValue => {
        state.leftTreeRef.filter(newValue);
      }
    );
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    const handleClose = () => {
      if (state.isModified) {
        proxy
          .$confirm('确认离开当前页面吗？离开后数据不可恢复', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false
          })
          .then(() => {
            state.tableList = [];
            state.isModified = false;
            goBack();
          })
          .catch(() => {});
      } else {
        state.tableList = [];
        goBack();
      }
    };
    const getVersionList = (isTree, isTrue) => {
      getVersionApi(state.detailData.productId).then(res => {
        if (res) {
          state.versionList = res.data.data.versionList;
          // 历史版本最后一个的信息
          state.lastVersion = state.versionList[state.versionList.length - 1];
          if (state.versionList.length > 0) {
            state.versionId = state.lastVersion.id;
          } else {
            state.versionId = state.detailData.versionId;
          }
          if (state.lastVersion.status === 0) {
            // 草稿状态
            state.isDraft = true;
          } else {
            // 没有草稿状态
            state.isDraft = false;
            state.radioVersion = state.lastVersion.id;
          }
          if (isTree) {
            getLeftTree(state.versionId, isTrue);
          }
        }
      });
    };
    watch(
      () => router.currentRoute.value.path,
      (newValue, oldValue) => {
        state.deletedList = [];
        state.detailData = route.query;
        // state.versionId = state.detailData.versionId
        state.formDataDialog.standardProductVersionId = state.versionId;
        if (state.detailData.isEdit === 'true') {
          state.isEditDetail = true;
        } else {
          state.isEditDetail = false;
        }
        state.oldRowIndex = 0;
      },
      { immediate: true }
    );
    getVersionList();
    // 获取左侧项目分类树 isRefresh是否确定刷新，区分新增和修改树节点, true刷新
    const getLeftTree = (id, isRefresh) => {
      getItemSort(id).then(res => {
        const treeData = formatTree(res.data.data);
        if (treeData.length > 0) {
          state.leftTreeData = JSON.parse(JSON.stringify(treeData));
          const all = { id: 'all', name: '全部' };
          state.leftTreeData.unshift(all);
        } else {
          state.leftTreeData = treeData;
        }
        state.dialogTreeData = treeData;
        state.itemTreeData = state.dialogTreeData;
        state.oldRowIndex = 0;
        if (treeData.length > 0) {
          nextTick(() => {
            state.checkTreeId = state.leftTreeData[0].id;
            getTableList();
          });
        } else {
          state.tableList = [];
          state.itemTreeData = [];
        }
      });
    };
    nextTick(() => {
      getVersionList(true, true);
    });
    // 导出标准
    const downloadBase = () => {
      state.listLoading = true;
      const params = {
        standardCategoryId: state.detailData.checkTreeId,
        materialCategoryCode: state.detailData.materialCategoryCode,
        standardProductIdList: [state.detailData.productId]
      };
      standardcategoryExport(params).then(res => {
        state.listLoading = false;
        const blob = new Blob([res.data], { type: '' });
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        // state.detailData.productName + ' （V' + state.lastVersion.version + '）.xlsx';
        a.download = state.detailData.productName + '.xlsx';
        a.href = blobUrl;
        a.click();
        proxy.$message.success('下载附件成功');
      });
    };
    // 树节点点击事件
    const clickNode = node => {
      if (state.checkTreeId !== node.id) {
        if (state.isModified) {
          state.leftTreeRef.setCurrentKey(state.checkTreeId, true);
          proxy
            .$confirm('当前分类数据未保存，是否确认切换', {
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'warning'
            })
            .then(() => {
              state.checkTreeId = node.id;
              state.oldRowIndex = 0;
              state.leftTreeRef.setCurrentKey(state.checkTreeId, true);
              state.isModified = false;
              getTableList();
            })
            .catch(() => {});
        } else {
          state.checkTreeId = node.id;
          state.oldRowIndex = 0;
          state.leftTreeRef.setCurrentKey(state.checkTreeId, true);
          getTableList();
        }
      }
    };
    // 获取项目信息列表
    const getTableList = () => {
      state.listLoading = true;
      getItemList({
        projectCategoryId: '',
        standardProductVersionId: state.versionId
      }).then(res => {
        state.listLoading = false;
        if (res) {
          initData(res);
        }
      });
    };
    const initData = res => {
      state.tableList = res.data.data;
      if (state.tableList.length > 0) {
        nextTick(async () => {
          if (state.oldRowIndex) {
            state.tableRef.setCurrentRow(state.tableList[state.oldRowIndex]);
            state.rowDetail = state.tableList[state.oldRowIndex];
          } else {
            state.oldRowIndex = 0;
            state.tableRef.setCurrentRow(state.tableList[0]);
            state.rowDetail = state.tableList[0];
          }
          showTable();
        });
      }
    };
    // 添加项目
    const addProject = () => {
      state.alreadyList = [];
      state.tableList.forEach(item => {
        state.alreadyList.push({
          ...item,
          capabilityparaVoList: item.custList,
          capabilityId: item.capabilityId
        });
      });
      nextTick(() => {
        state.showAdd = true;
      });
    };
    // 新增、编辑、删除树节点
    const handleEditTree = (type, node) => {
      if (type === 'add') {
        state.sortDialog = true;
        state.isAddTree = true;
        state.formDataDialog = {
          standardProductVersionId: state.versionId
        };
      } else {
        const data = node.data;
        if (type === 'delete') {
          proxy
            .$confirm('是否删除该类目', '删除确认', {
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'warning'
            })
            .then(() => {
              state.listLoading = true;
              canDeleteSort(data.id).then(res => {
                state.listLoading = false;
                if (res.data.data) {
                  deleteItemTree(data.id).then(function (res) {
                    if (res) {
                      proxy.$message.success(res.data.message);
                      getVersionList(true, true);
                    }
                  });
                } else {
                  proxy.$message.error('该分类下有项目，不允许删除');
                }
              });
            })
            .catch(() => {});
        } else {
          state.sortDialog = true;
          state.isAddTree = false;
          state.dialogTreeData = formatAllTree(data.id, state.dialogTreeData);
          state.formDataDialog = JSON.parse(JSON.stringify(data));
        }
      }
    };
    // 判断是否能修改项目分类，表单若已经修改提示先保存表单
    const isCanEditTree = () => {
      if (state.isModified) {
        proxy
          .$confirm('表单已修改，是否确认先保存表单', '提示', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(() => {
            onSubmitTable(false);
          })
          .catch(() => {
            state.isModified = false;
            onSubmitTree();
          });
      } else {
        onSubmitTree();
      }
    };
    // 提交操作树节点的表单结果(新增和编辑)
    const onSubmitTree = () => {
      state.formTree.validate().then(valid => {
        if (valid) {
          var parentId = state.formDataDialog.parentId;
          if (state.formDataDialog.parentId instanceof Array) {
            if (state.formDataDialog.parentId.length > 0) {
              parentId = state.formDataDialog.parentId[state.formDataDialog.parentId.length - 1].toString();
            } else {
              parentId = '';
            }
          }
          const params = {
            ...state.formDataDialog,
            parentId: parentId
          };
          if (state.isAddTree) {
            state.listLoading = true;
            addItemTree(params).then(res => {
              state.listLoading = false;
              if (res) {
                state.sortDialog = false;
                getVersionList(true, false);
                proxy.$message.success(res.data.message);
              } else {
                proxy.$message.error(res.data.message);
              }
            });
          } else {
            state.listLoading = true;
            updateItemTree(params).then(res => {
              state.listLoading = false;
              if (res) {
                state.sortDialog = false;
                getVersionList(true, false);
                proxy.$message.success(res.data.message);
              }
            });
          }
        }
      });
    };
    // 提交表格数据
    const onSubmitTable = isIssue => {
      // 如果有要提交的数据
      if (state.tableList.length > 0) {
        onSaveTable(isIssue);
      } else {
        state.listLoading = true;
        updateTable({
          parameterList: [],
          standardProductVersionId: state.versionId
        }).then(res => {
          state.listLoading = false;
          if (res) {
            proxy.$message.success(res.data.message);
            state.isModified = false;
            state.oldRowIndex = 0;
            state.rowDetail = {};
            goBack();
          }
        });
      }
    };
    // 表单的提交
    const onSaveTable = isIssue => {
      proxy.$refs['tableForm']
        .onSubmit()
        .then(valid => {
          if (isIssue) {
            state.listLoading = true;
            issueTable({
              parameterList: state.tableList,
              standardProductVersionId: state.versionId,
              standardProductId: state.detailData.productId
            }).then(res => {
              state.listLoading = false;
              if (res) {
                proxy.$message.success(res.data.message);
                state.isModified = false;
                goBack();
              }
            });
          } else {
            state.listLoading = true;
            updateTable({
              parameterList: state.tableList,
              standardProductVersionId: state.versionId
            }).then(res => {
              state.listLoading = false;
              if (res) {
                proxy.$message.success(res.data.message);
                state.isModified = false;
                // 如果切换了项目分类保存之后高亮第一个
                getVersionList(true, false);
              }
            });
          }
        })
        .catch(res => {
          proxy.$message.error('请按要求填写');
        });
    };
    const clickRow = async row => {
      if (state.oldRowIndex === row.order) {
        return false;
      }
      if (state.isEditDetail) {
        proxy.$refs['tableForm']
          .onSubmit()
          .then(valid => {
            if (valid) {
              state.oldRowIndex = row.order;
              state.rowDetail = JSON.parse(JSON.stringify(row));
              state.tableRef.setCurrentRow(state.tableList[state.oldRowIndex]);
            }
          })
          .catch(res => {
            state.tableRef.setCurrentRow(state.tableList[state.oldRowIndex]);
            proxy.$message.error('请先按要求填写');
          });
      } else {
        state.rowDetail = JSON.parse(JSON.stringify(row));
        state.oldRowIndex = row.order;
        state.tableRef.setCurrentRow(state.tableList[state.oldRowIndex]);
      }
    };
    // 调整项目分类
    const handleSort = row => {
      state.dialogSort = true;
    };
    const handleDelete = row => {
      if (row.id) {
        state.isModified = true;
        state.deletedList.push(row.id);
      }
      state.tableList.splice(row.order, 1);
      state.tableList.forEach((val, index) => {
        val.order = index;
      });
      if (row.order === state.oldRowIndex) {
        if (state.oldRowIndex > 0) {
          state.oldRowIndex = state.oldRowIndex - 1;
        }
        state.tableRef.setCurrentRow(state.tableList[state.oldRowIndex]);
        state.rowDetail = state.tableList[state.oldRowIndex] || {};
      } else if (row.order < state.oldRowIndex) {
        state.oldRowIndex = state.oldRowIndex - 1;
        state.tableRef.setCurrentRow(state.tableList[state.oldRowIndex]);
        state.rowDetail = state.tableList[state.oldRowIndex] || {};
      }
    };
    // 改动的数据传入提交表单里面
    const getRowDetail = val => {
      state.tableList[val.order] = val;
      state.isModified = true;
      // state.rowDetail = val
      state.tableRef.setCurrentRow(state.tableList[state.oldRowIndex]);
    };
    // 关闭新增项目
    const closeDialog = value => {
      state.showAdd = value;
    };
    // 获取新增的项目
    const selectData = async data => {
      const addArray = [];
      var childList = [];
      data.forEach((val, index) => {
        childList = [];
        val.capabilityparaVoList.forEach((item, i) => {
          childList.push({
            okflag: true,
            minselected: false,
            maxseleced: false,
            applylabel: item.applylabel,
            standardCategoryId: state.detailData.checkTreeId,
            capabilityParaId: item.id,
            unitname: item.unitname,
            resultOptionType: item.resultOptionType,
            smallnumber: isNaN(Number(item.resultoption)) ? undefined : Number(item.resultoption),
            resulttype: state.typeOfData[item.resulttype] || '',
            standardmathtype: state.typeOfData[item.resulttype] === 1 ? 1 : '',
            englishname: item.englishname,
            name: item.name,
            paramterId: item.paramterId,
            custlabel: item.resultoption, // 合格和不合格的下拉选项
            qualifiedOption: [], // 合格选项
            noQualifiedOption: [] // 不合格选项
          });
        });
        addArray.push({
          custList: childList,
          capabilityparaEntityList: childList,
          parameterlabal: val.method ? val.method.split(',') : [], // 试验方法的下拉选项
          order: state.tableList.length + index,
          status: 0,
          name: val.name,
          englishname: val.englishname,
          requirement: val.requirement,
          sourcenumber: val.sourcenumber,
          lastUpdateByUserId: state.accountId,
          capabilityId: val.id,
          lastUpdateDateTime: formatDate(new Date()),
          empiricalApproach: [] // 试验方法选中的值
        });
      });
      // 获取新增项目的所有检测依据
      const newData = await getSpecificationsAll(addArray);
      if (newData.length) {
        newData.forEach(item => {
          state.tableList.push(item);
        });
        if (state.tableList.length === data.length) {
          state.oldRowIndex = 0;
          state.tableRef.setCurrentRow(state.tableList[state.oldRowIndex]);
          nextTick(() => {
            state.rowDetail = state.tableList[state.oldRowIndex];
          });
        }
        state.isModified = true;
      }
    };
    const getSpecificationsAll = newArray => {
      return new Promise(resolve => {
        newArray.forEach(async item => {
          item.basisList = await getSpecifications(item);
          if (item.basisList?.length === 1) {
            item.capabilityBasisId = item.basisList[0].basisId;
            const selectBasisInfo = item.basisList[0];
            item.custList.forEach((item, index) => {
              // 循环给关键参数赋值
              const paraMapInfo = selectBasisInfo.paraMap[item.capabilityParaId];
              item.standardmathtype = paraMapInfo?.standardMathType;
              item.resulttype = item.resulttype ? item.resulttype : 1;
              item.requirement = paraMapInfo?.requirement;
              item.minselected = paraMapInfo?.minSelected;
              item.maxseleced = paraMapInfo?.maxSelected;
              item.maxNum = paraMapInfo?.maxNum;
              item.minNum = paraMapInfo?.minNum;
            });
          }
        });
        resolve(newArray);
      });
    };
    // 获取匹配的检测依据
    const getSpecifications = selectItem => {
      return new Promise((resolve, reject) => {
        const params = {
          capabilityId: selectItem.capabilityId,
          specIdValueMap: JSON.parse(route.query.specifications)
        };
        findByFeats(params)
          .then(res => {
            resolve(res.data.data);
          })
          .catch(error => {
            reject(error);
          });
      });
    };
    const changeIcon = (command, node) => {
      if (!command) {
        node.showIcon = true;
      } else {
        node.showIcon = !node.showIcon;
      }
    };
    // 获取单位
    const getUnitList = () => {
      getDictionary(13).then(res => {
        if (res) {
          state.unitListArray[0].group = [];
          state.unitListArray[1].group = [];
          res.data.data.dictionaryoption.forEach(item => {
            if (item.status === 1) {
              state.unitListArray[0].group.push(item);
            } else {
              state.unitListArray[1].group.push(item);
            }
            state.unitJson[item.code] = item.name;
          });
        }
      });
    };
    // 允许拖拽功能 只能同级拖拽
    const allowDrop = (draggingNode, dropNode, type) => {
      if (draggingNode.level === dropNode.level) {
        // parentid是父节点id
        if (draggingNode.data.parentId === dropNode.data.parentId) {
          return type === 'prev' || type === 'next';
        }
      } else {
        // 不同级进行处理
        return false;
      }
    };
    // 树排序
    const nodeDrop = (before, after) => {
      var orderList = [];
      if (after.parent.level === 0) {
        orderList = JSON.parse(JSON.stringify(after.parent.data));
        _.remove(orderList, function (n) {
          return n.id === 'all';
        });
        orderList.forEach((ol, index) => {
          ol.order = index + 1;
        });
      } else {
        orderList = after.parent.data.children;
        orderList.forEach((ol2, index) => {
          ol2.order = index + 1;
        });
      }
      updateOrderTree2(orderList).then(res => {
        if (res !== false) {
          proxy.$message.success('排序成功');
        }
      });
    };
    // 行拖拽
    const rowDrop = () => {
      // 获取当前表格
      if (state.isEditDetail) {
        const el = document.getElementById('sortableList').querySelector('.el-table__body-wrapper tbody');
        Sortable.create(el, {
          animation: 300,
          handle: '.tes-move',
          draggable: '.el-table__row',
          ghostClass: 'ghost',
          // 拖动对象移动样式
          dragClass: 'drag',
          forceFallback: true,
          onEnd({ newIndex, oldIndex }) {
            if (oldIndex !== newIndex) {
              const currRow = state.tableList.splice(oldIndex, 1)[0];
              state.oldRowIndex = newIndex;
              if (currRow.capabilityId === state.rowDetail.capabilityId) {
                state.rowDetail.order = newIndex;
              }
              state.tableList.splice(newIndex, 0, currRow);
              state.tableList.forEach((value, index) => {
                value.order = index;
              });
              state.isModified = true;
              showTable();
            }
          }
        });
      }
    };
    const showTable = () => {
      state.isShow = false;
      setTimeout(() => {
        state.isShow = true;
        nextTick(() => {
          state.tableRef.setCurrentRow(state.tableList[state.oldRowIndex]);
          rowDrop();
        });
      }, 0);
    };
    // 返回列表
    const goBack = () => {
      router.push({
        path: '/testBaseList',
        query: {
          checkTreeId: state.detailData.checkTreeId,
          materialCategoryCode: state.detailData.materialCategoryCode
        }
      });
    };
    // 删除草稿
    const deleteDraft = () => {
      proxy
        .$confirm('是否确认删除草稿', '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          deleteDraftApi(state.versionId).then(res => {
            if (res) {
              proxy.$message.success(res.data.message);
              if (state.versionList.length === 1) {
                goBack();
              } else {
                getVersionList(true, true);
              }
            }
          });
        })
        .catch(() => {});
    };
    // 切换本版
    const handleChangeVersion = versionId => {
      state.versionId = versionId;
      state.formDataDialog.standardProductVersionId = state.versionId;
      getLeftTree(versionId, true);
    };

    // #region 导入/导出标准

    const getCategoryTree = async code => {
      const res = await getCapabilityTree(code);
      if (res) {
        const data = res.data.data;
        state.treeData = formatTree(data);
        state.treeData.unshift({
          id: '-1',
          parentId: '0',
          materialCategoryCode: data.length > 0 ? data[0].materialCategoryCode : '266013',
          name: '全部',
          order: 0,
          status: 2
        });
        return true;
      }
      return false;
    };

    const closeUploadDialog = async val => {
      state.uploadDialogVisible = false;
      if (val) {
        // window.location.href = window.location.href || window.location.origin
        await getCategoryTree(state.detailData.materialCategoryCode);
        getVersionList(true, true);
      }
    };
    // 批量导入
    const openUploadDialog = () => {
      state.standardList = [];
      state.standardList.push({ id: state.detailData.versionId });
      state.dialogImport = true;
    };

    const handleCloseImport = isRefresh => {
      state.dialogImport = false;
      if (isRefresh) {
        getTableList();
      }
    };

    // #endregion

    getUnitList();
    return {
      ...toRefs(state),
      emptyImg,
      handleCloseImport,
      getPermissionBtn,
      downloadBase,
      handleChangeVersion,
      goBack,
      getVersionList,
      deleteDraft,
      showTable,
      allowDrop,
      nodeDrop,
      getUnitList,
      changeIcon,
      clickRow,
      isCanEditTree,
      filterNode,
      getTableList,
      initData,
      onSubmitTable,
      getNameByid,
      formatDate,
      addProject,
      closeDialog,
      selectData,
      getRowDetail,
      handleDelete,
      handleSort,
      onSaveTable,
      handleClose,
      handleEditTree,
      onSubmitTree,
      clickNode,
      closeUploadDialog,
      openUploadDialog
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/tree.scss';
.page-title {
  .title {
    max-width: 400px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.draftClass {
  font-size: 14px;
}
:deep(.el-radio-button) {
  margin-right: 10px;
}
:deep(.el-radio-button__inner) {
  border-left: 1px solid #dcdfe6;
  border-radius: 4px;
}
:deep(.el-radio-button:last-child .el-radio-button__inner) {
  border-radius: 4px;
}
:deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-radius: 4px;
}
.historyList {
  display: inline-block;
  margin-right: 10px;
  text-align: left;
}
.btnGroup {
  display: inline-block;
  min-width: 500px;
  max-width: 700px;
  top: 18px;
  position: relative;
  // padding-bottom: 5px;
}
.scrollbar-flex-content {
  display: flex;
  padding-bottom: 8px;
}
.btnAddItem {
  position: relative;
  left: -8px;
  // top: 27px;
  // right: 31px;
  z-index: 9;
}
.page-wrapper .page-list-main .page-main {
  padding: 0;
  background-color: transparent;
}
:deep(.el-container .el-main .page-main) {
  padding: 0;
  height: 100%;
  background: transparent;
}
:deep(.page-list-main .el-container .el-main) {
  background: transparent;
}
.page-wrapper .page-list-main {
  height: calc(100vh - 20rem);
}
:deep(.page-list-main .main-panel) {
  height: calc(100vh - 11rem);
  padding: 0 12px 0 20px;
}
:deep(.el-container .el-aside) {
  background: $background-color;
}
.tree-container .tree-content {
  height: calc(100%);
}

.tableAside.el-aside {
  padding: 20px;
}
.tableAndCs {
  height: 100%;
}
.gjcsMain {
  margin-left: 10px;
  .noData {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.el-footer {
  margin: 0 12px 0 22px;
  background: #fff;
  padding: 10px;
}
.format-height-table {
  :deep(.el-table__body-wrapper) {
    max-height: calc(100vh - 20rem);
    overflow-x: hidden !important;
  }
}
.frBtn {
  float: right;
}
.frBtn2 {
  float: right;
  margin-right: 10px;
}
.el-aside {
  background: #fff;
}
.el-main {
  padding: 0;
}
.checkRadio {
  margin-left: 6px;
}
.detailProduct {
  padding: 0;
  .el-aside-left {
    padding: 0;
  }
}
.top {
  display: inline-block;
}
.topRight {
  float: right;
}
.radiusBtn {
  border-radius: 20px;
}
:deep(.el-input-number--medium .el-input-number__decrease) {
  width: 16.7px;
  background: #fff;
}
.selectUnit {
  width: 100px;
  margin-left: 52px;
}
:deep(.el-cascader--medium) {
  line-height: 28px;
}

.detail-wrapper {
  height: calc(100%);
}
</style>
