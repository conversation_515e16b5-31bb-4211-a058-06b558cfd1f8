import { getAttachment, getName } from '@/api/login';
// import store from '@/store'
import { filterNameToStr } from '@/utils/chinese-to-str';
import { getDictionary } from '@/api/user';
import { getBusinessCodeList } from '@/api/businessCode';
import axios from 'axios';

const state = {
  nameList: [],
  allNameList: {},
  SignatureImg: [],
  processModeList: [],
  configURL: {},
  bussinessCodeList: []
};
const mutations = {
  SET_NAMELIST: (state, value) => {
    state.nameList = value;
  },
  SET_ALL_NAMELIST: (state, value) => {
    state.allNameList = value;
  },
  SET_IMG: (state, value) => {
    state.SignatureImg = value;
  },
  SET_PROCESS_MODELIST: (state, list) => {
    state.processModeList = list;
  },
  SET_CONFIG_URL: (state, configURL) => {
    state.configURL = configURL;
  },
  SET_BUSSINESS_CODE_LIST: (state, list) => {
    state.bussinessCodeList = list;
  }
};
// 获取人员列表信息
const actions = {
  setNameList({ commit }) {
    return new Promise(resolve => {
      getName().then(res => {
        if (res !== false) {
          const resultData = res.data.data;
          const allNameList = {};
          let userOptions = [];
          resultData.forEach(item => {
            allNameList[item.id] = item;
            if (item.isJob && item.status) {
              userOptions.push(item);
            }
          });
          userOptions = filterNameToStr(userOptions);
          commit('SET_NAMELIST', userOptions);
          commit('SET_ALL_NAMELIST', allNameList);
          resolve(resultData);
        } else {
          // store.dispatch('user/resetToken')
          resolve([]);
        }
        resolve(false);
      });
    });
  },
  // 获取签名
  getSignatureImg({ commit }, ids) {
    return new Promise(resolve => {
      var SignatureImg = [];
      if (ids && ids !== undefined) {
        getAttachment(ids).then(res => {
          SignatureImg = res.data.data;
          commit('SET_IMG', SignatureImg);
          resolve(SignatureImg);
        });
      } else {
        resolve(SignatureImg);
      }
    });
  },
  // 获取处理方式
  getProcessModeList({ commit }) {
    return new Promise(resolve => {
      var list = [];
      getDictionary(3).then(res => {
        if (res.data.code === 200) {
          list = res.data.data.dictionaryoption;
          commit('SET_PROCESS_MODELIST', list);
          resolve(list);
        }
      });
    });
  },
  getConfig({ commit }) {
    return new Promise(resolve => {
      var baseUrl = window.location.origin;
      axios({
        url: baseUrl + '/config.json',
        method: 'get',
        responseType: 'json'
      })
        .then(function (response) {
          commit('SET_CONFIG_URL', response.data);
          resolve(response.data);
        })
        .catch(function (error) {
          console.log(error);
        });
    });
  },
  getBusinessCodeStoreList({ commit }) {
    return new Promise(resolve => {
      let list = [];
      const params = {
        condition: '',
        page: '1',
        limit: '-1'
      };
      getBusinessCodeList(params).then(res => {
        if (res) {
          list = res.data.data.list;
          commit('SET_BUSSINESS_CODE_LIST', list);
          resolve(list);
        }
      });
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
