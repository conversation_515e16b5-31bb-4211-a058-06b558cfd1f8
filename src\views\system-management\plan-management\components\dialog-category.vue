<template>
  <!-- 类目维护 -->
  <el-dialog v-model="dialogVisiable" title="类目维护" :close-on-click-modal="false" :width="800" @close="handleClose">
    <div class="btn_group text-right">
      <el-button v-if="!isEdit && !isAdd" size="small" class="el-icon-edit" @click="handleEdit()"> 编辑</el-button>
      <el-button v-if="isEdit" size="small" class="el-icon-close" @click="cancleEdit()"> 取消编辑</el-button>
      <el-button v-if="!isEdit" type="primary" size="small" class="el-icon-plus" @click="handleAdd()"> 新增</el-button>
    </div>
    <el-form ref="formRef" :model="formData" label-width="0" size="small">
      <el-table
        ref="tableRef"
        :key="tableKey"
        v-loading="dialogLoading"
        :data="formData.tableList"
        fit
        border
        size="medium"
        class="dark-table base-table format-height-table no-quick-query"
        @header-dragend="drageHeader"
      >
        <el-table-column label="类目编号" prop="code" :min-width="colWidth.orderNo" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.code || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="类目名称" prop="name" :min-width="colWidth.orderNo" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="isEdit || !row.id"
              :prop="`tableList.${$index}.name`"
              :rules="{ required: true, message: '请输入类目名称', trigger: 'change' }"
            >
              <el-input
                :ref="`inputRef${$index}`"
                v-model="row.name"
                v-trim
                autocomplete="off"
                placeholder="请输入类目名称"
              />
            </el-form-item>
            <span v-else>{{ row.name || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="日历主题色" prop="color" :width="colWidth.xxys" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="isEdit || !row.id"
              :prop="`tableList.${$index}.color`"
              :rules="{ required: true, message: '请输入类目名称', trigger: 'change' }"
            >
              <el-select v-model="row.color" placeholder="请选择视图类型" style="width: 100%" size="small">
                <el-option v-for="(val, key) in colorList" :key="key" :value="key" :label="val.value">
                  <div class="color_group">
                    <div class="color_left" :style="`border: 1px solid rgba(${val.color},0.3);`">
                      <span :style="`background-color: rgba(${val.color},1);`" />
                    </div>
                    <span class="color_right">{{ val.value }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            <div v-else class="color_group">
              <div class="color_left" :style="`border: 1px solid rgba(${colorList[row.color].color},0.3);`">
                <span :style="`background-color: rgba(${colorList[row.color].color},1);`" />
              </div>
              <span>{{ colorList[row.color].value }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" :min-width="colWidth.status">
          <template #default="{ row, $index }">
            <el-form-item v-if="isEdit || !row.id" :prop="`tableList.${$index}.status`">
              <el-select
                v-if="isEdit || !row.id"
                v-model="row.status"
                placeholder="请选择状态"
                style="width: 100%"
                size="small"
              >
                <el-option v-for="(val, key) in statusJSON" :key="key" :label="val.edit" :value="Number(key)" />
              </el-select>
            </el-form-item>
            <el-tag v-else :type="statusJSON[row.status].type">{{ statusJSON[row.status].label }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column v-if="isAdd" label="操作" :width="colWidth.operationSingle">
          <template #default="{ row, $index }">
            <span v-if="!row.id" class="blue-color" @click="handleDelete($index)">删除</span>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
// getCurrentInstance
import { reactive, ref, toRefs, watch, getCurrentInstance, nextTick } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { plancategorySave } from '@/api/planManagement';
import store from '@/store';
export default {
  name: 'DialogCalendar',
  components: {},
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    detailList: {
      type: Array,
      default: () => []
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableKey: 0,
      formData: {
        tableList: []
      },
      oldTableList: [],
      formRef: ref(),
      type: '',
      isAllDay: false, // 是否是全天
      nameList: store.state.common.nameList,
      dialogLoading: false,
      ruleInfo: {},
      isEdit: false,
      isAdd: false,
      dialogVisiable: false,
      ruleForm: ref(),
      colorList: {
        red: { color: '250, 81, 81', value: '红色' },
        orange: { color: '255, 143, 31', value: '橙色' },
        yellow: { color: '255, 195, 0', value: '黄色' },
        green: { color: '0, 181, 120', value: '绿色' },
        cyan: { color: '7, 185, 185', value: '青色' },
        blue: { color: '54, 98, 236', value: '蓝色' },
        purple: { color: '138, 56, 245', value: '紫色' },
        magenta: { color: '235, 47, 150', value: '品红色' }
      },
      statusJSON: {
        1: { type: 'success', label: '已启用', edit: '启用' },
        0: { type: 'info', label: '未启用', edit: '停用' }
      },
      inputRef: ref(),
      loading: false
    });
    watch(props, newValue => {
      state.dialogVisiable = props.dialogShow;
      if (state.dialogVisiable) {
        state.isAdd = false;
        state.isEdit = false;
        state.formData.tableList = JSON.parse(JSON.stringify(props.detailList));
        state.oldTableList = JSON.parse(JSON.stringify(props.detailList));
      }
    });
    const onSubmit = () => {
      proxy.$refs['formRef']
        .validate()
        .then(valid => {
          if (valid) {
            plancategorySave({ planCategoryEntityList: state.formData.tableList }).then(res => {
              if (res) {
                proxy.$message.success('保存成功！');
                context.emit('closeDialog', true);
              }
            });
          }
        })
        .catch(error => {
          const fieldName = Object.keys(error)[0];
          proxy.$message.warning(error[fieldName][0].message);
          return false;
        });
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog');
    };
    const handleAdd = () => {
      state.isAdd = true;
      state.formData.tableList.push({
        status: 1,
        code: `BH${state.formData.tableList.length + 1}`
      });
      nextTick(() => {
        if (proxy.$refs[`inputRef${state.formData.tableList.length - 1}`]) {
          proxy.$refs[`inputRef${state.formData.tableList.length - 1}`].focus();
        }
      });
    };
    const handleEdit = () => {
      state.isEdit = true;
      nextTick(() => {
        if (proxy.$refs[`inputRef0`]) {
          proxy.$refs[`inputRef0`].focus();
        }
      });
    };
    // 取消编辑
    const cancleEdit = () => {
      state.isEdit = false;
      state.formData.tableList = JSON.parse(JSON.stringify(state.oldTableList));
    };
    // 删除
    const handleDelete = index => {
      state.formData.tableList.splice(index, 1);
      if (state.formData.tableList.length === state.oldTableList.length) {
        state.isAdd = false;
      }
    };
    return {
      ...toRefs(state),
      handleDelete,
      cancleEdit,
      handleAdd,
      handleEdit,
      onSubmit,
      handleClose,
      formatDate,
      colWidth,
      drageHeader
    };
  }
};
</script>
<style lang="scss" scoped>
.color_group {
  display: flex;
  align-items: center;
  gap: 8px;
}
.color_left {
  display: inline-flex;
  border-radius: 50%;
  padding: 1px;
  align-items: center;
  justify-content: center;
  span {
    display: inline-flex;
    width: 8px;
    height: 8px;
    border-radius: 8px;
  }
}
.el-form .el-form-item {
  margin: 0;
}
.btn_group {
  margin: 0 0 20px 0;
}
.selectOption {
  display: flex;
  .optionName {
    flex: 1;
  }
  .optionRemark {
    font-size: 12px;
    color: $tes-font3;
  }
}
h3 {
  display: inline-block;
  position: absolute;
  left: 20px;
}

::v-deep(.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 37.5rem) !important;
    overflow-y: auto;
  }
}
</style>
