export const colWidth = {
  amount: 110, // 数量
  batch: 140, // 批次
  checkbox: 55, // 复选框
  tag: 70, // 含有标记的编号/单号
  date: 130, // 日期
  dates: 250, // 时间段
  datetime: 160, // 年月日时分秒
  dateranger: 200, // 日期范围
  description: 220, // 描述 min
  email: 180, // 邮箱 min
  expand: 40, // 展开，折叠
  level: 100, // 项目等级
  projectName: 180, // 项目名称
  material: 180, // 物料(名称) min
  materialGroup: 140, // 分组
  model: 180, // 型号规格 min
  name: 200, // 名称/对象位置 min
  orderNo: 150, // 编号/单号
  objectNo: 150, // 检验对象
  operation: 120, // 操作 默认2项
  operationSingle: 80, // 操作-1项
  operationMultiple: 160, // 操作-3项
  operationMultiples: 180, // 操作-4项
  plate: 150, // 盘号
  person: 130, // 人名
  people: 180, // 人名-多人 min
  phone: 140, // 手机号
  process: 180, // 工序、进度
  productionOrderNo: 160, // 生产指令号
  productionQuantity: 100,
  remark: 200, // 备注 min
  result: 120, // 结果
  paraValue: 180, // 报告结果
  serialNo: 70, // 序号
  status: 120, // 状态
  typeGroup: 120, // 类型
  unit: 110, // 单位
  version: 125, // 版本号
  cycle: 100, // 周期
  departmentUnit: 140, // 部门单位
  measurementResult: 180, // 计量结果
  telephone: 200, // 电话
  address: 250, // 地址
  taxNo: 200, // 税号
  xxys: 180, // 线芯颜色
  inputSelect: 130, // 状态选择框
  customerName: 180, // 客户名称
  equipmentName: 180, // 设备名称
  testResults: 180, // 检测结果
  fileName: 220, // 附件名称
  mobile: 160, // 电话
  IP: 140, //
  URL: 220,
  requestBody: 250, // 请求体
  requestMethod: 90, // 请求方法
  errorMessage: 200, // 错误信息
  sampleNo: 112, // 样品编号
  workPoints: 90, // 工分
  customerInvoiceStatus: 180, // 付款方式，发票类型
  money: 150 // 费用
};

/** 表格列宽 - 字符长度 Char1表示1个中文字符长度 */
export const ColumnWidth = {
  Char1: 48,
  Char2: 62,
  Char3: 76,
  Char4: 90,
  Char5: 104,
  Char6: 118,
  Char7: 132,
  Char8: 146,
  Char9: 160,
  Char10: 174,
  Char11: 188,
  Char12: 202,
  Char13: 216,
  Char14: 230,
  Char15: 244,
  Char16: 258,
  Char17: 272,
  Char18: 286,
  Char19: 300,
  Char20: 314,
  Char21: 328,
  Char22: 342,
  Char23: 356,
  Char24: 370
};
