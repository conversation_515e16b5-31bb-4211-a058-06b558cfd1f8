<template>
  <!-- 选择委托单 -->
  <el-dialog
    v-model="dialogShow"
    custom-class="custom-dialog dialog_select_order"
    title="选择委托单"
    width="60%"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <div class="dialog-header">
      <div class="header-left">
        <el-input
          ref="inputRef"
          v-model="param"
          v-trim
          class="search"
          size="small"
          placeholder="请输入委托编号/委托方/缴款方"
          prefix-icon="el-icon-search"
          clearable
          @clear="getTableList()"
          @keyup.enter="getTableList()"
        />
        <el-button type="primary" size="small" @click="getTableList()">查询</el-button>
      </div>
    </div>
    <div class="dialog-content">
      <el-table
        ref="tableRef"
        v-loading="dialogLoading"
        :data="tableList"
        size="medium"
        fit
        border
        height="auto"
        class="dark-table base-table format-height-table3"
        :row-style="
          () => {
            return 'cursor: pointer';
          }
        "
        @header-dragend="drageHeader"
        @row-click="handleRowClick"
        @selection-change="selectChange"
      >
        <el-table-column type="selection" :width="colWidth.checkbox" align="center" :selectable="selectable" />
        <el-table-column
          label="委托编号"
          prop="entrustNo"
          :min-width="colWidth.order"
          align="left"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ row.entrustNo || '--' }}
          </template>
        </el-table-column>
        <el-table-column
          label="委托方"
          prop="entrustName"
          :min-width="colWidth.order"
          align="left"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ row.entrustName || '--' }}
          </template>
        </el-table-column>
        <el-table-column
          label="委托金额(￥)"
          prop="entrustCost"
          :min-width="colWidth.money"
          align="left"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ row.entrustCost || '--' }}
          </template>
        </el-table-column>
        <el-table-column
          label="已收金额(￥)"
          prop="paidCost"
          :min-width="colWidth.money"
          align="left"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ row.paidCost || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="登记日期" prop="regDate" :min-width="colWidth.date" align="left" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.regDate || '--' }}
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :page="listQuery.page"
        :limit="listQuery.limit"
        :total="total"
        @pagination="getTableList"
      />
    </div>
    <div class="dialog-other">
      <div class="title">
        <label>已选委托编号</label>
        <el-button v-if="tags.length > 0" size="small" icon="el-icon-delete" @click="clear">清空</el-button>
      </div>
      <div v-if="oldTags.length > 0 || tags.length > 0" class="select-items">
        <el-tag v-for="tag in oldTags" :key="tag.name" :closable="tag.closable" size="small" @close="closeTag(tag)">
          {{ tag.entrustNo }}
        </el-tag>
        <el-tag v-for="tag in tags" :key="tag.name" closable size="small" @close="closeTag(tag)">
          {{ tag.entrustNo }}
        </el-tag>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="dialogSuccess">确定选择</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, nextTick } from 'vue';
import {} from '@/api/user';
import Pagination from '@/components/Pagination';
import { getTaskRegistrationList } from '@/api/task-registration';
import { colWidth } from '@/data/tableStyle';
import { drageHeader } from '@/utils/formatTable';

export default {
  name: 'DialogSelectOrder',
  components: { Pagination },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    detailInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    alreadySelect: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    const state = reactive({
      dialogShow: false,
      inputRef: ref(),
      param: '',
      treeRef: ref(),
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      tags: [],
      tableRef: ref(),
      oldTags: [],
      tableList: [],
      detailData: {},
      dialogLoading: false,
      total: 0,
      listQuery: {
        page: 1,
        limit: 20
      }
    });

    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.param = '';
        state.detailData = props.detailInfo;
        getTableList();
        nextTick(() => {
          state.inputRef.focus();
        });
        state.tags = [];
        state.oldTags = JSON.parse(JSON.stringify(newValue.alreadySelect));
      }
    });
    const getTableList = query => {
      const params = {
        status: 3,
        payerCustomerId: state.detailData.payerCustomerId,
        param: state.param,
        settleStatus: 0
      };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.dialogLoading = true;
      getTaskRegistrationList(params).then(res => {
        state.dialogLoading = false;
        if (res !== false) {
          state.total = res.data.data.totalCount;
          state.tableList = res.data.data.list;
          state.tableList.forEach((item, index) => {
            if (
              state.oldTags.some(val => {
                return val.entrustId === item.id;
              })
            ) {
              item.alreadySelected = true;
            }
          });
        }
      });
    };
    // 确定选择
    const dialogSuccess = () => {
      context.emit('closeDialog', state.tags);
      state.dialogShow = false;
    };
    // 取消选择
    const close = () => {
      state.dialogShow = false;
      context.emit('closeDialog', false);
    };
    // 关闭tags
    const closeTag = tag => {
      state.tags.splice(state.tags.indexOf(tag), 1);
    };
    // 清空
    const clear = () => {
      state.tags = [];
      state.tableRef.clearSelection();
    };

    // 表格勾选框的禁用规则
    const selectable = row => {
      if (row.alreadySelected) {
        return false;
      } else {
        return true;
      }
    };
    const handleRowClick = row => {
      if (!row.alreadySelected) {
        state.tableRef.toggleRowSelection(
          row,
          !state.tags.some(item => {
            return row.id === item.id;
          })
        );
      }
    };
    const selectChange = val => {
      state.tags = val;
    };
    return {
      ...toRefs(state),
      handleRowClick,
      drageHeader,
      selectable,
      selectChange,
      colWidth,
      getTableList,
      dialogSuccess,
      close,
      closeTag,
      clear
    };
  }
};
</script>
<style lang="scss">
@import '@/styles/dialog.scss';
</style>
<style lang="scss">
.dialog_select_order {
  .format-height-table3 {
    .el-table__body-wrapper {
      max-height: calc(100vh - 36.5rem) !important;
      overflow-y: auto;
    }
  }
}
</style>
