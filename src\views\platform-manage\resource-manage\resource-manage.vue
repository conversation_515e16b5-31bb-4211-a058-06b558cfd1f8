<!--
  关于资源说明 ：资源管理这边分3级菜单，包括一级菜单、子菜单、按钮
    一级菜单：  type： 1，
              hidden(表示是否是内部页面，1：内部；0：外部；这边所谓的内部页面是指菜单里面不需要展示出来的页面)：1
              注意：一级菜单全部是外部页面所以这边hidden都是1；
              visible(表示是否隐藏该页面): true，  一级菜单全部显示该菜单
    子菜单：  type： 2,
              子菜单要是内部页面（hidden:0）,就需要关联父级页面，这样可以保证内部页面打开的时候可以看到父级的菜单是哪个
    按钮：    type：4
-->
<template>
  <ListLayout
    class="resource-management"
    :has-search-panel="false"
    :has-quick-query="false"
    :has-button-group="getPermissionBtn('addResourceBtn') ? true : false"
  >
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="condition">
          <el-input
            v-model="formInline.condition"
            v-trim
            v-focus
            placeholder="请输入资源名称搜索"
            class="ipt-360"
            prefix-icon="el-icon-search"
            size="large"
            clearable
            @keyup.enter="onSubmit(formInline.condition)"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="onSubmit(formInline.condition)">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button type="primary" icon="el-icon-plus" size="large" @click="addResource">新增资源</el-button>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      size="medium"
      height="auto"
      :default-expand-all="false"
      row-key="id"
      :tree-props="treeProps"
      class="dark-table base-table self-table format-height-table no-quick-query"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column label="资源KEY" prop="key" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.key || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="资源名称" prop="title" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.title || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="资源类型" prop="type">
        <template #default="{ row }">
          <span>{{ filterType(row.type) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="getPermissionBtn('editResourceBtn')"
        label="操作"
        width="100px"
        prop="caozuo"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <span class="blue-color" @click="editResource(row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination v-show="total>0" :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getList" /> -->
    <template #other>
      <add-edit-resource
        :show="showDialog"
        :title="dialogTitle"
        :data="dialogData"
        @close="closeDialog"
        @setInfo="setInfo"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, toRefs, getCurrentInstance } from 'vue';
// import { ElMessage, ElMessageBox } from 'element-plus'
import ListLayout from '@/components/ListLayout';
import { getPermissionBtn } from '@/utils/common';
// import { useStore } from 'vuex'
import { getLoginInfo } from '@/utils/auth';
import _ from 'lodash';
// import { checkPermissionList } from '@/api/permission'
// import { permissionTypeList } from '@/utils/permissionList'
import { getMenuList } from '@/api/platform-management';
// import Pagination from '@/components/Pagination'
import { drageHeader } from '@/utils/formatTable';
import AddEditResource from './addEditResource.vue';
import { colWidth } from '@/data/tableStyle';

export default {
  name: 'ResourceManagement',
  components: { AddEditResource, ListLayout },
  setup() {
    const { proxy } = getCurrentInstance();
    // const store = useStore().state
    const datas = reactive({
      accountId: getLoginInfo().accountId,
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: null
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      treeProps: {
        children: 'children',
        hasChildren: true // 'hasChildren'
      },
      list: [],
      copyList: [],
      formInline: {
        condition: ''
      },
      showDialog: false,
      dialogTitle: '',
      dialogData: null
    });
    // 查询
    function onSubmit(val) {
      // proxy.getList()
      if (val) {
        const list = [];
        datas.copyList.forEach(user => {
          if (user.title.indexOf(val) !== -1) {
            list.push(user);
          } else {
            if (user.children && user.children.length > 0) {
              const item = _.filter(user.children, function (us) {
                return us.title.indexOf(val) !== -1;
              });
              var newData = JSON.parse(JSON.stringify(user));
              if (item.length > 0) {
                newData.children = item;
                list.push(newData);
              }
            }
          }
        });
        datas.list = list;
      } else {
        datas.list = datas.copyList;
      }
    }
    // 重置
    function reset() {
      datas.formInline = {
        condition: ''
      };
      datas.list = datas.copyList;
    }
    // table排序
    const sortChange = data => {
      const { prop, order } = data;
      datas.listQuery.orderBy = prop;
      if (order === 'ascending') {
        datas.listQuery.isAsc = true;
      } else if (order === 'descending') {
        datas.listQuery.isAsc = false;
      } else {
        datas.listQuery.isAsc = null;
      }
    };
    // 新增
    const addResource = () => {
      datas.dialogTitle = 'add';
      datas.dialogData = null;
      datas.showDialog = true;
    };
    // 编辑资源
    const editResource = row => {
      datas.dialogTitle = 'edit';
      datas.dialogData = row;
      datas.showDialog = true;
    };
    // 关闭弹出框
    const closeDialog = v => {
      datas.showDialog = false;
    };
    // 弹出框-确认
    const setInfo = data => {
      datas.showDialog = false;
      proxy.getList();
    };
    // 过滤这样类型
    const filterType = type => {
      // 1：一级菜单 2：子菜单 3：页面 4：按钮/表单/字段
      const map = {
        1: '一级菜单',
        2: '子菜单',
        3: '页面',
        4: '按钮/表单/字段'
      };
      return map[type];
    };
    // 过滤是否有子菜单 hasChildren
    const filterMenus = (menus, parent) => {
      if (menus && menus.length > 0) {
        menus.forEach(m => {
          m.hasChildren = false;
          if (parent) {
            m.parent = parent;
          }
          var p = {
            id: m.id,
            parentId: m.parentId,
            key: m.key,
            title: m.title
          };
          if (m.children && m.children.length > 0) {
            m.hasChildren = true;
            filterMenus(m.children, p);
          }
        });
      }
      return menus;
    };

    return {
      ...toRefs(datas),
      onSubmit,
      reset,
      sortChange,
      getPermissionBtn,
      drageHeader,
      addResource,
      closeDialog,
      setInfo,
      filterType,
      filterMenus,
      editResource,
      colWidth
    };
  },
  created() {
    this.getList();
    // 刷新列表
    this.bus.$on('resourceManagementList', msg => {
      this.getList();
    });
  },
  methods: {
    getList(data) {
      const _this = this;
      // _this.listLoading = true
      if (data && data !== undefined) {
        _this.listQuery.page = data.page;
        _this.listQuery.limit = data.limit;
      }
      const param = Object.assign(_this.formInline, _this.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      getMenuList(param).then(res => {
        if (res !== false) {
          const { data } = res.data;
          _this.list = _this.filterMenus(data);
          _this.copyList = JSON.parse(JSON.stringify(_this.list));
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.format-height-table.no-quick-query.self-table.el-table) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 225px);
  }
}
</style>
