<template>
  <!-- 处置意见页面 -->
  <div class="disposalOpinion">
    <div
      v-if="lastStep.processParameter.isAssent === 1 && lastStep.endTime == ''"
      class="register-title"
      style="display: flex"
    >
      处置意见
      <span class="name">（待办人：{{ getNameByid(lastStep.processParameter.assignee) }}）</span>
      <div class="textR" style="flex: 1">
        <el-button
          v-if="!isFill && getPermissionBtn('DisposalOpinions') && lastStep.endTime == ''"
          type="primary"
          size="small"
          icon="el-icon-plus"
          :disabled="lastStep.processParameter.assignee != currentAccountId"
          @click="isFill = true"
          >处置意见</el-button
        >
      </div>
    </div>
    <div v-if="optionList.length > 0" class="timeLine">
      <el-timeline>
        <el-timeline-item
          v-for="(item, index) in optionList"
          :key="index"
          placement="top"
          :icon="item.processParameter.isAssent == 1 ? 'el-icon-check' : 'el-icon-close'"
          :type="item.type"
          :color="item.processParameter.isAssent == 1 ? '#67C23A' : '#F56C6C'"
          size="large"
        >
          <el-card>
            <div class="card-title">
              <span :class="item.processParameter.isAssent == 1 ? 'pass' : 'noPass'" class="optionTitle">{{
                item.processParameter.isAssent == 1 ? '审核通过' : '审核不通过'
              }}</span>
              <span class="fr time">{{ item.endTime }}</span>
            </div>
            <div class="approver">
              <span>{{ item.name }}</span
              >：{{ getNameByid(item.processParameter.assignee) }}（{{
                item.processParameter.isAssent == '1' ? '已同意' : '不同意'
              }}）
            </div>
            <div class="optionDetail">{{ item.processParameter.opinion }}</div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>
    <el-form
      v-if="isFill"
      ref="ruleFormOption"
      v-loading="loading"
      class="formDataOption"
      :model="formDataOption"
      label-position="top"
    >
      <el-form-item label="意见：" prop="opinion" :rules="{ required: true, message: '请输入意见', trigger: 'change' }">
        <el-input v-model="formDataOption.opinion" maxlength="300" type="textarea" :rows="2" placeholder="请输入意见" />
      </el-form-item>
      <el-form-item
        label="是否同意："
        prop="isAssent"
        :rules="{ required: true, message: '请选择是否同意', trigger: 'change' }"
      >
        <el-radio-group v-model="formDataOption.isAssent">
          <el-radio :label="1">同意</el-radio>
          <el-radio :label="0">不同意</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div v-if="isFill" class="textR formBtn">
      <el-button :loading="loading" size="small" @click="isFill = false">取 消</el-button>
      <el-button :loading="loading" type="primary" size="small" @click="handleSubmit">提 交</el-button>
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, ref, getCurrentInstance } from 'vue';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { processExecute } from '@/api/unqualifiedDisposition';
import { getLoginInfo } from '@/utils/auth';

export default {
  name: 'Disposalopinion',
  components: {},
  props: {
    historyList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    sampleInfoDetail: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['fresh'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const datas = reactive({
      optionList: [],
      loading: false,
      sampleInfoDetail: props.sampleInfoDetail,
      currentAccountId: '',
      lastStep: { processParameter: {} },
      lastCause: { processParameter: {}, approvalList: [] }, // 最后一步的原因分析
      isLastStepName: '', // 最后一步的流程是否是当前流程
      formDataOption: {
        isAssent: 1
      },
      userList: {},
      ruleFormOption: ref(),
      isFill: false, // 是否需要填写表单
      isShowForm: true // 是否显示处置意见按钮
    });

    if (getLoginInfo()) {
      datas.currentAccountId = getLoginInfo().accountId;
    }

    const initList = () => {
      datas.sampleInfoDetail = props.sampleInfoDetail;
      datas.isLastStepName = props.historyList[props.historyList.length - 1].name;
      datas.optionList = props.historyList.filter(item => {
        return item.endTime;
      });
      datas.optionList = datas.optionList.filter(item => {
        return (
          item.name === '技术研发部审批' ||
          item.name === '采购部审批' ||
          item.name === '质量管理部审批' ||
          item.name === '总工程师审批'
        );
      });
      var spArray = props.historyList.filter(item => {
        return item.name !== '处置验收';
      });
      datas.lastStep = spArray[spArray.length - 1];
      if (datas.lastStep.endTime === '') {
        datas.lastStep.processParameter.isAssent = 1;
      }
    };
    initList();
    const handleSubmit = () => {
      datas.ruleFormOption.validate().then(valid => {
        if (valid) {
          var params = {
            businessKey: datas.sampleInfoDetail.id,
            processInstanceId: datas.sampleInfoDetail.processInstanceId,
            isAssent: 1,
            ...datas.formDataOption
          };
          datas.loading = true;
          processExecute(params).then(res => {
            datas.loading = false;
            if (res.data.code === 200) {
              proxy.$message.success('提交成功');
              context.emit('fresh', {});
              datas.isFill = false;
            }
          });
        }
      });
    };

    return {
      ...toRefs(datas),
      handleSubmit,
      getPermissionBtn,
      getNameByid,
      initList
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
@import './common.scss';
.disposalOpinion {
  .name {
    font-size: 14px;
    color: #606266;
    margin-left: 10px;
    font-weight: 400;
  }
  .time {
    color: #a8abb2;
  }
  .el-timeline {
    margin-top: 10px;
  }
  .formDataOption {
    padding-bottom: 74px;
  }
  .formBtn {
    padding: 0 0 35px 0;
  }
  .optionTitle {
    display: inline-block;
    font-size: 14px;
    line-height: 20px;
  }
  .pass {
    color: #67c23a;
  }
  .noPass {
    color: #f56c6c;
  }
  .optionDetail {
    background: #f6f6f6;
    padding: 9px 10px;
    color: #909399;
    line-height: 20px;
    border-radius: 2px;
    width: 100%;
  }
  .approver {
    color: #606266;
    line-height: 20px;
    font-weight: normal;
    margin: 10px 0;
  }
  .timeLine {
    :deep(.el-icon-close:before) {
      position: absolute;
      left: 1.5px;
      top: 1.7px;
    }
  }
  :deep(.el-form .el-form-item .el-form-item__label) {
    font-size: 14px !important;
    font-weight: bold;
    color: #303133;
  }
  :deep(.el-timeline-item__node--large) {
    width: 16px;
    height: 16px;
  }
  .card-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
