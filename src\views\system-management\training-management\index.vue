<template>
  <!-- 培训管理 -->
  <ListLayout :has-quick-query="false" :has-button-group="getPermissionBtn('addTraining') ? true : false">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="code">
          <el-input
            v-model="formInline.param"
            v-trim
            v-focus
            placeholder="请输入培训名称"
            class="ipt-360"
            prefix-icon="el-icon-search"
            size="large"
            clearable
            @keyup.enter="getList"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="getList">查询</el-button>
          <el-button size="large" @click="reset"> 重置 </el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        type="primary"
        icon="el-icon-plus"
        size="large"
        @click="handleAdd"
        @keyup.prevent
        @keydown.enter.prevent
        >新增</el-button
      >
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="tableList"
      size="medium"
      fit
      border
      height="auto"
      style="width: auto"
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
    >
      <el-table-column label="培训名称" prop="name" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.name || '' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="培训地点" prop="address" :min-width="colWidth.address" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.address || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="开始日期" prop="startDate" :min-width="colWidth.date">
        <template #default="{ row }">
          <div class="nowrap">{{ row.startDate || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="结束日期" prop="endDate" :min-width="colWidth.date">
        <template #default="{ row }">
          <div class="nowrap">{{ row.endDate || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createBy" :min-width="colWidth.person">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.createBy) || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="创建日期" prop="createTime" :min-width="colWidth.date">
        <template #default="{ row }">
          <div class="nowrap">{{ row.createTime || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="getPermissionBtn('checkTraining') || getPermissionBtn('editTraining')"
        label="操作"
        :min-width="colWidth.operation"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <span v-if="getPermissionBtn('checkTraining')" class="blue-color" @click="handleCheck(row)">查看</span>
          <span v-if="getPermissionBtn('editTraining')" class="blue-color" @click="handleEdit(row)">编辑</span>
          <span v-if="getPermissionBtn('deleteTraining')" class="blue-color" @click="handleDelete(row)">删除</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
    <template #other>
      <DialogDetail
        :type="dialogType"
        :name-list="userList"
        :dialog-show="dialogVisiable"
        :row-detail="rowDetail"
        @closeDialog="closeDialog"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, onMounted } from 'vue';
import store from '@/store';
import Pagination from '@/components/Pagination';
import ListLayout from '@/components/ListLayout';
import { formatDate } from '@/utils/formatTime';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { ElMessage, ElMessageBox } from 'element-plus';
// Api
import { employeetrainList, deleteEmployeetrain } from '@/api/training-manage';
import { getMemberTable } from '@/api/departManagement';
// Components
import DialogDetail from './components/DialogDetail.vue';
import UserTag from '@/components/UserTag';

export default {
  name: 'TrainingManagement',
  components: { Pagination, ListLayout, DialogDetail, UserTag },
  setup() {
    const state = reactive({
      tableRef: ref(),
      dialogType: '',
      dialogVisiable: false,
      listLoading: false,
      formInline: {
        param: '' // 搜索的关键字
      },
      listQuery: {
        limit: 20,
        page: 1
      },
      tableList: [],
      nameList: store.state.common.nameList,
      userList: [],
      tableSelected: [], // 表格选中的值
      dialogFormVisible: false,
      total: 0,
      rowDetail: {} // 选中的行数据
    });

    const tableKey = ref(0);
    const getList = query => {
      const params = {
        param: state.formInline.param.trim()
      };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        params.page = state.listQuery.page.toString();
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoading = true;
      employeetrainList(params).then(res => {
        state.listLoading = false;
        if (res) {
          state.total = res.data.data.totalCount;
          state.tableList = res.data.data.list;
        }
      });
    };
    const getUserList = async () => {
      state.listLoading = true;
      const response = await getMemberTable({ departmentId: '', limit: '-1', page: '1' }).finally(
        (state.listLoading = false)
      );
      if (response) {
        state.userList = response.data.data.list;
      }
    };
    getUserList();
    onMounted(() => {
      getList();
    });
    const reset = () => {
      state.formInline.param = '';
      state.listQuery = {
        limit: 20,
        page: 1
      };
      getList();
    };
    const closeDialog = isRefresh => {
      state.dialogVisiable = false;
      if (isRefresh) {
        getList();
      }
    };
    // 查看
    const handleCheck = row => {
      state.dialogType = 'check';
      state.dialogVisiable = true;
      state.rowDetail = row;
    };
    // 编辑
    const handleEdit = row => {
      state.dialogType = 'edit';
      state.dialogVisiable = true;
      state.rowDetail = row;
    };
    // 删除
    const handleDelete = row => {
      ElMessageBox({
        title: '删除确认',
        message: '是否确认删除？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      }).then(async () => {
        state.listLoading = true;
        const response = await deleteEmployeetrain(row.id).finally((state.listLoading = false));
        if (response) {
          ElMessage.success('删除成功！');
          getList();
        }
      });
    };
    // 新增
    const handleAdd = () => {
      state.dialogType = 'add';
      state.dialogVisiable = true;
    };
    return {
      ...toRefs(state),
      getPermissionBtn,
      reset,
      closeDialog,
      handleAdd,
      drageHeader,
      getNameByid,
      getNamesByid,
      handleCheck,
      handleDelete,
      handleEdit,
      formatDate,
      getList,
      tableKey,
      colWidth
    };
  },
  computed: {},
  created() {}
};
</script>
<style lang="scss" scoped></style>
