import request from '@/utils/request';

// 保存国网（外部）物资类型信息
export function saveExternalMaterialClassification(data) {
  return request({
    url: '/api-capabilitystd/capability/externalmaterialclassification/saveOrUpdate',
    method: 'post',
    data
  });
}

// 国网策略获取版本列
export function versionApi(materialClassificationCode) {
  return request({
    url: `/api-capabilitystd/capability/externalstrategy/list/${materialClassificationCode}`,
    method: 'get'
  });
}
// 国网策略列表
export function findByExternalStrategyId(externalStrategyId) {
  return request({
    url: `/api-capabilitystd/capability/externalstrategydetails/findByExternalStrategyId/${externalStrategyId}`,
    method: 'get'
  });
}
// 国网策略
export function saveOrUpdate(data) {
  return request({
    url: `/api-capabilitystd/capability/externalstrategydetails/saveOrUpdate`,
    method: 'post',
    data
  });
}
// 删除
export function externalstrategyDelete(id) {
  return request({
    url: `/api-capabilitystd/capability/externalstrategydetails/delete/${id}`,
    method: 'delete'
  });
}
// 国网策略新增和修改版本
export function saveVersion(data) {
  return request({
    url: `/api-capabilitystd/capability/externalstrategy/saveOrUpdate`,
    method: 'post',
    data
  });
}
