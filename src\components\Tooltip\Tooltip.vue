<template>
  <el-tooltip ref="tooltipRef" v-bind="$props" :disabled="$props.disabled || disabled">
    <slot />
    <template #content>
      <slot name="content" />
    </template>
  </el-tooltip>
</template>

<script>
import { onMounted, onUnmounted, ref } from 'vue';
// import { ElTooltipProps } from 'element-plus'
// import { useEventListener } from '@vueuse/core'

export default {
  setup(props, context) {
    const disabled = ref(false);
    const tooltipRef = ref();
    const targetElement = ref();

    const setDisbled = () => {
      const { clientWidth, clientHeight, scrollWidth, scrollHeight } = targetElement.value;
      disabled.value = clientWidth >= scrollWidth && clientHeight >= scrollHeight;
    };

    onMounted(() => {
      targetElement.value = tooltipRef.value.$el.nextSibling;
      targetElement.value.addEventListener('mouseenter', setDisbled);
    });

    onUnmounted(() => {
      targetElement.value.removeEventListener('mouseenter', setDisbled);
    });

    return {
      disabled,
      tooltipRef
    };
  }
};
</script>
