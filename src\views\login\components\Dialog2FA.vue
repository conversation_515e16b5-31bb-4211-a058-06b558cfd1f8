<template>
  <el-dialog v-model="dialogShow" title="二次验证" width="500px" :close-on-click-modal="false">
    <el-form
      ref="formRef"
      :model="formData"
      label-position="top"
      label-width="110px"
      custom-class="submit-dialog"
      @submit.prevent
      @keydown.enter.prevent
    >
      <el-form-item
        label="输入验证码："
        prop="code2FA"
        :rules="{
          required: true,
          message: '请输入验证码',
          trigger: 'blur'
        }"
      >
        <el-input
          ref="input2FA"
          v-model="formData.code2FA"
          prefix-icon="el-icon-lock"
          placeholder="验证码"
          name="code"
          tabindex="3"
          @keyup.enter="submitCode"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancelDialog">取消</el-button>
      <el-button type="primary" @click="submitCode">确定</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { computed, nextTick, reactive, watch, ref } from 'vue';
// import { getVerificationCode } from '@/utils/totp'
import { ElMessage } from 'element-plus';
import { verifyCode } from '@/api/userInfo';

export default {
  name: 'Dialog2FA',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    username: {
      type: String,
      default: ''
    }
  },
  emits: ['closeDialog', 'verificationSucceeded'],
  setup(props, context) {
    const input2FA = ref(null);
    const formRef = ref(null);
    const formData = reactive({
      code2FA: ''
    });
    const dialogShow = computed({
      get: () => props.dialogVisible,
      set: val => context.emit('closeDialog', val)
    });

    const cancelDialog = () => {
      context.emit('closeDialog', false);
    };

    const submitCode = async () => {
      formRef.value.validate(async valid => {
        if (valid) {
          const verifiedResponse = await verifyCode({ username: props.username, code: formData.code2FA });
          console.log('verifiedResponse', verifiedResponse);
          if (verifiedResponse && verifiedResponse.data.data) {
            context.emit('verificationSucceeded', true);
            ElMessage.success('验证成功');
            context.emit('closeDialog', false);
          } else {
            context.emit('verificationSucceeded', false);
            ElMessage.warning('验证码验证失败');
          }
        }
      });
      // const codeList = []
      // codeList.push(getVerificationCode())
      // console.log(codeList)
      // formData.code2FA.toString() === getVerificationCode()
      // if (getVerificationCode()) {
      //   context.emit('verificationSucceeded', true)
      //   ElMessage.success('验证成功')
      //   context.emit('closeDialog', false)
      // } else {
      //   context.emit('verificationSucceeded', false)
      //   ElMessage.warning('验证码验证失败')
      // }
    };

    watch(
      () => props.dialogVisible,
      newValue => {
        if (newValue) {
          formData.code2FA = '';
          nextTick(() => {
            input2FA.value.focus();
          });
        }
      }
    );

    return {
      formData,
      dialogShow,
      cancelDialog,
      submitCode,
      input2FA,
      formRef
    };
  }
};
</script>
<style lang="scss" scoped>
.flex-group {
  display: flex;
  flex-direction: row;
  .flex-space {
    width: 15px;
  }
  .flex-content {
    flex: auto;
  }
}
</style>
