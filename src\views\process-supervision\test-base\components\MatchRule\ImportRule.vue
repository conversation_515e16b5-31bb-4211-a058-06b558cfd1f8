<template>
  <!-- 标准导入 dialog -->
  <el-dialog
    v-model="importRuleDialogVisible"
    title="数据导入"
    :close-on-click-modal="false"
    :show-close="!dialogLoading"
    @close="handleClose()"
  >
    <div
      v-loading="dialogLoading"
      element-loading-text="数据导入中，请稍后..."
      element-loading-background="rgba(255, 255, 255, 0.2)"
    >
      <div class="title">为确保数据导入的准确性，请按照以下规则进行导入</div>
      <ul class="uploadRules">
        <li>
          请按照<span class="blue-color" @click="downLoadFile('标准规则匹配导入模版.xlsx')"
            >标准规则匹配导入模板.xlsx</span
          >在模板内录入数据
        </li>
        <li>只导入第一张工作表（sheet1）；</li>
        <li>请上传*.xls，*.xlsx格式文件；</li>
        <li>目前一次性最多上传5000条数据；</li>
        <li>文件大小不超过10M；</li>
      </ul>
      <div class="uploadArea">
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="headerconfig"
          :data="uploadParams"
          :auto-upload="false"
          :limit="1"
          :accept="fileAcceptExcel"
          :on-exceed="handleExceed"
          :before-upload="beforeUpload"
          :on-success="handleFileSuccess"
        >
          <el-button size="small" type="primary" plain>选择上传文件</el-button>
        </el-upload>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button :disabled="dialogLoading" @click="handleClose()">取 消</el-button>
        <el-button type="primary" :disabled="dialogLoading" @click="handleSubmit()">确认上传</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, onMounted, ref } from 'vue';
import { getToken } from '@/utils/auth';
import { fileAcceptExcel } from '@/utils/fileAccept';
import { standardMatchingParamsImport } from '@/api/uploadAction';
import { ElMessage } from 'element-plus';

export default {
  name: 'DialogImportTestBase',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    standardCategoryId: {
      type: String,
      required: false,
      default: ''
    }
  },
  emits: ['close'],
  setup(props, ctx) {
    const state = reactive({
      importRuleDialogVisible: false,
      uploadRef: ref(),
      headerconfig: {
        Authorization: getToken()
      },
      uploadAction: standardMatchingParamsImport(), // 附件上传地址
      title: '导入规则',
      dialogLoading: false,
      uploadParams: {
        standardCategoryId: ''
      }
    });

    const downLoadFile = fileName => {
      const a = document.createElement('a');
      a.href = '/staticFile/' + fileName;
      a.download = fileName;
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      a.remove();
    };
    const handleSubmit = () => {
      const { uploadFiles } = state.uploadRef;
      if (!Array.isArray(uploadFiles) || uploadFiles.length === 0) {
        ElMessage.warning('请上传标准规则匹配导入文件');
        return;
      }
      state.dialogLoading = true;
      state.uploadRef.submit();
    };

    // #endregion

    watch(
      () => props.visible,
      () => {
        if (props.visible) {
          state.importRuleDialogVisible = true;
          state.uploadParams.standardCategoryId = props.standardCategoryId;
        }
      },
      { deep: true }
    );
    const handleClose = () => {
      state.importRuleDialogVisible = false;
      closeImportRuleDialog(false);
    };
    // 上传文件的限制
    const beforeUpload = file => {
      let fileName = '';
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        state.dialogLoading = false;
        ElMessage.error('上传附件大小不能超过10M');
        return false;
      } else if (fileName !== 'xls' && fileName !== 'xlsx') {
        state.dialogLoading = false;
        ElMessage.error('仅支持.xls，.xlsx文件扩展名');
        return false;
      } else if (file.size === 0) {
        state.dialogLoading = false;
        ElMessage.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };
    const handleExceed = files => {
      state.uploadRef.clearFiles(['success', 'fail', 'ready']);
      state.uploadRef.handleStart(files[0]);
    };
    // 上传成功的钩子
    const handleFileSuccess = (res, file) => {
      state.dialogLoading = false;
      if (res.code === 200) {
        ElMessage.success(res.message);
      } else {
        ElMessage.error(res.message);
      }
      state.importRuleDialogVisible = false;
      closeImportRuleDialog(true);
    };

    const closeImportRuleDialog = type => {
      state.uploadRef?.clearFiles(['success', 'fail', 'ready']);
      ctx.emit('close', type);
    };

    onMounted(() => {});

    return {
      ...toRefs(state),
      downLoadFile,
      handleFileSuccess,
      handleExceed,
      beforeUpload,
      handleClose,
      handleSubmit,
      fileAcceptExcel
    };
  }
};
</script>

<style scoped lang="scss">
.uploadRules {
  padding: 5px 10px;
  background: #f0f2f5;
  margin: 14px 0;
  li {
    list-style: none;
    line-height: 22px;
    font-size: 13px;
  }
  .blue-color {
    margin-right: 0;
  }
}
</style>
