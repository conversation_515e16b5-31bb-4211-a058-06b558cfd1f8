<template>
  <el-dialog
    :model-value="showDialog"
    custom-class="add-edit-account"
    :title="currentTitle"
    width="1000px"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <el-form ref="formInlineRef" :model="formInline" :rules="rules" label-position="top" label-width="120px">
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="资源类型：">
            <el-radio-group v-if="title === 'add'" v-model="formInline.type" @change="changeType">
              <el-radio v-for="item in sourceTypeOption" :key="item.name" :label="item.value">{{ item.name }}</el-radio>
            </el-radio-group>
            <div v-else>{{ filterType(formInline.type) }}</div>
          </el-form-item>
        </el-col>
        <el-col v-if="formInline.type === 1 || formInline.type === 2" :span="12">
          <el-form-item label="菜单key：" prop="key">
            <el-input v-model="formInline.key" size="small" placeholder="请输入菜单key" />
          </el-form-item>
        </el-col>
        <el-col v-if="formInline.type === 1 || formInline.type === 2" :span="12">
          <el-form-item label="菜单名称：" prop="title">
            <el-input ref="inputRef" v-model="formInline.title" v-trim size="small" placeholder="请输入菜单名称" />
          </el-form-item>
        </el-col>
        <el-col v-if="formInline.type === 4" :span="12">
          <el-form-item label="资源key：" prop="key">
            <el-input v-model="formInline.key" size="small" placeholder="请输入资源key" />
          </el-form-item>
        </el-col>
        <el-col v-if="formInline.type === 4" :span="12">
          <el-form-item label="资源名称：" prop="title">
            <el-input ref="inputRef2" v-model="formInline.title" v-trim size="small" placeholder="请输入资源名称" />
          </el-form-item>
        </el-col>
        <el-col v-if="formInline.type === 4" :span="12">
          <el-form-item label="父级资源：" prop="parentId">
            <el-cascader
              v-model="parentIds"
              size="small"
              :options="btnParentOption"
              :props="categoryProps"
              style="width: 100%"
              @change="changeBtnParentMenu"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="formInline.type === 1 || formInline.type === 2" :span="12">
          <el-form-item label="菜单英文名称：" prop="name">
            <el-input v-model="formInline.name" size="small" placeholder="请输入菜单英文名称" />
          </el-form-item>
        </el-col>
        <el-col v-if="formInline.type === 2" :span="12">
          <el-form-item label="父级菜单：" prop="parentId">
            <!-- <el-input v-model="formInline.parentId" placeholder="请选择父级菜单" /> -->
            <el-cascader
              v-model="parentIds"
              size="small"
              :options="parentOption"
              :props="categoryProps"
              style="width: 100%"
              @change="changeParentMenu"
              @visible-change="handleCascaderVisibleChange"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="formInline.type === 1 || formInline.type === 2" :span="12">
          <el-form-item label="菜单路径：" prop="path">
            <el-input v-model="formInline.path" size="small" placeholder="请输入菜单路径" />
          </el-form-item>
        </el-col>
        <el-col v-if="formInline.type === 1 || formInline.type === 2" :span="12">
          <el-form-item label="组件路径：" prop="component">
            <el-input v-model="formInline.component" size="small" placeholder="请输入组件路径" />
          </el-form-item>
        </el-col>
        <el-col v-if="formInline.type === 1 || formInline.type === 2" :span="12">
          <el-form-item label="菜单图标：" prop="icon">
            <el-input v-model="formInline.icon" size="small" placeholder="请输入菜单图标" />
          </el-form-item>
        </el-col>
        <el-col v-if="formInline.type === 1 || formInline.type === 2" :span="12">
          <el-form-item label="排序：" prop="sort">
            <el-input v-model="formInline.sort" size="small" placeholder="请输入排序" />
          </el-form-item>
        </el-col>
        <el-col v-if="formInline.type === 2" :span="12">
          <el-form-item label="打开方式：">
            <el-radio-group v-model="formInline.hidden" size="small" @change="changeOpenType">
              <el-radio v-for="item in openTypeOption" :key="item.name" :label="item.value">{{ item.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col v-if="formInline.hidden === 0 && formInline.type === 2" :span="12">
          <el-form-item label="关联菜单：" prop="menuKey">
            <el-input v-model="formInline.menuKey" size="small" placeholder="请输入关联菜单key" />
          </el-form-item>
        </el-col>
        <el-col v-if="formInline.type === 1 || formInline.type === 2" :span="12">
          <el-form-item label="是否显示：">
            <el-radio-group v-model="formInline.visible" size="small">
              <el-radio v-for="item in showOption" :key="item.name" :label="item.value">{{ item.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="无租户可见：">
            <el-radio-group v-model="formInline.initselect" size="small">
              <el-radio v-for="item in showInitSelectOption" :key="item.name" :label="item.value">{{
                item.name
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" :loading="addEditLoading" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent
          >保 存</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, toRefs, watch, nextTick } from 'vue';
import { getNameByid } from '@/utils/common';
import { ElMessage } from 'element-plus';
import { addMenu, editMenu, parentMenu, getMenuTree } from '@/api/platform-management';
// import _ from 'lodash'
// import { useRoute } from 'vue-router'
// import { formatPaginationList } from '@/utils/formatJson'

export default {
  name: 'AddOrEditAccount',
  components: {},
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: 'add'
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    const datas = reactive({
      showDialog: props.show,
      currentTitle: '新增资源',
      formInlineRef: ref(),
      addEditLoading: false,
      sourceTypeOption: [
        { name: '一级菜单', value: 1 },
        { name: '子菜单', value: 2 },
        { name: '按钮/表单/字段', value: 4 }
      ], // 资源类型（ 1：一级菜单 2：子菜单 3：页面 4：按钮/表单/字段）
      openTypeOption: [
        { name: '外部', value: 1 },
        { name: '内部', value: 0 }
      ],
      showOption: [
        { name: '显示', value: true },
        { name: '隐藏', value: false }
      ],
      showInitSelectOption: [
        { name: '可见', value: 1 },
        { name: '不可见', value: 0 }
      ],
      formInline: {
        type: 1,
        key: '',
        name: '',
        title: '',
        path: '',
        parentId: '',
        component: '',
        icon: '',
        sort: '',
        menuKey: '',
        hidden: 1,
        visible: true,
        initselect: 0
      },
      inputRef: ref(),
      inputRef2: ref(),
      parentIds: [],
      parentOption: [],
      btnParentOption: [],
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'title',
        value: 'id'
      },
      rules: {
        key: [{ required: true, message: '请输入菜单key', trigger: 'blur' }],
        title: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
        name: [{ required: true, message: '请输入菜单英文名称', trigger: 'blur' }],
        path: [{ required: true, message: '请输入菜单路径', trigger: 'blur' }],
        parentId: [{ required: true, message: '请选择父级菜单', trigger: 'change' }],
        component: [{ required: true, message: '请输入组件路径', trigger: 'blur' }],
        icon: [{ required: true, message: '请输入菜单图标', trigger: 'blur' }],
        menuKey: [{ required: true, message: '请输入关联菜单key', trigger: 'blur' }]
      }
    });

    let localInputFormData = {};

    watch(
      () => props.show,
      newValue => {
        // console.log(newValue)
        if (newValue) {
          datas.showDialog = newValue;
          datas.currentTitle = props.title === 'add' ? '新增资源' : '编辑资源';
          if (props.data) {
            datas.formInline = props.data;
            // console.log(datas.formInline)
            if (datas.formInline.parentId !== '0' && datas.formInline.type === 2) {
              datas.parentIds = [datas.formInline.parentId];
            } else if (datas.formInline.parentId !== '0' && datas.formInline.type === 4) {
              datas.parentIds = [datas.formInline.parent.parentId, datas.formInline.parent.id];
            }
          } else {
            datas.formInline = {
              type: 1,
              key: '',
              name: '',
              title: '',
              path: '',
              parentId: '',
              component: '',
              icon: '',
              sort: '',
              menuKey: '',
              hidden: 1,
              visible: true,
              initselect: 0
            };
            datas.parentIds = [];
            nextTick(() => {
              datas.inputRef.focus();
            });
          }
        }
      },
      { deep: true }
    );

    // 确定
    const dialogSuccess = () => {
      datas.formInlineRef.validate(valid => {
        if (valid) {
          const params = JSON.parse(JSON.stringify(datas.formInline));
          // params.hidden = datas.formInline.hidden === true ? 1 : 0
          if (datas.formInline.type === 1) {
            params.parentId = 0;
          }
          params.sort = Number(params.sort);
          params.isPublic = 0; // 是否公共权限(0:否，1:是)
          if (props.title === 'add') {
            addMenu(params).then(res => {
              if (res !== false) {
                ElMessage.success('新增成功');
                datas.showDialog = false;
                context.emit('setInfo', datas.formInline);
              } else {
                ElMessage.error('新增失败!');
              }
            });
          } else {
            editMenu(params).then(res => {
              if (res !== false) {
                ElMessage.success('编辑成功');
                datas.showDialog = false;
                context.emit('setInfo', datas.formInline);
              } else {
                ElMessage.error('编辑失败!');
              }
            });
          }
        }
      });
    };
    // 取消
    const close = () => {
      datas.showDialog = false;
      localInputFormData = {};
      context.emit('close', false);
    };
    // 获取父级菜单
    const getParentMenu = () => {
      parentMenu({}).then(res => {
        if (res !== false) {
          // console.log(res)
          datas.parentOption = res.data.data;
        }
      });
    };
    // 获取按钮父级菜单
    const getBtnParentMenu = () => {
      getMenuTree({}).then(res => {
        if (res !== false) {
          // console.log(res)
          datas.btnParentOption = res.data.data;
        }
      });
    };
    const handleCascaderVisibleChange = visible => {
      if (visible) {
        nextTick(() => {
          // Sometime el-cascader stuck error occurred, remove aria-owns attribute fix that bug.
          const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
          Array.from($el).map(item => item.removeAttribute('aria-owns'));
        });
      }
    };
    // 切换父级菜单
    const changeParentMenu = value => {
      datas.formInline.parentId = value[0];
    };
    // 切换按钮父级菜单
    const changeBtnParentMenu = value => {
      // console.log(value)
      datas.formInline.parentId = value[value.length - 1];
    };
    // 切换资源类型
    const changeType = type => {
      // console.log(type)
      // 本地缓存数据 - 防止切换资源类型时清空数据
      localInputFormData = {
        type: type,
        key: datas.formInline.key || (localInputFormData.key ?? ''),
        name: datas.formInline.name || (localInputFormData.name ?? ''),
        title: datas.formInline.title || (localInputFormData.title ?? ''),
        path: datas.formInline.path || (localInputFormData.path ?? ''),
        parentId: datas.formInline.parentId || (localInputFormData.parentId ?? ''),
        component: datas.formInline.component || (localInputFormData.component ?? ''),
        icon: datas.formInline.icon || (localInputFormData.icon ?? ''),
        sort: datas.formInline.sort || (localInputFormData.sort ?? ''),
        menuKey: datas.formInline.menuKey || (localInputFormData.menuKey ?? ''),
        hidden: localInputFormData.type === 2 ? datas.formInline.hidden : localInputFormData.hidden ?? 1,
        visible:
          localInputFormData.visible === undefined || localInputFormData.type !== 4
            ? datas.formInline.visible
            : localInputFormData.visible ?? true,
        initselect: datas.formInline.initselect ?? localInputFormData.initselect ?? 0,
        parentIds: datas.parentIds.length > 0 ? [...datas.parentIds] : localInputFormData.parentIds
      };

      switch (type) {
        case 1:
        case 2:
          datas.formInline = {
            type: type,
            key: localInputFormData.key ?? '',
            name: localInputFormData.name ?? '',
            title: localInputFormData.title ?? '',
            path: localInputFormData.path ?? '',
            parentId: type === 2 ? localInputFormData.parentId ?? '' : '',
            component: localInputFormData.component ?? '',
            icon: localInputFormData.icon ?? '',
            sort: localInputFormData.sort ?? '',
            menuKey: localInputFormData.menuKey ?? '',
            hidden: localInputFormData.hidden ?? 1,
            visible: localInputFormData.visible ?? true,
            initselect: localInputFormData.initselect ?? 0
          };
          break;

        case 4:
          datas.formInline = {
            type: type,
            key: localInputFormData.key ?? '',
            name: '',
            title: localInputFormData.title ?? '',
            path: '',
            parentId: localInputFormData.parentId ?? '',
            component: '',
            icon: '',
            sort: '',
            menuKey: '',
            hidden: 1,
            visible: true,
            initselect: localInputFormData.initselect ?? 0
          };
          break;
      }
      if ((type === 2 || type === 4) && localInputFormData.parentIds && localInputFormData.parentIds.length > 0) {
        datas.parentIds = [...localInputFormData.parentIds];
      } else {
        datas.parentIds = [];
      }
      nextTick(() => {
        if (type === 4) {
          datas.inputRef2.focus();
        } else {
          datas.inputRef.focus();
        }
      });
    };
    // 切换打开方式
    const changeOpenType = openType => {
      // console.log(openType)
      if (openType === 0) {
        // 内部菜单，需要关联父级菜单
      }
    };
    // 过滤资源类型
    const filterType = type => {
      // 资源类型（ 1：一级菜单 2：子菜单 3：页面 4：按钮/表单/字段）
      var map = {
        1: '一级菜单',
        2: '子菜单',
        3: '页面',
        4: '按钮/表单/字段'
      };
      if (type) {
        return map[type];
      } else {
        return '';
      }
    };

    return {
      ...toRefs(datas),
      close,
      getNameByid,
      dialogSuccess,
      changeType,
      getParentMenu,
      changeParentMenu,
      changeBtnParentMenu,
      getBtnParentMenu,
      filterType,
      changeOpenType,
      handleCascaderVisibleChange
    };
  },
  created() {
    this.getParentMenu();
    this.getBtnParentMenu();
  },
  methods: {}
};
</script>
<style lang="scss" scoped>
.add-edit-account {
  .search {
    width: 360px;
  }
}
</style>
