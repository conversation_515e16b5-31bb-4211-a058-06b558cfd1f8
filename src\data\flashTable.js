export const COMMAND = {
  /** 生命周期 - 模板创建时 */
  TEMPLATE_CREATED: 'TEMPLATE_CREATED',

  /** 生命周期 - 模板 html 加载完成后 */
  TEMPLATE_LOADED: 'TEMPLATE_LOADED',

  /** 生命周期 - 数据加载并设置到模板上后（如果是空模板就没有该生命周期） */
  DATA_LOADED: 'DATA_LOADED',

  /** 加载模板 */
  LOAD_TEMPLATE: 'LOAD_TEMPLATE',

  /** 加载模板数据 */
  LOAD_DATA: 'LOAD_DATA',

  /** 设置模板状态 */
  SET_TEMPLATE_STATUS: 'SET_TEMPLATE_STATUS',

  /** 获取模板数据 */
  GET_DATA: 'GET_DATA',

  /** 设置模板数据 */
  SET_DATA: 'SET_DATA',

  /** 提交模板数据 */
  SUBMIT_DATA: 'SUBMIT_DATA',

  /** 清除模板数据 */
  CLEAR_DATA: 'CLEAR_DATA',

  /** 提交成功回调函数 */
  SUBMIT_CALLBACK: 'SUBMIT_CALLBACK',

  /** 设置配置 */
  SET_CONFIG: 'SET_CONFIG',

  /** 设计模板 - 返回 */
  BACK: 'BACK',

  /** 设计模板 - 保存模板 */
  SAVE_TEMPLATE: 'SAVE_TEMPLATE',

  /** 设计模板 - 发布模板 */
  PUBLISH_TEMPLATE: 'PUBLISH_TEMPLATE',

  /** 生成PDF*/
  GENERATE_PDF: 'GENERATE_PDF',

  /** 生成PDF资源加载完成*/
  GENERATE_PDF_CREATED: 'GENERATE_PDF_CREATED',

  /** 生成PDF错误*/
  GENERATE_PDF_ERROR: 'GENERATE_PDF_ERROR'
};
