/**
 * indexs：准则遍历起始下标
 * values：均值X 或 极差R
 * UCL: 控制上限
 * CL: 中心线
 * LCL: 控制下限
 * 标准差σ(sigma): sigma
 */
export default class SPCEightCriterions {
  static criterions = {
    1: '在控制点外任何点: 一般认为是新员工，工艺方法错误，机器故障，原材料不合格，测量错误，计算错误，检验方法或标准变化',
    2: '3个点中有2个都在A区: 一般认为是新员工，工艺方法错误，机器故障，原材料不合格，测量错误，计算错误，检验方法或标准变化',
    3: '5个点中有4个都在C区外（即A、B区内）: 一般认为是新员工，工艺方法错误，机器故障，原材料不合格，测量错误，计算错误，检验方法或标准变化',
    4: '连续15个点排列在中心线1个标准差范围内(任一侧): 数据造假，控制限计算错误太宽了，数据分层不够',
    5: '连续8个点距中心线的距离大于1个标准差(任一侧): 数据分层不够(SPC没有分模具去分开做，没有分班次去做等等)',
    6: '连续9点位于中心线同一侧: 一般认为是新员工，工艺方法错误，机器故障，原材料不合格，测量错误，计算错误，检验方法或标准变化',
    7: '连续6点上升或下降: 刀具模具等工具的磨损，维护保养水平降低，操作工的技能越来越熟练',
    8: '连续14点交替上下变化: 轮流使用两台设备或两个操作工操作，分别针对两个模具定期抽样但一起统计等导致数据分层不够'
  };

  constructor(options) {
    this.indexs = {
      criterion1Index: options?.indexs?.criterion1Index > -1 ? options?.indexs?.criterion1Index : -1,
      criterion2Index: options?.indexs?.criterion2Index > -1 ? options?.indexs?.criterion2Index : -1,
      criterion3Index: options?.indexs?.criterion3Index > -1 ? options?.indexs?.criterion3Index : -1,
      criterion4Index: options?.indexs?.criterion4Index > -1 ? options?.indexs?.criterion4Index : -1,
      criterion5Index: options?.indexs?.criterion5Index > -1 ? options?.indexs?.criterion5Index : -1,
      criterion6Index: options?.indexs?.criterion6Index > -1 ? options?.indexs?.criterion6Index : -1,
      criterion7Index: options?.indexs?.criterion7Index > -1 ? options?.indexs?.criterion7Index : -1,
      criterion8Index: options?.indexs?.criterion8Index > -1 ? options?.indexs?.criterion8Index : -1
    };
  }

  getLastUsefulIndex(usefulSize, index) {
    return index < 0 ? -1 : index <= usefulSize ? (usefulSize < 0 ? -1 : usefulSize) : index;
  }

  getPassedCriterions({ values, UCL, CL, LCL, sigma }) {
    if (!values?.length) {
      return false;
    }

    // if (!(values?.length > 0 && UCL >= 0 && CL >= 0 && LCL >= 0 && sigma >= 0)) {
    //   console.warn(`[Warn]SPCEightDifferentCriteria: some param is less then 0, values?.length: ${values?.length}, UCL: ${UCL}, CL: ${CL}, LCL: ${LCL}, sigma: ${sigma}`)
    //   return false
    // }

    // 判异准则1控制变量
    let criterion1Count = 0;
    // 判异准则2控制变量
    let criterion2Count = 0;
    let criterion2FaultTolerantCount = 1;
    let criterion2DurationStartIndex = -1;
    // 判异准则3控制变量
    let criterion3Count = 0;
    let criterion3FaultTolerantCount = 1;
    let criterion3DurationStartIndex = -1;
    // 判异准则4控制变量
    let criterion4Count = 0;
    let criterion4DurationStartIndex = -1;
    // 判异准则5控制变量
    let criterion5Count = 0;
    let criterion5DurationStartIndex = -1;
    // 判异准则6控制变量
    let criterion6Count = 0;
    let criterion6Direction = '';
    let criterion6DurationStartIndex = -1;
    // 判异准则7控制变量
    let criterion7Count = 0;
    let criterion7Comparative = '';
    let criterion7DurationStartIndex = -1;
    // 判异准则8控制变量
    let criterion8Count = 0;
    let criterion8Comparative = '';
    let criterion8DurationStartIndex = -1;

    this.indexs.criterion1Index = this.getLastUsefulIndex(values.length - 1 - 1, this.indexs.criterion1Index);
    this.indexs.criterion2Index = this.getLastUsefulIndex(values.length - 1 - 3, this.indexs.criterion2Index);
    this.indexs.criterion3Index = this.getLastUsefulIndex(values.length - 1 - 5, this.indexs.criterion3Index);
    this.indexs.criterion4Index = this.getLastUsefulIndex(values.length - 1 - 15, this.indexs.criterion4Index);
    this.indexs.criterion5Index = this.getLastUsefulIndex(values.length - 1 - 8, this.indexs.criterion5Index);
    this.indexs.criterion6Index = this.getLastUsefulIndex(values.length - 1 - 9, this.indexs.criterion6Index);
    this.indexs.criterion7Index = this.getLastUsefulIndex(values.length - 1 - 6, this.indexs.criterion7Index);
    this.indexs.criterion8Index = this.getLastUsefulIndex(values.length - 1 - 14, this.indexs.criterion8Index);

    const minIndex = Math.min(...Object.values(this.indexs));
    const criterionList = [];

    for (let index = minIndex === -1 ? 0 : minIndex; index < values.length; index++) {
      const value = values[index];

      // 1、在控制点外任何点: x-bar>UCL(X)，x-bar<LCL(X)
      if (index > this.indexs.criterion1Index && (value > UCL || value < LCL)) {
        criterion1Count = criterion1Count + 1;
        this.indexs.criterion1Index = index;
        criterionList.push({
          criterion: 1,
          durationStartIndex: index,
          durationEndIndex: index,
          description: SPCEightCriterions.criterions[1]
        });
      }

      // 2、3个点中有2个都在A区: A区：CL+3σ>x-bar>CL+2σ，CL-3σ>x-bar>CL-2σ
      if (index > this.indexs.criterion2Index) {
        if ((CL + 3 * sigma > value && value > CL + 2 * sigma) || (CL - 3 * sigma > value && value > CL - 2 * sigma)) {
          criterion2Count = criterion2Count + 1;
          if (criterion2Count >= 2) {
            criterion2Count = 0;
            this.indexs.criterion2Index = index;
            criterion2FaultTolerantCount = 1;
            criterionList.push({
              criterion: 2,
              durationStartIndex: criterion2DurationStartIndex,
              durationEndIndex: index,
              description: SPCEightCriterions.criterions[2]
            });
            criterion2DurationStartIndex = -1;
          } else if (criterion2Count === 1) {
            criterion2DurationStartIndex = index;
          }
        } else {
          if (criterion2FaultTolerantCount <= 0) {
            criterion2Count = 0;
            criterion2FaultTolerantCount = 1;
            criterion2DurationStartIndex = -1;
          } else {
            criterion2FaultTolerantCount = criterion2FaultTolerantCount - 1;
          }
        }
      }

      // 3、5个点中有4个都在C区外（即A、B区内）: B区：CL+2σ>x-bar>CL+σ，CL-2σ>x-bar>CL-σ
      if (index > this.indexs.criterion3Index) {
        if ((CL + 3 * sigma > value && value > CL + sigma) || (CL - 3 * sigma < value && value < CL - sigma)) {
          criterion3Count = criterion3Count + 1;
          if (criterion3Count >= 4) {
            criterion3Count = 0;
            this.indexs.criterion3Index = index;
            criterion3FaultTolerantCount = 1;
            criterionList.push({
              criterion: 3,
              durationStartIndex: criterion3DurationStartIndex,
              durationEndIndex: index,
              description: SPCEightCriterions.criterions[3]
            });
            criterion3DurationStartIndex = -1;
          } else if (criterion3Count === 1) {
            criterion3DurationStartIndex = index;
          }
        } else {
          if (criterion3FaultTolerantCount <= 0) {
            criterion3Count = 0;
            criterion3FaultTolerantCount = 1;
            criterion3DurationStartIndex = index;
          } else {
            criterion3FaultTolerantCount = criterion3FaultTolerantCount - 1;
          }
        }
      }

      // 4、连续15个点排列在中心线1个标准差范围内(任一侧): C区：CL+σ>x-bar>CL-σ
      if (index > this.indexs.criterion4Index) {
        if (CL + sigma > value && value > CL - sigma) {
          criterion4Count = criterion4Count + 1;
          if (criterion4Count >= 15) {
            criterion4Count = 0;
            this.indexs.criterion4Index = index;
            criterionList.push({
              criterion: 4,
              durationStartIndex: criterion4DurationStartIndex,
              durationEndIndex: index,
              description: SPCEightCriterions.criterions[4]
            });
            criterion4DurationStartIndex = -1;
          } else if (criterion4Count === 1) {
            criterion4DurationStartIndex = index;
          }
        } else {
          criterion4Count = 0;
          criterion4DurationStartIndex = -1;
        }
      }

      // 5、连续8个点距中心线的距离大于1个标准差(任一侧): 连续8个点在A、B区
      if (index > this.indexs.criterion5Index) {
        if ((CL + 3 * sigma > value && value > CL + sigma) || (CL - 3 * sigma < value && value < CL - sigma)) {
          criterion5Count = criterion5Count + 1;
          if (criterion5Count >= 8) {
            criterion5Count = 0;
            this.indexs.criterion5Index = index;
            criterionList.push({
              criterion: 5,
              durationStartIndex: criterion5DurationStartIndex,
              durationEndIndex: index,
              description: SPCEightCriterions.criterions[5]
            });
            criterion5DurationStartIndex = -1;
          } else if (criterion5Count === 1) {
            criterion5DurationStartIndex = index;
          }
        } else {
          criterion5Count = 0;
          criterion5DurationStartIndex = -1;
        }
      }

      // 6、连续9点位于中心线同一侧: x-bar>CL或CL>x-bar
      if (index > this.indexs.criterion6Index) {
        if (value > CL) {
          if (criterion6Direction === 'upper') {
            criterion6Count = criterion6Count + 1;
          } else {
            criterion6Direction = 'upper';
            criterion6Count = 1;
          }
        } else if (value < CL) {
          if (criterion6Direction === 'lower') {
            criterion6Count = criterion6Count + 1;
          } else {
            criterion6Direction = 'lower';
            criterion6Count = 1;
          }
        }
        if (criterion6Count >= 9) {
          criterion6Count = 0;
          this.indexs.criterion6Index = index;
          criterionList.push({
            criterion: 6,
            durationStartIndex: criterion6DurationStartIndex,
            durationEndIndex: index,
            description: SPCEightCriterions.criterions[6]
          });
          criterion6DurationStartIndex = -1;
        } else if (criterion6Count === 1) {
          criterion6DurationStartIndex = index;
        }
      }

      // 7、连续6点上升或下降: x1>x2>x3>x4>x5>x6，相反同理
      if (index > this.indexs.criterion7Index && index > 0) {
        if (value > values[index - 1]) {
          if (criterion7Comparative === 'bigger') {
            criterion7Count = criterion7Count + 1;
          } else {
            criterion7Count = 2;
            criterion7Comparative = 'bigger';
          }
        } else if (value < values[index - 1]) {
          if (criterion7Comparative === 'smaller') {
            criterion7Count = criterion7Count + 1;
          } else {
            criterion7Count = 2;
            criterion7Comparative = 'smaller';
          }
        }
        if (criterion7Count >= 6) {
          criterion7Count = 0;
          this.indexs.criterion7Index = index;
          criterionList.push({
            criterion: 7,
            durationStartIndex: criterion7DurationStartIndex,
            durationEndIndex: index,
            description: SPCEightCriterions.criterions[7]
          });
          criterion7DurationStartIndex = -1;
        } else if (criterion7Count === 2) {
          criterion7DurationStartIndex = index - 1;
        }
      }

      // 8、连续14点交替上下变化: x-bar>CL，CL>x-bar之间交替
      if (index > this.indexs.criterion8Index && index > 0) {
        if (
          criterion8Count >= 2 &&
          ((criterion8Comparative === 'bigger' && value < values[index - 1]) ||
            (criterion8Comparative === 'smaller' && value > values[index - 1]))
        ) {
          criterion8Count = criterion8Count + 1;
          criterion8Comparative = criterion8Comparative === 'smaller' ? 'bigger' : 'smaller';
          if (criterion8Count >= 14) {
            criterion8Count = 0;
            this.indexs.criterion8Index = index;
            criterionList.push({
              criterion: 8,
              durationStartIndex: criterion8DurationStartIndex,
              durationEndIndex: index,
              description: SPCEightCriterions.criterions[8]
            });
            criterion8DurationStartIndex = -1;
          }
        } else {
          criterion8Count = 2;
          criterion8Comparative = value > values[index - 1] ? 'bigger' : 'smaller';
          criterion8DurationStartIndex = index - 1;
        }
      }
    }

    return criterionList.length > 0 ? criterionList : false;
  }
}
