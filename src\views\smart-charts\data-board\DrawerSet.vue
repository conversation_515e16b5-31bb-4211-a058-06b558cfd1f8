<template>
  <el-drawer
    v-model="showDrawer"
    title="编辑自定义看板"
    direction="rtl"
    :before-close="handleClose"
    :size="950"
    destroy-on-close
    :close-on-click-modal="false"
    custom-class="drawerSet"
  >
    <DrawerLayout v-loading="drawerLoading" :has-button-group="false" :has-page-header="false">
      <el-form ref="formRef" :model="detailInfo" :rules="dialogRules" label-position="left">
        <el-form-item
          label="标题名称："
          prop="title"
          :label-width="formLabelWidth"
          :rules="{ required: true, message: '请输入标题名称', trigger: 'change' }"
        >
          <el-input
            v-model="detailInfo.title"
            class="btInput inlineBlock"
            autocomplete="off"
            placeholder="请输入标题名称"
          />
          <span class="btTitle">（为了更好的视觉体验，建议10字以内）</span>
        </el-form-item>
        <el-form-item label=" 语音播报：" prop="voiceBroadcastFlag" :label-width="formLabelWidth">
          <el-switch
            v-model="detailInfo.voiceBroadcastFlag"
            class="inner-switch"
            :active-value="true"
            :inactive-value="false"
            :active-text="detailInfo.voiceBroadcastFlag ? '开启' : '关闭'"
          />
        </el-form-item>
        <el-form-item label=" 布局排版：" :label-width="formLabelWidth" style="margin-bottom: 0" />
      </el-form>
      <div class="addComponent cursorPointer" @click="dialogShow = true"><i class="el-icon-plus" /> 添加组件</div>
      <div v-loading="boxLoading" class="typesettingBox">
        <el-row v-if="isShowBoxContent" id="sortableList">
          <el-col
            v-for="(item, index) in detailInfo.layoutDetail"
            :key="item.id"
            :span="item.width"
            class="typeSetItem"
          >
            <div class="item">
              <div class="top">
                <span class="tes-move" style="font-size: 12px; cursor: move"
                  ><i class="iconfont" style="font-size: 12px; cursor: move"
                /></span>
                {{ item.name }}
              </div>
              <div class="bottom cursorPointer" @click="handleDelete(item, index)">
                <i class="iconfont tes-delete" />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="drawer-fotter">
        <el-button type="primary" :loading="drawerLoading" @click="onSubmit">确认</el-button>
        <el-button :loading="drawerLoading" @click="handleClose">取消</el-button>
      </div>
    </DrawerLayout>
    <DialogSet
      :dialog-visiable="dialogShow"
      :data-source-list="componentDetail"
      :layout-detail="detailInfo.layoutDetail"
      @closeDialog="closeDialog"
    />
  </el-drawer>
</template>

<script>
import { ref, watch, reactive, getCurrentInstance, toRefs, nextTick } from 'vue';
import DrawerLayout from '@/components/DrawerLayout';
import DialogSet from './DialogSet.vue';
import Sortable from 'sortablejs';
import { dashboardconfigSave } from '@/api/dataBoard';

export default {
  name: 'DrawerSet',
  components: { DrawerLayout, DialogSet },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // 抽屉事件
    const showDrawer = ref(props.drawer);
    const state = reactive({
      isModified: false,
      drawerLoading: false,
      isShowBoxContent: true,
      componentDetail: [],
      boxLoading: false,
      formLabelWidth: '85px',
      dialogShow: false,
      formRef: ref(),
      dialogRules: {},
      detailInfo: {
        layoutDetail: []
      }
    });
    // 关闭抽屉
    const handleClose = () => {
      if (state.isModified) {
        proxy
          .$confirm('当前配置未保存是否确认离开？', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false
          })
          .then(() => {
            showDrawer.value = false;
            state.isModified = false;
            context.emit('close', false);
          })
          .catch(() => {});
      } else {
        showDrawer.value = false;
        context.emit('close', false);
      }
    };
    watch(props, newValue => {
      showDrawer.value = newValue.drawer;
      if (showDrawer.value) {
        initDetail();
      }
    });
    const handleModify = value => {
      state.isModified = true;
    };
    const handleSortable = () => {
      nextTick(() => {
        const el = document.getElementById('sortableList');
        Sortable.create(el, {
          animation: 300,
          handle: '.tes-move',
          draggable: '.typeSetItem',
          ghostClass: 'ghost',
          // 拖动对象移动样式
          dragClass: 'drag',
          forceFallback: true,
          onEnd({ newIndex, oldIndex }) {
            if (newIndex !== oldIndex) {
              const currRow = state.detailInfo.layoutDetail.splice(oldIndex, 1)[0];
              state.detailInfo.layoutDetail.splice(newIndex, 0, currRow);
            }
          }
        });
      });
    };
    const initDetail = () => {
      state.detailInfo = JSON.parse(JSON.stringify(props.detailData));
      state.componentDetail = state.detailInfo.componentDetail;
      state.isModified = false;
      state.isShowBoxContent = true;
      handleSortable();
    };
    const onSubmit = () => {
      proxy
        .$confirm('是否确认保存当前配置', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          customClass: 'dataBoardMessageBox',
          type: 'warning',
          showClose: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        })
        .then(() => {
          dashboardconfigSave(state.detailInfo).then(res => {
            if (res) {
              context.emit('close', true);
            }
          });
        })
        .catch(() => {});
    };
    const closeDialog = detail => {
      if (detail.isRefresh) {
        // 重置为默认视图
        state.dialogShow = false;
        context.emit('close', true);
      } else if (detail.item) {
        state.boxLoading = true;
        state.isShowBoxContent = false;
        state.boxLoading = false;
        state.isShowBoxContent = true;
        state.detailInfo.layoutDetail.push(detail.item);
      } else {
        state.dialogShow = false;
      }
    };
    const handleDelete = (item, index) => {
      state.detailInfo.layoutDetail.splice(index, 1);
    };
    return {
      ...toRefs(state),
      handleSortable,
      handleModify,
      handleDelete,
      onSubmit,
      closeDialog,
      handleClose,
      showDrawer,
      initDetail
    };
  }
};
</script>

<style lang="scss">
.dataBoardMessageBox {
  background: linear-gradient(90deg, #245094 0%, #3f7ab6 100%) !important;
  border: 0;
  .el-message-box__message p {
    color: #7ad0ff;
  }
}
.drawerSet {
  background: linear-gradient(180deg, #2d59a5 0%, #4280b9 100%);
  // background: #253D87;
  border-radius: 10px 0 0 10px;
  .typeSetItem {
    height: 100px;
    // background: #fff;
    padding: 10px 0 0 10px;
  }
  .item {
    background: #2d59a5;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 5px;
    height: 100%;
    position: relative;
    .top {
      line-height: 30px;
      color: #fff;
      font-size: 12px;
      padding: 0 10px;
    }
    .bottom {
      height: 20px;
      line-height: 20px;
      position: absolute;
      bottom: 5px;
      right: 5px;
      color: #7ad0ff;
    }
  }
  .inlineBlock {
    display: inline-block;
  }
  .btInput {
    width: 500px;
  }
  .btTitle {
    display: inline-block;
    color: #7ad0ff;
    margin-left: 10px;
  }
  .cursorPointer {
    cursor: pointer;
  }
  .addComponent {
    height: 30px;
    line-height: 30px;
    width: 100px;
    background: #4280b9;
    color: #fff;
    position: relative;
    left: 85%;
    text-align: center;
    font-size: 14px;
    border-radius: 8px 8px 0 0;
  }
  .typesettingBox {
    background: #4280b9;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 8px;
    padding: 0 10px 10px 0;
  }
  .drawer-fotter {
    background: transparent !important;
  }
  .el-drawer__header {
    background: linear-gradient(90deg, #245094 0%, #3f7ab6 100%);
    padding-bottom: 10px;
    padding-top: 14px;
    // background: #4A6FD0;
  }
  .el-drawer__header span {
    color: #7ad0ff !important;
    font-weight: 700;
  }
  .el-drawer__close-btn {
    color: #7ad0ff;
  }
  .el-form .el-form-item .el-form-item__label {
    color: #fff;
  }
  .el-switch.is-checked .el-switch__core {
    // border-color: #66B3E7;
    // background-color: #66B3E7;
    border-color: #245094;
    background-color: #245094;
  }
  .el-button--primary {
    background: #245094;
    border-color: #245094;
    color: #fff;
  }
  .el-button:hover {
    color: #000;
    border-color: #7ad0ff;
    background-color: #d4edfe;
  }
  .el-button--primary:hover,
  .el-button--primary:focus {
    background: #245094;
    border-color: #245094;
    color: #fff;
  }
  .el-input__inner:focus {
    border-color: #7ad0ff;
  }
}
</style>
