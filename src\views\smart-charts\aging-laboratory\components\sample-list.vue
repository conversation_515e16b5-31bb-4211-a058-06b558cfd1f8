<template>
  <!-- 老化箱状态 -->
  <div class="sample-list flex flex-col flex-1">
    <div class="item-title">
      <div class="title">放样记录</div>
    </div>
    <div class="auto-scroll-table-container flex-1 overflow-y-auto flex flex-col">
      <el-row class="table-th">
        <el-col :span="3"> 设备编号 </el-col>
        <el-col :span="4"> 设备名称 </el-col>
        <el-col :span="2"> 状态 </el-col>
        <el-col :span="3"> 样品信息 </el-col>
        <el-col :span="2"> 实时温度℃ </el-col>
        <el-col :span="4"> 开始时间 </el-col>
        <el-col :span="4"> 结束时间 </el-col>
        <el-col :span="2"> 放样人 </el-col>
      </el-row>
      <div
        :id="`sampleContent${type}`"
        class="flex-1 overflow-y-auto table-content flex flex-col gap-2"
        @mouseenter="monseenter"
        @mouseleave="mouseleave"
        @mouseenter.prevent
      >
        <el-row v-for="item in tableData" :key="item.id">
          <el-col :span="3"> {{ item.deviceNumber }} </el-col>
          <el-col :span="4"> {{ item.boxName }} </el-col>
          <el-col :span="2"> {{ dictionaryAll['24'].all[item.boxStatus] || item.boxStatus }} </el-col>
          <el-col :span="3"> {{ item.sampleName }} </el-col>
          <el-col :span="2"> {{ item.sampleTemperature }} </el-col>
          <el-col :span="4"> {{ item.startDateTime }} </el-col>
          <el-col :span="4"> {{ item.endDateTime }} </el-col>
          <el-col :span="2"> {{ getNameByid(item.createBy) || item.createBy }} </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, toRefs, nextTick, onBeforeUnmount, onMounted } from 'vue';
import { sampleSplitList } from '@/api/aging-laboratory';
import { getNameByid } from '@/utils/common';

export default {
  props: {
    type: {
      type: Number,
      default: 0
    },
    dictionary: {
      type: Object,
      default: () => {}
    }
  },
  setup(props) {
    const tableRef = ref(null);
    const state = reactive({
      timer: null,
      maxDataLength: 20,
      totalCount: 0,
      dictionary: {
        24: {
          all: {},
          enable: {}
        }
      },
      dictionaryAll: props.dictionary || {
        24: {
          all: {},
          enable: {}
        }
      },
      detailLoading: false,
      tableData: []
    });

    // 初始化数据
    const initData = async () => {
      state.detailLoading = true;
      const { data } = await sampleSplitList().finally((state.detailLoading = false));
      if (data) {
        state.tableData = data.data[props.type];
        transferTotal(data.data[props.type]);
      }
    };
    const transferTotal = data => {
      state.totalCount = data.length;
      nextTick(() => {
        document.getElementById(`sampleContent${props.type}`).scrollTop = 0;
      });
      setTimer();
    };

    // 获取数据
    const loadMoreData = async () => {
      state.detailLoading = true;
      const { data } = await sampleSplitList().finally((state.detailLoading = false));
      if (data) {
        state.tableData = [...state.tableData, ...data.data[props.type]];
        state.totalCount = state.tableData.length;
        setTimeout(
          () => {
            setTimer();
          },
          state.totalCount == 0 ? 30000 : 500
        );
      }
    };

    const removeTimer = () => {
      if (state.timer) {
        clearInterval(state.timer);
        state.timer = null;
      }
    };
    const setTimer = () => {
      if (state.timer === null) {
        state.timer = setInterval(() => {
          if (document.getElementById(`sampleContent${props.type}`)) {
            // 可滚动内容的高度
            const scrollHeight = document.getElementById(`sampleContent${props.type}`).scrollHeight;
            // 可视区域的高度（内容实际高度+上下内边距）
            const clientHeight = document.getElementById(`sampleContent${props.type}`).clientHeight;
            const heightDifference = scrollHeight - clientHeight;
            // scroll height：readable and writable
            if (document.getElementById(`sampleContent${props.type}`).scrollTop >= heightDifference) {
              removeTimer();
              setTimeout(
                () => {
                  loadMoreData();
                },
                state.totalCount == 0 ? 30000 : 500
              );
            } else {
              document.getElementById(`sampleContent${props.type}`).scrollTop++;
            }
          }
        }, 44);
      }
    };
    const monseenter = () => {
      removeTimer();
    };
    const mouseleave = () => {
      setTimer();
    };
    onBeforeUnmount(() => {
      removeTimer();
    });
    onMounted(() => {
      nextTick(() => {
        initData();
      });
    });

    return {
      ...toRefs(state),
      tableRef,
      monseenter,
      mouseleave,
      getNameByid
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/intelligentChart.scss';
.table-content {
  color: #fff;
  text-align: center;
  .el-col {
    line-height: 28px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 2px 5px;
    font-size: 14px;
  }
  // .el-col:nth-child(2n) {
  //   background-color: #4163bc;
  // }
  // .el-col:nth-child(2n + 1) {
  //   background-color: #4a6fd0;
  // }
  // box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}
.table-th {
  color: #3898f6;
  line-height: 30px;
  font-size: 15px;
  background-color: #21345b;
}
.item-title {
  background: #1a2a47;
  border-radius: 8px 8px 0px 0px;
  font-size: 18px;
  color: #4ec3f7;
  height: 52px;
  line-height: 30px;
  font-weight: 700;
  padding: 10px 10px 10px 10px;
  text-align: left;
}
.title {
  border-bottom: 1px solid #87b7ff;
  padding: 5px 5px 5px 5px;
}
.auto-scroll-table-container {
  width: 100%;
  height: 100%;
  border: 2px solid #1a2a47;
  background-color: #1a2a47;
  padding: 3px 10px 10px 10px;
  border-radius: 0 0 4px 4px;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}
</style>
