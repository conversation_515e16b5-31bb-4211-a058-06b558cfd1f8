<!-- 样品借出 -->
<template>
  <div>
    <el-dialog
      v-model="detailVisible"
      title="样品借出"
      :width="720"
      :before-close="closeDialog"
      :close-on-click-modal="false"
    >
      <el-form
        v-if="detailVisible"
        ref="refForm"
        v-loading="loading"
        :model="formData"
        size="small"
        label-position="top"
        label-width="90px"
        class="form"
      >
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item :label="tenantType === 1 ? '申请单号：' : '委托编号：'">
              <span>{{ formData.presentationCode || '--' }}</span>
            </el-form-item></el-col
          >
          <el-col :span="12">
            <el-form-item label="样品名称：">
              <span class="name-txt">{{ formData.mateName || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="借出人：">
              <span class="name-txt">{{ getNameByid(formData.userId) || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="借出日期："
              prop="operateTime"
              :rules="{ required: true, message: '请选择借出日期', trigger: 'change' }"
            >
              <el-date-picker
                v-model="formData.operateTime"
                style="width: 100%"
                type="date"
                placeholder="请选择借出日期"
              /> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item
              label="借出分包商："
              prop="orgId"
              :rules="{ required: true, message: '请选择借出分包商', trigger: 'change' }"
            >
              <el-select v-model="formData.orgId" placeholder="请选择借出分包商">
                <el-option v-for="(value, key) in subcontractorJson" :key="key" :label="value.name" :value="key" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="借出数量："
              prop="quantity"
              :rules="[
                { required: true, message: '请输入借出数量', trigger: 'blur' },
                { validator: validateNumber, trigger: 'change' }
              ]"
            >
              <el-input
                v-model="formData.quantity"
                v-trim
                type="number"
                maxlength="18"
                placeholder="请输入借出数量"
                autocomplete="off"
                style="width: 58%"
              />
              <el-select v-model="formData.unitName" placeholder="请选择单位" disabled style="width: 40%; float: right">
                <el-option-group v-for="item in dirList" :key="item.label" :label="item.label">
                  <el-option
                    v-for="val in item.group"
                    :key="val.id"
                    :label="val.name"
                    :value="val.code"
                    :disabled="val.status !== 1"
                  >
                    <span style="float: left">{{ val.name }}</span>
                    <span v-if="val.status !== 1" class="fr" style="color: red">已停用</span>
                  </el-option>
                </el-option-group>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="物流信息：" prop="logisticsInfo">
              <el-input
                v-model="formData.logisticsInfo"
                placeholder="请输入物流信息"
                type="textarea"
                :rows="2"
                autocomplete="off"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog(false)">取 消</el-button>
          <el-button type="primary" @click="onSumbit()">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { reactive, toRefs, watch, ref, getCurrentInstance } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getDictionaryDetail } from '@/api/dictionary';
import { filterProcessMode, filterSampleUnitToName } from '@/utils/formatJson';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { getLoginInfo } from '@/utils/auth';
import { useStore } from 'vuex';
import { getList } from '@/api/customerManagement';
import { warehousingLoadn } from '@/api/samplestorage';
import { validateNumber } from '@/utils/validate';

export default {
  name: 'ModuleLend',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    status: {
      type: Number,
      default: 0
    },
    detail: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, ctx) {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const state = reactive({
      formData: {},
      refForm: ref(),
      tenantType: store.user.tenantInfo.type,
      subcontractorJson: {},
      dirList: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ],
      detailVisible: false,
      listData: [],
      loading: false
    });
    watch(
      () => props.visible,
      newValue => {
        if (props.visible) {
          state.detailVisible = props.visible;
          if (state.detailVisible) {
            getDictionaryList();
            getSubcontractorList();
          }
          state.formData = {
            presentationCode: props.detail.presentationCode,
            mateName: props.detail.mateName,
            sampleId: props.detail.sampleId,
            operateTime: new Date(),
            userId: getLoginInfo().accountId,
            unitName: props.detail.sampleUnit
          };
          state.subcontractorJson = {};
        }
      },
      { deep: true }
    );
    // 关闭弹框
    const closeDialog = i => {
      state.detailVisible = false;
      ctx.emit('close');
    };
    // 获取分包商列表
    const getSubcontractorList = () => {
      const params = {
        limit: '-1',
        page: '1',
        isValid: true
      };
      getList(params).then(res => {
        if (res) {
          const data = res.data.data.list;
          data.forEach(item => {
            state.subcontractorJson[item.id] = item;
          });
        }
      });
    };
    // 提交保存
    const onSumbit = () => {
      proxy.$refs['refForm'].validate(valid => {
        if (valid) {
          state.drawerLoading = true;
          state.formData.orgName = state.subcontractorJson[state.formData.orgId].name;
          warehousingLoadn(state.formData).then(res => {
            state.drawerLoading = false;
            if (res) {
              ctx.emit('close', true);
              state.detailVisible = false;
              proxy.$message.success(res.data.message);
            }
          });
        } else {
          return false;
        }
      });
    };

    // 回库数量单位
    const getDictionaryList = () => {
      getDictionaryDetail(5).then(res => {
        res.data.data.dictionaryoption.forEach(item => {
          if (item.status === 1) {
            state.dirList[0].group.push(item);
          } else {
            state.dirList[1].group.push(item);
          }
        });
      });
    };
    return {
      ...toRefs(state),
      getPermissionBtn,
      getDictionaryList,
      getSubcontractorList,
      closeDialog,
      filterProcessMode,
      formatDate,
      validateNumber,
      filterSampleUnitToName,
      getNameByid,
      onSumbit
    };
  }
};
</script>

<style scoped lang="scss">
:deep(.el-select) {
  width: 100%;
}
.file-upload-input {
  display: none;
  z-index: -9999;
}
.tips,
.flies {
  margin: 20px 0;
}
.tips {
  margin-left: 10px;
}
.btn-del {
  color: $tes-primary;
  margin-left: 5px;
  cursor: pointer;
}
.details-info-box {
  overflow: hidden;
  border: 1px solid #bbb;
  border-radius: 4px;
  padding: 10px 16px;
}
.details-info-box-row {
  overflow: hidden;
  line-height: 1.2;
  .el-col {
    margin: 8px 0;
  }
}
</style>
