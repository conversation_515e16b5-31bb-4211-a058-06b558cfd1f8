<template>
  <!-- 检测项目-检测依据 -->
  <div class="Specification">
    <div class="header-search-group">
      <div v-loading="listLoading" class="btn-group">
        <el-button
          v-if="!isEdit"
          size="small"
          icon="el-icon-plus"
          type="default"
          @click="handleAddEdit()"
          @keyup.prevent
          @keydown.enter.prevent
          >新增</el-button
        >
        <el-button
          v-if="!isAdd && formData.tableList.length > 0"
          size="small"
          type="default"
          icon="el-icon-edit-outline"
          @click="handleEditable()"
          @keyup.prevent
          @keydown.enter.prevent
          >编辑</el-button
        >
        <el-button
          v-if="isEdit || isAdd"
          size="small"
          icon="el-icon-check"
          type="primary"
          @click="handleSaveEdit()"
          @keyup.prevent
          @keydown.enter.prevent
          >保存</el-button
        >
        <el-button
          v-if="isEdit || isAdd"
          size="small"
          icon="el-icon-close"
          @click="handleCancelEdit()"
          @keyup.prevent
          @keydown.enter.prevent
          >取消</el-button
        >
      </div>
    </div>
    <el-form ref="ruleForm" :model="formData">
      <el-table
        ref="tableRef"
        v-loading="listLoading"
        :data="formData.tableList"
        size="medium"
        fit
        border
        height="auto"
        highlight-current-row
        class="dark-table base-table"
        @header-dragend="drageHeader"
        @expand-change="handleExpandChange"
      >
        <el-table-column type="expand">
          <template #default="{ row }">
            <div v-if="!row.id" class="expand-placeholder">请先新增检测依据</div>
            <div v-else-if="detailTableMap.get(row.id)?.loading" v-loading="true" class="expand-placeholder" />
            <template v-else-if="detailTableMap.get(row.id)?.columns?.length > 0">
              <el-table :data="detailTableMap.get(row.id).list">
                <el-table-column
                  v-for="column in detailTableMap.get(row.id).columns"
                  :key="column.code"
                  :prop="column.code"
                  :label="column.name"
                />
              </el-table>
              <pagination
                :page="detailTableMap.get(row.id).page"
                :limit="detailTableMap.get(row.id).limit"
                :total="detailTableMap.get(row.id).total"
                @pagination="changePage(row.id, $event)"
              />
            </template>
            <div v-else class="expand-placeholder">请先导入</div>
            <!-- <div v-if="row.loading" v-loading class="expand-placeholder" />
            <div v-else-if="!row.id" class="expand-placeholder">请先新增检测依据</div>
            <div v-else-if="row.id && !row.detailList?.length" class="expand-placeholder">请先导入</div>
            <el-table
              v-else-if="row.detailList?.length > 0"
              :data="row.detailList"
              highlight-current-row
              style="width: 100%"
            >
              <el-table-column type="index" label="序号" :width="colWidth.serialNo" />
              <el-table-column property="date" label="检测依据" width="120" />
              <el-table-column property="name" label="截面" width="120" />
              <el-table-column property="address" label="导体材质" />
            </el-table>
            <div v-else class="expand-placeholder" /> -->
          </template>
        </el-table-column>
        <el-table-column prop="length" label="检测依据" min-width="100">
          <template #default="{ row }">
            <el-form-item
              v-if="isEdit || (isAdd && !row.id)"
              :prop="`tableList.${row.index}.basisName`"
              :rules="{ required: true, message: '请输入检测依据', trigger: 'change' }"
              style="margin: 0px"
            >
              <el-input v-model="row.basisName" placeholder="请输入检测依据" @keyup.prevent @keydown.enter.prevent />
            </el-form-item>
            <span v-else> {{ row.basisName || '--' }} </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" :width="colWidth.operation">
          <template #default="{ row }">
            <template v-if="row.id">
              <span class="blue-color" @click="handleImport(row)">导入</span>
              <span class="blue-color" @click="handleExport(row)">导出</span>
            </template>
            <span v-else class="blue-color" @click="handleDeleteRow(row.index)">删除</span>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <!-- 导入弹出窗 -->
    <el-dialog
      v-model="dialogImport"
      title="数据导入"
      top="5vh"
      :close-on-click-modal="false"
      width="1080px"
      @close="handleDialogImportClose"
    >
      <div class="title">为确保数据导入的准确性，请按照以下规则进行导入</div>
      <ul class="uploadRules">
        <li>请按照<span class="blue-color" @click="downLoadFile">检测依据导入模板.xlsx</span>在模板内录入数据</li>
        <li>只导入第一张工作表（sheet1）；</li>
        <li>请上传*.xls，*.xlsx格式文件；</li>
        <li>目前一次性最多上传5000条数据；</li>
        <li>文件大小不超过10M；</li>
      </ul>
      <div class="uploadArea">
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="headerconfig"
          :data="uploadParams"
          :auto-upload="false"
          :limit="1"
          :accept="fileAcceptExcel"
          :on-exceed="handleExceed"
          :before-upload="beforeUpload"
          :on-success="handleFileSuccess"
        >
          <el-button size="small" type="primary" plain>选择上传文件</el-button>
        </el-upload>
      </div>
      <!-- :on-change="handleUploadChange" -->
      <!-- <template v-if="differentColumns.length">
        <p class="label">数据差异：</p>
        <el-table
          :data="differentData"
          class="test-configuration-table"
          style="width: 100%"
          :row-class-name="tableRowClassName"
        >
          <el-table-column
            v-for="column in differentColumns"
            :key="column.code"
            :prop="column.code"
            :label="column.name"
          >
            <template #default="{ row }">
              <template v-if="column.code === 'determine' && row.updateDetermine">
                <span class="value-before">{{ row.determine }}</span>
                <span class="value-after">{{ row.updateDetermine }}</span>
              </template>
              <template v-else-if="column.code === 'keyValue' && row.updateKeyValue">
                <span class="value-before">{{ row.keyValue }}</span>
                <span class="value-after">{{ row.updateKeyValue }}</span>
              </template>
              <span v-else>{{ row[column.code] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </template> -->
      <template #footer>
        <span class="dialog-footer">
          <el-button :loading="dialogLoading" @click="dialogImport = false">取 消</el-button>
          <el-button type="primary" :loading="dialogLoading" @click="submitUpload">确认上传</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getCurrentInstance, reactive, ref, toRefs, watch } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { getColWidth } from '@/utils/func/customTable';
import { getToken } from '@/utils/auth';
import {
  getCapabilityStandardBasisList,
  saveCapabilityStandardBasis,
  getCapabilityStandardBasisDetailPage
} from '@/api/capability';
import { capabilityStandardBasisImportExcelUploadUrl } from '@/api/uploadAction';
import Pagination from '@/components/Pagination';
import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import { fileAcceptExcel } from '@/utils/fileAccept';

export default {
  name: 'DetectionBasis',
  components: { Pagination },
  props: {
    capabilityId: {
      type: String,
      default: ''
    },
    activeName: {
      type: String,
      default: ''
    },
    itemName: {
      type: String,
      default: ''
    },
    keyParameter: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  setup(props) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableRef: ref(),
      dialogLoading: false,
      dialogImport: false, // 数据导入弹出框
      detailItemId: '', // 项目id
      keyParameterList: [], // 关键参数
      isAdd: false, // 是否是新增状态
      isEdit: false, // 是否是编辑状态 true 是
      ruleForm: ref(),
      listLoading: false,
      formData: {
        tableList: []
      },
      detailTableMap: new Map(),
      uploadAction: capabilityStandardBasisImportExcelUploadUrl(), // 附件上传地址
      headerconfig: {
        Authorization: getToken()
      },
      uploadParams: {
        basisId: '', // 检测依据id
        mode: '2' // 导入模式：0-仅新增，1-仅更新，2-更新和新增
      },
      uploadRef: ref(),
      projectName: '',
      capabilityId: '',
      differentColumns: [],
      differentData: [],
      submitting: false,
      expLoading: false
    });

    watch(props, newValue => {
      if (newValue.activeName === '9') {
        state.detailItemId = props.capabilityId;
        state.isEdit = false;
        state.isAdd = false;
        state.expLoading = false;
        state.submitting = false;
        state.keyParameterList = props.keyParameter.filter(item => item.status == 1);
        state.projectName = props.itemName;
        getList();
      }
    });

    // 获取检测依据列表
    const getList = () => {
      state.listLoading = true;
      getCapabilityStandardBasisList(state.detailItemId).then(res => {
        state.listLoading = false;
        if (res) {
          state.formData.tableList = res.data.data.map((row, index) => ({
            ...row,
            index
          }));
        }
      });
    };

    // 获取检测依据子表
    const getDetailList = ({ id, page = 1, limit = 10 }) => {
      const params = { id, page, limit };
      const detailTable = {
        loading: true,
        page: params.page,
        limit: params.limit,
        total: 0,
        columns: [],
        list: []
      };
      state.detailTableMap.set(params.id, detailTable);
      getCapabilityStandardBasisDetailPage(params).then(res => {
        if (res.data.code === 200) {
          const data = res.data.data;
          state.detailTableMap.set(params.id, {
            loading: false,
            page: data.currPage,
            limit: data.pageSize,
            total: data.totalCount,
            columns: data.headers,
            list: data.list
          });
        } else {
          detailTable.loading = false;
          state.detailTableMap.set(params.id, detailTable);
        }
      });
    };

    // 对话框关闭
    const handleDialogImportClose = () => {
      state.uploadRef?.clearFiles?.();
      state.dialogImport = false;
      state.uploadParams.basisId = '';
      state.uploadParams.mode = '2';
      state.differentColumns = [];
      state.differentData = [];
    };

    // 导出
    const downLoadFile = async () => {
      const keyParamsList = state.keyParameterList.filter(item => item.status == 1);
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Sheet1');
      const row1 = ['检测标准', '关键参数的特征值(需与物资分类中的规格保持一致)', '', 'XX项目下关键参数'];
      const row2 = ['', '', '', state.projectName];
      const row3 = ['检测依据', '', ''];
      const row4 = ['', '', ''];
      const row5 = ['', '', ''];
      const row6 = ['', '', ''];
      const row7 = ['', '', ''];
      for (var i = 0; i < keyParamsList.length - 1; i++) {
        row1.push('');
        row2.push('');
      }
      if (keyParamsList.length) {
        for (var j = 0; j < keyParamsList.length; j++) {
          row3.push(keyParamsList[j].name);
          row4.push('');
          row5.push(keyParamsList[j].unitname);
          row6.push('');
          row7.push('');
        }
      } else {
        row3.push('');
        row4.push('');
        row5.push('');
        row6.push('');
        row7.push('');
      }
      row1.push('示例说明');
      row2.push('该行输入检测项目,需与系统中保持一致');
      row3.push('该行输入关键参数，需与系统中保持一致');
      row4.push(
        '该行输入判定方式，说明: 区间值用:[],(],{),() 大于：> 大于等于：≥ 小于：< 小于等于：≤ 等于：= 仅有标准描述无具体上线下，则为空'
      );
      row5.push('该行输入关键参数的单位，默认从系统中导出');
      row6.push('该行输入一些特殊说明的备注，仅做导入时备注说明，不导入系统');
      row7.push('从当前行输入数据');
      // 添加数据
      worksheet.addRow(row1);
      worksheet.addRow(row2);
      worksheet.addRow(row3);
      worksheet.addRow(row4);
      worksheet.addRow(row5);
      worksheet.addRow(row6);
      worksheet.addRow(row7);
      const charaStartCol = 4; // B列开始
      const charaEndCol = charaStartCol + keyParamsList.length - 1;
      // 合并单元格
      worksheet.mergeCells('B1:C1'); // 合并
      // worksheet.mergeCells('D1:E1'); // 合并
      if (keyParamsList.length) {
        worksheet.mergeCells(1, charaStartCol, 1, charaEndCol);
        worksheet.mergeCells(2, charaStartCol, 2, charaEndCol);
      }
      // worksheet.mergeCells('D2:E2'); // 合并
      // 设置边框样式
      const boldBorder = {
        top: { style: 'thin' }, // 上边框加粗
        left: { style: 'thin' }, // 左边框加粗
        bottom: { style: 'thin' }, // 下边框加粗
        right: { style: 'thin' } // 右边框加粗
      };
      // 设置整个工作表内容水平居中
      worksheet.eachRow(row => {
        row.eachCell(cell => {
          cell.alignment = { horizontal: 'center', wrapText: true, vertical: 'middle' };
          cell.border = boldBorder;
        });
      });
      // 最后一列
      const lastColumn = worksheet.columnCount;
      // 设置斜体
      worksheet.getRow(1).font = {
        italic: true
      };
      worksheet.getColumn(lastColumn).font = {
        italic: true
      };
      // 每一列25
      worksheet.columns.forEach(column => {
        column.width = 25;
      });
      // 最后一列宽45
      worksheet.getColumn(lastColumn).width = 45;
      worksheet.getColumn(lastColumn).alignment = { horizontal: 'left', wrapText: true, vertical: 'middle' };

      // 设置边框
      worksheet.eachRow(row => {
        row.eachCell(cell => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      });

      // 导出文件
      const buffer = await workbook.xlsx.writeBuffer();
      // // 生成文件
      saveAs(new Blob([buffer]), '检测依据导入模板.xlsx');
      proxy.$message.success('导出成功！');
    };

    // 数据导入确认上传
    const submitUpload = () => {
      state.uploadRef.submit();
    };

    const handleExceed = files => {
      state.uploadRef.clearFiles(['success', 'fail', 'ready']);
      state.uploadRef.handleStart(files[0]);
    };

    // const handleUploadChange = uploadFile => {
    //   state.uploadRef.submit();
    // };

    // 上传文件的限制
    const beforeUpload = file => {
      var fileName = '';
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过10M');
        return false;
      } else if (fileName !== 'xls' && fileName !== 'xlsx') {
        proxy.$message.error('仅支持.xls，.xlsx文件扩展名');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };

    // 上传成功的钩子
    const handleFileSuccess = (res, file) => {
      if (res.code === 200) {
        proxy.$message.success(res.message);
        const id = state.uploadParams.basisId;
        if (state.detailTableMap.has(id)) {
          getDetailList({ id });
        }
        state.dialogImport = false;
      } else {
        proxy.$message.error(res.message);
      }
    };

    // 新增
    const handleAddEdit = () => {
      state.formData.tableList.push({
        index: state.formData.tableList.length,
        basisName: '',
        capabilityId: state.detailItemId
      });
      state.isAdd = true;
    };

    // 编辑表格
    const handleEditable = () => {
      state.isEdit = true;
    };

    // 保存修改
    const handleSaveEdit = () => {
      if (state.formData.tableList.length === 0) {
        state.isAdd = false;
        state.isEdit = false;
        return;
      }
      state.ruleForm
        .validate()
        .then(valid => {
          if (valid) {
            state.listLoading = true;
            saveCapabilityStandardBasis({ list: state.formData.tableList }).then(res => {
              state.listLoading = false;
              if (res) {
                getList();
                state.isAdd = false;
                state.isEdit = false;
                proxy.$message.success(res.data.message);
              }
            });
          } else {
            return false;
          }
        })
        .catch(() => {
          proxy.$message.error('请输入检测依据');
          return false;
        });
    };

    // 取消修改
    const handleCancelEdit = () => {
      state.isEdit = false;
      state.isAdd = false;
      for (let index = state.formData.tableList.length - 1; index >= 0; index--) {
        const item = state.formData.tableList[index];
        if (!item.id) {
          state.formData.tableList.splice(index, 1);
        }
      }
    };

    // 导入
    const handleImport = row => {
      state.uploadParams.basisId = row.id;
      state.dialogImport = true;
    };

    // 导出
    const handleExport = row => {
      getCapabilityStandardBasisDetailPage({ id: row.id, page: 1, limit: -1 }).then(res => {
        if (res.data.code === 200) {
          const data = res.data.data;
          if (data.list.length > 0) {
            exportExcel(data.headers, data.list, row);
            proxy.$message.success('导出成功！');
          } else {
            proxy.$message('没有导入数据');
          }
        }
      });
    };
    // 格式化返回的数据
    const filterMaterList = (list, characteristic) => {
      const groups = {};
      const featFields = characteristic.map(item => item.code);
      // 1. 按相同特征分组
      list.forEach(item => {
        const groupKeyParts = [item.standardBasis];
        featFields.forEach(field => {
          groupKeyParts.push(item[field] || ''); // 如果字段不存在，用空字符串代替
        });
        const groupKey = groupKeyParts.join('_');

        if (!groups[groupKey]) {
          groups[groupKey] = {
            feat1: item.feat1,
            feat3: item.feat3,
            feat4: item.feat4,
            feat5: item.feat5,
            standardBasis: item.standardBasis,
            keyNames: [],
            determines: [],
            keyValues: []
          };
        }

        // 2. 收集不同的 keyName、determine、keyValue
        groups[groupKey].keyNames.push(item.keyName);
        groups[groupKey].determines.push(item.determine || item.determine); // 处理拼写错误
        groups[groupKey].keyValues.push(item.keyValue);
      });

      // 3. 转换为最终格式
      return Object.values(groups).map(group => ({
        ...group,
        keyNames: group.keyNames, // 数组形式
        determines: group.determines, // 数组形式
        keyValues: group.keyValues // 数组形式
      }));
    };

    // 导出Excel函数
    const exportExcel = async (headers, list, row) => {
      // 特征值
      const characteristic = headers.filter(item => item.code.indexOf('feat') != -1);
      const dataInfo = filterMaterList(list, characteristic);
      // 创建工作簿
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Sheet1');

      // 计算关键列位置
      const charaStartCol = 2; // B列开始
      const charaEndCol = charaStartCol + characteristic.length - 1;
      const keyParamStartCol = charaEndCol + 1;
      const keyParamEndCol = keyParamStartCol + state.keyParameterList.length - 1;
      const exampleCol = keyParamEndCol + 1;

      // 定义换行样式
      const wrapTextStyle = {
        alignment: {
          wrapText: true,
          vertical: 'middle',
          horizontal: 'left'
        }
      };
      const totalColumns = 1 + characteristic.length + state.keyParameterList.length + 1; // A + 特征列 + 关键参数列 + 示例列

      // 设置所有列宽为25
      const columns = Array(totalColumns).fill({ width: 25 });
      worksheet.columns = columns;
      // 第一行
      const row1 = worksheet.getRow(1);
      row1.getCell(1).value = '检测标准'; // A1

      // 合并关键参数特征值单元格
      if (characteristic.length > 1) {
        worksheet.mergeCells(1, charaStartCol, 1, charaEndCol);
      }
      row1.getCell(charaStartCol).value = '关键参数的特征值(需与物资分类中的规格保持一致)';

      // XX项目下关键参数
      const defaultParamsCol = charaEndCol + 1;
      if (state.keyParameterList.length > 1) {
        worksheet.mergeCells(1, defaultParamsCol, 1, keyParamEndCol);
      }
      row1.getCell(defaultParamsCol).value = 'XX项目下关键参数';

      // 示例说明
      row1.getCell(exampleCol).value = '示例说明';

      // 第二行
      const row2 = worksheet.getRow(2);
      // 检测项目名称
      if (state.keyParameterList.length > 1) {
        worksheet.mergeCells(2, keyParamStartCol, 2, keyParamEndCol);
      }
      row2.getCell(keyParamStartCol).value = state.projectName;
      // 测试示例2
      row2.getCell(exampleCol).value = '该行输入检测项目,需与系统中保持一致';

      // 第三行
      const row3 = worksheet.getRow(3);
      row3.getCell(1).value = '检测依据'; // A3

      // 添加特征值名称
      characteristic.forEach((item, index) => {
        row3.getCell(charaStartCol + index).value = item.name;
      });

      // 添加关键参数名称
      state.keyParameterList.forEach((item, index) => {
        row3.getCell(keyParamStartCol + index).value = item.name;
      });

      row3.getCell(exampleCol).value = '该行输入关键参数，需与系统中保持一致';

      // 第四行
      const row4 = worksheet.getRow(4);
      row4.getCell(exampleCol).value =
        '该行输入判定方式，说明: 区间值用:[],(],{),() 大于：> 大于等于：≥ 小于：< 小于等于：≤ 等于：= 仅有标准描述无具体上线下，则为空';
      row4.getCell(exampleCol).style = wrapTextStyle;
      // 第五行
      const row5 = worksheet.getRow(5);
      // 添加单位
      state.keyParameterList.forEach((item, index) => {
        row5.getCell(keyParamStartCol + index).value = item.unitname;
      });
      // 测试示例5
      row5.getCell(exampleCol).value = '该行输入关键参数的单位，默认从系统中导出';
      row5.getCell(exampleCol).style = wrapTextStyle;

      // 第六行
      const row6 = worksheet.getRow(6);
      row6.getCell(exampleCol).value = '该行输入一些特殊说明的备注，仅做导入时备注说明，不导入系统';
      row6.getCell(exampleCol).style = wrapTextStyle;

      // 第七行 - 数据行
      dataInfo.forEach((item, index) => {
        const rowExcel = worksheet.getRow(7 + index);
        rowExcel.getCell(1).value = item.standardBasis; // A7
        // 特征值数据
        characteristic.forEach((charaItem, index) => {
          rowExcel.getCell(charaStartCol + index).value = item[charaItem.code];
        });
        // 关键参数数据
        state.keyParameterList.forEach((keyItem, index) => {
          const keyIndex = item.keyNames.findIndex(keyVal => keyVal == keyItem.name);
          if (keyIndex !== -1) {
            rowExcel.getCell(keyParamStartCol + index).value =
              item.keyValues[keyIndex] !== undefined ? item.keyValues[keyIndex] : '';
            row4.getCell(keyParamStartCol + index).value = item.determines[keyIndex];
          } else {
            rowExcel.getCell(keyParamStartCol + index).value = '';
            row4.getCell(keyParamStartCol + index).value = '';
          }
        });
        if (index == 0) {
          rowExcel.getCell(exampleCol).value = '从当前行输入数据';
        }
      });
      const boldBorder = {
        top: { style: 'thin' }, // 上边框加粗
        left: { style: 'thin' }, // 左边框加粗
        bottom: { style: 'thin' }, // 下边框加粗
        right: { style: 'thin' } // 右边框加粗
      };
      // 设置整个工作表内容水平居中
      worksheet.eachRow(row => {
        row.border = boldBorder;
        row.alignment = { horizontal: 'center', wrapText: true, vertical: 'middle' };
      });
      // 第一行斜体
      worksheet.getRow(1).font = {
        italic: true
      };
      // 最后一列斜体
      const lastColumn = worksheet.columnCount;
      worksheet.getColumn(lastColumn).font = {
        italic: true
      };
      // 最后一列宽度45
      worksheet.getColumn(lastColumn).width = 45;
      worksheet.getColumn(lastColumn).alignment = { horizontal: 'left', wrapText: true, vertical: 'middle' };

      // 生成Excel文件
      const buffer = await workbook.xlsx.writeBuffer();
      saveAs(
        new Blob([buffer], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }),
        `${row.basisName}检测依据参数.xlsx`
      );
    };

    // 删除新增的数据
    const handleDeleteRow = index => {
      state.formData.tableList.splice(index, 1);
    };

    // 设置编辑和新增的颜色
    const tableRowClassName = ({ row }) => {
      if (row.updateDetermine || row.updateKeyValue) {
        return 'updated-row';
      } else {
        return 'new-row';
      }
    };

    // 处理表格展开方法
    const handleExpandChange = (row, expandedRows) => {
      if (row.id) {
        if (expandedRows.some(item => item.id === row.id)) {
          getDetailList({ id: row.id });
        } else {
          state.detailTableMap.delete(row.id);
        }
      }
    };

    const changePage = (id, pagination) => {
      var params = {
        id,
        page: pagination.page,
        limit: pagination.limit
      };
      getDetailList(params);
    };

    return {
      ...toRefs(state),
      handleDialogImportClose,
      downLoadFile,
      handleAddEdit,
      handleImport,
      handleExport,
      handleDeleteRow,
      handleExceed,
      // handleUploadChange,
      handleFileSuccess,
      beforeUpload,
      submitUpload,
      drageHeader,
      getNameByid,
      getNamesByid,
      getPermissionBtn,
      formatDate,
      colWidth,
      handleEditable,
      handleCancelEdit,
      handleSaveEdit,
      getColWidth,
      tableRowClassName,
      handleExpandChange,
      changePage,
      fileAcceptExcel
    };
  }
};
</script>
<style lang="scss" scoped>
.header-search-group {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  .btn-group {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
.expand-placeholder {
  color: #aaa;
  line-height: 12px;
  text-align: center;
}

.base-table.dark-table :deep(.el-table__body-wrapper) {
  max-height: 100% !important;
}
.test-configuration-table {
  :deep(.el-table__body-wrapper) {
    max-height: calc(100vh - 556px);
  }
}

.base-table :deep(.el-table__body tr:hover > .el-table__expanded-cell) {
  background-color: transparent !important;
}

.label {
  color: #909399;
  line-height: 1;
}

.uploadArea {
  margin-bottom: 14px;
}

.title {
  background-color: #f0f2f5;
  line-height: 30px;
  padding: 0 10px;
  margin: 14px 0;
}

.uploadRules {
  padding: 5px 10px;
  background: #f0f2f5;
  margin: 14px 0;
  li {
    list-style: none;
    line-height: 22px;
    font-size: 13px;
  }
  .blue-color {
    margin-right: 0;
  }
}

.el-table {
  :deep(.updated-row) {
    background-color: #f3d19e;
  }

  :deep(.new-row) {
    background-color: #b3e19d;
  }
}

.value-before {
  text-decoration: line-through;
}

.value-after {
  margin-left: 4px;
}
</style>
