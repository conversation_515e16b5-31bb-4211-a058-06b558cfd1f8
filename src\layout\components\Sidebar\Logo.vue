<template>
  <div class="sidebar-logo-container">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" :to="DEFAULT_HOME_PAGE">
        <img v-if="logo" :src="logo" class="sidebar-logo logo-collapse" />
        <img v-if="!collapse" :src="isQms ? qmsLogo : logo_title" class="sidebar-title" />
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" :to="DEFAULT_HOME_PAGE">
        <img v-if="logo" :src="logo" class="sidebar-logo" />
        <img :src="isQms ? qmsLogo : logo_title" class="sidebar-title" />
      </router-link>
    </transition>
  </div>
</template>

<script>
import { computed } from 'vue';
import { getTenantConfig, getLIMSConfig } from '@/utils/auth';
import { checkServerIp, QmsServerIps } from '@/utils/server';
import logo from '@/assets/img/QMS.png';
import logoTitle from '@/assets/img/logo-title.svg';
import qmsLogo from '@/assets/img/logo-title-qms.svg';

export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  setup() {
    const isQms = computed({
      get: () => checkServerIp(QmsServerIps)
    });
    const DEFAULT_HOME_PAGE = getLIMSConfig().VITE_HOME_PAGE;
    const logo_title = getTenantConfig().normalLogo ? getTenantConfig().normalLogo : logoTitle;

    return { logo, logo_title, qmsLogo, isQms, DEFAULT_HOME_PAGE };
  }
};
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 48px;
  background: #19293d;

  & .sidebar-logo-link {
    display: flex !important;
    align-items: center;
    justify-content: center;
    .sidebar-logo {
      width: 37px;
      height: 27px;
      padding: 0 6px 0 4px;
    }
    .logo-collapse {
      margin: 0 auto;
    }
    .sidebar-title {
      padding: 0 4px;
    }
  }
}
</style>
