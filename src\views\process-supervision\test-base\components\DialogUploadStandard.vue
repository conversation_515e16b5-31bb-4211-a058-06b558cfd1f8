<template>
  <!-- 标准导入 dialog -->
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    :width="800"
    top="5vh"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :custom-class="listData.length === 1 ? '' : 'lot-dialog'"
  >
    <div class="certificate-container">
      <el-row>
        <el-col :span="24" style="margin-top: 10px">
          <DragUpload
            :clear-file="clearFile"
            :file-tip="fileTip"
            accept-file-type=".xlsx,.xls,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            @get-file="getFile"
            @set-clear-file="setClearFile"
          />
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog(false)">取 消</el-button>
        <el-button type="primary" :loading="isLoading" @click="sumbit()">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { importStandardExcel } from '@/api/testBase';
import DragUpload from '@/components/DragUpload';

export default {
  name: 'DialogUploadStandard',
  components: { DragUpload },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    uploadParams: {
      type: Object,
      default: function () {
        return {};
      }
    },
    lists: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['close'],
  setup(props, ctx) {
    const state = reactive({
      dialogVisible: false,
      listData: [],
      title: '导入标准',
      selectedFile: null,
      uploadParams: {},
      clearFile: false,
      isLoading: false
    });

    const excelRegex = /.(xls|xlsx)$/i;

    const fileSize = 200;

    const fileTip = ` 1.只能上传Excel文件(拓展名为xls或xlsx);2.文件大小限制在${fileSize}M以内`;

    // 关闭弹框
    const closeDialog = i => {
      state.dialogVisible = false;
      state.clearFile = true;
      ctx.emit('close', false);
    };

    const sumbit = () => {
      if (!state.selectedFile) {
        ElMessage.warning('检测不到已选择的文件, 请检查!');
        return false;
      }
      const params = new FormData();
      const limitSize = state.selectedFile.size / 1024 / 1024;
      if (!excelRegex.test(state.selectedFile.name)) {
        ElMessage.warning(state.selectedFile.name + '文件格式不符, 请导入Excel文件!');
        return false;
      }
      if (limitSize > fileSize) {
        ElMessage.warning(`${state.selectedFile.name}超过${fileSize}M, 请重新选择文件导入!`);
        return false;
      } else {
        state.isLoading = true;
        params.append('file', state.selectedFile);
        importStandardExcel(params, props.lists[0].id).then(res => {
          if (res) {
            ElMessage.success(`导入标准文件-${state.selectedFile.name}-成功！`);
            state.dialogVisible = false;
            state.clearFile = true;
            ctx.emit('close', true);
          }
          state.isLoading = false;
        });
      }
    };

    // #region 导入文件

    // #endregion

    // #region 拖拽导入文件

    const getFile = fileVal => {
      state.selectedFile = fileVal;
    };

    const setClearFile = val => {
      state.clearFile = false;
    };

    // #endregion

    watch(
      () => props.visible,
      newValue => {
        if (props.visible) {
          state.listData = props.lists;
          state.dialogVisible = true;
          state.uploadParams = props.uploadParams;
        }
      },
      { deep: true }
    );

    onMounted(() => {});

    return {
      ...toRefs(state),
      closeDialog,
      sumbit,
      getFile,
      setClearFile,
      fileTip
    };
  }
};
</script>

<style scoped lang="scss"></style>
