<template>
  <!-- 工作统计 -->
  <ListLayout :has-custom-header="true" :has-custom-main="true" :main-offset-top="topHeight">
    <template #page-custom-header>
      <SingleLineHeader>
        <template #left-form-group>
          <el-form
            ref="editFrom"
            label-width="110px"
            label-position="top"
            :inline="true"
            :model="searchForm"
            @submit.prevent
          >
            <el-row :gutter="10">
              <el-col :span="9">
                <el-form-item
                  label="检测日期："
                  class="date-range"
                  prop="submitTime"
                  :rules="{ required: true, message: '请选择时间范围', trigger: 'change' }"
                >
                  <el-date-picker
                    v-model="searchForm.submitTime"
                    type="daterange"
                    size="small"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :shortcuts="shortcuts"
                    @change="handleDatePicker"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item
                  label="检验类型："
                  prop="inspectionType"
                  :rules="{ required: true, message: '请选择检验类型', trigger: 'change' }"
                >
                  <el-select
                    v-model="searchForm.inspectionType"
                    class="chart-query-select"
                    placeholder="选择检验类型"
                    size="small"
                    clearable
                    @change="changeInspectionType"
                  >
                    <el-option
                      v-for="item in inspectionTypeOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="物料分组：" prop="materialGroupName">
                  <el-tag v-if="searchForm.materialGroupName" closable @close="deleteMaterialgroup">
                    {{ searchForm.materialGroupName }}
                  </el-tag>
                  <el-button
                    v-else
                    size="small"
                    icon="el-icon-plus"
                    @click="handleMaterialgroup"
                    @keyup.prevent
                    @keydown.enter.prevent
                    >选择物料分组</el-button
                  >
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="工序：">
                  <el-tag v-if="searchForm.workingProcedureName" size="small" closable @close="deleteProcess">
                    <span class="query-tag">{{ searchForm.workingProcedureName }}</span>
                  </el-tag>
                  <el-button
                    v-else
                    size="small"
                    icon="el-icon-plus"
                    @click="handleProcess"
                    @keyup.prevent
                    @keydown.enter.prevent
                    >选择工序</el-button
                  >
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
        <template #right-button-group>
          <el-button
            key="sample"
            type="text"
            size="small"
            @click="renderSampleData()"
            @keyup.prevent
            @keydown.enter.prevent
          >
            <span class="el-icon-data-analysis" />
          </el-button>
          <el-button key="cancel" size="small" @click="cancelRender()" @keyup.prevent @keydown.enter.prevent
            >清空</el-button
          >
          <el-button
            :loading="renderLoading"
            type="primary"
            size="small"
            @click="renderWorkData()"
            @keyup.prevent
            @keydown.enter.prevent
            >查询</el-button
          >
        </template>
      </SingleLineHeader>
    </template>

    <template #page-custom-main>
      <el-row>
        <el-col :span="12">
          <CustomPanel :has-margin-right="true" :has-margin-left="true">
            <template #panel-title>
              <div class="panel-header-left">
                <span class="title">样品数统计</span>
              </div>
            </template>
            <template #panel-content>
              <line-bar-chart
                v-if="showChart && showSampleData"
                :option="sampleChartOption"
                :width="'100%'"
                :height="'40vh'"
              />
              <el-empty v-else :image="emptyImg" description="暂无图表" />
            </template>
          </CustomPanel>
        </el-col>
        <el-col :span="12">
          <CustomPanel :has-margin-right="true">
            <template #panel-title>
              <div class="panel-header-left">
                <span class="title">项目数统计</span>
              </div>
            </template>
            <template #panel-content>
              <line-bar-chart
                v-if="showChart && showItemData"
                :option="itemChartOption"
                :width="'100%'"
                :height="'40vh'"
              />
              <el-empty v-else :image="emptyImg" description="暂无图表" />
            </template>
          </CustomPanel>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <CustomPanel
            :has-margin-top="true"
            :has-margin-right="true"
            :has-margin-bottom="true"
            :has-margin-left="true"
          >
            <template #panel-title>
              <div class="panel-header-left">
                <span class="title">负责人及时率</span>
              </div>
            </template>
            <template #panel-content>
              <line-bar-chart
                v-if="showChart && showOwnerData"
                :option="ownerChartOption"
                :width="'100%'"
                :height="'40vh'"
              />
              <el-empty v-else :image="emptyImg" description="暂无图表" />
            </template>
          </CustomPanel>
        </el-col>
        <el-col :span="12">
          <CustomPanel :has-margin-top="true" :has-margin-right="true" :has-margin-bottom="true">
            <template #panel-title>
              <div class="panel-header-left">
                <span class="title">实验员及时率</span>
              </div>
            </template>
            <template #panel-content>
              <line-bar-chart
                v-if="showChart && showTesterData"
                :option="testerChartOption"
                :width="'100%'"
                :height="'40vh'"
              />
              <el-empty v-else :image="emptyImg" description="暂无图表" />
            </template>
          </CustomPanel>
        </el-col>
      </el-row>

      <el-row>
        <el-col>
          <div style="height: 60px; width: 100%" />
        </el-col>
      </el-row>
    </template>
    <template #other>
      <process
        :dialog-visiable="dialogProcess"
        :is-add="isAdd"
        :detail-data="searchForm"
        @selectRow="getProcess"
        @closeDialog="closeProcess"
      />
      <MaterialGroup
        :dialog-visiable="dialogMaterialgroup"
        :detail-data="searchForm"
        :is-add="true"
        @selectRow="getMaterialgroup"
        @closeDialog="closeMaterialgroup"
      />
      <BottomPanel>
        <template #panel-content>
          <div style="text-align: right">
            <el-button
              :loading="downloadLoading"
              type="primary"
              size="small"
              @click="exportToExcel()"
              @keyup.prevent
              @keydown.enter.prevent
              >数据导出</el-button
            >
          </div>
        </template>
      </BottomPanel>
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, computed, onMounted } from 'vue';
import LineBarChart from '@/components/LineBarChart';
import ListLayout from '@/components/ListLayout';
import { formatDate, formatDateTime } from '@/utils/formatTime';
import { InspectionType } from '@/data/industryTerm';
import { getDictionary } from '@/api/user';
import { getAxisInterval } from '../func/calculate';
import { sampleSet, itemSet, ownerSet, testerSet } from '../data/testData';
import { past3Months } from '@/data/dateShortcuts';
import { useStore } from 'vuex';
import Process from '@/components/BusinessComponents/Process';
import MaterialGroup from '@/components/BusinessComponents/MaterialGroup';
import SingleLineHeader from '@/components/PageComponents/SingleLineHeader';
import BottomPanel from '@/components/PageComponents/BottomPanel';
import CustomPanel from '@/components/PageComponents/CustomPanel';
import { ElMessage } from 'element-plus';
import { getWorkStatistics, getWorkStatisticsExcel } from '@/api/order';
import { formatterTips } from '../func/formatter';
import emptyImg from '@/assets/img/empty-chart.png';

export default {
  name: 'WorkStatistics',
  components: { ListLayout, LineBarChart, Process, MaterialGroup, SingleLineHeader, BottomPanel, CustomPanel },
  setup(props, context) {
    const store = useStore();
    const processData = reactive({
      dialogProcess: false,
      isAdd: false
    });
    const data = reactive({
      searchForm: {
        // 检验类型
        inspectionType: '',
        inspectionTypeName: '',
        // 物料分组
        materialGroupId: '',
        materialGroupName: '',
        materialGroupNo: '',
        // 工序
        workingProcedureId: '',
        workingProcedureName: '',
        workingProcedureCode: '',
        // 检测日期
        submitTime: [formatDate(new Date().getTime() - 3600 * 1000 * 24 * 90), formatDate(new Date())],
        startTime: formatDate(new Date().getTime() - 3600 * 1000 * 24 * 90),
        endTime: formatDate(new Date())
      },
      shortcuts: past3Months,
      inspectionTypeOptions: InspectionType,
      materialGroupOptions: MaterialGroup,
      voltageLevelOptions: {},
      dialogMaterialgroup: false,
      topHeight: 140,
      showChart: false,
      showSampleData: false,
      showItemData: false,
      showOwnerData: false,
      showTesterData: false,
      downloadLoading: false,
      renderLoading: false
    });
    const sampleTable = ref([]);
    const paramTable = ref([]);

    // #region 计算和渲染图表
    const sampleChartOption = ref({
      title: {
        text: '示例折线图'
      },
      xAxis: {
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    });

    const itemChartOption = ref({
      title: {
        text: '示例折线图'
      },
      xAxis: {
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    });

    const ownerChartOption = ref({
      title: {
        text: '示例折线图'
      },
      xAxis: {
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    });

    const testerChartOption = ref({
      title: {
        text: '示例折线图'
      },
      xAxis: {
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    });

    const bottomBarSeriesLabel = {
      show: true,
      fontWeight: 'bold',
      offset: [0, 0]
    };

    const topBarSeriesLabel = {
      show: true,
      fontWeight: 'bold',
      offset: [0, -20]
    };

    function renderChart(sampleData = sampleSet, itemData = itemSet, ownerData = ownerSet, testerData = testerSet) {
      data.showChart = true;
      data.showSampleData = sampleData.length > 0;
      data.showItemData = itemData.length > 0;
      data.showOwnerData = ownerData.length > 0;
      data.showTesterData = testerData.length > 0;

      if (data.showSampleData) {
        renderSampleChart(sampleData);
      }
      if (data.showItemData) {
        renderItemChart(itemData);
      }
      if (data.showOwnerData) {
        renderOwnerChart(ownerData);
      }
      if (data.showTesterData) {
        renderTesterChart(testerData);
      }
    }

    function renderSampleChart(sampleData) {
      const sampleCount = sampleData.length;
      const sampleCompletedArray = [];
      const sampleIncompleteArray = [];
      const sampleUserArray = [];
      const sampleAllArray = [];
      sampleData.forEach(item => {
        sampleCompletedArray.push(item.finishNum);
        sampleIncompleteArray.push(item.unFinishNum);
        sampleUserArray.push(item.name);
        sampleAllArray.push(item.totalNum);
      });

      const sampleAllSortedArray = JSON.parse(JSON.stringify(sampleAllArray)).sort((a, b) => a - b);
      const sampleAxisInfo = getAxisInterval(0, sampleAllSortedArray[sampleAllSortedArray.length - 1], sampleCount);
      const sampleMin = sampleAxisInfo.axisMin;
      const sampleMax = sampleAxisInfo.axisMax;
      const sampleInterval = sampleAxisInfo.axistInterval;

      var emphasisStyle = {
        itemStyle: {
          // shadowBlur: 10,
          // shadowColor: 'rgba(0,0,0,0.3)'
        }
      };

      sampleChartOption.value = {
        color: ['#80D9C5', '#F2D09D'],
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis',
          extraCssText: 'text-align:left', // 设置tooltip的自定义样式
          textStyle: {
            color: '#303133'
          }
        },
        legend: {
          data: ['已完成', '未完成'],
          itemGap: 40
        },
        grid: {
          left: '40',
          right: '40',
          bottom: '30'
        },
        toolbox: {},
        xAxis: {
          type: 'category',
          name: '姓名',
          splitLine: { show: false },
          splitArea: { show: false },
          data: sampleUserArray
        },
        yAxis: {
          type: 'value',
          interval: sampleInterval,
          min: sampleMin,
          max: sampleMax
        },
        series: [
          {
            name: '已完成',
            type: 'bar',
            barWidth: '50%',
            stack: 'one',
            emphasis: emphasisStyle,
            barMaxWidth: 50,
            data: sampleCompletedArray,
            itemStyle: {
              borderWidth: 2,
              borderColor: '#00B38A'
            },
            label: bottomBarSeriesLabel
          },
          {
            name: '未完成',
            type: 'bar',
            stack: 'one',
            emphasis: emphasisStyle,
            barWidth: '50%',
            barMaxWidth: 50,
            data: sampleIncompleteArray,
            itemStyle: {
              borderWidth: 2,
              borderColor: '#E6A23C'
            },
            label: topBarSeriesLabel
          }
        ]
      };
    }

    function renderItemChart(itemData) {
      const itemCount = itemData.length;
      const itemCompletedArray = [];
      const itemIncompleteArray = [];
      const itemUserArray = [];
      const itemAllArray = [];
      itemData.forEach(item => {
        itemCompletedArray.push(item.finishNum);
        itemIncompleteArray.push(item.unFinishNum);
        itemUserArray.push(item.name);
        itemAllArray.push(item.totalNum);
      });

      const itemAllSortedArray = JSON.parse(JSON.stringify(itemAllArray)).sort((a, b) => a - b);
      const itemAxisInfo = getAxisInterval(0, itemAllSortedArray[itemAllSortedArray.length - 1], itemCount);
      const itemMin = itemAxisInfo.axisMin;
      const itemMax = itemAxisInfo.axisMax;
      const itemInterval = itemAxisInfo.axistInterval;

      var emphasisStyle = {
        itemStyle: {
          // shadowBlur: 10,
          // shadowColor: 'rgba(0,0,0,0.3)'
        }
      };

      itemChartOption.value = {
        color: ['#80D9C5', '#F2D09D'],
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis',
          extraCssText: 'text-align:left', // 设置tooltip的自定义样式
          textStyle: {
            color: '#303133'
          }
        },
        legend: {
          data: ['已完成', '未完成'],
          itemGap: 40
        },
        grid: {
          left: '40',
          right: '40',
          bottom: '30'
        },
        toolbox: {},
        xAxis: {
          type: 'category',
          name: '姓名',
          splitLine: { show: false },
          splitArea: { show: false },
          data: itemUserArray
        },
        yAxis: {
          type: 'value',
          interval: itemInterval,
          min: itemMin,
          max: itemMax
        },
        series: [
          {
            name: '已完成',
            type: 'bar',
            stack: 'one',
            emphasis: emphasisStyle,
            barWidth: '50%',
            barMaxWidth: 50,
            data: itemCompletedArray,
            itemStyle: {
              borderWidth: 2,
              borderColor: '#00B38A'
            },
            label: bottomBarSeriesLabel
          },
          {
            name: '未完成',
            type: 'bar',
            stack: 'one',
            emphasis: emphasisStyle,
            barWidth: '50%',
            barMaxWidth: 50,
            data: itemIncompleteArray,
            itemStyle: {
              borderWidth: 2,
              borderColor: '#E6A23C'
            },
            label: topBarSeriesLabel
          }
        ]
      };
    }

    function renderOwnerChart(ownerData) {
      const ownerTotalNumArray = [];
      const ownerCompleteArray = [];
      const ownerUserArray = [];
      const ownerAllArray = [];
      const ownerRateArray = [];
      ownerData.forEach(item => {
        ownerTotalNumArray.push(item.totalNum);
        ownerCompleteArray.push(item.finishNum);
        ownerUserArray.push(item.name);
        ownerAllArray.push(item.finishNum);
        ownerRateArray.push(item.totalNum > 0 ? Number(((item.finishNum / item.totalNum) * 100).toFixed(2)) : 0);
      });

      const ownerAllSortedArray = JSON.parse(JSON.stringify(ownerTotalNumArray)).sort((a, b) => a - b);
      const ownerAxisInfo = getAxisInterval(0, ownerAllSortedArray[ownerAllSortedArray.length - 1], 10);
      const ownerPercentageAxisInfo = getAxisInterval(0, 100, 10);

      var emphasisStyle = {
        itemStyle: {
          // shadowBlur: 10,
          // shadowColor: 'rgba(0,0,0,0.3)'
        }
      };

      ownerChartOption.value = {
        color: ['#9FCEFF', '#67C23A'],
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis',
          extraCssText: 'text-align:left', // 设置tooltip的自定义样式
          formatter: function (params) {
            return formatterTips(params);
          },
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          data: ['样品数', '完成及时率'],
          itemGap: 40
        },
        grid: {
          left: '40',
          right: '40',
          bottom: '30'
        },
        toolbox: {},
        xAxis: {
          type: 'category',
          name: '姓名',
          splitLine: { show: false },
          splitArea: { show: false },
          data: ownerUserArray,
          nameTextStyle: {
            verticalAlign: 'top',
            padding: [15, 1, 9, 1]
          },
          nameGap: 10,
          nameLocation: 'end'
        },
        yAxis: [
          {
            type: 'value',
            name: '数量',
            interval: ownerAxisInfo.axistInterval,
            min: ownerAxisInfo.axisMin,
            max: ownerAxisInfo.axisMax
          },
          {
            type: 'value',
            name: '百分比',
            interval: ownerPercentageAxisInfo.axistInterval,
            min: ownerPercentageAxisInfo.axisMin,
            max: ownerPercentageAxisInfo.axisMax,
            axisLabel: {
              formatter: '{value}%'
            }
          }
        ],
        series: [
          {
            name: '样品数',
            type: 'bar',
            stack: 'one',
            emphasis: emphasisStyle,
            data: ownerTotalNumArray,
            barWidth: '50%',
            barMaxWidth: 50,
            itemStyle: {
              borderWidth: 2,
              borderColor: '#409EFF'
            },
            label: topBarSeriesLabel
          },
          {
            name: '完成及时率',
            type: 'line',
            yAxisIndex: 1,
            symbol: 'circle',
            symbolSize: 10,
            emphasis: emphasisStyle,
            data: ownerRateArray,
            itemStyle: {
              color: '#D1EDC4',
              borderWidth: 2,
              borderColor: '#67C23A'
            },
            lineStyle: {
              color: '#67C23A',
              width: 2,
              shadowColor: 'rgba(0, 179, 138, 0.3)',
              shadowBlur: 4,
              shadowOffsetY: 6
            }
          }
        ]
      };
    }

    function renderTesterChart(testerData) {
      const testerCompletedArray = [];
      const testerIncompleteArray = [];
      const testerUserArray = [];
      const testerAllArray = [];
      const testerRateArray = [];
      testerData.forEach(item => {
        testerCompletedArray.push(item.finishNum);
        testerIncompleteArray.push(item.unFinishNum);
        testerUserArray.push(item.name);
        testerAllArray.push(item.totalNum);
        testerRateArray.push(item.totalNum > 0 ? Number(((item.finishNum / item.totalNum) * 100).toFixed(2)) : 0);
      });

      const testerAllSortedArray = JSON.parse(JSON.stringify(testerAllArray)).sort((a, b) => a - b);
      const testerAxisInfo = getAxisInterval(0, testerAllSortedArray[testerAllSortedArray.length - 1], 10);
      const testerPercentageAxisInfo = getAxisInterval(0, 100, 10);

      var emphasisStyle = {
        itemStyle: {
          // shadowBlur: 10,
          // shadowColor: 'rgba(0,0,0,0.3)'
        }
      };

      testerChartOption.value = {
        color: ['#B3E09C', '#E6A23C'],
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis',
          extraCssText: 'text-align:left', // 设置tooltip的自定义样式
          formatter: function (params) {
            return formatterTips(params);
          }
        },
        legend: {
          data: ['项目数', '完成及时率'],
          itemGap: 40
        },
        grid: {
          left: '40',
          right: '40',
          bottom: '30'
        },
        toolbox: {},
        xAxis: {
          type: 'category',
          name: '姓名',
          splitLine: { show: false },
          splitArea: { show: false },
          data: testerUserArray,
          nameTextStyle: {
            verticalAlign: 'top',
            padding: [15, 1, 9, 1]
          },
          nameGap: 10,
          nameLocation: 'end'
        },
        yAxis: [
          {
            type: 'value',
            interval: testerAxisInfo.axistInterval,
            min: testerAxisInfo.axisMin,
            max: testerAxisInfo.axisMax
          },
          {
            type: 'value',
            name: '百分比',
            interval: testerPercentageAxisInfo.axistInterval,
            min: testerPercentageAxisInfo.axisMin,
            max: testerPercentageAxisInfo.axisMax,
            axisLabel: {
              formatter: '{value}%'
            }
          }
        ],
        series: [
          {
            name: '项目数',
            type: 'bar',
            stack: 'one',
            emphasis: emphasisStyle,
            barWidth: '50%',
            barMaxWidth: 50,
            data: testerAllArray,
            itemStyle: {
              borderWidth: 2,
              borderColor: '#67C23A'
            },
            label: topBarSeriesLabel
          },
          {
            name: '完成及时率',
            type: 'line',
            symbol: 'circle',
            yAxisIndex: 1,
            symbolSize: 10,
            emphasis: emphasisStyle,
            data: testerRateArray,
            itemStyle: {
              color: '#F8E3C5',
              borderWidth: 2,
              borderColor: '#E6A23C'
            },
            lineStyle: {
              color: '#E6A23C',
              width: 2,
              shadowColor: 'rgba(230, 162, 60, 0.5)',
              shadowBlur: 4,
              shadowOffsetY: 6
            }
          }
        ]
      };
    }

    // #endregion

    // #region 查询条件表单
    const setMainOffset = height => {
      data.topHeight = height;
    };

    const getVoltageLevel = () => {
      getDictionary(2).then(res => {
        data.voltageLevelOptions = res.data.data?.dictionaryoption;
      });
    };

    const materialClassifications = computed({
      get: () => store.state.user.materialList
    });

    const closeProcess = value => {
      processData.dialogProcess = false;
    };
    const closeMaterialgroup = value => {
      data.dialogMaterialgroup = false;
    };

    const deleteProcess = () => {
      data.searchForm.workingProcedureId = '';
      data.searchForm.workingProcedureName = '';
      data.searchForm.workingProcedureCode = '';
    };

    const deleteMaterialgroup = () => {
      data.searchForm.materialGroupId = '';
      data.searchForm.materialGroupNo = '';
      data.searchForm.materialGroupName = '';
    };

    const handleProcess = () => {
      processData.dialogProcess = true;
    };

    const handleMaterialgroup = () => {
      data.dialogMaterialgroup = true;
    };

    const handleDatePicker = value => {
      if (value) {
        data.expired = false;
        data.searchForm.startTime = formatDate(value[0]);
        data.searchForm.endTime = formatDate(value[1]);
      } else {
        data.searchForm.startTime = '';
        data.searchForm.endTime = '';
      }
    };

    const getProcess = value => {
      processData.dialogProcess = false;
      if (data.searchForm.workingProcedureId && value.workingProcedureId !== data.formData.workingProcedureId) {
        data.searchForm.workingProcedureId = '';
        data.searchForm.workingProcedureName = '';
        data.searchForm.workingProcedureCode = '';
      }
      data.searchForm = { ...data.searchForm, ...value };
    };

    const getMaterialgroup = value => {
      data.dialogMaterialgroup = false;
      if (data.searchForm.materialGroupId && value.materialGroupId !== data.formData.materialGroupId) {
        data.searchForm.materialGroupId = '';
        data.searchForm.materialGroupName = '';
        data.searchForm.materialGroupNo = '';
      }
      data.searchForm = { ...data.searchForm, ...value };
    };

    const getInspectionTypeList = () => {
      getDictionary('JYLX').then(res => {
        const resultData = res.data.data;
        data.inspectionTypeOptions = [];
        resultData.dictionaryoption.forEach(item => {
          if (item.status === 1) {
            data.inspectionTypeOptions.push(item);
          }
        });
        data.searchForm.inspectionType = data.inspectionTypeOptions[0]?.code;
        data.searchForm.inspectionTypeName = data.inspectionTypeOptions[0]?.name;
        renderWorkData();
      });
    };

    const changeInspectionType = value => {
      data.inspectionTypeOptions.forEach(item => {
        if (item.id === value) {
          data.searchForm.inspectionTypeName = item.name;
        }
      });
    };

    const getWorkPostBody = () => {
      if (!data.searchForm.startTime || !data.searchForm.endTime) {
        ElMessage.warning({
          message: '请先选择检测日期',
          type: 'warning'
        });
        return false;
      }
      if (!data.searchForm.inspectionType) {
        ElMessage.warning({
          message: '请先选择检验类型',
          type: 'warning'
        });
        return false;
      }
      // if (!data.searchForm.materialGroupNo) {
      //   ElMessage.warning({
      //     message: '请先选择物料分组',
      //     type: 'warning'
      //   })
      //   return
      // }

      const postBody = {
        startDate: data.searchForm.startTime,
        endDate: data.searchForm.endTime,
        materialGroupId: data.searchForm.materialGroupNo,
        productionProcedureNo: data.searchForm.workingProcedureCode,
        type: `${data.searchForm.inspectionType}`
      };
      return postBody;
    };

    const renderWorkData = () => {
      const workPostBody = getWorkPostBody();
      if (workPostBody) {
        data.renderLoading = true;
        getWorkStatistics(workPostBody)
          .then(res => {
            if (res.data.code === 200) {
              const result = res.data.data;
              renderChart(result.samples, result.projects, result.responsiblies, result.experiments);
            }
          })
          .catch(err => {
            ElMessage.error({
              message: `${err.message}`,
              type: 'error'
            });
          })
          .finally(() => {
            data.renderLoading = false;
          });
      }
    };

    const renderSampleData = () => {
      data.showChart = true;
      renderChart();
    };

    const cancelRender = () => {
      data.showChart = false;
    };

    const exportToExcel = () => {
      const workPostBody = getWorkPostBody();
      if (workPostBody) {
        data.downloadLoading = true;
        getWorkStatisticsExcel(workPostBody)
          .then(res => {
            if (res.data.code === 200) {
              const result = res.data.data;
              if (result.samples.length > 0 || result.projects.length > 0) {
                const workSheets = [];
                import('@/utils/Export2Excel').then(excel => {
                  if (result.samples.length > 0) {
                    const sampleHeader = [
                      '序号',
                      '样品编号',
                      '样品名称',
                      '规格型号',
                      '物料分组',
                      '检验类型',
                      '检验开始日期',
                      '要求完成日期',
                      '实验负责人',
                      '送检工序',
                      '报告编号',
                      '报告首次提交日期'
                    ];
                    const sampleFilterVal = [
                      'num',
                      'secSampleNum',
                      'mateName',
                      'prodType',
                      'materialGroup',
                      'type',
                      'startDate',
                      'finishedDate',
                      'chargeBy',
                      'productionProcedure',
                      'reportNo',
                      'submitDate'
                    ];
                    const sampleExcelData = formatExcelData(sampleFilterVal, result.samples);
                    const sampleSheet = excel.getWorkSheet({
                      header: sampleHeader,
                      data: sampleExcelData,
                      wsName: `样品信息`
                    });
                    workSheets.push(sampleSheet);
                  }
                  if (result.projects.length > 0) {
                    for (let i = 0; i < result.projects.length; i++) {
                      result.projects[i].testDate = `${formatDate(result.projects[i].completeStartDate)}-${formatDate(
                        result.projects[i].completeDateTime
                      )}`;
                    }
                    const projectHeader = [
                      '序号',
                      '样品编号',
                      '样品名称',
                      '规格型号',
                      '检测项目',
                      '检测项目要求开始日期',
                      '检测项目要求完成日期',
                      '实验员',
                      '试验首次提交日期',
                      '试验日期',
                      '审核日期'
                    ];
                    const projectFilterVal = [
                      'num',
                      'secSampleNum',
                      'mateName',
                      'prodType',
                      'sourceName',
                      'startDateTime',
                      'finishDateTime',
                      'owners',
                      'submitDate',
                      'testDate',
                      'reviewDateTime'
                    ];
                    const projectExcelData = formatExcelData(projectFilterVal, result.projects);
                    const projectSheet = excel.getWorkSheet({
                      header: projectHeader,
                      data: projectExcelData,
                      wsName: `项目信息`
                    });
                    workSheets.push(projectSheet);
                  }
                  excel.exportMultiSheetExcel(workSheets, `LIMS-工作统计 ${formatDateTime()}`);
                });
              } else {
                ElMessage.warning({
                  message: '暂无可导出的的数据!',
                  type: 'warning'
                });
                return;
              }
            }
          })
          .catch(err => {
            ElMessage.error({
              message: `${err.message}`,
              type: 'error'
            });
          })
          .finally(() => {
            data.downloadLoading = false;
          });
      }
    };

    const formatInspectionType = code => {
      const inspection = data.inspectionTypeOptions.filter(item => item.code === code.toString())[0];
      return inspection?.name;
    };

    function formatExcelData(filterVal, jsonData) {
      return jsonData.map(v =>
        filterVal.map(j => {
          if (j === 'type') {
            return formatInspectionType(v[j]);
          } else {
            return v[j];
          }
        })
      );
    }

    onMounted(() => {
      getVoltageLevel();
      getInspectionTypeList();
    });
    return {
      ...toRefs(data),
      ...toRefs(processData),
      emptyImg,
      paramTable,
      sampleTable,
      itemChartOption,
      ownerChartOption,
      testerChartOption,
      sampleChartOption,
      materialClassifications,
      renderChart,
      handleDatePicker,
      getProcess,
      closeProcess,
      deleteProcess,
      handleProcess,
      deleteMaterialgroup,
      handleMaterialgroup,
      getMaterialgroup,
      closeMaterialgroup,
      getVoltageLevel,
      setMainOffset,
      renderWorkData,
      changeInspectionType,
      renderSampleData,
      cancelRender,
      exportToExcel
    };
  }
};
</script>
<style lang="scss" scoped></style>
