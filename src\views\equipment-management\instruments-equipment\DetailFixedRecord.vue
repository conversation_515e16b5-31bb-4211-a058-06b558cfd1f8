<template>
  <!-- 维修信息列表 -->
  <div v-loading="fixedLoading" class="textLeft">
    <el-button
      v-if="detailData.supportDataAcquisition && getPermissionBtn('addMaintainRecord')"
      class="add-btn"
      size="small"
      icon="el-icon-plus"
      @click="handleAdd"
      @keyup.prevent
      @keydown.enter.prevent
      >新增维修信息</el-button
    >
  </div>
  <el-table
    :data="tableList"
    fit
    border
    highlight-current-row
    size="medium"
    class="detail-table format-height-table dark-table drawer-height-table"
    @header-dragend="drageHeader"
  >
    <el-table-column type="index" label="序号" :width="70" align="center" />
    <el-table-column prop="pointNumber" label="维修人" :width="140">
      <template #default="{ row }">
        <UserTag v-for="item in row.fixedBy.split(',')" :key="item" :name="getNameByid(item) || item || '--'" />
      </template>
    </el-table-column>
    <el-table-column prop="fixedDate" label="维修日期" :width="140">
      <template #default="{ row }">
        <div>{{ formatDate(row.fixedDate) || '--' }}</div>
      </template>
    </el-table-column>
    <el-table-column prop="fixedResult" label="维修结果" :width="140">
      <template #default="{ row }">
        <el-tag size="small" :type="statusType[row.fixedResult]">{{
          dictionaryJSON[24].enable[row.fixedResult]
        }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="isRemeasured" label="维修后需要重新计量" :min-width="200">
      <template #default="{ row }">
        <div>{{ row.isRemeasured ? '是' : '否' }}</div>
      </template>
    </el-table-column>
    <el-table-column prop="description" label="维修描述" :min-width="200" show-overflow-tooltip>
      <template #default="{ row }">
        <div>{{ row.description || '--' }}</div>
      </template>
    </el-table-column>
    <el-table-column
      v-if="
        detailData.supportDataAcquisition &&
        (getPermissionBtn('editMaintainRecord') || getPermissionBtn('deleteMaintainRecord'))
      "
      fixed="right"
      class-name="fixed-right"
      prop="status"
      label="操作"
      width="120"
    >
      <template #default="{ row }">
        <span v-if="getPermissionBtn('editMaintainRecord')" class="blue-color" @click="handleEdit(row)">编辑</span>
        <span v-if="getPermissionBtn('deleteMaintainRecord')" class="blue-color" @click="handleDelete(row)">删除</span>
      </template>
    </el-table-column>
  </el-table>
  <!-- 新增编辑记录弹出窗 -->
  <DialogRecord
    :detail-data="rowDetail"
    :dialog-visible="dialogVisible"
    :dialog-type="dialogType"
    :page-type="'fixed'"
    :status-json="statusJson"
    @closeDialog="closeDialog"
  />
</template>

<script>
import { reactive, toRefs, watch, getCurrentInstance } from 'vue';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { getDetail } from '@/api/equipment';
import DialogRecord from '../DialogRecord.vue';
import UserTag from '@/components/UserTag';
import { deleteRecordApi } from '@/api/maintenanceRecordList';

export default {
  name: 'DetailFixedRecord',
  components: { UserTag, DialogRecord },
  props: {
    list: {
      type: Array,
      default: function () {
        return [];
      }
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    dictionaryAll: {
      type: Object,
      default: function () {
        return {};
      }
    },
    deviceId: {
      type: String,
      default: ''
    }
  },
  emits: ['handleResh'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableList: [],
      fixedLoading: false,
      dialogVisible: false, // 弹窗的隐藏与显示
      dialogType: '', // 弹窗类型
      deviceId: '',
      statusType: {
        Fault: 'danger',
        Running: 'success',
        Scrapped: 'info'
      },
      detailData: {}, // 仪器设备详情
      rowDetail: {}, // 维修弹窗详情
      statusJson: {},
      dictionaryJSON: {
        24: {
          enable: {},
          all: {}
        }
      }
    });
    watch(props, newValue => {
      state.tableList = props.list;
      state.detailData = props.detailData;
      state.dictionaryJSON = props.dictionaryAll;
      for (var key in state.dictionaryJSON[24].enable) {
        if (key !== 'Standby' && key !== 'Maintenance') {
          // 用在维修模块的状态
          state.statusJson[key] = state.dictionaryJSON[24].enable[key];
        }
      }
      state.deviceId = props.deviceId;
    });
    // 查询列表
    const initDetail = deviceId => {
      state.fixedLoading = true;
      getDetail(deviceId).then(res => {
        state.fixedLoading = false;
        if (res) {
          state.tableList = res.data.data.deviceFixedRecordList;
        }
      });
    };
    // 新增
    const handleAdd = () => {
      state.dialogType = 'add';
      state.dialogVisible = true;
      state.rowDetail = JSON.parse(JSON.stringify(state.detailData));
    };
    // 编辑
    const handleEdit = row => {
      state.dialogType = 'edit';
      state.dialogVisible = true;
      state.rowDetail = JSON.parse(JSON.stringify(row));
      state.rowDetail.name = state.detailData.name;
      state.rowDetail.deviceNumber = state.detailData.deviceNumber;
    };
    const closeDialog = val => {
      state.dialogVisible = false;
      if (val.isRefresh) {
        initDetail(state.deviceId);
        context.emit('handleResh');
      }
    };
    // 删除
    const handleDelete = row => {
      proxy
        .$confirm('是否删除该条维修记录？', '删除确认', {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          state.fixedLoading = true;
          deleteRecordApi(row.id).then(res => {
            state.fixedLoading = false;
            if (res) {
              proxy.$message.success(res.data.message);
              initDetail(state.deviceId);
              context.emit('handleResh');
            }
          });
        })
        .catch(() => {});
    };
    return {
      ...toRefs(state),
      handleAdd,
      closeDialog,
      initDetail,
      handleDelete,
      handleEdit,
      getPermissionBtn,
      drageHeader,
      formatDate,
      getNameByid
    };
  }
};
</script>

<style lang="scss" scoped>
.textLeft {
  text-align: left;
}
.el-select {
  width: 100%;
}
.add-btn {
  margin-bottom: 20px;
}
</style>
