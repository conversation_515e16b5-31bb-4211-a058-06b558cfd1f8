/* template */
.cell-wrapper,
.paragraph-wrapper {
  flex-grow: 1;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.paragraph-wrapper {
  padding: 0 10px;
}

.cell-wrapper .text,
.cell-wrapper .input,
.cell-wrapper .input-number,
.cell-wrapper .select-option,
.cell-wrapper .radio-group,
.cell-wrapper .checkbox-group,
.cell-wrapper .date-picker,
.paragraph-wrapper .text,
.paragraph-wrapper .input,
.paragraph-wrapper .input-number {
  outline: 0;
  border: none;
  font-family: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
}

.cell-wrapper .input,
.cell-wrapper .input-number,
.cell-wrapper .select-option,
.cell-wrapper .date-picker {
  text-align: center;
}

/* text */
.cell-wrapper .text {
  word-break: break-all;
}

.cell-wrapper .text.left,
.paragraph-wrapper .text.left {
  text-align: left;
}

.cell-wrapper .text.center,
.paragraph-wrapper .text.center {
  text-align: center;
}

.cell-wrapper .text.right,
.paragraph-wrapper .text.right {
  text-align: right;
}

.cell-wrapper .text.justify,
.paragraph-wrapper .text.justify {
  text-align: justify;
  text-align-last: justify;
}

.cell-wrapper .text.left {
  padding-left: 10px;
}

.cell-wrapper .text.justify {
  padding: 0 10px 0 10px;
}

.cell-wrapper .text.right {
  padding-right: 10px;
}

.cell-wrapper .text.bold,
.paragraph-wrapper .text.bold {
  font-weight: bold;
}

.cell-wrapper .text.italic,
.paragraph-wrapper .text.italic {
  font-style: italic;
}

.cell-wrapper .text.underline,
.paragraph-wrapper .text.underline {
  text-decoration: underline;
}

.cell-wrapper .text.strikethrough,
.paragraph-wrapper .text.strikethrough {
  text-decoration: line-through;
}

/* input & input-number */
.cell-wrapper .input,
.cell-wrapper .input-number {
  resize: none;
  overflow: hidden;
  background-color: var(--input-background-color);
}

.paragraph-wrapper .input,
.paragraph-wrapper .input-number {
  resize: none;
  overflow: hidden;
}

.cell-wrapper .input[readonly],
.cell-wrapper .input-number[readonly] {
  background-color: transparent;
}

.cell-wrapper .input.underline,
.cell-wrapper .input-number.underline,
.paragraph-wrapper .input.underline,
.paragraph-wrapper .input-number.underline {
  text-decoration: underline;
}

/* radio & checkbox */
.cell-wrapper .radio-group,
.cell-wrapper .checkbox-group {
  display: flex;
}

.cell-wrapper .radio-group.horizontal,
.cell-wrapper .checkbox-group.horizontal {
  justify-content: center;
  flex-direction: row;
  flex-wrap: wrap;
}

.cell-wrapper .radio-group.horizontal > .radio-item,
.cell-wrapper .checkbox-group.horizontal > .checkbox-item {
  margin-right: 10px;
}

.cell-wrapper .radio-group.horizontal > .radio-item:last-child,
.cell-wrapper .checkbox-group.horizontal > .checkbox-item:last-child {
  margin-right: 0;
}

.cell-wrapper .radio-group.vertical,
.cell-wrapper .checkbox-group.vertical {
  flex-direction: column;
  padding-left: 10px;
}

.cell-wrapper .radio-group input[type='radio'],
.cell-wrapper .checkbox-group input.checkbox {
  margin-right: 4px;
  vertical-align: -2px;
}

.cell-wrapper .radio-group label,
.cell-wrapper .checkbox-group label {
  font-weight: 500;
  cursor: pointer;
}

/* select */
.cell-wrapper select.select-option,
.cell-wrapper select.select-option > option,
.cell-wrapper select.select-option[disabled],
.cell-wrapper select.select-option[disabled] > option {
  color: var(--text-color);
  opacity: 1;
}

/* date-picker */
.cell-wrapper .date-picker {
  position: relative;
}

.cell-wrapper .date-picker-label {
  word-break: break-all;
  cursor: pointer;
}

.cell-wrapper .date-picker-input {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  border: none;
  outline: none;
  color: transparent;
  background-color: transparent;
}

.cell-wrapper input.date-picker-input::-webkit-calendar-picker-indicator {
  position: absolute;
  left: -50%;
  top: 0;
  z-index: 3;
  margin: 0;
  padding: 0;
  width: 1000%;
  height: 100%;
  background-color: transparent;
  background-position: right;
  color: transparent;
  cursor: pointer;
}

/* mathjax */
.cell-wrapper .mathjax {
  padding-left: 10px;
}
