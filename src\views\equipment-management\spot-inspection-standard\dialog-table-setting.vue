<template>
  <!-- 点检明细字段维护 -->
  <el-dialog
    v-model="dialogVisiable"
    title="点检明细字段维护"
    :close-on-click-modal="false"
    :width="1200"
    @close="handleClose"
  >
    <div v-if="dialogVisiable" v-loading="dialogLoading">
      <el-form ref="formRef" :model="formData" label-position="left" size="small">
        <div class="text-right btnGroup">
          <el-button
            v-if="!isEdit && oldTableData.length && !isAdd"
            type="primary"
            size="small"
            icon="el-icon-edit"
            @click="isEdit = true"
            >编辑</el-button
          >
          <el-button v-if="isEdit" size="small" icon="el-icon-cancle" @click="cancleEdit()">取消编辑</el-button>
          <el-button v-if="!isEdit" type="primary" icon="el-icon-plus" size="small" @click="handleAdd()"
            >新增</el-button
          >
        </div>
        <el-table
          v-if="isShowTable"
          id="sortableList"
          :data="formData.tableData"
          class="dark-table base-table format-height-table2"
          fit
          border
          size="medium"
          height="auto"
          style="margin-top: 5px"
          @header-dragend="drageHeader"
        >
          <el-table-column label="排序" prop="deviceNumber" :width="colWidth.serialNo">
            <i class="tes-move iconfont" style="font-size: 12px; cursor: move" />
          </el-table-column>
          <el-table-column label="序号" type="index" :width="colWidth.serialNo" />
          <el-table-column label="字段名称" prop="fieldName" :min-width="colWidth.projectName" show-overflow-tooltip>
            <template #default="{ row, $index }">
              <el-form-item
                v-if="isEdit || !row.id"
                :prop="`tableData.${$index}.fieldName`"
                :rules="{ required: true, message: '请输入字段名称', tigger: 'change' }"
              >
                <el-input v-model="row.fieldName" maxlength="30" placeholder="请输入字段名称" />
              </el-form-item>
              <span v-else>{{ row.fieldName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="属性格式" prop="fieldType" :min-width="colWidth.typeGroup" show-overflow-tooltip>
            <template #default="{ row, $index }">
              <el-form-item
                v-if="!row.id"
                :prop="`tableData.${$index}.fieldType`"
                :rules="{ required: !Boolean(row.dictionaryCode), message: '请选择属性格式', tigger: 'change' }"
              >
                <el-select
                  v-model="row.fieldType"
                  placeholder="请选择属性格式"
                  clearable
                  :disabled="Boolean(row.dictionaryCode)"
                  style="width: 100%"
                  @change="
                    val => {
                      return handleChangeFieldType(val, $index);
                    }
                  "
                >
                  <el-option label="文本" value="text" />
                  <el-option label="数字" value="number" />
                  <el-option label="日期" value="date" />
                </el-select>
              </el-form-item>
              <span v-else>{{ typeJSON[row.fieldType] || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="关联字典" prop="dictionaryCode" :min-width="colWidth.materialGroup">
            <template #default="{ row, $index }">
              <el-form-item
                v-if="isEdit || !row.id"
                :prop="`tableData.${$index}.dictionaryCode`"
                :rules="{ required: !Boolean(row.fieldType), message: '请选择关联字典', tigger: 'change' }"
              >
                <el-select
                  v-model="row.dictionaryCode"
                  placeholder="请选择关联字典"
                  clearable
                  :disabled="Boolean(row.fieldType)"
                  style="width: 100%"
                  @change="
                    val => {
                      return handleChangeDictionary(val, $index);
                    }
                  "
                >
                  <el-option v-for="(val, key) in dictionaryList" :key="key" :label="val.name" :value="key">
                    <div class="selectOption">
                      <div class="optionName">{{ val.name }}</div>
                      <div v-if="val.description" class="optionRemark">（{{ val.description }}）</div>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
              <span v-else>{{ row.dictionaryName || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="status" :width="colWidth.unit">
            <template #default="{ row, $index }">
              <el-form-item v-if="isEdit || !row.id" :prop="`tableData.${$index}.status`">
                <el-select v-model="row.status" placeholder="请选择状态" style="width: 100%">
                  <el-option label="启用" :value="1" />
                  <el-option label="停用" :value="0" />
                </el-select>
              </el-form-item>
              <el-tag v-else :type="statusJson[row.status].type" effect="dark" size="small">{{
                statusJson[row.status].label
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column v-if="isAdd" label="操作" prop="measurementCycle" :width="colWidth.cycle">
            <template #default="{ row, $index }">
              <span v-if="!row.id" class="blue-color" @click="handleDelete($index)">删除</span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
// getCurrentInstance
import { reactive, ref, toRefs, watch, getCurrentInstance, nextTick } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import Sortable from 'sortablejs';
import { pointInspectionFieldListAll, pointInspectionFieldSaveBatch } from '@/api/spotInspectionStandard';
export default {
  name: 'DialogTableHeader',
  components: {},
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    dictionaryAssemble: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      condition: '',
      formData: {
        tableData: []
      },
      oldTableData: [],
      formRef: {},
      statusJson: {
        1: { type: 'primary', label: '启用' },
        0: { type: 'info', label: '停用' }
      },
      typeJSON: {
        text: '文本',
        number: '数字',
        date: '日期'
      },
      dialogLoading: false,
      isShowTable: true,
      dictionaryList: {},
      isEdit: false,
      isAdd: false,
      dialogVisiable: false,
      ruleForm: ref(),
      tableRef: ref(null)
    });
    watch(props, newValue => {
      state.dialogVisiable = newValue.dialogShow;
      if (state.dialogVisiable) {
        state.isEdit = false;
        state.isAdd = false;
        state.dictionaryList = props.dictionaryAssemble;
        getTableList();
      }
    });
    const handleAdd = () => {
      state.formData.tableData.push({
        order: state.formData.tableData.length,
        status: 1
      });
      state.isAdd = true;
    };
    const getTableList = () => {
      state.dialogLoading = true;
      pointInspectionFieldListAll().then(res => {
        state.dialogLoading = false;
        if (res) {
          if (res.data.data.length) {
            state.formData.tableData = res.data.data;
            state.oldTableData = JSON.parse(JSON.stringify(res.data.data));
            nextTick(() => {
              rowDrop();
            });
          } else {
            handleAdd();
            state.oldTableData = [];
          }
        }
      });
    };
    const onSubmit = () => {
      state.formRef
        .validate()
        .then(valid => {
          if (valid) {
            pointInspectionFieldSaveBatch(state.formData.tableData).then(res => {
              if (res) {
                proxy.$message.success('保存成功！');
                handleClose();
              }
            });
          } else {
            return false;
          }
        })
        .catch(error => {
          const fieldName = Object.keys(error)[0];
          proxy.$message.warning(error[fieldName][0].message);
          return false;
        });
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog');
    };
    // 判断是否可以勾选
    const rowSelectable = (row, index) => {
      if (
        state.alreadyArray.some(item => {
          return item === row.deviceId;
        })
      ) {
        return false;
      } else {
        return true;
      }
    };
    const rowDrop = () => {
      // 获取当前表格
      const el = document.getElementById('sortableList').querySelector('.el-table__body-wrapper tbody');
      Sortable.create(el, {
        animation: 300,
        handle: '.tes-move',
        draggable: '.el-table__row',
        ghostClass: 'ghost',
        // 拖动对象移动样式
        dragClass: 'drag',
        forceFallback: true,
        onEnd: function (evt) {
          if (evt.oldIndex !== evt.newIndex) {
            const currRow = state.formData.tableData.splice(evt.oldIndex, 1)[0];
            state.formData.tableData.splice(evt.newIndex, 0, currRow);
            state.formData.tableData.forEach((value, index) => {
              value.order = index;
            });
            showTable();
          }
        }
      });
    };
    const showTable = () => {
      state.isShowTable = false;
      setTimeout(() => {
        state.isShowTable = true;
        nextTick(() => {
          rowDrop();
        });
      }, 0);
    };
    // 删除
    const handleDelete = index => {
      state.formData.tableData.splice(index, 1);
      if (state.formData.tableData.length === state.oldTableData.length) {
        state.isAdd = false;
      }
    };
    const selectChange = (selection, row) => {
      if (row && row.deviceId) {
        row.selected = !row.selected;
      }
    };
    // 取消编辑
    const cancleEdit = () => {
      state.isEdit = false;
      state.formData.tableData = JSON.parse(JSON.stringify(state.oldTableData));
    };
    // 切换关联的字典
    const handleChangeDictionary = (val, index) => {
      if (val) {
        state.formData.tableData[index].dictionaryName = state.dictionaryList[val].name;
        state.formData.tableData[index].fieldType = '';
      }
    };
    // 修改文本类型
    const handleChangeFieldType = (val, index) => {
      if (val) {
        state.formData.tableData[index].dictionaryName = '';
        state.formData.tableData[index].dictionaryCode = '';
      }
    };
    return {
      ...toRefs(state),
      rowDrop,
      handleChangeDictionary,
      handleChangeFieldType,
      handleDelete,
      cancleEdit,
      handleAdd,
      onSubmit,
      rowSelectable,
      handleClose,
      getTableList,
      formatDate,
      colWidth,
      drageHeader,
      selectChange
    };
  }
};
</script>
<style lang="scss" scoped>
.btnGroup {
  margin-bottom: 10px;
}
.selectOption {
  display: flex;
  .optionName {
    flex: 1;
  }
  .optionRemark {
    font-size: 12px;
    color: $tes-font3;
  }
}
:deep(.el-form .el-form-item) {
  margin-bottom: 0;
}
::v-deep(.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 37.5rem) !important;
    overflow-y: auto;
  }
}
</style>
