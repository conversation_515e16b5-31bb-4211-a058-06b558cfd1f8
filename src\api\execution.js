const Headers = {
  'Content-Type': 'application/json; charset=utf-8'
};
// 修改检测项目模板信息
import request from '@/utils/request';

// 查询列表检测项目
export function getTableList(data) {
  return request({
    url: '/api-orders/orders/experiment/experimentdata/capabilitylist',
    method: 'post',
    data
  });
}
// 查询样品列表
export function getsampleList(data) {
  return request({
    url: '/api-orders/orders/experiment/experimentdata/samplelist',
    method: 'post',
    data
  });
}

//  查询左侧列表检测项目
export function capabilityBysamplesId(samplesId) {
  return request({
    url: '/api-orders/orders/experiment/experimentdata/capability/' + samplesId,
    method: 'get'
  });
}

// 左侧列表原始记录模板下载
export function TemplateIdByexperimentId(experimentId) {
  return request({
    url: '/api-orders/orders/experiment/template/downloadByExperimentId/' + experimentId,
    method: 'get'
  });
}

// 查询原始记录模板的试验值
export function experimentmongodatainfo(experimentId) {
  return request({
    url: '/api-orders/orders/experiment/experimentmongodata/info/' + experimentId,
    method: 'get'
  });
}

// 检测执行添加原始记录模板
export function downloadByCapabilityId(data) {
  return request({
    url: '/api-orders/orders/experiment/template/downloadByCapabilityId',
    method: 'post',
    data
  });
}
// 查询单个检测项目
export function experiment(experimentId) {
  return request({
    url: '/api-orders/orders/experiment/experimentdata/experiment/' + experimentId,
    method: 'get'
  });
}

// 保存原始记录的值
export function saveExcelInfo(data) {
  return request({
    url: '/api-orders/orders/experiment/experimentmongodata/save',
    method: 'post',
    data
  });
}

// 保存提交原始记录的值
export function saveAndSubmit(data) {
  return request({
    url: '/api-orders/orders/experiment/experimentmongodata/saveAndSubmit',
    method: 'post',
    data
  });
}

// 检测项目退回
export function back(data) {
  return request({
    url: '/api-orders/orders/experiment/experimentdata/back',
    method: 'post',
    data
  });
}
// 检测项目复测
export function retest(data) {
  return request({
    url: '/api-orders/orders/experiment/experimentdata/retest',
    method: 'post',
    data
  });
}

// 检测结果审核
export function review(data) {
  return request({
    url: '/api-orders/orders/experiment/review/review',
    method: 'post',
    data
  });
}

// 获取仪器设备编号
export function equipmentList(data) {
  return request({
    url: 'http://*************:9100/device/Device',
    method: 'get',
    headers: Headers,
    data
  });
}

// // 获取仪器设备计量单位
// export function getDeviceDetail(data) {
//   return request({
//     url: 'http://*************:9100/device/DeviceMeasurement',
//     method: 'get',
//     headers: Headers,
//     data
//   })
// }

// 添加仪器设备
export function addDevice(data) {
  return request({
    url: '/api-orders/orders/experiment/deviceusage/save',
    method: 'post',
    data
  });
}

/**
 * 保存或更新线芯标识信息
 * 请求类型 application/json
 * @param {{
 * 	 "sampleId": "", "coreColourList": [{ "coreColour": "" }],
 * }} data
 * @returns
 */
export function experimentcorerecordsave(data) {
  return request({
    url: '/api-orders/orders/experiment/corerecord/saveOrUpdate',
    method: 'post',
    data
  });
}

/**
 * 保存或更新线芯标识信息
 * 请求类型 application/json
 * @param {{
 * 	 "sampleId": "", "coreColourList": [{ "coreColour": "" }], "capabilityId": ""
 * }} data
 * @returns
 */
export function saveCapabilitySampleGroupInfo(data) {
  return request({
    url: '/api-orders/ordres/experimentcapabilitycorerecord/saveOrUpdate',
    method: 'post',
    data
  });
}

// 根据主键Id查询线芯标识信息
export function experimentcorerecordInfo(id) {
  return request({
    url: '/api-orders/orders/experiment/corerecord/info/' + id,
    method: 'get'
  });
}

// 根据检测执行检测项Id查询试验颜色是否存在
export function getInfoByExperimentId(data) {
  return request({
    url: '/api-orders/orders/experiment/corerecord/getInfoBySampleId',
    method: 'post',
    data
  });
}

/**
 * 根据样品Id查询试样分组信息
 * @param  sampleId
 * @returns
 */
export function getInfoBySampleId(sampleId) {
  return request({
    url: `/api-orders/orders/experiment/corerecord/findListBySampleId/${sampleId}`,
    method: 'get'
  });
}

/**
 * 根据样品Id和检测项目Id查询试样分组信息
 * @param {{
 * 	 "sampleId": "", "capabilityId": "",
 * }} data
 * @returns
 */
export function getInfoBySampleCapabilityId(data) {
  return request({
    url: '/api-orders/ordres/experimentcapabilitycorerecord/findList',
    method: 'post',
    data
  });
}

// 模板重置列表
export function resetList(data) {
  return request({
    url: '/api-orders/orders/experiment/template/resetList',
    method: 'post',
    data
  });
}
// 模板重置后给新的原始记录模板赋值
export function resetSave(data) {
  return request({
    url: '/api-orders/orders/experiment/template/resetSave',
    method: 'post',
    data
  });
}
// 收藏项目
export function favoriteItem(experimentId) {
  return request({
    url: '/api-orders/orders/experiment/experimentdata/favorite/' + experimentId,
    method: 'get'
  });
}
// 取消收藏
export function unFavoriteItem(experimentId) {
  return request({
    url: '/api-orders/orders/experiment/experimentdata/unFavorite/' + experimentId,
    method: 'get'
  });
}
// 获取待办详情
export function getRemindInfo(data) {
  return request({
    url: '/api-message/message/remind-msg/info',
    method: 'post',
    data
  });
}
// 提交待办saveRemindInfo
export function saveRemindInfo(data) {
  return request({
    url: '/api-message/message/remind-msg/save',
    method: 'post',
    data
  });
}
// 获取项目待办状态
export function getItemRemindType(data) {
  return request({
    url: '/api-message/message/remind-msg/check',
    method: 'post',
    data
  });
}
// 删除项目提醒
export function deleteRemindApi(data) {
  return request({
    url: '/api-message/message/remind-msg/delete',
    method: 'post',
    data
  });
}
// 操作记录
export function getOperationLogs(experimentId) {
  return request({
    url: `/api-orders/orders/experiment/experimentlog/findByExperimentId/${experimentId}`,
    method: 'get'
  });
}

//  查询左侧列表当前项目下的样品
export function sampleByCapabilityId(data) {
  return request({
    url: '/api-orders/orders/experiment/experimentdata/samples',
    method: 'post',
    data
  });
}
// 样品首页打印
export function sampleHomePrint(data) {
  return request({
    url: `/api-orders/orders/experiment/experimentdata/sampleHomePrint/${data.secSampleNum}/${data.mateType}`,
    responseType: 'blob',
    method: 'post'
  });
}

// 获取样品信息补充列表页
export function sampleInfoExtend(sampleId) {
  return request({
    url: `/api-orders/orders/samples/infoExtend?sampleId=${sampleId}`,
    method: 'get'
  });
}

// 保存样品信息补充
export function saveInfoExtend(data) {
  const paramsInfo = data.params;
  return request({
    url: `/api-orders/orders/samples/infoExtend/${data.sampleId}`,
    method: 'post',
    headers: { 'Content-Type': 'application/json; charset=utf-8' },
    data: paramsInfo
  });
}

// 保存样品信息补充
export function saveOrUpdateExperimentProdType(data) {
  return request({
    url: `/api-orders/orders/experiment/experimentdata/saveOrUpdateExperimentProdType`,
    method: 'post',
    data
  });
}

// 保存样品信息补充
export function findDeviceByCapabilityId(capabilityId) {
  return request({
    url: `/api-capabilitystd/capabilitydevice/findDeviceByCapabilityId/${capabilityId}`,
    method: 'get'
  });
}

// 查找未停用的样品补充(小样)字段配置信息
export function findUseSampleSupplementaryConfig(materialCategoryCode, configType) {
  return request({
    url: `/api-material/material/samplesupplementaryconfig/findUseSampleSupplementaryConfig/${materialCategoryCode}/${configType}`,
    method: 'get'
  });
}
// 保存样品补充(小样)信息
export function saveOrUpdate(data) {
  return request({
    url: `/api-orders/orders/samplesupplementary/saveOrUpdate`,
    method: 'post',
    data
  });
}
// 根据Id查询样品补充(小样)信息
export function findBySampleId(sampleId, configType) {
  return request({
    url: `/api-orders/orders/samplesupplementary/findBySampleId/${sampleId}/${configType}`,
    method: 'get'
  });
}

// 检测执行试验要求更新
export function saveExperimentRequirement(data) {
  return request({
    url: '/api-orders/orders/experiment/experimentdata/updateExperiment',
    method: 'post',
    data
  });
}
