import { getKeyParamData } from '@/api/order';

export const getKeyParamResult = formData => {
  const keyParamPostBody = {
    capabilityId: formData.capabilityId,
    capabilityParaId: formData.capabilityParamId,
    capabilityParaName: formData.capabilityParamName,
    startDate: formData.startTime,
    endDate: formData.endTime,
    mateType: formData.materialClassification,
    materialGroupNo: formData.materialGroupNo,
    productionProcedureNo: formData.workingProcedureCode,
    templateKey: formData.templateKey,
    type: formData.inspectionType,
    voltName: formData.voltageLevel
  };
  const keyParamValueArray = [];
  getKeyParamData(keyParamPostBody).then(res => {
    if (res && res.data.code === 200 && res.data.data.length > 0) {
      res.data.data.forEach(item => {
        keyParamValueArray.push(Number(item.capabilityParaValue));
      });
    }
  });

  return keyParamValueArray;
};
