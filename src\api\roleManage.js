import request from '@/utils/request';

// 角色树
export function getRoleTree(data) {
  return request({
    url: `/api-user/user/sysrole/list`,
    method: 'post',
    data
  });
}
// 保存角色分类
export function saveRoleTree(data) {
  return request({
    url: '/api-user/user/sysrole/save',
    method: 'post',
    data
  });
}
// 编辑角色分类
export function updateRoleTree(data) {
  return request({
    url: '/api-user/user/sysrole/update',
    method: 'post',
    data
  });
}
// 权限树
export function getPremissionList(data) {
  return request({
    url: '/api-user/user/sysrolepermission/listTree',
    method: 'post',
    data
  });
}
// 保存角色绑定的权限
export function savePermission(data) {
  return request({
    url: '/api-user/user/sysrolepermission/save',
    method: 'post',
    data
  });
}
// 角色绑定的成员列表
export function getMemberList(data) {
  return request({
    url: '/api-user/user/sysroleemployee/list',
    method: 'post',
    data
  });
}
// 角色绑定的成员列表
export function getAllMemberList(data) {
  return request({
    url: '/api-user/user/sysroleemployee/listwhenadd',
    method: 'post',
    data
  });
}
// 角色添加成员
export function addRoleMember(data) {
  return request({
    url: '/api-user/user/sysroleemployee/save',
    method: 'post',
    data
  });
}
// 角色删除成员
export function deleteRoleMember(data) {
  return request({
    url: '/api-user/user/sysroleemployee/delete',
    method: 'post',
    data
  });
}
// 删除角色
export function deleteRoleTree(data) {
  return request({
    url: '/api-user/user/sysrole/delete',
    method: 'post',
    data
  });
}
