<template>
  <!-- 设备附件 -->
  <div v-loading="detailLoading" class="textLeft">
    <el-upload
      ref="uploadRef"
      :action="uploadAction"
      :headers="headerconfig"
      multiple
      :auto-upload="true"
      :show-file-list="false"
      :before-upload="beforeUpload"
      :on-success="handleFileSuccess"
    >
      <el-button
        v-if="getPermissionBtn('AddInstrumentsFile')"
        class="add-btn"
        type="primary"
        size="small"
        icon="el-icon-upload"
        @click="handleAdd"
        @keyup.prevent
        @keydown.enter.prevent
        >上传</el-button
      >
    </el-upload>
  </div>
  <el-table
    v-loading="detailLoading"
    :data="tableList"
    fit
    border
    highlight-current-row
    size="medium"
    class="detail-table dark-table drawer-height-table"
    @header-dragend="drageHeader"
  >
    <el-table-column type="index" label="序号" :width="70" align="center" />
    <el-table-column prop="fileName" label="附件名称" :min-width="200" show-overflow-tooltip>
      <template #default="{ row }">
        <span v-if="isPreview(row.fileName)" class="blue-color" @click="handlePreivew(row)">{{
          row.fileName || '--'
        }}</span>
        <span v-else class="blue-color" @click="handleDown(row)">{{ row.fileName || '--' }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="fileName" label="类型" :min-width="150" show-overflow-tooltip>
      <template #default="{ row }">
        <el-form :model="row" style="margin: 0px">
          <el-form-item prop="refType" style="margin: 0px">
            <el-select
              v-model="row.refType"
              placeholder="请选择附件类型"
              size="small"
              :disabled="!getPermissionBtn('EditInstrumentsFile')"
              @change="
                val => {
                  return handleChangeRefType(val, row);
                }
              "
            >
              <el-option-group v-for="item in refTypeArray" :key="item.label" :label="item.label">
                <el-option
                  v-for="val in item.group"
                  :key="val.id"
                  :label="val.name"
                  :value="val.code"
                  :disabled="val.status !== 1"
                >
                  <span style="float: left">{{ val.name }}</span>
                  <span v-if="val.status !== 1" class="fr" style="color: red">已停用</span>
                </el-option>
              </el-option-group>
            </el-select>
          </el-form-item>
        </el-form>
      </template>
    </el-table-column>
    <el-table-column prop="createTime" label="上传日期" :min-width="140">
      <template #default="{ row }">
        <div>{{ row.createTime || '--' }}</div>
      </template>
    </el-table-column>
    <el-table-column prop="createBy" label="上传人" :min-width="140">
      <template #default="{ row }">
        <UserTag :name="getNameByid(row.createBy) || row.createBy" />
      </template>
    </el-table-column>
    <el-table-column
      v-if="getPermissionBtn('DeleteInstrumentsFile')"
      fixed="right"
      class-name="fixed-right"
      prop="status"
      label="操作"
      :min-width="120"
    >
      <template #default="{ row }">
        <span class="blue-color" @click="handleDelete(row)">删除</span>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { reactive, toRefs, watch, getCurrentInstance } from 'vue';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
// import { findAttachment } from '@/api/equipment';
import { deviceFileUpload } from '@/api/uploadAction';
import UserTag from '@/components/UserTag';
import {
  attachmentDelete,
  saveDeviceFile,
  findAttachment,
  updateAttachment,
  downloadById
} from '@/api/maintenanceRecordList';
import { getToken } from '@/utils/auth';
import { getDictionary } from '@/api/user';

export default {
  name: 'DetailFile',
  components: { UserTag },
  props: {
    deviceId: {
      type: String,
      default: ''
    }
  },
  emits: ['close'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableList: [],
      detailLoading: false,
      uploadCount: 0, // 上传的文件数
      uploadAction: deviceFileUpload(),
      refTypeArray: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ], // 单位
      headerconfig: {
        Authorization: getToken()
      },
      detailData: {} // 仪器设备详情
    });
    watch(props, newValue => {});
    // 查询列表
    const initList = () => {
      state.detailLoading = true;
      findAttachment(props.deviceId).then(res => {
        state.uploadCount = 0;
        state.detailLoading = false;
        if (res) {
          state.tableList = res.data.data;
        }
      });
    };
    initList();
    const initDictionary = () => {
      getDictionary('SBFJLX').then(res => {
        if (res) {
          state.refTypeArray[0].group = [];
          state.refTypeArray[1].group = [];
          res.data.data.dictionaryoption.forEach(item => {
            if (item.status === 1) {
              state.refTypeArray[0].group.push(item);
            } else {
              state.refTypeArray[1].group.push(item);
            }
          });
        }
      });
    };
    initDictionary();
    // 新增
    const handleAdd = () => {};
    // 上传文件的限制
    const beforeUpload = file => {
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过10M');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };
    // 上传成功的钩子
    const handleFileSuccess = (res, file, fileList) => {
      state.uploadCount += 1;
      if (res.code === 200) {
        const params = {
          deviceId: props.deviceId
        };
        if (state.uploadCount == 1) {
          params.fileIdList = [];
        }
        params.fileIdList.push(res.data.fileId);
        if ((state.uploadCount = fileList.length)) {
          saveFile(params);
        }
      } else {
        proxy.$message.error(res.message);
      }
    };
    const saveFile = async params => {
      state.detailLoading = true;
      const response = await saveDeviceFile(params).finally((state.detailLoading = false));
      if (response) {
        proxy.$message.success('上传成功！');
        initList();
      }
    };
    // 删除
    const handleDelete = row => {
      proxy
        .$confirm('是否删除该附件？', '删除确认', {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(async () => {
          state.detailLoading = true;
          const response = await attachmentDelete(row.fileId).finally((state.detailLoading = false));
          if (response) {
            proxy.$message.success('删除成功！');
            initList();
            context.emit('close');
          }
        })
        .catch(() => {});
    };
    const handleDown = async row => {
      state.detailLoading = true;
      const response = await downloadById(row.fileId).finally((state.detailLoading = false));
      if (response) {
        const blob = new Blob([response.data], { type: '' });
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = `${row.fileName}`;
        a.href = blobUrl;
        a.click();
        proxy.$message.success('下载附件成功');
      }
    };
    // 更新附件类型
    const handleChangeRefType = async (val, row) => {
      state.detailLoading = true;
      const response = await updateAttachment({ refType: val, id: row.fileId }).finally((state.detailLoading = false));
      if (response) {
        proxy.$message.success('更新成功！');
        initList();
      }
    };
    /** 预览pdf */
    const handlePreivew = row => {
      window.open(row.fileUrl);
    };
    /** 是否在线预览 */
    const isPreview = fileName => {
      return (
        fileName?.indexOf('.png') != -1 ||
        fileName?.indexOf('.jpg') != -1 ||
        fileName?.indexOf('.jpeg') != -1 ||
        fileName?.indexOf('.pdf') != -1
      );
    };
    return {
      ...toRefs(state),
      handleAdd,
      isPreview,
      handlePreivew,
      handleChangeRefType,
      handleDown,
      beforeUpload,
      handleFileSuccess,
      initList,
      handleDelete,
      getPermissionBtn,
      drageHeader,
      formatDate,
      getNameByid
    };
  }
};
</script>

<style lang="scss" scoped>
.textLeft {
  text-align: left;
}
.el-select {
  width: 100%;
}
.add-btn {
  margin-bottom: 20px;
}
</style>
