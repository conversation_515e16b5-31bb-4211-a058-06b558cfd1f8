<template>
  <el-drawer
    v-model="showDrawer"
    :title="titles"
    direction="rtl"
    :before-close="handleClose"
    :size="750"
    destroy-on-close
    :close-on-click-modal="false"
    custom-class="add-item"
  >
    <DrawerLayout :has-button-group="false" :has-page-header="false">
      <el-form ref="form" :model="formInline" label-width="110px" label-position="top" class="form-height-auto">
        <el-row :gutter="40">
          <el-col :span="4">
            <el-form-item label="状态:">
              <el-switch
                v-model="otherDatas.status"
                class="inner-switch"
                :active-text="otherDatas.status ? '启用' : '停用'"
                >启用</el-switch
              >
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否是特殊项目：">
              <el-radio-group v-model="formInline.type" size="small" class="item-radio">
                <el-radio :label="1" border>是</el-radio>
                <el-radio :label="0" border>否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="所属分类："
              prop="categoryid"
              :rules="[{ required: true, message: '请选择所属分类', trigger: 'change' }]"
            >
              <el-cascader
                v-model="otherDatas.category"
                :options="otherDatas.newTree"
                :props="otherDatas.categoryProps"
                style="width: 100%"
                @change="changeCategory"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="项目编号："
              prop="number"
              :rules="[{ required: true, message: '请输入项目编号', trigger: 'blur' }]"
            >
              <el-input
                v-model="formInline.number"
                placeholder="请输入项目编号"
                :input="(formInline.number = formInline.number.replace(/\s+/g, ''))"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="项目名称："
              prop="name"
              :rules="[{ required: true, message: '请输入项目名称', trigger: 'blur' }]"
            >
              <el-input
                v-model="formInline.name"
                placeholder="请输入项目名称"
                maxlength="30"
                :input="(formInline.name = formInline.name.replace(/\s+/g, ''))"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="英文名：">
              <el-input v-model="formInline.name1" placeholder="" class="" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="基准价(¥)：">
              <el-input v-model="formInline.price" placeholder="请输入价格(元)" @input="clearNoNum(formInline.price)" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="基础工分：" style="margin: 0" />
            <el-row :gutter="20">
              <el-col v-for="(val, key) in otherDatas.dictionaryJSON['JCGF'].enable" :key="key" :span="8">
                <el-form-item :label="`${val}：`">
                  <el-input-number
                    v-model="formInline[key]"
                    controls-position="right"
                    :min="0"
                    :placeholder="`请输入${val}工分`"
                    class=""
                    @blur="
                      key => {
                        return handleBlur(key);
                      }
                    "
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目等级：">
              <el-checkbox-group v-model="otherDatas.testcapability" @change="changeTestcapability">
                <el-checkbox v-for="(val, key) in otherDatas.dictionaryJSON['XMDJ'].enable" :key="key" :label="key">{{
                  val
                }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="盖章范围：" @change="changeSealrange">
              <el-checkbox-group v-model="otherDatas.sealrange">
                <el-checkbox v-for="(val, key) in otherDatas.dictionaryJSON['GZFW'].enable" :key="key" :label="key">{{
                  val
                }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="项目描述：">
              <el-input
                v-model="formInline.remark"
                type="textarea"
                :rows="5"
                placeholder="请输入内容"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="drawer-footer">
        <el-button type="primary" @click="onSubmit" @keyup.prevent @keydown.enter.prevent>确认</el-button>
        <el-button @click="handleClose">取消</el-button>
      </div>
    </DrawerLayout>
  </el-drawer>
</template>

<script>
import { ref, watch, reactive, getCurrentInstance } from 'vue';
import { saveCapability, updateCapability } from '@/api/user';
import { ElMessage, ElMessageBox } from 'element-plus';
import DrawerLayout from '@/components/DrawerLayout';
import { twoDecimalPlaces } from '@/utils/validate';

export default {
  name: 'ADD',
  components: { DrawerLayout },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      required: true
    },
    editData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    dictionary: {
      type: Object,
      default: function () {
        return {};
      }
    },
    categoryIds: {
      type: Object,
      default: function () {
        return {};
      }
    },
    tree: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    const { appContext } = getCurrentInstance();
    const bus = appContext.config.globalProperties.bus;
    // console.log(appContext)
    // console.log(bus)
    // 抽屉事件
    const showDrawer = ref(props.drawer);
    const titles = ref(props.title);
    const form = ref(null);
    // 关闭抽屉
    const handleClose = () => {
      const oldData = otherDatas.oldFormInline;
      if (
        formInline.categoryid !== oldData.categoryid ||
        formInline.description !== oldData.description ||
        formInline.name !== oldData.name ||
        formInline.name1 !== oldData.name1 ||
        formInline.number !== oldData.number ||
        formInline.price !== oldData.price ||
        formInline.remark !== oldData.remark ||
        formInline.sealrange !== oldData.sealrange ||
        formInline.status !== oldData.status ||
        formInline.testcapability !== oldData.testcapability ||
        formInline.workhours !== oldData.workhours ||
        formInline.type !== oldData.type ||
        formInline.workPoints !== oldData.workPoints
      ) {
        ElMessageBox({
          title: '提示',
          message: '当前页面数据未保存，是否确认离开？',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'info'
        })
          .then(() => {
            showDrawer.value = false;
            context.emit('close', false);
            return true;
          })
          .catch(() => {
            return false;
          });
      } else {
        showDrawer.value = false;
        context.emit('close', false);
      }
    };
    // formInline
    const formInline = reactive({
      status: 0,
      categoryid: '',
      type: 0,
      number: '',
      name: '',
      workPoints: 0,
      name1: '',
      workhours: '',
      price: null,
      testcapability: '',
      sealrange: '',
      method: '',
      description: '',
      remark: ''
    });
    const otherDatas = reactive({
      oldFormInline: {},
      categoryOptions: [],
      newTree: [],
      categoryProps: {
        children: 'children',
        label: 'name',
        value: 'id',
        checkStrictly: true
      },
      status: true,
      sealrange: [],
      method: '',
      category: [],
      testcapability: [],
      dictionaryJSON: {
        // 字典集合
        JCGF: {
          enable: {},
          all: {}
        },
        XMDJ: {
          enable: {},
          all: {}
        },
        GZFW: {
          enable: {},
          all: {}
        }
      },
      // testcapabilitys: {},
      // sealScopeOf: {},
      workhoursOptions: [
        { label: '小于1小时' },
        { label: '1~2小时' },
        { label: '2~4小时' },
        { label: '4~6小时' },
        { label: '6~8小时' },
        { label: '大于8小时' }
      ],
      input1: ''
    });

    watch(props, newValue => {
      showDrawer.value = newValue.drawer;
      if (showDrawer.value) {
        otherDatas.dictionaryJSON = props.dictionary || {
          // 字典集合
          JCGF: {
            enable: {},
            all: {}
          },
          XMDJ: {
            enable: {},
            all: {}
          },
          GZFW: {
            enable: {},
            all: {}
          }
        };
        // otherDatas.testcapabilitys = props.itemLevel;
        // otherDatas.sealScopeOf = props.sealScopeOf;
      }
      titles.value = props.title;
      otherDatas.newTree = props.tree.slice(1);
      if (props.title !== '新增项目') {
        otherDatas.status = newValue.editData.status === 1;
        otherDatas.category = newValue.editData.categoryIds;
        formInline.number = newValue.editData.number;
        formInline.name = newValue.editData.name;
        formInline.name1 = newValue.editData.name1;
        formInline.workhours = newValue.editData.workhours;
        formInline.workPoints = newValue.editData.workPoints;
        formInline.price = newValue.editData.price;
        for (const key in otherDatas.dictionaryJSON['JCGF'].enable) {
          formInline[key] = newValue.editData[key];
        }
        formInline.categoryid = newValue.editData.categoryid;
        formInline.remark = newValue.editData.remark;
        formInline.id = newValue.editData.id;
        formInline.type = newValue.editData.type;
        if (newValue.editData.testcapability) {
          otherDatas.testcapability = JSON.parse(newValue.editData.testcapability);
          otherDatas.testcapability = otherDatas.testcapability.filter(i => i !== null);
        }
        if (newValue.editData.sealrange) {
          otherDatas.sealrange = JSON.parse(newValue.editData.sealrange);
        }
        // formInline.description = newValue.editData.description
        // if (newValue.editData.description) {
        //   showDes.value = true
        // }
      } else {
        formInline.number = '';
        formInline.name = '';
        formInline.name1 = '';
        formInline.workhours = '';
        formInline.price = null;
        formInline.description = '';
        for (const key in otherDatas.dictionaryJSON['JCGF'].enable) {
          formInline[key] = 0;
        }
        formInline.remark = '';
        showDes.value = false;
        otherDatas.method = '';
        otherDatas.testcapability = [];
        otherDatas.sealrange = [];
        otherDatas.category = props.categoryIds === '0' ? '' : props.categoryIds;
        formInline.categoryid =
          props.categoryIds[props.categoryIds.length - 1] === '0'
            ? ''
            : props.categoryIds[props.categoryIds.length - 1];
      }
      otherDatas.oldFormInline = JSON.parse(JSON.stringify(formInline));
    });
    // 确认新增
    const onSubmit = () => {
      form.value.validate(valid => {
        if (valid) {
          if (formInline.price) {
            formInline.price = parseFloat(formInline.price);
          }
          if (otherDatas.status) {
            formInline.status = 1;
          } else {
            formInline.status = 0;
          }
          if (otherDatas.testcapability.length > 0) {
            formInline.testcapability = JSON.stringify(otherDatas.testcapability);
          }
          if (props.title === '新增项目') {
            saveCapability(formInline).then(function (res) {
              if (res.status === 200 && res.data.code === 200) {
                // const datas = { show: true, detail: formInline }
                // context.emit('setDetail', datas)
                bus.$emit('reloadList', true);
                context.emit('close', true);
                ElMessage.success('新增成功');
              }
            });
          } else {
            updateCapability(formInline).then(function (res) {
              if (res.status === 200 && res.data.code === 200) {
                // const datas = { show: true, detail: formInline }
                // context.emit('setDetail', datas)
                bus.$emit('reloadList', true);
                bus.$emit('reloadDetail', formInline);
                context.emit('close', true);
                ElMessage.success('编辑成功');
              }
            });
          }
        } else {
          return false;
        }
      });
    };

    // 添加项目描述
    const showDes = ref(false);
    const addItemDis = () => {
      showDes.value = !showDes.value;
    };
    // 所属分类change
    const changeCategory = value => {
      const len = value.length - 1;
      formInline.categoryid = value[len];
    };
    // 检测项目change
    const changeTestcapability = value => {
      formInline.testcapability = JSON.stringify(value);
    };
    // 盖章范围
    const changeSealrange = () => {
      formInline.sealrange = JSON.stringify(otherDatas.sealrange);
    };
    // 基准价过滤
    const clearNoNum = obj => {
      if (obj) {
        // 先把非数字的都替换掉，除了数字和.
        obj = obj.replace(/[^\d.]/g, '');
        // 必须保证第一个为数字而不是.
        obj = obj.replace(/^\./g, '');
        // 保证只有出现一个.而没有多个.
        obj = obj.replace(/\.{2,}/g, '.');
        // 保证.只出现一次，而不能出现两次以上
        obj = obj.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
      }
      formInline.price = obj;
    };
    // 基础工分失去焦点
    const handleBlur = fieldKey => {
      if (!formInline[fieldKey]) {
        formInline[fieldKey] = 0;
      }
    };
    return {
      clearNoNum,
      form,
      twoDecimalPlaces,
      handleBlur,
      changeSealrange,
      changeTestcapability,
      showDes,
      changeCategory,
      addItemDis,
      otherDatas,
      onSubmit,
      formInline,
      handleClose,
      showDrawer,
      titles
    };
  }
};
</script>

<style lang="scss" scoped>
.add-item {
  .el-input-number--medium {
    width: 100%;
  }
  :deep(.el-form-item--medium .el-form-item__content) {
    line-height: 36px;
  }
  .item-status {
    width: 100%;
    margin-left: 18px;
    :deep(.el-form-item__content) {
      display: inline-block;
      margin-left: 10px;
    }
  }
  .remove-bg {
    background: none;
    color: $tes-primary;
    border: 0px;
    padding: 0px;
    margin: 0px 0px 0px 28px;
    font-weight: 500;
    font-size: 14px;
    float: left;
  }
  .allow-create-input {
    width: 100%;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .el-tag {
      margin: 0px 2px;
    }
    :deep(.el-input) {
      width: 20%;
      border: 0px;
      input {
        border: 0px;
      }
    }
  }
}
.drawer-fotter {
  position: absolute;
  bottom: 30px;
  right: 52px;
  //  border-top: 1px solid #e4e7ed;
  text-align: right;
}
</style>
