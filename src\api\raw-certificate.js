import request from '@/utils/request';

// 原材料合格证列表查询
export function getCertificateprintList(data) {
  return request({
    url: '/api-orders/orders/certificateprint/list',
    method: 'post',
    data
  });
}

// 原材料批量判定
export function certificateDecide(data) {
  return request({
    url: '/api-orders/orders/certificateprint/certificateDecide',
    method: 'post',
    data
  });
}

// 原材料批量入库
export function rawDataReturn(data) {
  return request({
    url: '/api-orders/orders/certificateprint/rawDataReturn',
    method: 'post',
    data
  });
}

// 查询原材料打印数据
export function getRawCertificatePrintData(data) {
  return request({
    url: '/api-orders/orders/samples/getRawCertificatePrintData',
    method: 'post',
    data
  });
}

// 查询原材料打印数据
export function updatePrintStatus(data) {
  return request({
    url: '/api-orders/orders/certificateprint/updatePrintStatus',
    method: 'post',
    data
  });
}

/** 更新入库状态
 * certificatePrintIdList  合格证id列表
 *  wareHouseDate: 入库日期
 */
export function updateInputWarehouseStatus(data) {
  return request({
    url: '/api-orders/orders/certificateprint/updateInputWarehouseStatus',
    method: 'post',
    data
  });
}
