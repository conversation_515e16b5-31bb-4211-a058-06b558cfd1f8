<template>
  <!-- 检测标准库 - 编辑表达式 - 选择关键参数 -->
  <el-dialog
    :model-value="show"
    custom-class="custom-dialog"
    title="选择关键参数"
    width="60%"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <div class="dialog-header">
      <div class="header-left">
        <el-input
          v-model="filterText"
          class="search"
          size="small"
          placeholder="请输入类目名称"
          prefix-icon="el-icon-search"
          clearable
          @keyup.enter="searchItem(filterText)"
          @clear="filterClear()"
        />
        <el-button type="primary" size="small" @click="searchItem(filterText)">查询</el-button>
      </div>
    </div>
    <div class="dialog-content">
      <el-row>
        <el-col :span="6">
          <div class="tree-container">
            <div class="tree-content">
              <el-tree
                ref="treeRef"
                :data="tree"
                node-key="id"
                :props="defaultProps"
                default-expand-all
                :expand-on-click-node="false"
                :highlight-current="true"
                draggable
                class="leftTree"
                @node-click="clickNode"
              >
                <template #default="{ node }">
                  <span>{{ node.label }}</span>
                </template>
              </el-tree>
            </div>
          </div>
        </el-col>
        <el-col :span="18">
          <div v-loading="loading" class="list-container">
            <div v-for="(item, index) in newTreeDetail" :key="index" class="item-content">
              <div class="main">
                <div class="title">{{ item.name }}</div>
                <div class="item-list">
                  <el-tag
                    v-for="(list, index1) in item.capabilityparaVoList"
                    :key="index1"
                    :type="list.checked ? 'primary' : 'info'"
                  >
                    <el-checkbox
                      v-if="list.resulttype === '数值型'"
                      v-model="list.checked"
                      @change="changeItems(list)"
                    />
                    {{ list.name }}
                  </el-tag>
                </div>
              </div>
            </div>
            <el-empty v-if="treeDetail.length === 0" :image="emptyImg" description="暂无数据" />
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="dialog-other">
      <el-row>
        <el-col :span="24">
          <div class="title">
            <label>已选择的关键参数</label>
          </div>
          <div v-if="tagsList.length > 0" class="select-items">
            <el-tag v-for="tag in tagsList" :key="tag.name" type="info" @close="closeTag(tag)">
              {{ tag.name }}
            </el-tag>
          </div>
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="dialogSuccess">确定选择</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, getCurrentInstance, inject } from 'vue';
import { getCapabilityUplist } from '@/api/user';
import _ from 'lodash';
import emptyImg from '@/assets/img/empty-data.png';
// import { ElLoading } from 'element-plus'
// import { useRoute } from 'vue-router'
// import { formatPaginationList } from '@/utils/formatJson'

export default {
  name: 'SelectKeyParams',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    },
    tree: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'selectData'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const lodash = inject('_');
    const detailList = ref(null);
    const datas = reactive({
      showDialog: props.show,
      filterText: '',
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      tagsList: [],
      treeDetail: [],
      newTreeDetail: [],
      loading: false,
      isRefresh: true,
      capabilityPara: []
    });

    watch(
      () => props.show,
      newValue => {
        datas.showDialog = newValue;
        if (datas.isRefresh === false) {
          return false;
        }
        datas.tagsList = JSON.parse(JSON.stringify(props.data));
        datas.tagsList = lodash.uniqBy(datas.tagsList, 'name');
        if (newValue && props.tree && props.tree.length > 0) {
          proxy.getCapabilityList(props.tree[0].id, props.tree[0].materialCategoryCode);
          datas.isRefresh = false;
        }
      },
      { deep: true }
    );

    // 过滤树节点
    const treeRef = ref(null);
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    // 点击树节点
    const clickNode = (data, node) => {
      proxy.getCapabilityList(data.id, data.materialCategoryCode);
    };

    // 确定选择
    const dialogSuccess = () => {
      context.emit('selectData', datas.tagsList);
      datas.showDialog = false;
      datas.isRefresh = true;
      context.emit('close', false);
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      datas.isRefresh = true;
      context.emit('close', false);
    };
    // changeCheckBox
    const changeCheckBox = (item, flag) => {
      if (item.disabled) {
        return false;
      }
      item.checked = !item.checked;
      if (item.checked) {
        filterItem(item);
      } else {
        datas.tagsList = [];
      }
    };
    // 数据过滤
    const filterItem = item => {
      item.sourceCapabilityType = 'Internal';
      item.newcapabilityparaVoList = [];
      if (item.capabilityparaVoList && item.capabilityparaVoList.length > 0) {
        item.capabilityparaVoList.forEach(cap => {
          const newItem1 = {
            sourceIdStr: cap.id,
            sourceNumber: cap.number,
            sourceparentIdStr: cap.capabilityid,
            sourceCapabilityDescription: cap.description,
            sourceName: cap.name,
            sourceCapabilityType: cap.sourceCapabilityType,
            experimentCategoryIdStr: cap.categoryid
          };
          const allItems = Object.assign(cap, newItem1);
          item.newcapabilityparaVoList.push(allItems);
        });
      }
      const newItem = {
        sourceIdStr: item.id,
        sourceNumber: item.number,
        sourceparentIdStr: item.capabilityid,
        sourceCapabilityDescription: item.description,
        sourceName: item.name,
        sourceCapabilityType: item.sourceCapabilityType,
        experimentCategoryIdStr: item.categoryid,
        parentid: '0',
        operationType: 1,
        status: 1,
        childList: item.newcapabilityparaVoList
      };
      const param = Object.assign(item, newItem);
      // datas.tags.push(param)
      datas.tagsList = param;
      datas.newTreeDetail.forEach(tree => {
        // if (tree.capabilityparaVoList && tree.capabilityparaVoList.length > 0) {
        //   tree.capabilityparaVoList.forEach(cpl => {
        //     cpl.checked = false
        //   })
        // }
        if (tree.id === param.id) {
          tree.checked = true;
          datas.capabilityPara = [];
        } else {
          tree.checked = false;
        }
      });
    };
    // 关闭tags
    const closeTag = tag => {
      datas.tagsList.splice(datas.tagsList.indexOf(tag), 1);
      datas.newTreeDetail.forEach(tree => {
        if (tree.id === tag.id) {
          tree.checked = false;
        }
      });
    };
    // searchItem
    const searchItem = value => {
      if (value) {
        datas.newTreeDetail = datas.treeDetail.filter(item => {
          return JSON.stringify(item).indexOf(value) !== -1;
        });
      } else {
        datas.newTreeDetail = datas.treeDetail;
      }
    };

    const filterClear = () => {
      datas.newTreeDetail = datas.treeDetail;
    };

    const changeItems = list => {
      if (list.checked) {
        datas.tagsList.push(list);
      } else {
        lodash.remove(datas.tagsList, n => {
          return list.id === n.id;
        });
      }
    };

    return {
      ...toRefs(datas),
      emptyImg,
      searchItem,
      dialogSuccess,
      close,
      filterNode,
      clickNode,
      treeRef,
      closeTag,
      changeCheckBox,
      detailList,
      filterItem,
      changeItems,
      filterClear
    };
  },
  methods: {
    getCapabilityList(id, materialCategoryCode) {
      // 获取检测项目list
      const vm = this;
      vm.loading = true;
      getCapabilityUplist(id, materialCategoryCode).then(response => {
        vm.loading = false;
        if (response !== false && response.data.code === 200) {
          const { data } = response.data;
          vm.treeDetail = data;
          vm.newTreeDetail = data;
          vm.newTreeDetail.forEach(item => {
            if (item.capabilityparaVoList.length > 0) {
              item.capabilityparaVoList.forEach(childItem => {
                const hasitem = _.filter(vm.tagsList, res => {
                  return res.id === childItem.id;
                });
                if (hasitem.length >= 1) {
                  childItem.checked = true;
                } else {
                  childItem.checked = false;
                }
              });
            }
          });
          if (vm.filterText) {
            vm.searchItem(vm.filterText);
          }
        } else {
          setTimeout(() => {
            vm.loading = false;
          }, 1.5 * 500);
        }
      });
    }
  }
};
</script>
<style lang="scss">
@import '@/styles/dialog.scss';
</style>
<style lang="scss" scoped>
@import '@/styles/tree.scss';

.dialog-content {
  margin-bottom: 0;
  padding-bottom: 20px;
  .tree-container {
    .tree-content {
      height: calc(100vh - 540px);
      overflow-y: auto;
      padding-left: 0;
    }
  }
  .list-container {
    height: calc(100vh - 500px);
    overflow-y: auto;
    .item-content {
      padding-bottom: 0 !important;
      margin-bottom: 10px !important;
      .main {
        width: 100%;
        .title {
          height: 32px;
          line-height: 32px;
          margin: 0;
          padding-bottom: 0;
        }
        .item-list {
          width: 100%;
          border-left: none;
          padding-bottom: 10px;
          .item-box {
            height: 16px;
            margin-right: 20px !important;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
}
</style>
