@import '@/styles/intelligentChart.scss';

.dataBoard {
  text-align: left;
  background: #253d87;
  height: 100%;
  overflow-y: auto;
  .inBlock {
    display: inline-block;
  }
  .dataPageTop {
    position: relative;
    .dataPageTitle {
      font-size: 45px;
      background: -webkit-linear-gradient(left, #87b7ff 1%, #ffffff 20%, #ffffff 50%, #ffffff 80%, #87b7ff 100%);
      -webkit-text-fill-color: transparent;
      font-family: 'YouSheBiaoTiHei';
      font-weight: bold;
      line-height: 48px;
      letter-spacing: 5px;
      -webkit-background-clip: text;
      background-clip: text;
      text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
      text-align: center;
      display: inline-block;
      position: absolute;
      top: 10%;
      left: 50%;
      transform: translate(-50%);
    }
    .logo {
      position: absolute;
      left: 7%;
      top: 32%;
    }
    .logoRight {
      position: absolute;
      max-height: 37px;
      max-width: 150px;
      left: 9.8%;
      top: 43%;
    }
    .topHeader {
      width: 100%;
      height: 79px;
    }
    .topRight {
      position: absolute;
      width: 24%;
      right: -10%;
      top: 45%;
      transform: translate(-45%);
      font-size: 18px;
      color: $color;
      img {
        vertical-align: middle;
      }
    }
    .time {
      margin-right: 41px;
      font-size: 18px;
      line-height: 26px;
      &:first-child img {
        margin-right: 14px;
        position: relative;
        top: -1px;
      }
      &:nth-child(2) img {
        margin-right: 5px;
        position: relative;
        top: -1px;
      }
    }
    .fullScreenQuit {
      position: absolute;
      width: 99px;
      height: 33px;
      bottom: 2%;
      right: 2%;
      color: #d5f2ff;
      text-shadow: 0px 0px 2px #19c9f8;
      font-weight: 700;
      font-size: 16px;
      background-repeat: no-repeat;
      background-position: 50% 50%;
      line-height: 33px;
      text-align: center;
      cursor: pointer;
      img {
        margin-right: 6px;
      }
    }
  }
  .componentItem {
    margin-top: 27px;
    padding-right: 20px;
  }
}

.dataBoardContent {
  padding: 0 20px 2px 40px;
  height: calc(100vh - 6.2rem);
  overflow-y: auto;
  .topTime {
    color: $color;
    background: linear-gradient(90deg, #245094 0%, #3f7ab6 100%);
    line-height: 20px;
    font-size: 12px;
    padding-right: 24px;
  }
  .textLeft {
    text-align: left;
  }
  .topHeader {
    width: 100%;
  }
  ul,
  li,
  div {
    margin: 0;
    padding: 0;
  }
  li {
    list-style: none;
  }
  .textRight {
    text-align: right;
  }
  h1 {
    font-weight: 700;
    font-size: 18px;
    line-height: 26px;
    color: $titleColor;
    padding: 7px 0 7px 29px;
    margin: 0;
  }
  .box-top {
    background: linear-gradient(90deg, #66b3e7 0%, #3f7ab6 100%);
    border-radius: 8px 8px 0px 0px;
    position: relative;
  }
  .box-Center {
    position: relative;
    background: linear-gradient(180deg, #2d59a5 0%, #4280b9 100%);
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 0 0 8px 8px;
    height: 260px;
  }
  .leftBorder {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
  .rightBorder {
    position: absolute;
    right: 0;
    top: 50%;
    transform: rotate(180deg) translateY(50%);
  }
  .top-right {
    font-weight: 700;
    font-size: 14px;
    line-height: 16px;
    color: $color;
    background: $darkBlue;
    border-radius: 20px;
    position: absolute;
    padding: 4px 12px;
    right: 10px;
    top: 50%;
    margin-top: -12px;
  }
  .phb {
    padding: 18px 26px 12px 60px;
    height: calc(100% - 20px);
    overflow-y: auto;
    img {
      position: absolute;
      left: -30px;
      top: 50%;
      transform: translateY(-50%);
    }
    li {
      line-height: 24px;
      color: $color;
      font-size: 14px;
      position: relative;
    }
    span {
      display: inline-block;
      width: 42px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .name {
      margin-right: 10px;
    }
  }
  .phb::-webkit-scrollbar {
    width: 0 !important;
  }
  :deep(.el-progress-bar__inner) {
    background: linear-gradient(90deg, #f18aa3 0%, #ffd27a 99.22%);
  }
  :deep(.el-progress__text) {
    color: #b9e3ff;
  }
  .centerBox {
    line-height: 36px;
    color: $scrollListColor;
    text-align: center;
    width: 100%;
    .boxTable {
      border: 2px solid $borderColor;
      padding: 3px 10px 10px 10px;
    }
    .listContent {
      height: 202px;
      overflow-y: auto;
    }
    .listContent::-webkit-scrollbar {
      width: 0 !important;
    }
    .textLeft {
      text-align: left;
    }
    .exceedTime {
      color: $scrollListColor;
      background-color: #f18ba2;
      border-radius: 4px;
      display: inline-block;
      padding: 0px 4px;
      height: 20px;
      line-height: 20px;
      font-size: 12px;
      position: absolute;
      right: 2px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .centerTop {
    width: 100%;
    height: 44px;
    line-height: 26px;
    color: $titleColor;
    padding: 9px 0 9px 39px;
    font-size: 18px;
    font-weight: 700;
    position: relative;
    .number {
      width: 173px;
      height: 56px;
      line-height: 56px;
      position: absolute;
      right: 0;
      bottom: 0px;
      font-size: 14px;
      padding-right: 20px;
      text-align: right;
      span {
        font-size: 45px;
        background: linear-gradient(180deg, #ffeaa0 0%, #72e6ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }
  }
  .listLi {
    font-size: 14px;
    margin-bottom: 6px;
    .el-col {
      width: 100%;
      padding: 0 5px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      position: relative;
    }
    .el-col:first-child {
      padding: 0;
    }
    span {
      margin-right: 5px;
      display: inline-block;
    }
    span:last-child {
      margin-right: 0;
    }
  }
  .listLi .el-col:nth-child(2n + 1) {
    background-color: $scrollListFirst;
  }
  .listLi .el-col:nth-child(2n) {
    background-color: $scrollListSecond;
  }
}
.dataBoardContent::-webkit-scrollbar {
  width: 0 !important;
}
