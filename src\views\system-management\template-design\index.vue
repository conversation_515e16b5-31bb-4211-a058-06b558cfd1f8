<template>
  <!-- 模板设计 -->
  <ListLayout>
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="searchForm" class="page-searchbar" @submit.prevent>
        <el-form-item prop="param">
          <div style="width: 42vw">
            <CombinationQuery
              :field-list="searchFieldList"
              field-tip="模板编号/模板名称"
              @get-single-text="getSingleText"
              @get-param-list="getParamList"
              @reset-search="handleRest"
            />
          </div>
        </el-form-item>
        <el-form-item>
          <el-button
            class="searchBtn"
            size="large"
            type="text"
            @click="advancedSearch"
            @keyup.prevent
            @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
          /></el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('AddTemplateDeisgn')"
        :loading="listLoading"
        size="large"
        icon="el-icon-plus"
        type="primary"
        @click="handleDetail('create')"
        @keyup.prevent
        @keydown.enter.prevent
        >新增模板</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" :model="searchForm" label-width="100px" label-position="right" size="small">
            <el-form-item label="类型：">
              <el-select
                v-model="searchForm.templateType"
                class="owner-select"
                clearable
                filterable
                placeholder="请选择类型"
                size="small"
              >
                <el-option v-for="(val, key) in templateTypeJSON" :key="key" :label="val" :value="key" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="8" :offset="16" style="text-align: right">
          <TableColumnView binding-menu="TemplateDesign" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="tableList"
      size="medium"
      fit
      border
      height="auto"
      class="dark-table test-item-table base-table format-height-table"
      @header-dragend="drageHeader"
    >
      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="item.fieldType === fieldTypesEnum.Person">
              <UserTag :name="getNameByid(row[item.fieldKey]) || row[item.fieldKey] || '--'" />
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Date">
              <span>{{ row[item.fieldKey] == '' ? '--' : formatDate(row[item.fieldKey]) }}</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Custom">
              <div v-if="item.fieldKey === 'templateType'" class="nowrap">
                {{ templateTypeJSON[row.templateType] || row.templateType || '--' }}
              </div>
            </template>
            <span v-else>{{ row[item.fieldKey] || '--' }}</span>
          </template>
        </el-table-column>
      </template>
      <el-table-column
        v-if="
          getPermissionBtn('CheckTemplateDesign') ||
          getPermissionBtn('EditTemplateDesign') ||
          getPermissionBtn('OnlineTemplateDesign') ||
          getPermissionBtn('DeleteTemplateDesign')
        "
        label="操作"
        :min-width="colWidth.operationMultiple"
        class-name="fixed-right"
        fixed="right"
      >
        <template #default="{ row }">
          <span
            v-if="getPermissionBtn('CheckTemplateDesign') && row.templateVersion"
            class="blue-color"
            @click="handleTemplate('check', row)"
            >查看</span
          >
          <span v-if="getPermissionBtn('EditTemplateDesign')" class="blue-color" @click="handleDetail('update', row)"
            >编辑</span
          >
          <span
            v-if="getPermissionBtn('OnlineTemplateDesign')"
            class="blue-color"
            @click="handleTemplate('update', row)"
            >模板设计</span
          >
          <span v-if="getPermissionBtn('DeleteTemplateDesign')" class="red-color" @click="handleDelete(row)">删除</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
    <template #other>
      <DialogTemplateDesign
        :dialog-visible="dialogSubmit"
        :type="dialogType"
        :template-type="templateTypeJSON"
        :detail-info="selectRow"
        @closeDialog="closeDialog"
      />
    </template>
  </ListLayout>
</template>

<script>
// Basic
import { reactive, ref, toRefs } from 'vue';
import router from '@/router/index.js';
import { useStore } from 'vuex';
import { mapGetters } from 'vuex';
import { ElMessage, ElMessageBox } from 'element-plus';
// Utils
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
// Api
import { designtemplateList, deleteDesigntemplate } from '@/api/templateDesign';
import { documentPlanCategoryList } from '@/api/planManagement';
// data
import { colWidth } from '@/data/tableStyle';
// components
import UserTag from '@/components/UserTag';
import Pagination from '@/components/Pagination';
import ListLayout from '@/components/ListLayout';
import CombinationQuery from '@/components/CombinationQuery';
import TableColumnView from '@/components/TableColumnView';
import DialogTemplateDesign from './components/DialogTemplateDesign';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';
export default {
  name: 'TemplateDesign',
  components: { Pagination, ListLayout, UserTag, CombinationQuery, TableColumnView, DialogTemplateDesign },
  setup() {
    const store = useStore().state;
    const state = reactive({
      tableRef: ref(),
      activeName: '0',
      dialogSubmit: false,
      templateTypeJSON: {}, // 模板类型
      searchFieldList: [],
      listLoading: false,
      dialogType: '',
      selectRow: {},
      showS: false,
      userOptions: store.common.nameList,
      listQuery: {
        limit: 20,
        page: 1
      },
      searchForm: {
        templateType: '',
        param: '',
        tableQueryParamList: []
      },
      tableColumns: [],
      tableList: [],
      nameList: store.common.nameList,
      total: 0
    });
    const tableKey = ref(0);
    const getList = async query => {
      const params = { ...state.searchForm };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoading = true;
      const response = await designtemplateList(params).finally((state.listLoading = false));
      if (response) {
        state.total = response.data.data.totalCount;
        state.tableList = response.data.data.list;
        state.listQuery.page = Number(params.page);
      }
    };
    getList();
    const handleRest = () => {
      state.searchForm = {
        param: '',
        templateType: '',
        tableQueryParamList: []
      };
      getList();
    };
    const initType = async () => {
      state.listLoading = true;
      const response = await documentPlanCategoryList({ page: '1', limit: '-1' }).finally((state.listLoading = false));
      if (response) {
        state.templateTypeJSON = {};
        response.data.data.forEach(item => {
          state.templateTypeJSON[item.code] = item.name;
        });
      }
    };
    initType();
    const handleDetail = (type, row) => {
      state.dialogSubmit = true;
      state.selectRow = row;
      state.dialogType = type;
    };
    const getSingleText = val => {
      state.searchForm.param = val;
      state.searchForm.tableQueryParamList = [];
      getList();
    };

    const getParamList = paramList => {
      state.searchForm.tableQueryParamList = paramList;
      state.searchForm.param = '';
      getList();
    };
    // 高级筛选
    const advancedSearch = () => {
      state.showS = !state.showS;
      if (state.activeName === '0') {
        state.activeName = '1';
      } else {
        state.activeName = '0';
      }
    };
    const onUpdateColumns = columns => {
      tableKey.value = tableKey.value + 1;
      state.tableColumns = columns;
      state.searchFieldList = columns.filter(item => {
        return item.isQuery == 1;
      });
    };
    const closeDialog = isRefresh => {
      state.dialogSubmit = false;
      if (isRefresh) {
        getList();
      }
    };
    const handleTemplate = (type, row) => {
      router.push({
        path: '/systemManagement/templateDesignDetail',
        query: {
          type: type,
          id: row.id,
          name: row.name
        }
      });
    };
    const handleDelete = row => {
      ElMessageBox({
        title: '删除确认',
        message: '是否确认删除？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      }).then(async () => {
        state.listLoading = true;
        const response = await deleteDesigntemplate(row.id).finally((state.listLoading = false));
        if (response) {
          ElMessage.success('删除成功！');
          getList();
        }
      });
    };
    return {
      ...toRefs(state),
      getSingleText,
      handleDelete,
      handleTemplate,
      closeDialog,
      getParamList,
      getPermissionBtn,
      handleDetail,
      drageHeader,
      getNameByid,
      getNamesByid,
      handleRest,
      formatDate,
      getList,
      tableKey,
      advancedSearch,
      colWidth,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  },
  computed: {
    ...mapGetters(['tenantGroup', 'tenantInfo'])
  }
};
</script>
<style lang="scss" scoped>
.btn-mg20 {
  margin-right: 20px;
}

.margin-right {
  margin-right: 4px;
}
.fr {
  float: right;
}

:deep(.el-radio.is-bordered + .el-radio.is-bordered) {
  margin-left: 0;
}
.submit_dialog {
  .el-form-item {
    margin-bottom: 20px;
  }
  .el-form-item__error {
    top: 85%;
  }
  .el-radio {
    margin-right: 0;
    background: #f4f4f5;
  }
  .sendBack.is-checked {
    background: $tes-red;
  }
  .pass.is-checked {
    background: $green;
  }
  .sendBack {
    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #fff;
    }
  }
  .pass {
    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #fff;
    }
  }
  .el-radio.is-bordered {
    border: 0;
    border-radius: 4px;
    width: 49%;
    text-align: center;
  }
  .radioGroup {
    width: 100%;
    :deep(.el-radio__input) {
      display: none;
    }
  }
}
</style>
