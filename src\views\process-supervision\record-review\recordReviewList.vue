<template>
  <!-- 原始记录审核 -->
  <ListLayout>
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="param">
          <div style="width: 42vw">
            <CombinationQuery
              :field-list="searchFieldList"
              field-tip="样品编号/样品名称/型号规格/生产订单号/销售订单号/批次"
              @get-single-text="getSingleText"
              @get-param-list="getParamList"
              @reset-search="reset"
            />
          </div>
        </el-form-item>
        <el-form-item>
          <el-button
            class="searchBtn"
            size="large"
            type="text"
            @click="advancedSearch"
            @keyup.prevent
            @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
          /></el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('recordAudit')"
        size="large"
        icon="el-icon-document-checked"
        type="primary"
        :disabled="!tableSelected.length"
        @click="handleAudit"
        @keyup.prevent
        @keydown.enter.prevent
        >审核</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" :model="formInline" label-width="110px" label-position="right" size="small">
            <!-- <el-form-item label="物资分类：">
              <el-radio-group v-model="formInline.mateType" size="small" max="1">
                <el-radio-button class="margin-right" @change="changeMateType('all')">不限</el-radio-button>
                <el-radio-button
                  v-for="item in types"
                  :key="item.code"
                  :label="item.code"
                  class="label-type"
                  @change="changeMateType(item)"
                >
                  {{ item.name }}
                </el-radio-button>
              </el-radio-group>
            </el-form-item> -->
            <el-form-item label="提交日期：" prop="submitTime">
              <el-date-picker
                v-model="formInline.submitTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="handleSubmitTime"
              />
            </el-form-item>
            <el-form-item label="审核日期：" prop="reviewTime">
              <el-date-picker
                v-model="formInline.reviewTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="handleReview"
              />
            </el-form-item>
            <el-form-item label="试验员：">
              <el-select
                v-model="formInline.ownerId"
                class="owner-select"
                clearable
                filterable
                placeholder="请选择"
                size="small"
                :filter-method="filterUserList"
                @focus="filterUserList(null)"
              >
                <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="审核员：">
              <el-select
                v-model="formInline.reviewerId"
                class="owner-select"
                clearable
                filterable
                placeholder="请选择"
                size="small"
                :filter-method="filterUserList"
                @focus="filterUserList(null)"
                @change="changeReviewerId"
              >
                <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="16" class="flex gap-6">
          <el-radio-group
            v-model="statusRadio"
            size="small"
            @change="
              val => {
                getList(1, val);
              }
            "
          >
            <el-radio-button label="-1">全部</el-radio-button>
            <el-radio-button v-for="(item, index) in status" :key="index" :label="item.value">{{
              item.label
            }}</el-radio-button>
          </el-radio-group>
          <el-checkbox v-model="onlyMy" @change="giveMeChange">仅看我的</el-checkbox>
          <el-select
            v-if="getPermissionBtn('inspectionType')"
            v-model="formInline.type"
            placeholder="请选择检验类型"
            size="small"
            clearable
            filterable
            @change="getList()"
          >
            <el-option v-for="(val, key) in dictionary['JYLX'].enable" :key="key" :label="val" :value="Number(key)" />
          </el-select>
          <el-select
            v-if="getPermissionBtn('registerDepartment')"
            v-model="formInline.registerDepartment"
            class="owner-select"
            filterable
            placeholder="请选择送检部门"
            size="small"
            clearable
            @change="getList()"
          >
            <el-option v-for="(val, key) in dictionary['JHDW']?.enable" :key="key" :label="val" :value="val" />
          </el-select>
        </el-col>
        <el-col :span="8" style="text-align: right">
          <TableColumnView binding-menu="record-review-list" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="tableList"
      size="medium"
      fit
      border
      height="auto"
      class="dark-table test-item-table base-table format-height-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      @header-dragend="drageHeader"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        :width="colWidth.checkbox"
        align="center"
        :selectable="selectable"
        fixed="left"
      />
      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="item.fieldType === fieldTypesEnum.Link">
              <template v-if="item.fieldKey === 'secSampleNum' && row[item.fieldKey]">
                <span v-if="row.isUrgent" class="urgent">急</span>
                <span v-copy="row[item.fieldKey]" class="blue" @click.stop="handleDetail(row)">{{
                  row[item.fieldKey] ? row[item.fieldKey] : '--'
                }}</span>
              </template>
              <template v-else-if="item.fieldKey === 'capabilityName'">
                <span class="blue" @click.stop="handleCheckOpera(row, row.status)">{{ row.capabilityName }}</span>
              </template>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Person">
              <template v-if="item.fieldKey === 'realOwnerIds'">
                <UserTag
                  v-for="ownerId in row.realOwnerIds.split(',')"
                  :key="ownerId"
                  :name="getNameByid(ownerId) || ownerId || '--'"
                />
              </template>
              <UserTag v-else :name="getNameByid(row[item.fieldKey]) || row[item.fieldKey] || '--'" />
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Status">
              <el-tag
                v-if="row.status == 3 || row.status == 5"
                size="small"
                effect="dark"
                :type="status[row.status].type"
                >{{ status[row.status].label }}</el-tag
              >
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Date">
              <span>{{ row[item.fieldKey] == '' ? '--' : formatDate(row[item.fieldKey]) }}</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Custom">
              <div v-if="item.fieldKey === 'samplesName'" class="nowrap">{{ row.samplesName || row.sampleName }}</div>
              <span v-else-if="item.fieldKey === 'type'">{{ dictionary['JYLX'].all[row[item.fieldKey]] }}</span>
              <!-- 检验对象 -->
              <div v-else-if="item.fieldKey === 'inputWarehouseNo,productionOrderNo'" class="nowrap">
                {{ [1, 8].includes(row.type) ? row.inputWarehouseNo || '--' : row.productionOrderNo || '--' }}
              </div>
              <!--对象位置 -->
              <template v-else-if="item.fieldKey === 'productionProcedure,productionStation,wareHouseName'">
                <div v-if="[1, 8].includes(row.type)" class="nowrap">{{ row.wareHouseName }}</div>
                <div v-else class="nowrap">{{ row.productionProcedure }}{{ row.productionStation }}</div>
              </template>
              <!--对象名称-->
              <template v-else-if="item.fieldKey === 'customerName'">
                <div v-if="[1, 8].includes(row.type)" class="nowrap">{{ row.supplierName }}</div>
                <div v-else class="nowrap">{{ row.customerName }}</div>
              </template>
            </template>
            <span v-else>{{ row[item.fieldKey] || '--' }}</span>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
    <template #other>
      <el-dialog
        v-model="dialogSubmit"
        title="原始记录审核 "
        :close-on-click-modal="false"
        width="480px"
        custom-class="submit_dialog"
      >
        <el-form v-if="dialogSubmit" ref="ruleForm" :model="formData" label-position="top" label-width="110px">
          <el-form-item
            label="请选择审核结果："
            prop="result"
            :rules="{ required: true, message: '请选择审核结果', trigger: 'change' }"
          >
            <el-radio-group v-model="formData.result" class="radioGroup">
              <el-radio :label="0" border class="sendBack">退回</el-radio>
              <el-radio :label="1" border class="fr pass">通过</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="formData.result === 0" label="退回原因：">
            <el-input
              v-model="formData.backReason"
              type="textarea"
              maxlength="250"
              :rows="2"
              placeholder="请输入退回原因"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogSubmit = false">取 消</el-button>
            <el-button type="primary" @click="onSubmit" @keyup.prevent @keydown.enter.prevent>确 认</el-button>
          </span>
        </template>
      </el-dialog>
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import router from '@/router/index.js';
import Pagination from '@/components/Pagination';
import ListLayout from '@/components/ListLayout';
import { formatDate } from '@/utils/formatTime';
import { useStore } from 'vuex';
import UserTag from '@/components/UserTag';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { recordList, reviewVoList } from '@/api/recordReview';
import { drageHeader } from '@/utils/formatTable';
import { addByTemp } from '@/api/messageAgent';
import { colWidth } from '@/data/tableStyle';
import { getLoginInfo } from '@/utils/auth';
import { mapGetters } from 'vuex';
import CombinationQuery from '@/components/CombinationQuery';
import TableColumnView from '@/components/TableColumnView';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';
import _ from 'lodash';
import { getDictionary } from '@/api/user';
export default {
  name: 'RecordReviewList',
  components: { Pagination, ListLayout, UserTag, CombinationQuery, TableColumnView },
  setup() {
    // const _ = inject('_')
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const state = reactive({
      tableRef: ref(),
      activeName: '0',
      dialogSubmit: false,
      onlyMy: true,
      ruleForm: ref(),
      searchFieldList: [],
      listLoading: false,
      showS: false,
      status: {
        3: { type: 'warning', label: '待审核', value: '3' },
        5: { type: 'success', label: '已通过', value: '5' }
      },
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ],
      userOptions: store.common.nameList,
      copyUserOptions: store.common.nameList,
      types: store.user.materialList,
      listQuery: {
        limit: 20,
        page: 1
      },
      formData: {
        // 审核弹框列表
        result: '1'
      },
      statusRadio: '3',
      formInline: {
        reviewerId: getLoginInfo().accountId, // 审核人员
        submitTime: [],
        reviewTime: [],
        ownerId: '', // 试验员
        mateType: '',
        param: '',
        tableQueryParamList: [],
        registerDepartment: ''
      },
      dictionary: {
        JYLX: {
          enable: {},
          all: {}
        },
        JHDW: {
          enable: {},
          all: {}
        }
      },
      tableColumns: [],
      tableList: [],
      nameList: store.common.nameList,
      samplesInfo: {},
      tableSelected: [], // 表格选中的值
      dialogFormVisible: false,
      total: 0,
      warehouseDateRange: [],
      orderDateRange: [],
      warehouseDate: '全部',
      orderDate: '全部',
      currentAccountId: getLoginInfo().accountId
    });
    // 要是从待办那边跳转过来，需要添加过滤条件，暂时过滤样品编号
    const winUrl = decodeURI(window.location.href);
    const urlList = winUrl.split('?');
    const sampleSecNo = urlList[1];
    if (sampleSecNo) {
      state.formInline.key = sampleSecNo;
    }
    const activeTabsName = ref('first');
    const tableKey = ref(0);
    const getList = query => {
      const params = { ...state.formInline };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      params.status = state.statusRadio;
      state.listLoading = true;
      recordList(params).then(res => {
        state.listLoading = false;
        if (res) {
          state.total = res.data.data.totalCount;
          state.tableList = res.data.data.list;
          state.listQuery.page = Number(params.page);
        }
      });
    };
    getList();
    const getDictionaryList = () => {
      Object.keys(state.dictionary).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          state.dictionary[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              state.dictionary[item].enable[optionItem.code] = optionItem.name;
            }
            state.dictionary[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    getDictionaryList();
    const initFieldList = () => {
      state.searchFieldList = [
        {
          field: 'secSampleNum',
          name: '样品编号'
        },
        {
          field: 'capabilityName',
          name: '检测项目'
        },
        {
          field: 'sampleName',
          name: '样品名称'
        },
        {
          field: 'prodType',
          name: '型号规格'
        }
      ];
      let addArray = [];
      if (store.user.tenantInfo.type === 1) {
        addArray = [
          {
            field: 'materialGroup',
            name: '物料分组'
          },
          {
            field: 'materialNo',
            name: '物料编号'
          },
          {
            field: 'reelNo',
            name: '盘号'
          },
          {
            field: 'batchNo',
            name: '批次'
          },
          {
            field: 'supplierName',
            name: '供应商名称'
          },
          {
            field: 'customerName',
            name: '客户名称'
          },
          {
            field: 'wareHouseName',
            name: '仓库名称',
            isNotQuery: 0
          },
          {
            field: 'productionProcedure',
            name: '生产工序',
            isNotQuery: 0
          },
          {
            field: 'productionStation',
            name: '生产机台',
            isNotQuery: 0
          },
          {
            field: 'inputWarehouseNo',
            name: '入库单号',
            isNotQuery: 0
          },
          {
            field: 'productionOrderNo',
            name: '生产单号',
            isNotQuery: 0
          },
          {
            field: 'salesOrderNo',
            name: '销售订单号',
            isNotQuery: 0
          }
        ];
      } else if (store.user.tenantInfo.type === 0) {
        addArray = [
          {
            field: 'mateTypeStr',
            name: '物资分类'
          }
        ];
      }
      state.searchFieldList = state.searchFieldList.concat(addArray);
    };
    initFieldList();
    const handleSizeChange = val => {
      state.listQuery.limit = val;
      getList();
    };
    // 表格勾选框的禁用规则
    const selectable = (row, index) => {
      if (row.status === 3) {
        return true;
      } else {
        return false;
      }
    };
    const reset = () => {
      state.formInline = {
        param: '',
        mateType: '',
        reviewerId: getLoginInfo().accountId, // 审核人员
        submitTime: [],
        reviewTime: [],
        ownerId: '', // 试验员
        tableQueryParamList: []
      };
      state.onlyMy = true;
      getList();
    };
    const handleCheckOpera = row => {
      router.push({
        path: '/recordReviewDetail',
        query: {
          samplesId: row.samplesId,
          capabilityId: row.capabilityId
        }
      });
    };
    const handleSelectionChange = val => {
      state.tableSelected = val;
    };
    const handleAudit = () => {
      state.dialogSubmit = true;
      state.formData = {
        result: 1
      };
    };
    const handleDetail = row => {
      router.push({
        path: '/recordReview/sample/detail',
        query: {
          orderId: row.orderId,
          sampleId: row.samplesId
        }
      });
    };
    const onSubmit = () => {
      state.ruleForm.validate().then(valid => {
        if (valid) {
          var params = [];
          var samplesIds = [];
          var capabilityNames = [];
          var capabilityIds = [];
          var secSampleNums = [];
          state.tableSelected.forEach(item => {
            params.push({
              backReason: state.formData.backReason,
              experimentId: item.experimentId,
              result: state.formData.result
            });
            capabilityNames.push(item.capabilityName);
            capabilityIds.push(item.capabilityId);
          });
          samplesIds.push(state.tableSelected[0].samplesId);
          secSampleNums.push(state.tableSelected[0].secSampleNum);
          state.listLoading = true;
          state.dialogSubmit = false;
          reviewVoList({ reviewList: params }).then(res => {
            state.listLoading = false;
            if (res) {
              // 添加消息待办
              const params = {
                eventCode: 'M009',
                receiverType: '1',
                senderName: getNameByid(state.currentAccountId),
                receiverIds: state.currentAccountId,
                receiverNames: getNameByid(state.currentAccountId),
                c_ids: capabilityIds.join(','),
                c_b_samplesIdArray: samplesIds.join(','),
                c_b_sampleNoArray: secSampleNums.join(','),
                c_b_projectNameArray: capabilityNames.join(','),
                c_b_capabilityIdArray: capabilityIds.join(',')
              };

              if (state.formData.result === '0' || state.formData.result === 0) {
                params.eventCode = 'M010';
              }
              addByTemp(params).then(res => {});
              state.formData = {
                result: '1'
              };
              state.listQuery.page === 1;
              getList();
              proxy.$message.success(res.data.message);
            }
          });
        }
      });
    };
    // 过滤试验员
    const filterUserList = val => {
      if (val) {
        const list = [];
        state.copyUserOptions.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        state.userOptions = list;
      } else {
        state.userOptions = state.copyUserOptions;
      }
    };
    const handleRowClick = row => {
      state.tableRef.toggleRowSelection(
        row,
        !state.tableSelected.some(item => {
          return row.experimentId === item.experimentId;
        })
      );
    };
    const getSingleText = val => {
      state.formInline.param = val;
      state.formInline.tableQueryParamList = [];
      getList();
    };

    const getParamList = paramList => {
      state.formInline.tableQueryParamList = paramList;
      state.formInline.param = '';
      getList();
    };
    // 高级筛选
    const advancedSearch = () => {
      state.showS = !state.showS;
      if (state.activeName === '0') {
        state.activeName = '1';
      } else {
        state.activeName = '0';
      }
    };
    // 高级搜索-提交日期-change
    const handleSubmitTime = date => {
      state.formInline.submitStartTime = date ? formatDate(date[0]) : '';
      state.formInline.submitEndTime = date ? formatDate(date[1]) : '';
    };
    // 高级搜索-审核日期-change
    const handleReview = date => {
      state.formInline.reviewStartTime = date ? formatDate(date[0]) : '';
      state.formInline.reviewEndTime = date ? formatDate(date[1]) : '';
    };
    // 高级搜索-物资分类-change
    const changeMateType = type => {
      if (type === 'all') {
        state.formInline.mateType = '';
      } else {
        state.formInline.mateType = type.code;
      }
    };
    // 仅看我的
    const giveMeChange = check => {
      if (check) {
        state.formInline.reviewerId = state.currentAccountId;
      } else {
        state.formInline.reviewerId = '';
      }
      getList();
    };
    const changeReviewerId = val => {
      if (val === state.currentAccountId) {
        state.onlyMy = true;
      } else {
        state.onlyMy = false;
      }
    };
    const onUpdateColumns = columns => {
      tableKey.value = tableKey.value + 1;
      state.tableColumns = columns;
    };
    return {
      ...toRefs(state),
      changeReviewerId,
      initFieldList,
      getSingleText,
      getParamList,
      giveMeChange,
      handleRowClick,
      getPermissionBtn,
      handleDetail,
      drageHeader,
      getNameByid,
      selectable,
      handleSizeChange,
      getNamesByid,
      handleAudit,
      onSubmit,
      reset,
      handleSelectionChange,
      handleCheckOpera,
      formatDate,
      activeTabsName,
      getList,
      tableKey,
      filterUserList,
      advancedSearch,
      handleSubmitTime,
      handleReview,
      changeMateType,
      colWidth,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  },
  computed: {
    ...mapGetters(['tenantGroup', 'tenantInfo'])
  }
};
</script>
<style lang="scss" scoped>
.btn-mg20 {
  margin-right: 20px;
}

.margin-right {
  margin-right: 4px;
}
.fr {
  float: right;
}

:deep(.el-radio.is-bordered + .el-radio.is-bordered) {
  margin-left: 0;
}
.submit_dialog {
  .el-form-item {
    margin-bottom: 20px;
  }
  .el-form-item__error {
    top: 85%;
  }
  .el-radio {
    margin-right: 0;
    background: #f4f4f5;
  }
  .sendBack.is-checked {
    background: $tes-red;
  }
  .pass.is-checked {
    background: $green;
  }
  .sendBack {
    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #fff;
    }
  }
  .pass {
    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #fff;
    }
  }
  .el-radio.is-bordered {
    border: 0;
    border-radius: 4px;
    width: 49%;
    text-align: center;
  }
  .radioGroup {
    width: 100%;
    :deep(.el-radio__input) {
      display: none;
    }
  }
}
</style>
