<template>
  <div class="online-excel">
    <div v-show="!isPrint" class="online-excel__container">
      <el-button v-if="!loading" size="small" class="online-excel__go-back" @click="handleGoBack"> 返 回 </el-button>
      <el-button v-if="!loading" class="online-excel__print-btn" type="primary" size="small" @click="handlePrint">
        打 印
      </el-button>
      <div id="luckysheet" />
    </div>
    <div v-if="isPrint" id="print-preview">
      <el-button size="small" class="online-excel__go-back2" @click="handleGoBack2"> 返 回 </el-button>
      <div class="online-excel__screenshots">
        <img
          v-for="(image, index) in images"
          :key="index"
          :src="image.src"
          :style="{
            position: 'absolute',
            top: image.top + 'px',
            left: image.left + 'px',
            width: typeof image.width === 'number' ? image.width + 'px' : image.width,
            height: typeof image.height === 'number' ? image.height + 'px' : image.height
          }"
          alt=""
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, toRefs, onMounted, onBeforeUnmount } from 'vue';
import { useRoute } from 'vue-router';
import router from '@/router/index.js';
import { ElMessage } from 'element-plus';
import ExcelEditer from './excelEditer';
import printJS from 'print-js';
import html2canvas from 'html2canvas';
import { reportTypes } from '@/data/industryTerm';
import { getNameByid, getIdByName } from '@/utils/common';
import { getCertificateDetail, saveCertificatePrint } from '@/api/certificate-export';

export default {
  name: 'OnlineExcel',
  setup(props, context) {
    const route = useRoute();
    const state = reactive({
      loading: false,
      detail: {},
      images: [],
      isPrint: false
    });
    const excelEditer = ref();

    onMounted(async () => {
      excelEditer.value = new ExcelEditer(false);
      state.loading = true;
      await excelEditer.value.loadExcel(route.query.url);
      getDetailData();
    });

    onBeforeUnmount(() => {
      excelEditer.value.destroyExcel();
    });

    const getDetailData = async () => {
      const { id, sampleId } = route.query;
      const res = await getCertificateDetail({ id, sampleId });
      if (res) {
        state.loading = false;
        state.detail = res.data.data;
        setTimeout(() => {
          excelEditer.value.setValuesByDefinedName(processDetailDataForDefinedName(res.data.data));
        }, 500);
      }
    };

    const processDetailDataForDefinedName = data => {
      const newData = { ...data };
      if (newData.reportType) {
        const reportTypeItem = reportTypes.find(item => item.code === Number(data.reportType));
        if (reportTypeItem) {
          newData.reportType = reportTypeItem.name;
        } else {
          newData.reportType = '';
        }
      }
      if (newData.inspector) {
        newData.inspector = getNameByid(newData.inspector) || '';
      }
      if (newData.suppDate) {
        const [year, month, day] = newData.suppDate.split('-');
        if (Number(year)) {
          newData.suppDateYear = year;
        }
        if (Number(month)) {
          newData.suppDateMonth = month;
        }
        if (Number(day)) {
          newData.suppDateDay = day;
        }
        newData.suppDate;
      }
      return newData;
    };

    const processDefinedNameForDetailData = (excelValues, defaultDetailData) => {
      const newData = { ...defaultDetailData };
      let suppDateYear = '';
      let suppDateMonth = '';
      let suppDateDay = '';
      for (let index = 0; index < excelValues?.length; index++) {
        const cellItem = excelValues[index];
        if (cellItem.rawValue === '') continue;
        switch (cellItem.definedName) {
          case 'reportType': {
            const reportTypeItem = reportTypes.find(item => item.name === cellItem.rawValue);
            if (reportTypeItem) {
              newData.reportType = reportTypeItem.code;
            } else {
              newData.reportType = '';
            }
            break;
          }
          case 'inspector': {
            const person = getIdByName(cellItem.rawValue);
            if (person) {
              newData.inspector = person;
            } else {
              newData.inspector = '';
            }
            break;
          }
          case 'suppDateYear': {
            const year = Number(cellItem.rawValue);
            if (year && year >= 1900 && year <= 9999) {
              suppDateYear = year;
            }
            break;
          }
          case 'suppDateMonth': {
            const month = Number(cellItem.rawValue);
            if (month && month >= 1 && month <= 12) {
              suppDateMonth = month;
            }
            break;
          }
          case 'suppDateDay': {
            const day = Number(cellItem.rawValue);
            if (day && day >= 1 && day <= 31) {
              suppDateDay = day;
            }
            break;
          }
          case 'startMeter': {
            newData.startMeter = cellItem.rawValue;
            break;
          }
          case 'endMeter': {
            newData.endMeter = cellItem.rawValue;
            break;
          }
          case 'segment': {
            newData.segment = cellItem.rawValue;
            break;
          }
          case 'judgmentName': {
            newData.judgmentName = cellItem.rawValue;
            break;
          }
          case 'startDate': {
            newData.startDate = cellItem.rawValue;
            break;
          }
          case 'grossWeight': {
            newData.grossWeight = cellItem.rawValue;
            break;
          }
        }
      }
      if (suppDateYear && suppDateMonth && suppDateDay) {
        newData.suppDate =
          suppDateYear + (suppDateMonth ? '-' + suppDateMonth : '') + (suppDateDay ? '-' + suppDateDay : '');
      }
      return newData;
    };

    const handlePrint = () => {
      const excelValues = excelEditer.value.getValuesByDefinedName()['0'];
      console.log(excelValues);
      const screenshots = excelEditer.value.getScreenshots();
      const detailData = processDefinedNameForDetailData(excelValues, state.detail);
      detailData.printStatus = 1;
      state.images = screenshots;
      state.isPrint = true;
      saveCertificatePrint({ entityList: [detailData] }).then(res => {
        if (res) {
          ElMessage.success('保存数据成功');
          html2canvas(document.getElementById('print-preview'), {
            dpi: 300,
            scale: 2, // 处理图片模糊
            background: '#FFFFFF',
            useCORS: true // 允许canvas画布内 可以跨域请求外部链接图片, 允许跨域请求。
          }).then(canvas => {
            const pageData = canvas.toDataURL('image/jpeg', 2.0);
            printJS({
              printable: pageData,
              style: `body { margin: 0; padding: 0; border: 0;} img { width: 100%; display: block; } @page{size: A4 portrait; overflow: hidden; }`,
              type: 'image',
              scanStyles: false,
              documentTitle: '产品合格证'
            });
          });
        }
      });
    };

    const handleGoBack = () => {
      router.go(-1);
    };

    const handleGoBack2 = () => {
      state.isPrint = false;
    };

    return {
      ...toRefs(state),
      handlePrint,
      handleGoBack,
      handleGoBack2
    };
  }
};
</script>

<style lang="scss" scoped>
.online-excel {
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  margin: 10px;
}

.online-excel__container {
  position: relative;
  width: 100%;
  height: 100%;
}

.online-excel__go-back {
  position: absolute;
  top: 6px;
  left: 6px;
  z-index: 1000;
}

.online-excel__print-btn {
  position: absolute;
  top: 6px;
  right: 90px;
  z-index: 1000;
}

#luckysheet {
  width: 100%;
  height: 100%;
}

#print-preview {
  position: relative;
  width: 794px;
  height: 1123px;
  margin: 0 auto;
  background-color: #fff;
  box-shadow: 0px 4px 12px 4px rgba(0, 0, 0, 0.08), 0px 2px 4px -2px rgba(0, 0, 0, 0.16);
  filter: drop-shadow(0px 3px 8px rgba(0, 0, 0, 0.12));
}

.online-excel__go-back2 {
  position: absolute;
  top: 0;
  right: -110px;
  display: block;
  width: 80px;
  height: 80px;
}

.online-excel__screenshots {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  padding: 0;
  margin: 0;
}
</style>
