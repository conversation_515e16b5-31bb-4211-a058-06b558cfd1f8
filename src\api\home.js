import request from '@/utils/request';

// 待办事项各分组及数量
export function getListNotNum(data) {
  return request({
    url: '/api-message/message/receiver/listnotnum',
    method: 'post',
    data
  });
}
// 某天的待办列表
export function getTodoByDay(data) {
  return request({
    url: '/api-message/message/receiver/todobyday',
    method: 'post',
    data
  });
}
// 某月的待办概览
export function getTodoByMonth(data) {
  return request({
    url: '/api-message/message/receiver/todobymonth',
    method: 'post',
    data
  });
}
// 个人任务统计
export function getStatistics(data) {
  return request({
    url: '/api-message/message/receiver/statistics',
    method: 'post',
    data
  });
}
// 即将超期待办
export function getOverdue() {
  return request({
    url: '/api-message/message/receiver/overdue',
    method: 'post'
  });
}
// 近30日用户待办处理情况
export function getBarLineApi() {
  return request({
    url: '/api-message/message/todomessage/handleTodoStatusOf30Days',
    method: 'get'
  });
}
// 近30天用户登录情况
export function getBarApi() {
  return request({
    url: '/api-user/user/sysloginlog/onlineStatusOf30Days',
    method: 'get'
  });
}
// 查询当前用户首页布局信息
export function sysLayoutInfo() {
  return request({
    url: '/api-user/user/sys_layout/info',
    method: 'post'
  });
}
// 保存当前用户首页布局信息
export function sysLayoutSave(data) {
  return request({
    url: '/api-user/user/sys_layout/save',
    method: 'post',
    data
  });
}
