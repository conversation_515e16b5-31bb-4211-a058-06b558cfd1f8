<template>
  <el-dialog
    v-model="showDialog"
    custom-class="custom-dialog"
    title="新增引用项目"
    width="720px"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <div class="dialog-header">
      <div class="header-left">
        <el-input
          ref="inputRef"
          v-model.trim="filterText"
          class="search"
          size="small"
          placeholder="请输入项目名称"
          prefix-icon="el-icon-search"
          clearable
          @keyup.enter="searchItem(filterText)"
        />
        <el-button type="primary" size="small" @click="searchItem(filterText)">查询</el-button>
      </div>
    </div>
    <div class="dialog-content">
      <el-row>
        <el-col :span="8">
          <div class="tree-container">
            <div class="tree-content">
              <el-tree
                ref="addTemplateTreeRef"
                :data="tree"
                node-key="id"
                :props="defaultProps"
                default-expand-all
                :expand-on-click-node="false"
                :highlight-current="true"
                draggable
                class="leftTree"
                @node-click="clickNode"
              >
                <template #default="{ node }">
                  <span>{{ node.label }}</span>
                </template>
              </el-tree>
            </div>
          </div>
        </el-col>
        <el-col :span="16">
          <div v-loading="loading" class="list-container">
            <div v-for="(item, index) in newTreeDetail" :key="index" class="item-content" @click="changeRadio(item)">
              <div class="item-box">
                <el-radio v-model="radioId" :label="item.id" :disabled="item.disable" @change="changeRadio(item)">{{
                  ''
                }}</el-radio>
                <span class="title">{{ item.name }}</span>
              </div>
            </div>
            <el-empty v-if="newTreeDetail.length === 0" :image="emptyImg" description="暂无数据" />
          </div>
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent>确定选择</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, getCurrentInstance, nextTick } from 'vue';
import { getCapabilityUplist } from '@/api/user';
// import _ from 'lodash'
import { ElMessage } from 'element-plus';
import emptyImg from '@/assets/img/empty-data.png';
// import { useRoute } from 'vue-router'
// import { formatPaginationList } from '@/utils/formatJson'

export default {
  name: 'AddTemplateRef',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    },
    tree: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'selectData'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // const lodash = inject('_')
    const datas = reactive({
      showDialog: false,
      filterText: '',
      inputRef: ref(),
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      treeDetail: [],
      newTreeDetail: [],
      oldDatas: [],
      loading: false,
      radioId: '',
      radioData: null,
      addTemplateTreeRef: ref()
    });

    watch(
      () => props.show,
      newValue => {
        if (newValue) {
          // console.log(props)
          datas.showDialog = newValue;
          datas.radioData = null;
          datas.radioId = '';
          datas.oldDatas = props.data;
          if (props.show && props.tree && props.tree.length > 0) {
            nextTick(() => {
              // console.log(datas.addTemplateTreeRef)
              datas.addTemplateTreeRef.setCurrentKey(props.tree[0].id);
              datas.inputRef.focus();
            });
            proxy.getCapabilityList(props.tree[0].id, props.tree[0].materialCategoryCode);
          }
        }
      },
      { deep: true }
    );

    // 过滤树节点
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    // 点击树节点
    const clickNode = (data, node) => {
      proxy.getCapabilityList(data.id, data.materialCategoryCode);
    };

    // 确定选择
    const dialogSuccess = () => {
      if (!datas.radioData) {
        ElMessage.warning('请选择要关联的检测项目');
        return false;
      }
      datas.showDialog = false;
      context.emit('selectData', datas.radioData);
      // context.emit('close', 'add')
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };

    // 选择检测项目-radio
    const changeRadio = item => {
      // console.log(item)
      if (item.disable === true) {
        return false;
      }
      datas.radioId = item.id;
      datas.radioData = item;
    };

    // 查询
    const searchItem = value => {
      if (value) {
        datas.newTreeDetail = datas.treeDetail.filter(item => {
          return item.name.indexOf(value) !== -1;
        });
      } else {
        datas.newTreeDetail = datas.treeDetail;
      }
    };

    return { ...toRefs(datas), emptyImg, searchItem, dialogSuccess, close, filterNode, clickNode, changeRadio };
  },
  methods: {
    getCapabilityList(id, materialCategoryCode) {
      // 获取检测项目list
      const vm = this;
      vm.loading = true;
      getCapabilityUplist(id, materialCategoryCode).then(response => {
        vm.loading = false;
        if (response !== false && response.data.code === 200) {
          const { data } = response.data;
          vm.treeDetail = data;
          if (vm.filterText && data.length > 0) {
            vm.newTreeDetail = data.filter(item => {
              return item.name.indexOf(vm.filterText) !== -1;
            });
          } else {
            vm.newTreeDetail = data;
          }
          // console.log(vm.newTreeDetail)
          // console.log(vm.oldDatas)
          // const currentList = JSON.parse(JSON.stringify(vm.newTreeDetail))
          if (vm.oldDatas && vm.oldDatas.length > 0) {
            vm.oldDatas.forEach(od => {
              vm.newTreeDetail.forEach(ntd => {
                if (ntd.id === od.refCapabilityId) {
                  ntd.disable = true;
                }
              });
            });
          }
        }
      });
    }
  }
};
</script>
<style lang="scss">
@import '@/styles/dialog.scss';
</style>
<style lang="scss" scoped>
@import '@/styles/tree.scss';

.dialog-content {
  margin-bottom: 0;
  padding-bottom: 20px;
  .tree-container {
    .tree-content {
      height: calc(100vh - 540px);
      overflow-y: auto;
      padding-left: 0;
    }
  }
  .list-container {
    height: calc(100vh - 500px);
    overflow-y: auto;
    padding: 10px 20px 10px;
    .item-content {
      padding: 0 !important;
      margin: 0 !important;
      height: 44px;
      line-height: 44px;
      .el-radio {
        margin-right: 10px;
      }
      .el-radio.is-checked + .title {
        color: $tes-primary;
      }
    }
  }
}
</style>
