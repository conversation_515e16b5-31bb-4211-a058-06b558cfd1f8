<template>
  <el-dialog
    v-model="dialogShow"
    top="5vh"
    title="编辑"
    width="950px"
    :close-on-click-modal="false"
    @close="cancelDialog()"
  >
    <el-form
      ref="ruleForm"
      v-loading="dialogLoading"
      :model="formData"
      label-position="right"
      class="form-class overflow-y-auto pr-2"
    >
      <el-row>
        <el-col v-for="field in pageViewAll" :key="field.fieldKey" :span="12">
          <el-form-item
            :label="`${field.fieldName}：`"
            :label-width="`${labelWidth[field.fieldKey] || 110}px`"
            :prop="field.fieldKey"
            :rules="{
              required: false,
              message: `请输入${field.fieldName}`,
              trigger: 'change'
            }"
          >
            <el-input
              v-if="editFieldAll[field.fieldKey]?.type == 'text'"
              v-model="formData[field.fieldKey]"
              clearable
              :placeholder="`请输入${field.fieldName}`"
            />
            <el-date-picker
              v-if="editFieldAll[field.fieldKey]?.type == 'date'"
              v-model="formData[field.fieldKey]"
              class="!w-full"
              type="date"
              clearable
              :placeholder="`请选择${field.fieldName}`"
            />
            <el-input
              v-if="editFieldAll[field.fieldKey]?.type == 'reelCodeText'"
              v-model="formData[field.fieldKey]"
              class="!cursor-pointer"
              readonly
              :placeholder="`请选择${field.fieldName}`"
              @click="onSelectMaterialDialogVisible(true)"
            >
              <template #append>
                <div class="w-[50px] text-center" @click="onSelectMaterialDialogVisible(true)">
                  <el-icon>
                    <Search />
                  </el-icon>
                </div>
              </template>
            </el-input>
            <el-input
              v-if="editFieldAll[field.fieldKey]?.type == 'textarea'"
              v-model="formData[field.fieldKey]"
              clearable
              type="textarea"
              :placeholder="`请输入${field.fieldName}`"
            />
            <el-row v-if="editFieldAll[field.fieldKey]?.type == 'number'" :gutter="10">
              <el-col :span="16">
                <el-input-number
                  v-model="formData[field.fieldKey]"
                  :min="0"
                  controls-position="right"
                  :placeholder="`请输入${field.fieldName}`"
                  style="width: 100%"
                  @change="onChangeNumberValue(field.fieldKey)"
                />
              </el-col>
              <el-col :span="8" class="text-center">
                <el-select
                  v-model="formData.inputWarehouseUnit"
                  filterable
                  size="small"
                  disabled
                  :placeholder="`请选择`"
                  class="w-full"
                >
                  <el-option v-for="(val, key) in dictionaryAll['5']?.enable" :key="key" :label="val" :value="key" />
                </el-select>
              </el-col>
            </el-row>
            <el-select
              v-if="editFieldAll[field.fieldKey]?.type == 'select'"
              v-model="formData[field.fieldKey]"
              filterable
              size="small"
              clearable
              :placeholder="`请选择${field.fieldName}`"
              class="w-full"
            >
              <el-option
                v-for="(val, key) in dictionaryAll[editFieldAll[field.fieldKey].code]?.enable"
                :key="key"
                :label="val"
                :value="key"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="items-baseline">
        <el-col :span="3">小盘起始序列号：</el-col>
        <el-col :span="4">
          <el-form-item
            label=""
            label-width="0"
            prop="startNumber"
            :rules="{
              required: formData.endNumber || formData.endNumber == 0,
              message: '请输入起始序号',
              trigger: 'change'
            }"
          >
            <el-input-number
              v-model="formData.startNumber"
              controls-position="right"
              :max="formData.endNumber"
              :min="1"
              placeholder="起"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="1" class="text-center"> ~ </el-col>
        <el-col :span="4">
          <el-form-item label="" label-width="0" prop="endNumber">
            <el-input-number
              v-model="formData.endNumber"
              controls-position="right"
              :min="formData.startNumber"
              placeholder="止"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-for="(item, index) in formData.smallSerialNumber" :key="index" class="items-center mb-1">
        <el-col :span="4" :offset="3">
          <el-input v-model="item[0]" disabled placeholder="起" size="small" />
        </el-col>
        <el-col :span="1" class="text-center"> ~ </el-col>
        <el-col :span="4">
          <el-input v-model="item[1]" disabled placeholder="止" size="small" />
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button :loading="dialogLoading" @click="cancelDialog()">取消</el-button>
      <el-button :loading="dialogLoading" type="primary" @click="handleSubmit()">确定</el-button>
    </template>
  </el-dialog>
  <MaterialItem
    :dialog-visiable="selectMaterialDialogVisible"
    :detail-data="formData"
    :is-add="false"
    @selectRow="getSelectedMaterial"
    @closeDialog="onSelectMaterialDialogVisible(false)"
  />
</template>
<script>
import { reactive, watch, ref, toRefs } from 'vue';
// import UserTag from '@/components/UserTag';
import { saveCertificatePrint } from '@/api/certificate-export';
import { calculateGrossWeight } from '@/api/mas';
import { getNameByid } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons';
import MaterialItem from '@/components/BusinessComponents/MaterialItem';
// import { certificateDecide } from '@/api/raw-certificate';
export default {
  name: 'DialogEditCertificate',
  components: { MaterialItem, Search },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    pageView: {
      type: Object,
      default: () => ({})
    },
    selectRow: {
      type: Array,
      default: () => []
    },
    dictionary: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      formData: {
        smallSerialNumber: []
      },
      dictionaryAll: {
        JHDW: {
          all: {},
          enable: {}
        },
        5: {
          all: {},
          enable: {}
        }
      },
      pageViewAll: {},
      dialogLoading: false,
      labelWidth: {
        //  allowedOperatingTemperature: 150,
        // suppDate: 150,
        // reelDesc: 150
      },

      editFieldAll: {
        projectName: { type: 'text' },
        serialNumber: { type: 'text' },
        inboundLength: { type: 'number' },
        customerQuantity: { type: 'number' },
        customerModel: { type: 'text' },
        customerSpecifications: { type: 'text' },
        customerVoltage: { type: 'text' },
        customerBarcodeNumber: { type: 'text' },
        allowedOperatingTemperature: { type: 'text' },
        // receivingInstitution: { type: 'text' },
        grossWeight: { type: 'text' },
        meterSpan: { type: 'text' },
        reelNo: { type: 'text' },
        mateName: { type: 'text' },
        // 收货单位
        receivingInstitution: { type: 'text' },
        judgmentName: { type: 'text' },
        // 盘具物料号
        reelCode: { type: 'reelCodeText' },
        // 盘具描述（线盘结构
        reelDesc: { type: 'text' },
        // 3C证书编号
        certificateNumber: { type: 'text' },
        // 客户型号规格
        prodType: { type: 'text' },
        // 报告编号(可修改默认同报告编号)
        // inspectionCode: { type: 'text' },
        // 检验日期
        startDate: { type: 'date' },
        // 检验员
        inspector: { type: 'text' },
        remark: { type: 'textarea' }
      },
      reportType: 1, // 0 合格， 1 不合格
      progress: 0,
      dialogShow: false,
      selectMaterialDialogVisible: false,
      ruleForm: ref()
    });

    const cancelDialog = value => {
      state.dialogShow = false;
      context.emit('closeDialog', value);
    };

    const submitJudgement = () => {};

    // 显示选择物料对话框
    const onSelectMaterialDialogVisible = visible => {
      state.selectMaterialDialogVisible = visible;
    };

    // 选择物料
    const getSelectedMaterial = material => {
      state.formData.reelCode = material.materialCode;
      state.formData.reelDesc = material.materialOriginalName;
      onSelectMaterialDialogVisible(false);
      onCalculateGrossWeight();
    };

    watch(
      () => props.dialogVisible,
      newValue => {
        state.dialogShow = newValue;
        if (newValue) {
          state.formData = props.selectRow[0] || { smallSerialNumber: [] };
          state.formData.inboundLength = Number(state.formData.inboundLength);
          state.formData.customerQuantity = Number(state.formData.customerQuantity);
          state.pageViewAll = props.pageView
            .filter(item => state.editFieldAll[item.fieldKey])
            .map(item => ({
              ...item,
              fieldName: item.fieldKey === 'grossWeight' ? `${item.fieldName}kg` : item.fieldName
            }));
          state.dictionaryAll = props.dictionary || {
            JHDW: {
              all: {},
              enable: {}
            }
          };
        }
      }
    );
    const handleSubmit = async () => {
      state.ruleForm.validate().then(async valid => {
        if (valid) {
          const params = {
            ...state.selectRow,
            ...JSON.parse(JSON.stringify(state.formData))
          };
          if (state.formData.startNumber && !state.formData.endNumber) {
            params.smallSerialNumber.push([Number(state.formData.startNumber), Number(state.formData.startNumber)]);
          } else if (state.formData.startNumber) {
            params.smallSerialNumber.push([Number(state.formData.startNumber), Number(state.formData.endNumber)]);
          }
          const { data } = await saveCertificatePrint({ entityList: [params] }).finally((state.dialogLoading = false));
          if (data) {
            ElMessage.success('编辑成功!');
            cancelDialog(true);
          }
        }
      });
    };

    const onChangeNumberValue = fieldKey => {
      if (fieldKey === 'customerQuantity') {
        onCalculateGrossWeight();
      }
    };

    const onCalculateGrossWeight = async () => {
      const { customerQuantity, reelCode } = state.formData;

      // isDivide 是否割线分盘  1：是 0：否
      const { materialNo, isDivide, realReelLength } = props.selectRow[0];
      const { data } = await calculateGrossWeight({
        quantity: isDivide ? realReelLength : customerQuantity,
        reelCode,
        materialNo
      });
      state.formData.grossWeight = data.code == 200 ? data.data : null;
    };

    return {
      ...toRefs(state),
      cancelDialog,
      handleSubmit,
      getNameByid,
      formatDate,
      submitJudgement,
      onSelectMaterialDialogVisible,
      getSelectedMaterial,
      onCalculateGrossWeight,
      onChangeNumberValue
    };
  }
};
</script>
<style lang="scss" scoped>
.form-class {
  max-height: 600px;
}
:deep(.el-input-group__append) {
  padding: 0 !important;
}
</style>
