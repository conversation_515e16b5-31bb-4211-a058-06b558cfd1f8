// import $ from 'jquery'

// 模板中关键参数的标准读取系统中的标准
export const getTemplateStandard = (standardVoList, isStandardCustom) => {
  if (isStandardCustom === 1) {
    // 自定义标准
    customStandard();
  } else {
    if (document.getElementById(`customOrNot`)) {
      document.getElementById(`customOrNot`).value = '1';
    }
    if (standardVoList?.length > 0) {
      standardVoList.forEach(item => {
        keyAddCriteria(item);
      });
    }
  }
};
// 自定义标准
function customStandard() {
  if (document.getElementById(`customOrNot`)) {
    document.getElementById(`customOrNot`).value = '0';
  }
}

function keyAddCriteria(item) {
  if (document.getElementById(`standard-${item.templateKey}`)) {
    // 标准获取技术要求的值
    document.getElementById(`standard-${item.templateKey}`).value = item.standardRequirement;
    // 最大值
    document.getElementById(`standardMax-${item.templateKey}`).value = item.maxNum;
    // 最大值包含
    document.getElementById(`standardMaxContain-${item.templateKey}`).value = item.maxseleced;
    // 最小值
    document.getElementById(`standardMin-${item.templateKey}`).value = item.minNum;
    // 最小值包含
    document.getElementById(`standardMinContain-${item.templateKey}`).value = item.minselected;
  }
}
