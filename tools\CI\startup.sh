#!/bin/sh

echo "Starting up..."
echo "Current directory: $(pwd)"
echo "Contents of current directory: $(ls -la)"

jq --version

JSON=$(cat /usr/share/nginx/html/serverConfig.json)
if [ ! -z "$JSON" ]; then
  echo $JSON

  if [ ! -z "$VITE_HOME_PAGE" ]; then
    JSON=$(echo $JSON | jq --arg val "$VITE_HOME_PAGE" '. + {"VITE_HOME_PAGE": $val}')
  fi

  if [ ! -z "$VITE_FLASH_TABLE" ]; then
    JSON=$(echo $JSON | jq --arg val "$VITE_FLASH_TABLE" '. + {"VITE_FLASH_TABLE": $val}')
  fi

  echo $JSON

  echo $JSON >/usr/share/nginx/html/serverConfig.json
fi

# 检查是否存在挂载的 default.conf 文件
if [ -f /etc/nginx/conf.d/default.custom ] && [ ! -L /etc/nginx/conf.d/default.custom ]; then
  echo "使用挂载的 default.conf 文件"
  CONFIG_FILE="/etc/nginx/conf.d/default.custom"
else
  echo "使用默认的 default.template 文件"
  CONFIG_FILE="/etc/nginx/conf.d/default.template"
fi

echo "Using config file: $CONFIG_FILE"
echo "Config file contents:"
cat $CONFIG_FILE

# 执行环境变量替换
echo "Performing environment variable substitution..."
envsubst '${API_GATEWAY} ${MINIO_HOST} ${WEBSOCKET_HOST} ${MANAGE_HOST} ${QMS_HOST} ${TEMPLATE_EDITOR_HOST}' <$CONFIG_FILE >/etc/nginx/conf.d/default.conf.tmp
mv /etc/nginx/conf.d/default.conf.tmp /etc/nginx/conf.d/default.conf

echo "Final config file contents:"
cat /etc/nginx/conf.d/default.conf


nginx -g "daemon off;"
