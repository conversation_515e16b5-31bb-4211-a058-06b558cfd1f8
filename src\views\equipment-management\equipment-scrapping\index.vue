<template>
  <!-- 设备报废 -->
  <ListLayout :has-button-group="getPermissionBtn('newApplicationForm')">
    <template #search-bar>
      <el-form ref="editFromRef" :inline="true" :model="searchForm" @submit.prevent>
        <el-form-item prop="condition">
          <el-input
            v-model="searchForm.condition"
            v-focus
            v-trim
            size="large"
            maxlength="100"
            placeholder="请输入申请单号/设备编号/设备名称/型号规格"
            class="ipt-360"
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter="getTableList"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="large" @click="getTableList" @keyup.prevent @keydown.enter.prevent
            >查询</el-button
          >
          <el-button size="large" @click="reset" @keyup.prevent @keydown.enter.prevent>重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button type="primary" size="large" icon="el-icon-plus" @click="handleRow('add')">新增申请单</el-button>
    </template>
    <template #radio-content>
      <el-radio-group v-model="searchForm.status" size="small" style="float: left" @change="changeStatus">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button :label="0">待提交</el-radio-button>
        <el-radio-button :label="1">审批中</el-radio-button>
        <el-radio-button :label="2">已完成</el-radio-button>
      </el-radio-group>
    </template>
    <el-table
      ref="forecastRef"
      :key="tableKey"
      v-loading="tableLoading"
      :data="tableList"
      class="dark-table base-table format-height-table"
      fit
      border
      height="auto"
      highlight-current-row
      size="medium"
      @header-dragend="drageHeader"
    >
      <el-table-column label="申请单号" prop="applyNo" :min-width="colWidth.orderNo" align="left" show-overflow-tooltip>
        <template #default="{ row }">
          <div>
            <div v-if="row.applyNo" v-copy="row.applyNo" class="blue-color" @click="handleRow('check', row)">
              {{ row.applyNo }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="设备编号" prop="deviceNumber" :min-width="colWidth.equipmentName">
        <template #default="{ row }">
          <span>{{ row.deviceNumber || '' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="设备名称" prop="name" :min-width="colWidth.equipmentName" align="center">
        <template #default="{ row }">
          <div>{{ row.name || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="型号规格" prop="model" :min-width="colWidth.model">
        <template #default="{ row }">
          <span>{{ row.model || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" align="left" :min-width="colWidth.status" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="statusType[row.status]">{{
            statusJson[row.status] || '--'
          }}</el-tag>
          <span v-if="row.status === 3" style="font-size: 12px; color: #f56c6c">（不通过）</span>
        </template>
      </el-table-column>
      <el-table-column label="申请人" prop="applyBy" :width="colWidth.people">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.applyBy) || row.applyBy || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="申请日期" prop="applyDate" :width="colWidth.date">
        <template #default="{ row }">
          <span>{{ row.applyDate ? formatDate(row.applyDate) : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="批准日期" prop="approveDate" :width="colWidth.date">
        <template #default="{ row }">
          <span>{{ row.approveDate ? formatDate(row.approveDate) : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="colWidth.operation" fixed="right" class-name="fixed-right">
        <template #default="{ row }">
          <span
            v-if="(row.status === 0 || row.status === 3) && row.createBy === accountId"
            class="blue-color"
            @click="handleRow('edit', row)"
            >编辑</span
          >
          <span
            v-if="
              (row.status === 0 || row.status === 3) &&
              row.createBy === accountId &&
              getPermissionBtn('DeleteDeviceScrap')
            "
            class="blue-color"
            @click="deleteDetail(row)"
            >删除</span
          >
          <span
            v-else-if="row.status === 1 && getPermissionBtn('approvalEquipmentScrap')"
            class="blue-color"
            @click="handleRow('approve', row)"
            >审批</span
          >
          <span v-else class="blue-color" @click="handleRow('check', row)">查看</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getTableList"
    />
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import UserTag from '@/components/UserTag';
import Pagination from '@/components/Pagination';
import { getScrappingList, deleteApply } from '@/api/scrapping';
import { formatDate } from '@/utils/formatTime';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import ListLayout from '@/components/ListLayout';
import { colWidth } from '@/data/tableStyle';
import router from '@/router/index.js';
import { useRoute } from 'vue-router';
import { getLoginInfo } from '@/utils/auth';

export default {
  name: 'EquipmentScrapping',
  components: { Pagination, ListLayout, UserTag },
  setup() {
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    const state = reactive({
      tableList: [],
      mangeList: [],
      dialogVisible: false,
      dialogEquipment: false,
      statusType: {
        // 申请单状态对应的tag类型
        2: 'success',
        1: 'warning',
        0: 'info',
        3: 'info'
      },
      showDrawer: false,
      planId: '', // 计量计划id
      rowType: '', // 计量计划抽屉类型
      dialogPlanType: '', // 计量计划弹出窗
      total: 0,
      statusJson: {
        0: '待提交',
        1: '审批中',
        2: '已完成',
        3: '待提交'
      },
      accountId: getLoginInfo().accountId,
      deviceIdList: [], // 设备id列表
      editFromRef: ref(),
      searchFromRef: ref(),
      showS: false,
      tableLoading: false,
      tableKey: 0,
      listQuery: {
        page: 1,
        limit: 20
      },
      searchForm: {
        status: route.query.status ? route.query.status : ''
      },
      equipmentJson: {}
    });
    // 重置
    function reset() {
      state.listQuery.page = 1;
      state.listQuery.limit = 20;
      state.searchForm = {};
      state.deviceIdList = [];
      getTableList();
    }
    // 列表查询
    const getTableList = query => {
      const params = { ...state.searchForm };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.tableLoading = true;
      getScrappingList(params).then(res => {
        state.tableLoading = false;
        if (res !== false) {
          state.total = res.data.data.totalCount;
          state.tableList = res.data.data.list;
        }
      });
    };
    getTableList();
    // 关闭弹出窗
    const closeDialog = val => {
      state.dialogVisible = false;
      if (val.isRefresh) {
        getTableList(state.listQuery);
      }
    };
    const closeEquipment = val => {
      state.dialogEquipment = false;
      if (val.isRefresh) {
        getTableList(state.listQuery);
      }
    };
    const closeDrawer = val => {
      state.showDrawer = false;
      if (val.isRefresh) {
        getTableList(state.listQuery);
      }
    };
    // 列表选择
    const handleSelectionChange = val => {
      state.deviceIdList = val.map(item => {
        return item.id;
      });
    };
    // 切换tab
    const changeStatus = val => {
      state.listQuery = {
        page: 1,
        limit: 20
      };
      state.total = 0;
      getTableList();
    };
    // 编辑、查看、新增
    const handleRow = (type, row) => {
      const routeQuery = {
        status: state.searchForm.status
      };
      if (type !== 'add') {
        routeQuery.id = row.id;
      }
      router.push({
        path: '/equipment/detailScrapping',
        query: routeQuery
      });
    };
    // 删除
    const deleteDetail = row => {
      deleteApply(row.id).then(res => {
        if (res) {
          proxy.$message.success(res.data.message);
          getTableList();
        }
      });
    };

    return {
      ...toRefs(state),
      getPermissionBtn,
      deleteDetail,
      drageHeader,
      getNameByid,
      formatDate,
      closeDialog,
      closeEquipment,
      closeDrawer,
      handleSelectionChange,
      getTableList,
      handleRow,
      reset,
      colWidth,
      changeStatus
    };
  }
};
</script>
<style lang="scss" scoped>
.label-type {
  margin-bottom: 0px;
  // min-width: 200px;
}
</style>
