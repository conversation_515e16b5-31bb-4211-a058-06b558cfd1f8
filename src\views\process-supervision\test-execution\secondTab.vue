<template>
  <div class="originalRecord">
    <div class="title"><span class="title-line" /> 仪器设备使用记录</div>
    <el-table :data="tableData" class="dark-table base-table" style="width: 100%">
      <el-table-column prop="date" label="仪器设备" :min-width="250" show-overflow-tooltip>
        <template #default="scope">
          <ul>
            <li>
              <span>编号：{{ scope.row.deviceNumber || '--' }}</span>
            </li>
            <li>
              <span class="nowarp">名称：{{ scope.row.deviceName || '--' }}</span>
            </li>
            <li>
              <span>型号规格：{{ scope.row.model || '--' }}</span>
            </li>
          </ul>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="检定有效期" :width="250" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="value">{{ row.validBeginDate || '--' }}</span> ~
          <br />
          <span class="value">{{ row.validEndDate || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="不确定度" :width="120" prop="measurementDeviation" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">
            {{ row.measurementDeviation || '--' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="使用情况" :width="180" show-overflow-tooltip>
        <template #default="{ row }">
          <ul>
            <li>
              <span>使用人：</span><span class="value">{{ getNameByid(row.ownerId) || '--' }}</span>
            </li>
            <li>
              <span>时间：</span>
              <span class="value">{{ row.deviceUseTime || '--' }}</span>
            </li>
            <li>
              <span>状态：</span>
              <span class="value">{{ status[row.deviceStatusBefore] || '--' }}</span>
              <span class="fgline">/</span>
              <span class="value">{{ status[row.deviceStatusAfter] || '--' }}</span>
            </li>
          </ul>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" :width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="remark">{{ row.remark || '--' }}</div>
        </template>
      </el-table-column>
    </el-table>
    <div class="title secondTitle"><span class="title-line" /> 试验图片</div>
    <el-table v-loading="imgLoading" :data="tablePicture" class="dark-table" style="width: 100%">
      <el-table-column prop="date" label="名称" :min-width="250" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.name }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="isReport" label="生成报告" :width="250">
        <template #default="{ row }">
          <span>{{ row.isReport == 1 ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="描述" :width="300" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.description }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="图片" :width="200">
        <template #default="{ row }">
          <el-image
            v-if="row.imageUrl"
            class="curson"
            style="width: 40px; height: 40px"
            :src="row.imageUrl"
            fit="cover"
            @click="row.showViewer = true"
          />
          <el-image-viewer
            v-if="row.showViewer"
            :src="row.imageUrl"
            :url-list="[row.imageUrl]"
            @close="
              () => {
                row.showViewer = false;
              }
            "
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { reactive, toRefs, watch, ref } from 'vue';
import { getDeviceList, imgrecordList } from '@/api/sampleItemTest';
import { getNameByid } from '@/utils/common';
// import router from '@/router/index.js'
// import { useRoute } from 'vue-router'
// import { samplesDetails } from '@/api/order'
// import { formatDate } from '@/utils/formatTime'
export default {
  name: 'SecondTab',
  props: {
    jsonData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    activeName: {
      type: String,
      default: ''
    }
  },
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    const state = reactive({
      dialogUpload: false,
      imgLoading: false,
      status: {
        0: '异常',
        1: '正常',
        3: '停用'
      },
      experimentId: props.jsonData.experimentId,
      formDataUpload: {
        isReport: '0'
      },
      tableData: [],
      tablePicture: [],
      upload: ref(0)
    });
    watch(props, () => {
      state.experimentId = props.jsonData.experimentId;
      if (props.activeName === '2') {
        getList(props.jsonData.experimentId);
      }
    });
    const getList = experimentId => {
      getDeviceList(experimentId).then(res => {
        if (res.data.code === 200) {
          state.tableData = res.data.data;
        }
      });
      state.imgLoading = true;
      imgrecordList(experimentId).then(res => {
        state.imgLoading = false;
        if (res.data.code === 200) {
          state.tablePicture = res.data.data;
        }
      });
    };
    return { ...toRefs(state), props, getList, getNameByid };
  },
  computed: {},
  created() {}
};
</script>

<style lang="scss" scoped>
.dark-table {
  .remark {
    width: 100px;
    word-break: break-all;
    overflow: hidden;
  }
}
.curson {
  cursor: pointer;
}
.uploadBtn {
  position: absolute;
  right: 0;
  top: -5px;
}
.uploadBtn2.el-button {
  color: $tes-primary;
}
ul {
  margin: 0;
  padding: 0;
}
.title {
  display: flex;
  align-items: center;
  color: $tes-font;
  font-size: 16px;
  text-align: left;
  margin-bottom: 10px;
}
.secondTitle {
  margin-top: 40px;
}
.span {
  display: inline-block;
}
li {
  list-style: none;
}
.originalRecord {
  margin: 20px;
  overflow: hidden;
}
:deep(.el-form .el-form-item) {
  margin-bottom: 25px;
}
:deep(.el-upload__tip) {
  position: absolute;
  top: -5px;
  left: 110px;
  color: $tes-font2;
}
:deep(.el-radio) {
  margin-right: 150px;
}
:deep(.el-table__body-wrapper) {
  margin-bottom: 0;
}
</style>
