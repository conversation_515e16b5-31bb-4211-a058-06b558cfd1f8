<template>
  <el-dialog
    v-model="dialogShow"
    :title="dialotTitle[type]"
    :close-on-click-modal="false"
    width="480px"
    @close="handleClose"
  >
    <el-form
      v-if="dialogShow"
      ref="ruleForm"
      v-loading="dialogLoading"
      :model="formData"
      label-position="right"
      label-width="100px"
      size="small"
    >
      <el-form-item
        label="计量负责人："
        prop="responsibleBy"
        :rules="{ required: true, message: '请选择计量负责人', trigger: 'change' }"
      >
        <el-select v-model="formData.responsibleBy" clearable filterable placeholder="请选择计量负责人">
          <el-option v-for="item in nameList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item
        label="计量单位："
        prop="org"
        :rules="{ required: true, message: '请输入计量单位', trigger: 'change' }"
      >
        <el-input
          ref="inputRef"
          v-model="formData.org"
          v-trim
          maxlength="100"
          type="text"
          placeholder="请输入计量单位"
        />
      </el-form-item>
      <el-form-item
        label="计划日期："
        prop="planDate"
        :rules="{ required: true, message: '请选择计划开始结束日期', trigger: 'change' }"
      >
        <el-date-picker
          v-model="formData.planDate"
          type="daterange"
          :disabled-date="disabledDate"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, ref, toRefs, watch, getCurrentInstance, nextTick } from 'vue';
import { useStore } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
import { addPlan, editPlan } from '@/api/PlannedMeasurementManagement';
import { formatDate } from '@/utils/formatTime';
export default {
  name: 'DialogPlan',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dialogPlanType: {
      type: String,
      default: ''
    },
    deviceIdList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const state = reactive({
      type: '', // 弹出窗类型
      dialogLoading: false, // 弹出窗loading
      formData: {
        responsibleBy: getLoginInfo().accountId,
        planDate: []
      }, // 表单数据
      allEquipmentList: [],
      inputRef: ref(),
      deviceIdList: [], // 勾选的设备列表id
      statusJson: {}, // 维修状态类型
      detailData: {}, // 表单详情
      dialogShow: false,
      disabledDate(time) {
        return time.getTime() < Date.now();
      },
      ruleForm: ref(),
      pageType: '', // 页面类型
      listLoading: false,
      nameList: store.common.nameList,
      dialotTitle: {
        add: '新增计划',
        edit: '编辑计划'
      },
      currentAccountId: getLoginInfo().accountId
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.deviceIdList = props.deviceIdList;
        state.type = props.dialogPlanType;
        if (state.type === 'edit') {
          state.formData = JSON.parse(JSON.stringify(props.detailData));
          state.formData.planDate = [state.formData.startDate, state.formData.endDate];
        } else {
          state.formData = {
            responsibleBy: getLoginInfo().accountId,
            planDate: []
          };
        }
        nextTick(() => {
          state.inputRef.focus();
        });
      }
    });
    const onSubmit = () => {
      proxy.$refs['ruleForm'].validate(valid => {
        if (valid) {
          const params = {
            responsibleBy: state.formData.responsibleBy,
            org: state.formData.org,
            startDate: formatDate(state.formData.planDate[0]),
            endDate: formatDate(state.formData.planDate[1])
          };
          state.dialogLoading = true;
          if (state.type === 'add') {
            params.deviceIdList = state.deviceIdList;
            addPlan({ ...params }).then(res => {
              state.dialogLoading = false;
              if (res) {
                proxy.$message.success(res.data.message);
                context.emit('closeDialog', { isRefresh: true });
              }
            });
          } else {
            params.id = state.deviceIdList.toString();
            editPlan({ ...params }).then(res => {
              state.dialogLoading = false;
              if (res) {
                proxy.$message.success(res.data.message);
                context.emit('closeDialog', { isRefresh: true });
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog', { isRefresh: false });
    };
    const handleRadioResult = val => {
      if (val === 'Scrapped') {
        state.formData.isRemeasured = 0;
        state.formData.description = '';
      }
    };
    return { ...toRefs(state), onSubmit, handleClose, handleRadioResult };
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-range-editor--small.el-input__inner) {
  width: 100%;
}
:deep(.el-select--small) {
  width: 100%;
}
</style>
