<template>
  <el-dialog
    v-model="dialogShow"
    :before-close="beforeClose"
    :close-on-click-modal="false"
    :title="isAdd ? '创建模板' : '编辑模板'"
    :width="640"
  >
    <el-form v-if="dialogShow" ref="refForm" :model="formData" :rules="rules" class="excel-dialog" label-position="top">
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="模板展示方式：" prop="showType">
            <el-radio-group v-model="formData.showType" size="small" class="item-radio">
              <el-radio :value="0" :label="0" :disabled="!isAdd" border>竖版</el-radio>
              <el-radio :value="1" :label="1" :disabled="!isAdd" border>横版</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否含有标准：" prop="isUseStandard">
            <el-radio-group v-model="formData.isUseStandard" size="small" class="item-radio">
              <el-radio :value="1" :label="1" :disabled="!isAdd" border>是</el-radio>
              <el-radio :value="0" :label="0" :disabled="!isAdd" border>否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="版本号：" prop="version">
            <el-input v-model="formData.version" :disabled="!isAdd" placeholder="请输入版本号" autocomplete="off" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="文件编号：" prop="fileNo">
            <el-input v-model="formData.fileNo" placeholder="请输入文件编号" autocomplete="off" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="coreNumber">
            <span>模板中动态分组数：</span>
            <el-tooltip
              class="item"
              effect="dark"
              content="需要模板支持多分组，0代表不支持，1及以上支持动态分组"
              placement="top"
            >
              <i class="el-icon-warning-outline" />
            </el-tooltip>
            <el-input
              v-model.trim="formData.coreNumber"
              type="number"
              min="0"
              max="999"
              placeholder="请输入0及以上正整数"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单个分组中试件个数：" prop="groupNumber">
            <el-input
              v-model.trim="formData.groupNumber"
              type="number"
              min="1"
              max="9999"
              placeholder="请输入正整数"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="版本描述：">
            <el-input v-model="formData.description" placeholder="请输入版本描述" type="textarea" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="关联项目：">
            <span>{{ capabilityNumber + ' ' + capabilityName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item v-if="isAdd" prop="id">
            <el-upload
              ref="uploadRef"
              class="upload-demo"
              :action="uploadurl"
              :before-upload="handleHtmlUpload"
              :on-success="handleSuccess"
              :headers="headerconfig"
              :with-credentials="true"
              accept=".html"
              :limit="1"
              :file-list="fileList"
            >
              <el-button size="small" icon="el-icon-upload" type="primary">选择文件</el-button>
              <span class="el-upload__tip">只能上传 *.html 格式的文件</span>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancle()">取 消</el-button>
        <el-button type="primary" @click="handleSubmit()">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref } from 'vue';
import { useRoute } from 'vue-router';
import { getPermissionBtn } from '@/utils/common';
import { savecapabilitytemplate, updatecapabilitytemplate } from '@/api/excel';
import { getGenerator } from '@/api/businessCode';
import { capabilityAttachmentUploadUrl } from '@/api/uploadAction';
import { ElMessage } from 'element-plus';
import { isInteger, isInteger1 } from '@/utils/validate';
import { getToken } from '@/utils/auth';
import emptyImg from '@/assets/img/empty-template.png';

export default {
  name: 'DialogTemplateUpload',
  components: {},
  props: {
    dialogVisiable: {
      type: Boolean,
      default: false
    },
    isAdd: {
      type: Boolean,
      default: false
    },
    formInfo: {
      type: Object,
      default: () => {}
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance();
    const route = useRoute();
    watch(
      () => props.dialogVisiable,
      newValue => {
        state.dialogShow = newValue;
        if (newValue) {
          state.isAdd = props.isAdd;
          if (state.isAdd) {
            state.formData = {
              groupNumber: 0,
              isUseStandard: 0,
              status: 1,
              showType: 0,
              type: props.formInfo.type,
              isDefault: 0,
              coreNumber: 0
            };
            getVersion();
          } else {
            state.formData = JSON.parse(JSON.stringify(props.formInfo));
          }
        }
      }
    );
    const state = reactive({
      dialogShow: false,
      dialogRef: ref(),
      refForm: ref(),
      isAdd: false,
      fileList: [],
      uploadurl: capabilityAttachmentUploadUrl(),
      headerconfig: {
        Authorization: getToken()
      },
      capabilityId: route.query.capabilityId,
      capabilityName: route.query.capabilityName,
      capabilityNumber: route.query.capabilityNumber,
      formData: {
        groupNumber: 0,
        isUseStandard: 0,
        status: 1,
        showType: 0,
        type: 0,
        isDefault: 0,
        coreNumber: 0
      },
      rules: {
        version: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入'
          }
        ],
        groupNumber: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入动态分组数'
          },
          { validator: isInteger1, trigger: 'blur' }
        ],
        coreNumber: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入单个分组中试件个数'
          },
          { validator: isInteger, trigger: 'blur' }
        ],
        fileNo: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入文件编号'
          }
        ],
        id: [
          {
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('请上传模板'));
              } else {
                callback();
              }
            },
            trigger: 'change'
          }
        ]
      }
    });
    const handleSuccess = (response, file, fileList) => {
      state.formData.id = response.data.id;
    };
    const handleHtmlUpload = file => {
      if (file.type !== 'text/html') {
        ElMessage.error('请选择正确的HTML模板文件');
        return false;
      }
    };
    // 提交表单
    const handleSubmit = () => {
      state.refForm.validate(valid => {
        if (valid) {
          if (state.isAdd) {
            const formdata = JSON.parse(JSON.stringify(state.formData));
            formdata.capabilityId = state.capabilityId;
            savecapabilitytemplate(formdata).then(res => {
              if (res.data.code === 200) {
                ElMessage.success({
                  message: '创建成功',
                  type: 'success'
                });
                handleCancle(true);
              }
            });
          } else {
            const formdata = JSON.parse(JSON.stringify(state.formData));
            formdata.capabilityId = state.capabilityId;
            formdata.isDefault = formdata.isDefault ? 1 : 0;
            // delete formdata.id
            updatecapabilitytemplate(formdata).then(res => {
              if (res.data.code === 200) {
                ElMessage.success({
                  message: '修改成功',
                  type: 'success'
                });
                handleCancle(true);
              }
            });
          }
        }
      });
    };
    const beforeClose = () => {
      handleCancle();
    };
    const handleCancle = isRefresh => {
      state.dialogShow = false;
      context.emit('closeDialog', isRefresh);
    };
    const getVersion = () => {
      getGenerator('19').then(res => {
        if (res.data.code === 200) {
          state.formData.version = res.data.data;
        }
      });
    };

    return {
      ...toRefs(state),
      emptyImg,
      beforeClose,
      handleSuccess,
      handleHtmlUpload,
      handleSubmit,
      handleCancle,
      getPermissionBtn
    };
  }
};
</script>

<style lang="scss" scoped>
.excel-dialog {
  .item-radio {
    :deep(.el-radio) {
      margin-right: 0;
    }
  }
  .upload-demo {
    .el-button {
      margin-right: 10px;
    }
  }
}
</style>
<style lang="scss"></style>
