<template>
  <!-- 检测项目-关联子项目 -->
  <div class="Specification">
    <div class="header-search-group">
      <div v-loading="listLoading" class="btn-group">
        <el-button
          v-if="getPermissionBtn('addTestProjectSubproject') && !isEdit"
          size="small"
          icon="el-icon-plus"
          type="default"
          @click="handleAddEdit()"
          @keyup.prevent
          @keydown.enter.prevent
          >新增</el-button
        >
        <el-button
          v-if="getPermissionBtn('editTestProjectSubproject') && !isAdd && formData.tableList.length > 0"
          size="small"
          type="default"
          icon="el-icon-edit-outline"
          @click="handleEditable()"
          @keyup.prevent
          @keydown.enter.prevent
          >编辑</el-button
        >
        <el-button
          v-if="isEdit || isAdd"
          size="small"
          icon="el-icon-check"
          type="primary"
          @click="handleSaveEdit()"
          @keyup.prevent
          @keydown.enter.prevent
          >保存</el-button
        >
        <el-button
          v-if="isEdit || isAdd"
          size="small"
          icon="el-icon-close"
          @click="handleCancelEdit()"
          @keyup.prevent
          @keydown.enter.prevent
          >取消</el-button
        >
      </div>
    </div>
    <el-form ref="ruleForm" :model="formData">
      <el-table
        v-if="isShow"
        id="sortableList"
        ref="tableRef"
        v-loading="listLoading"
        :data="formData.tableList"
        size="medium"
        fit
        border
        height="auto"
        highlight-current-row
        class="dark-table base-table format-height-table"
        @header-dragend="drageHeader"
      >
        <el-table-column label="序号" type="index" width="60" />
        <el-table-column prop="length" label="子项目名称" min-width="100">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="isEdit || (isAdd && !row.id)"
              :prop="`tableList.${$index}.name`"
              :rules="{ required: true, message: '请输入项目名称', trigger: 'change' }"
              style="margin: 0px"
            >
              <el-input v-model="row.name" placeholder="请输入子项目名称" />
            </el-form-item>
            <span v-else> {{ row.name || '--' }} </span>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="120">
          <template #default="{ row, $index }">
            <el-form-item v-if="isEdit || (isAdd && !row.id)" :prop="`tableList.${$index}.remark`" style="margin: 0px">
              <el-input v-model="row.remark" placeholder="请输入备注" />
            </el-form-item>
            <span v-else> {{ row.remark || '--' }} </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="getPermissionBtn('deleteTestProjectSubproject')"
          label="操作"
          :width="colWidth.operationSingle"
        >
          <template #default="{ row, $index }">
            <span class="blue-color" @click="handleDelete(row, $index)">删除</span>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>

<script>
import { getCurrentInstance, reactive, ref, toRefs, watch } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { getColWidth } from '@/utils/func/customTable';
import { capabilitySubCategory, deleteCapabilitySubCategory, saveCapabilitySubCategory } from '@/api/capability';
// import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'AssociatedSubitem',
  components: {},
  props: {
    capabilityId: {
      type: String,
      default: ''
    },
    activeName: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableRef: ref(),
      isShow: true,
      oldTableList: [], // 修改前的数据
      detailItemId: '', // 项目id
      isAdd: false, // 是否是新增状态
      isEdit: false, // 是否是编辑状态 true 是
      ruleForm: ref(),
      listLoading: false,
      formData: {
        tableList: []
      },
      capabilityId: ''
    });
    watch(props, newValue => {
      if (newValue.activeName === '8') {
        state.detailItemId = props.capabilityId;
        getList();
      }
    });
    const getList = () => {
      state.listLoading = true;
      capabilitySubCategory(state.detailItemId).then(res => {
        state.listLoading = false;
        if (res) {
          state.formData.tableList = res.data.data;
          state.oldTableList = JSON.parse(JSON.stringify(res.data.data));
        }
      });
    };
    // 新增
    const handleAddEdit = () => {
      state.formData.tableList.push({
        name: '',
        capabilityId: state.detailItemId
      });
      state.isAdd = true;
    };
    // 编辑表格
    const handleEditable = () => {
      state.isEdit = true;
    };
    // 保存修改
    const handleSaveEdit = () => {
      state.ruleForm
        .validate()
        .then(valid => {
          if (valid) {
            state.listLoading = true;
            saveCapabilitySubCategory({ subCategoryList: state.formData.tableList }).then(res => {
              state.listLoading = false;
              if (res) {
                getList();
                state.isAdd = false;
                state.isEdit = false;
                proxy.$message.success(res.data.message);
              }
            });
          } else {
            return false;
          }
        })
        .catch(() => {
          proxy.$message.error('请输入子项目名称');
          return false;
        });
    };
    // 取消修改
    const handleCancelEdit = () => {
      state.isEdit = false;
      state.isAdd = false;
      state.formData.tableList = JSON.parse(JSON.stringify(state.oldTableList));
    };

    const handleDelete = (row, index) => {
      if (row.id) {
        state.listLoading = true;
        deleteCapabilitySubCategory(row.id).then(res => {
          state.listLoading = false;
          if (res) {
            proxy.$message.success(res.data.message);
            state.formData.tableList.splice(index, 1);
          }
        });
      } else {
        state.formData.tableList.splice(index, 1);
      }
    };

    return {
      ...toRefs(state),
      handleAddEdit,
      drageHeader,
      getNameByid,
      getNamesByid,
      getPermissionBtn,
      formatDate,
      getList,
      colWidth,
      handleEditable,
      handleCancelEdit,
      handleSaveEdit,
      getColWidth,
      handleDelete
    };
  },
  computed: {},
  created() {}
};
</script>
<style lang="scss" scoped>
.header-search-group {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  .btn-group {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>
