import request from '@/utils/request';

// 获取考勤列表
export function getDutyTime(data) {
  return request({
    url: '/api-user/user/employee-on-duty/time/query',
    method: 'post',
    data
  });
}
// 获取请假列表
export function employeeLeave(data) {
  return request({
    url: '/api-user/user/employee-leave/list',
    method: 'post',
    data
  });
}
// 保存请假信息
export function saveEmployeeLeave(data) {
  return request({
    url: '/api-user/user/employee-leave/save',
    method: 'post',
    data
  });
}
// 人员考勤配置表
export function employeeAttendance() {
  return request({
    url: '/api-user/user/employee-attendance/info',
    method: 'get'
  });
}
// 放假列表
export function employeeHoliday(data) {
  return request({
    url: '/api-user/user/employee-holiday/list',
    method: 'post',
    data
  });
}

// 保存人员考勤配置表
export function saveEmployeeAttendance(data) {
  return request({
    url: '/api-user/user/employee-attendance/save',
    method: 'post',
    data
  });
}
// 保存放假信息
export function saveHolidayApi(data) {
  return request({
    url: '/api-user/user/employee-holiday/save',
    method: 'post',
    data
  });
}
// 调整记录列表
export function initRecordList(data) {
  return request({
    url: '/api-user/user/employee-on-duty-record/list',
    method: 'post',
    data
  });
}
// 获取在岗人员列表
export function employeeOnDuty() {
  return request({
    url: '/api-user/user/employee-on-duty/list',
    method: 'get'
  });
}
// 更新在岗人员信息
export function employeeOnDutyUpdate(data) {
  return request({
    url: '/api-user/user/employee-on-duty/update',
    method: 'post',
    data
  });
}
