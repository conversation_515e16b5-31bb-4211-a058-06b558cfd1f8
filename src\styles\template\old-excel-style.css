/**
 * Layui
 * Classic modular Front-End UI library
 * MIT Licensed
 */
/** 初始化 **/
body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
input,
button,
textarea,
p,
blockquote,
th,
td,
form,
pre {
  margin: 0;
  padding: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

a:active,
a:hover {
  outline: 0;
}

img {
  display: inline-block;
  border: none;
  vertical-align: middle;
}

li {
  list-style: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

h1,
h2,
h3 {
  font-weight: 400;
}

h4,
h5,
h6 {
  font-size: 100%;
  font-weight: 400;
}

button,
input,
select,
textarea {
  font-size: 100%;
}

input,
button,
textarea,
select,
optgroup,
option {
  font-family: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  outline: 0;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}

textarea:disabled,
input:disabled {
  background: none !important;
}

select:disabled {
  color: #303133;
  opacity: 1;
}

/** 初始化全局标签 **/
body {
  line-height: 1.6;
  color: #303133;
  font: 14px Times New Roman, 'SourceHanSerifCN';
}

hr {
  height: 0;
  line-height: 0;
  margin: 10px 0;
  padding: 0;
  border: none !important;
  border-bottom: 1px solid #eee !important;
  clear: both;
  overflow: hidden;
  background: none;
}

a {
  color: #303133;
  text-decoration: none;
}

a:hover {
  color: #777;
}

a cite {
  font-style: normal;
  cursor: pointer;
}

.excel-content {
  margin: 0 auto;
  text-align: center;
}

.flex-box-all {
  display: flex;
}

.flex-box-all .box:last-child .box-item {
  border-right: none;
}

.flex-box {
  /*display:flex;*/
  overflow: hidden;
  align-items: stretch;
  line-height: 20px;
}

.footer {
  box-sizing: border-box;
  margin: 0 auto;
  border-top: none;
  padding-top: 20px !important;
  width: 680px;
}

.txt-l.footer {
  margin: 0 !important;
  padding-top: 20px;
}

.flex1 {
  flex: 1;
  float: left;
  /*vertical-align: middle;*/
}

.flex-box .flex1 {
  width: 60px;
  text-align: center;
  box-sizing: border-box;
}

.flex-box .flex1-1 {
  width: 42px !important;
  text-align: center;
  /*box-sizing: border-box;*/
}

.flex-box .flex2 {
  width: 78px;
  text-align: center;
}

.flex-box .flex9 {
  width: 364px;
  text-align: center;
}

.flex-box .flex3 {
  width: 120px;
  text-align: center;
}

.flex-box .flex5 {
  width: 202px;
  text-align: center;
}

.flex-box .W45 {
  width: 41px;
  float: left;
  text-align: center;
}

.flex-box.hj-box .flex1 {
  width: 40px;
}

.flex2 {
  flex: 2;
  vertical-align: middle;
  float: left;
}

.flex3 {
  flex: 3;
  float: left;
}

.flex3 {
  flex: 3;
  float: left;
}

.flex4 {
  flex: 4;
  float: left;
}

.flex5 {
  flex: 5;
  float: left;
}

.flex6 {
  flex: 6;
  float: left;
}

.flex7 {
  flex: 7;
  float: left;
}

.flex8 {
  flex: 8;
  float: left;
}

.flex9 {
  flex: 9;
  float: left;
}

.lh50 {
  line-height: 62px;
  text-align: center;
}

.ln60 {
  height: 41px;
}

/*add---------------------------------------------------------*/
.elePrevAll {
  width: 680px;
  text-align: center;
  margin: 0 auto;
}

.bdb {
  border-bottom: 1px solid #000;
}

.bdr {
  border-right: 1px solid #000;
}

.printNext {
  border-right: 1px solid #000;
}

.excel-content .bd-r:last-child {
  border-right: 0;
}

.excel-content .bdr:last-child {
  border-right: 0;
}

.htmlFooter {
  margin-top: 40px;
}

.bdl {
  border-left: 1px solid #000;
}

.bdlr {
  border-left: 1px solid #000;
  border-right: 1px solid #000;
}

.bdt {
  border-top: 1px solid #000;
}

.table-empty {
  width: 680px;
  border-right: 1px solid #000;
}

.table-empty .tr {
  overflow: hidden;
  border-bottom: 1px solid #000;
  border-top: 1px solid #000;
}

.table-empty .td {
  line-height: 20px;
  height: 20px;
}

.ipt {
  border: none;
  text-align: center;
}

.ipt-wrap {
  width: 680px;
  text-align: left;
}

.ipt-wrap .div-ipt input {
  border: none;
  width: 98%;
}

.ipt-wrap .div-ipt {
  display: inline-block;
  width: 80px;
}

.ipt-wrap .ipt-80 {
  width: 78px;
}

.ipt-wrap .ipt-75 {
  width: 75px;
}

.ipt-wrap .ipt-76 {
  width: 75px;
}

.ipt-wrap .ipt-36 {
  width: 37px;
}

.ipt-wrap .ipt-50 {
  width: 58px;
  box-sizing: border-box;
}

.ipt-wrap .v-data .ipt-48 {
  width: 57px;
}

.ipt-wrap .ipt-39 {
  width: 37px;
}

.ipt-wrap .ipt-38 {
  width: 37px;
  box-sizing: border-box;
}

.ipt-wrap .ipt-60 {
  width: 60px;
}

.div-ipt {
  display: inline-block;
}

/*.ipt-wrap .div-ipt input{
 width: 79PX;
 }*/
/*----------------------------------------*/
.bg-grey {
  background: #f1f1f1;
}

.box-excel .el-row {
  line-height: 25px !important;
}

.box-excel .header .iptPublic {
  display: block;
}

.box-excel .header .el-input__inner {
  display: block;
  width: 100%;
  height: 25px !important;
  line-height: 25px;
  border: none;
}

.box-excel .header .el-select__caret.el-input__icon {
  line-height: 25px !important;
}

.box-excel .header .el-textarea {
  min-height: 25px;
  height: auto;
}

.box-excel .el-textarea__inner {
  padding-top: 0;
  padding-bottom: 0;
  min-height: 25px !important;
  line-height: 25px;
  border: none;
}

.box-excel .header .el-select .el-input__suffix-inner {
  display: block;
  line-height: 25px !important;
  text-indent: 0;
}

.box-wrap {
  overflow: hidden;
  border-top: 1px solid #000;
}

.box-wrap .box {
  overflow: hidden;
}

.box-wrap .title-l {
  width: 39px;
  float: left;
  height: 60px;
  line-height: 60px;
  border-right: 1px solid #000;
}

.box-wrap .box {
  float: left;
  line-height: 29px;
}

.box-item {
  border-right: 1px solid #000;
  border-bottom: 1px solid #000;
}

.box-small {
  overflow: hidden;
  width: 680px;
}

.box-small .top-box {
  overflow: hidden;
  float: left;
  /*width: 650PX;*/
}

.box-small .top-box .box {
  width: 54px;
  float: left;
  box-sizing: border-box;
}

.box-small .top-box .item-title-l {
  width: 100px;
}

.box-small .box:last-child .box-item {
  border-right: none;
}

.box-right {
  overflow: hidden;
}

.box-item input {
  width: 97%;
}

/*.ipt-wrap .div-ipt input{
 width: 79PX;
 }*/
.box-wrap.box-small .title-l {
  height: 120px;
  line-height: 120px;
  box-sizing: border-box;
}

.box-flex {
  display: flex;
}

.box-flex .box-item:last-child {
  border-bottom: none;
}

.box-flex .box:last-child .box-item {
  border-right: none;
}

.box-flex .box {
  flex: 1;
}

.box-flex .box:last-child .box-item {
  border-right: none;
}

.box-bb .box-item:first-child {
  height: 40px;
  font-size: 12px;
}

.box-bb .box-item {
  height: 30px;
}

.box-bb .flex1 .box-item {
  line-height: 40px;
}

.box-bb .box-item input {
  line-height: 30px;
  height: 28px;
  float: left;
}

.ipt option {
  color: #333 !important;
}

.bd-r {
  border-right: 1px solid #000;
}

.bd-1 {
  border: 1px solid #000;
}

.bd-b {
  border-bottom: 1px solid #000;
}

.txt-l {
  text-align: left;
  line-height: 1.5;
  text-indent: 10px;
}

.el-row {
  display: flex;
  flex-wrap: wrap;
  position: relative;
  box-sizing: border-box;
}

.el-row:after {
  clear: both;
}

.el-row:after,
.el-row:before {
  display: table;
  content: '';
}

*,
:after,
:before {
  box-sizing: inherit;
}

.bg-blue {
  background-color: #ccf0e8;
}

.textIndent5 {
  text-indent: 5px;
}

.textIndent10 {
  text-indent: 10px;
}

.textLeft {
  text-align: left;
}

.textCenter {
  text-align: center;
}

.el-row .ipt {
  width: 100%;
  min-height: 25px;
}

.unit {
  position: absolute;
  right: 40px;
  bottom: 0;
}

.unit sub {
  position: absolute;
  right: -12px;
  top: 7px;
}

.unit-box .span4 {
  position: relative;
}

.unit-box input {
  width: 54% !important;
  position: relative;
  right: 30px;
  top: 0;
}

.span1 {
  max-width: 4.1666666667%;
  flex: 0 0 4.1666666667%;
}

.span2 {
  max-width: 8.3333333333%;
  flex: 0 0 8.3333333333%;
}

.span2_4 {
  max-width: 9.96%;
  flex: 0 0 9.96%;
}

.span2point4 {
  max-width: 10%;
  flex: 0 0 10%;
}

.span2_6 {
  max-width: 11.8055555556%;
  flex: 0 0 11.8055555556%;
}

.span3 {
  max-width: 12.5%;
  flex: 0 0 12.5%;
}

.span3point6 {
  max-width: 15%;
  flex: 0 0 15%;
}

.span3dot6 {
  max-width: 15%;
  flex: 0 0 15%;
}

.span4 {
  max-width: 16.6666666667%;
  flex: 0 0 16.6666666667%;
}

.span4_1 {
  max-width: 16.7%;
  flex: 0 0 16.7%;
}

.span4_3 {
  max-width: 17.7%;
  flex: 0 0 17.7%;
}

.span4_2 {
  max-width: 16.6%;
  flex: 0 0 16.6%;
}

.span4_2_1 {
  max-width: 17.6%;
  flex: 0 0 17.6%;
}

.span4point5 {
  max-width: 18.75%;
  flex: 0 0 18.75%;
}

.span4point8 {
  max-width: 20%;
  flex: 0 0 20%;
}

.span5 {
  width: 19.9999999%;
  flex: 0 0 19.9999999%;
}

.span5_1_1 {
  width: 20.83%;
  flex: 0 0 20.83%;
}

.span5_1 {
  width: 20.98%;
  flex: 0 0 20.98%;
}

.span5_2 {
  width: 20.8%;
  flex: 0 0 20.8%;
}

.span5_3 {
  width: 19.9%;
  flex: 0 0 19.9%;
}

.span6 {
  width: 25%;
  flex: 0 0 25%;
}

.span6_1 {
  width: 21.2%;
  flex: 0 0 21.2%;
}

.span_6_1 {
  width: 25.8%;
  flex: 0 0 25.8%;
}

.span6_2 {
  width: 26.3%;
  flex: 0 0 26.3%;
}

.span6_3 {
  width: 26.3%;
  flex: 0 0 26.3%;
}

.span6_4 {
  width: 26%;
  flex: 0 0 26%;
}

.span7 {
  max-width: 29.1666666667%;
  flex: 0 0 29.1666666667%;
}

.span7_1 {
  max-width: 29.48%;
  flex: 0 0 29.48%;
}

.span8 {
  max-width: 33.3333333333%;
  flex: 0 0 33.3333333333%;
}

.span9 {
  max-width: 37.5%;
  flex: 0 0 37.5%;
}

.span9_6 {
  max-width: 40.2%;
  flex: 0 0 40.2%;
}

.span10 {
  max-width: 41.67%;
  flex: 0 0 41.67%;
}

.span11 {
  max-width: 45.8333333%;
  flex: 0 0 45.83333333%;
}

.span12 {
  max-width: 50%;
  flex: 0 0 50%;
}

.span13 {
  max-width: 53.2%;
  flex: 0 0 53.2%;
}

.span14 {
  max-width: 58.3%;
  flex: 0 0 58.3%;
}

.span14_4 {
  max-width: 59.8%;
  flex: 0 0 59.8%;
}

.span15 {
  max-width: 62.5%;
  flex: 0 0 62.5%;
}

.span15_1 {
  max-width: 63.39%;
  flex: 0 0 63.39%;
}

.span16 {
  max-width: 66.6666666667%;
  flex: 0 0 66.6666666667%;
}

.span17 {
  width: 70.8333333333%;
  flex: 0 0 70.8333333333%;
}

.span18 {
  width: 75%;
  flex: 0 0 75%;
}

.span19 {
  max-width: 79.1666666667%;
  flex: 0 0 79.1666666667%;
}

.span19_1 {
  max-width: 80.001%;
  flex: 0 0 80.001%;
}

.span20 {
  max-width: 83.3333333333%;
  flex: 0 0 83.3333333333%;
}

.span21 {
  max-width: 87.5%;
  flex: 0 0 87.5%;
}

.span22 {
  max-width: 91.6666666667%;
  flex: 0 0 91.6666666667%;
}

.span23 {
  max-width: 95.8333333333%;
  flex: 0 0 95.8333333333%;
}

.span24 {
  max-width: 100%;
  flex: 0 0 100%;
}

.excel-content input[type='text'],
.excel-content input[type^='date'] {
  display: block;
  width: 100%;
  height: 100%;
}

.excel-content input[type='radio'] {
  width: 20px;
}

.excel-content input[type='radio']::hover {
  cursor: pointer;
}

.excel-content input[type='radio'],
.excel-content input[type='checkbox'] {
  vertical-align: -2px;
}

.excel-content label {
  font-weight: normal;
}

.excel-content label::hover {
  cursor: pointer;
}

.excel-content input[type='date'] {
  border: 0;
  width: 100%;
}

.excel-content input[readonly] {
  background-color: #fff;
}

.excel-content .lineCenter {
  display: flex;
  align-items: center;
  justify-content: center;
}

.excel-content .radioLineCenter {
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: center;
}

.excel-content .justLineCenter {
  display: flex;
  align-items: center;
}

.excel-content .inline {
  display: inline;
}

.excel-content .subscript {
  font-size: 9px;
  position: relative;
  top: 3px;
}

.excel-content .supscript {
  font-size: 9px;
  position: relative;
  top: -3px;
}

.bg-green {
  background-color: rgba(146, 212, 143, 0.3) !important;
}

.excel-content select {
  width: 100%;
  border: 0;
  height: 100% !important;
}

.bd-r:last-child {
  border-right: 0;
}

.excel-content input.inputCs {
  width: 40px;
  height: 25px;
  display: inline-block;
}

.excel-content .padding10 {
  padding: 0 10px;
}

.excel-content .tsfh {
  font-size: 16px;
  position: relative;
  font-style: oblique;
}

.excel-content .tsfh::before {
  content: '-';
  position: absolute;
  top: 0;
  left: 0px;
  background-color: #000;
  height: 1px;
  width: 15px;
}

.excel-content .tsfh::after {
  content: '1';
  font-size: 10px;
}

.excel-content .textRight {
  text-align: right;
}

.excel-content .cf input {
  font-size: 10px;
  text-align: left;
  height: 50%;
  width: 33%;
  position: absolute;
  top: 0;
  right: 0;
}

.selectCenter {
  text-align-last: center;
}

input:-webkit-autofill {
  box-shadow: 0 0 0px 1000px #fff inset !important;
  -webkit-text-fill-color: #333 !important;
}

.radio-m {
  margin: 120px auto 0;
}

.el-row.box-20 {
  display: block;
}

.box-20 .span4 {
  max-width: 24.8% !important;
  float: left;
}

.box-20 select {
  line-height: 25px !important;
  height: 25px !important;
}

.box-20 .span8 {
  width: 75%;
  float: left;
  flex: 0 0 75%;
  max-width: 74%;
}

.moduleHidden {
  display: none;
}

textarea {
  resize: vertical;
  border: 0;
  line-height: 20px;
  width: 100%;
}

.displayFlex {
  display: flex;
}
.columnXhModule {
  display: flex;
  flex-direction: row;
}
.columnRow {
  flex: 1;
}
