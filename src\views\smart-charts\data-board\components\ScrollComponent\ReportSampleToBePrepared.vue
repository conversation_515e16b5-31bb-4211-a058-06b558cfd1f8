<template>
  <!-- 未完成报告样品 -->
  <div class="centerTop centerTop2">
    <span> 未完成报告样品</span>
    <div class="number">
      <span>{{ total }}</span
      >个
    </div>
  </div>
  <div class="centerBox">
    <div class="boxTable">
      <el-row>
        <el-col :span="2" />
        <el-col :span="10">样品编号</el-col>
        <el-col :span="12">试验负责人</el-col>
      </el-row>
      <ScrollList :type="'4'" @pageTotal="getTotal" />
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, watch } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getNameByid } from '@/utils/common';
import ScrollList from './ScrollList';

export default {
  name: 'ReportSampleToBePrepared',
  components: { ScrollList },
  props: {},
  setup(props, context) {
    const state = reactive({
      total: 0
    });
    watch(props, newValue => {
      state.pageType = props.type;
    });
    const getTotal = val => {
      state.total = val.total;
    };
    return {
      ...toRefs(state),
      getTotal,
      getNameByid,
      formatDate
    };
  }
};
</script>
<style lang="scss" scoped>
@import '../../data-board.scss';
.centerTop {
  background: url(../../../../../assets/img/dataBoard/center-left-top.png) no-repeat;
  background-size: 100% 100%;
  .number {
    background: url(../../../../../assets/img/dataBoard/center-left-top2.png) no-repeat;
    background-size: cover;
  }
}
</style>
