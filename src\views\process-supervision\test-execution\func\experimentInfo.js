import { experiment, downloadByCapabilityId, TemplateIdByexperimentId, experimentmongodatainfo } from '@/api/execution';
import _ from 'lodash';
import { decryptCBC } from '@/utils/ASE';
// import store from '@/store'

export function getExpermentInfo(state) {
  const init1 = () => {
    return new Promise((resolve, reject) => {
      // 获取单个模板信息
      experiment(state.experimentId).then(res => {
        if (res.data.code === 200 && res.data.data) {
          res.data.data.isower = _.indexOf(res.data.data.ownerIds.split(','), state.accountId) !== -1;
          resolve(res.data.data);
        } else {
          resolve(res.data.data);
        }
      });
    });
  };
  const init2 = () => {
    return new Promise((resolve, reject) => {
      if (state.new) {
        downloadByCapabilityId({ capabilityId: state.capabilityId, samplesId: state.samplesId }).then(res => {
          if (res.data.code === 200) {
            resolve(res.data.data);
          } else {
            reject('接口错误');
          }
        });
      } else {
        TemplateIdByexperimentId(state.experimentId).then(res => {
          if (res.data.code === 200) {
            resolve(res.data.data);
          } else {
            reject('接口错误');
          }
        });
      }
      // TemplateIdByexperimentId({capabilityId:state.capabilityId,samplesId:state.samplesId}).then(res => {
    });
  };

  // const getrealOwnerIdsimgs = async(ids) => {
  //   if (ids && ids !== undefined) {
  //     const ul = ids.split(',')
  //     const newPeopleInfoList = await store.dispatch('common/getSignatureImg', ul)
  //     return new Promise((resolve) => {
  //       resolve(newPeopleInfoList)
  //     })
  //   }
  // }

  const getall = () => {
    Promise.all([init1(), init2()])
      .then(async allres => {
        // 两个都调成功以后执行的操作
        state.jsonData = { ...allres[0] };
        state.jsonData.excelHtml = decryptCBC(allres[1].html);
        state.jsonData.templateValue = allres[1].templateValue;
        state.jsonData.fileNo = allres[1].fileNo;
        state.jsonData.coreNumber = allres[1].coreNumber;
        state.jsonData.disabled = state.disable !== 'check';
        if (state.new) {
          state.jsonData.templateId = allres[1].templateId;
        } else {
          state.jsonData.templateId = allres[0].templateId;
        }
        // 模板实验值信息
        await experimentmongodatainfo(state.experimentId).then(async resdata => {
          if (resdata) {
            state.jsonData.experimentData = resdata.data.data;
            state.jsonData.experimentData.coreNumber = state.jsonData.coreNumber;
            state.jsonData.realReviewerIdimg = state.jsonData.experimentData.reviewerSignUrl.split(',');
            state.jsonData.realOwnerIdsimgs = state.jsonData.experimentData.ownerSignUrls.split(',');
          }
        });
      })
      .catch(err => {
        // 抛出错误信息
        throw err;
      });
  };

  getall();
}
