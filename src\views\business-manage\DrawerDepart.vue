<template>
  <!-- 新增编辑员工信息 -->
  <el-drawer
    v-model="showDrawer"
    :title="drawerType === 'add' ? '新增员工' : '编辑员工'"
    direction="rtl"
    :before-close="handleClose"
    :size="750"
    destroy-on-close
    :close-on-click-modal="false"
    custom-class="page-drawer"
  >
    <DrawerLayout v-loading="drawerLoading" :has-button-group="false" :has-page-header="false">
      <el-form
        ref="formRef"
        :inline="true"
        :model="formDataUser"
        class="form-height-auto"
        label-width="110px"
        label-position="top"
      >
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item
              label="用户名："
              prop="username"
              :rules="[{ required: true, message: '请输入用户名', trigger: 'change' }]"
            >
              <el-input
                v-model="formDataUser.username"
                v-trim
                :disabled="drawerType !== 'add'"
                autocomplete="off"
                maxlength="18"
                placeholder="请输入用户名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="姓名："
              prop="nickname"
              :rules="{ required: true, message: '请输入姓名', trigger: 'change' }"
            >
              <el-input
                ref="inputRef"
                v-model="formDataUser.nickname"
                v-trim
                autocomplete="off"
                placeholder="请输入姓名"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="部门："
              prop="departmentId"
              :rules="{ required: true, message: '请选择部门', trigger: 'change' }"
            >
              <el-cascader
                v-model="formDataUser.departmentId"
                :options="dialogTreeData"
                :props="categoryProps"
                size="small"
                placeholder="请选择部门"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="在职状态："
              prop="beOnTheJob"
              :rules="{ required: true, message: '请输入在职状态', trigger: 'change' }"
            >
              <el-select
                v-model="formDataUser.beOnTheJob"
                :disabled="formDataUser.managerflag == 0"
                size="small"
                placeholder="请选择在职状态"
                style="width: 100%"
              >
                <el-option
                  v-for="(val, key) in dictionaryAll['jobType']"
                  :key="key"
                  :label="val.name"
                  :value="Number(key)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色分配：" prop="roles" label-width="120px">
              <el-select
                v-model="formDataUser.roles"
                :disabled="formDataUser.managerflag == 0"
                multiple
                filterable
                placeholder="请选择角色分配"
                style="width: 100%"
              >
                <el-option-group v-for="item in roleList" :key="item.label" :label="item.label">
                  <el-option
                    v-for="val in item.group"
                    :key="val.id"
                    :label="val.name"
                    :value="val.id"
                    :disabled="val.status !== 1"
                  >
                    <span style="float: left">{{ val.name }}</span>
                    <span v-if="val.status !== 1" class="fr" style="color: red">已停用</span>
                  </el-option>
                </el-option-group>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检验类型：" prop="inspectionType">
              <el-select
                v-model="formDataUser.inspectionType"
                multiple
                filterable
                placeholder="请选择检验类型"
                size="small"
                clearable
                style="width: 100%"
              >
                <el-option v-for="(val, key) in dictionaryAll['JYLX']" :key="key" :label="val.name" :value="key" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工号：" prop="jobNumber">
              <el-input
                v-model="formDataUser.jobNumber"
                maxlength="50"
                autocomplete="off"
                size="small"
                placeholder="请输入工号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号：" prop="mobile">
              <el-input v-model="formDataUser.mobile" autocomplete="off" size="small" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱：" prop="email">
              <el-input v-model="formDataUser.email" autocomplete="off" size="small" placeholder="请输入邮箱" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="身份证号："
              prop="identityCard"
              :rules="[{ validator: idNumberRex, message: '请输入正确格式', trigger: 'blur' }]"
            >
              <el-input
                v-model="formDataUser.identityCard"
                autocomplete="off"
                size="small"
                placeholder="请输入身份证号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别：" prop="gender">
              <el-select v-model="formDataUser.gender" size="small" placeholder="请选择性别" style="width: 100%">
                <el-option
                  v-for="(val, key) in dictionaryAll['sex']"
                  :key="key"
                  :label="val.name"
                  :value="Number(key)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出生日期：" prop="birthday">
              <el-date-picker
                v-model="formDataUser.birthday"
                type="date"
                size="small"
                placeholder="请选择出生日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入职日期：" prop="entryDate">
              <el-date-picker
                v-model="formDataUser.entryDate"
                type="date"
                size="small"
                placeholder="请选择入职日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最高学历：" prop="degree">
              <el-select v-model="formDataUser.degree" size="small" placeholder="请选择最高学历" style="width: 100%">
                <el-option
                  v-for="(val, key) in dictionaryAll['highestDegree']"
                  :key="key"
                  :label="val.name"
                  :value="Number(key)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="英文名称：" prop="englishname">
              <el-input
                v-model="formDataUser.englishname"
                autocomplete="off"
                size="small"
                placeholder="请输入英文名称"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="姓名拼音：" prop="transcription">
              <el-input
                v-model="formDataUser.transcription"
                autocomplete="off"
                maxlength="50"
                size="small"
                placeholder="请输入姓名拼音"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司邮箱：" prop="companyEmail" :rules="[{ validator: isEmail2, trigger: 'blur' }]">
              <el-input
                v-model="formDataUser.companyEmail"
                autocomplete="off"
                size="small"
                placeholder="请输入公司邮箱"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="公司电话："
              prop="companyMobile"
              :rules="[{ validator: isPhoneMobile, trigger: 'blur' }]"
            >
              <el-input
                v-model="formDataUser.companyMobile"
                autocomplete="off"
                size="small"
                placeholder="请输入公司电话"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="工龄（年）："
              prop="seniority"
              :rules="{ validator: greaterThanZero2, trigger: 'change' }"
            >
              <el-input
                v-model="formDataUser.seniority"
                v-trim
                autocomplete="off"
                maxlength="5"
                size="small"
                placeholder="请输入工龄"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="毕业院校：" prop="graduatedSchool">
              <el-input
                v-model="formDataUser.graduatedSchool"
                autocomplete="off"
                maxlength="100"
                size="small"
                placeholder="请输入毕业院校"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专业：" prop="major">
              <el-input
                v-model="formDataUser.major"
                autocomplete="off"
                maxlength="100"
                size="small"
                placeholder="请输入专业"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职称：" prop="technicalTitle">
              <el-input
                v-model="formDataUser.technicalTitle"
                autocomplete="off"
                maxlength="100"
                size="small"
                placeholder="请输入职称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="家庭地址：" prop="homeAddress">
              <el-input
                v-model="formDataUser.homeAddress"
                autocomplete="off"
                maxlength="100"
                size="small"
                placeholder="请输入家庭地址"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="紧急联系人：" prop="emergencyContact">
              <el-input
                v-model="formDataUser.emergencyContact"
                autocomplete="off"
                maxlength="100"
                size="small"
                placeholder="请输入紧急联系人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="紧急联系人电话："
              prop="emergencyContactPhone"
              :rules="[{ validator: isPhoneMobile, trigger: 'blur' }]"
            >
              <el-input
                v-model="formDataUser.emergencyContactPhone"
                autocomplete="off"
                maxlength="100"
                size="small"
                placeholder="请输入紧急联系人电话"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作简历：" prop="workResume">
              <el-upload
                v-model:file-list="formDataUser.workResume"
                class="upload-demo"
                :action="uploadAction"
                :headers="headerconfig"
                :before-upload="beforeUpload"
                multiple
                :on-success="
                  res => {
                    return uploadSuccess(res, 'workResume');
                  }
                "
                :on-preview="handlePreview"
                :on-remove="
                  (file, fileList) => {
                    return handleRemove(file, fileList, 'workResume');
                  }
                "
                :before-remove="beforeRemove"
              >
                <el-button type="primary">点击上传</el-button>
                <template #tip>
                  <div class="el-upload__tip">单文件大小不超过20M</div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="人员上岗证书：" prop="jobCertificate">
              <el-upload
                v-model:file-list="formDataUser.jobCertificate"
                class="upload-demo"
                :action="uploadAction2"
                :headers="headerconfig"
                :before-upload="beforeUpload"
                multiple
                :on-success="
                  res => {
                    return uploadSuccess(res, 'jobCertificate');
                  }
                "
                :on-preview="handlePreview"
                :on-remove="
                  (file, fileList) => {
                    return handleRemove(file, fileList, 'jobCertificate');
                  }
                "
                :before-remove="beforeRemove"
              >
                <el-button type="primary">点击上传</el-button>
                <template #tip>
                  <div class="el-upload__tip">单文件大小不超过20M</div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div class="drawer-fotter">
        <el-button type="primary" :loading="drawerLoading" @click="onSubmit">确认</el-button>
        <el-button :loading="drawerLoading" @click="handleClose">取消</el-button>
      </div>
    </DrawerLayout>
  </el-drawer>
</template>

<script>
// Basic
import { ref, watch, reactive, getCurrentInstance, toRefs } from 'vue';
import { ElMessageBox } from 'element-plus';

// Api
import {
  checkUserName,
  checkMobileURL,
  checkEmailURL,
  saveEmployees,
  updateEmployees,
  alreadyExist,
  deleteFile,
  downloadById
} from '@/api/departManagement';
import { getRoleTree } from '@/api/roleManage';
import { uploadAttachmentUrl } from '@/api/uploadAction';

// Components
import DrawerLayout from '@/components/DrawerLayout';

// Utils
import { getToken } from '@/utils/auth';
import { idNumberRex, greaterThanZero2, isPhoneMobile, isEmail2 } from '@/utils/validate';

export default {
  name: 'DrawerDepart',
  components: { DrawerLayout },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      required: true
    },
    treeNode: {
      type: Object,
      default: function () {
        return {};
      }
    },
    dictionary: {
      type: Object,
      default: function () {
        return {};
      }
    },
    treeData: {
      type: Array,
      default: function () {
        return [];
      }
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // 抽屉事件
    const showDrawer = ref(props.drawer);
    const state = reactive({
      drawerLoading: false,
      dialogTreeData: [],
      headerconfig: {
        Authorization: getToken()
      },
      uploadAction: uploadAttachmentUrl(),
      uploadAction2: uploadAttachmentUrl(),
      dictionaryAll: {
        sex: {},
        highestDegree: {},
        jobType: {},
        JYLX: {}
      },
      drawerType: '',
      oldFormData: {},
      formRef: ref(),
      formDataUser: {
        workResume: [],
        jobCertificate: []
      },
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      roleList: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ] // 角色列表
    });
    // 关闭抽屉
    const handleClose = () => {
      showDrawer.value = false;
      context.emit('close', { isRefresh: true });
      // context.emit('close', { isRefresh: false, isShow: false });
    };
    // 获取角色列表
    const getRoleList = () => {
      getRoleTree({}).then(res => {
        if (res) {
          const data = res.data.data;
          state.roleList[0].group = [];
          state.roleList[1].group = [];
          data.forEach(item => {
            if (item.status === 1) {
              state.roleList[0].group.push(item);
            } else {
              state.roleList[1].group.push(item);
            }
          });
        }
      });
    };
    getRoleList();
    watch(props, newValue => {
      showDrawer.value = newValue.drawer;
      if (showDrawer.value) {
        state.treeNodeData = props.treeNode;
        state.dialogTreeData = props.treeData.filter(item => item.status === 1);
        state.drawerType = props.type;
        state.dictionaryAll = props.dictionary || {
          sex: {
            1: '男',
            2: '女'
          },
          highestDegree: {},
          jobType: {
            1: '在职',
            0: '离职',
            2: '外部用户'
          },
          JYLX: {}
        };
        if (state.drawerType === 'add') {
          initDetail();
        } else {
          getDetail();
        }
      }
    });
    const initDetail = () => {
      state.formDataUser = {
        roles: [],
        inspectionType: [],
        workResume: [],
        jobCertificate: [],
        beOnTheJob: 1,
        departmentId: state.treeNodeData.id === 'all' ? '' : state.treeNodeData.id
      };
    };
    const getDetail = () => {
      state.formDataUser = JSON.parse(JSON.stringify(props.detailData));
      state.oldFormData = JSON.parse(JSON.stringify(props.detailData));
      state.formDataUser.inspectionType = state.formDataUser.inspectionType
        ? state.formDataUser.inspectionType.split(',')
        : [];
      if (state.formDataUser.roleId) {
        state.formDataUser.roles = state.formDataUser.roleId.split(',');
      }
    };
    const checkMobileEmail = params => {
      Promise.all([checkMobile(params.mobile), checkEmail(params.email)])
        .then(result => {
          if (result[0] && result[1]) {
            submitForm(params);
          }
        })
        .catch(_error => {});
    };
    const checkMobile = value => {
      return new Promise((resolve, _reject) => {
        // 输入了手机号，并且做了修改
        if (value && value !== state.oldFormData.mobile) {
          checkMobileURL({ mobile: value }).then(res => {
            if (res.data.data === 0) {
              resolve(true);
            } else {
              proxy.$message.error('手机号重复，请先修改');
              resolve(false);
            }
          });
        } else {
          resolve(true);
        }
      });
    };
    const checkEmail = value => {
      return new Promise((resolve, _reject) => {
        // 输入了邮箱，并且做了修改
        if (value && value !== state.oldFormData.email) {
          checkEmailURL({ email: value }).then(res => {
            if (res.data.data === 0) {
              resolve(true);
            } else {
              proxy.$message.error('邮箱重复，请先修改');
              resolve(false);
            }
          });
        } else {
          resolve(true);
        }
      });
    };
    // 确认新增
    const onSubmit = () => {
      proxy.$refs['formRef'].validate(valid => {
        if (valid) {
          var departmentId = state.formDataUser.departmentId;
          if (state.formDataUser.departmentId instanceof Array) {
            if (state.formDataUser.departmentId.length > 0) {
              departmentId = state.formDataUser.departmentId[state.formDataUser.departmentId.length - 1].toString();
            } else {
              departmentId = '';
            }
          }
          const params = {
            ...state.formDataUser,
            departmentId: departmentId,
            inspectionType: state.formDataUser.inspectionType.toString()
          };
          if (state.drawerType === 'add') {
            checkUserName({ username: params.username }).then(res => {
              if (res.data.data === '0') {
                checkMobileEmail(params);
              } else if (res.data.data === '-1') {
                proxy.$message.error('该用户已有部门，请勿重复添加！');
              } else {
                handlExisting({ userId: res.data.data, ...params });
              }
            });
          } else {
            checkMobileEmail(params);
          }
        }
      });
    };
    // 已存在，是否邀请或加入
    const handlExisting = params => {
      ElMessageBox({
        title: '提示',
        message: '当前用户名已存在,<br>是否确认发送消息邀请' + state.formDataUser.nickname + '加入？',
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(() => {
          alreadyExist(params).then(res => {
            if (res) {
              proxy.$message.success('成功发送邀请信息');
              showDrawer.value = false;
              context.emit('close', { isRefresh: true });
            }
          });
        })
        .catch(() => {});
    };
    const submitForm = params => {
      if (state.drawerType === 'add') {
        saveEmployees(params).then(res => {
          if (res) {
            proxy.$message.success('员工添加成功！');
            showDrawer.value = false;
            context.emit('close', { isRefresh: true });
          }
        });
      } else {
        updateEmployees(params).then(res => {
          if (res) {
            proxy.$message.success('员工编辑成功！');
            context.emit('close', { isRefresh: true });
          }
        });
      }
    };
    const uploadSuccess = (res, fieldName) => {
      if (res.code === 200) {
        state.formDataUser[fieldName].push(res.data);
      } else {
        proxy.$message.error(res.message);
      }
    };
    const beforeUpload = file => {
      const fileSize = file.size / 1024 / 1024 < 20;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过20M');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };
    const handlePreview = async file => {
      state.drawerLoading = true;
      const response = await downloadById(file.id).finally((state.drawerLoading = false));
      if (response) {
        const blob = new Blob([response.data], { type: '' });
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = `${file.name}`;
        a.href = blobUrl;
        a.click();
        proxy.$message.success('下载附件成功');
      }
    };
    const handleRemove = async (file, fileList, fieldName) => {
      if (file?.id) {
        const response = await deleteFile(file.id);
        if (response) {
          state.formDataUser[fieldName] = fileList;
          proxy.$message.success('删除成功！');
        }
      }
    };
    const beforeRemove = (file, fileList) => {};
    // 所属分类change
    const changeCategory = value => {
      const len = value.length - 1;
      state.formData.deviceCategoryId = value[len];
    };
    return {
      ...toRefs(state),
      getDetail,
      isEmail2,
      isPhoneMobile,
      uploadSuccess,
      beforeUpload,
      handleRemove,
      beforeRemove,
      handlePreview,
      greaterThanZero2,
      idNumberRex,
      changeCategory,
      onSubmit,
      handleClose,
      showDrawer,
      initDetail
    };
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-icon-close-tip, .el-icon-close) {
  display: none !important;
}
</style>
