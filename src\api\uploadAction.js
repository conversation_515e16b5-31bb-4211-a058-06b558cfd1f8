import { getAPIURL } from '@/utils/base-url';

export function deviceMeasurementUploadUrl() {
  return getAPIURL() + '/api-device/device/devicemeasurement/upload';
}

export function deviceImportExcelUploadUrl() {
  return getAPIURL() + '/api-device/device/device/importExcel';
}

export function experimentAttachmentUploadUrl() {
  return getAPIURL() + '/api-experiment/experiment/attachment/upload';
}

export function capabilityAttachmentUploadUrl() {
  return getAPIURL() + '/api-capabilitystd/capability/attachment/upload';
}

export function fiberInventoryReportUploadUrl() {
  return getAPIURL() + '/api-diplomat/diplomat/fiber-inventory/report/upload';
}

export function entrustRegAttachmentUploadUrl(taskId = '') {
  return getAPIURL() + `/api-diplomat/diplomat/entrust_reg_attachment/upload/${taskId}`;
}

export function inspectionOrgAttachmentUploadUrl(id = '') {
  return getAPIURL() + `/api-diplomat/diplomat/inspection_org_attachment/upload/${id}`;
}

export function diyLoginStyleBackgroundUploadUrl() {
  return getAPIURL() + '/api-user/diyLoginStyle/uploadBackground';
}

export function diyLoginStyleLogoUploadUrl() {
  return getAPIURL() + '/api-user/diyLoginStyle/uploadLogo';
}

export function diyLoginStyleWelcomeUploadUrl() {
  return getAPIURL() + '/api-user/diyLoginStyle/uploadWelcome';
}

export function oauthinformationConfigLogoUploadUrl(tenantId = '') {
  return getAPIURL() + `/api-user/user/oauthinformation/config/logo/normal/upload/${tenantId}`;
}

export function sampleTemplateFileUploadUrl() {
  return getAPIURL() + '/api-orders/orders/sampletemplatefile/upload';
}

export function distributionImportUploadUrl() {
  return getAPIURL() + '/api-orders/orders/distribution/import';
}

export function experimentImgRecordUploadUrl() {
  return getAPIURL() + '/api-orders/orders/experiment/imgrecord/save';
}

export function inspectionResultReportUploadUrl() {
  return getAPIURL() + '/api-diplomat/inspection-result/report/upload';
}

export function documentAttachmentUploadUrl() {
  return getAPIURL() + '/api-document/document/attachment/upload';
}

export function categoryCertificateTemplateUploadUrl() {
  return getAPIURL() + '/api-material/categoryCertificateTemplate/upload';
}

export function categoryReportTemplateUploadUrl() {
  return getAPIURL() + '/api-material/material/categoryreporttemplate/upload';
}

export function capabilityAttachmentFileListUploadUrl() {
  return getAPIURL() + '/api-capabilitystd/capability/attachment/fileListUpload';
}

export function capabilityStandardBasisImportExcelUploadUrl() {
  return getAPIURL() + '/api-capabilitystd/capability/capabilityStandardBasis/importExcel';
}

export function uploadAttachmentUrl() {
  return getAPIURL() + '/api-user/user/attachment/uploadAttachment';
}

export function deviceFileUpload() {
  return getAPIURL() + '/api-device/attachment/fileUpload';
}
export function testBaseStandardcategoryImport() {
  return getAPIURL() + '/api-capabilitystd/standard/standardcategory/import';
}
export function fileUploadApi() {
  return getAPIURL() + '/api-orders/orders/attachment/fileUpload';
}
export function plandetailImport() {
  return getAPIURL() + '/api-document/document/plandetail/import';
}

/** 检验标准库-规则导入 */
export function standardMatchingParamsImport() {
  return getAPIURL() + '/api-capabilitystd/capabilitystandard/standardMatchingParams/import';
}
