<template>
  <!--实时数据采集弹框-->
  <el-dialog v-model="dialogShow" width="480px" title="实时数采条件" @close="handleClose" @open="handleOpen">
    <el-form :model="form" label-width="110px" size="small" label-position="right">
      <el-form-item label="开始时间：">
        <el-date-picker
          v-model="startDateTime"
          type="datetime"
          size="small"
          style="width: 100%"
          placeholder="实时数采开始时间"
          :readonly="disabledTimePicker"
        />
      </el-form-item>
      <el-form-item label="写入方式：">
        <el-radio-group v-model="writeMethod" @change="changeWriteMethod">
          <el-radio :label="1">顺序写入</el-radio>
          <el-radio :label="2">自定义写入</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="colorList.length !== 0 && coreExistInfo.coreColourList" label="开始分组：">
        <el-select
          v-model="colorRadio"
          style="width: 100%"
          placeholder="请选择开始采集的第一个试样分组"
          @change="changeRadioColor"
        >
          <el-option v-for="(item, index) in colorList" :key="index" :label="item" :value="index" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="small" @click="handleCancel">取消</el-button>
        <el-button size="small" type="primary" @click="handleCommit" @keyup.prevent @keydown.enter.prevent
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { formatDateTime } from '@/utils/formatTime';
// import { clearInterval, setInterval } from 'timers'
import { computed, reactive, toRefs } from 'vue';
import { useRoute } from 'vue-router';

export default {
  name: 'RealTimeDataDialog',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    activeCapabilityItem: {
      type: String,
      default: ''
    },
    wireColorList: {
      type: Array,
      default() {
        return [];
      }
    },
    wireCoreExistInfo: {
      type: Object,
      default() {
        return {};
      }
    },
    wireColorRadio: {
      type: Number,
      default: 0
    }
  },
  emits: ['close', 'change-color', 'start-real-time', 'set'],
  setup(props, context) {
    const route = useRoute();
    const state = reactive({
      postData: {
        capabilityId: '', // 检测项目id
        deviceNo: route.query.deviceNumber,
        secSampleNum: '',
        startDate: '',
        endDate: '',
        timeType: 0
      },
      timerId: null,
      startDateTime: formatDateTime(new Date()),
      form: {},
      disabledTimePicker: true,
      writeMethod: 1
    });

    const dialogShow = computed({
      get: () => props.dialogVisible,
      set: val => context.emit('close', val)
    });
    const activeItem = computed({
      get: () => props.activeCapabilityItem
    });
    const colorList = computed({
      get: () => props.wireColorList.map(item => item.coreColour)
    });
    const coreExistInfo = computed({
      get: () => props.wireCoreExistInfo
    });
    const colorRadio = computed({
      get: () => props.wireColorRadio,
      set: val => context.emit('change-color', val)
    });

    const changeRadioColor = val => {
      colorRadio.value = val;
    };

    const handleClose = () => {
      clearTimer();
    };

    const handleOpen = () => {
      startTimer();
    };

    const handleCancel = () => {
      clearTimer();
      dialogShow.value = false;
    };

    const handleCommit = () => {
      clearTimer();
      dialogShow.value = false;
      context.emit('start-real-time', {
        startDateTime: state.startDateTime,
        writeMethod: state.writeMethod,
        colorRadio: colorRadio.value
      });
    };

    const startTimer = () => {
      state.startDateTime = formatDateTime(new Date());
      state.timerId = setInterval(() => {
        state.startDateTime = formatDateTime(new Date());
      }, 1000);
    };

    const clearTimer = () => {
      if (state.timerId) {
        clearInterval(state.timerId);
      }
    };

    // #region 写入方式

    const changeWriteMethod = () => {};

    // #endregion

    return {
      ...toRefs(state),
      dialogShow,
      activeItem,
      colorList,
      coreExistInfo,
      colorRadio,
      changeRadioColor,
      handleClose,
      handleOpen,
      handleCancel,
      handleCommit,
      changeWriteMethod
    };
  }
};
</script>
<style scoped>
.date-time-wrapper {
  border: solid 1px lightgray;
}
</style>
