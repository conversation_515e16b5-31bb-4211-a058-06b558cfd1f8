import request from '@/utils/request';

/**
 * 账号管理
 */
// 账号管理列表
export function getAccountList(data) {
  return request({
    url: '/api-user/user/sysuser/users/list',
    method: 'post',
    data
  });
}
// 获取账号信息
export function getAccountInfo(id) {
  return request({
    url: `/api-user/user/sysuser/users/${id}`,
    method: 'get'
  });
}
// 新增账号
export function addAccount(data) {
  return request({
    url: '/api-user/user/sysuser/users/saveOrUpdate',
    method: 'post',
    data
  });
}
// 编辑账号
export function editAccount(data) {
  return request({
    url: '/api-user/user/sysuser/users/saveOrUpdate',
    method: 'post',
    data
  });
}
// 密码重置
export function resetPassword(id) {
  return request({
    url: `/api-user/user/sysuser/admin/password/${id}`,
    method: 'get'
  });
}

/**
 * 登录日志
 */
// 登录日志列表
export function getLogList(data) {
  return request({
    url: '/log/list',
    method: 'post',
    data
  });
}
// 登出
export function logout(data) {
  return request({
    url: '/log/logout',
    method: 'post',
    data
  });
}

/**
 * 租户管理
 */
// 租户列表
export function getTenantList(data) {
  return request({
    url: '/api-user/user/oauthinformation/list',
    method: 'post',
    data
  });
}
// 新增租户
export function addTenant(data) {
  return request({
    url: '/api-user/user/oauthinformation/saveOrUpdate',
    method: 'post',
    data
  });
}
// 编辑租户
export function editTenant(data) {
  return request({
    url: '/tenantmgt/edit',
    method: 'post',
    data
  });
}
// 授权
export function reauthorize(id) {
  return request({
    url: '/api-user/user/oauthinformation/reauthorize/' + id,
    method: 'get'
  });
}
// 获取租户管理员集合
export function findManagerId() {
  return request({
    url: '/api-user/user/oauthinformation/findManagerId',
    method: 'get'
  });
}

// 租户详情
export function detailTenant(data) {
  return request({
    url: '/tenantmgt/detail',
    method: 'post',
    data
  });
}

/**
 * 权限管理
 */
// 权限组-结构树
export function getPermissionTreeList(data) {
  return request({
    url: '/api-user/user/syspermissiontree/listTree',
    method: 'post',
    data
  });
}
// 权限组-添加
export function addPermissionTreeList(data) {
  return request({
    url: '/api-user/user/syspermissiontree/add',
    method: 'post',
    data
  });
}
// 权限组-更新
export function updatePermissionTreeList(data) {
  return request({
    url: '/api-user/user/syspermissiontree/update',
    method: 'post',
    data
  });
}
// 权限组-更新
export function deleteTree(id) {
  return request({
    url: `/api-user/user/syspermissiontree/delete/${id}`,
    method: 'get'
  });
}
// 权限组-更新
export function isCanDelete(id) {
  return request({
    url: `/api-user/user/syspermissiontree/candelete/${id}`,
    method: 'post'
  });
}
// 权限-列表
export function getPermissionList(data) {
  return request({
    url: '/api-user/user/syspermission/list',
    method: 'post',
    data
  });
}
// 权限-新增权限
export function addPermission(data) {
  return request({
    url: '/api-user/user/syspermission/add',
    method: 'post',
    data
  });
}
// 权限-查看明细
export function detailPermission(data) {
  return request({
    url: '/api-user/user/syspermission/detail',
    method: 'post',
    data
  });
}
// 权限-编辑
export function editPermission(data) {
  return request({
    url: '/api-user/user/syspermission/edit',
    method: 'post',
    data
  });
}
// 权限-启用禁用
export function enablePermission(data) {
  return request({
    url: '/api-user/user/syspermission/enable',
    method: 'post',
    data
  });
}
// 权限设置-编辑
export function editMenuPermission(data) {
  return request({
    url: '/api-user/user/syspermissionmenu/edit',
    method: 'post',
    data
  });
}
/**
 * 资源管理
 */
// 资源-列表查询
export function getMenuList(data) {
  return request({
    url: '/api-user/user/sysmenu/listTree',
    method: 'post',
    data
  });
}
// 资源-展开(可以查看展开交互)
export function getMenuDetail(data) {
  return request({
    url: '/api-user/user/sysmenu/detail',
    method: 'post',
    data
  });
}
// 资源-新增
export function addMenu(data) {
  return request({
    url: '/api-user/user/sysmenu/add',
    method: 'post',
    data
  });
}
// 资源-父级菜单的选择
export function parentMenu(data) {
  return request({
    url: '/api-user/user/sysmenu/parent',
    method: 'post',
    data
  });
}
// 资源-编辑
export function editMenu(data) {
  return request({
    url: '/api-user/user/sysmenu/edit',
    method: 'post',
    data
  });
}
// 资源-所属菜单的选择
export function getMenuTree(data) {
  return request({
    url: '/api-user/user/sysmenu/parentmenu',
    method: 'post',
    data
  });
}
// 资源-所属页面的选择
export function getPageTree(data) {
  return request({
    url: '/api-user/user/sysmenu/parentpage',
    method: 'post',
    data
  });
}
// // 资源-禁用
// export function updateMenu(data) {
//   return request({
//     url: '/menu/update',
//     method: 'post',
//     data
//   })
// }
// // 资源-删除
// export function deleteMenu(data) {
//   return request({
//     url: '/menu/delete',
//     method: 'post',
//     data
//   })
// }
