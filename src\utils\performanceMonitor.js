/**
 * 性能监控工具
 * 用于检测可能的无限循环、性能瓶颈和页面卡死问题
 */

class PerformanceMonitor {
  constructor() {
    this.isMonitoring = false;
    this.frameCount = 0;
    this.lastFrameTime = performance.now();
    this.fpsHistory = [];
    this.maxFpsHistoryLength = 60; // 保留60帧的历史记录
    this.lowFpsThreshold = 30; // 低FPS阈值
    this.freezeThreshold = 1000; // 页面冻结阈值（毫秒）
    this.callbacks = {
      onLowFps: [],
      onFreeze: [],
      onRecover: []
    };

    // 绑定方法
    this.checkFrame = this.checkFrame.bind(this);
    this.handleVisibilityChange = this.handleVisibilityChange.bind(this);

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
  }

  /**
   * 开始监控
   */
  startMonitoring() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.frameCount = 0;
    this.lastFrameTime = performance.now();
    this.fpsHistory = [];

    console.log('Performance monitoring started');
    this.scheduleNextFrame();
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    this.isMonitoring = false;
    console.log('Performance monitoring stopped');
  }

  /**
   * 调度下一帧检查
   */
  scheduleNextFrame() {
    if (!this.isMonitoring) return;
    requestAnimationFrame(this.checkFrame);
  }

  /**
   * 检查帧性能
   */
  checkFrame(currentTime) {
    if (!this.isMonitoring) return;

    const deltaTime = currentTime - this.lastFrameTime;
    const fps = 1000 / deltaTime;

    // 记录FPS历史
    this.fpsHistory.push(fps);
    if (this.fpsHistory.length > this.maxFpsHistoryLength) {
      this.fpsHistory.shift();
    }

    // 检查是否有性能问题
    this.checkPerformanceIssues(deltaTime, fps);

    this.frameCount++;
    this.lastFrameTime = currentTime;

    // 调度下一帧
    this.scheduleNextFrame();
  }

  /**
   * 检查性能问题
   */
  checkPerformanceIssues(deltaTime, fps) {
    // 检查页面冻结
    if (deltaTime > this.freezeThreshold) {
      this.handleFreeze(deltaTime);
    }

    // 检查低FPS
    if (fps < this.lowFpsThreshold && this.fpsHistory.length >= 10) {
      const recentAvgFps = this.getRecentAverageFps(10);
      if (recentAvgFps < this.lowFpsThreshold) {
        this.handleLowFps(recentAvgFps);
      }
    }
  }

  /**
   * 处理页面冻结
   */
  handleFreeze(freezeTime) {
    console.warn(`Page freeze detected: ${freezeTime.toFixed(2)}ms`);

    // 触发冻结回调
    this.callbacks.onFreeze.forEach(callback => {
      try {
        callback(freezeTime);
      } catch (error) {
        console.error('Error in freeze callback:', error);
      }
    });

    // 尝试恢复措施
    this.attemptRecovery();
  }

  /**
   * 处理低FPS
   */
  handleLowFps(avgFps) {
    console.warn(`Low FPS detected: ${avgFps.toFixed(2)}`);

    // 触发低FPS回调
    this.callbacks.onLowFps.forEach(callback => {
      try {
        callback(avgFps);
      } catch (error) {
        console.error('Error in low FPS callback:', error);
      }
    });
  }

  /**
   * 尝试恢复
   */
  attemptRecovery() {
    // 清理可能的定时器
    this.clearSuspiciousTimers();

    // 强制垃圾回收（如果可用）
    if (window.gc) {
      try {
        window.gc();
      } catch (error) {
        // 忽略错误
      }
    }

    // 触发恢复回调
    this.callbacks.onRecover.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Error in recovery callback:', error);
      }
    });
  }

  /**
   * 清理可疑的定时器
   */
  clearSuspiciousTimers() {
    // 这里可以添加清理逻辑，比如清理频繁触发的定时器
    console.log('Attempting to clear suspicious timers...');
  }

  /**
   * 获取最近的平均FPS
   */
  getRecentAverageFps(frameCount) {
    if (this.fpsHistory.length < frameCount) {
      return this.fpsHistory.reduce((sum, fps) => sum + fps, 0) / this.fpsHistory.length;
    }

    const recentFrames = this.fpsHistory.slice(-frameCount);
    return recentFrames.reduce((sum, fps) => sum + fps, 0) / recentFrames.length;
  }

  /**
   * 获取当前性能统计
   */
  getPerformanceStats() {
    return {
      frameCount: this.frameCount,
      currentFps: this.fpsHistory.length > 0 ? this.fpsHistory[this.fpsHistory.length - 1] : 0,
      averageFps:
        this.fpsHistory.length > 0 ? this.fpsHistory.reduce((sum, fps) => sum + fps, 0) / this.fpsHistory.length : 0,
      minFps: this.fpsHistory.length > 0 ? Math.min(...this.fpsHistory) : 0,
      maxFps: this.fpsHistory.length > 0 ? Math.max(...this.fpsHistory) : 0,
      isMonitoring: this.isMonitoring
    };
  }

  /**
   * 添加回调函数
   */
  on(event, callback) {
    if (this.callbacks[event]) {
      this.callbacks[event].push(callback);
    }
  }

  /**
   * 移除回调函数
   */
  off(event, callback) {
    if (this.callbacks[event]) {
      const index = this.callbacks[event].indexOf(callback);
      if (index > -1) {
        this.callbacks[event].splice(index, 1);
      }
    }
  }

  /**
   * 处理页面可见性变化
   */
  handleVisibilityChange() {
    if (document.hidden) {
      this.stopMonitoring();
    } else {
      this.startMonitoring();
    }
  }

  /**
   * 销毁监控器
   */
  destroy() {
    this.stopMonitoring();
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);

    // 清空回调
    Object.keys(this.callbacks).forEach(key => {
      this.callbacks[key] = [];
    });
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor();

// 自动开始监控（仅在开发环境）
if (process.env.NODE_ENV === 'development') {
  performanceMonitor.startMonitoring();

  // 添加默认的性能问题处理
  performanceMonitor.on('onFreeze', freezeTime => {
    console.error(`页面冻结 ${freezeTime.toFixed(2)}ms，可能是开发工具拖拽导致的性能问题`);
  });

  performanceMonitor.on('onLowFps', avgFps => {
    console.warn(`检测到低FPS: ${avgFps.toFixed(2)}，可能影响用户体验`);
  });
}

export default performanceMonitor;

/**
 * Vue 3 Composition API 钩子
 */
export function usePerformanceMonitor() {
  const { onMounted, onUnmounted } = require('vue');

  onMounted(() => {
    performanceMonitor.startMonitoring();
  });

  onUnmounted(() => {
    performanceMonitor.stopMonitoring();
  });

  return {
    monitor: performanceMonitor,
    getStats: () => performanceMonitor.getPerformanceStats()
  };
}
