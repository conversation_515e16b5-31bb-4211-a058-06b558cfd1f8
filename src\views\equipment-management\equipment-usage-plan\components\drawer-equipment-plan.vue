<template>
  <!-- 设备使用计划 -->
  <el-drawer
    v-model="showDrawer"
    :title="titleJSON[type]"
    direction="rtl"
    :before-close="handleClose"
    :size="744"
    destroy-on-close
    :close-on-click-modal="false"
    custom-class="page-drawer"
    @opened="handleOpened"
  >
    <DrawerLayout v-loading="drawerLoading" :has-button-group="false" :has-page-header="false">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-position="top" class="form-height-auto">
        <el-row :gutter="4">
          <el-col :span="24">
            <el-form-item label="使用标题：" prop="title">
              <el-input
                ref="inputRef"
                v-model="formData.title"
                v-trim
                maxlength="100"
                placeholder="请输入使用标题"
                @change="handleModify"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="formData.isAllDay === 1" :span="21">
            <el-form-item label="使用时间：" prop="dateArray">
              <el-date-picker
                v-model="formData.dateArray"
                type="daterange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :shortcuts="shortcuts"
                size="small"
                style="width: 100%"
                @change="handleChangeDate"
              />
            </el-form-item>
          </el-col>
          <!-- 不勾选全天的显示 -->
          <el-col v-else :span="21">
            <el-row :gutter="4">
              <el-col :span="6">
                <el-form-item
                  label="使用时间："
                  prop="startTime"
                  :rules="{ required: true, message: '请选择开始日期', trigger: 'change' }"
                >
                  <el-date-picker
                    v-model="formData.startTime"
                    type="date"
                    placeholder="开始日期"
                    size="small"
                    style="width: 100%"
                    @change="
                      val => {
                        return handleFormat(val, 'startTime');
                      }
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5" class="padding_form">
                <el-form-item label="" prop="startTimeMinute">
                  <el-time-select
                    v-model="formData.startTimeMinute"
                    placeholder="开始"
                    start="00:00"
                    step="00:30"
                    end="23:30"
                    :max-time="formData.startTime === formData.finishTime ? formData.finishTimeMinute : ''"
                    style="width: 100%"
                    @change="handleModify"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="2" class="padding_form text-center">
                <el-form-item> 至 </el-form-item>
              </el-col>
              <el-col :span="6" class="padding_form">
                <el-form-item
                  prop="finishTime"
                  :rules="{ required: true, message: '请选择结束日期', trigger: 'change' }"
                >
                  <el-date-picker
                    v-model="formData.finishTime"
                    type="date"
                    placeholder="结束日期"
                    size="small"
                    style="width: 100%"
                    @change="
                      val => {
                        return handleFormat(val, 'finishTime');
                      }
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5" class="padding_form">
                <el-form-item prop="finishTimeMinute">
                  <el-time-select
                    v-model="formData.finishTimeMinute"
                    placeholder="结束"
                    start="00:00"
                    step="00:30"
                    end="23:30"
                    :min-time="formData.startTime === formData.finishTime ? formData.startTimeMinute : ''"
                    style="width: 100%"
                    @change="handleModify"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="3" class="padding_form">
            <el-form-item prop="isAllDay">
              <el-checkbox
                v-model="formData.isAllDay"
                label="全天"
                size="small"
                :true-label="1"
                :false-label="0"
                style="margin-left: 20px"
                @change="handleChangeCheckbox"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="使用人：" prop="userIdList">
              <el-select
                v-model="formData.userIdList"
                multiple
                placeholder="请选择使用人"
                style="width: 99%"
                size="small"
                @change="handleModify"
              >
                <el-option v-for="item in userList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item v-if="type === 'add'" label="仪器设备：" prop="deviceIdList">
              <el-select
                v-model="formData.deviceIdList"
                multiple
                placeholder="请选择仪器设备"
                style="width: 99%"
                size="small"
                @change="handleModify"
              >
                <el-option
                  v-for="item in keyDeviceList"
                  :key="item.deviceId"
                  :label="item.deviceName + ' (' + item.deviceNumber + ')'"
                  :value="item.deviceId"
                >
                  <span>{{ item.deviceName + '（' + item.deviceNumber + '）' }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="type === 'edit'" label="仪器设备：" prop="deviceId">
              <el-select
                v-model="formData.deviceId"
                placeholder="请选择仪器设备"
                style="width: 99%"
                size="small"
                @change="handleModify"
              >
                <el-option
                  v-for="item in keyDeviceList"
                  :key="item.deviceId"
                  :label="item.deviceName + ' (' + item.deviceNumber + ')'"
                  :value="item.deviceId"
                >
                  <span>{{ item.deviceName + '（' + item.deviceNumber + '）' }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="使用描述：" prop="remark">
              <el-input
                v-model="formData.remark"
                type="textarea"
                :rows="3"
                maxlength="300"
                placeholder="请输入描述"
                @change="handleModify"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="drawer-fotter">
        <el-button type="primary" :loading="drawerLoading" @click="onSubmit()">确认</el-button>
        <el-button :loading="drawerLoading" @click="handleClose()">取消</el-button>
      </div>
    </DrawerLayout>
  </el-drawer>
</template>

<script>
import { ref, watch, reactive, getCurrentInstance, toRefs } from 'vue';
import DrawerLayout from '@/components/DrawerLayout';
import store from '@/store';
import { formatDate } from '@/utils/formatTime';
import { durationDate } from '@/utils/validate';
import { devicePlanSave, devicePlanEdit } from '@/api/equipmentUsagePlan';

export default {
  name: 'DrawerEquipmentPlan',
  components: { DrawerLayout },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    drawerType: {
      type: String,
      required: true
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    keyDeviceList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  emits: ['closeDrawer'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // 抽屉事件
    const showDrawer = ref(props.drawer);
    const state = reactive({
      isModified: false,
      drawerLoading: false,
      dialogSelectEquipment: false,
      inputRef: ref(),
      type: '',
      titleJSON: {
        add: '新增计划',
        edit: '编辑计划'
      },
      userList: store.state.common.nameList,
      options: {
        1: '年',
        2: '月'
      },
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ],
      isShowChangeToday: false,
      weeksArray: [],
      detailData: {}, // 详情数据
      formRef: ref(),
      keyDeviceList: [], // 重点设备列表
      formData: {
        dateArray: [],
        deviceIdList: []
      },
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      }
    });
    const formRules = {
      title: [{ required: true, message: '请输入使用标题', trigger: 'change' }],
      dateArray: [{ required: true, message: '请选择使用时间', trigger: 'change' }],
      startTime: [
        { required: true, message: '请选择开始日期', trigger: 'change' },
        {
          validator: durationDate,
          value: () => ({
            startDate: formatDate(state.formData.startTime),
            endDate: formatDate(state.formData.finishTime)
          }),
          message: '不能大于结束日期',
          trigger: 'change'
        }
      ],
      startTimeMinute: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
      finishTime: [
        { required: true, message: '请选择结束日期', trigger: 'change' },
        {
          validator: durationDate,
          value: () => ({
            startDate: formatDate(state.formData.startTime),
            endDate: formatDate(state.formData.finishTime)
          }),
          message: '不能小于开始日期',
          trigger: 'change'
        }
      ],
      finishTimeMinute: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
      userIdList: [{ required: true, message: '请选择使用人', trigger: 'change' }],
      deviceIdList: [{ required: true, message: '请选择仪器设备', trigger: 'change' }],
      deviceId: [{ required: true, message: '请选择仪器设备', trigger: 'change' }]
    };
    // 关闭抽屉
    const handleClose = () => {
      if (state.isModified) {
        proxy
          .$confirm('确认离开当前页面吗？离开后数据不可恢复', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false
          })
          .then(() => {
            showDrawer.value = false;
            state.isModified = false;
            context.emit('closeDrawer', false);
          })
          .catch(() => {});
      } else {
        showDrawer.value = false;
        context.emit('closeDrawer', false);
      }
    };
    // 格式化日期
    const handleChangeDate = val => {
      if (val?.length) {
        state.formData.startTime = formatDate(val[0]);
        state.formData.finishTime = formatDate(val[1]);
      } else {
        state.formData.startTime = '';
        state.formData.finishTime = '';
      }
    };
    watch(props, newValue => {
      showDrawer.value = newValue.drawer;
      if (showDrawer.value) {
        state.keyDeviceList = props.keyDeviceList;
        state.isModified = false;
        state.type = props.drawerType;
        if (props.drawerType === 'add') {
          initDetail(props.detailData);
        } else {
          getDetail(props.detailData);
        }
      }
    });
    const handleOpened = () => {
      if (state.inputRef) {
        state.inputRef.focus();
      }
    };
    const handleModify = () => {
      state.isModified = true;
    };
    const initDetail = newData => {
      state.formData = {
        ...newData,
        deviceIdList: newData.deviceIdList,
        dateArray: [newData.startTime, newData.finishTime]
      };
    };
    const getDetail = newData => {
      const detailInfo = JSON.parse(JSON.stringify(newData));
      state.formData = {
        ...detailInfo,
        dateArray: [detailInfo.startTime, detailInfo.finishTime],
        userIdList: detailInfo.userIdList
      };
    };
    // 新增、编辑
    const onSubmit = () => {
      proxy.$refs['formRef'].validate(valid => {
        if (valid) {
          if (
            new Date(state.formData.startTime + ' ' + state.formData.startTimeMinute).getTime() >
            new Date(state.formData.finishTime + ' ' + state.formData.finishTimeMinute).getTime()
          ) {
            proxy.$message.warning('开始时间不能小于结束时间');
            return false;
          }
          state.drawerLoading = true;
          const params = {
            ...state.formData,
            userIdList: state.formData.userIdList
          };
          delete params.dateArray;
          if (state.type === 'add') {
            devicePlanSave(params).then(res => {
              state.drawerLoading = false;
              if (res) {
                state.isModified = false;
                proxy.$message.success('保存成功');
                context.emit('closeDrawer', true);
              }
            });
          } else {
            devicePlanEdit(params).then(res => {
              state.drawerLoading = false;
              if (res) {
                state.isModified = false;
                proxy.$message.success('修改成功!');
                context.emit('closeDrawer', true);
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    // 切换全天和半天
    const handleChangeCheckbox = val => {
      if (val) {
        if (state.formData.startTime) {
          state.formData.dateArray = [state.formData.startTime, state.formData.finishTime];
        } else {
          state.formData.dateArray = [];
        }
        state.formData.startTimeMinute = '';
        state.formData.finishTimeMinute = '';
      } else {
        state.formData.dateArray = [];
      }
      handleModify();
    };
    // 格式化日期
    const handleFormat = (val, fieldName) => {
      if (val) {
        state.formData[fieldName] = formatDate(val);
      }
      handleModify();
    };
    return {
      ...toRefs(state),
      getDetail,
      formatDate,
      formRules,
      handleOpened,
      handleFormat,
      handleChangeCheckbox,
      handleModify,
      handleChangeDate,
      onSubmit,
      handleClose,
      showDrawer
    };
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-input--medium) {
  line-height: 1;
}
.el-upload__tip {
  line-height: 20px;
  margin-top: 6px;
  font-size: 12px;
  color: #606266;
}
.padding_form {
  padding: 32px 0 0 0;
}
</style>
