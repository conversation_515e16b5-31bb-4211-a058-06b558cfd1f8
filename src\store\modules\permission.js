import { asyncRoutes, constantRoutes } from '@/router';
import router from '@/router';
// import { getMenuIdList } from '@/utils/formatJson'
import { checkPermissionBtnApi, checkConfigJSON } from '@/api/permission';
import { setLIMSConfig } from '@/utils/auth';
import { ElLoading, ElMessage } from 'element-plus';
import store from '@/store';

const state = {
  routes: [],
  config: {},
  addRoutes: [],
  perssionBtns: {}
};

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes;
    state.routes = constantRoutes.concat(routes);
  },
  SET_perssionBtns: (state, btns) => {
    state.perssionBtns = btns;
  },
  SET_LIMS_CONFIG: (state, data) => {
    console.log(data);
    state.config = data;
  }
};

const actions = {
  generateRoutes({ commit }, menus) {
    // getMenuIdList(constantRoutes, roles)
    return new Promise(resolve => {
      var accessedRoutes = asyncRoutes || [];
      // if (menus && menus.length > 0) {
      //   accessedRoutes = filterAsyncRoutes(accessedRoutes, menus, {})
      // }
      // console.log(accessedRoutes)
      // accessedRoutes.forEach(ar => {
      //   router.addRoute(ar)
      // })
      commit('SET_ROUTES', accessedRoutes);
      resolve(accessedRoutes);
    });
  },
  getcheckPermissionBtn({ commit }, params) {
    const loading = ElLoading.service({
      lock: true,
      text: '权限加载中...',
      background: 'rgba(255, 255, 255, 0.7)'
    });
    return new Promise((resolve, reject) => {
      var list = {};
      checkPermissionBtnApi()
        .then(response => {
          if (response !== false) {
            const controlList = response.data.data;
            if (controlList && controlList.length !== 0) {
              controlList.forEach((item, index) => {
                list[item.key] = true;
                if (controlList.length - 1 === index) {
                  localStorage.setItem('permissionBtnlist', JSON.stringify(list));
                  commit('SET_perssionBtns', list);
                  loading.close();
                  resolve(list);
                }
              });
            } else {
              resolve(list);
              loading.close();
            }
          } else {
            loading.close();
            ElMessage.error('权限获取失效,请重新登录！');
            store.dispatch('user/logout');
          }
        })
        .catch(error => {
          loading.close();
          console.log(error);
        });
    });
  },
  getLIMSConfig({ commit }, params) {
    const loading = ElLoading.service({
      lock: true,
      text: '资源加载中...',
      background: 'rgba(255, 255, 255, 0.7)'
    });
    return new Promise((resolve, reject) => {
      checkConfigJSON()
        .then(response => {
          loading.close();
          if (response !== false) {
            setLIMSConfig(response.data);
            commit('SET_LIMS_CONFIG', response.data);
            resolve(response.data);
          } else {
            ElMessage.error('资源加载失败！');
          }
        })
        .catch(error => {
          loading.close();
          console.log(error);
        });
    });
  },
  getPagePermission({ commit }, datas) {
    return new Promise((resolve, reject) => {
      // checkPagePermission(id).then(res => {
      //   // console.log(res.data.access)
      //   if (res !== false) {
      //     resolve(res.data.access)
      //   } else {
      //     ElMessage.error('权限获取失效,请重新登录！')
      //     store.dispatch('user/logout')
      //   }
      // }).catch(error => {
      //   console.log(error)
      // })
      // console.log(router.getRoutes())
      const routes = router.getRoutes();
      var flag = false;
      if (datas.meta.title) {
        flag = routes.some(item => datas.meta.title === item.meta.title);
      } else if (
        datas.name === 'FilePreview' ||
        datas.name === 'ReportPreview' ||
        datas.name === 'ReportPdfPreview' ||
        datas.name === 'AddOrUpdateOperationSteps'
      ) {
        flag = true;
      } else {
        flag = routes.some(item => item.path === datas.path);
      }
      if (datas.name === undefined && datas.path === undefined) {
        resolve(false);
      } else if (datas.path && !flag) {
        resolve(false);
      }
      resolve(true);
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
