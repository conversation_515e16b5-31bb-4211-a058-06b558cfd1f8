client_max_body_size 1000M;
client_body_buffer_size 1000M;
server {
    listen 80;
    sendfile on;
    default_type application/octet-stream;
    gzip on;
    gzip_http_version 1.1;
    gzip_disable "MSIE [1-6]\.";
    gzip_min_length 256;
    gzip_vary on;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_comp_level 9;
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
    add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
    root /usr/share/nginx/html;
    location / {
        try_files $uri $uri/ /index.html =404;
    }

    location /api {
        rewrite /api/?(.*)$ /$1 break;
        proxy_pass ${API_GATEWAY};
        proxy_set_header Host $host;
        proxy_set_header Cookie $http_cookie;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_redirect default;
        proxy_buffer_size         128k;
    	proxy_buffers             4 256k;
    	proxy_busy_buffers_size   256k;
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Headers X-Requested-With;
        add_header Access-Control-Allow-Methods GET,POST,DELETE,PUT,PATCH,OPTIONS;

    }

    location /minio {
        proxy_pass ${MINIO_HOST};
        proxy_redirect default;
        proxy_set_header Host $host;
    }

    
    location /attachmentview/ {
        # Allow any size file to be uploaded.
        # Set to a value such as 1000m; to restrict file size to a specific value
        client_max_body_size 0;
        # Disable buffering
        proxy_buffering off;
        proxy_request_buffering off;

        resolver 127.0.0.11;

        # proxy_set_header Host $http_host;
        # proxy_set_header Authorization "";
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 300;
        # Default is HTTP/1, keepalive is only enabled in HTTP/1.1
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        chunked_transfer_encoding off;

        rewrite ^/attachmentview/(.*)$ /$1 break;
        proxy_pass ${MINIO_HOST}; # This uses the upstream directive definition to load balance
    }

    location /minioview {
        # Allow any size file to be uploaded.
        # Set to a value such as 1000m; to restrict file size to a specific value
        client_max_body_size 0;
        # Disable buffering
        proxy_buffering off;
        proxy_request_buffering off;

        resolver 127.0.0.11;

        # proxy_set_header Host $http_host;
        # proxy_set_header Authorization "";
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 300;
        # Default is HTTP/1, keepalive is only enabled in HTTP/1.1
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        chunked_transfer_encoding off;

        rewrite ^/attachmentview/(.*)$ /$1 break;
        proxy_pass ${MINIO_HOST}; # This uses the upstream directive definition to load balance
    }

  
    location /message {
        proxy_pass ${WEBSOCKET_HOST};

        # 启用 HTTP/1.1
        proxy_http_version 1.1;

        # 设置 Upgrade 和 Connection 头
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # 调整超时设置
        proxy_read_timeout 3600s;  # WebSocket 长连接超时时间
        # proxy_send_timeout 60m;  # 发送超时时间
        # proxy_connect_timeout 5s;  # 连接超时时间

        # 传递客户端的主机头和真实 IP 地址
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 禁用缓存，确保实时传输
        proxy_buffering off;

        # 禁用重定向 可选，因为 WebSocket 不会触发重定向
        proxy_redirect off;
    }

    location /manage {
        resolver 127.0.0.11;
        proxy_pass ${MANAGE_HOST};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Host $http_host;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /qms {
        resolver 127.0.0.11;
        proxy_pass ${QMS_HOST};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Host $http_host;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /template-editor {
        resolver 127.0.0.11;
        proxy_pass ${TEMPLATE_EDITOR_HOST};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Host $http_host;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
