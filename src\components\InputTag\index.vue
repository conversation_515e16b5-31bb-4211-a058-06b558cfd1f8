<template>
  <div class="input-tags">
    <el-tag v-for="tag in inputTags" :key="tag" closable type="info" @close="removeTag(tag)">
      {{ tag }}
    </el-tag>
    <el-input v-model="input1" placeholder="请输入" @change="changeTags" />
  </div>
</template>

<script>
import { reactive, toRefs, watch } from 'vue';

export default {
  name: 'InputTags',
  components: {},
  props: {
    tags: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['setInfo'],
  setup(props, context) {
    const datas = reactive({
      inputTags: ''
    });

    watch(
      () => props,
      newValue => {
        console.log(newValue);
        if (newValue) {
          datas.inputTags = props.tags;
        }
      },
      { deep: true }
    );

    const changeTags = v => {
      datas.inputTags.push(v);
      datas.input1 = '';
      context.emit('setInfo', datas.inputTags);
    };

    const removeTag = tag => {
      datas.inputTags.splice(datas.inputTags.indexOf(tag), 1);
      context.emit('setInfo', datas.inputTags);
    };

    return { ...toRefs(datas), changeTags, removeTag };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.input-tags {
  width: 100%;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .el-tag {
    margin: 0px 2px;
  }
  :deep(.el-input) {
    width: 20%;
    border: 0px;
    input {
      border: 0px;
    }
  }
}
</style>
