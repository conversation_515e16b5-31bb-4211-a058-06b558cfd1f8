<template>
  <!--检测执行-->
  <ListLayout>
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="param">
          <div style="width: 42vw">
            <CombinationQuery
              :field-list="searchFieldList"
              field-tip="查询内容"
              @get-single-text="getSingleText"
              @get-param-list="getParamList"
              @reset-search="reset"
            />
          </div>
        </el-form-item>
        <el-form-item>
          <el-button class="searchBtn" type="text" size="large" @click="search" @keyup.prevent @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
          /></el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        v-if="isShowItem"
        type="primary"
        size="large"
        style="margin-right: 0.5rem"
        :loading="tableLoading"
        @click="exportExcel()"
        ><span class="iconfont tes-task-issued" @keyup.prevent @keydown.enter.prevent /> 导出</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="searchFrom" :model="formInline" label-width="120px" label-position="right" size="small">
            <el-form-item label="试验负责人：" prop="supervisorId" class="seal-staff-name">
              <el-select
                v-model="formInline.supervisorId"
                placeholder="请选择"
                size="small"
                clearable
                filterable
                :filter-method="filterUserList"
                @focus="filterUserList(null)"
              >
                <el-option v-for="item in state.nameList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="试验员：" prop="ownerId" class="seal-staff-name">
              <el-select
                v-model="formInline.ownerId"
                placeholder="请选择"
                size="small"
                clearable
                filterable
                :filter-method="filterUserList1"
                @focus="filterUserList(null)"
                @change="changeOwnerId"
              >
                <el-option v-for="item in state.nameList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="数据修改：" prop="isEditMore" class="seal-staff-name">
              <el-radio-group
                v-model="formInline.isEditMore"
                size="small"
                @change="
                  val => {
                    return handleChangeRadio(val, 'isEditMore');
                  }
                "
              >
                <el-radio-button style="margin-right: 4px">不限</el-radio-button>
                <el-radio-button :label="true" class="label-type">存在</el-radio-button>
                <el-radio-button :label="false" class="label-type">不存在</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="复测项目：" prop="isRetest" class="seal-staff-name">
              <el-radio-group
                v-model="formInline.isRetest"
                size="small"
                @change="
                  val => {
                    return handleChangeRadio(val, 'isRetest');
                  }
                "
              >
                <el-radio-button style="margin-right: 4px">不限</el-radio-button>
                <el-radio-button :label="true" class="label-type">存在</el-radio-button>
                <el-radio-button :label="false" class="label-type">不存在</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <div class="flex justify-between items-center">
        <div class="flex gap-8">
          <el-radio-group v-model="formInline.status" size="small" @change="changeStatus">
            <el-radio-button label="-1">全部</el-radio-button>
            <!-- <el-radio-button v-for="(item, index) in status" :key="index" :label="item.value">{{
              item.label
            }}</el-radio-button> -->
            <el-radio-button label="2">待提交</el-radio-button>
            <el-radio-button label="3">待审核</el-radio-button>
            <el-radio-button label="5">已通过</el-radio-button>
            <el-radio-button v-if="getPermissionBtn('overdueUnSubmit')" label="6">超期待提交</el-radio-button>
          </el-radio-group>
          <el-checkbox v-model="isOnlyMe" size="mini" style="margin: 0" @change="changeOnlyMe">仅看我的</el-checkbox>
          <el-checkbox v-model="formInline.isFavorite" :true-label="1" :false-label="0" size="mini" @change="getList()"
            >我的收藏</el-checkbox
          >
          <el-select
            v-if="getPermissionBtn('inspectionType')"
            v-model="formInline.type"
            placeholder="请选择检验类型"
            size="small"
            clearable
            filterable
            @change="getList()"
          >
            <el-option v-for="(val, key) in dictionary['JYLX'].enable" :key="key" :label="val" :value="Number(key)" />
          </el-select>

          <el-select
            v-if="getPermissionBtn('registerDepartment')"
            v-model="formInline.registerDepartment"
            class="owner-select"
            filterable
            placeholder="请选择送检部门"
            size="small"
            clearable
            @change="getList()"
          >
            <el-option v-for="(val, key) in dictionary['JHDW']?.enable" :key="key" :label="val" :value="val" />
          </el-select>
        </div>
        <el-radio-group v-model="viewType" @change="changeViewType">
          <el-radio-button :label="1">样品视图</el-radio-button>
          <el-radio-button :label="2">项目视图</el-radio-button>
        </el-radio-group>
      </div>
    </template>
    <el-table
      ref="state.multipleSelection"
      :key="tableKey"
      v-loading="tableLoading"
      :data="state.tableList"
      size="medium"
      fit
      border
      height="auto"
      class="dark-table test-item-table base-table format-height-table"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column label="序号" align="center" :width="70">
        <template #default="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column
        label="样品编号"
        prop="secSampleNum"
        :width="colWidth.orderNo"
        align="left"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span v-if="row.isUrgent" class="urgent">急</span>
          <span v-copy="row.secSampleNum" class="blue" @click="handleDetail(row)">{{
            row.secSampleNum ? row.secSampleNum : '--'
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isShowItem"
        label="检测项目"
        prop="capabilityName"
        :min-width="colWidth.name"
        align="left"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <i v-if="row.isFavorite === 1" class="icon el-icon-star-on starInit" />
          <span
            :key="row.capabilityName"
            v-copy="row.capabilityName"
            class="blue"
            @click="handleCheckOpera(row, row.status)"
          >
            {{ row.capabilityName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="!isShowItem"
        :label="tenantInfo.type === 1 ? '申请单号' : '委托编号'"
        prop="presentationCode"
        :width="colWidth.orderNo"
        align="left"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span :key="row.presentationCode" class="nowrap">{{
            row.presentationCode ? row.presentationCode : '--'
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="生产订单号"
        :width="colWidth.orderNo"
        prop="productionOrderNo"
        align="left"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span class="nowrap">{{ row.productionOrderNo }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="销售订单号"
        :width="colWidth.orderNo"
        prop="salesOrderNo"
        align="left"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span class="nowrap">{{ row.salesOrderNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="检验类型" prop="type" align="left" :min-width="colWidth.person">
        <template #default="{ row }">
          {{ dictionary['JYLX'].all[row.type] || row.type || '--' }}
        </template>
      </el-table-column>
      <el-table-column v-if="!isShowItem" label="完成情况" prop="schedule" :width="150">
        <template #header>
          <span
            >完成情况
            <el-tooltip content="待提交/待审核/已通过" placement="top" effect="light">
              <i class="iconfont tes-title" />
            </el-tooltip>
          </span>
        </template>
        <template #default="{ row }">
          <span>{{ row.schedule }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="tenantInfo.type === 0 ? '来样日期' : '登记日期'"
        prop="registerTime"
        :width="colWidth.date"
        sortable
      >
        <template #default="{ row }">
          <span>{{ row.registerTime ? formatDate(row.registerTime) : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="试验负责人" prop="ownerId" align="left" :min-width="colWidth.person">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.ownerId) || row.ownerId || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="试验员" prop="user" align="left" :min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.ownerIds">
            <UserTag
              v-for="(item, index) in row.ownerIds.split(',')"
              :key="index"
              :name="getNameByid(item) || item || '--'"
            />
          </div>
          <UserTag v-else name="--" />
        </template>
      </el-table-column>
      <el-table-column label="是否超期" prop="wzfl" :width="colWidth.status" align="left" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="row.overdue ? 'danger' : 'success'">{{
            row.overdue ? '超期' : '正常'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        v-if="tenantInfo.type === 0"
        label="物资分类"
        prop="wzfl"
        :width="colWidth.typeGroup"
        align="left"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.mateTypeStr ? row.mateTypeStr : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="tenantInfo.type === 1"
        label="物料分组"
        prop="wzfl"
        :width="colWidth.name"
        align="left"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.materialGroup ? row.materialGroup : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="isShowItem" label="状态" prop="status" :width="colWidth.status">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="status[row.status]?.class">{{ status[row.status]?.label }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column v-if="isShowItem" label="开始时间" prop="startDate" :width="colWidth.date" sortable>
        <template #default="{ row }">
          <span>{{ row.startDate ? formatDate(row.startDate) : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="isShowItem" label="结束时间" prop="finishedDate" :width="colWidth.date" sortable>
        <template #default="{ row }">
          <span>{{ row.finishedDate == '' ? '-' : formatDate(row.finishedDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="样品名称" prop="name" align="left" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.sampleName ? row.sampleName : '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="型号规格" prop="prod_type" align="left" :min-width="colWidth.model" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">
            <span>{{ row.prodType ? row.prodType : '--' }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="tenantInfo.type === 1"
        label="检验对象"
        prop="ProductionOrderNo"
        align="left"
        :width="colWidth.objectNo"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div>
            {{ [1, 8].includes(row.type) ? row.inputWarehouseNo || '--' : row.productionOrderNo || '--' }}
          </div>
        </template>
      </el-table-column>
      <!--对象位置-->
      <el-table-column
        v-if="tenantInfo.type === 1"
        label="对象位置"
        prop="wareHouseName"
        align="left"
        :width="colWidth.name"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div v-if="[1, 8].includes(row.type)" class="nowrap">
            {{ row.wareHouseName ? row.wareHouseName : '--' }}
          </div>
          <div v-else class="nowrap">
            {{ row.productionProcedure ? row.productionProcedure : '-'
            }}{{ row.productionStation ? row.productionStation : '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="tenantInfo.type === 1"
        label="对象名称"
        prop="customerName"
        align="left"
        :width="colWidth.name"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ [1, 8].includes(row.type) ? row.supplierName || '--' : row.customerName || '--' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="送检部门" prop="registerDepartment" align="left" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.registerDepartment }}
        </template>
      </el-table-column>

      <el-table-column
        v-if="tenantInfo.type === 1"
        label=" 批次"
        prop="batchNo"
        align="left"
        :width="colWidth.batch"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="nowrap">{{ row.batchNo ? row.batchNo : '--' }}</div>
        </template>
      </el-table-column>

      <el-table-column
        label="客户批号"
        prop="certificateReelNo"
        align="left"
        :width="colWidth.batch"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="nowrap">{{ row.certificateReelNo ? row.certificateReelNo : '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="特殊标识" prop="nuclearMarker" align="left" :width="colWidth.batch" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.nuclearMarker ? row.nuclearMarker : '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="环保标识" prop="identifier" align="left" :width="colWidth.batch" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.identifier ? row.identifier : '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="生产日期"
        prop="certificateSuppDate"
        align="left"
        :width="colWidth.batch"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="nowrap">{{ row.certificateSuppDate ? row.certificateSuppDate : '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="寿期" prop="validityDate" align="left" :width="colWidth.batch" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.validityDate ? row.validityDate : '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="tenantInfo.type === 1"
        label="盘号"
        prop="reelNo"
        align="left"
        :width="colWidth.plate"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="nowrap">{{ row.reelNo ? row.reelNo : '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="合同号" prop="salesOrderNo" align="left" :width="colWidth.plate" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.salesOrderNo || row.purchaseNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="样品数量" prop="sampleNum" align="left" :width="colWidth.amount" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.sampleNum" class="nowrap">
            {{ row.sampleNum }}{{ filterSampleUnitToName(row.unitName) || row.unitName }}
          </div>
          <div v-else class="nowrap">--</div>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        prop="caozuo"
        fixed="right"
        :width="colWidth.operationSingle"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <span v-if="isShowItem" class="blue-color" @click="handleCheckOpera(row)">{{
            row.status == '1' ? '执行' : '查看'
          }}</span>
          <span v-if="!isShowItem" class="blue-color" @click="handleCheckOpera(row)">查看</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="state.total > 0"
      :page="formInline.page"
      :limit="formInline.limit"
      class="tablePage"
      :total="state.total"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getList"
    />
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, watch, getCurrentInstance } from 'vue';
import router from '@/router/index.js';
import Pagination from '@/components/Pagination';
import store from '@/store';
import ListLayout from '@/components/ListLayout';
import UserTag from '@/components/UserTag';
import { formatDate } from '@/utils/formatTime';
import { filterSampleUnitToName } from '@/utils/formatJson';
import { materialCategoryList } from '@/api/material';
import { getTableList, getsampleList } from '@/api/execution';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import _ from 'lodash';
import { checkPermissionList } from '@/api/permission';
import { permissionTypeList } from '@/utils/permissionList';
import { mapGetters } from 'vuex';
// import { getInspectionList } from '@/api/inspection-application'
import { colWidth } from '@/data/tableStyle';
import CombinationQuery from '@/components/CombinationQuery';
import { setItemFirstRow } from '@/services/testExecutionService';
import { getDictionary } from '@/api/user';

export default {
  name: 'TestExecutionList',
  components: { Pagination, ListLayout, UserTag, CombinationQuery },
  setup(content) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      isShowItem: false, // true显示项目表格，false显示试验表格
      experimentOwnerList: [],
      tableLoading: false,
      allList: [],
      mangeList: [],
      status: {
        2: { class: 'info', label: '待提交', value: '2' },
        3: { class: 'warning', label: '待审核', value: '3' },
        5: { class: 'success', label: '已通过', value: '5' },
        6: { class: 'warning', label: '超期待提交', value: '6' }
      },
      viewType: 1,
      dictionary: {
        JYLX: {
          enable: {},
          all: {}
        },
        JHDW: {
          enable: {},
          all: {}
        }
      },
      searchFieldList: [],
      isOnlyMe: true,
      formInline: {
        status: '2',
        tableQueryParamList: [],
        page: 1,
        isFavorite: 0,
        limit: 20,
        key: '',
        ownerId: getLoginInfo().accountId,
        isAsc: false,
        orderBy: '',
        supervisorId: '',
        tenantType: store.state.user.tenantInfo.type,
        mateType: '',
        registerDepartment: ''
      },
      // getLoginInfo().accountId
      tableList: [],
      nameList: store.state.common.nameList,
      nameList1: store.state.common.nameList,
      copyNameList: store.state.common.nameList,
      samplesInfo: {},
      multipleSelection: [],
      total: 0,
      warehouseDateRange: [],
      orderDateRange: [],
      warehouseDate: '全部',
      orderDate: '全部'
    });
    const editFrom = ref(null);
    const searchFrom = ref(null);
    const activeName = ref('0');
    const showS = ref(false);
    const otherForm = reactive({
      types: [],
      userOptions: [
        { value: '1', label: '张三' },
        { value: '2', label: '李四' }
      ],
      list: [],
      content: ''
    });
    const activeTabsName = ref('first');
    const tableKey = ref(0);
    function onSubmit() {
      getList();
    }
    function reset() {
      editFrom.value.resetFields();
      searchFrom.value.resetFields();
      state.isOnlyMe = true;
      state.formInline = {
        status: '2',
        page: 1,
        limit: 20,
        tenantType: store.state.user.tenantInfo.type,
        tableQueryParamList: [],
        key: '',
        isAsc: false,
        orderBy: '',
        supervisorId: '',
        isFavorite: '0',
        mateType: '',
        ownerId: getLoginInfo().accountId
      };
      getList();
      // state.total = 0
    }
    const getDictionaryList = () => {
      Object.keys(state.dictionary).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          state.dictionary[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              state.dictionary[item].enable[optionItem.code] = optionItem.name;
            }
            state.dictionary[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    getDictionaryList();
    const initFieldList = () => {
      state.searchFieldList = [
        {
          field: 'secSampleNum',
          name: '样品编号'
        },
        {
          field: 'capabilityName',
          name: '检测项目'
        },
        {
          field: 'sampleName',
          name: '样品名称'
        },
        {
          field: 'prodType',
          name: '型号规格'
        }
      ];
      let addArray = [];
      if (state.formInline.tenantType === 1) {
        addArray = [
          {
            field: 'materialGroup',
            name: '物料分组'
          },
          {
            field: 'materialNo',
            name: '物料编号'
          },
          {
            field: 'reelNo',
            name: '盘号'
          },
          {
            field: 'batchNo',
            name: '批次'
          },
          {
            field: 'supplierName',
            name: '供应商名称'
          },
          {
            field: 'customerName',
            name: '客户名称'
          },
          {
            field: 'wareHouseName',
            name: '仓库名称',
            isNotQuery: 0
          },
          {
            field: 'productionProcedure',
            name: '生产工序',
            isNotQuery: 0
          },
          {
            field: 'productionStation',
            name: '生产机台',
            isNotQuery: 0
          },
          {
            field: 'inputWarehouseNo',
            name: '入库单号',
            isNotQuery: 0
          },
          {
            field: 'productionOrderNo',
            name: '生产单号',
            isNotQuery: 0
          },
          {
            field: 'salesOrderNo',
            name: '销售订单号',
            isNotQuery: 0
          }
        ];
      } else if (state.formInline.tenantType === 0) {
        addArray = [
          {
            field: 'mateTypeStr',
            name: '物资分类'
          }
        ];
      }
      state.searchFieldList = state.searchFieldList.concat(addArray);
    };
    initFieldList();
    // 获取试验员
    checkPermissionList(permissionTypeList.experimentOwner).then(res => {
      state.experimentOwnerList = res.data.data;
    });
    // 试验负责人
    checkPermissionList(permissionTypeList.sampleOwner).then(res => {
      state.mangeList = res.data.data;
    });
    const search = () => {
      showS.value = !showS.value;
      if (activeName.value === '0') {
        activeName.value = '1';
      } else {
        activeName.value = '0';
      }
    };
    materialCategoryList('').then(resdata => {
      const materialListByP = resdata.data.data;
      otherForm.types = [];
      materialListByP.forEach(m => {
        if (m.parentId === '0') {
          otherForm.types.push(m);
        }
      });
    });
    // 改变仅看我的
    const changeOnlyMe = val => {
      if (val) {
        state.formInline.ownerId = getLoginInfo().accountId;
      } else if (state.formInline.ownerId === getLoginInfo().accountId) {
        state.formInline.ownerId = '';
      }
      getList();
    };
    const changeOwnerId = val => {
      if (val === getLoginInfo().accountId) {
        state.isOnlyMe = true;
      } else {
        state.isOnlyMe = false;
      }
    };
    const getList = query => {
      if (query) {
        state.formInline.page = query.page;
        state.formInline.limit = query.limit;
      }
      const searchdata = JSON.parse(JSON.stringify(state.formInline));
      searchdata.page = JSON.stringify(searchdata.page);
      searchdata.limit = JSON.stringify(searchdata.limit);
      searchdata.key = searchdata.key.trim();
      if (state.isShowItem) {
        state.tableLoading = true;
        getTableList(searchdata).then(res => {
          state.tableLoading = false;
          if (res && res.data.code === 200) {
            state.total = res.data.data.totalCount;
            state.tableList = res.data.data.list;
          }
        });
      } else {
        state.tableLoading = true;
        getsampleList(searchdata).then(res => {
          state.tableLoading = false;
          if (res && res.data.code === 200) {
            state.total = res.data.data.totalCount;
            state.tableList = res.data.data.list;
          }
        });
      }
    };
    getList();
    const getSingleText = val => {
      state.formInline.param = val;
      state.formInline.tableQueryParamList = [];
      getList();
    };

    const getParamList = paramList => {
      state.formInline.tableQueryParamList = paramList;
      state.formInline.param = '';
      getList();
    };
    // state.warehouseDate
    const changeStatus = val => {
      getList();
    };

    const changeViewType = val => {
      state.isShowItem = val === 2;
      state.formInline.orderBy = '';
      state.formInline.isAsc = false;
    };

    // 导出
    const exportExcel = () => {
      state.tableLoading = true;
      getTableList({
        ...state.formInline,
        limit: '-1',
        page: '1',
        isFavorite: state.formInline.isFavorite ? '1' : '0'
      }).then(res => {
        state.tableLoading = false;
        if (res) {
          state.allList = [];
          res.data.data.list.forEach(row => {
            // 登记日期/来样日期
            row.registerTime = formatDate(row.registerTime);
            // 登记人
            row.ownerId = getNameByid(row.ownerId) ? getNameByid(row.ownerId) : row.ownerId;
            // 开始时间
            row.startDate = formatDate(row.startDate);
            // 结束时间
            row.finishedDate = formatDate(row.finishedDate);
            // 试验员
            row.ownerIds = getNamesByid(row.ownerIds) ? getNamesByid(row.ownerIds) : row.ownerIds;
            // 是否超期
            row.overdue = row.overdue ? '超期' : '正常';
            // 状态
            row.status = state.status[row.status]?.label;
            // 检验对象
            row.jydx = row.type === 1 ? row.inputWarehouseNo || '--' : row.productionOrderNo;
            // 对象位置
            if (row.type === 1) {
              row.dxwz = row.wareHouseName;
            } else {
              row.dxwz = row.productionProcedure + row.productionStation;
            }
            // 对象名称
            row.dxmc = row.type === 1 ? row.supplierName : row.customerName;
            state.allList.push(row);
          });
          var tHeader = [];
          var filterVal = [];
          if (state.formInline.tenantType === 0) {
            tHeader = [
              '样品编号',
              '检测项目',
              '来样日期',
              '试验负责人',
              '试验员',
              '是否超期',
              '物资分类',
              '状态',
              '开始时间',
              '结束时间',
              '样品名称',
              '型号规格',
              '样品数量'
            ];
            filterVal = [
              'secSampleNum',
              'capabilityName',
              'registerTime',
              'ownerId',
              'ownerIds',
              'overdue',
              'mateType',
              'status',
              'startDate',
              'finishedDate',
              'sampleName',
              'prodType',
              'sampleNum'
            ];
          } else if (state.formInline.tenantType === 1) {
            tHeader = [
              '样品编号',
              '检测项目',
              '登记日期',
              '试验负责人',
              '试验员',
              '是否超期',
              '物料分组',
              '状态',
              '开始时间',
              '结束时间',
              '样品名称',
              '型号规格',
              '检验对象',
              '对象位置',
              '对象名称',
              '批次',
              '盘号',
              '样品数量'
            ];
            filterVal = [
              'secSampleNum',
              'capabilityName',
              'registerTime',
              'ownerId',
              'ownerIds',
              'overdue',
              'materialGroup',
              'status',
              'startDate',
              'finishedDate',
              'sampleName',
              'prodType',
              'jydx',
              'dxwz',
              'dxmc',
              'batchNo',
              'reelNo',
              'sampleNum'
            ];
          } else {
            tHeader = [
              '样品编号',
              '检测项目',
              '登记日期',
              '试验负责人',
              '试验员',
              '是否超期',
              '状态',
              '开始时间',
              '结束时间',
              '样品名称',
              '型号规格',
              '样品数量'
            ];
            filterVal = [
              'secSampleNum',
              'capabilityName',
              'registerTime',
              'ownerId',
              'ownerIds',
              'overdue',
              'status',
              'startDate',
              'finishedDate',
              'sampleName',
              'prodType',
              'sampleNum'
            ];
          }
          export2Excel(tHeader, filterVal);
        }
      });
    };
    const export2Excel = (tHeader, filterVal) => {
      state.tableLoading = true;
      var fileName = '检测项目';
      import('@/utils/Export2Excel').then(excel => {
        const data = formatJson(filterVal, state.allList);
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: fileName,
          autoWidth: true,
          bookType: 'xlsx'
        });
        state.tableLoading = false;
        proxy.$message.success('导出成功！');
      });
    };
    const formatJson = (filterVal, jsonData) => {
      return jsonData.map(v =>
        filterVal.map(j => {
          return v[j];
        })
      );
    };
    // 列表排序
    const sortChange = column => {
      state.formInline.orderBy = column.prop;
      state.formInline.isAsc = !state.formInline.isAsc;
      getList();
    };

    const handleCheckOpera = row => {
      if (state.isShowItem) {
        setItemFirstRow(row);
        router.push({
          path: '/item-execution-detail',
          query: {
            sampleNo: row.secSampleNum,
            samplesId: row.samplesId,
            capabilityId: row.capabilityId,
            capabilityName: row.capabilityName,
            status: row.status
          }
        });
      } else {
        router.push({
          path: '/execution/detail',
          query: {
            samplesId: row.samplesId,
            capabilityId: row.capabilityId
          }
        });
      }
    };
    const handleDetail = row => {
      router.push({
        path: '/executionlist/detail',
        query: {
          orderId: row.orderId,
          sampleId: row.samplesId
        }
      });
    };
    const inputValue = data => {
      if (typeof data === 'string') {
        otherForm.content = data;
      } else {
        otherForm.content = '';
      }
    };
    watch(
      () => state.isShowItem,
      newValue => {
        state.formInline.page = 1;
        getList();
      },
      {
        deep: true
      }
    );
    // 过滤试验负责人
    const filterUserList = val => {
      if (val) {
        const list = [];
        state.copyNameList.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        state.nameList = list;
      } else {
        state.nameList = state.copyNameList;
      }
    };
    // 过滤试验员
    const filterUserList1 = val => {
      if (val) {
        const list = [];
        state.copyNameList.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        state.nameList1 = list;
      } else {
        state.nameList1 = state.copyNameList;
      }
    };
    const handleChangeRadio = (val, fildName) => {
      if (val === '') {
        delete state.formInline[fildName];
      }
    };
    return {
      ...toRefs(state),
      drageHeader,
      handleChangeRadio,
      changeOwnerId,
      changeOnlyMe,
      initFieldList,
      getSingleText,
      getParamList,
      formatJson,
      filterSampleUnitToName,
      exportExcel,
      export2Excel,
      handleDetail,
      getLoginInfo,
      sortChange,
      getNamesByid,
      getNameByid,
      handleCheckOpera,
      changeStatus,
      changeViewType,
      formatDate,
      state,
      inputValue,
      activeTabsName,
      getList,
      tableKey,
      showS,
      editFrom,
      searchFrom,
      otherForm,
      search,
      activeName,
      onSubmit,
      reset,
      filterUserList,
      filterUserList1,
      colWidth,
      getPermissionBtn
    };
  },
  computed: {
    ...mapGetters(['tenantGroup', 'tenantInfo'])
  },
  created() {}
};
</script>
<style lang="scss" scoped>
.starInit {
  color: $yellow;
  font-size: 20px;
  position: relative;
  top: 2px;
}

.btn-mg20 {
  margin-right: 20px;
}

.label-type {
  margin-bottom: 0px;
  // min-width: 200px;
}
</style>
