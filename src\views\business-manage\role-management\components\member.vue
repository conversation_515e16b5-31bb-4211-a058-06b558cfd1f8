<template>
  <!-- 角色管理 -->
  <div class="member">
    <el-row>
      <el-col :span="20" style="text-align: left; line-height: 32px">
        <el-input
          ref="searchKeyInput"
          v-model="condition"
          v-trim
          size="small"
          placeholder="请输入姓名/手机号"
          prefix-icon="el-icon-search"
          clearable
          style="width: 360px"
          @clear="getList()"
          @keyup.enter="getList()"
        />
        <el-button type="primary" size="small" style="margin-left: 10px" @click="getList()">查询</el-button>
        <!-- <el-button size="small" @click="reset">重置</el-button> -->
      </el-col>
      <el-col v-if="getPermissionBtn('addRoleUserBtn')" :span="4" style="text-align: right">
        <el-button
          size="small"
          type="primary"
          class="fr"
          icon="el-icon-plus"
          @click="handleAddDelete()"
          @keyup.prevent
          @keydown.enter.prevent
          >新增成员</el-button
        >
      </el-col>
    </el-row>

    <el-table
      v-loading="listLoading"
      :data="tableListMember"
      size="medium"
      fit
      border
      height="auto"
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
      @row-click="handleRowClick"
    >
      <el-table-column label="姓名" prop="index" width="155px">
        <template #default="{ row }">
          <UserTag :name="row.nickname || ''" />
        </template>
      </el-table-column>
      <el-table-column label="手机号" prop="mobile" min-width="170px">
        <template #default="{ row }">
          <span class="nowrap">{{ row.mobile || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="邮箱" prop="email" width="220px" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.email || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="部门" prop="path" min-width="140px">
        <template #default="{ row }">
          <span>{{ row.deptNamePath || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="在职状态" prop="beOnTheJob" min-width="120px">
        <template #default="{ row }">
          <div>{{ beOnTheJobJson[row.beOnTheJob] }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="getPermissionBtn('delRoleUserBtn')"
        label="操作"
        prop="caozuo"
        width="80px"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <span class="blue-color" @click="handleAddDelete(row)">删除</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
    <el-dialog
      v-model="dialogAdd"
      title="新增成员"
      :close-on-click-modal="false"
      width="950px"
      top="50px"
      custom-class="submit_dialog"
    >
      <el-row>
        <el-col :span="24" style="text-align: left; line-height: 32px">
          <el-input
            ref="searchInputRef"
            v-model="conditionAll"
            v-trim
            size="small"
            autocomplete="off"
            placeholder="请输入姓名/手机号"
            prefix-icon="el-icon-search"
            clearable
            style="width: 360px"
            @clear="getList()"
            @keyup.enter="getList()"
          />
          <el-button type="primary" size="small" style="margin-left: 10px" @click="getAllMembers()">查询</el-button>
          <el-button size="small" @click="resetAll">重置</el-button>
        </el-col>
      </el-row>
      <el-table
        ref="addTableRef"
        :key="tableKey"
        v-loading="listLoadingAll"
        :data="tableAllData"
        size="medium"
        fit
        border
        height="400"
        class="dark-table base-table"
        @header-dragend="drageHeader"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="姓名" prop="nickname" min-width="140px">
          <template #default="{ row }">
            <span class="nowrap">{{ row.nickname }}</span>
          </template>
        </el-table-column>
        <el-table-column label="手机号" prop="mobile" width="140px">
          <template #default="{ row }">
            <span class="nowrap">{{ row.mobile || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="邮箱" prop="email" min-width="120px" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="nowrap">{{ row.email || '--' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="部门" prop="name" min-width="140px" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="nowrap">{{ row.name || '--' }}</span>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button size="small" @click="dialogAdd = false">取 消</el-button>
          <el-button type="primary" size="small" @click="onSubmit">确 认</el-button>
        </span>
      </template>
      <pagination
        v-show="totalAll > 20"
        :page="listQuery.pageAll"
        :limit="listQuery.limitAll"
        :total="totalAll"
        @pagination="getAllMembers"
      />
    </el-dialog>
  </div>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance, watch, nextTick } from 'vue';
import Pagination from '@/components/Pagination';
import UserTag from '@/components/UserTag';
import { formatDate } from '@/utils/formatTime';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { getMemberList, getAllMemberList, addRoleMember, deleteRoleMember } from '@/api/roleManage';
import { drageHeader } from '@/utils/formatTable';

export default {
  name: 'Members',
  components: { Pagination, UserTag },
  props: {
    roleData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    activeName: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableRef: ref(),
      searchInputRef: ref(),
      searchKeyInput: ref(),
      roleData: {},
      categoryId: '', // 物资分类id
      categoryCode: '', // 物资分类code
      condition: '',
      conditionAll: '',
      dictionaryList: [], // 字典列表
      dialogAdd: false, // 新增成员
      addTableRef: ref(),
      listLoading: false,
      listLoadingAll: false,
      isEdit: true, // 规格弹出框是否是编辑页
      listQuery: {
        limit: 20,
        page: 1,
        limitAll: 20,
        pageAll: 1
      },
      fileList: [],
      formData: {},
      tableListMember: [],
      tableAllData: [], // 角色未绑定的所有成员
      selectedMembers: [], // 选中的成员
      total: 0,
      totalAll: 0,
      beOnTheJobJson: {
        1: '在职',
        0: '离职',
        2: '外部人员'
      }
    });
    watch(props, newValue => {
      state.roleData = props.roleData;
      if (state.roleData.id) {
        getList();
      }
      if (newValue.activeName === 'second') {
        nextTick(() => {
          state.searchKeyInput.focus();
        });
      }
    });
    const tableKey = ref(0);
    const getList = query => {
      const params = { roleId: state.roleData.id, condition: state.condition, isadmin: state.roleData.isadmin };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoading = true;
      getMemberList(params).then(res => {
        state.listLoading = false;
        if (res) {
          state.tableListMember = res.data.data.list;
          state.total = res.data.data.totalCount;
        }
      });
    };
    const handleSizeChange = val => {
      state.listQuery.limit = val;
      getList();
    };
    // 新增、删除成员
    const handleAddDelete = row => {
      if (row) {
        if (row.id === 0 || row.id === '0') {
          proxy.$message.error(row.nickname + '租户管理员，不允许删除');
          return false;
        }
      }
      if (state.roleData.id) {
        if (row) {
          // 删除
          proxy
            .$confirm('是否确认删除成员', '提示', {
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'warning'
            })
            .then(() => {
              deleteRoleMember({ roleId: state.roleData.id, employeeId: row.id }).then(res => {
                if (res) {
                  proxy.$message.success('成员删除成功');
                  getList();
                }
              });
            })
            .catch(() => {});
        } else {
          // 新增
          state.dialogAdd = true;
          nextTick(() => {
            state.searchInputRef.focus();
          });
          state.conditionAll = '';
          state.totalAll = 0;
          state.listQuery.limitAll = 20;
          state.listQuery.pageAll = 1;
          getAllMembers();
        }
      } else {
        proxy.$message.warning('请现在左侧添加角色！');
      }
    };
    const handleRowClick = row => {
      state.addTableRef.toggleRowSelection(row);
    };
    // 该角色所有未绑定的成员
    const getAllMembers = query => {
      const params = { roleId: state.roleData.id, condition: state.conditionAll };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
      } else {
        state.listQuery.pageAll = 1;
        params.page = '1';
        params.limit = state.listQuery.limitAll.toString();
      }
      state.listLoadingAll = true;
      getAllMemberList(params).then(res => {
        state.listLoadingAll = false;
        if (res) {
          state.tableAllData = res.data.data.list;
          state.totalAll = res.data.data.totalCount;
        }
      });
    };
    const reset = () => {
      state.condition = '';
      getList();
    };
    const resetAll = () => {
      state.conditionAll = '';
      getAllMembers();
    };
    // 选中的成员
    const handleSelectionChange = val => {
      state.selectedMembers = val;
    };
    const onSubmit = () => {
      const params = {
        employees: state.selectedMembers.map(item => {
          return item.id;
        }),
        roleId: state.roleData.id
      };
      addRoleMember(params).then(res => {
        if (res) {
          proxy.$message.success('成员添加成功');
          state.dialogAdd = false;
          getList();
        }
      });
    };
    return {
      ...toRefs(state),
      reset,
      handleRowClick,
      resetAll,
      getAllMembers,
      handleAddDelete,
      drageHeader,
      getNameByid,
      handleSizeChange,
      handleSelectionChange,
      getNamesByid,
      onSubmit,
      getPermissionBtn,
      formatDate,
      getList,
      tableKey
    };
  },
  computed: {},
  created() {}
};
</script>
<style lang="scss" scoped>
:deep(.el-table.format-height-table) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 325px);
  }
}

:deep(.el-dialog__body) {
  padding-bottom: 0;
}
.el-select {
  width: 100%;
}
.base-table {
  margin: 1rem 0 1rem 0;
}
.btn-mg20 {
  margin-right: 20px;
}
.el-radio {
  margin-right: 5px;
}
.tableBtn {
  margin: 0 0 15px 0;
}
.submit_dialog {
  .el-upload__tip {
    color: #909399;
    display: inline-block;
    margin-left: 12px;
    font-size: 14px;
  }
  .el-form-item {
    margin-bottom: 15px;
  }
  .el-form-item__error {
    top: 85%;
  }
}
.label-type {
  margin-bottom: 0px;
  min-width: 200px;
}
</style>
