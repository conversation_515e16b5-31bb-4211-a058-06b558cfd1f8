<template>
  <div v-if="isFullScreen" class="dataBoardMain">
    <app-main />
  </div>
  <div v-else :class="classObj" class="app-wrapper">
    <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <sidebar v-if="isShowLeft" class="sidebar-container" :class="sidebar.opened ? '' : 'close-sidebar-content'" />
    <hamburger
      v-if="isShowLeft"
      id="hamburger-container"
      :is-active="sidebar.opened"
      class="hamburger-container"
      :style="{ left: sidebar.opened ? '' : '15px' }"
      @toggleClick="toggleSideBar"
    />
    <div
      :class="{ hasTagsView: needTagsView }"
      class="main-container"
      :style="{ 'margin-left': isShowLeft ? '' : '0px' }"
    >
      <el-collapse-transition>
        <div v-if="showHeader" :class="{ 'fixed-header': fixedHeader }">
          <div class="flex">
            <tags-view v-if="needTagsView" />
          </div>
          <navbar />
        </div>
      </el-collapse-transition>

      <app-main />
      <right-panel v-if="showSettings">
        <settings />
      </right-panel>
    </div>
  </div>
</template>

<script>
import RightPanel from '@/components/RightPanel';
import { AppMain, Sidebar, TagsView, Settings } from './components';
import Navbar from '@/views/home/<USER>';
// import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger';
import ResizeMixin from './mixin/ResizeHandler';
import { mapState, mapGetters } from 'vuex';
import { reactive, toRefs, onMounted, watch } from 'vue';
import { useRouter, onBeforeRouteUpdate } from 'vue-router';

export default {
  name: 'Layout',
  components: {
    AppMain,
    Navbar,
    Sidebar,
    TagsView,
    RightPanel,
    // Breadcrumb,
    Hamburger,
    Settings
  },
  mixins: [ResizeMixin],
  setup() {
    const router = useRouter();
    const datas = reactive({
      isFullScreen: false, // 是否全屏显示，数据看板和移动端模板全屏显示
      showHeader: true
    });
    watch(
      () => router.currentRoute.value.path,
      (newValue, oldValue) => {
        if (
          newValue === '/smart-charts/dataBoard' ||
          newValue === '/smart-charts/aging-laboratory' ||
          newValue === '/mobileApp/template' ||
          newValue === '/smart-charts/equipmentMonitoring' ||
          newValue === '/testReport/template' ||
          newValue === '/uniappTemplate'
        ) {
          datas.isFullScreen = true;
        } else {
          datas.isFullScreen = false;
        }
      },
      { immediate: true }
    );
    onBeforeRouteUpdate(to => {});

    onMounted(() => {
      // document.onmousemove = function(e) {
      //   if (e.y < 40) {
      //     datas.showHeader = true
      //   } else {
      //     datas.showHeader = false
      //   }
      // }
    });

    return { ...toRefs(datas) };
  },
  computed: {
    ...mapState({
      sidebar: state => state.app.sidebar,
      device: state => state.app.device,
      showSettings: state => state.settings.showSettings,
      needTagsView: state => state.settings.tagsView,
      fixedHeader: state => state.settings.fixedHeader
    }),
    ...mapGetters(['sidebarMode']),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      };
    },
    isShowLeft() {
      // 判断是否是左侧布局
      return this.sidebarMode === 'vertical';
    }
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false });
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar');
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/mixin.scss';
@import '@/styles/variables.scss';

.dataBoardMain {
  height: 100%;
  .app-main {
    height: 100%;
  }
}
.dataBoardMain::-webkit-scrollbar {
  width: 0 !important;
}
.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.sidebar-container {
  box-shadow: 0px 0px 12px rgb(0 0 0 / 12%);
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  // position: fixed;
  // top: 0;
  // right: 0;
  // z-index: 9;
  // width: calc(100% - #{$sideBarWidth});
  width: 100%;
  background: #fff;
  // height: 40px;
  // transition: width 0.28s;
}

.mobile .fixed-header {
  width: 100%;
}
.hamburger-container {
  line-height: 48px;
  height: 48px;
  float: left;
  cursor: pointer;
  transition: left 0.3s;
  -webkit-tap-highlight-color: transparent;
  position: absolute;
  bottom: 0px;
  left: 168px;
  z-index: 2000;
  &:hover {
    color: $tes-primary;
  }
}

.flex-bc {
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
