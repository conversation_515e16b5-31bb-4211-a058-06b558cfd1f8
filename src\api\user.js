import request from '@/utils/request';

// 检测项目查询列表
export function getCapabilityList(data) {
  return request({
    url: '/api-capabilitystd/capability/capability/list',
    method: 'post',
    data
  });
}
// 根据Id查询检测项目信息
export function getCapabilityById(id) {
  return request({
    url: `/api-capabilitystd/capability/capability/info/${id}`,
    method: 'get'
  });
}
// 保存检测项目信息
export function saveCapability(data) {
  return request({
    url: '/api-capabilitystd/capability/capability/save',
    method: 'post',
    data
  });
}
// 修改检测项目信息
export function updateCapability(data) {
  return request({
    url: '/api-capabilitystd/capability/capability/update',
    method: 'post',
    data
  });
}
// 删除检测项目信息
export function deleteCapability(data) {
  return request({
    url: '/api-capabilitystd/capability/capability/delete',
    method: 'post',
    data
  });
}
// 检测项目分类树形数据
export function getCapabilityTree(typeId) {
  return request({
    url: `/api-capabilitystd/capabilitycategory/listTree/${typeId}`,
    method: 'get'
  });
}
// 根据Id查询检测项目分类信息
export function getCapabilityTreeById(id) {
  return request({
    url: `/api-capabilitystd/capabilitycategory/info/${id}`,
    method: 'get'
  });
}
// 保存检测项目分类
export function saveCapabilitycategory(data) {
  return request({
    url: '/api-capabilitystd/capabilitycategory/save',
    method: 'post',
    data
  });
}
// 更新检测项目分类
export function updateCapabilitycategory(data) {
  return request({
    url: '/api-capabilitystd/capabilitycategory/update',
    method: 'post',
    data
  });
}
// 删除检测项目分类
export function deleteCapabilitycategory(data) {
  return request({
    url: '/api-capabilitystd/capabilitycategory/delete',
    method: 'post',
    data
  });
}
// 获取关键参数
export function getCapabilityInfo(capabilityId) {
  return request({
    url: `/api-capabilitystd/capability/capabilitypara/info/${capabilityId}`,
    method: 'get'
  });
}
// 保存关键参数
export function saveCapabilitypara(data) {
  return request({
    url: '/api-capabilitystd/capability/capabilitypara/save',
    method: 'post',
    data
  });
}
// 更新关键参数
export function updateCapabilitypara(data) {
  return request({
    url: '/api-capabilitystd/capability/capabilitypara/update',
    method: 'post',
    data
  });
}
// 删除关键参数
export function deleteCapabilitypara(data) {
  return request({
    url: '/api-capabilitystd/capability/capabilitypara/delete',
    method: 'delete',
    data
  });
}
// 根据检测项目Id获取第三方库
export function extcapabilitymapinfo(capabilityId) {
  return request({
    url: '/api-capabilitystd/capability/extcapabilitymap/info/' + capabilityId,
    method: 'get'
  });
}
// 删除检测项目关联第三方库
export function deleteExtcapabilitymap(data) {
  return request({
    url: '/api-capabilitystd/capability/extcapabilitymap/delete',
    method: 'delete',
    data
  });
}
// 更新检测项目关联第三方库
export function extcapabilitymapSave(data) {
  return request({
    url: '/api-capabilitystd/capability/extcapabilitymap/save',
    method: 'post',
    data
  });
}
// 获取单位
export function getUnit() {
  return request({
    url: '/api-capabilitystd/capability/capabilitypara/unit',
    method: 'get'
  });
}
// 检测项目弹窗列表
export function getCapabilityUplist(categoryId, materialCategoryCode) {
  return request({
    url: `/api-capabilitystd/capability/capability/uplist/${materialCategoryCode}/${categoryId}`,
    method: 'get'
  });
}
// 模板引用列表
export function getCapabilityTemplateRefList(categoryId) {
  return request({
    url: `/api-capabilitystd/capabilitytemplateref/list/${categoryId}`,
    method: 'get'
  });
}
// 保存或者更新模板引用信息
export function updateTemplateRef(data) {
  return request({
    url: '/api-capabilitystd/capabilitytemplateref/saveOrUpdate',
    method: 'post',
    data
  });
}
// 删除模板引用
export function deleteTemplateRef(id) {
  return request({
    url: `/api-capabilitystd/capabilitytemplateref/delete/${id}`,
    method: 'delete'
  });
}
// 树分类拖动排序
export function updateOrderCategory(data) {
  return request({
    url: '/api-capabilitystd/capabilitycategory/updateOrder',
    method: 'post',
    data
  });
}
// 查询字典选项
export function getDictionary(code) {
  return request({
    url: `/api-dictionary/dictionary/dictionary/getAllByCode/${code}`,
    method: 'get'
  });
}

// 获取具有检测分配权限的人员
export function getAllocationUserList(data) {
  return request({
    url: `/api-user/user/sysuser/users/listAllocationBtn`,
    method: 'post',
    data
  });
}

// 获取具有检测执行/检测详情权限的人员
export function getRowDataUserList(data) {
  return request({
    url: `/api-user/user/sysuser/users/listRowDataAdd`,
    method: 'post',
    data
  });
}

// 获取具有检测报告权限的人员
export function getReportUserList(data) {
  return request({
    url: `/api-user/user/sysuser/users/listReportAudit`,
    method: 'post',
    data
  });
}

// 获取具有原始记录审核/原始记录审核详情权限的人员
export function getRecordUserList(data) {
  return request({
    url: `/api-user/user/sysuser/users/listRecordAudit`,
    method: 'post',
    data
  });
}

// 微信登陆获取二维码
export function getQrcode() {
  return request({
    url: `/api-uaa/social/wechatmp/qrcode`,
    method: 'get'
  });
}
// 微信登陆检测是否扫码
export function initCheckScan(data) {
  return request({
    url: `/api-uaa/social/wechatmp/checkScan`,
    method: 'post',
    data: data
  });
}
// 微信登陆绑定用户账号
export function wechatmpBind(data) {
  return request({
    url: `/api-uaa/social/wechatmp/bind`,
    method: 'post',
    data: data
  });
}
