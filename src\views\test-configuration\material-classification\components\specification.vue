<template>
  <div class="Specification">
    <div class="header-search-group">
      <div class="search-bar">
        <el-input
          ref="searchInputRef"
          v-model.trim="condition"
          size="small"
          placeholder="请输入编号/名称"
          prefix-icon="el-icon-search"
          clearable
          @clear="getList()"
          @keyup.enter="getList()"
        />
        <el-button type="primary" size="small" style="margin-left: 10px" @click="getList()">查询</el-button>
      </div>
      <el-button
        v-if="getPermissionBtn('addMCSpecsBtn')"
        size="small"
        icon="el-icon-plus"
        type="primary"
        @click="handleAddEdit()"
        @keyup.prevent
        @keydown.enter.prevent
        >新增规格</el-button
      >
    </div>
    <el-table
      v-if="isShow"
      id="sortableList"
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="tableList"
      size="medium"
      fit
      border
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
    >
      <el-table-column label="排序" :width="colWidth.serialNo">
        <i class="tes-move iconfont" style="font-size: 12px; cursor: move" />
      </el-table-column>
      <el-table-column label="序号" prop="index" :width="colWidth.serialNo" align="center">
        <template #default="{ $index }">
          <div class="">{{ Number($index) + 1 }}</div>
        </template>
      </el-table-column>
      <el-table-column label="编号" prop="specificationCode" :width="colWidth.orderNo" align="left">
        <template #default="{ row }">
          <span class="nowrap">{{ row.specificationCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="名称"
        prop="specificationName"
        align="left"
        :min-width="colWidth.name"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="nowrap">{{ row.specificationName || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="关联字典" prop="dictionaryName" :width="colWidth.orderNo" align="left">
        <template #default="{ row }">
          <span class="nowrap">{{ row.dictionaryName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="关键规格" prop="isKeySpecification" :width="colWidth.level" align="left">
        <template #default="{ row }">
          <span class="nowrap">{{ row.isKeySpecification ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" :width="colWidth.status">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="statusDicClass[row.status]"> {{ statusDic[row.status] }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        v-if="getPermissionBtn('editMCSpecsBtn')"
        label="操作"
        prop="caozuo"
        :width="colWidth.operationSingle"
      >
        <template #default="{ row }">
          <span class="blue-color" @click="handleAddEdit(row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-model="dialogUpload"
      :title="isEdit ? '编辑规格' : '新增规格'"
      :close-on-click-modal="false"
      custom-class="small-dialog"
    >
      <el-form
        v-if="dialogUpload"
        ref="ruleForm"
        label-position="right"
        :model="formData"
        label-width="120px"
        size="small"
      >
        <el-form-item label="状态：" prop="status">
          <el-radio-group v-model="formData.status" size="small">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="关键规格：" prop="isKeySpecification">
          <el-radio-group v-model="formData.isKeySpecification" size="small">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="编号："
          prop="specificationCode"
          :rules="{ required: true, message: '请输入规格编号', trigger: 'blur' }"
        >
          <el-input
            ref="inputRef"
            v-model="formData.specificationCode"
            v-trim
            :disabled="isEdit"
            placeholder="请输入规格编号"
          />
        </el-form-item>
        <el-form-item
          label="规格名称："
          prop="specificationName"
          :rules="{ required: true, message: '请输入规格名称', trigger: 'blur' }"
        >
          <el-input v-model="formData.specificationName" v-trim maxlength="50" placeholder="请输入规格名称" />
        </el-form-item>
        <el-form-item label="关联字典：" prop="dictionaryCode" style="position: relative">
          <span class="glzd">
            <el-tooltip
              content="关联后规格可从字典中选择，否则为自定义文本。此项保存后不可更改"
              placement="top"
              effect="dark"
            >
              <i class="iconfont tes-title" />
            </el-tooltip>
          </span>
          <el-select
            v-if="!isEdit"
            v-model="formData.dictionaryCode"
            :disabled="isEdit"
            placeholder="请选择关联字典"
            clearable
            filterable
            style="width: 100%"
          >
            <el-option v-for="item in dictionaryList" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
          <el-input v-if="isEdit" v-model="formData.dictionaryName" :disabled="isEdit" placeholder="请输入规格编号" />
        </el-form-item>
        <el-form-item label="规格连接符：" prop="joinChar" style="position: relative">
          <span class="ggljf">
            <el-tooltip content="输入后会在此规格之后与其他规格进行连接" placement="top" effect="dark">
              <i class="iconfont tes-title" />
            </el-tooltip>
          </span>
          <el-input v-model="formData.joinChar" maxlength="50" placeholder="请输入规格连接符" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogUpload = false">取 消</el-button>
          <el-button type="primary" @click="onSubmit">确 认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance, watch, nextTick } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { specificationList, addSpecify, editSpecify, getNumber, updateOrderApi } from '@/api/material';
import { drageHeader } from '@/utils/formatTable';
import { getDictionaryList } from '@/api/dictionary';
import { colWidth } from '@/data/tableStyle';
import Sortable from 'sortablejs';

export default {
  name: 'Specification',
  components: {},
  props: {
    categoryId: {
      type: String,
      default: ''
    },
    currentData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    categoryCode: {
      type: String,
      default: ''
    },
    activeName: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableRef: ref(),
      searchInputRef: ref(),
      currentData: {},
      inputRef: ref(),
      isShow: true,
      categoryId: '', // 物资分类id
      categoryCode: '', // 物资分类code
      condition: '',
      dictionaryList: [], // 字典列表
      dialogUpload: false,
      ruleForm: ref(),
      listLoading: false,
      isEdit: true, // 规格弹出框是否是编辑页
      fileList: [],
      formData: {},
      tableList: [],
      total: 0,
      statusDicClass: {
        0: 'info',
        1: 'success'
      },
      statusDic: {
        1: '已启用',
        0: '已停用'
      }
    });
    watch(props, newValue => {
      state.categoryId = props.categoryId;
      state.categoryCode = props.categoryCode;
      state.currentData = props.currentData;
      if (props.activeName === 'fourth') {
        getList();
        getDictionary();
        nextTick(() => {
          state.searchInputRef.focus();
        });
      }
    });
    const tableKey = ref(0);
    const getList = () => {
      const params = {
        categoryId: state.categoryId,
        categoryCode: state.categoryCode,
        condition: state.condition,
        limit: '-1',
        page: '-1'
      };
      state.listLoading = true;
      state.isShow = false;
      specificationList(params).then(res => {
        state.isShow = true;
        state.listLoading = false;
        if (res) {
          state.tableList = res.data.data.list;
          state.total = res.data.data.totalCount;
          nextTick(() => {
            rowDrop();
          });
        }
      });
    };
    const getDictionary = () => {
      getDictionaryList({ limit: '10000', page: '1', dictionaryType: '1' }).then(res => {
        state.dictionaryList = res.data.data.list;
      });
    };
    // 行拖拽
    const rowDrop = () => {
      // 获取当前表格
      const el = document.getElementById('sortableList').querySelector('.el-table__body-wrapper tbody');
      Sortable.create(el, {
        animation: 300,
        handle: '.tes-move',
        draggable: '.el-table__row',
        ghostClass: 'ghost',
        // 拖动对象移动样式
        dragClass: 'drag',
        forceFallback: true,
        onEnd({ newIndex, oldIndex }) {
          if (oldIndex !== newIndex) {
            const currRow = state.tableList.splice(oldIndex, 1)[0];
            state.tableList.splice(newIndex, 0, currRow);
            state.tableList.forEach((value, index) => {
              value.order = index;
            });
            updateOrder(state.tableList);
          }
        }
      });
    };
    const updateOrder = tableData => {
      state.listLoading = true;
      state.isShow = false;
      updateOrderApi(tableData).then(res => {
        state.listLoading = false;
        state.isShow = true;
        if (res) {
          proxy.$message.success(res.data.message);
          getList();
        }
      });
    };
    // 新增、编辑
    const handleAddEdit = row => {
      if (state.categoryId) {
        state.dialogUpload = true;
        nextTick(() => {
          state.inputRef.focus();
        });
        if (row) {
          state.isEdit = true;
          state.formData = JSON.parse(JSON.stringify(row));
        } else {
          state.isEdit = false;
          state.formData = {
            isKeySpecification: false,
            status: 1
          };
          generateNum();
        }
      } else {
        proxy.$message.warning('请现在左侧添加类目');
      }
    };
    const generateNum = () => {
      getNumber({ categoryCode: state.categoryCode, categoryId: state.categoryId }).then(res => {
        if (res) {
          state.formData.specificationCode = res.data.data;
        }
      });
    };
    const reset = () => {
      state.condition = '';
      getList();
    };
    const onSubmit = () => {
      state.ruleForm
        .validate()
        .then(valid => {
          if (valid) {
            state.listLoading = true;
            if (!state.isEdit) {
              addSpecify({
                categoryCode: state.categoryCode,
                categoryId: state.categoryId,
                ...state.formData,
                order: state.tableList.length
              }).then(res => {
                state.listLoading = false;
                if (res) {
                  proxy.$message.success(res.data.message);
                  state.dialogUpload = false;
                  getList();
                }
              });
            } else {
              editSpecify(state.formData).then(res => {
                state.listLoading = false;
                if (res) {
                  proxy.$message.success(res.data.message);
                  state.dialogUpload = false;
                  getList();
                }
              });
            }
          }
        })
        .catch(() => {});
    };
    return {
      ...toRefs(state),
      reset,
      updateOrder,
      rowDrop,
      generateNum,
      getDictionary,
      handleAddEdit,
      drageHeader,
      getNameByid,
      getNamesByid,
      onSubmit,
      getPermissionBtn,
      formatDate,
      getList,
      tableKey,
      colWidth
    };
  },
  computed: {},
  created() {}
};
</script>
<style lang="scss" scoped>
.ggljf {
  position: absolute;
  top: 0;
  left: -103px;
  display: inline-block;
}
.glzd {
  position: absolute;
  display: inline-block;
  top: 0;
  left: -88px;
}
.header-search-group {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  .search-bar {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}
</style>
