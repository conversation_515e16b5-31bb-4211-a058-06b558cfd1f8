export const weightSet = [
  [496, 504, 496, 505, 509],
  [501, 492, 510, 496, 521],
  [501, 498, 510, 499, 482],
  [493, 490, 506, 506, 505],
  [477, 460, 460, 478, 490],
  [503, 495, 493, 505, 499],
  [495, 494, 481, 490, 510],
  [502, 528, 520, 490, 505],
  [509, 502, 502, 489, 503],
  [499, 492, 503, 506, 490]
];

export const allWeightSet = [
  496, 504, 496, 505, 509, 501, 492, 510, 496, 521, 501, 498, 510, 499, 482, 493, 490, 506, 506, 505, 477, 460, 460,
  478, 490, 503, 495, 493, 505, 499, 495, 494, 481, 490, 510, 502, 528, 520, 490, 505, 509, 502, 502, 489, 503, 499,
  492, 503, 506, 490
];

export const sampleSet = [
  { name: '李栓', finishNum: 50, unFinishNum: 10, totalNum: 60 },
  { name: '计鑫', finishNum: 60, unFinishNum: 10, totalNum: 70 },
  { name: '王海洋', finishNum: 50, unFinishNum: 10, totalNum: 60 },
  { name: '陈强伟', finishNum: 50, unFinishNum: 10, totalNum: 60 },
  { name: '欧露雨', finishNum: 50, unFinishNum: 10, totalNum: 60 },
  { name: '施晓芸', finishNum: 50, unFinishNum: 10, totalNum: 60 },
  { name: '周威', finishNum: 90, unFinishNum: 10, totalNum: 100 },
  { name: '郭自有', finishNum: 50, unFinishNum: 10, totalNum: 60 },
  { name: '张潇帆', finishNum: 50, unFinishNum: 10, totalNum: 60 },
  { name: '冯思玲', finishNum: 40, unFinishNum: 10, totalNum: 50 },
  { name: '周雅榕', finishNum: 110, unFinishNum: 10, totalNum: 120 }
];

export const itemSet = [
  { name: '李栓', finishNum: 50, unFinishNum: 10, totalNum: 60 },
  { name: '计鑫', finishNum: 60, unFinishNum: 10, totalNum: 70 },
  { name: '王海洋', finishNum: 90, unFinishNum: 10, totalNum: 100 },
  { name: '陈强伟', finishNum: 60, unFinishNum: 10, totalNum: 70 },
  { name: '欧露雨', finishNum: 50, unFinishNum: 10, totalNum: 60 },
  { name: '施晓芸', finishNum: 50, unFinishNum: 10, totalNum: 60 },
  { name: '周威', finishNum: 20, unFinishNum: 30, totalNum: 50 },
  { name: '郭自有', finishNum: 50, unFinishNum: 10, totalNum: 60 },
  { name: '张潇帆', finishNum: 90, unFinishNum: 10, totalNum: 100 },
  { name: '冯思玲', finishNum: 40, unFinishNum: 10, totalNum: 50 },
  { name: '周雅榕', finishNum: 110, unFinishNum: 10, totalNum: 120 }
];

export const ownerSet = [
  { name: '李栓', finishNum: 50, unFinishNum: 40, totalNum: 90 },
  { name: '计鑫', finishNum: 60, unFinishNum: 60, totalNum: 100 },
  { name: '王海洋', finishNum: 90, unFinishNum: 80, totalNum: 170 },
  { name: '陈强伟', finishNum: 60, unFinishNum: 60, totalNum: 120 },
  { name: '欧露雨', finishNum: 50, unFinishNum: 50, totalNum: 100 },
  { name: '施晓芸', finishNum: 50, unFinishNum: 40, totalNum: 90 },
  { name: '周威', finishNum: 20, unFinishNum: 20, totalNum: 40 },
  { name: '郭自有', finishNum: 50, unFinishNum: 40, totalNum: 90 },
  { name: '张潇帆', finishNum: 90, unFinishNum: 90, totalNum: 180 },
  { name: '冯思玲', finishNum: 40, unFinishNum: 40, totalNum: 80 },
  { name: '周雅榕', finishNum: 110, unFinishNum: 100, totalNum: 210 }
];

export const testerSet = [
  { name: '李栓', finishNum: 50, unFinishNum: 40, totalNum: 90 },
  { name: '计鑫', finishNum: 80, unFinishNum: 60, totalNum: 140 },
  { name: '王海洋', finishNum: 90, unFinishNum: 80, totalNum: 170 },
  { name: '陈强伟', finishNum: 60, unFinishNum: 60, totalNum: 140 },
  { name: '欧露雨', finishNum: 80, unFinishNum: 50, totalNum: 130 },
  { name: '施晓芸', finishNum: 50, unFinishNum: 40, totalNum: 90 },
  { name: '周威', finishNum: 60, unFinishNum: 20, totalNum: 80 },
  { name: '郭自有', finishNum: 50, unFinishNum: 40, totalNum: 90 },
  { name: '张潇帆', finishNum: 90, unFinishNum: 90, totalNum: 180 },
  { name: '冯思玲', finishNum: 40, unFinishNum: 40, totalNum: 80 },
  { name: '周雅榕', finishNum: 110, unFinishNum: 100, totalNum: 210 }
];

export const batchSet = [
  { name: '批次001', qualifiedNum: 90, totalNum: 100, qualifiedRate: 90, unit: 'km' },
  { name: '批次002', qualifiedNum: 91, totalNum: 100, qualifiedRate: 91, unit: 'km' },
  { name: '批次003', qualifiedNum: 95, totalNum: 100, qualifiedRate: 95, unit: 'km' },
  { name: '批次005', qualifiedNum: 93, totalNum: 100, qualifiedRate: 93, unit: 'km' },
  { name: '批次045', qualifiedNum: 94, totalNum: 100, qualifiedRate: 94, unit: 'km' },
  { name: '批次065', qualifiedNum: 95, totalNum: 100, qualifiedRate: 95, unit: 'km' },
  { name: '批次075', qualifiedNum: 99, totalNum: 100, qualifiedRate: 99, unit: 'km' },
  { name: '批次015', qualifiedNum: 93, totalNum: 100, qualifiedRate: 93, unit: 'km' },
  { name: '批次035', qualifiedNum: 92, totalNum: 100, qualifiedRate: 92, unit: 'km' }
];

export const productSet = [
  { name: '22-6-10', qualifiedNum: 90, totalNum: 100, qualifiedRate: 90, unit: 'km' },
  { name: '22-6-11', qualifiedNum: 91, totalNum: 100, qualifiedRate: 91, unit: 'km' },
  { name: '22-6-12', qualifiedNum: 95, totalNum: 100, qualifiedRate: 95, unit: 'km' },
  { name: '22-6-13', qualifiedNum: 93, totalNum: 100, qualifiedRate: 93, unit: 'km' },
  { name: '22-6-14', qualifiedNum: 94, totalNum: 100, qualifiedRate: 94, unit: 'km' },
  { name: '22-6-15', qualifiedNum: 95, totalNum: 100, qualifiedRate: 95, unit: 'km' },
  { name: '22-6-16', qualifiedNum: 99, totalNum: 100, qualifiedRate: 99, unit: 'km' },
  { name: '22-6-17', qualifiedNum: 93, totalNum: 100, qualifiedRate: 93, unit: 'km' },
  { name: '22-6-18', qualifiedNum: 92, totalNum: 100, qualifiedRate: 92, unit: 'km' }
];

export const frequencySet = [
  { name: '供应商一', qualifiedNum: 90, unQualifiedNum: 10, totalNum: 100, unQualifiedRate: 10 },
  { name: '供应商二', qualifiedNum: 95, unQualifiedNum: 5, totalNum: 100, unQualifiedRate: 5 },
  { name: '供应商三', qualifiedNum: 94, unQualifiedNum: 6, totalNum: 100, unQualifiedRate: 6 },
  { name: '供应商四', qualifiedNum: 92, unQualifiedNum: 8, totalNum: 100, unQualifiedRate: 8 },
  { name: '供应商五', qualifiedNum: 98, unQualifiedNum: 2, totalNum: 100, unQualifiedRate: 2 },
  { name: '供应商六', qualifiedNum: 80, unQualifiedNum: 20, totalNum: 100, unQualifiedRate: 20 },
  { name: '供应商七', qualifiedNum: 99, unQualifiedNum: 1, totalNum: 100, unQualifiedRate: 1 },
  { name: '供应商八', qualifiedNum: 95, unQualifiedNum: 5, totalNum: 100, unQualifiedRate: 5 },
  { name: '供应商九', qualifiedNum: 88, unQualifiedNum: 12, totalNum: 100, unQualifiedRate: 12 }
];

export const machineFrequencySet = [
  { name: '机台一', qualifiedNum: 90, unQualifiedNum: 10, totalNum: 100, unQualifiedRate: 10 },
  { name: '机台二', qualifiedNum: 95, unQualifiedNum: 5, totalNum: 100, unQualifiedRate: 5 },
  { name: '机台三', qualifiedNum: 94, unQualifiedNum: 6, totalNum: 100, unQualifiedRate: 6 },
  { name: '机台四', qualifiedNum: 92, unQualifiedNum: 8, totalNum: 100, unQualifiedRate: 8 },
  { name: '机台五', qualifiedNum: 98, unQualifiedNum: 2, totalNum: 100, unQualifiedRate: 2 },
  { name: '机台六', qualifiedNum: 80, unQualifiedNum: 20, totalNum: 100, unQualifiedRate: 20 },
  { name: '机台七', qualifiedNum: 99, unQualifiedNum: 1, totalNum: 100, unQualifiedRate: 1 },
  { name: '机台八', qualifiedNum: 95, unQualifiedNum: 5, totalNum: 100, unQualifiedRate: 5 },
  { name: '机台九', qualifiedNum: 88, unQualifiedNum: 12, totalNum: 100, unQualifiedRate: 12 }
];

export const unqualifiedItemSet = [
  { name: '项目001', unQualifiedNum: 60, totalNum: 200, unQualifiedRate: 30 },
  { name: '项目002', unQualifiedNum: 40, totalNum: 200, unQualifiedRate: 50 },
  { name: '项目003', unQualifiedNum: 30, totalNum: 200, unQualifiedRate: 65 },
  { name: '项目004', unQualifiedNum: 20, totalNum: 200, unQualifiedRate: 75 },
  { name: '项目005', unQualifiedNum: 15, totalNum: 200, unQualifiedRate: 82.5 },
  { name: '项目006', unQualifiedNum: 10, totalNum: 200, unQualifiedRate: 87.5 },
  { name: '项目007', unQualifiedNum: 10, totalNum: 200, unQualifiedRate: 92.5 },
  { name: '项目008', unQualifiedNum: 5, totalNum: 200, unQualifiedRate: 95 },
  { name: '项目009', unQualifiedNum: 5, totalNum: 200, unQualifiedRate: 97.5 },
  { name: '项目010', unQualifiedNum: 5, totalNum: 200, unQualifiedRate: 100 }
];

export const unqualifiedTableList = {
  totalCount: 20,
  list: [
    { reportNo: 'RN23-0817001', batchNo: 'PC20230817', reelNo: 'PH20230817' },
    { reportNo: 'RN23-0830001', batchNo: '20220117155442202_X4', reelNo: 'X4' },
    { reportNo: 'RN23-0905013', batchNo: 'PC20220927', reelNo: 'PH20220902' },
    { reportNo: 'RN23-0907001', batchNo: 'PC20220923', reelNo: 'PH20220906' },
    { reportNo: 'RN23-0904001', batchNo: 'PC20230904', reelNo: 'PH20230904' },
    { reportNo: 'RN23-0911001', batchNo: 'PC20220906', reelNo: 'PH20220908' },
    { reportNo: 'RN23-1008004', batchNo: 'PC20231007', reelNo: 'PH20231007' },
    { reportNo: 'RN23-1010002', batchNo: 'PC20231007', reelNo: 'PH20231007' },
    { reportNo: 'RN23-1031002', batchNo: 'PC20231031', reelNo: 'PH20231031' },
    { reportNo: 'RN23-1114001', batchNo: 'PC20231114', reelNo: 'PH20231114' },
    { reportNo: 'RN23-1123002', batchNo: 'PC20231121', reelNo: 'PH20231121' },
    { reportNo: 'RN23-1123004', batchNo: 'PC20231123', reelNo: 'PH20231123' },
    { reportNo: 'RN23-1124001', batchNo: 'PC20220902', reelNo: 'PH20220923' },
    { reportNo: 'RN23-1128001', batchNo: 'PC20231127', reelNo: 'PH20231127' },
    { reportNo: 'RN23-1127005', batchNo: 'PC20231127', reelNo: 'PH20231127' },
    { reportNo: 'RN23-1213001', batchNo: 'PCC202312123', reelNo: 'PHC202312123' },
    { reportNo: 'RN23-1214001', batchNo: 'PCC202312123', reelNo: 'PHC202312123' },
    { reportNo: 'RN23-1225001', batchNo: 'PC20231225', reelNo: 'PH20231225' },
    { reportNo: 'RN24-0103001', batchNo: 'PC20240103', reelNo: 'PH20240103' },
    { reportNo: 'RN24-0104003', batchNo: 'PCQC24-01002', reelNo: 'PHQC24-01002' }
  ]
};

export const qualifiedRateOnceData = [
  {
    firstPassNum: 1000,
    firstPassYield: 1,
    name: '上海胜华电气股份有限公司',
    totalNum: 1000,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: 'PC2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: 'PC2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: 'PC2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: 'PC2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: 'PC2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 2500,
    firstPassYield: 0.9615,
    name: '江天科技有限公司',
    totalNum: 2600,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: 'PC2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: 'PC2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: 'PC2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: 'PC2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: 'PC2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 2100,
    firstPassYield: 0.9545,
    name: '福建源葆新材料有限公司',
    totalNum: 2200,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: 'PC2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: 'PC2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: 'PC2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: 'PC2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: 'PC2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 2100,
    firstPassYield: 0.9545,
    name: '浙江永裕金具有限公司',
    totalNum: 2200,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: 'PC2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: 'PC2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: 'PC2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: 'PC2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: 'PC2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 1800,
    firstPassYield: 0.9474,
    name: '焦作铁路电缆有限责任公司',
    totalNum: 1900,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: 'PC2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: 'PC2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: 'PC2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: 'PC2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: 'PC2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 1500,
    firstPassYield: 0.9375,
    name: '宁波东方电缆股份有限公司',
    totalNum: 1600,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: 'PC2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: 'PC2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: 'PC2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: 'PC2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: 'PC2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 1400,
    firstPassYield: 0.9333,
    name: '上海申通电缆厂有限公司',
    totalNum: 1500,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: 'PC2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: 'PC2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: 'PC2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: 'PC2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: 'PC2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 2800,
    firstPassYield: 0.9333,
    name: '上海朗达电缆(集团)有限公司',
    totalNum: 3000,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: 'PC2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: 'PC2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: 'PC2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: 'PC2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: 'PC2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 5000,
    firstPassYield: 0.9091,
    name: '山东华凌电缆有限公司',
    totalNum: 5500,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: 'PC2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: 'PC2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: 'PC2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: 'PC2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: 'PC2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 900,
    firstPassYield: 0.9,
    name: '温州优骏电气科技有限公司',
    totalNum: 1000,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: 'PC2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: 'PC2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: 'PC2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: 'PC2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: 'PC2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 800,
    firstPassYield: 0.8889,
    name: '中天启明集团有限公司',
    totalNum: 900,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: 'PC2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: 'PC2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: 'PC2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: 'PC2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: 'PC2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: 'PC2032001',
        totalNum: 200
      }
    ]
  }
];
export const qualifiedRateOnceData2 = [
  {
    firstPassNum: 990,
    firstPassYield: 0.99,
    name: '内护套',
    totalNum: 1000,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '机台2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '机台2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '机台2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '机台2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '机台2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 2500,
    firstPassYield: 0.9615,
    name: '云母带',
    totalNum: 2600,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '机台2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '机台2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '机台2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '机台2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '机台2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 2100,
    firstPassYield: 0.9545,
    name: '编织屏蔽',
    totalNum: 2200,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '机台2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '机台2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '机台2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '机台2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '机台2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 2100,
    firstPassYield: 0.9545,
    name: '成缆',
    totalNum: 2200,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '机台2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '机台2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '机台2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '机台2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '机台2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 1800,
    firstPassYield: 0.9474,
    name: '分割块合股',
    totalNum: 1900,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '机台2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '机台2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '机台2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '机台2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '机台2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 1500,
    firstPassYield: 0.9375,
    name: '绞合',
    totalNum: 1600,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '机台2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '机台2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '机台2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '机台2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '机台2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 1400,
    firstPassYield: 0.9333,
    name: '内屏＋绝缘',
    totalNum: 1500,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '机台2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '机台2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '机台2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '机台2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '机台2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 2800,
    firstPassYield: 0.9333,
    name: '外护套',
    totalNum: 3000,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '机台2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '机台2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '机台2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '机台2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '机台2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 5000,
    firstPassYield: 0.9091,
    name: '拉丝',
    totalNum: 5500,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '机台2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '机台2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '机台2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '机台2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '机台2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 900,
    firstPassYield: 0.9,
    name: '金属屏蔽',
    totalNum: 1000,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '机台2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '机台2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '机台2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '机台2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '机台2032001',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 800,
    firstPassYield: 0.8889,
    name: '阻水绕包',
    totalNum: 900,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '机台2032005',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '机台2032004',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '机台2032003',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '机台2032002',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '机台2032006',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '机台2032001',
        totalNum: 200
      }
    ]
  }
];
export const qualifiedRateOnceData3 = [
  {
    firstPassNum: 990,
    firstPassYield: 0.99,
    name: '绝缘纸',
    totalNum: 1000,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '2023-03-27',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '2023-03-25',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '2023-03-24',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '2023-03-22',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '2023-03-23',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '2023-03-26',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '2023-03-21',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 2100,
    firstPassYield: 0.9545,
    name: '纸板机加工件',
    totalNum: 2200,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '2023-03-27',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '2023-03-25',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '2023-03-24',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '2023-03-22',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '2023-03-23',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '2023-03-26',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '2023-03-21',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 1500,
    firstPassYield: 0.9375,
    name: '电磁线',
    totalNum: 1600,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '2023-03-27',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '2023-03-25',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '2023-03-24',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '2023-03-22',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '2023-03-23',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '2023-03-26',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '2023-03-21',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 2800,
    firstPassYield: 0.9333,
    name: '压力释放阀',
    totalNum: 3000,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '2023-03-27',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '2023-03-25',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '2023-03-24',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '2023-03-22',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '2023-03-23',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '2023-03-26',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '2023-03-21',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 5000,
    firstPassYield: 0.9091,
    name: '冷却器配件',
    totalNum: 5500,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '2023-03-27',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '2023-03-25',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '2023-03-24',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '2023-03-22',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '2023-03-23',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '2023-03-26',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '2023-03-21',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 900,
    firstPassYield: 0.9,
    name: '冷却装置类组件',
    totalNum: 1000,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '2023-03-27',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '2023-03-25',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '2023-03-24',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '2023-03-22',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '2023-03-23',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '2023-03-26',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '2023-03-21',
        totalNum: 200
      }
    ]
  },
  {
    firstPassNum: 800,
    firstPassYield: 0.8889,
    name: '压力阀',
    totalNum: 900,
    subList: [
      {
        firstPassNum: 150,
        firstPassYield: 0.98,
        name: '2023-03-27',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.95,
        name: '2023-03-25',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.94,
        name: '2023-03-24',
        totalNum: 150
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.93,
        name: '2023-03-22',
        totalNum: 150
      },
      {
        firstPassNum: 100,
        firstPassYield: 0.92,
        name: '2023-03-23',
        totalNum: 100
      },
      {
        firstPassNum: 150,
        firstPassYield: 0.91,
        name: '2023-03-26',
        totalNum: 150
      },
      {
        firstPassNum: 200,
        firstPassYield: 0.91,
        name: '2023-03-21',
        totalNum: 200
      }
    ]
  }
];
export const rateOncetable = [
  {
    reportNo: 'RN23-0203001',
    reportResult: 0,
    batchNo: 'PC20230303',
    reelNo: 'PH20230303',
    mateName: '聚酯带 0.05*25',
    prodType: 'KVVP-450/750V 4×2.5',
    inspectionObj: 'RK20230304',
    objPosition: '原材料仓库',
    objName: '上海程析智能科技有限公司'
  },
  {
    reportNo: 'RN23-0203002',
    reportResult: 0,
    batchNo: 'PC20230304',
    reelNo: 'PC20230304',
    mateName: '聚酯带 0.05*26',
    prodType: 'KVVP-450/750V 4×2.6',
    inspectionObj: 'RK20230305',
    objPosition: '原材料仓库',
    objName: '中天启明集团有限公司'
  },
  {
    reportNo: 'RN23-0203003',
    reportResult: 0,
    batchNo: 'PC20230305',
    reelNo: 'PC20230305',
    mateName: '聚酯带 0.05*27',
    prodType: 'KVVP-450/750V 4×2.7',
    inspectionObj: 'RK20230306',
    objPosition: '原材料仓库',
    objName: '中天启明集团有限公司'
  },
  {
    reportNo: 'RN23-0203004',
    reportResult: 0,
    batchNo: 'PC20230306',
    reelNo: 'PC20230306',
    mateName: '聚酯带 0.05*28',
    prodType: 'KVVP-450/750V 4×2.8',
    inspectionObj: 'RK20230307',
    objPosition: '原材料仓库',
    objName: '上海程析智能科技有限公司'
  },
  {
    reportNo: 'RN23-0203005',
    reportResult: 0,
    batchNo: 'PC20230307',
    reelNo: 'PC20230307',
    mateName: '聚酯带 0.05*29',
    prodType: 'KVVP-450/750V 4×2.9',
    inspectionObj: 'RK20230308',
    objPosition: '原材料仓库',
    objName: '中天启明集团有限公司'
  },
  {
    reportNo: 'RN23-0203006',
    reportResult: 0,
    batchNo: 'PC20230308',
    reelNo: 'PC20230308',
    mateName: '聚酯带 0.05*30',
    prodType: 'KVVP-450/750V 4×2.10',
    inspectionObj: 'RK20230309',
    objPosition: '原材料仓库',
    objName: '上海程析智能科技有限公司'
  },
  {
    reportNo: 'RN23-0203007',
    reportResult: 0,
    batchNo: 'PC20230309',
    reelNo: 'PC20230309',
    mateName: '聚酯带 0.05*31',
    prodType: 'KVVP-450/750V 4×2.11',
    inspectionObj: 'RK20230310',
    objPosition: '原材料仓库',
    objName: '中天启明集团有限公司'
  },
  {
    reportNo: 'RN23-0203008',
    reportResult: 0,
    batchNo: 'PC20230310',
    reelNo: 'PC20230310',
    mateName: '聚酯带 0.05*32',
    prodType: 'KVVP-450/750V 4×2.12',
    inspectionObj: 'RK20230311',
    objPosition: '原材料仓库',
    objName: '上海程析智能科技有限公司'
  },
  {
    reportNo: 'RN23-0203009',
    reportResult: 0,
    batchNo: 'PC20230311',
    reelNo: 'PC20230311',
    mateName: '聚酯带 0.05*33',
    prodType: 'KVVP-450/750V 4×2.13',
    inspectionObj: 'RK20230312',
    objPosition: '原材料仓库',
    objName: '中天启明集团有限公司'
  },
  {
    reportNo: 'RN23-0203010',
    reportResult: 0,
    batchNo: 'PC20230312',
    reelNo: 'PC20230312',
    mateName: '聚酯带 0.05*34',
    prodType: 'KVVP-450/750V 4×2.14',
    inspectionObj: 'RK20230313',
    objPosition: '原材料仓库',
    objName: '中天启明集团有限公司'
  },
  {
    reportNo: 'RN23-0203011',
    reportResult: 0,
    batchNo: 'PC20230313',
    reelNo: 'PC20230313',
    mateName: '聚酯带 0.05*35',
    prodType: 'KVVP-450/750V 4×2.15',
    inspectionObj: 'RK20230314',
    objPosition: '原材料仓库',
    objName: '中天启明集团有限公司'
  },
  {
    reportNo: 'RN23-0203012',
    reportResult: 0,
    batchNo: 'PC20230314',
    reelNo: 'PC20230314',
    mateName: '聚酯带 0.05*36',
    prodType: 'KVVP-450/750V 4×2.16',
    inspectionObj: 'RK20230315',
    objPosition: '原材料仓库',
    objName: '中天启明集团有限公司'
  }
];

export const rateOncetable2 = [
  {
    reportNo: 'RN23-0203013',
    reportResult: 0,
    batchNo: 'PC20230153',
    reelNo: 'PH20230153',
    mateName: '聚酯带 0.05*25',
    prodType: 'KVVP-450/750V 4×2.5',
    inspectionObj: 'PUR122-8808',
    objPosition: '外护套',
    objName: '宁波东方电缆股份有限公司'
  },
  {
    reportNo: 'RN23-0203014',
    reportResult: 0,
    batchNo: 'PC20230154',
    reelNo: 'PC20230154',
    mateName: '聚酯带 0.05*26',
    prodType: 'KVVP-450/750V 4×2.6',
    inspectionObj: 'PUR122-8809',
    objPosition: '外护套',
    objName: '宁波东方电缆股份有限公司'
  },
  {
    reportNo: 'RN23-0203015',
    reportResult: 0,
    batchNo: 'PC20230155',
    reelNo: 'PC20230155',
    mateName: '聚酯带 0.05*27',
    prodType: 'KVVP-450/750V 4×2.7',
    inspectionObj: 'PUR122-8810',
    objPosition: '外护套',
    objName: '宁波东方电缆股份有限公司'
  },
  {
    reportNo: 'RN23-0203015',
    reportResult: 0,
    batchNo: 'PC20230156',
    reelNo: 'PC20230156',
    mateName: '聚酯带 0.05*28',
    prodType: 'KVVP-450/750V 4×2.8',
    inspectionObj: 'PUR122-8807',
    objPosition: '外护套',
    objName: '宁波东方电缆股份有限公司'
  },
  {
    reportNo: 'RN23-0203017',
    reportResult: 0,
    batchNo: 'PC20230157',
    reelNo: 'PC20230157',
    mateName: '聚酯带 0.05*29',
    prodType: 'KVVP-450/750V 4×2.9',
    inspectionObj: 'PUR122-8816',
    objPosition: '外护套',
    objName: '宁波东方电缆股份有限公司'
  },
  {
    reportNo: 'RN23-0203018',
    reportResult: 0,
    batchNo: 'PC20230158',
    reelNo: 'PC20230158',
    mateName: '聚酯带 0.05*30',
    prodType: 'KVVP-450/750V 4×2.10',
    inspectionObj: 'PUR122-8817',
    objPosition: '外护套',
    objName: '宁波东方电缆股份有限公司'
  },
  {
    reportNo: 'RN23-0203019',
    reportResult: 0,
    batchNo: 'PC20230159',
    reelNo: 'PC20230159',
    mateName: '聚酯带 0.05*31',
    prodType: 'KVVP-450/750V 4×2.11',
    inspectionObj: 'PUR122-8818',
    objPosition: '阻水绕包',
    objName: '宁波东方电缆股份有限公司'
  },
  {
    reportNo: 'RN23-0203020',
    reportResult: 1,
    batchNo: 'PC20230160',
    reelNo: 'PC20230160',
    mateName: '聚酯带 0.05*32',
    prodType: 'KVVP-450/750V 4×2.12',
    inspectionObj: 'PUR122-8811',
    objPosition: '阻水绕包',
    objName: '宁波东方电缆股份有限公司'
  },
  {
    reportNo: 'RN23-0203021',
    reportResult: 0,
    batchNo: 'PC20230161',
    reelNo: 'PC20230161',
    mateName: '聚酯带 0.05*33',
    prodType: 'KVVP-450/750V 4×2.13',
    inspectionObj: 'PUR122-8812',
    objPosition: '阻水绕包',
    objName: '宁波东方电缆股份有限公司'
  },
  {
    reportNo: 'RN23-0203022',
    reportResult: 0,
    batchNo: 'PC20230162',
    reelNo: 'PC20230162',
    mateName: '聚酯带 0.05*34',
    prodType: 'KVVP-450/750V 4×2.14',
    inspectionObj: 'PUR122-8813',
    objPosition: '阻水绕包',
    objName: '宁波东方电缆股份有限公司'
  },
  {
    reportNo: 'RN23-0203023',
    reportResult: 0,
    batchNo: 'PC20230163',
    reelNo: 'PC20230163',
    mateName: '聚酯带 0.05*35',
    prodType: 'KVVP-450/750V 4×2.15',
    inspectionObj: 'PUR122-8814',
    objPosition: '阻水绕包',
    objName: '宁波东方电缆股份有限公司'
  },
  {
    reportNo: 'RN23-0203024',
    reportResult: 0,
    batchNo: 'PC20230164',
    reelNo: 'PC20230164',
    mateName: '聚酯带 0.05*36',
    prodType: 'KVVP-450/750V 4×2.16',
    inspectionObj: 'PUR122-8815',
    objPosition: '阻水绕包',
    objName: '宁波东方电缆股份有限公司'
  }
];
export const rateOncetable3 = [
  {
    reportNo: 'RN23-0203024',
    reportResult: 0,
    batchNo: 'PC20230327',
    reelNo: 'PC20230327',
    mateName: '聚酯带 0.05*25',
    prodType: 'KVVP-450/750V 4×2.5',
    inspectionObj: 'CK20230304',
    objPosition: '#001',
    objName: '川北智能科技有限公司'
  },
  {
    reportNo: 'RN23-0203025',
    reportResult: 0,
    batchNo: 'PC20230328',
    reelNo: 'PC20230328',
    mateName: '聚酯带 0.05*26',
    prodType: 'KVVP-450/750V 4×2.6',
    inspectionObj: 'CK20230305',
    objPosition: '#002',
    objName: '川北智能科技有限公司'
  },
  {
    reportNo: 'RN23-0203026',
    reportResult: 0,
    batchNo: 'PC20230329',
    reelNo: 'PC20230329',
    mateName: '聚酯带 0.05*27',
    prodType: 'KVVP-450/750V 4×2.7',
    inspectionObj: 'CK20230306',
    objPosition: '#003',
    objName: '川北智能科技有限公司'
  },
  {
    reportNo: 'RN23-0203027',
    reportResult: 0,
    batchNo: 'PC20230330',
    reelNo: 'PC20230330',
    mateName: '聚酯带 0.05*28',
    prodType: 'KVVP-450/750V 4×2.8',
    inspectionObj: 'CK20230307',
    objPosition: '#004',
    objName: '川北智能科技有限公司'
  },
  {
    reportNo: 'RN23-0203028',
    reportResult: 0,
    batchNo: 'PC20230331',
    reelNo: 'PC20230331',
    mateName: '聚酯带 0.05*29',
    prodType: 'KVVP-450/750V 4×2.9',
    inspectionObj: 'CK20230308',
    objPosition: '#005',
    objName: '川北智能科技有限公司'
  },
  {
    reportNo: 'RN23-0203029',
    reportResult: 0,
    batchNo: 'PC20230332',
    reelNo: 'PC20230332',
    mateName: '聚酯带 0.05*30',
    prodType: 'KVVP-450/750V 4×2.10',
    inspectionObj: 'CK20230309',
    objPosition: '#006',
    objName: '川北智能科技有限公司'
  },
  {
    reportNo: 'RN23-0203030',
    reportResult: 0,
    batchNo: 'PC20230333',
    reelNo: 'PC20230333',
    mateName: '聚酯带 0.05*31',
    prodType: 'KVVP-450/750V 4×2.11',
    inspectionObj: 'CK20230310',
    objPosition: '#007',
    objName: '川北智能科技有限公司'
  },
  {
    reportNo: 'RN23-0203031',
    reportResult: 0,
    batchNo: 'PC20230334',
    reelNo: 'PC20230334',
    mateName: '聚酯带 0.05*32',
    prodType: 'KVVP-450/750V 4×2.12',
    inspectionObj: 'CK20230311',
    objPosition: '#008',
    objName: '川北智能科技有限公司'
  },
  {
    reportNo: 'RN23-0203032',
    reportResult: 1,
    batchNo: 'PC20230335',
    reelNo: 'PC20230335',
    mateName: '聚酯带 0.05*33',
    prodType: 'KVVP-450/750V 4×2.13',
    inspectionObj: 'CK20230312',
    objPosition: '#009',
    objName: '川北智能科技有限公司'
  },
  {
    reportNo: 'RN23-0203033',
    reportResult: 0,
    batchNo: 'PC20230336',
    reelNo: 'PC20230336',
    mateName: '聚酯带 0.05*34',
    prodType: 'KVVP-450/750V 4×2.14',
    inspectionObj: 'CK20230313',
    objPosition: '#010',
    objName: '川北智能科技有限公司'
  },
  {
    reportNo: 'RN23-020303034',
    reportResult: 0,
    batchNo: 'PC20230337',
    reelNo: 'PC20230337',
    mateName: '聚酯带 0.05*35',
    prodType: 'KVVP-450/750V 4×2.15',
    inspectionObj: 'CK20230314',
    objPosition: '#011',
    objName: '川北智能科技有限公司'
  },
  {
    reportNo: 'RN23-020303035',
    reportResult: 0,
    batchNo: 'PC20230338',
    reelNo: 'PC20230338',
    mateName: '聚酯带 0.05*36',
    prodType: 'KVVP-450/750V 4×2.16',
    inspectionObj: 'CK20230315',
    objPosition: '#012',
    objName: '川北智能科技有限公司'
  }
];

export const demoSet = [
  0.831, 0.837, 0.837, 0.837, 0.887, 0.837, 0.932, 0.837, 0.837, 0.837, 0.837, 0.837, 0.936, 0.826, 0.836, 0.836, 0.856,
  0.836, 0.836, 0.836, 0.906, 0.846, 0.836, 0.836, 0.836, 0.836, 0.836, 0.836, 0.836, 0.836, 0.836, 0.836, 0.836, 0.836,
  0.837, 0.836, 0.834, 0.834, 0.835, 0.835, 0.835, 0.834, 0.837, 0.984, 0.834, 0.808, 0.831, 0.831, 0.831, 0.831, 0.831,
  0.835, 0.91, 0.921, 0.918, 0.921, 0.837, 0.837, 0.837, 0.837, 0.96, 0.96, 0.96, 0.968, 0.965, 0.815, 0.82, 0.82,
  0.726, 0.837, 0.837, 0.807, 0.807, 0.805, 0.805, 0.804, 0.804, 0.807, 0.807, 0.807, 0.807, 0.804, 0.767, 0.764, 0.77,
  0.771, 0.764, 0.764, 0.764, 0.764, 0.771, 0.768, 0.771, 0.772, 0.772, 0.853, 0.855, 0.856, 0.851, 0.858
];

export const treeData = {
  type: 'entrust',
  name: '委托订单号/申请单号',
  status: 6,
  children: [
    {
      type: 'sample',
      name: '样品编号',
      status: 6,
      children: [
        {
          type: 'test-item',
          name: '检测项目名称1',
          status: 6
        },
        {
          type: 'test-item',
          name: '检测项目名称2',
          status: 1
        },
        {
          type: 'test-item',
          name: '检测项目名称3',
          status: 2
        },
        {
          type: 'test-item',
          name: '检测项目名称4',
          status: 2
        }
      ]
    },
    {
      type: 'sample',
      name: '样品编号',
      status: 6,
      children: [
        {
          type: 'report',
          name: '报告编号1',
          status: 2,
          children: [
            {
              type: 'test-item',
              name: '检测项目名称1',
              status: 6
            },
            {
              type: 'test-item',
              name: '检测项目名称2',
              status: 2
            },
            {
              type: 'test-item',
              name: '检测项目名称3',
              status: 2
            },
            {
              type: 'test-item',
              name: '检测项目名称4',
              status: 2
            },
            {
              type: 'test-item',
              name: '检测项目名称5',
              status: 2
            },
            {
              type: 'test-item',
              name: '检测项目名称6',
              status: 2
            }
          ]
        },
        {
          type: 'report',
          name: '报告编号2'
        },
        {
          type: 'report',
          name: '报告编号3'
        }
      ]
    },
    {
      type: 'sample',
      name: '样品编号',
      status: 6,
      children: [
        {
          type: 'report',
          name: '报告编号1',
          children: [
            {
              type: 'test-item',
              name: '检测项目名称1',
              status: 6
            },
            {
              type: 'test-item',
              name: '检测项目名称2',
              status: 6
            }
          ]
        },
        {
          type: 'report',
          name: '报告编号2',
          children: [
            {
              type: 'test-item',
              name: '检测项目名称1',
              status: 6
            },
            {
              type: 'test-item',
              name: '检测项目名称2',
              status: 6
            }
          ]
        }
      ]
    },
    {
      type: 'sample',
      name: '样品编号',
      status: 6,
      children: [
        {
          type: 'report',
          name: '报告编号',
          children: [
            {
              type: 'test-item',
              name: '检测项目名称1',
              status: 6
            },
            {
              type: 'test-item',
              name: '检测项目名称2',
              status: 6
            },
            {
              type: 'test-item',
              name: '检测项目名称3',
              status: 6
            },
            {
              type: 'test-item',
              name: '检测项目名称4',
              status: 6
            }
          ]
        }
      ]
    }
  ]
};
