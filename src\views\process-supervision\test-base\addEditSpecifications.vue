<template>
  <div class="AddEditSpecifications">
    <!-- 添加规格 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTypeTitle[dialogType]"
      :close-on-click-modal="false"
      width="550px"
      custom-class="submit_dialog"
      @close="cacle()"
    >
      <el-form
        v-if="dialogVisible"
        ref="ruleForm"
        :model="formData"
        label-position="top"
        label-width="120px"
        size="small"
      >
        <el-row :gutter="40">
          <el-col :span="24">
            <el-form-item
              label="标准分类："
              prop="standardCategoryId"
              :rules="{ required: true, message: '请选择标准分类', trigger: 'change' }"
            >
              <el-cascader
                v-model="formData.standardCategoryId"
                :options="standardTree"
                :props="categoryProps"
                placeholder="请选择标准分类"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-row class="productStyle">
              <el-col v-for="(item, index) in specifyList" :key="item.id" :span="11" :offset="index % 2 === 1 ? 2 : 0">
                <el-form-item
                  :label="item.specificationName + '：'"
                  :prop="'productMap.' + item.id"
                  :rules="{
                    required: item.isKeySpecification,
                    message: '请选择' + item.specificationName,
                    trigger: 'change'
                  }"
                >
                  <!-- 使用简化虚拟选择组件，支持搜索和大数据量 -->
                  <SimpleVirtualSelect
                    v-if="item.dictionaryCode"
                    v-model="formData.productMap[item.id]"
                    :placeholder="'请选择' + item.specificationName"
                    :options="getDictionaryOptionsList(item.dictionaryCode)"
                    clearable
                    filterable
                    size="small"
                    :item-height="34"
                    :max-height="200"
                    @change="
                      val => {
                        return handleChangeSelect(val, item.id, index);
                      }
                    "
                  />
                  <el-input
                    v-else
                    v-model.trim="formData.productMap[item.id]"
                    :placeholder="'请输入' + item.specificationName"
                    @input="
                      val => {
                        return handleChangeSelect(val, item.id, index, 'input');
                      }
                    "
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="产品规格："
              prop="productName"
              :rules="{ required: true, message: '请填写产品规格', trigger: 'change' }"
            >
              <el-input v-model="formData.productName" type="textarea" placeholder="请填写产品规格" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cacle()">取 消</el-button>
          <el-button type="primary" @click="onSubmit">确 认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch, getCurrentInstance } from 'vue';
import { formatGgTree } from '@/utils/formatJson';
import { saveProduct, copyProduct, updateProduct } from '@/api/testBase';
import { getDictionary } from '@/api/user';
import SimpleVirtualSelect from '@/components/SimpleVirtualSelect/index.vue';

export default {
  name: 'AddEditSpecifications',
  components: {
    SimpleVirtualSelect
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    specifyList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    dialogType: {
      type: String,
      default: ''
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    productDetail: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();

    const state = reactive({
      dialogVisible: false,
      dialogTitle: '',
      ruleForm: ref(),
      specifyList: [], // 规格列表
      dictionaryCode: [], // 字典code集合
      dictionaryAll: [], // 字典选项集合
      dictionaryOptions: {}, // 获取字典选项
      productJson: {},
      standardTree: [],
      detailData: {}, // 详情
      dialogType: '', // 弹出框类型
      dialogTypeTitle: {
        edit: '编辑规格',
        add: '新增规格',
        copy: '复制规格'
      },
      formData: {
        productMap: {}
      },
      specifyTitle: {}, // 产品规格
      joinCarAll: {}, // 连接符集合
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'code',
        value: 'id'
      }
    });
    watch(
      props,
      newValue => {
        state.dialogVisible = newValue.isShow;
        state.dialogTitle = newValue.dialogTitle;
        state.detailData = props.detailData;
        state.productJson = state.detailData.productJson;
        if (state.dialogVisible) {
          state.dialogType = props.dialogType;
          state.specifyTitle = {};
          // 标准分类树
          state.standardTree = formatGgTree(props.detailData.standardTree);
          // 规格列表
          state.specifyList = props.specifyList;
          state.specifyList.forEach(item => {
            state.joinCarAll[item.id] = item.joinChar;
            state.specifyTitle[item.id] = '';
          });
          if (state.dialogType === 'add') {
            state.formData = {
              standardCategoryId: state.detailData.checkTreeNode.id === 'all' ? '' : state.detailData.checkTreeNode.id,
              materialCategoryCode: state.detailData.materialCode,
              materialCategoryId: state.detailData.materialCategoryId,
              // name: props.detailData.checkTreeNode.name,
              // materialModelId: props.detai lData.checkTreeNode.id,
              productMap: {}
            };
            state.formData.materialCategoryCode = props.detailData.materialCategoryCode;
            getDictionaryData(state.specifyList);
          }

          // else {
          //   console.log('1', props.detailData.productDetail);
          //   if (props.detailData.productDetail) {
          //     state.formData = JSON.parse(JSON.stringify(props.detailData.productDetail));
          //   }
          //   for (var key in state.formData.productMap) {
          //     state.specifyTitle[key] = state.formData.productMap[key];
          //   }
          // }
          // state.formData.materialCategoryCode = props.detailData.materialCategoryCode;
          // getDictionaryData(state.specifyList);
        }
      },
      {
        deep: true
      }
    );

    const onSetProductDetail = (productDetail, isShow) => {
      if (isShow) {
        state.dialogVisible = isShow;
        if (productDetail) {
          state.formData = JSON.parse(JSON.stringify(productDetail));
        }
        for (var key in state.formData.productMap) {
          state.specifyTitle[key] = state.formData.productMap[key];
        }
        state.formData.materialCategoryCode = props.detailData.materialCategoryCode;
        if (!Array.isArray(state.dictionaryAll) || state.dictionaryAll?.length === 0) {
          getDictionaryData(state.specifyList);
        }
      }
    };

    // 获取规格列表中所有的字典code
    const getDictionaryData = dataList => {
      state.dictionaryCode = dataList?.filter(item => {
        if (item.dictionaryCode) {
          return item.dictionaryCode;
        }
      });
      state.dictionaryCode = [
        ...new Set(
          state.dictionaryCode?.map(item => {
            return item.dictionaryCode;
          })
        )
      ];
      getDictionaryOptions(state.dictionaryCode);
    };
    // 获取字典选项 - 优化大数据量一次性加载
    const getDictionaryOptions = async codeList => {
      const loadPromises = codeList.map(async val => {
        try {
          const res = await getDictionary(val);
          if (res && res.data && res.data.data && res.data.data.dictionaryoption) {
            const rawData = res.data.data.dictionaryoption;

            // 直接处理数据，简单高效
            const validOptions = [];
            rawData.forEach(item => {
              if (item && item.status === 1 && item.code && item.name) {
                validOptions.push({
                  value: item.code,
                  label: item.name,
                  disabled: false
                });
              }
            });

            // 构建字典映射（保持向后兼容）
            const dictionary = {};
            validOptions.forEach(option => {
              dictionary[option.value] = option.label;
            });

            state.dictionaryOptions[val] = dictionary;
            state.dictionaryAll[val] = validOptions;
          } else {
            state.dictionaryAll[val] = [];
          }
        } catch (error) {
          console.error(`字典 ${val} 加载失败:`, error);
          // 确保即使出错也有正确的数据结构
          state.dictionaryOptions[val] = {};
          state.dictionaryAll[val] = [];
        }
      });

      // 并行加载所有字典，提高效率
      await Promise.all(loadPromises);
    };

    // 获取格式化的字典选项列表（用于 HighPerformanceSelect）
    const getDictionaryOptionsList = dictionaryCode => {
      if (!dictionaryCode || !state.dictionaryAll || !state.dictionaryAll[dictionaryCode]) {
        return [];
      }

      const options = state.dictionaryAll[dictionaryCode];
      if (!Array.isArray(options)) {
        return [];
      }

      // 返回已经处理好的选项数据
      const validOptions = options.filter(
        option =>
          option &&
          option.value !== null &&
          option.value !== undefined &&
          option.label !== null &&
          option.label !== undefined
      );

      return validOptions;
    };
    // 修改参数
    const handleChangeSelect = (val, itemId) => {
      getSpecifications(val, itemId);
    };
    // 获取产品规格
    const getSpecifications = (name, itemId) => {
      state.specifyTitle[itemId] = name;
      const allKeys = [];
      for (var key in state.specifyTitle) {
        if (state.specifyTitle[key]) {
          allKeys.push(key);
        }
      }
      const newObject = {};
      allKeys.forEach((item, index) => {
        if (index !== Number(allKeys.length - 1) && state.joinCarAll[item] && state.specifyTitle[item]) {
          newObject[item] = state.specifyTitle[item] + state.joinCarAll[item];
        } else {
          newObject[item] = state.specifyTitle[item];
        }
      });
      if (allKeys.length > 0) {
        state.formData.productName = Object.values(newObject).join(' ');
      }
    };
    const onSubmit = () => {
      state.ruleForm.validate().then(valid => {
        if (valid) {
          var standardCategoryId = state.formData.standardCategoryId;
          if (typeof state.formData.standardCategoryId !== 'string') {
            if (state.formData.standardCategoryId !== undefined && state.formData.standardCategoryId.length > 0) {
              standardCategoryId =
                state.formData.standardCategoryId[state.formData.standardCategoryId.length - 1].toString();
            } else {
              standardCategoryId = '';
            }
          }
          var params = {
            ...state.formData,
            standardCategoryId: standardCategoryId
          };
          if (state.dialogType === 'add') {
            saveProduct(params).then(res => {
              if (res) {
                proxy.$message.success(res.data.message);
                cacle(true);
              }
            });
          } else if (state.dialogType === 'copy') {
            params.id = state.formData.id;
            copyProduct(params).then(res => {
              if (res) {
                proxy.$message.success(res.data.message);
                cacle(true);
              }
            });
          } else {
            updateProduct(params).then(res => {
              if (res) {
                cacle(true, params);
                proxy.$message.success('编辑成功');
              }
            });
          }
        }
      });
    };
    const cacle = (isRefresh, params) => {
      state.dialogVisible = false;
      context.emit('closeDialog', { dialogVisible: state.dialogVisible, isRefresh: isRefresh, productDetail: params });
    };
    return {
      ...toRefs(state),
      onSubmit,
      handleChangeSelect,
      getSpecifications,
      getDictionaryData,
      getDictionaryOptions,
      getDictionaryOptionsList,
      cacle,
      onSetProductDetail
    };
  },
  computed: {},
  created() {
    // 初始化字典数据结构
    if (!this.dictionaryAll || typeof this.dictionaryAll !== 'object') {
      this.dictionaryAll = {};
    }

    // 添加测试数据来验证搜索功能
    this.dictionaryAll['TEST_SEARCH'] = [
      { value: 'test-001', label: '产品规格 A1 - 标准型' },
      { value: 'test-002', label: '产品规格 B2 - 增强型' },
      { value: 'test-003', label: '测试标准 ISO9001' },
      { value: 'test-004', label: '质量等级 优秀' },
      { value: 'test-005', label: '认证类型 CE认证' }
    ];

    // 获取字典数据
    this.getDictionaryData();
  }
};
</script>

<style lang="scss" scoped>
.AddEditSpecifications {
  :deep(.el-cascader) {
    width: 100%;
  }
  .productStyle {
    background-color: #f5f7fa;
    border-radius: 3px;
    padding: 0 10px;
  }
  .el-select {
    width: 100%;
  }
  :deep(.el-dialog .el-dialog__body .el-form-item__label) {
    overflow: hidden;
    white-space: nowrap;
    width: 100%;
    text-overflow: ellipsis;
  }
}
</style>
