<template>
  <el-dialog
    :model-value="show"
    custom-class="custom-dialog"
    title="判定标准"
    width="60%"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <div class="dialog-header">
      <div class="header-left">
        <el-input
          ref="inputRef"
          v-model="filterText"
          class="search"
          size="small"
          placeholder="请输入产品名称"
          prefix-icon="el-icon-search"
          clearable
          @keyup.enter="searchItem(filterText)"
        />
        <el-button type="primary" size="small" @click="searchItem(filterText)">查询</el-button>
      </div>
      <div class="header-right">
        <el-button size="small" @click="selectNone(newTreeDetail)" @keyup.prevent @keydown.enter.prevent
          >反选</el-button
        >
        <el-button size="small" @click="selectAll(newTreeDetail)" @keyup.prevent @keydown.enter.prevent>全选</el-button>
      </div>
    </div>
    <div class="dialog-content">
      <el-row>
        <el-col :span="7">
          <div class="tree-container">
            <div class="tree-content">
              <el-tree
                ref="treeRef"
                :data="tree"
                node-key="id"
                :props="defaultProps"
                default-expand-all
                :expand-on-click-node="false"
                :highlight-current="true"
                draggable
                class="leftTree"
                @node-click="clickNode"
              >
                <template #default="{ node }">
                  <span>{{ node.label }}</span>
                </template>
              </el-tree>
            </div>
          </div>
        </el-col>
        <el-col :span="17">
          <div class="list-container">
            <el-table
              ref="tableRef"
              :key="tableKey"
              v-loading="loading"
              :data="newTreeDetail"
              fit
              border
              size="medium"
              height="auto"
              class="dark-table allocation-table base-table format-height-table"
              @select="selectChange"
              @selection-change="handleSelectionChange"
              @row-click="handleRowClick"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column label="产品名称" prop="name" show-overflow-tooltip>
                <template #default="{ row }">
                  <span>{{ row.productName || '--' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="更新时间" prop="lastUpdateDateTime" :width="130" sortable>
                <template #default="{ row }">
                  <span>{{ formatDate(row.lastUpdateDateTime) || '--' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="更新人" prop="user" :width="130">
                <template #default="{ row }">
                  <UserTag :name="getNameByid(row.lastUpdateByuserId) || '--'" />
                </template>
              </el-table-column>
              <el-table-column label="版本" prop="version" :width="100" style="text-align: left">
                <template #default="{ row }">
                  <span>V{{ row.version }}</span>
                </template>
              </el-table-column>
              <el-table-column label="状态" prop="status" :width="100" style="text-align: left">
                <template #default="{ row }">
                  <el-tag size="small" :type="statusDicClass[row.status]"> {{ statusDic[row.status] }}</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="dialog-other">
      <div class="title">
        <label>已选标准</label>
        <el-button v-if="tags.length > 0" size="small" icon="el-icon-delete" @click="clear">清空</el-button>
      </div>
      <div v-if="tags.length > 0" class="select-items">
        <el-tag v-for="tag in tags" :key="tag.productName" closable size="small" @close="closeTag(tag)">
          {{ tag.productName }}
        </el-tag>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="dialogSuccess">确定选择</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, getCurrentInstance, inject, nextTick } from 'vue';
import { getProductList } from '@/api/testBase';
import _ from 'lodash';
import { formatDate } from '@/utils/formatTime';
import { getNameByid } from '@/utils/common';
import UserTag from '@/components/UserTag';
// import { ElLoading } from 'element-plus'
// import { useRoute } from 'vue-router'
// import { formatPaginationList } from '@/utils/formatJson'

export default {
  name: 'JudgementStandards',
  components: { UserTag },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    },
    tree: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'selectData'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const lodash = inject('_');
    const tableRef = ref(null);
    const datas = reactive({
      showDialog: props.show,
      filterText: '',
      inputRef: ref(),
      defaultProps: {
        children: 'children',
        label: 'code'
      },
      tags: [],
      oldTags: [],
      treeDetail: [],
      newTreeDetail: [],
      loading: false,
      tableKey: 0,
      currentTreeNode: {},
      statusDicClass: {
        0: 'info',
        1: 'success',
        2: 'wait'
      },
      statusDic: {
        2: '停用',
        1: '生效',
        0: '草稿'
      }
    });

    watch(
      () => props.show,
      newValue => {
        // console.log(newValue)
        datas.showDialog = newValue;
        if (datas.showDialog) {
          nextTick(() => {
            datas.inputRef.focus();
          });
        }
        datas.tags = [];
        if (props.data.length > 0) {
          datas.oldTags = [];
          props.data.forEach(item => {
            datas.oldTags.push({
              id: item.standardProductId,
              productName: item.standardProductName,
              version: item.standardProductVersion,
              standardCategoryId: item.standardCategoryId,
              isDeleted: false
            });
          });
          datas.oldTags = lodash.uniqBy(datas.oldTags, 'id');
          datas.tags = datas.oldTags;
        }
        // console.log(datas.tags)
        if (newValue && props.tree && props.tree.length > 0) {
          datas.currentTreeNode = props.tree[0];
          proxy.getCapabilityList();
        }
      },
      { deep: true }
    );

    // 过滤树节点
    const treeRef = ref(null);

    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    // 点击树节点
    const clickNode = (data, node) => {
      // console.log(data)
      datas.currentTreeNode = data;
      proxy.getCapabilityList();
    };

    // 确定选择
    const dialogSuccess = () => {
      // console.log('editDialog Success')
      context.emit('selectData', datas.tags);
      datas.showDialog = false;
      context.emit('close', false);
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };
    // 全选
    const selectAll = arr => {
      // console.log('selectAll')
      // console.log(arr)
      if (arr && arr.length > 0) {
        arr.forEach(item => {
          item.checked = true;
        });
        datas.tags = datas.tags.concat(arr);
        datas.tags = lodash.uniqBy(datas.tags, 'id');
        tableRef.value.toggleAllSelection();
      }
    };
    // 反选
    const selectNone = arr => {
      // console.log('selectNone')
      // console.log(arr)
      // console.log(datas.tags)
      if (arr && arr.length > 0) {
        arr.forEach(item => {
          item.checked = !item.checked;
          if (item.checked === false) {
            _.pull(datas.tags, item);
          } else {
            datas.tags.push(item);
          }
          tableRef.value.toggleRowSelection(item);
        });
      }
    };
    // 关闭tags
    const closeTag = tag => {
      // console.log(tag)
      const hasitem = _.filter(datas.newTreeDetail, res => {
        if (res.id === tag.id) {
          res.checked = false;
        }
        return res.id === tag.id;
      });
      if (hasitem.length > 0) {
        tableRef.value.toggleRowSelection(hasitem[0]);
      }
      datas.tags.splice(datas.tags.indexOf(tag), 1);
    };
    // 清空
    const clear = () => {
      datas.tags = [];
      if (datas.newTreeDetail && datas.newTreeDetail.length > 0) {
        tableRef.value.clearSelection();
      }
    };
    // searchItem 查询
    const searchItem = value => {
      if (value) {
        proxy.getCapabilityList();
      } else {
        datas.filterText = '';
        proxy.getCapabilityList();
      }
    };
    // table 选择事件
    const handleSelectionChange = val => {
      // console.log(val)
      // console.log(datas.newTreeDetail)
      if (val.length > 0 && datas.newTreeDetail.length > 0 && val.length === datas.newTreeDetail.length) {
        datas.newTreeDetail.forEach(tree => {
          tree.checked = true;
        });
      } else if (val.length === 0) {
        datas.newTreeDetail.forEach(tree => {
          tree.checked = false;
        });
        _.pullAll(datas.tags, datas.newTreeDetail);
      }
      datas.tags = datas.tags.concat(val);
      datas.tags = lodash.uniqBy(datas.tags, 'id');
    };
    const selectChange = (val, row) => {
      row.checked = !row.checked;
      // console.log(row)
      if (row.checked === false) {
        datas.tags.splice(datas.tags.indexOf(row), 1);
      }
    };

    function handleRowClick(row) {
      if (row && row.id) {
        const rowIndex = datas.newTreeDetail.findIndex(item => item.id === row.id);
        if (rowIndex !== -1) {
          row.checked = !row.checked;
          tableRef.value?.toggleRowSelection(datas.newTreeDetail[rowIndex], row.checked);
          if (row.checked === false) {
            datas.tags.splice(datas.tags.indexOf(row), 1);
          }
        }
      }
    }

    return {
      ...toRefs(datas),
      searchItem,
      dialogSuccess,
      close,
      filterNode,
      selectAll,
      selectNone,
      clickNode,
      treeRef,
      closeTag,
      clear,
      handleSelectionChange,
      selectChange,
      formatDate,
      getNameByid,
      tableRef,
      handleRowClick
    };
  },
  methods: {
    getCapabilityList() {
      // 获取检测项目list
      const _this = this;
      const params = {
        param: _this.filterText,
        standardCategoryId: _this.currentTreeNode.id,
        materialCategoryCode: _this.currentTreeNode.materialCategoryCode,
        page: '1',
        isReleased: true,
        limit: '-1'
      };
      _this.loading = true;
      getProductList(params).then(response => {
        // console.log(response.data.data)
        // console.log(_this.oldTags)
        if (response !== false) {
          const { data } = response.data;
          const filterList = [];
          // 判定标准只能选择启用的项目
          if (data.list && data.list.length > 0) {
            data.list.forEach(fl => {
              if (fl.status === 1) {
                filterList.push(fl);
              }
            });
          }
          _this.treeDetail = filterList;
          _this.newTreeDetail = filterList;
          if (_this.newTreeDetail && _this.newTreeDetail.length > 0) {
            _this.$nextTick(() => {
              // console.log(_this.$refs.tableRef)
              _this.newTreeDetail.forEach(item => {
                item.checked = false;
                const hasitem = _.filter(_this.tags, res => {
                  return res.id === item.id;
                });
                // console.log(hasitem)
                if (hasitem.length >= 1) {
                  item.checked = true;
                  _this.$refs.tableRef.toggleRowSelection(item);
                }
              });
            });
          }
          setTimeout(() => {
            _this.loading = false;
          }, 1.5 * 500);
        } else {
          setTimeout(() => {
            _this.loading = false;
          }, 1.5 * 500);
        }
      });
    }
  }
};
</script>

<style lang="scss">
@import '@/styles/dialog.scss';
</style>
<style lang="scss" scoped>
@import '@/styles/tree.scss';

.dialog-content {
  margin-bottom: 0;
}
.tree-container {
  margin-bottom: 20px;
  .tree-content {
    height: calc(100vh - 500px);
    overflow-y: auto;
  }
}
.list-container {
  padding-bottom: 0;
  margin-bottom: 20px;
  height: calc(100vh - 460px);
  overflow: hidden;
  .dark-table {
    :deep(.el-table__body-wrapper) {
      max-height: calc(100vh - 545px);
      overflow-y: auto;
      overflow-x: hidden !important;
    }
  }
}
</style>
