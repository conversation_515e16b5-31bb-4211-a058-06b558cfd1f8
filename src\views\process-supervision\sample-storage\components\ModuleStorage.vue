<template>
  <!-- 样品入库 dialog -->
  <el-dialog
    v-model="StorageVisible"
    :title="title"
    :width="800"
    top="5vh"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    custom-class="storageDialog lot-dialog"
  >
    <div v-if="StorageVisible" class="storage-box">
      <el-form
        v-for="(item, index) in listData"
        ref="formRefs"
        :key="index"
        v-loading="loading"
        :model="item"
        :rules="rules"
        size="small"
        label-position="top"
      >
        <el-row :gutter="40" class="lot-content">
          <el-col :span="12">
            <el-form-item label="委托编号：">
              <span class="nowrap">{{ item.presentationCode }}</span>
            </el-form-item></el-col
          >
          <el-col :span="12"
            ><el-form-item label="样品名称：">
              <div class="nowrap">{{ item.mateName }}</div>
            </el-form-item></el-col
          >
          <el-col :span="12">
            <el-form-item label="来样日期：" prop="localWarehousingTime">
              <el-date-picker
                v-model="item.localWarehousingTime"
                style="width: 100%"
                type="date"
                placeholder="选择日期"
              /> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="入库数量：" prop="inputWarehouseQuantity">
              <div class="col-between">
                <el-input
                  v-model.trim="item.inputWarehouseQuantity"
                  type="number"
                  autocomplete="off"
                  :min="0"
                  maxlength="18"
                  style="width: 60%"
                />
                <el-select
                  v-model="item.inputWarehouseUnit"
                  :disabled="isadd"
                  placeholder="请选择单位"
                  style="width: 40%; margin-left: 10px"
                >
                  <el-option-group v-for="it in dirList" :key="it.label" :label="it.label">
                    <el-option
                      v-for="val in it.group"
                      :key="val.id"
                      :label="val.name"
                      :value="val.code"
                      :disabled="val.status !== 1"
                    >
                      <span style="float: left">{{ val.name }}</span>
                      <span v-if="val.status !== 1" class="fr" style="color: red">已停用</span>
                    </el-option>
                  </el-option-group>
                </el-select>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="批次：" prop="batchNo">
              <el-input v-model.trim="item.batchNo" autocomplete="off" maxlength="50" /> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="盘号：" prop="reelNo">
              <el-input v-model.trim="item.reelNo" autocomplete="off" maxlength="50" /> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="入库状态：" prop="sampleWarehousingStatus">
              <el-radio-group v-model="item.sampleWarehousingStatus">
                <el-radio label="完好" />
                <el-radio label="其他" />
              </el-radio-group> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="规格长度：" prop="prodType">
              <el-radio-group v-model="item.prodType">
                <el-radio label="完好" />
                <el-radio label="其他" />
              </el-radio-group> </el-form-item
          ></el-col>
          <el-col :span="24">
            <el-form-item label="存放地：" prop="storageLocation">
              <el-input v-model.trim="item.storageLocation" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="入库说明：" prop="warehousingDescription">
              <el-input v-model="item.warehousingDescription" type="textarea" autocomplete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="上传附件：">
              <el-upload
                ref="uploadFileRefs"
                class="avatar-uploader"
                :accept="'.jpg, .png, .jpeg, .gif'"
                multiple
                :action="imgAction"
                :headers="headerconfig"
                :auto-upload="true"
                list-type="picture-card"
                :file-list="item.fileInfo"
                :on-success="
                  (res, file, files) => {
                    return handleFileSuccess(res, file, files, index);
                  }
                "
                :before-upload="beforeUpload"
              >
                <!-- <img src="@/assets/img/empty-img.png" alt="" style="max-width: 100px;"> -->
                <i class="el-icon-plus icon" />
                <template #file="{ file }">
                  <div class="imgPicture">
                    <img :src="file.url" alt="" />
                    <span class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handleDeleteFile(file, index)">
                        <i class="el-icon-delete icon" />
                      </span>
                    </span>
                  </div>
                  <el-select
                    v-if="file.response?.data"
                    v-model="item.fileIdLabels[file.response.data.fileId]"
                    placeholder="请选择"
                    size="small"
                    style="width: 100%"
                    multiple
                    clearable
                    filterable
                    popper-class="select-popper-no-tick"
                    @change="
                      val => {
                        return changeImgKey(val, index, file.response.data.fileId);
                      }
                    "
                  >
                    <el-option
                      v-for="label in item.imgKeyList"
                      :key="label.labelKey"
                      :label="label.labelName"
                      :value="label.labelKey"
                    />
                  </el-select>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-dialog v-model="dialogVisible">
      <img w-full :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog(false)">取 消</el-button>
        <el-button type="primary" @click="onSumbit()">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, getCurrentInstance } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getDictionaryDetail } from '@/api/dictionary';
import { addWarehousing, addWarehousings, updateSampleInventory } from '@/api/samplestorage';
// import { addWarehousing, addWarehousings, uploadStorageFile } from '@/api/samplestorage'
import { ElMessage } from 'element-plus';
import { getToken } from '@/utils/auth';
import { getImgLabelListByMaterialCode } from '@/api/material';
import { sampleTemplateFileUploadUrl } from '@/api/uploadAction';

export default {
  name: 'ModuleStorage',
  props: {
    moduleTitle: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    isadd: {
      type: Boolean,
      default: false
    },
    lists: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['close', 'setinfo'],
  setup(props, ctx) {
    const validateNumber = (rule, value, callback) => {
      const numberReg = /^\d+$|^\d+[.]?\d+$/;
      if (value === '' || value === undefined || value === null) {
        callback(new Error('请输入数字'));
      } else {
        if (numberReg.test(value)) {
          if (value <= 0) {
            callback(new Error('请输入非负整数'));
          } else {
            callback();
          }
        } else {
          callback(new Error('请输入非负整数'));
        }
      }
    };
    const { proxy } = getCurrentInstance();
    const state = reactive({
      uploadinputRef: ref(),
      dialogImageUrl: '',
      dialogVisible: false,
      headerconfig: {
        Authorization: getToken()
      },
      imgAction: sampleTemplateFileUploadUrl(),
      formItem: ref(''),
      dirList: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ],
      StorageVisible: false,
      listData: [],
      isadd: false,
      loading: false,
      files: [],
      fileList: [],
      title: '样品入库',
      rules: {
        localWarehousingTime: [{ required: true, message: '请选择正确的日期', trigger: 'change' }],
        inputWarehouseQuantity: [{ validator: validateNumber, required: true, trigger: 'blur' }],
        inputWarehouseUnit: [{ required: true, message: '请选择单位', trigger: 'change' }],
        batchNo: [{ max: 300, message: '限制300中文字长度', trigger: 'blur' }],
        reelNo: [{ max: 300, message: '限制300中文字长度', trigger: 'blur' }],
        storageLocation: [{ max: 300, message: '限制300中文字长度', trigger: 'blur' }],
        warehousingDescription: [{ max: 300, message: '限制300中文字长度', trigger: 'blur' }]
      },
      form: {
        region: 'shanghai',
        name: ''
      }
    });
    const formRefs = ref([]);
    const uploadFileRefs = ref([]);

    const imgRegex = /.(jpg|png|gif|ico|svg)$/i;

    watch(
      () => props.visible,
      newValue => {
        if (props.visible) {
          state.StorageVisible = props.visible;
          if (state.StorageVisible) {
            state.title = props.moduleTitle;
            state.isadd = props.isadd;
            state.listData = handleList(props.lists);
            state.listData.forEach(item => {
              item.fileIdLabelsMap = {};
              item.fileIdLabels = {};
            });
            getDictionaryList();
            // getItemImgLabelList()
          }
        }
      },
      { deep: true }
    );
    // 关闭弹框
    const closeDialog = i => {
      for (var index = 0; index < formRefs.value.length; index++) {
        formRefs.value[index].clearValidate();
      }
      state.StorageVisible = false;
      ctx.emit('close', i);
    };
    // 给数组默认值
    const handleList = list => {
      let listNew = [];
      if (list.length > 0) {
        list.forEach((item, index) => {
          item.inputWarehouseQuantity = item.sampleQuantity;
          item.inputWarehouseUnit = item.sampleUnit;
          item.prodType = '完好';
          item.sampleWarehousingStatus = '完好';
          item.localWarehousingTime = formatDate(new Date());
          if (index === list.length - 1) {
            listNew = list;
          }
        });
      }
      return listNew;
    };
    const getDictionaryList = () => {
      getDictionaryDetail(5).then(res => {
        res.data.data.dictionaryoption.forEach(item => {
          if (item.status === 1) {
            state.dirList[0].group.push(item);
          } else {
            state.dirList[1].group.push(item);
          }
        });
      });
    };
    const handleFileSuccess = (res, file, files, index) => {
      if (res.code === 200) {
        const fileData = res.data;
        state.listData[index].fileIdLabels[fileData.fileId] = [];
        state.listData[index].fileIdLabelsMap[fileData.fileId] = [];
        state.listData[index].fileInfo = files;
        getItemImgLabelList(index);
      }
    };
    const beforeUpload = file => {
      var fileName = '';
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      const fileSize = file.size / 1024 / 1024 < 20;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过20M');
        return false;
      } else if (fileName !== 'jpg' && fileName !== 'jpeg' && fileName !== 'png' && fileName !== 'gif') {
        proxy.$message.error('仅支持上传图片');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };
    const onSumbit = () => {
      var flag = false;
      if (state.listData.length !== 0) {
        for (var index = 0; index < formRefs.value.length; index++) {
          formRefs.value[index].validate(valid => {
            if (!valid) {
              flag = true;
            }
          });
        }
        if (flag) {
          return false;
        } else {
          if (state.listData.length === 1) {
            if (!state.isUpdate) {
              if (!state.listData[0].fileinfo) {
                state.listData[0].fileId = '';
              }
              warehousing(state.listData[0]);
            } else {
              update(state.listData);
            }
          } else {
            warehousings(state.listData);
          }
        }
      }
    };
    // 入库
    const warehousing = data => {
      state.loading = true;
      addWarehousing(data).then(RES => {
        if (RES.data && RES.data.code === 200) {
          ElMessage.success('入库成功');
          closeDialog(true);
        } else {
          ElMessage.warning(RES.data.message);
        }
        state.loading = false;
      });
    };
    // 批量入库
    const warehousings = data => {
      state.loading = true;
      addWarehousings(data).then(RES => {
        if (RES.data && RES.data.code === 200) {
          ElMessage.success('入库成功');
          closeDialog(true);
        } else {
          ElMessage.warning(RES.data.message);
        }
        state.loading = false;
      });
    };
    const handleDeleteFile = (file, index) => {
      const fileId = file.response.data.fileId;
      uploadFileRefs.value[index].handleRemove(file);
      state.listData[index].fileInfo = state.listData[index].fileInfo.filter(item => item.uid != file.uid);
      delete state.listData[index].fileIdLabels[fileId];
      delete state.listData[index].fileIdLabelsMap[fileId];
    };
    const update = data => {
      updateSampleInventory(data).then(RES => {
        if (RES.data && RES.data.code === 200) {
          ctx.emit('setinfo', data);
          ElMessage.success('入库成功');
          closeDialog(true);
        } else {
          ElMessage.warning(RES.data.message);
        }
        state.loading = false;
      });
    };

    function getItemImgLabelList(index) {
      getImgLabelListByMaterialCode(state.listData[index].mateType).then(res => {
        if (res) {
          state.listData[index].imgKeyList = res.data.data;
        }
      });
    }

    const changeImgKey = (val, index, fileId) => {
      state.listData[index].fileIdLabelsMap[fileId] = [];
      if (val.length) {
        val.forEach(item => {
          state.listData[index].fileIdLabelsMap[fileId].push({
            labelKey: item,
            labelName: state.listData[index].imgKeyList.filter(value => {
              return value.labelKey === item;
            })[0].labelName
          });
        });
      }
    };

    return {
      ...toRefs(state),
      formRefs,
      uploadFileRefs,
      closeDialog,
      handleDeleteFile,
      handleFileSuccess,
      beforeUpload,
      getDictionaryList,
      update,
      warehousing,
      handleList,
      onSumbit,
      getItemImgLabelList,
      imgRegex,
      changeImgKey
    };
  }
};
</script>

<style scoped lang="scss">
// .storage-box {
//   max-height: 750px;
//   overflow-y: auto;
// }
.col-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.file-upload-input {
  display: none;
  z-index: -9999;
}
.tips {
  color: $tes-font2;
  font-size: 12px;
  height: 20px;
  line-height: 20px;
  margin-left: 10px;
}
.flies {
  margin: 10px 0;
  color: $tes-primary;
}
</style>
<style lang="scss">
.storageDialog {
  .el-upload-list--picture-card .el-upload-list__item {
    border: 0;
    height: auto;
    width: 100px;
    padding-bottom: 2px;
    text-align: center;
  }
  .el-upload--picture-card {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
  .imgPicture {
    height: 100px;
    max-width: 100px;
    margin-bottom: 5px;
    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
  .el-upload-list--picture-card .el-upload-list__item-actions {
    height: 100px;
  }
}
</style>
