import request from '@/utils/request';

// 根据检测项目ID和实验方法名称查看试验方法信息
export function getCapabilityById(data) {
  return request({
    url: `/api-capabilitystd/capability/capabilitymethod/getInfoByName`,
    method: 'post',
    data
  });
}

// 标准库产品的项目分类树
export function getStandardItemSort(id) {
  return request({
    url: `/api-capabilitystd/standard/projectcategory/listTree/${id}`,
    method: 'post'
  });
}

// 查询标准库产品的版本
export function getStandardProductVersion(id) {
  return request({
    url: `/api-capabilitystd/standard/standardproduct/info/${id}`,
    method: 'get'
  });
}

/**
 * 保存或更新检测项目图片标签信息
 * @param {*} data
 * @returns
 */
export function saveCapabilityImageLabel(data) {
  return request({
    url: `/api-capabilitystd/capability/capabilityimagelabel/saveOrUpdate`,
    method: 'post',
    data
  });
}

/**
 * 删除检测项目图片标签信息
 * @param {string} id
 * @returns
 */
export function deleteCapabilityImageLabel(id) {
  return request({
    url: `/api-capabilitystd/capability/capabilityimagelabel/delete/${id}`,
    method: 'delete'
  });
}

/**
 * 根据检测项目Id查询检测项目图片标签信息列表
 * @param {string} capabilityId
 * @returns
 */
export function getCapabilityImageLabel(capabilityId) {
  return request({
    url: `/api-capabilitystd/capability/capabilityimagelabel/findByCapabilityId/${capabilityId}`,
    method: 'get'
  });
}
/**
 * 根据检测项目Id查询子项目列表
 * @param {string} capabilityId
 * @returns
 */
export function capabilitySubCategory(capabilityId) {
  return request({
    url: `/api-capabilitystd/capability/capabilitysubcategory/findByCapabilityId/${capabilityId}`,
    method: 'get'
  });
}
/**
 * 删除子项目
 * @param {string} id
 * @returns
 */
export function deleteCapabilitySubCategory(id) {
  return request({
    url: `/api-capabilitystd/capability/capabilitysubcategory/delete/${id}`,
    method: 'delete'
  });
}

/**
 * 保存子项目
 */
export function saveCapabilitySubCategory(data) {
  return request({
    url: `/api-capabilitystd/capability/capabilitysubcategory/saveOrUpdate`,
    method: 'post',
    data
  });
}

/**
 * 根据检测项目Id查询检测依据列表
 * @param {string} capabilityId
 * @returns
 */
export function getCapabilityStandardBasisList(capabilityId) {
  return request({
    url: `/api-capabilitystd/capability/capabilityStandardBasis/list?capabilityId=${capabilityId}`,
    method: 'get'
  });
}

/**
 * 根据检测项目Id查询检测依据列表
 * @param {string} id
 * @param {string} limit
 * @param {string} page
 * @returns
 */
export function getCapabilityStandardBasisDetailPage(params) {
  return request({
    url: '/api-capabilitystd/capability/capabilityStandardBasis/detailPage',
    method: 'get',
    params
  });
}

/**
 * 保存检测依据名称
 */
export function saveCapabilityStandardBasis(data) {
  return request({
    url: '/api-capabilitystd/capability/capabilityStandardBasis/save',
    method: 'post',
    data
  });
}

/**
 * 检测依据下提交导入文件
 * @param {string} id
 * @returns
 */
export function submitCapabilityStandardBasis(params) {
  return request({
    url: '/api-capabilitystd/capability/capabilityStandardBasis/submit',
    method: 'get',
    params
  });
}
