<template>
  <el-dialog
    v-model="HandleVisible"
    title="样品处理"
    :width="480"
    :before-close="closeDialog"
    :close-on-click-modal="false"
  >
    <div class="storage-box">
      <el-form
        ref="refForm"
        v-loading="loading"
        :model="listData"
        :rules="rules"
        size="small"
        label-width="110px"
        label-position="right"
      >
        <el-form-item label="委托编号：" style="margin-bottom: 0">
          <span>{{ listData.presentationCode }}</span>
        </el-form-item>
        <el-form-item label="样品名称：" style="margin-bottom: 0">
          <div class="nowrap" @mouseenter="titleEnter">
            {{ listData.mateName }}
          </div>
        </el-form-item>
        <el-form-item label="处理人：" prop="processingUserId" style="margin-bottom: 10px">
          <span>{{ getNameByid(listData.processingUserId) }}</span>
        </el-form-item>
        <el-form-item label="处理日期：" prop="processingTime">
          <el-date-picker v-model="listData.processingTime" style="width: 100%" type="date" placeholder="选择日期" />
        </el-form-item>
        <el-form-item label="处理方式：" prop="processingManner">
          <el-select v-model="listData.processingManner" style="width: 100%">
            <el-option v-for="it in dictionaryoption" :key="it.code" :label="it.name" :value="it.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="处理数量：" prop="processingNum">
          <div class="col-between">
            <el-input v-model.trim="listData.processingNum" type="number" autocomplete="off" style="width: 60%" />
            <el-select
              v-model="listData.processingUnit"
              disabled
              placeholder="请选择单位"
              style="width: 40%; margin-left: 10px"
            >
              <el-option v-for="it in dirList" :key="it.code" :label="it.name" :value="it.code" />
            </el-select>
          </div>
        </el-form-item>
        <!-- <el-form-item label="" label-width="0px" prop="processingUnit" style="width: 100%;">
              <el-select v-model="listData.processingUnit" disabled placeholder="请选择单位">
                <el-option
                  v-for="it in dirList"
                  :key="it.code"
                  :label="it.name"
                  :value="it.code"
                />
              </el-select>
            </el-form-item> -->
        <el-form-item label="处理说明：" prop="handlingInstructions">
          <el-input v-model="listData.handlingInstructions" type="textarea" :rows="2" autocomplete="off" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog(false)">取 消</el-button>
        <el-button type="primary" @click="sumbit()">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getNameByid } from '@/utils/common';
import { getDictionaryDetail } from '@/api/dictionary';
import { handleSampleInventory } from '@/api/samplestorage';
import { ElMessage } from 'element-plus';
import { getDictionary } from '@/api/user';
import { getLoginInfo } from '@/utils/auth';

export default {
  name: 'ModuleHandle',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    lists: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['close'],
  setup(props, ctx) {
    const validateNumber = (rule, value, callback) => {
      const numberReg = /^\d+$|^\d+[.]?\d+$/;
      if (value === '' || value === undefined || value === null) {
        callback(new Error('请输入数字'));
      } else {
        if (numberReg.test(value)) {
          if (value <= 0) {
            callback(new Error('请输入非负整数'));
          } else {
            callback();
          }
        } else {
          callback(new Error('请输入非负整数'));
        }
      }
    };
    const state = reactive({
      dictionaryoption: [],
      processingUserId: getLoginInfo().accountId,
      uploadinputRef: ref(),
      refForm: ref(''),
      formItem: ref(''),
      HandleVisible: false,
      listData: [],
      defaultValue: '', // 字典的默认值
      loading: false,
      files: [],
      fileList: [],
      rules: {
        processingTime: [{ required: true, message: '请选择正确的日期', trigger: 'change' }],
        processingManner: [{ required: true, message: '请选择处理方式', trigger: 'change' }],
        processingNum: [{ validator: validateNumber, required: true, trigger: 'blur' }],
        processingUnit: [{ required: true, message: '请选择单位', trigger: 'change' }],
        handlingInstructions: [{ max: 300, message: '限制300中文字长度', trigger: 'blur' }]
      },
      form: {}
    });
    watch(
      () => props.visible,
      newValue => {
        if (props.visible) {
          state.listData = props.lists[0];
          state.listData.processingUserId = getLoginInfo().accountId;
          state.listData.processingTime = formatDate(new Date());
          state.listData.processingUnit = state.listData.sampleUnit;
          if (!state.listData.processingManner) {
            state.listData.processingManner = state.defaultValue;
          }
          state.HandleVisible = props.visible;
          if (state.HandleVisible) {
            getDictionaryList();
          }
        }
      },
      { deep: true }
    );
    // 关闭弹框
    const closeDialog = i => {
      state.refForm.clearValidate();
      state.HandleVisible = false;
      ctx.emit('close', i);
    };
    const sumbit = () => {
      state.refForm.validate().then(valid => {
        if (valid) {
          state.listData.internalId = state.listData.id;
          handleSampleInventory(state.listData).then(res => {
            if (res.data.code === 200) {
              ElMessage.success('操作成功');
              closeDialog(true);
            }
          });
        }
      });
    };
    const getDictionaryList = () => {
      console.log(45556);
      getDictionary(3).then(res => {
        if (res.data.code === 200) {
          state.dictionaryoption = res.data.data.dictionaryoption;
          state.defaultValue = state.dictionaryoption.filter(item => item.status === 1)[0].code;
        }
      });
    };
    const titleEnter = e => {
      const target = e.target;
      const { clientWidth, scrollWidth } = target;
      if (scrollWidth > clientWidth) {
        target.title = target.innerText;
      }
    };
    return {
      ...toRefs(state),
      closeDialog,
      getDictionaryList,
      titleEnter,
      getNameByid,
      sumbit
    };
  },
  created() {
    getDictionaryDetail(5).then(res => {
      this.dirList = res.data.data?.dictionaryoption;
    });
  }
};
</script>

<style scoped lang="scss">
.storage-box {
  .col-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  :deep(.el-form .el-form-item.is-required) {
    margin-bottom: 18px;
  }
}
</style>
