<template>
  <div :loading="loading" class="payer-information">
    <!-- 付款方信息 -->
    <div class="panel-header">
      <!-- <el-button size="small" type="primary" icon="el-icon-edit" @click="handleEdit()" @keyup.prevent @keydown.enter.prevent>编辑</el-button> -->
    </div>
    <div class="collapse-content">
      <!--开票信息-->
      <div class="collapse-item">
        <div class="collapse-content-title"><span class="line-space" />开票信息</div>
        <el-row :gutter="20" class="collapse-item">
          <el-col :span="8">
            <span class="title">付款方：</span>
            <span class="txt"> {{ detailInfo.payer.customerName || '--' }}</span>
          </el-col>
          <el-col :span="8">
            <span class="title">公司税号：</span>
            <span class="txt"> {{ detailInfo.payer.taxNo || '--' }}</span>
          </el-col>
          <el-col :span="8">
            <span class="title">电话：</span>
            <span class="txt"> {{ detailInfo.payer.phone || '--' }}</span>
          </el-col>
          <el-col :span="8">
            <span class="title">开户行：</span>
            <span class="txt"> {{ detailInfo.payer.openingBank || '--' }}</span>
          </el-col>
          <el-col :span="8">
            <span class="title">账号：</span>
            <span class="txt">
              {{ detailInfo.payer.acctNo || '--' }}
            </span>
          </el-col>
          <el-col :span="24">
            <span class="title">开票地址：</span>
            <span class="txt"> {{ detailInfo.payer.address || '--' }} </span>
          </el-col>
        </el-row>
        <el-divider />
      </div>
      <!--发票邮寄-->
      <div class="collapse-item">
        <div class="collapse-content-title"><span class="line-space" />发票邮寄</div>
        <el-row :gutter="20" class="collapse-item">
          <el-col :span="8">
            <span class="title">发票邮寄抬头：</span>
            <span class="txt"> {{ detailInfo.invoice.customerName || '--' }}</span>
          </el-col>
          <el-col :span="8">
            <span class="title">联系人：</span>
            <span class="txt"> {{ detailInfo.invoice.contactsName || '--' }}</span>
          </el-col>
          <el-col :span="8">
            <span class="title">电话：</span>
            <span class="txt"> {{ detailInfo.invoice.phone || '--' }}</span>
          </el-col>
          <el-col :span="24">
            <span class="title">收件地址：</span>
            <span class="txt"> {{ detailInfo.invoice.address || '--' }}</span>
          </el-col>
        </el-row>
      </div>
    </div>
    <DialogPayerInfo :dialog-visible="dialogShow" :detail-data="detailInfo" @closeDialog="closeDialog" />
  </div>
</template>
<script>
import { reactive, toRefs, ref } from 'vue';
import { getPermissionBtn } from '@/utils/common';
import { getNameByid } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { useRoute } from 'vue-router';
import DialogPayerInfo from './dialog-payer-info';
import { getTaskCustomerInfo } from '@/api/task-registration';

export default {
  name: 'PayerInformation',
  components: { DialogPayerInfo },
  props: {},
  emits: ['refreshDetail'],
  setup(props, context) {
    const route = useRoute();
    const state = reactive({
      tableRef: ref(),
      dialogShow: false,
      detailInfo: {
        payer: {},
        invoice: {}
      },
      tableList: [],
      selectRow: {}, // 选中的数据
      oldTableList: [],
      loading: false
    });

    // 查询详情
    const getDetailList = () => {
      state.loading = true;
      getTaskCustomerInfo(route.query.id).then(res => {
        state.loading = false;
        if (res) {
          state.detailInfo = res.data.data;
        }
      });
    };
    getDetailList();
    const closeDialog = val => {
      state.dialogShow = false;
      if (val) {
        getDetailList();
        context.emit('refreshDetail', true);
      }
    };

    const handleHaveRevise = (val, type) => {
      state.isRefresh[type] = val;
    };
    const handleEdit = () => {
      state.dialogShow = true;
    };
    return {
      ...toRefs(state),
      drageHeader,
      closeDialog,
      handleEdit,
      colWidth,
      getDetailList,
      getPermissionBtn,
      handleHaveRevise,
      getNameByid
    };
  }
};
</script>

<style lang="scss" scoped>
.panel-header {
  margin-top: 8px;
  text-align: right;
}
.collection-record {
  margin: 20px 0 0 0;
}
.btnGroup {
  margin: 0 0 10px 0;
}
.collapse-content {
  padding: 15px 0 0 0 !important;
}
</style>
