<template>
  <el-table
    :key="dataTableKey"
    :data="dataList"
    fit
    border
    size="medium"
    class="dark-table base-table"
    :height="{ tableHeight }"
    :class="{ 'format-height-table': tableType === 0 || tableType === 1, 'no-quick-query': tableType === 1 }"
  >
    <template v-for="(item, index) in list" :key="index">
      <el-table-column
        v-if="item.checkbox && item.isHide !== 1"
        :prop="item.field"
        :label="item.name"
        :sortable="Number(item.isOrder) === 1"
        :width="item.isMinWidth ? '' : getColWidth(item.colWidth)"
        :min-width="item.isMinWidth ? getColWidth(item.colWidth) : ''"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div v-if="item.type === 0">
            <!-- 默认字段显示 -->
            {{ row[item.field] || '--' }}
          </div>
          <div v-else-if="item.type === 1">
            <!-- 用户名字段显示 -->
            <UserTag :name="getNameByid(row[item.field]) || row[item.field] || '--'" />
          </div>
          <div v-else-if="item.type === 2">
            <!-- 日期字段显示 -->
            <span>{{ formatDate(row[item.field]) || '--' }}</span>
          </div>
          <div v-else-if="item.type === 3">
            <!-- 链接跳转字段显示 -->
            <span class="nowrap blue-color" @click="jumpPage(row)">{{ row[item.field] || '--' }}</span>
          </div>
          <div v-else-if="item.type === 4">
            <!-- 状态字段显示 -->
            <el-tag size="small" effect="dark" :type="filterStatus(row[item.field])[0]">
              {{ filterStatus(row[item.field])[1] }}
            </el-tag>
          </div>
          <div v-else>
            <!-- 未处理字段显示 -->
            {{ row[item.field] || '--' }}
          </div>
        </template>
      </el-table-column>
    </template>
  </el-table>
</template>
<script>
import { colWidth } from '@/data/tableStyle';
import { ref, reactive, toRefs, onMounted, watch, nextTick } from 'vue';
import { getColWidth } from '@/utils/func/customTable';
import UserTag from '@/components/UserTag';
import { getNameByid } from '@/utils/common';
import router from '@/router/index.js';

export default {
  name: 'CommonTable',
  components: { UserTag },
  props: {
    tableInfo: {
      type: Object,
      default: function () {
        return {
          tableHeader: [],
          tableData: []
        };
      }
    },
    tableType: {
      type: Number,
      default: function () {
        // 0 代表默认列表页表格
        // 1 代表无快捷查询的列表页表格
        // 2 代表通过height属性自定义高度的表格
        return 0;
      }
    },
    tableHeight: {
      type: String,
      default: function () {
        return 'auto';
      }
    }
  },
  setup(props, context) {
    const tableRef = ref(null);
    const data = reactive({
      selectable: true,
      list: props.tableInfo.tableHeader,
      dataList: props.tableInfo.tableData,
      tableKey: 0,
      dataTableKey: 0
    });

    function handleFormatter(row, column, cellValue, index) {
      data.list.forEach(item => {
        if (item.field === column.property) {
          // 更具styleContent, styleType来格式化
          switch (item.styleType) {
            case 0:
              // 默认样式
              break;
            case 1:
              // 跳转连接
              break;
            case 2:
              // 用户名显示
              break;
            case 3:
              // 状态标签显示
              break;
            case 4:
              // 按钮操作
              break;
            default:
              break;
          }
        }
      });
    }

    function handleRouteJump(row, styleContent) {
      const queryParams = {};
      styleContent.params.forEach(item => {
        if (item.name) {
          if (row[item.code] && (item.value === 0 || !item.value)) {
            queryParams[item.name] = row[item.code];
          } else {
            queryParams[item.name] = item.value;
          }
        } else {
          if (row[item.code] && (item.value === 0 || !item.value)) {
            queryParams[item.code] = row[item.code];
          } else {
            queryParams[item.code] = item.value;
          }
        }
      });
      router.push({
        path: styleContent.path,
        query: queryParams
      });
    }

    // 查看申请详情
    const jumpPage = row => {};

    // 过滤审批状态颜色
    const filterStatus = status => {
      // 审批状态  1、待提交2、待审核、3、待签字4、待盖章5、待发送6、已完成
      const classMap = {
        1: ['info', '待提交'],
        2: ['warning', '待审核'],
        3: ['warning', '待签字'],
        4: ['warning', '待盖章'],
        5: ['warning', '待发送'],
        6: ['success', '已完成']
      };
      return classMap[status];
    };

    onMounted(() => {});

    watch(
      () => props.tableInfo.tableHeader,
      (newValue, oldValue) => {
        data.list = newValue;
      }
    );

    watch(
      () => props.tableInfo.tableData,
      (newValue, oldValue) => {
        data.dataList = newValue;
      }
    );

    watch(
      () => data.tableKey,
      (newValue, oldValue) => {
        nextTick(() => {});
      }
    );

    return {
      ...toRefs(data),
      colWidth,
      tableRef,
      getColWidth,
      handleFormatter,
      getNameByid,
      handleRouteJump,
      jumpPage,
      filterStatus
    };
  }
};
</script>
<style lang="scss" scoped>
.pop-zr {
  width: 320px;
  position: fixed;
  top: 200px;
  right: 50px;
  transition: all 0.2s ease 0s;
  z-index: 10000;

  .pop-content {
    max-height: 300px;
    overflow-y: auto;
  }

  #title {
    height: 1rem;
    width: 100%;
    text-align: left;
    margin-bottom: 5px;
    font-size: 1rem;
    font-weight: bold;
    line-height: 1rem;
  }

  .i-close {
    position: absolute;
    top: 10px;
    right: 5px;
    cursor: pointer;
  }
}

.settings-button {
  width: 280px;
  position: fixed;
  top: 180px;
  right: 50px;
  transition: all 0.2s ease 0s;
  z-index: 10000;
}

.blue-color {
  color: $tes-primary;
  cursor: pointer;
}
</style>
