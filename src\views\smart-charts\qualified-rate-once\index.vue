<template>
  <!-- 一次交检合格率 -->
  <ListLayout :has-custom-header="true" :has-custom-main="true" :main-offset-top="topHeight">
    <template #page-custom-header>
      <SingleLineHeader>
        <template #left-form-group>
          <el-form
            ref="editFrom"
            label-width="110px"
            label-position="top"
            :inline="true"
            :model="searchForm"
            @submit.prevent
          >
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item
                  label="报告日期："
                  class="date-range"
                  prop="submitTime"
                  :rules="{
                    required: true,
                    message: '请选择时间范围',
                    trigger: 'change'
                  }"
                >
                  <el-date-picker
                    v-model="searchForm.submitTime"
                    type="daterange"
                    size="small"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :shortcuts="shortcuts"
                    @change="handleDatePicker"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  label="检验类型："
                  prop="type"
                  :rules="{
                    required: true,
                    message: '请选择检验类型',
                    trigger: 'change'
                  }"
                >
                  <el-select
                    v-model="searchForm.type"
                    class="chart-query-select"
                    placeholder="选择检验类型"
                    size="small"
                    clearable
                    @change="changeInspectionType"
                  >
                    <el-option
                      v-for="item in inspectionTypeOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
        <template #right-button-group>
          <el-button
            key="sample"
            type="text"
            size="small"
            @click="renderSampleData()"
            @keyup.prevent
            @keydown.enter.prevent
          >
            <span class="el-icon-data-analysis" />
          </el-button>
          <el-button key="cancel" size="small" @click="cancelRender()" @keyup.prevent @keydown.enter.prevent
            >清空</el-button
          >
          <el-button
            :loading="renderLoading"
            type="primary"
            size="small"
            @click="initData()"
            @keyup.prevent
            @keydown.enter.prevent
            >查询</el-button
          >
        </template>
      </SingleLineHeader>
    </template>

    <template #page-custom-main>
      <el-row>
        <el-col v-loading="renderLoading" :span="19">
          <CustomPanel
            :has-margin-top="true"
            :has-margin-right="true"
            :has-margin-bottom="true"
            :has-margin-left="true"
          >
            <template #panel-title>
              <div class="panel-header-left">
                <span class="title">{{ unqualifiedFreqTitle }}</span>
                <el-button v-if="isShowReturn" type="primary" @click="echartsReturn">返回</el-button>
              </div>
            </template>
            <template #panel-content>
              <line-bar-pie-chart
                v-if="detailData.resultList.length"
                :option="unqualifiedFreqChartOption"
                :width="'100%'"
                :height="'40vh'"
                @clickEcharts="handleClickEcharts"
              />
              <el-empty v-else :image="emptyImg" description="暂无图表" />
            </template>
          </CustomPanel>
        </el-col>
        <el-col v-loading="renderLoading" :span="5">
          <CustomPanel :has-margin-top="true" :has-margin-right="true" :has-margin-bottom="true" class="heightSample">
            <template #panel-title>
              <div class="panel-header-left" style="width: 100%">
                <el-row style="width: 100%">
                  <el-col :span="24" style="text-align: left">
                    <span class="title">{{ unqualifiedItemTitle }}</span>
                  </el-col>
                </el-row>
              </div>
            </template>
            <template #panel-content>
              <ul v-if="detailData.resultList.length">
                <li v-for="(item, index) in detailData.resultList" :key="index">
                  <span>{{ item.name }}</span>
                  <el-progress :percentage="item.firstPassYield" />
                </li>
              </ul>
              <el-empty v-else :image="emptyData" description="暂无数据" />
            </template>
          </CustomPanel>
        </el-col>
      </el-row>
      <el-row>
        <el-col v-loading="renderLoading" :span="24">
          <CustomPanel :has-margin-right="true" :has-margin-left="true">
            <template #panel-title>
              <div class="panel-header-left">
                <span class="title">{{ qualifiedTitle }}</span>
              </div>
            </template>
            <template #panel-content>
              <el-table
                :data="detailData.detailList"
                fit
                border
                height="auto"
                size="medium"
                class="dark-table format-height-table format-height-table2 no-quick-query base-table"
              >
                <el-table-column label="序号" type="index" width="55" />
                <el-table-column label="报告编号" prop="reportNo" :width="colWidth.orderNo" show-overflow-tooltip>
                  <template #default="{ row }">
                    <span>{{ row.reportNo || '--' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="检验结论" prop="lastUpdateDateTime" :min-width="colWidth.result">
                  <template #default="{ row }">
                    <span v-if="row.reportResult === 0">合格</span>
                    <span v-else-if="row.reportResult === 1">不合格</span>
                    <span v-else>--</span>
                  </template>
                </el-table-column>
                <el-table-column label="批次" prop="batchNo" :width="130" show-overflow-tooltip>
                  <template #default="{ row }">
                    {{ row.batchNo || '--' }}
                  </template>
                </el-table-column>
                <el-table-column label="盘号" prop="reelNo" :width="130" show-overflow-tooltip>
                  <template #default="{ row }">
                    {{ row.reelNo || '--' }}
                  </template>
                </el-table-column>
                <el-table-column label="样品名称" prop="mateName" :min-width="colWidth.name" show-overflow-tooltip>
                  <template #default="{ row }">
                    {{ row.mateName || '--' }}
                  </template>
                </el-table-column>
                <el-table-column label="型号规格" prop="prodType" :min-width="colWidth.model" show-overflow-tooltip>
                  <template #default="{ row }">
                    {{ row.prodType || '--' }}
                  </template>
                </el-table-column>
                <el-table-column label="检验对象" prop="inspectionObj" :width="colWidth.objectNo" show-overflow-tooltip>
                  <template #default="{ row }">
                    {{ row.inspectionObj || '--' }}
                  </template>
                </el-table-column>
                <el-table-column label="对象位置" prop="objPosition" :min-width="colWidth.name" show-overflow-tooltip>
                  <template #default="{ row }">
                    {{ row.objPosition || '--' }}
                  </template>
                </el-table-column>
                <el-table-column label="对象名称" prop="objName" :min-width="colWidth.name" show-overflow-tooltip>
                  <template #default="{ row }">
                    {{ row.objName || '--' }}
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </CustomPanel>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <div style="height: 50px; width: 100%" />
        </el-col>
      </el-row>
    </template>
    <template #other>
      <BottomPanel>
        <template #panel-content>
          <div style="text-align: right">
            <el-button
              :loading="renderLoading"
              type="primary"
              size="small"
              @click="exportToExcel()"
              @keyup.prevent
              @keydown.enter.prevent
              >数据导出</el-button
            >
          </div>
        </template>
      </BottomPanel>
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, onMounted } from 'vue';
import LineBarPieChart from '@/components/LineBarPieChart';
import ListLayout from '@/components/ListLayout';
import { formatDate } from '@/utils/formatTime';
// formatDateTime
import { InspectionType } from '@/data/industryTerm';
import { getAxisInterval, getAxisLabelInterval } from '../func/calculate';
import {
  qualifiedRateOnceData,
  qualifiedRateOnceData2,
  qualifiedRateOnceData3,
  rateOncetable,
  rateOncetable2,
  rateOncetable3
} from '../data/testData';
import { past3Months } from '@/data/dateShortcuts';
import SingleLineHeader from '@/components/PageComponents/SingleLineHeader';
import CustomPanel from '@/components/PageComponents/CustomPanel';
import BottomPanel from '@/components/PageComponents/BottomPanel';
import { ElMessage } from 'element-plus';
import { getQualifiedOnce, exportData } from '@/api/qualifiedRateOnce';
import { formatterTips } from '../func/formatter';
import { getNameByid, calculator } from '@/utils/common';
import { colWidth } from '@/data/tableStyle';
import { getDictionary } from '@/api/user';
import emptyImg from '@/assets/img/empty-chart.png';
import emptyData from '@/assets/img/empty-data.png';

export default {
  name: 'QualifiedRateOnce',
  components: {
    ListLayout,
    LineBarPieChart,
    SingleLineHeader,
    CustomPanel,
    BottomPanel
  },
  setup(props, context) {
    const processData = reactive({
      isAdd: false
    });
    const state = reactive({
      rankingList: [], // 右侧排名列表
      searchForm: {
        type: '',
        submitTime: [formatDate(new Date().getTime() - 3600 * 1000 * 24 * 90), formatDate(new Date())],
        startTime: formatDate(new Date().getTime() - 3600 * 1000 * 24 * 90),
        endTime: formatDate(new Date())
      },
      tableData: [],
      detailData: {
        detailList: [],
        resultList: []
      },
      isShowReturn: false,
      shortcuts: past3Months,
      emphasisStyle: {},
      inspectionTypeOptions: InspectionType,
      topHeight: 120,
      qualifiedTitle: '单据明细',
      unqualifiedFreqTitle: '原材料一次交检合格率',
      unqualifiedItemTitle: '供应商合格率排名',
      qualifiedTotalName: '入库数量',
      renderLoading: false
    });
    const unqualifiedFreqChartOption = ref({
      color: ['#80D9C5', '#F2D09D'],
      title: {
        text: ''
      },
      tooltip: {
        trigger: 'axis',
        extraCssText: 'text-align:left', // 设置tooltip的自定义样式
        textStyle: {
          color: '#303133'
        },
        formatter: function (params) {
          return formatterTips(params);
        }
      },
      legend: {
        data: ['一次合格数量', '入库数量', '合格率'],
        itemGap: 40
      },
      grid: {
        left: '20',
        right: '20',
        bottom: '10',
        containLabel: true
      },
      toolbox: {},
      xAxis: {
        type: 'category',
        splitLine: { show: false },
        splitArea: { show: false },
        data: [],
        axisLabel: {
          interval: 0,
          rotate: 20,
          margin: 10,
          fontSize: 12,
          autoRotate: true
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '数量',
          interval: 0,
          min: 0,
          max: 0
        },
        {
          type: 'value',
          name: '百分比',
          interval: 0,
          min: 0,
          max: 0,
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      series: [
        {
          name: '一次合格数量',
          type: 'bar',
          emphasis: state.emphasisStyle,
          barWidth: '48%',
          barMaxWidth: 38,
          data: [],
          itemStyle: {
            borderWidth: 2,
            borderColor: '#00B38A'
          }
        },
        {
          name: '入库数量',
          type: 'bar',
          emphasis: state.emphasisStyle,
          data: [],
          barWidth: '48%',
          barMaxWidth: 38,
          itemStyle: {
            borderWidth: 2,
            borderColor: '#E6A23C'
          }
        },
        {
          name: '合格率',
          type: 'line',
          yAxisIndex: 1,
          symbol: 'circle',
          symbolSize: 10,
          emphasis: state.emphasisStyle,
          data: [],
          itemStyle: {
            color: '#F8E3C5',
            borderWidth: 2,
            borderColor: '#E6A23C'
          },
          lineStyle: {
            color: '#E6A23C',
            width: 2,
            shadowColor: 'rgba(230, 162, 60, 0.5)',
            shadowBlur: 6,
            shadowOffsetY: 4
          }
        }
      ]
    });

    // 切换图表
    const switchCharts = () => {
      if (state.searchForm.type === '1') {
        state.qualifiedTitle = '生产合格率';
        state.unqualifiedFreqTitle = '原材料一次交检合格率';
        state.unqualifiedItemTitle = '供应商合格率排名';
        state.qualifiedTotalName = '供货数量';
      } else if (state.searchForm.type === '2') {
        state.qualifiedTitle = '生产合格率';
        state.unqualifiedFreqTitle = '过程检一次交检合格率';
        state.unqualifiedItemTitle = '工序合格率排名';
        state.qualifiedTotalName = '生产数量';
      } else {
        state.qualifiedTitle = '单据明细';
        state.unqualifiedFreqTitle = '完工检一次交检合格率';
        state.unqualifiedItemTitle = '物料分组合格率排名';
        state.qualifiedTotalName = '入库数量';
      }
    };

    const renderSampleData = () => {
      state.isShowReturn = false;
      if (state.searchForm.type === '1') {
        state.detailData.resultList = calculation(qualifiedRateOnceData);
        state.detailData.detailList = rateOncetable;
      } else if (state.searchForm.type === '2') {
        state.detailData.resultList = calculation(qualifiedRateOnceData2);
        state.detailData.detailList = rateOncetable2;
      } else {
        state.detailData.resultList = calculation(qualifiedRateOnceData3);
        state.detailData.detailList = rateOncetable3;
      }
      formatterEchartsData(state.detailData.resultList);
    };
    const calculation = array => {
      const newArray = JSON.parse(JSON.stringify(array));
      newArray.forEach(item => {
        item.firstPassYield = calculator(item.firstPassYield).multiply(100).toNumber();
        item.subList.forEach(val => {
          val.firstPassYield = calculator(val.firstPassYield).multiply(100).toNumber();
        });
      });
      return newArray;
    };
    const formatterEchartsData = chartsData => {
      const firstQuantity = [];
      const secondQuantity = [];
      const passRate = [];
      const xAxisNames = [];
      chartsData.forEach(item => {
        xAxisNames.push(item.name);
        firstQuantity.push({ value: item.firstPassNum, subList: item.subList });
        secondQuantity.push({ value: item.totalNum, subList: item.subList });
        passRate.push({ value: item.firstPassYield, subList: item.subList });
      });
      initEcharts(xAxisNames, firstQuantity, secondQuantity, passRate, chartsData);
    };
    // x轴：xAxisNames、数据：firstQuantity、secondQuantity、passRate，总数据：chartsData
    const initEcharts = (xAxisNames, firstQuantity, secondQuantity, passRate, chartsData) => {
      if (state.searchForm.type === '1') {
        unqualifiedFreqChartOption.value.legend.data = ['一次合格数量', '入库数量', '合格率'];
        unqualifiedFreqChartOption.value.series[0].name = '一次合格数量';
        unqualifiedFreqChartOption.value.series[1].name = '入库数量';
        unqualifiedFreqChartOption.value.series[2].name = '合格率';
      } else if (state.searchForm.type === '2') {
        unqualifiedFreqChartOption.value.legend.data = ['一次合格数量', '生产数量', '合格率'];
        unqualifiedFreqChartOption.value.series[0].name = '一次合格数量';
        unqualifiedFreqChartOption.value.series[1].name = '生产数量';
        unqualifiedFreqChartOption.value.series[2].name = '合格率';
      } else {
        unqualifiedFreqChartOption.value.legend.data = ['一次合格数量', '生产数量', '合格率'];
        unqualifiedFreqChartOption.value.series[0].name = '一次合格数量';
        unqualifiedFreqChartOption.value.series[1].name = '生产数量';
        unqualifiedFreqChartOption.value.series[2].name = '合格率';
      }
      // 数据
      unqualifiedFreqChartOption.value.series[0].data = firstQuantity;
      unqualifiedFreqChartOption.value.series[1].data = secondQuantity;
      unqualifiedFreqChartOption.value.series[2].data = passRate;
      const firstQuantityValue = firstQuantity.map(item => item.value);
      const secondQuantityValue = secondQuantity.map(item => item.value);
      // x轴
      unqualifiedFreqChartOption.value.xAxis.data = xAxisNames;
      unqualifiedFreqChartOption.value.xAxis.axisLabel.interval = getAxisLabelInterval(xAxisNames.length, 0.25, 80);
      // y轴 数量
      const quantityMax = Math.max.apply(null, [...new Set(firstQuantityValue), ...new Set(secondQuantityValue)]);
      const quantityInfo = getAxisInterval(0, quantityMax, chartsData.length);
      unqualifiedFreqChartOption.value.yAxis[0].interval = quantityInfo.axistInterval;
      unqualifiedFreqChartOption.value.yAxis[0].min = 0;
      unqualifiedFreqChartOption.value.yAxis[0].max = quantityInfo.axisMax;
      // y轴 百分比
      const percentageInfo = getAxisInterval(0, chartsData[0]?.firstPassYield, chartsData.length);
      unqualifiedFreqChartOption.value.yAxis[1].interval = percentageInfo.axistInterval;
      unqualifiedFreqChartOption.value.yAxis[1].min = 0;
      unqualifiedFreqChartOption.value.yAxis[1].max = percentageInfo.axisMax;
    };

    const handleDatePicker = value => {
      if (value) {
        state.expired = false;
        state.searchForm.startTime = formatDate(value[0]);
        state.searchForm.endTime = formatDate(value[1]);
      } else {
        state.searchForm.startTime = '';
        state.searchForm.endTime = '';
      }
      initData();
    };

    const getInspectionTypeList = () => {
      getDictionary('JYLX').then(res => {
        const resultData = res.data.data;
        state.inspectionTypeOptions = [];
        resultData.dictionaryoption.forEach(item => {
          if (item.status === 1 && (item.code === '1' || item.code === '2' || item.code === '3')) {
            state.inspectionTypeOptions.push(item);
          }
        });
        state.searchForm.type = state.inspectionTypeOptions[0]?.code;
        initData();
      });
    };

    const changeInspectionType = () => {
      switchCharts();
      initData();
    };

    const getQualifiedPostBody = () => {
      if (!state.searchForm.startTime || !state.searchForm.endTime) {
        ElMessage.warning({
          message: '请先选择检测日期',
          type: 'warning'
        });
        return false;
      }
      if (!state.searchForm.type) {
        ElMessage.warning({
          message: '请先选择检验类型',
          type: 'warning'
        });
        return false;
      }
      const qualifiedRatePostBody = {
        reportDateStart: state.searchForm.startTime,
        reportDateEnd: state.searchForm.endTime,
        type: state.searchForm.type,
        materialGroupCode: state.searchForm.materialGroupNo,
        procedureCode: state.searchForm.workingProcedureCode
      };

      return qualifiedRatePostBody;
    };

    const initData = () => {
      state.isShowReturn = false;
      const qualifiedRatePostBody = getQualifiedPostBody();
      if (qualifiedRatePostBody) {
        state.renderLoading = true;
        getQualifiedOnce(qualifiedRatePostBody)
          .then(res => {
            state.renderLoading = false;
            if (res) {
              state.detailData = res.data.data;
              if (Object.keys(state.detailData).length === 0) {
                ElMessage.warning({
                  message: '查询不到相关数据',
                  type: 'warning'
                });
                state.detailData = {
                  detailList: [],
                  resultList: []
                };
                return;
              } else {
                state.detailData.resultList = calculation(state.detailData.resultList);
                formatterEchartsData(state.detailData.resultList);
              }
            }
          })
          .catch(err => {
            console.log(err);
            ElMessage.error({
              message: `${err.message}`,
              type: 'error'
            });
          })
          .finally(() => {
            state.renderLoading = false;
          });
      }
    };

    const cancelRender = () => {
      state.isShowReturn = false;
      state.detailData = {
        detailList: [],
        resultList: []
      };
    };

    onMounted(() => {
      getInspectionTypeList();
    });
    const handleClickEcharts = nodeData => {
      state.renderLoading = true;
      if (nodeData.data.subList?.length > 0) {
        state.isShowReturn = true;
        formatterEchartsData(nodeData.data.subList);
      } else if (!state.isShowReturn) {
        ElMessage.warning('暂无数据');
      }
      state.renderLoading = false;
    };
    // 返回
    const echartsReturn = () => {
      state.isShowReturn = false;
      formatterEchartsData(state.detailData.resultList);
    };
    const exportToExcel = () => {
      const keyParamPostBody = getQualifiedPostBody();
      if (!keyParamPostBody) {
        return;
      }
      state.renderLoading = true;
      exportData(keyParamPostBody).then(res => {
        state.renderLoading = false;
        if (res) {
          const exportList = res.data.data;
          if (exportList.length > 0) {
            const tHeader = [
              '检验类型',
              '物料分组编号',
              '物料分组',
              '试验日期',
              '样品编号',
              '物料编号',
              '样品名称(物料名称)',
              '型号规格',
              '电压等级', // 待定
              '批次',
              '盘号',
              '入库/生产数量', // 待定
              '实验负责人', // 待定
              '检验对象(入库单号/订单编号)',
              '对象位置(入库仓库/工序/机台)',
              '对象名称(供应商名称/客户名称)',
              '不良品单号', // 待定
              '原因分析',
              '处置结果',
              '合格数量',
              '报告编号',
              '签发日期'
            ];
            const filterVal = [
              'type',
              'materialGroupNo',
              'materialGroup',
              'expDate',
              'sampleNumber',
              'materialNo',
              'mateName',
              'prodType',
              'voltName',
              'batchNo',
              'reelNo',
              'productionQuantity',
              'ownerId',
              'inspectionObj',
              'objPosition',
              'objName',
              'defectiveProductNo',
              'reason',
              'disposalType',
              'qualifiedNum',
              'reportNo',
              'issueDate'
            ];
            const fileName = '一次交检合格率';
            import('@/utils/Export2Excel').then(excel => {
              const data = formatExcelData(filterVal, exportList);
              excel.export_json_to_excel({
                header: tHeader,
                data,
                filename: fileName,
                autoWidth: true,
                bookType: 'xlsx'
              });
              state.renderLoading = false;
            });
          } else {
            ElMessage.warning({
              message: '暂无可导出的的数据!',
              type: 'warning'
            });
          }
        }
      });
    };
    const formatExcelData = (filterVal, jsonData) => {
      const typeJson = {};
      InspectionType.forEach(item => {
        typeJson[item.id] = item.name;
      });
      return jsonData.map(v =>
        filterVal.map(j => {
          if (j === 'ownerId') {
            return getNameByid(v[j]);
          } else if (j === 'type') {
            return typeJson[v[j]];
          } else {
            return v[j];
          }
        })
      );
    };

    return {
      ...toRefs(state),
      ...toRefs(processData),
      emptyImg,
      calculation,
      getNameByid,
      formatExcelData,
      echartsReturn,
      formatterEchartsData,
      initEcharts,
      formatDate,
      emptyData,
      calculator,
      handleClickEcharts,
      unqualifiedFreqChartOption,
      colWidth,
      handleDatePicker,
      initData,
      changeInspectionType,
      renderSampleData,
      cancelRender,
      switchCharts,
      exportToExcel
    };
  }
};
</script>
<style lang="scss" scoped>
ul {
  margin: 0;
  padding: 0;
  text-align: left;
  max-height: 375px;
  overflow-y: auto;
  li {
    list-style: none;
    line-height: 30px;
    display: flex;
    justify-content: space-between;
  }
}
.panel-header-left {
  justify-content: space-between;
}
::v-deep(.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 38.5rem) !important;
    overflow-y: auto;
  }
}
</style>
