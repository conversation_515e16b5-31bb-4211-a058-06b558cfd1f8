<template>
  <!-- 获取数据 -->
  <el-dialog
    v-model="dialogVisiable"
    title="获取数据"
    :close-on-click-modal="false"
    custom-class="DialogFinishData"
    width="950px"
    top="50px"
    @close="handleClose"
  >
    <el-form :model="formSearch" label-width="80px" label-position="left">
      <el-row>
        <el-col v-if="type !== 'process'" :span="9">
          <el-form-item prop="materialNo" label="物料编号：">
            <el-input
              v-model="formSearch.materialNo"
              v-trim
              size="small"
              autocomplete="off"
              placeholder="请输入物料编号"
              clearable
              @keyup.enter="getReportList()"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="type !== 'process'" :span="9" :offset="1">
          <el-form-item prop="batchNo" label="批次号：">
            <el-input
              v-model="formSearch.batchNo"
              v-trim
              size="small"
              autocomplete="off"
              placeholder="请输入批次号"
              clearable
              @keyup.enter="getReportList()"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="type !== 'raw'" :span="9">
          <el-form-item prop="productionOrderNo" label="生产订单：">
            <el-input
              v-model="formSearch.productionOrderNo"
              v-trim
              size="small"
              autocomplete="off"
              placeholder="请输入生产订单"
              clearable
              @keyup.enter="getReportList()"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="type !== 'process'" :span="9" :offset="type === 'finish' ? 1 : 0">
          <el-form-item prop="reelNo" label="盘号：">
            <el-input
              v-model="formSearch.reelNo"
              v-trim
              size="small"
              autocomplete="off"
              placeholder="请输入盘号"
              clearable
              @keyup.enter="getReportList()"
            />
          </el-form-item>
        </el-col>
        <el-col :span="9" :offset="type !== 'finish' ? 1 : 0">
          <el-form-item prop="key" label="模糊搜索：">
            <el-input
              v-model="formSearch.key"
              v-trim
              size="small"
              autocomplete="off"
              placeholder="请输入样品名称/样品、报告编号"
              clearable
              @keyup.enter="getReportList()"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4" :offset="1" class="textRight">
          <el-button type="primary" size="small" style="margin-left: 10px" @click="getReportList()">查询</el-button>
          <el-button size="small" @click="reset">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-table
      ref="tableRef"
      v-loading="dialogLoading"
      :data="tableData"
      size="medium"
      fit
      border
      class="dark-table base-table format-height-table3"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" :selectable="selectable" width="55" />
      <el-table-column label="样品编号" prop="secSampleNum">
        <template #default="{ row }">
          <span>{{ row.secSampleNum || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报告编号" prop="reportNo">
        <template #default="{ row }">
          <span>{{ row.reportNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="type === 'finish'" label="盘号" prop="reelNo" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.reelNo || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="批次" prop="batchNo" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.batchNo || '--' }}</div>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="small" @click="handleClose">取 消</el-button>
        <el-button type="primary" size="small" @click="onSubmit">确 认</el-button>
      </span>
    </template>
    <pagination
      v-show="total > 20"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getReportList"
    />
  </el-dialog>
</template>
<script>
// getCurrentInstance
import { reactive, ref, toRefs, watch } from 'vue';
import { getReportDetail } from '@/api/sdcc';
import Pagination from '@/components/Pagination';
// import { useRoute } from 'vue-router'
export default {
  name: 'DialogFinishData',
  components: { Pagination },
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    selectRow: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    // const route = useRoute()
    const state = reactive({
      dialogLoading: false, // 弹出窗loading
      tableData: [],
      dialogVisiable: false,
      dialogTitle: props.dialogTitle,
      alreadySelected: [],
      tableRef: ref(),
      selectRow: {},
      formSearch: {},
      selectedReport: [],
      type: '',
      total: 0,
      listQuery: {
        limit: 20,
        page: 1
      }
    });
    watch(props, newValue => {
      state.dialogVisiable = newValue.dialogShow;
      if (state.dialogVisiable) {
        state.selectedReport = [];
        state.tableData = [];
        state.total = 0;
        state.listQuery = {
          limit: 20,
          page: 1
        };
        state.selectRow = props.selectRow.data;
        state.type = props.selectRow.type;
        state.formSearch = JSON.parse(JSON.stringify(state.selectRow));
        getReportList();
      }
    });
    // 关闭弹出窗
    const handleClose = () => {
      if (state.dialogVisiable) {
        context.emit('closeDialog', { data: [] });
        state.dialogVisiable = false;
      }
    };
    // 提交所选报告
    const onSubmit = () => {
      context.emit('closeDialog', { data: state.selectedReport });
      state.dialogVisiable = false;
    };
    // 获取报告列表
    const getReportList = query => {
      const params = state.formSearch;
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoadingAll = true;
      getReportDetail(params).then(res => {
        state.listLoadingAll = false;
        if (res) {
          state.tableData = res.data.data.list;
          state.total = res.data.data.totalCount;
        }
      });
    };
    // 选中的成员
    const handleSelectionChange = val => {
      state.selectedReport = val;
    };
    const reset = () => {
      state.key = '';
      state.total = 0;
      state.listQuery = {
        limit: 20,
        page: 1
      };
      state.formSearch = {};
      getReportList();
    };
    const handleRowClick = row => {
      if (
        !state.alreadySelected.some(item => {
          return item === row.id;
        })
      ) {
        state.tableRef.toggleRowSelection(row);
      }
    };
    const selectable = row => {
      if (
        state.alreadySelected.some(item => {
          return item === row.id;
        })
      ) {
        return false;
      } else {
        return true;
      }
    };
    return {
      ...toRefs(state),
      onSubmit,
      handleClose,
      getReportList,
      reset,
      selectable,
      handleSelectionChange,
      handleRowClick
    };
  }
};
</script>
<style lang="scss" scoped>
.textRight {
  text-align: right;
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 100%;
}
</style>
<style lang="scss">
.DialogFinishData {
  .el-table__body-wrapper {
    max-height: calc(100vh - 440px);
    overflow-y: auto;
  }
}
</style>
