import request from '@/utils/request';
// 样品进度查询列表
export function getList(data) {
  return request({
    url: '/api-orders/orders/report/findReportCapabilityData',
    method: 'post',
    data
  });
}
// 获取各类型数量
export function getNumber(mateCode) {
  return request({
    url: `/api-orders/orders/samples/sampleAmount/${mateCode}`,
    method: 'get'
  });
}
// 获取流程轨迹
export function getLine(sampleId) {
  return request({
    url: `/api-orders/orders/samples/samplePipeline/${sampleId}`,
    method: 'get'
  });
}

// 获取检测中数据
export function getRowNumber(sampleId) {
  return request({
    url: `/api-orders/orders/samples/experimentSchedule/${sampleId}`,
    method: 'get'
  });
}
// 导出
export function getExport(data) {
  return request({
    url: `/api-orders/orders/report/findAllReportCapabilityData`,
    method: 'post',
    data
  });
}
// 新增分类树节点
export function addTreeNode(data) {
  return request({
    url: '/api-device/device/devicecategory/save',
    method: 'post',
    data
  });
}
// 删除分类树节点
export function deleteTreeNode(id) {
  return request({
    url: `/api-device/device/devicecategory/delete/${id}`,
    method: 'post'
  });
}
// 更新分类列表树节点
export function updateTreeNode(data) {
  return request({
    url: '/api-device/device/devicecategory/update',
    method: 'post',
    data
  });
}
// 添加设备码点
export function addCodePoint(data) {
  return request({
    url: '/api-device/device/devicecodepoint/save',
    method: 'post',
    data
  });
}
// 编辑设备码点
export function updateCodePoint(data) {
  return request({
    url: `/api-device/device/devicecodepoint/update`,
    method: 'post',
    data
  });
}
// 删除设备码点
export function deleteCodePoint(id, deviceId) {
  return request({
    url: `/api-device/device/devicecodepoint/delete/${id}/${deviceId}`,
    method: 'post'
  });
}
// 添加设备计量信息
export function addMeasuring(data) {
  return request({
    url: `/api-device/device/devicemeasurement/save`,
    method: 'post',
    data
  });
}
// 修改设备计量信息状态
export function changeEqStatus(data) {
  return request({
    url: `/api-device/device/devicemeasurement/cancelMeasurement`,
    method: 'post',
    data
  });
}
// 下载计量信息附件
export function downLoadFile(id) {
  return request({
    url: `/api-device/device/devicemeasurement/download?fileId=${id}`,
    method: 'get'
  });
}

// 关联仪器设备-新增弹出框-仪器设备列表
export function getDeviceCapabilityList(data) {
  return request({
    url: '/api-device/device/device/deviceCapabilityList',
    method: 'post',
    data
  });
}
// 关联仪器设备 列表
export function getCapabilityDevice(capabilityId) {
  return request({
    url: `/api-capabilitystd/capabilitydevice/findByCapabilityId/${capabilityId}`,
    method: 'get'
  });
}
// 关联仪器设备-保存或更新关联仪器设备
export function saveOrUpdateDevice(data) {
  return request({
    url: '/api-capabilitystd/capabilitydevice/saveOrUpdate',
    method: 'post',
    data
  });
}
// 删除关联仪器设备
export function deleteCapabilityDevice(id) {
  return request({
    url: `/api-capabilitystd/capabilitydevice/delete/${id}`,
    method: 'delete'
  });
}
// 展示分析-设备使用记录列表- 查询所有仪器设备使用记录
export function getDeviceRecordList(data) {
  return request({
    url: '/api-orders/orders/experiment/deviceusage/deviceRecordList',
    method: 'post',
    data
  });
}
// 展示分析-设备使用记录列表- 分页查询所有仪器设备使用记录
export function getDeviceRecordPage(data) {
  return request({
    url: '/api-orders/orders/experiment/deviceusage/deviceRecordPage',
    method: 'post',
    data
  });
}
// 根据Id查询deviceCategoryId
export function getDeviceCategoryId(id) {
  return request({
    url: `/api-device/device/device/getDeviceCategoryId/${id}`,
    method: 'get'
  });
}
// 项目树分类拖动排序
export function updateOrderTree(data) {
  return request({
    url: '/api-device/device/devicecategory/updateOrder',
    method: 'post',
    data
  });
}
// 展示分析-所有样品领用记录列表
export function sampleRecordAllList(data) {
  return request({
    url: '/api-orders/orders/experiment/samplereceive/receiverecordlist',
    method: 'post',
    data
  });
}
// 展示分析-所有样品领用记录列表
export function sampleRecordList(data) {
  return request({
    url: '/api-orders/orders/experiment/samplereceive/receiverecordpage',
    method: 'post',
    data
  });
}
