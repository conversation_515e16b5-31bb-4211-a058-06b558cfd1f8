<template>
  <!--检测报告列表-->
  <ListLayout>
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="param">
          <div style="width: 42vw">
            <CombinationQuery
              :field-list="searchFieldList"
              :field-tip="fieldTips"
              @get-single-text="getSingleText"
              @get-param-list="getParamList"
              @reset-search="reset"
            />
          </div>
        </el-form-item>
        <el-form-item style="margin-left: 0px">
          <el-button class="searchBtn" size="large" type="text" @click="search" @keyup.prevent @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
          /></el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('DataReuseBtn')"
        :loading="listLoading"
        class="radio-con-btn"
        type="primary"
        plain
        size="large"
        :disabled="selectSampleList.length == 0"
        @click="handleDataReuse()"
        @keyup.prevent
        @keydown.enter.prevent
        >数据复用</el-button
      >
      <el-button
        v-if="showMoreApprove && getPermissionBtn('reportBatchAudit')"
        :disabled="disabledMoreCheck"
        icon="el-icon-document-copy"
        class="radio-con-btn"
        type="primary"
        size="large"
        @click="moreCheck"
        @keyup.prevent
        @keydown.enter.prevent
        >批量审批</el-button
      >
      <el-button
        v-if="getPermissionBtn('reportBatchDownLoad')"
        :disabled="selectSampleList.length == 0"
        class="radio-con-btn"
        :loading="listLoading"
        type="primary"
        size="large"
        plain
        @click="handleDown()"
        @keyup.prevent
        @keydown.enter.prevent
        >批量下载</el-button
      >
      <el-button
        v-if="getPermissionBtn('reportAdd')"
        icon="el-icon-plus"
        :loading="listLoading"
        class="radio-con-btn"
        type="primary"
        size="large"
        @click="addReport"
        @keyup.prevent
        @keydown.enter.prevent
        >新增报告</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" :model="searchForm" label-width="110px" label-position="right">
            <!-- <el-form-item label="物资分类：">
              <el-radio-group v-model="searchForm.prodType" size="small" max="1">
                <el-radio-button style="margin-right: 4px" @change="changeProdType('all')">不限</el-radio-button>
                <el-radio-button
                  v-for="item in types"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                  @change="changeProdType(item)"
                />
              </el-radio-group>
            </el-form-item> -->
            <el-form-item label="编制日期：">
              <el-date-picker
                v-model="searchForm.bzDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="changeBZTime"
              />
            </el-form-item>
            <el-form-item label="更新日期：">
              <el-date-picker
                v-model="searchForm.gxDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="changeUpdateTime"
              />
            </el-form-item>
            <el-form-item label="报告审批人：">
              <el-select
                v-model="searchForm.examineByUserId"
                class="owner-select"
                placeholder="请选择审批人"
                size="small"
                clearable
                filterable
                :filter-method="filterUserList"
                @focus="filterUserList(null)"
                @change="
                  val => {
                    return changeUser(val, 'examineByUserId');
                  }
                "
              >
                <el-option v-for="item in userOptions" :key="item.id" :label="item.nickname" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="报告编制人：">
              <el-select
                v-model="searchForm.reportFormationByUserId"
                class="owner-select"
                placeholder="请选择报告编制人"
                size="small"
                clearable
                filterable
                @change="
                  val => {
                    return changeUser(val, 'reportFormationByUserId');
                  }
                "
              >
                <el-option v-for="item in reportUser" :key="item.id" :label="item.nickname" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="检测结果：" class="seal-staff-name">
              <el-select
                v-model="formInline.reportType"
                class="owner-select"
                placeholder="请选择"
                clearable
                size="small"
              >
                <el-option label="合格" value="0" />
                <el-option label="不合格" value="1" />
                <el-option label="不判定" value="2" />
                <el-option label="空" value="3" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="16" class="flex gap-6">
          <el-radio-group v-model="radioData" size="small" @change="changeRadio">
            <el-radio-button label="全部" />
            <el-radio-button label="待提交" />
            <el-radio-button label="待审核" />
            <el-radio-button label="待签字" />
            <el-radio-button label="已完成" />
          </el-radio-group>
          <el-checkbox v-model="giveMeChecked" @change="giveMeChange">仅看我的</el-checkbox>
          <el-select
            v-if="getPermissionBtn('inspectionType')"
            v-model="formInline.type"
            placeholder="请选择检验类型"
            size="small"
            clearable
            filterable
            @change="getList()"
          >
            <el-option v-for="(val, key) in dictionary['JYLX'].enable" :key="key" :label="val" :value="Number(key)" />
          </el-select>
          <el-select
            v-if="getPermissionBtn('registerDepartment')"
            v-model="formInline.registerDepartment"
            class="owner-select"
            filterable
            placeholder="请选择送检部门"
            size="small"
            clearable
            @change="getList()"
          >
            <el-option v-for="(val, key) in dictionary['JHDW']?.enable" :key="key" :label="val" :value="val" />
          </el-select>
        </el-col>
        <el-col :span="8" style="text-align: right">
          <TableColumnView binding-menu="test-report" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table allocation-table base-table format-height-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      @header-dragend="drageHeader"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column type="selection" align="center" :width="colWidth.checkbox" fixed="left" />
      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <TableColumnContentRenderer :column="item" :row="row" format="yyyy-MM-dd">
              <template #link>
                <div>
                  <span v-if="item.fieldKey === 'secSampleNum' && row.isUrgent" class="urgent">急</span>
                  <span
                    v-if="row[item.fieldKey]"
                    v-copy="row[item.fieldKey]"
                    class="nowrap blue-color"
                    @click.stop="handleJumpDetail(item.fieldKey, row)"
                    >{{ row[item.fieldKey] || '--' }}</span
                  >
                </div>
              </template>
              <template #status>
                <div v-if="item.fieldKey === 'result'">
                  {{ row.result === 0 ? '合格' : row.result === 1 ? '不合格' : row.result === 2 ? '不判定' : '--' }}
                </div>
                <el-tag v-else size="small" effect="dark" :type="filterExamineStatus(row[item.fieldKey])[0]">{{
                  filterExamineStatus(row[item.fieldKey])[1]
                }}</el-tag>
              </template>
              <template #custom>
                <span v-if="item.fieldKey === 'hth'">{{ row.salesOrderNo || row.purchaseNo || '--' }}</span>
                <span v-else-if="item.fieldKey === 'mateType'">{{ filterMaterialCode(row.mateType) || '--' }}</span>
                <span v-else-if="item.fieldKey === 'type'">{{ dictionary['JYLX'].all[row[item.fieldKey]] }}</span>
                <!-- 检验对象 -->
                <div v-else-if="item.fieldKey === 'inputWarehouseNo,productionOrderNo'" class="nowrap">
                  {{ [1, 8].includes(row.type) ? row.inputWarehouseNo || '--' : row.productionOrderNo || '--' }}
                </div>
                <!--对象位置 -->
                <template v-else-if="item.fieldKey === 'productionProcedure,productionStation,wareHouseName'">
                  <div v-if="[1, 8].includes(row.type)" class="nowrap">
                    {{ row.wareHouseName }}
                  </div>
                  <div v-else class="nowrap">{{ row.productionProcedure }}{{ row.productionStation }}</div>
                </template>
                <!--对象名称 -->
                <template v-else-if="item.fieldKey === 'customerName,supplierName'">
                  <div v-if="[1, 8].includes(row.type)" class="nowrap">{{ row.supplierName }}</div>
                  <div v-else class="nowrap">{{ row.customerName }}</div>
                </template>
                <template v-else-if="item.fieldKey === 'sampleNum'">
                  <div v-if="row.sampleNum" class="nowrap">
                    {{ row.sampleNum }}{{ filterSampleUnitToName(row.sampleUnit) || row.unitName }}
                  </div>
                  <div v-else class="nowrap">--</div>
                </template>
              </template>
            </TableColumnContentRenderer>
          </template>
        </el-table-column>
      </template>

      <el-table-column
        label="操作"
        :width="colWidth.operationMultiple"
        prop="caozuo"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="scope">
          <!-- <span class="blue-color" @click="handlePreview(scope.row)">预览</span> -->
          <div v-if="formInline.appoint === '1' && formInline.examineStatus === '1'">
            <span
              v-if="getPermissionBtn('reportEdit')"
              class="blue-color margin-right-10"
              @click.stop="editDetail(scope.row)"
              >编辑</span
            >
            <span
              v-if="getPermissionBtn('previewTestReportBtn')"
              class="blue-color margin-right-10"
              @click.stop="handlePreview(scope.row)"
              >预览</span
            >
            <el-dropdown :hide-on-click="false">
              <span class="el-dropdown-link blue-color" style="margin-left: 15px"><span class="el-icon-more" /></span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-if="getPermissionBtn('reportSumbit')" @click.stop="submitReport(scope.row)">
                    <span class="blue-color">提交</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="getPermissionBtn('downDocx')"
                    class="blue-color"
                    @click.stop="downloadReport(scope.row)"
                    >下载</el-dropdown-item
                  >
                  <el-dropdown-item
                    v-if="getPermissionBtn('progressTestReportBtn')"
                    @click.stop="progressReport(scope.row)"
                  >
                    <span class="blue-color">进度</span>
                  </el-dropdown-item>
                  <el-dropdown-item v-if="getPermissionBtn('reportDelete')" @click.stop="removeReport(scope.row)">
                    <span class="blue-color">删除</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div
            v-else-if="
              formInline.appoint === '1' &&
              (formInline.examineStatus === '2' ||
                formInline.examineStatus === '3' ||
                formInline.examineStatus === '4' ||
                formInline.examineStatus === '5')
            "
          >
            <span
              v-if="getPermissionBtn('reportAudit')"
              class="blue-color margin-right-10"
              @click.stop="approvalDetail(scope.row)"
              >审批</span
            >
            <span
              v-if="getPermissionBtn('previewTestReportBtn')"
              class="blue-color margin-right-10"
              @click.stop="handlePreview(scope.row)"
              >预览</span
            >
            <el-dropdown trigger="hover">
              <span class="el-dropdown-link blue-color" style="margin-left: 15px"><span class="el-icon-more" /></span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-if="getPermissionBtn('downDocx')"
                    class="blue-color"
                    @click.stop="downloadReport(scope.row)"
                    >下载</el-dropdown-item
                  >
                  <el-dropdown-item
                    v-if="getPermissionBtn('progressTestReportBtn')"
                    @click.stop="progressReport(scope.row)"
                  >
                    <span class="blue-color">进度</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div v-else-if="formInline.examineStatus === '6'">
            <span
              v-if="getPermissionBtn('SyncMESSBtn') && scope.row.isSync == 0"
              class="blue-color margin-right-10"
              @click.stop="handleSync(scope.row)"
              >同步</span
            >
            <span
              v-if="getPermissionBtn('previewTestReportBtn')"
              class="blue-color margin-right-10"
              @click.stop="handlePreview(scope.row)"
              >预览</span
            >
            <span
              v-if="getPermissionBtn('downDocx')"
              class="blue-color margin-right-10"
              @click.stop="downloadReport(scope.row)"
              >下载</span
            >
            <el-dropdown trigger="hover">
              <span class="el-dropdown-link blue-color" style="margin-left: 15px"><span class="el-icon-more" /></span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-if="getPermissionBtn('progressTestReportBtn')"
                    class="blue-color"
                    @click.stop="progressReport(scope.row)"
                    >进度</el-dropdown-item
                  >
                  <el-dropdown-item
                    v-if="getPermissionBtn('downDCC')"
                    class="blue-color"
                    @click.stop="handleXMLFormat(scope.row)"
                    >DCC</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div v-else>
            <span
              v-if="getPermissionBtn('previewTestReportBtn')"
              class="blue-color margin-right-10"
              @click.stop="handlePreview(scope.row)"
              >预览</span
            >
            <span
              v-if="getPermissionBtn('downDocx')"
              class="blue-color margin-right-10"
              @click.stop="downloadReport(scope.row)"
              >下载</span
            >
            <span v-if="!haveMoreOperation(scope.row)" @click.stop="progressReport(scope.row)"
              ><span class="blue-color">进度</span></span
            >
            <el-dropdown v-else trigger="hover" @visible-change="val => handleVisible(val, scope.row)">
              <span class="el-dropdown-link blue-color" style="margin-left: 15px"><span class="el-icon-more" /></span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-if="getPermissionBtn('progressTestReportBtn')"
                    @click.stop="progressReport(scope.row)"
                  >
                    <span class="blue-color">进度</span>
                  </el-dropdown-item>
                  <el-dropdown-item v-if="rowOperation.edit" class="blue-color" @click.stop="editDetail(scope.row)"
                    >编辑</el-dropdown-item
                  >
                  <!-- 审批不同权限的用户拥有不同的操作权限，后续经过调研再做审批 -->
                  <el-dropdown-item
                    v-if="rowOperation.approval"
                    class="blue-color"
                    @click.stop="approvalDetail(scope.row)"
                    >审批</el-dropdown-item
                  >
                  <el-dropdown-item v-if="rowOperation.submit" @click.stop="submitReport(scope.row)"
                    ><span class="blue-color">提交</span></el-dropdown-item
                  >
                  <el-dropdown-item v-if="rowOperation.remove" @click.stop="removeReport(scope.row)"
                    ><span class="blue-color">删除</span></el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />

    <template #other>
      <!-- 新增报告 -->
      <add-report :show="showAddReport" @close="closeAddReport" @selectData="selectData" />
      <!-- 审批 -->
      <approval
        :show="showReportApproval"
        :detail="showApprovalDetail"
        :data="selectApprovalData"
        :approval-list="approvalList"
        @close="closeReportApproval"
      />
      <!-- 批量审批弹出框 -->
      <batch-approval
        :show="batchApprovalDialog"
        :data="batchApprovalData"
        :approval-list="approvalList"
        @close="closeBatchApprovalDialog"
      />
      <!-- 提交弹出框 -->
      <el-dialog
        v-model="submitReportDialog"
        title="选择审核人"
        width="480px"
        :close-on-click-modal="false"
        @close="submitReportDialog = false"
      >
        <el-form ref="submitReportRef" :model="submitReportFrom" label-position="right" size="small" label-width="70px">
          <el-form-item label="审核人：" prop="assigneeList">
            <el-select
              v-model="submitReportFrom.assigneeList"
              class="owner-select"
              placeholder="请选择审核人"
              clearable
              multiple
              filterable
              size="small"
              style="width: 100%"
            >
              <el-option
                v-for="item in userOptions"
                :key="item.id"
                :label="item.nickname"
                :value="item.id"
                :disabled="item.status === -2"
              />
            </el-select>
          </el-form-item>
          <el-form-item label=" " prop="isDefaultUser">
            <el-checkbox
              v-model="submitReportFrom.isDefaultUser"
              :true-label="1"
              :false-label="0"
              label="下次自动选择当前选择人"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="submitReportDialog = false">取 消</el-button>
            <el-button
              type="primary"
              :loading="approvalSuccessLoading"
              @click="submitReportSuccess"
              @keyup.prevent
              @keydown.enter.prevent
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
      <!-- 预览功能-word转html模板 -->
      <!-- <word-to-html :drawer="isShowWordDrawer" :data="showWordDrawerData" @close="closeDrawer" /> -->
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance, inject, onMounted } from 'vue';
import router from '@/router/index.js';
import { useRoute } from 'vue-router';
import Pagination from '@/components/Pagination';
import {
  getReportList,
  getSampleInfoByReportId,
  deleteReportList,
  processSubmit,
  processExecute,
  getProcessHistory,
  exportWord,
  getProcessList,
  batchExportReportWord,
  experimentDataReturn,
  listReportEdit
} from '@/api/testReport';
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus';
import { getNameByid, getNamesByid, getPermissionBtn } from '@/utils/common';
import { formatDate, formatDateTime } from '@/utils/formatTime';
import { mapGetters, useStore } from 'vuex';
import { drageHeader } from '@/utils/formatTable';
import AddReport from './add-report.vue';
import Approval from './approval.vue';
import BatchApproval from './batchApproval.vue';
import ListLayout from '@/components/ListLayout';
import { filterMaterialCode, filterSampleUnitToName } from '@/utils/formatJson';
import { setCurrentReportInfo, getLoginInfo } from '@/utils/auth';
import { toDCCXML, downloadByBlob } from '@/utils/toDCCXML';
import _ from 'lodash';
import { addByTemp } from '@/api/messageAgent';
import { colWidth } from '@/data/tableStyle';
import { getInspectionList } from '@/api/inspection-application';
import { getTaskRegistrationList } from '@/api/task-registration';
import { getReportUserList, getDictionary } from '@/api/user';
import CombinationQuery from '@/components/CombinationQuery';
import TableColumnView from '@/components/TableColumnView';
import TableColumnContentRenderer from '@/components/TableColumnView/TableColumnContentRenderer';

export default {
  name: 'TestReport',
  components: {
    Pagination,
    AddReport,
    Approval,
    BatchApproval,
    ListLayout,
    CombinationQuery,
    TableColumnView,
    TableColumnContentRenderer
  },
  setup() {
    const mittBus = inject('$mittBus');
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const route = useRoute();
    const editFrom = ref(null);
    const otherForm = reactive({
      currentAccountId: getLoginInfo().accountId,
      tableRef: ref(),
      reportUser: [],
      searchFieldList: [],
      fieldTips: '',
      auditNameList: [],
      activeName: '0',
      showS: false,
      type: 'info',
      disabledMoreCheck: true,
      giveMeChecked: true,
      dictionary: {
        JYLX: {
          enable: {},
          all: {}
        },
        JHDW: {
          enable: {},
          all: {}
        }
      },
      formInline: {
        param: '',
        tableQueryParamList: [],
        endFormationDateTime: '',
        startFormationDateTime: '',
        startUpdateDateTime: '',
        endUpdateDateTime: '',
        reportFormationByUserId: '',
        mateType: '',
        examineStatus: '1',
        appoint: '1'
        // examineStatus: 1
      },
      searchForm: {
        prodType: '',
        reportFormationByUserId: '',
        bzDateRange: '',
        gxDateRange: ''
      },
      types: store.user.materialList,
      userOptions: [],
      copyUserOptions: [],
      list: [],
      content: '',
      radioData: '待提交',
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        sort: 'DESC'
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ],
      batchApprovalDialog: false,
      submitReportDialog: false,
      selectFrom: {
        ownerId: ''
      },
      submitReportFrom: {
        assigneeList: [],
        businessKey: '',
        isAssent: 1
      },
      showAddReport: false,
      selectSampleList: [],
      showReportApproval: false,
      selectApprovalData: { data: [], ids: {} },
      isShowWordDrawer: false,
      showMoreApprove: false,
      showApprovalDetail: false,
      hasProcessInstanceId: false,
      exportwordUrl: '',
      batchApprovalData: [],
      submitReportData: null,
      approvalSuccessLoading: false,
      showWordDrawerData: {},
      approvalList: [],
      rowOperation: {
        edit: false,
        approval: false,
        submit: false,
        remove: false
      },
      tableColumns: []
    });
    const getReportEditUser = async () => {
      otherForm.listLoading = true;
      const { data } = await listReportEdit({ params: '' }).finally(() => {
        otherForm.listLoading = false;
      });
      if (data) {
        otherForm.reportUser = data.data;
      }
    };
    onMounted(() => {
      getReportEditUser();
    });
    // #region 设置搜索条件

    // 查询
    function onSubmit() {
      // console.log(otherForm.formInline)
      // console.log(otherForm.searchForm)
      getList();
    }

    // 重置
    function reset() {
      otherForm.giveMeChecked = true;
      editFrom.value.resetFields();
      otherForm.formInline = {
        param: '',
        endFormationDateTime: '',
        tableQueryParamList: [],
        startFormationDateTime: '',
        startUpdateDateTime: '',
        endUpdateDateTime: '',
        reportFormationByUserId: '',
        mateType: '',
        examineStatus: '1',
        appoint: '1'
        // examineStatus: 1
      };
      otherForm.radioData = '待提交';
      otherForm.searchForm = {
        prodType: '',
        reportFormationByUserId: '',
        bzDateRange: '',
        gxDateRange: ''
      };
      otherForm.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        sort: 'DESC'
      };
      getList();
    }
    // 高级搜索
    const search = () => {
      otherForm.showS = !otherForm.showS;
      if (otherForm.activeName === '0') {
        otherForm.activeName = '1';
      } else {
        otherForm.activeName = '0';
      }
    };
    const sortChange = data => {
      const { prop, order } = data;
      console.log(prop);
      console.log(order);
    };
    /** 下载附件 */
    const handleDown = async () => {
      const params = [];
      otherForm.selectSampleList.forEach(item => {
        params.push({
          mateType: item.mateType,
          reportId: item.id,
          reportNo: item.reportNo,
          sampleId: item.sampleId
        });
      });
      otherForm.listLoading = true;
      const { data } = await batchExportReportWord({ list: params }).finally(() => {
        otherForm.listLoading = false;
      });
      if (data) {
        ElNotification({
          title: '下载报告准备中',
          dangerouslyUseHTMLString: true,
          message: '你可以进行其他操作,下载工作将会在后台进行,下载完成后,将自动在浏览器中进行下载(5s后自动关闭提示)',
          type: 'success',
          duration: 5000
        });
      }
    };
    const getDictionaryList = () => {
      Object.keys(otherForm.dictionary).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          otherForm.dictionary[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              otherForm.dictionary[item].enable[optionItem.code] = optionItem.name;
            }
            otherForm.dictionary[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    getDictionaryList();
    // 是否禁用checkbox
    // const selectable = (row, index) => {
    //   if (row.examineByUserId !== otherForm.currentAccountId && row.examineStatus !== 1) {
    //     return false;
    //   } else {
    //     return true;
    //   }
    // };
    // 选择checkbox
    const handleSelectionChange = val => {
      if (val.length > 0) {
        otherForm.disabledMoreCheck = false;
      } else {
        otherForm.disabledMoreCheck = true;
      }
      otherForm.selectSampleList = val;
    };
    const handleRowClick = row => {
      otherForm.tableRef.toggleRowSelection(
        row,
        !otherForm.selectSampleList.some(item => {
          return row.id === item.id;
        })
      );
    };
    // 仅看我的-change
    const giveMeChange = check => {
      otherForm.formInline.appoint = check ? '1' : '0';
      getList();
    };

    // 切换tab
    const changeRadio = value => {
      const param = {
        全部: '',
        待提交: 1,
        待审核: 2,
        待签字: 3,
        待盖章: 4,
        待发送: 5,
        已完成: 6
      };
      if (value === '待提交' || value === '已完成' || value === '全部') {
        otherForm.showMoreApprove = false;
      } else {
        otherForm.showMoreApprove = true;
      }
      // otherForm.formInline.examineStage = param[value]
      otherForm.formInline.examineStatus = param[value].toString();
      getList();
    };
    // 高级搜索-物资分类-change
    const changeProdType = type => {
      // console.log(type)
      if (type === 'all') {
        otherForm.formInline.mateType = '';
      } else {
        otherForm.formInline.mateType = type.code;
      }
    };
    // 高级搜索-更新日期-change
    const changeUpdateTime = date => {
      otherForm.formInline.startUpdateDateTime = date ? formatDate(date[0]) : '';
      otherForm.formInline.endUpdateDateTime = date ? formatDate(date[1]) : '';
    };
    // 高级搜索-编制日期-change
    const changeBZTime = date => {
      otherForm.formInline.startFormationDateTime = date ? formatDate(date[0]) : '';
      otherForm.formInline.endFormationDateTime = date ? formatDate(date[1]) : '';
    };
    // 高级搜索-实验负责人-change
    const changeUser = (id, fieldName) => {
      // console.log(id)
      otherForm.formInline[fieldName] = id;
    };
    // 关闭预览抽屉
    const closeDrawer = value => {
      // console.log(value)
      otherForm.isShowWordDrawer = value;
    };
    // 过滤试验员
    const filterUserList = val => {
      if (val) {
        const list = [];
        otherForm.copyUserOptions.forEach(user => {
          const item = _.filter(user.nickname, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        otherForm.userOptions = list;
      } else {
        otherForm.userOptions = otherForm.copyUserOptions;
      }
    };
    // 过滤提交按钮是否展示
    const filterSubmitReport = row => {
      if (row.examineStatus === 1 && row.examineStage === 1) {
        return true;
      } else if (
        row.examineStatus === 1 &&
        row.examineStage > 1 &&
        row.examineByUserId === otherForm.currentAccountId
      ) {
        return true;
      }
      return false;
    };

    // #endregion

    // #region button-group

    // 批量审批
    const moreCheck = async () => {
      // console.log('more-Check')
      if (otherForm.selectSampleList.length === 0) {
        ElMessage.warning('请选择要审批的检测报告！');
        return false;
      }
      if (
        otherForm.selectSampleList.some(item => {
          return item.examineByUserId !== otherForm.currentAccountId && item.examineStatus !== 1;
        })
      ) {
        ElMessage.warning('包含非当前审批人!');
        return false;
      }
      otherForm.approvalList = await proxy.getProcessLists();
      otherForm.batchApprovalData = otherForm.selectSampleList;
      otherForm.batchApprovalDialog = true;
    };
    // 批量审批弹出框-确定
    const batchApprovalSuccess = () => {
      // console.log(otherForm.selectFrom)
      getList();
      otherForm.batchApprovalDialog = false;
    };
    // 提交弹出框-确定
    const submitReportSuccess = () => {
      // console.log(otherForm.submitReportFrom)
      if (!otherForm.submitReportFrom.assigneeList.length) {
        ElMessage.error('请选择审核人');
        return false;
      }
      otherForm.approvalSuccessLoading = true;
      if (otherForm.hasProcessInstanceId) {
        otherForm.submitReportFrom.processInstanceId = otherForm.hasProcessInstanceId;
        processExecute(otherForm.submitReportFrom).then(res => {
          if (res !== false) {
            // console.log(res.data)
            otherForm.submitReportDialog = false;
            getList();
            addMsg();
            ElMessage.success('提交成功！');
            otherForm.approvalSuccessLoading = false;
          } else {
            otherForm.approvalSuccessLoading = false;
          }
        });
      } else {
        processSubmit(otherForm.submitReportFrom).then(res => {
          if (res !== false) {
            // console.log(res.data)
            otherForm.submitReportDialog = false;
            getList();
            addMsg();
            ElMessage.success('提交成功！');
            otherForm.approvalSuccessLoading = false;
          } else {
            otherForm.approvalSuccessLoading = false;
          }
        });
      }
    };
    // 添加消息待办
    const addMsg = () => {
      // 添加消息待办
      const params = {
        eventCode: 'M012',
        receiverType: '1',
        senderName: getNameByid(otherForm.currentAccountId),
        receiverIds: otherForm.submitReportFrom.assigneeList.toString(),
        receiverNames: getNamesByid(otherForm.submitReportFrom.assigneeList.toString()).toString(),
        c_ids: otherForm.submitReportData.reportNo,
        c_b_samplesIdArray: otherForm.submitReportData.sampleId,
        c_b_sampleNoArray: otherForm.submitReportData.secSampleNum,
        c_b_reportNoArray: otherForm.submitReportData.reportNo
      };
      addByTemp(params).then(res => {
        // if (res !== false) {
        // }
      });
    };
    // 新增报告
    const addReport = () => {
      otherForm.showAddReport = true;
    };
    // 关闭-新增报告
    const closeAddReport = value => {
      otherForm.showAddReport = value;
    };
    // 新增报告-获取选择的样品
    const selectData = data => {
      getList();
    };

    // #endregion button-group

    // #region 表格设置

    // 预览点击
    const handlePreview = row => {
      const newRouter = row.pdfSealUrl
        ? router.resolve({ path: `/preview-report-pdf/${row.id}/${row.mateType}/${row.sampleId}/${row.reportNo}` })
        : router.resolve({ path: `/preview-report/${row.id}/${row.mateType}/${row.sampleId}/${row.reportNo}` });

      window.open(newRouter.href, '_blank');
    };
    /** 数据复用 */
    const handleDataReuse = () => {};
    // 点击编辑
    const editDetail = row => {
      const params = {
        reportId: row.id,
        sampleId: row.sampleId,
        sampleName: row.sampleName,
        reportNo: row.reportNo,
        mateType: row.mateType,
        reportStage: row.reportStage,
        processInstanceId: row.processInstanceId,
        pdfSealUrl: row.pdfSealUrl
      };
      setCurrentReportInfo(params);
      router.push({
        path: '/edit-report',
        query: {
          reportId: row.id
        }
      });
    };
    // 点击提交
    const submitReport = row => {
      otherForm.submitReportData = row;
      otherForm.hasProcessInstanceId = row.processInstanceId;
      otherForm.submitReportDialog = true;
      otherForm.submitReportFrom.assigneeList = row?.defaultReviewUserIdList || [];
      console.log(row?.defaultReviewUserIdList);
      if (row?.defaultReviewUserIdList?.length) {
        otherForm.submitReportFrom.isDefaultUser = 1;
      }
      otherForm.submitReportFrom.businessKey = row.id;
      otherForm.submitReportFrom.tenantType = store.user.tenantInfo.type;
    };
    // 点击进度
    const progressReport = row => {
      if (!row.processInstanceId) {
        ElMessage.warning('暂无进度！');
        return false;
      }
      otherForm.selectApprovalData.ids = {
        businessKey: row.id,
        processInstanceId: row.processInstanceId,
        currentAccountId: otherForm.currentAccountId
      };
      getProcessHistory(row.processInstanceId).then(res => {
        if (res !== false) {
          // console.log(res.data.data)
          otherForm.showApprovalDetail = true;
          otherForm.selectApprovalData.data = res.data.data;
          otherForm.showReportApproval = true;
        }
      });
    };
    const handleXMLFormat = row => {
      getSampleInfoByReportId(row.id).then(res => {
        console.log('handleXMLFormat res', res);
        downloadByBlob(`report_${formatDateTime(new Date(), 'yyyyMMddhhmmss')}.xml`, toDCCXML(res.data.data));
      });
    };
    // 点击审批
    const approvalDetail = async row => {
      // 'e4b65784-1607-11ec-ad73-0242ac12000b'
      otherForm.selectApprovalData.currentData = row;
      otherForm.selectApprovalData.ids = {
        businessKey: row.id,
        processInstanceId: row.processInstanceId,
        currentAccountId: otherForm.currentAccountId
      };
      otherForm.approvalList = await proxy.getProcessLists();
      getProcessHistory(row.processInstanceId).then(res => {
        if (res !== false) {
          // console.log(res.data.data)
          otherForm.selectApprovalData.data = res.data.data;
          otherForm.showReportApproval = true;
          otherForm.showApprovalDetail = false;
        }
      });
    };
    // 下载报告
    const downloadReport = row => {
      otherForm.listLoading = true;
      exportWord(row.id, row.mateType, row.sampleId).then(res => {
        otherForm.listLoading = false;
        if (res !== false) {
          // console.log(res)
          const reader = new FileReader();
          reader.addEventListener('loadend', () => {
            try {
              // console.log(JSON.parse(reader.result))
              const resdata = JSON.parse(reader.result);
              if (resdata.code === 400) {
                ElMessage({
                  message: resdata.message,
                  type: 'error',
                  duration: 3000
                });
              }
            } catch (error) {
              // const blob = new Blob([res.data], { type: 'application/octet-stream' })
              // const filename = res.headers['content-disposition']
              const blob = res.data;
              var suffix = row.pdfSealUrl ? '.pdf' : '.doc';
              var fileName = row.reportNo + '-' + row.sampleName + suffix; // res.headers.filename
              var downloadElement = document.createElement('a');
              var href = window.URL.createObjectURL(blob);
              downloadElement.style.display = 'none';
              downloadElement.href = href;
              downloadElement.download = decodeURI(fileName);
              document.body.appendChild(downloadElement);
              downloadElement.click();
              document.body.removeChild(downloadElement);
              window.URL.revokeObjectURL(href);
            }
          });
          reader.readAsText(res.data, 'utf-8');
        }
      });
    };

    // 删除报告
    const removeReport = row => {
      ElMessageBox({
        title: '提示',
        message: '是否确认删除此检测报告?',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          deleteReportList([row.id]).then(res => {
            if (res !== false) {
              ElMessage.success('删除成功');
              getList();
            }
          });
        })
        .catch(() => {});
    };

    // 关闭审批抽屉
    const closeReportApproval = value => {
      otherForm.showReportApproval = value;
    };
    // 关闭批量审批
    const closeBatchApprovalDialog = value => {
      otherForm.batchApprovalDialog = value;
      otherForm.disabledMoreCheck = false;
    };

    // 过滤操作
    const filterOperation = row => {
      var list = [];
      var flag = 0;
      // console.log(otherForm.currentAccountId)
      // 编辑 0
      if (
        row.examineStatus === 1 &&
        getPermissionBtn('reportEdit') &&
        row.reportFormationByUserId === otherForm.currentAccountId
      ) {
        list.push({ show: true, num: 1 });
        flag += 1;
      } else {
        list.push({ show: false });
      }
      // 审批 1
      if (
        row.examineStatus !== 1 &&
        row.examineStatus !== 6 &&
        row.examineByUserId.indexOf(otherForm.currentAccountId) !== '-1' &&
        getPermissionBtn('reportAudit')
      ) {
        list.push({ show: true, num: flag + 1 }); // 审批
        flag += 1;
      } else {
        list.push({ show: false }); // 审批
      }
      // 预览 2
      if (getPermissionBtn('downDocx')) {
        list.push({ show: true, num: flag + 1 });
        flag += 1;
      } else {
        list.push({ show: false });
      }
      // 提交 3
      if (
        row.examineStatus === 1 &&
        getPermissionBtn('reportSumbit') &&
        row.reportFormationByUserId === otherForm.currentAccountId
      ) {
        list.push({ show: true, num: flag + 1 });
        flag += 1;
      } else {
        list.push({ show: false });
      }
      // 下载 4
      if (getPermissionBtn('downDocx')) {
        list.push({ show: true, num: flag + 1 });
        flag += 1;
      } else {
        list.push({ show: false });
      }
      // 进度 5
      if (getPermissionBtn('reportAudit')) {
        list.push({ show: true, num: flag + 1 }); // 进度
        flag += 1;
      } else {
        list.push({ show: false }); // 进度
      }
      // 删除 6
      if (
        row.examineStatus === 1 &&
        getPermissionBtn('reportDelete') &&
        row.reportFormationByUserId === otherForm.currentAccountId
      ) {
        list.push({ show: true, num: flag + 1 });
        flag += 1;
      } else {
        list.push({ show: false });
      }
      return { list: list, flag: flag };
    };

    const haveMoreOperation = row => {
      if (row.examineStatus === 1) {
        return row.reportFormationByUserId === otherForm.currentAccountId;
      } else if (
        row.examineStatus === 2 ||
        row.examineStatus === 3 ||
        row.examineStatus === 4 ||
        row.examineStatus === 5
      ) {
        return row.examineByUserId === otherForm.currentAccountId;
      } else {
        return false;
      }
    };
    /**
     * examineStage 审批阶段 1、报告编制 2、报告审核、3、报告发送 4、报告完成
     * examineStatus 审批状态 1、待提交 2、待审核、3、待签字 4、待盖章 5、待发送 6、已发送
     */
    const handleVisible = (val, row) => {
      otherForm.rowOperation.edit = false;
      otherForm.rowOperation.approval = false;
      otherForm.rowOperation.submit = false;
      otherForm.rowOperation.remove = false;
      // 待提交
      switch (row.examineStatus) {
        case 1:
          if (row.reportFormationByUserId === otherForm.currentAccountId) {
            otherForm.rowOperation.edit = true;
            otherForm.rowOperation.submit = true;
            otherForm.rowOperation.remove = true;
          }
          break;
        case 2:
        case 3:
        case 4:
        case 5:
          if (row.examineByUserId === otherForm.currentAccountId) {
            otherForm.rowOperation.approval = true;
          }
          break;
        default:
          otherForm.rowOperation.edit = false;
          otherForm.rowOperation.approval = false;
          otherForm.rowOperation.submit = false;
          otherForm.rowOperation.remove = false;
          break;
      }
    };

    // 过滤审批阶段颜色
    const filterExamineStage = status => {
      // 审批阶段  1、报告编制2、报告审核、3、报告发送 4、报告完成
      const classMap = {
        1: ['icon-tes-info', '报告编制'],
        2: ['icon-tes-info', '报告审核'],
        3: ['icon-tes-info', '报告发送'],
        4: ['icon-tes-success', '报告完成']
      };
      return classMap[status];
    };
    // 过滤审批状态颜色
    const filterExamineStatus = status => {
      // 审批状态  1、待提交2、待审核、3、待签字4、待盖章5、待发送6、已完成
      const classMap = {
        1: ['info', '待提交'],
        2: ['warning', '待审核'],
        3: ['warning', '待签字'],
        4: ['warning', '待盖章'],
        5: ['warning', '待发送'],
        6: ['success', '已完成']
      };
      return classMap[status];
    };

    const handleJumpDetail = (fieldKey, row) => {
      switch (fieldKey) {
        // 点击样品编号跳转到样品详情页面
        case 'secSampleNum': {
          router.push({
            path: '/report/detail',
            query: {
              orderId: row.orderId,
              sampleId: row.sampleId
            }
          });
          break;
        }

        // 点击报告编号跳转到报告详情页面
        case 'reportNo': {
          router.push({
            path: '/detail-report',
            query: {
              reportId: row.id,
              sampleId: row.sampleId,
              reportStage: 6
            }
          });
          break;
        }

        // 查看申请详情
        case 'presentationCode': {
          if (row.type == 10 || row.type == 11 || row.type == 12) {
            getTaskRegistrationList({
              condition: `${row.presentationCode}`
            }).then(res => {
              if (res && res.status === 200) {
                if (res.data.data.list.length > 0) {
                  router.push({
                    name: 'TestReportRegistration',
                    query: { id: res.data.data.list.find(item => item.entrustNo === row.presentationCode)?.id, flag: 1 }
                  });
                }
              }
            });
          } else {
            getInspectionList({ param: `${row.presentationCode}` }).then(res => {
              if (res && res.status === 200) {
                if (res.data.data.list.length > 0) {
                  router.push({
                    name: 'TestReportApplication',
                    query: { id: res.data.data.list[0].id, flag: 1 }
                  });
                }
              }
            });
          }
          break;
        }
      }
    };

    // // 点击样品编号跳转到样品详情页面
    // const handleSampleOrdersDetail = row => {
    //   router.push({
    //     path: '/report/detail',
    //     query: {
    //       orderId: row.orderId,
    //       sampleId: row.sampleId
    //     }
    //   })
    // }
    // // 点击报告编号跳转到报告详情页面
    // const handleAllReportDetail = row => {
    //   // console.log(row)
    //   router.push({
    //     path: '/detail-report',
    //     query: {
    //       reportId: row.id,
    //       sampleId: row.sampleId,
    //       reportStage: 6
    //     }
    //   })
    // }

    // // 查看申请详情
    // const jumpApplicationDetail = row => {
    //   if (
    //     store.common.bussinessCodeList
    //       .filter(item => item.name.toString().includes('委托'))
    //       .findIndex(item =>
    //         row.presentationCode
    //           .toString()
    //           .startsWith(item.rule.toString().substring(0, 2))
    //       ) !== -1
    //   ) {
    //     getTaskRegistrationList({
    //       condition: `${row.presentationCode}`
    //     }).then(res => {
    //       if (res && res.status === 200) {
    //         if (res.data.data.list.length > 0) {
    //           router.push({
    //             name: 'TestReportRegistration',
    //             query: { id: res.data.data.list.find(item => item.entrustNo === row.presentationCode)?.id, flag: 1 }
    //           })
    //         }
    //       }
    //     })
    //   } else {
    //     getInspectionList({ param: `${row.presentationCode}` }).then(
    //       res => {
    //         if (res && res.status === 200) {
    //           if (res.data.data.list.length > 0) {
    //             router.push({
    //               name: 'TestReportApplication',
    //               query: { id: res.data.data.list[0].id, flag: 1 }
    //             })
    //           }
    //         }
    //       }
    //     )
    //   }
    // }

    // #region 获取列表信息

    // 要是从待办那边跳转过来，需要添加过滤条件，暂时过滤检测报告编号
    const winUrl = window.location.href;
    const urlList = winUrl.split('?');
    const reportNo = urlList[1];
    if (route.query.examineStatus) {
      otherForm.formInline.examineStatus = route.query.examineStatus;
      const classMaps = {
        1: '待提交',
        2: '待审核',
        3: '待签字',
        4: '待盖章',
        5: '待发送'
      };
      otherForm.radioData = classMaps[route.query.examineStatus];
      if (otherForm.formInline.examineStatus == '2' || otherForm.formInline.examineStatus == '3') {
        otherForm.showMoreApprove = true;
      }
    } else if (reportNo) {
      otherForm.formInline.param = reportNo;
      otherForm.formInline.examineStatus = '';
      otherForm.radioData = '全部';
    }

    // 获取检测报告列表
    function getList(data) {
      otherForm.listLoading = true;
      if (data && data !== undefined) {
        otherForm.listQuery.page = data.page;
        otherForm.listQuery.limit = data.limit;
      }
      const param = Object.assign(otherForm.formInline, otherForm.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      // 检测报告列表接口
      getReportList(param).then(res => {
        // console.log(res.data)
        if (res !== false) {
          const { data } = res.data;
          otherForm.list = data.list;
          otherForm.total = data.totalCount;
        }
        otherForm.listLoading = false;
      });
    }

    getList({
      page: otherForm.listQuery.page,
      limit: otherForm.listQuery.limit,
      refreshCustomHeader: true
    });
    // 刷新列表
    mittBus.$on('reloadReportList', msg => {
      getList({
        page: otherForm.listQuery.page,
        limit: otherForm.listQuery.limit,
        refreshCustomHeader: true
      });
    });
    const getSingleText = val => {
      otherForm.formInline.param = val;
      otherForm.formInline.tableQueryParamList = [];
      getList();
    };

    const getParamList = paramList => {
      otherForm.formInline.tableQueryParamList = paramList;
      otherForm.formInline.param = '';
      getList();
    };

    // #endregion

    // #region 立即执行的方法

    // 获取具有检测报告权限的人员列表
    getReportUserList({}).then(res => {
      if (res.data.code === 200) {
        otherForm.userOptions = res.data.data;
        otherForm.copyUserOptions = JSON.parse(JSON.stringify(res.data.data));
      }
    });

    // #endregion

    // 更新表格字段
    const onUpdateColumns = columns => {
      otherForm.tableColumns = columns;
      otherForm.tableKey = otherForm.tableKey + 1;
      columns.forEach(column => {
        if (column.isQuery) {
          const item = {
            fieldKey: column.fieldKey,
            fieldName: column.fieldName,
            isQuery: true
          };
          switch (column.fieldKey) {
            case 'materialGroup':
              otherForm.searchFieldList.push(item, {
                fieldKey: 'materialNo',
                fieldName: '物料编号',
                isQuery: true
              });
              break;

            case 'inputWarehouseNo,productionOrderNo':
              otherForm.searchFieldList.push(
                {
                  fieldKey: 'productionOrderNo',
                  fieldName: '生产单号',
                  isQuery: true
                },
                {
                  fieldKey: 'inputWarehouseNo',
                  fieldName: '入库单号',
                  isQuery: true
                }
              );
              break;

            case 'productionProcedure,productionStation,wareHouseName':
              otherForm.searchFieldList.push(
                {
                  fieldKey: 'productionProcedure',
                  fieldName: '生产工序',
                  isQuery: true
                },
                {
                  fieldKey: 'productionStation',
                  fieldName: '生产机台',
                  isQuery: true
                },
                {
                  fieldKey: 'wareHouseName',
                  fieldName: '仓库名称',
                  isQuery: true
                }
              );
              break;

            case 'customerName,supplierName':
              otherForm.searchFieldList.push(
                {
                  fieldKey: 'customerName',
                  fieldName: '客户名称',
                  isQuery: true
                },
                {
                  fieldKey: 'supplierName',
                  fieldName: '供应商名称',
                  isQuery: true
                }
              );
              break;

            default:
              otherForm.searchFieldList.push(item);
              break;
          }
        }
      });
      otherForm.fieldTips = '查询内容';
    };

    // const initFieldList = () => {
    //   otherForm.searchFieldList = [{
    //     field: 'secSampleNum',
    //     name: '样品编号'
    //   },
    //   {
    //     field: 'presentationCode',
    //     name: store.user.tenantInfo.type === 1 ? '申请单号' : '委托编号'
    //   },
    //   {
    //     field: 'reportNo',
    //     name: '报告编号'
    //   },
    //   {
    //     field: 'sampleName',
    //     name: '样品名称'
    //   }, {
    //     field: 'prodType',
    //     name: '型号规格'
    //   }]
    //   let addArray = []
    //   if (store.user.tenantInfo.type === 1) {
    //     addArray = [{
    //       field: 'materialGroup',
    //       name: '物料分组'
    //     }, {
    //       field: 'materialNo',
    //       name: '物料编号'
    //     }, {
    //       field: 'reelNo',
    //       name: '盘号'
    //     }, {
    //       field: 'batchNo',
    //       name: '批次'
    //     }, {
    //       field: 'supplierName',
    //       name: '供应商名称'
    //     },
    //     {
    //       field: 'customerName',
    //       name: '客户名称'
    //     }, {
    //       field: 'wareHouseName',
    //       name: '仓库名称',
    //       isNotQuery: 0
    //     },
    //     {
    //       field: 'productionProcedure',
    //       name: '生产工序',
    //       isNotQuery: 0
    //     },
    //     {
    //       field: 'productionStation',
    //       name: '生产机台',
    //       isNotQuery: 0
    //     }, {
    //       field: 'inputWarehouseNo',
    //       name: '入库单号',
    //       isNotQuery: 0
    //     },
    //     {
    //       field: 'productionOrderNo',
    //       name: '生产单号',
    //       isNotQuery: 0
    //     }]
    //   }
    //   otherForm.searchFieldList = otherForm.searchFieldList.concat(addArray)
    // }
    /** 同步 */
    const handleSync = row => {
      ElMessageBox({
        title: '同步',
        message: '是否确认同步数据?',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          otherForm.listLoading = true;
          experimentDataReturn(row.id).then(res => {
            otherForm.listLoading = false;
            if (res !== false) {
              ElMessage.success('同步成功！');
              getList();
            }
          });
        })
        .catch(() => {});
    };
    return {
      getNamesByid,
      handleDataReuse,
      handleSync,
      handleDown,
      handleRowClick,
      filterExamineStage,
      getSingleText,
      getParamList,
      filterExamineStatus,
      selectData,
      closeAddReport,
      batchApprovalSuccess,
      addReport,
      moreCheck,
      addMsg,
      filterSampleUnitToName,
      giveMeChange,
      approvalDetail,
      editDetail,
      drageHeader,
      handleJumpDetail,
      // handleSampleOrdersDetail,
      // handleAllReportDetail,
      filterOperation,
      formatDate,
      changeUser,
      changeUpdateTime,
      changeBZTime,
      changeProdType,
      getNameByid,
      changeRadio,
      filterUserList,
      filterSubmitReport,
      handleSelectionChange,
      handlePreview,
      sortChange,
      editFrom,
      ...toRefs(otherForm),
      filterMaterialCode,
      submitReportSuccess,
      search,
      onSubmit,
      reset,
      closeReportApproval,
      closeBatchApprovalDialog,
      closeDrawer,
      removeReport,
      downloadReport,
      submitReport,
      progressReport,
      handleXMLFormat,
      getPermissionBtn,
      colWidth,
      // jumpApplicationDetail,
      handleVisible,
      haveMoreOperation,
      getList,
      onUpdateColumns
    };
  },
  computed: {
    ...mapGetters(['tenantGroup', 'tenantInfo'])
  },
  created() {},
  methods: {
    getProcessLists() {
      var that = this;
      return new Promise((resolve, reject) => {
        getProcessList(that.tenantInfo.type)
          .then(res => {
            if (res !== false) {
              resolve(res.data.data);
            } else {
              resolve([]);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.sample-order-form {
  text-align: left;
  .el-form-item {
    margin-bottom: 0px;
  }
  .searchBtn {
    border: 0;
    color: $tes-primary;
    padding: 0;
    background: none;
  }
}
.radio-content {
  display: flex;
  justify-content: space-between;
  .only-me {
    margin-right: 15px;
  }
  .radio-con-btn {
    margin-left: 10px;
  }
}
.tag-order {
  min-width: 100px;
}
.blue-color {
  color: $tes-primary;
  cursor: pointer;
}
.margin-right-10 {
  margin-right: 10px;
}
</style>
