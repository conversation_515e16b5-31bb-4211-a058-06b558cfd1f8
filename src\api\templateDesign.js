import request from '@/utils/request';

// 列表
export function designtemplateList(data) {
  return request({
    url: '/api-document/document/designtemplate/list',
    method: 'post',
    data
  });
}
// 删除
export function deleteDesigntemplate(id) {
  return request({
    url: `/api-document/document/designtemplate/delById/${id}`,
    method: 'delete'
  });
}

// 删除
export function saveOrUpdate(data) {
  return request({
    url: `/api-document/document/designtemplate/saveOrUpdate`,
    method: 'post',
    data
  });
}
// 根据Id查询模板设计信息
export function designtemplateInfo(id) {
  return request({
    url: `/api-document/document/designtemplate/info/${id}`,
    method: 'get'
  });
}
