<template>
  <el-dialog
    v-model="dialogVisible"
    title="微信授权登录"
    width="400px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="content">
      <div v-loading="dialogLoading" class="imgBox">
        <img :src="qrCodeDetail.url" alt="" />
        <div v-if="scanDetail.isExpire" class="failure" />
        <div v-if="scanDetail.isExpire" class="failureWord">
          您的二维码已失效，<br />
          请点击下方刷新按钮
        </div>
      </div>
      <div class="weChatTitle">
        请使用微信扫描二维码登录
        <span @click="getTwoDimensionalCode()"><i class="el-icon-refresh" />刷新</span>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { reactive, toRefs, watch, onBeforeUnmount } from 'vue';
import { getQrcode, initCheckScan, wechatmpBind } from '@/api/user';
import { ElMessageBox, ElMessage } from 'element-plus';

export default {
  name: 'DialogWechat',
  components: {},
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    pageType: {
      type: String,
      default: ''
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      dialogVisible: false,
      dialogLoading: false,
      page: '',
      timer: null,
      scanDetail: {},
      qrCodeDetail: {}
    });
    watch(props, newValue => {
      state.dialogVisible = newValue.dialog;
      if (state.dialogVisible) {
        state.pageType = newValue.pageType;
        getTwoDimensionalCode();
      }
    });
    /** 微信登录，获取二维码 */
    const getTwoDimensionalCode = () => {
      state.dialogLoading = true;
      getQrcode().then(res => {
        state.dialogLoading = false;
        if (res) {
          state.qrCodeDetail = res.data.data;
          state.timer = setInterval(() => {
            checkScan();
          }, 4000);
        }
      });
    };
    const checkScan = () => {
      initCheckScan({ sceneId: state.qrCodeDetail.sceneId }).then(res => {
        if (res) {
          state.scanDetail = res.data.data;
          if (state.scanDetail.isScan) {
            // 已经扫码成功
            removeTimer();
            if (state.scanDetail.isBind) {
              // 已经绑定成功
              handleClose({ isRefreshLogin: true, detail: state.scanDetail });
            } else {
              if (state.pageType === 'login') {
                // 没有绑定成功
                ElMessageBox({
                  title: '提示',
                  message:
                    '<div style="line-height: 40px; text-align: center;">当前微信未绑定账号，请绑定后再进行登录</div><div style="line-height: 40px; text-align: center;">绑定步骤：登录系统-个人管理-基本信息</div>',
                  confirmButtonText: '确定',
                  dangerouslyUseHTMLString: true,
                  showCancelButton: false,
                  closeOnClickModal: false
                })
                  .then(() => {
                    context.emit('closeDialog');
                  })
                  .catch(() => {});
              } else {
                handleBindWechat();
              }
            }
          } else if (state.scanDetail.isExpire) {
            removeTimer();
          }
        }
      });
    };
    const handleBindWechat = () => {
      wechatmpBind({ socialId: state.scanDetail.id, unionId: state.scanDetail.unionId }).then(res => {
        if (res) {
          ElMessage.success('绑定微信成功');
          handleClose({ isRefreshLogin: true });
        }
      });
    };
    const removeTimer = () => {
      if (state.timer) {
        clearInterval(state.timer);
        state.timer = null;
      }
    };
    onBeforeUnmount(() => {
      removeTimer();
    });
    const handleClose = info => {
      removeTimer();
      context.emit('closeDialog', info);
      state.dialogVisible = false;
    };
    return { ...toRefs(state), getTwoDimensionalCode, handleBindWechat, removeTimer, handleClose };
  }
};
</script>
<style lang="scss" scoped>
.content {
  text-align: center;
  padding-top: 20px;
  transform: scale(0.8);
}
.imgBox {
  width: 270px;
  position: relative;
  margin: 0 auto;
  img {
    width: 100%;
  }
  .failure {
    position: absolute;
    background-color: #fff;
    z-index: 100;
    text-align: center;
    opacity: 0.9;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
  }
  .failureWord {
    position: absolute;
    color: #fa5b5b;
    font-size: 14px;
    height: 100%;
    width: 100%;
    line-height: 20px;
    top: 0;
    left: 0;
    z-index: 100;
    padding-top: 106px;
  }
}
.weChatTitle {
  font-size: 14px;
  color: #898d90;
  text-align: center;
  line-height: 14px;
  margin-bottom: 100px;
  span {
    color: #38adff;
    cursor: pointer;
    font-size: 14px;
  }
}
</style>
