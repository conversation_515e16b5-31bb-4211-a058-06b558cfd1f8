<template>
  <section class="app-main">
    <router-view v-slot="{ Component }" :key="key">
      <transition name="router-transform" mode="out-in">
        <keep-alive>
          <div :key="key" style="width: 100%; height: 100%">
            <component :is="Component" />
          </div>
        </keep-alive>
      </transition>
    </router-view>
  </section>
</template>
router-view
<script>
import { watchEffect } from 'vue';
import store from '@/store';
import Watermark from '@/utils/watermark';
import { getLoginInfo } from '@/utils/auth';
export default {
  name: 'AppMain',
  setup() {
    watchEffect(() => {
      if (store.state.user.tenantInfo.isWatermark) {
        const userInfo = getLoginInfo();
        Watermark.add(`${userInfo.nickname} ${userInfo.username}`);
      } else {
        Watermark.remove();
      }
    });
  },
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews;
    },
    key() {
      return this.$route.path;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  width: 100%;
  position: relative;
  height: calc(100vh - 48px);
  overflow: auto;
}

.fixed-header + .app-main {
  // padding-top: 90px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 86px);
  }

  .fixed-header + .app-main {
    padding-top: 124px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    // padding-right: 15px;
  }
}
</style>
