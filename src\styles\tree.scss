.tree-container {
  .tree-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    .el-button {
      width: 32px;
      margin-left: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 9px;
    }
  }
  .tree-content {
    // 此处可以根据实际页面情况覆盖默认高度
    height: calc(100vh - 260px);
    overflow: auto;
    .tree-node {
      max-width: 90%;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.leftTree {
  padding: 0;
}
:deep(.el-tree.leftTree .el-tree-node__content span) {
  max-width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
}
.el-tree {
  :deep(.el-tree-node:focus > .el-tree-node__content) {
    background: $tes-primary2 !important;
  }
  :deep(.tree-dropdown) {
    position: absolute;
    right: 8px;
    height: 20px;
    line-height: 20px;
    width: 20px;
    text-align: center;
    background: $tes-primary2;
    i {
      color: #909399;
    }
  }
  :deep(.tree-dropdown:hover) {
    background: #909399;
  }
}
:deep(.el-tree-node__content:hover) {
  background: $tes-primary2;
  // border-radius: 4px;
}
:deep(.el-tree .el-tree-node__content:hover .el-icon:hover) {
  background: none;
}
:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background: $tes-primary2;
  color: $tes-primary;
  // border-radius: 4px;
}
.treeTop {
  padding: 10px 0;
  position: relative;
  text-align: left;
  .el-input {
    vertical-align: middle;
    width: calc(100% - 36px);
  }
  .addTreeBtn {
    width: 28px;
    height: 28px;
    padding: 0px 0px 0px 2px;
    position: absolute;
    right: 9px;
  }
}
