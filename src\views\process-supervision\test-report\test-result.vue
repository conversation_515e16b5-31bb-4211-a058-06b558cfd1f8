<template>
  <div class="test-result">
    <el-form
      ref="testResultRef"
      :model="formData"
      :rules="infoRules"
      label-position="right"
      label-width="110px"
      class="test-form"
    >
      <el-form-item label="编制人：">
        <!-- <UserTag
          :name="
            formData.editorName ||
              getNameByid(formData.editorId) ||
              formData.editorId
          "
        /> -->
        {{ formData.editorName || getNameByid(formData.editorId) || formData.editorId }}
      </el-form-item>
      <el-form-item label="编制日期：" prop="editDate">
        <el-date-picker
          v-model="formData.editDate"
          type="date"
          clearable
          placeholder="选择日期"
          format="YYYY年MM月DD日"
          @change="changeTime(formData.editDate, 1)"
        />
      </el-form-item>
      <el-form-item label="签发日期：" prop="issueDate">
        <el-date-picker
          v-model="formData.issueDate"
          type="date"
          clearable
          placeholder="选择日期"
          format="YYYY年MM月DD日"
          @change="changeTime(formData.issueDate, 2)"
        />
      </el-form-item>
      <el-form-item v-if="isPass && tenantInfo.type === 0" prop="unqualifiedLevel" label="等级：">
        <el-select
          v-model="formData.unqualifiedLevel"
          placeholder="请选择"
          @change="changeLevel(formData.unqualifiedLevel)"
        >
          <el-option v-for="item in levelOptions" :key="item.name" :label="item.name" :value="item.name" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="报告类型：" prop="reportType">
        <el-radio-group v-model="formData.reportType" @change="changeReportType">
          <el-radio border class="test-result-border" label="0">抽检试验</el-radio>
          <el-radio border class="test-result-border" label="1">型式试验</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item label="盖章范围：">
        <el-checkbox-group v-model="sealRange" @change="changeSealRange">
          <el-checkbox border class="test-result-border1" label="CNAS" />
          <el-checkbox border class="test-result-border1" label="CMA" />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="检测结果：" prop="reportResult" class="report-result">
        <el-radio-group v-model="formData.reportResult" @change="changeReportResult">
          <el-radio border class="test-result-border" :label="0">合格</el-radio>
          <el-radio border class="test-result-border" :label="1">不合格</el-radio>
          <el-radio border class="test-result-border" :label="2">不判定</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="检测结论："
        prop="reportResultTypeValue"
        :rules="[
          {
            required: reportResultTypeValueRequired,
            message: '请输入检测结论',
            trigger: 'change'
          }
        ]"
      >
        <el-input
          v-model="formData.reportResultTypeValue"
          type="textarea"
          rows="2"
          placeholder="请输入内容"
          maxlength="300"
          @change="changeText(formData.reportResultTypeValue, 0)"
        />
      </el-form-item>
      <el-form-item label="备注：">
        <el-input
          v-model="formData.remark"
          type="textarea"
          rows="2"
          placeholder="请输入内容"
          maxlength="300"
          @change="changeText(formData.remark, 1)"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { reactive, toRefs, watch } from 'vue';
import { getNameByid } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { getTree } from '@/api/testBase';
import { formatTree } from '@/utils/formatJson';
import { getLoginInfo } from '@/utils/auth';
import { mapGetters } from 'vuex';
// import { UserTag } from '@/components/UserTag'
// import { ElMessage } from 'element-plus'
// import _ from 'lodash'

export default {
  name: 'TestResult',
  components: {},
  props: {
    data: {
      type: Object,
      default: function () {
        return {};
      }
    },
    sampleDetail: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['setInfo'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    // console.log(props.data)
    const currentUserId = getLoginInfo().accountId;
    // 校验盖章范围
    // const validateSealRange = (rule, value, callback) => {
    //   if (datas.sealRange.length === 0) {
    //     callback(new Error('请选择盖章范围'))
    //   } else {
    //     callback()
    //   }
    // }
    const validateUnqualifiedLevel = (rule, value, callback) => {
      if (!value && datas.formData.reportResult === 1) {
        callback(new Error('请选择等级'));
      } else {
        callback();
      }
    };

    const datas = reactive({
      formData: {
        editorName: getNameByid(currentUserId),
        editDate: formatDate(new Date()),
        issueDate: formatDate(new Date()),
        sealRange: '',
        reportType: '1',
        reportResult: 0,
        unqualifiedLevel: '',
        reportResultTypeValue: '',
        remark: ''
      },
      sealRange: [],
      treeData: [],
      isPass: false,
      reportResultTypeValueRequired: false,
      levelOptions: [{ name: 'Ⅰ级' }, { name: 'Ⅱ级' }, { name: 'Ⅲ级' }, { name: '轻微级' }],
      infoRules: {
        editDate: [{ required: true, message: '请选择编制日期', trigger: 'change' }],
        issueDate: [{ required: true, message: '请选择签发日期', trigger: 'change' }],
        reportType: [{ required: true, message: '请选择报告类型', trigger: 'change' }],
        reportResult: [{ required: true, message: '请选择检测结果', trigger: 'change' }],
        // reportResultTypeValue: [{ required: true, message: '请输入检测结论', trigger: 'change' }],
        unqualifiedLevel: [
          {
            required: true,
            trigger: 'change',
            validator: validateUnqualifiedLevel
          }
        ]
      }
    });

    const filterDatas = () => {
      datas.formData = Object.assign(datas.formData, props.data);
      if (datas.formData.editorId) {
        datas.formData.editorName = getNameByid(datas.formData.editorId);
      } else {
        datas.formData.editorId = currentUserId;
        datas.formData.editorName = getNameByid(currentUserId);
      }
      if (datas.formData.unqualifiedLevel === null) {
        datas.formData.unqualifiedLevel = '';
      }
      if (datas.formData.sealRange) {
        datas.sealRange = props.data.sealRange.split(',');
      }
      if (datas.formData.reportResult === 1) {
        datas.isPass = true;
      }
      if (!datas.formData.editDate) {
        datas.formData.editDate = formatDate(new Date());
      }
      if (!datas.formData.issueDate) {
        datas.formData.issueDate = formatDate(new Date());
      }
      if (!datas.formData.reportType) {
        datas.formData.reportType = '0';
      }
      checkReportResultTypeValueRequired(datas.formData.reportResult);
      if (!datas.formData.reportResultTypeValue?.trim()) {
        datas.formData.reportResultTypeValue = getReportResultTypeValueDefaultValue(datas.formData.reportResult);
      }

      // if (datas.formData.reportResult) {
      //   switch (datas.formData.reportResult) {
      //     case 0:
      //       datas.formData.reportResultTypeValue = '该样品所测项目符合要求';
      //       break;
      //     case 1:
      //       datas.formData.reportResultTypeValue = '该样品所测项目不符合要求';
      //       break;
      //     case 2:
      //       datas.formData.reportResultTypeValue = '';
      //       break;
      //   }
      // }
      context.emit('setInfo', datas.formData);
    };
    filterDatas();

    watch(
      () => props,
      newValue => {
        // console.log(newValue)
        if (newValue) {
          context.emit('setInfo', datas.formData);
        }
      },
      { deep: true }
    );

    // 时间选择
    const changeTime = (date, flag) => {
      if (flag === 1) {
        // console.log(formatDate(date))
        datas.formData.editDate = formatDate(date);
      } else {
        // console.log(formatDate(date))
        datas.formData.issueDate = formatDate(date);
      }
      context.emit('setInfo', datas.formData);
    };
    // 选择报告类型
    const changeReportType = data => {
      // console.log(data)
      datas.formData.reportType = data;
      context.emit('setInfo', datas.formData);
    };
    // 选择盖章范围
    const changeSealRange = data => {
      // console.log(data)
      datas.formData.sealRange = data.join(',');
      context.emit('setInfo', datas.formData);
    };
    // 选择检测结果
    const changeReportResult = data => {
      // console.log(data)
      if (data === 1) {
        datas.isPass = true;
      } else {
        datas.isPass = false;
        datas.formData.unqualifiedLevel = '';
      }
      datas.formData.reportResult = data;
      checkReportResultTypeValueRequired(datas.formData.reportResult);
      datas.formData.reportResultTypeValue = getReportResultTypeValueDefaultValue(datas.formData.reportResult);
      context.emit('setInfo', datas.formData);
    };
    // 选择等级
    const changeLevel = data => {
      // console.log(data)
      datas.formData.unqualifiedLevel = data;
      context.emit('setInfo', datas.formData);
    };
    // changeText
    const changeText = (value, flag) => {
      if (flag === 0) {
        datas.formData.reportResultTypeValue = value;
      } else {
        datas.formData.remark = value;
      }
      context.emit('setInfo', datas.formData);
    };

    function getReportResultTypeValueDefaultValue(reportResult) {
      return {
        0: `经检测，所测项目均符合${props.sampleDetail?.testBasis}的要求`,
        1: '该样品所测项目不符合要求',
        2: ''
      }[reportResult];
    }

    function checkReportResultTypeValueRequired(reportResult) {
      datas.reportResultTypeValueRequired = [0, 1].includes(reportResult);
    }
    return {
      ...toRefs(datas),
      formatDate,
      changeReportType,
      changeSealRange,
      changeReportResult,
      changeLevel,
      changeText,
      changeTime,
      getNameByid
    };
  },
  computed: {
    ...mapGetters(['tenantInfo'])
  },
  created() {
    // this.getLeftTree('266013')
  },
  methods: {
    // 获取左侧列表树接口
    getLeftTree(code) {
      const vm = this;
      getTree(code).then(res => {
        const data = res.data.data;
        vm.treeData = formatTree(data);
        // console.log(vm.treeData)
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.test-result {
  padding: 20px;
  background: $background-color;
  margin-bottom: 60px;
  .test-form {
    padding: 0 300px;
    :deep(.el-form-item) {
      margin-bottom: 20px;
    }
    :deep(.el-form-item__content) {
      text-align: left;
    }
    :deep(.el-radio),
    :deep(.el-checkbox) {
      margin-right: 8px;
    }
  }
}
</style>
