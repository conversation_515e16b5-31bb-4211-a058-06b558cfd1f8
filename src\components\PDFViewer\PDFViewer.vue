<template>
  <div ref="container" class="viewer w-full h-full relative">
    <Toolbar class="absolute left-1/2 -translate-x-1/2 bottom-0 z-[101] opacity-0 transition" />
    <PDFViewer
      :pdf="pdf"
      :args="{
        httpHeaders: {
          authorization: getToken()
        }
      }"
      :id-config="idConfig"
      :page-scale="props.pageScale"
      use-custom-toolbar
      @before-run="beforeRunHandle"
      @after-initialized="afterInitializedHandle"
    />
  </div>
</template>

<script setup>
import PDFViewer from 'pdf-viewer';
import { EventName } from './enum';
import Toolbar from './Toolbar.vue';
import { computed, ref } from 'vue';
import { Annotation } from './annotation';
import { getToken } from '@/utils/auth';

const props = defineProps({
  pdf: {
    type: [String, null, ArrayBuffer],
    required: true
  },
  pageScale: {
    type: Number,
    default: null
  },
  annotations: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['loaded', 'update:pageNumber']);

const idConfig = {
  download: 'download',
  next: 'next',
  numPages: 'numPages',
  pageNumber: 'pageNumber',
  pageRotateCcw: 'pageRotateCcw',
  pageRotateCw: 'pageRotateCw',
  previous: 'previous',
  sidebarToggle: 'sidebarToggle',
  zoomIn: 'zoomIn',
  zoomOut: 'zoomOut'
};

const cmapsUrl = new URL(`/cmaps/`, import.meta.url).href;
const container = ref();

const pdf = computed(() => props.pdf);
let pdfViewerApplication = null;

function beforeRunHandle(AppOptions) {
  AppOptions.set('cMapUrl', `${cmapsUrl}/`);
}

function afterInitializedHandle(app) {
  removeKeyboardEvent(app);
  pdfViewerApplication = app;
  setTimeout(() => {
    app.eventBus.off(EventName.PAGE_CHANGING, viewPageChanging);
    app.eventBus.on(EventName.PAGE_CHANGING, viewPageChanging);
    app.eventBus.off(EventName.PAGE_RENDER, pageRender);
    app.eventBus.on(EventName.PAGE_RENDER, pageRender);
  }, 50);
  emit('loaded', app);
}

function viewPageChanging({ pageNumber }) {
  emit('update:pageNumber', pageNumber);
}

function pageRender(e) {
  appendCustomAnnotation(e.source);
}

/** 不在激活状态时不处理键盘事件, 模拟overlay打开，跳过事件处理 */
function removeKeyboardEvent(app) {
  app.overlayManager = new Proxy(app.overlayManager, {
    get: function (target, property) {
      if (property === 'active') {
        return !container.value?.contains(document.activeElement);
      }
      return target[property];
    }
  });
}

/** 在annotation中添加自定义的annotation */
function proxyGetAnnotations(pdfPage, customAnnotations) {
  pdfPage.getAnnotations = new Proxy(pdfPage.getAnnotations, {
    apply: async function (target, thisArg, argArray) {
      return target.apply(thisArg, argArray).then(annotations => {
        annotations.push(...customAnnotations);
        return annotations;
      });
    }
  });
}

function appendCustomAnnotation(pageView) {
  const { pdfPage } = pageView;
  const pageAnnotations = getAnnotationByPageNumber(pdfPage.pageNumber);
  if (pageAnnotations.length < 1) {
    return;
  }
  proxyGetAnnotations(pdfPage, pageAnnotations);
}

function getAnnotationByPageNumber(pageNumber) {
  const annotations = props.annotations;
  if (!Array.isArray(annotations)) {
    return [];
  }
  const pageAnnotations = annotations.filter(annotation => annotation.pageNumber === pageNumber);
  const results = [];
  pageAnnotations.forEach(annotation => {
    results.push(new Annotation(annotation));
    if (annotation.hasPopup) {
      results.push(
        new Annotation({
          ...annotation,
          parentId: annotation.id,
          id: `${annotation.id}-popup`,
          hasPopup: false
        })
      );
    }
  });
  return results;
}

function setPage(pageNo) {
  if (!pdfViewerApplication?.initialized) {
    return;
  }
  pdfViewerApplication.page = pageNo;
}

defineExpose({
  setPage
});
</script>

<style scoped lang="scss">
@mixin scroll-bar {
  &::-webkit-scrollbar-track-piece {
    background: transparent;
  }

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 20px;
  }

  &:hover::-webkit-scrollbar-thumb {
    background: rgb(144 147 153 / 30%);

    &:hover {
      background: rgb(144 147 153 / 50%);
    }
  }
}

:deep(.pdf-viewer) {
  @include scroll-bar;

  box-sizing: content-box;

  .highlightAnnotation[data-annotation-id^='custom-highlight-'] {
    background-color: rgb(255 255 0 / 30%);
  }

  #thumbnailView,
  #viewerContainer {
    @include scroll-bar;
  }

  // pdf.js sidebar resizer use clientX. start clientX must zero.
  #sidebarResizer {
    pointer-events: none;
  }
}

.viewer:hover {
  .toolbar {
    @apply opacity-100;
  }
}
</style>
