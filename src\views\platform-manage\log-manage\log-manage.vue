<template>
  <!-- 日志管理 -->
  <ListLayout class="account-management" :has-search-panel="false" :has-quick-query="false" :has-button-group="true">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="title">
          <el-input
            v-model="formInline.title"
            v-trim
            v-focus
            placeholder="请输入标题"
            class="ipt-360"
            prefix-icon="el-icon-search"
            size="large"
            clearable
            @keyup.enter="onSubmit"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="onSubmit">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button v-if="getPermissionBtn('addLogBtn')" type="primary" icon="el-icon-plus" size="large" @click="addLog"
        >新增更新日志</el-button
      >
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      size="medium"
      height="auto"
      class="dark-table allocation-table base-table format-height-table no-quick-query"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column type="index" label="序号" width="70px" align="center" />
      <el-table-column label="标题" prop="title" min-width="160px" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.title || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="系统版本" prop="version" width="160px">
        <template #default="{ row }">
          <span>{{ row.version || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="160px" sortable>
        <template #default="{ row }">
          <span>{{ row.createTime || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createBy" width="160px">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.createBy) || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="操作" prop="caozuo" :width="160" fixed="right" class-name="fixed-right">
        <template #default="{ row, $index }">
          <span v-if="getPermissionBtn('detailLogBtn')" class="blue-color" @click="handleLM(row, 1, $index)">查看</span>
          <span v-if="getPermissionBtn('editLogBtn')" class="blue-color" @click="handleLM(row, 2)">编辑</span>
          <span v-if="getPermissionBtn('deleteLogBtn')" class="blue-color" @click="handleLM(row, 3)">删除</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
    <template #other>
      <add-log
        :drawer="showDialog"
        :title="dialogTitle"
        :edit-data="dialogData"
        @close="closeDialog"
        @setInfo="setInfo"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, toRefs, getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getPermissionBtn, getNameByid } from '@/utils/common';
import ListLayout from '@/components/ListLayout';
import { getLoginInfo } from '@/utils/auth';
// import _ from 'lodash'
import { getLogInfoList, deleteLogInfo } from '@/api/logInfo';
import Pagination from '@/components/Pagination';
import { drageHeader } from '@/utils/formatTable';
import UserTag from '@/components/UserTag';
import AddLog from './add-log.vue';
import router from '@/router';

export default {
  name: 'LogManagement',
  components: { Pagination, ListLayout, UserTag, AddLog },
  setup() {
    const { proxy } = getCurrentInstance();
    // const store = useStore().state
    const datas = reactive({
      accountId: getLoginInfo().accountId,
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: null
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      list: [],
      formInline: {
        title: ''
      },
      showDialog: false,
      dialogTitle: '',
      dialogData: null
    });
    // 查询
    function onSubmit() {
      proxy.getList();
    }
    // 重置
    function reset() {
      datas.formInline = {
        title: ''
      };
      datas.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: null
      };
      proxy.getList();
    }
    // table排序
    const sortChange = data => {
      const { prop, order } = data;
      datas.listQuery.orderBy = prop;
      if (order === 'ascending') {
        datas.listQuery.isAsc = true;
      } else if (order === 'descending') {
        datas.listQuery.isAsc = false;
      } else {
        datas.listQuery.isAsc = null;
      }
    };
    // 新增日志
    const addLog = () => {
      datas.dialogTitle = 'add';
      datas.showDialog = true;
      datas.dialogData = {};
    };
    // 关闭弹出框
    const closeDialog = v => {
      datas.showDialog = false;
      proxy.getList();
    };
    // 弹出框-确认
    const setInfo = data => {
      datas.showDialog = false;
      proxy.getList();
    };
    // 操作
    const handleLM = (row, flag, index) => {
      if (flag === 1) {
        // 查看
        router.push({
          path: '/platform-manage/log-detail',
          query: {
            id: row.id,
            index: index
          }
        });
      } else if (flag === 2) {
        // 编辑
        datas.dialogTitle = 'edit';
        datas.showDialog = true;
        datas.dialogData = row;
      } else {
        // 删除
        ElMessageBox({
          title: '',
          message: '是否删除该日志？',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: true,
          type: 'warning'
        })
          .then(() => {
            deleteLogInfo([row.id]).then(res => {
              if (res !== false) {
                ElMessage.success('删除成功！');
                proxy.getList();
              }
            });
          })
          .catch(() => {});
      }
    };

    return {
      ...toRefs(datas),
      onSubmit,
      reset,
      sortChange,
      handleLM,
      drageHeader,
      addLog,
      closeDialog,
      setInfo,
      getPermissionBtn,
      getNameByid
    };
  },
  created() {
    this.getList();
    // 刷新列表
    this.bus.$on('logManagementList', msg => {
      this.getList();
    });
  },
  methods: {
    getList(data) {
      const _this = this;
      // _this.listLoading = true
      if (data && data !== undefined) {
        _this.listQuery.page = data.page;
        _this.listQuery.limit = data.limit;
      }
      const param = Object.assign(_this.formInline, _this.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      getLogInfoList(param).then(res => {
        if (res !== false) {
          const { data } = res.data;
          _this.list = data.list;
          _this.total = data.totalCount;
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.account-management {
  .allocation-table {
    // width: auto;
    .blue-color {
      color: $tes-primary;
      cursor: pointer;
    }
  }
}
</style>
