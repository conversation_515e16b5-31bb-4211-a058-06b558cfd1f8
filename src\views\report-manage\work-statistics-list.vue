<template>
  <!-- 工分统计列表 -->
  <ListLayout ref="dutyTimeList" :has-left-panel="false" :has-quick-query="false">
    <template #search-bar>
      <div class="searchDataClass textAlignL">
        <el-date-picker
          v-model="searchForm.showMonth"
          type="month"
          size="large"
          :clearable="false"
          placeholder="请选择日期"
          style="width: 40%; margin-right: 7px"
        />
        <el-button type="primary" :loading="tableLoading" size="large" @click="getTableList()">查询</el-button>
        <el-button size="large" :loading="tableLoading" @click="handleReset()">重置</el-button>
      </div>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('personnelScreener')"
        :loading="tableLoading"
        type="primary"
        size="large"
        icon="el-icon-user"
        @click="getAllReadySelect()"
        @keyup.prevent
        @keydown.enter.prevent
        >筛选人员</el-button
      >
      <el-button
        :loading="tableLoading"
        type="primary"
        size="large"
        icon="el-icon-upload"
        @click="downLoad()"
        @keyup.prevent
        @keydown.enter.prevent
        >导出</el-button
      >
    </template>
    <el-table
      v-if="!tableLoading && tableData.length > 0"
      ref="tableRef"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      height="auto"
      size="medium"
      highlight-current-row
      class="dark-table format-height-table base-table dutyTimeTable no-quick-query"
      @sort-change="sortChange"
      @header-dragend="drageHeader"
    >
      <el-table-column label="姓名" prop="userId" fixed="left" :min-width="colWidth.person">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.userId) || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="部门" prop="deptName" fixed="left" :min-width="colWidth.departmentUnit">
        <template #default="{ row }">
          {{ row.deptName || '--' }}
        </template>
      </el-table-column>
      <el-table-column label="合计工分" prop="total" fixed="left" :min-width="colWidth.version" sortable>
        <template #default="{ row }">
          <span v-if="row.total" class="blue-color" @click="handleCheck(row)">{{ row.total }}</span>
          <span v-else>{{ row.total }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-for="(val, index) in tableDataHeader"
        :key="index"
        :label="val"
        :prop="val"
        :min-width="70"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ row.dayWorkPoints[val] }}
        </template>
      </el-table-column>
    </el-table>
    <div v-if="tableData.length === 0" v-loading="tableLoading">
      <el-empty description="暂无数据" />
    </div>
    <pagination
      v-show="total > 0"
      v-loading="tableLoading"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getTableList"
    />
    <!-- 人员管理弹出框 -->
    <DialogMulDepartPerson
      :dialog-visible="dialogPerson"
      :is-show-record="false"
      :already-select="searchUserList"
      @selectData="handleClosePerson"
    />
    <!-- 合计工分 -->
    <el-drawer
      v-model="drawerVisiable"
      title="工分统计详情"
      direction="rtl"
      :before-close="handleCloseDrawer"
      size="88%"
      destroy-on-close
      custom-class="page-drawer"
    >
      <DrawerLayout v-loading="drawerLoading" :has-left-panel="false" :main-offset-top="103" :has-button-group="false">
        <el-table
          ref="tableRef"
          :data="drawerTable"
          class="dark-table base-table format-height-table2"
          fit
          border
          size="medium"
          style="margin-top: 5px"
          @header-dragend="drageHeader"
        >
          <el-table-column label="序号" type="index" :min-width="colWidth.serialNo" />
          <el-table-column label="样品编号" prop="secSampleNum" :min-width="colWidth.orderNo" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.secSampleNum || '--' }}
            </template>
          </el-table-column>
          <el-table-column
            label="检测项目"
            prop="capabilityName"
            :min-width="colWidth.projectName"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.capabilityName || '--' }}
            </template>
          </el-table-column>
          <el-table-column label="试验负责人" prop="ownerId" :min-width="colWidth.person">
            <template #default="{ row }">
              <UserTag :name="getNameByid(row.ownerId) || '--'" />
            </template>
          </el-table-column>
          <el-table-column label="试验员" prop="ownerIds" :min-width="colWidth.people" show-overflow-tooltip>
            <template #default="{ row }">
              <UserTag v-for="(item, index) in getNamesByid(row.ownerIds)" :key="index" :name="item" />
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="status" :min-width="colWidth.status">
            <template #default="{ row }">
              <el-tag size="small" effect="dark" :type="statusJSON[row.status].type">{{
                statusJSON[row.status].label || '--'
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="耗费工分" prop="workPoints" :min-width="colWidth.workPoints">
            <template #default="{ row }">
              {{ row.workPoints || '0' }}
            </template>
          </el-table-column>
          <el-table-column label="提交日期" prop="finishDateTime" :min-width="colWidth.date">
            <template #default="{ row }">
              {{ formatDate(row.finishDateTime) || '--' }}
            </template>
          </el-table-column>
          <el-table-column label="审核日期" prop="reviewDateTime" :min-width="colWidth.date">
            <template #default="{ row }">
              {{ formatDate(row.reviewDateTime) || '--' }}
            </template>
          </el-table-column>
          <el-table-column label="样品名称" prop="sampleName" :min-width="colWidth.projectName" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.sampleName || '--' }}
            </template>
          </el-table-column>
          <el-table-column label="型号规格" prop="prodType" :min-width="colWidth.model" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.prodType || '--' }}
            </template>
          </el-table-column>
          <el-table-column label="批次" prop="batchNo" :min-width="colWidth.batch" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.batchNo || '--' }}
            </template>
          </el-table-column>
          <el-table-column label="盘号" prop="reelNo" :min-width="colWidth.plate" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.reelNo || '--' }}
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="totalDrawer > 0"
          v-loading="drawerLoading"
          :page="listQueryDrawer.page"
          :limit="listQueryDrawer.limit"
          :total="totalDrawer"
          @pagination="getWorkList"
        />
        <div class="drawer-fotter">
          <el-button :loading="drawerLoading" @click="handleCloseDrawer">取消</el-button>
        </div>
      </DrawerLayout>
    </el-drawer>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import UserTag from '@/components/UserTag';
import { getWorkpoints, workPointsInfo } from '@/api/workStatisticsList';
import DialogMulDepartPerson from '@/components/BusinessComponents/DialogMulDepartPerson';
import { colWidth } from '@/data/tableStyle';
import ListLayout from '@/components/ListLayout';
import { getNameByid, getNamesByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { formatYM } from '@/utils/formatTime';
import Pagination from '@/components/Pagination';
import DrawerLayout from '@/components/DrawerLayout';

export default {
  name: 'WorkStatisticsList',
  components: { ListLayout, UserTag, DialogMulDepartPerson, Pagination, DrawerLayout },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      searchForm: {
        showMonth: formatYM(new Date()) // 显示月份
      },
      searchUserList: [], // 筛选人员
      selectUserId: '',
      drawerTable: [],
      drawerType: '',
      drawerVisiable: false,
      drawerLoading: false,
      allTableData: [], // 导出的数据
      dialogPerson: false,
      statusJSON: {
        3: { label: '待审核', type: 'warning' },
        5: { label: '审核通过', type: 'success' }
      },
      tableRef: ref(),
      dutyTimeList: ref(),
      selectData: [],
      tableDataHeader: [], // 表格头
      type: 0,
      tableData: [],
      total: 0,
      totalDrawer: 0,
      tableLoading: false, // 表格加载的loading
      listQueryDrawer: {
        page: 1,
        limit: 20
      },
      listQuery: {
        page: 1,
        limit: 20
      }
    });
    const getTableList = query => {
      const params = { ...state.searchForm };
      if (params.showMonth) {
        params.showMonth = formatYM(params.showMonth);
      }
      params.userList = state.searchUserList;
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.tableLoading = true;
      getWorkpoints(params).then(res => {
        state.tableLoading = false;
        if (res) {
          getAllTableList();
          const data = res.data.data;
          state.tableData = data.list;
          state.total = data.totalCount;
          if (state.tableData.length) {
            state.tableDataHeader = Object.keys(state.tableData[0].dayWorkPoints);
          }
        }
      });
    };
    getTableList();
    const getAllTableList = async () => {
      state.tableLoading = true;
      const response = await getWorkpoints({ limit: '-1', page: '1' }).finally((state.tableLoading = false));
      if (response) {
        state.searchUserList = response.data.data.list;
      }
    };
    // 列表排序
    const sortChange = column => {
      state.searchForm.orderBy = column.prop;
      state.searchForm.isAsc = !state.searchForm.isAsc;
      getTableList();
    };
    // 重置列表
    const handleReset = () => {
      state.searchForm = {
        showMonth: formatYM(new Date()),
        orderBy: '',
        isAsc: null
      };
      getTableList();
    };
    const downLoad = () => {
      const params = { ...state.searchForm, page: '1', limit: '-1' };
      if (params.showMonth) {
        params.showMonth = formatYM(params.showMonth);
      }
      if (state.searchUserList.length) {
        params.userList = state.searchUserList;
      }
      state.tableLoading = true;
      getWorkpoints(params).then(res => {
        state.tableLoading = false;
        if (res) {
          const data = res.data.data;
          state.allTableData = data.list;
          if (state.allTableData.length) {
            export2Excel();
          } else {
            proxy.$message.warning('暂无数据导出！');
          }
        }
      });
    };
    const export2Excel = () => {
      state.tableLoading = true;
      var tHeader = [...['姓名', '部门', '合计工分'], ...state.tableDataHeader];
      var filterVal = [...['userId', 'deptName', 'total'], ...state.tableDataHeader];
      var fileName = '工分统计';
      import('@/utils/Export2Excel').then(excel => {
        const data = formatJson(filterVal, state.allTableData);
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: fileName,
          autoWidth: true,
          bookType: 'xlsx'
        });
        state.tableLoading = false;
        proxy.$message.success('导出成功！');
      });
    };
    const formatJson = (filterVal, jsonData) => {
      return jsonData.map(v =>
        filterVal.map(j => {
          if (j === 'userId') {
            return getNameByid(v[j]);
          } else if (Number(j)) {
            return v.dayWorkPoints[j];
          } else {
            return v[j];
          }
        })
      );
    };
    const getAllReadySelect = () => {
      state.dialogPerson = true;
    };
    const handleClosePerson = val => {
      state.dialogPerson = false;
      if (val) {
        state.searchUserList = val;
        getTableList();
      }
    };
    const handleCloseDrawer = () => {
      state.drawerVisiable = false;
    };
    // 查看合计
    const handleCheck = row => {
      state.drawerVisiable = true;
      state.selectUserId = row.userId;
      getWorkList();
    };
    const getWorkList = query => {
      const params = { ...state.searchForm, userId: state.selectUserId };
      if (params.showMonth) {
        params.showMonth = formatYM(params.showMonth);
      }
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQueryDrawer.page = query.page;
        state.listQueryDrawer.limit = query.limit;
      } else {
        state.listQueryDrawer.page = 1;
        params.page = '1';
        params.limit = state.listQueryDrawer.limit.toString();
      }
      state.drawerLoading = true;
      workPointsInfo(params).then(res => {
        state.drawerLoading = false;
        if (res) {
          const data = res.data.data;
          state.drawerTable = data.list;
          state.totalDrawer = res.data.data.totalCount;
        }
      });
    };

    return {
      ...toRefs(state),
      getNamesByid,
      getWorkList,
      getAllReadySelect,
      sortChange,
      handleCheck,
      downLoad,
      handleReset,
      handleCloseDrawer,
      handleClosePerson,
      getTableList,
      getPermissionBtn,
      formatDate,
      getNameByid,
      drageHeader,
      colWidth
    };
  }
};
</script>
<style lang="scss" scoped>
.textAlignL {
  text-align: left;
}
.searchDataClass {
  width: 800px;
}
</style>
