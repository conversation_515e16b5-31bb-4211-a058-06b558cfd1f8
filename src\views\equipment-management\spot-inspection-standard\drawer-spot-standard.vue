<template>
  <el-drawer
    v-model="drawerVisiable"
    :title="titleJSON[drawerType]"
    direction="rtl"
    :before-close="handleClose"
    size="88%"
    destroy-on-close
    custom-class="page-drawer"
  >
    <DrawerLayout v-loading="drawerLoading" :has-left-panel="false" :main-offset-top="53" :has-button-group="false">
      <el-form
        ref="formRef"
        :model="formData"
        label-position="right"
        size="small"
        label-width="110px"
        class="form-height-auto"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="standardNo" label="点检标准编号：">
              <el-input v-model="formData.standardNo" disabled placeholder="自动生成无需填写" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              prop="standardName"
              label="点检标准名称："
              :rules="{ required: true, message: '请输入点检标准名称', tigger: 'change' }"
            >
              <el-input v-model="formData.standardName" maxlength="30" placeholder="请输入点检标准名称" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="仪器设备：">
              <div class="device-list">
                <el-button
                  icon="el-icon-plus"
                  type="primary"
                  size="mini"
                  style="margin-right: 5px"
                  @click="handleChooseEquipment()"
                  >选择仪器设备</el-button
                >
                <el-tag
                  v-for="(item, index) in formData.deviceList"
                  :key="item.deviceId"
                  type="primary"
                  effect="dark"
                  closable
                  style="margin-right: 5px"
                  @close="handleDeleteTag(item, index)"
                >
                  {{ item.deviceName }}（{{ item.deviceNumber }}）</el-tag
                >
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="remark" label="描述：">
              <el-input v-model="formData.remark" type="textarea" maxlength="300" :rows="2" placeholder="请输入描述" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="btnGroup">
          <el-col :span="12">
            <h3>点检标准明细</h3>
          </el-col>
          <el-col :span="12" class="text-right">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
          </el-col>
        </el-row>
        <el-table
          v-if="isShowTable"
          id="sortableList"
          ref="tableRef"
          :data="formData.itemList"
          class="dark-table base-table format-height-table2"
          fit
          border
          size="medium"
          height="auto"
          style="margin-top: 5px"
          @header-dragend="drageHeader"
        >
          <el-table-column label="排序" prop="deviceNumber" :width="colWidth.serialNo">
            <i class="tes-move iconfont" style="font-size: 12px; cursor: move" />
          </el-table-column>
          <el-table-column label="序号" type="index" :width="colWidth.serialNo" />
          <el-table-column
            v-for="item in tableHeader"
            :key="item.id"
            :label="item.fieldName"
            :prop="item.id"
            :min-width="120"
            show-overflow-tooltip
          >
            <template #default="{ row, $index }">
              <div v-if="item.fieldType === 'text'">
                <!-- 文本输入框 -->
                <el-form-item :prop="`itemList.${$index}${[item.id]}`" style="margin-bottom: 0" label-width="0">
                  <el-input
                    v-model="row[item.id]"
                    :placeholder="`请输入${item.fieldName}`"
                    maxlength="100"
                    style="width: 100%"
                    @change="
                      val => {
                        return handleChange(val, $index, item.id);
                      }
                    "
                  />
                </el-form-item>
              </div>
              <div v-if="item.fieldType === 'date'">
                <!-- 日期选择框 -->
                <el-form-item :prop="`itemList.${$index}${[item.id]}`" style="margin-bottom: 0" label-width="0">
                  <el-date-picker
                    v-model="row[item.id]"
                    type="date"
                    :placeholder="`请选择${item.fieldName}`"
                    style="width: 100%"
                    @change="
                      val => {
                        return handleEditDate(val, $index, item.id);
                      }
                    "
                  />
                </el-form-item>
              </div>
              <div v-if="item.fieldType === 'number'">
                <!-- 数字输入框 -->
                <el-form-item
                  :prop="`itemList.${$index}${[item.id]}`"
                  :rules="{ validator: isNumberNot, validata: row[item.id], trigger: 'change' }"
                  style="margin-bottom: 0"
                  label-width="0"
                >
                  <el-input
                    v-model="row[item.id]"
                    :placeholder="`请输入${item.fieldName}`"
                    style="width: 100%"
                    @change="
                      val => {
                        return handleChange(val, $index, item.id);
                      }
                    "
                  />
                </el-form-item>
              </div>
              <div v-if="item.dictionaryCode">
                <!-- 字典选择框 -->
                <el-form-item :prop="`itemList.${$index}${[item.id]}`" style="margin-bottom: 0" label-width="0">
                  <el-select
                    v-model="row[item.id]"
                    :placeholder="`请选择${item.fieldName}`"
                    clearable
                    style="width: 100%"
                    @change="
                      val => {
                        return handleChange(val, $index, item.id);
                      }
                    "
                  >
                    <el-option
                      v-for="(val, key) in dictionaryDataAssemble[item.dictionaryCode]"
                      :key="key"
                      :label="val"
                      :value="key"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="formData.itemList.length > 1"
            label="操作"
            prop="measurementCycle"
            :width="colWidth.cycle"
          >
            <template #default="{ $index }">
              <span class="blue-color" @click="handleDelete($index)">删除</span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="drawer-fotter">
        <el-button type="primary" :loading="drawerLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确认</el-button
        >
        <el-button :loading="drawerLoading" @click="handleClose">取消</el-button>
      </div>
    </DrawerLayout>
  </el-drawer>
  <AddMultipleEquipment
    :show="drawerSelectEquipment"
    :select-info="formData.deviceList"
    @select-data="handleSelectData"
    @close="handleCloseDialog"
  />
</template>
<script>
// getCurrentInstance
import { reactive, ref, toRefs, watch, getCurrentInstance, nextTick } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { getDictionary } from '@/api/dictionary';
import DrawerLayout from '@/components/DrawerLayout';
import { isNumberNot } from '@/utils/validate';
import Sortable from 'sortablejs';
import AddMultipleEquipment from '@/components/BusinessComponents/AddMultipleEquipment';
import {
  pointInspectionStandardInfoId,
  pointInspectionFieldListAll,
  pointInspectionStandardSave
} from '@/api/spotInspectionStandard';
export default {
  name: 'DrawerSpotStandard',
  components: { AddMultipleEquipment, DrawerLayout },
  props: {
    drawerShow: {
      type: Boolean,
      default: false
    },
    drawerType: {
      type: String,
      default: ''
    },
    detailInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      formData: {
        itemList: [{ tableInfo: {} }],
        deviceList: []
      },
      formRef: ref(),
      isShowTable: true,
      type: '',
      tableHeader: [],
      drawerSelectEquipment: false,
      drawerLoading: false,
      dictionaryList: [],
      detailInfo: {},
      titleJSON: {
        copy: '复制点检标准',
        edit: '编辑点检标准',
        add: '新增点检标准'
      },
      drawerType: '', // 抽屉类型
      isEdit: false, // 是否是编辑页
      drawerVisiable: false,
      dictionaryDataAssemble: {}, // 字典集合
      dictionaryCodeAll: [],
      ruleForm: ref(),
      loading: false,
      tableRef: ref(null)
    });
    watch(props, newValue => {
      state.drawerVisiable = newValue.drawerShow;
      if (state.drawerVisiable) {
        state.detailInfo = props.detailInfo;
        state.drawerType = props.drawerType;
        state.dictionaryDataAssemble = {};
        getTableHeader();
        if (state.drawerType === 'add') {
          state.formData = {
            itemList: [{ order: 0, tableInfo: {} }],
            deviceList: []
          };
          state.isEdit = false;
          nextTick(() => {
            rowDrop();
          });
        } else {
          getDetailInfo();
          state.isEdit = true;
        }
      }
    });
    const rowDrop = () => {
      // 获取当前表格
      const el = document.getElementById('sortableList').querySelector('.el-table__body-wrapper tbody');
      Sortable.create(el, {
        animation: 300,
        handle: '.tes-move',
        draggable: '.el-table__row',
        ghostClass: 'ghost',
        // 拖动对象移动样式
        dragClass: 'drag',
        forceFallback: true,
        onEnd: function (evt) {
          if (evt.oldIndex !== evt.newIndex) {
            const currRow = state.formData.itemList.splice(evt.oldIndex, 1)[0];
            state.formData.itemList.splice(evt.newIndex, 0, currRow);
            state.formData.itemList.forEach((value, index) => {
              value.order = index;
            });
            showTable();
          }
        }
      });
    };
    const handleCloseDialog = () => {
      state.drawerSelectEquipment = false;
    };
    const showTable = () => {
      state.isShowTable = false;
      setTimeout(() => {
        state.isShowTable = true;
        nextTick(() => {
          rowDrop();
        });
      }, 0);
    };
    // 获取所有字典选项
    const getDictionaryDataAssemble = dictionaryCodeArray => {
      dictionaryCodeArray.forEach(item => {
        if (item) {
          state.dictionaryDataAssemble[item] = {};
          getDictionary(item).then(res => {
            state.loading = false;
            if (res) {
              const data = res.data.data.dictionaryoption;
              data.forEach(val => {
                if (val.status === 1) {
                  state.dictionaryDataAssemble[item][val.code] = val.name;
                }
              });
            }
          });
        }
      });
    };
    // 获取表格头
    const getTableHeader = () => {
      state.drawerLoading = true;
      pointInspectionFieldListAll({ status: 1 }).then(res => {
        state.drawerLoading = false;
        if (res) {
          state.tableHeader = res.data.data;
          const params = {};
          state.tableHeader.forEach(item => {
            params[item.id] = '';
          });
          if (!state.detailInfo.id) {
            state.formData.itemList = [{ order: 0, tableInfo: params }];
          }
          state.dictionaryCodeAll = state.tableHeader.map(item => item.dictionaryCode);
          getDictionaryDataAssemble([...new Set(state.dictionaryCodeAll)]);
        }
      });
    };
    const handleAdd = () => {
      state.formData.itemList.push({
        order: state.formData.itemList.length,
        tableInfo: {}
      });
    };
    const getDetailInfo = () => {
      state.drawerLoading = true;
      pointInspectionStandardInfoId(state.detailInfo.id).then(res => {
        state.drawerLoading = false;
        if (res) {
          if (state.drawerType === 'edit') {
            state.formData = res.data.data;
            state.formData.itemList.forEach(item => {
              item = Object.assign(item, item.tableInfo);
            });
            nextTick(() => {
              rowDrop();
            });
          } else {
            formatterForm(res.data.data);
          }
        }
      });
    };
    const formatterForm = detailInfo => {
      const paramsInfo = detailInfo;
      delete paramsInfo.id;
      delete paramsInfo.standardNo;
      state.formData = paramsInfo;
      state.formData.itemList.forEach(item => {
        item = Object.assign(item, item.tableInfo);
        delete item.id;
        delete item.standardId;
      });
      nextTick(() => {
        rowDrop();
      });
    };
    const onSubmit = () => {
      proxy.$refs['formRef']
        .validate()
        .then(valid => {
          if (valid) {
            if (!state.formData.deviceList.length) {
              proxy.$message.warning('请选择仪器设备');
              return false;
            }
            const tableInfo = state.formData.itemList.map(item => {
              return item.tableInfo;
            });
            const isEmpty = tableInfo.some(item => {
              return (
                JSON.stringify(item) === '{}' ||
                ([...new Set(Object.values(item))][0] === '' && [...new Set(Object.values(item))].length === 1)
              );
            });
            if (isEmpty) {
              proxy.$message.warning('标准明细存在空行数据！');
            } else {
              pointInspectionStandardSave(state.formData).then(res => {
                if (res) {
                  proxy.$message.success('保存成功');
                  context.emit('closeDialog', true);
                }
              });
            }
          }
        })
        .catch(error => {
          const fieldName = Object.keys(error)[0];
          proxy.$message.warning(error[fieldName][0].message);
          return false;
        });
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog');
    };
    // 删除
    const handleDelete = index => {
      state.formData.itemList.splice(index, 1);
    };
    // 格式化日期选择框
    const handleEditDate = (val, index, field) => {
      state.formData.itemList[index].tableInfo[field] = formatDate(val);
    };
    const handleChange = (val, index, field) => {
      state.formData.itemList[index].tableInfo[field] = val;
    };
    // 选择仪器设备
    const handleChooseEquipment = () => {
      state.drawerSelectEquipment = true;
    };
    // 删除选择的仪器设备
    const handleDeleteTag = (item, index) => {
      state.formData.deviceList.splice(index, 1);
    };
    const handleSelectData = val => {
      state.drawerSelectEquipment = false;
      const addDevice = [];
      if (val?.length) {
        val.forEach(item => {
          addDevice.push({
            deviceName: item.name,
            deviceId: item.id,
            deviceNumber: item.deviceNumber
          });
        });
      }
      state.formData.deviceList = [...addDevice, ...state.formData.deviceList];
    };
    return {
      ...toRefs(state),
      isNumberNot,
      formatterForm,
      handleChange,
      handleCloseDialog,
      handleChooseEquipment,
      handleSelectData,
      handleDeleteTag,
      getDictionaryDataAssemble,
      getTableHeader,
      handleEditDate,
      handleDelete,
      handleAdd,
      onSubmit,
      handleClose,
      getDetailInfo,
      formatDate,
      colWidth,
      drageHeader
    };
  }
};
</script>
<style lang="scss" scoped>
.btnGroup {
  margin-top: 20px;
}
h3 {
  line-height: 32px;
  margin: 0;
}
.device-list {
  max-height: 65px;
  overflow-y: auto;
}
.selectOption {
  display: flex;
  .optionName {
    flex: 1;
  }
  .optionRemark {
    font-size: 12px;
    color: $tes-font3;
  }
}

::v-deep(.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 31.5rem) !important;
    overflow-y: auto;
  }
}
</style>
