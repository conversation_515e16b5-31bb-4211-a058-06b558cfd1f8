<template>
  <!-- 试验配置 - 国网检测项目 -->
  <ListLayout
    class="test-item"
    :has-page-header="false"
    :has-quick-query="false"
    :has-left-panel="true"
    :aside-max-width="520"
    :aside-panel-width="300"
  >
    <template #page-left-side>
      <div class="tree-container">
        <div class="tree-header">
          <div class="header-select">
            <span class="icon el-icon-menu" />
            <el-select
              v-model="otherForm.materialCode"
              filterable
              size="small"
              class="topSelect"
              placeholder="请选择物资分类"
              @change="clickMaterial"
            >
              <el-option v-for="val in otherForm.tabsData" :key="val.value" :label="val.name" :value="val.code" />
            </el-select>
          </div>
          <div class="header-input-button">
            <el-input
              v-model="otherForm.filterText"
              size="small"
              placeholder="请输入类目名称"
              prefix-icon="el-icon-search"
              clearable
            />
            <el-button
              v-if="getPermissionBtn('sgItemTreeAdd')"
              class="addTreeBtn"
              size="small"
              icon="el-icon-plus"
              @click="addTreeItem"
              @keyup.prevent
              @keydown.enter.prevent
            />
          </div>
        </div>
        <div class="tree-content">
          <el-tree
            ref="tree"
            :data="otherForm.treeData"
            node-key="id"
            :props="otherForm.defaultProps"
            :expand-on-click-node="false"
            highlight-current
            draggable
            :allow-drop="allowDrop"
            :filter-node-method="filterNode"
            :current-node-key="otherForm.currentNodeKey"
            class="leftTree"
            @node-drop="nodeDrop"
            @filter="filterNode"
            @node-click="clickNode"
          >
            <template #default="{ node, data }">
              <span>{{ node.label }}</span>
              <el-dropdown
                v-if="data.id !== '0'"
                trigger="hover"
                class="tree-dropdown el-icon"
                :class="node.showIcon ? 'icon-show' : ''"
                @visible-change="changeIcon(node.showIcon, node)"
              >
                <i class="el-icon-more" />
                <template v-if="getPermissionBtn('sgItemTreeAdd')" #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-if="getPermissionBtn('sgEditTestItemTreeBtn')"
                      @click="editTree(node.data, node)"
                      ><i class="iconfont tes-edit" />编辑</el-dropdown-item
                    >
                    <!-- :disabled="node.data.status === 2" 删除按钮不用禁用，以红色字显示就可以 -->
                    <el-dropdown-item
                      v-if="getPermissionBtn('sgDelTestItemTreeBtn')"
                      class="color-red"
                      @click="delTree(node.data)"
                      ><i class="iconfont tes-delete" />删除</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-tree>
        </div>
      </div>
    </template>
    <el-form ref="editFrom" :inline="true" :model="formInline" class="test-item-form" @submit.prevent>
      <el-form-item prop="name" class="from-name" style="width: 100%; margin-bottom: 0">
        <el-row>
          <el-col :span="24">
            <el-input
              v-model="formInline.name"
              v-trim
              v-focus
              clearable
              placeholder="请输入项目名称搜索"
              size="small"
              prefix-icon="el-icon-search"
              style="width: 360px"
              @keyup.enter="onSubmit"
            />
            <el-button type="primary" size="small" style="margin-left: 10px" @click="onSubmit">查询</el-button>
            <el-button size="small" @click="reset">重置</el-button>
            <el-form-item v-if="getPermissionBtn('sgItemAdd')" style="float: right">
              <el-button
                type="primary"
                size="small"
                icon="el-icon-plus"
                @click="addItem"
                @keyup.prevent
                @keydown.enter.prevent
                >新增项目</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="otherForm.list"
      fit
      border
      height="auto"
      :size="otherForm.tableSize"
      highlight-current-row
      class="dark-table test-item-table base-table format-height-table no-quick-query"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="expand" :width="colWidth.expand">
        <template #default="{ row }">
          <!-- <div class="word-break">{{ row.description || '--' }}</div> -->
          <el-tag
            v-for="(item, index) in row.externalCapabilityParaList"
            :key="index"
            effect="dark"
            size="mini"
            type="info"
            style="margin-right: 8px"
            >{{ item.name }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="项目编号" prop="code" :width="colWidth.name" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <div class="blue-color" @click="handleDetail(row)">
            {{ row.code || '--' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="项目名称" prop="name" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目描述" prop="remark" :min-width="colWidth.remark" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.remark ? row.remark : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目等级" prop="grade" :width="colWidth.level">
        <template #default="{ row }">
          <div v-if="row.grade && row.grade !== '[]'">
            <span v-for="item in row.grade.split(',')" :key="item">{{ otherForm.itemLevel[item] }}</span>
          </div>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" :width="colWidth.status">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="row.status == 1 ? 'success' : 'info'">
            {{ row.status == 1 ? '已启用' : '已停用' }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="70" prop="caozuo" class-name="fixed-right" fixed="right">
        <template #default="scope">
          <span class="blue-color" @click="handleDetail(scope.row)">查看</span>
          <!-- <span class="blue-color" @click="handleEdit(scope.row)">编辑</span> -->
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getCapabilityListData"
    />
    <template #other>
      <add
        :drawer="drawer"
        :title="addTitle"
        :tree="otherForm.newTree"
        :edit-data="otherForm.editData"
        :category-ids="otherForm.currentCategoryids"
        :item-level="otherForm.itemLevel"
        :seal-scope-of="otherForm.sealScopeOf"
        :material-classification="otherForm.currentTabsData"
        @close="closeDrawer"
      />
      <!-- 新增检测项目树弹出框 -->
      <el-dialog
        v-model="showEditDialog"
        :title="otherForm.isAddTree === true ? '新增类目' : '编辑类目'"
        width="480px"
        :close-on-click-modal="false"
      >
        <el-form ref="formTree" :model="dialogFrom" :rules="otherForm.dialogRules" label-position="right">
          <el-form-item label="类目名称：" prop="name" :label-width="formLabelWidth" style="margin-bottom: 20px">
            <el-input
              v-model="dialogFrom.name"
              autocomplete="off"
              maxlength="30"
              :input="(dialogFrom.name = dialogFrom.name.replace(/\s+/g, ''))"
              placeholder="请输入类目名称"
            />
          </el-form-item>
          <el-form-item label="父级分类：" :label-width="formLabelWidth">
            <el-cascader
              v-model="otherForm.category"
              :options="otherForm.dialogTreeData"
              :props="otherForm.categoryProps"
              clearable
              style="width: 100%"
              @change="changeCategory"
            />
          </el-form-item>
          <el-form-item label="编码：" prop="code" :label-width="formLabelWidth" style="margin-bottom: 20px">
            <el-input
              v-model="dialogFrom.code"
              autocomplete="off"
              :input="(dialogFrom.code = dialogFrom.code.replace(/\s+/g, ''))"
              placeholder="请输入编码"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="closeTreeDialog">取 消</el-button>
            <el-button type="primary" @click="editDialogSuccess">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <drawer-item-detail
        :drawer="otherForm.detaildrawer"
        title="国网项目详情"
        :item-level="otherForm.itemLevel"
        :seal-scope-of="otherForm.sealScopeOf"
        :detail-data="detialData.data"
        :material-code="otherForm.materialCode"
        @close="closeDeatilDrawer"
      />
    </template>
  </ListLayout>
</template>
<script>
import { reactive, ref, watch, onMounted, nextTick, inject } from 'vue';
import { getPermissionBtn } from '@/utils/common';
import Pagination from '@/components/Pagination';
import {
  formatTree,
  formatAllTree,
  formatTestcapabilityByValue,
  formatTreeByIds,
  formatTreeByNames
} from '@/utils/formatJson';
import Add from './components/add';
import { getDictionary } from '@/api/user';
import DrawerItemDetail from './components/DrawerItemDetail';
import ListLayout from '@/components/ListLayout';
import { ElMessage, ElMessageBox } from 'element-plus';
import router from '@/router';
// import { useRoute } from 'vue-router'
import { drageHeader } from '@/utils/formatTable';
import _ from 'lodash';
import { colWidth } from '@/data/tableStyle';
import { getSgMaterialClassifcation } from './func/sgData';
import {
  getExternalCapabilityCategory,
  getExternalCapabilityList,
  saveExternalCapabilityCategory,
  deleteExternalCapabilityCategory,
  getExternalCapabilityInfoById,
  getKeyParamsByExternalCapabilityId,
  updateOrderCategory
} from '@/api/sg-capabilitystd';

export default {
  name: 'SgTestItem',
  components: { Pagination, Add, DrawerItemDetail, ListLayout },
  setup(props, context) {
    // const _ = inject('_')
    // 添加监控reloadList
    // const { proxy } = getCurrentInstance()
    const mittBus = inject('$mittBus');
    const formInline = reactive({
      name: '',
      cestCapability: [],
      categoryId: '',
      materialCategoryCode: '0'
    });
    const addTitle = ref('新增项目');
    const editFrom = ref(null);
    const activeName = ref('0');

    const otherForm = reactive({
      tableList: [],
      itemLevel: {},
      sealScopeOf: {},
      materialCode: '',
      list: [],
      content: '',
      treeData: [],
      dialogTreeData: [],
      editData: {},
      newTree: [],
      treeTitle: '', // 选中树节点的name
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      category: [],
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      menuVisible: false,
      activeIndex: '0',
      activeMoreIndex: null,
      tabsData: [],
      tabsMoreData: [],
      moreIndex: 0,
      currentTabsData: { code: '-1', name: '默认节点' },
      filterText: '',
      isAddTree: true,
      dialogRules: {
        name: [{ required: true, message: '请输入类目名称' }],
        parentId: [{ required: true, message: '请选择父级目录' }]
      },
      testcapability: [],
      asideWidth: 240,
      tableSize: 'medium',
      showIcon: false,
      currentNodeKey: '',
      currentCategoryids: [],
      detaildrawer: false
    });

    const total = ref(otherForm.tableList.length);
    const listLoading = ref(false);
    const tableKey = ref(0);
    const listQuery = reactive({
      page: 1,
      limit: 20,
      orderBy: '',
      isAsc: false
    });

    // otherForm.list = formatPaginationList(otherForm.tableList, listQuery.page, listQuery.limit)
    function reset() {
      editFrom.value.resetFields();
      otherForm.testcapability = [];
      formInline.cestCapability = '';
      listQuery.page = 1;
      listQuery.limit = 20;
      listQuery.orderBy = '';
      listQuery.isAsc = false;
      getCapabilityListData();
    }

    const sortChange = data => {
      const { prop, order } = data;
      listQuery.orderBy = prop;
      if (order === 'ascending') {
        listQuery.isAsc = true;
      } else if (order === 'descending') {
        listQuery.isAsc = false;
      } else {
        listQuery.isAsc = null;
      }
    };

    const handleSelectionChange = val => {
      console.log(val);
    };

    const statusFilterName = status => {
      const statusNames = {
        0: 'success',
        1: 'info'
      };
      return statusNames[status];
    };

    const inputValue = data => {
      if (typeof data === 'string') {
        otherForm.content = data;
      } else {
        otherForm.content = '';
      }
    };

    // 树的 鼠标右击事件
    const rightClick = (MouseEvent, object, node, element) => {
      otherForm.menuVisible = true;
    };
    // 鼠标hover到树节点
    const mouseover = () => {
      otherForm.showIcon = true;
    };
    const mouseleave = () => {
      otherForm.showIcon = false;
    };
    // 树节点编辑
    const showEditDialog = ref(false);
    const formLabelWidth = ref('120px');
    const dialogFrom = ref({
      code: '',
      id: '',
      materialClassificationCode: otherForm.currentTabsData ? otherForm.currentTabsData.code : '',
      materialClassificationId: otherForm.currentTabsData ? otherForm.currentTabsData.id : '',
      name: '',
      order: 0,
      parentId: ''
    });
    const editTree = (data, node) => {
      otherForm.dialogTreeData = formatAllTree(data.id, otherForm.dialogTreeData);
      showEditDialog.value = true;
      otherForm.isAddTree = false;
      dialogFrom.value = data;
      otherForm.category = formatTreeByIds(node.parent);
    };
    // 保存树节点
    const formTree = ref(null);
    const editDialogSuccess = () => {
      formTree.value.validate(valid => {
        if (valid) {
          // showEditDialog.value = false
          if (otherForm.isAddTree !== true) {
            saveExternalCapabilityCategory(dialogFrom.value).then(function (res) {
              // console.log(res)
              if (res !== false && res.data.code === 200) {
                ElMessage.success('编辑成功!');
                showEditDialog.value = false;
              }
              getCapabilityTreeData();
            });
          } else {
            if (!dialogFrom.value.parentId) {
              dialogFrom.value.parentId = '0';
            }
            saveExternalCapabilityCategory(dialogFrom.value).then(function (res) {
              // console.log(res)
              if (res !== false && res.data.code === 200) {
                ElMessage.success('新增成功!');
                getCapabilityTreeData();
                showEditDialog.value = false;
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    // 关闭树的新增编辑的弹出框
    const closeTreeDialog = () => {
      showEditDialog.value = false;
      dialogFrom.value = {
        name: '',
        parentId: '',
        materialClassificationCode: otherForm.currentTabsData.code,
        materialClassificationId: otherForm.currentTabsData.id,
        code: '',
        id: '',
        order: 0
      };
      if (formTree.value) {
        formTree.value.clearValidate();
      }
      getCapabilityTreeData();
    };
    // 新增树节点
    const addTreeItem = () => {
      showEditDialog.value = true;
      dialogFrom.value = {
        name: '',
        parentId: '',
        materialClassificationCode: otherForm.currentTabsData.code,
        materialClassificationId: otherForm.currentTabsData.id,
        code: '',
        id: '',
        order: 0
      };
      // console.log(formTree.value)
      if (formTree.value) {
        formTree.value.clearValidate();
      }
      otherForm.category = [];
      otherForm.isAddTree = true;
    };
    // 所属分类change
    const changeCategory = value => {
      if (value) {
        const len = value.length - 1;
        dialogFrom.value.parentId = value[len];
      } else {
        dialogFrom.value.parentId = 0;
      }
    };
    // 树节点删除
    const delTree = node => {
      console.log(node);
      var ids = [];
      ids.push(node.id);
      ElMessageBox({
        title: '提示',
        message: '是否删除该类目?',
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(() => {
          deleteExternalCapabilityCategory(ids).then(function (res) {
            console.log(res);
            if (res !== false) {
              ElMessage.success('删除成功!');
              getCapabilityTreeData();
            }
          });
        })
        .catch(() => {
          // ElMessage.info('已取消删除!')
        });
    };

    // 新增项目
    const drawer = ref(false);
    const addItem = () => {
      if (otherForm.treeData.length === 0) {
        ElMessage.warning('请选择项目树');
        return false;
      }
      addTitle.value = '新增项目';
      otherForm.editData = {};
      drawer.value = true;
    };
    const closeDrawer = () => {
      drawer.value = false;
    };
    const getDetail = detail => {
      // console.log(detail)
      detialData.data = detail.detail;
      otherForm.detaildrawer = detail.show;
    };
    // 编辑项目
    const handleEdit = row => {
      // console.log(row)
      addTitle.value = '编辑项目';
      otherForm.editData = row;
      drawer.value = true;
    };
    // 详情
    const detialData = reactive({ data: {} });
    const handleDetail = row => {
      // console.log(row)
      getExternalCapabilityInfoById(row.id).then(function (res1) {
        if (res1 !== false) {
          const { data } = res1.data;
          // 获取关键参数列表
          getKeyParamsByExternalCapabilityId(row.id).then(function (res) {
            data.tableData = res.data.data;
            data.tableData.forEach(item => {
              item.status = item.status === 1;
            });
            detialData.data = data;
            if (detialData.data.categoryIds) {
              detialData.data.categoryIds = detialData.data.categoryIds.reverse();
            } else {
              detialData.data.categoryIds = [];
            }
            detialData.data.apabilityTree = otherForm.treeData;
            otherForm.treeTitle = otherForm.treeData[0].name;
            detialData.data.currentMaterial = otherForm.currentTabsData;
            detialData.data.treelist = otherForm.treeData;
            otherForm.detaildrawer = true;
          });
        }
      });
    };

    // otherForm.detaildrawer监控
    // watch(
    //   () => otherForm.detaildrawer,
    //   (newValue) => {
    //     console.log(newValue)
    //   })
    const closeDeatilDrawer = () => {
      otherForm.detaildrawer = false;
    };
    const getDictionaryList = () => {
      getDictionary('XMDJ').then(res => {
        if (res) {
          res.data.data.dictionaryoption.forEach(item => {
            if (item.status === 1) {
              otherForm.itemLevel[item.code] = item.name;
            }
          });
        }
      });
      getDictionary('GZFW').then(res => {
        if (res) {
          res.data.data.dictionaryoption.forEach(item => {
            if (item.status === 1) {
              otherForm.sealScopeOf[item.code] = item.name;
            }
          });
        }
      });
    };
    getDictionaryList();
    // 检测项目filter
    const filterTestcapability = value => {
      return formatTestcapabilityByValue(value);
    };
    // 检测项目change
    const changeTestcapability = value => {
      formInline.cestCapability = value;
    };
    // 过滤树节点
    const tree = ref(null);
    watch(
      () => otherForm.filterText,
      newValue => {
        tree.value.filter(newValue);
      }
    );
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };

    // 选择物资-更多里面的物资
    const clickMaterial = async val => {
      otherForm.currentTabsData = otherForm.tabsData.filter(item => item.code === val)[0];
      formInline.categoryId = '';
      otherForm.currentNodeKey = '';
      router.replace({ query: {} });
      otherForm.materialCode = otherForm.currentTabsData.code;
      getCapabilityTreeData();
    };

    const changeIcon = (command, node) => {
      if (!command) {
        node.showIcon = true;
      } else {
        node.showIcon = !node.showIcon;
      }
      // console.log(node.showIcon)
    };
    // 允许拖拽功能 只能同级拖拽
    const allowDrop = (draggingNode, dropNode, type) => {
      // console.log(draggingNode)
      // console.log(dropNode)
      if (draggingNode.level === dropNode.level) {
        // parentId是父节点id
        if (draggingNode.data.parentId === dropNode.data.parentId) {
          return type === 'prev' || type === 'next';
        }
      } else {
        // 不同级进行处理
        return false;
      }
    };
    // 树排序
    const nodeDrop = (before, after) => {
      // console.log(after)
      var orderList = [];
      if (after.parent.level === 0) {
        orderList = JSON.parse(JSON.stringify(after.parent.data));
        _.remove(orderList, function (n) {
          return n.id === '0';
        });
        orderList.forEach((ol, index) => {
          ol.order = index + 1;
        });
      } else {
        orderList = after.parent.data.children;
        orderList.forEach((ol2, index) => {
          ol2.order = index + 1;
        });
      }
      // console.log(orderList)
      updateOrderCategory(orderList).then(res => {
        if (res !== false) {
          ElMessage.success('排序成功!');
        }
      });
    };

    function getCapabilityListData(pageData = undefined) {
      if (otherForm.treeData.length > 0) {
        listLoading.value = true;
        if (pageData && pageData !== undefined) {
          listQuery.page = pageData.page;
          listQuery.limit = pageData.limit;
        }
        const capabilityParams = {
          limit: listQuery.limit.toString(),
          page: listQuery.page.toString(),
          categoryId: formInline.categoryId,
          grade: '',
          isAsc: listQuery.isAsc,
          key: formInline.name,
          materialClassificationCode: formInline.materialCategoryCode,
          orderBy: listQuery.orderBy
        };
        getExternalCapabilityList(capabilityParams).then(res => {
          listLoading.value = false;
          if (res.data.data) {
            const { list, totalCount } = res.data.data;
            otherForm.list = list;
            total.value = totalCount;
          }
        });
      }
    }

    function getCapabilityTreeData() {
      getExternalCapabilityCategory(otherForm.currentTabsData.code).then(res => {
        const result = res.data.data;
        if (result) {
          otherForm.treeData = formatTree(result);
          otherForm.newTree = formatTree(result);
          otherForm.dialogTreeData = result;
          if (otherForm.treeData.length === 0) {
            listLoading.value = false;
            otherForm.treeTitle = '';
            otherForm.list = [];
            total.value = 0;
          }
          const allParam = {
            id: '0',
            name: '全部',
            materialCategoryCode: otherForm.currentTabsData.code,
            categoryId: ''
          };
          otherForm.treeData.unshift(allParam);
          if (formInline.categoryId === '' && otherForm.treeData[0].id === '0') {
            formInline.categoryId = '';
            formInline.materialCategoryCode = otherForm.currentTabsData.code;
            otherForm.treeTitle = otherForm.treeData[0].name;
          }
          // 默认选中树节点
          if (otherForm.currentNodeKey === '') {
            otherForm.currentNodeKey = otherForm.treeData[0].id;
            otherForm.currentCategoryids = [otherForm.treeData[0].id];
            otherForm.treeTitle = otherForm.treeData[0].name;
          } else {
            nextTick(() => {
              tree.value.setCurrentKey(otherForm.currentNodeKey);
            });
          }

          getCapabilityListData();
        }
      });
    }

    function clickNode(data, node) {
      if (data.id === '0') {
        formInline.categoryId = '';
        formInline.materialCategoryCode = data.materialCategoryCode;
        otherForm.currentNodeKey = data.id;
      } else {
        formInline.categoryId = data.id;
        otherForm.currentNodeKey = data.id;
      }
      router.replace({ query: {} });
      otherForm.treeTitle = formatTreeByNames(node)
        .filter(item => {
          return item;
        })
        .reverse()
        .join('/');
      otherForm.currentCategoryids = formatTreeByIds(node);
      getCapabilityListData();
    }

    function onSubmit() {
      getCapabilityListData();
    }

    // #region 立即执行的函数

    onMounted(() => {
      getSgMaterialClassifcation().then(res => {
        if (res && res.length > 0) {
          otherForm.tabsData = res;
          otherForm.currentTabsData = res[0];
          otherForm.materialCode = res[0].code;
          getCapabilityTreeData();
        }
      });

      // 刷新列表
      mittBus.$on('reloadList', msg => {
        getCapabilityListData();
      });
      // 刷新详情
      mittBus.$on('reloadDetail', row => {
        handleDetail(row);
      });
      // if (router.query.code) {
      // // console.log(this.router.query)
      //   formInline.categoryId = router.query.categoryId
      //   formInline.materialCategoryCode = router.query.code
      //   otherForm.currentTabsData.code = router.query.code
      //   otherForm.currentNodeKey = router.query.categoryId
      //   otherForm.currentCategoryids = [router.query.categoryId]
      // }
    });
    //

    return {
      getPermissionBtn,
      closeTreeDialog,
      drageHeader,
      changeIcon,
      clickMaterial,
      mouseleave,
      mouseover,
      formTree,
      getDictionaryList,
      changeTestcapability,
      changeCategory,
      addTreeItem,
      tree,
      filterNode,
      filterTestcapability,
      dialogFrom,
      editDialogSuccess,
      formLabelWidth,
      showEditDialog,
      delTree,
      editTree,
      addTitle,
      getDetail,
      detialData,
      closeDeatilDrawer,
      closeDrawer,
      drawer,
      addItem,
      rightClick,
      inputValue,
      handleSelectionChange,
      statusFilterName,
      handleEdit,
      handleDetail,
      sortChange,
      tableKey,
      listLoading,
      total,
      listQuery,
      formInline,
      editFrom,
      otherForm,
      activeName,
      reset,
      allowDrop,
      nodeDrop,
      colWidth,
      clickNode,
      onSubmit,
      getCapabilityListData
    };
  },
  methods: {}
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
.tree-container {
  .tree-header {
    flex-direction: column;
    .header-select {
      display: flex;
      width: 100%;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 10px;
      :deep(.el-select .el-input__inner) {
        padding-left: 0;
        font-size: 16px;
        color: #303133;
        border: none;
      }
      .icon {
        font-size: 16px;
        margin-right: 10px;
      }
      .el-select {
        width: 100%;
      }
    }
    .header-input-button {
      display: flex;
      width: 100%;
      justify-content: flex-start;
      align-items: center;
    }
  }
  .tree-content {
    height: calc(100vh - 210px);
  }
}
.test-capability {
  width: 100%;
  margin: 0px;
  :deep(.el-form-item__content) {
    width: inherit;
  }
}
:deep(.el-form--inline .el-form-item) {
  margin-right: 0;
}
.test-item {
  .filter-btn {
    float: right;
    width: 32px;
    height: 32px;
    line-height: 32px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    text-align: center;
    color: #909399;
    cursor: pointer;
    i {
      font-size: 18px;
    }
  }
  .test-item-form {
    text-align: left;
    margin-bottom: 20px;
    .el-form-item {
      :deep(.el-form-item__label) {
        color: #303133;
        font-size: 14px;
        font-weight: normal;
      }
    }
    .searchBtn {
      border: 0;
      background: none;
    }
    .from-name {
      width: 100%;
      :deep(.el-form-item__content) {
        width: 100%;
      }
      .el-col {
        padding-right: 10px;
      }
    }
  }
  .blue-color {
    color: $tes-primary;
    cursor: pointer;
  }
}
.test-item-table {
  :deep(.el-table__expanded-cell) {
    padding-top: 8px;
    padding-bottom: 8px;
  }
}
</style>
