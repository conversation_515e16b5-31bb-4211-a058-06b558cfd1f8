<template>
  <!-- 接口日志 -->
  <ListLayout v-loading="tableLoading" :has-button-group="false">
    <template #search-bar>
      <div class="searchInput">
        <el-input
          v-model="searchForm.url"
          v-trim
          v-focus
          placeholder="请输入URL"
          size="large"
          @keyup.enter="getTableList()"
        >
          <template #prefix>
            <i class="el-input__icon el-icon-search" />
          </template>
        </el-input>
        <el-button type="primary" size="large" @click="getTableList()">查询</el-button>
        <el-button size="large" @click="reset">重置</el-button>
        <el-button class="searchBtn" type="text" @click="advancedSearch" @keyup.prevent @keydown.enter.prevent
          >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
        /></el-button>
      </div>
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="searchFrom" :model="searchForm" label-width="110px" label-position="right">
            <el-form-item label="请求时间：">
              <el-date-picker
                v-model="requestDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="handleSubmitTime"
              />
            </el-form-item>
            <el-form-item label="是否补偿：">
              <el-radio-group v-model="searchForm.isCompensation" size="small">
                <el-radio-button :label="1">是</el-radio-button>
                <el-radio-button :label="0">否</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-radio-group v-model="searchForm.status" size="small" @change="getTableList()">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button v-for="(value, key, index) in statusJson" :key="index" :label="key"
          >{{ value }}
        </el-radio-button>
      </el-radio-group>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      size="medium"
      height="auto"
      highlight-current-row
      class="dark-table format-height-table base-table data-table"
      @header-dragend="drageHeader"
    >
      <el-table-column label="序号" type="index" :width="colWidth.serialNo" align="center" />
      <el-table-column label="IP" prop="ip" :width="colWidth.IP" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.ip || '--' }}
        </template>
      </el-table-column>
      <el-table-column label="URL" prop="url" :width="colWidth.URL" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-copy="'URL：' + row.url" class="blue-color">{{ row.url || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="请求体" prop="requestBody" :width="colWidth.requestBody" min-width="280px">
        <template #default="{ row }">
          <span v-copy="row.requestBody" class="blue-color nowrap">{{ row.requestBody || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="请求时间" prop="requestTime" :width="colWidth.datetime" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.requestTime || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="返回JSON" prop="respondBody" :width="colWidth.requestBody" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-copy="row.respondBody" class="blue-color">{{ row.respondBody || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="请求方法" prop="requestMethod" :width="colWidth.requestMethod" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.requestMethod || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" :width="colWidth.status" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag :type="statusTag[row.status]" effect="dark">{{
            statusJson[row.status] || row.status || '--'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="错误信息" prop="errorMessage" :width="colWidth.errorMessage" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.errorMessage" v-copy="row.errorMessage" class="blue-color">{{ row.errorMessage }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="是否补偿" prop="isCompensation" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.isCompensation ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="补偿次数" prop="compensationCnt" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.compensationCnt }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="getPermissionBtn('retry') && searchForm.status !== '1' && searchForm.status !== '2'"
        label="操作"
        :min-width="colWidth.operationSingle"
        fixed="right"
      >
        <template #default="{ row }">
          <span v-if="row.status === 0" class="blue-color" @click="handleRetry(row)">重试</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getTableList" />
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import Pagination from '@/components/Pagination';
import ListLayout from '@/components/ListLayout';
import { getNameByid, getNamesByid, getPermissionBtn } from '@/utils/common';
import { getList, compensationApi } from '@/api/interfaceManagement';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { getDictionary } from '@/api/user';
import { colWidth } from '@/data/tableStyle';

export default {
  name: 'InterfaceManagement',
  components: { Pagination, ListLayout },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableData: [],
      alreadyList: [],
      key: '', // 模糊查询关键字
      total: 0,
      activeCollapse: '',
      itemProjects: {},
      showS: false,
      activeName: '0',
      submitTime: [],
      requestDate: [], // 请求时间
      searchForm: {}, // 高级筛选的表单
      result: '-1', // 查询
      tableLoading: false, // 表格加载的loading
      listQuery: {
        page: 1,
        limit: 20
      },
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ],
      activeIndex: '0',
      activeMoreIndex: null,
      tabsMoreData: [],
      statusTag: {
        0: 'danger',
        1: 'success',
        2: 'default'
      },
      statusJson: {
        0: '失败',
        1: '成功',
        2: '补偿成功'
      }, // 类型
      moreIndex: 0
    });
    const tableKey = ref(0);
    const getRadioType = () => {
      getDictionary('JKZT').then(res => {
        if (res) {
          state.statusJson = {};
          res.data.data.dictionaryoption.forEach(val => {
            if (val.status === 1) {
              state.statusJson[val.code] = val.name;
            }
          });
        }
      });
    };
    // 处理签发日期
    const handleSubmitTime = date => {
      state.searchForm.startDate = date ? formatDate(date[0]) : '';
      state.searchForm.endDate = date ? formatDate(date[1]) : '';
    };
    getRadioType();
    // 重试
    const handleRetry = row => {
      state.tableLoading = true;
      compensationApi({ id: row.id }).then(res => {
        state.tableLoading = false;
        if (res) {
          proxy.$message.success(res.data.message);
          getTableList();
        }
      });
    };
    const reset = () => {
      state.listQuery.page = 1;
      state.listQuery.limit = 20;
      state.result = '-1';
      state.requestDate = []; // 请求时间
      state.searchForm = {};
      getTableList();
    };

    const getTableList = query => {
      var params = { ...state.searchForm };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
      } else {
        params.page = state.listQuery.page.toString();
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoading = true;
      getList(params).then(res => {
        state.listLoading = false;
        if (res) {
          state.tableData = res.data.data.records;
          state.total = res.data.data.total;
        }
      });
    };
    getTableList();
    // 高级搜索
    const advancedSearch = () => {
      state.showS = !state.showS;
      if (state.activeName === '0') {
        state.activeName = '1';
      } else {
        state.activeName = '0';
      }
    };
    return {
      ...toRefs(state),
      colWidth,
      handleRetry,
      getPermissionBtn,
      handleSubmitTime,
      advancedSearch,
      getRadioType,
      getTableList,
      formatDate,
      getNameByid,
      getNamesByid,
      drageHeader,
      tableKey,
      reset
    };
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/variables.scss';
// .el-radio-button__original-radio:checked+.el-radio-button__inner {
//     background-color: $tes-primary2;
//     border-color: $tes-primary1;
//     color: $tes-primary;
//   }

.el-radio-button--small .el-radio-button__inner {
  font-size: 14px !important;
  padding: 8px 15px;
}

.el-radio-button__original-radio:checked + .el-radio-button__inner {
  box-shadow: -0.067rem 0 0 0 $tes-primary1;
}
.el-tag {
  margin-right: 10px;
}
.searchInput {
  display: flex;
  .el-input {
    width: 360px;
    margin-right: 10px;
  }
}
</style>
