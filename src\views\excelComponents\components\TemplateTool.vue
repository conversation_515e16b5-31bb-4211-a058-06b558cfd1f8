<template>
  <div id="tool" class="tool">
    <ul id="leftTool" class="leftTool">
      <li
        v-for="item in toolAssemblePermission"
        :key="item.type"
        :class="{ affixOpen: item.type === 'affix' && isAffixOpen }"
        @click="handleClick(item.type)"
      >
        <div class="itemContent">
          <svg-icon v-if="item.type === 'affix' && isAffixOpen" icon-class="affixOpen" :width="20" :height="20" />
          <svg-icon v-else :icon-class="item.svgName" :width="20" :height="20" />
          <div v-if="item.type === 'snapshotHistory' && isHasNoRead" class="noRead" />
          <span class="name">{{ item.name }}</span>
        </div>
      </li>
    </ul>
    <div v-if="getPermissionBtn('templateVoiceSample') && isEdit" id="rightTool" class="rightTool">
      <div class="rightContent" :class="{ voiceOpen: voiceFlag, voiceIsError: voiceIsError }" @click="handleVoice()">
        <svg-icon :icon-class="voiceFlag ? 'voice-open' : 'voice'" :width="28" :height="28" class="voiceContent" />
        <div class="borderAnimation animation" />
        <div class="borderAnimation1 animation" />
      </div>
    </div>
  </div>
</template>
<script>
import { reactive, toRefs, watch, getCurrentInstance, onUnmounted, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import Recorder from 'recorder-core';
import 'recorder-core/src/engine/pcm';
import { formatterSocketMsg } from '@/utils/formatSocketMsg';
import { getPermissionBtn } from '@/utils/common';
import SvgIcon from '@/components/SvgIcon';
import $ from 'jquery';
import { throttle } from 'lodash';

export default {
  name: 'TemplateTool',
  components: { SvgIcon },
  props: {
    isHasNoRead: {
      type: Boolean,
      default: false
    },
    isTransverse: {
      type: Boolean,
      default: false
    }
  },
  emits: ['handleType'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      toolAssemble: [
        {
          type: 'affix',
          name: '数值粘贴',
          svgName: 'affix',
          pageType: 'edit',
          permission: 'TemplateCopyAffix'
        },
        {
          type: 'ocr',
          name: 'OCR识别',
          svgName: 'ocr',
          pageType: 'edit',
          permission: 'TemplateOCR'
        },
        {
          type: 'snapshot',
          name: '截图',
          svgName: 'snapshot',
          pageType: 'edit check',
          permission: 'TemplateSnapshot'
        },
        {
          type: 'snapshotHistory',
          name: '截图记录',
          svgName: 'snapshotHistory',
          pageType: 'edit check',
          permission: 'TemplateSnapshotHistory'
        }
      ],
      toolAssemblePermission: [], // 有权限的工具
      isTransverse: false, // 是否是横向模板
      isHasNoRead: false, // 是否有未读图片
      templateWebSocket: null, // 语音websocket
      dialogSnapshot: false,
      voiceFlag: false,
      isAffixOpen: false,
      isRec: false,
      isEdit: false,
      voiceIsError: false, // 语音打开是否异常
      focusDom: [],
      lengthNum: 0,
      sampleBuf: new Int16Array(),
      templateNodes: [], // 模板中可语音输入的所有节点的集合；用在语音指令下一步的功能上
      nodeIsNumber: false, // 聚焦的模板输入框是否是数字输入框
      ocrType: ''
    });
    watch(
      () => props.isHasNoRead,
      newValue => {
        state.isHasNoRead = newValue;
      }
    );
    watch(
      () => props.isTransverse,
      newValue => {
        state.isTransverse = newValue;
      }
    );
    const route = useRoute();
    const initList = () => {
      state.isEdit = route.query.type === 'edit';
      state.toolAssemblePermission = state.toolAssemble.filter(item => {
        return getPermissionBtn(item.permission) && item.pageType.includes(route.query.type);
      });
    };
    initList();
    const handleClick = type => {
      if (type === 'affix') {
        handleAffix();
      } else if (type === 'ocr') {
        // OCR识别
        context.emit('handleType', { type: 'ocr', value: true });
      } else if (type === 'snapshot') {
        // 截图
        context.emit('handleType', { type: 'snapshot', value: true });
      } else if (type === 'snapshotHistory') {
        // 截图记录
        context.emit('handleType', { type: 'snapshotHistory', value: true });
      }
    };

    // 粘贴模式开启关闭
    const handleAffix = () => {
      if (localStorage.getItem('templateValue') && localStorage.getItem('templateValue') !== '[]') {
        state.isAffixOpen = !state.isAffixOpen;
        context.emit('handleType', { type: 'affix', value: state.isAffixOpen });
      } else {
        proxy.$message.warning('暂无数据，请先去模板复制!');
      }
    };
    // 创建WebSocket连接函数
    const connectWebSocket = () => {
      state.templateWebSocket = new WebSocket('wss://asr.cxist.com');
      // 处理WebSocket连接打开事件
      state.templateWebSocket.onopen = () => {
        state.websocketState = true;
        state.voiceIsError = false;
        startRecording();
      };
      // 处理WebSocket失败
      state.templateWebSocket.onerror = event => {
        proxy.$message.warning('语音地址无效，功能打开失败');
        state.voiceIsError = true;
        state.voiceFlag = false;
      };
      // 处理WebSocket接收到消息事件（可选）
      state.templateWebSocket.onmessage = event => {
        voiceMessage(event.data);
      };
      // 处理WebSocket连接关闭事件
      state.templateWebSocket.onclose = () => {};
    };
    // buffers音频缓冲区数组,powerLevel：音频能量级别,bufferDuration：缓冲区的持续时间,bufferSampleRate：缓冲区的采样率,newBufferIdx：新的缓冲区索引,asyncEnd：如果设置为true，表示异步结束
    const recProcess = (buffer, powerLevel, bufferDuration, bufferSampleRate, newBufferIdx, asyncEnd) => {
      if (state.isRec === true) {
        var data_48k = buffer[buffer.length - 1];
        var array_48k = new Array(data_48k);
        var data_16k = Recorder.SampleData(array_48k, bufferSampleRate, 16000).data;
        state.sampleBuf = Int16Array.from([...state.sampleBuf, ...data_16k]);
        var chunk_size = 960;
        while (state.sampleBuf.length >= chunk_size) {
          const sendBuf = state.sampleBuf.slice(0, chunk_size);
          state.sampleBuf = state.sampleBuf.slice(chunk_size, state.sampleBuf.length);
          if (state.templateWebSocket && state.templateWebSocket.readyState === WebSocket.OPEN) {
            state.templateWebSocket.send(sendBuf);
          }
        }
      }
    };
    var rec = Recorder({
      type: 'pcm',
      bitRate: 16,
      sampleRate: 16000,
      onProcess: function (buffers, powerLevel, bufferDuration, bufferSampleRate, newBufferIdx, asyncEnd) {
        recProcess(buffers, powerLevel, bufferDuration, bufferSampleRate, newBufferIdx, asyncEnd);
      }
    });
    // 创建开始录音
    const startRecording = () => {
      var request = {
        chunk_size: [5, 10, 5],
        wav_name: 'h5',
        is_speaking: true,
        chunk_interval: 10,
        mode: 'online'
      };
      state.templateWebSocket.send(JSON.stringify(request));
      rec.open(function () {
        rec.start();
        state.isRec = true;
      });
    };
    const stopRecording = () => {
      if (state.isRec) {
        rec.stop((blob, duration) => {
          var request = {
            chunk_size: [5, 10, 5],
            wav_name: 'h5',
            is_speaking: false,
            chunk_interval: 10,
            mode: 'online'
          };
          state.isRec = false;
          if (state.sampleBuf.length > 0) {
            state.templateWebSocket.send(state.sampleBuf);
            state.sampleBuf = new Int16Array();
          }
          state.templateWebSocket.send(JSON.stringify(request));
          // state.templateWebSocket.close()
        });
      }
    };
    onUnmounted(() => {
      stopRecording();
      state.templateWebSocket?.close();
    });
    // 获取所有的节点DOM
    const initInputDom = () => {
      state.focusDom = [];
      state.lengthNum = 0;
      document.querySelectorAll('.ipt').forEach(item => {
        if (!item.disabled && item.tagName === 'INPUT') {
          state.lengthNum += 1;
          state.focusDom.push({
            tagName: item.tagName,
            dom: item
          });
        }
      });
    };
    initInputDom();
    const skipToNext = (id, type) => {
      const index = state.focusDom.findIndex(item => {
        return item.dom.id === id;
      });
      if (type.includes('下')) {
        if (index === state.focusDom.length - 1) {
          state.focusDom[0].focus();
        } else {
          $(state.focusDom[index + 1].dom).focus();
        }
      } else if (type.includes('上')) {
        if (index === 0) {
          state.focusDom[0].focus();
        } else {
          $(state.focusDom[index - 1].dom).focus();
        }
      }
    };
    const changInputValue = (inputDom, newText) => {
      const lastValue = inputDom.value;
      inputDom.value = newText;
      const event = new Event('input', { bubbles: true });
      event.simulated = true;
      const tracker = inputDom._valueTracker;
      if (tracker) {
        tracker.setValue(lastValue);
      }
      inputDom.dispatchEvent(event);
    };
    // 打开关闭语音
    const handleVoice = () => {
      state.voiceFlag = !state.voiceFlag;
      if (state.voiceFlag) {
        // 开启语音录入
        if (state.templateWebSocket === null) {
          connectWebSocket();
        } else if (state.voiceIsError) {
          connectWebSocket();
          state.voiceFlag = false;
        } else {
          startRecording();
        }
      } else {
        // 关闭语音录入
        stopRecording();
      }
    };
    const voiceMessage = async message => {
      if (state.voiceFlag) {
        const valueId = $('input:focus').get(0)?.id;
        state.nodeIsNumber = $($('input:focus').get(0)).hasClass('number');
        if (valueId && message) {
          if (JSON.parse(message)?.text?.includes('下') || JSON.parse(message)?.text?.includes('上')) {
            if (state.focusDom.length === 0) {
              await initInputDom();
            }
            skipToNext(valueId, JSON.parse(message)?.text);
          } else {
            const filterMessage = formatterSocketMsg(
              message,
              state.nodeIsNumber,
              document.getElementById(`${valueId}`).value
            );
            changInputValue(document.getElementById(`${valueId}`), filterMessage);
          }
        }
      }
    };
    const setContainerDisplayMode = throttle(() => {
      const mobanWidth = document.querySelector('.moban').offsetWidth;
      const toolElement = document.getElementById('tool');
      const leftElement = document.getElementById('leftTool');
      const rightElement = document.getElementById('rightTool');
      const slotTempElement = document.getElementsByClassName('slot-temp')[0];
      if ((state.isTransverse && mobanWidth <= 1346) || (!state.isTransverse && mobanWidth <= 1016)) {
        leftElement.classList.remove('leftTool');
        leftElement.classList.add('topLeftTool');
        rightElement?.classList.remove('rightTool');
        rightElement?.classList.add('topRightTool');
        toolElement.classList.add('topTool');
        slotTempElement.style.marginTop = '80px';
      } else {
        leftElement.classList.remove('topLeftTool');
        leftElement.classList.add('leftTool');
        rightElement?.classList.add('rightTool');
        rightElement?.classList.remove('topRightTool');
        toolElement.classList.remove('topTool');
        slotTempElement.style.marginTop = '0';
      }
    });
    onMounted(() => {
      setContainerDisplayMode();
      window.addEventListener('resize', setContainerDisplayMode);
    });

    onUnmounted(() => {
      window.removeEventListener('resize', setContainerDisplayMode);
    });
    return {
      ...toRefs(state),
      handleClick,
      handleVoice,
      handleAffix,
      stopRecording,
      connectWebSocket,
      getPermissionBtn
    };
  }
};
</script>
<style scoped lang="scss">
.leftTool {
  position: absolute;
  left: -120px;
  top: 0;
}
.topTool {
  display: flex;
  width: 100%;
  flex-direction: row;
  justify-content: space-between;
  position: absolute;
  left: 0;
  top: -80px;
}
.topLeftTool {
  display: flex;
  flex-direction: row;
  gap: 10px;
}
li {
  color: #303133;
  font-size: 13px;
  text-align: center;
  width: 80px;
  height: 62px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 0 0 10px 0;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  cursor: pointer;
  position: relative;
  padding-top: 5px;
  &:hover {
    background-color: $tes-primary2;
  }
}
.name {
  display: inline-block;
  line-height: 22px;
  user-select: none;
}
.svg-icon {
  display: block;
  margin: 0 auto 4px auto;
}
.affixOpen .itemContent {
  &::before {
    content: '';
    position: absolute;
    right: -1px;
    top: -1px;
    width: 13px;
    height: 13px;
    border-radius: 0 4px 0 0;
    border-right: 3px solid $tes-primary;
    border-top: 3px solid $tes-primary;
  }
  &::after {
    content: '';
    position: absolute;
    left: -1px;
    bottom: -1px;
    width: 13px;
    height: 13px;
    border-radius: 0 0 0 4px;
    border-left: 3px solid $tes-primary;
    border-bottom: 3px solid $tes-primary;
  }
}
.affixOpen {
  &::before {
    content: '';
    position: absolute;
    left: -1px;
    top: -1px;
    width: 13px;
    height: 13px;
    border-radius: 4px 0 0 0;
    border-top: 3px solid $tes-primary;
    border-left: 3px solid $tes-primary;
  }
  &::after {
    content: '';
    position: absolute;
    right: -1px;
    bottom: -1px;
    width: 13px;
    height: 13px;
    border-radius: 0 0 4px 0;
    border-bottom: 3px solid $tes-primary;
    border-right: 3px solid $tes-primary;
  }
  .name {
    color: $tes-primary;
  }
}
.noRead {
  background: #f56c6c;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  position: absolute;
  top: 7px;
  right: 22px;
}
.rightContent {
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 33px;
  cursor: pointer;
  display: flex;
  width: 53px;
  height: 53px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  &:hover {
    background-color: $tes-primary2;
  }
  .svg-icon {
    margin: 0 auto;
    z-index: 9;
  }
}
.rightTool {
  position: absolute;
  right: -110px;
  top: 120px;
}
.voiceOpen {
  background-color: transparent;
  border: none;
  .animation {
    position: absolute;
    top: 0;
    right: 0;
    width: 53px;
    height: 53px;
    border-radius: 33px;
    transform: scale(0.8);
  }
  .borderAnimation {
    background-color: $tes-primary;
    animation: testAnimation 1s ease-in-out infinite;
  }
  .borderAnimation1 {
    background-color: $tes-primary;
    animation: testAnimation1 1s ease-in-out infinite;
    animation-delay: 0.1s;
  }
}

@keyframes testAnimation {
  0% {
    transform: scale(0.8);
    box-shadow: 0 0 0 0 $tes-primary3;
  }
  50% {
    transform: scale(1);
    box-shadow: 0 0 0 8px $tes-primary2;
  }
  100% {
    transform: scale(0.8);
    box-shadow: 0 0 0 8px transparent;
  }
}
@keyframes testAnimation1 {
  0% {
    transform: scale(0.8);
    box-shadow: 0 0 0 0 $tes-primary4;
  }
  50% {
    transform: scale(1);
    box-shadow: 0 0 0 10px $menuHover;
  }
  100% {
    transform: scale(0.8);
    box-shadow: 0 0 0 10px transparent;
  }
}
</style>
