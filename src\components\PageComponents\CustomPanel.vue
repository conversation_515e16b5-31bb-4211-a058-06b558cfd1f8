<template>
  <div
    class="panelPage"
    :style="{
      marginTop: (hasMarginTop ? panelMarginWidth : 0) + 'rem',
      marginRight: (hasMarginRight ? panelMarginWidth : 0) + 'rem',
      marginBottom: (hasMarginBottom ? panelMarginWidth : 0) + 'rem',
      marginLeft: (hasMarginLeft ? panelMarginWidth : 0) + 'rem',
      height: panelHeight,
      minHeight: panelMinHeight
    }"
  >
    <div v-if="hasPanelHeader">
      <div class="panel-header">
        <slot name="panel-title" />
      </div>
      <div class="panel-content">
        <slot name="panel-content" />
      </div>
    </div>
    <div v-else class="panel-all" style="width: 100%; height: 100%">
      <slot />
    </div>
  </div>
</template>
<script>
import { reactive, toRefs } from 'vue';

export default {
  name: 'CustomPanel',
  props: {
    hasMarginTop: {
      type: Boolean,
      default: false
    },
    hasMarginRight: {
      type: Boolean,
      default: false
    },
    hasMarginBottom: {
      type: Boolean,
      default: false
    },
    hasMarginLeft: {
      type: Boolean,
      default: false
    },
    panelHeight: {
      type: String,
      default: 'auto'
    },
    panelMinHeight: {
      type: String,
      default: '200px'
    },
    panelMarginWidth: {
      type: Number,
      default: 1.714286
    },
    hasPanelHeader: {
      type: Boolean,
      default: true
    }
  },
  emits: ['setMainOffset'],
  setup(props, context) {
    const data = reactive({
      showPanel: false,
      show: false,
      topHeight: 80,
      headerPadding: 0
    });

    return {
      ...toRefs(data)
    };
  }
};
</script>

<style lang="scss" scoped>
.panelPage {
  background-color: $background-color;
}
.panel-header {
  width: 100%;
  padding: 16px 24px;
  :slotted(.panel-header-left) {
    display: flex;
    align-items: center;
    .title {
      font-size: 18px;
      height: 32px;
      line-height: 32px;
      color: $tes-font;
    }
    .standard-controller {
      padding-left: 20px;
      .standard-label {
        width: 80px;
        font-size: 12px;
      }
      .el-input--small {
        width: 88px;
        .el-input__inner {
          height: 24px;
          line-height: 24px;
        }
      }
      .standard-space {
        padding: 0 4px;
        color: #a8abb2;
      }
    }
  }
  :slotted(.panel-flex) {
    .el-select.el-select--small,
    .el-button.el-button--small,
    .el-range-editor--small,
    .el-input-number--small,
    .el-tag {
      width: 100%;
    }
  }
}
.panel-content {
  padding: 0 24px 20px;
  height: calc(100% - 64px);
  width: 100%;
  text-align: center;
  :slotted(.no-data) {
    margin: 0 auto;
    color: $tes-font2;
  }
  :slotted(.el-descriptions) {
    .el-descriptions__label {
      background: #f5f7fa;
      color: $tes-font1;
      font-weight: normal;
      width: 150px;
    }
    .el-descriptions__content {
      color: $tes-font;
    }
  }

  :slotted(.no-data) {
    display: flex;
    height: 100%;
    width: 100%;
    justify-content: center;
    align-items: center;
    // height: 32px;
    // line-height: 32px;
  }
}

.panel-all {
  :slotted(.el-form) {
    .el-form-item,
    .el-form-item__content {
      width: 100%;
    }
    .el-select.el-select--small,
    .el-button.el-button--small,
    .el-range-editor--small,
    .el-input-number--small,
    .el-input__inner,
    .el-tag {
      width: 100%;
    }
    .el-form-item__content {
      display: flex;
      align-items: center;
    }

    .el-button {
      font-weight: normal;
      border-radius: 4px;
      border-color: #dcdfe6;
      &:hover {
        border-color: #b3e8dc;
      }
    }
    .el-tag {
      height: 32px;
      line-height: 32px;
      text-overflow: ellipsis;
      max-width: 100%;
      word-break: break-all;
      overflow: hidden;
      border-color: transparent;
      .el-tag__close {
        font-size: 16px;
        position: absolute;
        right: 6px;
        top: 8px;
      }
    }
  }
  :slotted(.el-descriptions) {
    .el-descriptions__label {
      background: #f5f7fa;
      color: $tes-font1;
      font-weight: normal;
      width: 150px;
    }
    .el-descriptions__content {
      color: $tes-font;
    }
  }
}
</style>
