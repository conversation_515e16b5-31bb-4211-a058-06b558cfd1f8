<template>
  <!-- 智能图表模块数据看板 -->
  <div class="dataBoard">
    <div class="dataPageTop">
      <SvgIcon
        ref="voiceRef"
        class="logo"
        :icon-class="'cxistLogo'"
        :width="37"
        :height="37"
        @click="voiceBroadcast(2)"
      />
      <img class="logoRight" :src="isQms ? qmsLogo : logo_title" alt="" />
      <img class="topHeader" src="@/assets/img/dataBoard/topHeader.png" alt="" />
      <span class="dataPageTitle">{{ typesettingData.title }}</span>
      <div class="topRight">
        <div class="time inBlock">
          <img src="@/assets/img/dataBoard/calendar.png" alt="" />{{ formatDate(new Date()) }}
        </div>
        <div class="time inBlock"><img src="@/assets/img/dataBoard/timepiece.png" alt="" />{{ digitalClock }}</div>
        <i class="el-icon-setting" style="font-size: 21px" @click="showDrawer = true" />
      </div>
    </div>
    <div class="dataBoardContent">
      <el-row class="dataBoardTop">
        <el-col v-for="item in typesettingData.layoutDetail" :key="item.id" :span="item.width" class="componentItem">
          <SampleCompletionNumber v-if="item.name === '近15天样品完成数'" />
          <SubmitLeaderboard v-if="item.name === '近15天提交项目排行榜'" type="submitProject" />
          <SubmitLeaderboard v-if="item.name === '近15天提交报告排行榜'" type="submitReport" />
          <ToBeIssued v-if="item.name === '待下达（待认领）样品'" />
          <SampleToBeTested v-if="item.name === '待检测样品'" />
          <SampleToBeReviewed v-if="item.name === '待审核样品'" />
          <ReportSampleToBePrepared v-if="item.name === '未完成报告样品'" />
          <NearlyThirthDays v-if="item.name === '近30天成品合格率/近30天半成品合格率/近30天原材料合格率'" />
          <UnqualifiedSample v-if="item.name === '近30天不合格样品'" />
          <WaitingDetection v-if="item.name === '未来15天待检样品'" />
        </el-col>
      </el-row>
    </div>
    <DrawerSet :drawer="showDrawer" :detail-data="typesettingData" @close="closeDrawer" />
  </div>
</template>

<script>
import { nextTick, reactive, ref, toRefs, computed } from 'vue';
import { getLoginInfo } from '@/utils/auth';
import { formatDate } from '@/utils/formatTime';
import { getNameByid } from '@/utils/common';
import { mapGetters, mapState } from 'vuex';
import { dashboardconfig } from '@/api/dataBoard';
import SampleCompletionNumber from './components/SampleCompletionNumber';
import SubmitLeaderboard from './components/SubmitLeaderboard';
import DrawerSet from './DrawerSet';
import NearlyThirthDays from './components/NearlyThirthDays';
import UnqualifiedSample from './components/UnqualifiedSample';
import WaitingDetection from './components/WaitingDetection';
import SampleToBeTested from './components/ScrollComponent/SampleToBeTested';
import SampleToBeReviewed from './components/ScrollComponent/SampleToBeReviewed';
import ReportSampleToBePrepared from './components/ScrollComponent/ReportSampleToBePrepared';
import ToBeIssued from './components/ScrollComponent/ToBeIssued';
import SvgIcon from '@/components/SvgIcon';
import { getTenantConfig } from '@/utils/auth';
import { checkServerIp, QmsServerIps } from '@/utils/server';
import logoTitle from '@/assets/img/logo-title.svg';
import qmsLogo from '@/assets/img/logo-title-qms.svg';

export default {
  name: 'DataBoard',
  components: {
    SampleCompletionNumber,
    WaitingDetection,
    SvgIcon,
    UnqualifiedSample,
    SubmitLeaderboard,
    DrawerSet,
    NearlyThirthDays,
    SampleToBeTested,
    ToBeIssued,
    SampleToBeReviewed,
    ReportSampleToBePrepared
  },
  setup(props, context) {
    const state = reactive({
      showDrawer: false,
      typesettingData: [],
      digitalClock: '', // 数字时钟
      tenantId: getLoginInfo().tenantId,
      logo_title: getTenantConfig().normalLogo ? getTenantConfig().normalLogo : logoTitle,
      qmsLogo,
      tenantGroupList: JSON.parse(localStorage.getItem('tenantGroup')),
      histogramJson: {}, // 未来15天待见样品预测
      msg: '',
      timer: null, // 定时器
      failList: [], // 不合格样品集合
      failLoading: false,
      dvtime: 10, // 轮训时间
      voiceRef: ref()
    });
    const isQms = computed({
      get: () => checkServerIp(QmsServerIps)
    });
    // 获取看板配置信息
    const getDashboardconfig = () => {
      dashboardconfig().then(res => {
        if (res) {
          const data = res.data.data;
          state.typesettingData = data;
        }
      });
    };
    getDashboardconfig();

    const closeDrawer = isRefresh => {
      if (isRefresh) {
        getDashboardconfig();
      }
      state.showDrawer = false;
    };
    // 数字时钟
    const getDigitalClock = () => {
      const date = new Date(); // 创建时间对象
      let Hours = date.getHours(); // 获取时
      let Min = date.getMinutes(); // 获取秒
      let Sec = date.getSeconds(); // 获取分
      Hours = Hours < 10 ? '0' + Hours : Hours;
      Min = Min < 10 ? '0' + Min : Min;
      Sec = Sec < 10 ? '0' + Sec : Sec;
      state.digitalClock = Hours + '\t:\t' + Min + '\t:\t' + Sec;
    };
    getDigitalClock();
    setInterval(getDigitalClock, 1000);
    // 自动滚动
    // 实现全屏f11显示
    // const handleFullScreen = () => {
    //   const ele = document.documentElement
    //   voiceBroadcast(2)
    //   if (!state.fullscreen) {
    //     if (document.fullscreenElement && document.exitFullscreen) {
    //       document.exitFullscreen()
    //     } else if (document.fullscreenElement && document.webkitCancelFullScreen) {
    //       document.webkitCancelFullScreen()
    //     } else if (document.fullscreenElement && document.mozCancelFullScreen) {
    //       document.mozCancelFullScreen()
    //     } else if (document.fullscreenElement && document.msExitFullscreen) {
    //       document.msExitFullscreen()
    //     }
    //   } else {
    //     if (ele.requestFullscreen) {
    //       ele.requestFullscreen()
    //     } else if (ele.webkitRequestFullScreen) {
    //       ele.webkitRequestFullScreen()
    //     } else if (ele.mozRequestFullScreen) {
    //       ele.mozRequestFullScreen()
    //     } else if (ele.msRequestFullscreen) {
    //       // IE11
    //       ele.msRequestFullscreen()
    //     }
    //   }
    // }
    // window.onresize = () => {
    //   state.fullscreen = !state.fullscreen
    // }
    // 计算百分占比
    const getProportion = (cardinal, comparisonVal) => {
      return (comparisonVal / cardinal) * 100;
    };
    const voiceBroadcast = volume => {
      const localAudio = window.speechSynthesis;
      const audioService = new SpeechSynthesisUtterance();
      const voices = localAudio.getVoices().filter(item => {
        return item.lang === 'zh-CN';
      });
      localAudio.cancel();
      audioService.text = '';
      audioService.text = state.msg || '开启语音';
      audioService.lang = 'zh-CN';
      audioService.volume = 1;
      audioService.pitch = 1.5;
      audioService.rate = 0.8;
      audioService.voice = voices[0];
      if (state.typesettingData.voiceBroadcastFlag) {
        localAudio.speak(audioService);
      }
    };
    const handleVoice = () => {
      nextTick(() => {
        voiceBroadcast(2);
      });
    };
    return {
      ...toRefs(state),
      isQms,
      getDashboardconfig,
      closeDrawer,
      getNameByid,
      voiceBroadcast,
      handleVoice,
      getProportion,
      formatDate
    };
  },
  computed: {
    ...mapGetters(['sidebar', 'tenantGroup', 'webSocketMsg']),
    ...mapState(['tenantInfo'])
  },
  watch: {
    webSocketMsg(msg) {
      const vm = this;
      if (msg.toString().startsWith('{"header":{"cmd":1}')) {
        const data = JSON.parse(msg).body.resultList;
        if (data.length > 0) {
          vm.msg = data.join('。');
          vm.handleVoice();
        }
      }
    }
  }
};
</script>
<style lang="scss" scoped>
@import 'data-board.scss';
</style>
