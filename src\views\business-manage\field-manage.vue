<template>
  <!-- 字典维护 -->
  <ListLayout
    :has-search-panel="false"
    :has-quick-query="false"
    :has-button-group="getPermissionBtn('addDictionary') ? true : false"
  >
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="code">
          <el-input
            v-model="formInline.condition"
            v-trim
            v-focus
            placeholder="请输入视图名称/编码"
            class="ipt-360"
            prefix-icon="el-icon-search"
            size="large"
            clearable
            @keyup.enter="getList"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="getList">查询</el-button>
          <el-button size="large" @click="reset"> 重置 </el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        type="primary"
        icon="el-icon-plus"
        size="large"
        @click="handleAddFixedView"
        @keyup.prevent
        @keydown.enter.prevent
        >新增固定视图</el-button
      >
    </template>
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      size="medium"
      fit
      border
      style="width: auto"
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
    >
      <el-table-column label="表格编码" prop="bindingMenu" />
      <el-table-column label="视图名称" prop="viewName" />
      <el-table-column label="是否为固定视图" prop="isFixedView">
        <template #default="{ row }">{{ row.isFixedView === 1 ? '是' : '否' }}</template>
      </el-table-column>
      <el-table-column label="是否为默认展示视图" prop="isDefault">
        <template #default="{ row }">{{ row.isDefault === 1 ? '是' : '否' }}</template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" />
      <el-table-column label="更新时间" prop="updateTime" />
      <el-table-column label="操作" :width="180">
        <template #default="{ row }">
          <span class="blue-color" @click="handleEditView(row)">编辑</span>
          <span v-if="row.isFixedView" class="blue-color" @click="handleAddCustomView(row)">新增自定义视图</span>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-show="total > 0"
      :page="pagination.page"
      :limit="pagination.limit"
      :total="total"
      @pagination="getList"
    />
    <template #other>
      <table-column-editer
        v-model="showDrawer"
        :is-new-fixed-view="isNewFixedView"
        :is-new-custom-view="isNewCustomView"
        :user-id="userId"
        :table-code="tableCode"
        :view="currentView"
        @update:view="updateView"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, toRefs, onMounted } from 'vue';
import Pagination from '@/components/Pagination';
import ListLayout from '@/components/ListLayout';
import TableColumnEditer from '@/components/TableColumnView/TableColumnEditer';
import { getViewListByType } from '@/api/tableView';
import { getLoginInfo } from '@/utils/auth';
import { getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';

export default {
  name: 'FieldManage',
  components: { ListLayout, Pagination, TableColumnEditer },
  setup() {
    const state = reactive({
      userId: getLoginInfo().accountId,
      formInline: {
        condition: '' // 搜索的关键字
      },
      tableData: [
        {
          id: '1688460056717529134',
          tenantId: '***********',
          userId: '1523582176947060773',
          bindingMenu: 'OutsourcingManagement',
          viewName: '固定视图',
          isFixedView: 1,
          isDefault: 1,
          createBy: '1523582176947060773',
          createTime: '2023-08-07 16:00:22',
          updateBy: '1523582176947060773',
          updateTime: '2023-08-07 16:25:40'
        },
        {
          id: '1688460056717529134',
          tenantId: '***********',
          userId: '1523582176947060773',
          bindingMenu: 'OutsourcingManagement',
          viewName: 'Test',
          isFixedView: 0,
          isDefault: 0,
          createBy: '1523582176947060773',
          createTime: '2023-08-07 16:00:22',
          updateBy: '1523582176947060773',
          updateTime: '2023-08-07 16:25:40'
        }
      ],
      total: 1,
      tableLoading: false,
      pagination: {
        limit: 20,
        page: 1
      },
      showDrawer: false,
      isNewFixedView: false,
      isNewCustomView: false,
      tableCode: 'OutsourcingManagement',
      currentView: {}
    });

    const getList = query => {
      const params = { isFixedView: '0' };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
      } else {
        params.page = state.pagination.page.toString();
        params.limit = state.pagination.limit.toString();
      }
      state.tableLoading = true;
      getViewListByType(params).then(res => {
        console.log('res', res);
        state.tableLoading = false;
        if (res) {
          state.total = res.data.data.totalCount;
          state.tableData = res.data.data.list;
        }
      });
    };
    const reset = () => {};
    const handleAddFixedView = () => {
      state.showDrawer = true;
      state.isNewFixedView = true;
      state.isNewCustomView = false;
    };
    const handleEditView = row => {
      state.showDrawer = true;
      state.isNewFixedView = false;
      state.isNewCustomView = false;
      state.currentView = row;
    };
    const handleAddCustomView = row => {
      state.showDrawer = true;
      state.isNewFixedView = false;
      state.isNewCustomView = true;
      state.currentView = row;
    };
    const updateView = () => {};

    onMounted(() => {
      getList();
    });

    return {
      ...toRefs(state),
      getPermissionBtn,
      getList,
      reset,
      handleAddFixedView,
      handleEditView,
      handleAddCustomView,
      updateView,
      drageHeader
    };
  }
};
</script>
<style lang="scss" scoped></style>
