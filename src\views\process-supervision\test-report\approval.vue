<template>
  <el-drawer
    :model-value="showDialog"
    size="600px"
    :before-close="close"
    destroy-on-close
    modal-class="report-approval"
  >
    <template #title>
      <span class="drawer-title">审批流程</span>
    </template>
    <!-- 审批流程-可编辑 -->
    <div v-if="detailEdit === false">
      <el-steps direction="vertical" :space="100" :active="1" style="margin: 0 15px">
        <el-step v-for="(item, index) in selectApprovalData" :key="index" :status="filterStatus(item, index)">
          <template #title>
            <span class="name-title">{{ item.name }}</span>
            <span class="time-title">{{ item.endTime }}</span>
          </template>
          <template #description>
            <div v-if="item.name.indexOf('提交') === -1" :v-loading="dialogLoading" class="step-desc">
              <div class="name">
                {{ getNameByid(item.assignee) || item.assignee
                }}<span>{{ filterIsAssent(item.isAssent, item.name) }}</span>
              </div>
              <div v-if="item.endTime === '' && item.name !== '发送'" class="approva-radio">
                <div class="title">结论:</div>
                <el-radio-group v-model="formData.isAssent" @change="changeRadio">
                  <el-radio :label="0">拒绝</el-radio>
                  <el-radio :label="1">同意</el-radio>
                </el-radio-group>
              </div>
              <div v-if="item.name === '发送'" class="send-date">
                <div class="lable-title">发送日期:</div>
                <div class="lable-contant">
                  <el-date-picker
                    v-model="formData.sendDate"
                    type="date"
                    clearable
                    placeholder="选择日期"
                    style="width: 100%"
                    @change="changeSendTime(formData.sendDate)"
                  />
                </div>
              </div>
              <div v-if="item.endTime && item.opinion" class="desc">{{ item.opinion }}</div>
              <el-input v-if="item.endTime === ''" v-model="formData.opinion" size="small" type="textarea" :rows="2" />
              <div v-if="item.endTime === '' && item.name !== lastName && formData.isAssent === 1" class="next-user">
                <div>{{ lableName }}</div>
                <el-select
                  v-model="formData.assignee"
                  class="owner-select"
                  placeholder="请选择"
                  clearable
                  filterable
                  size="small"
                  style="width: 100%"
                >
                  <el-option v-for="user in userOptions" :key="user.id" :label="user.nickname" :value="user.id" />
                </el-select>
                <el-checkbox
                  v-model="formData.isDefaultUser"
                  :true-label="1"
                  :false-label="0"
                  label="下次自动选择当前选择人"
                />
              </div>
            </div>
            <div v-else class="step-desc">
              <div class="name">{{ getNameByid(item.startUser) || item.startUser }}</div>
            </div>
          </template>
        </el-step>
      </el-steps>
      <div class="drawer-fotter">
        <el-button :loading="dialogLoading" @click="close">取 消</el-button>
        <el-button :loading="dialogLoading" type="primary" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent
          >确定</el-button
        >
      </div>
    </div>
    <!-- 显示审批流程详情-只读 -->
    <div v-else :v-loading="dialogLoading">
      <el-steps direction="vertical" :space="100" :active="1" style="margin: 0 15px">
        <el-step v-for="(item, index) in selectApprovalData" :key="item" :status="filterStatus(item, index)">
          <template #title>
            <span class="name-title">{{ item.name }}</span>
            <span class="time-title">{{ item.endTime }}</span>
          </template>
          <template #description>
            <div class="step-desc">
              <div class="name">
                {{ getNameByid(item.assignee) || item.assignee
                }}<span>{{ filterIsAssent(item.isAssent, item.name) }}</span>
              </div>
              <div v-if="item.name === '发送'">发送日期: {{ item.sendDate }}</div>
              <div v-if="item.opinion" class="desc">{{ item.opinion }}</div>
            </div>
          </template>
        </el-step>
      </el-steps>
    </div>
  </el-drawer>
</template>

<script>
import { reactive, toRefs, watch, getCurrentInstance } from 'vue';
import { getNameByid } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { processExecute } from '@/api/testReport';
import { ElMessage } from 'element-plus';
import { addByTemp } from '@/api/messageAgent';
import { getLoginInfo } from '@/utils/auth';
// import _ from 'lodash'
import { getReportUserList } from '@/api/user';

export default {
  name: 'Approval',
  components: {},
  props: {
    show: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Boolean,
      default: false
    },
    approvalList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    const { appContext } = getCurrentInstance();
    const bus = appContext.config.globalProperties.bus;
    const datas = reactive({
      lastName: '',
      detailEdit: false,
      dialogLoading: false,
      currentAccountId: getLoginInfo().accountId,
      showDialog: props.show,
      selectApprovalData: [],
      oldApprovalData: [],
      lableName: '签字人',
      formData: {
        assignee: '',
        businessKey: '',
        isAssent: 1,
        opinion: '',
        processInstanceId: '',
        sendDate: '',
        startUser: ''
      },
      userOptions: [],
      sampleIds: [],
      secSampleNums: [],
      reportNos: [],
      rejectStepNames: [],
      currentData: {},
      examineStatus: 0
    });

    // 获取具有检测报告权限的人员列表
    getReportUserList({}).then(res => {
      if (res.data.code === 200) {
        datas.userOptions = res.data.data;
      }
    });

    watch(
      () => props.show,
      newValue => {
        datas.showDialog = newValue;
        datas.sampleIds = [];
        datas.secSampleNums = [];
        datas.reportNos = [];
        if (newValue) {
          if (props.approvalList.length > 0) {
            datas.lastName = props.approvalList[props.approvalList.length - 1];
          }
          datas.currentData = props.data.currentData;
          datas.detailEdit = props.detail;
          if (!datas.detailEdit) {
            datas.selectApprovalData = props.data.data.filter(item => {
              return item.endTime || (!item.endTime && item.assignee === getLoginInfo().accountId);
            });
          } else {
            datas.selectApprovalData = props.data.data;
          }
          datas.formData.businessKey = props.data.ids.businessKey;
          datas.formData.processInstanceId = props.data.ids.processInstanceId;
          datas.formData.assignee = props.data.currentData?.defaultSignatureUserId || props.data.ids.currentAccountId;
          datas.formData.isDefaultUser = props.data.currentData?.defaultSignatureUserId ? 1 : 0;
          datas.formData.opinion = '';
          datas.formData.sendDate = '';
          datas.formData.startUser = props.data.data[0].startUser;
          datas.oldApprovalData = JSON.parse(JSON.stringify(props.data));
          var applength = datas.selectApprovalData.length - 1;
          switch (datas.selectApprovalData[applength]?.name) {
            case '审核':
              datas.lableName = '下一步签字人';
              break;
            case '签字':
              datas.lableName = '下一步盖章人';
              break;
            case '盖章':
              datas.lableName = '下一步发送人';
              break;
          }
          if (datas.currentData) {
            datas.examineStatus = datas.currentData.examineStatus;
            datas.sampleIds.push(datas.currentData.sampleId);
            datas.secSampleNums.push(datas.currentData.secSampleNum);
            datas.reportNos.push(datas.currentData.reportNo);
          }
        }
      },
      { deep: true }
    );

    // 确定选择
    const dialogSuccess = () => {
      datas.dialogLoading = true;
      processExecute(datas.formData).then(res => {
        datas.dialogLoading = false;
        if (res !== false) {
          if (datas.formData.isAssent === 1) {
            switch (datas.examineStatus) {
              case 2:
                addMsg('M013');
                break;
              case 3:
                addMsg('M014');
                break;
              case 4:
                addMsg('M015');
                break;
              case 5:
                addMsg('M016');
                break;
            }
          } else {
            datas.rejectStepNames = [];
            switch (datas.examineStatus) {
              case 2:
                datas.rejectStepNames.push('审核');
                break;
              case 3:
                datas.rejectStepNames.push('签字');
                break;
              case 4:
                datas.rejectStepNames.push('盖章');
                break;
              case 5:
                datas.rejectStepNames.push('发送');
                break;
            }
            addMsg('M011');
          }
          ElMessage.success('审批成功！');
          datas.showDialog = false;
          bus.$emit('reloadReportList', true);
          context.emit('close', false);
        }
      });
    };
    // 添加消息待办
    const addMsg = eventCode => {
      // 添加消息待办
      const params = {
        eventCode: eventCode,
        receiverType: '1',
        senderName: getNameByid(datas.currentAccountId),
        receiverIds: datas.formData.assignee,
        receiverNames: getNameByid(datas.formData.assignee),
        todoStartTime: formatDate(new Date()),
        c_ids: datas.reportNos.join(','),
        c_b_samplesIdArray: datas.sampleIds.join(','),
        c_b_sampleNoArray: datas.secSampleNums.join(','),
        c_b_reportNoArray: datas.reportNos.join(','),
        c_b_rejectStepNameArray: datas.rejectStepNames.join(',')
      };
      if (eventCode === 'M011') {
        params.receiverIds = datas.formData.startUser;
        params.receiverNames = getNameByid(datas.formData.startUser);
      }
      if (eventCode === 'M016') {
        // params.eventCode = 'M017'
        // params.receiverIds = datas.formData.assignee
        // params.receiverNames = getNameByid(datas.formData.assignee)
        // addByTemp(params).then(res => {
        //   if (res !== false) {
        //     console.log(res.data)
        //     datas.rejectStepNames = []
        //   }
        // })
        params.eventCode = 'M016';
        params.receiverIds = datas.currentData.reportFormationByUserId;
        params.receiverNames = getNameByid(datas.currentData.reportFormationByUserId);
        addByTemp(params).then(res => {
          if (res !== false) {
            datas.rejectStepNames = [];
          }
        });
      } else {
        addByTemp(params).then(res => {
          if (res !== false) {
            datas.rejectStepNames = [];
          }
        });
      }
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };
    // 过滤是否同意
    const filterIsAssent = (isAssent, name) => {
      if (isAssent === 0) {
        return '(拒绝)';
      } else if (isAssent === 1) {
        return '(同意)';
      }
      return '(' + name + '中)';
    };
    // 过滤状态
    const filterStatus = (item, index) => {
      if (index === datas.selectApprovalData.length - 1 && !item.endTime) {
        return 'process';
      } else {
        return 'finish';
      }
    };
    // 选择发送时间
    const changeSendTime = date => {
      datas.formData.sendDate = formatDate(date);
    };
    // 判定是否同意
    const changeRadio = value => {};

    return {
      ...toRefs(datas),
      close,
      getNameByid,
      dialogSuccess,
      formatDate,
      filterIsAssent,
      changeSendTime,
      filterStatus,
      changeRadio,
      addMsg
    };
  },
  created() {
    // this.getSampleOrderList()
  },
  methods: {
    getSampleOrderList(value) {}
  }
};
</script>
<style lang="scss" scoped>
.report-approval {
  .drawer-title {
    color: #303133;
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
  }
  .el-steps {
    .name-title {
      color: #303339;
      font-size: 14px;
      font-style: normal;
      font-weight: normal;
    }
    .time-title {
      color: #999999;
      font-size: 12px;
      margin: 0 10px;
      float: right;
      font-weight: 500;
    }
    .step-des {
      color: #606266;
      font-size: 12px;
    }
    .approval-from-item {
      :deep(.el-form-item__content) {
        padding-left: 10px;
      }
    }

    :deep(.el-step .el-step__head) {
      margin-top: 15px;
      .el-step__line {
        margin: 10px 0 -15px;
      }
      .el-step__line-inner {
        border: none;
      }
      &.is-finish {
        color: $tes-green;
      }
    }
    :deep(.el-step .el-step__main) {
      .el-step__title {
        padding: 8px 0 2px;
      }
      .el-step__description {
        padding-right: 0px;
        .step-desc {
          .name {
            color: #999999;
            span {
              margin-left: 5px;
            }
          }
          .desc {
            color: #999999;
            background: #f6f6f6;
            border-radius: 3px;
            padding: 5px 10px;
            max-width: 520px;
          }
          .el-textarea {
            width: 100%;
            margin-top: 10px;
          }
          .approva-radio {
            font-size: 14px;
            margin-top: 10px;
            display: flex;
            align-items: center;
            .title {
              float: left;
              margin-right: 10px;
            }
          }
          .next-user {
            margin-top: 10px;
            height: 78px;
          }
          .send-date {
            margin: 10px 0px;
          }
        }
      }
    }
    :deep(.el-step__icon.is-text) {
      width: 12px;
      height: 12px;
      border: 3px solid;
      .el-step__icon-inner {
        display: none;
      }
    }
  }
  .el-steps--vertical {
    height: calc(100vh - 9.5rem);
    overflow-y: auto;
  }
  .drawer-fotter {
    position: absolute;
    bottom: 0;
    width: 100%;
    // margin: 10px 0px 20px 0px;
    height: 50px;
    line-height: 50px;
    text-align: right;
    padding-right: 20px;
  }
}
</style>
