<template>
  <!-- 待下达（待认领）样品 -->
  <div class="centerTop">
    <span> 待下达（待认领）样品</span>
    <div class="number">
      <span>{{ total }}</span
      >个
    </div>
  </div>
  <div class="centerBox">
    <div class="boxTable">
      <el-row>
        <el-col :span="2" />
        <el-col :span="7">样品编号</el-col>
        <el-col :span="5">申请人</el-col>
        <el-col :span="10">对象位置</el-col>
      </el-row>
      <ScrollList :type="'1'" @pageTotal="getTotal" />
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, watch } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getNameByid } from '@/utils/common';
import ScrollList from './ScrollList';

export default {
  name: 'ToBeIssued',
  components: { ScrollList },
  props: {},
  setup(props, context) {
    const state = reactive({
      total: 0
    });
    watch(props, newValue => {
      state.pageType = props.type;
    });
    const getTotal = val => {
      state.total = val.total;
    };
    return {
      ...toRefs(state),
      getTotal,
      getNameByid,
      formatDate
    };
  }
};
</script>
<style lang="scss" scoped>
@import '../../data-board.scss';
.centerTop {
  background: url(../../../../../assets/img/dataBoard/center-left-top.png) no-repeat;
  background-size: 100% 100%;
  .number {
    background: url(../../../../../assets/img/dataBoard/center-left-top2.png) no-repeat;
    background-size: cover;
  }
}
</style>
