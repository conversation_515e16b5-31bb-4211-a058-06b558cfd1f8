<!-- AI助手 -->
<template>
  <el-drawer
    v-model="dialogShow"
    :with-header="false"
    :before-close="handleClose"
    custom-class="drawerServe"
    :show-close="false"
    @opened="handleOpened"
  >
    <iframe class="ai-container" :src="aiChatUrl" allow="microphone" />
  </el-drawer>
</template>
<script>
import { reactive, toRefs, watch, onMounted, onBeforeUnmount } from 'vue';

export default {
  name: 'DialogIntelligentService',
  props: {
    dialogVisiable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      dialogShow: false,
      aiChatUrl: 'http://**************:30000'
    });

    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisiable;
    });

    onMounted(() => {
      window.addEventListener('message', onListenAIChatClose, false);
    });

    onBeforeUnmount(() => {
      window.removeEventListener('message', onListenAIChatClose, false);
    });

    const onListenAIChatClose = event => {
      if (event.origin === state.aiChatUrl && event.data === 'exit') {
        handleClose();
      }
    };

    const handleOpened = () => {
      if (state.textarea) {
        state.textarea.focus();
      }
    };

    const handleClose = () => {
      console.log('点击');
      state.dialogShow = false;
      context.emit('closeDialog');
    };

    return {
      ...toRefs(state),
      handleClose,
      handleOpened
    };
  }
};
</script>

<style lang="scss" scoped>
.ai-container {
  border: 0;
  width: 100%;
  height: 100%;
}
</style>
<style lang="scss">
.el-drawer.drawerServe {
  width: 100vw !important;
  background-color: transparent !important;
  box-shadow: none !important;

  .el-drawer__body {
    padding: 0;
  }
}
</style>
