<template>
  <!-- 模板设计详情 -->
  <div class="h-full">
    <FlashTableDesign v-if="isDesign" :url-type="pageType" :option="option" @receive="handleReceive" />
    <FlashTableViewer v-else :option="option" />
  </div>
</template>

<script>
// Basic
import { reactive, toRefs, ref, onMounted } from 'vue';
import { useStore } from 'vuex';
import router from '@/router/index.js';
// import { ElMessage } from 'element-plus';
import { useRoute } from 'vue-router';

// Utils
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';

// components
import FlashTableDesign from '@/components/FlashTable/design.vue';
import FlashTableViewer from '@/components/FlashTable/viewer.vue';
// Api
import { saveOrUpdate, designtemplateInfo } from '@/api/templateDesign';

// Data
import { COMMAND } from '@/data/flashTable';

export default {
  name: 'TemplateDesignDetail',
  components: { FlashTableDesign, FlashTableViewer },
  setup() {
    const store = useStore().state;
    const route = useRoute();
    const state = reactive({
      userOptions: store.common.nameList,
      formRef: ref(),
      detailInfo: {},
      option: {},
      pageType: route.query.type,
      isDesign: false,
      detailLoading: false
    });
    onMounted(() => {
      initDetailPage();
    });
    // 获取详情数据
    const initDetailPage = async () => {
      state.isDesign = state.pageType == 'update' || state.pageType == 'create';
      if (route.query.id) {
        // 编辑和查看
        state.detailLoading = true;
        const reponse = await designtemplateInfo(route.query.id).finally((state.detailLoading = false));
        if (reponse) {
          state.detailInfo = reponse.data.data;
          state.option = { name: state.detailInfo.name, id: state.detailInfo.id };
        }
      } else {
        // 新增页面
        state.detailLoading = true;
        state.option = { name: state.detailInfo.name, id: state.detailInfo.id };
      }
    };
    const handleReceive = res => {
      if (res.command == COMMAND.SAVE_TEMPLATE || res.command == COMMAND.PUBLISH_TEMPLATE) {
        state.detailInfo.templateVersion = res.data.data.version;
        onSave(res.command == COMMAND.SAVE_TEMPLATE ? '保存' : '发布');
      } else if (res.command == COMMAND.TEMPLATE_LOADED) {
        state.detailLoading = false;
      }
    };
    // 保存
    const onSave = async type => {
      state.detailLoading = true;
      const reponse = await saveOrUpdate(state.detailInfo).finally((state.detailLoading = false));
      if (reponse) {
        if (type == '发布') {
          router.go(-1);
        }
      }
    };

    return {
      ...toRefs(state),
      initDetailPage,
      onSave,
      formatDate,
      getNameByid,
      handleReceive,
      getPermissionBtn
    };
  }
};
</script>
<style lang="scss" scoped></style>
