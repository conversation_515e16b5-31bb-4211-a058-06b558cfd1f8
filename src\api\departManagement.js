import request from '@/utils/request';
// 部门管理左侧树列表
export function getDepartTree(data) {
  return request({
    url: '/api-user/user/sysdepartment/listTree',
    method: 'post',
    data
  });
}
// 新增部门管理树
export function addDepartTree(data) {
  return request({
    url: '/api-user/user/sysdepartment/save',
    method: 'post',
    data
  });
}
// 编辑部门管理树
export function editDepartTree(data) {
  return request({
    url: '/api-user/user/sysdepartment/update',
    method: 'post',
    data
  });
}
// 删除部门树
export function deleteDepartmentTree(id) {
  return request({
    url: `/api-user/user/sysdepartment/delete/${id}`,
    method: 'get'
  });
}
// 部门员工列表
export function getMemberTable(data) {
  return request({
    url: '/api-user/user/sysemployee/list',
    method: 'post',
    data
  });
}
// 新增员工
export function saveEmployees(data) {
  return request({
    url: '/api-user/user/sysemployee/save',
    method: 'post',
    data
  });
}
// 编辑员工
export function updateEmployees(data) {
  return request({
    url: '/api-user/user/sysemployee/update',
    method: 'post',
    data
  });
}
// 查询员工个人信息
export function getMemberInfo(id) {
  return request({
    url: `/api-user/user/sysemployee/info/${id}`,
    method: 'get'
  });
}
// 检查用户名
export function checkUserName(data) {
  return request({
    url: '/api-user/user/sysemployee/checkusername',
    method: 'post',
    data
  });
}
// 检查mobile的全局唯一性
export function checkMobileURL(data) {
  return request({
    url: '/api-user/user/sysemployee/checkmobile',
    method: 'post',
    data
  });
}
// 检查email的全局唯一性
export function checkEmailURL(data) {
  return request({
    url: '/api-user/user/sysemployee/checkemail',
    method: 'post',
    data
  });
}
// 员工密码重置
export function resetUserPassword(id) {
  return request({
    url: `/api-user/user/sysuser/admin/password/${id}`,
    method: 'get'
  });
}
// 判断部门是否可以停用
export function isDepartMentClose(id) {
  return request({
    url: `/api-user/user/sysdepartment/candelete/${id}`,
    method: 'post'
  });
}

// 部门树排序
export function updateOrderTree(data) {
  return request({
    url: `/api-user/user/sysdepartment/updateOrder`,
    method: 'post',
    data
  });
}
// 邀请成员加入部门
export function alreadyExist(data) {
  return request({
    url: `/api-user/user/sysemployee/invite`,
    method: 'post',
    data
  });
}
// 获取关联项目
export function getUserItemApi(employeeId) {
  return request({
    url: `/api-capabilitystd/capability/capabilityemployee/findByEmployeeId/${employeeId}`,
    method: 'get'
  });
}
// 根据id删除附件
export function deleteFile(field) {
  return request({
    url: `/api-user/user/attachment/delById/${field}`,
    method: 'delete'
  });
}

// 根据id下载附件
export function downloadById(id) {
  return request({
    url: `/api-user/user/attachment/downloadById/${id}`,
    method: 'get',
    responseType: 'blob'
  });
}
// 根据人员id查询培训信息
export function findByUserId(userId) {
  return request({
    url: `/api-user/user/employeetrain/findByUserId/${userId}`,
    method: 'get'
  });
}
