<template>
  <div class="review-info">
    <div class="review-info-header">
      <el-row>
        <el-col :span="12"><div class="title">技术评审</div></el-col>
      </el-row>
    </div>
    <div class="content">
      <el-steps
        direction="vertical"
        :space="100"
        :active="reviewListData[reviewListData.length - 1]?.endTime ? reviewListData.length : reviewListData.length - 1"
        style="margin: 0 15px"
      >
        <el-step v-for="item in reviewListData" :key="item.name">
          <template #title>
            <span class="name-title">{{ item.name === '提交流程' ? '委托提交' : item.name }}</span>
            <span class="time-title">{{ formatDateTime(item.endTime) }}</span>
            <el-button
              v-if="
                currentTaskStatus === 1 &&
                getPermissionBtn('TaskReviewBtn') &&
                item.name === '合同评审' &&
                item.endTime === ''
              "
              class="review-btn floatR"
              type="primary"
              size="small"
              icon="el-icon-plus"
              @click="addReview()"
              @keyup.prevent
              @keydown.enter.prevent
              >合同评审</el-button
            >
            <el-button
              v-if="
                currentTaskStatus === 2 &&
                getPermissionBtn('TaskReviewBtn') &&
                item.name === '委托确认' &&
                item.endTime === ''
              "
              class="review-btn floatR"
              type="primary"
              size="small"
              icon="el-icon-plus"
              @click="addCommission()"
              @keyup.prevent
              @keydown.enter.prevent
              >委托确认</el-button
            >
            <el-upload
              ref="uploadRef"
              class="displayInBlock floatR"
              :action="fileAction"
              :headers="headerconfig"
              multiple
              :before-upload="beforeUpload"
              :data="formDataCommoission"
              :on-success="handleFileSuccess"
              :file-list="fileList"
            >
              <el-button
                v-if="
                  currentTaskStatus === 2 &&
                  getPermissionBtn('TaskReviewBtn') &&
                  item.name === '委托确认' &&
                  item.endTime === ''
                "
                class="review-btn floatR"
                type="info"
                size="small"
                icon="el-icon-upload"
                @keyup.prevent
                @keydown.enter.prevent
                >附件上传</el-button
              >
              <span
                v-if="
                  currentTaskStatus === 2 &&
                  getPermissionBtn('TaskReviewBtn') &&
                  item.name === '委托确认' &&
                  item.endTime === ''
                "
                class="uploadTitle"
              >
                文件大小不超过20M，支持.doc，.docx，.xls，.xlsx，.pdf，.jpg，.png，.jpeg文件扩展名
              </span>
            </el-upload>
          </template>
          <template #description>
            <div v-if="item.endTime" class="step-desc">
              <el-form :model="item" label-width="110px">
                <el-row v-if="item.name === '提交流程' || item.name === '重新提交'" :gutter="20">
                  <el-col :span="6">
                    <el-form-item label="提交人：">
                      <UserTag :name="getNameByid(item.assignee) || '--'" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-else-if="item.name === '委托确认'" :gutter="20">
                  <el-col :span="6">
                    <el-form-item :label="`${item.name}：`">
                      <UserTag :name="getNameByid(item.assignee) || '--'" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="确认日期：">
                      {{ item.processParameter.confirmDate }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="确认人：">
                      <UserTag :name="getNameByid(item.processParameter.confirmPerson) || '--'" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="是否已到样：">
                      <el-tag size="small" effect="dark" :type="Number(item.isArrival) === 1 ? 'success' : 'warning'">
                        {{ Number(item.processParameter.isArrival) === 1 ? '是' : '否' }}
                      </el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item v-if="item.processParameter.isArrival" label="到样日期：">
                      {{ item.processParameter.confirmDate }}
                    </el-form-item>
                    <el-form-item v-if="!item.processParameter.isArrival" label="预计到样日期：">
                      {{ item.processParameter.preArrivalDate }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="审批结果：">
                      <el-tag
                        size="small"
                        effect="dark"
                        :type="Number(item.processParameter.isAssent) === 1 ? 'success' : 'danger'"
                      >
                        {{ Number(item.processParameter.isAssent) === 1 ? '通过' : '不通过' }}
                      </el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="18" />
                  <el-col :span="24">
                    <el-form-item label="备注：">
                      <div>{{ item.processParameter.remark }}</div>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-else :gutter="20">
                  <el-col :span="6">
                    <el-form-item label="合同评审：">
                      <UserTag :name="getNameByid(item.assignee) || '--'" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="人员能力：">
                      <el-tag
                        size="small"
                        effect="dark"
                        :type="Number(item.processParameter.staffAbility) === 1 ? 'success' : 'warning'"
                      >
                        {{ Number(item.processParameter.staffAbility) === 1 ? '满足' : '不满足' }}
                      </el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="仪器设备：">
                      <el-tag
                        size="small"
                        effect="dark"
                        :type="Number(item.processParameter.devAbility) === 1 ? 'success' : 'warning'"
                      >
                        {{ Number(item.processParameter.devAbility) === 1 ? '满足' : '不满足' }}
                      </el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="环境设备：">
                      <el-tag
                        size="small"
                        effect="dark"
                        :type="Number(item.processParameter.envAbility) === 1 ? 'success' : 'warning'"
                      >
                        {{ Number(item.processParameter.envAbility) === 1 ? '满足' : '不满足' }}
                      </el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="审批结果：">
                      <el-tag
                        size="small"
                        effect="dark"
                        :type="Number(item.processParameter.isAssent) === 1 ? 'success' : 'danger'"
                      >
                        {{ Number(item.processParameter.isAssent) === 1 ? '通过' : '不通过' }}
                      </el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="评审意见：">
                      <div>{{ item.processParameter.opinion }}</div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            <div v-else class="step-desc">
              <div class="name">
                {{ getNameByid(item.approveBy) || item.approveBy }}
              </div>
            </div>
          </template>
        </el-step>
      </el-steps>
      <el-empty v-if="!reviewListData || reviewListData.length === 0" :image="emptyImg" description="暂无数据" />
    </div>
    <div class="other">
      <el-dialog v-model="dialogCommission" title="委托确认" width="40%" :close-on-click-modal="false">
        <el-form
          v-if="dialogCommission"
          ref="formCommissionRef"
          :model="formDataCommoission"
          class="formDataSample"
          label-position="right"
          label-width="115px"
          size="small"
        >
          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item
                label="确认日期："
                prop="confirmDate"
                :rules="{ required: true, message: '请选择确认日期', trigger: 'change' }"
              >
                <el-date-picker
                  v-model="formDataCommoission.confirmDate"
                  type="date"
                  placeholder="请选择确认日期"
                  @change="val => handleChangeDate(val, 'confirmDate')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="确认人："
                prop="confirmPerson"
                :rules="{ required: true, message: '请选择确认人', trigger: 'change' }"
              >
                <el-select
                  v-model="formDataCommoission.confirmPerson"
                  placeholder="请选择确认人"
                  clearable
                  filterable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in commissionPerson"
                    :key="item.userId"
                    :label="item.nickname"
                    :value="item.userId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否已到样：" prop="isArrival">
                <el-radio-group v-model="formDataCommoission.isArrival" @change="handleChangeRadio">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-if="formDataCommoission.isArrival" :span="12">
              <el-form-item
                label="到样日期："
                prop="arrivalDate"
                :rules="{ required: true, message: '请选择到样日期', trigger: 'change' }"
              >
                <el-date-picker
                  v-model="formDataCommoission.arrivalDate"
                  type="date"
                  placeholder="请选择到样日期"
                  style="width: 100%"
                  @change="val => handleChangeDate(val, 'arrivalDate')"
                />
              </el-form-item>
            </el-col>
            <el-col v-if="formDataCommoission.isArrival === 0" :span="12">
              <el-form-item
                label="预计到样日期："
                prop="preArrivalDate"
                :rules="{ required: true, message: '请选择预计到样日期', trigger: 'change' }"
              >
                <el-date-picker
                  v-model="formDataCommoission.preArrivalDate"
                  type="date"
                  placeholder="请选择预计到样日期"
                  style="width: 100%"
                  @change="val => handleChangeDate(val, 'preArrivalDate')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="是否通过：" prop="isAssent">
                <el-radio-group
                  id="review-result"
                  v-model="formDataCommoission.isAssent"
                  text-color="#ffffff"
                  result-pass
                >
                  <div id="flex-group">
                    <div class="flex-content">
                      <el-radio id="review-pass" :label="1" size="large" border>通过</el-radio>
                    </div>
                    <div id="flex-space" />
                    <div class="flex-content">
                      <el-radio id="review-refuse" :label="0" border size="large">不通过</el-radio>
                    </div>
                  </div>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注：" prop="remark">
                <el-input v-if="!isCheck" v-model="formDataCommoission.remark" type="textarea" :rows="2" size="small" />
                <span v-else>
                  {{ formDataCommoission.remark || '--' }}
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogCommission = false">取消</el-button>
            <el-button type="primary" :loading="submitFeeLoading" @click="submitCommission"> 确认 </el-button>
          </div>
        </template>
      </el-dialog>
      <el-dialog v-model="showFeeDialog" :title="feeTitle" width="40%">
        <template #default>
          <el-form
            ref="formRef"
            :model="currentFeeInfo"
            class="formDataSample"
            label-position="right"
            label-width="110px"
          >
            <el-row :gutter="40">
              <el-col :span="12">
                <el-form-item label="人员能力：" prop="staffAbility">
                  <el-radio-group v-model="currentFeeInfo.staffAbility">
                    <el-radio :label="1" size="large">满足</el-radio>
                    <el-radio :label="0" size="large">不满足</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="仪器设备：" prop="devAbility">
                  <el-radio-group v-model="currentFeeInfo.devAbility">
                    <el-radio :label="1" size="large">满足</el-radio>
                    <el-radio :label="0" size="large">不满足</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="环境设备：" prop="envAbility">
                  <el-radio-group v-model="currentFeeInfo.envAbility">
                    <el-radio :label="1" size="large">满足</el-radio>
                    <el-radio :label="0" size="large">不满足</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12" />
              <el-col :span="24">
                <el-form-item label="是否通过：" prop="approveResult">
                  <el-radio-group
                    id="review-result"
                    v-model="currentFeeInfo.approveResult"
                    text-color="#ffffff"
                    result-pass
                  >
                    <div id="flex-group">
                      <div class="flex-content">
                        <el-radio id="review-pass" :label="1" size="large" border>通过</el-radio>
                      </div>
                      <div id="flex-space" />
                      <div class="flex-content">
                        <el-radio id="review-refuse" :label="0" border size="large">不通过</el-radio>
                      </div>
                    </div>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="评审意见：" prop="approveDetail">
                  <el-input
                    v-if="!isCheck"
                    v-model="currentFeeInfo.approveDetail"
                    type="textarea"
                    :rows="2"
                    size="small"
                  />
                  <span v-else>
                    {{ currentFeeInfo.approveDetail || '--' }}
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="showFeeDialog = false">取消</el-button>
            <el-button type="primary" :loading="submitFeeLoading" @click="submitFeeData"> 确认 </el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch, computed, getCurrentInstance } from 'vue';
// import { useStore } from 'vuex'
import { getLoginInfo } from '@/utils/auth';
// import router from '@/router/index.js'
import { useRoute } from 'vue-router';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getTechnicalReviewList, saveTechnicalReviewInfo } from '@/api/task-registration';
import { entrustRegAttachmentUploadUrl } from '@/api/uploadAction';
import { formatDate, formatDateTime } from '@/utils/formatTime';
// import store from '@/store'
import UserTag from '@/components/UserTag';
import { getToken } from '@/utils/auth';
import { getRoleTree, getMemberList } from '@/api/roleManage';
import emptyImg from '@/assets/img/empty-table.png';

export default {
  name: 'PanelReviewInfo',
  components: { UserTag },
  props: {
    processId: {
      type: String,
      default: ''
    },
    showDetail: {
      type: Boolean,
      default: false
    },
    reviewInfo: {
      type: Array,
      default: function () {
        return [];
      }
    },
    taskStatus: {
      type: Number,
      default: 0
    }
  },
  emits: ['setInfo'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    const datas = reactive({
      tableReviewKey: 'tableReviewKey',
      reviewListData: props.reviewInfo,
      fileList: [],
      formDataCommoission: {
        isAssent: 1,
        isArrival: 1
      }, // 委托确认的表单
      fileAction: entrustRegAttachmentUploadUrl(route.query.id),
      addReviewTitle: '新增附件',
      headerconfig: {
        Authorization: getToken()
      },
      showFeeDialog: false,
      dialogCommission: false,
      commissionPerson: [], // 委托确认人
      uploadRef: ref(),
      feeTitle: '委托确认',
      formRef: ref(),
      formCommissionRef: ref(),
      isCheck: false,
      currentFeeInfo: {
        approveType: 0,
        approveResult: 1,
        devAbility: 1,
        envAbility: 1,
        staffAbility: 1,
        approveDetail: ''
      },
      costTypeList: [],
      submitFeeLoading: false
    });

    watch(
      () => props.reviewInfo,
      newValue => {
        if (newValue) {
          datas.reviewListData = props.reviewInfo;
        }
      },
      { deep: true }
    );
    // 添加附件-打开新增附件弹出框
    const addReview = () => {
      datas.feeTitle = '合同评审';
      datas.feeInfoDetail = {};
      datas.showFeeDialog = true;
    };
    const reviewTask = () => {};

    // 删除附件
    const deleteReview = row => {
      ElMessageBox({
        title: '提交',
        message: '是否确认删除该附件？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          getTechnicalReviewList(row.id).then(res => {
            if (res !== false) {
              ElMessage.success('删除成功');
              context.emit('setInfo', 'delete');
            }
          });
        })
        .catch(() => {});
    };

    const submitFeeData = () => {
      // 提交费用信息
      datas.formRef.validate(valid => {
        if (valid) {
          datas.currentFeeInfo.superId = route.query.id;
          datas.currentFeeInfo.approveType = props.taskStatus - 1;
          datas.currentFeeInfo.approveBy = getLoginInfo().accountId;
          datas.submitFeeLoading = true;
          const resultParams = {
            devAbility: `${datas.currentFeeInfo.devAbility}`,
            envAbility: `${datas.currentFeeInfo.envAbility}`,
            staffAbility: `${datas.currentFeeInfo.staffAbility}`,
            isAssent: `${datas.currentFeeInfo.approveResult}`,
            opinion: datas.currentFeeInfo.approveDetail
          };
          const executeParams = {
            businessKey: route.query.id,
            processInstanceId: props.processId,
            processParameter: resultParams
          };
          saveTechnicalReviewInfo(executeParams).then(function (res) {
            datas.submitFeeLoading = false;
            if (res !== false) {
              context.emit('setInfo', datas.currentFeeInfo);
              ElMessage.success('评审成功');
              datas.currentFeeInfo.approveType = 0;
              datas.currentFeeInfo.approveResult = 1;
              datas.currentFeeInfo.devAbility = 1;
              datas.currentFeeInfo.envAbility = 1;
              datas.currentFeeInfo.approveDetail = '';
              datas.showFeeDialog = false;
            }
          });
        } else {
          return false;
        }
      });
    };

    const currentTaskStatus = computed({
      get: () => props.taskStatus
    });
    // 附件上传成功的回调
    const handleFileSuccess = (response, uploadFile, uploadFiles) => {
      if (response.code === 200) {
        proxy.$message.success(`${uploadFile.name}上传成功!`);
        context.emit('setInfo', { formData: datas.formDataCommoission, hasFile: true });
      } else {
        proxy.$message.error(response.message);
      }
    };

    const submitCommission = () => {
      datas.formCommissionRef.validate().then(valid => {
        if (valid) {
          const params = {
            businessKey: route.query.id,
            processInstanceId: props.processId,
            processParameter: datas.formDataCommoission
          };
          datas.submitFeeLoading = true;
          saveTechnicalReviewInfo(params).then(function (res) {
            datas.submitFeeLoading = false;
            if (res !== false) {
              context.emit('setInfo', { formData: datas.formDataCommoission });
              ElMessage.success('委托确认成功！');
              datas.dialogCommission = false;
            }
          });
        } else {
          return false;
        }
      });
    };
    // 上传文件的限制
    const beforeUpload = (file, files) => {
      var fileName = '';
      const passExtension = ['doc', 'docx', 'xls', 'xlsx', 'pdf', 'jpg', 'png', 'jpeg'];
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      const fileSize = file.size / 1024 / 1024 < 20;
      if (!fileSize) {
        proxy.$message.closeAll();
        proxy.$message.error(`附件${file.name}上传失败大小超过20M`);
        return false;
      } else if (
        !passExtension.some(item => {
          return item === fileName;
        })
      ) {
        proxy.$message.closeAll();
        proxy.$message.error(`${file.name}上传失败，不支持附件${file.name}的格式`);
        return false;
      } else if (file.size === 0) {
        proxy.$message.closeAll();
        proxy.$message.error(`附件${file.name}上传失败大小不能为空`);
        return false;
      } else {
        return true;
      }
    };
    const handleChangeDate = (val, fieldName) => {
      datas.formDataCommoission[fieldName] = formatDate(val);
    };
    const handleChangeRadio = val => {
      if (val) {
        datas.formDataCommoission.arrivalDate = '';
      } else {
        datas.formDataCommoission.preArrivalDate = '';
      }
    };
    const addCommission = () => {
      datas.dialogCommission = true;
      getNameList();
      datas.formDataCommoission = {
        isAssent: 1,
        isArrival: 1,
        confirmDate: formatDate(new Date()),
        arrivalDate: formatDate(new Date()),
        confirmPerson: getLoginInfo().accountId
      };
    };
    // 获取委托确认人
    const getNameList = () => {
      getRoleTree({}).then(res => {
        if (res) {
          const WTQRInfoRole = res.data.data.filter(item => {
            return item.code === 'WTQR';
          })[0];
          if (WTQRInfoRole.code) {
            getRoleMember(WTQRInfoRole);
          }
        }
      });
    };
    const getRoleMember = info => {
      getMemberList({ isadmin: 0, limit: '-1', page: '1', roleId: info.id }).then(res => {
        if (res) {
          datas.commissionPerson = res.data.data.list;
        }
      });
    };
    return {
      ...toRefs(datas),
      emptyImg,
      beforeUpload,
      addCommission,
      handleChangeRadio,
      handleChangeDate,
      submitCommission,
      getNameList,
      handleFileSuccess,
      currentTaskStatus,
      getNameByid,
      formatDate,
      formatDateTime,
      addReview,
      drageHeader,
      deleteReview,
      reviewTask,
      getPermissionBtn,
      submitFeeData
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.floatR {
  float: right;
  margin-left: 10px;
}
.displayInBlock {
  display: inline-block;
}
.uploadTitle {
  font-size: 12px;
  margin-left: 10px;
  color: #808080;
}
.review-info {
  .review-info-header {
    margin: 15px 0 15px 0;
    text-align: left;
    .title {
      font-size: 16px;
      font-weight: bold;
      height: 32px;
      line-height: 32px;
      text-align: left;
      display: inline-block;
      margin-right: 16px;
    }
    .review-btn {
      margin-left: 10px;
    }
    .review-button {
      text-align: right;
    }
    :deep(.el-button) {
      float: right;
    }
  }
  .content {
    background: $background-color;
    text-align: left;
    position: relative;
    .review-info-table {
      margin-bottom: 15px;
    }
  }
}

.step-desc {
  .name {
    color: #999999;
    span {
      margin-left: 5px;
    }
  }
  .desc {
    color: #999999;
    background: #f6f6f6;
    border-radius: 3px;
    padding: 5px 10px;
    max-width: 520px;
  }
  .el-textarea {
    width: 100%;
    margin-top: 10px;
  }
  .approva-radio {
    font-size: 14px;
    margin-top: 10px;
    .title {
      float: left;
      margin-right: 10px;
    }
  }
  .next-user {
    margin-top: 10px;
    height: 50px;
  }
  .send-date {
    margin: 10px 0px;
  }
}

.content {
  :deep(.el-step__description) {
    padding-right: 0%;
  }
}
.el-steps {
  .name-title {
    color: #303339;
    font-size: 14px;
    font-style: normal;
    font-weight: normal;
  }
  .time-title {
    color: #999999;
    font-size: 12px;
    margin: 0 10px;
    font-weight: 500;
  }
}

#review-result {
  width: 100%;
  #review-pass,
  #review-refuse {
    padding: 0px;
    height: 2.5rem;
    line-height: 2.5rem;
    font-weight: 540;
    width: 100%;
    text-align: center;
    :deep(.el-radio__input) {
      display: none !important;
    }
  }

  #review-refuse.is-checked,
  #review-pass.is-checked {
    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #fff;
    }
  }

  #review-refuse.is-checked {
    background-color: $tes-red;
    border-color: $tes-red !important;
    box-shadow: $tes-red -1px 0px 0px 0px;
  }

  #review-pass.is-checked {
    background-color: $green;
    border-color: $green !important;
    box-shadow: $green -1px 0px 0px 0px;
  }
  #flex-group {
    display: flex;
    flex-direction: row;
    #flex-space {
      width: 15px;
    }
    .flex-content {
      flex: auto;
    }
  }
}
</style>
