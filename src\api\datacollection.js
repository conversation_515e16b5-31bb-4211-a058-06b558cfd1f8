import request from '@/utils/request';
// 数据采集api
// 数据采集列表
export function acquisitionList(data) {
  return request({
    url: '/api-device/device/acquisition/list',
    method: 'post',
    data
  });
}
// 查询该仪器设备下的所有的检测项目
export function findCapabilityByDeviceId(deviceId) {
  return request({
    url: '/api-capabilitystd/capabilitydevice/findCapabilityByDeviceId/' + deviceId,
    method: 'get'
  });
}
// 根据项目id查询样品树
export function deviceByInfo(Id) {
  return request({
    url: '/api-orders/orders/experiment/experiment/deviceByInfo/' + Id,
    method: 'get'
  });
}
// 获取RDS历史数据
export function getRdsData(data) {
  return request({
    url: '/api-orders/orders/experiment/experimentdata/getRdsData',
    method: 'post',
    data
  });
}

// 开始实时数据采集
export function startRdsData(data) {
  return request({
    url: '/api-orders/orders/experiment/experimentdata/rdsStart',
    method: 'post',
    data
  });
}

// 停止实时数据采集
export function stopRdsData(scheduledId, deviceNumber) {
  return request({
    url: `/api-orders/orders/experiment/experimentdata/rdsStop/${scheduledId}/${deviceNumber}`,
    method: 'get'
  });
}
