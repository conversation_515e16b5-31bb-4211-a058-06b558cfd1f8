import request from '@/utils/request';

// 试验方法-列表
export function getMethodList(capabilityId) {
  return request({
    url: `/api-capabilitystd/capability/capabilitymethod/list/${capabilityId}`,
    method: 'get'
  });
}
// 保存和更新试验方法信息
export function saveOrUpdateMethod(data) {
  return request({
    url: '/api-capabilitystd/capability/capabilitymethod/saveOrUpdate',
    method: 'post',
    data
  });
}
// 试验方法-查看
export function detailMethodList(methodId) {
  return request({
    url: `/api-capabilitystd/capability/capabilitymethod/findByMethodId/${methodId}`,
    method: 'get'
  });
}
// 根据主键Id查查看试验方法信息
export function detailMethodById(id) {
  return request({
    url: `/api-capabilitystd/capability/capabilitymethod/info/${id}`,
    method: 'get'
  });
}
// 删除未发布试验方法
export function deleteMethod(id) {
  return request({
    url: `/api-capabilitystd/capability/capabilitymethod/delete/${id}`,
    method: 'delete'
  });
}
// 试验方法-发布
export function publishMethod(id) {
  return request({
    url: `/api-capabilitystd/capability/capabilitymethod/release/${id}`,
    method: 'get'
  });
}
// 附件下载
export function downloadMethod(data) {
  return request({
    url: `/api-capabilitystd/capability/attachment/downloadByTemplateId`,
    method: 'get',
    data
  });
}
// 上传附件
export function uploadMethod(data) {
  return request({
    url: '/api-capabilitystd/capability/attachment/upload',
    method: 'post',
    data
  });
}
// 批量上传附件
export function uploadMoreMethod(data, callback) {
  return request({
    url: '/api-capabilitystd/capability/attachment/fileListUpload',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: function (progressEvent) {
      // 原生获取上传进度的事件
      callback(progressEvent);
    },
    data
  });
}
// 根据业务主键Id获取附件信息
export function getAttachmentById(id) {
  return request({
    url: `/api-capabilitystd/capability/attachment/findAuthAttachmentList/${id}`,
    method: 'get'
  });
}
// 检测项目绑定的人员
export function getPersionList(capabilityId) {
  return request({
    url: `/api-capabilitystd/capability/capabilityemployee/findByCapabilityId/${capabilityId}`,
    method: 'get'
  });
}
// 删除绑定的人员
export function deletePersonApi(id) {
  return request({
    url: `/api-capabilitystd/capability/capabilityemployee/delete/${id}`,
    method: 'delete'
  });
}
// 保存项目关联的人员
export function savePerson(data) {
  return request({
    url: `/api-capabilitystd/capability/capabilityemployee/saveOrUpdate`,
    method: 'post',
    data
  });
}
