<template>
  <!-- 检测项目 详情 -->
  <el-drawer
    v-model="showDrawer"
    :title="titles"
    direction="rtl"
    :before-close="handleClose"
    size="88%"
    destroy-on-close
    :close-on-click-modal="false"
    custom-class="testItemDrawer"
    @opened="handleOpened"
  >
    <DrawerLayout :has-left-panel="false" :has-button-group="getPermissionBtn('sgItemEdit')">
      <template #drawer-title>
        <span>{{ detailData.name || '名称' }}</span>
        <el-tag v-if="detailData.status === 1" size="mini" type="success">启用</el-tag>
        <el-tag v-else size="mini" type="danger">停用</el-tag>
      </template>
      <template #button-group>
        <el-button
          v-if="getPermissionBtn('sgItemEdit')"
          size="small"
          @click="editDetial"
          @keyup.prevent
          @keydown.enter.prevent
          >编辑项目</el-button
        >
      </template>
      <el-form class="isCheck form-height-auto">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="所属分类：" prop="categoryName">
              <div>{{ detailData.categoryName || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="项目编号：" prop="code">
              <div>{{ detailData.code || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="关联工序：" prop="workHours">
              <div v-if="detailData.workingProcedureCode || detailData.workingProcedureName">
                {{ detailData.workingProcedureCode || '--' }}{{ ' - ' }}{{ detailData.workingProcedureName || '--' }}
              </div>
              <span v-else>--</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="项目等级：" prop="grade">
              <div v-if="detailData.grade && detailData.grade !== '[]'">
                <span v-for="item in detailData.grade.split(',')" :key="item">{{ otherDatas.itemLevel[item] }}</span>
              </div>
              <span v-else>--</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="项目描述：">
              <div>{{ detailData.remark || '--' }}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs v-model="activeName" class="marginTop" @tab-click="tabsClick">
        <!-- 关键参数 tab -->
        <el-tab-pane label="关键参数" name="1">
          <div class="btn-group">
            <el-button
              v-if="getPermissionBtn('sgKeyAdd')"
              icon="el-icon-plus"
              size="small"
              @click="addItemCZ"
              @keyup.prevent
              @keydown.enter.prevent
              >新增关键参数</el-button
            >
            <el-button
              v-if="!otherDatas.showBtn && otherDatas.tableData.length > 0 && getPermissionBtn('sgKeyEdit')"
              icon="el-icon-edit"
              size="small"
              @click="editItemCZ"
              @keyup.prevent
              @keydown.enter.prevent
              >编辑关键参数</el-button
            >
            <el-button
              v-show="keyParamEditable"
              type="primary"
              size="small"
              @click="onSubmit"
              @keyup.prevent
              @keydown.enter.prevent
              >保存</el-button
            >
            <el-button v-show="otherDatas.showBtn" size="small" @click="closeGJ">取消</el-button>
          </div>
          <el-table
            id="paramTable"
            ref="paramTableRef"
            :key="otherDatas.paramTableKey"
            :data="otherDatas.tableData"
            fit
            border
            highlight-current-row
            class="detail-table dark-table base-table"
          >
            <el-table-column label="" :width="keyParamEditable ? 55 : 10">
              <i v-show="keyParamEditable" class="tes-move iconfont" style="font-size: 12px; cursor: move" />
            </el-table-column>
            <el-table-column prop="code" label="编号" width="100">
              <template #default="{ row }">
                {{ row.code }}
              </template>
            </el-table-column>
            <el-table-column prop="identifierKey" label="Key" width="220">
              <template #default="{ row }">
                <span v-if="row.disabled"> {{ row.identifierKey || '--' }}</span>
                <el-input v-else v-model="row.identifierKey" placeholder="请输入模板Key" />
              </template>
            </el-table-column>
            <el-table-column prop="name" min-width="220" label="数据名称">
              <template #default="{ row, $index }">
                <span v-if="row.disabled"> {{ row.name }}</span>
                <el-form
                  v-else
                  :ref="'name' + $index"
                  :model="row"
                  :rules="otherDatas.keywordRules"
                  style="margin: 0px"
                >
                  <el-form-item prop="name" style="margin: 0px">
                    <el-input v-model="row.name" placeholder="请输入数据名称" />
                  </el-form-item>
                </el-form>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="englishName" label="英文名称">
              <template #default="{ row }">
                <span v-if="row.disabled"> {{ row.englishName || '--' }}</span>
                <el-input
                  v-else
                  v-model="row.englishName"
                  placeholder="请输入英文名"
                />
              </template>
            </el-table-column> -->
            <el-table-column prop="unitName" label="单位" width="120">
              <template #default="{ row }">
                <span v-if="row.disabled"> {{ row.unitName || '--' }}</span>
                <el-select
                  v-else
                  v-model="row.unitName"
                  placeholder="请选择"
                  clearable
                  filterable
                  size="small"
                  @change="changeUnit(row)"
                >
                  <el-option v-for="item in otherDatas.units" :key="item.id" :label="item.name" :value="item.name" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="resultType" label="数据类型" width="140">
              <template #default="{ row, $index }">
                <span v-if="row.disabled"> {{ row.resultType }}</span>
                <el-form
                  v-else
                  :ref="'resultType' + $index"
                  :model="row"
                  :rules="otherDatas.keywordRules"
                  style="margin: 0px"
                >
                  <el-form-item prop="resultType" style="margin: 0px">
                    <el-select
                      v-model="row.resultType"
                      placeholder="请选择"
                      clearable
                      filterable
                      size="small"
                      style="width: 100%"
                      @change="changeSelect(row)"
                    >
                      <el-option
                        v-for="item in otherDatas.resultTypeOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-form>
              </template>
            </el-table-column>
            <el-table-column prop="resultOption" label="参数设置" min-width="220" show-overflow-tooltip>
              <template #default="{ row }">
                <div v-if="row.resultType === '数值型'" class="option-number">
                  <span>约束小数位：</span>
                  <span v-if="row.disabled"> {{ row.resultOption || '--' }} 位</span>
                  <el-input-number
                    v-else
                    v-model="row.resultOption"
                    controls-position="right"
                    :min="0"
                    size="small"
                    style="width: 80px"
                  />
                </div>
                <div v-if="row.resultType === '枚举型'" class="result-option">
                  <span v-if="row.disabled"> {{ filterEnumOptions(row.resultOption) }}</span>
                  <el-select
                    v-else
                    v-model="row.resultOption"
                    placeholder="请选择"
                    clearable
                    size="small"
                    class="multiple-select"
                    filterable
                    :filter-method="changeEnumOptions"
                    @focus="changeEnumOptions(null)"
                  >
                    <el-option
                      v-for="item in otherDatas.enumOptions"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag v-if="row.disabled" :type="row.status ? 'success' : 'info'" size="small">
                  {{ row.status ? '已启用' : '已停用' }}
                </el-tag>
                <el-switch
                  v-else
                  v-model="row.status"
                  class="inner-switch"
                  :active-text="row.status ? '启用' : '停用'"
                  @change="handleQY(row.status, row)"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-if="getPermissionBtn('sgKeyDelete') && !otherDatas.tableData[otherDatas.tableData.length - 1]?.id"
              prop="operation"
              label="操作"
              width="100"
            >
              <template #default="{ row, $index }">
                <span
                  v-if="!row.id"
                  class="blue-color"
                  @click="handleDel(row, $index)"
                  @keyup.prevent
                  @keydown.enter.prevent
                  >删除</span
                >
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <!-- 关联项目 tab -->
        <el-tab-pane label="关联项目" name="3">
          <el-button
            v-if="getPermissionBtn('sgLimsRefAdd')"
            class="add-btn"
            icon="el-icon-plus"
            size="small"
            @click="addTemplateRef"
            @keyup.prevent
            @keydown.enter.prevent
            >新增引用项目</el-button
          >
          <el-table
            :data="otherDatas.templaterefList"
            fit
            border
            highlight-current-row
            size="medium"
            class="base-table dark-table detail-table"
            :span-method="objectSpanMethod"
          >
            <el-table-column type="index" label="序号" width="70" align="center" />
            <el-table-column prop="refMaterialCategoryName" label="LIMS项目物资分类" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.refMaterialCategoryName }}
              </template>
            </el-table-column>
            <el-table-column prop="refCapabilityName" label="LIMS项目名称" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.refCapabilityName }}
              </template>
            </el-table-column>
            <el-table-column prop="refCapabilityParaName" label="LIMS项目关键参数名称" show-overflow-tooltip>
              <template #default="{ row }">
                <span v-if="row.disable">{{ row.refCapabilityParaName || '--' }}</span>
                <el-select
                  v-else
                  v-model="otherDatas.paramsATR.refCapabilityParaId"
                  placeholder="LIMS项目关键参数名称"
                  clearable
                  @change="changeRefCapabilityPara(row)"
                  @clear="clearRefCapabilityPara(row)"
                >
                  <el-option
                    v-for="item in row.capabilityparaVoList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="capabilityParaName" label="国网项目关键参数名称">
              <template #default="{ row }">
                <span v-if="row.disable">{{ row.capabilityParaName || '--' }}</span>
                <el-select
                  v-else
                  v-model="otherDatas.paramsATR.capabilityParaId"
                  placeholder="请输入本项目关键参数名称"
                  clearable
                  @change="changeSgCapabilityPara(row)"
                  @clear="clearSgCapabilityPara(row)"
                >
                  <el-option v-for="item in otherDatas.tableData" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="operation" label="操作" width="140" class-name="fixed-right">
              <template #default="{ row, $index }">
                <span
                  v-if="row.showEditTR && getPermissionBtn('sgLimsRefEdit')"
                  class="blue-color"
                  @click="editTemplateRef(row)"
                  @keyup.prevent
                  @keydown.enter.prevent
                  >编辑</span
                >
                <span
                  v-if="row.showSaveTR && getPermissionBtn('sgLimsRefSave')"
                  class="blue-color"
                  @click="saveTemplateRef(row)"
                  @keyup.prevent
                  @keydown.enter.prevent
                  >保存</span
                >
                <span
                  v-if="row.showDeleteTR && getPermissionBtn('sgLimsRefDelete')"
                  class="blue-color"
                  @click="deleteTemplateRef(row)"
                  @keyup.prevent
                  @keydown.enter.prevent
                  >删除</span
                >
                <span
                  v-if="row.showCancleTR && getPermissionBtn('sgLimsRefDelete')"
                  class="blue-color"
                  @click="cancelTemplateRef(row, $index)"
                  @keyup.prevent
                  @keydown.enter.prevent
                  >取消</span
                >
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <edit
        :drawer="otherDatas.drawer"
        title="编辑项目"
        :seal-scope-of="otherDatas.sealScopeOf"
        :item-level="otherDatas.itemLevel"
        :tree="otherDatas.newTree"
        :edit-data="otherDatas.editData"
        @close="closeDrawer"
      />
      <!-- 新增单个LIMS检测项目-弹出框 -->
      <AddSingleItem
        :show="otherDatas.showATR"
        :data="otherDatas.oldItemsATR"
        @selectData="selectATRData"
        @close="closeATR"
      />
    </DrawerLayout>
  </el-drawer>
</template>

<script>
import { ref, watch, reactive, getCurrentInstance, toRefs, computed, nextTick } from 'vue';
import { addNumToStr, formatTestcapabilityByValue } from '@/utils/formatJson';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getPermissionBtn } from '@/utils/common';
import Edit from './add';
import DrawerLayout from '@/components/DrawerLayout';
import { useStore } from 'vuex';
import { getDictionaryList } from '@/api/dictionary';
import Sortable from 'sortablejs';
import {
  deleteExternalKeyParam,
  saveExternalKeyParam,
  getKeyParamsByExternalCapabilityId,
  getMapKeyParamsByExternalCapabilityId,
  saveExternalKeyParamMap,
  deleteExternalKeyParamMap
} from '@/api/sg-capabilitystd';
import AddSingleItem from '@/components/BusinessComponents/AddSingleItem';
import _ from 'lodash';

export default {
  name: 'DrawerItemDetail',
  components: {
    Edit,
    AddSingleItem,
    DrawerLayout
  },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      required: true
    },
    itemLevel: {
      type: Object,
      default: function () {
        return {};
      }
    },
    sealScopeOf: {
      type: Object,
      default: function () {
        return {};
      }
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    materialCode: {
      type: String,
      default: function () {
        return '0';
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    // 抽屉事件
    // console.log(props.detailData)
    const { proxy } = getCurrentInstance();
    const stores = useStore().state.user;
    // console.log(stores)
    const showAdd = ref(false);
    const modeldata = reactive({
      isshow: false,
      capabilityid: ''
    });
    const showDrawer = ref(props.drawer);
    const titles = ref(props.title);
    const paramTableRef = ref(null);

    const otherDatas = reactive({
      newFlag: true,
      tableData: [],
      tableDataGW: [],
      tableDataGLYQ: [],
      tableDataGL: [],
      thislist: [],
      curList: [],
      itemLevel: {},
      sealScopeOf: {},
      testcapabilityOptions: {
        '339081954591256576': 'A',
        '339081954591256577': 'B',
        '339081954591256578': 'C'
      },
      resultTypeOptions: [
        { value: '数值型', label: '数值型' },
        { value: '字符串', label: '字符串' },
        { value: '枚举型', label: '枚举型' },
        { value: '日期型', label: '日期型' },
        { value: '自定义枚举', label: '自定义枚举' }
      ],
      enumOptions: [],
      copyEnumOptions: [],
      applylabels: [
        { id: '1', name: '数据采集' },
        { id: '2', name: '检测报告' },
        { id: '3', name: 'SPC' }
      ],
      dynamicTags: [],
      inputVisible: false,
      inputValue: '',
      drawer: false,
      newTree: [],
      editData: {},
      showBtn: false,
      showBtn1: false,
      units: stores.unit,
      keywordRules: {
        name: [{ required: true, message: '', trigger: 'blur' }],
        resultType: [{ required: true, message: '', trigger: 'blur' }]
      },
      keywordFlag: true,
      dataChangeFlag: false,
      templaterefList: [],
      currentCapabilityId: '',
      oldItemsATR: [],
      showATR: false,
      showAddTR: true,
      paramsATR: {
        id: '',
        capabilityId: '',
        refCapabilityId: '',
        refCapabilityName: '',
        refCapabilityParaId: '',
        refCapabilityParaName: '',
        capabilityParaId: '',
        capabilityParaName: '',
        refKeyId: '',
        keyId: '',
        keyName: ''
      },
      deviceList: [],
      paramsDevice: {
        capabilityId: '',
        capabilityParaId: '',
        codePointId: '',
        deviceId: '',
        id: '',
        paraConvert: 1,
        pointConvert: 1
      },
      showAddDevice: true,
      operationStepsList: [],
      paramTableKey: 0
    });
    // 关闭抽屉
    const handleClose = () => {
      if (otherDatas.dataChangeFlag === true) {
        ElMessageBox({
          title: '提示',
          message: '当前页面数据未保存，是否确认离开？',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'info'
        })
          .then(() => {
            showDrawer.value = false;
            context.emit('close', false);
            return true;
          })
          .catch(() => {
            return false;
          });
      } else {
        showDrawer.value = false;
        context.emit('close', false);
      }
    };
    // 过滤关键参数数据
    const filterTabs = tableDatas => {
      if (tableDatas.length > 0) {
        tableDatas.forEach(item => {
          item.disabled = true;
          if (item.resultType === '枚举型' && typeof item.resultOption === 'string' && item.resultOption) {
            // item.resultOption = item.resultOption.split(',')
          } else if (item.resultType === '数值型') {
            item.resultOption = parseInt(item.resultOption);
          }
          if (item.applylabel) {
            item.applylabels = item.applylabel.split(',');
          }
        });
      }
      return tableDatas;
    };

    watch(
      () => props.drawer,
      async newValue => {
        // console.log(props)
        if (newValue) {
          otherDatas.itemLevel = props.itemLevel;
          otherDatas.sealScopeOf = props.sealScopeOf;
          otherDatas.enumOptions = await proxy.getMeiJu();
          otherDatas.copyEnumOptions = JSON.parse(JSON.stringify(otherDatas.enumOptions));
          otherDatas.currentCapabilityId = props.detailData.id;
          getTemplateRefList(props.detailData.id);
          showDrawer.value = props.drawer;
          if (showDrawer.value) {
            otherDatas.dataChangeFlag = false;
          }
          otherDatas.tableData = filterTabs(props.detailData.tableData);
          otherDatas.tableData.status = props.detailData.tableData.status === 1;
          otherDatas.newTree = props.detailData.apabilityTree;
          activeName.value = '1';
          otherDatas.showBtn = false;
        }
      },
      { deep: true }
    );
    // store.dispatch('user/getCapabilityTree').then(function(data) {
    //   console.log(data)
    //   otherDatas.newTree = data
    // })

    // tabs标签页
    const activeName = ref('1');
    const tabsClick = tab => {
      // console.log(tab)
    };
    // 关键参数 保存编辑
    const onSubmit = () => {
      // console.log(proxy.$refs)
      var newTabDatas = JSON.parse(JSON.stringify(otherDatas.tableData));
      if (newTabDatas.length === 0) {
        return false;
      }
      otherDatas.keywordFlag = true;
      for (const i in proxy.$refs) {
        // console.log(proxy.$refs[i].model.name)
        if (proxy.$refs[i] !== null && i !== 'paramTableRef') {
          proxy.$refs[i].validate();
          if (!proxy.$refs[i].model.name) {
            otherDatas.keywordFlag = false;
          }
          if (!proxy.$refs[i].model.resultType) {
            otherDatas.keywordFlag = false;
          }
        }
      }
      if (otherDatas.keywordFlag === false) {
        ElMessage.error('请输入数据名称/数据类型');
        return false;
      }

      if (newTabDatas.length > 0) {
        newTabDatas.forEach(item => {
          if (item.resultType === '枚举型' && item.resultOption.length > 0) {
            // item.resultOption = item.resultOption.toString()
          } else if (item.resultType === '枚举型' && item.resultOption.length === 0) {
            // item.resultOption = ''
          } else if (item.resultType === '数值型') {
            item.resultOption = item.resultOption + '';
          }
        });
      }
      const params = JSON.parse(JSON.stringify(newTabDatas));
      params.forEach(item => {
        item.status = item.status ? 1 : 0;
        item.materialClassificationCode = props.materialCode;
      });
      saveExternalKeyParam(params).then(function (res) {
        if (res !== false && res.data.code === 200) {
          const { data } = res.data;
          // console.log(data)
          otherDatas.newFlag = true;
          otherDatas.dataChangeFlag = false;
          data.status = data.status === 1;
          otherDatas.tableData = filterTabs(data);
          otherDatas.showBtn = false;
          ElMessage.success('保存成功！');
        }
      });
    };
    // 过滤应用场景
    const filterGJCS = v => {
      var list = [];
      var name = [];
      if (v) {
        list = v.split(',');
      }
      const param = {
        1: '数据采集',
        2: '检测报告',
        3: 'SPC'
      };
      if (list.length > 0) {
        list.forEach(l => {
          name.push(param[l]);
        });
      }
      return name.join(',');
    };

    // 关闭抽屉
    const closeD = () => {
      // console.log('close')
    };
    // 编辑项目
    const editDetial = () => {
      // console.log(props.detailData)
      otherDatas.editData = props.detailData;
      otherDatas.drawer = true;
    };
    // 关闭编辑项目
    const closeDrawer = () => {
      otherDatas.drawer = false;
    };

    // #region 关键参数

    // 新增-关键参数
    const addItemCZ = () => {
      otherDatas.newFlag = false;
      otherDatas.dataChangeFlag = true;
      const data = {
        code: '',
        description: '',
        englishName: '',
        externalCapabilityId: props.detailData.id,
        id: '',
        identifierKey: '',
        materialClassificationCode: '',
        name: '',
        order: 0,
        resultType: '',
        resultOption: 1,
        status: true,
        unitName: '',
        disabled: false
      };
      data.status = true;
      const len = otherDatas.tableData.length;
      if (len > 0) {
        data.code = addNumToStr(otherDatas.tableData[len - 1].code);
      } else {
        data.code = addNumToStr(data.code);
      }
      otherDatas.tableData.push(data);
      otherDatas.showBtn = true;
    };
    // 编辑关键参数
    const editItemCZ = () => {
      // console.log('editItemCZ')
      otherDatas.newFlag = false;
      otherDatas.tableData.forEach(res => {
        res.disabled = false;
        if (typeof res.status === 'number') {
          res.status = res.status === 1;
        }
      });
      otherDatas.showBtn = true;
    };

    // 删除-关键参数
    const handleDel = (row, index) => {
      // console.log(row)
      // console.log(otherDatas.tableData)
      // const hasitem = _.filter(otherDatas.tableData, res => {
      //   return res.id === row.id
      // })
      // console.log(hasitem)
      // if (hasitem.length === 1 && hasitem[0].id) {
      //   ElMessage.warning('关键参数至少需要保存一个!')
      //   return false
      // }
      ElMessageBox({
        title: '提示',
        message: '确认删除关键参数吗？删除后不可恢复!',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          const newArr = [];
          if (row.id) {
            newArr.push(row.id);
            deleteExternalKeyParam(newArr).then(function (res) {
              if (res !== false && res.data.code === 200) {
                otherDatas.newFlag = true;
                otherDatas.tableData.splice(index, 1);
                ElMessage.success('删除成功！');
              }
            });
          } else {
            // otherDatas.newFlag = true
            otherDatas.tableData.splice(index, 1);
          }
          return true;
        })
        .catch(() => {
          return false;
        });
      otherDatas.newFlag = false;
      // console.log(otherDatas.tableData)
    };
    // 启用 true  停用 false
    const handleQY = (flag, row) => {
      // console.log(flag)
      var num = 0;
      otherDatas.tableData.forEach(data => {
        if (data.status) {
          num += 1;
        }
      });
      if (num < 1 && flag === false) {
        ElMessage.warning('至少需要一个启动项');
        row.status = true;
        return false;
      }
      otherDatas.dataChangeFlag = true;
    };
    // 选择单位
    const changeUnit = row => {
      // console.log(row)
      otherDatas.dataChangeFlag = true;
    };
    // 关键参数--数据类型
    const changeSelect = row => {
      // console.log(row)
      if (row.resultType === '枚举型' || row.resultType === '自定义枚举') {
        row.resultOption = '';
        row.inputVisible = false;
        row.inputValue = '';
      } else if (row.resultType === '字符串' || row.resultType === '日期型') {
        row.resultOption = '';
      } else {
        row.resultOption = 1;
      }
      otherDatas.dataChangeFlag = true;
    };
    // 关键参数-应用场景修改
    const changeApplylabel = row => {
      row.applylabel = row.applylabels.join(',');
    };

    // 关键参数--参数设置
    const handleCloseTag = (row, tag) => {
      row.resultOption.splice(row.resultOption.indexOf(tag), 1);
      otherDatas.dataChangeFlag = true;
    };
    const showInput = row => {
      row.inputVisible = true;
      if (row.resultOption === '' || row.resultOption === undefined || row.resultOption === null) {
        row.resultOption = [];
      }
    };
    const handleInputConfirm = row => {
      const inputValue = row.inputValue;
      if (inputValue) {
        row.resultOption.push(inputValue);
      }
      row.inputVisible = false;
      row.inputValue = '';
    };
    // 关键参数 取消保存
    const closeGJ = () => {
      // console.log('closeGJ')
      otherDatas.showBtn = false;
      getKeyParamsByExternalCapabilityId(props.detailData.id).then(function (res) {
        if (res !== false && res.data.code === 200) {
          const { data } = res.data;
          data.forEach(item => {
            item.status = item.status === 1;
          });
          otherDatas.tableData = filterTabs(data);
          showDrawer.value = true;
          // context.emit('close', false)
        }
      });
    };
    const formatValue = val => {
      return formatTestcapabilityByValue(val);
    };

    // #endregion

    // #region 模板引用模块
    // 新增-模板引用
    const addTemplateRef = () => {
      if (otherDatas.showAddTR === false) {
        ElMessage.warning('只能同时编辑一行');
        return false;
      } else if (otherDatas.tableData.length === 0) {
        ElMessage.warning('本项目关键参数为空,请新增关键参数');
        return false;
      }
      // otherDatas.oldItemsATR.push({
      //   // refCapabilityId: otherDatas.currentCapabilityId
      // })
      otherDatas.showATR = true;
    };
    // 编辑-模板引用
    const editTemplateRef = async row => {
      row.disable = false;
      otherDatas.paramsATR = Object.assign(otherDatas.paramsATR, row);
      row.showEditTR = false;
      row.showSaveTR = true;
      row.showDeleteTR = false;
      row.showCancleTR = true;
      // console.log(otherDatas.paramsATR)
      otherDatas.templaterefList.forEach(trl => {
        if (trl.id !== row.id) {
          trl.showEditTR = false;
          trl.showSaveTR = false;
          trl.showDeleteTR = false;
          trl.showCancleTR = false;
        }
      });
      otherDatas.showAddTR = false;
    };
    // 保存-模板引用
    const saveTemplateRef = async row => {
      // console.log(row)
      // if (!row.capabilityParaId) {
      //   ElMessage.error('请选择本项目关键参数')
      //   return false
      // } else if (!row.refCapabilityParaId) {
      //   ElMessage.error('请选择被引用项目关键参数')
      //   return false
      // }
      const flag = await updateTemplateRefList();
      if (flag === true) {
        row.disable = true;
        otherDatas.showAddTR = true;
      }
    };
    // 删除-模板引用
    const deleteTemplateRef = row => {
      // console.log(row)
      ElMessageBox({
        title: '',
        message: '是否删除该项目？删除后不可恢复',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          deleteExternalKeyParamMap(row.id).then(res => {
            if (res) {
              // console.log(data.data)
              ElMessage.success('删除成功');
              getTemplateRefList(otherDatas.currentCapabilityId);
            }
          });
        })
        .catch(() => {});
    };
    // 取消-模板引用
    const cancelTemplateRef = (row, index) => {
      getTemplateRefList(otherDatas.currentCapabilityId);
    };
    // 获取模板引用列表
    const getTemplateRefList = capabilityId => {
      return new Promise((resolve, reject) => {
        getMapKeyParamsByExternalCapabilityId(capabilityId)
          .then(res => {
            if (res !== false) {
              const { data } = res;
              // console.log(data.data)
              otherDatas.templaterefList = [];
              data.data.forEach(item => {
                otherDatas.templaterefList.push({
                  id: item.id,
                  capabilityId: item.externalCapabilityId,
                  capabilityName: item.externalCapabilityName,
                  capabilityParaId: item.externalCapabilityParaId,
                  capabilityParaName: item.externalCapabilityParaName,
                  refCapabilityId: item.capabilityId,
                  refCapabilityName: item.capabilityName,
                  refCapabilityParaId: item.capabilityParaId,
                  refCapabilityParaName: item.capabilityParaName,
                  refMaterialCategoryName: stores.materialList.find(ele => ele.code === item.materialCategoryCode)
                    ?.name,
                  refMaterialCategoryCode: item.materialCategoryCode,
                  capabilityparaVoList: item.capabilityparaVoList,
                  capabilityParaTemplateKey: item.capabilityParaTemplateKey
                });
              });
              proxy.getSpanArr(otherDatas.templaterefList);
              // otherDatas.oldItemsATR = JSON.parse(JSON.stringify(data.data))
              if (otherDatas.templaterefList.length > 0) {
                otherDatas.templaterefList.forEach(list => {
                  list.disable = true;
                  list.showEditTR = true;
                  list.showSaveTR = false;
                  list.showDeleteTR = true;
                  list.showCancleTR = false;
                });
              }
              otherDatas.showAddTR = true;
              resolve(data.data);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    };
    // 新增、编辑-模板引用列表
    const updateTemplateRefList = () => {
      return new Promise((resolve, reject) => {
        const keyParamBody = {
          capabilityId: otherDatas.paramsATR.refCapabilityId,
          capabilityName: otherDatas.paramsATR.refCapabilityName,
          capabilityParaId: otherDatas.paramsATR.refCapabilityParaId,
          capabilityParaName: otherDatas.paramsATR.refCapabilityParaName,
          externalCapabilityId: props.detailData.id,
          externalCapabilityName: props.detailData.name,
          externalCapabilityParaId: otherDatas.paramsATR.capabilityParaId,
          externalCapabilityParaName: otherDatas.paramsATR.capabilityParaName,
          id: otherDatas.paramsATR.id || ''
        };
        if (keyParamBody.capabilityParaId && keyParamBody.externalCapabilityParaId) {
          const refList = otherDatas.templaterefList.filter(item => {
            return item.refCapabilityParaId === keyParamBody.capabilityParaId && item.id;
          });
          if (refList.length > 0) {
            const paraFilterList = refList.filter(item => {
              return item.capabilityParaId === keyParamBody.externalCapabilityParaId;
            });
            if (keyParamBody.id) {
              const finalList = paraFilterList.filter(item => {
                return item.id !== keyParamBody.id;
              });
              if (finalList.length > 0) {
                ElMessage.warning('该关联关系已添加，请勿重复添加!');
                return;
              }
            } else {
              if (paraFilterList.length > 0) {
                ElMessage.warning('该关联关系已添加，请勿重复添加!');
                return;
              }
            }
          }
        }
        saveExternalKeyParamMap(keyParamBody)
          .then(res => {
            if (res !== false) {
              // const { data } = res
              ElMessage.success(`${keyParamBody.id ? '更新成功!' : '新增成功!'} `);
              getTemplateRefList(otherDatas.currentCapabilityId);
              resolve(true);
            } else {
              resolve(false);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    };
    // 删除-模板引用
    const deleteTemplateRefList = id => {
      return new Promise((resolve, reject) => {
        deleteExternalKeyParamMap(id)
          .then(res => {
            if (res !== false) {
              const { data } = res;
              // console.log(data.data)
              ElMessage.success('删除成功');
              getTemplateRefList(otherDatas.currentCapabilityId);
              resolve(data.data);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    };
    // 选择新增模板引用-弹出框
    const selectATRData = value => {
      // console.log(value)
      otherDatas.showATR = false;
      if (value) {
        otherDatas.paramsATR = {
          id: '',
          capabilityId: otherDatas.currentCapabilityId,
          capabilityparaVoList: value.capabilityparaVoList,
          capabilityParaId: '',
          capabilityParaName: '',
          capabilityParaTemplateKey: '',
          refCapabilityId: value.id,
          refCapabilityName: value.name,
          refMaterialCategoryName: value.materialCategoryName,
          refMaterialCategoryCode: value.materialCode,
          refCapabilityParaId: '',
          refCapabilityParaName: '',
          refCapabilityParaTemplateKey: '',
          disable: false,
          showEditTR: false,
          showSaveTR: true,
          showDeleteTR: false,
          showCancleTR: true
        };
        otherDatas.showAddTR = false;
        const newData = JSON.parse(JSON.stringify(otherDatas.templaterefList));
        newData.push(otherDatas.paramsATR);
        proxy.getSpanArr(newData);
        otherDatas.templaterefList.push(otherDatas.paramsATR);
        otherDatas.templaterefList.forEach(trl => {
          if (trl.id) {
            trl.showEditTR = false;
            trl.showSaveTR = false;
            trl.showDeleteTR = false;
            trl.showCancleTR = false;
          }
        });
      }
    };
    // 关闭弹出框---新增模板引用
    const closeATR = value => {
      otherDatas.showATR = false;
      // getTemplateRefList(otherDatas.currentCapabilityId)
    };

    function changeRefCapabilityPara(row) {
      const selectKeyParam = row.capabilityparaVoList.find(item => item.id === row.refCapabilityParaId);
      otherDatas.templaterefList.forEach(obj => {
        if (obj.refCapabilityParaId === row.refCapabilityParaId) {
          obj.refCapabilityParaName = selectKeyParam.name;
          obj.refCapabilityParaTemplateKey = selectKeyParam.templatekey;
        }
      });
    }

    function clearRefCapabilityPara(row) {
      otherDatas.paramsATR.refCapabilityParaName = '';
    }

    function changeSgCapabilityPara(row) {
      const selectSgKeyParam = otherDatas.tableData.find(item => item.id === row.capabilityParaId);
      otherDatas.templaterefList.forEach(obj => {
        if (obj.capabilityParaId === row.capabilityParaId) {
          obj.capabilityParaName = selectSgKeyParam.name;
          obj.capabilityParaTemplateKey = selectSgKeyParam.identifierKey;
        }
      });
    }

    function clearSgCapabilityPara(row) {
      otherDatas.paramsATR.capabilityParaName = '';
      otherDatas.paramsATR.capabilityParaTemplateKey = '';
    }

    // #endregion

    const atrDatas = reactive({
      spanArr: [],
      pos: 0,
      spanArr1: [],
      pos1: 0
    });

    // 获取关键参数列表
    const getCapabilityById = id => {
      return new Promise(resolve => {
        getMapKeyParamsByExternalCapabilityId(id).then(function (res) {
          if (res !== false) {
            resolve(res.data.data);
          }
        });
      });
    };

    const testMethods = computed(() => props.detailData.method.toString().replace(',', ', ') || '--');

    const keyParamEditable = computed(() => otherDatas.showBtn && getPermissionBtn('sgKeyEdit'));

    // 关键参数-枚举型修改
    const changeEnumOptions = val => {
      // console.log(val)
      if (val) {
        const list = [];
        otherDatas.copyEnumOptions.forEach(user => {
          if (user.name.indexOf(val) !== -1) {
            list.push(user);
          }
        });
        otherDatas.enumOptions = list;
      } else {
        otherDatas.enumOptions = otherDatas.copyEnumOptions;
      }
    };

    // 过滤枚举型数据
    const filterEnumOptions = value => {
      var name = '';
      if (value) {
        const items = _.filter(otherDatas.enumOptions, function (eo) {
          return value === eo.code;
        });
        if (items.length > 0) {
          name = items[0].name;
        }
      }
      return name;
    };

    // 行拖拽
    function elTableRowDrop() {
      // 获取当前表格
      const tbodyEle = document.getElementById('paramTable').querySelector('.el-table__body-wrapper tbody');
      if (tbodyEle) {
        Sortable.create(tbodyEle, {
          animation: 150,
          handle: '.tes-move',
          draggable: '.el-table__row',
          ghostClass: 'ghost',
          // 拖动对象移动样式
          dragClass: 'drag',
          forceFallback: true,
          onChoose(evt) {
            // console.log(evt)
          },
          onEnd(evt) {
            // console.log(evt.oldIndex, evt.newIndex)
            if (evt.oldIndex !== evt.newIndex) {
              // 移除原来的数据
              const currRow = otherDatas.tableData.splice(evt.oldIndex, 1)[0];
              // 移除原来的数据并插入新的数据
              otherDatas.tableData.splice(evt.newIndex, 0, currRow);
              otherDatas.tableData.forEach((value, index) => {
                value.code = addNumToStr(index);
              });
              otherDatas.paramTableKey += 1;
            }
          }
        });
      }
    }

    // #region 表格功能
    watch(
      () => otherDatas.paramTableKey,
      (newValue, oldValue) => {
        nextTick(() => {
          elTableRowDrop();
        });
      }
    );
    // #endregion

    function handleOpened() {
      nextTick(() => {
        elTableRowDrop();
      });
    }

    return {
      editItemCZ,
      changeUnit,
      changeApplylabel,
      formatValue,
      showAdd,
      modeldata,
      closeGJ,
      closeDrawer,
      editDetial,
      changeSelect,
      handleInputConfirm,
      showInput,
      handleCloseTag,
      handleQY,
      handleDel,
      tabsClick,
      activeName,
      addItemCZ,
      otherDatas,
      closeD,
      onSubmit,
      handleClose,
      showDrawer,
      titles,
      selectATRData,
      closeATR,
      addTemplateRef,
      editTemplateRef,
      saveTemplateRef,
      deleteTemplateRef,
      cancelTemplateRef,
      getTemplateRefList,
      updateTemplateRefList,
      deleteTemplateRefList,
      getPermissionBtn,
      filterGJCS,
      ...toRefs(atrDatas),
      getCapabilityById,
      testMethods,
      paramTableRef,
      handleOpened,
      keyParamEditable,
      changeRefCapabilityPara,
      clearRefCapabilityPara,
      changeSgCapabilityPara,
      clearSgCapabilityPara,
      changeEnumOptions,
      filterEnumOptions
    };
  },
  methods: {
    // 模板引用合并table
    getSpanArr(data) {
      this.spanArr = [];
      // data就是我们从后台拿到的数据
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].refCapabilityId === data[i - 1].refCapabilityId) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
        // console.log(this.spanArr)
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        // console.log(`rowspan:${_row} colspan:${_col}`);
        return {
          // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        };
      }
    },
    // 关联仪器设备table合并
    getSpanArr1(data) {
      this.spanArr1 = [];
      // data就是我们从后台拿到的数据
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr1.push(1);
          this.pos1 = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].deviceNumber === data[i - 1].deviceNumber) {
            this.spanArr1[this.pos1] += 1;
            this.spanArr1.push(0);
          } else {
            this.spanArr1.push(1);
            this.pos1 = i;
          }
        }
        // console.log(this.spanArr1)
      }
    },
    objectSpanMethod1({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1) {
        const _row = this.spanArr1[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        // console.log(`rowspan:${_row} colspan:${_col}`);
        return {
          // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        };
      }
    },
    // 获取枚举值列表
    getMeiJu() {
      return new Promise((resolve, reject) => {
        const p = {
          condition: '',
          orderby: 'desc',
          dictionaryType: '3',
          page: '1',
          limit: '10000'
        };
        getDictionaryList(p).then(res => {
          if (res !== false) {
            // console.log(res.data.data.list)
            resolve(res.data.data.list);
          }
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep(.drawer-wrapper .drawer-title) {
  margin-bottom: 0 !important;
}
.overflowHidden {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.drawer-fotter {
  position: absolute;
  bottom: 0;
  width: 100%;
  margin: 10px 0px 20px 0px;
  border-top: 1px solid #e4e7ed;
  height: 80px;
  line-height: 100px;
  text-align: right;
  padding-right: 20px;
}
.add-btn {
  float: left;
  margin-bottom: 10px;
  // margin: 0px 0px 14px 14px;
}
.save-btn {
  float: left;
  margin: 0px 0px 14px 0px;
}
.canle-btn {
  float: left;
  margin: 0px 0px 14px 14px;
}
.result-option {
  .el-tag {
    white-space: pre-wrap;
    height: auto;
    margin: 2px 0px;
  }
}
.isCheck {
  margin-top: 12px;
  padding: 10px 20px;
  background: #f5f7fa;
  border-radius: 3px;
}
.marginTop {
  margin-top: 20px;
}
::v-deep(.dark-table) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 28rem) !important;
    overflow-x: hidden !important;
    overflow-y: auto;
  }
}
.option-number {
  display: flex;
  align-items: center;
  width: 100%;
}
.multiple-select {
  width: 100%;
}
.btn-group {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 10px;
}
</style>
<style lang="scss">
.testItemDrawer .el-drawer__header {
  padding: 20px 40px 0px !important;
}
.testItemDrawer .el-drawer__body {
  padding: 20px 40px !important;
  // background: #f0f2f5;
}
.testItemDrawer {
  .drawer-content {
    height: auto !important;
  }
}
</style>
