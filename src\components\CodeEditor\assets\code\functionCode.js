export default [
  {
    name: 'if',
    category: '常用函数',
    code: 'if () {\n\t\n}'
  },
  {
    name: 'if/else',
    category: '常用函数',
    code: 'if() {\n\n} else {\n\n}'
  },
  {
    name: 'if/else if',
    category: '常用函数',
    code: 'if() {\n\n} else if() {\n\n} else {\n\n}'
  },
  {
    name: 'for',
    category: '常用函数',
    code: 'for(var i = 0; i< 100; i++) {\n\n}'
  },
  {
    name: 'switch',
    category: '常用函数',
    code: 'switch(a) {\n case 1: \n\t console.log(1)\n\t break;\n default:\n\t console.log(a)\n\t break; \n}'
  },
  {
    name: 'try/catch',
    category: '常用函数',
    code: 'try {\n\n} catch(err) {\n\n}'
  },
  {
    name: 'error',
    category: 'Console',
    code: 'console.error("");'
  },
  {
    name: 'log',
    category: 'Console',
    code: 'console.log("");'
  },
  {
    name: 'warn',
    category: 'Console',
    code: 'console.warn("");'
  },
  {
    name: '说明',
    category: '代码说明',
    code: "/*第一步: 计算标准值，定义标准值为standardValue，并初始化为0*/\n var standardValue = 0;\nif(ypyxcd>0 && ypyxcd < 11) {\n\tstandardValue = 123;\n} else if(ypyxcd>11) {\n\t standardValue = 234;\n} else {\n standardValue = 345;\n }\n console.log(standardValue); /*最终的标准值*/\n\n /*第二步：根据检测结果expValue,和上面计算的standardValue来判定检测结论expResult\n * 注：这边的判断根据实际情况写结论，expValue是检测报告那边的实测值属于传参\n */\n var expResult = '合格'; /*初始化检测结论为合格*/\n if (expValue > standardValue){\n   expResult = '合格';\n} else if (expValue < standardValue) {\n    expResult = '不合格';\n} else {\n    expResult = '不判定';\n}\n/* 第三步，返回结果,这边主要看检测报告那边需不需要展示计算的标准值， \n * 要是需要展示，这边就返回下面的定义的对象param \n * 要是不需要展示，可以直接return expResult \n */ \n const param = { \n    expResult: expResult, \n   standardValue: standardValue \n}; \n return param;"
  }
];
