<template>
  <!-- 不良品处置 列表 -->
  <ListLayout>
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" @submit.prevent="submit">
        <el-form-item prop="param">
          <div style="width: 43vw">
            <CombinationQuery
              :field-list="queryFieldList"
              field-tip="处置单编号/申请单号/报告编号/物料编号/订单编号"
              @get-query-info="getQueryInfo"
              @reset-search="reset"
            />
          </div>
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button size="large" type="text" class="searchBtn" @click="search" @keyup.prevent @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']" />
          </el-button>
        </el-form-item>
      </el-form>
    </template>

    <template #button-group>
      <el-button
        v-if="getPermissionBtn('registerBtn')"
        icon="el-icon-document-delete"
        type="primary"
        size="large"
        @click="register"
        >不合格登记</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" :model="formInline" label-width="110px" label-position="right" size="small">
            <el-form-item label="检验类型：" prop="type" class="seal-staff-name">
              <el-select v-model="formInline.type" placeholder="请选择" size="small" clearable filterable>
                <el-option
                  v-for="(val, key) in dictionary['JYLX'].enable"
                  :key="key"
                  :label="val"
                  :value="Number(key)"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-radio-group v-model="radioData" size="small" @change="changeRadio">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button v-for="(key, val, index) in listStatus" :key="index" :label="val">{{ key }}</el-radio-button>
      </el-radio-group>
      <el-checkbox v-model="giveMeChecked" class="only-me" @change="giveMeChange">仅看我的</el-checkbox>
    </template>

    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table allocation-table base-table format-height-table"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column
        label="申请单号"
        prop="presentationCode"
        :width="colWidth.orderNo"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span v-copy="row.presentationCode" class="blue" @click="jumpToApplicationDetail(row)">{{
            row.presentationCode || '--'
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="样品编号" prop="secSampleNum" :width="colWidth.orderNo" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <span v-copy="row.secSampleNum" class="blue" @click="jumpToSampleDetail(row)">{{
            row.secSampleNum || '--'
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报告编号" prop="reportNo" :width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-copy="row.reportNo" class="blue" @click="jumpToReportDetail(row)">{{ row.reportNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="处置单编号"
        prop="disposalNumber"
        :width="colWidth.orderNo"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span v-copy="row.disposalNumber" class="blue" @click="editDetail(row)">{{
            row.disposalNumber || '--'
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="检验类型" prop="type" :width="colWidth.typeGroup">
        <template #default="{ row }">
          <div>{{ inspectionType[row.type] || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="检验对象" prop="customerName" :width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.type === 1 ? row.inputWarehouseNo || '--' : row.productionOrderNo || '--' }}
        </template>
      </el-table-column>
      <el-table-column label="对象位置" prop="examineStage" :width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{
            row.type === 1 ? row.wareHouseName : (row.productionProcedure || '-') + '-' + (row.productionStation || '-')
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="tenantInfo.type === 1"
        label="盘号"
        prop="reelNo"
        :width="colWidth.plate"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="nowrap">{{ row.reelNo || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="物料编号" prop="materialNo" :width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.materialNo || '--' }}
        </template>
      </el-table-column>
      <el-table-column label="样品名称" prop="sampleName" :width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.materialDesc || row.sampleName || '--' }}
        </template>
      </el-table-column>
      <el-table-column label="物料规格" prop="materialGroup" :width="colWidth.model" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.materialGroup || '--' }}
        </template>
      </el-table-column>
      <el-table-column label="订单编号" prop="productionOrderNo" :width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.productionOrderNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="登记日期" prop="createDateTime" :width="colWidth.date" sortable>
        <template #default="{ row }">
          <span v-if="row.createDateTime">{{ formatDate(row.createDateTime) }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="登记人" prop="createByUserId" :width="colWidth.person" show-overflow-tooltip>
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.createByUserId) || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="待办人" prop="examineOwnerId" :width="colWidth.person" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="!getNameByid(row.examineOwnerId) || row.status == '4'">--</span>
          <UserTag v-else :name="getNameByid(row.examineOwnerId)" />
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" :width="colWidth.status">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="iconListStatus[row.status]">{{
            listStatus[row.status] || '--'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        v-if="getPermissionBtn('unqualified-dispositionBtn') || getPermissionBtn('detailUnqualifiedBtn')"
        label="操作"
        :width="colWidth.operationSingle"
        prop="caozuo"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="scope">
          <span
            v-if="
              currentAccountId === scope.row.examineOwnerId &&
              scope.row.status !== '4' &&
              getPermissionBtn('unqualified-dispositionBtn')
            "
            class="blue-color"
            @click="editDetail(scope.row)"
            >处置</span
          >
          <span
            v-if="
              (currentAccountId !== scope.row.examineOwnerId || scope.row.status === '4') &&
              getPermissionBtn('detailUnqualifiedBtn')
            "
            class="blue-color"
            @click="editDetail(scope.row)"
            >查看</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
  </ListLayout>
</template>

<script>
import { reactive, toRefs, getCurrentInstance } from 'vue';
import router from '@/router/index.js';
import { getLoginInfo } from '@/utils/auth';
import UserTag from '@/components/UserTag';
import Pagination from '@/components/Pagination';
import { colWidth } from '@/data/tableStyle';
import { getDispotitionList } from '@/api/unqualifiedDisposition';
import { getNameByid } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { getPermissionBtn } from '@/utils/common';
import { getInspectionList } from '@/api/inspection-application';
// import { useStore } from 'vuex'
import { drageHeader } from '@/utils/formatTable';
// import _ from 'lodash'
import ListLayout from '@/components/ListLayout';
import CombinationQuery from '@/components/CombinationQuery';
import { queryFieldList } from './func/format';
import { getDictionary } from '@/api/user';
import { mapGetters } from 'vuex';

export default {
  name: 'UnqualifiedDisposition',
  components: { UserTag, Pagination, ListLayout, CombinationQuery },
  setup() {
    const { proxy } = getCurrentInstance();
    // const store = useStore().state
    // console.log(store)

    const datas = reactive({
      radioData: '',
      showS: false,
      activeName: '0',
      currentAccountId: '',
      isShowDrawer: false,
      giveMeChecked: false,
      dictionary: {
        JYLX: {
          enable: {},
          all: {}
        }
      },
      iconListStatus: {
        1: 'info',
        2: 'warning',
        3: 'primary',
        4: 'success'
      },
      listStatus: {
        1: '待分析',
        2: '待审批',
        3: '待验收',
        4: '已完成'
      },
      inspectionType: {
        1: '采购入库',
        2: '过程检验',
        3: '完工检验'
      },
      list: [],
      formInline: {
        param: '',
        status: '',
        appoint: '0',
        tableQueryParamList: []
      },
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: ''
      },
      tableKey: 0,
      listLoading: false,
      total: 0
    });

    if (getLoginInfo()) {
      datas.currentAccountId = getLoginInfo().accountId;
    }
    const search = () => {
      datas.showS = !datas.showS;
      if (datas.activeName === '0') {
        datas.activeName = '1';
      } else {
        datas.activeName = '0';
      }
    };

    // 重置
    const reset = () => {
      datas.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: ''
      };
      datas.giveMeChecked = false;
      datas.formInline = {
        status: '',
        param: '',
        tableQueryParamList: [],
        appoint: '0'
      };
      datas.radioData = '';
      proxy.getList();
    };
    // 查询
    const submit = () => {
      proxy.getList();
    };

    // 过滤状态
    const filterStatus = status => {
      const classMap = {
        1: ['icon-tes-info', '待分析'],
        2: ['icon-tes-info', '待评审'],
        3: ['icon-tes-info', '待处置'],
        4: ['icon-tes-info', '已完成']
      };
      return classMap[status];
    };
    // 点击编辑
    const editDetail = row => {
      // console.log(row)
      router.push({
        name: 'Register',
        params: {
          title: 'detail',
          id: row.disposalNumber,
          token: 0
        }
      });
    };

    // 切换tab
    const changeRadio = value => {
      // console.log(value)
      datas.formInline.status = value;
      proxy.getList();
    };
    // 仅看我的-change
    const giveMeChange = check => {
      if (check) {
        datas.formInline.appoint = '1';
      } else {
        datas.formInline.appoint = '0';
      }
      proxy.getList();
    };
    // 不合格登记
    const register = () => {
      // datas.isShowDrawer = true
      localStorage.setItem('sampleInfoDetailBLP', null);
      router.push({
        name: 'Register',
        params: {
          title: 'add',
          id: 0,
          token: 0
        }
      });
    };
    // 排序
    const sortChange = data => {
      const { prop, order } = data;
      datas.listQuery.orderBy = prop;
      if (order === 'ascending') {
        datas.listQuery.isAsc = 'true';
      } else if (order === 'descending') {
        datas.listQuery.isAsc = 'false';
      } else {
        datas.listQuery.isAsc = '';
      }
    };
    const getDictionaryList = () => {
      Object.keys(datas.dictionary).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          datas.dictionary[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              datas.dictionary[item].enable[optionItem.code] = optionItem.name;
            }
            datas.dictionary[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    getDictionaryList();
    const handleCloseDrawer = () => {
      datas.isShowDrawer = false;
    };
    // 申请单号详情页
    const jumpToApplicationDetail = row => {
      getInspectionList({ param: `${row.presentationCode}` }).then(res => {
        if (res) {
          router.push({
            path: '/unqualified-disposition/application',
            query: { id: res.data.data.list[0].id, flag: 1 }
          });
        }
      });
    };
    // 跳转样品详情
    const jumpToSampleDetail = row => {
      router.push({
        path: '/unqualified-disposition/sample-detail',
        query: {
          orderId: row.orderId,
          sampleId: row.sampleId
        }
      });
    };

    // 点击报告编号跳转到报告详情页面
    const jumpToReportDetail = row => {
      router.push({
        path: '/unqualified-disposition/report-detail',
        query: {
          reportId: row.reportId,
          sampleId: row.sampleId,
          reportStage: 6
        }
      });
    };

    const getQueryInfo = info => {
      datas.formInline.param = info.param;
      datas.formInline.tableQueryParamList = info.tableQueryParamList;
      proxy.getList();
    };

    return {
      ...toRefs(datas),
      editDetail,
      search,
      drageHeader,
      filterStatus,
      formatDate,
      getPermissionBtn,
      getNameByid,
      changeRadio,
      reset,
      submit,
      register,
      sortChange,
      giveMeChange,
      handleCloseDrawer,
      jumpToApplicationDetail,
      jumpToSampleDetail,
      jumpToReportDetail,
      colWidth,
      getQueryInfo,
      queryFieldList
    };
  },
  computed: {
    ...mapGetters(['tenantInfo'])
  },
  created() {
    this.getList();
    // 刷新列表
    this.bus.$on('reloadUnqualifiedDispositionList', msg => {
      this.getList();
    });
  },
  methods: {
    // 获取不良品处置列表
    getList(data) {
      const vm = this;
      vm.listLoading = true;
      if (data && data !== undefined) {
        vm.listQuery.page = data.page;
        vm.listQuery.limit = data.limit;
      }
      const param = Object.assign(vm.formInline, vm.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      // 不良品处置列表接口
      getDispotitionList(param).then(res => {
        vm.listLoading = false;
        if (res !== false) {
          const { data } = res.data;
          vm.list = data.list;
          vm.total = data.totalCount;
        }
      });
    }
  }
};
</script>
