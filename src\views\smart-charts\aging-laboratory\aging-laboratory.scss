@import '@/styles/intelligentChart.scss';

.dataBoard {
  background: #0a1a2e;
  .dataPageTop {
    position: relative;
    background-color: #1a2a47;
    margin-bottom: 10px;
    height: 75px;
    .dataPageTitle {
      font-size: 30px;
      background: -webkit-linear-gradient(left, #87b7ff 1%, #ffffff 20%, #ffffff 50%, #ffffff 80%, #87b7ff 100%);
      -webkit-text-fill-color: transparent;
      font-family: 'YouSheBiaoTiHei';
      font-weight: bold;
      line-height: 48px;
      letter-spacing: 5px;
      -webkit-background-clip: text;
      background-clip: text;
      text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
      text-align: center;
      display: inline-block;
      position: absolute;
      top: 10%;
      left: 50%;
      transform: translate(-50%);
    }
  }
}
