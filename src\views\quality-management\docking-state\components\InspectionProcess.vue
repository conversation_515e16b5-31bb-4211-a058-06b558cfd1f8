<template>
  <!-- 过程检验 -->
  <div v-loading="loading" class="InspectionProcess textLeft">
    <el-row class="header">
      <el-col :span="14">
        <el-radio-group v-model="process" size="small" @change="changeProcess">
          <el-radio-button v-for="item in processList" :key="item.code" :label="item.code">{{
            item.name
          }}</el-radio-button>
        </el-radio-group>
      </el-col>
      <el-col :span="10" class="textRight">
        <el-button
          v-if="oldTableTestData.length > 0"
          class="add-btn"
          size="small"
          type="warning"
          icon="el-icon-upload"
          @click="uploadSdcc"
          @keyup.prevent
          @keydown.enter.prevent
          >上传SDCC</el-button
        >
        <el-button
          class="add-btn"
          size="small"
          icon="el-icon-download"
          type="warning"
          @click="dialogSelect = true"
          @keyup.prevent
          @keydown.enter.prevent
          >获取数据</el-button
        >
        <el-button v-if="isAddTestData || isEditTestData || isGetData" type="primary" size="small" @click="onSaveTable">
          保存
        </el-button>
        <el-button v-if="isAddTestData || isEditTestData || isGetData" size="small" @click="calceTestTable">
          取消
        </el-button>
        <el-button
          v-if="!isEditTestData && !isAddTestData && oldTableTestData.length > 0"
          icon="el-icon-edit"
          type="primary"
          size="small"
          @click="handleEditTest"
        >
          编辑
        </el-button>
        <el-button
          v-if="!isAddTestData && selectRow?.externalCapabilityMapList?.length > 0"
          type="primary"
          icon="el-icon-plus"
          size="small"
          @click="handleAdd"
        >
          新增
        </el-button>
      </el-col>
    </el-row>
    <el-form ref="tableTestRef" :model="tableTestForm" :rules="testRules" size="small" style="margin-top: 10px">
      <el-table
        :data="tableTestForm.tableData"
        fit
        border
        size="medium"
        max-height="440px"
        class="detail-table format-height-table2 dark-table"
        :span-method="objectSpanMethod"
        @header-dragend="drageHeader"
      >
        <el-table-column type="index" label="序号" :width="colWidth.serialNo" fixed="left" align="center" />
        <el-table-column prop="secSampleNum" label="样品编号" :min-width="colWidth.orderNo">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
              :prop="'tableData.' + $index + '.secSampleNum'"
              :rules="testRules.secSampleNum"
              style="margin: 0px"
            >
              <el-input
                v-model="row.secSampleNum"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="请输入样品编号"
                @input="
                  val => {
                    return handleEditGroup(val, $index, 'secSampleNum');
                  }
                "
              />
            </el-form-item>
            <span v-else>{{ row.secSampleNum || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="reportNo" label="报告编号" :min-width="colWidth.orderNo">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
              :prop="'tableData.' + $index + '.reportNo'"
              :rules="testRules.reportNo"
              style="margin: 0px"
            >
              <el-input
                v-model="row.reportNo"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="请输入报告编号"
                @input="
                  val => {
                    return handleEditGroup(val, $index, 'reportNo');
                  }
                "
              />
            </el-form-item>
            <span v-else>{{ row.reportNo || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="deviceUsageVoList" label="设备编号" :width="colWidth.orderNo">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
              :prop="'tableData.' + $index + '.deviceUsageVoList'"
              :rules="testRules.deviceUsageVoList"
              style="margin: 0px"
            >
              <el-input
                v-model="row.deviceUsageVoList"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="设备编号"
                @input="
                  val => {
                    return handleEditGroup(val, $index, 'deviceUsageVoList');
                  }
                "
              />
            </el-form-item>
            <span v-else>{{ row.deviceUsageVoList || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="submitStatus" label="上传状态" :width="colWidth.result">
          <template #default="{ row }">
            <el-tag :type="row.submitStatus ? 'success' : 'warning'" size="small">{{
              row.submitStatus ? '已上传' : '待上传'
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="externalCapabilityParaName" label="关键参数" :min-width="colWidth.orderNo">
          <template #default="{ row }">
            <span>{{ row.externalCapabilityParaName || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="unitName" label="单位" :width="colWidth.unit">
          <template #default="{ row }">
            <div>{{ row.unitName || '--' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="resultType" label="数据类型" :width="colWidth.inputSelect" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.resultType || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="paraValue" label="报告结果" :width="colWidth.paraValue" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="ellipsisNumber">{{ row.paraValue || '--' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="sdccParaValue" label="上传结果" :min-width="colWidth.xxys" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
              :prop="'tableData.' + $index + '.sdccParaValue'"
              :rules="testRules.sdccParaValue[row.resultType]"
              style="margin: 0px"
            >
              <el-input
                v-if="row.resultType === '数值型'"
                v-model="row.sdccParaValue"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="上传结果"
                @input="
                  val => {
                    return handleNumber(val, $index, 'sdccParaValue');
                  }
                "
              />
              <el-input
                v-else-if="row.resultType === '字符串'"
                v-model="row.sdccParaValue"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="上传结果"
              />
              <el-select v-else v-model="row.sdccParaValue" placeholder="请选择" size="small">
                <el-option
                  v-for="(val, key) in dictionaryJson[row.resultOption]"
                  :key="key"
                  :label="val"
                  :value="key"
                />
              </el-select>
            </el-form-item>
            <div v-else>
              <span v-if="row.resultType === '枚举型'">{{
                dictionaryJson[row.resultOption][row.sdccParaValue] || '--'
              }}</span>
              <span v-else>{{ row.sdccParaValue || '--' }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="result" label="结论" :min-width="colWidth.result">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
              :prop="'tableData.' + $index + '.result'"
              :rules="testRules.result"
              style="margin: 0px"
            >
              <el-select v-model="row.result" placeholder="结论" size="small">
                <el-option value="合格" />
                <el-option value="不合格" />
              </el-select>
            </el-form-item>
            <span v-else>{{ row.result || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="oldTableTestData.length > 0"
          class-name="fixed-right"
          prop="status"
          label="操作"
          fixed="right"
          :width="colWidth.operation"
        >
          <template #default="{ row, $index }">
            <span
              v-if="row.sourcePath !== 'M' && !row.isAddRow && !row.submitStatus"
              class="blue-color"
              @click="handleDeleteTestData(row, $index)"
              >删除</span
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <!-- 获取数据的弹出框 -->
    <DialogFinishData
      :dialog-show="dialogSelect"
      :select-row="{ type: 'process', data: obtainingData }"
      @closeDialog="closeDialog"
    />
  </div>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import DialogFinishData from './DialogFinishData';
import { ElMessage } from 'element-plus';
import { isDigital } from '@/utils/validate';
import { getInspectionApi, getTestListProcess, saveTestData, deleteTestData, submitSDCC } from '@/api/sdcc';
import { getDictionary } from '@/api/user';
// import { method } from 'lodash'
export default {
  name: 'InspectionProcess',
  components: { DialogFinishData },
  props: {
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    unitList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    externalList: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['isModify'],
  setup(props, context) {
    // eslint-disable-next-line no-empty-pattern
    const { proxy } = getCurrentInstance();
    const state = reactive({
      processList: [],
      externalList: [], // 工序tabs列
      experimentProjectCode: [], // 提交SDCC需要传的code
      process: '',
      tableTestRef: ref(),
      tableTestForm: {
        tableData: []
      },
      selectRow: {},
      obtainingData: {},
      loading: false,
      isAddTestData: false,
      isEditTestData: false,
      pos: 0,
      isGetData: false,
      dialogSelect: false,
      spanArr: [],
      tableList: [],
      oldTableTestData: [], // 修改之前的数据
      unitList: props.unitList,
      dictionaryJson: {},
      testRules: {
        secSampleNum: { required: true, tigger: 'blur', message: '请输入样品编号' },
        reportNo: { required: true, tigger: 'blur', message: '请输入报告编号' },
        result: { required: true, tigger: 'blur', message: '请选择结论' },
        sdccParaValue: {
          字符串: { required: true, tigger: 'blur', message: '请输入上传结果' },
          枚举型: { required: true, tigger: 'blur', message: '请输入上传结果' },
          数值型: [{ required: true, tigger: 'blur', message: '请输入上传结果' }, { validator: isDigital }]
        },
        deviceUsageVoList: { required: true, tigger: 'blur', message: '请输入设备编号' }
      },
      detailData: JSON.parse(localStorage.getItem('productionOrderInfo'))
    });
    // 获取已保存数据
    const getInspectionList = () => {
      state.loading = true;
      getInspectionApi({
        inspectionType: 'PROGRESS',
        productionOrderNo: state.detailData.no,
        productionOrderId: state.detailData.id,
        materialSdccType: state.selectRow.code
      }).then(res => {
        state.loading = false;
        if (res) {
          state.tableTestForm.tableData = JSON.parse(JSON.stringify(res.data.data));
          state.oldTableTestData = JSON.parse(JSON.stringify(res.data.data));
          getSpanArr(state.tableTestForm.tableData);
        }
      });
    };
    // 切换工序
    const changeProcess = val => {
      state.selectRow = state.processList.filter(item => {
        return item.code === val;
      })[0];
      calceTestTable();
      getDictionaryList();
    };
    // 获取字典数据
    const getDictionaryList = () => {
      if (state.selectRow.externalCapabilityMapList) {
        const array = state.selectRow.externalCapabilityMapList.filter(item => {
          return item.resultType === '枚举型';
        });
        array.forEach(val => {
          getDictionary(val.resultOption).then(res => {
            if (res) {
              const dictionaryJson = {};
              res.data.data.dictionaryoption.forEach(item => {
                if (item.status === 1) {
                  dictionaryJson[item.code] = item.name;
                }
              });
              state.dictionaryJson[val.resultOption] = dictionaryJson;
            }
          });
        });
      }
    };
    // 获取顶部工序
    const getTableList = () => {
      state.unitList = props.unitList;
      state.obtainingData = {
        productionOrderNo: state.detailData.no
      };
      state.processList = props.externalList;
      state.experimentProjectCode = props.externalList.map(item => {
        return item.code;
      });
      if (state.processList.length > 0) {
        state.process = state.processList[0].code;
        state.selectRow = state.processList[0];
        getDictionaryList();
        getInspectionList();
      }
    };
    getTableList();
    // 上传SDCC
    const uploadSdcc = () => {
      if (state.isAddTestData || state.isEditTestData || state.isGetData) {
        proxy.$message.error('请先完成测试数据的编辑或新增');
        return false;
      }
      proxy
        .$confirm('是否确认上传SDCC', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          showClose: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        })
        .then(() => {
          state.loading = true;
          submitSDCC({
            ids: state.detailData.id,
            inspectionType: 'PROGRESS',
            materialSdccType: state.selectRow.code,
            experimentProjectCode: state.experimentProjectCode
          }).then(res => {
            state.loading = false;
            if (res) {
              proxy.$message.success('上传成功');
              context.emit('isModify', state.isAddTestData || state.isEditTestData || state.isGetData);
              getInspectionList();
            }
          });
        })
        .catch(() => {});
    };
    // 新增
    const handleAdd = () => {
      state.isAddTestData = true;
      state.selectRow?.externalCapabilityMapList?.forEach((item, index) => {
        state.tableTestForm.tableData.push({
          ...item,
          submitStatus: 0,
          sourcePath: 'I',
          isAddRow: true,
          inspectionType: 'PROGRESS',
          productionOrderNo: state.detailData.no,
          productionOrderId: state.detailData.id,
          materialSdccType: state.selectRow.code,
          reportNo: '',
          result: '合格',
          productionProcedureNo: state.selectRow.productionProcedureNo
        });
      });
      context.emit('isModify', state.isAddTestData || state.isEditTestData || state.isGetData);
      getSpanArr(state.tableTestForm.tableData);
    };
    const closeDialog = val => {
      if (val.data.length > 0) {
        const params = {
          reportIdList: val.data.map(item => {
            return item.reportId;
          }),
          externalCapabilityMapList: state.selectRow.externalCapabilityMapList
        };
        state.loading = true;
        getTestListProcess(params).then(res => {
          state.loading = false;
          if (res) {
            state.dialogSelect = false;
            var reportNoArray = [];
            state.tableTestForm.tableData.forEach(item => {
              reportNoArray.push({
                reportNo: item.reportNo,
                externalCapabilityParaName: item.externalCapabilityParaName
              });
            });
            const detailDataInfo = JSON.parse(JSON.stringify(res.data.data));
            detailDataInfo.forEach(val => {
              const deviceUsageVo = val.deviceUsageVoList
                .map(item => {
                  return item.deviceName;
                })
                .toString();
              const valItem = val;
              delete valItem.id;
              const isExistence = reportNoArray.some(item => {
                return (
                  item.reportNo === valItem.reportNo &&
                  item.externalCapabilityParaName === valItem.externalCapabilityParaName
                );
              });
              if (!isExistence) {
                if (valItem.paraValue && valItem.resultType !== '枚举值') {
                  const sdccParaValue = valItem.paraValue.split(',')[0];
                  // 有报告结果
                  if (sdccParaValue.split(':').length > 1) {
                    valItem.sdccParaValue = sdccParaValue.split(':')[1];
                  } else {
                    valItem.sdccParaValue = sdccParaValue;
                  }
                } else {
                  valItem.sdccParaValue = '';
                }
                state.tableTestForm.tableData.push({
                  ...valItem,
                  submitStatus: 0,
                  sourcePath: 'I',
                  isAddRow: true,
                  deviceUsageVoList: deviceUsageVo,
                  inspectionType: 'PROGRESS',
                  sdccParaValue: valItem.sdccParaValue,
                  materialSdccType: state.selectRow.code,
                  productionOrderNo: state.detailData.no,
                  productionOrderId: state.detailData.id
                });
              } else {
                setTimeout(() => {
                  ElMessage({
                    message: `报告编号：${valItem.reportNo}, 项目名称：${valItem.externalCapabilityParaName}已存在`,
                    grouping: true,
                    type: 'error'
                  });
                }, 10);
              }
            });
            state.isGetData = true;
            context.emit('isModify', state.isAddTestData || state.isEditTestData || state.isGetData);
            getSpanArr(state.tableTestForm.tableData);
          }
        });
      } else {
        state.dialogSelect = false;
      }
    };
    // 保存检测数据
    const onSaveTable = () => {
      proxy.$refs['tableTestRef'].validate(valid => {
        if (valid) {
          state.loading = true;
          saveTestData(state.tableTestForm.tableData).then(res => {
            state.loading = false;
            if (res) {
              proxy.$message.success('保存成功');
              state.isAddTestData = false;
              state.isEditTestData = false;
              state.isGetData = false;
              context.emit('isModify', state.isAddTestData || state.isEditTestData || state.isGetData);
              getInspectionList();
            }
          });
        } else {
          proxy.$message.warning('请先完成必填项');
        }
      });
    };
    // 取消检测数据的新增和编辑
    const calceTestTable = () => {
      state.isAddTestData = false;
      state.isEditTestData = false;
      state.isGetData = false;
      context.emit('isModify', state.isAddTestData || state.isEditTestData || state.isGetData);
      getInspectionList();
    };
    // 删除测试数据
    const handleDeleteTestData = (row, index) => {
      var deleteArrayId = [];
      for (var i = 0; i < state.spanArr[index]; i++) {
        deleteArrayId.push(state.tableTestForm.tableData[index + i].id);
      }
      proxy
        .$confirm('是否确认删除', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          showClose: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        })
        .then(() => {
          state.loading = true;
          deleteTestData(deleteArrayId).then(res => {
            state.loading = false;
            if (res) {
              proxy.$message.success('删除成功');
              state.tableTestForm.tableData.splice(index, deleteArrayId.length);
              state.oldTableTestData.splice(index, deleteArrayId.length);
            }
          });
        })
        .catch(() => {});
    };
    // 编辑样品编号和报告编号
    const handleEditGroup = (val, index, field) => {
      for (var i = 0; i < state.spanArr[index]; i++) {
        state.tableTestForm.tableData[index + i][field] = val;
      }
    };
    const getSpanArr = data => {
      state.spanArr = [];
      // data就是我们从后台拿到的数据
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          state.spanArr.push(1);
          state.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].reportId && data[i].reportId === data[i - 1].reportId) {
            state.spanArr[state.pos] += 1;
            state.spanArr.push(0);
          } else if (data[i].reportNo === data[i - 1].reportNo) {
            state.spanArr[state.pos] += 1;
            state.spanArr.push(0);
          } else {
            state.spanArr.push(1);
            state.pos = i;
          }
        }
      }
    };
    const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
      if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2 || columnIndex === 3 || columnIndex === 11) {
        const _row = state.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        };
      }
    };
    // 编辑检测数据
    const handleEditTestData = (row, index) => {
      for (var i = 0; i < state.spanArr[index]; i++) {
        state.tableTestForm.tableData[index + i].isEditTestRow = true;
      }
    };
    const handleEditTest = () => {
      state.isEditTestData = true;
      context.emit('isModify', state.isAddTestData || state.isEditTestData || state.isGetData);
    };
    const handleNumber = (val, index, filedName) => {
      // if (Number(val) || Number(val) === 0) {
      //   state.tableTestForm.tableData[index][filedName] = Number(val)
      // } else {
      //   state.tableTestForm.tableData[index][filedName] = val
      // }
    };
    return {
      ...toRefs(state),
      changeProcess,
      handleNumber,
      isDigital,
      getSpanArr,
      objectSpanMethod,
      handleEditGroup,
      colWidth,
      handleEditTest,
      handleDeleteTestData,
      handleEditTestData,
      calceTestTable,
      handleAdd,
      onSaveTable,
      closeDialog,
      uploadSdcc,
      getTableList,
      getPermissionBtn,
      drageHeader,
      formatDate,
      getNameByid
    };
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  color: $tes-primary;
}
.header {
  line-height: 40px;
}
.textRight {
  text-align: right;
}
.topBtn {
  margin: 10px 0;
}
.ellipsisNumber {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
</style>
