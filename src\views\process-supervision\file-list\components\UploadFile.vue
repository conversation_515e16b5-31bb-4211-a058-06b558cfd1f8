<template>
  <el-dialog
    v-model="showDialog"
    custom-class="upload-file"
    :title="isAdd === 'add' ? '上传文档' : isAdd === 'edit' ? '编辑文件' : '文件详情'"
    width="70%"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <el-form label-position="top" label-width="110px" :model="uploadForm">
      <el-form-item label="文件分类">
        <el-cascader
          ref="fileCategoryRef"
          v-model="uploadForm.categories"
          size="small"
          :disabled="isAdd === 'detial'"
          :options="tree"
          :props="fileProps"
          clearable
          @change="changeFileCategory"
        />
      </el-form-item>
      <el-form-item label="文件">
        <div v-if="isAdd === 'add'" class="file-title">
          <el-button size="small" @click="addFile" @keyup.prevent @keydown.enter.prevent>上传文件</el-button>
          <span>1.仅限PDF文件; 2.每个文件大小限制在20M之内; 3.最多上传10个文件;</span>
        </div>
        <input
          id="upload-file"
          ref="uploadinputRef"
          class="file-upload-input"
          type="file"
          accept=".pdf"
          multiple
          @input="handleClick"
        />
        <el-table
          ref="uploadFileTableRef"
          :key="tableKey"
          :data="uploadForm.fileNames"
          fit
          border
          size="small"
          max-height="400px"
          highlight-current-row
          class="dark-table upload-file-table base-table"
        >
          <el-table-column label="文件名" prop="fileName" min-width="200px">
            <template #default="{ row, $index }">
              <el-input
                :ref="'fileName' + $index"
                v-model.trim="row.fileName"
                :disabled="isAdd === 'detial'"
                size="small"
                placeholder=""
                clearable
                @input="inputName(row.fileName, $index)"
              />
            </template>
          </el-table-column>
          <el-table-column v-if="isAdd !== 'add'" label="上传人" prop="uploadUser" width="100px">
            <template #default="{ row }">
              <span>{{ getNameByid(row.uploadUser) || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180px">
            <template #default="{ row, $index }">
              <span v-if="isAdd !== 'add' && getPermissionBtn('PreviewFile')" class="blue-color" @click="fileHandle(2)"
                >预览</span
              >
              <span v-if="isAdd !== 'add'" class="blue-color" @click="fileHandle(3)">下载</span>
              <span v-if="isAdd === 'edit' && getPermissionBtn('UpdateFile')" class="blue-color" @click="addFile"
                >更新</span
              >
              <span
                v-if="isAdd === 'add' && getPermissionBtn('DeleteFile')"
                class="blue-color"
                @click="fileDelete1(row, $index)"
                >删除</span
              >
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item v-if="isAdd !== 'detial'" label="文件标签">
        <div class="tips">最多添加10个，每个限制3-20个字</div>
        <el-tag
          v-for="tag in uploadForm.fileTags"
          :key="tag"
          closable
          size="small"
          :disable-transitions="false"
          @close="handleClose(tag)"
        >
          {{ tag.name }}
        </el-tag>
        <!-- <el-input
          v-if="inputVisible"
          ref="saveTagInput"
          v-model="inputValue"
          class="input-new-tag"
          maxlength="20"
          size="small"
          @keyup.enter="handleInputConfirm"
          @blur="handleInputConfirm"
        /> -->
        <el-autocomplete
          v-if="inputVisible"
          ref="saveTagInput"
          v-model="inputValue"
          class="input-new-tag"
          maxlength="20"
          size="small"
          value-key="name"
          :fetch-suggestions="querySearchAsync"
          placeholder="请输入内容"
          @keyup.enter="handleInputConfirm"
          @change="handleInputConfirm"
          @select="handleInputConfirm"
        />
        <el-button v-else size="small" icon="el-icon-plus" :disabled="disabledTag" @click="showInput"
          >添加标签</el-button
        >
      </el-form-item>
      <el-form-item v-if="isAdd === 'detial'" label="文件标签">
        <el-tag v-for="tag in uploadForm.fileTags" :key="tag" size="small">
          {{ tag.name }}
        </el-tag>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button v-if="isAdd === 'edit' && getPermissionBtn('DeleteFile')" type="danger" @click="fileDelete()"
          >删 除</el-button
        >
        <el-button v-if="isAdd !== 'detial'" type="primary" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent
          >确 定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, getCurrentInstance } from 'vue';
import _ from 'lodash';
import { ElMessage, ElLoading } from 'element-plus';
import { getPermissionBtn, getNameByid } from '@/utils/common';
import { formatUploadFileTree } from '@/utils/formatJson';
import { uploadFileList, addFileList, editFileList, tipsTags, saveTags } from '@/api/file-list';

export default {
  name: 'UploadFile',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    isAdd: {
      type: String,
      default: 'add'
    },
    tree: {
      type: Array,
      default: function () {
        return [];
      }
    },
    fileData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    const { proxy, appContext } = getCurrentInstance();
    const bus = appContext.config.globalProperties.bus;
    const datas = reactive({
      showDialog: false,
      uploadForm: {
        categories: '',
        fileNames: [],
        files: [],
        fileTags: []
      },
      options: [],
      fileProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      fileCategoryRef: ref(),
      inputVisible: false,
      inputValue: '',
      uploadinputRef: ref(),
      tableKey: 'upload-file-0',
      disabledTag: false,
      restaurants: [],
      timeout: null,
      currentProgress: 0
    });

    const pdfRegex = /.(pdf)$/i;

    watch(
      () => props.show,
      newValue => {
        if (newValue) {
          datas.showDialog = newValue;
          datas.disabledTag = false;
          if (props.isAdd === 'add') {
            datas.uploadForm.fileNames = [];
            datas.uploadForm.fileTags = [];
            datas.uploadForm.categories = formatUploadFileTree(props.tree, props.fileData.categoryId);
          } else {
            datas.uploadForm.categories = formatUploadFileTree(props.tree, props.fileData.categoryId);
            datas.uploadForm.fileNames = [
              {
                fileName: props.fileData.fileName,
                remoteName: props.fileData.remoteName,
                uploadUser: props.fileData.uploadUser
              }
            ];
            datas.uploadForm.fileTags = props.fileData.tags;
            if (datas.uploadForm.fileTags.length === 10) {
              datas.disabledTag = true;
            }
          }
        }
      },
      { deep: true }
    );

    // 确定选择
    const dialogSuccess = () => {
      if (datas.uploadForm.categories.length === 0) {
        ElMessage.warning('请选择文件目录');
        return false;
      }
      if (datas.uploadForm.fileNames.length > 10) {
        ElMessage.warning('最多只能上传10个文件！');
        return false;
      }
      if (datas.uploadForm.fileNames.length === 0) {
        ElMessage.warning('请上传附件！');
        return false;
      }
      if (datas.uploadForm.fileNames.length > 0) {
        for (var i = 0; i < datas.uploadForm.fileNames.length; i++) {
          var name = proxy.$refs['fileName' + i].input.value;
          if (!name) {
            proxy.$refs['fileName' + i].input.style.border = '1px solid #f56c6c';
            ElMessage.error('请输入文件名');
            return false;
          } else {
            proxy.$refs['fileName' + i].input.style.border = '';
          }
        }
      }
      if (props.isAdd === 'add') {
        const params = {
          categoryId: datas.uploadForm.categories[datas.uploadForm.categories.length - 1],
          files: datas.uploadForm.fileNames,
          tags: datas.uploadForm.fileTags
        };
        addFileList(params).then(res => {
          if (res !== false) {
            ElMessage.success('新增成功');
            bus.$emit('reloadFileList', true);
            datas.showDialog = false;
            context.emit('setInfo', datas.uploadForm);
          }
        });
      } else if (props.isAdd === 'edit') {
        const params = {
          categoryId: datas.uploadForm.categories[datas.uploadForm.categories.length - 1],
          fileName: datas.uploadForm.fileNames[0].fileName,
          remoteName: datas.uploadForm.fileNames[0].remoteName,
          tags: datas.uploadForm.fileTags,
          id: props.fileData.id
        };
        editFileList(params).then(res => {
          if (res !== false) {
            ElMessage.success('编辑成功');
            bus.$emit('reloadFileList', true);
            datas.showDialog = false;
            context.emit('setInfo', datas.uploadForm);
          }
        });
      }
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };
    // 点击上传按钮
    const addFile = () => {
      datas.uploadinputRef.click();
    };
    // 选择文件
    const handleClick = e => {
      const files = e.target.files;
      if (files && files.length > 0) {
        const params = new FormData();
        var canUpload = true;
        [...files].forEach(file => {
          if (!pdfRegex.test(file.name)) {
            ElMessage.warning(`${file.name}文件格式不符, 请上传PDF文件!`);
            canUpload = false;
          }
          const limitSize = file.size / 1024 / 1024;
          if (limitSize > 20) {
            ElMessage.warning(file.name + '超过20M, 请重新上传！');
            canUpload = false;
          } else if (limitSize === 0) {
            ElMessage.warning(file.name + '为空文件, 请重新上传！');
            canUpload = false;
          } else {
            params.append('files', file);
          }
        });
        if (!canUpload) {
          return false;
        }
        const loading = ElLoading.service({
          lock: true,
          text: '文件上传中...',
          background: 'rgba(255, 255, 255, 0.5)'
        });
        uploadFileList(params, callback => {
          const num = callback.loaded / callback.total;
          const per = num.toFixed(3) * 100;
          datas.currentProgress = per;
          loading.setText('上传进度-' + per + '%');
        }).then(res => {
          loading.close();
          if (res) {
            const remoteNameList = res.data.data.remoteNameList;
            if (remoteNameList && remoteNameList.length > 0) {
              remoteNameList.forEach((name, index) => {
                if (props.isAdd === 'edit') {
                  datas.uploadForm.fileNames[0].remoteName = name;
                  datas.uploadForm.fileNames[0].fileName = files[index].name;
                } else {
                  datas.uploadForm.fileNames.push({
                    remoteName: name,
                    fileName: files[index].name
                  });
                }
              });
              e.target.value = '';
            }
          } else {
            loading.close();
            return false;
          }
        });
        if (!files) {
          return false;
        }
      }
      // datas.uploadForm.files = files
      // files.forEach(async(file) => {
      //   const params = new FormData()
      //   params.append('file', file)
      //   params.append('name', file.name)
      //   uploadFileList(params, (callback) => {
      //     console.log(callback)
      //   }).then(res => {
      //     if (res !== false) {
      //       console.log(res.data.data)
      //       const remoteNameList = res.data.data.remoteNameList
      //       if (remoteNameList && remoteNameList.length > 0) {
      //         remoteNameList.forEach(name => {
      //           datas.uploadForm.fileNames.push({ remoteName: name, fileName: file.name })
      //         })
      //       }
      //     } else {
      //       return false
      //     }
      //   })
      //   if (!file) { return false }
      //   // readerData(file)
      // })
    };
    // 接口删除文件
    const fileDelete = row => {
      context.emit('setInfo', 'delete');
    };
    // 物理删除文件
    const fileDelete1 = (row, index) => {
      datas.uploadForm.fileNames.splice(index, 1);
      datas.uploadinputRef.value = '';
    };
    // 2预览，3下载
    const fileHandle = type => {
      if (type === 2) {
        context.emit('setInfo', 'preview');
      } else if (type === 3) {
        context.emit('setInfo', 'download');
      }
    };
    // 更新
    const updateFile = row => {
      addFile();
    };
    // 选择文件目录
    const changeFileCategory = data => {};
    // 可搜索选择框
    const createStateFilter = queryString => {
      return state => {
        return state.name.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
      };
    };
    const querySearchAsync = async (queryString, cb) => {
      var restaurants = datas.restaurants;
      var results = queryString ? restaurants.filter(createStateFilter(queryString)) : restaurants;
      clearTimeout(datas.timeout);
      datas.timeout = setTimeout(() => {
        cb(results);
      }, 3000 * Math.random());
    };
    // 输入框输入内容怎么办
    const inputName = (value, index) => {
      if (value) {
        proxy.$refs['fileName' + index].input.style.border = '';
      } else {
        proxy.$refs['fileName' + index].input.style.border = '1px solid #f56c6c';
      }
    };

    return {
      ...toRefs(datas),
      dialogSuccess,
      close,
      addFile,
      getPermissionBtn,
      handleClick,
      fileDelete,
      getNameByid,
      changeFileCategory,
      querySearchAsync,
      fileHandle,
      updateFile,
      fileDelete1,
      inputName
    };
  },
  async mounted() {
    this.restaurants = await this.getTags();
  },
  created() {},
  methods: {
    handleClose(tag) {
      // 删除标签
      // this.uploadForm.fileTags.splice(this.uploadForm.fileTags.indexOf(tag), 1)
      _.remove(this.uploadForm.fileTags, function (n) {
        return n.name === tag.name;
      });
      if (this.uploadForm.fileTags.length < 10) {
        this.disabledTag = false;
      }
    },
    showInput() {
      // 显示标签输入框
      this.inputVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.inputRef.input.focus();
        this.getTags();
      });
    },
    async handleInputConfirm(name) {
      // 添加标签
      const inputValue = this.inputValue || name;
      if (inputValue.length < 3) {
        ElMessage.warning('添加失败，请至少输入三个字');
        this.$refs.saveTagInput.$refs.inputRef.input.focus();
        this.inputVisible = false;
        this.inputValue = '';
        return false;
      }
      if (Array.isArray(this.uploadForm.fileTags) && this.uploadForm.fileTags.some(({ name }) => name === inputValue)) {
        ElMessage.warning('添加失败，标签已存在');
        this.$refs.saveTagInput.$refs.inputRef.input.focus();
        this.inputVisible = false;
        this.inputValue = '';
        return false;
      }
      var p = {
        name: inputValue,
        id: name.id ? name.id : ''
      };
      if (!name.name) {
        p.id = await this.saveNewTags(inputValue);
      }
      if (inputValue) {
        this.uploadForm.fileTags.push(p);
      }
      this.inputVisible = false;
      this.inputValue = '';
      if (this.uploadForm.fileTags.length === 10) {
        this.disabledTag = true;
      }
    },
    getTags(name) {
      var that = this;
      return new Promise((resolve, reject) => {
        tipsTags({ name: name }).then(res => {
          if (res !== false) {
            that.restaurants = res.data.data;
            resolve(res.data.data);
          }
        });
      });
    },
    saveNewTags(name) {
      // var that = this
      return new Promise((resolve, reject) => {
        saveTags({ name: name }).then(res => {
          if (res !== false) {
            resolve(res.data.data.id);
          }
        });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.upload-file {
  .upload-file-table {
    margin-top: 15px;
    :deep(.el-table__header-wrapper) {
      thead th {
        padding: 0;
        background: #f6f6f6;
        border-right: 0px;
      }
    }
  }
  .file-title {
    span {
      margin-left: 15px;
    }
  }
  .tips {
    font-size: 12px;
    height: 20px;
    line-height: 20px;
    position: absolute;
    top: -27px;
    left: 65px;
  }
  .file-upload-input {
    display: none;
    z-index: -9999;
  }
  .el-tag {
    margin-right: 10px;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
}
</style>
