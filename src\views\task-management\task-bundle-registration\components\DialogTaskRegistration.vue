<template>
  <el-dialog
    v-model="showDialog"
    custom-class="double-dialog tiny-dialog info-add"
    :title="title"
    top="5vh"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <div class="dialog-main">
      <el-form
        ref="inspectionInfoRef"
        class="formDataInfo"
        :model="formInline"
        :rules="inspectionInfoRule"
        label-width="110px"
        label-position="top"
      >
        <el-space direction="vertical">
          <el-row :gutter="60">
            <el-col :span="12">
              <el-form-item label="任务来源：" prop="regUserId">
                <el-input v-model="formInline.remark" placeholder="输入备注内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="装备类别：" prop="regUserId">
                <el-input v-model="formInline.remark" placeholder="输入备注内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检测地点：" prop="regUserId">
                <el-input v-model="formInline.remark" placeholder="输入备注内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="样品状态：" prop="regUserId">
                <el-select
                  v-model="formInline.regUserId"
                  class="owner-select"
                  placeholder="请选择登记人"
                  clearable
                  filterable
                  :filter-method="filterUserList"
                  @focus="filterUserList(null)"
                  @change="changeUser"
                >
                  <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="送样方式：" prop="regUserId">
                <el-input v-model="formInline.remark" placeholder="输入备注内容" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="供电方式：" prop="regUserId">
                <el-input v-model="formInline.remark" placeholder="输入备注内容" clearable />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="专业：" prop="serviceType">
                <el-radio-group v-model="formInline.serviceType">
                  <el-radio :label="0" size="large">输电</el-radio>
                  <el-radio :label="1" size="large">变电</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="所属运维单位：" prop="regUserId">
                <el-input v-model="formInline.remark" placeholder="输入备注内容" clearable />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="委托单位：" prop="regUserId">
                <el-input v-model="formInline.remark" placeholder="输入备注内容" clearable />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="委托单位地址：" prop="regUserId">
                <el-input v-model="formInline.remark" placeholder="输入备注内容" clearable />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="供货单位：" prop="regUserId">
                <el-input v-model="formInline.remark" placeholder="输入备注内容" clearable />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="供货单位地址：" prop="regUserId">
                <el-input v-model="formInline.remark" placeholder="输入备注内容" clearable />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="委托类型：" prop="regUserId">
                <el-input v-model="formInline.remark" placeholder="输入备注内容" clearable />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="物资分类：" prop="regUserId">
                <el-input v-model="formInline.remark" placeholder="输入备注内容" clearable />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="登记人：" prop="regUserId">
                <el-select
                  v-model="formInline.regUserId"
                  class="owner-select"
                  placeholder="请选择登记人"
                  clearable
                  filterable
                  :filter-method="filterUserList"
                  @focus="filterUserList(null)"
                  @change="changeUser"
                >
                  <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>

            <!-- <el-col :span="12">
              <el-form-item label="物资分类：" prop="materialCode">
                <el-select
                  v-model="formInline.materialCode"
                  class="owner-select"
                  placeholder="请选择物资分类"
                  clearable
                  filterable
                  :filter-method="filterMaterialList"
                  @focus="filterMaterialList(null)"
                  @change="changeMaterialType"
                >
                  <el-option
                    v-for="item in materialOptions"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
            </el-col> -->

            <el-col :span="12">
              <el-form-item label="登记日期：" prop="regDate">
                <el-date-picker
                  v-model="formInline.regDate"
                  type="date"
                  placeholder="请选择登记日期"
                  class="register-date"
                  @change="changeRegisterTime"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="截止日期：" prop="endDate">
                <el-date-picker
                  v-model="formInline.endDate"
                  type="date"
                  placeholder="请选择登记日期"
                  class="register-date"
                  @change="changeEndDate"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="服务类型：" prop="serviceType">
                <el-radio-group v-model="formInline.serviceType">
                  <el-radio :label="0" size="large">标准服务</el-radio>
                  <el-radio :label="1" size="large">加急服务</el-radio>
                  <el-radio :label="2" size="large">特急服务</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="样品回收：" prop="sampleRecycle">
                <el-radio-group id="sample-recycle" v-model="formInline.sampleRecycle">
                  <el-radio :label="0" size="large">否</el-radio>
                  <el-radio :label="1" size="large">是</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="盖章范围：">
                <el-checkbox v-model="formInline.isCnas">CNAS</el-checkbox>
                <el-checkbox v-model="formInline.isCma">CMA</el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="报告语言：">
                <el-row>
                  <el-col :span="6">
                    <el-checkbox v-model="formInline.reportCn" @change="changeReport(true)"
                      >中文报告
                    </el-checkbox></el-col
                  >
                  <el-col :span="12">
                    <el-input-number
                      v-model="formInline.reportCnNum"
                      size="small"
                      :min="0"
                      :max="999"
                      controls-position="right"
                    />份
                  </el-col>
                  <el-col :span="6" />
                </el-row>
                <el-row>
                  <el-col :span="6">
                    <el-checkbox v-model="formInline.reportEn" @change="changeReport(true)"
                      >英文报告
                    </el-checkbox></el-col
                  >
                  <el-col :span="12">
                    <el-input-number
                      v-model="formInline.reportEnNum"
                      size="small"
                      :min="0"
                      :max="999"
                      controls-position="right"
                    />份
                  </el-col>
                  <el-col :span="6" />
                </el-row>
                <el-row>
                  <el-col :span="6">
                    <el-checkbox v-model="formInline.reportBilingual" @change="changeReport(true)"
                      >中英文报告
                    </el-checkbox></el-col
                  >
                  <el-col :span="12">
                    <el-input-number
                      v-model="formInline.reportBilingualNum"
                      size="small"
                      :min="0"
                      :max="999"
                      controls-position="right"
                    />份
                  </el-col>
                  <el-col :span="6" />
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-checkbox
                      v-model="formInline.reportBilingualNone"
                      style="display: inline-block; width: 30%"
                      @change="changeReport(false)"
                      >只需结果，不需报告
                    </el-checkbox></el-col
                  >
                </el-row>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注：" prop="remark">
                <el-input v-model="formInline.remark" :rows="2" type="textarea" placeholder="输入备注内容" clearable />
              </el-form-item>
            </el-col>
          </el-row>
        </el-space>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent>保 存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref } from 'vue';
import _ from 'lodash';
import { ElMessage } from 'element-plus';
import router from '@/router/index.js';
// import { useRoute } from 'vue-router'
import { saveTaskRegistrationInfo } from '@/api/task-registration';
import { useStore } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
import { getProcessListNew } from '@/api/mas';
import { formatDate } from '@/utils/formatTime';
import { taskType } from '@/data/industryTerm';
import { getNameByid } from '@/utils/common';

export default {
  name: 'DialogTaskRegistration',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '新增任务包'
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    // console.log(proxy)
    // const lodash = inject('_')
    const store = useStore().state;
    const datas = reactive({
      currentAccountId: getLoginInfo().accountId,
      userOptions: store.common.nameList,
      copyUserOptions: store.common.nameList,
      materialOptions: store.user.materialList,
      copyMaterialOptions: store.user.materialList,
      showDialog: false,
      formInline: {
        endDate: '',
        entrustCost: '',
        entrustNo: '',
        entrustType: '0',
        id: '',
        isCma: false,
        isCnas: false,
        justResult: true,
        regDate: formatDate(new Date()),
        regUserId: getLoginInfo().accountId,
        regUserName: getNameByid(getLoginInfo().accountId),
        remark: '',
        reportBilingualNone: false,
        reportBilingual: true,
        reportBilingualNum: 1,
        reportCn: true,
        reportCnNum: 1,
        reportEn: true,
        reportEnNum: 1,
        // rowVersion: '0',
        sampleRecycle: 0,
        serviceType: 0,
        status: 0,
        materialCode: store.user.materialList[0]?.code || '',
        materialName: store.user.materialList[0]?.name || ''
        // tenantId: '0'
        // type: 1,
        // inputWarehouseNo: '',
        // wareHouseNo: '',
        // wareHouseName: '',
        // wareHousePerson: '',
        // inputWarehouseDate: '',
        // applyStatus: 1
      },
      inspectionInfo: {
        list: []
      },
      inspectionInfoRef: ref(),
      inspectionInfoRule: {
        materialCode: [{ required: true, message: '请选择物资分类' }],
        regUserId: [{ required: true, message: '请选择登记人' }],
        serviceType: [{ required: true, message: '请选择服务类型' }],
        sampleRecycle: [{ required: true, message: '请选择样品回收' }],
        regDate: [{ required: true, message: '请选择登记日期' }],
        type: [{ required: true, message: '请输入检验类型' }]
      },
      typeOptions: taskType,
      processList: [],
      copyProcessList: [],
      showEdit: false
    });

    watch(
      () => props.show,
      newValue => {
        if (newValue) {
          // console.log(props)
          datas.showDialog = newValue;
          datas.showEdit = props.isEdit;
          if (props.isEdit) {
            datas.formInline = props.info;
            if (!datas.formInline.reportCn && !datas.formInline.reportEn && !datas.formInline.reportBilingual) {
              datas.formInline.reportBilingualNone = true;
            }
          }
        }
      },
      { deep: true }
    );

    // 确定选择
    const dialogSuccess = () => {
      datas.inspectionInfoRef.validate(valid => {
        if (valid) {
          if (datas.showEdit) {
            saveTaskRegistrationInfo(datas.formInline).then(res => {
              if (res !== false) {
                datas.showDialog = false;
                context.emit('close', 'update');
                ElMessage.success('更新成功');
              }
            });
          } else {
            saveTaskRegistrationInfo(datas.formInline).then(res => {
              if (res !== false) {
                ElMessage.success('新增成功');
                datas.showDialog = false;
                // context.emit('setInfo', true)
                context.emit('close', false);
                router.push({
                  name: 'TaskRegistrationDetail',
                  query: { id: res.data.data, flag: 2 }
                });
              }
            });
          }
        }
      });
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };
    // 过滤物资分类
    const filterMaterialList = val => {
      if (val) {
        const list = [];
        datas.copyMaterialOptions.forEach(material => {
          const item = _.filter(material.name, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (material.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(material);
          }
        });
        datas.materialOptions = list;
      } else {
        datas.materialOptions = datas.copyMaterialOptions;
      }
    };
    // 过滤登记人
    const filterUserList = val => {
      if (val) {
        const list = [];
        datas.copyUserOptions.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        datas.userOptions = list;
      } else {
        datas.userOptions = datas.copyUserOptions;
      }
    };
    // 登记人-change
    const changeUser = id => {
      datas.formInline.regUserId = id;
      datas.formInline.regUserName = getNameByid(id);
    };

    // 登记人-change
    const changeMaterialType = materialCode => {
      const materialIndex = datas.copyMaterialOptions.findIndex(item => item.code === materialCode);
      datas.formInline.materialName = materialIndex === -1 ? '' : datas.copyMaterialOptions[materialIndex];
      datas.formInline.materialCode = materialCode;
    };
    // 选择登记日期
    const changeRegisterTime = time => {
      datas.formInline.regDate = formatDate(time);
    };

    const changeEndDate = time => {
      datas.formInline.endDate = formatDate(time);
    };

    // 检验类型-change
    const changeType = type => {
      datas.formInline.entrustType = type;
    };
    // 生产工序-change
    const changeProductionProcedure = no => {
      // console.log(no)
      datas.processList.forEach(item => {
        if (item.no === no) {
          datas.formInline.productionProcedure = item.name;
          datas.formInline.productionProcedureNo = item.no;
        }
      });
    };
    // 选择入库日期
    const changeInputWarehouseDate = time => {
      // console.log(time)
    };

    function changeReport(value) {
      if (value) {
        datas.formInline.reportBilingualNone = 0;
      } else {
        if (datas.formInline.reportBilingualNone) {
          datas.formInline.reportCn = 0;
          datas.formInline.reportEn = 0;
          datas.formInline.reportBilingual = 0;
        }
      }
    }

    return {
      ...toRefs(datas),
      dialogSuccess,
      close,
      filterUserList,
      filterMaterialList,
      changeUser,
      changeMaterialType,
      changeRegisterTime,
      changeEndDate,
      changeType,
      changeReport,
      changeProductionProcedure,
      changeInputWarehouseDate
    };
  },
  created() {
    this.getProcessLists();
  },
  methods: {
    // 获取生产工序列表接口
    getProcessLists() {
      var that = this;
      var param = {
        limit: '-1',
        page: '1',
        content: ''
      };
      getProcessListNew(param).then(res => {
        if (res !== false) {
          that.processList = res.data.data.list;
          that.copyProcessList = res.data.data.list;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
#sample-recycle {
  :deep(.el-radio__label) {
    margin-right: 10px;
  }
}

.formDataInfo {
  :deep(.el-input--medium .el-input__inner) {
    height: 32px;
    line-height: 32px;
  }
}
.scdd {
  position: absolute;
  left: 70px;
  top: -34px;
}
.info-add {
  .dialog-main {
    :deep(.el-space) {
      width: 100%;
      .el-space__item {
        margin: 16px 0;
        width: 100%;
      }
    }
  }
  .el-input--medium .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
  .el-divider--horizontal {
    margin: 15px 0px;
  }
  .el-form-item {
    text-align: left;
    // width: 46%;
    margin-bottom: 10px;
    :deep(.el-form-item__label) {
      font-weight: normal;
      font-size: 14px;
      color: #606266;
      padding: 0px;
    }
    :deep(.el-form-item__content) {
      .el-select {
        width: 100%;
      }
      .el-date-editor.el-input {
        width: 100%;
      }
      .el-radio-group {
        .el-radio {
          margin: 0px;
        }
        .el-radio:nth-child(2) {
          margin: 0px 5px;
        }
      }
    }
  }
}
</style>
