const COMBINATION_QUERY = 'combinationQuery';

export function setCombinationQuery(key, value) {
  const queryString = localStorage.getItem(COMBINATION_QUERY);
  let queryObject = {};
  if (queryString) {
    queryObject = JSON.parse(queryString);
    if (queryObject) {
      queryObject[key] = value;
    } else {
      queryObject = {};
      queryObject[key] = value;
    }
  }
  localStorage.setItem(JSON.stringify(queryObject));
}

export function getCombinationQuery(key) {
  const queryString = localStorage.getItem(COMBINATION_QUERY);
  const queryObject = JSON.parse(queryString);

  return queryObject[key];
}
