import request from '@/utils/request';

// 合格证（亨通）列表查询
export function getCertificatePrintList(data) {
  return request({
    url: '/api-orders/orders/certificateprint/previousList',
    method: 'post',
    data
  });
}

// 合格证（亨通）保存
export function saveCertificatePrint(data) {
  return request({
    url: '/api-orders/orders/certificateprint/saveOrUpdate',
    method: 'post',
    data
  });
}

// 获取合格证单条数据
export function getCertificateDetail(data) {
  return request({
    url: '/api-orders/orders/certificateprint/getOne',
    method: 'post',
    data
  });
}

// 成品合格证打印获取检测项目
export function certificateprintItem(sampleId) {
  return request({
    url: `/api-orders/orders/certificateprint/findFinishedProductCapability/${sampleId}`,
    method: 'get'
  });
}

/** 合格证复制，手动新增 */
export function certificateprintItemCopy(data) {
  return request({
    url: `/api-orders/orders/certificateprint/copy`,
    method: 'post',
    data
  });
}
