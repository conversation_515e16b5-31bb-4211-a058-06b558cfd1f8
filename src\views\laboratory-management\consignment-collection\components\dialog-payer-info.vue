<template>
  <el-dialog
    v-model="dialogShow"
    title="编辑付款方信息"
    :close-on-click-modal="false"
    width="880px"
    @close="handleClose"
  >
    <!-- 新增委托费用 -->
    <el-form
      v-if="dialogShow"
      ref="ruleForm"
      v-loading="dialogLoading"
      :model="formData"
      label-position="right"
      label-width="100px"
      size="small"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="付款方：" prop="payer.customerId">
            <el-select
              v-model="formData.payer.customerId"
              placeholder="请选择付款方"
              clearable
              filterable
              style="width: 100%"
              @change="handleChangeCustomer"
            >
              <el-option v-for="(val, key) in userOptionsJSON" :key="key" :label="val.name" :value="key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="公司税号：" prop="payer.taxNo">
            <el-input v-model="formData.payer.taxNo" type="text" clearable placeholder="请输入费用描述" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话：" prop="payer.phone">
            <el-input v-model="formData.payer.phone" type="text" clearable placeholder="请输入金额" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户行：" prop="payer.openingBank">
            <el-input v-model="formData.payer.openingBank" placeholder="输入开户行" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="账号：" prop="payer.acctNo">
            <el-input v-model="formData.payer.acctNo" placeholder="输入账号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="地址：" prop="payer.address">
            <el-input v-model="formData.payer.address" type="textarea" :rows="2" placeholder="输入地址" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发票邮寄：" prop="invoice.customerId">
            <el-select
              v-model="formData.invoice.customerId"
              placeholder="请选择发票邮寄"
              clearable
              filterable
              style="width: 100%"
              @change="handleChangeInvoiceCustomer"
            >
              <el-option v-for="(val, key) in userOptionsJSON" :key="key" :label="val.name" :value="key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人：" prop="invoice.contactsId">
            <el-select
              v-model="formData.invoice.contactsId"
              placeholder="请选择联系人"
              clearable
              filterable
              style="width: 100%"
              @change="handleChangeContacts"
            >
              <el-option v-for="item in invoiceContactsList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话：" prop="invoice.phone">
            <el-input v-model="formData.invoice.phone" placeholder="请输入电话" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="收件地址：" prop="invoice.address">
            <el-select v-model="formData.invoice.address" placeholder="请选择地址" clearable filterable>
              <el-option
                v-for="item in invoiceAddressList"
                :key="item.id"
                :label="item.exactAddress"
                :value="item.exactAddress"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, ref, toRefs, watch, getCurrentInstance } from 'vue';
import { saveTaskFeeInfo, getInvoiceInfo, getClientContacts, getClientAddress } from '@/api/task-registration';
import { useRoute } from 'vue-router';
import { getList } from '@/api/customerManagement';

export default {
  name: 'DialogFree',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const route = useRoute();
    const { proxy } = getCurrentInstance();
    const state = reactive({
      dialogLoading: false, // 弹出窗loading
      formData: { payer: {} }, // 表单数据
      userOptionsJSON: {},
      invoiceContactsList: [], // 联系人列表
      invoiceAddressList: [], // 地址列表
      dialogShow: false,
      ruleForm: ref()
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.formData = JSON.parse(JSON.stringify(props.detailData));
        getAllClients();
        getInvoiceContactsList(false);
        getInvoiceAddressList(false);
      }
    });
    const getAllClients = () => {
      const clientParams = {
        condition: '',
        isDesc: false,
        page: '1',
        limit: '-1',
        orderBy: '',
        isAsc: ''
      };
      getList(clientParams).then(res => {
        if (res) {
          state.userOptionsJSON = {};
          res.data.data.list.forEach(item => {
            state.userOptionsJSON[item.id] = item;
          });
        }
      });
    };
    const onSubmit = () => {
      proxy.$refs['ruleForm'].validate(valid => {
        if (valid) {
          state.dialogLoading = true;
          saveTaskFeeInfo({ ...state.formData, superId: route.query.id }).then(res => {
            state.dialogLoading = false;
            if (res) {
              proxy.$message.success(res.data.message);
              context.emit('closeDialog', true);
            }
          });
        } else {
          return false;
        }
      });
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog', false);
    };
    // 切换付款方信息
    const handleChangeCustomer = val => {
      const selectInfo = state.userOptionsJSON[val];
      state.formData.payer.latitude = selectInfo.latitude;
      state.formData.payer.longitude = selectInfo.longitude;
      state.formData.payer.customerName = selectInfo.name;
      state.loading = true;
      getInvoiceInfo(val).then(res => {
        state.loading = false;
        if (res) {
          const { data } = res.data;
          state.formData.payer.acctNo = data.acctNo;
          state.formData.payer.taxNo = data.taxNo;
          state.formData.payer.address = data.exactAddress
            ? `${data.regionState.join(',')},${data.exactAddress}`
            : `${data.regionState.join(',')}`;
          state.formData.payer.openingBank = data.openingBank;
          state.formData.payer.phone = data.phone;
        }
      });
    };
    // 切换发票抬头
    const handleChangeInvoiceCustomer = val => {
      // 联系人
      state.formData.invoice.contactsId = '';
      state.formData.invoice.contactsName = '';
      state.formData.invoice.contactsPhone = '';
      state.formData.invoice.address = '';
      if (val) {
        getInvoiceContactsList(true);
        // 发票地址
        getInvoiceAddressList(true);
      }
    };
    // 获取联系人列表
    const getInvoiceContactsList = isChange => {
      getClientContacts(state.formData.invoice.customerId).then(res => {
        if (res) {
          state.invoiceContactsList = res.data.data;
          if (isChange) {
            const defaultRow = state.invoiceContactsList.filter(item => {
              return item.isDefault;
            })[0];
            state.formData.invoice.contactsId = defaultRow.id;
            state.formData.invoice.contactsName = defaultRow.name;
            state.formData.invoice.contactsPhone = defaultRow.phone;
          }
        }
      });
    };
    // 切换联系人
    const handleChangeContacts = val => {
      const defaultRow = state.invoiceContactsList.filter(item => {
        return item.id === val;
      })[0];
      state.formData.invoice.contactsId = defaultRow?.id;
      state.formData.invoice.contactsName = defaultRow?.name;
      state.formData.invoice.contactsPhone = defaultRow?.phone;
    };
    // 获取发票地址列表
    const getInvoiceAddressList = isChange => {
      getClientAddress(state.formData.invoice.customerId).then(res => {
        if (res) {
          state.invoiceAddressList = [];
          res.data.data.forEach(item => {
            if (item.exactAddress) {
              item.exactAddress = `${item.regionState.join(',')},${item.exactAddress}`;
            } else {
              item.exactAddress = `${item.regionState.join(',')}`;
            }
            state.invoiceAddressList.push(item);
          });
          if (isChange) {
            const defaultRow = state.invoiceAddressList.filter(item => {
              return item.isDefault;
            })[0];
            state.formData.invoice.address = defaultRow?.exactAddress;
          }
        }
      });
    };
    return {
      ...toRefs(state),
      handleChangeContacts,
      handleChangeInvoiceCustomer,
      getInvoiceContactsList,
      getInvoiceAddressList,
      handleChangeCustomer,
      onSubmit,
      handleClose
    };
  }
};
</script>
<style lang="scss" scoped>
.unitClass {
  text-align: center;
  margin-left: 5px;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  background-color: rgb(245, 247, 250);
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 100%;
}
:deep(.el-select) {
  width: 100%;
}
:deep(.el-input-number--small) {
  width: 100%;
}
</style>
