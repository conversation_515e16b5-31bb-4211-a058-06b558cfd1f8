<template>
  <!-- 检测信息弹出框 -->
  <el-dialog
    v-model="dialogShow"
    title="检测信息"
    :close-on-click-modal="false"
    width="680px"
    :show-close="false"
    :before-close="handleClose"
  >
    <el-form v-if="dialogShow" ref="ruleForm" :model="formData" label-position="top">
      <el-row v-loading="dialogLoading" :gutter="20">
        <el-col v-for="viewItem in pageViewGroup" :key="viewItem.fieldKey" :span="12">
          <el-form-item :label="`${viewItem.fieldName}：`" :prop="viewItem.fieldKey">
            <template v-if="viewItem.fieldType == 'text'">
              <el-input
                v-model="formData[viewItem.fieldKey]"
                :type="viewItem.fieldKey == 'remark' ? 'textarea' : 'text'"
                :rows="3"
                :maxlength="100"
                :placeholder="`请输入${viewItem.fieldName}`"
              />
            </template>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose">取 消</el-button>
        <el-button :loading="dialogLoading" type="primary" @click="handleSave" @keyup.prevent @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, toRefs, watch, getCurrentInstance } from 'vue';
import { saveOrUpdate } from '@/api/order';
export default {
  name: 'DialogDetection',
  components: {},
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    pageView: {
      type: Object,
      default: function () {
        return {};
      }
    },
    info: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      dialogShow: false,
      formData: {},
      dialogLoading: false,
      pageViewGroup: {}
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialog;
      if (state.dialogShow) {
        state.detailInfo = {};
        state.pageViewGroup = props.pageView;
        state.dialogLoading = false;
        state.formData = JSON.parse(JSON.stringify(props.info));
      }
    });
    // 确认
    const handleSave = async () => {
      state.dialogLoading = true;
      const response = await saveOrUpdate({ entityList: [state.formData] }).finally(() => {
        state.dialogLoading = false;
      });
      if (response) {
        proxy.$message.success('操作成功！');
        state.dialogShow = false;
        context.emit('close', true);
      }
    };
    // 关闭抽屉
    const handleClose = () => {
      state.dialogShow = false;
      context.emit('close', false);
    };
    return { ...toRefs(state), handleClose, handleSave };
  }
};
</script>
<style lang="scss" scoped></style>
