<template>
  <!-- 任务分配列表 -->
  <ListLayout>
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="sample-order-form" @submit.prevent>
        <el-form-item prop="param">
          <el-input
            v-model="formInline.param"
            v-trim
            v-focus
            placeholder="请输入编号/样品名称/型号规格"
            class="ipt-360"
            size="large"
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter="onSubmit"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button size="large" type="primary" @click="onSubmit">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
          <el-button size="large" type="text" class="searchBtn" @click="search" @keyup.prevent @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']" />
          </el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <div class="btn-group">
        <el-button
          v-if="radioData === '待同步' && getPermissionBtn('batchJudgement')"
          type="primary"
          size="large"
          icon="el-icon-refresh"
          :disabled="multipleSelection.length === 0"
          @click="batchJudgement()"
          >批量判定</el-button
        >
        <el-button
          v-if="radioData === '待同步' && getPermissionBtn('ResultReturn')"
          type="primary"
          size="large"
          icon="el-icon-refresh"
          :disabled="multipleSelection.length === 0"
          @click="resultBack()"
          >结论回传</el-button
        >
        <el-button
          v-if="(radioData === '待同步' || radioData === '已同步') && getPermissionBtn('batchFinish')"
          type="primary"
          size="large"
          icon="el-icon-refresh"
          :disabled="multipleSelection.length === 0"
          @click="batchFinish()"
          >批量完成</el-button
        >
        <el-button
          v-if="radioData === '待认领' && getPermissionBtn('allocationBatchClaim')"
          type="primary"
          size="large"
          icon="el-icon-document-copy"
          :disabled="multipleSelection.length === 0"
          @click="multipleGet()"
          >批量认领</el-button
        >
        <el-button
          v-if="getPermissionBtn('allocationExport')"
          type="primary"
          size="large"
          :loading="listLoading"
          @click="exportExcel()"
          ><span class="iconfont tes-task-issued" @keyup.prevent @keydown.enter.prevent />导出</el-button
        >
      </div>
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" :model="searchForm" label-width="110px" label-position="right">
            <el-form-item label="物资分类：">
              <el-radio-group v-model="searchForm.prodType" max="1">
                <el-radio-button style="margin-right: 4px" @change="changeProdType('all')">不限</el-radio-button>
                <el-radio-button
                  v-for="item in types"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                  @change="changeProdType(item)"
                />
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="tenantInfo.type === 0 ? '来样日期：' : '登记日期：'">
              <el-date-picker
                v-model="searchForm.rukuDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="changeRuku"
              />
            </el-form-item>
            <el-form-item label="下达日期：">
              <el-date-picker
                v-model="searchForm.xiadaDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="changeXaida"
              />
            </el-form-item>
            <el-form-item label="试验负责人：" class="seal-staff-name">
              <el-select
                v-model="formInline.ownerId"
                class="owner-select"
                placeholder="请选择"
                clearable
                filterable
                size="small"
                :filter-method="filterUserList"
                @focus="filterUserList(null)"
              >
                <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="来源：" class="seal-staff-name">
              <el-select
                v-model="formInline.thirdType"
                class="owner-select"
                placeholder="请选择"
                clearable
                size="small"
              >
                <el-option v-for="item in thirdTypeOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-radio-group v-model="radioData" size="small" @change="changeRadio">
        <el-radio-button label="全部" />
        <!-- <el-radio-button label="待认领">
          待认领<span v-if="tableNumber.unClaimCount" class="listNumber">{{
            '（' + tableNumber.unClaimCount + '）'
          }}</span>
        </el-radio-button> -->
        <el-radio-button
          label="待派发"
          :class="{
            isHaveNew: oldTableNumber.unAssignmentCount !== tableNumber.unAssignmentCount
          }"
        >
          待派发
          <Digitalscroll
            :start="Number(oldTableNumber.unAssignmentCount)"
            :end="Number(tableNumber.unAssignmentCount)"
            duration="3"
          />
          <!-- <span v-if="tableNumber.unAssignmentCount" class="listNumber">{{ '（'+ tableNumber.unAssignmentCount +'）' }}</span> -->
        </el-radio-button>
        <el-radio-button
          label="已派发"
          :class="{
            isHaveNew: oldTableNumber.assignmentCount !== tableNumber.assignmentCount
          }"
        >
          已派发
          <Digitalscroll :start="Number(oldTableNumber.assignmentCount)" :end="Number(tableNumber.assignmentCount)" />
        </el-radio-button>
        <!-- <el-radio-button label="超期待派发">
          超期待派发<span v-if="tableNumber.extendedCount" class="listNumber">{{
            '（' + tableNumber.extendedCount + '）'
          }}</span>
        </el-radio-button>
        <el-radio-button label="已完成" />
        <el-radio-button v-if="getPermissionBtn('ResultReturn')" label="待同步">
          待同步<span v-if="tableNumber.unDataReturnCount" class="listNumber">{{
            '（' + tableNumber.unDataReturnCount + '）'
          }}</span>
        </el-radio-button>
        <el-radio-button
          v-if="getPermissionBtn('ResultReturn')"
          label="已同步"
        />
        <el-radio-button
          v-if="getPermissionBtn('allocationCancelBtn')"
          label="已作废" -->
        />
        <el-radio-button v-if="getPermissionBtn('allocationCancelBtn')" label="已退回" />
        <!--      <el-radio-button label="超期已派发" /> &ndash;&gt;-->
      </el-radio-group>
      <el-checkbox v-model="giveMeChecked" class="only-me" @change="giveMeChange">仅看我的</el-checkbox>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table allocation-table base-table format-height-table"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column
        type="selection"
        :width="colWidth.checkbox"
        align="center"
        fixed="left"
        :selectable="selectable"
      />
      <el-table-column label="序号" type="index" width="50" />
      <el-table-column
        :label="tenantInfo.type === 1 ? '申请单号' : '委托编号'"
        prop="newId"
        :width="colWidth.orderNo"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span class="nowrap blue-color" @click.stop="jumpApplicationDetail(row)">{{
            row.presentationCode || row.no || '--'
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="样品编号" prop="id" :width="colWidth.orderNo" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.isUrgent" class="urgent">急</span>
          <span class="blue-color" @click="handleSampleOrdersDetail(row)">
            {{ row.secSampleNum || '--' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="样品名称" prop="name" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.mateName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="型号规格" prop="xhgg" :min-width="colWidth.model" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.prodType || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="完成情况" prop="newId" :width="140" show-overflow-tooltip>
        <template #header>
          <span
            >完成情况
            <el-tooltip content="待提交/待审核/已通过" placement="top" effect="light">
              <i class="iconfont tes-title" />
            </el-tooltip>
          </span>
        </template>
        <template #default="{ row }">
          <span>{{ row.schedule ? row.schedule : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="试验负责人" prop="user" :width="colWidth.person">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.ownerId) || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" :width="colWidth.status">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="row.status === 2 || row.status === 4 ? 'success' : 'warning'">
            {{ filterStatus(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否超期" prop="wzfl" :width="colWidth.status" align="left" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="row.overdue ? 'danger' : 'success'">{{
            row.overdue ? '超期' : '正常'
          }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column
        v-if="tenantInfo.type === 0"
        label="物资分类"
        prop="mateName"
        :width="colWidth.typeGroup"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.mateTypeStr || '--' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="截止日期" prop="rukuDate" :width="colWidth.date" sortable>
        <template #default="{ row }">
          <span>{{ formatDate(row.createTime) }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        v-if="tenantInfo.type === 1"
        label="物料分组"
        prop="wzfl"
        :width="colWidth.typeGroup"
        align="left"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.materialGroup }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="tenantInfo.type === 1"
        label="物料编号"
        prop="materialNo"
        :width="colWidth.orderNo"
        align="left"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.materialNo }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="来源"
        prop="thirdType"
        show-overflow-tooltip
        align="left"
        min-width="70"
      >
        <template #default="{ row }">
          <div>
            {{
              row.thirdType === 0 ? 'ERP' : row.thirdType === 1 ? 'MES' : '自建'
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="第三方单号"
        prop="thirdNo"
        show-overflow-tooltip
        align="left"
        sortable
        :width="colWidth.orderNo"
      >
        <template #default="{ row }">
          <div>{{ row.thirdNo || '--' }}</div>
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        label="下达日期"
        prop="assignedTime"
        :width="colWidth.date"
        sortable
      >
        <template #default="{ row }">
          <span>{{ formatDate(row.assignedTime) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="试验日期"
        prop="assignedTime"
        :width="colWidth.dates"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ formatDate(row.startDate) || '--' }}</span>
          ~
          <span>{{ formatDate(row.finishedDate) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="检验结论"
        prop="reportType"
        :min-width="colWidth.result"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{
            row.reportType.toString() === '0'
              ? '合格'
              : row.reportType.toString() === '1'
                ? '不合格'
                : '--'
          }}{{
            row.reportType.toString() === '1'
              ? '(' + row.disposalType + ')' + row.disposalNo
              : ''
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="同步状态"
        prop="thirdSyncStatus"
        :width="colWidth.status"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{
            row.thirdSyncStatus === 0
              ? '待同步'
              : row.thirdSyncStatus === 1
                ? '已同步'
                : '--'
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="tenantInfo.type === 1"
        label="检验对象"
        prop="ProductionOrderNo"
        align="left"
        :width="colWidth.objectNo"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="nowrap">
            {{
              row.type === 1
                ? row.inputWarehouseNo || '--'
                : row.productionOrderNo || '--'
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="tenantInfo.type === 1"
        label="对象位置"
        prop="wareHouseName"
        align="left"
        :min-width="colWidth.name"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div v-if="row.type === 1" class="nowrap">
            {{ row.wareHouseName }}
          </div>
          <div v-else class="nowrap">
            {{ row.productionProcedure }}{{ row.productionStation }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="tenantInfo.type === 1"
        label="对象名称"
        prop="customerName"
        align="left"
        :min-width="colWidth.name"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div v-if="row.type === 1" class="nowrap">
            {{ row.supplierName ? row.supplierName : '--' }}
          </div>
          <div v-else class="nowrap">
            {{ row.customerName ? row.customerName : '--' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="检验策略"
        prop="inspectionStrategyId"
        align="left"
        :width="colWidth.projectName"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="nowrap">
            {{
              row.inspectionStrategyName
                ? row.inspectionStrategyName
                : '--'
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="tenantInfo.type === 1"
        label="批次"
        prop="batchNo"
        align="left"
        :width="colWidth.batch"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="nowrap">{{ row.batchNo ? row.batchNo : '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="tenantInfo.type === 1"
        label="盘号"
        prop="reelNo"
        align="left"
        :width="colWidth.plate"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="nowrap">{{ row.reelNo ? row.reelNo : '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="样品数量"
        prop="sampleNum"
        align="left"
        :width="colWidth.amount"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div v-if="row.sampleNum" class="nowrap">
            {{ row.sampleNum
            }}{{ filterSampleUnitToName(row.sampleUnit) || row.sampleUnit }}
          </div>
          <div v-else class="nowrap">--</div>
        </template>
      </el-table-column> -->

      <el-table-column
        label="操作"
        :width="colWidth.operationMultiple"
        prop="caozuo"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="scope">
          <div v-if="formInline.isInvalidated === '1'">
            <span v-if="getPermissionBtn('allocationDetail')" class="blue-color" @click="handleDetail(scope.row, true)"
              >查看</span
            >
            <span v-if="getPermissionBtn('allocationCancelBtn')" class="blue-color" @click="handleRestore(scope.row)"
              >还原</span
            >
          </div>
          <div v-else>
            <span
              v-if="getPermissionBtn('allocationBtn') && scope.row.ownerId === accountId"
              class="blue-color"
              @click="handleDetail(scope.row)"
              >分配</span
            >
            <span v-if="getPermissionBtn('allocationDetail')" class="blue-color" @click="handleDetail(scope.row, true)"
              >查看</span
            >
            <span
              v-if="getPermissionBtn('allocationClaim') && scope.row.status === 0"
              class="blue-color"
              @click="handleClaim(scope.row)"
              >认领</span
            >
            <span v-if="getPermissionBtn('allocationCancelBtn')" class="blue-color" @click="handleCancel(scope.row)"
              >作废</span
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row>
      <!-- <el-col :span="1" style="text-align: left; padding-top: 10px;">
        <el-button
          type="primary"
          size="small"
          :disabled="multipleSelection.length === 0"
          @click="handleUrgent()"
        >加急</el-button></el-col>
      <el-col :span="2" style="text-align: left; padding-top: 10px;">
        <el-button
          size="small"
          :disabled="multipleSelection.length === 0"
          @click="handleUrgent('cancle')"
        >取消加急</el-button>
      </el-col> -->
      <el-col :span="24">
        <pagination
          v-show="total > 0"
          :page="listQuery.page"
          :limit="listQuery.limit"
          :total="total"
          @pagination="getList"
        />
      </el-col>
    </el-row>
    <template #other>
      <!-- 认领、批量认领 -->
      <claim
        :is-show="claimDialogVisible"
        :samples-info="samplesInfo"
        :mange-list="mangeList"
        :multiple-selection="multipleSelection"
        @setInfo="getClaimSInfo"
        @close="closeClaimModel()"
      />
      <BatchJudgement
        :dialog-visible="showJudgementDialog"
        @close-dialog="closeJudgementDialog"
        @save-judgement="saveJudgement"
      />
      <el-dialog v-model="showFinishDialog" title="确认完成" width="500px" :close-on-click-modal="false">
        <CustomPanel
          :has-margin-bottom="true"
          :panel-margin-width="0.8"
          :has-panel-header="false"
          panel-min-height="80px"
          class="tip-panel"
          style="background-color: rgb(237, 250, 255)"
        >
          <div class="tip-message">
            <el-row>
              <el-col :span="2" style="text-align: center"><i class="el-icon-warning info-icon" style="" /></el-col>
              <el-col :span="22"><span style="line-height: 2.571429rem; font-size: 1.2rem">提示信息</span></el-col>
            </el-row>
            <el-row>
              <el-col :span="2" />
              <el-col :span="22">
                <p class="tip-text">完成检测后，样品下的所有已派发项目将自动结束。</p>
                <p class="tip-text">如已经开始试验，则不允许结束检测。</p>
                <p class="tip-text">是否确认继续完成样品检测？</p>
              </el-col>
            </el-row>
          </div>
        </CustomPanel>
        <template #footer>
          <el-button @click="cancelFinishDialog">取消</el-button>
          <el-button type="primary" @click="submitBatchFinish">确定</el-button>
        </template>
      </el-dialog>
      <el-dialog v-model="showUnFinishedDialog" title="未完成检测样品" width="500px" :close-on-click-modal="false">
        <CustomPanel
          :has-margin-bottom="true"
          :panel-margin-width="0.8"
          :has-panel-header="false"
          panel-min-height="80px"
          class="tip-panel"
          style="background-color: rgb(237, 250, 255)"
        >
          <div class="tip-message">
            <el-row>
              <el-col :span="2" style="text-align: center"><i class="el-icon-warning warning-icon" style="" /></el-col>
              <el-col :span="22"
                ><span style="line-height: 2.571429rem; font-size: 0.9rem"
                  >以下样品因已有检测数据或报告数据不能完成检测</span
                ></el-col
              >
            </el-row>
            <el-row>
              <el-col :span="2" />
              <el-col :span="22">
                <el-tag v-for="sample in unFinishedSampleList" :key="sample" type="info" effect="dark">
                  {{ sample }}
                </el-tag>
              </el-col>
            </el-row>
          </div>
        </CustomPanel>
        <template #footer>
          <el-button type="primary" @click="submitUnfinishTip">确定</el-button>
        </template>
      </el-dialog>
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import router from '@/router/index.js';
import ListLayout from '@/components/ListLayout';
import Pagination from '@/components/Pagination';
import UserTag from '@/components/UserTag';
import {
  // getDistributionList,
  claimSample,
  cancelSample,
  restoreSample,
  getNumberApi,
  unUrgentApi,
  urgentApi
} from '@/api/allocation';
import { getTaskViewList } from '@/api/task-management';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { mapGetters, useStore } from 'vuex';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import _ from 'lodash';
import { checkPermissionList } from '@/api/permission';
import { permissionTypeList } from '@/utils/permissionList';
import Claim from './components/claim';
import { thirdReportReturn } from '@/api/inspection-application';
import { filterSampleUnitToName } from '@/utils/formatJson';
import { colWidth } from '@/data/tableStyle';
import { getInspectionList } from '@/api/inspection-application';
import BatchJudgement from './components/BatchJudgement';
import { saveBatchJudgement, saveBatchFinish } from '@/api/order';
import Digitalscroll from '@/components/DigitalScroll';
import { getStrategy } from '@/api/strategy';
import CustomPanel from '@/components/PageComponents/CustomPanel.vue';

export default {
  name: 'TaskAllocation',
  components: { ListLayout, Pagination, Claim, UserTag, BatchJudgement, Digitalscroll, CustomPanel },
  setup() {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const editFrom = ref(null);
    const otherForm = reactive({
      accountId: getLoginInfo().accountId,
      tableRef: ref(),
      allList: [],
      giveMeChecked: false,
      activeName: '0',
      showS: false,
      mangeList: [],
      tenantType: store.user.tenantInfo.type,
      formInline: {
        param: '',
        assignedEndTime: '',
        assignedStartTime: '',
        startTime: '',
        endTime: '',
        ownerId: '',
        prodType: '',
        status: '1',
        thirdType: '',
        isInvalidated: '0'
      },
      searchForm: {
        prodType: '',
        rukuDateRange: '',
        xiadaDateRange: ''
      },
      types: store.user.materialList,
      userOptions: store.common.nameList,
      copyUserOptions: store.common.nameList,
      tableNumber: {}, // 各个状态的数据
      oldTableNumber: {}, // 各个状态的数据
      list: [],
      content: '',
      radioData: '待派发',
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        sort: 'DESC'
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ],
      claimDialogVisible: false,
      samplesInfo: {},
      multipleSelection: [],
      thirdTypeOptions: [
        { id: 0, name: 'ERP' },
        { id: 1, name: 'MES' },
        { id: 2, name: '自建' }
      ],
      showJudgementDialog: false,
      showFinishDialog: false,
      unFinishedSampleList: [],
      showUnFinishedDialog: false
    });

    function onSubmit() {
      // console.log(otherForm.formInline)
      // console.log(otherForm.searchForm)
      proxy.getList();
    }

    function reset() {
      // console.log('reset')
      editFrom.value.resetFields();
      otherForm.formInline = {
        param: '',
        assignedEndTime: '',
        assignedStartTime: '',
        startTime: '',
        endTime: '',
        ownerId: '',
        mate_type: '',
        status: '1',
        thirdType: ''
      };
      otherForm.radioData = '待派发';
      otherForm.searchForm = {
        prodType: '',
        rukuDateRange: '',
        xiadaDateRange: ''
      };
      otherForm.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        sort: 'DESC'
      };
      proxy.getList();
    }

    const search = () => {
      otherForm.showS = !otherForm.showS;
      if (otherForm.activeName === '0') {
        otherForm.activeName = '1';
      } else {
        otherForm.activeName = '0';
      }
    };

    // console.log(store.common.nameList)

    const sortChange = data => {
      const { prop, order } = data;
      // console.log(prop)
      // console.log(order)
      otherForm.listQuery.orderBy = prop;
      if (order === 'ascending') {
        otherForm.listQuery.isAsc = true;
      } else if (order === 'descending') {
        otherForm.listQuery.isAsc = false;
      } else {
        otherForm.listQuery.isAsc = null;
      }
      proxy.getList();
    };
    // 导出
    const exportExcel = () => {
      otherForm.listLoading = true;
      getTaskViewList({ limit: '-1', page: '1', ...otherForm.formInline }).then(res => {
        otherForm.listLoading = false;
        if (res) {
          otherForm.allList = [];
          res.data.data.list.forEach(row => {
            // 申请单号' : '委托编号
            row.sqwtdh = row.presentationCode || row.no || '';
            // '来样日期' : '登记日期'
            row.createTime = formatDate(row.createTime);
            // 下达日期
            row.assignedTime = formatDate(row.assignedTime);
            // 试验负责人
            row.ownerId = getNameByid(row.ownerId) ? getNameByid(row.ownerId) : row.ownerId;
            // 来源
            row.thirdType = row.thirdType === 0 ? 'ERP' : row.thirdType === 1 ? 'MES' : '自建';
            // 是否超期
            row.overdue = row.overdue ? '超期' : '正常';
            // 状态
            row.status = filterStatus(row.status);
            // 试验日期
            row.syrq = formatDate(row.startDate) + '~' + formatDate(row.finishedDate);
            // 检验对象
            row.jydx = row.type === 1 ? row.inputWarehouseNo || '--' : row.productionOrderNo;
            // 检验结果
            if (row.reportType === '0') {
              row.reportType = '合格';
            } else if (row.reportType === '1') {
              row.reportType = '不合格' + '(' + row.disposalType + ')';
            } else {
              row.reportType = row.disposalNo;
            }
            // 对象位置
            if (row.type === 1) {
              row.dxwz = row.wareHouseName;
            } else {
              row.dxwz = row.productionProcedure + row.productionStation;
            }
            // 样品数量
            row.sampleNum = row.sampleNum + filterSampleUnitToName(row.sampleUnit) || row.sampleUnit;
            // 对象名称
            row.dxmc = row.type === 1 ? row.supplierName : row.customerName;
            // 同步状态
            row.thirdSyncStatus = row.thirdSyncStatus === 0 ? '待同步' : row.thirdSyncStatus === 1 ? '已同步' : '';
            otherForm.allList.push(row);
          });
          var tHeader = [];
          var filterVal = [];
          if (otherForm.tenantType === 0) {
            tHeader = [
              '样品编号',
              '委托编号',
              '完成情况',
              '来样日期',
              '试验负责人',
              '来源',
              '第三方单号',
              '状态',
              '是否超期',
              '下达日期',
              '试验日期',
              '样品名称',
              '检验结果',
              '同步状态',
              '型号规格',
              '样品数量',
              '物资分类'
            ];
            filterVal = [
              'secSampleNum',
              'sqwtdh',
              'schedule',
              'createTime',
              'ownerIds',
              'thirdType',
              'thirdNo',
              'status',
              'overdue',
              'assignedTime',
              'syrq',
              'mateName',
              'reportType',
              'thirdSyncStatus',
              'prodType',
              'sampleNum',
              'mateTypeStr'
            ];
          } else if (otherForm.tenantType === 1) {
            tHeader = [
              '样品编号',
              '申请单号',
              '完成情况',
              '登记日期',
              '试验负责人',
              '来源',
              '第三方单号',
              '状态',
              '是否超期',
              '下达日期',
              '试验日期',
              '样品名称',
              '检验结果',
              '同步状态',
              '型号规格',
              '检验对象',
              '对象位置',
              '对象名称',
              '批次',
              '盘号',
              '样品数量',
              '物料分组'
            ];
            filterVal = [
              'secSampleNum',
              'sqwtdh',
              'schedule',
              'createTime',
              'ownerIds',
              'thirdType',
              'thirdNo',
              'status',
              'overdue',
              'assignedTime',
              'syrq',
              'mateName',
              'reportType',
              'thirdSyncStatus',
              'prodType',
              'jydx',
              'dxwz',
              'dxmc',
              'batchNo',
              'reelNo',
              'sampleNum',
              'materialGroup'
            ];
          } else {
            tHeader = [
              '样品编号',
              '委托编号',
              '完成情况',
              '登记日期',
              '试验负责人',
              '来源',
              '第三方单号',
              '状态',
              '是否超期',
              '下达日期',
              '试验日期',
              '样品名称',
              '检验结果',
              '同步状态',
              '型号规格',
              '样品数量'
            ];
            filterVal = [
              'secSampleNum',
              'sqwtdh',
              'schedule',
              'createTime',
              'ownerIds',
              'thirdType',
              'thirdNo',
              'status',
              'overdue',
              'assignedTime',
              'syrq',
              'mateName',
              'reportType',
              'thirdSyncStatus',
              'prodType',
              'sampleNum'
            ];
          }
          export2Excel(tHeader, filterVal);
        }
      });
    };
    const export2Excel = (tHeader, filterVal) => {
      otherForm.listLoading = true;
      var fileName = '检测分配';
      import('@/utils/Export2Excel').then(excel => {
        const data = formatJson(filterVal, otherForm.allList);
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: fileName,
          autoWidth: true,
          bookType: 'xlsx'
        });
        otherForm.listLoading = false;
        proxy.$message.success('导出成功！');
      });
    };
    const formatJson = (filterVal, jsonData) => {
      return jsonData.map(v =>
        filterVal.map(j => {
          return v[j];
        })
      );
    };
    const handleSelectionChange = val => {
      // console.log(val)
      otherForm.multipleSelection = val;
    };
    // 是否禁用checkbox
    const selectable = (row, index) => {
      // if (row.examineByUserId !== otherForm.currentAccountId && row.examineStatus !== 1) {
      //   return false
      // } else {
      //   return true
      // }
      return true;
    };
    // 分配点击  查看点击
    const handleDetail = (row, readOnly = false) => {
      // console.log(row)
      // console.log(row.ownerId, otherForm.accountId)
      router.push({
        name: 'TaskAllocationDetail',
        params: {
          sampleId: row.sampleId,
          status: row.status,
          id: row.id,
          isView: readOnly ? 1 : 0
        }
      });
    };
    // 点击样品编号跳转到样品详情页面
    const handleSampleOrdersDetail = row => {
      router.push({
        path: '/experiment/detail',
        query: {
          orderId: row.orderId,
          sampleId: row.sampleId
        }
      });
    };

    const handleEdit = row => {
      // console.log(row)
    };

    const inputValue = data => {
      // console.log(data)
      if (typeof data === 'string') {
        otherForm.content = data;
      } else {
        otherForm.content = '';
      }
    };

    // 过滤状态
    const filterStatus = status => {
      const map = {
        0: '待认领',
        1: '待派发',
        2: '已派发',
        3: '报告审批',
        4: '已完成',
        11: '超期待派发',
        21: '超期已派发',
        22: '检测中',
        33: '已退回',
        待同步: '待同步',
        已同步: '已同步'
      };
      return map[status];
    };
    // 切换tab
    const changeRadio = value => {
      switch (value) {
        case '待认领':
          setQuickQuery('0', '', '0');
          break;
        case '待派发':
          setQuickQuery('1', '', '0');
          break;
        case '已派发':
          setQuickQuery('2', '', '0');
          break;
        case '超期待派发':
          setQuickQuery('11', '', '0');
          break;
        case '超期已派发':
          setQuickQuery('21', '', '0');
          break;
        case '已完成':
          setQuickQuery('4', '', '0');
          break;
        case '待同步':
          setQuickQuery('', '0', '0');
          break;
        case '已同步':
          setQuickQuery('', '1', '0');
          break;
        case '已作废':
          setQuickQuery('', '', '1');
          break;
        default:
          setQuickQuery('', '', '');
          break;
      }
      proxy.getList();
      if (value === '待派发') {
        getNumber(false, 'unAssignmentCount');
      } else if (value === '已派发') {
        getNumber(false, 'assignmentCount');
      }
    };

    /**
     * 设置快捷查询的查询条件
     */
    function setQuickQuery(status = '', thirdSyncStatus = '', isInvalidated = '0') {
      otherForm.formInline.status = status;
      otherForm.formInline.thirdSyncStatus = thirdSyncStatus;
      otherForm.formInline.isInvalidated = isInvalidated;
    }

    // 高级搜索-物资分类-change
    const changeProdType = type => {
      // console.log(type)
      if (type === 'all') {
        otherForm.formInline.mateType = '';
      } else {
        otherForm.formInline.mateType = type.code;
      }
    };
    // 高级搜索-入库日期-change
    const changeRuku = date => {
      otherForm.formInline.startTime = date ? formatDate(date[0]) : '';
      otherForm.formInline.endTime = date ? formatDate(date[1]) : '';
    };
    // 高级搜索-下达日期-change
    const changeXaida = date => {
      otherForm.formInline.assignedStartTime = date ? formatDate(date[0]) : '';
      otherForm.formInline.assignedEndTime = date ? formatDate(date[1]) : '';
    };
    // 过滤试验员
    const filterUserList = val => {
      if (val) {
        const list = [];
        otherForm.copyUserOptions.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        otherForm.userOptions = list;
      } else {
        otherForm.userOptions = otherForm.copyUserOptions;
      }
    };
    // 结果回传
    const resultBack = () => {
      ElMessageBox({
        title: '同步确认',
        message: '检测结果同步后将不可撤回，是否确认同步?',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          const loading = ElLoading.service({
            lock: true,
            text: '数据同步中，请稍后...',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          var params = {
            thirdDataReturnVoList: []
          };
          otherForm.multipleSelection.forEach(ms => {
            const p = {
              disposalNo: ms.disposalNo,
              disposalProcessId: ms.disposalProcessId,
              disposalType: ms.disposalType,
              disposalTypeCode: ms.disposalTypeCode,
              reportProcessId: ms.reportProcessId,
              reportType: ms.reportType,
              sampleId: ms.sampleId,
              secSampleNum: ms.secSampleNum,
              thirdNo: ms.thirdNo,
              thirdType: ms.thirdType
            };
            params.thirdDataReturnVoList.push(p);
          });
          thirdReportReturn(params).then(res => {
            if (res !== false) {
              // console.log(res)
              loading.close();
              ElMessage.success('同步成功!');
            } else {
              loading.close();
              ElMessage.error('同步失败!');
            }
          });
        })
        .catch(() => {});
    };
    // 获取各个状态的数量
    const getNumber = (isAll, type) => {
      getNumberApi(otherForm.formInline).then(res => {
        if (res) {
          otherForm.tableNumber = res.data.data;
          if (isAll) {
            otherForm.oldTableNumber = JSON.parse(JSON.stringify(otherForm.tableNumber));
          } else {
            otherForm.oldTableNumber[type] = otherForm.tableNumber[type];
          }
        }
      });
    };
    getNumber(true);
    // #region 批量判定
    // 批量判定
    const batchJudgement = () => {
      otherForm.showJudgementDialog = true;
    };

    const closeJudgementDialog = value => {
      otherForm.showJudgementDialog = value;
    };

    const saveJudgement = batchFormData => {
      const params = {
        reportType: 0,
        sampleIdList: []
      };
      otherForm.multipleSelection.forEach(item => {
        params.sampleIdList.push(item.sampleId);
      });
      params.reportType = batchFormData.result;
      saveBatchJudgement(params).then(res => {
        if (res.data.code === 200) {
          if (batchFormData.progress === 1) {
            submitBatchFinish();
          } else {
            proxy.getList();
            ElMessage.success('批量判定成功!');
          }
        } else {
          ElMessage.error(`${res.data.message}`);
        }
      });
    };

    // #endregion

    // #region 批量认领

    // 批量认领
    const multipleGet = () => {
      // console.log('multipleGet')
      otherForm.claimDialogVisible = true;
    };
    // 认领
    const handleClaim = row => {
      // console.log(row)
      otherForm.samplesInfo = row;
      otherForm.multipleSelection = [];
      otherForm.multipleSelection.push(row);
      otherForm.claimDialogVisible = true;
    };
    // 获取认领弹出框数据
    const getClaimSInfo = data => {
      // console.log(data)
      var params = {
        samplesVoList: []
      };
      if (data.formdata.length > 0) {
        data.formdata.forEach(d => {
          const p = {
            id: d.id,
            status: d.status,
            ownerId: d.ownerId,
            assignedTime: formatDate(new Date()),
            assignedRemark: d.assignedRemark,
            startDate: d.startDate,
            finishedDate: d.finishedDate
          };
          params.samplesVoList.push(p);
        });
      }
      claimSamples(params);
    };
    // 取消认领弹出框
    const closeClaimModel = data => {
      // console.log(data)
      otherForm.claimDialogVisible = false;
    };
    // 认领接口
    const claimSamples = param => {
      claimSample(param).then(res => {
        if (res !== false) {
          // console.log(res)
          ElMessage.success(res.data.message);
          otherForm.claimDialogVisible = false;
          getNumber(false);
          proxy.getList();
        }
      });
    };

    // 查看申请详情
    const jumpApplicationDetail = row => {
      getInspectionList({ param: `${row.presentationCode}` }).then(res => {
        if (res && res.status === 200) {
          router.push({
            name: 'TestAllocationApplication',
            query: { id: res.data.data.list[0].id, flag: 1 }
          });
        }
      });
    };

    const handleCancel = row => {
      if (row.sampleId) {
        cancelSample(row.sampleId).then(res => {
          if (res.data.code === 200) {
            ElMessage.success(`样品 ${row.secSampleNum} 已成功作废！`);
            proxy.getList();
          }
        });
      }
    };
    // 仅看我的
    const giveMeChange = check => {
      if (check) {
        otherForm.formInline.ownerId = otherForm.accountId;
      } else {
        otherForm.formInline.ownerId = '';
      }
      getNumber(true);
      proxy.getList();
    };
    const handleRestore = row => {
      if (row.sampleId) {
        restoreSample(row.sampleId).then(res => {
          if (res.data.code === 200) {
            ElMessage.success(`样品 ${row.secSampleNum} 已成功还原！`);
            proxy.getList();
          }
        });
      }
    };
    const handleRowClick = row => {
      if (
        otherForm.multipleSelection.some(item => {
          return item.secSampleNum === row.secSampleNum;
        })
      ) {
        otherForm.multipleSelection = otherForm.multipleSelection.filter(item => {
          return item.secSampleNum !== row.secSampleNum;
        });
      } else {
        otherForm.multipleSelection.push(row);
      }
      otherForm.tableRef.toggleRowSelection(row);
    };
    // 加急、取消加急
    const handleUrgent = type => {
      const params = otherForm.multipleSelection.map(item => {
        return item.sampleId;
      });
      if (type) {
        // 取消加急
        unUrgentApi(params).then(res => {
          if (res) {
            proxy.$message.success(res.data.data);
            otherForm.multipleSelection = [];
            proxy.getList();
          }
        });
      } else {
        // 加急
        urgentApi(params).then(res => {
          if (res) {
            proxy.$message.success(res.data.data);
            otherForm.multipleSelection = [];
            proxy.getList();
          }
        });
      }
    };

    // #endregion 批量认领

    // #region 批量完成

    const batchFinish = () => {
      otherForm.showFinishDialog = true;
    };

    const cancelFinishDialog = () => {
      otherForm.showFinishDialog = false;
    };

    const submitBatchFinish = (event = false) => {
      const params = {
        sampleBatchFinishVoList: []
      };
      otherForm.multipleSelection.forEach(item => {
        params.sampleBatchFinishVoList.push({
          sampleId: item.sampleId,
          secSampleNum: item.secSampleNum,
          status: item.status
        });
      });
      saveBatchFinish(params).then(res => {
        if (res.data.code === 200) {
          otherForm.showFinishDialog = false;
          proxy.getList();
          if (res.data.data && res.data.data.length > 0) {
            if (event) {
              ElMessage.success('部分样品已完成检测,无法完成的样品见提示框!');
            } else {
              ElMessage.success('所选样品已全部判定合格,部分样品已完成检测,无法完成的样品见提示框!');
            }
            otherForm.unFinishedSampleList = res.data.data;
            otherForm.showUnFinishedDialog = true;
          } else {
            if (event) {
              ElMessage.success('所选样品已全部完成检测!');
            } else {
              ElMessage.success('所选样品已全部判定合格并完成检测!');
            }
          }
        } else {
          ElMessage.error(`${res.data.message}`);
        }
      });
    };

    const submitUnfinishTip = () => {
      otherForm.showUnFinishedDialog = false;
    };

    // #endregion

    return {
      filterUserList,
      handleUrgent,
      handleRowClick,
      getNumber,
      formatJson,
      giveMeChange,
      exportExcel,
      export2Excel,
      getPermissionBtn,
      drageHeader,
      handleSampleOrdersDetail,
      formatDate,
      changeXaida,
      changeRuku,
      changeProdType,
      getNameByid,
      changeRadio,
      inputValue,
      handleSelectionChange,
      selectable,
      handleEdit,
      handleDetail,
      handleClaim,
      claimSamples,
      sortChange,
      editFrom,
      ...toRefs(otherForm),
      search,
      onSubmit,
      reset,
      resultBack,
      multipleGet,
      getClaimSInfo,
      closeClaimModel,
      filterStatus,
      filterSampleUnitToName,
      colWidth,
      jumpApplicationDetail,
      handleCancel,
      handleRestore,
      batchJudgement,
      closeJudgementDialog,
      saveJudgement,
      cancelFinishDialog,
      submitBatchFinish,
      batchFinish,
      submitUnfinishTip
    };
  },
  computed: {
    ...mapGetters(['tenantGroup', 'tenantInfo'])
  },
  created() {
    this.getList();
    this.getnamelist();
    // 刷新列表
    this.bus.$on('reloadTestAllocationList', msg => {
      this.getList();
    });
    this.copyUserOptions = JSON.parse(JSON.stringify(this.userOptions));
  },
  methods: {
    getList(data) {
      const _this = this;
      _this.listLoading = true;
      if (data && data !== undefined) {
        _this.listQuery.page = data.page;
        _this.listQuery.limit = data.limit;
      }
      const param = Object.assign(_this.formInline, _this.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      // console.log(param)
      getTaskViewList(param).then(res => {
        // console.log(res.data)
        if (res !== false && res.data.code === 200) {
          const { data } = res.data;
          _this.list = data.list;
          _this.list.forEach(item => {
            if (item.inspectionStrategyId) {
              getStrategy(item.inspectionStrategyId).then(res => {
                if (res) {
                  item.inspectionStrategyName = res.data.data.inspectionName;
                }
              });
            }
          });
          _this.total = data.totalCount;
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
    },
    getnamelist() {
      checkPermissionList(permissionTypeList.sampleOwner).then(res => {
        this.mangeList = res.data.data;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.listNumber {
  font-size: 12px;
}
.isHaveNew {
  &:after {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 5px;
    position: absolute;
    top: 3px;
    right: 4px;
    background: #f56c6c;
  }
}
.page-wrapper {
  .sample-order-form {
    text-align: left;

    .el-form-item {
      margin-bottom: 0px;
    }

    .searchBtn {
      border: 0;
      background: none;
      color: $tes-primary;
      padding: 0;
    }
  }

  .allocation-table {
    .blue-color {
      color: $tes-primary;
      cursor: pointer;
    }
  }
}

.tip-panel {
  border: 1px solid $tes-primary;
  border-radius: 10px;
  .info-icon {
    color: #409eff;
    line-height: 2.571429rem;
    font-size: 0.9rem;
  }
  .warning-icon {
    color: orange;
    line-height: 2.571429rem;
    font-size: 0.9rem;
  }
}
.tip-panel:hover {
  box-shadow: 1px 1px 2px 1px $tes-primary4;
}

.tip-message {
  padding: 5px 10px 10px 10px;
}

.tip-text {
  margin: 0px;
  line-height: 1.25rem;
  color: black;
}

.el-button .iconfont {
  margin-right: 5px;
}

#flex-group {
  display: flex;
  flex-direction: row;
  #flex-space {
    width: 15px;
  }
  .flex-content {
    flex: auto;
  }
}
</style>
