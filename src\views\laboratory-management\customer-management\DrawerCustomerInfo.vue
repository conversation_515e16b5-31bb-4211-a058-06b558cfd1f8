<template>
  <!-- 客户信息 -->
  <el-drawer
    v-model="showDrawer"
    :title="titles[drawerType]"
    direction="rtl"
    :before-close="handleClose"
    :size="750"
    destroy-on-close
    :close-on-click-modal="false"
    custom-class="page-drawer"
    @opened="handleOpened"
  >
    <DrawerLayout v-loading="drawerLoading" :has-button-group="false" :has-page-header="false">
      <el-form ref="formRef" :model="formData" label-width="110px" label-position="top" class="form-height-auto">
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item
              label="客户编号："
              prop="no"
              :rules="{ required: true, message: '请输入客户编号', trigger: 'change' }"
            >
              <el-input ref="inputRef" v-model="formData.no" v-trim maxlength="100" placeholder="请输入客户编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="客户名称："
              prop="name"
              :rules="{ required: true, message: '请输入客户名称', trigger: 'change' }"
            >
              <el-input v-model="formData.name" maxlength="100" placeholder="请输入客户名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="英文名：" prop="enName">
              <el-input v-model="formData.enName" maxlength="100" placeholder="请输入英文名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司传真：" prop="faxNo">
              <el-input v-model="formData.faxNo" maxlength="100" placeholder="请输入公司传真" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司电话：" prop="phone" :rules="{ validator: isPhoneMobile, tigger: 'blur' }">
              <el-input v-model="formData.phone" placeholder="请输入公司电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结算方式：" prop="reckoningWay">
              <el-select v-model="formData.reckoningWay" placeholder="请输入结算方式">
                <el-option v-for="(val, key) in customerStatus" :key="key" :label="val" :value="key" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户信誉：" prop="credit">
              <el-select v-model="formData.credit" placeholder="请输入客户信誉">
                <el-option v-for="(val, key) in reputation" :key="key" :label="val" :value="key" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="客户描述：" prop="remark">
              <el-input
                v-model="formData.remark"
                type="textarea"
                maxlength="300"
                :rows="5"
                placeholder="请输入客户描述"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="drawer-fotter">
        <el-button type="primary" :loading="drawerLoading" @click="onSubmit">确认</el-button>
        <el-button :loading="drawerLoading" @click="handleClose">取消</el-button>
      </div>
    </DrawerLayout>
  </el-drawer>
</template>

<script>
import { ref, watch, reactive, getCurrentInstance, toRefs } from 'vue';
import { saveCustomer, updateCustomer } from '@/api/customerManagement';
import { isPhoneMobile } from '@/utils/validate';
import DrawerLayout from '@/components/DrawerLayout';
import store from '@/store';

export default {
  name: 'DrawerCustomerInfo',
  components: { DrawerLayout },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    drawerType: {
      type: String,
      required: true
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    customerStatus: {
      type: Object,
      default: function () {
        return {};
      }
    },
    reputation: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // 抽屉事件
    const showDrawer = ref(props.drawer);
    const state = reactive({
      titles: {
        add: '新增客户信息',
        edit: '编辑客户信息'
      },
      customerStatus: {},
      inputRef: ref(),
      reputation: {},
      drawerType: '',
      drawerLoading: false,
      userList: store.state.common.nameList,
      tit: {
        1: '年',
        2: '月'
      },
      dialogTreeData: [],
      equipUnit: [],
      formRef: ref(),
      formData: {},
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      }
    });
    const handleOpened = () => {
      if (state.inputRef) {
        state.inputRef.focus();
      }
    };
    // 关闭抽屉
    const handleClose = () => {
      showDrawer.value = false;
      context.emit('close', { isRefresh: false, isShow: false });
    };
    watch(props, newValue => {
      showDrawer.value = newValue.drawer;
      if (showDrawer.value) {
        state.drawerType = props.drawerType;
        state.customerStatus = props.customerStatus;
        state.reputation = props.reputation;
        if (state.drawerType === 'add') {
          state.formData = {};
        } else {
          state.formData = JSON.parse(JSON.stringify(props.detailData));
        }
      }
    });
    const getDetail = newData => {
      state.formData = JSON.parse(JSON.stringify(newData.detailData));
    };
    // 确认新增
    const onSubmit = () => {
      proxy.$refs['formRef'].validate(valid => {
        if (valid) {
          if (props.drawerType === 'add') {
            state.drawerLoading = true;
            saveCustomer(state.formData).then(res => {
              state.drawerLoading = false;
              if (res) {
                context.emit('close', { isRefresh: true, isShow: false });
                proxy.$message.success('新增成功');
              }
            });
          } else {
            state.drawerLoading = true;
            updateCustomer(state.formData).then(res => {
              state.drawerLoading = false;
              if (res) {
                context.emit('close', { isRefresh: true, isShow: false });
                proxy.$message.success('编辑成功');
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    const validateNumber = (rule, value, callback) => {
      const numberReg = /^\d+$/;
      if (value === '' || value === undefined || value === null) {
        callback(new Error('请输入计量周期'));
      } else {
        if (numberReg.test(value)) {
          if (value.length > 1 || value === 0 || value === '0') {
            if (value.substring(0, 1) > 0) {
              callback();
            } else {
              callback(new Error('请输入非负整数'));
            }
          } else {
            callback();
          }
        } else {
          callback(new Error('请输入非负整数'));
        }
      }
    };

    return {
      ...toRefs(state),
      getDetail,
      handleOpened,
      isPhoneMobile,
      validateNumber,
      onSubmit,
      handleClose,
      showDrawer
    };
  }
};
</script>

<style lang="scss" scoped></style>
