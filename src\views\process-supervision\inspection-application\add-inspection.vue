<template>
  <!-- 检验申请详情 -->
  <DetailLayout>
    <template #page-header>
      <div class="header-flex flex-between">
        <div class="page-title">
          编号：{{ inspectionInfo.no ? inspectionInfo.no : '保存基本信息后生成' }}
          <el-tag size="small" :type="inspectionInfo.status === 1 ? 'success' : 'warning'">{{
            filterStatus(inspectionInfo.status)
          }}</el-tag>
        </div>
        <div class="btn-group">
          <el-button
            v-if="!showDetail && inspectionInfo.isInvalidated === 0 && getPermissionBtn('voidAddinspectionBtn')"
            :loading="detailLoading"
            type="danger"
            size="large"
            icon="el-icon-delete"
            @click="cancleIA"
            @keyup.prevent
            @keydown.enter.prevent
            >作废申请单</el-button
          >
          <el-button
            v-if="!showDetail && inspectionInfo.isInvalidated === 0 && getPermissionBtn('sumitInspectionBtn')"
            :loading="detailLoading"
            type="primary"
            size="large"
            icon="el-icon-position"
            @click="submitIA"
            @keyup.prevent
            @keydown.enter.prevent
            >提交申请单</el-button
          >
        </div>
      </div>
    </template>
    <div class="page-main">
      <div class="panel-headline">
        <div class="title">基本信息</div>
        <el-button
          v-if="!showDetail && inspectionInfo.isInvalidated === 0 && getPermissionBtn('editAddinspectionBtn')"
          size="small"
          icon="el-icon-edit"
          @click="editInfo"
          @keyup.prevent
          @keydown.enter.prevent
          >编辑</el-button
        >
      </div>
      <div class="panel-content">
        <el-form ref="inspectionInfoRef" :inline="true" :model="formInline" label-width="110px">
          <el-row :gutter="20">
            <el-col
              v-for="item in pageViewGroup[`${inspectionInfo.type}-basicInfo`]"
              :key="item.fieldKey"
              :span="Number(item.columnWidth)"
            >
              <el-form-item :label="`${item.fieldName}：`" :prop="item.fieldKey">
                <!-- 人员 -->
                <template v-if="item.fieldType == 'person'">
                  <UserTag
                    :name="getNameByid(inspectionInfo[item.fieldKey]) || inspectionInfo[item.fieldKey] || '--'"
                  />
                </template>
                <!-- 日期 -->
                <template v-if="item.fieldType == 'date'">
                  <span>{{ formatDateTime(inspectionInfo[item.fieldKey]) || '--' }}</span>
                </template>
                <!-- 文本 -->
                <template v-if="item.fieldType == 'text'">
                  <span>
                    {{ inspectionInfo[item.fieldKey] || '--' }}
                  </span>
                </template>
                <!-- 自定义 -->
                <template v-if="item.fieldType == 'custom'">
                  <span v-if="item.fieldKey == 'type'">
                    {{ dictionaryAll['JYLX'].all[inspectionInfo.type.toString()] || '--' }}
                  </span>
                  <span v-else-if="item.fieldKey == 'productionProcedureNo'">
                    {{ inspectionInfo.productionProcedureNo + '-' + inspectionInfo.productionProcedure || '--' }}
                  </span>
                </template>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-divider />
      <!-- 样品相关 -->
      <sample-about
        v-if="showEdit || showDetail"
        :type="formInline.type"
        :is-invalidated="inspectionInfo.isInvalidated"
        :inspection-id="formInline.id"
        :show-detail="showDetail"
        :sample-info="inspectionInfo.list"
        @set-info="getSampleInfoList"
      />
    </div>
    <!-- 提交成功弹出框 -->
    <submit-success :show="showSubmitSuccessDialog" :sub-title="subTitle" :back-url="backUrl" />
    <!-- 编辑检验单 -->
    <add-info
      :show="showEditInfoDialog"
      :dictionary="dictionaryAll"
      title="编辑检验单"
      :page-view="pageViewGroup"
      :is-edit="true"
      :info="inspectionInfo"
      @close="closeEditInfo"
    />
  </DetailLayout>
</template>

<script>
import { reactive, toRefs, ref } from 'vue';
import router from '@/router/index.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate, formatDateTime } from '@/utils/formatTime';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { getLoginInfo } from '@/utils/auth';
import {
  addInspection,
  getInspectionInfo,
  updateInspection,
  cancelInspection,
  submitInspection
} from '@/api/inspection-application';
import { getProcessListNew } from '@/api/mas';
// import { drageHeader } from '@/utils/formatTable'
import _ from 'lodash';
import SampleAbout from './sample-about';
import AddInfo from './add-info.vue';
import SubmitSuccess from './submit-success.vue';
import DetailLayout from '@/components/DetailLayout';
import UserTag from '@/components/UserTag';
import { getDictionary } from '@/api/user';
import { getViewByBindingMenu } from '@/api/tableView';
import { formatViewData } from '@/utils/formatJson';

export default {
  name: 'AddInspection',
  components: { DetailLayout, SampleAbout, AddInfo, SubmitSuccess, UserTag },
  setup() {
    // const { proxy } = getCurrentInstance()
    const route = useRoute();
    const store = useStore().state;
    const datas = reactive({
      currentAccountId: getLoginInfo().accountId,
      userOptions: store.common.nameList,
      copyUserOptions: store.common.nameList,
      showDetail: route.query.id !== '0',
      dictionaryAll: {
        JYLX: {
          enable: {},
          all: {}
        }
      },
      showEdit: false,
      formInline: {
        registerUserId: '',
        registerDepartment: '',
        registerTime: '',
        type: 1,
        inputWarehouseNo: '',
        wareHouseNo: '',
        wareHouseName: '',
        wareHousePerson: '',
        inputWarehouseDate: '',
        applyStatus: 1
      },
      inspectionInfo: {
        list: []
      },
      inspectionInfoRef: ref(),
      inspectionInfoRule: {
        registerUserId: [{ required: true, message: '请输入登记人' }],
        registerTime: [{ required: true, message: '请选择登记日期' }],
        type: [{ required: true, message: '请输入检验类型' }]
      },
      processList: [],
      showEditInfoDialog: false,
      showSubmitSuccessDialog: false,
      subTitle: '申请单已提交',
      backUrl: '/inspection-application',
      detailLoading: false,
      pageViewGroup: {
        '1-basicInfo': {},
        '2-basicInfo': {},
        '3-basicInfo': {},
        '4-basicInfo': {},
        '5-basicInfo': {},
        '6-basicInfo': {},
        '7-basicInfo': {},
        '8-basicInfo': {},
        '1-basicInfo-dialog': {},
        '2-basicInfo-dialog': {},
        '3-basicInfo-dialog': {},
        '4-basicInfo-dialog': {},
        '5-basicInfo-dialog': {},
        '6-basicInfo-dialog': {},
        '7-basicInfo-dialog': {},
        '8-basicInfo-dialog': {}
      }
    });
    const getDetailView = async () => {
      datas.detailLoading = true;
      const res = await getViewByBindingMenu('AddInspection');
      datas.detailLoading = false;
      if (res) {
        datas.pageViewGroup = formatViewData(res.data.data[0].sysEmployeeListConfigList);
      }
    };
    getDetailView();
    const getDictionaryList = () => {
      Object.keys(datas.dictionaryAll).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          datas.dictionaryAll[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              datas.dictionaryAll[item].enable[optionItem.code] = optionItem.name;
            }
            datas.dictionaryAll[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    getDictionaryList();
    // 获取检验单信息
    const getInspectionInfos = flag => {
      getInspectionInfo(route.query.id).then(res => {
        if (res !== false) {
          datas.inspectionInfo = res.data.data;
          datas.formInline = datas.inspectionInfo;
          if (route.query.flag === '1' || flag === 1) {
            datas.showDetail = true;
            datas.showEdit = false;
          } else if (route.query.flag === '2') {
            datas.showDetail = false;
            datas.showEdit = true;
          }
        }
      });
    };

    // 判断是否是新增, 要是有id调用详情接口
    if (route.query.id !== '0') {
      getInspectionInfos();
    }

    // 过滤状态
    const filterStatus = status => {
      const classMap = {
        0: '待提交',
        1: '已提交',
        2: '已作废'
      };
      return classMap[status] ? classMap[status] : '暂无';
    };
    // 过滤登记人
    const filterUserList = val => {
      if (val) {
        const list = [];
        datas.copyUserOptions.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        datas.userOptions = list;
      } else {
        datas.userOptions = datas.copyUserOptions;
      }
    };
    // 登记人-change
    const changeUser = id => {
      datas.formInline.registerUserId = id;
    };
    // 选择登记日期
    const changeRegisterTime = time => {};
    // 检验类型-change
    const changeType = type => {
      datas.formInline.type = type;
    };
    // 选择入库日期
    const changeInputWarehouseDate = time => {};
    // 保存
    const saveInspectionInfo = () => {
      if (datas.showEdit) {
        datas.inspectionInfoRef.validate(valid => {
          if (valid) {
            updateInspection(datas.formInline).then(res => {
              if (res !== false) {
                getInspectionInfo(datas.formInline.id).then(res1 => {
                  if (res1 !== false) {
                    datas.inspectionInfo = res1.data.data;
                    datas.showDetail = false;
                    datas.showEdit = true;
                  }
                });
                ElMessage.success('更新成功');
              }
            });
          }
        });
      } else {
        datas.inspectionInfoRef.validate(valid => {
          if (valid) {
            ElMessageBox({
              title: '提交',
              message: '是否确认提交？提交后检验类型不可修改',
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              showCancelButton: true,
              closeOnClickModal: true,
              type: 'warning'
            })
              .then(() => {
                addInspection(datas.formInline).then(res => {
                  if (res !== false) {
                    router.push({
                      query: { ...route.query, id: res.data.data?.id, flag: 2 }
                    });
                    getInspectionInfo(res.data.data?.id).then(res1 => {
                      if (res1 !== false) {
                        datas.inspectionInfo = res1.data.data;
                        datas.showDetail = false;
                        datas.showEdit = true;
                      }
                    });
                    ElMessage.success('保存成功');
                  }
                });
              })
              .catch(() => {});
          }
        });
      }
    };
    // 获取样品相关列表
    const getSampleInfoList = list => {
      getInspectionInfos();
    };
    // 点击提交
    const submitIA = () => {
      if (datas.inspectionInfo.list.length > 0) {
        ElMessageBox({
          title: '提示',
          message: '确认提交吗？提交后不可编辑',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: true,
          type: 'warning'
        })
          .then(() => {
            datas.detailLoading = true;
            submitInspection(datas.inspectionInfo.id).then(res => {
              datas.detailLoading = false;
              if (res !== false) {
                // ElMessage.success('编号：' + datas.inspectionInfo.no + '，提交成功')
                datas.subTitle = '编号：' + datas.inspectionInfo.no + '，已提交';
                datas.showSubmitSuccessDialog = true;
                router.push({
                  query: { ...route.query, flag: 1 }
                });
                getInspectionInfos(1);
              }
            });
          })
          .catch(() => {
            datas.detailLoading = false;
          });
      } else {
        ElMessage.warning('提交前请先添加样品');
      }
    };
    // 作废
    const cancleIA = () => {
      ElMessageBox({
        title: '提示',
        message: '确定作废当前检验申请吗？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          datas.detailLoading = true;
          cancelInspection(datas.inspectionInfo.id).then(res => {
            datas.detailLoading = true;
            if (res !== false) {
              ElMessage.success('编号:' + datas.inspectionInfo.no + ',已作废');
              router.push({
                query: { ...route.query, flag: 1 }
              });
              getInspectionInfos(1);
            }
          });
        })
        .catch(() => {
          datas.detailLoading = true;
        });
    };
    // 编辑
    const editInfo = () => {
      datas.showEditInfoDialog = true;
    };
    // 编辑弹出框-关闭
    const closeEditInfo = v => {
      datas.showEditInfoDialog = false;
      getInspectionInfos();
    };
    // 过滤生产工序
    const filterProcessList = value => {
      var nameStr = '';
      if (datas.processList.length > 0) {
        datas.processList.forEach(list => {
          if (value === list.name || value === list.no) {
            nameStr = list.no + ' - ' + list.name;
          }
        });
      }
      return nameStr;
    };

    return {
      ...toRefs(datas),
      formatDate,
      formatDateTime,
      getNameByid,
      filterStatus,
      filterUserList,
      changeRegisterTime,
      getInspectionInfos,
      editInfo,
      changeType,
      changeInputWarehouseDate,
      getDictionaryList,
      saveInspectionInfo,
      changeUser,
      getSampleInfoList,
      cancleIA,
      submitIA,
      closeEditInfo,
      filterProcessList,
      getPermissionBtn
    };
  },
  async created() {
    await this.getProcessLists();
  },
  methods: {
    // 获取生产工序列表接口
    getProcessLists() {
      var that = this;
      return new Promise((resolve, reject) => {
        var param = {
          limit: '-1',
          page: '1',
          content: ''
        };
        getProcessListNew(param)
          .then(res => {
            if (res !== false) {
              that.processList = res.data.data.list;
              resolve(res.data.data.list);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    }
  }
};
</script>
<style lang="scss" scoped></style>
