<template>
  <!-- 合格率统计 -->
  <ListLayout :has-custom-header="true" :has-custom-main="true" :main-offset-top="topHeight">
    <template #page-custom-header>
      <CollapseHeader ref="collapse" @set-main-offset="setMainOffset">
        <template #title>
          <div class="page-title">选择查询条件</div>
        </template>

        <template #collapse>
          <div class="collapse-group">
            <div class="left-form-group">
              <el-form
                ref="editFrom"
                label-width="110px"
                label-position="top"
                :inline="true"
                :model="searchForm"
                @submit.prevent
              >
                <el-row :gutter="16">
                  <el-col :span="8">
                    <el-form-item
                      label="报告日期："
                      class="date-range"
                      prop="submitTime"
                      :rules="{
                        required: true,
                        message: '请选择时间范围',
                        trigger: 'change'
                      }"
                    >
                      <el-date-picker
                        v-model="searchForm.submitTime"
                        type="daterange"
                        size="small"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :shortcuts="shortcuts"
                        @change="handleDatePicker"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item
                      label="检验类型："
                      prop="inspectionType"
                      :rules="{
                        required: true,
                        message: '请选择检验类型',
                        trigger: 'change'
                      }"
                    >
                      <el-select
                        v-model="searchForm.inspectionType"
                        class="chart-query-select"
                        placeholder="选择检验类型"
                        size="small"
                        clearable
                        @change="changeInspectionType"
                      >
                        <el-option
                          v-for="item in inspectionTypeOptions"
                          :key="item.id"
                          :label="item.name"
                          :value="item.code"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col v-show="searchForm.inspectionType.toString() === '1'" :span="4">
                    <el-form-item label="物料分组：" prop="materialGroupName">
                      <el-tag v-if="searchForm.materialGroupName" closable @close="deleteMaterialgroup">
                        {{ searchForm.materialGroupName }}
                      </el-tag>
                      <el-button
                        v-else
                        size="small"
                        icon="el-icon-plus"
                        @click="handleMaterialgroup"
                        @keyup.prevent
                        @keydown.enter.prevent
                        >选择物料分组</el-button
                      >
                    </el-form-item>
                    <el-form-item v-show="searchForm.inspectionType.toString() === '2'" label="工序：">
                      <el-tag v-if="searchForm.workingProcedureName" size="small" closable @close="deleteProcess">
                        <span class="query-tag">{{ searchForm.workingProcedureName }}</span>
                      </el-tag>
                      <el-button
                        v-else
                        size="small"
                        icon="el-icon-plus"
                        @click="handleProcess"
                        @keyup.prevent
                        @keydown.enter.prevent
                        >选择工序</el-button
                      >
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item label="特殊送样:" prop="isSpecial">
                      <el-select
                        v-model="searchForm.isSpecial"
                        class="chart-query-select"
                        placeholder="请选择特殊送样"
                        size="small"
                        clearable
                      >
                        <el-option label="是" value="1" />
                        <el-option label="否" value="0" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item label="供应商名称:" prop="supplierName">
                      <el-input
                        v-model="searchForm.supplierName"
                        clearable
                        size="small"
                        placeholder="请输入供应商名称"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            <div class="right-button-group">
              <el-button
                key="sample"
                type="text"
                size="small"
                @click="renderSampleData()"
                @keyup.prevent
                @keydown.enter.prevent
              >
                <span class="el-icon-data-analysis" />
              </el-button>
              <el-button key="cancel" size="small" @click="cancelRender()" @keyup.prevent @keydown.enter.prevent
                >取消</el-button
              >
              <el-button
                :loading="renderLoading"
                type="primary"
                size="small"
                @click="renderQualifiedRateData()"
                @keyup.prevent
                @keydown.enter.prevent
                >生成图表</el-button
              >
            </div>
          </div>
        </template>
      </CollapseHeader>
    </template>

    <template #page-custom-main>
      <el-row>
        <el-col :span="24">
          <CustomPanel :has-margin-right="true" :has-margin-left="true">
            <template #panel-title>
              <div class="panel-header-left">
                <span class="title">{{ qualifiedTitle }}</span>
              </div>
            </template>
            <template #panel-content>
              <line-bar-pie-chart
                v-if="showChart && showQualified"
                :option="qualifiedRateChartOption"
                :width="'80vw'"
                :height="'40vh'"
              />
              <el-empty v-else :image="emptyImg" description="暂无图表" />
            </template>
          </CustomPanel>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <CustomPanel
            :has-margin-top="true"
            :has-margin-right="true"
            :has-margin-bottom="true"
            :has-margin-left="true"
          >
            <template #panel-title>
              <div class="panel-header-left">
                <span class="title">{{ unqualifiedFreqTitle }}</span>
              </div>
            </template>
            <template #panel-content>
              <line-bar-pie-chart
                v-if="showChart && showUnqualifiedFreq"
                :option="unqualifiedFreqChartOption"
                :width="'100%'"
                :height="'40vh'"
              />
              <el-empty v-else :image="emptyImg" description="暂无图表" />
            </template>
          </CustomPanel>
        </el-col>
        <el-col :span="12">
          <CustomPanel :has-margin-top="true" :has-margin-right="true" :has-margin-bottom="true">
            <template #panel-title>
              <div class="panel-header-left" style="width: 100%">
                <el-row style="width: 100%">
                  <el-col :span="9" style="text-align: left">
                    <span class="title">{{ unqualifiedItemTitle }}</span>
                  </el-col>
                  <el-col :span="15" style="text-align: right">
                    <div v-if="showChart && showUnqualifiedItem && !showTable">
                      <el-radio-group v-model="itemChartRadio" size="small">
                        <el-radio-button label="环形饼图" />
                        <el-radio-button label="帕累托图" />
                      </el-radio-group>
                    </div>
                    <div v-if="showTable">
                      <el-button type="primary" size="small" @click="showTable = false">返回</el-button>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </template>
            <template #panel-content>
              <div v-if="showChart && showUnqualifiedItem && !showTable">
                <line-bar-pie-chart
                  v-if="itemChartRadio === '帕累托图'"
                  :option="paretoChartOption"
                  :width="'100%'"
                  :height="'40vh'"
                />
                <line-bar-pie-chart
                  v-if="itemChartRadio === '环形饼图'"
                  :option="pieChartOption"
                  :width="'100%'"
                  :height="'40vh'"
                  @clickEcharts="handleClickEcharts"
                />
              </div>
              <div v-else-if="showTable">
                <DownTable
                  :capability-id="detailInfoId"
                  :search-info="{
                    procedureCode: searchForm.workingProcedureCode,
                    reportDateStart: searchForm.startTime,
                    reportDateEnd: searchForm.endTime,
                    materialGroupNo: searchForm.materialGroupNo,
                    materialDesc: searchForm.materialDesc,
                    type: searchForm.inspectionType,
                    isSpecial: searchForm.isSpecial
                  }"
                />
              </div>
              <el-empty v-else :image="emptyImg" description="暂无图表" />
            </template>
          </CustomPanel>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <div style="height: 20px; width: 100%" />
        </el-col>
      </el-row>
    </template>
    <template #other>
      <process
        :dialog-visiable="dialogProcess"
        :is-add="isAdd"
        :detail-data="searchForm"
        @selectRow="getProcess"
        @closeDialog="closeProcess"
      />
      <MaterialGroup
        :dialog-visiable="dialogMaterialgroup"
        :detail-data="searchForm"
        :is-add="true"
        @selectRow="getMaterialgroup"
        @closeDialog="closeMaterialgroup"
      />
      <BottomPanel>
        <template #panel-content>
          <div style="text-align: right">
            <el-button
              :loading="downloadLoading"
              type="primary"
              size="small"
              @click="exportToExcel()"
              @keyup.prevent
              @keydown.enter.prevent
              >数据导出</el-button
            >
          </div>
        </template>
      </BottomPanel>
    </template>
  </ListLayout>
</template>

<script>
import { computed, reactive, ref, toRefs, onMounted, getCurrentInstance } from 'vue';
import LineBarPieChart from '@/components/LineBarPieChart';
import ListLayout from '@/components/ListLayout';
import { formatDate, formatDateTime } from '@/utils/formatTime';
import { InspectionType } from '@/data/industryTerm';
import { getDictionary } from '@/api/user';
import { getAxisInterval, getAxisLabelInterval } from '../func/calculate';
import { batchSet, productSet, frequencySet, machineFrequencySet, unqualifiedItemSet } from '../data/testData';
import { past3Months } from '@/data/dateShortcuts';
import { useStore } from 'vuex';
import Process from '@/components/BusinessComponents/Process';
import DownTable from './DownTable';
import MaterialGroup from '@/components/BusinessComponents/MaterialGroup';
import CollapseHeader from '@/views/smart-charts/components/CollapseHeader';
import CustomPanel from '@/components/PageComponents/CustomPanel';
import BottomPanel from '@/components/PageComponents/BottomPanel';
import { ElMessage } from 'element-plus';
import { getQualifiedRate, exportQualifiedRate } from '@/api/order';
import { formatterTips } from '../func/formatter';
import { getNameByid } from '@/utils/common';
import emptyImg from '@/assets/img/empty-chart.png';

export default {
  name: 'QualifiedRateStatistics',
  components: {
    ListLayout,
    LineBarPieChart,
    Process,
    MaterialGroup,
    DownTable,
    CollapseHeader,
    CustomPanel,
    BottomPanel
  },
  setup(props, context) {
    const store = useStore();
    const { proxy } = getCurrentInstance();
    const processData = reactive({
      dialogProcess: false,
      isAdd: false
    });
    const data = reactive({
      detailInfoId: '', // 不合格项目下转数据
      showCondition: false,
      searchForm: {
        // 检验类型
        inspectionType: '',
        inspectionTypeName: '',
        // 物料分组
        materialGroupId: '',
        materialGroupName: '',
        materialGroupNo: '',
        materialDesc: '',
        // 工序
        workingProcedureId: '',
        workingProcedureName: '',
        workingProcedureCode: '',
        // 检测日期
        submitTime: [formatDate(new Date().getTime() - 3600 * 1000 * 24 * 90), formatDate(new Date())],
        startTime: formatDate(new Date().getTime() - 3600 * 1000 * 24 * 90),
        endTime: formatDate(new Date()),
        // 是否是特殊送样
        isSpecial: '0'
      },
      shortcuts: past3Months,
      inspectionTypeOptions: InspectionType,
      materialGroupOptions: MaterialGroup,
      voltageLevelOptions: {},
      dialogMaterialgroup: false,
      topHeight: 85,
      showChart: false,
      showTable: false,
      qualifiedTitle: '检验批次合格率统计',
      unqualifiedFreqTitle: '供应商不合格次数统计',
      unqualifiedItemTitle: '不合格项目统计',
      qualifiedTotalName: '入库数量',
      itemChartRadio: '环形饼图',
      showQualified: true,
      showUnqualifiedItem: true,
      showUnqualifiedFreq: true,
      renderLoading: false,
      downloadLoading: false
    });

    // #region ECharts Option定义

    const qualifiedRateChartOption = ref({
      title: {
        text: '示例折线图'
      },
      xAxis: {
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    });

    const unqualifiedFreqChartOption = ref({
      title: {
        text: '示例折线图'
      },
      xAxis: {
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    });

    const pieChartOption = ref({
      title: {
        text: '示例环形柱状图'
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        top: '5%',
        left: 'center'
      },
      series: [
        {
          name: 'Access From',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '40',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: 1048, name: 'Search Engine' },
            { value: 735, name: 'Direct' },
            { value: 580, name: 'Email' },
            { value: 484, name: 'Union Ads' },
            { value: 300, name: 'Video Ads' }
          ]
        }
      ]
    });

    const paretoChartOption = ref({
      title: {
        text: '示例折线图'
      },
      xAxis: {
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    });

    // #endregion

    // #region 切换图表

    const switchCharts = () => {
      if (data.searchForm.inspectionType.toString() === '2' || data.searchForm.inspectionType.toString() === '3') {
        data.qualifiedTitle = '生产合格率';
        data.unqualifiedFreqTitle = '机台不合格次数统计';
        data.qualifiedTotalName = '生产数量';
        data.searchForm.materialGroupId = '';
        data.searchForm.materialGroupName = '';
        data.searchForm.materialGroupNo = '';
      } else {
        data.qualifiedTitle = '检验批次合格率统计';
        data.unqualifiedFreqTitle = '供应商不合格次数统计';
        data.unqualifiedItemTitle = '不合格项目统计';
        data.qualifiedTotalName = '入库数量';
        data.searchForm.workingProcedureId = '';
        data.searchForm.workingProcedureName = '';
        data.searchForm.workingProcedureCode = '';
      }
    };

    const renderSampleData = () => {
      data.showChart = true;
      data.showCondition = false;
      proxy.$refs.collapse.handleCollapse();
      switchCharts();
      if (data.searchForm.inspectionType.toString() === '2' || data.searchForm.inspectionType.toString() === '3') {
        renderChart(productSet, machineFrequencySet);
      } else {
        renderChart();
      }
    };

    // #endregion

    // #region 计算和渲染图表

    function renderChart(
      qualifiedData = batchSet,
      frequencyData = frequencySet,
      unqualifiedItemData = unqualifiedItemSet
    ) {
      data.showChart = true;
      data.showQualified = qualifiedData.length > 0;
      data.showUnqualifiedFreq = frequencyData.length > 0;
      data.showUnqualifiedItem = unqualifiedItemData.length > 0;
      if (data.showQualified) {
        renderQualifiedRateChart(qualifiedData);
      }
      if (data.showUnqualifiedFreq) {
        renderUnqualifiedFreqChart(frequencyData);
      }
      if (data.showUnqualifiedItem) {
        renderUnqualifiedItemChart(unqualifiedItemData);
      }
    }

    function renderQualifiedRateChart(qualifiedData) {
      // 合格率统计数据
      const qualifiedArray = [];
      const totalArray = [];
      const qualifiedNameArray = [];
      const qualifiedPercentageArray = [];
      qualifiedData.forEach(item => {
        qualifiedArray.push(item.qualifiedNum);
        totalArray.push(item.totalNum);
        qualifiedNameArray.push(item.name);
        qualifiedPercentageArray.push(item.qualifiedRate);
      });

      // 合格率统计图
      const qualifiedSortedArray = JSON.parse(JSON.stringify(totalArray)).sort((a, b) => a - b);
      const qualifiedAxisInfo = getAxisInterval(0, qualifiedSortedArray[qualifiedSortedArray.length - 1], 10);
      const qualifiedPercentageAxisInfo = getAxisInterval(0, 100, 10);

      // 图强调样式
      var emphasisStyle = {
        itemStyle: {
          // shadowBlur: 10,
          // shadowColor: 'rgba(0,0,0,0.3)'
        }
      };

      qualifiedRateChartOption.value = {
        color: ['#8FAEFD', '#B3E09C', '#E6A23C'],
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis',
          extraCssText: 'text-align:left', // 设置tooltip的自定义样式
          textStyle: {
            color: '#303133'
          },
          formatter: function (params) {
            return formatterTips(params);
          }
        },
        legend: {
          data: ['合格数量', data.qualifiedTotalName, '合格率'],
          left: 'center',
          itemGap: 40
        },
        grid: {
          left: '40',
          right: '40',
          bottom: '10',
          containLabel: true
        },
        toolbox: {},
        xAxis: {
          type: 'category',
          splitLine: { show: false },
          splitArea: { show: false },
          data: qualifiedNameArray,
          axisLabel: {
            interval: getAxisLabelInterval(qualifiedNameArray.length, 0.25, 50),
            rotate: 0,
            margin: 8,
            fontSize: 11
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '数量',
            interval: qualifiedAxisInfo.axistInterval,
            min: qualifiedAxisInfo.axisMin,
            max: qualifiedAxisInfo.axisMax
          },
          {
            type: 'value',
            name: '百分比',
            interval: qualifiedPercentageAxisInfo.axistInterval,
            min: qualifiedPercentageAxisInfo.axisMin,
            max: qualifiedPercentageAxisInfo.axisMax,
            axisLabel: {
              formatter: '{value}%'
            }
          }
        ],
        series: [
          {
            name: '合格数量',
            type: 'bar',
            stack: 'one',
            emphasis: emphasisStyle,
            barWidth: '50%',
            barMaxWidth: 50,
            data: qualifiedArray,
            itemStyle: {
              borderWidth: 2,
              borderColor: '#4870FC'
            }
          },
          {
            name: data.qualifiedTotalName,
            type: 'bar',
            stack: 'two',
            emphasis: emphasisStyle,
            barWidth: '50%',
            barMaxWidth: 50,
            data: totalArray,
            itemStyle: {
              borderWidth: 2,
              borderColor: '#67C23A'
            }
          },
          {
            name: '合格率',
            type: 'line',
            yAxisIndex: 1,
            symbol: 'circle',
            symbolSize: 10,
            emphasis: emphasisStyle,
            data: qualifiedPercentageArray,
            itemStyle: {
              color: '#F8E3C5',
              borderWidth: 2,
              borderColor: '#E6A23C'
            },
            lineStyle: {
              color: '#E6A23C',
              width: 2,
              shadowColor: 'rgba(230, 162, 60, 0.5)',
              shadowBlur: 6,
              shadowOffsetY: 4
            }
          }
        ]
      };
    }

    function renderUnqualifiedFreqChart(frequencyData) {
      // 不合格次数统计数据
      const freqCount = frequencyData.length;
      const frequencyNameArray = [];
      const unqualifiedFreqArray = [];
      const qualifiedFreqArray = [];
      const freqAllArray = [];
      const freqPercentageArray = [];
      frequencyData.forEach(item => {
        frequencyNameArray.push(item.name);
        unqualifiedFreqArray.push(item.unQualifiedNum);
        qualifiedFreqArray.push(item.qualifiedNum);
        freqAllArray.push(item.totalNum);
        freqPercentageArray.push(item.unQualifiedRate);
      });

      // 供应商不合格次数统计/机台不合格次数统计
      const freqAllSortedArray = JSON.parse(JSON.stringify(freqAllArray)).sort((a, b) => a - b);
      const freqAxisInfo = getAxisInterval(0, freqAllSortedArray[freqAllSortedArray.length - 1], freqCount);

      const freqPercentageSortedArray = JSON.parse(JSON.stringify(freqPercentageArray)).sort((a, b) => a - b);
      const freqPercentageAxisInfo = getAxisInterval(
        0,
        freqPercentageSortedArray[freqPercentageSortedArray.length - 1],
        freqCount
      );

      // 图强调样式
      var emphasisStyle = {
        itemStyle: {
          // shadowBlur: 10,
          // shadowColor: 'rgba(0,0,0,0.3)'
        }
      };

      unqualifiedFreqChartOption.value = {
        color: ['#80D9C5', '#F2D09D'],
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis',
          extraCssText: 'text-align:left', // 设置tooltip的自定义样式
          textStyle: {
            color: '#303133'
          },
          formatter: function (params) {
            return formatterTips(params);
          }
        },
        legend: {
          data: ['合格数量', '不合格数量', '不合格率'],
          itemGap: 40
        },
        grid: {
          left: '40',
          right: '40',
          bottom: '10',
          containLabel: true
        },
        toolbox: {},
        xAxis: {
          type: 'category',
          splitLine: { show: false },
          splitArea: { show: false },
          data: frequencyNameArray,
          axisLabel: {
            interval: getAxisLabelInterval(frequencyNameArray.length, 0.25, 80),
            rotate: 0,
            margin: 10,
            lineHeight: 10,
            fontSize: 10,
            formatter: function (value, index) {
              return value.toString().substring(0, 8).split('').join('\n');
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '数量',
            interval: freqAxisInfo.axistInterval,
            min: freqAxisInfo.axisMin,
            max: freqAxisInfo.axisMax
          },
          {
            type: 'value',
            name: '百分比',
            interval: freqPercentageAxisInfo.axistInterval,
            min: freqPercentageAxisInfo.axisMin,
            max: freqPercentageAxisInfo.axisMax,
            axisLabel: {
              formatter: '{value}%'
            }
          }
        ],
        series: [
          {
            name: '合格数量',
            type: 'bar',
            stack: 'one',
            emphasis: emphasisStyle,
            barWidth: '50%',
            barMaxWidth: 50,
            data: qualifiedFreqArray,
            itemStyle: {
              borderWidth: 2,
              borderColor: '#00B38A'
            }
          },
          {
            name: '不合格数量',
            type: 'bar',
            stack: 'one',
            emphasis: emphasisStyle,
            barWidth: '50%',
            barMaxWidth: 50,
            data: unqualifiedFreqArray,
            itemStyle: {
              borderWidth: 2,
              borderColor: '#E6A23C'
            }
          },
          {
            name: '不合格率',
            type: 'line',
            yAxisIndex: 1,
            symbol: 'circle',
            symbolSize: 10,
            emphasis: emphasisStyle,
            data: freqPercentageArray,
            itemStyle: {
              color: '#F8E3C5',
              borderWidth: 2,
              borderColor: '#E6A23C'
            },
            lineStyle: {
              color: '#E6A23C',
              width: 2,
              shadowColor: 'rgba(230, 162, 60, 0.5)',
              shadowBlur: 6,
              shadowOffsetY: 4
            }
          }
        ]
      };
    }

    function renderUnqualifiedItemChart(unqualifiedItemData) {
      // 不合格项目统计数据
      const unqualifiedItemCountArray = [];
      const unqualifiedItemPercentageArray = [];
      const itemNameArray = [];
      const pieChartDataArray = [];
      // const totalNum = unqualifiedItemData[0].totalNum
      unqualifiedItemData.forEach(item => {
        unqualifiedItemCountArray.push(item.unQualifiedNum);
        unqualifiedItemPercentageArray.push(item.unQualifiedRate);
        itemNameArray.push(item.name);
        pieChartDataArray.push({
          value: item.unQualifiedNum,
          name: item.name,
          capabilityParaId: item.capabilityParaId
        });
      });
      // 不合格项目统计
      const unqualifiedItemAxisInfo = getAxisInterval(0, unqualifiedItemCountArray[0], 10);
      const unqualifiedItemPercentageAxisInfo = getAxisInterval(0, 100, 10);

      // 图强调样式
      var emphasisStyle = {
        itemStyle: {
          // shadowBlur: 10,
          // shadowColor: 'rgba(0,0,0,0.3)'
        }
      };

      pieChartOption.value = {
        color: [
          '#99D46B',
          '#6085E5',
          '#FFC14A',
          '#FA5660',
          '#F1F380',
          '#64C0F4',
          '#FF974C',
          '#33BB61',
          '#A683EF',
          '#C18D4F'
        ],
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'item',
          extraCssText: 'text-align:left', // 设置tooltip的自定义样式
          textStyle: {
            color: '#303133'
          }
        },
        toolbox: {},
        series: [
          {
            name: '不合格项目统计',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            label: {
              formatter: function (params) {
                const rawNameList = params.name.split('--');
                if (rawNameList.length === 2) {
                  return `${rawNameList[1]}: ${params.percent}%`;
                }
                return `${params.name}: ${params.percent}%`;
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '15',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: pieChartDataArray
          }
        ]
      };

      paretoChartOption.value = {
        color: ['#9FCEFF', '#67C23A'],
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis',
          extraCssText: 'text-align:left', // 设置tooltip的自定义样式
          textStyle: {
            color: '#303133'
          },
          formatter: function (params) {
            return formatterTips(params);
          }
        },
        legend: {
          data: ['此项目不合格次数', '累计不合格占比'],
          left: 'center',
          itemGap: 40
        },
        grid: {
          left: '40',
          right: '40',
          bottom: '30',
          containLabel: true
        },
        toolbox: {},
        xAxis: {
          type: 'category',
          splitLine: { show: false },
          splitArea: { show: false },
          data: itemNameArray,
          axisLabel: {
            interval: getAxisLabelInterval(itemNameArray.length, itemNameArray.length > 50 ? 0.5 : 0.25),
            rotate: 20,
            margin: 10,
            formatter: function (value, index) {
              const rawValueList = value.toString().split('--');
              if (rawValueList.length === 2) {
                return rawValueList[1];
              }
              return value;
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '次数',
            interval: unqualifiedItemAxisInfo.axistInterval,
            min: 0,
            max: unqualifiedItemAxisInfo.axisMax
          },
          {
            type: 'value',
            name: '百分比',
            interval: unqualifiedItemPercentageAxisInfo.axistInterval,
            min: 0,
            max: unqualifiedItemPercentageAxisInfo.axisMax,
            axisLabel: {
              formatter: '{value}%'
            }
          }
        ],
        series: [
          {
            name: '此项目不合格次数',
            type: 'bar',
            stack: 'one',
            emphasis: emphasisStyle,
            data: unqualifiedItemCountArray,
            barWidth: '50%',
            barMaxWidth: 50,
            itemStyle: {
              borderWidth: 2,
              borderColor: '#409EFF'
            }
          },
          {
            name: '累计不合格占比',
            type: 'line',
            yAxisIndex: 1,
            symbol: 'circle',
            symbolSize: 10,
            emphasis: emphasisStyle,
            data: unqualifiedItemPercentageArray,
            itemStyle: {
              color: '#D1EDC4',
              borderWidth: 2,
              borderColor: '#67C23A'
            },
            lineStyle: {
              color: '#67C23A',
              width: 2,
              shadowColor: 'rgba(0, 179, 138, 0.3)',
              shadowBlur: 6,
              shadowOffsetY: 8
            }
          }
        ]
      };
    }

    // #endregion

    // #region 查询条件表单
    const setMainOffset = height => {
      data.topHeight = height == 75 ? height : 200;
    };

    const getVoltageLevel = () => {
      getDictionary(2).then(res => {
        data.voltageLevelOptions = res.data.data?.dictionaryoption;
      });
    };

    const materialClassifications = computed({
      get: () => store.state.user.materialList
    });

    const closeProcess = value => {
      processData.dialogProcess = false;
    };
    const closeMaterialgroup = value => {
      data.dialogMaterialgroup = false;
    };

    const deleteProcess = () => {
      data.searchForm.workingProcedureId = '';
      data.searchForm.workingProcedureName = '';
      data.searchForm.workingProcedureCode = '';
    };

    const deleteMaterialgroup = () => {
      data.searchForm.materialGroupId = '';
      data.searchForm.materialGroupNo = '';
      data.searchForm.materialGroupName = '';
      data.searchForm.materialDesc = '';
    };

    const handleProcess = () => {
      processData.dialogProcess = true;
    };

    const handleMaterialgroup = () => {
      data.dialogMaterialgroup = true;
    };

    const handleDatePicker = value => {
      if (value) {
        data.expired = false;
        data.searchForm.startTime = formatDate(value[0]);
        data.searchForm.endTime = formatDate(value[1]);
      } else {
        data.searchForm.startTime = '';
        data.searchForm.endTime = '';
      }
    };

    const getProcess = value => {
      processData.dialogProcess = false;
      if (data.searchForm.workingProcedureId && value.workingProcedureId !== data.formData.workingProcedureId) {
        data.searchForm.workingProcedureId = '';
        data.searchForm.workingProcedureName = '';
        data.searchForm.workingProcedureCode = '';
      }
      data.searchForm = { ...data.searchForm, ...value };
    };

    const getMaterialgroup = value => {
      data.dialogMaterialgroup = false;
      if (data.searchForm.materialGroupId && value.materialGroupId !== data.formData.materialGroupId) {
        data.searchForm.materialGroupId = '';
        data.searchForm.materialGroupName = '';
        data.searchForm.materialGroupNo = '';
      }
      data.searchForm = { ...data.searchForm, ...value };
    };

    const getInspectionTypeList = () => {
      getDictionary('JYLX').then(res => {
        const resultData = res.data.data;
        data.inspectionTypeOptions = [];
        resultData.dictionaryoption.forEach(item => {
          if (item.status === 1) {
            data.inspectionTypeOptions.push(item);
          }
        });
        data.searchForm.inspectionType = data.inspectionTypeOptions[0]?.code;
        data.searchForm.inspectionTypeName = data.inspectionTypeOptions[0]?.name;
        renderQualifiedRateData();
      });
    };

    const changeInspectionType = value => {
      data.inspectionTypeOptions.forEach(item => {
        if (item.id === value) {
          data.searchForm.inspectionTypeName = item.name;
        }
      });
    };

    const getQualifiedPostBody = () => {
      if (!data.searchForm.startTime || !data.searchForm.endTime) {
        ElMessage.warning({
          message: '请先选择检测日期',
          type: 'warning'
        });
        return false;
      }
      if (!data.searchForm.inspectionType) {
        ElMessage.warning({
          message: '请先选择检验类型',
          type: 'warning'
        });
        return false;
      }
      // if (!data.searchForm.materialGroupNo) {
      //   ElMessage.warning({
      //     message: '请先选择物料分组',
      //     type: 'warning'
      //   })
      //   return
      // }

      const qualifiedRatePostBody = {
        reportDateStart: data.searchForm.startTime,
        reportDateEnd: data.searchForm.endTime,
        type: `${data.searchForm.inspectionType}`,
        supplierName: data.searchForm.supplierName,
        materialGroupCode: data.searchForm.inspectionType.toString() === '1' ? data.searchForm.materialGroupNo : '',
        materialDesc: data.searchForm.inspectionType.toString() === '1' ? data.searchForm.materialDesc : '',
        procedureCode: data.searchForm.inspectionType.toString() === '2' ? data.searchForm.workingProcedureCode : '',
        isSpecial: data.searchForm.isSpecial
      };

      return qualifiedRatePostBody;
    };

    const renderQualifiedRateData = () => {
      switchCharts();
      data.showTable = false;
      data.showCondition = true;
      const qualifiedRatePostBody = getQualifiedPostBody();
      if (qualifiedRatePostBody) {
        data.renderLoading = true;
        getQualifiedRate(qualifiedRatePostBody)
          .then(res => {
            if (res.data.code === 200) {
              const result = res.data.data;
              if (
                result.qualifiedRate.length === 0 &&
                result.unqualifiedFrequency.length === 0 &&
                result.capabilityParaMap.length === 0
              ) {
                ElMessage.warning({
                  message: '查询不到相关数据',
                  type: 'warning'
                });
                data.showChart = false;
                return;
              }
              renderChart(result.qualifiedRate, result.unqualifiedFrequency, result.capabilityParaMap);
            }
          })
          .catch(err => {
            ElMessage.error({
              message: `${err.message}`,
              type: 'error'
            });
          })
          .finally(() => {
            data.renderLoading = false;
          });
      }
    };

    const cancelRender = () => {
      data.showChart = false;
      data.showCondition = false;
      proxy.$refs.collapse.handleCollapse();
    };

    const formatInspectionType = code => {
      const inspectionIndex = data.inspectionTypeOptions.findIndex(item => item.code === code.toString());
      return inspectionIndex === -1 ? '' : InspectionType[inspectionIndex].name;
    };

    const exportToExcel = () => {
      const keyParamPostBody = getQualifiedPostBody();
      if (!keyParamPostBody) {
        return;
      }
      data.downloadLoading = true;
      exportQualifiedRate(keyParamPostBody)
        .then(res => {
          if (res.data.code === 200) {
            const result = res.data.data;
            if (result.length > 0) {
              const workSheets = [];
              import('@/utils/Export2Excel').then(excel => {
                if (result.length > 0) {
                  for (let i = 0; i < result.length; i++) {
                    result[i].ownerName = `${getNameByid(result[i].ownerId)}`;
                    result[i].realOwnerNames = `${getTesterNames(result[i].realOwnerIds)}`;
                    result[i].inspectionType = formatInspectionType(result[i].type);
                    result[i].voltageLevelName = `${result[i].voltName ? getVoltageLevelName(result[i].voltName) : ''}`;
                  }
                  const sampleHeader = [
                    '检验类型',
                    '物料分组编号',
                    '物料分组',
                    '试验日期',
                    '样品编号',
                    '物料编号',
                    '样品名称(物料名称)',
                    '型号规格',
                    '电压等级',
                    '批次',
                    '盘号',
                    '入库/生产数量',
                    '实验负责人',
                    '检验对象(入库单号/订单编号)',
                    '对象位置(入库仓库/工序/机台)',
                    '对象名称(供应商名称/客户名称)',
                    '不良品单号',
                    '原因分析',
                    '处置结果',
                    '合格数量',
                    '不合格数量',
                    '报告编号',
                    '签发日期',
                    '报告结论',
                    '报告中项目',
                    '关键参数',
                    '检验结论',
                    '试验员'
                  ];
                  const sampleFilterVal = [
                    'inspectionType',
                    'materialGroupNo',
                    'materialGroup',
                    'expDate',
                    'sampleNumber',
                    'materialNo',
                    'mateName',
                    'prodType',
                    'voltageLevelName',
                    'batchNo',
                    'reelNo',
                    'productionQuantity',
                    'ownerName',
                    'inspectionObj',
                    'objPosition',
                    'objName',
                    'defectiveProductNo',
                    'reason',
                    'disposalType',
                    'qualifiedNum',
                    'unQualifiedNum',
                    'reportNo',
                    'issueDate',
                    'reportResult',
                    'projectName',
                    'paramName',
                    'expResult',
                    'realOwnerNames'
                  ];
                  const sampleExcelData = formatExcelData(sampleFilterVal, result);
                  const sampleSheet = excel.getWorkSheet({
                    header: sampleHeader,
                    data: sampleExcelData,
                    wsName: `样品信息`
                  });
                  workSheets.push(sampleSheet);
                }
                excel.exportMultiSheetExcel(workSheets, `LIMS-合格率统计 ${formatDateTime()}`);
              });
            } else {
              ElMessage.warning({
                message: '暂无可导出的的数据!',
                type: 'warning'
              });
              return;
            }
          }
        })
        .catch(err => {
          ElMessage.error({
            message: `${err.message}`,
            type: 'error'
          });
        })
        .finally(() => {
          data.downloadLoading = false;
        });
    };

    function formatExcelData(filterVal, jsonData) {
      return jsonData.map(v =>
        filterVal.map(j => {
          return v[j];
        })
      );
    }

    function getVoltageLevelName(code) {
      const voltageIndex = data.voltageLevelOptions.findIndex(item => item.code === code);
      return voltageIndex === -1 ? '' : data.voltageLevelOptions[voltageIndex].name;
    }

    function getTesterNames(testerIdArray) {
      let testerNames = '';
      for (let i = 0; i < testerIdArray.length; i++) {
        testerNames += `${getNameByid(testerIdArray[i])},`;
      }
      return testerNames.length > 1 ? testerNames.substring(0, testerNames.length - 1) : testerNames;
    }

    // #endregion

    onMounted(() => {
      getVoltageLevel();
      getInspectionTypeList();
    });
    const handleClickEcharts = nodeData => {
      data.showTable = true;
      console.log(nodeData.data);
      data.detailInfoId = nodeData.data.capabilityParaId;
    };
    return {
      ...toRefs(data),
      ...toRefs(processData),
      emptyImg,
      handleClickEcharts,
      pieChartOption,
      paretoChartOption,
      unqualifiedFreqChartOption,
      qualifiedRateChartOption,
      materialClassifications,
      renderChart,
      handleDatePicker,
      getProcess,
      closeProcess,
      deleteProcess,
      handleProcess,
      deleteMaterialgroup,
      handleMaterialgroup,
      getMaterialgroup,
      closeMaterialgroup,
      getVoltageLevel,
      setMainOffset,
      renderQualifiedRateData,
      changeInspectionType,
      renderSampleData,
      cancelRender,
      switchCharts,
      exportToExcel
    };
  }
};
</script>
<style lang="scss" scoped>
:deep(.right-button-group) {
  margin-bottom: 18px !important;
}
</style>
