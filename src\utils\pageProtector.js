/**
 * 页面保护器
 * 专门用于防止开发工具拖拽导致的页面卡死问题
 */

class PageProtector {
  constructor() {
    this.isProtecting = false;
    this.freezeDetectionTimer = null;
    this.lastActivityTime = Date.now();
    this.activityThreshold = 2000; // 2秒无活动认为可能卡死
    this.protectionLevel = 0; // 保护级别 0-3
    this.maxProtectionLevel = 3;

    this.setupProtection();
  }

  /**
   * 设置保护机制
   */
  setupProtection() {
    // 监听用户活动
    this.setupActivityMonitoring();

    // 监听开发工具状态变化
    this.setupDevToolsDetection();

    // 设置定期检查
    this.setupPeriodicCheck();
  }

  /**
   * 设置用户活动监控
   */
  setupActivityMonitoring() {
    const events = ['mousedown', 'mousemove', 'keydown', 'scroll', 'touchstart'];

    const updateActivity = () => {
      this.lastActivityTime = Date.now();
      if (this.protectionLevel > 0) {
        this.reduceProtectionLevel();
      }
    };

    events.forEach(event => {
      document.addEventListener(event, updateActivity, { passive: true });
    });
  }

  /**
   * 检测开发工具状态
   */
  setupDevToolsDetection() {
    const devtools = {
      open: false,
      orientation: null
    };

    const threshold = 160;

    setInterval(() => {
      const widthThreshold = window.outerWidth - window.innerWidth > threshold;
      const heightThreshold = window.outerHeight - window.innerHeight > threshold;
      const orientation = widthThreshold ? 'vertical' : 'horizontal';

      if (
        !(heightThreshold && widthThreshold) &&
        ((window.Firebug && window.Firebug.chrome && window.Firebug.chrome.isInitialized) ||
          widthThreshold ||
          heightThreshold)
      ) {
        if (!devtools.open || devtools.orientation !== orientation) {
          devtools.open = true;
          devtools.orientation = orientation;
          this.onDevToolsOpen(orientation);
        }
      } else {
        if (devtools.open) {
          devtools.open = false;
          devtools.orientation = null;
          this.onDevToolsClose();
        }
      }
    }, 500);
  }

  /**
   * 开发工具打开时的处理
   */
  onDevToolsOpen(orientation) {
    console.log('DevTools opened:', orientation);
    this.increaseProtectionLevel();

    // 如果是垂直方向（通常是拖拽导致），增加更多保护
    if (orientation === 'vertical') {
      this.increaseProtectionLevel();
    }
  }

  /**
   * 开发工具关闭时的处理
   */
  onDevToolsClose() {
    console.log('DevTools closed');
    this.reduceProtectionLevel();
  }

  /**
   * 设置定期检查
   */
  setupPeriodicCheck() {
    setInterval(() => {
      this.checkPageHealth();
    }, 1000);
  }

  /**
   * 检查页面健康状态
   */
  checkPageHealth() {
    const now = Date.now();
    const timeSinceLastActivity = now - this.lastActivityTime;

    // 如果长时间无活动且保护级别较高，可能是卡死了
    if (timeSinceLastActivity > this.activityThreshold && this.protectionLevel >= 2) {
      this.handlePotentialFreeze();
    }
  }

  /**
   * 处理潜在的页面冻结
   */
  handlePotentialFreeze() {
    console.warn('Potential page freeze detected, attempting recovery...');

    // 强制垃圾回收
    if (window.gc) {
      try {
        window.gc();
      } catch (e) {
        // 忽略错误
      }
    }

    // 清理可能的定时器
    this.clearSuspiciousTimers();

    // 暂停所有动画
    this.pauseAnimations();

    // 显示恢复提示
    this.showRecoveryNotification();

    // 重置活动时间
    this.lastActivityTime = Date.now();
  }

  /**
   * 增加保护级别
   */
  increaseProtectionLevel() {
    if (this.protectionLevel < this.maxProtectionLevel) {
      this.protectionLevel++;
      this.applyProtectionMeasures();
      console.log(`Protection level increased to ${this.protectionLevel}`);
    }
  }

  /**
   * 减少保护级别
   */
  reduceProtectionLevel() {
    if (this.protectionLevel > 0) {
      this.protectionLevel--;
      this.applyProtectionMeasures();
      console.log(`Protection level reduced to ${this.protectionLevel}`);
    }
  }

  /**
   * 应用保护措施
   */
  applyProtectionMeasures() {
    switch (this.protectionLevel) {
      case 0:
        // 正常状态，无特殊保护
        this.enableAllFeatures();
        break;
      case 1:
        // 轻度保护：减少动画
        this.reduceAnimations();
        break;
      case 2:
        // 中度保护：禁用非关键功能
        this.disableNonCriticalFeatures();
        break;
      case 3:
        // 高度保护：最小化所有操作
        this.enableMinimalMode();
        break;
    }
  }

  /**
   * 启用所有功能
   */
  enableAllFeatures() {
    document.body.style.removeProperty('pointer-events');
    document.body.classList.remove('page-protection-active');
  }

  /**
   * 减少动画
   */
  reduceAnimations() {
    const style = document.createElement('style');
    style.id = 'page-protection-animations';
    style.textContent = `
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-delay: -1ms !important;
        transition-duration: 0.01ms !important;
        transition-delay: -1ms !important;
      }
    `;

    // 移除旧的样式
    const oldStyle = document.getElementById('page-protection-animations');
    if (oldStyle) {
      oldStyle.remove();
    }

    document.head.appendChild(style);
  }

  /**
   * 禁用非关键功能
   */
  disableNonCriticalFeatures() {
    this.reduceAnimations();

    // 暂停所有视频
    document.querySelectorAll('video').forEach(video => {
      video.pause();
    });

    // 添加保护类
    document.body.classList.add('page-protection-active');
  }

  /**
   * 启用最小化模式
   */
  enableMinimalMode() {
    this.disableNonCriticalFeatures();

    // 临时禁用指针事件（除了关键元素）
    const style = document.createElement('style');
    style.id = 'page-protection-minimal';
    style.textContent = `
      .page-protection-active * {
        pointer-events: none !important;
      }
      .page-protection-active button,
      .page-protection-active a,
      .page-protection-active input,
      .page-protection-active select,
      .page-protection-active textarea {
        pointer-events: auto !important;
      }
    `;

    const oldStyle = document.getElementById('page-protection-minimal');
    if (oldStyle) {
      oldStyle.remove();
    }

    document.head.appendChild(style);
  }

  /**
   * 清理可疑的定时器
   */
  clearSuspiciousTimers() {
    // 这是一个激进的方法，清理所有定时器
    const highestTimeoutId = setTimeout(() => {}, 0);
    for (let i = 0; i < highestTimeoutId; i++) {
      clearTimeout(i);
    }

    const highestIntervalId = setInterval(() => {}, 0);
    clearInterval(highestIntervalId);
    for (let i = 0; i < highestIntervalId; i++) {
      clearInterval(i);
    }
  }

  /**
   * 暂停动画
   */
  pauseAnimations() {
    document.querySelectorAll('*').forEach(el => {
      const computedStyle = window.getComputedStyle(el);
      if (computedStyle.animationName !== 'none') {
        el.style.animationPlayState = 'paused';
      }
    });
  }

  /**
   * 显示恢复通知
   */
  showRecoveryNotification() {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #ff6b6b;
      color: white;
      padding: 15px 20px;
      border-radius: 5px;
      z-index: 999999;
      font-size: 14px;
      font-family: Arial, sans-serif;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      cursor: pointer;
    `;
    notification.innerHTML = `
      <div>⚠️ 检测到页面性能问题</div>
      <div style="font-size: 12px; margin-top: 5px;">正在自动恢复... 点击关闭</div>
    `;

    notification.onclick = () => {
      notification.remove();
    };

    document.body.appendChild(notification);

    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 5000);
  }

  /**
   * 手动触发恢复
   */
  manualRecovery() {
    this.handlePotentialFreeze();
    this.protectionLevel = 0;
    this.applyProtectionMeasures();
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      protectionLevel: this.protectionLevel,
      lastActivityTime: this.lastActivityTime,
      timeSinceLastActivity: Date.now() - this.lastActivityTime
    };
  }
}

// 创建全局实例
const pageProtector = new PageProtector();

// 在开发环境自动启用
if (process.env.NODE_ENV === 'development') {
  console.log('Page protector activated for development environment');

  // 添加全局快捷键用于手动恢复
  document.addEventListener('keydown', e => {
    // Ctrl+Shift+R 手动恢复
    if (e.ctrlKey && e.shiftKey && e.key === 'R') {
      e.preventDefault();
      pageProtector.manualRecovery();
      console.log('Manual recovery triggered');
    }
  });
}

export default pageProtector;
