<template>
  <div class="search-container">
    <div class="textbox-wrapper">
      <el-input
        ref="queryRef"
        v-model="singleText"
        v-trim
        v-focus
        :placeholder="placeholder"
        size="small"
        prefix-icon="el-icon-search"
        clearable
        @keyup.enter="enterQuery"
        @clear="reset()"
      />
    </div>
    <el-button v-if="showBtn" class="query-btn" type="primary" size="small" @click="queryList()">查询</el-button>
    <el-button v-if="showBtn" class="query-btn" size="small" @click="reset()">重置</el-button>
  </div>
</template>

<script>
import { reactive, toRefs, ref } from 'vue';

export default {
  name: 'SimpleQuery',
  components: {},
  props: {
    fieldTip: {
      type: String,
      default: function () {
        return '内容';
      }
    },
    showBtn: {
      type: Boolean,
      default: true
    }
  },
  emits: ['resetSearch', 'getQueryInfo'],
  setup(props, context) {
    const queryRef = ref(null);
    const datas = reactive({
      singleText: '',
      placeholder: `请输入${props.fieldTip}进行搜索`
    });

    // 查询
    const queryList = () => {
      context.emit('getQueryInfo', datas.singleText);
    };

    const enterQuery = val => {
      datas.singleText = datas.singleText.toString().trim();
      queryList();
    };

    const reset = () => {
      datas.singleText = '';
      datas.placeholder = `请输入${props.fieldTip}进行搜索`;
      context.emit('resetSearch', true);
    };

    return {
      ...toRefs(datas),
      queryRef,
      queryList,
      enterQuery,
      reset
    };
  }
};
</script>
<style lang="scss" scoped>
.search-container {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex: 1 1 5rem;
  .textbox-wrapper {
    flex: 1;
    width: 100%;
  }
  .switch-wrapper {
    width: 10rem;
    margin-right: 0.5rem;
  }
  .default-search-wrapper {
    flex: 5;
  }
  .combination-wrapper {
    flex: 5;
    width: 100%;
    display: flex;
    flex-direction: row;
    .field-input,
    .value-input {
      width: 10rem;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .add-wrapper {
      width: 5rem;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .combination-search-wrapper {
      flex: 5;
    }
  }
  .query-btn {
    width: 5rem;
    margin-left: 0.5rem;
  }
}

.combination-search-wrapper.select-items {
  border: 0.071429rem solid #fff;
  background-color: #fff;
  box-shadow: 0 0 0.857143rem rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  // padding: 0.714286rem 0.714286rem 0;
  max-height: 8.571429rem;
  overflow-y: auto;
  display: flex;
  flex-direction: row;
  align-items: center;
  .el-tag {
    margin: 0;
  }
  .tag-wrapper {
    width: 100%;
    overflow-y: auto;
  }
}
</style>
