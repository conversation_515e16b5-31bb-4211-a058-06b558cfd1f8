import request from '@/utils/request';

// 人员培训列表
export function employeetrainList(data) {
  return request({
    url: '/api-user/user/employeetrain/list',
    method: 'post',
    data
  });
}

// 保存人员培训信息
export function saveOrUpdate(data) {
  return request({
    url: '/api-user/user/employeetrain/saveOrUpdate',
    method: 'post',
    data
  });
}

// 根据Id查询人员培训信息
export function employeetrainInfo(id) {
  return request({
    url: `/api-user/user/employeetrain/info/${id}`,
    method: 'get'
  });
}

// 人员培训删除
export function deleteEmployeetrain(id) {
  return request({
    url: `/api-user/user/employeetrain/delete/${id}`,
    method: 'delete'
  });
}
