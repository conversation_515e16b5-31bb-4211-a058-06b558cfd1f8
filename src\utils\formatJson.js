import _ from 'lodash';
import store from '@/store';
import router from '@/router';
import { ElMessage } from 'element-plus';

/**
 * 后台查询的菜单数据拼装成路由格式的数据
 * @param routes
 */
// 保留几位小数 n 四舍五入的小数，d 需要保留的位数
function toFixed(n, d) {
  var s = n + '';
  if (!d) d = 0;
  if (s.indexOf('.') === -1) s += '.';
  s += new Array(d + 1).join('0');
  if (new RegExp('^(-|\\+)?(\\d+(\\.\\d{0,' + (d + 1) + '})?)\\d*$').test(s)) {
    // eslint-disable-next-line
    var s = '0' + RegExp.$2;
    var pm = RegExp.$1;
    var a = RegExp.$3.length;
    var b = true;
    if (a === d + 2) {
      a = s.match(/\d/g);
      if (parseInt(a[a.length - 1]) > 4) {
        for (var i = a.length - 2; i >= 0; i--) {
          a[i] = parseInt(a[i]) + 1;
          if (a[i] === 10) {
            a[i] = 0;
            b = i !== 1;
          } else break;
        }
      }
      s = a.join('').replace(new RegExp('(\\d+)(\\d{' + d + '})\\d$'), '$1.$2');
    }
    if (b) s = s.substring(1);
    return (pm + s).replace(/\.$/, '');
  }
  return this + '';
}
// 过滤左侧菜单树
export function formatMenuList(routerList, list) {
  if (list.length === 0) {
    return routerList;
  }
  console.log('routerList');
  console.log(list);
  // routerList.forEach(function(d) {
  //   if (d.id) {
  //     const flag = _.find(list, { id: d.id })
  //     if (flag) {
  //       d.hidden = false
  //       d.meta.title = flag.name
  //     } else {
  //       d.hidden = true
  //     }
  //     // // 添加消息模块 由于权限问题目前先手动添加菜单
  //     // if (d.id === '10302') {
  //     //   d.hidden = false
  //     // }
  //     // // 添加设备仪器 由于权限问题目前先手动添加菜单
  //     // if (d.id === '10302') {
  //     //   d.hidden = false
  //     // }
  //     // 个人管理 开发阶段，临时跳过权限，后面权限配置好后可以删除
  //     // 平台管理 开发阶段，临时跳过权限，后面权限配置好后可以删除
  //     // 企业管理 开发阶段，临时跳过权限，后面权限配置好后可以删除
  //     // if (d.id === 'userManage' || d.id === 'platformManage' || d.id === 'businessManage') {
  //     //   d.hidden = false
  //     // }
  //     // 过滤子菜单
  //     // if (d.children && d.children.length > 0) {
  //     //   d.children.forEach(function(child) {
  //     //     const flag1 = _.find(list, { id: child.id })
  //     //     if (flag1) {
  //     //       // console.log(child)
  //     //       child.hidden = false
  //     //       child.meta.title = flag1.name
  //     //     } else {
  //     //       child.hidden = true
  //     //     }
  //     //     // 展示分析子菜单模块 临时跳过权限 后面权限加上了，可以去掉下面代码 + 检验申请模块
  //     //     // if (child.id === 'FileList') {
  //     //     //   child.hidden = false
  //     //     // }
  //     //     // 个人管理子菜单，开发阶段，临时跳过权限，后面权限配置好后可以删除
  //     //     // 平台管理子菜单，开发阶段，临时跳过权限，后面权限配置好后可以删除
  //     //     // 企业管理子菜单，开发阶段，临时跳过权限，后面权限配置好后可以删除
  //     //     // if (child.id === 'userManage' || child.id === 'platformManage' || child.id === 'businessManage') {
  //     //     //   child.hidden = false
  //     //     // }
  //     //   })
  //     // }
  //   }
  // })
  return routerList;
}

// 过滤页面权限
export function formatPagePermission(name) {
  if (router.hasRoute(name)) {
    return true;
  } else {
    ElMessage.error('您没有权限访问该页面！');
    return false;
  }
}

export function formatPaginationList(list, current, size) {
  const newList = [];
  if (list.length === 0) {
    return [];
  }

  list.forEach(function (value, key) {
    if (key >= (current - 1) * size && key <= current * size) {
      newList.push(value);
    }
  });
  return newList;
}

export function formatTree(tree) {
  if (tree === null) {
    return null;
  }

  tree.forEach(function (value) {
    if (value.children === undefined || value.children === null) {
      return false;
    } else if (value.children.length === 0) {
      // value.children = null
      delete value['children'];
    } else {
      formatTree(value.children);
    }
  });
  return tree;
}

export function formatAllTree(id, tree) {
  tree.forEach(function (value) {
    if (id === value.id || id === value.parentid || id === value.parentId) {
      value.disabled = true;
    } else {
      value.disabled = false;
    }
    if (value.children === undefined || value.children === null || value.children.length === 0) {
      return false;
    } else {
      formatAllTree(id, value.children);
    }
  });
  return tree;
}
export function findTreeNode(id, tree) {
  // 使用ES6的for...of循环遍历数组
  for (const node of tree) {
    // 检查当前节点是否匹配
    if (node['id'] === id) {
      return node;
    }

    // 如果有子节点，递归查找
    if (node['children'] && node['children'].length > 0) {
      const foundNode = findTreeNode(id, node['children']);
      if (foundNode) {
        return foundNode;
      }
    }
  }

  // 未找到返回null
  return null;
}
// 标准库新增标准的树
export function formatBzTree(tree) {
  tree.forEach(function (value) {
    if (value.standardType !== 1) {
      value.disabled = true;
    } else if (value.children) {
      if (value.children.length > 0 && value.children[0].standardType !== 1) {
        value.disabled = true;
      } else {
        value.disabled = false;
      }
    } else {
      value.disabled = false;
    }
    if (value.children === undefined || value.children === null || value.children.length === 0) {
      return false;
    } else {
      formatBzTree(value.children);
    }
  });
  return tree;
}
// 标准库新增型号的树
export function formatXhTree(tree) {
  tree.forEach(function (value) {
    if (value.standardType !== 1) {
      value.disabled = true;
    } else if (value.children) {
      if (value.children.length > 0 && value.children[0].standardType === 1) {
        value.disabled = true;
      } else {
        value.disabled = false;
      }
    } else {
      value.disabled = false;
    }
    if (value.children === undefined || value.children === null || value.children.length === 0) {
      return false;
    } else {
      formatXhTree(value.children);
    }
  });
  return tree;
}
// 标准库新增规格的树
export function formatGgTree(tree) {
  tree.forEach(function (value) {
    if (value.standardType !== 2) {
      value.disabled = true;
    } else {
      value.disabled = false;
    }
    if (value.children === undefined || value.children === null || value.children.length === 0) {
      return false;
    } else {
      formatGgTree(value.children);
    }
  });
  return tree;
}
export function formatTreeByIds(node, ids) {
  if (!ids) {
    ids = [];
  }
  if (node === null) {
    return [];
  } else if (node.data.parentid === '0' || node.data.parentId === '0') {
    ids.push(node.data.id);
    let ss = [];
    ss = ids.reverse();
    // console.log(ss)
    return ss;
  } else {
    ids.push(node.data.id);
    formatTreeByIds(node.parent, ids);
  }
  return ids;
}

export function formatTreeByNames(node, ids) {
  if (!ids) {
    ids = [];
  }
  if (node === null) {
    return [];
  } else if (node.data.parentId === '0' || node.data.id === 'all') {
    ids.push(node.data.name);
    let ss = [];
    ss = ids.reverse();
    // console.log(ss)
    return ss;
  } else {
    ids.push(node.data.name);
    formatTreeByNames(node.parent, ids);
  }
  return ids;
}

export function formatTestcapabilityByKey(keys) {
  const newList = [];
  const testcapabilityOptions = {
    A: '339081954591256576',
    B: '339081954591256577',
    C: '339081954591256578'
  };
  keys.forEach(v => {
    newList.push(testcapabilityOptions[v]);
  });

  return newList; // JSON.stringify(newList)
}

export function formatTestcapabilityByValue(value) {
  if (!value) {
    return '';
  }
  const arr = JSON.parse(value);
  const newList = [];
  const testcapabilityOptions = {
    '339081954591256576': 'A',
    '339081954591256577': 'B',
    '339081954591256578': 'C'
  };
  arr.forEach(v => {
    if (v) {
      newList.push(testcapabilityOptions[v]);
    }
  });

  return newList.join(' / ');
}

export function addNumToStr(str) {
  if (!str) {
    str = '0000';
  }
  let newstr = '';
  const num = parseInt(str) + 1;
  if (num > 0 && num < 10) {
    newstr = '000' + num;
  } else if (num < 100) {
    newstr = '00' + num;
  } else if (num < 1000) {
    newstr = '0' + num;
  } else {
    newstr = '' + num;
  }
  return newstr;
}

// 过滤table高度
export function filterHeight(limit, total, flag) {
  console.log(limit, total);
  if (total === 0 || !total) {
    return '6.4rem';
  }
  var remBase = 3.2; // 2.86
  if (flag === 1) {
    remBase = 3.2;
  }
  var height = '';
  if (total <= limit) {
    const rem = remBase * (total + 1);
    height = rem + 'rem';
  } else {
    const rem = remBase * (limit + 1);
    height = rem + 'rem';
  }
  return height;
}
// 过滤物资,根据code查名字
export function filterMaterialCode(code) {
  const name = '';
  const materialList = store.state.user.materialList;
  if (materialList.length > 0) {
    const person = materialList.find(item => {
      return code === item.code;
    });
    if (person) {
      return person.name;
    } else {
      return code;
    }
  }

  return name;
}

function isNumber(value) {
  return !isNaN(parseFloat(value)) && isFinite(value);
}
// 保留几位小数（四舍五入）
export function getFloatByNum(num, n) {
  if (isNumber(num)) {
    if (num === '/') {
      return '/';
    }
    n = n ? parseInt(n) : 0;
    if (n <= 0) {
      return Math.round(num);
    }
    num = Math.round(num * Math.pow(10, n)) / Math.pow(10, n); // 四舍五入
    num = Number(num).toFixed(n); // 补足位数
    return num;
  }
  return num;
}
// 有效小数位（四舍五入）
export function getReservedSign(num, n) {
  if (isNumber(num)) {
    if (num === '/') {
      return '/';
    }
    n = n ? parseInt(n) : 0;
    if (n < 1) {
      return num;
    }
    let resultVal;
    if (Math.abs(num) >= 1) {
      resultVal = num > 0 ? toFixed(num, jDZGreaterOne(num, n)) : toFixed(num, jDZGreaterOne(Math.abs(num), n));
    } else if (Math.abs(num) === 0) {
      resultVal = 0;
    } else {
      // 小数
      resultVal = num > 0 ? toFixed(num, fractionalPart(num, n)) : toFixed(num, fractionalPart(Math.abs(num), n));
    }
    return resultVal;
  }
  return num;
}
// 绝对值大于1的保留小数位 numberValue: 值；roundNumber：保留的位数
function jDZGreaterOne(numberValue, roundNumber) {
  const integralPart = parseInt(numberValue); // 整数部分
  if (integralPart.toString().length > roundNumber) {
    // 如果保留的长度小于整数部分，则没法儿处理返回原数据
    return 0;
  } else if (integralPart.toString().length === roundNumber) {
    // 如果保留的长度等于整数部分，则直接返回
    return 0;
  } else {
    // 保留的位数大于整数部分，则小数部分的保留位数是保留位数-整数的位数
    return Number(roundNumber) - Number(integralPart.toString().length);
  }
}
// 绝对值大于1的保留小数位 numberValue: 值；roundNumber：保留的位数
function fractionalPart(numberValue, roundNumber) {
  const nonzeroIndex = Array.from(numberValue.toString().split('.')[1]).findIndex(char => char !== '0'); // 获取左侧开始第一个非0的下标
  const oldLength = numberValue.toString().split('.')[1].length;
  // 小数部分总长度-非0下标 >= 保留位数
  if (oldLength - nonzeroIndex <= roundNumber) {
    const zeroFill = roundNumber - (oldLength - nonzeroIndex); // 需要补零的个数
    return oldLength + zeroFill;
  } else {
    return oldLength - (oldLength - nonzeroIndex - roundNumber);
  }
}
// 过滤lims那边获取的物料组
export function formatMaterialTree(list) {
  var result = [];
  if (!Array.isArray(list)) {
    return result;
  }
  var map = {};
  list.forEach(item => {
    map[item.no] = item;
  });
  list.forEach(item => {
    const parent = map[item.parentNo];
    if (parent) {
      (parent.children || (parent.children = [])).push(item);
    } else {
      result.push(item);
    }
  });
  return result;
}
// 过滤部门树 lims的
export function formatDepartmentTree(list) {
  var newList = [];
  if (list && list.length > 0) {
    list.forEach(function (value) {
      value.children = [];
      var childList = _.filter(list, function (d) {
        return d.parentId === value.id;
      });
      value.children = childList;
      if (!value.parentId) {
        newList.push(value);
      }
    });
  }
  return newList;
}
// 过滤上传文档里面的文件分类
function getAllFileTree(list, ids) {
  if (!ids) {
    ids = [];
  }
  if (list && list.length > 0) {
    list.forEach(function (value) {
      ids.push(value);
      if (value.children && value.children.length > 0) {
        getAllFileTree(value.children, ids);
      } else {
        return ids;
      }
    });
  }
  return ids;
}
function filterParentTree(list, node, ids) {
  if (!ids) {
    ids = [];
  }
  if (node.parentId === '0') {
    return ids;
  } else {
    const item = _.filter(list, function (ct) {
      return ct.id === node.parentId;
    });
    if (item.length > 0) {
      ids.push(item[0].id);
      filterParentTree(list, item[0], ids);
    }
  }
  return ids;
}
export function formatUploadFileTree(list, id, ids) {
  var currentTreeList = getAllFileTree(list);
  if (!ids) {
    ids = [];
  }
  if (currentTreeList.length > 0) {
    currentTreeList.forEach(tl => {
      if (tl.id === id) {
        ids.push(tl.id);
        if (tl.parentId !== '0') {
          const newIds = filterParentTree(currentTreeList, tl).reverse();
          newIds.push(tl.id);
          ids = newIds;
          return ids;
        }
      }
    });
  }
  return ids;
}
// 根据tenantId， 查询租户信息
export function formatTenantId(tenantId) {
  var tenantInfo = {};
  const tenantList = JSON.parse(localStorage.getItem('tenantGroup'));
  if (tenantList && tenantList.length > 0) {
    tenantList.forEach(list => {
      if (list.tenantId === tenantId) {
        tenantInfo = list;
        tenantInfo.type = list.tenantType;
      }
    });
  }
  return tenantInfo;
}
// 根据样品id筛选样品name
export function filterSampleUnitToName(unitId) {
  var unitName = '';
  var sampleUnit = store.state.user.sampleUnit;
  if (unitId && sampleUnit.length > 0) {
    sampleUnit.forEach(opt => {
      if (opt.code === unitId || opt.id === unitId) {
        unitName = opt.name;
      }
    });
  } else {
    return '';
  }
  return unitName || unitId;
}

export function filterProcessMode(Id) {
  var name = '';
  var processMode = store.state.common.processModeList;
  // console.log('store.state.common.processModeList')
  // console.log(store.state.common.processModeList)
  if (Id && processMode.length > 0) {
    processMode.forEach(opt => {
      if (opt.code === Id) {
        name = opt.name;
      }
    });
  }
  return name || Id;
}
// 过滤权限树
export function formatAuthorityTree(list, treeId, pIds) {
  if (list && list.length > 0) {
    list.forEach(function (value) {
      if (value.id === treeId && value.parentId === 0) {
        pIds.push(value.id);
        return pIds;
      } else if (value.id === treeId) {
        pIds.push(value.parentId);
        pIds.push(value.id);
        return pIds;
      } else if (value.children.length > 0) {
        formatAuthorityTree(value.children, treeId, pIds);
      }
    });
  }
  return pIds;
}
// 根据单位id或code，筛选单位名称
export function filterUnit(id) {
  var unitName = '';
  var unitList = store.state.user.unit;
  if (id && unitList.length > 0) {
    unitList.forEach(opt => {
      if (opt.code === id || opt.id === id || opt.name === id) {
        unitName = opt.name;
      }
    });
  }
  return unitName || id;
}
// 格式化视图
export function formatViewData(viewData) {
  const newViewData = {};
  viewData.forEach(item => {
    if (
      item.isShow == 1 &&
      (item.fieldTenantType == '-1' ||
        item.fieldTenantType == '' ||
        item.fieldTenantType.includes(store.state.user.tenantInfo.type))
    ) {
      const suffix = item.groupCode;
      if (!newViewData[suffix]) {
        newViewData[suffix] = [];
      }
      newViewData[suffix].push({
        ...item
      });
    }
  });
  return newViewData;
}

// 视图必填提示语
export function formatViewRequirePrompt(type, fieldName, title) {
  const ptompt = {
    person: '选择',
    date: '选择',
    text: '输入'
  };
  return ptompt[type] ? `请${ptompt[type]}${fieldName}` : `请${title}${fieldName}`;
}
