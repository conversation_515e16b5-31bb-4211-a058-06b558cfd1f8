<template>
  <!-- 选择仪器设备单选 -->
  <el-dialog
    v-model="dialogVisiable"
    title="选择仪器设备"
    :close-on-click-modal="false"
    width="1050px"
    custom-class="custom-dialog"
    top="80px"
    @close="handleClose"
  >
    <div class="dialog-header">
      <div class="header-left">
        <el-input
          ref="inputRef"
          v-model="param"
          v-trim
          class="search"
          size="small"
          placeholder="请输入设备编号/设备名称/型号规格"
          prefix-icon="el-icon-search"
          clearable
          @clear="getEquipmentList()"
          @keyup.enter="getEquipmentList()"
        />
        <el-button type="primary" size="small" @click="getEquipmentList()">查询</el-button>
      </div>
    </div>
    <el-row class="dialog-content" :gutter="20">
      <el-col :span="6">
        <div class="tree-container">
          <div class="tree-content">
            <el-tree
              ref="treeRef"
              :data="treeData"
              node-key="id"
              :props="defaultProps"
              default-expand-all
              :expand-on-click-node="false"
              :highlight-current="true"
              draggable
              class="leftTree"
              @node-click="clickNode"
            >
              <template #default="{ node }">
                <span>{{ node.label }}</span>
              </template>
            </el-tree>
          </div>
        </div>
      </el-col>
      <el-col :span="18">
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="tableData"
          fit
          border
          size="medium"
          height="auto"
          highlight-current-row
          class="dark-table base-table no-quick-query format-height-table3"
          :row-style="
            () => {
              return 'cursor: pointer';
            }
          "
          @header-dragend="drageHeader"
          @current-change="changeRadio"
        >
          <el-table-column type="index" label="选择" width="70" align="center">
            <template #default="{ row, $index }">
              <el-radio v-model="row.radio" :label="row.id" @change="changeRadio(row, $index)">{{ '' }}</el-radio>
            </template>
          </el-table-column>
          <el-table-column label="设备编号" prop="name" :min-width="colWidth.name">
            <template #default="{ row }">
              <span>{{ row.deviceNumber || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="设备名称" prop="deviceNumber" :min-width="colWidth.name">
            <template #default="{ row }">
              <span>{{ row.name || '--' }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :page="listQuery.page"
          :limit="listQuery.limit"
          :total="total"
          @pagination="getEquipmentList"
        />
      </el-col>
    </el-row>
    <div class="dialog-other">
      <div class="title">
        <label>已选设备</label>
      </div>
      <div v-if="tableSelectInfo.deviceNumber" class="select-items">
        <el-tag closable @close="handleDeleteTag(tableSelectInfo)">{{ tableSelectInfo.name }}</el-tag>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose()">关闭</el-button>
        <el-button type="primary" @click="handleSave()">确认选择</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, nextTick } from 'vue';
import { getNameByid, getPermissionBtn } from '@/utils/common';
// import { formatDate } from '@qms-frontend/common/utils/formatTime'
import { colWidth } from '@/data/tableStyle';
import Pagination from '@/components/Pagination';
import { drageHeader } from '@/utils/formatTable';
import { getTree, getList } from '@/api/equipment';

export default {
  name: 'AddSingleEquipment',
  components: { Pagination },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    selectInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    watch(props, newValue => {
      state.dialogVisiable = props.show;
      if (state.dialogVisiable) {
        state.treeData = [];
        state.tableData = [];
        state.tableSelectId = props.selectId;
        nextTick(() => {
          state.inputRef.focus();
        });
        // 获取左侧树
        getEquipmentTree();
      }
    });
    // const { proxy } = getCurrentInstance()
    const state = reactive({
      loading: false,
      listQuery: {
        page: 1,
        limit: 20
      },
      param: '', // 过滤的设备编号和名称
      defaultProps: {
        children: 'subList',
        label: 'name'
      },
      treeRef: ref(),
      tableRef: ref(),
      inputRef: ref(),
      tableSelectInfo: {},
      treeSelectId: '',
      tableData: [],
      treeData: [],
      total: 0,
      dialogType: '',
      dialogVisiable: false
    });
    // 获取获取左侧树
    const getEquipmentTree = async () => {
      getTree().then(res => {
        if (res) {
          state.treeData = res.data.data;
          state.treeData.unshift({ id: '-1', name: '全部' });
          nextTick(() => {
            state.treeRef.setCurrentKey('-1', true);
          });
          state.treeSelectId = '';
          // 获取设备
          getEquipmentList();
        }
      });
    };
    // 获取设备
    const getEquipmentList = query => {
      const params = {
        deviceCategoryId: state.treeSelectId,
        param: state.param
      };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.limit = query.limit;
        state.listQuery.page = query.page;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.loading = true;
      getList(params).then(res => {
        state.loading = false;
        if (res) {
          state.tableData = res.data.data.list;
          if (state.tableSelectInfo.deviceNumber) {
            const findIndex = state.tableData.findIndex(item => {
              return item.id === state.tableSelectId;
            });
            if (findIndex !== -1) {
              state.tableData[findIndex].radio = state.tableData[findIndex].id;
              nextTick(() => {
                state.tableRef?.setCurrentRow(state.tableData[findIndex], true);
              });
            }
          }
          state.total = res.data.data.totalCount;
        }
      });
    };
    // 关闭弹出框
    const handleClose = () => {
      state.dialogVisiable = false;
      context.emit('closeDialog', false);
    };
    // 点击树节点
    const clickNode = node => {
      if (node.id !== '-1') {
        state.treeSelectId = node.id;
        getEquipmentList();
      } else {
        state.treeSelectId = '';
        getEquipmentList();
      }
    };
    // 确认选择
    const handleSave = () => {
      if (state.tableSelectInfo.deviceNumber) {
        state.dialogVisiable = false;
        context.emit('closeDialog', state.tableSelectInfo);
      } else {
        handleClose();
      }
    };
    // 选择
    const changeRadio = val => {
      if (val?.id) {
        state.tableSelectInfo = val;
        state.tableData.forEach(item => {
          item.radio = item.id === val.id ? item.id : '';
        });
      }
    };

    // 删除已选择的
    const handleDeleteTag = () => {
      state.tableSelectInfo = {};
      state.tableData.forEach(item => {
        item.radio = '';
      });
    };
    return {
      ...toRefs(state),
      colWidth,
      handleDeleteTag,
      changeRadio,
      clickNode,
      handleSave,
      getEquipmentTree,
      getEquipmentList,
      handleClose,
      drageHeader,
      getNameByid,
      getPermissionBtn
    };
  }
};
</script>
<style lang="scss">
@import '@/styles/dialog.scss';
</style>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
.dialogBtn {
  text-align: right;
  margin: 15px 0;
}
.tree-container {
  padding: 20px;
  margin: 0 0 10px 0;
  background: var(--backgroundColor);
  .tree-content {
    max-height: calc(100vh - 510px);
    overflow-y: auto;
    padding-left: 0;
  }
}
::v-deep(.format-height-table3) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 36.5rem) !important;
    overflow-y: auto;
  }
}
</style>
