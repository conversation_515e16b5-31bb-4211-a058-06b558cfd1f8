<template>
  <el-dialog
    v-model="dialogVisiable"
    title="选择检测地点"
    :close-on-click-modal="false"
    width="450px"
    top="50px"
    @close="handleClose"
  >
    <el-select
      v-model="templateAddress"
      allow-create
      filterable
      multiple
      default-first-option
      placeholder="/"
      style="width: 100%"
    >
      <el-option v-for="item in testSiteList" :key="item.id" :label="item.name" :value="item.code" />
    </el-select>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="small" @click="handleClose">取 消</el-button>
        <el-button type="primary" size="small" @click="onSubmit">确 认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, toRefs, watch, getCurrentInstance } from 'vue';
export default {
  name: 'DialogTemplateSit',
  components: {},
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    address: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      dialogVisiable: false,
      templateAddress: '',
      testSiteList: []
    });
    watch(props, newValue => {
      state.dialogVisiable = newValue.dialogShow;
      if (state.dialogVisiable) {
        state.testSiteList = newValue.address;
      }
    });
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog');
    };
    // 提交检测地点
    const onSubmit = () => {
      if (state.templateAddress.length) {
        context.emit('closeDialog', { value: state.templateAddress.toString() });
      } else {
        proxy.$message.warning('请选择检测地点');
      }
    };
    return { ...toRefs(state), handleClose, onSubmit };
  }
};
</script>
<style lang="scss" scoped></style>
