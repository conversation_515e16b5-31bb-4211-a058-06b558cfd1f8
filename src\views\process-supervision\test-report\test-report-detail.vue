<template>
  <!-- 检测报告详情 -->
  <DetailLayout :main-offset-top="102">
    <template #page-header>
      <div class="header-flex flex-between">
        <el-space :size="40" :spacer="spacer">
          <div class="item-column">
            <span class="item-label">样品编号</span>
            <div class="item-content">{{ detailData.reportEntity.secSampleNum }}</div>
          </div>
          <div class="item-column">
            <span class="item-label">报告编号</span>
            <div class="item-content">{{ detailData.reportEntity.reportNo }}</div>
          </div>
          <div class="item-column">
            <span class="item-label">报告状态</span>
            <div class="item-content">
              <div v-if="detailData.reportEntity.examineStatus === 6" class="success">
                <i class="el-icon-success" />
                {{ filterExamineStatus(detailData.reportEntity.examineStatus)[1] }}
              </div>
              <div v-else class="warning">
                <i class="tes-clock iconfont" /> {{ filterExamineStatus(detailData.reportEntity.examineStatus)[1] }}
              </div>
            </div>
          </div>
        </el-space>
        <el-button size="large" icon="el-icon-back" @click="close">返回列表</el-button>
      </div>
    </template>
    <el-collapse v-model="activeNames" class="collapse-wrap">
      <el-collapse-item name="1">
        <template #title>
          <div class="collapse-header-title">报告详情</div>
        </template>
        <div class="collapse-content">
          <!--检测信息-->
          <div class="collapse-item">
            <div class="collapse-content-title"><span class="line-space" />检测信息</div>
            <el-row :gutter="20" class="collapse-item">
              <el-col
                v-for="viewItem in pageViewGroup['detectionInfo']"
                :key="viewItem.fieldKey"
                :span="Number(viewItem.columnWidth)"
              >
                <span class="title">{{ viewItem.fieldName }}：</span>
                <span class="txt">
                  <template v-if="viewItem.fieldType == 'text'">
                    {{ detailData.reportdetailresultinfoResponse[viewItem.fieldKey] || '--' }}
                  </template>
                  <template v-if="viewItem.fieldType == 'person'">
                    <template v-if="viewItem.fieldKey == 'editorId'">
                      <UserTag
                        :name="
                          detailData.reportdetailresultinfoResponse.editorName ||
                          getNameByid(detailData.reportdetailresultinfoResponse[viewItem.fieldKey]) ||
                          detailData.reportdetailresultinfoResponse[viewItem.fieldKey] ||
                          '--'
                        "
                      />
                    </template>
                  </template>
                  <template v-if="viewItem.fieldType == 'date'">
                    {{ detailData.reportdetailresultinfoResponse[viewItem.fieldKey] || '--' }}
                  </template>
                  <template v-if="viewItem.fieldType == 'custom'">
                    <template v-if="viewItem.fieldKey == 'reportResult'">
                      {{ filterTestResult(detailData.reportdetailresultinfoResponse[viewItem.fieldKey]) || '--' }}
                    </template>
                    <template v-if="viewItem.fieldKey == 'reportType'">
                      {{ dictionary['BGLX'].all[detailData.reportdetailresultinfoResponse[viewItem.fieldKey]] }}
                    </template>
                    <template v-if="viewItem.fieldKey == 'attachmentList'">
                      <div v-if="detailData.reportDetailResponse?.attachmentList" class="flex flex-row flex-wrap">
                        <div v-for="(item, index) in detailData.reportDetailResponse?.attachmentList" :key="item.id">
                          <span class="blue-color" @click="handleDown(item)">{{ item.name }}</span>
                          <span v-if="index !== detailData.reportDetailResponse?.attachmentList?.length - 1">，</span>
                        </div>
                      </div>
                      <div v-else>--</div>
                    </template>
                  </template>
                </span>
              </el-col>
            </el-row>
            <el-divider />
          </div>
          <!--样品信息-->
          <div class="collapse-item">
            <div class="collapse-content-title"><span class="line-space" />样品信息</div>
            <el-row :gutter="20" class="collapse-item">
              <el-col
                v-for="viewItem in pageViewGroup[`${detailData.reportDetailResponse.type}-sampleInfo`]"
                :key="viewItem.fieldKey"
                :span="Number(viewItem.columnWidth)"
              >
                <span class="title">{{ viewItem.fieldName }}：</span>
                <span v-if="viewItem.fieldKey !== 'judgmentBasis'" class="txt">
                  <!-- 文本 -->
                  <template v-if="viewItem.fieldType == 'text'">
                    {{ detailData.reportDetailResponse[viewItem.fieldKey] || '--' }}
                  </template>
                  <!-- 人员 -->
                  <template v-if="viewItem.fieldType == 'person'">
                    <UserTag
                      :name="
                        detailData.reportDetailResponse[viewItem.fieldKey]
                          ? getNameByid(detailData.reportDetailResponse[viewItem.fieldKey])
                          : '--'
                      "
                    />
                  </template>
                  <!-- 日期 -->
                  <template v-if="viewItem.fieldType == 'date'">
                    {{ formatDate(detailData.reportDetailResponse[viewItem.fieldKey]) }}
                  </template>
                  <!-- 自定义 -->
                  <template v-if="viewItem.fieldType == 'custom'">
                    <template v-if="viewItem.fieldKey == 'startDate'">
                      {{
                        detailData.reportDetailResponse[viewItem.fieldKey]
                          ? formatDate(detailData.reportDetailResponse[viewItem.fieldKey]) +
                            '-' +
                            formatDate(detailData.reportDetailResponse.finishedDate)
                          : '--'
                      }}
                    </template>
                    <template v-if="viewItem.fieldKey == 'sampleNum'">
                      {{ detailData.reportDetailResponse[viewItem.fieldKey] || '-' }}
                      {{
                        filterSampleUnitToName(detailData.reportDetailResponse.sampleUnit) ||
                        detailData.reportDetailResponse.sampleUnit ||
                        '-'
                      }}
                    </template>
                    <template v-if="viewItem.fieldKey == 'inputWarehouseQuantity'">
                      {{ detailData.reportDetailResponse[viewItem.fieldKey] || '--'
                      }}{{
                        filterSampleUnitToName(detailData.reportDetailResponse.inputWarehouseUnit) ||
                        detailData.reportDetailResponse.inputWarehouseUnit
                      }}
                    </template>
                  </template>
                </span>
                <el-tooltip
                  v-else
                  effect="light"
                  :content="detailData.reportDetailResponse.judgmentBasis || '--'"
                  placement="top-start"
                >
                  <span class="txt">
                    {{ detailData.reportDetailResponse.judgmentBasis || '--' }}
                  </span>
                </el-tooltip>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-collapse-item>
      <!--检测项目结果-->
      <el-collapse-item name="2">
        <template #title>
          <div class="collapse-header-title">检测项目结果</div>
        </template>
        <div class="collapse-content">
          <div class="export-btn">
            <el-button size="small" icon="el-icon-download" @click="exportExcel()" @keyup.prevent @keydown.enter.prevent
              >导出</el-button
            >
          </div>
          <el-table
            :data="detailData.reportStandardExpResponse.experimentRequestList"
            :default-expand-all="true"
            class="dark-table base-table"
          >
            <el-table-column type="expand" width="15px">
              <template #default="{ row }">
                <el-table
                  id="report-expand-table"
                  :data="row.childList"
                  fit
                  border
                  highlight-current-row
                  size="small"
                  class="dark-table expand-table"
                >
                  <el-table-column prop="name" label="关键参数" :width="285" show-overflow-tooltip>
                    <template #default="scope">
                      {{ scope.row.name || '--' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="expRequirement" label="技术要求" :width="280" show-overflow-tooltip>
                    <template #default="scope">
                      {{ scope.row.expRequirement || '--' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="expValueUnit" label="单位" :width="130">
                    <template #default="scope">
                      {{ scope.row.expValueUnit || '--' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="value" label="检测结果" :min-width="colWidth.result" show-overflow-tooltip>
                    <template #default="scope">
                      {{ filterValue(scope.row.value) || '--' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="name1" label="结论" :width="colWidth.status">
                    <template #default="scope">
                      {{
                        scope.row.expResult ||
                        filterResult(
                          scope.row.value,
                          scope.row.maxNum,
                          scope.row.minNum,
                          scope.row.minselected,
                          scope.row.maxseleced
                        ) ||
                        '--'
                      }}
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="检测项目" />
            <el-table-column prop="completeDateTime" label="日期" :width="colWidth.date">
              <template #default="{ row }">
                <div v-if="row.completeDateTime">
                  {{ formatDate(row.completeDateTime) }}
                </div>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column label="试验员" prop="ownerId" align="left" :width="colWidth.person" show-overflow-tooltip>
              <template #default="{ row }">
                <div v-if="row.ownerId">
                  <UserTag :name="getNamesByIds(row.ownerId) || '--'" />
                </div>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column prop="standardProduct" label="判定标准" />
            <el-table-column prop="status" label="试验状态" :width="colWidth.status">
              <template #default="{ row }">
                <el-tag v-if="row.status === 5" size="small" effect="dark" type="success">{{
                  filterStatus(row.status)
                }}</el-tag>
                <el-tag v-else size="small" effect="dark" type="warning">{{ filterStatus(row.status) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              v-if="getPermissionBtn('TestReportTemplateButton')"
              prop="status"
              label="操作"
              :width="colWidth.status"
            >
              <template #default="{ row }">
                <span class="blue-color" @click="handleCheckTemplate(row)">原始记录</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-collapse-item>
      <!--仪器设备-->
      <el-collapse-item name="3">
        <template #title>
          <div class="collapse-header-title">仪器设备</div>
        </template>
        <div class="collapse-content">
          <el-table :data="detailData.deviceysageInfoResponse.deviceysageResponseList" class="dark-table base-table">
            <el-table-column prop="name" label="仪器设备" :min-width="colWidth.name" show-overflow-tooltip />
            <el-table-column prop="model" label="型号规格" :min-width="colWidth.model" show-overflow-tooltip />
            <el-table-column
              prop="deviceNumber"
              label="仪器设备编号"
              :min-width="colWidth.orderNo"
              show-overflow-tooltip
            />
            <el-table-column
              prop="measurementClientName"
              label="检定/校准结构"
              :min-width="colWidth.name"
              show-overflow-tooltip
            />
            <el-table-column prop="validEndDate" label="有效日期" :width="colWidth.date" />
          </el-table>
        </div>
      </el-collapse-item>
      <!-- 试验图片 -->
      <el-collapse-item name="4">
        <template #title>
          <div class="collapse-header-title">试验图片</div>
        </template>
        <div class="collapse-content">
          <el-empty v-if="detailData.imgAllList.length === 0" :image="emptyImg" description="暂无数据" />
          <el-image-viewer v-if="showViewer" :url-list="srcList" :initial-index="currentIndex" @close="closeViewer" />
          <div
            v-for="(img, index) in detailData.imgAllList"
            :key="img"
            class="img-border img-list"
            @click="zoominImg(index)"
          >
            <el-image :src="img.imageUrl" fit="cover">
              <template #placeholder>
                <div class="image-slot">
                  <i class="el-icon-loading" />
                </div>
              </template>
              <template #error>
                <div class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </template>
            </el-image>
            <div class="img-name">{{ img.remoteName }}</div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
    <!-- <el-backtop target=".report-detail" :bottom="100">
      <i class="el-icon-download go-back-icon" />
    </el-backtop> -->
  </DetailLayout>
</template>

<script>
import { h, reactive, toRefs } from 'vue';
import router from '@/router/index.js';
import { getAllReportDetail, downloadById } from '@/api/testReport';
import { useRoute } from 'vue-router';
import { getNamesByIds, getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
// import { ElMessage } from 'element-plus'
import { getFloatByNum, filterSampleUnitToName } from '@/utils/formatJson';
import _ from 'lodash';
import { parseTime } from '@/utils';
import { getDictionary } from '@/api/user';
import { mapGetters } from 'vuex';
import { ElDivider, ElMessage } from 'element-plus';
import DetailLayout from '@/components/DetailLayout';
import UserTag from '@/components/UserTag';
import { colWidth } from '@/data/tableStyle';
import emptyImg from '@/assets/img/empty-data.png';
import { getViewByBindingMenu } from '@/api/tableView';
import { formatViewData } from '@/utils/formatJson';

export default {
  name: 'ReportDetail',
  components: { DetailLayout, UserTag },
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    const spacer = h(ElDivider, { direction: 'vertical' });
    const route = useRoute();
    const datas = reactive({
      params: {
        reportId: route.query.reportId,
        sampleId: route.query.sampleId,
        reportStage: route.query.reportStage
      },
      dictionary: {
        BGLX: {
          all: {},
          enable: {}
        }
      },
      activeNames: ['1', '2', '3', '4'],
      detailData: {
        deviceysageInfoResponse: {},
        experimentImgRecordResponse: {},
        reportDetailResponse: {},
        reportEntity: {},
        reportdetailresultinfoResponse: {},
        reportStandardExpResponse: {},
        imgAllList: [],
        allCoreColour: {}
      },
      summarySelect: [],
      srcList: [],
      showViewer: false,
      currentIndex: 0,
      pageViewGroup: {
        detectionInfo: {},
        '1-sampleInfo': {},
        '2-sampleInfo': {},
        '3-sampleInfo': {}
      }
    });
    const getDetailView = async () => {
      const res = await getViewByBindingMenu('detail-report');
      if (res) {
        datas.pageViewGroup = formatViewData(res.data.data[0].sysEmployeeListConfigList);
      }
    };
    getDetailView();
    // 关闭详情
    const close = () => {
      router.go(-1);
    };
    // 试验状态过滤
    const filterStatus = status => {
      var name = '';
      switch (status) {
        case -2:
          name = '已作废';
          break;
        case 1:
          name = '待分配';
          break;
        case '2':
        case 2:
          name = '待提交';
          break;
        case 3:
          name = '待审核';
          break;
        case 4:
          name = '退回拒绝';
          break;
        case 5:
          name = '已通过';
          break;
        case -5:
          name = '已删除';
          break;
      }
      return name;
    };
    // 过滤审批状态颜色
    const filterExamineStatus = status => {
      // 审批状态  1、待提交2、待审核、3、待签字4、待盖章5、待发送6、已发送
      if (!status) {
        status = 1;
      }
      const classMap = {
        1: ['icon-tes-info', '待提交'],
        2: ['icon-tes-info', '待审核'],
        3: ['icon-tes-info', '待签字'],
        4: ['icon-tes-info', '待盖章'],
        5: ['icon-tes-info', '待发送'],
        6: ['icon-tes-success', '已完成']
      };
      return classMap[status];
    };
    // 检测结果过滤
    const filterTestResult = value => {
      if (value === undefined || value === null || value === '') {
        return '';
      }
      const classMap = {
        0: '合格',
        1: '不合格',
        2: '不判定'
      };
      return classMap[value];
    };
    const handleCheckTemplate = item => {
      const routeUrl = router.resolve({
        path: '/testReport/template',
        query: { type: 'check', experimentId: item.id, samplesId: item.sampleId, capabilityId: item.sourceId }
      });
      window.open(
        routeUrl.href,
        '_blank',
        'height=1080, width=1080, top=100, toolbar=no, resizable=yes, location=no, status=no'
      );
    };
    // 判定是否合格
    const filterResult = (expValue, maxNum, minNum, minselected, maxseleced) => {
      var flag = true;
      if (expValue && expValue.length > 0 && maxNum && minNum) {
        expValue.forEach(ev => {
          if (
            (parseFloat(ev.paraValue) < parseFloat(minNum) || parseFloat(ev.paraValue) > parseFloat(maxNum)) &&
            ev.paraValue !== '/'
          ) {
            flag = false;
          }
          if (
            (minselected !== true && minselected !== 1 && parseFloat(ev.paraValue) === parseFloat(minNum)) ||
            (maxseleced !== true && maxseleced !== 1 && parseFloat(ev.paraValue) === parseFloat(maxNum))
          ) {
            flag = false;
          }
        });
      } else if (expValue && expValue.length > 0 && maxNum) {
        expValue.forEach(ev => {
          if (parseFloat(ev.paraValue) > parseFloat(maxNum) && ev.paraValue !== '/') {
            flag = false;
          }
          if (maxseleced !== true && maxseleced !== 1 && parseFloat(ev.paraValue) === parseFloat(maxNum)) {
            flag = false;
          }
        });
      } else if (expValue && expValue.length > 0 && minNum) {
        expValue.forEach(ev => {
          if (parseFloat(ev.paraValue) < parseFloat(minNum) && ev.paraValue !== '/') {
            flag = false;
          }
          if (minselected !== true && minselected !== 1 && parseFloat(ev.paraValue) === parseFloat(minNum)) {
            flag = false;
          }
        });
      } else {
        return '';
      }
      if (flag) {
        return '合格';
      } else {
        return '不合格';
      }
    };
    // 判定枚举型是否合格
    const filterMJResult = (dList, qualifiedOption, noQualifiedOption, values) => {
      // console.log(dList)
      var isTrue = '合格';
      if (dList && dList.length > 0) {
        dList.forEach(d => {
          if (qualifiedOption.indexOf(d.code) !== -1) {
            if (values.length > 0) {
              values.forEach(v => {
                if (v.paraValue.indexOf(d.name) === -1) {
                  isTrue = '不合格';
                }
              });
            }
          } else if (noQualifiedOption.indexOf(d.code) !== -1) {
            values.forEach(v => {
              if (v.paraValue.indexOf(d.name) !== -1) {
                isTrue = '不合格';
              }
            });
          }
        });
      }
      return isTrue;
    };

    // 图片预览
    const zoominImg = index => {
      datas.currentIndex = index;
      datas.showViewer = true;
    };
    // 关闭预览
    const closeViewer = () => {
      datas.showViewer = false;
    };
    // 过滤检测结果
    const filterValue = values => {
      var str = [];
      if (values && values.length > 0) {
        values.forEach(v => {
          if (v.paraValue) {
            str.push(v.colourValue + ' ' + v.paraValue);
          }
        });
      }
      return str.join(', ');
    };
    // 导出过滤检测结果
    const filterValueUpload = values => {
      var str = [];
      if (values && values.length > 0) {
        values.forEach(v => {
          if (v.paraValue) {
            if (!v.colourValue) {
              str.push(v.paraValue);
            }
          }
        });
      }
      return str.join(', ');
    };
    const getDicList = () => {
      Object.keys(datas.dictionary).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          datas.dictionary[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              datas.dictionary[item].enable[optionItem.code] = optionItem.name;
            }
            datas.dictionary[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    const handleDown = async file => {
      const response = await downloadById(file.id);
      if (response) {
        const blob = new Blob([response.data], { type: '' });
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = `${file.name}`;
        a.href = blobUrl;
        a.click();
        ElMessage.success('下载附件成功');
      }
    };
    getDicList();
    return {
      ...toRefs(datas),
      emptyImg,
      handleDown,
      getDicList,
      getPermissionBtn,
      handleCheckTemplate,
      close,
      getNamesByIds,
      getNameByid,
      formatDate,
      filterStatus,
      filterExamineStatus,
      filterResult,
      filterMJResult,
      filterSampleUnitToName,
      zoominImg,
      closeViewer,
      filterTestResult,
      filterValue,
      filterValueUpload,
      colWidth,
      spacer
    };
  },
  computed: {
    ...mapGetters(['tenantInfo'])
  },
  created() {
    this.getAllReportDetails();
  },
  methods: {
    getAllReportDetails() {
      const that = this;
      // const param1 = { sampleId: '1631877068154', reportStage: 3, reportId: '1440963526613471233' }
      getAllReportDetail(that.params).then(res => {
        if (res !== false) {
          // console.log(res.data.data)
          that.detailData = res.data.data;
          const experimentRequestList = that.detailData.reportStandardExpResponse.experimentRequestList;
          const capabilityParaVos = that.detailData.reportStandardExpResponse.capabilityParaVos;
          that.summarySelect = that.detailData.reportStandardExpResponse.reportStandardProductList;
          const newExperimentRequestList = [];
          if (experimentRequestList && experimentRequestList.length > 0) {
            experimentRequestList.forEach(erl => {
              const standardResultList = erl.standardResultList;
              if (erl.localList && erl.localList.length > 0) {
                erl.localList.forEach(ll => {
                  if (ll.expValues) {
                    ll.value = JSON.parse(ll.expValues);
                    // console.log(ll.value)
                    // ll.expValueSize1 = Object.getOwnPropertyNames(ll.value).length
                    // ll.expValuesList = []
                    // ll.expKeysList = []
                    // for (var key1 in ll.value) {
                    //   ll.expKeysList.push(key1)
                    //   if (ll.smallnumber && ll.smallnumber !== '0') { // 约束小数位
                    //     ll.expValuesList.push(getFloatByNum(ll.value[key1], ll.smallnumber))
                    //   } else {
                    //     ll.expValuesList.push(ll.value[key1])
                    //   }
                    // }
                    ll.value.forEach(i => {
                      if (ll.smallNumber && ll.smallNumber !== '0' && ll.smallNumber !== 0) {
                        // 约束小数位
                        i.paraValue = getFloatByNum(i.paraValue, ll.smallNumber);
                      }
                    });
                    if (ll.expResult === '' || ll.expResult === null || ll.expResult === undefined) {
                      if (ll.isDetermine === true || ll.isDetermine === 'true') {
                        ll.expResult = that.filterResult(ll.value, ll.maxNum, ll.minNum, ll.minSelected, ll.maxSeleced);
                      } else if (ll.isDetermine === false || ll.isDetermine === 'false') {
                        ll.expResult = '不判定';
                      } else {
                        ll.expResult = '';
                      }
                    }
                  }
                });
                erl.childList = erl.localList;
              }
              if (erl.childList && erl.childList.length > 0 && erl.localList.length === 0) {
                erl.childList.forEach(async cl => {
                  cl.expValuesList = [];
                  cl.expKeysList = [];
                  if (standardResultList && standardResultList.length > 0) {
                    const hasitem1 = _.filter(standardResultList, srl => {
                      return cl.sourceId === srl.capabilityParaId;
                    });
                    if (hasitem1.length > 0) {
                      cl = _.merge(cl, hasitem1[0]);
                    }
                  }
                  if (capabilityParaVos && capabilityParaVos.length > 0) {
                    const hasitem = _.filter(capabilityParaVos, cpv => {
                      return cl.sourceId === cpv.capabilityParaId;
                    });
                    if (hasitem.length > 0) {
                      cl = _.merge(cl, hasitem[0]);
                      cl.value.forEach(i => {
                        if (cl.smallNumber && cl.smallNumber !== '0' && cl.smallNumber !== 0) {
                          // 约束小数位
                          i.paraValue = getFloatByNum(i.paraValue, cl.smallNumber);
                        }
                      });
                      if (cl.expResult === '' || cl.expResult === null || cl.expResult === undefined) {
                        if (cl.isDetermine === true || cl.isDetermine === 'true') {
                          // 数值型判定
                          if (cl.resultType === '1') {
                            cl.expResult = that.filterResult(
                              cl.value,
                              cl.maxNum,
                              cl.minNum,
                              cl.minSelected,
                              cl.maxSeleced
                            );
                          } else if (cl.resultType === '2') {
                            // 枚举型判定
                            const dList = await that.getResultDictionary(cl.custlabel);
                            cl.expResult = that.filterMJResult(
                              dList,
                              cl.qualifiedOption,
                              cl.noQualifiedOption,
                              cl.value
                            );
                            // console.log(cl.expResult)
                          }
                        } else if (cl.isDetermine === false || cl.isDetermine === 'false') {
                          cl.expResult = '不判定';
                        } else {
                          cl.expResult = '';
                        }
                      }
                    }
                  }
                });
              }
              if (erl.selectFlag === 1 || erl.selectFlag === '1') {
                newExperimentRequestList.push(erl);
              }
              // console.log(erl)
            });
            const newExperimentRequestList1 = _.orderBy(newExperimentRequestList, ['order']);
            // console.log(newExperimentRequestList1)
            that.detailData.reportStandardExpResponse.experimentRequestList = newExperimentRequestList1;
          }
          that.detailData.imgAllList = that.detailData.experimentImgRecordResponse.reportimageEntityList.concat(
            that.detailData.experimentImgRecordResponse.list
          );
          if (that.detailData.imgAllList.length > 0) {
            that.detailData.imgAllList.forEach(img => {
              that.srcList.push(img.imageUrl);
            });
          }
          // console.log(that.detailData)
        }
      });
    },
    // 获取枚举型字典
    getResultDictionary(code) {
      return new Promise((resolve, reject) => {
        getDictionary(code)
          .then(res => {
            if (res !== false) {
              resolve(res.data.data.dictionaryoption);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    exportExcel() {
      var type = 1;
      var tHeader = [];
      var tHeaders = [];
      var fieldAll = [];
      var fileName = '模板';
      var that = this;
      that.expLoading = true;
      var userName =
        this.detailData.reportdetailresultinfoResponse.editorName ||
        getNameByid(this.detailData.reportdetailresultinfoResponse.editorId) ||
        this.detailData.reportdetailresultinfoResponse.editorId;
      var filterResult = this.filterTestResult(this.detailData.reportdetailresultinfoResponse.reportResult) || '--';
      var jydx =
        this.detailData.reportDetailResponse.type === 1
          ? this.detailData.reportDetailResponse.inputWarehouseNo || '--'
          : this.detailData.reportDetailResponse.productionOrderNo || '--';
      var dxwz =
        this.detailData.reportDetailResponse.type === 1
          ? this.detailData.reportDetailResponse.wareHouseName || '--'
          : (this.detailData.reportDetailResponse.productionProcedure || '--') +
            '-' +
            (this.detailData.reportDetailResponse.productionStation || '--');
      var dxmc =
        this.detailData.reportDetailResponse.type === 1
          ? this.detailData.reportDetailResponse.supplierName || '--'
          : this.detailData.reportDetailResponse.customerName || '--';
      var scsl =
        this.detailData.reportDetailResponse.productionQuantity +
        filterSampleUnitToName(this.detailData.reportDetailResponse.productionUnit);
      var rksl =
        this.detailData.reportDetailResponse.inputWarehouseQuantity +
        filterSampleUnitToName(this.detailData.reportDetailResponse.inputWarehouseUnit);

      if (type === 1) {
        fileName = '检测报告';
        const tHeaderTop = ['序号', '检测项目', '结论', '标准描述', '单位', '检测结果'];
        const tHeaderBottom = ['', '', '', '', '', ''];
        tHeaders = [
          [
            '样品编号:',
            this.detailData.reportEntity.secSampleNum,
            '报告编号:',
            this.detailData.reportEntity.reportNo,
            '样品名称:',
            this.detailData.reportDetailResponse.sampleName
          ],
          [
            '型号规格:',
            this.detailData.reportDetailResponse.prodType,
            '物资分类:',
            this.detailData.reportDetailResponse.mateType,
            '检验对象:',
            jydx
          ],
          ['对象位置：', dxwz, '对象名称：', dxmc, '生产数量：', scsl],
          [
            '入库数量：',
            rksl,
            '批次：',
            this.detailData.reportDetailResponse.batchNo,
            '检验盘号：',
            this.detailData.reportDetailResponse.reelNo
          ],
          [
            '编制日期：',
            this.detailData.reportdetailresultinfoResponse.editDate,
            '试验负责人：',
            userName,
            '检验结果：',
            filterResult
          ],
          [''],
          tHeaderTop
        ];
        fieldAll = ['number', 'name', 'expResult', 'expRequirement', 'expValueUnit', 'filterValue'];
        Object.keys(this.detailData.allCoreColour).forEach((item, index) => {
          tHeaderBottom.push(this.detailData.allCoreColour[item]);
          fieldAll.push(item);
          tHeaders[0].push('');
          // if (index !== Object.keys(this.detailData.allCoreColour).length - 1) {
          // }
        });
        tHeader = tHeaderBottom;
      }
      var reportList = this.filterListNumber(that.detailData.reportStandardExpResponse.experimentRequestList, fieldAll);
      import('@/utils/Export2Excel').then(excel => {
        const data = reportList;
        excel.export_json_to_excel({
          multiHeader: tHeaders,
          header: tHeader,
          data,
          filename: fileName,
          autoWidth: true,
          bookType: 'xlsx'
        });
        that.expLoading = false;
      });
    },
    filterListNumber(list, fieldAll) {
      var newList = [];
      var that = this;
      if (list && list.length > 0) {
        list.forEach((L, index) => {
          L.number = index + 1 + '';
          newList.push([L.number, L.name]);
          if (L.childList && L.childList.length > 0) {
            L.childList.forEach((c, cindex) => {
              c.number = L.number + '.' + (cindex + 1);
              c.filterValue = that.filterValueUpload(c.value);
              c.expResult = c.expResult || that.filterResult(c.value, c.maxNum, c.minNum, c.minselected, c.maxseleced);
              Object.keys(this.detailData.allCoreColour).forEach(item => {
                c[item] = this.filterChildColor(c.value, item);
              });
            });
          }
          newList = newList.concat(this.formatJson(fieldAll, L.childList));
        });
        return newList;
      } else {
        return [];
      }
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v =>
        filterVal.map(j => {
          if (j === 'timestamp') {
            return parseTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    },
    filterChildColor(childValue, fieldName) {
      const result = {};
      childValue.forEach(item => {
        if (item.paraKey.indexOf('xxys') !== -1) {
          const key = item.paraKey.slice(item.paraKey.indexOf('xxys'), item.paraKey.length);
          result[key] = item.paraValue;
          // if (key.splice('-').length === 2) {
          // } else {
          //   console.log('切割两次')
          // }
        }
      });
      return result[fieldName];
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--vertical) {
  height: 40px;
}
:deep(.el-table__expanded-cell) {
  border-left: 4px solid;
  padding: 0 !important;
  margin-left: 10px !important;
}
.export-btn {
  text-align: right;
  margin-bottom: 10px;
}

.img-list {
  width: 146px;
  height: auto;
  text-align: center;
  margin: 0px 5px;
  border-radius: 4px;
  float: left;
  .el-image {
    width: 146px;
    height: 146px;
    border: 1px solid #e6e8ee;
    position: relative;
    border-radius: 4px;
  }
  .img-name {
    word-break: break-all;
  }
  .el-icon-picture-outline,
  .el-icon-loading {
    line-height: 146px;
    font-size: 26px;
    color: #909399;
  }
}
</style>
