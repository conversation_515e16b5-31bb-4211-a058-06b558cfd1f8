<template>
  <!-- 合格证生成 -->
  <ListLayout>
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="param">
          <el-input
            v-model="formInline.param"
            v-trim
            v-focus
            placeholder="请输入关键字搜索"
            class="ipt-360"
            prefix-icon="el-icon-search"
            size="large"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="handleSearch">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
          <el-button
            class="searchBtn"
            size="large"
            type="text"
            @click="advancedSearch"
            @keyup.prevent
            @keydown.enter.prevent
          >
            <span>高级搜索</span>
            <i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']" />
          </el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('CertificateBatchEdit')"
        type="primary"
        size="large"
        icon="el-icon-edit"
        :disabled="selectedItems.length === 0"
        @click="handleBatchModify"
        @keyup.prevent
        @keydown.enter.prevent
      >
        批量编辑
      </el-button>
      <el-button
        v-if="getPermissionBtn('CertificatePrint')"
        type="primary"
        size="large"
        icon="el-icon-print"
        :disabled="selectedItems.length === 0"
        @click="handleBatchPrint"
        @keyup.prevent
        @keydown.enter.prevent
      >
        批量打印
      </el-button>
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="searchFromRef" :model="searchForm" label-width="80px" label-position="right">
            <el-form-item label="物资分类：">
              <el-radio-group v-model="searchForm.mateType" size="small" max="1">
                <el-radio-button v-for="item in materialTypes" :key="item.code" :label="item.code">
                  {{ item.name }}
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="制造日期：" prop="suppDate">
              <el-date-picker v-model="searchForm.suppDate" type="date" size="small" />
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="16" class="flex gap-2">
          <el-radio-group v-model="printType" size="small" style="float: left" @change="onPrintTypeChange">
            <el-radio-button label="all">全部</el-radio-button>
            <el-radio-button label="unprinted">未打印</el-radio-button>
            <el-radio-button label="printed">已打印</el-radio-button>
          </el-radio-group>
          <el-select
            v-model="formInline.type"
            placeholder="请选择检验类型"
            size="small"
            clearable
            filterable
            @change="getTableData()"
          >
            <el-option v-for="(val, key) in dictionary['JYLX'].enable" :key="key" :label="val" :value="Number(key)" />
          </el-select>
        </el-col>
        <el-col :span="8" style="text-align: right">
          <TableColumnView binding-menu="CertificateExport" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      height="auto"
      size="medium"
      highlight-current-row
      class="dark-table format-height-table base-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      @header-dragend="drageHeader"
      @selection-change="onSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column
        v-if="getPermissionBtn('CertificateBatchEdit')"
        type="selection"
        fixed="left"
        prop="checkbox"
        :width="colWidth.checkbox"
        align="center"
      />
      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          :fixed="
            item.columnFixedType === columnFixedTypesEnum.Left
              ? 'left'
              : item.columnFixedType === columnFixedTypesEnum.Right
              ? 'right'
              : false
          "
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="item.fieldType === fieldTypesEnum.Person">
              <UserTag v-if="row[item.fieldKey]" :name="getNameByid(row[item.fieldKey]) || row[item.fieldKey]" />
              <span v-else>--</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Status">
              <div v-if="item.fieldKey === 'reportType'">
                {{
                  row.reportType === '0'
                    ? '合格'
                    : row.reportType === '1'
                    ? '不合格'
                    : row.reportType === '2'
                    ? '不判定'
                    : '--'
                }}
              </div>
              <el-tag v-else size="small" effect="dark" :type="setprintStatus(row[item.fieldKey])[0]">{{
                setprintStatus(row[item.fieldKey])[1]
              }}</el-tag>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Custom">
              <span v-if="item.fieldKey == 'type'">
                {{ dictionary['JYLX'].all?.[row.type] || '--' }}
              </span>
            </template>
            <span v-else>{{ row[item.fieldKey] || '--' }}</span>
          </template>
        </el-table-column>
      </template>
      <el-table-column
        v-if="getPermissionBtn('CertificatePrint')"
        label="操作"
        width="50"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <span class="blue-color margin-right-10" @click.stop="handlePrint(row)"> 打印 </span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-model:limit="pagination.limit"
      :page="pagination.page"
      :total="pagination.total"
      :page-sizes="[20, 50, 100, 300]"
      @pagination="getTableData"
    />
    <template #other>
      <el-dialog
        v-model="dialogVisible"
        :title="dialogType === 'batchModify' ? '批量编辑' : '合格证打印'"
        :close-on-click-modal="false"
        width="20vw"
      >
        <template v-if="dialogType === 'batchModify'">
          <el-form
            v-if="modifying"
            ref="batchModifyFormRef"
            :model="batchModifyForm"
            :rules="batchModifyFormRules"
            label-position="top"
          >
            <el-form-item label="请选择要修改的字段：" prop="field">
              <el-select
                v-model="batchModifyForm.field"
                size="small"
                placeholder=""
                style="width: 100%"
                @change="onModifyFieldChange"
              >
                <el-option
                  v-for="item in modifiableFields"
                  :key="item.fieldKey"
                  :label="item.fieldName"
                  :value="item.fieldKey"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="修改为：" prop="valueType">
              <el-radio-group v-model="batchModifyForm.valueType" size="small">
                <el-radio label="fixed">固定值</el-radio>
                <el-radio label="empty">空值</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="batchModifyForm.valueType === 'fixed'" prop="value">
              <el-input
                v-if="batchModifyForm.fieldType === 'text'"
                v-model="batchModifyForm.value"
                v-trim
                size="small"
                clearable
              />
              <el-date-picker
                v-else-if="batchModifyForm.fieldType === 'date'"
                v-model="batchModifyForm.value"
                type="date"
                size="small"
              />
              <el-select
                v-else-if="batchModifyForm.fieldType === 'person'"
                v-model="batchModifyForm.value"
                placeholder=""
                size="small"
                clearable
              >
                <el-option v-for="item in userNameList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
              <el-select
                v-else-if="batchModifyForm.field === 'reportType'"
                v-model="batchModifyForm.value"
                placeholder=""
                size="small"
                clearable
              >
                <el-option v-for="item in reportTypes" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>
          </el-form>
          <div v-else style="text-align: center">
            <i class="el-icon-success" style="font-size: 50px; color: var(--tesPrimary)" />
            <p style="line-height: 2">批量编辑成功{{ modifiedCount }}条数据</p>
            <el-button type="primary" plain @click="modifying = true">继续编辑</el-button>
          </div>
        </template>
        <template v-if="dialogType === 'printTemplate'">
          <el-form
            ref="printTemplateFormRef"
            :model="printTemplateForm"
            :rules="printTemplateFormRules"
            label-position="top"
          >
            <el-form-item label="合格证模板：" prop="printTemplate">
              <el-select v-model="printTemplateForm.printTemplateId" placeholder="" size="small" style="width: 100%">
                <el-option v-for="item in certificateTemplates" :key="item.id" :label="item.name" :value="item.id">
                  <span style="float: left">{{ item.name }}</span>
                  <span
                    style="
                      float: right;
                      display: inline-block;
                      color: #909399;
                      font-size: 13px;
                      max-width: 200px;
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                    "
                    >{{ item.description }}</span
                  >
                </el-option>
              </el-select>
              <el-checkbox v-model="rememberMyChoise">记住我的选择</el-checkbox>
            </el-form-item>
          </el-form>
        </template>
        <template #footer>
          <span class="dialog-footer">
            <el-button v-if="dialogType === 'batchModify' && !modifying" type="primary" @click="handleCancel"
              >完 成</el-button
            >
            <template v-else>
              <el-button @click="handleCancel">取 消</el-button>
              <el-button type="primary" @click="handleConfirm">确 认</el-button>
            </template>
          </span>
        </template>
      </el-dialog>
    </template>
  </ListLayout>
</template>

<script>
import { ref, reactive, toRefs, onMounted, nextTick } from 'vue';
import { useStore } from 'vuex';
import { ElMessage } from 'element-plus';
import router from '@/router/index.js';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { formatDate } from '@/utils/formatTime';
import { colWidth } from '@/data/tableStyle';
import { reportTypes } from '@/data/industryTerm';
import { getCertificatePrintList, saveCertificatePrint } from '@/api/certificate-export';
import { certificateList } from '@/api/material';
import ListLayout from '@/components/ListLayout';
import UserTag from '@/components/UserTag';
import Pagination from '@/components/Pagination';
import TableColumnView from '@/components/TableColumnView';
import { getDictionary } from '@/api/user';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';

export default {
  name: 'CertificateExport',
  components: { ListLayout, UserTag, Pagination, TableColumnView },
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    const store = useStore();
    const state = reactive({
      formInline: {
        param: ''
      },
      searchForm: {
        mateType: '',
        suppDate: ''
      },
      showS: false,
      activeName: '0',
      printType: 'all',
      tableKey: 0,
      dictionary: {
        JYLX: {
          enable: {},
          all: {}
        }
      },
      tableLoading: false, // 表格加载的loading
      tableColumns: [], // 表头
      isSinglePrint: false, // 是否是单个打印
      tableData: [], // 表格数据
      selectedItems: [],
      modifiedCount: 0,
      hasSetSelection: false,
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      dialogVisible: false,
      dialogType: 'batchModify',
      modifying: true,
      batchModifyForm: {
        field: '',
        fieldType: '',
        value: '',
        valueType: 'fixed'
      },
      modifiableFields: [],
      originCertificateTemplates: [],
      certificateTemplates: [],
      printTemplateForm: {
        printTemplateId: ''
      },
      rememberMyChoise: false,
      currentRow: {}
    });

    const materialTypes = reactive([{ name: '全部', code: 'all' }, ...store.state.user.materialList]);

    const tableRef = ref();

    const getDictionaryList = () => {
      Object.keys(state.dictionary).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          state.dictionary[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              state.dictionary[item].enable[optionItem.code] = optionItem.name;
            }
            state.dictionary[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    getDictionaryList();

    // 过滤审批状态颜色
    const setprintStatus = status => {
      // 打印状态(0:未打印，1:已打印)
      const classMap = {
        0: ['info', '未打印'],
        1: ['success', '已打印']
      };
      return classMap[status];
    };

    const userNameList = reactive(store.state.common.nameList);

    const batchModifyFormRef = ref();

    const batchModifyFormRules = {
      field: [{ required: true, message: '请选择要修改的字段', trigger: 'change' }],
      value: [
        {
          trigger: 'blur',
          validator: (rule, value, callback) => {
            if (value === '' && state.batchModifyForm.field && state.batchModifyForm.valueType === 'fixed') {
              callback(new Error('固定值不能为空'));
            } else {
              callback();
            }
          }
        }
      ]
    };

    const printTemplateFormRef = ref();

    const printTemplateFormRules = {
      printTemplateId: [{ required: true, message: '请选择要打印的模板', trigger: 'change' }]
    };

    onMounted(() => {
      getTableData();
      getCertificateTemplates();
      const printTemplateId = localStorage.getItem('myTemplateChoise');
      if (printTemplateId) {
        state.printTemplateForm.printTemplateId = printTemplateId;
        state.rememberMyChoise = true;
      }
    });

    const getTableData = async query => {
      const params = {
        page: query?.page || state.pagination.page.toString(),
        limit: query?.limit || state.pagination.limit.toString(),
        printStatus: state.printType === 'all' ? '' : state.printType === 'unprinted' ? '0' : '1',
        mateType: state.searchForm.mateType === 'all' ? '' : state.searchForm.mateType,
        suppDate: formatDate(state.searchForm.suppDate),
        ...state.formInline
      };
      const selectedIds = state.selectedItems.map(item => item.id);
      state.tableLoading = true;
      const res = await getCertificatePrintList(params);
      state.tableLoading = false;
      if (res) {
        state.tableData = res.data.data.list;
        state.pagination.total = res.data.data.totalCount;
        state.pagination.page = Number(params.page);
        if (state.hasSetSelection && selectedIds.length > 0) {
          nextTick(() => {
            if (state.tableData.length === selectedIds.length) {
              tableRef.value?.toggleAllSelection();
              state.selectedItems = state.tableData;
            } else {
              const newSelectedItems = [];
              state.tableData.forEach(item => {
                for (let index = 0; index < selectedIds.length; index++) {
                  if (item.id === selectedIds[index]) {
                    newSelectedItems.push(item);
                    tableRef.value?.toggleRowSelection(item, true);
                    break;
                  }
                }
              });
              state.selectedItems = newSelectedItems;
            }
          });
        }
      }
    };

    // 合格证模板
    const getCertificateTemplates = async () => {
      const res = await certificateList({ page: '1', limit: '-1', categoryId: '' });
      if (res) {
        state.originCertificateTemplates = res.data.data.list.filter(item => item.category === 1 && item.status === 1);
      }
    };

    const handleSearch = () => {
      state.hasSetSelection = false;
      state.selectedItems = [];
      getTableData();
    };

    const reset = () => {
      state.formInline = {
        param: ''
      };
      state.searchForm = {
        mateType: '',
        suppDate: ''
      };
      state.pagination = {
        page: 1,
        limit: 20,
        total: 0
      };
      state.hasSetSelection = false;
      state.selectedItems = [];
      getTableData();
    };

    // 高级搜索
    const advancedSearch = () => {
      state.showS = !state.showS;
      if (state.activeName === '0') {
        state.activeName = '1';
      } else {
        state.activeName = '0';
      }
    };

    const handleBatchModify = () => {
      state.dialogVisible = true;
      state.dialogType = 'batchModify';
    };

    const handleBatchPrint = () => {
      state.dialogVisible = true;
      state.dialogType = 'printTemplate';
      state.isSinglePrint = false;
      state.currentRow = state.selectedItems[0];
      if (state.selectedItems[0].mateType) {
        state.certificateTemplates = state.originCertificateTemplates.filter(
          item => item.categoryCode === state.selectedItems[0].mateType
        );
      }
    };

    const onPrintTypeChange = () => {
      getTableData({ page: '1' });
    };

    const onUpdateColumns = columns => {
      state.tableKey = state.tableKey + 1;
      state.tableColumns = columns;
      state.modifiableFields = columns.filter(
        column => !/^(productionOrderNo|no|secSampleNum|printStatus)$/.test(column.fieldKey)
      );
    };

    const onSelectionChange = selection => {
      state.selectedItems = selection;
    };
    const handleRowClick = row => {
      tableRef.value?.toggleRowSelection(
        row,
        !state.selectedItems.some(item => {
          return row.sampleId === item.sampleId;
        })
      );
    };

    const handlePrint = row => {
      state.dialogVisible = true;
      state.dialogType = 'printTemplate';
      state.isSinglePrint = true;
      state.currentRow = row;
      if (row.mateType) {
        state.certificateTemplates = state.originCertificateTemplates.filter(
          item => item.categoryCode === row.mateType
        );
      }
    };

    const onModifyFieldChange = fieldKey => {
      state.batchModifyForm.value = '';
      state.batchModifyForm.fieldType = state.modifiableFields.find(item => item.fieldKey === fieldKey)?.fieldType;
    };

    const handleCancel = () => {
      state.dialogVisible = false;
      state.modifying = true;
      state.batchModifyForm = {
        field: '',
        fieldType: '',
        value: '',
        valueType: 'fixed'
      };
      batchModifyFormRef.value?.clearValidate();
    };

    const handleConfirm = () => {
      state.dialogType === 'batchModify' ? bacthModifySave() : gotoTemplatePrint();
    };

    // 保存批量修改
    const bacthModifySave = async () => {
      await batchModifyFormRef.value.validate();
      const entityList = state.selectedItems.map(item => ({
        ...item,
        [state.batchModifyForm.field]: state.batchModifyForm.valueType === 'fixed' ? state.batchModifyForm.value : ''
      }));
      state.modifiedCount = state.selectedItems.length;
      const res = await saveCertificatePrint({ entityList });
      if (res) {
        ElMessage.success('批量编辑成功！');
        state.modifying = false;
        state.hasSetSelection = true;
        getTableData();
      }
    };

    // const gotoTemplatePrint = async () => {
    //   const templateItem = state.certificateTemplates.find(item => item.id === state.printTemplateForm.printTemplateId);
    //   if (!templateItem) {
    //     ElMessage.warning('未找到模板！');
    //     return;
    //   }
    //   const fileUrl = templateItem.fileUrl;
    //   if (state.rememberMyChoise) {
    //     localStorage.setItem('myTemplateChoise', templateItem.id);
    //   }
    //   await printTemplateFormRef.value.validate();
    //   router.push({
    //     path: '/qualityManagement/online-excel',
    //     query: {
    //       id: state.currentRow.id,
    //       sampleId: state.currentRow.sampleId,
    //       url: import.meta.env.DEV ? fileUrl.replace(window.location.host, '*************') : fileUrl
    //     }
    //   });
    // };
    const gotoTemplatePrint = async () => {
      const templateItem = state.certificateTemplates.find(item => item.id === state.printTemplateForm.printTemplateId);
      if (!templateItem) {
        ElMessage.warning('未找到模板！');
        return;
      }
      const fileUrl = templateItem.fileUrl;

      const ids = state.isSinglePrint ? [state.currentRow.sampleId] : state.selectedItems.map(item => item.sampleId);

      await printTemplateFormRef.value.validate();
      router.push({
        path: '/qualityManagement/batch-print-certificate',
        query: {
          ids: JSON.stringify(ids),
          paperSize: templateItem.paperSize,
          paperPrintWidth: templateItem.paperPrintWidth,
          paperPrintHeight: templateItem.paperPrintHeight,
          paperOrientation: templateItem.paperPrintOrientation,
          url: import.meta.env.DEV ? fileUrl.replace(window.location.host, '*************') : fileUrl
        }
      });
    };

    return {
      ...toRefs(state),
      materialTypes,
      tableRef,
      handleRowClick,
      handleBatchPrint,
      setprintStatus,
      userNameList,
      reportTypes,
      batchModifyFormRef,
      batchModifyFormRules,
      printTemplateFormRef,
      printTemplateFormRules,
      getNameByid,
      getPermissionBtn,
      getTableData,
      handleSearch,
      reset,
      advancedSearch,
      handleBatchModify,
      onPrintTypeChange,
      onUpdateColumns,
      drageHeader,
      onSelectionChange,
      handlePrint,
      onModifyFieldChange,
      handleCancel,
      handleConfirm,
      colWidth,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  }
};
</script>
