<template>
  <div class="tes-home-main-contant">
    <div class="page-header">
      <div class="header-left">
        <div class="left-icon">
          <span class="name-icon">{{ accountStr }}</span>
        </div>
        <div class="left-content">
          <div class="name">
            Hi {{ nickname }}, 欢迎使用 {{ systemName }}
            <el-popover v-model:visible="visible" placement="bottom" title="首页配置" :width="300" trigger="click">
              <template #reference>
                <img class="icon-setting" src="@/assets/img/wrench.png" alt="" />
              </template>
              <ul id="sortableList" class="homePageSetting">
                <li>
                  <span class="sortingIcon">排序</span>
                  <span class="moduleName">模块名</span>
                  <span class="showModule" />
                </li>
                <li v-for="(item, index) in showPageSetting" :key="item.code" class="moduleMove">
                  <span class="sortingIcon tes-move iconfont" style="font-size: 12px; cursor: move" />
                  <span class="moduleName">{{ item.name }}</span>
                  <span class="showModule">
                    <el-switch
                      v-model="item.params.checked"
                      :active-value="'true'"
                      :inactive-value="'false'"
                      size="mini"
                      @change="
                        val => {
                          return handleChangeChecked(val, index);
                        }
                      "
                    />
                  </span>
                </li>
              </ul>
              <el-button size="small" style="width: 100%" @click="saveTypography">应用设置</el-button>
            </el-popover>
          </div>
          <span class="time">今天是 {{ formatDate(new Date(), 1) }}</span>
        </div>
      </div>
      <div class="header-right">
        <div class="right-content">
          <p class="title">即将超期待办</p>
          <div
            :class="[nearOverdueNum === 0 ? 'number-none' : 'number font-barlow']"
            class="cursorPoint"
            @click="goAgency"
          >
            {{ nearOverdueNum === 0 ? '暂无' : nearOverdueNum }}
          </div>
        </div>
        <el-divider direction="vertical" />
        <div class="right-content">
          <p class="title">已超期待办</p>
          <div
            :class="[overdueNum === 0 ? 'number-none' : 'number font-barlow']"
            class="cursorPoint"
            @click="goAgency(1)"
          >
            {{ overdueNum === 0 ? '暂无' : overdueNum }}
          </div>
        </div>
      </div>
    </div>
    <el-row v-loading="showPageLoading" class="main-contant">
      <el-col :span="18" class="left-contant">
        <div v-for="val in showPageSetting" :key="val.code">
          <div v-if="val.code === 'DBSX' && val.params.checked === 'true'" class="agency-item moveItem card">
            <div class="top-agency">
              <div class="title">待办事项</div>
            </div>
            <div class="list-main">
              <div v-for="item in optionLists" :key="item" class="item-list" @click="clickData(item.groupName)">
                <div class="left">
                  <div class="name">
                    <div class="groupName">{{ item.groupName }}</div>
                    <div :class="[item.num === 0 ? 'num-none' : 'num font-barlow']">
                      {{ item.num === 0 ? '暂无' : item.num }}
                    </div>
                  </div>
                  <div class="img">
                    <SvgIcon :icon-class="item.icon" :width="50" :height="50" />
                  </div>
                </div>
                <div class="right">
                  <span class="el-icon-arrow-right" />
                </div>
              </div>
            </div>
          </div>
          <div v-if="val.code === 'WDDB' && val.params.checked === 'true'" class="my-agency moveItem card">
            <div class="top-agency">
              <div class="title">我的待办</div>
              <div class="more-item" @click="moreItem">查看全部 <i class="el-icon-arrow-right" /></div>
            </div>
            <div class="my-agency-right">
              <el-calendar ref="calendarRef" v-model="calendarValue">
                <template #dateCell="{ data }">
                  <el-popover
                    :width="370"
                    :hide-after="0"
                    placement="top-end"
                    trigger="click"
                    popper-class="calendar-popover"
                    :disabled="filterDayDate(data.day).strList.length === 0"
                  >
                    <template #reference>
                      <div class="my-agency-calendar" @click="selectCalendar(data)">
                        <span>{{ data.day.split('-')[2] }}</span>
                        <div v-for="item in filterDayDate(data.day).arr" :key="item" class="calender-item">
                          {{ item.groupName }}
                        </div>
                        <div v-if="filterDayDate(data.day).length > 0" class="more-num">
                          + {{ filterDayDate(data.day).length }} 项
                        </div>
                      </div>
                    </template>
                    <div class="calendar-content">
                      <div
                        v-for="item in agencyList"
                        :key="item"
                        class="content-list"
                        @click="gotoDetailPage(item.forwardUrl)"
                      >
                        <div class="flag-main">{{ item.content }}</div>
                        <div class="flag-content">
                          <div class="flag-tags">
                            <el-tag v-if="item.status === 0" class="tag-doing" size="small" type="warning"
                              >未完成</el-tag
                            >
                            <el-tag v-else size="small" class="tag-done" type="success">已完成</el-tag>
                            <el-tag size="small" class="tag-user">
                              <span class="icon el-icon-user-solid" />
                              <span class="name ellipsis"> {{ item.senderName }}</span>
                            </el-tag>
                          </div>
                          <div class="flag-time">{{ item.startDate + ' ~ ' + item.endDate }}</div>
                        </div>
                      </div>
                    </div>
                  </el-popover>
                </template>
              </el-calendar>
            </div>
          </div>
          <UserToDo v-if="val.code === 'DBCLQK' && val.params.checked === 'true'" class="moveItem card" />
        </div>
      </el-col>
      <el-col :span="6" class="right-contant">
        <div v-for="value in showPageSetting" :key="value.code">
          <PersonalTask v-if="value.code === 'GRRWTJ' && value.params.checked === 'true'" class="card moveItem" />
          <loginInformation v-if="value.code === 'YHDLQK' && value.params.checked === 'true'" class="card" />
          <BulletinBoard v-if="value.code === 'GGL' && value.params.checked === 'true'" class="card moveItem" />
          <PersonalCollection v-if="value.code === 'GRSC' && value.params.checked === 'true'" class="card moveItem" />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getCurrentInstance, reactive, toRefs, ref, onMounted, nextTick } from 'vue';
import { getLoginInfo, getTenantConfig } from '@/utils/auth';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { makePy } from '@/utils/chinese-to-str';
import { formatDate, formatYM } from '@/utils/formatTime';
import {
  getOverdue,
  getListNotNum,
  getTodoByDay,
  getTodoByMonth,
  getStatistics,
  sysLayoutInfo,
  sysLayoutSave
} from '@/api/home';
import router from '@/router';
import { mapGetters } from 'vuex';
import _ from 'lodash';
import UserToDo from './components/UserToDo.vue';
import SvgIcon from '@/components/SvgIcon';
import PersonalTask from './components/PersonalTask.vue';
import loginInformation from './components/loginInformation.vue';
import BulletinBoard from './components/BulletinBoard.vue';
import PersonalCollection from './components/PersonalCollection.vue';
import { getDay } from '@/utils/getDay';
import { formatPagePermission } from '@/utils/formatJson';
import Sortable from 'sortablejs';

export default {
  name: 'TesMain',
  components: { UserToDo, loginInformation, BulletinBoard, PersonalCollection, SvgIcon, PersonalTask },
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close'],
  setup() {
    const { proxy } = getCurrentInstance();
    const activeAccordion = ref('3');
    const datas = reactive({
      activeAccordion,
      systemName: getTenantConfig().systemName,
      accountId: '',
      showPageLoading: false,
      visible: false,
      nickname: getLoginInfo().nickname,
      accountStr: '',
      overdueNum: 0,
      showPageSetting: [
        {
          name: '待办事项',
          code: 'DBSX',
          params: {
            checked: 'true'
          }
        },
        {
          name: '个人任务统计',
          code: 'GRRWTJ',
          params: {
            checked: 'true'
          }
        },
        {
          name: '我的待办',
          code: 'WDDB',
          params: {
            checked: 'true'
          }
        },
        {
          name: '近30日用户待办处理情况',
          code: 'DBCLQK',
          params: {
            checked: 'false'
          }
        },
        {
          name: '近30天用户登录情况',
          code: 'YHDLQK',
          params: {
            checked: 'false'
          }
        },
        {
          name: '公告栏',
          code: 'GGL',
          params: {
            checked: 'false'
          }
        },
        {
          name: '个人收藏',
          code: 'GRSC',
          params: {
            checked: 'false'
          }
        }
      ],
      // 首页配置
      nearOverdueNum: 0,
      numList: [],
      optionLists: [
        { icon: 'tes-task-issued', groupName: '样品下达', permissionKey: 'sampleOrderBtn', num: 0, bg: 'bg2' },
        { icon: 'tes-test-allocation', groupName: '检测分配', permissionKey: 'allocationBtn', num: 0, bg: 'bg3' },
        { icon: 'tes-test-exe', groupName: '检测执行', permissionKey: 'rawDataAdd', num: 0, bg: 'bg1' },
        { icon: 'tes-record-review', groupName: '试验审核', permissionKey: 'recordAudit', num: 0, bg: 'bg4' },
        { icon: 'tes-report-bianzhi', groupName: '报告编制', permissionKey: 'reportAdd', num: 0, bg: 'bg2' },
        { icon: 'tes-report-approval', groupName: '报告审核', permissionKey: 'reportAudit', num: 0, bg: 'bg3' },
        { icon: 'tes-report-sign', groupName: '报告签字', permissionKey: 'reportAudit', num: 0, bg: 'bg1' }
        // { icon: 'tes-report-seal', groupName: '报告盖章', num: 0, bg: 'bg4' },
        // { icon: 'tes-report-send', groupName: '报告发送', num: 0, bg: 'bg2' }
      ],
      tabOptions: [
        { lable: '今天', name: 'first' },
        { lable: '未来三天', name: 'second' },
        { lable: '未来七天', name: 'third' }
      ],
      activeName: 'first',
      currentStatus: 'all',
      dayList: {},
      agencyList: [],
      startTime: null,
      endTime: null,
      calendarValue: new Date(),
      calendarRef: ref(),
      monthData: {},
      statisticsData: {
        yesterdayNum: 0,
        thisWeekNum: 0,
        todayNewNum: 0,
        remainNum: 0
      }
    });

    datas.accountId = getLoginInfo().accountId;
    const name = getLoginInfo().username;
    datas.accountStr = makePy(name.charAt(0))[0].toUpperCase();
    // tab切换
    const handleClick = tab => {
      // console.log(tab.props.name)
      switch (tab.props.name) {
        case 'first':
          datas.startTime = formatDate(new Date());
          datas.endTime = formatDate(new Date());
          break;
        case 'second':
          datas.startTime = getDay(1);
          datas.endTime = getDay(3);
          break;
        case 'third':
          datas.startTime = getDay(1);
          datas.endTime = getDay(7);
          break;
      }
      proxy.getTodoByDays(datas.startTime, datas.endTime);
    };
    // 切换changeStatus
    const changeStatus = (val, list) => {
      const arr = [];
      const arr1 = [];
      if (list.length > 0) {
        list.forEach(data => {
          if (data.status === 0) {
            arr.push(data);
          } else {
            arr1.push(data);
          }
        });
        if (val === 'all') {
          datas.agencyList = list;
        } else if (val === '0') {
          datas.agencyList = arr;
        } else {
          datas.agencyList = arr1;
        }
      } else {
        datas.agencyList = [];
      }
    };
    // 更多
    const moreItem = () => {
      router.push({ name: 'MyAgency' });
    };
    // 跳转到待办
    const goAgency = flg => {
      if (flg === 1) {
        router.push({ name: 'MyAgency', params: { todoStatus: 2 } });
      } else {
        router.push({ name: 'MyAgency', params: { todoStatus: 0 } });
      }
    };
    // 点击日历
    const selectCalendar = data => {
      datas.currentStatus = 'all';
      proxy.getTodoByDays(data.day, data.day);
    };

    // 点击跳转对应列表
    const clickData = flag => {
      switch (flag) {
        case '样品下达':
          formatPagePermission('SampleOrder') ? router.push({ name: 'SampleOrder' }) : '';
          break;
        case '检测执行':
          formatPagePermission('TestExecutionList') ? router.push({ name: 'TestExecutionList' }) : '';
          break;
        case '检测分配':
          formatPagePermission('TestAllocation') ? router.push({ name: 'TestAllocation', query: { status: 1 } }) : '';
          break;
        case '试验审核':
          formatPagePermission('RecordReviewList') ? router.push({ name: 'RecordReviewList' }) : '';
          break;
        case '报告编制':
          formatPagePermission('TestReport') ? router.push({ name: 'TestReport', query: { examineStatus: 1 } }) : '';
          break;
        case '报告审核':
          formatPagePermission('TestReport') ? router.push({ name: 'TestReport', query: { examineStatus: 2 } }) : '';
          break;
        case '报告签字':
          formatPagePermission('TestReport') ? router.push({ name: 'TestReport', query: { examineStatus: 3 } }) : '';
          break;
        case '报告盖章':
          formatPagePermission('TestReport') ? router.push({ name: 'TestReport', query: { examineStatus: 4 } }) : '';
          break;
        case '报告发送':
          formatPagePermission('TestReport') ? router.push({ name: 'TestReport', query: { examineStatus: 5 } }) : '';
          break;
      }
    };
    // 跳转到详情
    const gotoDetailPage = url => {
      window.location.href = url;
    };
    // 过滤日历每天待办数量
    const filterDayDate = day => {
      // console.log(day)
      const param = {
        arr: [],
        length: 0,
        strList: []
      };
      const items = _.filter(datas.monthData, list1 => {
        return day === list1.day;
      });
      if (items.length >= 1) {
        param.arr.push(items[0]);
        param.length = items.length - 1;
      }
      if (items.length > 0) {
        items.forEach(i => {
          // param.strList = param.strList + i.groupName + ': ' + i.num + ';' // + '&#13;'
          param.strList.push(i.groupName + '&nbsp;' + i.num);
        });
      }
      return param;
    };

    onMounted(() => {
      nextTick(() => {
        const calendarRef = datas.calendarRef[0];
        const headerHtml =
          '<thead><th>周日</th><th>周一</th><th>周二</th><th>周三</th><th>周四</th><th>周五</th><th>周六</th></thead>';
        calendarRef.$el.children[1].firstChild.firstChild.innerHTML = headerHtml;
        // 点击今天按钮
        calendarRef.$el.children[0].children[1].children[0].children[1].onclick = function () {
          proxy.getTodoByMonths(formatYM());
          proxy.getTodoByDays(formatDate(new Date()), formatDate(new Date()));
        };
        // // 点击上个月按钮
        calendarRef.$el.children[0].children[1].children[0].children[0].onclick = function () {
          // console.log(calendarRef.date.$d)
          proxy.getTodoByMonths(formatYM(calendarRef.date.$d));
          proxy.getTodoByDays(formatDate(calendarRef.date.$d), formatDate(calendarRef.date.$d));
        };
        // 点击下个月按钮
        calendarRef.$el.children[0].children[1].children[0].children[2].onclick = function () {
          proxy.getTodoByMonths(formatYM(calendarRef.date.$d));
          proxy.getTodoByDays(formatDate(calendarRef.date.$d), formatDate(calendarRef.date.$d));
        };
      });
      const newOptions = _.filter(datas.optionLists, opt => {
        return getPermissionBtn(opt.permissionKey);
      });
      datas.optionLists = newOptions;
    });
    // 处理个人统计
    const handleDispose = item => {
      window.location.href = item.forwardUrl;
    };
    // 配置首页
    const handleChangeChecked = (val, index) => {
      datas.showPageSetting[index].params.checked = val;
    };
    // 首页配置弹出框
    const afterEnter = isSortable => {
      datas.showPageLoading = true;
      sysLayoutInfo().then(res => {
        datas.showPageLoading = false;
        if (res) {
          datas.showPageSetting = [];
          res.data.data.forEach(item => {
            if (item.code !== 'GGL' && item.code !== 'GRSC') {
              datas.showPageSetting.push(item);
            } else if (item.code === 'GGL' && getPermissionBtn('AnnouncementDisplayHide')) {
              datas.showPageSetting.push(item);
            } else if (item.code === 'GRSC' && getPermissionBtn('FileDisplayHide')) {
              datas.showPageSetting.push(item);
            }
          });
          if (res.data.data.length > 0 && isSortable) {
            initSortable();
          }
        }
      });
    };
    nextTick(() => {
      afterEnter(true);
    });
    // 保存首页模板排版
    const saveTypography = () => {
      datas.visible = false;
      sysLayoutSave(datas.showPageSetting).then(res => {
        if (res) {
          proxy.$message.success(res.data.message);
          afterEnter();
        }
      });
    };
    const initSortable = () => {
      nextTick(() => {
        const el = document.getElementById('sortableList');
        Sortable.create(el, {
          animation: 300,
          handle: '.tes-move',
          draggable: '.moduleMove',
          ghostClass: 'ghost',
          // 拖动对象移动样式
          dragClass: 'drag',
          forceFallback: true,
          onEnd({ newIndex, oldIndex }) {
            if (oldIndex !== newIndex) {
              const currRow = datas.showPageSetting.splice(oldIndex - 1, 1)[0];
              datas.showPageSetting.splice(newIndex - 1, 0, currRow);
            }
          }
        });
      });
    };

    return {
      ...toRefs(datas),
      saveTypography,
      initSortable,
      handleChangeChecked,
      afterEnter,
      handleDispose,
      getNameByid,
      formatDate,
      handleClick,
      changeStatus,
      moreItem,
      goAgency,
      selectCalendar,
      clickData,
      gotoDetailPage,
      getPermissionBtn,
      filterDayDate,
      formatYM
    };
  },
  computed: {
    ...mapGetters(['materialList', 'webSocketMsg'])
  },
  watch: {
    webSocketMsg(msg) {
      this.getOverdues();
      this.getListNotNums();
      this.getTodoByDays(formatDate(new Date()), formatDate(new Date()));
      this.getTodoByMonths(formatYM());
      // this.getStatistics()
    }
  },
  created() {
    this.getOverdues();
    this.getListNotNums();
    this.getTodoByDays(formatDate(new Date()), formatDate(new Date()));
    this.getTodoByMonths(formatYM());
    // this.getStatistics()
    // console.log(this.materialList)
  },
  methods: {
    getOverdues() {
      var that = this;
      getOverdue().then(res => {
        if (res !== false) {
          that.overdueNum = res.data.data.overdueNum;
          that.nearOverdueNum = res.data.data.nearOverdueNum;
        }
      });
    },
    // 待办事项接口
    getListNotNums() {
      var that = this;
      getListNotNum({ messageType: 2 }).then(res => {
        if (res !== false) {
          that.numList = res.data.data;
          if (that.numList.length > 0) {
            that.optionLists.forEach(list => {
              const items = _.filter(that.numList, list1 => {
                return list.groupName === list1.groupName;
              });
              if (items.length > 0) {
                list = Object.assign(list, items[0]);
              }
            });
          }
        }
      });
    },
    getTodoByDays(startDay, endDay) {
      var that = this;
      const param = {
        startDay: startDay,
        endDay: endDay
      };
      getTodoByDay(param).then(res => {
        if (res !== false) {
          that.dayList = res.data.data;
          that.agencyList = that.dayList.todoDayRecord;
        }
      });
    },
    // 日历数据接口
    getTodoByMonths(month) {
      var that = this;
      getTodoByMonth({ month: month }).then(res => {
        if (res !== false) {
          that.monthData = res.data.data;
        }
      });
    },
    // 个人任务统计接口
    getStatistics() {
      const that = this;
      getStatistics().then(res => {
        if (res !== false) {
          that.statisticsData = res.data.data;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.cursorPoint {
  cursor: pointer;
}
li {
  list-style: none;
}
.homePageSetting {
  padding: 0;
  margin: 5px 0 5px 0;
  border-bottom: 1px solid #ebeef5;
  li {
    padding: 5px;
    border-bottom: 1px solid #ebeef5;
    font-size: 12px;
    height: 30px;
    line-height: 20px;
  }
  li:first-child {
    background-color: #f5f7fa;
    font-size: 14px;
    height: 35px;
    line-height: 25px;
  }
  li:last-child {
    border-bottom: 0;
  }
  span {
    display: inline-block;
    text-align: left;
    padding: 0 5px 0 5px;
    overflow: hidden;
  }
  span:nth-child(1) {
    text-align: center;
  }
  .moduleName {
    width: 59%;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .sortingIcon {
    width: 20%;
  }
}

.tes-home-main-contant {
  height: calc(100vh - 75px);
  overflow-y: auto;
  width: 100%;
  .page-header {
    border-bottom: 1px solid #f0f2f5;
    background: $background-color;
    padding: 8px 24px 12px;
    display: flex;
    justify-content: space-between;
    position: absolute;
    width: 100%;
    top: 0;
    right: 0;
    z-index: 10;
    .header-left {
      height: 62px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .left-icon {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background: $tes-primary;
        margin-right: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        .name-icon {
          font-size: 24px;
          color: $background-color;
        }
      }
      .left-content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
        span {
          display: block;
        }
        .name {
          font-size: 20px;
          color: $tes-font;
          margin-bottom: 20px;
        }
        .time {
          font-size: 14px;
          color: $tes-font1;
        }
        .icon-setting {
          display: inline-block;
          cursor: pointer;
          margin: 0 8px;
        }
      }
    }
    .header-right {
      width: 250px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .el-divider--vertical {
        height: 40px;
        margin-top: 14px;
      }
      .right-content {
        height: 50px;
        span {
          display: block;
        }
        .title {
          color: $tes-font2;
          font-size: 14px;
          margin-top: 0;
          margin-bottom: 14px;
        }
        .number {
          color: $tes-font;
          font-size: 24px;
          line-height: 1.5;
        }
        .number-none {
          color: $tes-font3;
          font-size: 22px;
          line-height: 1.5;
        }
      }
    }
  }
  .main-contant {
    position: relative;
    top: 90px;
    padding: 10px 24px;
    .top-agency {
      // background: #eff1f4;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 35px;
      margin-bottom: 0;
      .title {
        font-size: 16px;
        color: $tes-font;
      }
      .more-item {
        flex: auto;
        text-align: right;
        color: $tes-primary;
        font-size: 14px;
        cursor: pointer;
        i {
          font-size: 12px;
        }
      }
    }
    .left-contant {
      .agency-item {
        background: $background-color;
        border-radius: 4px;
        .list-main {
          width: 100%;
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap;
          flex-grow: 1;
          flex-shrink: 1;
          .item-list {
            height: 160px;
            padding: 14px 20px;
            border-radius: 3px;
            background: $background-color;
            border: 1px solid transparent;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            flex-grow: 1;
            flex-shrink: 1;
            cursor: pointer;
            transition: 250ms;
            .left {
              height: 100%;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              align-items: flex-start;
              .name {
                .groupName {
                  font-size: 14px;
                  color: $tes-font1;
                }
                .num {
                  font-size: 32px;
                  line-height: 1;
                  text-align: left;
                  color: $tes-font;
                }
                .num-none {
                  font-size: 16px;
                  line-height: 2;
                  text-align: left;
                  color: $tes-font3;
                }
              }
              .img {
                padding-bottom: 5px;
                img {
                  width: 40px;
                  height: 40px;
                }
              }
            }
            .right {
              display: none;
              font-size: 24px;
              color: var(--tesPrimary3);
              transition: 250ms;
            }
            &:hover {
              background: var(--tesPrimary2);
              border-color: var(--tesPrimary4);
              box-shadow: 0px 8px 12px var(--tesPrimary2);
              flex-grow: 2;
              .right {
                display: block;
              }
            }
          }
        }
      }
      .my-agency {
        background: $background-color;
        .my-agency-left {
          background: $background-color;
          box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.08);
          border-radius: 4px;
          border: 1px solid #e6f1fb;
          margin: 0px 5px 0px 15px;
          width: 330px;
          height: calc(100% - 40px);
          float: left;
        }
        .my-agency-right {
          width: 100%;
          .my-agency-calendar {
            width: 100%;
            height: 100%;
            padding: 8px;
            span {
              display: block;
              width: 22px;
              height: 22px;
              margin-bottom: 4px;
            }
            .calender-item {
              color: $tes-primary;
              background: $tes-primary2;
              border-radius: 2px;
              font-size: 14px;
              line-height: 22px;
              padding: 0 8px;
              border-left: 3px solid $tes-primary;
              text-align: left;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .more-num {
              color: $tes-primary;
              font-style: normal;
              font-weight: normal;
              font-size: 12px;
              line-height: 20px;
              padding-top: 4px;
              text-align: left;
            }
          }
        }
        .el-tabs {
          :deep(.el-tabs__nav) {
            .el-tabs__item {
              width: 100px !important;
            }
          }
        }
        .el-calendar {
          // border-radius: 4px;
          :deep(.el-calendar__header) {
            padding: 8px 0 12px;
            border-bottom-color: transparent;
          }
          :deep(.el-calendar__body) {
            padding: 0;
            .el-calendar-table thead th {
              padding: 14px 0;
              background: #f5f7fa;
            }
            .el-calendar-table__row {
              td {
                // background: #F5F7FA;
                line-height: 18px;
                // border: 0.5px solid #DCDFE6;
                &:hover {
                  background: $tes-primary2;
                }
                .el-calendar-day:hover {
                  background: transparent;
                }
                .el-calendar-day {
                  padding: 0;
                  height: 88px;
                }
              }
              .is-today {
                span {
                  width: 22px;
                  height: 22px;
                  line-height: 22px;
                  background: $tes-primary; // #3392E2;
                  color: $background-color;
                  border-radius: 11px;
                  display: block;
                }
              }
              .is-selected {
                background: $tes-primary2;
                .calender-item {
                  background: $tes-primary4;
                }
                &:hover {
                  .calender-item {
                    background: $tes-primary4;
                  }
                }
              }
            }
          }
        }
      }
    }
    .right-contant {
      padding-left: 20px;
      .statistics {
        :deep(.el-collapse) {
          border: none;
          border-radius: 4px;
          .el-collapse-item__header {
            padding: 0;
            font-weight: normal;
            border-bottom-color: transparent;
          }
          .el-collapse-item__wrap {
            border: none;
            overflow: auto;
            // max-height: calc(100vh - 285px);
            .el-collapse-item__content {
              padding: 0;
              .agency-list {
                max-height: calc(100vh - 400px);
                width: 100%;
                overflow: auto;
                padding: 10px;
                .no-data {
                  font-size: 16px;
                  color: $tes-font3;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                }
                .content-list {
                  cursor: pointer;
                  border: 1px solid transparent;
                  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
                  padding: 10px;
                  border-radius: 4px;
                  &:hover {
                    border-color: $tes-primary1;
                  }
                  &:not(:last-of-type) {
                    margin-bottom: 10px;
                  }
                  .flag-main {
                    text-align: left;
                    color: $tes-font;
                    padding-bottom: 8px;
                  }
                  .flag-content {
                    display: flex;
                    flex-direction: row;
                    flex-wrap: nowrap;
                    justify-content: space-between;
                    align-items: center;
                    .flag-time {
                      color: #a8abb2;
                    }
                    .flag-tags {
                      display: flex;
                      flex-direction: row;
                      .el-tag {
                        margin-right: 4px;
                        border: none;
                      }
                      .tag-user {
                        display: flex;
                        align-items: center;
                        background: $user-tag-backGroundColor;
                        .icon {
                          color: $tes-font3;
                          padding-right: 4px;
                        }
                        .name {
                          max-width: 64px;
                          color: $user-tag-color;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
        .statistics-list {
          width: 100%;
          padding: 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .left {
            display: flex;
            align-items: center;
            .icon {
              display: flex;
              justify-content: center;
              align-items: center;
              margin-right: 12px;
            }
            .num-bg1 {
              color: $tes-primary;
            }
            .num-bg2 {
              color: $tes-yellow;
            }
            .num-bg3 {
              color: $tes-red;
            }
            .title {
              font-size: 14px;
              color: $tes-font;
            }
          }
          .right {
            display: flex;
            align-items: center;
            .number {
              font-size: 20px;
            }
            .txt {
              padding-left: 10px;
              font-size: 14px;
            }
            .no-data {
              font-size: 16px;
              color: $tes-font3;
            }
          }
        }
        .my-agency-list {
          .content-list {
            cursor: pointer;
            box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
            padding: 10px;
            border-radius: 4px;
            &:not(:last-of-type) {
              margin-bottom: 10px;
            }
            .flag-main {
              color: $tes-font;
              padding-bottom: 8px;
            }
            .flag-content {
              display: flex;
              flex-direction: row;
              flex-wrap: nowrap;
              justify-content: space-between;
              align-items: center;
              .flag-time {
                color: #a8abb2;
              }
              .flag-tags {
                display: flex;
                flex-direction: row;
                .el-tag {
                  margin-right: 4px;
                  border: none;
                }
                .tag-user {
                  display: flex;
                  align-items: center;
                  background: #f4f4f5;
                  .icon {
                    color: $tes-font3;
                    padding-right: 4px;
                  }
                  .name {
                    max-width: 64px;
                    color: $tes-font;
                  }
                }
              }
            }
          }
        }
      }
    }
    .name {
      line-height: 22px;
    }
    .bgc {
      width: 50px;
      height: 50px;
      border-radius: 25px;
      float: left;
      span {
        font-size: 25px;
        line-height: 50px;
      }
    }
    .bg1 {
      color: $tes-blue;
      background: #e6f1fb;
    }
    .bg2 {
      color: $tes-red;
      background: #fff2f0;
    }
    .bg3 {
      color: $tes-primary;
      background: #daf2e9;
    }
    .bg4 {
      color: $tes-yellow;
      background: #fffbf0;
    }
  }
}
.calendar-popover {
  .calendar-content {
    max-height: 360px;
    width: 100%;
    overflow: auto;
    padding: 10px;
    .content-list {
      cursor: pointer;
      box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
      padding: 10px;
      border-radius: 4px;
      &:not(:last-of-type) {
        margin-bottom: 10px;
      }
      .flag-main {
        text-align: left;
        color: $tes-font;
        padding-bottom: 8px;
      }
      .flag-content {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: space-between;
        align-items: center;
        .flag-time {
          color: #a8abb2;
        }
        .flag-tags {
          display: flex;
          flex-direction: row;
          .el-tag {
            margin-right: 4px;
            border: none;
          }
          .tag-user {
            display: flex;
            align-items: center;
            background: $user-tag-backGroundColor;
            .icon {
              color: $tes-font3;
              padding-right: 4px;
            }
            .name {
              max-width: 64px;
              color: $user-tag-color;
            }
          }
        }
      }
    }
  }
}
.moveItem {
  margin-bottom: 15px;
}

.card {
  background: $background-color;
  border-radius: 0.25rem;
  box-shadow: 0 0.125rem 0.25rem #0000000a;
  padding: 0.75rem 1rem;
  .top-agency .title {
    font-size: 16px;
    color: $tes-font;
    line-height: 1.5rem;
    margin-left: -1rem;
    font-weight: 500;
    &::before {
      background: $tes-primary;
      content: '';
      display: inline-block;
      height: 0.9rem;
      margin-bottom: -0.03125rem;
      margin-right: 0.625rem;
      width: 0.25rem;
    }
  }
}
</style>
