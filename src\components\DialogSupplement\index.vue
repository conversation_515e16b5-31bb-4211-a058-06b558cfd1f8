<template>
  <el-dialog
    v-model="dialogVisible"
    title="样品信息补充"
    width="720px"
    :close-on-click-modal="false"
    custom-class="submit_dialog"
    top="10vh"
    @close="closeDialog"
  >
    <div class="formStyle">
      <el-form
        v-loading="dialogLoading"
        :model="formDataInfo"
        size="small"
        label-position="right"
        label-width="80px"
        class="dialog-form form-info"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="样品编号：" prop="secSampleNum">
              <span>{{ formDataInfo.secSampleNum || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="样品名称：" prop="samplesName">
              <span>{{ formDataInfo.samplesName || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="型号规格：" prop="prodType">
              <span>{{ formDataInfo.prodType || '--' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form
        ref="formRef"
        v-loading="dialogLoading"
        :model="formData"
        :rules="rules"
        size="small"
        label-position="top"
      >
        <el-row :gutter="20">
          <el-col v-for="item in tableFieldList" :key="item.id" :span="12">
            <!-- 文本框 -->
            <el-form-item
              v-if="item.fieldType === '0'"
              :prop="item.id"
              :label="`${item.fieldName}`"
              :rules="{ required: item.isRequired === 1, message: `请输入${item.fieldName}`, tigger: 'blur' }"
            >
              <el-input v-model="formData[item.id]" maxlength="300" :placeholder="`请输入${item.fieldName}`" />
            </el-form-item>
            <!-- 数字框 -->
            <el-form-item
              v-if="item.fieldType === '1'"
              :prop="item.id"
              :label="`${item.fieldName}`"
              :rules="{
                required: item.isRequired === 1,
                validator: isNumber,
                isRequire: item.isRequired,
                fieldName: item.fieldName,
                trigger: 'blur'
              }"
            >
              <el-input v-model="formData[item.id]" maxlength="30" :placeholder="`请输入${item.fieldName}`" />
            </el-form-item>
            <!-- 日期框 -->
            <el-form-item
              v-if="item.fieldType === '2'"
              :prop="item.id"
              :label="`${item.fieldName}`"
              :rules="{
                required: item.isRequired === 1,
                validator: isDate,
                isRequire: item.isRequired,
                fieldName: item.fieldName,
                trigger: 'change'
              }"
            >
              <el-date-picker
                v-model="formData[item.id]"
                type="date"
                :placeholder="`请选择${item.fieldName}`"
                style="width: 100%"
              />
            </el-form-item>
            <!-- 字典框 -->
            <el-form-item
              v-if="item.fieldType === '3'"
              :prop="item.id"
              :label="`${item.fieldName}`"
              :rules="{ required: item.isRequired === 1, message: `请选择${item.fieldName}`, tigger: 'blur' }"
            >
              <el-select
                v-model="formData[item.id]"
                filterable
                size="small"
                clearable
                :placeholder="`请选择${item.fieldName}`"
                style="width: 100%"
              >
                <el-option
                  v-for="val in dictionaryCodeArray[item.dictionaryCode]"
                  :key="val.value"
                  :label="val.name"
                  :value="val.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-for="val in richTextList" :key="val.id" :span="24">
            <el-form-item
              :label="`${val.fieldName}：`"
              :prop="val.id"
              :rules="{ required: val.isRequired === 1, message: `请输入${val.fieldName}`, tigger: 'change' }"
            >
              <el-input
                v-model="formData[val.id]"
                maxlength="3000"
                type="textarea"
                :rows="3"
                :placeholder="`请输入${val.fieldName}`"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <div v-if="tableFieldList.length || richTextList.length" class="dialog-footer">
        <el-button :loading="dialogLoading" @click="closeDialog">取 消</el-button>
        <el-button :loading="dialogLoading" type="primary" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确 认</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, getCurrentInstance } from 'vue';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { findUseSampleSupplementaryConfig, saveOrUpdate, findBySampleId } from '@/api/execution';
import { getDictionary } from '@/api/user';
import { formatDate } from '@/utils/formatTime';
export default {
  name: 'DialogMakeSample',
  components: {},
  props: {
    show: {
      type: Boolean,
      default: false
    },
    sampleId: {
      type: String,
      default: ''
    },
    materialCategoryCode: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      formData: {},
      formRef: ref(),
      formDataInfo: {},
      paramsField: {},
      rules: {},
      fieldTypeJSON: {
        0: '文本',
        1: '数字',
        2: '日期',
        3: '字典',
        4: '富文本'
      },
      toolbar:
        'formatselect | forecolor backcolor | fontselect | fontsizeselect | bullist numlist | outdent indent lineheight | undo redo | bold italic underline strikethrough subscript superscript removeformat charmap hr selectall | alignleft aligncenter alignright alignjustify | table link pagebreak | code preview fullscreen',
      tableFieldList: [],
      richTextList: [],
      dictionaryCodeArray: {}, // 用到的字典集合
      dialogLoading: false,
      dialogVisible: false
    });
    watch(props, newValue => {
      state.dialogVisible = newValue.show;
      if (state.dialogVisible) {
        state.formDataInfo = props.data;
        state.tableFieldList = [];
        state.richTextList = [];
        state.paramsField = {
          materialCategoryCode: props.materialCategoryCode,
          configType: 2,
          sampleId: props.sampleId
        };
        state.formData = {};
        getList();
      }
    });
    const isNumber = (rule, val, callback) => {
      if (rule.isRequire === 1 && !val && val !== 0 && val !== '0') {
        // 必填
        callback(new Error(`请输入${rule.fieldName}`));
      } else if (val !== undefined) {
        // 只要有值就判断数字类型
        if (Number(val).toString() === 'NaN' || val?.substring(0, 1) === '.') {
          callback(new Error(`请输入数字`));
        } else if (Number(val) < 0) {
          callback(new Error(`请输入非负数`));
        } else {
          callback();
        }
      } else {
        // 不必填无值可通过校验
        callback();
      }
    };
    const isDate = (rule, val, callback) => {
      if (rule.isRequire === 1 && (val === undefined || val === null)) {
        callback(new Error(`请输入${rule.fieldName}`));
      } else {
        callback();
      }
    };
    const getDetail = () => {
      state.dialogLoading = true;
      findBySampleId(props.sampleId, '2').then(res => {
        state.dialogLoading = false;
        if (res) {
          state.formData = res.data.data.tableDataList[0]?.tableInfo || {};
        }
      });
    };
    const getList = () => {
      findUseSampleSupplementaryConfig(props.materialCategoryCode, '2').then(res => {
        if (res) {
          res.data.data.forEach(item => {
            if (item.fieldType !== '4') {
              state.tableFieldList.push({ ...item, fieldId: item.id });
            } else {
              state.richTextList.push({ ...item, fieldId: item.id });
            }
          });
          const dicArray = [
            ...new Set(
              state.tableFieldList.map(item => {
                return item.dictionaryCode;
              })
            )
          ];
          getDictionaryAll(dicArray);
          getDetail();
        }
      });
    };
    const getDictionaryAll = codes => {
      codes.forEach(item => {
        if (item) {
          getDictionaryCode(item);
        }
      });
    };
    const getContent = (value, fieldName) => {
      state.formData[fieldName] = value;
    };
    const getDictionaryCode = item => {
      getDictionary(item).then(res => {
        if (res) {
          state.dictionaryCodeArray[item] = {};
          res.data.data.dictionaryoption.forEach(val => {
            if (val.status === 1) {
              state.dictionaryCodeArray[item][val.code] = val;
            }
          });
        }
      });
    };
    const onSubmit = () => {
      state.formRef
        .validate()
        .then(valid => {
          if (valid) {
            const params = {
              ...state.paramsField,
              tableDataList: [{ tableInfo: state.formData }],
              tableHeader: [...state.tableFieldList, ...state.richTextList]
            };
            state.dialogLoading = true;
            saveOrUpdate(params).then(res => {
              state.dialogLoading = false;
              if (res) {
                proxy.$message.success('保存成功！');
                closeDialog();
              }
            });
          } else {
            return false;
          }
        })
        .catch(() => {});
    };
    const closeDialog = () => {
      state.dialogVisible = false;
      context.emit('closeDialog', false);
    };
    // 删除行数据
    const handleDelete = index => {
      state.formData.tableData.splice(index, 1);
    };

    return {
      ...toRefs(state),
      drageHeader,
      getContent,
      isNumber,
      isDate,
      formatDate,
      colWidth,
      handleDelete,
      closeDialog,
      onSubmit
    };
  }
};
</script>

<style lang="scss" scoped>
.btn-group {
  text-align: right;
  margin-bottom: 10px;
}
.dialog-form .el-form-item {
  margin-bottom: 0;
}
.form-info {
  background-color: #f0f2f5;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
}
.formStyle {
  max-height: 650px;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
