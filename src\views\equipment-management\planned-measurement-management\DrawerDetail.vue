<!-- 查看计量计划 -->
<template>
  <el-drawer
    v-model="drawerShow"
    title="查看计量计划"
    direction="rtl"
    :before-close="handleClose"
    size="88%"
    destroy-on-close
    custom-class="page-drawer"
  >
    <DrawerLayout
      v-loading="detailLoading"
      :has-left-panel="false"
      :main-offset-top="53"
      :has-button-group="getPermissionBtn('editEquipment')"
    >
      <template #drawer-title>
        <span>计划信息</span>
        <el-tag size="mini" :type="planType[detailData.status]">{{ planJson[detailData.status] }}</el-tag>
      </template>
      <template #button-group>
        <div v-if="!isFinish">
          <el-button
            size="small"
            type="primary"
            icon="iconfont tes-task-issued"
            @click="downLoadPlan"
            @keyup.prevent
            @keydown.enter.prevent
          >
            导出计划</el-button
          >
          <el-button
            v-if="detailData.status === 'Uncommitted' && getPermissionBtn('deleteMeasurePlan')"
            size="small"
            type="danger"
            icon="iconfont tes-delete"
            @click="deleteDetail"
            @keyup.prevent
            @keydown.enter.prevent
          >
            删除</el-button
          >
          <el-button
            v-if="detailData.status === 'Uncommitted' && getPermissionBtn('editMeasurePlan')"
            size="small"
            type="primary"
            icon="el-icon-edit"
            @click="editDetail"
            @keyup.prevent
            @keydown.enter.prevent
          >
            编辑计划</el-button
          >
          <el-button
            v-if="detailData.status === 'Uncommitted' && getPermissionBtn('submitMeasurePlan')"
            size="small"
            icon="el-icon-upload2"
            type="warning"
            @click="submitDetail"
            @keyup.prevent
            @keydown.enter.prevent
            >提交计划</el-button
          >
        </div>
      </template>
      <el-form class="isCheck">
        <el-row>
          <el-col :span="9">
            <el-form-item label="计划编号：" prop="planNo">
              <span>{{ detailData.planNo }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="创建人：" prop="createBy">
              <UserTag :name="getNameByid(detailData.createBy) || '--'" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建日期：" prop="createDateTime">
              <span>{{ detailData.createTime ? formatDate(detailData.createTime) : '--' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="9">
            <el-form-item label="计划日期：" prop="arrivalDate">
              <span>{{ detailData.startDate ? formatDate(detailData.startDate) : '--' }}</span>
              ~
              <span>{{ detailData.endDate ? formatDate(detailData.endDate) : '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="计量单位：" prop="org">
              <div>{{ detailData.org || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="负责人：" prop="responsibleBy">
              <UserTag :name="getNameByid(detailData.responsibleBy) || '--'" />
              <!-- <div>{{ getNameByid(detailData.responsibleBy) || '--' }}</div> -->
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs v-model="activeName" class="marginTop">
        <el-tab-pane label="设备信息" name="1" style="text-align: left">
          <div class="textRight">
            <el-button
              v-if="detailData.status === 'Uncommitted' && getPermissionBtn('savePlanEquipment')"
              class="add-btn"
              size="small"
              icon="el-icon-plus"
              @click="handleAddEquipment"
              @keyup.prevent
              @keydown.enter.prevent
              >添加设备</el-button
            >
            <el-button
              v-if="detailData.status === 'Unfinished' && getPermissionBtn('savePlanEquipment')"
              class="add-btn"
              size="small"
              icon="el-icon-plus"
              @click="handleBatch"
              @keyup.prevent
              @keydown.enter.prevent
              >批量计量</el-button
            >
          </div>
          <el-table
            :data="detailData.deviceList"
            class="dark-table base-table format-height-table2"
            fit
            border
            highlight-current-row
            height="auto"
            size="medium"
            @selection-change="handleSelectionChange"
            @header-dragend="drageHeader"
          >
            <el-table-column
              v-if="isFinish && getPermissionBtn('savePlanEquipment')"
              type="selection"
              prop="checkbox"
              :width="colWidth.checkbox"
              align="center"
              :selectable="handleSelectable"
            />
            <el-table-column
              label="仪器设备编号"
              prop="deviceNumber"
              :min-width="colWidth.name"
              align="left"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <div>{{ row.deviceNumber }}</div>
              </template>
            </el-table-column>
            <el-table-column label="仪器设备名称" prop="startDate" :min-width="colWidth.name" show-overflow-tooltip>
              <template #default="{ row }">
                <div>{{ row.name }}</div>
              </template>
            </el-table-column>
            <el-table-column label="型号规格" prop="model" :width="colWidth.name" align="left" show-overflow-tooltip>
              <template #default="{ row }">
                <div>{{ row.model }}</div>
              </template>
            </el-table-column>
            <el-table-column
              label="测量范围"
              prop="measurementRange"
              align="left"
              :width="colWidth.name"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <div>{{ row.measurementRange || '--' }}</div>
              </template>
            </el-table-column>
            <el-table-column label="计量周期" prop="measurementCycle" :width="colWidth.unit">
              <template #default="{ row }">
                <div>
                  <span v-if="row.measurementCycle || row.measurementCycle === 0">{{ row.measurementCycle }}</span>
                  <span v-else>--</span>
                  {{ row.measurementCycleUnitType ? '月' : '年' }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-if="isFinish || isFinished"
              label="计量结果"
              prop="measurementResult"
              :width="colWidth.measurementResult"
            >
              <template #header>
                <span class="required">计量结果</span>
              </template>
              <template #default="{ row }">
                <div v-if="isFinished || !getPermissionBtn('savePlanEquipment') || row.finishStatus === 3">
                  {{ row.measurementResult || '--' }}
                </div>
                <el-select v-else v-model="row.measurementResult" filterable clearable placeholder="请选择计量结果">
                  <el-option :value="'合格'">合格</el-option>
                  <el-option :value="'矫正后可用'">矫正后可用</el-option>
                  <el-option :value="'不合格'">不合格</el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column v-if="isFinish || isFinished" label="计量日期" prop="measurementDate" width="200">
              <template #header>
                <span class="required">计量日期</span>
              </template>
              <template #default="{ row, $index }">
                <div v-if="isFinished || !getPermissionBtn('savePlanEquipment') || row.finishStatus === 3">
                  {{ formatDate(row.measurementDate) || '--' }}
                </div>
                <el-date-picker
                  v-else
                  v-model="row.measurementDate"
                  type="date"
                  size="small"
                  :clearable="true"
                  placeholder="请选择日期"
                  @change="val => editDate(val, $index)"
                />
              </template>
            </el-table-column>
            <el-table-column v-if="isFinish || isFinished" label="计量有效期" prop="submitTime" width="250">
              <template #header>
                <span class="required">计量有效期</span>
              </template>
              <template #default="{ row }">
                <div v-if="isFinished || !getPermissionBtn('savePlanEquipment') || row.finishStatus === 3">
                  {{ (formatDate(row.validBeginDate) || '--') + '~' + (formatDate(row.validEndDate) || '--') }}
                </div>
                <el-date-picker
                  v-else
                  v-model="row.submitTime"
                  type="daterange"
                  range-separator="至"
                  :clearable="true"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  size="small"
                />
              </template>
            </el-table-column>
            <el-table-column v-if="isFinish || isFinished" label="不确定度" prop="measurementDeviation" width="200">
              <template #default="{ row }">
                <div v-if="isFinished || !getPermissionBtn('savePlanEquipment') || row.finishStatus === 3">
                  {{ row.measurementDeviation || '--' }}
                </div>
                <el-input
                  v-else
                  v-model="row.measurementDeviation"
                  type="textarea"
                  maxlength="300"
                  :rows="1"
                  placeholder="请输入不确定度"
                  size="small"
                />
              </template>
            </el-table-column>
            <el-table-column v-if="isFinish || isFinished" label="性能指标" prop="performanceIndex" width="200">
              <template #default="{ row }">
                <div v-if="isFinished || !getPermissionBtn('savePlanEquipment') || row.finishStatus === 3">
                  {{ row.performanceIndex || '--' }}
                </div>
                <el-input
                  v-else
                  v-model="row.performanceIndex"
                  type="textarea"
                  maxlength="300"
                  :rows="1"
                  placeholder="请输入性能指标"
                  size="small"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-if="isFinish || isFinished"
              label="附件"
              prop="measurementCycle"
              :width="colWidth.fileName"
              align="center"
              show-overflow-tooltip
            >
              <template #default="{ row, $index }">
                <ul
                  v-if="
                    (isFinished || !getPermissionBtn('savePlanEquipment') || row.finishStatus === 3) &&
                    row.fileList.length > 0
                  "
                >
                  <li v-for="item in row.fileList" :key="item.fileId" class="blur-color">
                    <span class="blue-color fileListName" @click="handlePreview(item)">{{ item.fileName }}</span>
                  </li>
                </ul>
                <span v-else-if="(isFinished || !getPermissionBtn('savePlanEquipment')) && row.fileList.length === 0"
                  >--</span
                >
                <el-upload
                  v-else
                  :action="imageAction"
                  :headers="headerconfig"
                  :on-preview="handlePreview"
                  :on-remove="
                    (uploadFile, uploadFiles) => {
                      handleRemove(uploadFile, uploadFiles, $index);
                    }
                  "
                  :file-list="row.fileList"
                  :on-success="
                    (res, file, fileList) => {
                      uploadSuccess(res, file, fileList, $index);
                    }
                  "
                >
                  <span class="blue-color" icon="el-icon-upload2">上传</span>
                  <!-- <el-button type="text" icon="el-icon-upload2" /> -->
                </el-upload>
              </template>
            </el-table-column>
            <el-table-column
              v-if="
                (detailData.status === 'Uncommitted' && getPermissionBtn('deletePlanEquipment')) ||
                (isFinish &&
                  (getPermissionBtn('startUsingPlanEquipment') || getPermissionBtn('cancellationPlanEquipment')))
              "
              label="操作"
              :width="colWidth.operationSingle"
              fixed="right"
            >
              <template #default="{ row, $index }">
                <span v-if="detailData.status === 'Uncommitted'" class="blue-color" @click="deleteRow(row)">删除</span>
                <span
                  v-if="isFinish && row.finishStatus === 3"
                  class="blue-color"
                  @click="handleStartUsing(row, $index)"
                  >还原</span
                >
                <span
                  v-if="isFinish && row.finishStatus === 1"
                  class="blue-color"
                  style="margin-right: 0"
                  @click="handleCancellation(row, $index)"
                  >作废</span
                >
              </template>
            </el-table-column>
          </el-table>
          <span v-if="isFinish && getPermissionBtn('savePlanEquipment')" class="footerBtn">
            <el-button :loading="detailLoading" @click="handleClose">取 消</el-button>
            <el-button :loading="detailLoading" type="primary" @click="saveInfo" @keyup.prevent @keydown.enter.prevent
              >保存</el-button
            >
            <el-button
              :loading="detailLoading"
              size="small"
              icon="el-icon-upload2"
              type="warning"
              @click="finishDetail"
              @keyup.prevent
              @keydown.enter.prevent
              >保存并完成</el-button
            >
          </span>
        </el-tab-pane>
      </el-tabs>
      <el-dialog
        v-model="dialogBatch"
        title="批量计量"
        :close-on-click-modal="false"
        width="480px"
        @close="dialogBatch = false"
      >
        <el-form
          v-if="dialogBatch"
          ref="ruleForm"
          :model="formBatch"
          label-position="right"
          label-width="120px"
          size="small"
        >
          <el-form-item
            label="计量结果："
            prop="measurementResult"
            :rules="{ required: true, message: '请选择仪器设备', trigger: 'change' }"
          >
            <el-select v-model="formBatch.measurementResult" filterable placeholder="请选择计量结果">
              <el-option :value="'合格'">合格</el-option>
              <el-option :value="'矫正后可用'">矫正后可用</el-option>
              <el-option :value="'不合格'">不合格</el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="计量日期："
            prop="measurementDate"
            :rules="{ required: true, message: '请输入计量日期', trigger: 'change' }"
          >
            <el-date-picker
              v-model="formBatch.measurementDate"
              type="date"
              :clearable="false"
              size="small"
              placeholder="请选择日期"
            />
          </el-form-item>
          <el-form-item label="不确定度：" prop="measurementDeviation">
            <el-input
              v-model="formBatch.measurementDeviation"
              maxlength="300"
              type="textarea"
              placeholder="请输入不确定度"
            />
          </el-form-item>
          <el-form-item label="性能指标：" prop="performanceIndex">
            <el-input
              v-model="formBatch.performanceIndex"
              maxlength="300"
              type="textarea"
              placeholder="请输入性能指标"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogBatch = false">取 消</el-button>
            <el-button type="primary" @click="onSaveBatch" @keyup.prevent @keydown.enter.prevent>确 认</el-button>
          </span>
        </template>
      </el-dialog>
      <DialogPlan
        :dialog-visible="dialogVisible"
        :detail-data="detailData"
        :dialog-plan-type="dialogPlanType"
        :device-id-list="deviceIdList"
        @closeDialog="closeDialog"
      />
      <DialogEquipment :dialog-visible="dialogEquipment" :plan-id="planId" @closeDialog="closeEquipment" />
    </DrawerLayout>
  </el-drawer>
</template>
<script>
import { watch, reactive, toRefs, getCurrentInstance, ref } from 'vue';
import { getLoginInfo, getToken } from '@/utils/auth';
import {
  getDetail,
  deletePlan,
  deleteDevice,
  submitPlan,
  saveDeviceInfo,
  finishPlan,
  downLoadFile,
  cancellationApi,
  startUsingApi
} from '@/api/PlannedMeasurementManagement';
import { deviceMeasurementUploadUrl } from '@/api/uploadAction';
import DrawerLayout from '@/components/DrawerLayout';
import DialogEquipment from './DialogEquipment.vue';
import DialogPlan from './DialogPlan.vue';
// import { parseTime } from '@/utils'
import { getPermissionBtn } from '@/utils/common';
import { getNameByid } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { formatDate, addMonth } from '@/utils/formatTime';
import { colWidth } from '@/data/tableStyle';
import UserTag from '@/components/UserTag';
export default {
  name: 'PlanDetail',
  components: { DrawerLayout, UserTag, DialogEquipment, DialogPlan },
  props: {
    showDrawer: {
      type: Boolean,
      default: false
    },
    planId: {
      type: String,
      default: ''
    },
    planJson: {
      type: Object,
      default: function () {
        return {};
      }
    },
    rowType: {
      type: String,
      default: ''
    }
  },
  emits: ['closeDrawer'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      drawerShow: false,
      imageAction: '',
      dialogEquipment: false,
      rowType: '', // 状态
      dialogBatch: false, // 批量计量弹出窗
      dialogVisible: false,
      isFinish: false, // 是否是确认完成页面
      isFinished: false, // 是否已经完成过
      formBatch: {}, // 批量计量表单
      deviceIds: [], // 批量计量的表格
      tableMultiple: [],
      oldDeviceList: [],
      activeName: '1',
      tenantGroupList: JSON.parse(localStorage.getItem('tenantGroup')),
      tenantId: getLoginInfo().tenantId,
      ruleForm: ref(),
      planId: '', // 计量计划id
      dialogPlanType: '', // 计量计划弹出窗类型
      deviceIdList: [],
      detailLoading: false,
      detailData: {
        deviceList: []
      },
      headerconfig: {
        Authorization: getToken()
      },
      planJson: {}, // 计量计划状态
      isRefresh: true,
      accountId: getLoginInfo().accountId, // 当前登录人的id
      listLoading: false,
      drawerTitle: {
        check: '查看计量计划',
        submit: '提交计量计划'
      },
      planType: {
        // 计量计划状态
        Finished: 'success',
        Unfinished: 'warning',
        Uncommitted: 'info'
      },
      tableSize: 'medium',
      title: ''
    });
    const handleClose = () => {
      context.emit('closeDrawer', { isRefresh: true });
    };
    watch(props, newValue => {
      state.drawerShow = props.showDrawer;
      if (state.drawerShow) {
        state.planId = props.planId;
        state.planJson = props.planJson;
        state.rowType = props.rowType;
        state.imageAction = deviceMeasurementUploadUrl();
        state.deviceIds = [];
        if (state.rowType === 'Unfinished') {
          // 未完成状态
          state.isFinish = true;
          state.isFinished = false;
        } else if (state.rowType === 'Finished') {
          // 已完成状态
          state.isFinished = true;
          state.isFinish = false;
        } else {
          // 待提交状态
          state.isFinished = false;
          state.isFinish = false;
        }
        initDetail();
      }
    });

    // 查询详情
    const initDetail = () => {
      state.detailLoading = true;
      getDetail(state.planId).then(res => {
        state.detailLoading = false;
        if (res) {
          state.detailData = res.data.data;
          state.detailData.deviceList.forEach((item, index) => {
            item.index = index + 1;
            if (state.isFinish) {
              if (item.validBeginDate && item.validEndDate) {
                item.submitTime = [item.validBeginDate, item.validEndDate];
              }
              item.fileList.forEach(val => {
                val.name = val.fileName;
              });
            }
          });
          state.oldDeviceList = JSON.parse(JSON.stringify(state.detailData.deviceList));
        }
      });
    };
    // 添加设备
    const handleAddEquipment = () => {
      state.dialogEquipment = true;
    };
    // 批量计量
    const handleBatch = () => {
      if (state.deviceIds.length === 0) {
        proxy.$message.error('请先选择需要计量的仪器设备');
      } else {
        state.dialogBatch = true;
        state.formBatch = {
          measurementResult: '合格',
          measurementDate: new Date()
        };
      }
    };
    const handleSelectionChange = val => {
      state.tableMultiple = val;
      state.deviceIds = val.map(item => {
        return item.id;
      });
    };
    // 保存批量计量
    const onSaveBatch = () => {
      state.detailData.deviceList.forEach((item, index) => {
        if (
          state.deviceIds.some(val => {
            return item.id === val;
          })
        ) {
          item.measurementResult = state.formBatch.measurementResult;
          item.measurementDate = state.formBatch.measurementDate;
          item.measurementDeviation = state.formBatch.measurementDeviation;
          item.performanceIndex = state.formBatch.performanceIndex;
          editDate(item.measurementDate, index);
        }
      });
      state.dialogBatch = false;
    };
    // 保存设备信息
    const saveInfo = () => {
      const submitTable = state.detailData.deviceList.filter(item => {
        return item.finishStatus === 1;
      });
      saveDeviceInfo({ planId: state.planId, deviceList: submitTable }).then(res => {
        if (res) {
          proxy.$message.success(res.data.message);
          initDetail();
        }
      });
    };
    const closeDialog = value => {
      state.dialogVisible = false;
      if (value.isRefresh) {
        initDetail();
      }
    };
    const closeEquipment = val => {
      state.dialogEquipment = false;
      if (val.isRefresh) {
        initDetail();
      }
    };
    const tabsClick = () => {};
    const editDetail = () => {
      state.dialogVisible = true;
      state.dialogPlanType = 'edit';
      state.deviceIdList = [state.planId];
    };
    // 提交计划
    const submitDetail = () => {
      if (state.detailData.startDate && new Date(state.detailData.startDate).getTime() < new Date().getTime()) {
        proxy.$message.warning('已过计划开始日期，请重新修改计划开始日期');
        editDetail();
        return false;
      }
      if (state.detailData.deviceList.length === 0) {
        proxy
          .$confirm('提交前请先添加设备', '提交确认', {
            confirmButtonText: '添加设备',
            cancelButtonText: '取消',
            showCancelButton: true,
            closeOnClickModal: false,
            type: 'warning'
          })
          .then(() => {
            state.dialogEquipment = true;
          })
          .catch(() => {});
      } else if (!state.detailData.startDate || !state.detailData.org) {
        let messageTitle;
        if (state.detailData.startDate && !state.detailData.org) {
          messageTitle = '提交前请先填写计量单位';
        } else if (!state.detailData.startDate && state.detailData.org) {
          messageTitle = '提交前请先填写计划日期';
        } else if (!state.detailData.startDate && !state.detailData.org) {
          messageTitle = '提交前请先输入计划日期和计量单位';
        }
        proxy
          .$confirm(messageTitle, '提交确认', {
            confirmButtonText: '填写',
            cancelButtonText: '取消',
            showCancelButton: true,
            closeOnClickModal: false,
            type: 'warning'
          })
          .then(() => {
            state.dialogVisible = true;
            state.deviceIdList = [state.detailData.id];
            state.dialogPlanType = 'edit';
          })
          .catch(() => {});
      } else {
        proxy
          .$confirm('是否确认提交该计划？提交后不可编辑', '提交确认', {
            confirmButtonText: '确认提交',
            cancelButtonText: '取消',
            showCancelButton: true,
            closeOnClickModal: false,
            type: 'warning'
          })
          .then(() => {
            submitPlan(state.detailData.id).then(res => {
              proxy.$message.success(res.data.message);
              initDetail();
              state.isFinish = true;
            });
          })
          .catch(() => {});
      }
    };
    // 确认完成计划
    const finishDetail = () => {
      const submitTable = state.detailData.deviceList.filter(item => {
        return item.finishStatus === 1;
      });
      if (
        submitTable.some(item => {
          return !item.measurementResult;
        })
      ) {
        proxy.$message.error('计量结果不能为空，请先选择计量日期');
        return false;
      }
      if (
        submitTable.some(item => {
          return !item.measurementDate;
        })
      ) {
        proxy.$message.error('计量日期不能为空，请先选择计量日期');
        return false;
      }
      proxy
        .$confirm('是否确认完成该计划？确认后不可编辑', '完成确认', {
          confirmButtonText: '确认完成',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'warning'
        })
        .then(() => {
          saveDeviceInfo({ planId: state.planId, deviceList: submitTable }).then(res => {
            if (res) {
              finishPlan(state.planId).then(function (res) {
                if (res) {
                  proxy.$message.success(res.data.message);
                  state.isFinished = true;
                  state.isFinish = false;
                  initDetail();
                }
              });
            }
          });
        })
        .catch(() => {});
    };
    // 导出计划
    const downLoadPlan = () => {
      export2Excel();
    };
    // 修改计量日期
    const editDate = (date, index) => {
      if (date) {
        state.detailData.deviceList[index].validBeginDate = formatDate(date);
        if (state.detailData.deviceList[index].measurementCycleUnitType === 1) {
          if (state.detailData.deviceList[index].measurementCycle) {
            const newDate = new Date(addMonth(formatDate(date), state.detailData.deviceList[index].measurementCycle));
            state.detailData.deviceList[index].validEndDate = formatDate(
              new Date(newDate.getTime() - 3600 * 1000 * 24 * 1)
            );
          } else {
            state.detailData.deviceList[index].validEndDate = formatDate(date);
          }
        } else {
          state.detailData.deviceList[index].validEndDate = formatDate(
            new Date(date.getTime() + 3600 * 1000 * 24 * 365 * state.detailData.deviceList[index].measurementCycle)
          );
        }
        state.detailData.deviceList[index].submitTime = [
          state.detailData.deviceList[index].validBeginDate,
          state.detailData.deviceList[index].validEndDate
        ];
        state.detailData.deviceList[index].measurementDate = formatDate(date);
      }
    };
    const export2Excel = () => {
      var tHeader = [];
      var tHeaders = [];
      var filterVal = [];
      var fileName = '模板';
      state.detailLoading = true;
      fileName = '设备计量计划清单';
      const startDate = state.detailData.startDate ? formatDate(state.detailData.startDate) + '~' : '';
      const endDate = state.detailData.endDate ? formatDate(state.detailData.endDate) : '';
      const jlrq = startDate + endDate;
      const clientFullName = state.tenantGroupList.filter(item => item.tenantId === state.tenantId)[0].clientFullName;
      tHeaders = [
        ['计量单位:', state.detailData.org, '计划日期:', jlrq, '', '     ', '', '     '],
        ['送检单位:', clientFullName, '联系人:', getNameByid(state.detailData.responsibleBy), '', '     ', '', '     '],
        ['']
      ];
      if (state.detailData.status === 'Uncommitted') {
        // 待提交
        tHeader = ['序号', '仪器设备编号', '仪器设备名称', '型号规格', '测量范围', '计量周期'];
        filterVal = ['index', 'deviceNumber', 'name', 'model', 'measurementRange', 'measurementCycle'];
        tHeaders = [
          ['计量单位:', state.detailData.org, '计划日期:', jlrq, '', '     '],
          ['送检单位:', clientFullName, '联系人:', getNameByid(state.detailData.responsibleBy), '', '     '],
          ['']
        ];
      } else {
        tHeader = [
          '序号',
          '仪器设备编号',
          '仪器设备名称',
          '型号规格',
          '测量范围',
          '计量周期',
          '计量结果',
          '计量日期',
          '计量有效期',
          '不确定度',
          '性能指标'
        ];
        filterVal = [
          'index',
          'deviceNumber',
          'name',
          'model',
          'measurementRange',
          'measurementCycle',
          'measurementResult',
          'measurementDate',
          'validDate',
          'measurementDeviation',
          'performanceIndex'
        ];
        tHeaders = [
          ['计量单位:', state.detailData.org, '计划日期:', jlrq, '', '     ', '', '     ', '', '     ', '', '     '],
          [
            '送检单位:',
            clientFullName,
            '联系人:',
            getNameByid(state.detailData.responsibleBy),
            '',
            '     ',
            '',
            '     ',
            '',
            '     ',
            '',
            '     '
          ],
          ['']
        ];
      }
      import('@/utils/Export2Excel').then(excel => {
        const data = formatJson(filterVal, state.detailData.deviceList);
        excel.export_json_to_excel({
          multiHeader: tHeaders,
          header: tHeader,
          data,
          filename: fileName,
          autoWidth: true,
          bookType: 'xlsx'
        });
        state.detailLoading = false;
      });
    };
    const formatJson = (filterVal, jsonData) => {
      return jsonData.map((v, index) =>
        filterVal.map(j => {
          if (j === 'measurementDate') {
            return formatDate(v[j]);
          } else if (j === 'measurementCycle') {
            return v[j] + (v['measurementCycleUnitType'] ? '月' : '年');
          } else if (j === 'validDate') {
            return (formatDate(v['validBeginDate']) || '--') + '~' + (formatDate(v['validEndDate']) || '--');
          } else if (j === 'index') {
            return index + 1;
          } else {
            return v[j];
          }
        })
      );
    };
    // 删除计量计划
    const deleteDetail = () => {
      proxy
        .$confirm('是否删除该计划？', '删除确认', {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'warning'
        })
        .then(() => {
          deletePlan(state.detailData.id).then(function (res) {
            if (res) {
              proxy.$message.success(res.data.message);
              context.emit('closeDrawer', { isRefresh: true });
            }
          });
        })
        .catch(() => {});
    };
    // 删除设备
    const deleteRow = row => {
      deleteDevice([row.id]).then(function (res) {
        if (res) {
          proxy.$message.success(res.data.message);
          initDetail();
        }
      });
    };
    // 点击已上传的附件列表
    const handlePreview = file => {
      const id = file.id;
      downLoadFile(id).then(res => {
        if (res.data.code === 200) {
          window.open(res.data.data.url);
        }
      });
    };
    // 移除文件列表已上传的文件
    const handleRemove = (uploadFile, uploadFiles, index) => {
      state.detailData.deviceList[index].fileList = uploadFiles;
    };
    // 文件上传成功的钩子函数
    const uploadSuccess = (res, file, fileList, index) => {
      if (res.code === 200) {
        state.detailData.deviceList[index].fileList.push({
          name: res.data.fileName,
          id: res.data.fileId,
          ...res.data
        });
      } else {
        proxy.$message.error(res.message);
      }
    };
    // 作废
    const handleCancellation = (row, index) => {
      proxy
        .$confirm('是否作废该设备信息？', '作废确认', {
          confirmButtonText: '确认作废',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'warning'
        })
        .then(() => {
          cancellationApi(row.id).then(function (res) {
            if (res) {
              proxy.$message.success(res.data.message);
              state.detailData.deviceList[index] = state.oldDeviceList[index];
              state.detailData.deviceList[index].finishStatus = 3;
              // initDetail()
            }
          });
        })
        .catch(() => {});
    };
    // 还原
    const handleStartUsing = (row, index) => {
      startUsingApi(row.id).then(function (res) {
        if (res) {
          proxy.$message.success(res.data.message);
          state.detailData.deviceList[index].finishStatus = 1;
          // initDetail()
        }
      });
    };
    // 判断表格的多选框是否禁用
    const handleSelectable = row => {
      if (row.finishStatus !== 3) {
        return true;
      } else {
        return false;
      }
    };
    return {
      ...toRefs(state),
      drageHeader,
      handleSelectable,
      handleCancellation,
      handleStartUsing,
      handleSelectionChange,
      saveInfo,
      formatJson,
      export2Excel,
      editDate,
      handlePreview,
      uploadSuccess,
      handleRemove,
      colWidth,
      submitDetail,
      finishDetail,
      downLoadPlan,
      deleteDetail,
      deleteRow,
      closeDialog,
      closeEquipment,
      handleAddEquipment,
      onSaveBatch,
      handleBatch,
      initDetail,
      getPermissionBtn,
      tabsClick,
      editDetail,
      getNameByid,
      formatDate,
      addMonth,
      handleClose
    };
  }
};
</script>

<style lang="scss" scoped>
li {
  list-style: none;
}
.fileListName {
  cursor: pointer;
}
.required::before {
  content: '*';
  color: $red;
  position: absolute;
  left: 0;
  top: 0;
}
.isCheck {
  margin-top: 12px;
  padding: 10px 20px;
  background: #f5f7fa;
  border-radius: 3px;
}
:deep(.el-range-editor--small.el-input__inner) {
  width: 100%;
}
:deep(.el-select) {
  width: 100%;
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 100%;
}
:deep(.el-upload-list__item.is-success.focusing .el-icon-close-tip) {
  display: none !important;
}
.add-btn {
  margin-bottom: 20px;
}
.footerBtn {
  position: fixed;
  bottom: 20px;
  right: 40px;
}
::v-deep(.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 34rem) !important;
    overflow-y: auto;
  }
}
.marginTop {
  margin-top: 35px;
}
</style>
