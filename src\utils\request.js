import axios from 'axios';
import { ElMessage } from 'element-plus';
import store from '@/store';
import { initToken, ClientId, getToken } from '@/utils/auth';
import { getAPIURL } from '@/utils/base-url';
import { throttle } from 'lodash';

// import qs from 'qs'
// import { ElLoading } from 'element-plus'

axios.defaults.headers.get['Content-Type'] = 'application/x-www-form-urlencoded';
axios.defaults.headers.post['Content-Type'] = 'application/json; charset=utf-8';
axios.defaults.withCredentials = false;
/* axios.defaults.transformRequest = [function(data) {
  let ret = ''
  for (const it in data) {
    ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
  }
  return ret
}] */
// create an axios instance
const service = axios.create({
  baseURL: getAPIURL(),
  timeout: 50000 // request timeout
});

// let loadding
// const startLoading = () => {
//   const options = {
//     lock: true,
//     text: '加载中。。。',
//     background: 'rgba(0,0,0,0.7)'
//   }
//   loadding = ElLoading.service(options)
// }
// const endLoading = () => {
//   loadding.close()
// }

const throttleMessage = throttle(({ type = 'error', message, duration = 3 * 1000 }) => {
  ElMessage({
    message,
    type,
    duration
  });
}, 300);

// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent
    // if (config.method === 'post') {
    //   config.headers['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8'
    // } else {
    //   config.headers['Content-Type'] = 'application/json; charset=utf-8'
    // }
    // config.data = qs.stringify(config.data)
    // if (store.getters.token !== initToken) {
    if (store.getters.token !== initToken) {
      const token = getToken();
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      // 如果 Vuex 中的 token 和 cookie 中的 token 不相等，则表示浏览器内账号已切换。
      if (store.getters.token !== token && store.getters.token && token) {
        return Promise.reject({ type: 'warning', message: '浏览器内账号已切换，请刷新浏览器后重新操作' });
      }
      config.headers['Authorization'] = getToken();
    } else {
      config.headers['Authorization'] = initToken;
    }
    config.headers['Client-Id'] = ClientId;
    // console.log(config)
    return config;
  },
  error => {
    // do something with request error
    return Promise.reject(error);
  }
);

// response interceptor
service.interceptors.response.use(
  response => {
    const res = response;
    // 登录信息过期
    if (res.data.resp_code === '503' || res.data.code === '403' || res.data.code === 403) {
      store.dispatch('user/resetToken');
      ElMessage({
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
      return false;
    }
    // console.log(res)
    /**
     *
     * lims接口返回的code值有的接口有，有的接口没有，这边需要根据具体情况判断，目前为止code为200001是数据正常标识，不排除还存在其他值情况，
     * 后面要是遇到可以再添加;
     *
     * TES接口code值为200为数据正常状态
     *
     */
    if (res.status && res.status === 200) {
      if (res.data.code && res.data.code !== 200 && res.data.code !== 200001) {
        // 我们自己的接口返回
        ElMessage({
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        return false;
      } else {
        // lims的接口返回
        return res;
      }
    }
    return res;
  },
  error => {
    console.log('error', error);
    if (error?.response?.data?.httpStatus === 401 || error?.response?.status === 401) {
      store.dispatch('user/resetToken');
      ElMessage.error('暂无权限，请重新登录');
    } else {
      throttleMessage({
        message: error.message,
        type: error.type || 'error',
        duration: error.duration || 3 * 1000
      });
    }
    return false; // Promise.reject(error)
  }
);

export default service;
