<template>
  <DetailLayout :main-offset-top="105" has-full-main-height>
    <template #page-header>
      <div class="header-flex flex-start">
        <el-space :size="40" :spacer="spacer">
          <div class="item-column">
            <span class="item-label">样品编号</span>
            <div class="item-content">{{ secSampleNum }}</div>
          </div>
          <div class="item-column">
            <span class="item-label">委外编号</span>
            <div class="item-content">{{ experimentEntrustCode }}</div>
          </div>
          <div class="item-column">
            <span class="item-label">委外报告</span>
            <div class="item-content">
              <template v-if="attachmentUrl">
                <div class="file-item">
                  <a :href="attachmentUrl" target="_blank" class="nowrap blue-color file-name">{{ attachmentName }}</a>
                  <span v-if="isEditMode" class="el-icon-close" @click="handleDeleteFile" />
                </div>
              </template>
              <template v-else-if="isEditMode">
                <div class="file-item">
                  <el-button icon="el-icon-upload2" round size="mini" @click="handleUpload"> 上传文件 </el-button>
                  <input ref="fileRef" type="file" hidden @change="onFileChange" />
                </div>
              </template>
              <span v-else class="file-name">无</span>
            </div>
          </div>
        </el-space>
        <div>
          <el-button size="small" @click="handleGoBack">返回</el-button>
          <el-button v-if="isEditMode" type="primary" size="small" icon="el-icon-receiving" @click="handleSave">
            保存草稿
          </el-button>
          <el-button v-if="isEditMode" type="primary" size="small" icon="el-icon-circle-check" @click="handleSubmit">
            保存并提交
          </el-button>
        </div>
      </div>
    </template>
    <div class="container">
      <div v-if="tableData.length > 0" class="wrapper">
        <el-aside v-show="showAside" :style="{ width: asideWidth + 'px' }">
          <p class="title">检测项目</p>
          <div :key="asideKey" class="left-main">
            <div
              v-for="(item, index) in tableData"
              :key="item.id"
              class="tree-list nowrap"
              :title="item.sourceName"
              @click="scrollJump(index)"
            >
              <span
                :class="{
                  line: true,
                  'is-current': currentIndex === index,
                  'red-border': item.expResult === results.find(item => item.code === '1')?.name,
                  'green-border': item.expResult === results.find(item => item.code === '0')?.name,
                  'yellow-border': item.expResult === ''
                }"
              />
              <!-- <span class="line green-border" /> -->
              <span class="sortingIcon tes-move iconfont" />
              <!-- <span v-if="item.retestSourceId" class="custom-icon text-copy">复</span>
              <span v-if="item.isRetest===1" class="custom-icon text-origin">源</span> -->
              <span class="text">{{ item.sourceName }}</span>
            </div>
          </div>
        </el-aside>
        <el-tooltip effect="dark" :content="collapseTip" :hide-after="0" placement="right">
          <DragHandle
            :class="{ 'close-handle': !showAside }"
            @widthChange="widthChange"
            @mouseup="collapseLeft"
            @mousedown="setOldAsideWidth"
          />
        </el-tooltip>
        <el-main ref="elMainRef">
          <div v-for="(item, index) in tableData" :key="index" class="main-content">
            <div class="content-header flex-between expanded">
              <!-- <el-checkbox v-model="item.selectFlag" class="checkbox-cell" true-label="1" false-label="0" @change="changeSelectFlag(item)" /> -->
              <h4 class="title">{{ item.sourceName || '--' }}</h4>
              <div>
                <span class="label">结论：</span>
                <el-select
                  v-if="isEditMode"
                  v-model="item.expResult"
                  filterable
                  size="small"
                  clearable
                  placeholder="请选择结论"
                  class="header-select"
                >
                  <el-option
                    v-for="itemResult in results"
                    :key="itemResult.id"
                    :label="itemResult.name"
                    :value="itemResult.name"
                  />
                </el-select>
                <span v-else>{{ item.expResult }}</span>
              </div>
            </div>
            <el-table
              :data="item.childList"
              fit
              border
              height="auto"
              highlight-current-row
              class="dark-table base-table expand-table"
              @header-dragend="drageHeader"
            >
              <!-- <el-table-column prop="selectFlag" :label="Number(item.selectFlag) ? '启用' : ''" :width="Number(item.selectFlag) ? colWidth.checkbox : 10">
                <template #default="{ row }">
                  <el-checkbox v-if="Number(item.selectFlag)" v-model="row.selectFlag" class="checkbox-cell" true-label="1" false-label="0" @change="changeParamSelectFlag(row, item)" />
                </template>
              </el-table-column> -->
              <el-table-column prop="sourceName" label="关键参数" :width="180" show-overflow-tooltip>
                <template #default="{ row }">
                  {{ row.sourceName }}
                </template>
              </el-table-column>
              <el-table-column prop="expRequirement" label="技术要求" :width="180" show-overflow-tooltip>
                <template #default="{ row }">
                  <el-input
                    v-if="isEditMode"
                    v-model="row.expRequirement"
                    placeholder="输入技术要求"
                    size="small"
                    :title="row.expRequirement"
                  />
                  <span v-else>{{ row.expRequirement }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="expValueUnit" label="单位" :width="120">
                <template #default="{ row }">
                  <el-select
                    v-if="isEditMode"
                    v-model="row.expValueUnit"
                    placeholder="选择"
                    clearable
                    filterable
                    size="small"
                  >
                    <el-option
                      v-for="itemUnit in units"
                      :key="itemUnit.id"
                      :label="itemUnit.name"
                      :value="itemUnit.name"
                    />
                  </el-select>
                  <span v-else>{{ row.expValueUnit }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="value" label="检测结果" :min-width="180">
                <template #default="{ row }">
                  <el-row v-if="isEditMode" class="item-input" :gutter="20">
                    <el-col
                      v-for="(expItem, expIndex) in row.expValue"
                      :key="expIndex"
                      :span="row.expValue.length >= 3 ? 8 : 24"
                      :class="{
                        'input-group': true,
                        group: expItem.colourValue,
                        marginBottom: row.expValue.length === 2 || row.expValue.length > 3
                      }"
                    >
                      <el-input
                        v-model="expItem.paraValue"
                        size="small"
                        placeholder="请输入"
                        :title="expItem.paraValue"
                        @change="changeTestResult(row, expIndex)"
                      >
                        <template v-if="expItem.colourValue" #prepend>
                          <el-tooltip :content="expItem.colourValue" placement="top" effect="light">
                            <span>{{ expItem.colourValue }}</span>
                          </el-tooltip>
                        </template>
                      </el-input>
                    </el-col>
                  </el-row>
                  <template v-else>
                    <template v-for="(expItem, expIndex) in row.expValue" :key="expIndex">
                      <span v-if="expItem.colourValue" class="exp-item-gap">{{ expItem.colourValue }}: </span>
                      <span>{{ expItem.paraValue }}</span>
                    </template>
                  </template>
                </template>
              </el-table-column>
              <el-table-column prop="expResult" label="结论" :width="130">
                <template #default="{ row }">
                  <el-select
                    v-if="isEditMode"
                    v-model="row.expResult"
                    placeholder="选择结论"
                    clearable
                    filterable
                    size="small"
                    @change="changeResult(row, index)"
                  >
                    <el-option
                      v-for="itemResult in results"
                      :key="itemResult.id"
                      :label="itemResult.name"
                      :value="itemResult.name"
                    />
                  </el-select>
                  <span v-else>{{ row.expResult }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-main>
      </div>
      <el-empty v-else :image="emptyImg" description="暂无数据" />
    </div>
  </DetailLayout>
</template>

<script>
import { h, reactive, toRefs, ref, watch, onMounted, onBeforeUnmount, nextTick, getCurrentInstance } from 'vue';
import { useRoute } from 'vue-router';
import { useStore } from 'vuex';
import { ElDivider, ElMessage, ElMessageBox } from 'element-plus';
import Sortable from 'sortablejs';
import router from '@/router';
import { drageHeader } from '@/utils/formatTable';
import { getDictionary } from '@/api/user';
import {
  queryExperimentEntrust,
  saveExperimentEntrust,
  submitExperimentEntrust,
  uploadExperimentEntrustFile,
  deleteExperimentEntrustFile
} from '@/api/outsourcing-management';
import DetailLayout from '@/components/DetailLayout';
import DragHandle from '@/components/DragHandle/handle';
import emptyImg from '@/assets/img/empty-table.png';

export default {
  name: 'OutsourcingManagementDetail',
  components: { DetailLayout, DragHandle },
  setup() {
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    const stores = useStore().state.user;
    const state = reactive({
      id: route.query.id,
      secSampleNum: '',
      experimentEntrustCode: '',
      attachmentName: '',
      attachmentUrl: '',
      tableData: [],
      templateJsonData: {
        isStandardCustom: 0
      }, // 模板信息
      tableKey: 'tableKey1',
      units: stores.unit,
      results: [],
      noWatch: false,
      currentIndex: 0,
      summarySelect: [],
      loadingStep2: false,
      selectRef: ref(),
      allValues: [],
      showAside: true,
      asideWidth: 280,
      asideMaxWidth: 600,
      asideMinWidth: 200,
      oldAsideWidth: 300,
      collapseTip: '点击折叠左面板',
      asideKey: 0,
      scrollTop: 0,
      isExistFile: false,
      file: null
    });
    const spacer = h(ElDivider, { direction: 'vertical' });
    const isEditMode = ref(route.name === 'OutsourcingManagementEdit');
    const elMainRef = ref();
    const fileRef = ref();

    watch(
      () => state.asideKey,
      () => {
        nextTick(() => {
          rowDrop();
        });
      }
    );

    onMounted(() => {
      if (isEditMode.value) {
        getItemResultList();
      }
      getData();
    });

    onBeforeUnmount(() => {
      beforeDestory();
    });

    const getItemResultList = () => {
      getDictionary('JCXMSHJLB').then(res => {
        state.results = res.data.data?.dictionaryoption;
      });
    };

    const getData = async () => {
      const res = await queryExperimentEntrust(state.id);
      if (res) {
        const result = res.data.data.map(testItem => {
          if (testItem.isColor && testItem.colorList?.length > 0) {
            // const result = res.data.data.map(testItem => {
            //   if (testItem.colorList?.length > 0) {
            testItem.childList.forEach(child => {
              child.expValue =
                child.expValue.length > 0
                  ? child.expValue
                  : testItem.colorList.map(colorName => ({
                      colourValue: colorName,
                      paraValue: '',
                      paraKey: ''
                    }));
            });
          } else {
            testItem.childList.forEach(child => {
              child.expValue =
                child.expValue.length > 0
                  ? child.expValue
                  : [
                      {
                        colourValue: '',
                        paraValue: '',
                        paraKey: ''
                      }
                    ];
            });
          }
          delete testItem.expValue;
          return testItem;
        });
        state.tableData = result;
        if (result?.length > 0) {
          state.secSampleNum = result[0].secSampleNum;
          state.experimentEntrustCode = result[0].experimentEntrustCode;
          state.attachmentName = result[0].attachmentName;
          state.attachmentUrl = result[0].attachmentUrl;
          if (import.meta.env.DEV) {
            state.attachmentUrl = result[0].attachmentUrl?.replace(window.location.host, '*************');
          }
          state.isExistFile = Boolean(state.attachmentUrl);
          nextTick(afterRender);
        }
      }
    };

    const saveData = async isSubmit => {
      const params = state.tableData;
      let res = null;
      if (isSubmit) {
        res = await submitExperimentEntrust(params);
      } else {
        res = await saveExperimentEntrust(params);
      }
      if (res) {
        ElMessage.success(isSubmit ? '提交成功！' : '保存成功！');
      }

      if (state.file) {
        const formData = new FormData();
        formData.append('file', state.file);
        await uploadExperimentEntrustFile(formData, state.id);
      } else if (!state.file && state.isExistFile) {
        await deleteExperimentEntrustFile(state.id);
      }
      handleGoBack();
    };

    const afterRender = () => {
      if (elMainRef.value?.$el) {
        elMainRef.value.$el.addEventListener('scroll', onScroll);
        // 监听鼠标滚动事件
        window.addEventListener('mousewheel', handleScroll, false) ||
          window.addEventListener('DOMMouseScroll', handleScroll, false);
      }
      rowDrop();
    };

    const beforeDestory = () => {
      if (elMainRef.value.$el) {
        // 卸载前监听鼠标滚动事件
        elMainRef.value.$el.removeEventListener('scroll', onScroll);
        window.removeEventListener('mousewheel', handleScroll, false) ||
          window.removeEventListener('DOMMouseScroll', handleScroll, false);
      }
    };

    const handleDeleteFile = () => {
      ElMessageBox({
        title: '提示',
        message: '是否删除委外报告',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      }).then(() => {
        state.file = null;
        state.attachmentName = '';
        state.attachmentUrl = '';
      });
    };

    const handleUpload = () => {
      fileRef.value.click();
    };

    const onFileChange = event => {
      const files = event.target.files;
      if (files.length > 0) {
        const file = files[0];
        const fileSize = file.size / 1024 / 1024 < 20;
        if (!fileSize) {
          ElMessage.error('上传附件大小不能超过20M');
          return;
        } else if (file.size === 0) {
          ElMessage.error('上传附件大小不能为空');
          return;
        } else {
          state.file = file;
          state.attachmentName = file.name;
          state.attachmentUrl = window.URL.createObjectURL(file);
        }
      }
    };

    const handleGoBack = () => {
      router.push({ name: 'OutsourcingManagement' });
    };

    const handleSave = () => {
      saveData();
    };

    const handleSubmit = () => {
      saveData(true);
    };

    // 用 class 添加锚点
    const scrollJump = index => {
      state.currentIndex = index;
      const jump = document.querySelectorAll('.main-content');
      let total = 0;
      for (let i = 0; i < index; i++) {
        const marginBottomOffset = parseInt(window.getComputedStyle(jump[i]).getPropertyValue('margin-bottom'));
        total = total + jump[i].offsetHeight + marginBottomOffset;
      }
      const distance = state.scrollTop;
      // 平滑滚动，时长500ms，每10ms一跳，共50跳
      let step = total / 50;
      if (total > distance) {
        smoothDown(distance, step, total);
      } else {
        const newTotal = distance - total;
        step = newTotal / 50;
        smoothUp(distance, step, total);
      }
    };

    // 向下滑动
    const smoothDown = (distance, step, total) => {
      if (distance < total) {
        distance += step;
        elMainRef.value.$el.scrollTop = distance;
        setTimeout(function () {
          smoothDown(distance, step, total);
        }, 10);
      } else {
        elMainRef.value.$el.scrollTop = total;
      }
    };

    // 向上滑动
    const smoothUp = (distance, step, total) => {
      if (distance > total) {
        distance -= step;
        elMainRef.value.$el.scrollTop = distance;
        setTimeout(function () {
          smoothUp(distance, step, total);
        }, 10);
      } else {
        elMainRef.value.$el.scrollTop = total;
      }
    };

    // 结论-change
    const changeResult = (item, index) => {
      judgmentItemResult(state.tableData[index].childList).then(res => {
        state.tableData[index].expResult = res;
      });
    };

    // 判断项目结论
    const judgmentItemResult = childArray => {
      return new Promise(resolve => {
        var itemResult = '';
        const childResult = Array.from(
          new Set(
            childArray.map(item => {
              return item.expResult;
            })
          )
        );
        if (childResult.length === 1) {
          itemResult = childResult[0];
        } else if (childResult.length === 2) {
          if (
            childResult.some(item => {
              return item === '不合格';
            })
          ) {
            // 含有不合格的，直接为不合格
            itemResult = '不合格';
          } else if (
            childResult.some(item => {
              return item === '不符合要求';
            })
          ) {
            // 含有不符合要求的，直接为不符合要求
            itemResult = '不符合要求';
          } else if (
            childResult.some(item => {
              return item === '合格';
            })
          ) {
            // 含有合格的，其他为不判定和空，直接为合格
            itemResult = '合格';
          } else if (
            childResult.some(item => {
              return item === '符合要求';
            })
          ) {
            // 含有符合要求的，其他为不判定和空，直接为符合要求
            itemResult = '符合要求';
          } else {
            // 只有不判定、空两种，检测项目结论为空
            itemResult = '';
          }
        } else if (childResult.length === 3) {
          if (
            childResult.some(item => {
              return item === '不合格';
            })
          ) {
            // 含有不合格的，直接为不合格
            itemResult = '不合格';
          } else if (
            childResult.some(item => {
              return item === '不符合要求';
            })
          ) {
            // 含有不符合要求的，直接为不符合要求
            itemResult = '不符合要求';
          } else if (
            childResult.some(item => {
              return item === '合格';
            })
          ) {
            // 只有不判定、空和合格三种，检测项目结论为合格
            itemResult = '合格';
          } else {
            // 只有不判定、空和符合要求三种，检测项目结论为符合要求
            itemResult = '符合要求';
          }
        } else {
          // 四种值都有，结论为不合格
          if (
            childResult.some(item => {
              return item === '不合格';
            })
          ) {
            itemResult = '不合格';
          } else {
            // 四种值都有，结论为不符合要求
            itemResult = '不符合要求';
          }
        }
        resolve(itemResult);
      });
    };

    // 获取滚动高度
    const onScroll = e => {
      state.scrollTop = e.target.scrollTop;
    };

    // 鼠标滚动时候，让判定标准下拉框隐藏
    const handleScroll = () => {
      if (state.tableData.length > 0) {
        state.tableData.forEach((td, index) => {
          const sname = 'selectRef' + 2 * index;
          if (proxy.$refs[sname]) {
            proxy.$refs[sname].blur();
          }
        });
      }
    };

    // 拖拽边框
    const widthChange = m => {
      state.asideWidth = state.asideWidth - m;
      if (state.asideWidth < state.asideMinWidth) {
        state.asideWidth = state.asideMinWidth;
      } else {
        if (state.asideWidth >= state.asideMaxWidth) {
          state.asideWidth = state.asideMaxWidth;
        }
      }
    };

    const setOldAsideWidth = () => {
      state.oldAsideWidth = state.asideWidth;
    };

    const collapseLeft = () => {
      if (state.asideWidth === state.oldAsideWidth) {
        state.showAside = !state.showAside;
        state.collapseTip = state.showAside ? '点击折叠左面板' : '点击展开左面板';
      }
    };

    // 拖拽功能
    const rowDrop = () => {
      const tbody = document.querySelector('.left-main');
      Sortable.create(tbody, {
        handle: '.tes-move',
        draggable: '.tree-list',
        ghostClass: 'ghost',
        dragClass: 'drag',
        forceFallback: true,
        onEnd({ newIndex, oldIndex }) {
          // 移除原来的数据
          const currRow = state.tableData.splice(oldIndex, 1)[0];
          // 移除原来的数据并插入新的数据
          state.tableData.splice(newIndex, 0, currRow);
          state.tableData.forEach((value, index) => {
            value.order = index;
          });
          state.asideKey += 1;
        }
      });
    };

    return {
      drageHeader,
      ...toRefs(state),
      spacer,
      emptyImg,
      isEditMode,
      elMainRef,
      fileRef,
      handleDeleteFile,
      handleUpload,
      onFileChange,
      handleGoBack,
      handleSave,
      handleSubmit,
      scrollJump,
      changeResult,
      widthChange,
      setOldAsideWidth,
      collapseLeft
    };
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-divider--vertical) {
  height: 40px;
}

.flex-start {
  justify-content: space-between !important;
}

.container {
  height: 100%;

  .wrapper {
    display: flex;
    width: 100%;
    height: 100%;
  }

  :deep(.el-table .hidden-row) {
    display: none;
  }
  .exponent {
    width: 44px;
    position: absolute;
    top: -10px;
    left: 26px;
    :deep(.el-input__inner) {
      height: 20px;
      padding: 0 5px;
    }
  }
  .zsNumber {
    display: inline-block;
    position: relative;
    margin-left: 5px;
  }
  .el-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    background: $background-color;
    width: 100%;
  }
  .el-aside {
    background: $background-color;
    margin-bottom: 0;
    padding: 14px 20px 20px;
    p.title {
      text-align: left;
      margin: 0;
      font-size: 16px;
      font-weight: bold;
      padding-bottom: 5px;
      border-bottom: 1px solid #ebeef5;
    }
    .left-main {
      max-height: calc(100vh - 300px);
      overflow: auto;
    }
    .tree-list {
      display: flex;
      align-items: center;
      padding: 4px 0;
      text-align: left;
      border-bottom: 1px solid #ebeef5;
      cursor: pointer;
      &:hover {
        background: $tes-primary2;
      }
      &:active {
        background: $tes-primary2;
      }
      .line {
        display: inline-block;
        width: 4px;
        height: 16px;
        margin-right: 12px;
      }
      .custom-icon {
        width: 20px;
        height: 20px;
        font-size: 14px;
        margin-right: 12px;
      }
      .red-border {
        background: $red;
      }
      .green-border {
        background: $green;
      }
      .yellow-border {
        background: $yellow;
      }

      .sortingIcon {
        margin-right: 5px;
        font-size: 10px;
        cursor: move;
      }
      .text {
        font-size: 14px;
        max-width: calc(100% - 40px);
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
      }
    }
    .ghost {
      background-color: #e6f8f4 !important;
    }
    .drag {
      background: #e6f8f4 !important;
      background-image: linear-gradient(#e9e9eb, #ffffff) !important;
    }
  }
  .close-handle {
    height: calc(100vh - 24rem);
    position: absolute;
    left: -24px;
  }
  .el-main {
    padding: 0;
    overflow-y: auto;
  }
  .main-content {
    background: $background-color;
    padding: 20px;
    &:not(:last-of-type) {
      margin-bottom: 20px;
    }
    .content-header {
      display: flex;
      width: 100%;
      height: 32px;
      align-items: center;
      &.flex-between {
        width: 100%;
        justify-content: space-between;
      }
      .header-select {
        display: inline-block;
        width: 240px;
      }
      .title {
        font-size: 16px;
        font-weight: bold;
        color: $tes-primary;
        margin: 0;
      }
      .label {
        color: $tes-font2;
      }
      :deep(.el-checkbox__input) {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .expanded {
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
      position: relative;
    }
    .marginBottom {
      margin-bottom: 8px;
    }
    .item-input {
      display: flex;
      .input-group {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &.group:deep(.el-input-group__prepend) {
          padding: 0 4px;
          max-width: 60px;
          overflow: hidden;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        &.group:deep(.el-input__inner) {
          padding: 0 7px;
          min-width: 40px;
        }
      }
    }

    .exp-item-gap {
      margin-left: 10px;
      &:first-child {
        margin-left: 0;
      }
    }
  }
}

.file-item {
  display: flex;
  align-items: center;

  &:hover .el-icon-close {
    opacity: 1;
  }

  .el-icon-close {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    font-size: 18px;
    cursor: pointer;
  }
}

.file-name {
  max-width: 300px;
  line-height: 30px;
  text-overflow: ellipsis;
  font-size: 16px;
  text-align: left;
}
</style>
