// @import './variables.scss';

// common layout page styles

// list page
.page-wrapper {
  height: inherit;
  overflow: hidden auto;

  .page-header,
  .page-custom-header {
    background: $background-color;
    padding: 12px 24px;
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    z-index: 1000;
    border-bottom: 1px solid #f0f2f5;
    .page-title {
      color: #303133;
      font-size: 20px;
      line-height: 40px;
      display: flex;
      align-items: center;

      .el-tag {
        margin-left: 10px;
      }
    }

    .page-searchbar {
      .el-form-item {
        margin-bottom: 0px;
      }
    }
  }

  .page-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .page-list-main {
    position: relative;

    .search-panel {
      border: 0;
      padding: 0 24px;
      box-sizing: border-box;
      overflow: hidden;
      text-align: left;

      .el-form-item {
        margin-bottom: 10px;
      }

      .el-radio-group {
        .date-radio {
          display: inline-block;
          margin-left: 10px;
        }
      }

      .el-collapse {
        border: none;

        .el-collapse-item {
          border: 0;
          margin: 0;
        }
      }
    }

    .page-main {
      background: $background-color;
      margin: 0 24px;
      padding: 20px 20px 20px;
    }
  }

  .el-form {
    text-align: left;
  }
}

// detail page
.page-detail-wrapper {
  .page-header {
    border-bottom: 1px solid #f0f2f5;
    .flex-between {
      justify-content: space-between;
    }
    .flex-start {
      justify-content: flex-start;
    }
    .header-flex {
      display: flex;
      align-items: center;
      .item-column {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        .item-label {
          color: $tes-font1;
          line-height: 22px;
          font-size: 14px;
        }
        .item-content {
          margin-top: 4px;
          color: $tes-font;
          line-height: 32px;
          font-size: 24px;
          .iconfont {
            margin-right: 8px;
            font-size: 24px;
          }
          .warning {
            color: $tes-yellow;
          }
          .primary {
            color: $tes-primary;
          }
          .success {
            color: $tes-green;
          }
        }
      }
    }
    .page-title {
      color: $tes-font;
      font-size: 20px;
      line-height: 40px;
      display: flex;
      align-items: center;

      .el-tag {
        margin-left: 10px;
      }
    }
    .page-left {
      width: 70%;
      text-align: left;
    }
    .page-title2 {
      color: $tes-font;
      font-size: 14px;
      line-height: 32px;
      display: flex;
      flex-direction: row;
      align-items: center;
    }
    .page-title2Item {
      flex: 1;
    }
  }

  .page-detail-main {
    padding-bottom: 20px;
    .page-main {
      padding: 20px;
      background: $background-color;
    }
    // 详情页折叠面板样式
    .collapse-wrap {
      border: none;
      .el-collapse-item:not(:last-of-type) {
        margin-bottom: 20px;
      }
      .el-collapse-item__header,
      .el-collapse-item__wrap {
        padding: 0 20px;
        border-bottom: none;
      }
      .el-collapse-item__header {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
        .collapse-header-title {
          font-size: 18px;
          font-weight: bold;
          color: $tes-font;
        }
      }
      .el-collapse-item__wrap {
        box-shadow: 0 1px 0 rgba(0, 0, 0, 0.12) inset;
        .collapse-content {
          padding-top: 20px;
          .collapse-top {
            .collapse-item {
              margin-bottom: 10px;
            }
          }
          .collapse-item {
            font-size: 14px;
            .el-col {
              display: flex;
              line-height: 32px;
              text-align: left;
              .title {
                width: 100px;
                display: inline-block;
                text-align: right;
                color: $tes-font2;
              }
              .txt {
                display: inline-block;
                max-width: calc(100% - 100px);
                overflow: hidden;
                word-break: break-all;
                text-overflow: ellipsis;
                white-space: nowrap; //不换行
              }
            }
          }
          .collapse-content-title {
            display: flex;
            align-items: center;
            font-size: 16px;
            line-height: 24px;
            color: $tes-font;
            margin-bottom: 8px;
            .line-space {
              width: 4px;
              height: 16px;
              margin-right: 10px;
              background: $tes-primary;
            }
          }
        }
      }
    }
    .panel-headline {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 10px;

      .title {
        font-size: 16px;
        line-height: 24px;
        color: #303133;
        font-weight: bold;
      }
    }

    .panel-content {
      .el-form {
        text-align: left;
      }

      .el-form .el-form-item {
        margin: 0;
        width: 100%;
        .el-form-item__label {
          color: $tes-font2;
        }
        .el-form-item__content {
          // 标准情况下label宽度为90px
          max-width: calc(100% - 90px);
        }
      }
    }
  }
}

.no-select {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
