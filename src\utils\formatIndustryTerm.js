import { InspectionType, taskType, testType } from '@/data/industryTerm';

export function formatInspectionType(id) {
  id = Number(id);
  const inspectionIndex = InspectionType.findIndex(item => item.id === id);
  return inspectionIndex === -1 ? '' : InspectionType[inspectionIndex].name;
}

export function formatTaskType(id) {
  id = Number(id);
  const taskIndex = taskType.findIndex(item => item.id === id);
  return taskIndex === -1 ? '' : taskType[taskIndex].name;
}

export function formatTestType(id) {
  id = Number(id);
  const testIndex = testType.findIndex(item => item.id === id);
  return testIndex === -1 ? '' : testType[testIndex].name;
}
