<template>
  <!-- 人员培训信息 -->
  <!-- <div class="mb-5 text-left">
    <el-button
      v-if="getPermissionBtn('AddDepartPersonnelTraining')"
      icon="el-icon-plus"
      size="small"
      @click="handleAdd"
      @keyup.prevent
      @keydown.enter.prevent
      >新增</el-button
    >
    <el-button
      v-if="formData.oldTableData.length > 0 && getPermissionBtn('EditDepartPersonnelTraining')"
      icon="el-icon-edit"
      size="small"
      @click="handleEdit"
      @keyup.prevent
      @keydown.enter.prevent
      >编辑</el-button
    >
    <el-button v-show="isShowSave" type="primary" size="small" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
      >保存</el-button
    >
    <el-button v-show="isShowSave" size="small" @click="handleCancle">取消</el-button>
  </div> -->
  <el-form ref="formRef" :model="formData" style="margin: 0px">
    <el-table
      id="paramTable"
      ref="paramTableRef"
      key="id"
      :data="formData.tableData"
      fit
      border
      height="auto"
      highlight-current-row
      class="detail-table dark-table drawer-height-table"
    >
      <el-table-column label="序号" type="index" :width="colWidth.serialNo" />
      <el-table-column prop="name" label="培训名称" :min-width="150" show-overflow-tooltip>
        <template #default="{ row }">
          <span> {{ row.name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="培训地点" :min-width="150" show-overflow-tooltip>
        <template #default="{ row }">
          <span> {{ row.address || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="theoryScore" label="理论成绩">
        <template #default="{ row }">
          <span> {{ row.theoryScore }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="practicalScore" label="实操成绩">
        <template #default="{ row }">
          <span> {{ row.practicalScore }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="startDate" label="开始日期" :min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <span> {{ row.startDate || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="endDate" label="结束日期" :min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <span> {{ row.endDate || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" :min-width="150" show-overflow-tooltip>
        <template #default="{ row }">
          <span> {{ row.createTime || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="attachmentList" label="培训附件" :min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-for="item in row.attachmentList" :key="item.id" class="blue" @click="handleDownLoad(item)">
            {{ item.fileName || '--' }}；
          </div>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
</template>

<script>
// Basic
import { ref, watch, reactive, getCurrentInstance, toRefs } from 'vue';
// Api
import { findByUserId, downloadById } from '@/api/departManagement';
// import { ElMessageBox } from 'element-plus';
// Data
import { colWidth } from '@/data/tableStyle';

// Utils
import { isUsername, idNumberRex } from '@/utils/validate';
import { getPermissionBtn } from '@/utils/common';

export default {
  name: 'DetailPersonTrain',
  components: {},
  props: {
    personId: {
      type: String,
      default: ''
    }
  },
  // emits: ['handleRefresh'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // 抽屉事件
    const state = reactive({
      detailLoading: false,
      isShowSave: false,
      formRef: ref(),
      formData: {
        tableData: [],
        oldTableData: []
      }
    });
    watch(props, newValue => {
      if (newValue.personId) {
        initDetail(newValue.personId);
      }
    });
    const initDetail = async personId => {
      state.detailLoading = true;
      const response = await findByUserId(personId).finally((state.detailLoading = false));
      if (response) {
        state.formData.tableData = response.data.data;
      }
    };
    // 确认新增
    const onSubmit = () => {
      proxy.$refs['formRef'].validate(valid => {
        if (valid) {
          // context.emit('handleRefresh', true);
          initDetail();
        }
      });
    };
    // 新增
    const handleAdd = () => {
      state.formData.tableData.push({});
    };
    // 下载
    const handleDownLoad = async row => {
      state.detailLoading = true;
      const response = await downloadById(row.id).finally((state.detailLoading = false));
      if (response) {
        const blob = new Blob([response.data], { type: '' });
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = `${row.name}`;
        a.href = blobUrl;
        a.click();
        proxy.$message.success('下载附件成功');
      }
    };
    // 编辑
    const handleEdit = () => {};
    // 删除
    const handleDelete = (row, index) => {
      state.formData.tableData.splice(index, 1);
    };
    // 取消
    const handleCancle = () => {};
    return {
      ...toRefs(state),
      isUsername,
      handleDownLoad,
      getPermissionBtn,
      handleEdit,
      colWidth,
      handleAdd,
      handleDelete,
      handleCancle,
      idNumberRex,
      onSubmit,
      initDetail
    };
  }
};
</script>

<style lang="scss" scoped></style>
