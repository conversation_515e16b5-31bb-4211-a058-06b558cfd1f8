import request from '@/utils/request';

// 保存或更新环境监测信息
export function saveOrUpdate(data) {
  return request({
    url: '/api-device/device/environmentalmonitoring/saveOrUpdate',
    method: 'post',
    data
  });
}
// 获取已经保存的设备信息
export function environmentalList() {
  return request({
    url: '/api-device/device/environmentalmonitoring/list',
    method: 'get'
  });
}
// 获取已经保存的设备信息
export function getRdsData() {
  return request({
    url: '/api-device/device/environmentalmonitoring/getRdsData',
    method: 'get'
  });
}

// 删除环境监测设备
export function deleteDevice(data) {
  return request({
    url: '/api-device/device/environmentalmonitoring/delete',
    method: 'delete',
    data
  });
}

// 导出环境数据
export function exportRdsData(data) {
  return request({
    url: '/api-device/device/environmentalmonitoring/exportRdsData',
    responseType: 'blob',
    method: 'post',
    data
  });
}
