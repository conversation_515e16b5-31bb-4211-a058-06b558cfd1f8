import request from '@/utils/request';
import qs from 'qs';
const Headers = {
  'Content-Type': 'application/json; charset=utf-8'
};
const formHeaders = {
  'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
};
// 接口日志查询列表
export function getList(data) {
  return request({
    url: '/api-log/log/getList',
    method: 'get',
    headers: Headers,
    params: data
  });
}

// 请求重试
export function compensationApi(data) {
  data = qs.stringify(data);
  return request({
    url: '/api-log/log/compensation',
    method: 'PUT',
    headers: formHeaders,
    data
  });
}
