<template>
  <div class="login-container" />
</template>

<script>
import store from '@/store';
import router from '@/router';
import { useRoute } from 'vue-router';
export default {
  name: 'LoginMobile',
  components: {},
  setup(props, ctx) {
    const route = useRoute();
    const getLoginPush = async () => {
      const params = {
        username: route.query.username,
        password: route.query.password,
        grant_type: 'password'
      };
      const loginResult = await store.dispatch('user/login', params);
      if (loginResult) {
        router.push({
          name: 'UniappTemplate',
          query: {
            experimentId: route.query.experimentId,
            samplesId: route.query.samplesId,
            capabilityId: route.query.capabilityId,
            type: route.query.type,
            new: route.query.new
          }
        });
      }
    };
    getLoginPush();
    return {
      getLoginPush
    };
  }
};
</script>
<style lang="scss" scoped></style>
