<template>
  <!-- 委托费用详情 -->
  <DetailLayout :loading="loading">
    <template #page-header>
      <div class="header-flex flex-between">
        <div class="page-title">委托编号：{{ formData.entrustNo || '--' }}</div>
        <div class="btn-group">
          <el-button size="large" icon="el-icon-back" @click="goBack()">返回列表</el-button>
        </div>
      </div>
    </template>
    <el-collapse v-model="activeNames" class="collapse-wrap">
      <el-collapse-item name="1">
        <template #title>
          <div class="collapse-header-title">
            <div class="title">基本信息</div>
          </div>
        </template>
        <div class="panel-content">
          <el-form ref="taskInfoRef" class="form_class" :model="formData" label-width="110px">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="登记日期：" prop="regDate">
                  <div class="nowrap">
                    {{ formatDate(formData.regDate) || '--' }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="付款方：" prop="payerName">
                  {{ formData.payerName || '--' }}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="委托金额(￥)：" prop="entrustCost">
                  <div class="nowrap">
                    {{ formData.entrustCost || '--' }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="已收金额(￥)：" prop="paidCost">
                  <div class="nowrap">
                    {{ formData.paidCost || '--' }}
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-collapse-item>
      <el-collapse-item name="2">
        <!-- 委托费用 -->
        <template #title>
          <div class="collapse-header-title">费用信息</div>
        </template>
        <div class="commission-fee">
          <!-- 费用信息 -->
          <div class="btnGroup text-right">
            <el-button
              v-if="getPermissionBtn('additionalCost')"
              icon="el-icon-plus"
              type="primary"
              size="small"
              @click="handleAddFee()"
              >新增费用</el-button
            >
          </div>
          <el-table
            ref="tableRef"
            v-loading="loading"
            :data="tableCostInfo"
            size="medium"
            fit
            border
            height="auto"
            class="dark-table base-table format-height-table"
            @header-dragend="drageHeader"
          >
            <el-table-column label="序号" type="index" :min-width="colWidth.serialNo" align="center" />
            <el-table-column
              label="费用类型"
              prop="costType"
              :min-width="colWidth.typeGroup"
              align="left"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <span>{{ row.costType || '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="费用(￥)"
              prop="inspectionCost"
              :min-width="colWidth.material"
              align="left"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                {{ row.inspectionCost || '--' }}
              </template>
            </el-table-column>
            <el-table-column
              label="费用描述"
              prop="remark"
              :min-width="colWidth.remark"
              align="left"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                {{ row.remark || '--' }}
              </template>
            </el-table-column>
            <el-table-column
              label="创建日期"
              prop="createTime"
              :min-width="colWidth.date"
              align="left"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                {{ formatDate(row.createTime) || '--' }}
              </template>
            </el-table-column>
            <el-table-column
              label="创建人"
              prop="createBy"
              :min-width="colWidth.person"
              align="left"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <UserTag :name="getNameByid(row.createBy) || '--'" />
              </template>
            </el-table-column>
          </el-table>
          <DialogFree
            :dialog-visible="dialogShow"
            :cost-type="costTypeList"
            :detail-data="selectRow"
            @close-dialog="closeDialog"
          />
        </div>
      </el-collapse-item>
      <el-collapse-item name="3">
        <!-- 付款方信息 -->
        <template #title>
          <div class="collapse-header-title">付款方信息</div>
        </template>
        <PayerInformation @refreshDetail="refreshDetail" />
      </el-collapse-item>
      <el-collapse-item name="4">
        <!-- 收款记录 -->
        <template #title>
          <div class="collapse-header-title">收款记录</div>
        </template>
        <div class="collection-record">
          <!-- 收款记录 -->
          <div
            v-if="Number(formData.entrustCost) > Number(formData.paidCost) && getPermissionBtn('ReceiptAndPayment')"
            class="btnGroup text-right"
          >
            <el-button icon="el-icon-plus" type="primary" size="small" @click="handleAddRecord()">添加收款单</el-button>
          </div>
          <el-table
            ref="tableRef"
            :data="tableRecord"
            size="medium"
            fit
            border
            height="auto"
            class="dark-table base-table format-height-table"
            @header-dragend="drageHeader"
          >
            <el-table-column
              label="收款编号"
              prop="collectionNo"
              :min-width="colWidth.orderNo"
              align="left"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <span class="blue-color" @click="handleEditRecord(row)"> {{ row.collectionNo || '--' }} </span>
              </template>
            </el-table-column>
            <el-table-column
              label="收款日期"
              prop="createTime"
              :min-width="colWidth.date"
              align="left"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                {{ formatDate(row.createTime) || '--' }}
              </template>
            </el-table-column>
            <el-table-column
              label="收款人"
              prop="createBy"
              :min-width="colWidth.people"
              align="left"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <UserTag :name="getNameByid(row.createBy) || '--'" />
              </template>
            </el-table-column>
            <!-- <el-table-column label="折扣金额(￥)" prop="discountCost" :min-width="colWidth.money" align="left" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.discountCost || '--' }}
              </template>
            </el-table-column> -->
            <el-table-column
              label="收款金额(￥)"
              prop="paidCost"
              :min-width="colWidth.money"
              align="left"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                {{ row.paidCost }}
              </template>
            </el-table-column>
            <el-table-column label="状态" prop="status" :min-width="colWidth.status" align="left" show-overflow-tooltip>
              <template #default="{ row }">
                <el-tag :type="statusJSON[row.status].type" size="small" effect="dark">{{
                  statusJSON[row.status].label
                }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="备注" prop="remark" :min-width="colWidth.remark" align="left" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.remark || '--' }}
              </template>
            </el-table-column>
          </el-table>
          <DrawerCollection
            :drawer-show="drawerShow"
            :drawer-type="drawerType"
            :detail-info="recordInfo"
            @closeDrawer="closeDrawer"
          />
        </div>
      </el-collapse-item>
    </el-collapse>

    <!-- <template #other>
    </template> -->
  </DetailLayout>
</template>

<script>
import { reactive, toRefs, ref } from 'vue';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { getLoginInfo } from '@/utils/auth';
import DetailLayout from '@/components/DetailLayout';
import router from '@/router/index.js';
import DrawerCollection from './components/drawer-collection.vue';
import { getTaskRegistrationList, getFeeInfoListByTaskId } from '@/api/task-registration';
import PayerInformation from './components/payer-information.vue';
import { entrustCostCollectionList } from '@/api/consignmentCollection';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import UserTag from '@/components/UserTag';
import DialogFree from './components/dialog-fee.vue';
import { getDictionary } from '@/api/dictionary';

export default {
  name: 'ConsignmentCollectionDetail',
  components: {
    DetailLayout,
    DrawerCollection,
    PayerInformation,
    UserTag,
    DialogFree
  },
  setup() {
    const route = useRoute();
    const store = useStore().state;
    const state = reactive({
      activeNames: ['1', '2', '3', '4'],
      tableRecord: [], // 收款记录
      drawerType: '', // 收款抽屉类型
      dialogShow: false,
      tableCostInfo: [], // 费用信息
      recordInfo: {}, // 收费弹出窗详情
      costTypeList: [],
      statusJSON: {
        0: { label: '作废', type: 'danger' },
        1: { label: '生效', type: 'success' }
      },
      currentAccountId: getLoginInfo().accountId,
      drawerShow: false,
      selectRow: {},
      userOptions: store.common.nameList,
      loading: false,
      queryDetail: route.query,
      formData: {},
      taskInfoRef: ref()
    });

    // 初始化数据
    const getDetailInfo = () => {
      state.loading = true;
      getTaskRegistrationList({
        limit: '-1',
        page: '1',
        tableQueryParamList: [{ tableName: 'entrustNo', tableValue: route.query.entrustNo }]
      }).then(res => {
        state.loading = false;
        if (res) {
          state.formData = res.data.data.list[0];
        }
      });
    };
    getDetailInfo();
    const handleAddRecord = () => {
      state.drawerShow = true;
      state.drawerType = 'add';
      state.recordInfo = state.formData;
    };
    // 编辑收款单记录
    const handleEditRecord = row => {
      state.drawerShow = true;
      state.drawerType = 'edit';
      state.recordInfo = {
        ...state.formData,
        collectionId: row.collectionId,
        entrustCost: row.discountCost,
        paidCost: row.paidCost,
        status: row.status,
        collectionNo: row.collectionNo,
        remark: row.remark,
        createBy: row.createBy,
        createTime: row.createTime
      };
    };
    // 关闭收款单抽屉
    const closeDrawer = val => {
      state.drawerShow = false;
      if (val) {
        getDetailInfo();
        getDetailList();
      }
    };
    // 查询收款记录
    const getDetailList = () => {
      state.loading = true;
      entrustCostCollectionList(route.query.id).then(res => {
        state.loading = false;
        if (res) {
          state.tableRecord = res.data.data;
        }
      });
    };
    getDetailList();
    // 查询费用信息
    const getCostInfoList = () => {
      state.loading = true;
      getFeeInfoListByTaskId(route.query.id).then(res => {
        state.loading = false;
        if (res) {
          state.tableCostInfo = res.data.data.list;
        }
      });
    };
    getCostInfoList();
    const getDictionaryList = () => {
      state.costTypeList = [];
      getDictionary('FeeType').then(res => {
        res.data.data.dictionaryoption.forEach(item => {
          if (item.status === 1) {
            state.costTypeList.push(item);
          }
        });
      });
    };
    getDictionaryList();
    const handleAddFee = () => {
      state.dialogShow = true;
      state.selectRow = {};
    };
    // 增加费用信息
    const closeDialog = val => {
      state.dialogShow = false;
      if (val) {
        getCostInfoList();
        getDetailInfo();
      }
    };
    // 修改付款方信息
    const refreshDetail = val => {
      if (val) {
        getDetailInfo();
      }
    };
    const goBack = () => {
      router.push({ path: '/laboratory-management/consignmentCollection' });
    };

    return {
      ...toRefs(state),
      goBack,
      refreshDetail,
      handleEditRecord,
      closeDialog,
      closeDrawer,
      formatDate,
      handleAddFee,
      handleAddRecord,
      drageHeader,
      colWidth,
      getNameByid,
      getDetailInfo,
      getPermissionBtn
    };
  }
};
</script>
<style lang="scss" scoped>
.form_class {
  padding: 15px 0 0 0;
}
.collection-record,
.commission-fee {
  margin: 8px 0 0 0;
}

.btnGroup {
  margin: 0 0 10px 0;
}
</style>
