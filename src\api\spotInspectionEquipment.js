import request from '@/utils/request';
/**
 * 设备点检列表
 */
export function devicePointInspectionList(params) {
  return request({
    url: `/api-device/device/devicePointInspection/list`,
    method: 'get',
    params
  });
}
/**
 * 删除点检表
 */
export function devicePointInspectionDelete(data) {
  return request({
    url: `/api-device/device/devicePointInspection/delete`,
    method: 'post',
    data
  });
}
// 保存点检字段表信息
export function devicePointInspectionSave(data) {
  return request({
    url: '/api-device/device/devicePointInspection/save',
    method: 'post',
    data
  });
}
// 根据Id查询设备点检表信息
export function devicePointInspectionInfo(id) {
  return request({
    url: `/api-device/device/devicePointInspection/info/${id}`,
    method: 'get'
  });
}
