import { throttle } from 'lodash';

export const singleChartTooltipFormatter = throttle(params => {
  if (params.length > 0) {
    return `
      <div style="margin: 0px 0 0;line-height:1;">
        <div style="margin: 0px 0 0;line-height:1;">
        ${params
          .map(
            (item, index) => `
          <div style="margin: 0px 0 0;line-height:1;">
            <div style="margin: ${index === 0 ? 0 : 10}px 0 0;line-height:1;">
              ${item.marker}
              <span style="font-size:14px;color:#303133;font-weight:400;margin-left:2px">${item.name}</span>
              <span style="margin-left:10px;font-size:14px;color:#303133;font-weight:900">${item.value[0]}</span>
              <span style="float:right;margin-left:10px;font-size:14px;color:#303133;">${item.data.date}</span>
              <div style="clear:both"></div>
            </div>
            <div style="clear:both"></div>
          </div>`
          )
          .join('')}
          <div style="clear:both"></div>
        </div>
        <div style="clear:both"></div>
      </div>
    `;
  }
}, 100);

export const SPCChartTooltipFormatter = throttle((params, list) => {
  const axisValue = params[0]?.axisValue || 0;
  const dataIndex = params[0]?.dataIndex;
  const warnings = list[dataIndex] || [];
  return `
  <div style="margin:0px 0 0;line-height:1;">
    <div style="margin:0px 0 0;line-height:1;">
      <div style="font-size:14px;color:#303133;font-weight:400;line-height:1;">${axisValue}</div>
      <div style="margin:0px 0 0;line-height:1;">
        ${params
          .map(
            item => `
        <div style="margin:10px 0 0;line-height:1;">
          <div style="margin:0px 0 0;line-height:1;">
            ${item.marker}
            <span style="font-size:14px;color:#303133;font-weight:400;margin-left:2px">${item.seriesName}</span>
            <span style="float:right;margin-left:20px;font-size:14px;color:#303133;font-weight:900">${item.value.toExponential()}</span>
            <div style="clear:both"></div>
          </div>
          <div style="clear:both"></div>
        </div>`
          )
          .join('')}
        ${
          warnings.length > 0
            ? warnings
                .map(
                  item => `
        <div style="margin:10px 0 0;line-height:1;">
          <div style="margin:0px 0 0;line-height:1;max-width:240px;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;color:#e6a23c;">
            <span style="font-size:14px;font-weight:400;margin-left:2px">${item.criterion}.</span>
            <span style="margin-left:3px;font-size:14px;font-weight:100">${item.description}</span>
            <div style="clear:both"></div>
          </div>
          <div style="clear:both"></div>
        </div>`
                )
                .join('')
            : ''
        }
        <div style="clear:both"></div>
      </div>
      <div style="clear:both"></div>
    </div>
    <div style="clear:both"></div>
  </div>
  `;
}, 100);
