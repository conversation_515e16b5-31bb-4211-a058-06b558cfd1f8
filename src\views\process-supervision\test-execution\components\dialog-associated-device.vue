<template>
  <el-dialog
    v-model="dialogVisiable"
    title="选择设备"
    :close-on-click-modal="false"
    width="800px"
    custom-class="dialogRecipients"
    :before-close="closedialog"
  >
    <el-table
      ref="tableRef"
      v-loading="dialogLoading"
      :data="tableData"
      class="dark-table format-height-table"
      tooltip-effect="light"
      style="width: 100%"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      highlight-current-row
      @current-change="changeRadio"
      @header-dragend="drageHeader"
    >
      <el-table-column type="index" label="选择" width="70" align="center">
        <template #default="{ row }">
          <el-radio v-model="row.radio" :label="row.deviceId">{{ '' }}</el-radio>
        </template>
      </el-table-column>
      <el-table-column prop="deviceNumber" label="设备编号" :min-width="colWidth.orderNo">
        <template #default="{ row }">
          {{ row.deviceNumber }}
        </template>
      </el-table-column>
      <el-table-column prop="deviceName" label="设备名称" :min-width="colWidth.projectName">
        <template #default="{ row }">
          <span>{{ row.deviceName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="计量有效期" :min-width="colWidth.dateranger" show-overflow-tooltip>
        <template #default="{ row }">
          {{ (formatDate(row.validBeginDate) || '--') + ' ~ ' + (formatDate(row.validEndDate) || '--') }}
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="small" :loading="dialogLoading" @click="closedialog()">取 消</el-button>
        <el-button
          size="small"
          type="primary"
          :loading="dialogLoading"
          @click="onSubmit"
          @keyup.prevent
          @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, ref, toRefs, watch, getCurrentInstance } from 'vue';
import { colWidth } from '@/data/tableStyle';
import { getNameByid } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { formatDate } from '@/utils/formatTime';
import router from '@/router/index.js';

export default {
  name: 'DialogAssociatedDevice',
  components: {},
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    secsamplenum: {
      type: String,
      default: ''
    },
    capabilityId: {
      type: String,
      default: ''
    },
    deviceList: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // const store = useStore()
    const state = reactive({
      dialogVisiable: false,
      dialogLoading: false,
      tableRef: ref(),
      selectRow: {}, // 选中的设备
      tableData: []
    });
    watch(props, newValue => {
      state.dialogVisiable = newValue.dialogShow;
      if (state.dialogVisiable) {
        state.tableData = props.deviceList;
      }
    });

    const onSubmit = () => {
      if (state.selectRow.deviceId) {
        router.push({
          path: '/collectiondetail',
          query: {
            id: state.selectRow.deviceId,
            deviceNumber: state.selectRow.deviceNumber,
            name: state.selectRow.deviceName,
            secsamplenum: props.secsamplenum,
            capabilityId: props.capabilityId
          }
        });
      } else {
        proxy.$message.warning('请先选择设备！');
      }
    };
    const changeRadio = row => {
      state.tableData.forEach(item => {
        if (item.deviceId === row.deviceId) {
          item.radio = item.deviceId;
          state.selectRow = item;
        } else {
          item.radio = '';
        }
      });
    };
    const closedialog = () => {
      context.emit('closeDialog');
    };
    return {
      ...toRefs(state),
      changeRadio,
      drageHeader,
      props,
      onSubmit,
      formatDate,
      getNameByid,
      closedialog,
      colWidth
    };
  },
  computed: {},
  created() {}
};
</script>

<style lang="scss" scoped></style>
