import request from '@/utils/request';

// 检测分配查询列表
export function getDistributionList(data) {
  return request({
    url: '/api-orders/orders/distribution/distributionList',
    method: 'post',
    data
  });
}
// 根据样品Id委托信息(回显样品详情信息)
export function getDistributionInfoById(data) {
  return request({
    url: '/api-orders/orders/distribution/info',
    method: 'post',
    data
  });
}
// 保存或修改样品分配信息
export function saveDistributionInfo(data) {
  return request({
    url: '/api-orders/orders/distribution/save',
    method: 'post',
    data
  });
}
// 认领
export function claimSample(data) {
  return request({
    url: '/api-orders/orders/distribution/claim',
    method: 'post',
    data
  });
}

/**
 * 检测分配时，样品作废
 * 更新字段 isInvalidated = 1;
 * isInvalidated默认为 0，作废后则为 1
 * @param {string} sampleId
 */
export function cancelSample(sampleId) {
  return request({
    url: `/api-orders/orders/samples/invalidated/${sampleId}`,
    method: 'get'
  });
}

/**
 * 检测分配时，将已作废样品还原
 * 更新字段 isInvalidated = 1;
 * 作废样品isInvalidated为 1，还原后则为 0
 * @param {string} sampleId
 */
export function restoreSample(sampleId) {
  return request({
    url: `/api-orders/orders/samples/unInvalidated/${sampleId}`,
    method: 'get'
  });
}
// 检测分配不同状态的样品数量统计
export function getNumberApi(data) {
  return request({
    url: `/api-orders/orders/distribution/statistics`,
    method: 'post',
    data
  });
}
// 取消加急
export function unUrgentApi(data) {
  return request({
    url: `/api-orders/orders/samples/unUrgent`,
    method: 'post',
    data
  });
}

// 加急
export function urgentApi(data) {
  return request({
    url: `/api-orders/orders/samples/urgent`,
    method: 'post',
    data
  });
}
// standard查询列表
export function standardproductMatch(data) {
  return request({
    url: `/api-capabilitystd/standard/standardproduct/matching`,
    method: 'post',
    data
  });
}

/**
 * 特殊送样
 * @param {Array<string>} ids 样品id集合
 * @returns
 */
export function specialSampleApi(ids) {
  return request({
    url: `/api-orders/orders/samples/special`,
    method: 'post',
    data: {
      isSpecial: '1',
      sampleId: ids
    }
  });
}

/**
 * 取消特殊送样
 * @param {Array<string>} ids 样品id集合
 * @returns
 */
export function cancelSpecialSampleApi(ids) {
  return request({
    url: `/api-orders/orders/samples/special`,
    method: 'post',
    data: {
      isSpecial: '0',
      sampleId: ids
    }
  });
}

/**
 * 从实验方案复制申请单并提交申请单
 */
export function copyAndSubmit(data) {
  return request({
    url: `/api-diplomat/diplomat/inspection/copyAndSubmit`,
    method: 'post',
    data
  });
}
