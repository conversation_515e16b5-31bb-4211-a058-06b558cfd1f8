.el-table th,
.el-table td {
  padding: 10px 0;
  box-sizing: border-box;
}
.el-table {
  padding: 0px !important;

  &::before {
    height: 0;
  }

  thead {
    //background: #f6f6f6;
    th {
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      color: #7b8791;
      //background: #f6f6f6;
      border-bottom-color: #ebeef5 !important;
    }
  }

  .el-table__body-wrapper {
    margin: 0px;
  }

  tr {
    td {
      border-color: #ebeef5 !important;
    }
  }
}
.el-table__body tr.hover-row > td,
.el-table__body tr.hover-row.current-row > td,
.el-table__body tr.hover-row.el-table__row--striped > td,
.el-table__body tr.hover-row.el-table__row--striped.current-row > td,
.el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: $tes-primary2 !important;
}
.el-table--border th {
  height: 3rem !important;
}

.el-table--border {
  border: none !important;
}

// 表格最外层边框-底部边框
.el-table--border::after,
.el-table--group::after {
  width: 0 !important;
}

.el-table__body td.fixed-right .cell {
  display: flex;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: center;
  .blue-color:last-of-type {
    margin-right: 0;
  }
}
.el-table__fixed-right::before,
.el-table__fixed::before {
  height: 0 !important;
}

.el-table--border {
  padding-bottom: 30px;
  box-sizing: border-box;
  .el-table__fixed-right {
    // background-color: $background-color;
    height: 100% !important;
  }
  .el-table__body-wrapper.is-scrolling-left > .el-table__fixed-right {
    height: calc(100% - 50px) !important;
  }
  .el-table__fixed-right-patch {
    background: #f5f7fa;
  }
  &::after {
    width: 0 !important;
  }
}

.el-table::before {
  height: 0 !important;
}

.el-table__body-wrapper {
  margin-bottom: 20px;
}

.el-table th.is-leaf {
  border-top: 1px solid #f6f6f6;
}
.el-table--border td {
  border-right: 0 !important;
}

//默认表格滚动高度，特殊页面需要根据实际情况，在单页中覆盖滚动高度
.el-table {
  .el-table__body-wrapper {
    max-height: calc(100vh - 330px);
    overflow-y: auto;
  }
  .el-table__fixed-body-wrapper {
    max-height: calc(100vh - 330px);
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  .el-table__fixed-body-wrapper::-webkit-scrollbar {
    display: none;
  }
  .el-table__fixed-right-patch {
    border-bottom-color: transparent;
  }
}
.format-height-table.el-table {
  .el-table__body-wrapper {
    max-height: calc(100vh - 330px);
  }
}

.drawer-height-table.el-table {
  .el-table__body-wrapper {
    max-height: calc(100vh - 550px);
  }
  .el-table__fixed-body-wrapper {
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    max-height: calc(100vh - 550px);
  }
}

// 列表页表格高度：无快捷搜索区域时，需加类名no-quick-query
.format-height-table.no-quick-query.el-table {
  .el-table__body-wrapper {
    max-height: calc(100vh - 270px);
  }
  .el-table__fixed-body-wrapper {
    max-height: calc(100vh - 270px);
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
}

.dark-table.el-table {
  color: #303133;
  width: 100%;
  thead {
    th {
      font-weight: 500;
      border: 0;
      color: #606266;
      box-shadow: inset 0px -1px 0px #ebeef5;
      background: #f5f7fa;
    }
  }
}

// 悬浮表格上显示滚动条
.el-table:hover {
  ::-webkit-scrollbar-thumb {
    background: #d4d7de;
  }
}

.el-table--hidden {
  visibility: hidden;
}

.el-table__body-wrapper,
.el-table__footer-wrapper,
.el-table__header-wrapper {
  width: 100%;
}

.el-table {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.el-table__body-wrapper {
  flex: 1 1 0%;
  overflow: hidden;
  position: relative;
}

.el-table__fixed-right {
  display: flex;
}

.el-table__fixed-body-wrapper {
  flex: 1 1 0%;
  overflow: hidden;
}
