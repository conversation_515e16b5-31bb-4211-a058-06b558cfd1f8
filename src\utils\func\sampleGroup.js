// 处理试样分组的相关方法

/**
 * 根据试样颜色下标获取指定颜色的id
 * @param {*} sampleColor
 * @returns
 */
export function getCoreColorId(coreColourSuffix, coreColourList) {
  let sampleColorId = '';
  coreColourList.forEach(item => {
    if (item.coreColourSuffix === coreColourSuffix) {
      sampleColorId = item.id;
    }
  });
  return sampleColorId;
}
/**
 * 根据试样颜色下标获取指定颜色的id
 * @param {*} sampleColor
 * @returns
 */
export function getCoreColorByNameId(coreColour, coreColourList) {
  let sampleColorId = '';
  coreColourList.forEach(item => {
    if (item.coreColour === coreColour) {
      sampleColorId = item.id;
    }
  });
  return sampleColorId;
}
/**
 * 根据试样颜色下标获取指定颜色的id
 * @param {*} sampleColor
 * @returns
 */
export function getHaveSaved(coreColourSuffix, coreColourList) {
  const isHave = coreColourList.some(item => {
    return item.coreColourSuffix === coreColourSuffix;
  });
  return isHave;
}

/**
 * 获取试样颜色列表
 * @param {*} coreColourList
 * @returns
 */
export function getSampleColorList(coreColourList) {
  const sampleColorList = [];
  coreColourList.forEach(item => {
    sampleColorList.push(item.coreColour);
  });
  return sampleColorList;
}

/**
 * 获取试样下标列表
 * @param {*} coreColourList
 * @returns
 */
export function getSampleSuffixList(coreColourList) {
  const sampleSuffixList = [];
  coreColourList.forEach(item => {
    sampleSuffixList.push(item.coreColourSuffix);
  });
  return sampleSuffixList;
}
/**
 * 获取试样分组其他参数列表
 * @param {*} coreColourList
 * @returns
 */
export function getSampleGroupMapList(coreColourList) {
  const groupMapList = [];
  coreColourList.forEach(item => {
    groupMapList.push(item.groupMap);
  });
  return groupMapList;
}
