import request from '@/utils/request';

// 关键设备-列表
export function deviceListKey() {
  return request({
    url: '/api-device/device/devicePlan/listKey',
    method: 'get'
  });
}

// 关键设备-列表
export function deviceSaveKey(data) {
  return request({
    url: '/api-device/device/devicePlan/saveKey',
    method: 'post',
    data
  });
}
// 使用计划展示
export function devicePlanList(data) {
  return request({
    url: '/api-device/device/devicePlan/show',
    method: 'get',
    params: data
  });
}
// 使用计划展示
export function deviceDeleteKey(data) {
  return request({
    url: '/api-device/device/devicePlan/deleteKey',
    method: 'post',
    data
  });
}
// 保存使用计划
export function devicePlanSave(data) {
  return request({
    url: '/api-device/device/devicePlan/save',
    method: 'post',
    data
  });
}
// 保存使用计划
export function deleteDevicePlan(data) {
  return request({
    url: '/api-device/device/devicePlan/delete',
    method: 'post',
    data
  });
}
// 编辑使用计划
export function devicePlanEdit(data) {
  return request({
    url: '/api-device/device/devicePlan/update',
    method: 'post',
    data
  });
}
// 重点设备顺序调整
export function updateKeyOrder(data) {
  return request({
    url: '/api-device/device/devicePlan/updateKeyOrder',
    method: 'post',
    data
  });
}
