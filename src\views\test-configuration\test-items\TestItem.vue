<template>
  <!-- 试验配置 - 检测项目列表 -->
  <ListLayout
    class="test-item"
    :has-page-header="false"
    :has-quick-query="false"
    :has-left-panel="true"
    :aside-max-width="520"
    :aside-panel-width="300"
  >
    <template #page-left-side>
      <div class="tree-container">
        <div class="tree-header">
          <div class="header-select">
            <span class="icon el-icon-menu" />
            <el-select
              v-model="otherForm.materialCode"
              filterable
              size="small"
              class="topSelect"
              placeholder="请选择物资分类"
              @change="clickMaterial"
            >
              <el-option v-for="val in otherForm.tabsData" :key="val.value" :label="val.name" :value="val.code" />
            </el-select>
          </div>
          <div class="header-input-button">
            <el-input
              v-model="otherForm.filterText"
              size="small"
              placeholder="请输入类目名称"
              prefix-icon="el-icon-search"
              clearable
            />
            <el-button
              v-if="getPermissionBtn('itemTreeAdd')"
              class="addTreeBtn"
              size="small"
              icon="el-icon-plus"
              @click="addTreeItem"
              @keyup.prevent
              @keydown.enter.prevent
            />
          </div>
        </div>
        <div class="tree-content">
          <el-tree
            ref="tree"
            :data="otherForm.treeData"
            node-key="id"
            :props="otherForm.defaultProps"
            :expand-on-click-node="false"
            highlight-current
            draggable
            :allow-drop="allowDrop"
            :filter-node-method="filterNode"
            :current-node-key="otherForm.currentNodeKey"
            class="leftTree"
            @node-drop="nodeDrop"
            @filter="filterNode"
            @node-click="clickNode"
          >
            <template #default="{ node, data }">
              <span>{{ node.label }}</span>
              <el-dropdown
                v-if="data.id !== '0'"
                trigger="hover"
                class="tree-dropdown el-icon"
                :class="node.showIcon ? 'icon-show' : ''"
                @visible-change="changeIcon(node.showIcon, node)"
              >
                <i class="el-icon-more" />
                <template v-if="getPermissionBtn('itemTreeAdd')" #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="getPermissionBtn('editTestItemTreeBtn')" @click="editTree(node.data, node)"
                      ><i class="iconfont tes-edit" />编辑</el-dropdown-item
                    >
                    <!-- :disabled="node.data.status === 2" 删除按钮不用禁用，以红色字显示就可以 -->
                    <el-dropdown-item
                      v-if="getPermissionBtn('delTestItemTreeBtn')"
                      class="color-red"
                      @click="delTree(node.data)"
                      ><i class="iconfont tes-delete" />删除</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-tree>
        </div>
      </div>
    </template>
    <el-form ref="editFrom" :inline="true" :model="formInline" class="test-item-form" @submit.prevent>
      <el-row>
        <el-col :span="20">
          <el-form-item prop="name" class="from-name" style="width: 100%; margin-bottom: 0">
            <el-input
              v-model="formInline.name"
              v-trim
              v-focus
              clearable
              placeholder="请输入项目名称搜索"
              size="small"
              prefix-icon="el-icon-search"
              style="width: 360px"
              @keyup.enter="onSubmit"
            />
            <el-button type="primary" size="small" style="margin-left: 10px" @click="onSubmit">查询</el-button>
            <el-button size="small" @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item v-if="getPermissionBtn('itemAdd')" style="float: right">
            <el-button
              type="primary"
              size="small"
              icon="el-icon-plus"
              @click="addItem"
              @keyup.prevent
              @keydown.enter.prevent
              >新增项目</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item class="test-capability" style="margin: 0">
        <el-form-item label="项目等级：">
          <el-checkbox-group
            v-model="otherForm.testcapability"
            style="width: 400px; float: left"
            @change="changeTestcapability"
          >
            <el-checkbox v-for="(val, key) in otherForm.dictionaryJSON['XMDJ'].enable" :key="key" :label="key">{{
              val
            }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item style="float: right">
          <el-dropdown style="float: right; display: none" @command="handleCommand">
            <div class="filter-btn"><i class="iconfont tes-filter-select" /></div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="medium">默认</el-dropdown-item>
                <el-dropdown-item command="small">中等</el-dropdown-item>
                <el-dropdown-item command="mini">紧凑</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-form-item>
      </el-form-item>
    </el-form>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="otherForm.list"
      fit
      border
      height="auto"
      :size="otherForm.tableSize"
      highlight-current-row
      class="dark-table test-item-table base-table"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="expand" :width="colWidth.expand">
        <template #default="{ row }">
          <div
            v-if="row.list.length"
            :style="{
              marginLeft: '47px'
            }"
          >
            <el-tag
              v-for="(item, index) in row.list"
              :key="index"
              size="mini"
              type="info"
              effect="dark"
              style="margin-right: 8px"
              >{{ item.name }}</el-tag
            >
          </div>
          <div v-else style="line-height: 20px; text-align: center; color: #aaa">暂无数据</div>
        </template>
      </el-table-column>
      <el-table-column label="项目编号" prop="number" :width="colWidth.name" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <div class="blue-color" @click="handleDetail(row)">{{ row.number || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="项目名称" prop="name" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目描述" prop="description" :min-width="colWidth.description" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.remark ? row.remark : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目等级" prop="testcapability" :width="colWidth.level">
        <template #default="{ row }">
          <div v-if="row.testcapability && JSON.parse(row.testcapability).length > 0">
            <span v-for="item in JSON.parse(row.testcapability)" :key="item">{{
              otherForm.dictionaryJSON['XMDJ'].all[item] || '--'
            }}</span>
          </div>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" :width="colWidth.status">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="row.status == 1 ? 'success' : 'info'">
            {{ row.status == 1 ? '已启用' : '已停用' }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="模板版本号" prop="templateVersion" :width="colWidth.version">
        <template #default="{ row }">
          {{ row.templateVersion || '--' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="140" prop="caozuo" class-name="fixed-right" fixed="right">
        <template #default="scope">
          <span v-if="getPermissionBtn('templateDetail')" class="blue-color" @click="handleExcel(scope.row)">模板</span>
          <span class="blue-color" @click="handleDetail(scope.row)">查看</span>
          <!-- <span class="blue-color" @click="handleEdit(scope.row)">编辑</span> -->
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getCapabilityLists"
    />
    <template #other>
      <add
        :drawer="drawer"
        :title="addTitle"
        :tree="otherForm.newTree"
        :edit-data="otherForm.editData"
        :category-ids="otherForm.currentCategoryids"
        :dictionary="otherForm.dictionaryJSON"
        @close="closeDrawer"
      />
      <!-- :item-level="otherForm.itemLevel"
        :seal-scope-of="otherForm.sealScopeOf" -->
      <!-- 新增检测项目树弹出框 -->
      <el-dialog
        v-model="showEditDialog"
        :title="otherForm.isAddTree === true ? '新增类目' : '编辑类目'"
        width="480px"
        :close-on-click-modal="false"
      >
        <el-form ref="formTree" :model="dialogFrom" :rules="otherForm.dialogRules" label-position="right">
          <el-form-item label="类目名称：" prop="name" :label-width="formLabelWidth" style="margin-bottom: 20px">
            <el-input
              v-model="dialogFrom.name"
              autocomplete="off"
              maxlength="50"
              :input="(dialogFrom.name = dialogFrom.name.replace(/\s+/g, ''))"
              placeholder="请输入类目名称"
            />
          </el-form-item>
          <el-form-item label="父级分类：" :label-width="formLabelWidth">
            <el-cascader
              v-model="otherForm.category"
              :options="otherForm.dialogTreeData"
              :props="otherForm.categoryProps"
              clearable
              style="width: 100%"
              @change="changeCategory"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="closeTreeDialog">取 消</el-button>
            <el-button type="primary" @click="editDialogSuccess">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <detail
        :drawer="otherForm.detaildrawer"
        :test-item-list="otherForm.list"
        title="项目详情"
        :dictionary="otherForm.dictionaryJSON"
        :detail-data="detialData.data"
        @close="closeDeatilDrawer"
      />
      <!-- :item-level="otherForm.itemLevel"
        :seal-scope-of="otherForm.sealScopeOf" -->
    </template>
  </ListLayout>
</template>
<script>
import { reactive, ref, watch, getCurrentInstance } from 'vue';
import { getPermissionBtn } from '@/utils/common';
import Pagination from '@/components/Pagination';
import {
  formatTree,
  formatAllTree,
  formatTestcapabilityByValue,
  formatTreeByIds,
  formatTreeByNames
} from '@/utils/formatJson';
import Add from './add';
import {
  getCapabilityList,
  getCapabilityTree,
  getCapabilityInfo,
  deleteCapabilitycategory,
  updateCapabilitycategory,
  saveCapabilitycategory,
  getCapabilityById,
  updateOrderCategory
} from '@/api/user';
import { getDictionary } from '@/api/user';
import Detail from './detail';
import ListLayout from '@/components/ListLayout';
import { ElMessage, ElMessageBox } from 'element-plus';
import router from '@/router';
import { useStore } from 'vuex';
import { drageHeader } from '@/utils/formatTable';
import _ from 'lodash';
import { colWidth } from '@/data/tableStyle';
import { getLoginInfo } from '@/utils/auth';
import { useRoute } from 'vue-router';

export default {
  name: 'TestItem',
  components: { Pagination, Add, Detail, ListLayout },
  setup(props, context) {
    const route = useRoute();
    const { proxy } = getCurrentInstance();
    const store = useStore();
    const formInline = reactive({
      name: '',
      cestCapability: [],
      categoryId: '',
      materialCategoryCode: '0'
    });
    const addTitle = ref('新增项目');
    const editFrom = ref(null);
    const activeName = ref('0');

    const otherForm = reactive({
      tableList: [],
      // itemLevel: {},
      dictionaryJSON: {
        // 字典集合
        JCGF: {
          enable: {},
          all: {}
        },
        XMDJ: {
          enable: {},
          all: {}
        },
        GZFW: {
          enable: {},
          all: {}
        }
      },
      // sealScopeOf: {},
      materialCode:
        getLoginInfo().tenantId === route.query.tenantId
          ? route.query.code || store.state.user.materialList[0]?.code
          : store.state.user.materialList[0]?.code,
      list: [],
      content: '',
      treeData: [],
      dialogTreeData: [],
      editData: {},
      newTree: [],
      treeTitle: '', // 选中树节点的name
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      category: [],
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      menuVisible: false,
      activeIndex: '0',
      activeMoreIndex: null,
      tabsData: store.state.user.materialList,
      tabsMoreData: [],
      moreIndex: 0,
      currentTabsData: store.state.user.materialList[0],
      filterText: '',
      isAddTree: true,
      dialogRules: {
        name: [{ required: true, message: '请输入类目名称' }],
        parentid: [{ required: true, message: '请选择父级目录' }]
      },
      testcapability: [],
      asideWidth: 240,
      tableSize: 'medium',
      showIcon: false,
      currentNodeKey: '',
      currentCategoryids: [],
      detaildrawer: false
    });
    const total = ref(otherForm.tableList.length);
    const listLoading = ref(false);
    const tableKey = ref(0);
    const listQuery = reactive({
      page: 1,
      limit: 20,
      orderBy: '',
      isAsc: false
    });

    function reset() {
      editFrom.value.resetFields();
      otherForm.testcapability = [];
      formInline.cestCapability = '';
      listQuery.page = 1;
      listQuery.limit = 20;
      listQuery.orderBy = '';
      listQuery.isAsc = false;
      proxy.getCapabilityLists();
    }
    const sortChange = data => {
      const { prop, order } = data;
      listQuery.orderBy = prop;
      if (order === 'ascending') {
        listQuery.isAsc = true;
      } else if (order === 'descending') {
        listQuery.isAsc = false;
      } else {
        listQuery.isAsc = null;
      }
    };

    const handleSelectionChange = val => {};

    const statusFilterName = status => {
      const statusNames = {
        0: 'success',
        1: 'info'
      };
      return statusNames[status];
    };

    const inputValue = data => {
      if (typeof data === 'string') {
        otherForm.content = data;
      } else {
        otherForm.content = '';
      }
    };

    // 树的 鼠标右击事件
    const rightClick = (MouseEvent, object, node, element) => {
      otherForm.menuVisible = true;
    };
    // 鼠标hover到树节点
    const mouseover = () => {
      otherForm.showIcon = true;
    };
    const mouseleave = () => {
      otherForm.showIcon = false;
    };
    // 树节点编辑
    const showEditDialog = ref(false);
    const formLabelWidth = ref('90px');
    const dialogFrom = ref({
      name: '',
      parentid: '',
      materialCategoryCode: otherForm.currentTabsData ? otherForm.currentTabsData.code : '',
      materialCategoryId: otherForm.currentTabsData ? otherForm.currentTabsData.id : ''
    });
    const editTree = (data, node) => {
      otherForm.dialogTreeData = formatAllTree(data.id, otherForm.dialogTreeData);
      showEditDialog.value = true;
      otherForm.isAddTree = false;
      dialogFrom.value = data;
      otherForm.category = formatTreeByIds(node.parent);
    };
    // 保存树节点
    const formTree = ref(null);
    const editDialogSuccess = () => {
      formTree.value.validate(valid => {
        if (valid) {
          if (otherForm.isAddTree !== true) {
            updateCapabilitycategory(dialogFrom.value).then(function (res) {
              if (res !== false && res.data.code === 200) {
                ElMessage.success('编辑成功!');
                showEditDialog.value = false;
              }
              proxy.getCapabilityTrees();
            });
          } else {
            saveCapabilitycategory(dialogFrom.value).then(function (res) {
              if (res !== false && res.data.code === 200) {
                ElMessage.success('新增成功!');
                proxy.getCapabilityTrees();
                showEditDialog.value = false;
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    // 关闭树的新增编辑的弹出框
    const closeTreeDialog = () => {
      showEditDialog.value = false;
      dialogFrom.value = {
        name: '',
        parentid: '',
        materialCategoryCode: otherForm.currentTabsData.code,
        materialCategoryId: otherForm.currentTabsData.id
      };
      if (formTree.value) {
        formTree.value.clearValidate();
      }
      proxy.getCapabilityTrees();
    };
    // 新增树节点
    const addTreeItem = () => {
      showEditDialog.value = true;
      dialogFrom.value = {
        name: '',
        parentid: '',
        materialCategoryCode: otherForm.currentTabsData.code,
        materialCategoryId: otherForm.currentTabsData.id
      };
      if (formTree.value) {
        formTree.value.clearValidate();
      }
      otherForm.category = [];
      otherForm.isAddTree = true;
    };
    // 所属分类change
    const changeCategory = value => {
      if (value) {
        const len = value.length - 1;
        dialogFrom.value.parentid = value[len];
      } else {
        dialogFrom.value.parentid = 0;
      }
    };
    // 树节点删除
    const delTree = node => {
      var ids = [];
      ids.push(node.id);
      ElMessageBox({
        title: '提示',
        message: '是否删除该类目?',
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(() => {
          deleteCapabilitycategory(ids).then(function (res) {
            if (res !== false) {
              ElMessage.success('删除成功!');
              proxy.getCapabilityTrees();
            }
          });
        })
        .catch(() => {});
    };

    // 新增项目
    const drawer = ref(false);
    const addItem = () => {
      if (otherForm.treeData.length === 0) {
        ElMessage.warning('请选择项目树');
        return false;
      }
      addTitle.value = '新增项目';
      otherForm.editData = {};
      drawer.value = true;
    };
    const closeDrawer = () => {
      drawer.value = false;
    };
    // 编辑项目
    const handleEdit = row => {
      addTitle.value = '编辑项目';
      otherForm.editData = row;
      drawer.value = true;
    };
    // 详情
    const detialData = reactive({ data: {} });
    const handleDetail = row => {
      getCapabilityById(row.id).then(function (res1) {
        if (res1 !== false) {
          const { data } = res1.data;
          // 获取关键参数列表
          getCapabilityInfo(row.id).then(function (res) {
            data.tableData = res.data.data;
            detialData.data = data;
            if (detialData.data.categoryIds) {
              detialData.data.categoryIds = detialData.data.categoryIds.reverse();
            } else {
              detialData.data.categoryIds = [];
            }
            detialData.data.apabilityTree = otherForm.treeData;
            otherForm.treeTitle = otherForm.treeData[0].name;
            detialData.data.currentMaterial = otherForm.currentTabsData;
            detialData.data.treelist = otherForm.treeData;
            otherForm.detaildrawer = true;
          });
        }
      });
    };
    // 跳转模板
    const handleExcel = row => {
      const capabilityId = row.id;
      const capabilityName = row.name;
      const number = row.number;
      router.push({
        path: '/experiment/experimentexcel',
        query: {
          capabilityId: capabilityId,
          capabilityName: capabilityName,
          capabilityNumber: number,
          code: otherForm.currentTabsData.code,
          categoryId: formInline.categoryId
        }
      });
    };
    const closeDeatilDrawer = () => {
      otherForm.detaildrawer = false;
    };
    const getDictionaryList = () => {
      Object.keys(otherForm.dictionaryJSON).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          otherForm.dictionaryJSON[item] = {
            enable: {},
            all: {}
          };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status === 1) {
              otherForm.dictionaryJSON[item].enable[optionItem.code] = optionItem.name;
            }
            otherForm.dictionaryJSON[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    getDictionaryList();
    // 检测项目filter
    const filterTestcapability = value => {
      return formatTestcapabilityByValue(value);
    };
    // 检测项目change
    const changeTestcapability = value => {
      formInline.cestCapability = value;
    };
    // 过滤树节点
    const tree = ref(null);
    watch(
      () => otherForm.filterText,
      newValue => {
        tree.value.filter(newValue);
      }
    );
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    // table 紧凑类型修改
    const handleCommand = command => {
      otherForm.tableSize = command;
      proxy.getCapabilityLists();
    };
    // 选择物资-更多里面的物资
    const clickMaterial = async val => {
      otherForm.currentTabsData = store.state.user.materialList.filter(item => item.code === val)[0];
      formInline.categoryId = '';
      otherForm.currentNodeKey = '';
      router.replace({ query: {} });
      await proxy.getCapabilityTrees();
      proxy.getCapabilityLists();
    };

    const changeIcon = (command, node) => {
      if (!command) {
        node.showIcon = true;
      } else {
        node.showIcon = !node.showIcon;
      }
    };
    // 允许拖拽功能 只能同级拖拽
    const allowDrop = (draggingNode, dropNode, type) => {
      if (draggingNode.level === dropNode.level) {
        // parentid是父节点id
        if (draggingNode.data.parentid === dropNode.data.parentid) {
          return type === 'prev' || type === 'next';
        }
      } else {
        // 不同级进行处理
        return false;
      }
    };
    // 树排序
    const nodeDrop = (before, after) => {
      var orderList = [];
      if (after.parent.level === 0) {
        orderList = JSON.parse(JSON.stringify(after.parent.data));
        _.remove(orderList, function (n) {
          return n.id === '0';
        });
        orderList.forEach((ol, index) => {
          ol.order = index + 1;
        });
      } else {
        orderList = after.parent.data.children;
        orderList.forEach((ol2, index) => {
          ol2.order = index + 1;
        });
      }
      updateOrderCategory(orderList).then(res => {
        if (res !== false) {
          ElMessage.success('排序成功');
        }
      });
    };

    return {
      getPermissionBtn,
      closeTreeDialog,
      drageHeader,
      changeIcon,
      clickMaterial,
      mouseleave,
      mouseover,
      handleCommand,
      formTree,
      getDictionaryList,
      changeTestcapability,
      changeCategory,
      addTreeItem,
      tree,
      filterNode,
      filterTestcapability,
      dialogFrom,
      editDialogSuccess,
      formLabelWidth,
      showEditDialog,
      delTree,
      editTree,
      addTitle,
      detialData,
      closeDeatilDrawer,
      closeDrawer,
      drawer,
      addItem,
      rightClick,
      inputValue,
      handleSelectionChange,
      statusFilterName,
      handleEdit,
      handleDetail,
      sortChange,
      tableKey,
      listLoading,
      total,
      listQuery,
      formInline,
      editFrom,
      otherForm,
      activeName,
      reset,
      handleExcel,
      allowDrop,
      nodeDrop,
      colWidth
    };
  },
  async created() {
    if (this.$route.query.code && getLoginInfo().tenantId === this.$route.query.tenantId) {
      this.formInline.categoryId = this.$route.query.categoryId;
      this.otherForm.materialCode = this.$route.query.code;
      this.formInline.materialCategoryCode = this.$route.query.code;
      this.otherForm.currentNodeKey = this.$route.query.categoryId;
      this.otherForm.currentTabsData = this.$store.state.user.materialList.filter(item => {
        return item.code === this.$route.query.code;
      })[0];
      this.otherForm.currentCategoryids = [this.$route.query.categoryId];
    }
    await this.getCapabilityTrees();
    this.getCapabilityLists();
    // 刷新列表
    this.bus.$on('reloadList', msg => {
      this.getCapabilityLists();
    });
    // 刷新详情
    this.bus.$on('reloadDetail', row => {
      this.handleDetail(row);
    });
  },
  methods: {
    getCapabilityLists(pdata) {
      const _this = this;
      _this.listLoading = true;
      if (pdata && pdata !== undefined) {
        _this.listQuery.page = pdata.page;
        _this.listQuery.limit = pdata.limit;
      }
      const param = Object.assign(_this.formInline, _this.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      getCapabilityList(param).then(response => {
        if (response !== false) {
          const data = response.data.data;
          _this.otherForm.list = data.list;
          _this.total = data.totalCount;
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
      //   }
      // })
    },
    getCapabilityTrees() {
      const _this = this;
      return new Promise(resolve => {
        if (_this.otherForm.currentTabsData) {
          getCapabilityTree(_this.otherForm.currentTabsData.code).then(response1 => {
            if (response1 !== false) {
              const data = response1.data.data;
              _this.otherForm.treeData = formatTree(data);
              _this.otherForm.newTree = formatTree(data);
              _this.otherForm.dialogTreeData = data;
              if (_this.otherForm.treeData.length === 0) {
                _this.listLoading = false;
                _this.otherForm.treeTitle = '';
                _this.otherForm.list = [];
                _this.total = 0;
                return false;
              }
              const allParam = {
                id: '0',
                name: '全部',
                materialCategoryCode: _this.otherForm.currentTabsData.code,
                categoryId: ''
              };
              _this.otherForm.treeData.unshift(allParam);
              if (_this.formInline.categoryId === '' && _this.otherForm.treeData[0].id === '0') {
                _this.formInline.categoryId = '';
                _this.formInline.materialCategoryCode = _this.otherForm.currentTabsData.code;
                _this.otherForm.treeTitle = _this.otherForm.treeData[0].name;
              }
              // 默认选中树节点
              if (_this.otherForm.currentNodeKey === '') {
                _this.otherForm.currentNodeKey = _this.otherForm.treeData[0].id;
                _this.otherForm.currentCategoryids = [_this.otherForm.treeData[0].id];
                _this.otherForm.treeTitle = _this.otherForm.treeData[0].name;
                _this.$nextTick(function () {
                  _this.$refs.tree.setCurrentKey(_this.otherForm.currentNodeKey);
                });
              } else {
                _this.$nextTick(function () {
                  _this.$refs.tree.setCurrentKey(_this.otherForm.currentNodeKey);
                });
              }
              resolve(true);
            } else {
              resolve(false);
            }
          });
        } else {
          resolve(false);
        }
      });
    },
    onSubmit() {
      this.getCapabilityLists();
    },
    clickNode(data, node) {
      if (data.id === '0') {
        this.formInline.categoryId = '';
        this.formInline.materialCategoryCode = data.materialCategoryCode;
        this.otherForm.currentNodeKey = data.id;
      } else {
        this.formInline.categoryId = data.id;
        this.otherForm.currentNodeKey = data.id;
      }
      router.replace({ query: {} });
      this.otherForm.treeTitle = formatTreeByNames(node)
        .filter(item => {
          return item;
        })
        .reverse()
        .join('/');
      this.otherForm.currentCategoryids = formatTreeByIds(node);
      this.getCapabilityLists();
    }
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
.tree-container {
  .tree-header {
    flex-direction: column;
    .header-select {
      display: flex;
      width: 100%;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 10px;
      :deep(.el-select .el-input__inner) {
        padding-left: 0;
        font-size: 16px;
        color: #303133;
        border: none;
      }
      .icon {
        font-size: 16px;
        margin-right: 10px;
      }
      .el-select {
        width: 100%;
      }
    }
    .header-input-button {
      display: flex;
      width: 100%;
      justify-content: flex-start;
      align-items: center;
    }
  }
  .tree-content {
    height: calc(100vh - 250px);
  }
}
.test-capability {
  width: 100%;
  margin: 0px;
  :deep(.el-form-item__content) {
    width: inherit;
  }
  .el-form-item {
    margin-bottom: 0;
  }
}
:deep(.el-form--inline .el-form-item) {
  margin-right: 0;
}
.test-item {
  .filter-btn {
    float: right;
    width: 32px;
    height: 32px;
    line-height: 32px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    text-align: center;
    color: #909399;
    cursor: pointer;
    i {
      font-size: 18px;
    }
  }
  .test-item-form {
    text-align: left;
    .el-form-item {
      :deep(.el-form-item__label) {
        color: #303133;
        font-size: 14px;
        font-weight: normal;
      }
    }
    .searchBtn {
      border: 0;
      background: none;
    }
    .from-name {
      width: 100%;
      :deep(.el-form-item__content) {
        width: 100%;
      }
      .el-col {
        padding-right: 10px;
      }
    }
  }
  .blue-color {
    color: $tes-primary;
    cursor: pointer;
  }
}
.test-item-table {
  :deep(.el-table__expanded-cell) {
    padding-top: 8px;
    padding-bottom: 8px;
  }
}
</style>
