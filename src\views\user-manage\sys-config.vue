<template>
  <div class="base-info">
    <div class="header">系统配置</div>
    <el-form ref="baseInfoRef" :model="baseInfoForm">
      <el-row>
        <el-col :span="11">
          <el-form-item
            label="用户名："
            prop="username"
            :rules="{ required: true, message: '请输入用户名', trigger: 'change' }"
          >
            <el-input v-model="baseInfoForm.username" size="small" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item
            label="姓名："
            prop="nickname"
            :rules="{ required: true, message: '请输入姓名', trigger: 'change' }"
            class="edit-info"
          >
            <el-input v-model.trim="baseInfoForm.nickname" maxlength="18" size="small" :disabled="!isEditNickName" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="手机：" class="edit-info" prop="mobile">
            <el-input v-model.trim="baseInfoForm.mobile" size="small" :disabled="!isEditMobile" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="邮箱：" class="edit-info" prop="email">
            <el-input v-model="baseInfoForm.email" size="small" :disabled="!isEditEmail" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import { reactive, toRefs } from 'vue';
// import { getNameByid } from '@/utils/common'
import { getLoginInfo } from '@/utils/auth';
// import _ from 'lodash'
// import { ElMessage } from 'element-plus'

export default {
  name: 'BaseInfo',
  components: {},
  setup() {
    // const { proxy } = getCurrentInstance()
    const datas = reactive({
      accountId: getLoginInfo().accountId,
      baseInfoForm: {}
    });
    // 保存
    const save = flag => {};
    // 取消
    const cancel = flag => {};
    // 返回重置
    const handleReset = () => {
      datas.baseInfoForm = JSON.parse(JSON.stringify(datas.oldBaseInfoForm));
    };

    return {
      ...toRefs(datas),
      handleReset,
      save,
      cancel
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.base-info {
  padding: 16px 24px;
  .qm {
    width: 100%;
    height: 140px;
    border: 1px solid #e4e7ed;
    position: relative;
    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
  .btnGroup {
    position: absolute;
    bottom: 20px;
    right: 25px;
  }
  .zxqm {
    position: absolute;
    right: 0;
    bottom: -40px;
  }
  .fr {
    float: right;
  }
  .header {
    width: 100%;
    height: 36px;
    line-height: 36px;
    text-align: left;
    font-size: 18px;
    font-weight: bold;
    margin: 0 0 6px 0;
  }
  :deep(.el-form-item--medium .el-form-item__label) {
    line-height: 32px;
  }
  :deep(.el-form .el-form-item) {
    margin-bottom: 22px;
  }
  .el-form {
    background: #fff;
    border-radius: 8px;
    padding: 25px 25px 200px 24px;
    position: relative;
    :deep(.el-form-item) {
      .el-form-item__label {
        width: 100%;
      }
      .el-form-item__content {
        .el-input {
          min-width: 200px;
          // width: calc(100% - 147px);
          float: left;
          height: 32px;
        }
        .el-button {
          float: left;
          margin-left: 15px;
        }
      }
    }
  }
  .electronic-signature {
    text-align: left;
    img {
      width: 300px;
      height: 150px;
      margin: 15px 0px;
      display: block;
    }
    .img-upload-input {
      display: none;
      z-index: -9999;
    }
    .sign-preview {
      border: 1px solid #e4e7ed;
    }
  }
}
</style>
