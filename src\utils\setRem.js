import { setScale } from '@/utils/auth';

export function setRem(baseWidth = 1440) {
  const dpr = window.devicePixelRatio;
  const currentWidth = document.documentElement.clientWidth;
  let remSize = 0;
  let scale = 2;
  scale = currentWidth / baseWidth;
  remSize = baseWidth / 120;
  remSize = remSize * scale;
  document.documentElement.style.fontSize = remSize + 'px';
  document.documentElement.setAttribute('data-dpr', `${dpr}`);
  // console.log(dpr, scale, remSize)
}

export function setRem1(currentFontSize) {
  // 相对于1920像素的缩放比
  // 基准值
  const baseSize = currentFontSize || 14;
  var currentSize = currentFontSize || 14;
  const currentWidth = document.documentElement.clientWidth;
  const scale = currentWidth / 1920;
  var baseScale = 1;
  if (currentWidth <= 1044) {
    baseScale = Math.min(Math.max(scale, 0.7), 1);
    currentSize = baseSize * baseScale;
  } else if (currentWidth > 1920) {
    baseScale = Math.min(Math.max(scale, 0.9), 2);
    currentSize = baseSize * baseScale;
  }
  document.documentElement.style.fontSize = currentSize + 'px';
  setScale(baseScale);
  // console.log('css-to-rem', currentWidth, baseScale, currentSize)
}
