import request from '@/utils/request';
import qs from 'qs';

// const postHeaders = {
//   'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
// }
const Headers = {
  'Content-Type': 'application/json; charset=utf-8'
};
const baseUrl = 'http://*************:9100';

// 不良品处置列表
export function getDispotitionList(data) {
  return request({
    url: '/api-defectiveproduct/defectiveproduct/defectiveproduct/list',
    method: 'post',
    data
  });
}
// 不良品流程审批历史记录
export function getHistoryList(processInstanceId) {
  return request({
    url: `/api-defectiveproduct/defectiveproduct/defectiveproduct/processHistory/${processInstanceId}`,
    method: 'get'
  });
}
// 上传图片
export function uploadReportImg(data, callback) {
  return request({
    url: '/api-defectiveproduct/defectiveproduct/defectiveimage/upload',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: function (progressEvent) {
      // 原生获取上传进度的事件
      // console.log(progressEvent)
      callback(progressEvent);
    },
    data
  });
}
// 图片删除
export function delReportImgList(data) {
  return request({
    url: '/api-defectiveproduct/defectiveproduct/defectiveimage/delete',
    method: 'post',
    data
  });
}
// 根据id查询处置单信息
export function defectiveProductInfo(id) {
  return request({
    url: `/api-defectiveproduct/defectiveproduct/defectiveproduct/info/${id}`,
    method: 'get'
  });
}
// 根据处置单号查询处置单信息
export function getInfoByDisposalNumber(disposalNumber) {
  return request({
    url: `/api-defectiveproduct/defectiveproduct/defectiveproduct/getInfoByDisposalNumber/${disposalNumber}`,
    method: 'post'
  });
}
// 保存不良品处置单信息
export function saveDefectiveProduct(data) {
  return request({
    url: '/api-defectiveproduct/defectiveproduct/defectiveproduct/save',
    method: 'post',
    data
  });
}
// 查询流程审批历史记录
export function getProcessHistoryInfo(processInstanceId) {
  return request({
    url: `/api-defectiveproduct/defectiveproduct/defectiveproduct/processHistory/${processInstanceId}`,
    method: 'get'
  });
}
// 执行流程审批
export function processExecute(data) {
  return request({
    url: '/api-defectiveproduct/defectiveproduct/defectiveproduct/processExecute',
    method: 'post',
    data
  });
}
// 检验单校验
export function checkNo(no) {
  return request({
    url: `/api-defectiveproduct/defectiveproduct/defectiveproduct/checkNo/${no}`,
    method: 'get'
  });
}
// 不合格报告查询
export function findUnqualifiedReport(data) {
  return request({
    url: `/api-orders/orders/report/findUnqualifiedReport`,
    method: 'post',
    data
  });
}
// 根据报告Id获取报告的检测项目和检测结果
export function findReportCapability(reportId) {
  return request({
    url: `/api-orders/orders/reportdetailexpinfo/findReportCapability/${reportId}`,
    method: 'get'
  });
}
/**
 * lims接口
 */

// 检验单 http://*************:9100/diplomat/inspection/218228103816740869
export function getInspection(id) {
  return request({
    url: baseUrl + '/diplomat/inspection/' + id,
    method: 'post',
    headers: Headers
  });
}
// http://*************:9100/diplomat/inspection?limit=4&searchText=&inspectionIds=212365821765287941&inspectionIds=217903210801725445&inspectionIds=217898771579731973&inspectionIds=217903210801725445
export function getInspectionList(param) {
  // param = qs.stringify(param, { indices: false })
  return request({
    url: baseUrl + '/diplomat/inspection/',
    method: 'get',
    headers: Headers,
    params: param,
    paramsSerializer: params => {
      return qs.stringify(params, { indices: false });
    }
  });
}
// 检测报告 http://*************:9100/report/report?limit=10&offset=0&assignedToMe=false&sourceType=inspection&orderBy%5BcreateDateTime%5D=desc
export function getReportList(params) {
  return request({
    url: baseUrl + '/report/report',
    method: 'get',
    headers: Headers,
    params: params
  });
}
// 样品单位 http://*************:9100/dictionary/DictionaryOption?limit=100&dictionaryId=5
export function getSampleUnit() {
  const params = {
    limit: 10000,
    dictionaryId: 5
  };
  return request({
    url: baseUrl + '/dictionary/DictionaryOption',
    method: 'get',
    headers: Headers,
    params: params
  });
}
// 处置方式 http://*************:9100/dictionary/DictionaryOption?limit=100&dictionaryId=23
export function getDisposalWay() {
  const params = {
    limit: 10000,
    dictionaryId: 23
  };
  return request({
    url: baseUrl + '/dictionary/DictionaryOption',
    method: 'get',
    headers: Headers,
    params: params
  });
}
// 关键参数 http://*************:9100/report/ReportDetailExpInfo?limit=10000&offset=0&reportId=632615171595964416
export function getKeyWords(reportId) {
  const params = {
    limit: 1000000,
    offset: 0,
    reportId: reportId
  };
  return request({
    url: baseUrl + '/report/ReportDetailExpInfo',
    method: 'get',
    headers: Headers,
    params: params
  });
}
