import { saveSysLoginLog } from '@/api/login-log';
import { heartApi } from '@/api/sysConfig';
import { setHasWebSocket } from '@/utils/auth';
import { loginOutApi } from '@/api/sysConfig';
import Watermark from '@/utils/watermark';
import { getLoginId, setLoginId } from '@/utils/auth';
import store from '@/store';

/**
 * 登录日志-记录
 * @returns
 */
export async function setCurrentIp() {
  const currentIP = localStorage.getItem('currentIp');
  const param = {
    ie: navigator.userAgent,
    address: currentIP
  };
  const response = await saveSysLoginLog(param);
  setLoginId(response.data.data);
  await loginFirstHeartbeat();
  return response;
}

/**
 * 登录日志-第一次心跳记录
 */
export async function loginFirstHeartbeat() {
  const loginId = getLoginId();
  await heartApi(loginId);
}

/**
 * 登出处理流程
 */
export async function logoutProcess(websocket) {
  if (websocket) {
    setHasWebSocket(false);
    websocket.close();
  }
  Watermark.remove();
  await loginOut();
}

export const loginOut = async () => {
  const loginId = getLoginId();
  if (loginId) {
    const result = await loginOutApi(loginId);
    if (result) {
      setLoginId('');
    }
  }
  const resetToken = await store.dispatch('user/resetCurrentToken');
  return resetToken;
};
