<template>
  <!-- 近30天用户登录情况 -->
  <div class="PersonalTask">
    <div class="top-agency">
      <div class="title">个人任务统计</div>
    </div>
    <div class="statistics-list-main mt-10">
      <el-collapse v-model="activeAccordion" accordion>
        <el-collapse-item name="1">
          <template #title>
            <div class="statistics-list">
              <div class="left">
                <span class="icon iconfont tes-todo num-bg1" />
                <span class="title">今日新增待办</span>
              </div>
              <div v-if="statisticsData.todayNewNum === 0" class="right">
                <span class="no-data">暂无</span>
              </div>
              <div v-else class="right">
                <span class="number">{{ statisticsData.todayNewNum }}</span>
                <span class="txt">项</span>
              </div>
            </div>
          </template>
          <div class="agency-list" @scroll="scrollEventToday">
            <div v-if="statisticsData.todayNewNum === 0" class="no-data">
              <img src="@/assets/img/empty-data.png" alt="no-data" />
            </div>
            <div
              v-for="item in statisticsData.todayNewList"
              v-else
              :key="item"
              class="content-list"
              @click="handleDispose(item)"
            >
              <div class="flag-main">{{ item.content }}</div>
              <div class="flag-content">
                <div class="flag-tags">
                  <el-tag size="small" class="tag-user">
                    <span class="icon el-icon-user-solid" />
                    <span class="name ellipsis"> {{ item.senderName }}</span>
                  </el-tag>
                </div>
                <div class="flag-time">{{ formatDate(item.createTime) }}</div>
              </div>
            </div>
          </div>
        </el-collapse-item>
        <el-collapse-item name="2">
          <template #title>
            <div class="statistics-list">
              <div class="left">
                <span class="icon iconfont tes-todo num-bg2" />
                <span class="title">即将超期待办</span>
              </div>
              <div v-if="statisticsData.nearOverdueNum === 0" class="right">
                <span class="no-data">暂无</span>
              </div>
              <div v-else class="right">
                <span class="number">{{ statisticsData.nearOverdueNum }}</span>
                <span class="txt">项</span>
              </div>
            </div>
          </template>
          <div class="agency-list" @scroll="scrollEventNearOver">
            <div v-if="statisticsData.nearOverdueNum === 0" class="no-data">
              <img src="@/assets/img/empty-data.png" alt="no-data" />
            </div>
            <div
              v-for="item in statisticsData.nearOverdueList"
              v-else
              :key="item"
              class="content-list"
              @click="handleDispose(item)"
            >
              <div class="flag-main">{{ item.content }}</div>
              <div class="flag-content">
                <div class="flag-tags">
                  <el-tag size="small" class="tag-user">
                    <span class="icon el-icon-user-solid" />
                    <span class="name ellipsis"> {{ item.senderName }}</span>
                  </el-tag>
                </div>
                <div class="flag-time">{{ formatDate(item.createTime) }}</div>
              </div>
            </div>
          </div>
        </el-collapse-item>
        <el-collapse-item name="3">
          <template #title>
            <div class="statistics-list">
              <div class="left">
                <span class="icon iconfont tes-todo num-bg3" />
                <span class="title">已超期待办</span>
              </div>
              <div v-if="statisticsData.overdueNum === 0" class="right">
                <span class="no-data">暂无</span>
              </div>
              <div v-else class="right">
                <span class="number">{{ statisticsData.overdueNum }}</span>
                <span class="txt">项</span>
              </div>
            </div>
          </template>
          <div class="agency-list" @scroll="scrollEventOverdue">
            <div v-if="statisticsData.overdueNum === 0" class="no-data">
              <img src="@/assets/img/empty-data.png" alt="no-data" />
            </div>
            <div
              v-for="item in statisticsData.overdueList"
              v-else
              :key="item"
              class="content-list"
              @click="handleDispose(item)"
            >
              <div class="flag-main">{{ item.content }}</div>
              <div class="flag-content">
                <div class="flag-tags">
                  <el-tag size="small" class="tag-user">
                    <span class="icon el-icon-user-solid" />
                    <span class="name ellipsis"> {{ item.senderName }}</span>
                  </el-tag>
                </div>
                <div class="flag-time">{{ formatDate(item.createTime) }}</div>
              </div>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script>
import { reactive, toRefs } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getStatistics } from '@/api/home';

export default {
  name: 'PersonalTask',
  setup(props, context) {
    const state = reactive({
      activeAccordion: '',
      listQueryTodayAdd: {
        page: 1,
        limit: 20
      },
      listQueryWill: {
        page: 1,
        limit: 20
      },
      listQueryAlready: {
        page: 1,
        limit: 20
      },
      statisticsData: {
        overdueNum: 0,
        nearOverdueNum: 0,
        todayNewNum: 0,
        todayNewList: [],
        overdueList: [],
        nearOverdueList: []
      }
    });
    // 已超期待办
    const scrollEventOverdue = e => {
      if (e.srcElement.scrollTop + e.srcElement.clientHeight === e.srcElement.scrollHeight) {
        if (state.statisticsData.overdueList.length < state.statisticsData.overdueNum) {
          state.listQueryAlready.page += 1;
          getDetail('overdueList', state.listQueryAlready);
        }
      }
    };
    // 今日新增待办
    const scrollEventToday = e => {
      if (e.srcElement.scrollTop + e.srcElement.clientHeight === e.srcElement.scrollHeight) {
        if (state.statisticsData.todayNewList.length < state.statisticsData.todayNewNum) {
          state.listQueryTodayAdd.page += 1;
          getDetail('todayNewList', state.listQueryTodayAdd);
        }
      }
    };
    // 即将超期待办
    const scrollEventNearOver = e => {
      if (e.srcElement.scrollTop + e.srcElement.clientHeight === e.srcElement.scrollHeight) {
        if (state.statisticsData.nearOverdueList.length < state.statisticsData.nearOverdueNum) {
          state.listQueryWill.page += 1;
          getDetail('nearOverdueList', state.listQueryWill);
        }
      }
    };
    // 个人任务统计接口
    const getDetail = (type, page) => {
      let params = {};
      if (!type) {
        params = { limit: 20, page: 1 };
      } else {
        params = page;
      }
      getStatistics(params).then(res => {
        if (res) {
          if (!type) {
            state.statisticsData = res.data.data;
          } else {
            state.statisticsData[type] = [...state.statisticsData[type], ...res.data.data[type]];
          }
        }
      });
    };
    getDetail();
    return {
      ...toRefs(state),
      getDetail,
      scrollEventOverdue,
      scrollEventNearOver,
      scrollEventToday,
      formatDate
    };
  }
};
</script>
<style lang="scss" scoped>
.PersonalTask {
  :deep(.el-collapse) {
    border: none;
    border-radius: 4px;
    .el-collapse-item__header {
      padding: 0;
      font-weight: normal;
      border-bottom-color: transparent;
    }
    .el-collapse-item__wrap {
      border: none;
      overflow: auto;
      .el-collapse-item__content {
        padding: 0;
        .agency-list {
          max-height: calc(100vh - 400px);
          width: 100%;
          overflow: auto;
          padding: 10px;
          .no-data {
            font-size: 16px;
            color: $tes-font3;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .content-list {
            cursor: pointer;
            border: 1px solid transparent;
            box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
            padding: 10px;
            border-radius: 4px;
            &:hover {
              border-color: $tes-primary1;
            }
            &:not(:last-of-type) {
              margin-bottom: 10px;
            }
            .flag-main {
              text-align: left;
              color: $tes-font;
              padding-bottom: 8px;
            }
            .flag-content {
              display: flex;
              flex-direction: row;
              flex-wrap: nowrap;
              justify-content: space-between;
              align-items: center;
              .flag-time {
                color: #a8abb2;
              }
              .flag-tags {
                display: flex;
                flex-direction: row;
                .el-tag {
                  margin-right: 4px;
                  border: none;
                }
                .tag-user {
                  display: flex;
                  align-items: center;
                  background: $user-tag-backGroundColor;
                  .icon {
                    color: $tes-font3;
                    padding-right: 4px;
                  }
                  .name {
                    max-width: 64px;
                    color: $user-tag-color;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .statistics-list {
    width: 100%;
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      display: flex;
      align-items: center;
      .icon {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 12px;
      }
      .num-bg1 {
        color: $tes-primary;
      }
      .num-bg2 {
        color: $tes-yellow;
      }
      .num-bg3 {
        color: $tes-red;
      }
      .title {
        font-size: 14px;
        color: $tes-font;
      }
    }
    .right {
      display: flex;
      align-items: center;
      .number {
        font-size: 20px;
      }
      .txt {
        padding-left: 10px;
        font-size: 14px;
      }
      .no-data {
        font-size: 16px;
        color: $tes-font3;
      }
    }
  }
  .my-agency-list {
    .content-list {
      cursor: pointer;
      box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
      padding: 10px;
      border-radius: 4px;
      &:not(:last-of-type) {
        margin-bottom: 10px;
      }
      .flag-main {
        color: $tes-font;
        padding-bottom: 8px;
      }
      .flag-content {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: space-between;
        align-items: center;
        .flag-time {
          color: #a8abb2;
        }
        .flag-tags {
          display: flex;
          flex-direction: row;
          .el-tag {
            margin-right: 4px;
            border: none;
          }
          .tag-user {
            display: flex;
            align-items: center;
            background: #f4f4f5;
            .icon {
              color: $tes-font3;
              padding-right: 4px;
            }
            .name {
              max-width: 64px;
              color: $tes-font;
            }
          }
        }
      }
    }
  }
}
</style>
