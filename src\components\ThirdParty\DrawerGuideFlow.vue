<!-- 业务导览 -->
<template>
  <el-drawer
    v-model="drawerShow"
    modal-class="drawer-modal-transparent"
    :with-header="false"
    :size="480"
    custom-class="drawer-guide"
    :before-close="handleClose"
  >
    <div class="container">
      <div class="text-right">
        <span class="close-btn el-icon el-icon-close" @click="handleClose" />
      </div>
      <div class="headline">
        <div class="header">
          <span class="title">业务导览</span>
          <span class="el-icon" @click="onOpenDialog">
            <SvgIcon icon-class="settings" :width="20" :height="20" />
          </span>
        </div>
        <p class="guide-description">{{ dashboardData.description }}</p>
      </div>
      <div class="guide-steps">
        <el-scrollbar>
          <div class="card-group">
            <div
              v-for="(item, index) in dashboardData.guideFlowList"
              :key="index"
              class="card-item"
              @click="onGuideFlowItemClick(item)"
            >
              <div class="serial">{{ index + 1 }}</div>
              <div class="content">
                <div class="title">{{ item.menuTitle }}</div>
                <div class="description">{{ item.description }}</div>
              </div>
              <div class="arrow">
                <SvgIcon icon-class="arrow-flow" :width="12" :height="52" />
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </el-drawer>
  <el-dialog
    v-model="dialogVisiable"
    title="业务导览配置"
    custom-class="business-guide"
    :close-on-click-modal="false"
    :width="1000"
    @opened="onDialogOpened"
    @close="onDialogClose"
  >
    <div class="description">
      <template v-if="isEdit">
        <div class="description-label">简介</div>
        <el-input
          v-model="description"
          size="small"
          maxlength="30"
          show-word-limit
          placeholder="简要介绍业务导览的内容"
        />
      </template>
      <div v-else class="description-text">{{ description }}</div>
    </div>
    <div class="btn_group text-right">
      <template v-if="isEdit">
        <el-button size="small" class="el-icon-close" @click="onCancelEdit"> 取消编辑</el-button>
        <el-button type="primary" size="small" class="el-icon-plus" @click="onGuideFlowDataAdd"> 新增</el-button>
      </template>
      <el-button v-else size="small" class="el-icon-edit" @click="onEdit"> 编辑</el-button>
    </div>
    <el-form ref="formRef" :model="guideFlowData" label-width="0" size="small">
      <el-table
        ref="tableRef"
        :key="tableKey"
        v-loading="tableLoading"
        :data="guideFlowData"
        max-height="480"
        fit
        border
        size="medium"
        class="dark-table base-table format-height-table no-quick-query"
      >
        <el-table-column v-if="isEdit" label="" prop="code" width="50" align="center" show-overflow-tooltip>
          <i class="tes-move iconfont" style="font-size: 12px; cursor: move" />
        </el-table-column>
        <el-table-column label="子菜单" prop="menuKeys" width="220" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item v-if="isEdit" :prop="`${$index}.menuKeys`">
              <el-cascader
                v-model="row.menuKeys"
                :props="{ label: 'title', value: 'key' }"
                :options="menuList"
                @change="values => onMenuKeysChange(values, row)"
              />
            </el-form-item>
            <span v-else>{{ row.menu?.title }}</span>
          </template>
        </el-table-column>
        <el-table-column label="描述" prop="description" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item v-if="isEdit" :prop="`${$index}.description`">
              <el-input
                v-model="row.description"
                v-trim
                autocomplete="off"
                maxlength="30"
                show-word-limit
                placeholder="请输入类目名称"
              />
            </el-form-item>
            <span v-else>{{ row.description }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="isEdit" label="操作" width="50">
          <template #default="{ $index }">
            <span class="red-color" @click="onGuideFlowDataDelete($index)">
              <span class="el-icon-delete" />
            </span>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onDialogClose">取消</el-button>
        <el-button type="primary" @click="onDialogSave" @keyup.prevent @keydown.enter.prevent>确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, nextTick } from 'vue';
import router from '@/router/index.js';
import SvgIcon from '@/components/SvgIcon';
import Sortable from 'sortablejs';
import { getMenuList } from '@/utils/auth';
import { transformMenusToRoutes } from '@/utils/permission';
import { queryBusinessGuideList, saveBusinessGuide } from '@/api/buinessGuide';
import { ElMessage } from 'element-plus';
import { isExternal } from '@/utils/validate';

export default {
  name: 'DrawerGuideFlowService',
  components: { SvgIcon },
  props: {
    drawerVisible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['closeDrawer'],
  setup(props, context) {
    const state = reactive({
      drawerShow: false,
      dialogVisiable: false,
      description: '',
      isEdit: false,
      menuList: [],
      tableKey: 0,
      tableLoading: false,
      guideFlowData: [],
      dashboardData: {
        description: '',
        guideFlowList: []
      }
    });
    const tableRef = ref(null);
    const menuKeyMap = new Map();

    watch(props, newValue => {
      state.drawerShow = newValue.drawerVisible;
      if (state.drawerShow) {
        if (state.menuList.length === 0) {
          const menuList = transformMenusToRoutes([], getMenuList());
          const newMenuList = [];
          menuList.forEach(menuItem => {
            if (menuItem.children.length > 0 && !/^(Enterprise|userManage|Platform)$/.test(menuItem.name)) {
              const newMenu = {
                id: menuItem.meta.id,
                title: menuItem.meta.title,
                key: menuItem.meta.key,
                path: menuItem.path,
                children: menuItem.children
                  .filter(item => item.meta.visible)
                  .map((item, index) => {
                    const newChild = {
                      indexs: [newMenuList.length, index],
                      id: item.meta.id,
                      title: item.meta.title,
                      parentKey: menuItem.meta.key,
                      key: item.meta.key,
                      path: item.path,
                      disabled: false
                    };
                    menuKeyMap.set(newChild.key, newChild);
                    return newChild;
                  })
              };
              newMenuList.push(newMenu);
              menuKeyMap.set(newMenu.key, newMenu);
            }
          });
          state.menuList = newMenuList;
        }
        getBusinessGuideList();
      }
    });

    watch(
      () => state.tableKey,
      () => {
        nextTick(() => {
          implementRowDrop();
        });
      }
    );

    // 查询业务导览数据列表
    const getBusinessGuideList = async () => {
      state.tableLoading = true;
      const res = await queryBusinessGuideList();
      state.tableLoading = false;
      if (res && res.data.code === 200) {
        const data = res.data.data;
        const description = data[0]?.introduce;
        state.dashboardData.description = description;
        state.dashboardData.guideFlowList = data;
      }
    };

    // 保存业务导览数据
    const saveData = async () => {
      const guideFlowData = state.guideFlowData;
      const params = {
        businessTourList: guideFlowData.map((item, index) => {
          return {
            id: item.id,
            introduce: state.description,
            menuId: item.menu?.id,
            menuKey: item.menu?.key,
            menuTitle: item.menu?.title,
            menuPath: item.menu?.path,
            description: item.description,
            order: index
          };
        })
      };
      const res = await saveBusinessGuide(params);
      if (res && res.data.code === 200) {
        ElMessage.success('保存成功!');
        getBusinessGuideList();
        onDialogClose();
      }
    };

    const handleClose = () => {
      context.emit('closeDrawer');
      state.drawerShow = false;
    };

    const onOpenDialog = () => {
      state.dialogVisiable = true;
      resetData();
    };

    const onGuideFlowItemClick = item => {
      const menus = getBindingMenuItems(item.menuKey);
      if (menus.length > 0) {
        const menu = menus[menus.length - 1];
        if (
          isExternal(menu.path) ||
          menu.path === '/smart-charts/dataBoard' ||
          menu.path === '/smart-charts/aging-laboratory'
        ) {
          return window.open(menu.path, '_blank');
        } else {
          router.push({ path: menu.path });
        }
      }
    };

    const onDialogOpened = () => {
      state.tableKey++;
    };

    const onDialogClose = () => {
      state.dialogVisiable = false;
    };

    const onDialogSave = () => {
      if (state.isEdit) {
        saveData();
      }
    };

    // 处理 Cascader 事件
    const onMenuKeysChange = (values, row) => {
      if (values instanceof Array && values.length > 1) {
        const bindingMenuItems = getBindingMenuItems(values);
        if (bindingMenuItems.length > 1) {
          const menu = bindingMenuItems[1];
          row.menu = menu;
          setMenusDisabled();
        }
      }
    };

    // 查找所属页面的路径数据
    const getBindingMenuItems = menuKeys => {
      const bindingMenuItems = [];
      if (menuKeys instanceof Array) {
        menuKeys.forEach(key => {
          if (menuKeyMap.has(key)) {
            bindingMenuItems.push(menuKeyMap.get(key));
          }
        });
        return bindingMenuItems;
      } else if (typeof menuKeys === 'string') {
        let temMenu = menuKeyMap.get(menuKeys);
        if (menuKeys === 'Home') {
          return [temMenu, temMenu.children[0]];
        } else {
          if (temMenu) {
            bindingMenuItems.unshift(temMenu);
            while (menuKeyMap.has(temMenu.parentKey)) {
              temMenu = menuKeyMap.get(temMenu.parentKey);
              bindingMenuItems.unshift(temMenu);
            }
          }
          return bindingMenuItems;
        }
      }
      return [];
    };

    // 设置子菜单 disabled
    const setMenusDisabled = () => {
      state.menuList.forEach(menu => {
        menu.children.forEach(item => {
          item.disabled = false;
        });
      });
      state.guideFlowData.forEach(item => {
        if (item.menu) {
          item.menu.disabled = true;
        }
      });
    };

    const resetData = () => {
      state.isEdit = false;
      state.description = state.dashboardData.description;
      state.guideFlowData = state.dashboardData.guideFlowList.map(item => {
        const menus = getBindingMenuItems(item.menuKey);
        return {
          id: item.id,
          menuKeys: menus.map(menu => menu.key),
          menu: menus[menus.length - 1],
          description: item.description
        };
      });
      setMenusDisabled();
    };

    const onEdit = () => {
      state.isEdit = true;
    };

    const onCancelEdit = () => {
      resetData();
    };

    const onGuideFlowDataAdd = () => {
      state.guideFlowData.push({
        id: '',
        menuKeys: [],
        menu: null,
        description: ''
      });
      setMenusDisabled();
    };

    const onGuideFlowDataDelete = index => {
      state.guideFlowData.splice(index, 1);
      setMenusDisabled();
    };

    // 行拖拽
    const implementRowDrop = () => {
      // 获取当前表格
      const tbody = tableRef.value.$el.querySelector('.el-table__body-wrapper tbody');
      Sortable.create(tbody, {
        animation: 150,
        handle: '.tes-move',
        draggable: '.el-table__row',
        ghostClass: 'ghost',
        // 拖动对象移动样式
        dragClass: 'drag',
        forceFallback: true,
        onEnd(evt) {
          if (evt.oldIndex !== evt.newIndex) {
            // 移除原来的数据
            const currRow = state.guideFlowData.splice(evt.oldIndex, 1)[0];
            // 移除原来的数据并插入新的数据
            state.guideFlowData.splice(evt.newIndex, 0, currRow);
            state.tableKey += 1;
          }
        }
      });
    };

    return {
      SvgIcon,
      ...toRefs(state),
      tableRef,
      handleClose,
      onOpenDialog,
      onGuideFlowItemClick,
      onDialogOpened,
      onDialogClose,
      onDialogSave,
      onMenuKeysChange,
      onEdit,
      onCancelEdit,
      onGuideFlowDataAdd,
      onGuideFlowDataDelete
    };
  }
};
</script>

<style lang="scss" scoped>
.drawer-guide {
  .container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    z-index: 2;
    .close-btn {
      font-size: 20px;
      color: $tes-font1;
      cursor: pointer;
      &:hover {
        color: #00b38a;
      }
    }
    .headline {
      display: flex;
      flex-direction: column;
      gap: 10px;
      margin: 12px 0 24px;
      span {
        display: inline-block;
      }
      .header {
        display: flex;
        gap: 12px;
        align-items: center;
        .title {
          font-size: 28px;
        }
        .el-icon {
          transition: all 0.15s;
          cursor: pointer;
          &:hover {
            transform: scale(1.1);
          }
        }
      }
      p {
        text-align: left;
        font-size: 14px;
        color: $tes-font2;
      }
    }
    .guide-description {
      line-height: 22px;
    }
    .guide-steps {
      flex-grow: 1;
      min-height: 0;
    }
  }
  .card-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 8px;
    margin-right: 4px;
  }
  .card-item {
    position: relative;
    padding: 16px 20px;
    border-radius: 10px;
    background: #e5f7f3;
    border: 1px solid #ccf0e8;
    transition: all 150ms;
    display: flex;
    align-items: center;
    gap: 20px;
    &:last-of-type {
      .arrow {
        display: none;
      }
    }
    .arrow {
      position: absolute;
      bottom: -28px;
      left: 29px;
      z-index: 3;
    }
    .serial {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      background: #4dcaae;
      color: #fff;
    }
    .content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
      gap: 8px;
      .title {
        font-size: 18px;
        line-height: 18px;
        color: #00b38a;
      }
      .description {
        text-align: left;
        font-size: 14px;
        line-height: 1.5;
        color: $tes-font2;
      }
    }
    &:hover {
      z-index: 1;
      cursor: pointer;
      background: #d9f4ee;
      border: 1px solid #bfece1;
      transform: translateX(2px) translateY(-2px);
      box-shadow: 0px 2px 4px 0px rgba(0, 182, 120, 0.3);
      .serial {
        background: #00b38a;
      }
      .arrow {
        transform: translateX(0) translateY(0);
        z-index: 3;
      }
    }
  }
}

.business-guide {
  .description {
    display: flex;
    margin-bottom: 10px;
  }

  .description-label {
    flex-shrink: 0;
    padding: 5px 0;
    margin-right: 10px;
    line-height: 1.5;
  }

  .description-text {
    color: $tes-font2;
    padding: 5px 0;
    line-height: 1.5;
  }

  .btn_group {
    margin-bottom: 12px;
  }

  .el-form-item {
    margin-bottom: 0;
  }
}
</style>
<style lang="scss">
.drawer-guide .el-drawer__body {
  background-image: url('@/assets/img/drawer-bg.png');
  background-size: 100% 100%;
}

.drawer-modal-transparent {
  background: transparent;
}
</style>
