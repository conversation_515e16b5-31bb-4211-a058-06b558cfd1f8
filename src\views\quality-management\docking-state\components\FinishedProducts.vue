<template>
  <!-- 成品检验 -->
  <div v-loading="loading" class="FinishedProducts textLeft">
    <el-row class="header">
      <el-col :span="4">
        <span class="title">生产数据</span>
      </el-col>
      <el-col :span="20" class="textRight">
        <el-button
          v-if="oldTableProductList.length > 0 && oldTableTestList.length > 0"
          class="add-btn"
          size="small"
          type="warning"
          icon="el-icon-upload"
          @click="uploadSdcc"
          @keyup.prevent
          @keydown.enter.prevent
          >上传SDCC</el-button
        >
        <el-button v-if="isAddProduct || isEditProduct" type="primary" size="small" @click="onSaveProduction">
          保存
        </el-button>
        <el-button v-if="isAddProduct || isEditProduct" size="small" @click="calceProduction"> 取消 </el-button>
        <el-button
          v-if="!isEditProduct && !isAddProduct && oldTableProductList.length > 0"
          icon="el-icon-edit"
          type="primary"
          size="small"
          @click="handleEditProduct"
        >
          编辑
        </el-button>
        <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAddProduction"> 新增 </el-button>
      </el-col>
    </el-row>
    <el-form ref="tableProductRef" :model="tableProductForm" :rules="productionRules" size="small">
      <el-table
        ref="tableLeftRef"
        :data="tableProductForm.tableData"
        fit
        border
        highlight-current-row
        size="medium"
        max-height="440px"
        class="detail-table format-height-table2 dark-table"
        @row-click="rowClick"
        @header-dragend="drageHeader"
      >
        <el-table-column type="index" fixed="left" label="序号" :min-width="colWidth.serialNo" align="center" />
        <el-table-column prop="materialNo" label="物料编号" :min-width="colWidth.plate" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="
                !row.id || (row.sourcePath !== 'M' && isEditProduct && row.reportSubmitStatus.split('/')[0] === '0')
              "
              :prop="'tableData.' + $index + '.materialNo'"
              :rules="productionRules.materialNo"
              style="margin: 0px"
            >
              <el-input
                v-model="row.materialNo"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="请输入物料编号"
                @click.stop.prevent
              />
            </el-form-item>
            <span v-else>{{ row.materialNo || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="batchNo" label="批次" :min-width="colWidth.plate" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="
                !row.id || (row.sourcePath !== 'M' && isEditProduct && row.reportSubmitStatus.split('/')[0] === '0')
              "
              :prop="'tableData.' + $index + '.batchNo'"
              :rules="productionRules.batchNo"
              style="margin: 0px"
            >
              <el-input
                v-model="row.batchNo"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="请输入批次"
                @click.stop.prevent
              />
            </el-form-item>
            <span v-else>{{ row.batchNo || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="reelNo" label="盘号" :width="colWidth.plate" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="
                !row.id || (row.sourcePath !== 'M' && isEditProduct && row.reportSubmitStatus.split('/')[0] === '0')
              "
              :prop="'tableData.' + $index + '.reelNo'"
              :rules="productionRules.reelNo"
              style="margin: 0px"
            >
              <el-input
                v-model="row.reelNo"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="请输入盘号"
                @click.stop.prevent
              />
            </el-form-item>
            <span v-else>{{ row.reelNo || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="需求数量" :width="colWidth.plate">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="
                !row.id || (row.sourcePath !== 'M' && isEditProduct && row.reportSubmitStatus.split('/')[0] === '0')
              "
              :prop="'tableData.' + $index + '.amount'"
              :rules="productionRules.amount"
              style="margin: 0px"
            >
              <el-input
                v-model.number="row.amount"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="请输入需求数量"
                @click.stop.prevent
              />
            </el-form-item>
            <span v-else>{{ row.amount || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="productionUnit" label="需求单位" :width="colWidth.plate">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="
                !row.id || (row.sourcePath !== 'M' && isEditProduct && row.reportSubmitStatus.split('/')[0] === '0')
              "
              :prop="'tableData.' + $index + '.productionUnit'"
              :rules="productionRules.productionUnit"
              style="margin: 0px"
            >
              <el-select v-model="row.productionUnit" placeholder="请选择单位" clearable filterable style="width: auto">
                <el-option-group v-for="item in unitList" :key="item.label" :label="item.label">
                  <el-option
                    v-for="val in item.group"
                    :key="val.id"
                    :label="val.name"
                    :value="val.name"
                    :disabled="val.status !== 1"
                  >
                    <span style="float: left">{{ val.name }}</span>
                    <span v-if="val.status !== 1" class="fr" style="color: red">已停用</span>
                  </el-option>
                </el-option-group>
              </el-select>
            </el-form-item>
            <span v-else>{{ row.productionUnit || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="reportSubmitStatus" label="上传状态" :width="colWidth.plate">
          <template #header>
            <span
              >上传状态
              <el-tooltip content="此生产数据下关联的未上传报告/总报告数量" placement="top" effect="light">
                <i class="iconfont tes-title" />
              </el-tooltip>
            </span>
          </template>
          <template #default="{ row }">
            <span>{{ row.reportSubmitStatus || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="75px">
          <template #default="{ row, $index }">
            <span
              v-if="row.id && row.reportSubmitStatus.split('/')[0] === '0'"
              class="blue-color"
              @click.stop="handleDeleteProduct(row, $index)"
              >删除</span
            >
            <span
              v-if="!row.id && tableProductForm.tableData.length > 1"
              class="blue-color"
              @click.stop="handleDeleteProduct(row, $index)"
              >删除</span
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <div v-if="selectRow.id" class="textRight topBtn">
      <el-button
        class="add-btn"
        size="small"
        icon="el-icon-download"
        type="warning"
        @click="dialogSelect = true"
        @keyup.prevent
        @keydown.enter.prevent
        >获取数据</el-button
      >
      <el-button
        v-if="(isAddTestData || isEditTestData || isGetData) && tableTestForm.tableData.length > 0"
        type="primary"
        size="small"
        @click="onSaveTestData"
      >
        保存
      </el-button>
      <el-button
        v-if="(isAddTestData || isEditTestData || isGetData) && tableTestForm.tableData.length > 0"
        size="small"
        @click="calceTestData"
      >
        取消
      </el-button>
      <el-button
        v-if="!isEditTestData && !isAddTestData && oldTableTestList.length > 0"
        type="primary"
        icon="el-icon-edit"
        size="small"
        @click="isEditTestData = true"
      >
        编辑
      </el-button>
      <el-button
        v-if="!isAddTestData"
        class="add-btn"
        icon="el-icon-plus"
        size="small"
        type="primary"
        @click="handleAddTestData"
        @keyup.prevent
        @keydown.enter.prevent
        >新增</el-button
      >
    </div>
    <el-form v-if="selectRow.id" ref="tableTestRef" :model="tableTestForm" :rules="testRules" size="small">
      <el-table
        :data="tableTestForm.tableData"
        fit
        border
        highlight-current-row
        size="medium"
        max-height="440px"
        class="detail-table format-height-table2 dark-table"
        :span-method="objectSpanMethod"
        @header-dragend="drageHeader"
      >
        <el-table-column type="index" label="序号" :width="colWidth.serialNo" fixed="left" align="center" />
        <el-table-column prop="secSampleNum" label="样品编号" :min-width="colWidth.orderNo" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
              :prop="'tableData.' + $index + '.secSampleNum'"
              :rules="testRules.secSampleNum"
              style="margin: 0px"
            >
              <el-input
                v-model="row.secSampleNum"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="样品编号"
                @input="
                  val => {
                    return handleEditGroup(val, $index, 'secSampleNum');
                  }
                "
              />
            </el-form-item>
            <span v-else>{{ row.secSampleNum || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="reportNo" label="报告编码" :min-width="colWidth.orderNo" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
              :prop="'tableData.' + $index + '.reportNo'"
              :rules="testRules.reportNo"
              style="margin: 0px"
            >
              <el-input
                v-model="row.reportNo"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="报告编码"
                @input="
                  val => {
                    return handleEditGroup(val, $index, 'reportNo');
                  }
                "
              />
            </el-form-item>
            <span v-else>{{ row.reportNo || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="inspectionStatus" label="试验状态" :width="colWidth.inputSelect">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
              :prop="'tableData.' + $index + '.inspectionStatus'"
              :rules="testRules.inspectionStatus"
              style="margin: 0px"
            >
              <el-select
                v-model="row.inspectionStatus"
                placeholder="试验状态"
                size="small"
                @change="
                  val => {
                    return handleEditGroup(val, $index, 'inspectionStatus');
                  }
                "
              >
                <el-option label="准备" :value="0" />
                <el-option label="进行中" :value="1" />
                <el-option label="完成" :value="2" />
              </el-select>
            </el-form-item>
            <div v-else>{{ inspectionStatusJON[row.inspectionStatus] || '--' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="experimentProjectName" label="项目名称" :width="colWidth.orderNo" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.experimentProjectName || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="submitStatus" label="上传状态" :width="colWidth.status">
          <template #default="{ row }">
            <el-tag :type="row.submitStatus ? 'success' : 'warning'" size="small">{{
              row.submitStatus ? '已上传' : '待上传'
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="externalCapabilityParaName"
          label="关键参数"
          :width="colWidth.orderNo"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ row.externalCapabilityParaName || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="unitName" label="单位" :width="colWidth.inputSelect" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.unitName || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="resultType" label="数据类型" :width="colWidth.inputSelect" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.resultType || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="paraValue" label="报告结果" :min-width="colWidth.testResults">
          <template #default="{ row }">
            {{ row.paraValue || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="sdccParaValue" label="上传结果" :min-width="colWidth.xxys" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
              :prop="'tableData.' + $index + '.sdccParaValue'"
              :rules="testRules.sdccParaValue[row.resultType]"
              style="margin: 0px"
            >
              <el-input
                v-if="row.resultType === '数值型'"
                v-model="row.sdccParaValue"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="上传结果"
              />
              <el-input
                v-else-if="row.resultType === '字符串'"
                v-model="row.sdccParaValue"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="上传结果"
              />
              <el-select v-else v-model="row.sdccParaValue" placeholder="请选择" size="small">
                <el-option
                  v-for="(val, key) in dictionaryJson[row.resultOption]"
                  :key="key"
                  :label="val"
                  :value="key"
                />
              </el-select>
            </el-form-item>
            <div v-else>
              <span v-if="row.resultType === '枚举型'">{{
                dictionaryJson[row.resultOption][row.sdccParaValue] || '--'
              }}</span>
              <span v-else>{{ row.sdccParaValue || '--' }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="result" label="结论" :width="colWidth.inputSelect">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
              :prop="'tableData.' + $index + '.result'"
              :rules="testRules.result"
              style="margin: 0px"
            >
              <el-select
                v-model="row.result"
                placeholder="结论"
                size="small"
                @change="
                  val => {
                    return handleEditGroup2(val, $index, 'result');
                  }
                "
              >
                <el-option label="合格" value="合格" />
                <el-option label="不合格" value="不合格" />
              </el-select>
            </el-form-item>
            <div v-else>{{ row.result || '--' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="colourValue" label="线芯颜色" :min-width="colWidth.xxys">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
              :prop="'tableData.' + $index + '.colourValue'"
              :rules="testRules.colourValue"
              style="margin: 0px"
            >
              <el-input
                v-model="row.colourValue"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="线芯颜色"
                @input="
                  val => {
                    return handleEditGroup2(val, $index, 'colourValue');
                  }
                "
              />
            </el-form-item>
            <span v-else>{{ row.colourValue || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="开始时间" :width="colWidth.datetime">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
              :prop="'tableData.' + $index + '.startDate'"
              :rules="testRules.startDate"
              style="margin: 0px"
            >
              <el-date-picker
                v-model="row.startDate"
                type="date"
                size="small"
                placeholder="开始时间"
                @change="
                  val => {
                    return handleChangeDate(val, $index, 'startDate');
                  }
                "
              />
            </el-form-item>
            <span v-else>{{ formatDate(row.startDate) || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="finishedDate" label="结束时间" :min-width="colWidth.datetime">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
              :prop="'tableData.' + $index + '.finishedDate'"
              :rules="testRules.finishedDate"
              style="margin: 0px"
            >
              <el-date-picker
                v-model="row.finishedDate"
                type="date"
                size="small"
                placeholder="结束时间"
                @change="
                  val => {
                    return handleChangeDate(val, $index, 'finishedDate');
                  }
                "
              />
            </el-form-item>
            <div v-else>{{ formatDate(row.finishedDate) || '--' }}</div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="oldTableTestList.length > 0"
          prop="status"
          label="操作"
          fixed="right"
          :width="colWidth.operation"
        >
          <template #default="{ row, $index }">
            <span
              v-if="row.sourcePath !== 'M' && !row.isAddRow && !row.submitStatus"
              class="blue-color"
              @click="handleDeleteTestData(row, $index)"
              >删除</span
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <!-- 获取数据的弹出框 -->
    <DialogFinishData
      :dialog-show="dialogSelect"
      :select-row="{ type: 'finish', data: obtainingData }"
      @closeDialog="closeDialog"
    />
  </div>
</template>

<script>
import { reactive, ref, toRefs, nextTick, getCurrentInstance } from 'vue';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { getTestListProcess, getInspectionApi, submitSDCC } from '@/api/sdcc';
import DialogFinishData from './DialogFinishData';
import { ElMessage } from 'element-plus';
import { isDigital } from '@/utils/validate';
import { getDictionary } from '@/api/user';
import {
  detailListApi,
  saveFinishProduction,
  saveTestData,
  deleteFinishedProduction,
  deleteTestData
} from '@/api/sdcc';
export default {
  name: 'FinishedProducts',
  components: { DialogFinishData },
  props: {
    unitList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    externalList: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['isModify'],
  setup(props, context) {
    // eslint-disable-next-line no-empty-pattern
    const { proxy } = getCurrentInstance();
    const state = reactive({
      obtainingData: {}, // 传递给获取数据用于查询的参数
      inspectionStatusJON: {
        0: '准备',
        1: '进行中',
        2: '完成'
      },
      loading: false,
      tableProductForm: {
        tableData: [] // 生产数据
      },
      tableTestForm: {
        tableData: []
      }, // 检测数据
      oldTableProductList: [], // 修改之前的项目信息
      spanArr: [],
      mergeArr: [],
      oldTableTestList: [],
      experimentProjectCode: [], // 提交SDCC需要传的code
      unitList: [],
      pos: 0,
      pos2: 0,
      isEditProduct: false, // 项目信息编辑状态
      isAddProduct: false, // 项目信息编辑状态
      isEditTestData: false,
      isAddTestData: false,
      isGetData: false, // 已获取数据
      dialogSelect: false, // 获取数据的弹出框
      selectRow: {}, // 选中的生产数据
      dictionaryJson: {},
      externalList: [],
      productionRules: {
        // 生产数据的必填校验
        materialNo: { required: true, tigger: 'blur', message: '物料编号' },
        batchNo: { required: true, tigger: 'blur', message: '请输入批次' },
        reelNo: { required: true, tigger: 'blur', message: '请输入盘号' },
        productionUnit: { required: true, tigger: 'blur', message: '请选择需求单位' },
        amount: { required: true, type: 'number', tigger: 'blur', message: '请输入需求数量' }
      },
      testRules: {
        // 测试数据的必填校验
        secSampleNum: { required: true, tigger: 'blur', message: '请输入样品编号' },
        reportNo: { required: true, tigger: 'blur', message: '请输入报告编码' },
        externalCapabilityParaName: { required: true, tigger: 'blur', message: '请输入项目名称' },
        sdccParaValue: {
          字符串: { required: true, tigger: 'blur', message: '请输入上传结果' },
          枚举型: { required: true, tigger: 'blur', message: '请输入上传结果' },
          数值型: [{ required: true, tigger: 'blur', message: '请输入上传结果' }, { validator: isDigital }]
        },
        result: { required: true, tigger: 'blur', message: '请选择结论' },
        colourValue: { required: true, tigger: 'blur', message: '请输入线芯颜色' },
        startDate: { required: false },
        finishedDate: { required: false },
        inspectionStatus: { required: true, type: 'number', tigger: 'blur', message: '请选择试验状态' }
      },
      detailData: JSON.parse(localStorage.getItem('productionOrderInfo')),
      group: [],
      tableLeftRef: ref(),
      tableProductRef: ref(),
      tableTestRef: ref(),
      isModify: false
    });
    // 获取字典数据成品检枚举型
    const getDictionaryList = () => {
      const array = state.externalList.filter(item => {
        return item.resultType === '枚举型';
      });
      array.forEach(val => {
        getDictionary(val.resultOption).then(res => {
          if (res) {
            const dictionaryJson = {};
            res.data.data.dictionaryoption.forEach(item => {
              if (item.status === 1) {
                dictionaryJson[item.code] = item.name;
              }
            });
            state.dictionaryJson[val.resultOption] = dictionaryJson;
          }
        });
      });
    };
    // 获取生产数据表格
    const getTableList = isFirst => {
      state.externalList = [];
      props.externalList.forEach(item => {
        state.experimentProjectCode.push(item.code);
        item.externalCapabilityMapList.forEach(val => {
          state.externalList.push({ experimentProjectName: item.name, experimentProjectCode: item.code, ...val });
        });
      });
      state.unitList = props.unitList;
      if (isFirst) {
        getDictionaryList();
      }
      state.loading = true;
      detailListApi(state.detailData.id).then(res => {
        state.loading = false;
        if (res) {
          state.tableProductForm.tableData = res.data.data;
          state.oldTableProductList = JSON.parse(JSON.stringify(res.data.data));
          if (state.oldTableProductList.length > 0) {
            getSelectTable(state.tableProductForm.tableData[0]);
          } else {
            state.tableTestForm.tableData = [];
          }
        }
      });
    };
    getTableList(true);
    const getSelectTable = row => {
      nextTick(() => {
        state.tableLeftRef.setCurrentRow(row, true);
      });
      state.selectRow = row;
      state.obtainingData = {
        materialNo: state.selectRow.materialNo,
        batchNo: state.selectRow.batchNo,
        productionOrderNo: state.selectRow.productionOrderNo,
        productionOrderId: state.selectRow.productionOrderId,
        reelNo: state.selectRow.reelNo
      };
      // 获取检验数据
      if (state.selectRow.id) {
        getInspectionList();
      } else {
        state.tableTestForm.tableData = [];
      }
    };
    const rowClick = row => {
      getSelectTable(row);
    };
    const getInspectionList = () => {
      getInspectionApi({
        inspectionType: 'END_PRODUCT',
        mainDataId: state.selectRow.id,
        productionOrderId: state.selectRow.productionOrderId
      }).then(res => {
        state.isAddTestData = false;
        state.isEditTestData = false;
        state.isGetData = false;
        if (res) {
          state.tableTestForm.tableData = JSON.parse(JSON.stringify(res.data.data));
          state.oldTableTestList = JSON.parse(JSON.stringify(res.data.data));
          getSpanArr(state.tableTestForm.tableData);
          // console.log(getSpanArr(state.tableTestForm.tableData))
        }
      });
    };
    // 新增测试数据
    const handleAddTestData = () => {
      state.isAddTestData = true;
      state.externalList.forEach((item, index) => {
        const selectDetail = JSON.parse(JSON.stringify(state.selectRow));
        selectDetail.mainDataId = selectDetail.id;
        delete selectDetail.id;
        state.tableTestForm.tableData.push({
          ...selectDetail,
          submitStatus: 0,
          isAddRow: true,
          sourcePath: 'I',
          inspectionStatus: 2,
          result: '合格',
          inspectionType: 'END_PRODUCT',
          ...item
        });
      });
      context.emit(
        'isModify',
        state.isAddProduct || state.isEditProduct || state.isAddTestData || state.isEditTestData || state.isGetData
      );
      getSpanArr(state.tableTestForm.tableData);
    };
    // 获取数据
    const closeDialog = val => {
      if (val.data.length > 0) {
        const params = {
          reportIdList: val.data.map(item => {
            return item.reportId;
          }),
          externalCapabilityMapList: state.externalList
        };
        var reportNoArray = [];
        state.tableTestForm.tableData.forEach(item => {
          reportNoArray.push({
            reportNo: item.reportNo,
            externalCapabilityParaName: item.externalCapabilityParaName
          });
        });
        state.loading = true;
        getTestListProcess(params).then(res => {
          state.loading = false;
          if (res) {
            state.dialogSelect = false;
            const detailDataInfo = JSON.parse(JSON.stringify(res.data.data));
            detailDataInfo.forEach(val => {
              const valItem = { ...state.selectRow, ...val };
              valItem.mainDataId = state.selectRow.id;
              delete valItem.id;
              delete valItem.deviceUsageVoList;
              const isExistence = reportNoArray.some(item => {
                return (
                  item.reportNo === valItem.reportNo &&
                  item.externalCapabilityParaName === valItem.externalCapabilityParaName
                );
              });
              if (!isExistence) {
                if (valItem.paraValue && valItem.resultType !== '枚举值') {
                  const sdccParaValue = valItem.paraValue.split(',')[0];
                  // 有报告结果
                  if (sdccParaValue.split(':').length > 1) {
                    valItem.sdccParaValue = sdccParaValue.split(':')[1];
                  } else {
                    valItem.sdccParaValue = sdccParaValue;
                  }
                } else {
                  valItem.sdccParaValue = '';
                }
                state.tableTestForm.tableData.push({
                  ...valItem,
                  submitStatus: 0,
                  sourcePath: 'I',
                  isAddRow: true,
                  inspectionType: 'END_PRODUCT'
                });
              } else {
                setTimeout(() => {
                  ElMessage({
                    message: `报告编号：${valItem.reportNo}, 项目名称：${valItem.externalCapabilityParaName}已存在`,
                    grouping: true,
                    type: 'error'
                  });
                }, 10);
              }
            });
            state.isGetData = true;
            context.emit(
              'isModify',
              state.isAddProduct ||
                state.isEditProduct ||
                state.isAddTestData ||
                state.isEditTestData ||
                state.isGetData
            );
            getSpanArr(state.tableTestForm.tableData);
          }
        });
      } else {
        state.dialogSelect = false;
      }
    };
    // 取消生产数据编辑
    const calceProduction = () => {
      getTableList();
      state.isAddProduct = false;
      state.isEditProduct = false;
      context.emit(
        'isModify',
        state.isAddProduct || state.isEditProduct || state.isAddTestData || state.isEditTestData || state.isGetData
      );
    };
    // 取消测试数据编辑新增
    const calceTestData = () => {
      getInspectionList();
      state.isAddTestData = false;
      state.isEditTestData = false;
      state.isGetData = false;
      context.emit(
        'isModify',
        state.isAddProduct || state.isEditProduct || state.isAddTestData || state.isEditTestData || state.isGetData
      );
    };
    // 删除生产数据
    const handleDeleteProduct = (row, index) => {
      if (row.id) {
        handleDeleteProduction(row, index);
      } else {
        state.tableProductForm.tableData.splice(index, 1);
        computedHighlighting(index);
      }
    };
    // 计算生产数据表格高亮
    const computedHighlighting = index => {
      if (state.tableProductForm.tableData.length > 0) {
        getSelectTable(state.tableProductForm.tableData[0]);
      } else {
        state.selectRow = {};
        state.tableTestForm.tableData = [];
      }
    };
    // 保存生产数据
    const onSaveProduction = () => {
      proxy.$refs['tableProductRef'].validate(valid => {
        if (valid) {
          state.loading = true;
          saveFinishProduction({
            id: state.detailData.id,
            no: state.detailData.no,
            productionOrderDetailDTOList: state.tableProductForm.tableData
          }).then(res => {
            state.loading = false;
            if (res) {
              proxy.$message.success('保存成功');
              state.isAddProduct = false;
              state.isEditProduct = false;
              context.emit(
                'isModify',
                state.isAddProduct ||
                  state.isEditProduct ||
                  state.isAddTestData ||
                  state.isEditTestData ||
                  state.isGetData
              );
              getTableList();
            }
          });
        } else {
          proxy.$message.warning('请先完成必填项');
        }
      });
    };
    // 保存测试数据
    const onSaveTestData = () => {
      proxy.$refs['tableTestRef'].validate(valid => {
        if (valid) {
          state.loading = true;
          saveTestData(state.tableTestForm.tableData).then(res => {
            state.loading = false;
            if (res) {
              proxy.$message.success('保存成功');
              state.isAddTestData = false;
              state.isEditTestData = false;
              state.isGetData = false;
              context.emit(
                'isModify',
                state.isAddProduct ||
                  state.isEditProduct ||
                  state.isAddTestData ||
                  state.isEditTestData ||
                  state.isGetData
              );
              getInspectionList();
            }
          });
        } else {
          proxy.$message.warning('请先完成必填项');
        }
      });
    };
    // 新增生产数据
    const handleAddProduction = () => {
      state.isAddProduct = true;
      state.tableProductForm.tableData.push({
        reportSubmitStatus: '0/0',
        sourcePath: 'I'
      });
      context.emit(
        'isModify',
        state.isAddProduct || state.isEditProduct || state.isAddTestData || state.isEditTestData || state.isGetData
      );
    };
    const handleSelectionChange = val => {
      state.seleDeleteRow = val;
    };
    // 删除生产数据
    const handleDeleteProduction = (row, index) => {
      proxy
        .$confirm('是否确认删除', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          showClose: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        })
        .then(() => {
          state.loading = true;
          deleteFinishedProduction(row.id).then(res => {
            state.loading = false;
            if (res) {
              proxy.$message.success('删除成功');
              state.tableProductForm.tableData.splice(index, 1);
              // const indexId = state.oldTableProductList.findIndex((item) => { return item.id === row.id })
              state.oldTableProductList.splice(index, 1);
              computedHighlighting(index);
            }
          });
        })
        .catch(() => {});
    };
    // 上传sdcc
    const uploadSdcc = () => {
      if (state.isEditProduct || state.isAddProduct) {
        proxy.$message.error('请先完成生产数据的编辑或新增');
        return false;
      }
      if (state.isAddTestData || state.isEditTestData || state.isGetData) {
        proxy.$message.error('请先完成测试数据的编辑或新增');
        return false;
      }
      proxy
        .$confirm('是否确认上传SDCC', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          showClose: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        })
        .then(() => {
          const ids = state.tableProductForm.tableData.map(item => {
            return item.id;
          });
          state.loading = true;
          submitSDCC({
            ids: ids.toString(),
            inspectionType: 'END_PRODUCT',
            experimentProjectCode: state.experimentProjectCode
          }).then(res => {
            state.loading = false;
            if (res) {
              proxy.$message.success('上传成功');
              context.emit(
                'isModify',
                state.isAddProduct ||
                  state.isEditProduct ||
                  state.isAddTestData ||
                  state.isEditTestData ||
                  state.isGetData
              );
              getTableList();
            }
          });
        })
        .catch(() => {});
    };
    // 格式化日期格式
    const handleChangeDate = (val, index, type) => {
      if (val) {
        state.tableTestForm.tableData[index][type] = formatDate(val);
        handleEditGroup2(formatDate(val), index, type);
      } else {
        state.tableTestForm.tableData[index][type] = '';
        handleEditGroup2(formatDate(val), index, type);
      }
    };
    // 删除测试数据
    const handleDeleteTestData = (row, index) => {
      var deleteArrayId = [];
      for (var i = 0; i < state.spanArr[index]; i++) {
        deleteArrayId.push(state.tableTestForm.tableData[index + i].id);
      }
      proxy
        .$confirm('是否确认删除', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          showClose: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        })
        .then(() => {
          state.loading = true;
          deleteTestData(deleteArrayId).then(res => {
            state.loading = false;
            if (res) {
              proxy.$message.success('删除成功');
              state.tableTestForm.tableData.splice(index, deleteArrayId.length);
              state.oldTableTestList.splice(index, deleteArrayId.length);
              getSpanArr(state.tableTestForm.tableData);
            }
          });
        })
        .catch(() => {});
    };
    const getSpanArr = data => {
      state.spanArr = [];
      state.mergeArr = [];
      // data就是我们从后台拿到的数据
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          state.mergeArr.push(1);
          state.spanArr.push(1);
          state.pos = 0;
          state.pos2 = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].reportId && data[i].reportId === data[i - 1].reportId) {
            state.spanArr[state.pos] += 1;
            state.spanArr.push(0);
          } else if (data[i].reportNo === data[i - 1].reportNo) {
            state.spanArr[state.pos] += 1;
            state.spanArr.push(0);
          } else {
            state.spanArr.push(1);
            state.pos = i;
          }
          if (
            data[i].reportNo === data[i - 1].reportNo &&
            data[i].experimentProjectCode === data[i - 1].experimentProjectCode
          ) {
            state.mergeArr[state.pos2] += 1;
            state.mergeArr.push(0);
          } else {
            state.mergeArr.push(1);
            state.pos2 = i;
          }
        }
      }
    };
    // else if (data[i].reportNo === data[i - 1].reportNo && data[i].experimentProjectCode === data[i - 1].experimentProjectCode) {
    //         state.spanArr[state.pos] += 1
    //         state.spanArr.push(0)
    //       }
    const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
      if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2 || columnIndex === 3 || columnIndex === 15) {
        const _row = state.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        };
      } else if (
        columnIndex === 4 ||
        columnIndex === 11 ||
        columnIndex === 12 ||
        columnIndex === 13 ||
        columnIndex === 14
      ) {
        const _row = state.mergeArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        };
      }
    };
    // 编辑样品编号和报告编号
    const handleEditGroup = (val, index, field) => {
      for (var i = 0; i < state.spanArr[index]; i++) {
        state.tableTestForm.tableData[index + i][field] = val;
      }
    };
    // 编辑结论、线芯颜色、开始时间、结束时间
    const handleEditGroup2 = (val, index, field) => {
      for (var i = 0; i < state.mergeArr[index]; i++) {
        state.tableTestForm.tableData[index + i][field] = val;
      }
    };
    // 编辑生产数据
    const handleEditProduct = () => {
      state.isEditProduct = true;
      context.emit(
        'isModify',
        state.isAddProduct || state.isEditProduct || state.isAddTestData || state.isEditTestData || state.isGetData
      );
    };
    return {
      ...toRefs(state),
      colWidth,
      getDictionaryList,
      isDigital,
      getSpanArr,
      handleEditProduct,
      handleEditGroup,
      handleEditGroup2,
      objectSpanMethod,
      computedHighlighting,
      handleDeleteTestData,
      handleChangeDate,
      getInspectionList,
      handleDeleteProduct,
      uploadSdcc,
      handleAddProduction,
      handleDeleteProduction,
      handleSelectionChange,
      onSaveProduction,
      onSaveTestData,
      calceProduction,
      calceTestData,
      closeDialog,
      getSelectTable,
      handleAddTestData,
      rowClick,
      getTableList,
      getPermissionBtn,
      drageHeader,
      formatDate,
      getNameByid
    };
  }
};
</script>

<style lang="scss" scoped>
.header {
  line-height: 40px;
  .title {
    font-size: 16px;
    font-weight: bold;
  }
}
.textRight {
  text-align: right;
}

.topBtn {
  margin: 10px 0;
}
.textCenter {
  text-align: center;
}
:deep(.el-date-editor.el-input) {
  width: 100%;
}
</style>
