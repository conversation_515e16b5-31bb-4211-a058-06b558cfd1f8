<template>
  <div>
    <div v-if="isValid" class="btn-group">
      <el-button
        :loading="tableLoading"
        icon="el-icon-plus"
        type="primary"
        size="small"
        @click="addAddress"
        @keyup.prevent
        @keydown.enter.prevent
        >新增</el-button
      >
      <el-button
        v-if="formData.tableList.length > 0 && !isEdit && !isAdd"
        icon="el-icon-edit"
        :loading="tableLoading"
        size="small"
        @click="isEdit = true"
        @keyup.prevent
        @keydown.enter.prevent
        >编辑</el-button
      >
      <el-button
        v-if="isEdit || isAdd"
        size="small"
        :loading="tableLoading"
        type="primary"
        @click="saveAddress"
        @keyup.prevent
        @keydown.enter.prevent
        >保存</el-button
      >
      <el-button
        v-if="isEdit || isAdd"
        size="small"
        :loading="tableLoading"
        @click="calceAddress"
        @keyup.prevent
        @keydown.enter.prevent
        >取消</el-button
      >
    </div>
    <el-form ref="ruleFormTable" :model="formData">
      <el-table
        v-loading="tableLoading"
        :data="formData.tableList"
        fit
        border
        height="auto"
        highlight-current-row
        class="detail-table dark-table base-table format-height-table2"
      >
        <el-table-column label="序号" :width="colWidth.serialNo" align="center">
          <template #default="{ $index }">
            <span> {{ $index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="regionState" label="省份/地区" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <div v-if="!isEdit && row.id">{{ row.regionState ? row.regionState.toString() : row.regionState }}</div>
            <el-form-item
              v-else
              :prop="`tableList.${$index}.regionState`"
              :rules="{ required: true, message: '请选择省份/地区', trigger: 'change' }"
              style="margin: 0px"
            >
              <el-cascader
                v-model="row.regionState"
                placeholder="请选择省份/地区"
                :props="cascaderProps"
                :options="provice"
                popper-class="province"
                filterable
                @change="
                  val => {
                    return handleChangeRegionState(val, $index);
                  }
                "
                @visible-change="handleCascaderVisibleChange"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="exactAddress" label="详细地址" show-overflow-tooltip>
          <template #default="{ row }">
            <div v-if="!isEdit && row.id">{{ row.exactAddress || '--' }}</div>
            <el-input v-else v-model="row.exactAddress" maxlength="120" placeholder="请输入详细地址" />
          </template>
        </el-table-column>
        <el-table-column prop="isDefault" label="默认地址" width="180">
          <template #default="{ row }">
            <el-tag v-if="!isEdit && row.id" :type="row.isDefault ? 'success' : 'info'" size="small">
              {{ row.isDefault ? '是' : '否' }}
            </el-tag>
            <el-switch v-else v-model="row.isDefault" class="inner-switch" :active-text="row.isDefault ? '是' : '否'" />
          </template>
        </el-table-column>
        <el-table-column v-if="isValid" label="操作" :width="colWidth.operationSingle">
          <template #default="{ row, $index }">
            <span class="blue-color" @click="deleteAddress(row, $index)">删除</span>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch, getCurrentInstance, nextTick } from 'vue';
import { getAddress, deleteAddressApi, saveAddressApi } from '@/api/customerManagement';
import provice from '@/data/administrativeDivisionsOfChina';
import proviceLatLng from '@/data/proviceLatLng';
import { colWidth } from '@/data/tableStyle';

export default {
  name: 'CustomerAddress',
  props: {
    activeName: {
      type: String,
      default: ''
    },
    isValid: {
      type: Boolean,
      default: false
    },
    detailId: {
      type: String,
      default: ''
    }
  },
  emits: ['isHaveRevised'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      infoId: '', // 详情id
      isValid: false,
      tableLoading: false,
      isEdit: false,
      ruleFormTable: ref(),
      isAdd: false,
      formData: {
        tableList: []
      },
      oldTableList: [],
      provice: [],
      cascaderProps: {
        expandTrigger: 'hover',
        children: 'children',
        label: 'name',
        value: 'name'
      }
    });
    watch(props, newValue => {
      if (newValue.activeName === '1' && props.detailId) {
        state.isValid = props.isValid;
        state.provice = provice;
        state.infoId = props.detailId;
        getList();
      }
    });
    const getList = () => {
      getAddress(state.infoId).then(res => {
        state.tableLoading = false;
        if (res) {
          state.isEdit = false;
          state.isAdd = false;
          state.formData.tableList = JSON.parse(JSON.stringify(res.data.data));
          state.oldTableList = JSON.parse(JSON.stringify(res.data.data));
        }
      });
    };
    // 地址发生改变，经纬度重新获取
    const handleChangeRegionState = (val, index) => {
      if (val) {
        state.formData.tableList[index].latitude = proviceLatLng[val]?.latitude;
        state.formData.tableList[index].longitude = proviceLatLng[val]?.longitude;
      }
    };
    const handleCascaderVisibleChange = visible => {
      if (visible) {
        nextTick(() => {
          // Sometime el-cascader stuck error occurred, remove aria-owns attribute fix that bug.
          const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
          Array.from($el).map(item => item.removeAttribute('aria-owns'));
        });
      }
    };
    // 删除地址
    const deleteAddress = (row, index) => {
      if (row.id) {
        state.tableLoading = true;
        deleteAddressApi(row.id).then(res => {
          state.tableLoading = false;
          if (res) {
            proxy.$message.success(res.data.message);
            state.formData.tableList.splice(index, 1);
            context.emit('isHaveRevised', true);
          }
        });
      } else {
        state.formData.tableList.splice(index, 1);
      }
    };
    // 保存地址
    const saveAddress = () => {
      state.ruleFormTable
        .validate()
        .then(valid => {
          if (valid) {
            state.tableLoading = true;
            saveAddressApi(state.formData.tableList).then(res => {
              state.tableLoading = false;
              if (res) {
                proxy.$message.success(res.data.message);
                context.emit('isHaveRevised', true);
                getList();
              }
            });
          } else {
            return false;
          }
        })
        .catch(error => {
          const fieldName = Object.keys(error)[0];
          proxy.$message.warning(error[fieldName][0].message);
          return false;
        });
    };
    // 取消保存
    const calceAddress = () => {
      state.isEdit = false;
      state.isAdd = false;
      if (state.oldTableList.length > 0) {
        state.formData.tableList = JSON.parse(JSON.stringify(state.oldTableList));
      } else {
        state.formData.tableList = [];
      }
    };
    // 新增地址
    const addAddress = () => {
      state.isAdd = true;
      state.formData.tableList.push({
        isDefault: true,
        regionState: [],
        customerId: state.infoId
      });
    };
    return {
      ...toRefs(state),
      getList,
      colWidth,
      addAddress,
      calceAddress,
      saveAddress,
      deleteAddress,
      handleChangeRegionState,
      handleCascaderVisibleChange
    };
  }
};
</script>

<style scoped lang="scss">
.btn-group {
  margin-bottom: 16px;
}
</style>
