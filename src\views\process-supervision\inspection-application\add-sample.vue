<template>
  <el-drawer
    v-model="showDrawer"
    :title="titles"
    direction="rtl"
    :before-close="handleClose"
    size="50%"
    destroy-on-close
    :close-on-click-modal="false"
    custom-class="addSampleDrawer"
  >
    <DrawerLayout v-loading="drawerLoading" :has-button-group="false" :has-page-header="false">
      <el-form
        ref="formRef"
        v-loading="drawerLoading"
        :model="formInline"
        class="form-height-auto formDataSample"
        :rules="addRules"
        :label-width="labelWidth"
        :label-position="position"
      >
        <el-row :gutter="40">
          <el-col :span="24">
            <el-form-item class="full-item" label-width="0">
              <el-descriptions :column="2" border>
                <template v-if="!isCheck" #title>
                  <el-button
                    type="primary"
                    size="small"
                    icon="el-icon-search"
                    @click="selectMaterial"
                    @keyup.prevent
                    @keydown.enter.prevent
                    >选择物料</el-button
                  >
                </template>
                <el-descriptions-item label-align="center">
                  <template #label>
                    <div class="cell-item">
                      <span class="el-icon-collection-tag" />
                      物料编号
                    </div>
                  </template>
                  {{ formInline.materialNo || '--' }}
                </el-descriptions-item>
                <el-descriptions-item label-align="center">
                  <template #label>
                    <div class="cell-item">
                      <span class="el-icon-share" />
                      物料分组
                    </div>
                  </template>
                  {{ formInline.materialGroup || formInline.materialGroupId || '--' }}
                </el-descriptions-item>
              </el-descriptions>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="物料名称：">
              <el-input v-if="!isCheck" v-model="formInline.materialDesc" />
              <div v-else class="nowrap">{{ formInline.materialDesc || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="型号：">
              <el-input v-if="!isCheck" v-model="formInline.model" placeholder="" class="" />
              <div v-else class="nowrap">{{ formInline.model || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格：">
              <el-input v-if="!isCheck" v-model="formInline.specifications" placeholder="" class="" />
              <div v-else class="nowrap">{{ formInline.specifications || '--' }}</div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="送样数量：" prop="sampleQuantity">
              <el-input-number
                v-if="!isCheck"
                v-model="formInline.sampleQuantity"
                controls-position="right"
                :precision="3"
                :step="0.001"
                :min="0"
                size="small"
                style="width: 60%"
                @change="changeSampleNum(formInline.sampleQuantity, 1)"
              />
              <span v-else> {{ formInline.sampleQuantity || '--' }} </span>
              <el-select
                v-if="!isCheck"
                v-model="formInline.sampleUnit"
                placeholder="请选择"
                size="small"
                style="width: 40%; padding-left: 8px"
                @change="changeSampleNum(formInline.sampleUnit, 2)"
              >
                <el-option-group v-for="item in optionsArray" :key="item.label" :label="item.label">
                  <el-option
                    v-for="val in item.group"
                    :key="val.id"
                    :label="val.name"
                    :value="val.code"
                    :disabled="val.status !== 1"
                  >
                    <span style="float: left">{{ val.name }}</span>
                    <span v-if="val.status !== 1" class="fr" style="color: #f56c6c">已停用</span>
                  </el-option>
                </el-option-group>
              </el-select>
              <span v-else>{{ filterUnit(formInline.sampleUnit) }}</span>
            </el-form-item>
          </el-col>
          <el-col v-if="type === 1 || type === 8" :span="12">
            <el-form-item label="入库数量：" prop="inputWarehouseQuantity">
              <el-input-number
                v-if="!isCheck"
                v-model="formInline.inputWarehouseQuantity"
                controls-position="right"
                :precision="3"
                :step="0.001"
                :min="0"
                maxlength="18"
                size="small"
                style="width: 60%"
                @change="changeInputWarehouseNum(formInline.inputWarehouseQuantity, 1)"
              />
              <span v-else> {{ formInline.inputWarehouseQuantity || '--' }} </span>
              <el-select
                v-if="!isCheck"
                v-model="formInline.inputWarehouseUnit"
                placeholder="请选择"
                size="small"
                style="width: 40%; padding-left: 8px"
                @change="changeInputWarehouseNum(formInline.inputWarehouseUnit, 2)"
              >
                <el-option-group v-for="item in optionsArray" :key="item.label" :label="item.label">
                  <el-option
                    v-for="val in item.group"
                    :key="val.id"
                    :label="val.name"
                    :value="val.code"
                    :disabled="val.status !== 1"
                  >
                    <span style="float: left">{{ val.name }}</span>
                    <span v-if="val.status !== 1" class="fr" style="color: #f56c6c">已停用</span>
                  </el-option>
                </el-option-group>
              </el-select>
              <span v-else>{{ filterUnit(formInline.inputWarehouseUnit) }}</span>
            </el-form-item>
          </el-col>
          <el-col v-if="type !== 1 && type !== 8" :span="12">
            <el-form-item label="生产数量：" prop="productionQuantity">
              <el-input-number
                v-if="!isCheck"
                v-model="formInline.productionQuantity"
                controls-position="right"
                :precision="3"
                :step="0.001"
                :min="0"
                size="small"
                style="width: 60%"
                @change="changeProductionNum(formInline.productionQuantity, 1)"
              />
              <span v-else> {{ formInline.productionQuantity || '--' }} </span>
              <el-select
                v-if="!isCheck"
                v-model="formInline.productionUnit"
                placeholder="请选择"
                size="small"
                style="width: 40%; padding-left: 8px"
                @change="changeProductionNum(formInline.productionUnit, 2)"
              >
                <el-option-group v-for="item in optionsArray" :key="item.label" :label="item.label">
                  <el-option
                    v-for="val in item.group"
                    :key="val.code"
                    :label="val.name"
                    :value="val.code"
                    :disabled="val.status !== 1"
                  >
                    <span style="float: left">{{ val.name }}</span>
                    <span v-if="val.status !== 1" class="fr" style="color: #f56c6c">已停用</span>
                  </el-option>
                </el-option-group>
              </el-select>
              <span v-else>{{ filterUnit(formInline.productionUnit) }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="批次：" prop="batchNo">
              <el-input v-if="!isCheck" v-model="formInline.batchNo" v-trim placeholder="" class="" />
              <div v-else class="nowrap">{{ formInline.batchNo || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col v-if="type === 1 || type === 8" :span="12">
            <el-form-item label="供应商批次：" prop="supplierBatchNo">
              <el-input v-if="!isCheck" v-model="formInline.supplierBatchNo" placeholder="" class="" />
              <div v-else class="nowrap">{{ formInline.supplierBatchNo || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col v-if="type !== 1 && type !== 8" :span="12">
            <el-form-item label="抽样序号：">
              <template #label>
                <span class="panhao">
                  <el-tooltip content="生产批次" placement="top" effect="dark">
                    <i class="iconfont tes-title" />
                  </el-tooltip>
                </span>
                <span>抽样序号：</span>
              </template>
              <!-- <el-tooltip content="生产批次" placement="top" effect="dark">
              <i class="iconfont tes-title cyxh-icon" />
            </el-tooltip> -->
              <el-input v-if="!isCheck" v-model="formInline.productionBatchNo" v-trim placeholder="" class="" />
              <div v-else class="nowrap">{{ formInline.productionBatchNo || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="reelNo" style="position: relative">
              <template #label>
                <span class="panhao">
                  <el-tooltip content="部分厂商对应ID号" placement="top" effect="dark">
                    <i class="iconfont tes-title" />
                  </el-tooltip>
                </span>
                <span>盘号：</span>
              </template>
              <el-input v-if="!isCheck" v-model="formInline.reelNo" placeholder="" class="" />
              <div v-else class="nowrap">{{ formInline.reelNo || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col v-if="type !== 1 && type !== 8" :span="12">
            <el-form-item label="生产盘号：">
              <el-input v-if="!isCheck" v-model="formInline.productionReelNo" placeholder="" class="" />
              <div v-else class="nowrap">{{ formInline.productionReelNo || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col v-if="type !== 1 && type !== 8" :span="12">
            <el-form-item label="工艺参数：">
              <el-input v-if="!isCheck" v-model="formInline.craftParameter" placeholder="" class="" />
              <div v-else class="nowrap">{{ formInline.craftParameter || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col v-if="type === 1 || type === 8" :span="12">
            <el-form-item label="入库行项目号：" prop="inputWarehouseItemNo">
              <el-input v-if="!isCheck" v-model="formInline.inputWarehouseItemNo" v-trim placeholder="" class="" />
              <div v-else class="nowrap">{{ formInline.inputWarehouseItemNo || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col v-if="type === 1 || type === 8" :span="12">
            <el-form-item label="采购单号：">
              <el-input v-if="!isCheck" v-model="formInline.purchaseNo" placeholder="" class="" />
              <div v-else class="nowrap">{{ formInline.purchaseNo || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col v-if="type === 1 || type === 8" :span="12">
            <el-form-item label="采购行号：">
              <el-input v-if="!isCheck" v-model="formInline.purchaseItemNo" placeholder="" class="" />
              <div v-else class="nowrap">{{ formInline.purchaseItemNo || '--' }}</div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <!-- 电压等级需要在字典里面添加相关数据才行，目前使用的是物资分类option，可以不选 -->
            <el-form-item label="电压等级：">
              <el-select
                v-if="!isCheck"
                v-model="formInline.voltageLevel"
                placeholder="请选择"
                clearable
                filterable
                style="width: 100%"
              >
                <el-option v-for="item in voltageList" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
              <div v-else class="nowrap">{{ filterVoltage(formInline.voltageLevel) || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物资分类：" prop="materialId">
              <el-select
                v-if="!isCheck"
                v-model="formInline.materialId"
                placeholder="请选择"
                clearable
                filterable
                style="width: 100%"
                @change="changeMaterial"
              >
                <el-option v-for="item in materialList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
              <div v-else class="nowrap">{{ filterMaterialType(formInline.materialId) || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注：">
              <el-input
                v-if="!isCheck"
                v-model="formInline.remark"
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                maxlength="300"
                show-word-limit
              />
              <div v-else class="nowrap">{{ formInline.remark || '--' }}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="drawer-fotter">
        <el-button
          v-if="!isCheck"
          type="primary"
          :loading="drawerLoading"
          @click="onSubmit"
          @keyup.prevent
          @keydown.enter.prevent
          >保存</el-button
        >
        <el-button :loading="drawerLoading" @click="handleClose">取消</el-button>
      </div>
      <!-- 选择物料 -->
      <add-material
        :show="showMaterial"
        :tree="materialTree"
        :material-no="formInline.materialNo"
        @selectData="selectMaterialData"
        @close="closeMaterial"
      />
    </DrawerLayout>
  </el-drawer>
</template>

<script>
import { ref, watch, reactive, toRefs } from 'vue';
import { addSample, updateSample } from '@/api/inspection-application';
import { ElMessage } from 'element-plus';
import { getDictionary } from '@/api/user';
import { useStore } from 'vuex';
import { getWlGroupNew } from '@/api/mas';
import { formatMaterialTree } from '@/utils/formatJson';
import AddMaterial from './components/add-material.vue';
// import { useRoute } from 'vue-router'
// components
import DrawerLayout from '@/components/DrawerLayout';

export default {
  name: 'AddSample',
  components: { AddMaterial, DrawerLayout },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    type: {
      type: Number,
      default: 1
    },
    inspectionId: {
      type: String,
      default: '000'
    },
    title: {
      type: String,
      required: true
    },
    editData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    // const { appContext } = getCurrentInstance()
    // const bus = appContext.config.globalProperties.bus
    // console.log(appContext)
    // console.log(bus)
    // const route = useRoute()
    const store = useStore().state;
    // 样品数量、样品单位校验
    var validateSample = (rule, value, callback) => {
      if (!datas.formInline.sampleQuantity) {
        callback(new Error('请输入样品数量'));
      } else if (!datas.formInline.sampleUnit) {
        callback(new Error('请选择样品单位'));
      } else {
        callback();
      }
    };
    // 入库数量、入库单位校验
    var validateInputWarehouse = (rule, value, callback) => {
      if (!datas.formInline.inputWarehouseQuantity) {
        callback(new Error('请输入入库数量'));
      } else if (!datas.formInline.inputWarehouseUnit) {
        callback(new Error('请选择入库单位'));
      } else {
        callback();
      }
    };
    // 生产数量、生产单位校验
    var validateProduction = (rule, value, callback) => {
      if (!datas.formInline.productionQuantity) {
        callback(new Error('请输入生产数量'));
      } else if (!datas.formInline.productionUnit) {
        callback(new Error('请选择生产单位'));
      } else {
        callback();
      }
    };
    const datas = reactive({
      showDrawer: false,
      titles: '新增样品',
      formRef: ref(),
      formInline: {
        inspectionId: '',
        materialNo: ''
      },
      position: 'top',
      labelWidth: '110px',
      drawerLoading: false,
      addRules: {
        materialNo: [{ required: true, message: '请选择物料' }],
        materialGroupId: [{ required: true, message: '请选择物料' }],
        materialDesc: [{ required: true, message: '请选择物料' }],
        sampleQuantity: [{ required: true, validator: validateSample }],
        inputWarehouseQuantity: [{ required: true, validator: validateInputWarehouse }],
        inputWarehouseItemNo: [{ required: false, message: '请输入入库行项目号' }],
        productionQuantity: [{ required: true, validator: validateProduction }],
        batchNo: [{ required: true, message: '请输入批次' }],
        productionBatchNo: [{ required: false, message: '请输入抽样序号' }],
        materialId: [{ required: true, message: '请选择物资分类' }]
      },
      options: [],
      defaultValue: '', // 单位字典项默认code
      defaultVolt: '', // 电压等级的默认字典值
      optionsArray: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ],
      isCheck: false, // 是否是查看页面
      materialList: store.user.materialList,
      materialTree: [],
      allMaterialTree: [],
      materialItem: {},
      showMaterial: false,
      voltageList: []
    });

    // console.log(datas.materialList)

    watch(
      () => props.drawer,
      newValue => {
        // console.log(props)
        // console.log(newValue)
        if (newValue) {
          datas.showDrawer = newValue;
          datas.titles = props.title;
          if (datas.titles === '查看样品') {
            datas.isCheck = true;
            datas.position = 'right';
            datas.labelWidth = 'auto';
          } else {
            datas.isCheck = false;
            datas.position = 'top';
            datas.labelWidth = '110px';
          }
          datas.formInline = props.editData;
          datas.formInline.inspectionId = props.inspectionId;
          if (!datas.formInline.sampleUnit) {
            datas.formInline.sampleUnit = datas.defaultValue;
          }
          if (!datas.formInline.inputWarehouseUnit) {
            datas.formInline.inputWarehouseUnit = datas.defaultValue;
          }
          if (!datas.formInline.productionUnit) {
            datas.formInline.productionUnit = datas.defaultValue;
          }

          if (!datas.isCheck) {
            if (!datas.formInline.voltageLevel) {
              datas.formInline.voltageLevel = datas.defaultVolt;
            }
          }

          if (!datas.formInline.materialId && datas.materialList.length > 0) {
            datas.formInline.materialId = datas.materialList[0].id;
            changeMaterial(datas.formInline.materialId);
          }
        }
      },
      { deep: true }
    );

    // 过滤分组数据
    const filterGroupById = id => {
      // console.log(id)
      // console.log(datas.allMaterialTree)
      var groupName = '';
      if (datas.allMaterialTree.length > 0) {
        datas.allMaterialTree.forEach(tree => {
          if (tree.id === id) {
            groupName = tree.name;
          }
        });
      }
      return groupName;
    };

    // 过滤单位
    const filterUnit = unitId => {
      var unitName = '';
      if (unitId && datas.options.length > 0) {
        datas.options.forEach(opt => {
          if (opt.code === unitId) {
            unitName = opt.name;
          }
        });
      }
      return unitName;
    };

    // 过滤电压等级
    const filterVoltage = code => {
      var voltageName = '';
      if (code && datas.voltageList.length > 0) {
        datas.voltageList.forEach(item => {
          // console.log(item)
          if (item.code === code) {
            voltageName = item.name;
          }
        });
      }
      return voltageName;
    };

    // 过滤物资分类
    const filterMaterialType = materialId => {
      var materialName = '';
      if (materialId && datas.materialList.length > 0) {
        datas.materialList.forEach(item => {
          if (item.id === materialId) {
            materialName = item.name;
          }
        });
      }
      return materialName;
    };

    // 确认新增
    const onSubmit = () => {
      // console.log(datas.formInline)
      if (!datas.formInline.materialNo) {
        ElMessage.error('请选择物料');
        return false;
      }
      // console.log(datas.formRef)
      datas.formRef.validate(valid => {
        // console.log(valid)
        if (valid) {
          if (props.title === '新增样品') {
            datas.drawerLoading = true;
            addSample(datas.formInline).then(function (res) {
              datas.drawerLoading = false;
              // console.log(res)
              if (res !== false) {
                context.emit('setInfo', datas.formInline);
                context.emit('close', false);
                ElMessage.success('新增成功');
                datas.showDrawer = false;
              }
            });
          } else {
            datas.drawerLoading = true;
            updateSample(datas.formInline).then(function (res) {
              datas.drawerLoading = false;
              // console.log(res)
              if (res !== false) {
                context.emit('setInfo', datas.formInline);
                context.emit('close', false);
                ElMessage.success('编辑成功');
                datas.showDrawer = false;
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    // 关闭抽屉
    const handleClose = () => {
      datas.showDrawer = false;
      context.emit('close', false);
    };
    // change样品数量 样品单位
    const changeSampleNum = (value, flag) => {
      // console.log(value)
      if (flag === 1) {
        datas.formInline.sampleQuantity = value;
      } else {
        datas.formInline.sampleUnit = value;
        datas.optionsArray[0].group.forEach(opt => {
          if (opt.code === value) {
            datas.formInline.sampleUnitName = opt.name;
          }
        });
        datas.optionsArray[1].group.forEach(opt => {
          if (opt.code === value) {
            datas.formInline.sampleUnitName = opt.name;
          }
        });
      }
      // context.emit('setInfo', datas.formInline)
    };
    // 入库数量-入库单位
    const changeInputWarehouseNum = (value, flag) => {
      if (flag === 1) {
        datas.formInline.inputWarehouseQuantity = value;
      } else {
        datas.formInline.inputWarehouseUnit = value;
        datas.optionsArray[0].group.forEach(opt => {
          if (opt.code === value) {
            datas.formInline.inputWarehouseUnitName = opt.name;
          }
        });
        datas.optionsArray[1].group.forEach(opt => {
          if (opt.code === value) {
            datas.formInline.inputWarehouseUnitName = opt.name;
          }
        });
      }
      // context.emit('setInfo', datas.formInline)
    };
    // 生产数量-生产单位
    const changeProductionNum = (value, flag) => {
      if (flag === 1) {
        datas.formInline.productionQuantity = value;
      } else {
        datas.formInline.productionUnit = value;
        datas.optionsArray[0].group.forEach(opt => {
          if (opt.code === value) {
            datas.formInline.productionUnitName = opt.name;
          }
        });
        datas.optionsArray[1].group.forEach(opt => {
          if (opt.code === value) {
            datas.formInline.productionUnitName = opt.name;
          }
        });
      }
      // context.emit('setInfo', datas.formInline)
    };
    // 选择物料
    const selectMaterial = () => {
      datas.showMaterial = true;
    };
    // 物资分类-change
    const changeMaterial = id => {
      datas.materialList.forEach(material => {
        if (material.id === id) {
          datas.formInline.materialCode = material.code;
        }
      });
    };
    // 选择物料事件
    const selectMaterialData = data => {
      datas.materialItem = data;
      datas.formInline.materialNo = data.no;
      datas.formInline.materialGroupId = data.materialGroupNo;
      datas.formInline.materialDesc = data.name;
      datas.formInline.materialGroup = data.materialGroupName; // filterGroupById(data.groupId)
      datas.formInline.model = data.model || data.name.split(' / ')[0];
      datas.formInline.specifications = data.specification || data.name.split(' / ')[1];
    };
    // 关闭物料选择框
    const closeMaterial = value => {
      // console.log(value)
      datas.showMaterial = value;
    };

    return {
      ...toRefs(datas),
      onSubmit,
      handleClose,
      changeSampleNum,
      changeProductionNum,
      filterGroupById,
      filterUnit,
      filterMaterialType,
      filterVoltage,
      changeInputWarehouseNum,
      selectMaterial,
      changeMaterial,
      selectMaterialData,
      closeMaterial
    };
  },
  created() {
    this.getSampleUnits();
    this.getMaterialGroup();
    this.getVoltage();
  },
  methods: {
    getSampleUnits() {
      // 从tes字典里面读取样品单位
      const that = this;
      getDictionary(5).then(res => {
        // console.log(res.data.items)
        that.optionsArray[0].group = [];
        that.optionsArray[1].group = [];
        res.data.data.dictionaryoption.forEach(item => {
          if (item.status === 1) {
            that.optionsArray[0].group.push(item);
          } else {
            that.optionsArray[1].group.push(item);
          }
        });
        if (that.optionsArray[0].group.length > 0) {
          that.defaultValue = that.optionsArray[0].group[0].code;
        }
        that.options = res.data.data.dictionaryoption;
      });
    },
    getMaterialGroup() {
      // 从lims里面获取物料分组
      const that = this;
      const param = {
        limit: '-1',
        page: 1
      };
      getWlGroupNew(param).then(res => {
        if (res !== false) {
          that.allMaterialTree = res.data.data.list;
          that.materialTree = formatMaterialTree(res.data.data.list);
          const allParam = {
            id: 'all',
            parentNo: '',
            remark: '',
            status: 1,
            no: '',
            name: '全部'
          };
          that.materialTree.unshift(allParam);
          // console.log(that.materialTree)
        }
      });
    },
    getVoltage() {
      var that = this;
      getDictionary(2).then(res => {
        that.voltageList = res.data.data.dictionaryoption;
        that.defaultVolt = that.voltageList.filter(item => item.status === 1)[0].code;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.cyxh-icon {
  position: absolute;
  left: -9px;
}
.formDataSample {
  text-align: left;
  .panhao {
    margin-right: 4px;
  }
  :deep(.el-form-item) {
    margin-right: 0;

    & .el-form-item__label-wrap {
      float: left;
    }
  }
  :deep(.el-form-item__content) {
    display: block;
  }
}
</style>
<style lang="scss">
.page-drawer .el-drawer__header {
  padding: 20px 40px 0px !important;
}
.page-drawer .el-drawer__body {
  padding: 10px 40px !important;
  margin-bottom: 50px;
}
</style>
