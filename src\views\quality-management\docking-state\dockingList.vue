<template>
  <!-- 国网EIP对接 -->
  <ListLayout :has-button-group="false" :has-search-panel="false">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="queryStr">
          <el-input
            v-model="formInline.queryStr"
            v-trim
            v-focus
            placeholder="请输入搜索条件"
            class="ipt-360"
            prefix-icon="el-icon-search"
            size="large"
            clearable
            @keyup.enter="getTableList()"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="getTableList()">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #radio-content>
      <el-radio-group v-model="uploadSdccFlag" size="small" style="float: left" @change="changeStatus">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button v-for="(value, key, index) in statusList" :key="index" :label="key"
          >{{ value }}
        </el-radio-button>
      </el-radio-group>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      height="auto"
      size="medium"
      highlight-current-row
      class="dark-table format-height-table base-table"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column label="序号" fixed="left" :width="colWidth.checkbox" align="center">
        <template #default="{ $index }">
          <div>{{ $index + 1 }}</div>
        </template>
      </el-table-column>
      <el-table-column label="生产订单号" prop="no" :min-width="colWidth.orderNo" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.no || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="销售订单号" prop="salesOrderNo" :min-width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.salesOrderNo || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="物资类型"
        prop="materialSdccTypeName"
        :min-width="colWidth.typeGroup"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.materialSdccTypeName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="销售订单行项目号"
        prop="salesOrderItemNo"
        :min-width="colWidth.orderNo"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.salesOrderItemNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目名称" prop="projectName" :width="colWidth.projectName" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.projectName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户名称" prop="customerName" :width="colWidth.customerName" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.customerName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="成品检验状态" prop="endProductStatus" :width="colWidth.status" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag size="small" :type="row.endProductStatus ? 'success' : 'warning'" effect="dark">{{
            row.endProductStatus ? '已上传' : '待上传'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="过程检验状态" prop="processStatus" :width="colWidth.status" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag size="small" :type="row.processStatus ? 'success' : 'warning'" effect="dark">{{
            row.processStatus ? '已上传' : '待上传'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="原材料检验状态" prop="materialsStatus" :width="colWidth.status" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag size="small" :type="row.materialsStatus ? 'success' : 'warning'" effect="dark">{{
            row.materialsStatus ? '已上传' : '待上传'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        prop="testTime"
        :width="colWidth.operationSingle"
        fixed="right"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span class="blue-color" @click="handleRoute(row)">查看</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getTableList" />
  </ListLayout>
</template>

<script>
// getCurrentInstance
import { reactive, ref, toRefs } from 'vue';
import Pagination from '@/components/Pagination';
import { getList } from '@/api/sdcc';
import { getNameByid, getNamesByid, getPermissionBtn } from '@/utils/common';
import { formatDateTime } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import ListLayout from '@/components/ListLayout';
import { colWidth } from '@/data/tableStyle';
import router from '@/router/index.js';

export default {
  name: 'DockingList',
  components: { Pagination, ListLayout },
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    const state = reactive({
      tableData: [],
      uploadSdccFlag: '',
      orderBy: '', // 排序字段
      order: '', // 是否倒叙
      tableRef: ref(),
      dialogCode: false,
      codeLoading: false,
      formInline: {
        queryStr: ''
      },
      editFrom: ref(0),
      total: 0,
      searchFromRef: ref(),
      showS: false,
      tableLoading: false, // 表格加载的loading
      dialogFrom: {}, // 操作树节点的弹窗表格
      listQuery: {
        page: 1,
        limit: 20
      },
      qrCodeList: [],
      statusList: {
        false: '待上传',
        true: '已上传'
      }, // 类型
      moreIndex: 0
    });
    const tableKey = ref(0);
    const changeStatus = val => {
      getTableList();
    };
    const sortChange = column => {
      state.orderBy = column.prop;
      if (column.order === 'descending') {
        state.order = 'desc';
      } else {
        state.order = 'asc';
      }
      getTableList();
    };
    const reset = () => {
      state.formInline = {};
      state.orderBy = '';
      state.order = '';
      state.listQuery.page = 1;
      state.listQuery.limit = 20;
      getTableList();
    };

    const getTableList = query => {
      var params = {
        uploadSdccFlag: state.uploadSdccFlag,
        order: state.order,
        sort: state.orderBy,
        ...state.formInline
      };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        params.page = state.listQuery.page.toString();
        params.limit = state.listQuery.limit.toString();
      }
      if (params.uploadSdccFlag) {
        params.uploadSdccFlag = params.uploadSdccFlag === 'true';
      }
      state.tableLoading = true;
      getList(params).then(res => {
        state.tableLoading = false;
        if (res) {
          state.tableData = res.data.data.list;
          state.total = res.data.data.totalCount;
        }
      });
    };
    getTableList();
    const handleRoute = row => {
      localStorage.setItem('productionOrderInfo', JSON.stringify(row));
      router.push({
        path: '/qualityManagement/dockingDetail',
        query: {
          productionNo: 'PRD_0013'
        }
      });
    };
    return {
      ...toRefs(state),
      sortChange,
      handleRoute,
      colWidth,
      getPermissionBtn,
      changeStatus,
      getTableList,
      formatDateTime,
      getNameByid,
      getNamesByid,
      drageHeader,
      tableKey,
      reset
    };
  }
};
</script>
<style lang="scss" scoped></style>
<style lang="scss"></style>
