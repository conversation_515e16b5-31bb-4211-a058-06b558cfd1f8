:export {
  menuText: #bfcbd9;
  menuActiveText: #409eff;
  subMenuActiveText: #f4f4f5;
  menuBg: #304156;
  menuHover: #263445;
  subMenuBg: #1f2d3d;
  subMenuHover: #001528;
  sideBarWidth: 210px;
}

/* fade */

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.28s;
}

.fade-enter,
.fade-leave-active {
  opacity: 0;
}

/* fade-transform */

.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* breadcrumb transition */

.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all 0.5s;
}

.breadcrumb-enter,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all 0.5s;
}

.breadcrumb-leave-active {
  position: absolute;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload input[type='file'] {
  display: none !important;
}

.el-upload__input {
  display: none;
}

.cell .el-tag {
  margin-right: 4px;
}

.small-padding .cell {
  padding-left: 5px;
  padding-right: 5px;
}

.fixed-width .el-button--mini {
  padding: 7px 10px;
  min-width: 60px;
}

.status-col .cell {
  padding: 0 10px;
  text-align: center;
}

/* .status-col .cell .el-tag {
    margin-right: 0px;
} */

.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

.upload-container .el-upload {
  width: 100%;
}

.upload-container .el-upload .el-upload-dragger {
  width: 100%;
  height: 200px;
}

.el-dropdown-menu a {
  display: block;
}

.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

.el-range-separator {
  box-sizing: content-box;
}

#app .main-container {
  min-height: 100%;
  transition: margin-left 0.28s;
  margin-left: 210px;
  position: relative;
}

#app .sidebar-container {
  transition: width 0.28s;
  width: 210px !important;
  background-color: #304156;
  height: 100%;
  position: fixed;
  font-size: 0px;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  overflow: hidden;
}

#app .sidebar-container .horizontal-collapse-transition {
  transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
}

#app .sidebar-container .scrollbar-wrapper {
  overflow-x: hidden !important;
}

#app .sidebar-container .el-scrollbar__bar.is-vertical {
  right: 0px;
}

#app .sidebar-container .el-scrollbar {
  height: 100%;
}

#app .sidebar-container.has-logo .el-scrollbar {
  height: calc(100% - 50px);
}

#app .sidebar-container .is-horizontal {
  display: none;
}

#app .sidebar-container a {
  display: inline-block;
  width: 100%;
  overflow: hidden;
}

#app .sidebar-container .svg-icon {
  margin-right: 16px;
}

#app .sidebar-container .sub-el-icon {
  margin-right: 12px;
  margin-left: -2px;
}

#app .sidebar-container .el-menu {
  border: none;
  height: 100%;
  width: 100% !important;
}

#app .sidebar-container .submenu-title-noDropdown:hover,
#app .sidebar-container .el-sub-menu__title:hover {
  background-color: #263445 !important;
}

#app .sidebar-container .is-active > .el-sub-menu__title {
  color: #f4f4f5 !important;
}

#app .sidebar-container .nest-menu .el-sub-menu > .el-sub-menu__title,
#app .sidebar-container .el-sub-menu .el-menu-item {
  min-width: 210px !important;
  background-color: #1f2d3d !important;
}

#app .sidebar-container .nest-menu .el-sub-menu > .el-sub-menu__title:hover,
#app .sidebar-container .el-sub-menu .el-menu-item:hover {
  background-color: #001528 !important;
}

#app .hideSidebar .sidebar-container {
  width: 54px !important;
}

#app .hideSidebar .main-container {
  margin-left: 54px;
}

#app .hideSidebar .submenu-title-noDropdown {
  padding: 0 !important;
  position: relative;
}

#app .hideSidebar .submenu-title-noDropdown .el-tooltip {
  padding: 0 !important;
}

#app .hideSidebar .submenu-title-noDropdown .el-tooltip .svg-icon {
  margin-left: 20px;
}

#app .hideSidebar .submenu-title-noDropdown .el-tooltip .sub-el-icon {
  margin-left: 19px;
}

#app .hideSidebar .el-sub-menu {
  overflow: hidden;
}

#app .hideSidebar .el-sub-menu > .el-sub-menu__title {
  padding: 0 !important;
}

#app .hideSidebar .el-sub-menu > .el-sub-menu__title .svg-icon {
  margin-left: 20px;
}

#app .hideSidebar .el-sub-menu > .el-sub-menu__title .sub-el-icon {
  margin-left: 19px;
}

#app .hideSidebar .el-sub-menu > .el-sub-menu__title .el-sub-menu__icon-arrow {
  display: none;
}

#app .hideSidebar .el-menu--collapse .el-sub-menu > .el-sub-menu__title > span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}

#app .el-menu--collapse .el-menu .el-sub-menu {
  min-width: 210px !important;
}

#app .mobile .main-container {
  margin-left: 0px;
}

#app .mobile .sidebar-container {
  transition: transform 0.28s;
  width: 210px !important;
}

#app .mobile.hideSidebar .sidebar-container {
  pointer-events: none;
  transition-duration: 0.3s;
  transform: translate3d(-210px, 0, 0);
}

#app .withoutAnimation .main-container,
#app .withoutAnimation .sidebar-container {
  transition: none;
}

.el-menu--vertical > .el-menu .svg-icon {
  margin-right: 16px;
}

.el-menu--vertical > .el-menu .sub-el-icon {
  margin-right: 12px;
  margin-left: -2px;
}

.el-menu--vertical .nest-menu .el-sub-menu > .el-sub-menu__title:hover,
.el-menu--vertical .el-menu-item:hover {
  background-color: #263445 !important;
}

.el-menu--vertical > .el-menu--popup {
  max-height: 100vh;
  overflow-y: auto;
}

.el-menu--vertical > .el-menu--popup::-webkit-scrollbar-track-piece {
  background: #d3dce6;
}

.el-menu--vertical > .el-menu--popup::-webkit-scrollbar {
  width: 6px;
}

.el-menu--vertical > .el-menu--popup::-webkit-scrollbar-thumb {
  background: #99a9bf;
  border-radius: 20px;
}

:export {
  menuText: #bfcbd9;
  menuActiveText: #409eff;
  subMenuActiveText: #f4f4f5;
  menuBg: #304156;
  menuHover: #263445;
  subMenuBg: #1f2d3d;
  subMenuHover: #001528;
  sideBarWidth: 210px;
}

.blue-btn {
  background: #324157;
}

.blue-btn:hover {
  color: #324157;
}

.blue-btn:hover:before,
.blue-btn:hover:after {
  background: #324157;
}

.light-blue-btn {
  background: #3a71a8;
}

.light-blue-btn:hover {
  color: #3a71a8;
}

.light-blue-btn:hover:before,
.light-blue-btn:hover:after {
  background: #3a71a8;
}

.red-btn {
  background: #c03639;
}

.red-btn:hover {
  color: #c03639;
}

.red-btn:hover:before,
.red-btn:hover:after {
  background: #c03639;
}

.pink-btn {
  background: #e65d6e;
}

.pink-btn:hover {
  color: #e65d6e;
}

.pink-btn:hover:before,
.pink-btn:hover:after {
  background: #e65d6e;
}

.green-btn {
  background: #30b08f;
}

.green-btn:hover {
  color: #30b08f;
}

.green-btn:hover:before,
.green-btn:hover:after {
  background: #30b08f;
}

.tiffany-btn {
  background: #4ab7bd;
}

.tiffany-btn:hover {
  color: #4ab7bd;
}

.tiffany-btn:hover:before,
.tiffany-btn:hover:after {
  background: #4ab7bd;
}

.yellow-btn {
  background: #fec171;
}

.yellow-btn:hover {
  color: #fec171;
}

.yellow-btn:hover:before,
.yellow-btn:hover:after {
  background: #fec171;
}

.pan-btn {
  font-size: 14px;
  color: #fff;
  padding: 14px 36px;
  border-radius: 8px;
  border: none;
  outline: none;
  transition: 600ms ease all;
  position: relative;
  display: inline-block;
}

.pan-btn:hover {
  background: #fff;
}

.pan-btn:hover:before,
.pan-btn:hover:after {
  width: 100%;
  transition: 600ms ease all;
}

.pan-btn:before,
.pan-btn:after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 2px;
  width: 0;
  transition: 400ms ease all;
}

.pan-btn::after {
  right: inherit;
  top: inherit;
  left: 0;
  bottom: 0;
}

.custom-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  color: #fff;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  padding: 10px 15px;
  font-size: 14px;
  border-radius: 4px;
}

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: 'MiSans', 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif, iconfont;
  font-variant-numeric: tabular-nums;
  font-feature-settings: 'tnum';
  letter-spacing: -0.04em;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: ' ';
  clear: both;
  height: 0;
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: 'MiSans', 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif, iconfont;
  color: #303133;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

aside a {
  color: #337ab7;
  cursor: pointer;
}

aside a:hover {
  color: #20a0ff;
}

.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, #20b6f9 0%, #20b6f9 0%, #2178f1 100%, #2178f1 100%);
}

.sub-navbar .subtitle {
  font-size: 20px;
  color: #fff;
}

.sub-navbar.draft {
  background: #d0d0d0;
}

.sub-navbar.deleted {
  background: #d0d0d0;
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;
}

.link-type:hover,
.link-type:focus:hover {
  color: #20a0ff;
}

.filter-container {
  border: 1px solid #dfe6ec;
  margin-bottom: 15px;
  margin-top: 15px;
  padding: 10px;
}

.filter-container .filter-item {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 10px;
}

.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

/*# sourceMappingURL=index.css.map */
