variables:
  MODULE_NAME: cxist-tes
  REGISTRY_HOST: hub-internal.cxist.com:5000/
  IMAGE_PREFIX: byzan/xlab
  IMAGE_NAME: web-cxist-tes
  DEPLOY_DIR_BASE: byzan-deploy/web-cxist-tes
  DEPLOY_TARGET: root@**************
  DEPLOY_TARGET_PORT: "22"
  STACK_NAME: cx-main

stages:
  - prepare
  - lint
  - build_src
  - build_image
  - deploy

prepare:
  stage: prepare
  rules:
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
  tags:
    - node
  script:
    - node --version

lint:
  stage: lint
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: never
    - if: '$CI_COMMIT_BRANCH == "dev"'
    - if: '$CI_COMMIT_BRANCH == "test"'
  tags:
    - node
  script:
    - node --version
    - npm --version
#    - rm -rf node_modules/
#    - npm install
    - npm ci --no-audit --no-progress --loglevel=error
#   - npm run lint

build_src:
  stage: build_src
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: $CI_COMMIT_TAG
    - if: '$CI_COMMIT_BRANCH == "test"'
  tags:
    - node
  variables:
    GIT_STRATEGY: none
  script:
#    - npm i --prefer-offline=true --no-audit --no-progress --loglevel=error
    - npm install
    - npm run build1
    - cp ./tools/CI/docker/* ./tools/CI/nginx/default.conf ./dist/
  artifacts:
    name: "dist-$CI_COMMIT_REF_SLUG-$CI_PIPELINE_ID"
    paths:
      - ./dist

build_image:
  after_script:
    - rm -rf ./dist
  stage: build_image
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: $CI_COMMIT_TAG
    - if: '$CI_COMMIT_BRANCH == "test"'
  tags:
    - docker
  variables:
    GIT_STRATEGY: none
    #BASE_IMAGE: byzan/nginx:byz-nightly
    BASE_IMAGE: byzan/nginx:byz-nightly
    IMAGE_TAG: test
    PUSH_LATEST: "false"
  script:
    - if [ $CI_COMMIT_REF_NAME == dev ]; then IMAGE_TAG=dev; DEPLOY_TARGET=root@*************; fi
    - if [ $CI_COMMIT_REF_NAME == test ]; then IMAGE_TAG=test; DEPLOY_TARGET=root@**************; fi
    - if [ $CI_COMMIT_REF_NAME == master ]; then BASE_IMAGE=byzan/nginx:byz-prod; IMAGE_TAG=prod; PUSH_LATEST=true; fi
    #- if [ $CI_COMMIT_REF_NAME == test ]; then BASE_IMAGE=byzan/nginx:byz-test; IMAGE_TAG=test; PUSH_LATEST=true; fi
    - IMAGE_FULL_NAME=$IMAGE_PREFIX/$IMAGE_NAME
#    - bash ./dist/build-image.sh --base-image $BASE_IMAGE --image-name $IMAGE_FULL_NAME --env $IMAGE_TAG --ci-commit-sha $CI_COMMIT_SHA --ci-pipeline-id $CI_PIPELINE_ID --registry "$REGISTRY_HOST"
#    - bash ./dist/push-image.sh --name $IMAGE_FULL_NAME --tag $IMAGE_TAG-$CI_PIPELINE_ID --latest $PUSH_LATEST --registry "$REGISTRY_HOST"
    - bash ./dist/build-image.sh --base-image $BASE_IMAGE --image-name $IMAGE_FULL_NAME --env $IMAGE_TAG --ci-commit-sha $CI_COMMIT_SHA --ci-pipeline-id $CI_PIPELINE_ID --registry "$REGISTRY_HOST"
    - bash ./dist/push-image.sh --name $IMAGE_FULL_NAME --tag $IMAGE_TAG --latest $PUSH_LATEST --registry "$REGISTRY_HOST"

  dependencies:
    - build_src

deploy_test:
  stage: deploy
  tags:
    - node
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
    - if: '$CI_COMMIT_BRANCH == "test"'
  variables:
    GIT_STRATEGY: none
    IMAGE_TAG: test-$CI_PIPELINE_ID
    TARGET_PROD: "false"
    STACK_NAME: $STACK_NAME
    API_GATEWAY: http://**************:8800
  script:
    - if [ $CI_COMMIT_REF_NAME == dev ]; then IMAGE_TAG=dev-$CI_PIPELINE_ID; API_GATEWAY=http://*************:8800; DEPLOY_TARGET=root@*************; fi
    - if [ $CI_COMMIT_REF_NAME == test ]; then IMAGE_TAG=test-$CI_PIPELINE_ID; API_GATEWAY=http://**************:8800; DEPLOY_TARGET=root@**************; fi
    - ssh -q -p $DEPLOY_TARGET_PORT $DEPLOY_TARGET "mkdir -p ~/$DEPLOY_DIR_BASE/$IMAGE_TAG;rm -rf ~/$DEPLOY_DIR_BASE/$IMAGE_TAG/*"
    - scp -q -P $DEPLOY_TARGET_PORT ./tools/CI/deploy/deploy.sh ./tools/CI/deploy/stack.yml $DEPLOY_TARGET:~/$DEPLOY_DIR_BASE/$IMAGE_TAG
    - ssh -p $DEPLOY_TARGET_PORT $DEPLOY_TARGET "bash $DEPLOY_DIR_BASE/$IMAGE_TAG/deploy.sh --registry \"$REGISTRY_HOST\" --name $IMAGE_NAME --tag $IMAGE_TAG --stack $STACK_NAME"
  environment:
    name: dev
  dependencies: []

deploy_master:
  stage: deploy
  tags:
    - node
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  variables:
    GIT_STRATEGY: none
    IMAGE_TAG: prod-$CI_PIPELINE_ID
    TARGET_PROD: "false"
    STACK_NAME: $STACK_NAME
    API_GATEWAY: http://**************:8800
  script:
    - if [ $CI_COMMIT_REF_NAME == master ]; then IMAGE_TAG=prod; TARGET_PROD=true; fi
    - ssh -q -p $DEPLOY_TARGET_PORT $DEPLOY_TARGET "mkdir -p ~/$DEPLOY_DIR_BASE/$IMAGE_TAG;rm -rf ~/$DEPLOY_DIR_BASE/$IMAGE_TAG/*"
    - scp -q -P $DEPLOY_TARGET_PORT ./tools/CI/deploy/deploy.sh ./tools/CI/deploy/stack.yml $DEPLOY_TARGET:~/$DEPLOY_DIR_BASE/$IMAGE_TAG
    - ssh -p $DEPLOY_TARGET_PORT $DEPLOY_TARGET "bash $DEPLOY_DIR_BASE/$IMAGE_TAG/deploy.sh --registry \"$REGISTRY_HOST\" --name $IMAGE_NAME --tag $IMAGE_TAG --prod $TARGET_PROD --api-gateway $API_GATEWAY --stack $STACK_NAME"
  environment:
    name: master
  dependencies: []
