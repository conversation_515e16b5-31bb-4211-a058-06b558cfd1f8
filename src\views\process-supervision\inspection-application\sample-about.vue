<template>
  <div class="sample-about">
    <div class="sample-about-header">
      <div class="title">样品相关</div>
      <el-button
        v-if="!showDetail && getPermissionBtn('addAddinspectionSampleBtn') && isInvalidated === 0"
        type="primary"
        class="analysisBtn"
        size="small"
        icon="el-icon-plus"
        @click="addSample"
        @keyup.prevent
        @keydown.enter.prevent
        >添加样品</el-button
      >
    </div>
    <div class="content">
      <el-table
        v-if="sampleInfoDetail.length > 0"
        ref="tableSampleRef"
        :key="tableSampleKey"
        :data="sampleInfoDetail"
        fit
        border
        height="auto"
        class="dark-table sample-about-table"
        @header-dragend="drageHeader"
      >
        <el-table-column label="物料编号" prop="materialNo" min-width="140px" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="nowrap">{{ row.materialNo || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="物料名称" prop="materialDesc" min-width="140px" show-overflow-tooltip>
          <template #default="{ row }">
            <div>{{ row.materialDesc || '--' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="物料分组" prop="materialGroup" min-width="140px" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.materialGroup || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="批次" prop="batchNo" min-width="100px" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.batchNo || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="盘号" prop="reelNo" min-width="100px" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.reelNo || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="型号" prop="model" min-width="120px" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.model || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="规格" prop="specifications" min-width="120px" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.specifications || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="type === 1 || type === 8 ? '入库数量' : '生产数量'"
          prop="inputWarehouseQuantity"
          min-width="80px"
          style="text-align: left"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div>
              {{ type === 1 || type === 8 ? row.inputWarehouseQuantity || '--' : row.productionQuantity || '--'
              }}{{
                type === 1 || type === 8
                  ? filterUnit(row.inputWarehouseUnit) || row.inputWarehouseUnit
                  : filterUnit(row.productionUnit) || row.productionUnit
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="送样数量"
          prop="sampleQuantity"
          min-width="80px"
          style="text-align: left"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div>{{ row.sampleQuantity || '--' }}{{ filterUnit(row.sampleUnit) || row.sampleUnit }}</div>
          </template>
        </el-table-column>
        <el-table-column label="样品信息" prop="inspectType" min-width="100px" show-overflow-tooltip>
          <template #default="{ row }">
            <QRCodeTrigger :value="{ type: 'sample', id: row.id, orderId: row.inspectionId }" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="190" prop="caozuo" fixed="right" align="center">
          <template #default="{ row }">
            <span class="blue-color" @click="chenckSampleIA(row)">查看</span>
            <span v-if="!showDetail && isInvalidated === 0" class="blue-color" @click="editSampleIA(row)">编辑</span>
            <span
              v-if="!showDetail && isInvalidated === 0 && getPermissionBtn('copyInspectionBtn')"
              class="blue-color"
              @click="copySampleInfo(row)"
              >复制</span
            >
            <span v-if="!showDetail && isInvalidated === 0" class="blue-color" @click="deleteSampleIA(row)">删除</span>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-if="sampleInfoDetail.length === 0" :image="emptyImg" description="暂无数据" />
    </div>
    <add-sample
      :drawer="showDrawer"
      :title="addSampleTitle"
      :edit-data="editSampleData"
      :inspection-id="currentInspectionId"
      :type="type"
      @set-info="getAddSampleData"
      @close="closeAddSampleDrawer"
    />
    <!-- 二维码弹出框 -->
    <QRCodePopup title="样品二维码" />
  </div>
</template>

<script>
import { reactive, toRefs, watch } from 'vue';
import { useStore } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
// import router from '@/router/index.js'
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import AddSample from './add-sample';
import { ElMessage, ElMessageBox } from 'element-plus';
import { deleteSample, copyInspectionSample } from '@/api/inspection-application';
import { QRCodePopup, QRCodeTrigger } from '@/components/QRCodePopup';
import emptyImg from '@/assets/img/empty-table.png';
// import { getSampleUnitDict } from '@/api/login'

export default {
  name: 'SampleAbout',
  components: { AddSample, QRCodePopup, QRCodeTrigger },
  props: {
    type: {
      type: Number,
      default: 1
    },
    inspectionId: {
      type: String,
      default: '000'
    },
    isInvalidated: {
      type: Number,
      default: 0
    },
    showDetail: {
      type: Boolean,
      default: false
    },
    sampleInfo: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['setInfo'],
  setup(props, context) {
    // watch(props, (newValue) => {
    //
    // })
    const store = useStore().state;
    const datas = reactive({
      tableSampleKey: 'tableSampleKey',
      currentAccountId: getLoginInfo().accountId,
      userOptions: store.common.nameList,
      sampleInfoDetail: props.sampleInfo,
      showDrawer: false,
      addSampleTitle: '新增样品',
      isInvalidated: props.isInvalidated,
      editSampleData: {},
      currentInspectionId: '',
      options: store.user.sampleUnit
    });

    watch(
      () => props.inspectionId,
      newValue => {
        if (newValue) {
          datas.currentInspectionId = newValue;
          datas.sampleInfoDetail = props.sampleInfo;
        }
      },
      { deep: true }
    );

    watch(
      () => props.sampleInfo,
      newValue => {
        if (newValue) {
          datas.sampleInfoDetail = props.sampleInfo;
        }
      },
      { deep: true }
    );

    // 添加样品-打开新增样品弹出框
    const addSample = () => {
      datas.addSampleTitle = '新增样品';
      datas.editSampleData = {};
      datas.showDrawer = true;
      // context.emit('setInfo', datas.sampleInfoDetail)
    };
    // 关闭新增样品页面弹出框
    const closeAddSampleDrawer = value => {
      datas.showDrawer = value;
      context.emit('setInfo', 'close');
    };
    // 获取新增样品数据
    const getAddSampleData = data => {
      console.log(data);
      // datas.sampleInfoDetail.push(data)
      context.emit('setInfo', datas.sampleInfoDetail);
    };
    // 编辑样品
    const editSampleIA = row => {
      datas.addSampleTitle = '编辑样品';
      datas.editSampleData = row;
      datas.showDrawer = true;
    };
    // 查看样品
    const chenckSampleIA = row => {
      datas.addSampleTitle = '查看样品';
      datas.editSampleData = row;
      datas.showDrawer = true;
    };
    // 删除样品
    const deleteSampleIA = row => {
      console.log(row);
      ElMessageBox({
        title: '提交',
        message: '是否确认删除该样品？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          deleteSample(row.id).then(res => {
            if (res !== false) {
              console.log(res);
              ElMessage.success('删除成功');
              context.emit('setInfo', 'delete');
            }
          });
        })
        .catch(() => {});
    };
    // 过滤单位
    const filterUnit = unitId => {
      var unitName = '';
      if (unitId && datas.options.length > 0) {
        datas.options.forEach(opt => {
          if (opt.code === unitId) {
            unitName = opt.name;
          }
        });
      }
      return unitName;
    };

    // #region 复制申请单

    const copySampleInfo = row => {
      if (row && row.id) {
        ElMessageBox({
          title: '提示',
          message: `确认复制该样品(物料编号:${row.materialNo})吗？`,
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: true,
          type: 'warning'
        })
          .then(() => {
            const formData = {
              inspectionOrdersId: row.id
            };
            copyInspectionSample(formData).then(res => {
              if (res) {
                ElMessage.success(`新增样品(物料编号:${row.materialNo})成功！`);
                context.emit('setInfo', 'update');
              }
            });
          })
          .catch(() => {});
      } else {
        ElMessage.warning('请先选择要复制的样品!');
      }
    };

    // #endregion

    return {
      ...toRefs(datas),
      emptyImg,
      getNameByid,
      addSample,
      drageHeader,
      closeAddSampleDrawer,
      getAddSampleData,
      editSampleIA,
      deleteSampleIA,
      filterUnit,
      chenckSampleIA,
      getPermissionBtn,
      copySampleInfo
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.sample-about {
  .sample-about-header {
    margin: 15px 0 15px 0;
    text-align: left;
    .title {
      font-size: 16px;
      font-weight: bold;
      height: 32px;
      line-height: 32px;
      text-align: left;
      display: inline-block;
      margin-right: 16px;
    }
    .el-button {
      float: right;
    }
  }
  .content {
    background: $background-color;
    text-align: left;
    position: relative;
    .sample-about-table {
      margin-bottom: 15px;
    }
  }
}
</style>
