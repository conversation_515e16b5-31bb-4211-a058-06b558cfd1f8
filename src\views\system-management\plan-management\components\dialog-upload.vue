<template>
  <!-- 导入计划 -->
  <el-dialog
    v-model="dialogVisiable"
    title="导入计划"
    :close-on-click-modal="false"
    :width="800"
    @close="handleClose()"
  >
    <div class="title">为确保数据导入的准确性，请按照以下规则进行导入</div>
    <div class="bt">下载表单模板</div>
    <ul class="uploadRules">
      <li>
        请按照<span class="blue-color" @click="downLoadFile('导入计划模板.xlsx')">导入计划模板.xlsx</span
        >在模板内录入数据
      </li>
    </ul>
    <el-upload
      v-if="dialogVisiable"
      ref="uploadRef"
      :action="uploadAction"
      :headers="headerconfig"
      :auto-upload="false"
      :limit="1"
      :accept="fileAcceptExcel"
      :on-exceed="handleExceed"
      :before-upload="beforeUpload"
      :on-success="handleFileSuccess"
    >
      <el-button size="small" type="primary" plain>选择上传文件</el-button>
    </el-upload>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose()">取消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="submitUpload" @keyup.prevent @keydown.enter.prevent
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
// getCurrentInstance
import { reactive, ref, toRefs, watch, getCurrentInstance } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { plandetailImport } from '@/api/uploadAction';
import { getToken } from '@/utils/auth';
import { fileAcceptExcel } from '@/utils/fileAccept';
export default {
  name: 'DialogCalendarUpload',
  components: {},
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableKey: 0,
      uploadAction: plandetailImport(),
      headerconfig: {
        Authorization: getToken()
      },
      formRef: ref(),
      uploadRef: ref(),
      dialogLoading: false,
      dialogVisiable: false
    });
    watch(props, newValue => {
      state.dialogVisiable = props.dialogShow;
    });
    // 关闭弹出窗
    const handleClose = isRefresh => {
      context.emit('closeDialog', isRefresh);
    };
    const submitUpload = () => {
      state.uploadRef.submit();
    };
    const handleExceed = files => {
      state.uploadRef.clearFiles(['success', 'fail', 'ready']);
      state.uploadRef.handleStart(files[0]);
    };
    // 上传文件的限制
    const beforeUpload = file => {
      var fileName = '';
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      const fileSize = file.size / 1024 / 1024 < 20;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过20M');
        return false;
      } else if (fileName !== 'xls' && fileName !== 'xlsx') {
        proxy.$message.error('仅支持.xls，.xlsx文件扩展名');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };
    // 下载附件
    const downLoadFile = fileName => {
      const a = document.createElement('a');
      a.href = '/staticFile/' + fileName;
      a.download = fileName;
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      a.remove();
    };
    // 上传成功的钩子
    const handleFileSuccess = (res, file) => {
      if (res.code === 200) {
        proxy.$message.success(res.message);
        handleClose(true);
      } else {
        proxy.$message.error(res.message);
      }
    };
    return {
      ...toRefs(state),
      handleFileSuccess,
      beforeUpload,
      downLoadFile,
      handleExceed,
      submitUpload,
      handleClose,
      formatDate,
      colWidth,
      drageHeader,
      fileAcceptExcel
    };
  }
};
</script>
<style lang="scss" scoped>
.uploadRules {
  padding: 5px 10px;
  background: #f0f2f5;
  margin: 14px 0;
  li {
    list-style: none;
    line-height: 22px;
    font-size: 13px;
  }
  .blue-color {
    margin-right: 0;
  }
}
.title {
  background-color: #f0f2f5;
  line-height: 30px;
  padding: 0 10px;
  margin-bottom: 15px;
}
.bt {
  font-size: 15px;
  line-height: 16px;
  font-weight: bold;
  // padding: 0 10px;
}
</style>
