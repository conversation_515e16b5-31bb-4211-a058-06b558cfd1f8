<!-- 预览标准库 -->
<template>
  <el-drawer
    v-model="showDrawer"
    :title="drawerTitle"
    direction="rtl"
    :before-close="handleClose"
    size="90%"
    destroy-on-close
    custom-class="DrawerSnapshot"
  >
    <DrawerLayout v-loading="drawerLoading">
      <template #button-group>
        <el-button size="small" type="primary" @click="handleExport()" @keyup.prevent @keydown.enter.prevent
          ><span class="iconfont tes-task-issued" @keyup.prevent @keydown.enter.prevent /> 导 出</el-button
        >
      </template>
      <div id="luckysheet" style="margin: 0px; padding: 0px; width: 100%; height: 100%" />
    </DrawerLayout>
  </el-drawer>
</template>

<script>
import { ref, watch, reactive, toRefs, nextTick } from 'vue';
import DrawerLayout from '@/components/DrawerLayout';
import { getNameByid, getUserNameByid } from '@/utils/common';
import { standardcategoryExport } from '@/api/testBase';
// import { ElMessageBox, ElMessage } from 'element-plus';
import { getLoginInfo } from '@/utils/auth';
import '../../../../../public/jquery.mousewheel.min.js';
import store from '@/store';
import LuckySheet from '@cxist/luckysheet/dist/luckysheet.esm.js';
import LuckyExcel from 'luckyexcel';
import '@cxist/luckysheet/dist/css/luckysheet.css';
import '@cxist/luckysheet/dist/plugins/css/pluginsCss.css';
import '@cxist/luckysheet/dist/assets/iconfont/iconfont.css';
import { ElMessage } from 'element-plus';

export default {
  name: 'DrawerPreview',
  components: { DrawerLayout },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    isAll: {
      type: Boolean,
      default: false
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    selectData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    checkTree: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDrawer'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    // 抽屉事件
    const showDrawer = ref(props.drawer);
    const state = reactive({
      drawerLoading: false,
      params: {},
      isAll: false,
      luckysheetInstance: null,
      userList: store.state.common.nameList,
      ownerId: getLoginInfo().accountId,
      checkTreeNode: {},
      srcList: [],
      pictureHistory: [],
      selectData: [],
      excelDetail: '',
      drawerTitle: '',
      detailData: {}
    });
    watch(props, newValue => {
      showDrawer.value = newValue.drawer;
      if (showDrawer.value) {
        state.detailData = newValue.detailData;
        state.drawerLoading = false;
        state.excelDetail = '';
        state.luckysheetInstance = null;
        state.selectData = newValue.selectData;
        state.isAll = newValue.isAll;
        state.checkTreeNode = newValue.checkTree;
        state.drawerTitle = state.checkTreeNode.name + '（' + state.checkTreeNode.productModel + '）';
        nextTick(() => {
          initLuckysheet();
        });
      }
    });
    const initLuckysheet = () => {
      state.luckysheetInstance = LuckySheet.create({
        container: 'luckysheet',
        allowEdit: false,
        lang: 'zh',
        showtoolbar: false, // 隐藏工具栏
        showinfobar: false, // 隐藏信息栏
        showsheetbar: false, // 隐藏底部sheet栏
        showstatisticBar: false, // 隐藏统计栏
        allowCopy: false, // 禁用复制
        allowUpdate: false, // 禁用更新
        enableAddRow: false, // 禁用添加行
        enableAddCol: false, // 禁用添加列
        cellRightClickConfig: {
          // 禁用右键菜单
          copy: false,
          paste: false,
          insertRow: false,
          insertColumn: false,
          deleteRow: false,
          deleteColumn: false,
          hideRow: false,
          hideColumn: false,
          clear: false
        },
        sheetProtection: {
          selectLockedCells: false, // 不能选中锁定的单元格
          selectunLockedCells: false, // 不能选中未锁定的单元格
          formatCells: false, // 不能设置单元格格式
          formatColumns: false, // 不能设置列格式
          formatRows: false, // 不能设置行格式
          insertColumns: false, // 不能插入列
          insertRows: false, // 不能插入行
          insertHyperlinks: false, // 不能插入超链接
          deleteColumns: false, // 不能删除列
          deleteRows: false, // 不能删除行
          sort: false, // 不能排序
          filter: false, // 不能筛选
          usePivotTablereports: false, // 不能使用数据透视表
          editObjects: false, // 不能编辑对象
          editScenarios: false // 不能编辑方案
        }
      });
      initDetail();
    };
    const initDetail = async () => {
      state.drawerLoading = true;
      const params = {
        ...state.detailData
      };
      if (!state.isAll) {
        params.standardProductIdList = state.selectData.map(item => item.id);
      }
      const { data } = await standardcategoryExport(params).finally((state.drawerLoading = false));
      state.excelDetail = data;
      const arrayBuffer = await data.arrayBuffer();
      LuckyExcel.transformExcelToLucky(arrayBuffer, function (exportJson, luckysheetfile) {
        if (exportJson.sheets == null || exportJson.sheets.length == 0) {
          console.error('读取excel文件失败!');
          return;
        }

        if (state.luckysheetInstance) {
          state.luckysheetInstance.destroy();
        }

        state.luckysheetInstance = LuckySheet.create({
          container: 'luckysheet',
          data: exportJson.sheets,
          title: exportJson.info.name,
          allowEdit: false,
          lang: 'zh',
          showtoolbar: false, // 隐藏工具栏
          showinfobar: false, // 隐藏信息栏
          showsheetbar: false, // 隐藏底部sheet栏
          showstatisticBar: false, // 隐藏统计栏
          allowCopy: false, // 禁用复制
          allowUpdate: false, // 禁用更新
          enableAddRow: false, // 禁用添加行
          enableAddCol: false, // 禁用添加列
          cellRightClickConfig: {
            // 禁用右键菜单
            copy: false,
            paste: false,
            insertRow: false,
            insertColumn: false,
            deleteRow: false,
            deleteColumn: false,
            hideRow: false,
            hideColumn: false,
            clear: false
          },
          sheetProtection: {
            selectLockedCells: false, // 不能选中锁定的单元格
            selectunLockedCells: false, // 不能选中未锁定的单元格
            formatCells: false, // 不能设置单元格格式
            formatColumns: false, // 不能设置列格式
            formatRows: false, // 不能设置行格式
            insertColumns: false, // 不能插入列
            insertRows: false, // 不能插入行
            insertHyperlinks: false, // 不能插入超链接
            deleteColumns: false, // 不能删除列
            deleteRows: false, // 不能删除行
            sort: false, // 不能排序
            filter: false, // 不能筛选
            usePivotTablereports: false, // 不能使用数据透视表
            editObjects: false, // 不能编辑对象
            editScenarios: false // 不能编辑方案
          }
        });
      });
    };
    // 关闭抽屉
    const handleClose = () => {
      showDrawer.value = false;
      LuckySheet.destroy();
      context.emit('closeDrawer', false);
    };
    const handleExport = () => {
      const blob = new Blob([state.excelDetail], { type: '' });
      const blobUrl = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.download = `${state.checkTreeNode.name}（${state.checkTreeNode.productModel}）.xlsx`;
      a.href = blobUrl;
      a.click();
      ElMessage.success('下载附件成功');
    };
    // 确认新增
    return {
      ...toRefs(state),
      getNameByid,
      handleExport,
      initLuckysheet,
      getLoginInfo,
      getUserNameByid,
      initDetail,
      handleClose,
      showDrawer
    };
  }
};
</script>

<style lang="scss" scoped></style>
