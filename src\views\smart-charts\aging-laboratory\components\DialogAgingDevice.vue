<template>
  <!--老化箱状态选择设备弹出框-->
  <el-dialog
    v-model="dialogVisible"
    top="3vh"
    title="选择老化箱"
    width="980px"
    custom-class="aging-laboratory-dialog"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleCloseDialog()"
  >
    <el-table
      ref="tableRef"
      :data="tableData"
      size="medium"
      fit
      border
      height="auto"
      class="dark-table base-table format-height-table2"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="65" />
      <el-table-column label="设备编号" prop="deviceNumber" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.deviceNumber }}
        </template>
      </el-table-column>
      <el-table-column label="设备名称" prop="boxName" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.boxName || '--' }}
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCloseDialog()">取 消</el-button>
        <el-button type="primary" @click="handleSubmit()">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, ref, watch, nextTick } from 'vue';
import { upBoxBoardStatusBatch } from '@/api/aging-laboratory';
import { ElMessage } from 'element-plus';
export default {
  name: 'DialogAgingDevice',
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    tableList: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    const dialogForm = ref();
    const state = reactive({
      tableData: [],
      tableRef: ref(),
      selectInfo: [], // 选中的老化箱
      dialogVisible: false,
      dialogLoading: true
    });
    watch(props, newValue => {
      state.dialogVisible = newValue.isShow;
      if (state.dialogVisible) {
        state.tableData = newValue.tableList;
        nextTick(() => {
          initSelect();
        });
      }
    });
    const initSelect = () => {
      state.tableData.forEach(item => {
        state.tableRef.toggleRowSelection(item, item.boardStatus == 1);
      });
    };
    const handleRowClick = row => {
      state.tableRef.toggleRowSelection(
        row,
        !state.selectInfo.some(item => {
          return row.id === item.id;
        })
      );
    };
    const handleSelectionChange = val => {
      state.selectInfo = val;
    };
    const handleSubmit = async () => {
      const params = state.selectInfo.map(item => item.id);
      const { data } = await upBoxBoardStatusBatch(params).finally((state.dialogLoading = true));
      if (data) {
        ElMessage.success('保存成功！');
        handleCloseDialog(true);
      }
    };
    const handleCloseDialog = isRefresh => {
      state.dialogVisible = false;
      context.emit('close', isRefresh);
    };
    return {
      ...toRefs(state),
      dialogForm,
      handleRowClick,
      handleSelectionChange,
      handleSubmit,
      handleCloseDialog,
      props
    };
  }
};
</script>

<style scoped lang="scss"></style>
<style lang="scss">
@import '@/styles/intelligentChart.scss';
.aging-laboratory-dialog {
  background-color: $darkBlue !important;
  .el-dialog__title {
    color: $scrollListColor !important;
  }
  .el-dialog__headerbtn .el-dialog__close {
    color: $scrollListColor !important;
  }
  .el-table {
    background-color: $borderColor !important;
  }
  .dark-table.el-table thead th {
    background-color: $borderColor;
    color: $scrollListColor;
  }
  .el-table tr {
    background-color: $borderColor !important;
    color: $scrollListColor;
  }
}
</style>
