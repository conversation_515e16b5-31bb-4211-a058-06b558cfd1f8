<template>
  <!-- 个人收藏 -->
  <div class="PersonalCollection">
    <div class="top-agency">
      <div class="title">个人收藏</div>
    </div>
    <div class="bulletinContent">
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane label="文件" name="first">
          <el-table
            :data="fileList"
            fit
            height="auto"
            border
            highlight-current-row
            class="dark-table format-height-table no-quick-query2 base-table"
          >
            <el-table-column label="序号" type="index" width="50" />
            <el-table-column label="文件名称" prop="fileName" show-overflow-tooltip>
              <template #default="{ row }">
                <span v-if="!row.hasPermission"
                  ><i class="el-icon-lock file-no-permission" />{{ row.fileName || '--' }}</span
                >
                <span v-else class="blue-color" @click="filePreview(row)">{{ row.fileName || '--' }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="检测项目" name="second">
          <el-table
            :data="itemList"
            fit
            height="auto"
            border
            highlight-current-row
            class="dark-table format-height-table no-quick-query2 base-table"
          >
            <el-table-column label="序号" type="index" width="50" />
            <el-table-column label="检测项目" prop="sourceName" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.sourceName }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { reactive, toRefs } from 'vue';
import router from '@/router/index.js';
import { getFileList } from '@/api/file-list';
import { getItemFavoriteList } from '@/api/personalCollection';

export default {
  name: 'PersonalCollection',
  components: {},
  props: {
    show: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    const state = reactive({
      fileList: [],
      itemList: [],
      activeName: 'first'
    });
    const getFileTable = () => {
      const params = {
        isMyFavorite: true,
        limit: '-1',
        page: '1'
      };
      getFileList(params).then(res => {
        if (res) {
          const data = res.data.data;
          state.fileList = data.list;
        }
      });
    };
    getFileTable();
    const getItemTable = () => {
      getItemFavoriteList().then(res => {
        if (res) {
          state.itemList = res.data.data;
        }
      });
    };
    getItemTable();

    // 预览
    const filePreview = row => {
      const newRouter = router.resolve({
        path: '/preview-file',
        query: {
          fileId: row.fileId
        }
      });
      window.open(newRouter.href, '_blank');
    };
    return { ...toRefs(state), filePreview, getFileTable, getItemTable };
  }
};
</script>
<style lang="scss" scoped>
.bulletinContent {
  background-color: $background-color;
  text-align: left;
  padding: 0 20px;
  :deep(.el-tabs__content) {
    padding-bottom: 20px;
  }
  .file-no-permission {
    color: $tes-yellow;
    margin-right: 10px;
  }
  .item {
    display: flex;
    flex-direction: row;
    padding: 5px 0;
    border-bottom: 1px solid #ebeef5;
  }
  .itemLine {
    line-height: 25px;
    width: calc(100% - 32px);
  }
  .title {
    max-width: 60%;
    color: $tes-primary;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    height: 16px;
    line-height: 20px;
    display: inline-block;
    cursor: pointer;
  }
  .name-icon {
    height: 24px;
    width: 24px;
    align-self: center;
    line-height: 24px;
    text-align: center;
    display: inline-block;
    background: $tes-primary;
    border-radius: 50%;
    color: #fff;
    margin-right: 8px;
  }

  .top-agency .title {
    font-size: 16px;
    color: $tes-font;
    line-height: 1.5rem;
    margin-left: -1rem;
    font-weight: 500;
    &::before {
      background: $tes-primary;
      content: '';
      display: inline-block;
      height: 0.9rem;
      margin-bottom: -0.03125rem;
      margin-right: 0.625rem;
      width: 0.25rem;
    }
  }
}
</style>
<style lang="scss">
.no-quick-query2.el-table {
  .el-table__body-wrapper {
    max-height: calc(100vh - 600px);
  }
  .el-table__fixed-body-wrapper {
    max-height: calc(100vh - 600px);
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
}
</style>
