<template>
  <div class="test-picture">
    <div class="img-title">
      <el-button @click="addImg" @keyup.prevent @keydown.enter.prevent>上传图片</el-button>
      <span class="text">只能上传 jpg/png 文件，且不超过 20M</span>
    </div>
    <div id="sortableList" ref="mainRef" :key="mainKey" class="img-main">
      <el-image-viewer v-if="showViewer" :url-list="srcList" :initial-index="currentIndex" @close="closeViewer" />
      <div v-for="(img, index) in imgLists" :key="img" class="img-container moduleMove">
        <el-row>
          <el-col :span="24">
            <div class="img-border img-list">
              <el-checkbox v-model="img.checked" @change="changeImg(img.checked, img)" />
              <el-image :src="img.imageUrl" fit="fill">
                <template #placeholder>
                  <div class="image-slot">
                    <i class="el-icon-loading" />
                  </div>
                </template>
                <template #error>
                  <div class="image-slot">
                    <i class="el-icon-picture-outline" />
                  </div>
                </template>
              </el-image>
              <div class="img-contant">
                <i class="el-icon-zoom-in" @click="zoominImg(index)" />
                <i v-if="img.source !== 0" class="el-icon-delete" @click="deleteImg(img)" />
                <i class="tes-drag tes-move" style="cursor: move" />
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <div class="img-key-list">
              <div class="img-key-label">图片key:</div>
              <el-select
                v-model="img.labelKeyList"
                placeholder="请选择图片key"
                size="small"
                multiple
                clearable
                filterable
                popper-class="select-popper-no-tick"
                :disabled="img.source === 0"
                @change="changeImgKey(img)"
              >
                <el-option
                  v-for="item in keyList"
                  :key="item.labelKey"
                  :label="item.labelName"
                  :value="item.labelKey"
                />
              </el-select>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-empty v-if="imgLists.length === 0" :image="emptyImg" description="暂无图片" />
      <input
        ref="imguploadinputRef"
        class="img-upload-input"
        type="file"
        accept=".jpg, .png"
        multiple
        @change="handleClick"
      />
    </div>
  </div>
</template>

<script>
import { watch, reactive, toRefs, onMounted, nextTick, ref } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { uploadReportImg, delReportImgList } from '@/api/testReport';
import { getCurrentReportInfo } from '@/utils/auth';
import { ElMessage } from 'element-plus';
import Sortable from 'sortablejs';
import emptyImg from '@/assets/img/empty-data.png';
export default {
  name: 'TestPicture',
  components: {},
  props: {
    data: {
      type: Object,
      default: function () {
        return {};
      }
    },
    keyList: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['setInfo', 'refreshImg'],
  setup(props, context) {
    const mainRef = ref();
    const imguploadinputRef = ref();
    const currentInfo = getCurrentReportInfo();

    const state = reactive({
      imgLists: props.data,
      srcList: [],
      showViewer: false,
      currentIndex: 0,
      marginR: 15,
      mainKey: 0
    });

    watch(props, newValue => {
      state.imgLists = JSON.parse(JSON.stringify(props.data));
      state.srcList = formatImgUrl(props.data);
      nextTick(() => {
        initSortable();
      });
    });

    onMounted(() => {
      nextTick(() => {
        state.srcList = formatImgUrl(state.imgLists);
        const mainWidth = mainRef.value.offsetWidth;
        const num = parseInt(mainWidth / (146 + 5));
        const otherWidth = mainWidth - num * (146 + 0);
        state.marginR = otherWidth / (num * 2);
      });
    });

    const changeImg = (checked, img) => {
      if (img.isReport === 1) {
        img.isReport = 0;
        img.checked = false;
      } else {
        img.isReport = 1;
        img.checked = true;
      }
      context.emit('setInfo', state.imgLists);
    };

    const addImg = () => {
      imguploadinputRef.value.click();
    };

    // 选择图片
    const handleClick = e => {
      const files = Array.from(e.target.files);
      [...files].forEach(async file => {
        const imgsrc = await compressedImg(file);
        const newfile = dataURLtoFile(imgsrc, file.name);
        const params = new FormData();
        params.append('file', newfile);
        params.append('isReport', 1);
        params.append('name', file.name);
        params.append('sampleId', currentInfo.sampleId);
        params.append('experimentId', '12343231');

        params.append('reportId', currentInfo.reportId);
        uploadReportImg(params, callback => {}).then(res => {
          if (res !== false) {
            const imgData = res.data.data;
            const param = {
              imageId: imgData.imageId,
              imageUrl: imgData.url,
              name: imgData.name,
              remoteName: imgData.remoteName,
              sampleId: currentInfo.sampleId,
              isReport: 1,
              id: imgData.id,
              isDeleted: false,
              checked: true,
              order: state.imgLists.length - 1
            };
            state.imgLists.push(param);
            state.srcList.push(imgData.url);
            context.emit('setInfo', state.imgLists);
          } else {
            return false;
          }
        });
      });
    };

    const readerData = rawFile => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => {
          resolve();
        };
        reader.readAsDataURL(rawFile);
      });
    };

    // base64转文件file
    const dataURLtoFile = (dataurl, filename) => {
      var arr = dataurl.split(',');
      var mime = arr[0].match(/:(.*?);/)[1];
      var bstr = atob(arr[1]);
      var n = bstr.length;
      var u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], filename, { type: mime });
    };

    // canvas压缩图片
    const compressedImg = file => {
      return new Promise((resolve, reject) => {
        var reader = new FileReader();
        reader.readAsDataURL(file);
        var img = new Image();
        reader.onload = function (e) {
          var width = 1024;
          var quality = 0.3;
          var canvas = document.createElement('canvas');
          var drawer = canvas.getContext('2d');
          img.src = e.target.result;
          var flag = false;
          img.onload = function () {
            if (flag === true) {
              img.onload = null;
              return false;
            }
            canvas.width = width;
            canvas.height = width * (img.height / img.width);
            drawer.drawImage(img, 0, 0, canvas.width, canvas.height);
            img.src = canvas.toDataURL('image/jpeg', quality);
            flag = true;
            resolve(img.src);
          };
        };
      });
    };

    // formatImgUrl
    const formatImgUrl = list => {
      const urlArr = [];
      if (list.length > 0) {
        list.forEach(img => {
          urlArr.push(img.imageUrl);
          if (img.isReport === 1) {
            img.checked = true;
          } else {
            img.checked = false;
          }
        });
      }
      return urlArr;
    };

    // 图片预览
    const zoominImg = index => {
      state.currentIndex = index;
      state.showViewer = true;
    };

    // 关闭预览
    const closeViewer = () => {
      state.showViewer = false;
    };

    // 删除图片
    const deleteImg = img => {
      const param = {
        id: img.id,
        imageId: img.imageId,
        remoteName: img.remoteName
      };
      delReportImgList(param).then(res => {
        if (res !== false) {
          ElMessage.success('删除成功');
          context.emit('refreshImg', true);
        }
      });
    };

    const changeImgKey = img => {
      if (img) {
        const imgIndex = state.imgLists.findIndex(item => item.id === img.id);
        if (imgIndex !== -1) {
          const currentImg = state.imgLists[imgIndex];
          state.imgLists[imgIndex].labelList = state.imgLists[imgIndex].labelList || [];
          if (currentImg.labelKeyList && currentImg.labelKeyList.length > 0) {
            currentImg.labelKeyList.forEach(itemKey => {
              if (state.imgLists[imgIndex].labelList.findIndex(item => item.labelKey === itemKey) === -1) {
                state.imgLists[imgIndex].labelList.push({
                  id: '',
                  labelKey: itemKey,
                  labelName: props.keyList.find(item => item.labelKey === itemKey)?.labelName
                });
              }
            });
          }
          state.imgLists[imgIndex].labelList.forEach((item, index) => {
            if (currentImg.labelKeyList.findIndex(keyItem => keyItem === item.labelKey) === -1) {
              state.imgLists[imgIndex].labelList.splice(index, 1);
            }
          });
          context.emit('setInfo', state.imgLists);
        }
      }
    };

    const initSortable = () => {
      nextTick(() => {
        const el = document.getElementById('sortableList');
        Sortable.create(el, {
          animation: 300,
          handle: '.tes-move',
          draggable: '.moduleMove',
          ghostClass: 'ghost',
          // 拖动对象移动样式
          dragClass: 'drag',
          forceFallback: true,
          onEnd({ newIndex, oldIndex }) {
            if (oldIndex !== newIndex) {
              const currRow = state.imgLists.splice(oldIndex, 1)[0];
              state.imgLists.splice(newIndex, 0, currRow);
              const currRow2 = state.srcList.splice(oldIndex, 1)[0];
              state.srcList.splice(newIndex, 0, currRow2);
              state.imgLists.forEach((item, index) => {
                item.order = index;
              });
              context.emit('setInfo', state.imgLists);
            }
          }
        });
      });
    };

    return {
      ...toRefs(state),
      formatDate,
      initSortable,
      changeImg,
      addImg,
      formatImgUrl,
      zoominImg,
      closeViewer,
      deleteImg,
      handleClick,
      mainRef,
      imguploadinputRef,
      compressedImg,
      readerData,
      emptyImg,
      changeImgKey
    };
  }
};
</script>

<style lang="scss" scoped>
.test-picture {
  min-height: 600px;
  padding: 20px;
  margin-bottom: 60px;
  background: $background-color;

  .img-title {
    text-align: left;

    .text {
      font-size: 12px;
      color: $tes-font2;
      margin-left: 10px;
    }
  }

  .img-main {
    display: flex;
    flex-wrap: wrap;
    margin-right: -16px;

    .el-empty {
      margin: 0 auto;
    }
  }

  .img-border {
    border: 1px solid #b3e9dc !important;
  }

  .img-border:hover {
    .img-contant {
      width: 100%;
      display: inline-block;
      left: 0;
    }
  }

  .img-container {
    width: 252px;
    height: 252px;
    margin: 16px 16px 0 0;
  }

  .img-key-list {
    .img-key-label {
      float: left;
      margin-right: 10px;
      line-height: 40px;
    }

    width: 252px;
    height: 70px;

    :deep(.el-select--small) {
      width: 100%;
    }
  }

  .img-list {
    width: 252px;
    height: 108px;
    border: 1px dashed #e6e8ee;
    text-align: center;
    border-radius: 4px;
    float: left;
    margin: 16px 16px 0 0;

    .el-image {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      border-radius: 4px;
    }

    .el-icon-plus {
      line-height: 108px;
      font-size: 26px;
      color: #909399;
      cursor: pointer;
    }

    .el-icon-picture-outline,
    .el-icon-loading {
      line-height: 108px;
      font-size: 26px;
      color: #909399;
    }

    .el-checkbox {
      float: right;
      margin: 5px;

      :deep(.el-checkbox__inner) {
        border-radius: 14px;
      }
    }

    .img-contant {
      // display: none;
      background: #000000;
      opacity: 0.5;
      color: #ffffff;
      position: absolute;
      bottom: 0;
      width: 99%;
      border-radius: 0px 0px 4px 4px;
      height: 40px;
      line-height: 40px;
      font-size: 16px;

      i {
        margin: 0px 10px;
        cursor: pointer;
      }
    }
  }

  .img-upload-input {
    display: none;
    z-index: -9999;
  }
}
</style>
