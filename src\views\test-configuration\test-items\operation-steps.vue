<template>
  <div class="operation-steps-contant">
    <el-button
      v-if="getPermissionBtn('addTestMethods')"
      class="add-btn"
      size="small"
      icon="el-icon-plus"
      @click="addOperationSteps"
      @keyup.prevent
      @keydown.enter.prevent
      >新增试验方法</el-button
    >
    <el-table
      :data="operationStepsList"
      fit
      border
      height="auto"
      highlight-current-row
      size="medium"
      class="base-table format-height-table2 dark-table operation"
    >
      <el-table-column type="index" label="序号" width="70" align="center" />
      <el-table-column prop="name" label="试验方法" min-width="140" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.name }}
        </template>
      </el-table-column>
      <el-table-column prop="createBy" label="创建人" width="120">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.createBy) || row.createBy || '--'" />
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="200" sortable>
        <template #default="{ row }">
          <span>{{ row.createTime || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="updateBy" label="更新人" width="130">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.updateBy) || row.updateBy || '--'" />
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="更新时间" width="200" sortable>
        <template #default="{ row }">
          <span>{{ row.updateTime || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="试验要求" width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.description || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="version" label="版本" width="120" sortable>
        <template #default="{ row }">
          <span>{{ row.version }} <span v-if="row.status === 0">( 草稿 )</span></span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="120">
        <template #default="{ row }">
          <el-tag size="small" :type="row.status == 1 ? 'success' : 'info'">
            {{ row.status == 1 ? '已发布' : '待发布' }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        v-if="getPermissionBtn('checkTestMethods') || getPermissionBtn('editTestMethods')"
        prop="status"
        label="操作"
        width="140"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <span
            v-if="row.version && getPermissionBtn('checkTestMethods')"
            class="blue-color"
            @click="detailOperationSteps(row)"
            @keyup.prevent
            @keydown.enter.prevent
            >查看</span
          >
          <span
            v-if="getPermissionBtn('editTestMethods')"
            class="blue-color"
            @click="editOperationSteps(row)"
            @keyup.prevent
            @keydown.enter.prevent
            >编辑</span
          >
          <!-- <el-button type="primary" class="remove-bg1" size="small" @click="deleteOperationSteps(row)" @keyup.prevent @keydown.enter.prevent>删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { reactive, toRefs, watch } from 'vue';
// import { useRoute } from 'vue-router'
import { formatDate } from '@/utils/formatTime';
import { getNameByid, getNamesByid, getPermissionBtn } from '@/utils/common';
import router from '@/router';
// import { getLoginInfo } from '@/utils/auth'
// import { ElMessageBox } from 'element-plus'
// import { mapGetters } from 'vuex'
import { getMethodList } from '@/api/testItem';
import UserTag from '@/components/UserTag';

export default {
  name: 'AddOrUpdateOperationSteps',
  components: { UserTag },
  props: {
    item: {
      type: Object,
      default: function () {
        return {};
      }
    },
    activeName: {
      type: String,
      default: ''
    }
  },
  emits: ['close'],
  setup(props, context) {
    // const route = useRoute()
    const state = reactive({
      operationStepsList: [],
      detailData: props.item
    });
    watch(props, newValue => {
      if (newValue.activeName === '4') {
        state.detailData = props.item;
        getMethodLists(state.detailData.id);
      }
    });

    // 操作步骤模块 --start--
    // 新增操作步骤
    const addOperationSteps = row => {
      const param = {
        type: 'add',
        capabilityId: state.detailData.id,
        capabilityName: encodeURIComponent(state.detailData.name),
        id: '0',
        methodId: '0'
      };
      const newRouter = router.resolve({
        path:
          '/add-or-update-operation-steps/' +
          param.type +
          '/' +
          param.capabilityName +
          '/' +
          param.capabilityId +
          '/' +
          param.id +
          '/' +
          param.methodId
      });
      const newpage = window.open(newRouter.href, 'OperationSteps');
      // console.log(newpage)
      const newTime = setInterval(() => {
        if (newpage.opener === null) {
          // console.log(newpage.opener)
          clearInterval(newTime);
          getMethodLists(state.detailData.id);
        }
      }, 500);
    };
    // 查看
    const detailOperationSteps = row => {
      const param = {
        type: 'detail',
        capabilityId: state.detailData.id,
        capabilityName: encodeURIComponent(state.detailData.name),
        id: row.id,
        methodId: row.methodId
      };
      const newRouter = router.resolve({
        path:
          '/add-or-update-operation-steps/' +
          param.type +
          '/' +
          param.capabilityName +
          '/' +
          param.capabilityId +
          '/' +
          param.id +
          '/' +
          param.methodId
      });
      const newPage = window.open(newRouter.href, 'DetailOperationSteps');
      const newTime = setInterval(() => {
        if (newPage.opener === null) {
          // console.log(newPage.opener)
          clearInterval(newTime);
          getMethodLists(state.detailData.id);
        }
      }, 500);
    };
    // 编辑
    const editOperationSteps = row => {
      const param = {
        type: 'edit',
        capabilityId: state.detailData.id,
        capabilityName: encodeURIComponent(state.detailData.name),
        id: row.id,
        methodId: row.methodId
      };
      const newRouter = router.resolve({
        path:
          '/add-or-update-operation-steps/' +
          param.type +
          '/' +
          param.capabilityName +
          '/' +
          param.capabilityId +
          '/' +
          param.id +
          '/' +
          param.methodId
      });
      const newEPage = window.open(newRouter.href, 'EditOperationSteps');
      const newTime = setInterval(() => {
        if (newEPage.opener === null) {
          // console.log(newEPage.opener)
          clearInterval(newTime);
          getMethodLists(state.detailData.id);
        }
      }, 500);
    };
    // 获取试验方法列表
    const getMethodLists = capabilityId => {
      getMethodList(capabilityId).then(res => {
        if (res !== false) {
          // console.log(res.data.data)
          state.operationStepsList = res.data.data;
        }
      });
    };
    // --end--

    return {
      ...toRefs(state),
      formatDate,
      getPermissionBtn,
      getNameByid,
      getNamesByid,
      addOperationSteps,
      detailOperationSteps,
      editOperationSteps,
      getMethodLists
    };
  },
  computed: {},
  created() {
    this.getMethodLists(this.item.id);
  }
};
</script>
<style lang="scss" scoped>
.operation-steps-contant {
  .add-btn {
    float: left;
    margin-bottom: 10px;
  }
  :deep(.format-height-table2) {
    .el-table__body-wrapper {
      max-height: calc(100vh - 33.5rem) !important;
      overflow-x: hidden !important;
      overflow-y: auto;
    }
  }
}
</style>
