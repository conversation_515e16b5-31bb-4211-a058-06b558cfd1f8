<template>
  <div class="drawHistorical">
    <el-drawer v-model="drawerVisiable" title="历史记录" direction="rtl" :before-close="handleClose" size="599px">
      <div v-if="drawerVisiable" class="hollow">
        <el-timeline>
          <el-timeline-item v-for="(item, index) in newHistoryList" :key="index" type="primary" :hollow="item.hollow">
            <div class="timeTitle">方案</div>
            <div class="userName">
              <div class="name">{{ getNameByid(item.startUser) || item.startUser }}</div>
              {{ item.startTime }}
            </div>
            <div v-if="item.processParameter.reason" class="content">{{ item.processParameter.reason }}</div>
            <ul class="operation">
              <li>处置方式：{{ disposalWayJson[item.processParameter.disposalType] || '--' }}</li>
              <li>
                处置结果：合格 {{ item.processParameter.qualifiedNum }}{{ sampleUnit }} ; 不合格
                {{ item.processParameter.unQualifiedNum }}{{ sampleUnit }} ; 报废 {{ item.processParameter.scrappedNum
                }}{{ sampleUnit }} ; 退货 {{ item.processParameter.returnNum }}{{ sampleUnit }}
              </li>
              <li>初步处置方案：{{ item.processParameter.disposalPlan || '--' }}</li>
            </ul>
            <div v-if="item.newApprovalList.length > 0" class="optionTimeLine">
              <el-timeline>
                <el-timeline-item
                  v-for="(val, i) in item.newApprovalList"
                  :key="i"
                  placement="top"
                  :icon="val.processParameter.isAssent == 1 ? 'el-icon-check' : 'el-icon-close'"
                  :type="val.type"
                  :color="val.processParameter.isAssent == 1 ? '#67C23A' : '#F56C6C'"
                  size="large"
                  :timestamp="val.endTime"
                  ><div>
                    <div :class="val.processParameter.isAssent == 1 ? 'pass' : 'noPass'" class="optionTitle">
                      {{ val.processParameter.isAssent == 1 ? '审核通过' : '审核不通过' }}
                    </div>
                    <div class="approver">
                      <span>{{ val.name }}</span
                      >：{{ getNameByid(val.processParameter.assignee) }}（{{
                        val.processParameter.isAssent == '1' ? '已同意' : '不同意'
                      }}）
                    </div>
                    <div v-if="val.processParameter.opinion" class="optionDetail">
                      {{ val.processParameter.opinion }}
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch } from 'vue';
import { getNameByid } from '@/utils/common';
// import router from '@/router/index.js'

export default {
  name: 'HistoricalDrawer',
  components: {},
  props: {
    disposalWay: {
      type: Array,
      default: function () {
        return [];
      }
    },
    isShow: {
      type: Boolean,
      default: false
    },
    historyList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    sampleUnit: {
      type: String,
      default: ''
    }
  },
  emits: ['close'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    const datas = reactive({
      drawerVisiable: ref(props.isShow),
      newHistoryList: [],
      historyList: [],
      sampleUnit: '',
      disposalWayJson: {}
    });
    const handleClose = () => {
      datas.drawerVisiable = false;
      context.emit('close', false);
    };
    watch(props, newValue => {
      datas.drawerVisiable = newValue.isShow;
      if (datas.drawerVisiable) {
        initList();
      }
    });
    const initList = () => {
      props.disposalWay[0].group.forEach(item => {
        datas.disposalWayJson[item.code] = item.name;
      });
      props.disposalWay[1].group.forEach(item => {
        datas.disposalWayJson[item.code] = item.name;
      });
      datas.historyList = props.historyList;
      datas.newHistoryList = [];
      props.historyList.forEach((item, index) => {
        var childApprovel = [];
        item.approvalList.forEach(val => {
          if (val.endTime !== '' && val.name !== '处置验收') {
            childApprovel.push(val);
          }
        });
        if (item.endTime !== '') {
          datas.newHistoryList.push({
            newApprovalList: childApprovel,
            ...item
          });
        }
      });
      datas.sampleUnit = props.sampleUnit;
    };
    return {
      ...toRefs(datas),
      handleClose,
      initList,
      getNameByid
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.drawHistorical {
  text-align: left;
  :deep(.el-drawer__body) {
    overflow-y: auto;
  }
  .timeTitle {
    line-height: 21px;
    font-size: 14px;
  }
  .el-timeline {
    padding: 0;
  }

  .userName {
    font-size: 12px;
    line-height: 20px;
    margin: 4px 0 7px 0;
    .name {
      margin-right: 10px;
      display: inline-block;
    }
  }
  .content {
    line-height: 20px;
    background: #f6f6f6;
    padding: 8px;
    border-radius: 2px;
    border: 1px solid #ebeef5;
    font-size: 12px;
    color: #909399;
  }
  .operation {
    font-size: 12px;
    color: #909399;
    line-height: 20px;
    list-style: none;
    padding-left: 0;
    margin: 7px 0 12px 0;
    li {
      margin: 6px 0 0 0;
      // letter-spacing: 1px;
    }
  }
  .label {
    flex: 1;
  }
  .optionTitle {
    display: inline-block;
    font-size: 14px;
    line-height: 20px;
  }
  :deep(.el-timeline-item__node--primary) {
    background: #fff;
    border-style: solid;
    border-width: 2px;
    border-color: #0099ff;
  }
  .optionTimeLine {
    background: #f6f6f6;
    border-radius: 2px;
    padding: 16px 17px 0 16px;
    .el-timeline {
      padding: 0;
    }
    :deep(.el-timeline-item__tail) {
      display: inline-block;
    }
    :deep(.el-timeline-item:last-child .el-timeline-item__tail) {
      display: none;
    }
    .pass {
      color: #67c23a;
    }
    .noPass {
      color: #f56c6c;
    }
    .optionDetail {
      font-size: 12px;
      background: #fff;
      padding: 9px 10px;
      color: #909399;
      line-height: 20px;
      border-radius: 2px;
      width: 100%;
      // letter-spacing: 1px;
    }
    .approver {
      font-size: 12px;
      color: #606266;
      line-height: 20px;
      font-weight: normal;
      margin: 4px 0;
    }
    :deep(.el-timeline-item__timestamp) {
      position: absolute;
      right: 0;
      font-size: 12px;
    }
    :deep(.el-icon-close:before) {
      position: absolute;
      left: 0;
      top: 0;
    }
  }
  :deep(.el-timeline-item) {
    padding-bottom: 16px;
  }
}
</style>
