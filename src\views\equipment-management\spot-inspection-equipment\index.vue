<template>
  <!-- 设备点检 -->
  <ListLayout :has-button-group="getPermissionBtn('addTallySheet')">
    <template #search-bar>
      <div class="searchInput">
        <el-input
          v-model="param"
          v-trim
          v-focus
          size="large"
          class="ipt-360"
          clearable
          prefix-icon="el-icon-search"
          placeholder="请输入搜索内容"
          @clear="getTableList()"
          @keyup.enter="getTableList()"
        />
        <el-button size="large" type="primary" @click="getTableList">查询</el-button>
        <el-button size="large" @click="handleReset()">重置</el-button>
      </div>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('addTallySheet')"
        size="large"
        icon="el-icon-plus"
        type="primary"
        @click="addStandard()"
        @keyup.prevent
        @keydown.enter.prevent
        >新增点检单</el-button
      >
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="8" :offset="16" class="text-right">
          <TableColumnView binding-menu="SpotInspectionEquipment" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="loading"
      :data="tableList"
      size="medium"
      height="auto"
      fit
      border
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
    >
      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="item.fieldType === fieldTypesEnum.Person">
              <UserTag :name="getNameByid(row[item.fieldKey]) || '--'" />
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Date">
              {{ formatDate(row[item.fieldKey]) }}
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Status">
              <el-tag size="small" effect="dark" :type="equipUnitType[row.status]">{{
                equipUnitJson[row[item.fieldKey]] || '--'
              }}</el-tag>
            </template>
            <span v-else>{{ row[item.fieldKey] || '--' }}</span>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" :width="colWidth.operationMultiples" fixed="right" class-name="fixed-right">
        <template #default="{ row }">
          <span class="blue-color" @click="handleCheckRow(row)">查看</span>
          <span v-if="getPermissionBtn('editTallySheet')" class="blue-color" @click="handleEditRow(row)">编辑</span>
          <span v-if="getPermissionBtn('deleteTallySheet')" class="blue-color" @click="deleteRow(row)">删除</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getTableList"
    />
    <template #other>
      <DrawerSpotMonad
        :drawer-show="drawerSpotShow"
        :drawer-type="drawerSpotType"
        :detail-info="detailInfo"
        @close-drawer="closeDrawerStandard"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import Pagination from '@/components/Pagination';
import ListLayout from '@/components/ListLayout';
import { formatDate } from '@/utils/formatTime';
import UserTag from '@/components/UserTag';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import TableColumnView from '@/components/TableColumnView';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';
import DrawerSpotMonad from './drawer-spot-monad';
import { getDictionary } from '@/api/user';
import { devicePointInspectionList, devicePointInspectionDelete } from '@/api/spotInspectionEquipment';

export default {
  name: 'SpotInspectionEquipment',
  components: { Pagination, ListLayout, UserTag, TableColumnView, DrawerSpotMonad },
  setup() {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableRef: ref(),
      drawerSpotShow: false,
      isValid: '',
      detailInfo: {},
      param: '', // 列表关键字
      drawerSpotType: '', // 点检标准类型
      ruleForm: ref(),
      loading: false,
      types: [],
      equipUnitJson: {}, // 设备状态json类型
      equipUnitType: {
        // 设备状态对应的tag类型
        Running: 'success',
        Standby: 'warning',
        Maintenance: 'default',
        Fault: 'danger',
        Scrapped: 'info'
      },
      listQuery: {
        limit: 20,
        page: 1
      },
      tableColumns: [],
      tableList: [],
      total: 0
    });
    const tableKey = ref(0);
    const getTableList = query => {
      const params = { param: state.param };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.loading = true;
      devicePointInspectionList(params).then(res => {
        state.loading = false;
        if (res) {
          state.total = res.data.data.totalCount;
          state.tableList = res.data.data.list;
        }
      });
    };
    const getEquipUnit = () => {
      getDictionary(24).then(res => {
        if (res) {
          res.data.data.dictionaryoption.forEach(item => {
            state.equipUnitJson[item.code] = item.name;
          });
        }
      });
    };
    getEquipUnit();
    getTableList();
    const handleReset = () => {
      state.param = '';
      getTableList();
    };
    // 查看点检单
    const handleCheckRow = row => {
      state.drawerSpotType = 'check';
      state.drawerSpotShow = true;
      state.detailInfo = row;
    };
    // 删除
    const deleteRow = row => {
      proxy
        .$confirm('是否确认删除，删除后无法恢复', '删除确认', {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'warning'
        })
        .then(() => {
          state.listLoading = true;
          devicePointInspectionDelete([row.id]).then(function (res) {
            state.listLoading = false;
            if (res) {
              proxy.$message.success('删除成功！');
              getTableList();
            }
          });
        })
        .catch(() => {});
    };
    // 编辑
    const handleEditRow = row => {
      state.drawerSpotType = 'edit';
      state.drawerSpotShow = true;
      state.detailInfo = row;
    };
    // 新增点检单
    const addStandard = () => {
      state.drawerSpotType = 'add';
      state.drawerSpotShow = true;
      state.detailInfo = {};
    };
    // 关闭弹出窗
    const closeDrawerStandard = val => {
      state.drawerSpotShow = false;
      if (val) {
        getTableList();
      }
    };
    const onUpdateColumns = columns => {
      tableKey.value = tableKey.value + 1;
      state.tableColumns = columns;
    };
    return {
      ...toRefs(state),
      addStandard,
      getPermissionBtn,
      drageHeader,
      getNameByid,
      getNamesByid,
      handleCheckRow,
      deleteRow,
      handleEditRow,
      closeDrawerStandard,
      handleReset,
      formatDate,
      getTableList,
      tableKey,
      colWidth,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  }
};
</script>
<style lang="scss" scoped>
.searchInput {
  display: flex;
  .el-input {
    width: 360px;
    margin-right: 10px;
  }
}
</style>
