export const filterStatus = status => {
  const classMap = {
    0: ['info', '未提交'],
    1: ['warning', '待评审'],
    2: ['warning', '待确认'],
    3: ['success', '已确认'],
    4: ['warning', '已拒绝']
  };
  return classMap[status] ? classMap[status] : classMap[3];
};

export const taskRegQueryList = [
  {
    field: 'entrustNo',
    name: '委托编号',
    isNotQuery: 0
  },
  {
    field: 'regUserName',
    name: '登记人',
    isNotQuery: 1
  },
  {
    field: 'regDate',
    name: '登记日期',
    isNotQuery: 1
  },
  {
    field: 'entrustName',
    name: '委托方',
    isNotQuery: 0
  },
  {
    field: 'payerName',
    name: '缴款方',
    isNotQuery: 0
  },
  {
    field: 'entrustCost',
    name: '委托金额(¥)',
    isNotQuery: 0
  },
  {
    field: 'status',
    name: '委托状态',
    isNotQuery: 1
  }
];
