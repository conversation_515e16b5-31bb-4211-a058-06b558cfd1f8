import request from '@/utils/request';
const formHeaders = {
  'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
};

// 根据编码查询图片地址
export function diyLoginStyleQuery(data) {
  return request({
    url: '/api-user/diyLoginStyle/query',
    method: 'get',
    params: data
  });
}
// 根据图片地址获取图片预览地址
export function diyLoginStylePreview(data) {
  return request({
    url: '/api-user/diyLoginStyle/preview',
    method: 'get',
    params: data
  });
}
// 重置背景图
export function resetBackground(id) {
  return request({
    url: '/api-user/diyLoginStyle/resetBackground',
    method: 'post',
    headers: formHeaders,
    params: { id: id }
  });
}
// 重置背景图
export function resetLogo(id) {
  return request({
    url: '/api-user/diyLoginStyle/resetLogo',
    method: 'post',
    headers: formHeaders,
    params: { id: id }
  });
}
// 重置背景图
export function resetWelcome(id) {
  return request({
    url: '/api-user/diyLoginStyle/resetWelcome',
    method: 'post',
    headers: formHeaders,
    params: { id: id }
  });
}
