import request from '@/utils/request';

// 消息待办数量  登录后调用，切换租户再次调用
export function getMessageNum() {
  return request({
    url: '/api-message/message/receiver/amount',
    method: 'post'
  });
}
// 我的消息-消息列表 messageType: 1：消息  readStatus：all:全部 1:已读，0:未读
// 我的待办-待办列表 messageType: 2：待办  todoStatus：all:全部 1:已处理，0:未处理 2:已超期
export function getMessageList(data) {
  return request({
    url: '/api-message/message/receiver/list',
    method: 'post',
    data
  });
}
// 我的消息-消息详情 messageType: 1：消息
// 我的待办-待办详情 messageType: 2：待办
export function getMessageInfo(data) {
  return request({
    url: '/api-message/message/receiver/info',
    method: 'post',
    data
  });
}
// 我的消息-消息设为已读    消息待办打开详情的时候，应调用此接口将消息待办设为已读，要改为可以多条已读
export function readMessage(data) {
  return request({
    url: '/api-message/message/receiver/read',
    method: 'post',
    data
  });
}
// 我的消息-删除消息
export function deleteMessage(data) {
  return request({
    url: '/api-message/message/receiver/delete',
    method: 'post',
    data
  });
}
// 业务系统调用-完成待办     在业务系统完成待办后，调用该接口标识待办，其他系统用http请求调用，TES内用feign调用，可处理多条，完成后更新待办的状态
export function completetodoMessage(data) {
  return request({
    url: '/api-message/message/todomessage/completetodo',
    method: 'post',
    data
  });
}
// 消息管理-消息待办列表
export function getMessageAgentList(data) {
  return request({
    url: '/api-message/message/messagemgt/list',
    method: 'post',
    data
  });
}
// 消息管理-新建消息待办
export function addMessageAgent(data) {
  return request({
    url: '/api-message/message/messagemgt/add',
    method: 'post',
    data
  });
}
// 消息管理-消息待办详情
export function detailMessageAgent(data) {
  return request({
    url: '/api-message/message/messagemgt/detail',
    method: 'post',
    data
  });
}
// 消息管理-编辑消息待办
export function editMessageAgent(data) {
  return request({
    url: '/api-message/message/messagemgt/edit',
    method: 'post',
    data
  });
}
// 消息管理-删除消息待办
export function deleteMessageAgent(data) {
  return request({
    url: '/api-message/message/messagemgt/delete',
    method: 'post',
    data
  });
}
// 消息管理-撤回消息待办
export function withdrawMessageAgent(data) {
  return request({
    url: '/api-message/message/messagemgt/withdraw',
    method: 'post',
    data
  });
}
// 消息管理、我的消息、我的待办-下载消息待办附件
export function downloadMessage(data) {
  return request({
    url: '/api-message/message/messagemgt/download',
    responseType: 'blob',
    headers: { 'Content-Type': 'application/octet-stream; charset=utf-8' },
    params: data,
    method: 'get'
  });
}
// 消息管理-上传附件
export function uploadMessageAgent(data) {
  return request({
    url: '/api-message/message/messagemgt/upload',
    method: 'post',
    data
  });
}
// 分组管理-分类-添加分类
export function saveGroupCategory(data) {
  return request({
    url: '/api-message/message/groupcategory/save',
    method: 'post',
    data
  });
}
// 分组管理-分类-编辑分类
export function editGroupCategory(data) {
  return request({
    url: '/api-message/message/groupcategory/edit',
    method: 'post',
    data
  });
}
// 分组管理-分类-删除之前先做判断
export function isDeleteGroupCategory(id) {
  return request({
    url: `/api-message/message/groupcategory/candelete/${id}`,
    method: 'post'
  });
}
// 分组管理-分类-删除分类   删除之前，请先调用candelete接口进行判断是否可以删除
export function deleteGroupCategory(id) {
  return request({
    url: `/api-message/message/groupcategory/delete/${id}`,
    method: 'post'
  });
}
// 分组管理-分类-结构树
export function groupCategoryListTree(typeId) {
  return request({
    url: `/api-message/message/groupcategory/listTree/${typeId}`,
    method: 'post'
  });
}
// 分组管理-分组-分组列表
export function messagegroupList(data) {
  return request({
    url: '/api-message/message/messagegroup/list',
    method: 'post',
    data
  });
}
// 分组管理-分组-添加分组   根据业务需要，分组和分类只能一一对应，对categoryId和groupKey有重复性检查，前端请注意返回类型是5018的话就重复了
export function addMessagegroupList(data) {
  return request({
    url: '/api-message/message/messagegroup/add',
    method: 'post',
    data
  });
}
// 分组管理-分组-编辑分组   根据业务需要，分组和分类只能一一对应，对categoryId和groupKey有重复性检查，前端请注意返回类型是5018的话就重复了
export function editMessagegroupList(data) {
  return request({
    url: '/api-message/message/messagegroup/edit',
    method: 'post',
    data
  });
}
// 分组及各分组的记录数量   我的消息、我的待办、消息管理，三个模块列表页搜索框区的分组及各分组的数量
export function messagegroupListNum(data) {
  return request({
    url: '/api-message/message/messagegroup/listnum',
    method: 'post',
    data
  });
}
// 通用-部门树   用于消息管理-新建消息-收件人选择-部门树，目前数据是造的
export function departmentListTree(typeId) {
  return request({
    url: `/api-message/message/department/listTree/${typeId}`,
    method: 'post'
  });
}
// 通用-部门树-部门人员   用于消息管理-新建消息-收件人选择-点击部门树的某个部门列出该部门下的所有人员（不分页），目前数据是造的
export function departmentUserListTree(id) {
  return request({
    url: `/api-message/message/departmentuser/listTree/${id}`,
    method: 'post'
  });
}
// 通用-角色树   用于消息管理-新建消息-收件人选择-部门树，目前数据是造的
export function roleListTree(typeId) {
  return request({
    url: `/api-message/message/role/listTree/${typeId}`,
    method: 'post'
  });
}
// 通用-角色树-角色人员   用于消息管理-新建消息-收件人选择-点击部门树的某个部门列出该部门下的所有人员（不分页），目前数据是造的
export function roleUserListTree(id) {
  return request({
    url: `/api-message/message/roleuser/listTree/${id}`,
    method: 'post'
  });
}
// 定时任务-逾期提醒   定时任务，用于检查是否有待办逾期，需确认
export function overdue() {
  return request({
    url: '/task/messagemgt/overdue',
    method: 'post'
  });
}
// 消息管理-根据模板添加消息待办
export function addByTemp(data) {
  return request({
    url: '/api-message/message/messagemgt/addbytemp',
    method: 'post',
    data
  });
}
// 消息管理-根据业务ID撤回消息待办
export function withDrawTodoById(data) {
  return request({
    url: '/api-message/message/messagemgt/withdrawtodobycid',
    method: 'post',
    data
  });
}
// 消息管理-根据模板添加检测项目分配
export function addAllocationMsg(data) {
  return request({
    url: '/api-message/message/messagemgt/jcfpbytemp',
    method: 'post',
    data
  });
}
