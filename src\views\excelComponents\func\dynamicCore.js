import { getSampleColorList, getSampleSuffixList, getSampleGroupMapList } from '@/utils/func/sampleGroup';
import $ from 'jquery';

// 动态线芯生成相关的方法

// 判断是否需要动态线芯
export const getXhModuleNumber = (routeName, originCoreNumber, experimentData) => {
  if (document.getElementsByClassName('appendXhmodule')) {
    if (routeName === 'ExperimentExcel') {
      // 检测项目库模块
      if (document.getElementById('columnIndex')?.value) {
        // 列循环
        startXhColumnModule(originCoreNumber, Number(document.getElementById('columnIndex').value));
      } else {
        startXhModule(originCoreNumber);
      }
    } else {
      // 检测执行页面
      if (experimentData) {
        // 如果已经保存过则按照之前保存的线芯数量来渲染
        var xhLength = 0;
        if (experimentData.coreRecord.coreColourList?.length > 0) {
          const sampleColorList = getSampleColorList(experimentData.coreRecord.coreColourList);
          const sampleSuffixList = getSampleSuffixList(experimentData.coreRecord.coreColourList);
          const groupMapList = getSampleGroupMapList(experimentData.coreRecord.coreColourList);
          if (experimentData.saveColorNumber) {
            if (experimentData.coreRecord.coreColourList?.length >= experimentData.saveColorNumber) {
              xhLength = experimentData.saveColorNumber;
              startXhModule(xhLength, sampleSuffixList);
              beRelatedTo(sampleColorList, sampleSuffixList, groupMapList);
            }
          } else {
            xhLength = experimentData.coreRecord.coreColourList.length;
            startXhModule(xhLength, sampleSuffixList);
            beRelatedTo(sampleColorList, sampleSuffixList, groupMapList);
          }
        }
      }
    }
  }
};

// 将线芯颜色赋值给对应的元素
const setXXYSValue = (colorList, suffixList, beRelatedNodeList) => {
  for (var x = 0; x < colorList.length; x++) {
    const coreEle = document.getElementById(`xxys_${suffixList[x]}`);
    if (coreEle) {
      coreEle.options.add(new Option(colorList[x], colorList[x]));
      coreEle.value = colorList[x];
      if (beRelatedNodeList.length > 0) {
        beRelatedNodeList[x].value = colorList[x];
      }
    }
  }
};
// 判断是否有跟线芯颜色相依赖的参数
const beRelatedTo = (sampleColorList, sampleSuffixList, groupMapList) => {
  const beRelatedNodeList = document.querySelectorAll('select[be-related-to]');
  setXXYSValue(sampleColorList, sampleSuffixList, beRelatedNodeList);
  const dynamicGroupingNode = document.getElementsByClassName('dynamicGrouping');
  setDynamicGrouping(groupMapList, dynamicGroupingNode, sampleSuffixList);
};
// 为动态试样分组其他相关参数赋值
const setDynamicGrouping = (groupMapList, nodeList, sampleSuffixList) => {
  const ids = Array.from(nodeList).map(item => {
    return item.id.split('_')[0];
  });
  sampleSuffixList.forEach((item, index) => {
    new Set(ids).forEach(idItem => {
      if (document.getElementById(`${idItem}_${item}`)) {
        document.getElementById(`${idItem}_${item}`).value = groupMapList[index][idItem] || '';
      }
    });
  });
};

/**
 * 满足动态线芯的条件开始循环
 * @param {*} length
 */
export const startXhModule = (length, sampleSuffixList = []) => {
  if (length) {
    if (document.getElementById('xhmoduleIndex')) {
      document.getElementById('xhmoduleIndex').value = length;
    }
    for (var i = 0; i < document.getElementsByClassName('appendXhmodule').length; i++) {
      const xhDom = document.getElementsByClassName('xhModule')[i].innerHTML;
      document.getElementsByClassName('appendXhmodule')[i].innerHTML = '';
      const newDom = getNewModuleString(xhDom, length, sampleSuffixList);
      document.getElementsByClassName('appendXhmodule')[i].insertAdjacentHTML('beforeend', newDom);
    }
  } else {
    for (var j = 0; j < document.getElementsByClassName('appendXhmodule').length; j++) {
      document.getElementsByClassName('appendXhmodule')[j].innerHTML = '';
    }
  }
};

export const getNewModuleString = (xhString, moduleIndex, sampleSuffixList) => {
  var newString = '';
  if (sampleSuffixList && sampleSuffixList.length > 0) {
    for (let i = 1; i <= moduleIndex; i++) {
      newString +=
        '<div class="newXhModule pdf-page pdf-page-' +
        i +
        '">' +
        xhString.replace(/moduleIndex/g, sampleSuffixList[i - 1]) +
        '</div>';
    }
  } else {
    for (let j = 1; j <= moduleIndex; j++) {
      newString +=
        '<div class="newXhModule pdf-page pdf-page-' + j + '">' + xhString.replace(/moduleIndex/g, j) + '</div>';
    }
  }
  return newString;
};
/**
 * 满足动态线芯的条件开始循环
 * @param {*} length
 */
export const startXhColumnModule = (length, columnNum, sampleSuffixList = []) => {
  if (length) {
    if (document.getElementById('xhmoduleIndex')) {
      document.getElementById('xhmoduleIndex').value = length;
    }
    for (var i = 0; i < document.getElementsByClassName('appendXhmodule').length; i++) {
      document.getElementsByClassName('appendXhmodule')[i].innerHTML = '';
      const xhDom = document.getElementsByClassName('xhModule')[i].innerHTML; // 大的循环体
      const columnXhDom = document.getElementsByClassName('xhModule')[i].querySelector('.columnRow').innerHTML; // 列的循环体
      let columnAll = ''; // 这个循环体的所有列
      for (var index = 1; index <= length; index++) {
        if (sampleSuffixList && sampleSuffixList.length > 0) {
          columnAll += getNewColumnString(columnXhDom, sampleSuffixList[index - 1]);
        } else {
          columnAll += getNewColumnString(columnXhDom, index);
        }
        if (index % columnNum === 0 || index === length) {
          let moduleIndex = 0;
          if (index === length) {
            moduleIndex = Math.floor(index / columnNum + 1);
          } else {
            moduleIndex = Math.floor(index / columnNum);
          }
          const newDom = getNewColumnModuleString(xhDom, moduleIndex);
          document.getElementsByClassName('appendXhmodule')[i].insertAdjacentHTML('beforeend', newDom);
          $(`.pdf-page-${moduleIndex}`).find('.columnXhModule').html(columnAll);
          columnAll = '';
        }
      }
    }
  } else {
    for (var j = 0; j < document.getElementsByClassName('appendXhmodule').length; j++) {
      document.getElementsByClassName('appendXhmodule')[j].innerHTML = '';
    }
  }
};
const getNewColumnModuleString = (xhString, index) => {
  var newString =
    '<div class="newXhModule pdf-page pdf-page-' + index + '">' + xhString.replace(/moduleIndex/g, index) + '</div>';
  return newString;
};
const getNewColumnString = (xhString, index) => {
  var newString = '<div class="newXhColumn columnRow bd-r">' + xhString.replace(/moduleIndex/g, index) + '</div>';
  return newString;
};
/**
 * 禁用线芯选择
 */
export const disabledXxysSelection = () => {
  const inputEleCollection = document.getElementsByClassName('ipt');
  Array.from(inputEleCollection).forEach(item => {
    const id = item.getAttribute('id');
    if ($('#' + id).attr('data-code') === 'XXYS') {
      if (!$('#' + id).attr('disabled')) {
        $('#' + id).attr('disabled', true);
      }
      if ($('#' + id).hasClass('bg-blue')) {
        $('#' + id).removeClass('bg-blue');
      }
    }
  });
};
