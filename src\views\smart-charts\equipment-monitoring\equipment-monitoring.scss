@import '@/styles/intelligentChart.scss';

.dataBoard {
  text-align: left;
  background: #253d87;
  height: 100%;
  overflow-y: auto;
  .inBlock {
    display: inline-block;
  }
  .dataPageTop {
    position: relative;
    .dataPageTitle {
      font-size: 45px;
      background: -webkit-linear-gradient(left, #87b7ff 1%, #ffffff 20%, #ffffff 50%, #ffffff 80%, #87b7ff 100%);
      -webkit-text-fill-color: transparent;
      font-family: 'YouSheBiaoTiHei';
      font-weight: bold;
      line-height: 48px;
      letter-spacing: 5px;
      -webkit-background-clip: text;
      background-clip: text;
      text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
      text-align: center;
      display: inline-block;
      position: absolute;
      top: 10%;
      left: 50%;
      transform: translate(-50%);
    }
    .logo {
      position: absolute;
      left: 7%;
      top: 32%;
    }
    .logoRight {
      position: absolute;
      max-height: 37px;
      max-width: 150px;
      left: 9.8%;
      top: 43%;
    }
    .topHeader {
      width: 100%;
      height: 79px;
    }
    .topRight {
      position: absolute;
      width: 24%;
      right: -10%;
      top: 45%;
      transform: translate(-45%);
      font-size: 18px;
      color: $color;
      img {
        vertical-align: middle;
      }
    }
    .time {
      margin-right: 41px;
      font-size: 18px;
      line-height: 26px;
      &:first-child img {
        margin-right: 14px;
        position: relative;
        top: -1px;
      }
      &:nth-child(2) img {
        margin-right: 5px;
        position: relative;
        top: -1px;
      }
    }
    .el-icon-setting {
      cursor: pointer;
    }
  }
  .componentItem {
    margin-top: 27px;
    padding-right: 20px;
  }
}

.dataBoardContent {
  padding: 0 20px 2px 40px;
  height: calc(100vh - 6.2rem);
  overflow-y: auto;
  .textLeft {
    text-align: left;
  }
  .topHeader {
    width: 100%;
  }
  ul,
  li,
  div {
    margin: 0;
    padding: 0;
  }
  li {
    list-style: none;
  }
  .textRight {
    text-align: right;
  }
  h1 {
    font-weight: 700;
    font-size: 18px;
    line-height: 26px;
    color: $titleColor;
    padding: 7px 0 7px 29px;
    margin: 0;
  }
  .box-top {
    background: linear-gradient(90deg, #66b3e7 0%, #3f7ab6 100%);
    border-radius: 8px 8px 0px 0px;
    position: relative;
  }
  .box-Center {
    position: relative;
    background: linear-gradient(180deg, #2d59a5 0%, #4280b9 100%);
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 0 0 8px 8px;
    height: 360px;
  }
  .singleData {
    position: relative;
    background: linear-gradient(180deg, #2d59a5 0%, #4280b9 100%);
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 0 0 8px 8px;
    max-height: 260px;
    overflow-y: auto;
  }
  .jkt {
    padding: 18px 26px 12px 26px;
    line-height: 24px;
    color: #fff;
    font-size: 14px;
    .name {
      margin-right: 10px;
    }
  }
  .phb::-webkit-scrollbar {
    width: 0 !important;
  }
  :deep(.el-progress-bar__inner) {
    background: linear-gradient(90deg, #f18aa3 0%, #ffd27a 99.22%);
  }
  :deep(.el-progress__text) {
    color: #b9e3ff;
  }
}
.dataBoardContent::-webkit-scrollbar {
  width: 0 !important;
}
