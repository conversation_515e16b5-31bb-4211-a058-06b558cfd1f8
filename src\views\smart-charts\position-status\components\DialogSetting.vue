<template>
  <el-dialog
    v-model="dialogVisiable"
    title="设置模块"
    :close-on-click-modal="false"
    width="450px"
    top="50px"
    @close="handleClose"
  >
    <ul v-loading="loading">
      <li v-for="(item, index) in moduleList" :key="item.code" style="display: flex">
        <el-checkbox v-model="item.isShow" />
        <div style="flex: auto" @click="handleClick(index)">{{ item.name }}</div>
      </li>
    </ul>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="loading" size="small" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="small" @click="onSubmit">确 认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, toRefs, watch, getCurrentInstance } from 'vue';
import { queryConfList, updateConf } from '@/api/positionStatus';
export default {
  name: 'DialogSetting',
  components: {},
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      dialogVisiable: false,
      loading: false,
      moduleList: []
    });
    watch(
      () => props.dialogShow,
      newValue => {
        state.dialogVisiable = newValue;
        if (state.dialogVisiable) {
          state.moduleList = [];
          initList();
        }
      }
    );
    const initList = () => {
      state.loading = true;
      queryConfList().then(res => {
        state.loading = false;
        if (res) {
          state.moduleList = res.data.data;
        }
      });
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog');
    };
    // 提交
    const onSubmit = () => {
      state.loading = true;
      updateConf(state.moduleList).then(res => {
        state.loading = false;
        if (res) {
          context.emit('closeDialog', true);
          proxy.$message.success(res.data.message);
        }
      });
    };
    // 切换选中取消
    const handleClick = index => {
      state.moduleList[index].isShow = !state.moduleList[index].isShow;
    };
    return { ...toRefs(state), initList, handleClose, onSubmit, handleClick };
  }
};
</script>
<style lang="scss" scoped>
ul {
  margin: 0;
  padding: 0 10px;
}
li {
  list-style: none;
  line-height: 40px;
  padding: 0 10px;
  cursor: pointer;
  &:hover {
    background-color: $subMenuHover;
  }
  .el-checkbox {
    margin-right: 10px;
  }
}
</style>
