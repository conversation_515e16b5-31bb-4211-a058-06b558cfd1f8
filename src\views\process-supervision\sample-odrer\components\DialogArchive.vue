<template>
  <el-dialog
    v-model="dialogShow"
    title="归档进度"
    :close-on-click-modal="false"
    width="680px"
    :show-close="false"
    :before-close="handleClose"
  >
    <div class="archiveContent">
      <div class="step_list">
        <div v-for="(val, key) in stepList" :key="key" class="step_item">
          <span :class="{ active: detailInfo.steps > key || detailInfo.steps === 4 }">{{ val.name }}</span>
        </div>
      </div>
      <el-progress
        :percentage="detailInfo.progress"
        :stroke-width="15"
        status="success"
        striped
        striped-flow
        :duration="10"
      />
      <el-button v-if="detailInfo.message === '归档完成'" type="primary" style="margin-top: 30px" @click="handleClose()"
        >归档完成</el-button
      >
      <div v-else class="text">{{ detailInfo.message }}</div>
    </div>
    <!-- 归档进度弹出窗 -->
  </el-dialog>
</template>
<script>
import { reactive, toRefs, watch } from 'vue';
import { samplesArchivalApi } from '@/api/order';
import { mapGetters } from 'vuex';
import { ElMessage } from 'element-plus';
import { batchPrint } from '@/api/excel';
import router from '@/router/index.js';
import { capabilityBysamplesId } from '@/api/execution';
export default {
  name: 'DialogArchive',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    selectArchive: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    const state = reactive({
      dialogShow: false,
      isSure: true, // 是否允许归档
      experimentIds: [], // 批量打印模板的id
      detailInfo: {
        progress: 0
      },
      sampleId: '',
      stepList: {
        1: {
          name: '样品信息查询中'
        },
        2: {
          name: '原始记录查询中'
        },
        3: {
          name: '检测报告查询中'
        },
        4: {
          name: '归档完成'
        }
      }
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.detailInfo = {
          progress: 0,
          sampleId: newValue.selectArchive.sampleId
        };
        state.isSure = true;
        startOperation();
        getTestItem();
      }
    });
    // 开始操作
    const startOperation = () => {
      samplesArchivalApi({ sampleId: state.detailInfo.sampleId }).then(res => {
        if (res) {
          ElMessage.success(res.data.data);
        } else {
          state.isSure = false;
          handleClose();
        }
      });
    };
    // 获取检测项目
    const getTestItem = () => {
      capabilityBysamplesId(state.detailInfo.sampleId).then(res => {
        if (res) {
          const resultData = res.data.data;
          if (JSON.stringify(resultData) !== '{}') {
            if (!resultData[0].length && !resultData[1].length && resultData[2].length > 0 && state.isSure) {
              state.experimentIds = resultData[2].map(item => {
                return item.experimentId;
              });
              getTemplate();
            }
          }
        }
      });
    };
    const getTemplate = () => {
      batchPrint(state.experimentIds).then(res => {
        if (res) {
          const { href } = router.resolve({
            path: '/sample/templatePdf',
            query: {
              type: 'print',
              sampleId: state.detailInfo.sampleId,
              printlist: state.experimentIds.toString()
            }
          });
          window.open(href, '_blank');
        }
      });
    };
    // 关闭抽屉
    const handleClose = () => {
      state.dialogShow = false;
      context.emit('closeDialog', false);
    };
    // 计算进度条
    const initProgress = detailInfo => {
      console.log(detailInfo);
      if (detailInfo.steps === 4) {
        state.detailInfo.progress = 100;
      } else if (detailInfo.steps === 2 && detailInfo.progress === 0) {
        // 第一步已结束
        state.detailInfo.progress = 25;
      } else if (detailInfo.steps === 2 && detailInfo.progress !== 0) {
        state.detailInfo.progress = 25 + 25 * detailInfo.progress;
      } else if (detailInfo.steps === 3) {
        // 开始第三步，进度在50%-75%之间
        state.detailInfo.progress = 50 + Math.floor(Math.random() * 25) + 1;
      } else if (detailInfo.steps === 1) {
        state.detailInfo.progress = Math.floor(Math.random() * 25) + 1;
      }
    };
    return { ...toRefs(state), handleClose, startOperation, getTestItem, getTemplate, initProgress };
  },
  computed: {
    ...mapGetters(['webSocketMsg'])
  },
  watch: {
    webSocketMsg(msg) {
      const vm = this;
      if (JSON.stringify(msg).indexOf('{"cmd":33}') !== -1 && JSON.stringify(msg).indexOf('header')) {
        const detailData = msg.body;
        if (detailData.steps !== -1) {
          vm.detailInfo = { ...vm.detailInfo, ...detailData };
          vm.initProgress(vm.detailInfo);
        } else {
          vm.handleClose();
        }
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.archiveContent {
  text-align: center;
  margin: 50px 0 20px 0;
}
.step_list {
  margin: 0 0 20px 0;
}
.text {
  line-height: 30px;
  margin-top: 20px;
  font-size: 16px;
}
.step_item {
  display: inline-block;
  background: repeating-linear-gradient(90deg, #d8d8d8 0, #d8d8d8 1px, #0000 0, #0000) no-repeat bottom,
    repeating-linear-gradient(90deg, #d8d8d8 0, #d8d8d8 1px, #0000 0, #0000 7px) no-repeat bottom;
  background-size: 100% 100%, 100% 7px;
  text-align: center;
  &:last-child {
    background: repeating-linear-gradient(90deg, #d8d8d8 0, #d8d8d8 1px, #0000 0, #0000) no-repeat bottom,
      repeating-linear-gradient(90deg, #d8d8d8 0, #d8d8d8 0.0625rem, #0000 0, #0000 7px) no-repeat bottom,
      repeating-linear-gradient(90deg, #0000 0, #0000 calc(100% - 1px), #d8d8d8 calc(100% - 0.125rem), #d8d8d8)
        no-repeat bottom;
    background-size: 100% 100%, 100% 7px;
  }
  span {
    display: inline-block;
    line-height: 15px;
    position: relative;
    font-size: 12px;
    top: -17px;
    &.active {
      color: #00b678;
    }
  }
  &:nth-child(1) {
    width: 20%;
  }
  &:nth-child(2) {
    width: 36%;
  }
  &:nth-child(3) {
    width: 22%;
  }
  &:nth-child(4) {
    width: 22%;
  }
}
:deep(.el-progress-bar__inner) {
  background: repeating-linear-gradient(-60deg, #fff3 0, #fff3 2px, #0000 2px, #0000 8px) no-repeat bottom #00b678;
}
:deep(.el-progress.is-success .el-progress__text) {
  display: none;
}
</style>
