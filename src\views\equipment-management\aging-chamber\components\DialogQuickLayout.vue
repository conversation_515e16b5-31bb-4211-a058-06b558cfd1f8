<template>
  <el-dialog v-model="dialogShow" title="新增放样" :close-on-click-modal="false" :width="500" @close="handleClose()">
    <div v-loading="dialogLoading">
      <el-form
        v-if="dialogShow"
        ref="formRef"
        v-loading="dialogLoading"
        :model="formData"
        label-position="right"
        label-width="100px"
        size="small"
      >
        <el-form-item
          label="样品信息："
          prop="sampleName"
          :rules="{ required: true, message: '请输入样品信息', trigger: 'blur' }"
        >
          <el-input v-model="formData.sampleName" maxlength="100" placeholder="请输入样品信息" clearable />
        </el-form-item>
        <el-form-item
          label="试样数量："
          prop="sampleCount"
          :rules="{ required: true, message: '请输入试样数量', trigger: 'blur' }"
        >
          <el-input-number
            v-model="formData.sampleCount"
            controls-position="right"
            :min="0"
            placeholder="请输入试样数量"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item
          label="试样温度（℃）："
          prop="sampleTemperature"
          :rules="{ required: true, message: '请输入试样温度', trigger: 'blur' }"
        >
          <el-input-number
            v-model="formData.sampleTemperature"
            controls-position="right"
            placeholder="请输入试样温度"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item
          label="开始时间："
          prop="startDateTime"
          :rules="{ required: true, message: '请选择开始时间', trigger: 'blur' }"
        >
          <el-date-picker
            v-model="formData.startDateTime"
            type="datetime"
            :clearable="false"
            placeholder="请选择开始时间"
            :disabled-date="
              time => {
                return disabledDateStart(time, formData.endDateTime);
              }
            "
            style="width: 100%"
            @change="
              val => {
                return handleChangeDate(val, 'startDateTime');
              }
            "
          />
        </el-form-item>
        <el-form-item
          label="结束时间："
          prop="endDateTime"
          :rules="{ required: true, message: '请选择结束时间', trigger: 'change' }"
        >
          <el-date-picker
            v-model="formData.endDateTime"
            type="datetime"
            :clearable="false"
            placeholder="请选择结束时间"
            :disabled-date="
              time => {
                return disabledDateEnd(time, formData.startDateTime);
              }
            "
            style="width: 100%"
            @change="
              val => {
                return handleChangeDate(val, 'endDateTime');
              }
            "
          />
        </el-form-item>
        <el-form-item label="放置周期：">
          <el-row>
            <el-col :span="9">
              <el-input v-model="formData.periodHour" placeholder="请输入" clearable @change="handleChangeHoursMin" />
            </el-col>
            <el-col :span="3" class="text-center">h</el-col>
            <el-col :span="9">
              <el-input v-model="formData.periodMinute" placeholder="请输入" clearable @change="handleChangeHoursMin" />
            </el-col>
            <el-col :span="3" class="text-center">min</el-col>
          </el-row>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose()">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, ref, toRefs, watch } from 'vue';
import { formatDateTime, differenceTimes, calculateEndTime } from '@/utils/formatTime';
import { sampleAdd } from '@/api/aging-chamber';
import { ElMessage } from 'element-plus';
export default {
  name: 'DialogQuickLayout',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    boxId: {
      type: String,
      default: ''
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    const state = reactive({
      type: '', // 弹出窗类型
      dialogLoading: false, // 弹出窗loading
      formData: {
        startDateTime: '',
        endDateTime: ''
      }, // 表单数据
      dialogShow: false,
      disabledDateStart: (time, endDate) => {
        if (endDate) {
          return time.getTime() >= new Date(endDate).getTime();
        } else {
          return;
        }
      },
      disabledDateEnd: (time, startDate) => {
        if (startDate) {
          return time.getTime() < new Date(startDate).getTime();
        } else {
          return;
        }
      },
      formRef: ref(),
      listLoading: false,
      tableRef: ref(null)
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.formData = {
          startDateTime: formatDateTime(new Date())
        };
      }
    });
    const handleChangeDate = (val, fieldType) => {
      if (val) {
        state.formData[fieldType] = formatDateTime(val);
      }
      if (state.formData.startDateTime && state.formData.endDateTime) {
        const { hours, minutes } = differenceTimes(state.formData.startDateTime, state.formData.endDateTime);
        state.formData.periodHour = hours;
        state.formData.periodMinute = minutes;
      }
    };

    const onSubmit = () => {
      state.formRef.validate(async valid => {
        if (valid) {
          const { data } = await sampleAdd({ boxId: props.boxId, ...state.formData }).finally(
            (state.dialogLoading = false)
          );
          if (data) {
            ElMessage.success('放样成功！');
            handleClose(true);
          }
        } else {
          return false;
        }
      });
    };
    const handleChangeHoursMin = () => {
      state.formData.endDateTime = calculateEndTime(
        state.formData.startDateTime,
        state.formData.periodHour || 0,
        state.formData.periodMinute || 0
      );
    };
    // 关闭弹出窗
    const handleClose = isRefresh => {
      context.emit('closeDialog', isRefresh);
    };

    return {
      ...toRefs(state),
      onSubmit,
      handleChangeDate,
      handleChangeHoursMin,
      handleClose,
      formatDateTime
    };
  }
};
</script>
<style lang="scss" scoped></style>
