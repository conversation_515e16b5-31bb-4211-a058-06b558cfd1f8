<!-- 考勤管理 -->
<template>
  <el-dialog
    v-model="dialogShow"
    title="考勤管理"
    :close-on-click-modal="false"
    width="880px"
    top="4vh"
    @close="handleClose"
  >
    <el-form
      v-if="dialogShow"
      ref="ruleForm"
      v-loading="dialogLoading"
      :model="formData"
      :hide-required-asterisk="true"
      label-position="right"
      size="small"
    >
      <el-row>
        <el-col class="textLeft" :span="quickIsEdit ? 4 : 13">
          <span class="label">快捷设置考勤时间：</span>
          <span v-if="!quickIsEdit">
            {{ formData.quickStartTime + '-' + formData.quickEndTime }}
            <i class="el-icon-edit" style="margin-right: 10px" @click="quickIsEdit = true" />
          </span>
        </el-col>
        <el-col v-if="quickIsEdit" :span="4">
          <el-form-item
            label=""
            prop="quickStartTime"
            :rules="{ required: true, message: '请选择开始时间', trigger: 'change' }"
          >
            <el-time-select
              v-model="formData.quickStartTime"
              :max-time="formData.quickEndTime"
              placeholder="开始时间"
              start="00:00"
              step="0:30"
              end="23:30"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="quickIsEdit" :span="4" style="display: flex">
          <span class="interval">-</span>
          <el-form-item
            label=""
            prop="quickEndTime"
            :rules="{ required: true, message: '请选择结束时间', trigger: 'change' }"
          >
            <el-time-select
              v-model="formData.quickEndTime"
              :min-time="formData.quickStartTime"
              placeholder="结束时间"
              start="00:00"
              step="0:30"
              end="23:30"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="quickIsEdit" :span="4" class="textRight">
          <el-button type="primary" size="small" @click="saveQuick()">保存</el-button>
          <el-button size="small" @click="cancleSet('quick')">取消</el-button>
        </el-col>
      </el-row>
      <el-table
        ref="tableRef"
        :data="formData.weekdayConfigs"
        fit
        border
        size="medium"
        height="auto"
        highlight-current-row
        class="dark-table format-height-table base-table"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" prop="checkbox" :width="colWidth.checkbox" align="center" fixed="left" />
        <el-table-column label="工作日" prop="label" width="160px" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.label || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="考勤时间" prop="reportNo" :min-width="180" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-row v-if="row.isEdit">
              <el-col :span="11">
                <el-form-item label="" :prop="'weekdayConfigs.' + $index + '.startTime'">
                  <el-time-select
                    v-model="row.startTime"
                    start="00:00"
                    step="0:30"
                    end="23:30"
                    :max-time="row.endTime"
                    placeholder="开始时间"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="11" :offset="2">
                <el-form-item label="" :prop="'weekdayConfigs.' + $index + '.endTime'">
                  <el-time-select
                    v-model="row.endTime"
                    start="00:00"
                    step="0:30"
                    end="23:30"
                    :min-time="row.startTime"
                    placeholder="结束时间"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <div v-else>
              <span v-if="row.startTime === '' && row.endTime === ''">休息</span>
              <span v-else> {{ (row.startTime || '/') + '-' + (row.endTime || '/') }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="100px" class-name="fixed-right">
          <template #default="{ row, $index }">
            <span v-if="row.isEdit" class="blue-color" @click.stop="cancleTableTime($index)">取消</span>
            <span v-else class="blue-color" @click.stop="handleEditTime($index)">更改</span>
          </template>
        </el-table-column>
      </el-table>
      <el-row style="margin-top: 10px">
        <el-col :span="restIsEdit ? 2 : 13">
          <span class="label">休息时间：</span>
          <span v-if="!restIsEdit">
            {{ formData.restStartTime + '-' + formData.restEndTime }}
            <i class="el-icon-edit" style="margin-right: 10px" @click="restIsEdit = true" />
          </span>
        </el-col>
        <el-col v-if="restIsEdit" :span="4">
          <el-form-item
            label=""
            prop="restStartTime"
            label-width="0"
            :rules="{ required: true, message: '请选择开始时间', trigger: 'change' }"
          >
            <el-time-select
              v-model="formData.restStartTime"
              :max-time="formData.restEndTime"
              placeholder="开始时间"
              start="00:00"
              step="0:30"
              end="23:30"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="restIsEdit" :span="4" style="display: flex">
          <span class="interval">-</span>
          <el-form-item
            label=""
            prop="restEndTime"
            label-width="0"
            :rules="{ required: true, message: '请选择结束时间', trigger: 'change' }"
            style="flex: 1"
          >
            <el-time-select
              v-model="formData.restEndTime"
              :min-time="formData.restStartTime"
              placeholder="结束时间"
              start="00:00"
              step="0:30"
              end="23:30"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="restIsEdit" :span="2" class="textRight">
          <el-button size="small" @click="cancleSet('rest')">取消</el-button>
        </el-col>
      </el-row>
    </el-form>
    <span v-if="formDataHoliday.holidayList.length === 0" class="blue-color addHolidayTime" @click="handleAddHoliday"
      >添加放假时间</span
    >
    <el-form
      v-if="dialogShow && formDataHoliday.holidayList.length > 0"
      ref="ruleFormHoliday"
      class="holidayFormData"
      label-width="86px"
      :model="formDataHoliday"
      label-position="right"
      size="small"
    >
      <div v-if="formDataHoliday.holidayList.length > 0" class="holidayContent">
        <el-row v-for="(item, index) in formDataHoliday.holidayList" :key="index">
          <el-form-item
            label="放假时间："
            :prop="'holidayList.' + index + '.startTime'"
            :rules="{ required: !item.id, message: '请选择开始时间', trigger: 'change' }"
          >
            <span v-if="item.id">{{ item.startTime }}</span>
            <el-date-picker
              v-else
              v-model="item.startTime"
              :disabled-date="
                time => {
                  return disabledDateStart(time, item.endTime);
                }
              "
              type="date"
              placeholder="开始时间"
              @change="
                val => {
                  return handleFormatterDate(val, index, 'startTime');
                }
              "
            />
          </el-form-item>
          <span class="dateLine"> 至 </span>
          <el-form-item
            label=""
            label-width="0"
            :prop="'holidayList.' + index + '.endTime'"
            :rules="{ required: !item.id, message: '请选择结束时间', trigger: 'change' }"
          >
            <span v-if="item.id">{{ item.endTime }}</span>
            <el-date-picker
              v-else
              v-model="item.endTime"
              type="date"
              :disabled-date="
                time => {
                  return disabledDateEnd(time, item.startTime);
                }
              "
              placeholder="结束时间"
              @change="
                val => {
                  return handleFormatterDate(val, index, 'endTime');
                }
              "
            />
          </el-form-item>
          <span class="blue-color" @click="deleteHoliday(index, item)">删除</span>
          <span
            v-if="formDataHoliday.holidayList.length > 0 && index === formDataHoliday.holidayList.length - 1"
            class="blue-color"
            @click="handleAddHoliday"
            >添加放假时间</span
          >
        </el-row>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, ref, toRefs, watch, getCurrentInstance } from 'vue';
// import { formatDate } from '@/utils/formatTime'
import { colWidth } from '@/data/tableStyle';
import { formatDate } from '@/utils/formatTime';
import { employeeAttendance, saveEmployeeAttendance, saveHolidayApi, employeeHoliday } from '@/api/dutyTime';
export default {
  name: 'DialogTimeManage',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      dialogLoading: false, // 弹出窗loading
      formData: {}, // 表单数据
      detailData: {}, // 表单详情
      formDataHoliday: {
        holidayList: []
      },
      disabledDateStart: (time, endTime) => {
        if (endTime) {
          return time.getTime() >= new Date(endTime).getTime();
        } else {
          return;
        }
      },
      disabledDateEnd: (time, startTime) => {
        if (startTime) {
          return time.getTime() < new Date(startTime).getTime() - 3600 * 1000 * 24;
        } else {
          return;
        }
      },
      tableRef: ref(),
      selected: [], // 选中的表格数据,用于设置快捷考勤时间
      oldFormData: {}, // 修改之前的数据,用于取消
      pickerOptions: {},
      deleteIds: [], // 删除的放假信息
      defaultValue: new Date(),
      dialogShow: false,
      quickIsEdit: false, // 是否开启设置考勤
      restIsEdit: false, // 是否开启设置休息时间
      tableData: [],
      tableOldData: [],
      tableLoading: false,
      ruleForm: ref(),
      ruleFormHoliday: ref(),
      listLoading: false
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.dialogLoading = false;
        state.formData = {
          startWorkTime: '09:00',
          endWorkTime: '18:00',
          weekdayConfigs: [
            {
              label: '周一',
              code: 1
            },
            {
              label: '周二',
              code: 2
            },
            {
              label: '周三',
              code: 3
            },
            {
              label: '周四',
              code: 4
            },
            {
              label: '周五',
              code: 5
            },
            {
              label: '周六',
              code: 6
            },
            {
              label: '周日',
              code: 7
            }
          ]
        };
        state.formDataHoliday = {
          holidayList: []
        };
        state.deleteIds = [];
        state.quickIsEdit = false;
        state.restIsEdit = false;
        initTable();
        initHoliday();
      }
    });
    const initTable = () => {
      state.dialogLoading = true;
      employeeAttendance().then(res => {
        state.dialogLoading = false;
        if (res) {
          const data = res.data.data;
          data.weekdayConfigs.forEach(item => {
            if (JSON.stringify(item.endTime) === '{}') {
              item.endTime = '';
            }
            if (JSON.stringify(item.startTime) === '{}') {
              item.startTime = '';
            }
            // state.tableData.push(item)
          });
          state.formData = JSON.parse(JSON.stringify(data));
          state.oldFormData = JSON.parse(JSON.stringify(data));
        }
      });
    };
    // 放假列表
    const initHoliday = () => {
      state.dialogLoading = true;
      employeeHoliday({ page: '-1' }).then(res => {
        state.dialogLoading = false;
        if (res) {
          state.formDataHoliday.holidayList = res.data.data.list;
        }
      });
    };
    // 保存考勤时间
    const saveAttendance = () => {
      return new Promise((resolve, reject) => {
        state.ruleForm.validate().then(valid => {
          if (valid) {
            saveEmployeeAttendance(state.formData).then(res => {
              if (res) {
                resolve(true);
              }
            });
          } else {
            resolve(false);
            return false;
          }
        });
      });
    };
    // 保存放假时间
    const saveHoliday = () => {
      return new Promise((resolve, reject) => {
        const params = {
          addList: state.formDataHoliday.holidayList.filter(item => {
            return !item.id;
          }),
          delIdList: state.deleteIds
        };
        if (state.formDataHoliday.holidayList.length === 0) {
          saveHolidayApi(params).then(res => {
            if (res) {
              resolve(true);
            }
          });
        } else {
          state.ruleFormHoliday.validate().then(valid => {
            if (valid) {
              saveHolidayApi(params).then(res => {
                if (res) {
                  resolve(true);
                }
              });
            } else {
              resolve(false);
              return false;
            }
          });
        }
      });
    };
    const onSubmit = () => {
      Promise.all([saveAttendance(), saveHoliday()])
        .then(result => {
          if (result[0] && result[1]) {
            proxy.$message.success('配置成功');
            state.dialogShow = false;
            context.emit('closeDialog', true);
          }
        })
        .catch(() => {});
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog', false);
    };
    const handleSelectionChange = val => {
      state.selected = val.map(item => {
        return item.code;
      });
    };
    const handleRowClick = row => {
      const index = state.selected.findIndex(code => {
        return code === row.code;
      });
      if (index !== -1) {
        state.tableRef.toggleRowSelection(row, false);
        // state.selected.splice(index, 1)
      } else {
        state.tableRef.toggleRowSelection(row, true);
        // state.selected.push(row.code)
      }
    };
    const handleEditTime = index => {
      state.formData.weekdayConfigs[index].isEdit = true;
    };
    const deleteHoliday = (index, row) => {
      state.formDataHoliday.holidayList.splice(index, 1);
      if (row.id) {
        state.deleteIds.push(row.id);
      }
    };
    const handleAddHoliday = () => {
      state.formDataHoliday.holidayList.push({ startTime: '' });
    };
    // 保存快捷设置考勤时间
    const saveQuick = () => {
      if (state.selected.length === 0) {
        proxy.$message.warning('请先勾选工作日');
        return false;
      }
      state.selected.forEach(code => {
        state.formData.weekdayConfigs[code - 1].startTime = state.formData.quickStartTime;
        state.formData.weekdayConfigs[code - 1].endTime = state.formData.quickEndTime;
        state.formData.weekdayConfigs[code - 1].isEdit = true;
      });
    };
    // 取消设置快捷考勤时间\休息时间
    const cancleSet = key => {
      state.formData[key + 'StartTime'] = state.oldFormData[key + 'StartTime'];
      state.formData[key + 'EndTime'] = state.oldFormData[key + 'EndTime'];
      state[key + 'IsEdit'] = false;
    };
    // 取消表格考勤时间设置
    const cancleTableTime = index => {
      state.formData.weekdayConfigs[index] = state.oldFormData.weekdayConfigs[index];
    };
    // 格式化日期
    const handleFormatterDate = (val, index, type) => {
      state.formDataHoliday.holidayList[index][type] = formatDate(val);
    };
    return {
      ...toRefs(state),
      initTable,
      initHoliday,
      formatDate,
      handleFormatterDate,
      saveAttendance,
      saveHoliday,
      handleRowClick,
      onSubmit,
      saveQuick,
      cancleSet,
      cancleTableTime,
      handleClose,
      handleEditTime,
      handleAddHoliday,
      deleteHoliday,
      colWidth,
      handleSelectionChange
    };
  }
};
</script>
<style lang="scss" scoped>
.textRight {
  text-align: right;
}
.textLeft {
  text-align: left;
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 100%;
}
:deep(.el-select) {
  width: 100%;
}
:deep(.el-date-editor--timerange.el-input__inner) {
  width: 100%;
}
.blue-color {
  display: inline-block;
  line-height: 31px;
  margin-left: 10px;
}
.label {
  display: inline-block;
  line-height: 32px;
  text-align: right;
}
.interval {
  display: inline-block;
  line-height: 32px;
  width: 15px;
  text-align: center;
}
.addHolidayTime {
  margin-left: 0;
}
.dateLine {
  display: inline-block;
  line-height: 31px;
  margin: 0 3px;
}
.holidayContent {
  max-height: 250px;
  overflow-y: auto;
}
.holidayFormData {
  background-color: #f0f2f5;
  border-radius: 5px;
  padding: 15px 10px 0 10px;
}
</style>
