<template>
  <div
    v-show="softKeyboard"
    class="softKeyboardTips"
    :style="{ left: clientOffset.clientX + 'px', top: clientOffset.clientY + 'px' }"
  >
    <ul>
      <li v-for="(val, key) in operateType" :key="key" @click="handleSelect(key)">{{ val.name }}</li>
    </ul>
  </div>
</template>

<script>
import { nextTick, reactive, toRefs, watch } from 'vue';
export default {
  name: 'SoftKeyboard',
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    focusId: {
      type: String,
      default: ''
    },
    clientOffset: {
      type: Object,
      default: () => {
        return {};
      }
    },
    selectMark: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  setup(props, content) {
    const state = reactive({
      clientOffset: {},
      focusId: '',
      selectMarkInfo: {},
      operateType: {
        1: { name: '上标', content: '<sup></sup>' },
        2: { name: '下标', content: '<sub></sub>' },
        3: { name: '斜体', content: '<i></i>' },
        4: { name: '加粗', content: '<b></b>' }
      },
      lastStepType: '', // 上一步的类型
      softKeyboard: false
    });
    watch(
      () => [props.isShow, props.clientOffset, props.selectMark],
      ([newIsShow, newClientOffset, newSelectMark]) => {
        state.softKeyboard = newIsShow;
        if (state.softKeyboard) {
          state.clientOffset = newClientOffset;
          state.focusId = props.focusId;
          state.selectMarkInfo = newSelectMark;
          document.getElementById('yw').focus();
        }
      }
    );
    // 选择添加的操作方式
    const handleSelect = type => {
      document.getElementById('yw').value = document.getElementById('yw').value + state.operateType[type].content;
      nextTick(() => {
        document.getElementById('yw').style.display = 'inline-block';
        document.getElementById('yw_rich_text').style.display = 'none';
        document.getElementById('yw').focus();
        if (type === '1' || type === '2') {
          document
            .getElementById('yw')
            .setSelectionRange(
              document.getElementById('yw').value.length - 6,
              document.getElementById('yw').value.length - 6
            );
        } else {
          document
            .getElementById('yw')
            .setSelectionRange(
              document.getElementById('yw').value.length - 4,
              document.getElementById('yw').value.length - 4
            );
        }
      });
    };
    return {
      ...toRefs(state),
      handleSelect
    };
  }
};
</script>

<style lang="scss" scoped>
.softKeyboardTips {
  background: #fff;
  // width: 120px;
  // max-height: 200px;
  position: fixed;
  font-size: 14px;
  // overflow-y: auto;
  // padding: 10px 10px;
  text-align: left;
  box-shadow: 0px 4px 12px 4px rgba(0, 0, 0, 0.08), 0px 2px 4px -2px rgba(0, 0, 0, 0.16);
  :deep(.el-tabs--border-card > .el-tabs__content) {
    padding: 0;
  }
  ul {
    margin: 0;
    padding: 0;
  }
  li {
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    word-wrap: break-word;
    &:hover {
      background: $tes-primary2;
      color: $tes-primary;
    }
  }
}
</style>
