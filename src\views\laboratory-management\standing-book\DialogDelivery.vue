<template>
  <el-dialog v-model="dialogShow" title="出库登记" :close-on-click-modal="false" width="480px" @close="handleClose">
    <el-form
      v-if="dialogShow"
      ref="ruleForm"
      v-loading="dialogLoading"
      :model="formData"
      label-position="right"
      label-width="100px"
      size="small"
    >
      <el-form-item label="辅料编号：" prop="no">
        {{ formData.no || '--' }}
      </el-form-item>
      <el-form-item label="辅料名称：" prop="name">
        {{ formData.name || '--' }}
      </el-form-item>
      <el-form-item
        label="登记人："
        prop="userId"
        :rules="{ required: true, message: '请选择登记人', trigger: 'blur' }"
      >
        <el-select v-model="formData.userId" clearable filterable placeholder="请选择登记人">
          <el-option v-for="item in nameList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item
        label="登记日期："
        prop="recordDate"
        :rules="{ required: true, message: '请选择登记日期', trigger: 'blur' }"
      >
        <el-date-picker v-model="formData.recordDate" type="date" placeholder="请选择登记日期" />
      </el-form-item>
      <el-form-item
        label="出库数量："
        prop="num"
        :rules="{ required: true, message: '请输入出库数量', trigger: 'blur' }"
      >
        <el-row>
          <el-col :span="20">
            <el-input-number v-model="formData.num" :min="0" controls-position="right" placeholder="请输入出库数量" />
          </el-col>
          <el-col :span="4">
            <div class="unitClass">{{ unitAllJson[formData.unit] }}</div>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="批号：" prop="batchNo">
        <el-input v-model="formData.batchNo" type="text" placeholder="请输入批号" />
      </el-form-item>
      <el-form-item label="关联样品编号：" prop="sampleNo">
        <el-tag v-if="selectedData.sampleNo" effect="dark" closable @close="handleDeleteTag">
          {{ selectedData.sampleNo }}
        </el-tag>
        <el-button v-else size="small" type="primary" @click="dialogShowSpecimen = true">选择样品编号 </el-button>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
  <DialogSpecimen :dialog-show="dialogShowSpecimen" @closeDialog="handleCloseSpecimen" />
</template>
<script>
import { reactive, ref, toRefs, watch, getCurrentInstance } from 'vue';
import { useStore } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
import { deliveryStorage } from '@/api/standingBook';
import { formatDate } from '@/utils/formatTime';
import DialogSpecimen from './DialogSpecimen';
export default {
  name: 'DialogDelivery',
  components: { DialogSpecimen },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    unitJson: {
      type: Object,
      default: function () {
        return {};
      }
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const state = reactive({
      type: '', // 弹出窗类型
      dialogLoading: false, // 弹出窗loading
      selectedData: {}, // 选择的样品
      formData: {}, // 表单数据
      dialogShow: false,
      unitAllJson: {},
      dialogShowSpecimen: false,
      ruleForm: ref(),
      listLoading: false,
      nameList: store.common.nameList,
      currentAccountId: getLoginInfo().accountId
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      state.unitAllJson = props.unitJson;
      if (state.dialogShow) {
        state.formData = {
          recordDate: formatDate(new Date()),
          userId: state.currentAccountId,
          productId: props.detailData.id,
          unit: props.detailData.unit,
          no: props.detailData.no,
          name: props.detailData.name
        };
      }
    });
    const onSubmit = () => {
      proxy.$refs['ruleForm'].validate(valid => {
        if (valid) {
          state.dialogLoading = true;
          deliveryStorage({
            ...state.formData,
            recordDate: formatDate(state.formData.recordDate),
            ...state.selectedData
          }).then(res => {
            state.dialogLoading = false;
            if (res) {
              proxy.$message.success(res.data.message);
              context.emit('closeDialog', { isRefresh: true });
            }
          });
        } else {
          return false;
        }
      });
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog', { isRefresh: false });
    };
    const handleCloseSpecimen = data => {
      state.dialogShowSpecimen = false;
      if (data) {
        state.selectedData = data.selectedData;
      }
    };
    const handleDeleteTag = () => {
      state.selectedData = {};
    };
    return { ...toRefs(state), onSubmit, handleClose, handleCloseSpecimen, handleDeleteTag };
  }
};
</script>
<style lang="scss" scoped>
.unitClass {
  text-align: center;
  margin-left: 5px;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  background-color: rgb(245, 247, 250);
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 100%;
}
:deep(.el-select) {
  width: 100%;
}
:deep(.el-input-number--small) {
  width: 100%;
}
</style>
