<template>
  <!-- 点检标准列表页 -->
  <ListLayout :has-button-group="getPermissionBtn('addInspectionStandard') || getPermissionBtn('fieldMaintenance')">
    <template #search-bar>
      <div class="searchInput">
        <el-input
          v-model="param"
          v-trim
          v-focus
          size="large"
          class="ipt-360"
          clearable
          prefix-icon="el-icon-search"
          placeholder="请输入搜索内容"
          @clear="getTableList"
          @keyup.enter="getTableList"
        />
        <el-button size="large" type="primary" :loading="loading" @click="getTableList()">查询</el-button>
        <el-button size="large" :loading="loading" @click="reset">重置</el-button>
      </div>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('fieldMaintenance')"
        :loading="loading"
        size="large"
        icon="el-icon-setting"
        type="primary"
        @click="dialogSetting = true"
        @keyup.prevent
        @keydown.enter.prevent
        >设置点检字段</el-button
      >
      <el-button
        v-if="getPermissionBtn('addInspectionStandard')"
        :loading="loading"
        size="large"
        icon="el-icon-plus"
        type="primary"
        @click="addStandard()"
        @keyup.prevent
        @keydown.enter.prevent
        >新增点检标准</el-button
      >
    </template>
    <template #radio-content>
      <el-row v-loading="loading">
        <el-col :span="8" :offset="16" class="text-right">
          <TableColumnView binding-menu="SpotInspectionStandard" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="loading"
      :data="tableList"
      size="medium"
      height="auto"
      fit
      border
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
    >
      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="item.fieldType === fieldTypesEnum.Person">
              <UserTag :name="getNameByid(row[item.fieldKey]) || '--'" />
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Date">
              <span>{{ formatDateTime(row[item.fieldKey]) }}</span>
            </template>
            <span v-else>{{ row[item.fieldKey] || '--' }}</span>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" :width="colWidth.operationMultiples" fixed="right" class-name="fixed-right">
        <template #default="{ row }">
          <span class="blue-color" @click="handleCheckRow(row)">查看</span>
          <span v-if="getPermissionBtn('editInspectionStandard')" class="blue-color" @click="handleEdit(row)"
            >编辑</span
          >
          <span v-if="getPermissionBtn('copyInspectionStandard')" class="blue-color" @click="handleCopyRow(row)"
            >复制</span
          >
          <span v-if="getPermissionBtn('deleteInspectionStandard')" class="blue-color" @click="handleDeleteRow(row)"
            >删除</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getTableList"
    />
    <template #other>
      <DialogTableSetting
        :dialog-show="dialogSetting"
        :dictionary-assemble="dictionaryList"
        @close-dialog="closeDialog"
      />
      <DrawerSpotStandard
        :drawer-show="drawerSpotStandard"
        :drawer-type="drawerType"
        :detail-info="detailInfo"
        @close-dialog="closeDialogStandard"
      />
      <DrawerSpotStandardCheck
        :drawer-show="drawerCheckSpotStandard"
        :detail-info="detailInfo"
        @close-dialog="closeDrawerCheckStandard"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import Pagination from '@/components/Pagination';
import ListLayout from '@/components/ListLayout';
import { formatDateTime } from '@/utils/formatTime';
import UserTag from '@/components/UserTag';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { pointInspectionStandardList, pointInspectionStandardDelete } from '@/api/spotInspectionStandard';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import TableColumnView from '@/components/TableColumnView';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';
import DialogTableSetting from './dialog-table-setting';
import DrawerSpotStandard from './drawer-spot-standard';
import DrawerSpotStandardCheck from './drawer-spot-standard-check';
import { getDictionaryList } from '@/api/dictionary';

export default {
  name: 'SpotInspectionStandard',
  components: {
    Pagination,
    ListLayout,
    UserTag,
    TableColumnView,
    DialogTableSetting,
    DrawerSpotStandard,
    DrawerSpotStandardCheck
  },
  setup() {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableRef: ref(),
      dialogSetting: false,
      drawerCheckSpotStandard: false,
      drawerSpotStandard: false,
      drawerType: '',
      detailInfo: {},
      param: '', // 列表关键字
      dictionaryList: {},
      ruleForm: ref(),
      loading: false,
      types: [],
      listQuery: {
        limit: 20,
        page: 1
      },
      tableColumns: [],
      tableList: [],
      total: 0
    });
    const getDictionaryAll = () => {
      getDictionaryList({ limit: '-1', page: '1', dictionaryType: '1' }).then(res => {
        const list = res.data.data.list;
        list.forEach(item => {
          state.dictionaryList[item.code] = item;
        });
      });
    };
    if (getPermissionBtn('fieldMaintenance')) {
      // 点检标准字段设置
      getDictionaryAll();
    }
    const tableKey = ref(0);
    const getTableList = query => {
      const params = { param: state.param };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.loading = true;
      pointInspectionStandardList(params).then(res => {
        state.loading = false;
        if (res) {
          state.total = res.data.data.totalCount;
          state.tableList = res.data.data.list;
        }
      });
    };
    getTableList();
    const reset = () => {
      state.param = '';
      getTableList();
    };
    // 查看点检标准
    const handleCheckRow = row => {
      state.drawerCheckSpotStandard = true;
      state.detailInfo = row;
    };
    // 删除
    const handleDeleteRow = row => {
      proxy
        .$confirm('是否确认删除，删除后无法恢复', '删除确认', {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'warning'
        })
        .then(() => {
          state.loading = true;
          pointInspectionStandardDelete([row.id]).then(res => {
            state.loading = false;
            if (res) {
              proxy.$message.success('删除成功');
              getTableList();
            }
          });
        })
        .catch(() => {});
    };
    // 编辑
    const handleEdit = row => {
      state.drawerSpotStandard = true;
      state.drawerType = 'edit';
      state.detailInfo = row;
    };
    // 复制
    const handleCopyRow = row => {
      state.drawerSpotStandard = true;
      state.drawerType = 'copy';
      state.detailInfo = row;
    };
    // 新增点检标准
    const addStandard = () => {
      state.drawerSpotStandard = true;
      state.drawerType = 'add';
      state.detailInfo = {};
    };
    // 关闭弹出窗
    const closeDialog = val => {
      state.dialogSetting = false;
    };
    const closeDrawerCheckStandard = () => {
      state.drawerCheckSpotStandard = false;
    };
    const closeDialogStandard = val => {
      state.drawerSpotStandard = false;
      if (val) {
        getTableList();
      }
    };

    const onUpdateColumns = columns => {
      tableKey.value = tableKey.value + 1;
      state.tableColumns = columns;
    };
    return {
      ...toRefs(state),
      closeDrawerCheckStandard,
      addStandard,
      handleCopyRow,
      getPermissionBtn,
      drageHeader,
      getNameByid,
      getNamesByid,
      handleCheckRow,
      handleDeleteRow,
      handleEdit,
      closeDialog,
      closeDialogStandard,
      reset,
      formatDateTime,
      getTableList,
      tableKey,
      colWidth,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  }
};
</script>
<style lang="scss" scoped>
.searchInput {
  display: flex;
  .el-input {
    width: 360px;
    margin-right: 10px;
  }
}
</style>
