// 节流函数，避免频繁调用
function throttle(func, limit) {
  let inThrottle;
  return function () {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// 拖拽表头改变宽事件 - 添加节流处理避免频繁触发
const drageHeaderThrottled = throttle(function (newWidth, oldWidth, column) {
  if (column && column.width <= column.minWidth) {
    column.width = column.minWidth;
  }
}, 16); // 约60fps的节流

export function drageHeader(newWidth, oldWidth, column) {
  // 添加错误边界处理
  try {
    drageHeaderThrottled(newWidth, oldWidth, column);
  } catch (error) {
    console.warn('drageHeader error:', error);
    // 静默处理错误，避免页面崩溃
  }
}
