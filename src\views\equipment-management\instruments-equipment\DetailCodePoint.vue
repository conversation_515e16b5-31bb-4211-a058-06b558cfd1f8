<template>
  <div class="detail-code-point" style="text-align: left">
    <div>
      <el-button
        v-if="detailData.supportDataAcquisition && getPermissionBtn('addCodePointBtn')"
        class="add-btn"
        size="small"
        icon="el-icon-plus"
        type="primary"
        @click="handleAdd"
        @keyup.prevent
        @keydown.enter.prevent
        >新增设备码点</el-button
      >
      <el-button
        v-if="
          tableList.map(item => {
            item.id;
          }).length > 0 &&
          !isEdit &&
          !isAdd &&
          getPermissionBtn('editCodePointBtn')
        "
        class="add-btn"
        size="small"
        icon="el-icon-edit"
        @click="handleEdit"
        >编辑设备码点</el-button
      >
      <el-button v-if="isAdd || isEdit" class="add-btn" type="primary" size="small" @click="onSubmit()">保存</el-button>
      <el-button v-if="isAdd || isEdit" size="small" @click="handleCalce">取消</el-button>
    </div>
    <el-table
      :data="tableList"
      fit
      border
      highlight-current-row
      size="medium"
      class="detail-table dark-table format-height-table format-height-table2"
      @header-dragend="drageHeader"
    >
      <el-table-column type="index" label="序号" width="70" align="center" />
      <el-table-column prop="pointNumber" label="码点编号">
        <template #default="{ row, $index }">
          <el-form
            v-if="(isAdd && !row.id) || isEdit"
            :ref="'pointNumber' + $index"
            :model="row"
            style="margin: 0px"
            :rules="codePointRules"
          >
            <el-form-item prop="pointNumber" style="margin: 0px">
              <el-input
                v-model="row.pointNumber"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="请输入码点编号"
              />
            </el-form-item>
          </el-form>
          <div v-else class="nowrap">{{ row.pointNumber || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="pointName" label="码点名称">
        <template #default="{ row, $index }">
          <el-form
            v-if="(isAdd && !row.id) || isEdit"
            :ref="'pointName' + $index"
            :model="row"
            style="margin: 0px"
            :rules="codePointRules"
          >
            <el-form-item prop="pointName" style="margin: 0px">
              <el-input
                v-model="row.pointName"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="请输入码点名称"
              />
            </el-form-item>
          </el-form>
          <div v-else class="nowrap">{{ row.pointName || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="dateType" label="数据类型">
        <template #default="{ row }">
          <el-select
            v-if="(isAdd && !row.id) || isEdit"
            v-model="row.dataType"
            placeholder="请选择数据类型"
            size="small"
          >
            <el-option v-for="(val, key, index) in dateType" :key="index" :label="val" :value="Number(key)" />
          </el-select>
          <div v-else class="nowrap">{{ dateType[row.dataType] || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="pointUnit" label="单位">
        <template #default="{ row, $index }">
          <el-form
            v-if="(isAdd && !row.id) || isEdit"
            :ref="'pointUnit' + $index"
            :model="row"
            style="margin: 0px"
            :rules="codePointRules"
          >
            <el-form-item prop="pointUnit" style="margin: 0px">
              <el-select v-model="row.pointUnit" placeholder="请输入码点单位" size="small">
                <el-option-group v-for="item in unitArray" :key="item.label" :label="item.label">
                  <el-option
                    v-for="val in item.group"
                    :key="val.id"
                    :label="val.name"
                    :value="val.code"
                    :disabled="val.status !== 1"
                  >
                    <span style="float: left">{{ val.name }}</span>
                    <span v-if="val.status !== 1" class="fr" style="color: red">已停用</span>
                  </el-option>
                </el-option-group>
              </el-select>
            </el-form-item>
          </el-form>
          <div v-else class="nowrap">{{ unitJson[row.pointUnit] || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="lastUpdateByUserId" label="更新人">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.lastUpdateByUserId) || '--'" />
        </template>
      </el-table-column>
      <el-table-column prop="lastUpdateDateTime" label="更新日期">
        <template #default="{ row }">
          <div v-if="row.lastUpdateDateTime" class="nowrap">{{ formatDate(row.lastUpdateDateTime) }}</div>
          <div v-else>{{ '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" show-overflow-tooltip>
        <template #default="{ row }">
          <el-input
            v-if="(isAdd && !row.id) || isEdit"
            v-model="row.description"
            type="textarea"
            maxlength="300"
            placeholder="请输入描述"
          />
          <div v-else class="nowrap">{{ row.description || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="
          detailData.supportDataAcquisition &&
          (getPermissionBtn('editCodePointBtn') || getPermissionBtn('delCodePointBtn'))
        "
        fixed="right"
        class-name="fixed-right"
        prop="status"
        label="操作"
        width="120"
      >
        <template #default="{ row, $index }">
          <span v-if="getPermissionBtn('delCodePointBtn')" class="blue-color" @click="handleDelete(row, $index)"
            >删除</span
          >
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      :close-on-click-modal="false"
      width="480px"
      custom-class="submit_dialog"
    >
      <el-form
        v-if="dialogVisible"
        ref="ruleForm"
        v-loading="dialogLoading"
        :model="formData"
        label-position="right"
        label-width="110px"
        size="small"
      >
        <el-form-item
          label="码点编号："
          prop="pointNumber"
          :rules="{ required: true, message: '请输入码点编号', trigger: 'change' }"
        >
          <el-input
            v-model="formData.pointNumber"
            v-trim
            v-focus
            size="small"
            type="text"
            maxlength="100"
            placeholder="请输入码点编号"
          />
        </el-form-item>
        <el-form-item
          label="码点名称："
          prop="pointName"
          :rules="{ required: true, message: '请输入码点名称', trigger: 'change' }"
        >
          <el-input
            v-model="formData.pointName"
            v-trim
            size="small"
            type="text"
            maxlength="100"
            placeholder="请输入码点名称"
          />
        </el-form-item>
        <el-form-item
          label="码点单位："
          prop="pointUnit"
          :rules="{ required: true, message: '请输入码点单位', trigger: 'change' }"
        >
          <el-select v-model="formData.pointUnit" placeholder="请输入码点单位" size="small">
            <el-option-group v-for="item in unitArray" :key="item.label" :label="item.label">
              <el-option
                v-for="val in item.group"
                :key="val.id"
                :label="val.name"
                :value="val.code"
                :disabled="val.status !== 1"
              >
                <span style="float: left">{{ val.name }}</span>
                <span v-if="val.status !== 1" class="fr" style="color: red">已停用</span>
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="数据类型：" prop="dataType">
          <el-select v-model="formData.dataType" placeholder="请选择数据类型" size="small">
            <el-option v-for="(val, key, index) in dateType" :key="index" :label="val" :value="Number(key)" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述：" prop="description">
          <el-input v-model="formData.description" type="textarea" maxlength="300" :rows="2" placeholder="请输入描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button size="small" :loading="dialogLoading" @click="dialogVisible = false">取 消</el-button>
          <el-button size="small" :loading="dialogLoading" type="primary" @click="onSubmit()">确 认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { reactive, toRefs, watch, ref, getCurrentInstance } from 'vue';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import UserTag from '@/components/UserTag';
import { getDictionary } from '@/api/user';
import { deleteCodePoint, getDetail, addCodePoint } from '@/api/equipment';
// import { method } from 'lodash'
export default {
  name: 'DetailCodePoint',
  components: { UserTag },
  props: {
    list: {
      type: Array,
      default: function () {
        return [];
      }
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['detail'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableList: [],
      oldTableList: [],
      isAdd: false,
      isEdit: false,
      codePointRules: {
        pointNumber: { required: true, tigger: 'blur' },
        pointName: { required: true, tigger: 'blur' },
        pointUnit: { required: true, tigger: 'blur' }
      },
      detailData: {}, // 仪器设备详情
      unitJson: {}, // 单位
      unitArray: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ], // 单位
      dialogVisible: false,
      dialogTitle: '', // 弹出框标题
      dialogLoading: false, // 弹出框的loading
      formData: {},
      ruleForm: ref(),
      dateType: {
        1: '数值型',
        2: '字符串',
        3: '枚举型'
      },
      statusType: {
        0: '作废',
        1: '生效'
      }
    });
    watch(props, newValue => {
      if (props.list.length > 0) {
        state.oldTableList = JSON.parse(JSON.stringify(props.list));
      } else {
        state.oldTableList = [];
      }
      state.detailData = props.detailData;
      initUnit();
      getTableList();
    });
    const getTableList = () => {
      getDetail(state.detailData.id).then(res => {
        if (res) {
          state.tableList = res.data.data.devicecodepointList;
        }
      });
    };
    const initUnit = () => {
      getDictionary(13).then(res => {
        if (res) {
          state.unitArray[0].group = [];
          state.unitArray[1].group = [];
          res.data.data.dictionaryoption.forEach(item => {
            if (item.status === 1) {
              state.unitArray[0].group.push(item);
            } else {
              state.unitArray[1].group.push(item);
            }
            state.unitJson[item.code] = item.name;
          });
        }
      });
    };
    const handleCalce = () => {
      state.isAdd = false;
      state.isEdit = false;
      if (state.oldTableList.length > 0) {
        state.tableList = JSON.parse(JSON.stringify(state.oldTableList));
      } else {
        state.tableList = [];
      }
    };
    // 新增
    const handleAdd = () => {
      state.isAdd = true;
      state.tableList.push({
        pointNumber: '',
        deviceId: state.detailData.id
      });
    };
    // 编辑
    const handleEdit = row => {
      state.isEdit = true;
    };
    // 删除
    const handleDelete = (row, index) => {
      proxy
        .$confirm(`是否删除${row.pointName}？`, {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          showClose: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        })
        .then(() => {
          if (row.id) {
            deleteCodePoint(row.id, state.detailData.id).then(res => {
              if (res) {
                proxy.$message.success('删除成功');
                state.tableList.splice(index, 1);
              }
            });
          } else {
            state.tableList.splice(index, 1);
          }
        });
    };
    const onSubmit = () => {
      const validateArry = [];
      for (const i in proxy.$refs) {
        if (proxy.$refs[i] !== null) {
          proxy.$refs[i].validate(valid => {
            if (!valid) {
              if (i.indexOf('pointNumber') >= 0 && validateArry.indexOf('pointNumber') === -1) {
                validateArry.push('pointNumber');
              } else if (i.indexOf('pointName') >= 0 && validateArry.indexOf('pointName') === -1) {
                validateArry.push('pointName');
              } else if (i.indexOf('pointUnit') >= 0 && validateArry.indexOf('pointUnit') === -1) {
                validateArry.push('pointUnit');
              }
            }
          });
        }
      }
      if (validateArry.length === 0) {
        state.dialogLoading = true;
        addCodePoint({ devicecodepointRequestList: state.tableList }).then(res => {
          state.dialogLoading = false;
          if (res) {
            proxy.$message.success(res.data.message);
            state.isAdd = false;
            state.isEdit = false;
            getTableList();
          }
        });
      } else {
        var messageError = '';
        if (
          validateArry.some(item => {
            return item === 'pointNumber';
          })
        ) {
          messageError = '<div style="line-height: 20px;">请输入码点编号</div>';
        }
        if (
          validateArry.some(item => {
            return item === 'pointName';
          })
        ) {
          messageError += '<div style="line-height: 20px;">请输入码点名称</div>';
        }
        if (
          validateArry.some(item => {
            return item === 'pointUnit';
          })
        ) {
          messageError += '<div style="line-height: 20px;">请输入码点单位</div>';
        }
        proxy.$message.error({
          dangerouslyUseHTMLString: true,
          message: messageError
        });
      }
    };
    // 不能输入汉字的正则校验
    const validateZh = (rule, value, callback) => {
      const numberReg = /[\u4E00-\u9FA5]/g;
      if (value === '' || value === undefined || value === null) {
        callback(new Error('请输入码点编号'));
      } else {
        if (numberReg.test(value)) {
          callback(new Error('不能输入汉字'));
        } else {
          callback();
        }
      }
    };
    return {
      ...toRefs(state),
      handleAdd,
      handleCalce,
      handleDelete,
      handleEdit,
      getPermissionBtn,
      validateZh,
      drageHeader,
      onSubmit,
      formatDate,
      getNameByid
    };
  }
};
</script>

<style lang="scss" scoped>
.textRight {
  text-align: right;
}
.el-select {
  width: 100%;
}
.add-btn {
  margin-bottom: 20px;
}
</style>
<style lang="scss">
.detail-code-point {
  .format-height-table2.el-table {
    .el-table__body-wrapper {
      max-height: calc(100vh - 550px) !important;
    }
    .el-table__fixed-body-wrapper {
      max-height: calc(100vh - 550px) !important;
    }
  }
}
</style>
