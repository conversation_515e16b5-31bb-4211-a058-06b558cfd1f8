<template>
  <el-dialog
    v-model="dialogShow"
    title="添加看板组件"
    :close-on-click-modal="false"
    width="590px"
    custom-class="DialogSet"
    :before-close="closedialog"
  >
    <ul class="instructionsUse">
      <li>1、可以通过“添加组件”操作，来自定义看板中心的显示内容；</li>
      <li>2、未添加的组件数据不会消失，仅在看板中进行了隐藏，添加后可看到相应数据；</li>
    </ul>
    <el-row v-for="item in componentDetail" :key="item.id" class="listLine">
      <el-col :span="18" class="name">{{ item.name }}</el-col>
      <el-col :span="6" class="textRight">
        <span v-if="layoutDetailJson[item.id]" class="alreadyModules">已添加</span>
        <span v-else class="addModules cursorPointer" @click="addComponents(item)">添加组件</span>
      </el-col>
    </el-row>
    <div class="dialog-footer textRight">
      <el-button size="small" @click="closedialog">关 闭</el-button>
      <el-button size="small" type="primary" @keyup.prevent @keydown.enter.prevent @click="handleResetView"
        >重置为默认视图</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, getCurrentInstance } from 'vue';
import { dashboardconfigReset } from '@/api/dataBoard';
export default {
  name: 'DialogSet',
  components: {},
  props: {
    dialogVisiable: {
      type: Boolean,
      default: false
    },
    dataSourceList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    layoutDetail: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // const store = useStore()
    const state = reactive({
      dialogSubmit: false,
      dialogShow: false,
      componentDetail: [], // 全部模块
      layoutDetailJson: {}, // 选中的模块
      listLoading: false
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisiable;
      if (state.dialogShow) {
        state.componentDetail = props.dataSourceList;
        state.layoutDetailJson = {};
        props.layoutDetail.forEach(item => {
          state.layoutDetailJson[item.id] = true;
        });
      }
    });
    // 添加组件
    const addComponents = item => {
      state.layoutDetailJson[item.id] = true;
      context.emit('closeDialog', { isRefresh: false, item: item });
    };
    const closedialog = () => {
      context.emit('closeDialog', { isRefresh: false });
    };
    // 重置视图
    const handleResetView = () => {
      proxy
        .$confirm('是否确认重置为默认视图', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          showClose: false,
          customClass: 'dataBoardMessageBox',
          closeOnClickModal: false,
          closeOnPressEscape: false
        })
        .then(() => {
          dashboardconfigReset().then(res => {
            if (res) {
              context.emit('closeDialog', { isRefresh: true });
            }
          });
        })
        .catch(() => {});
    };
    return { ...toRefs(state), handleResetView, props, addComponents, closedialog };
  },
  computed: {},
  created() {}
};
</script>
<style lang="scss" scoped>
.dataBoardMessageBox {
  background: linear-gradient(90deg, #245094 0%, #3f7ab6 100%) !important;
  border: 0;
  .el-message-box__message p {
    color: #7ad0ff;
  }
}
.textRight {
  text-align: right;
}
.listLine {
  height: 30px;
  line-height: 30px;
  margin: 10px 0 0 0;
  border-radius: 5px;
  padding: 0 10px;
  background: linear-gradient(90deg, #66b3e7 0%, #3f7ab6 100%);
  box-shadow: 0 0.285714rem 0.285714rem rgba(0, 0, 0, 0.25);
}
.name {
  color: #fff;
  font-size: 13px;
}
.dialog-footer {
  margin-top: 20px;
}
.instructionsUse {
  padding: 0;
  color: #7ad0ff;
  font-size: 12px;
  text-align: left;
  li {
    list-style: none;
    height: 20px;
    line-height: 20px;
  }
}
.addModules {
  color: #7ad0ff;
  font-weight: 700;
}
.alreadyModules {
  color: #245094;
  font-weight: 700;
}
.cursorPointer {
  cursor: pointer;
}
</style>
<style lang="scss">
.DialogSet {
  border-radius: 10px;
  .el-dialog__header {
    background: linear-gradient(90deg, #245094 0%, #3f7ab6 100%);
    border-radius: 10px 10px 0 0;
  }
  .el-dialog__title {
    color: #7ad0ff;
    font-weight: 700;
  }
  .el-dialog__body {
    background: linear-gradient(180deg, #2d59a5 0%, #4280b9 100%);
    border-radius: 0 0 10px 10px;
  }
  .el-button:hover {
    color: #000;
    border-color: #7ad0ff;
    background-color: #d4edfe;
  }
}
</style>
