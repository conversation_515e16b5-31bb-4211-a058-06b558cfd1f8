<template>
  <!-- 人员在岗时间列表 -->
  <ListLayout ref="dutyTimeList" v-loading="tableLoading" :has-left-panel="false">
    <template #button-group>
      <el-button type="primary" size="large" @click="toImg()" @keyup.prevent @keydown.enter.prevent>截屏快照</el-button>
      <el-button
        v-if="getPermissionBtn('leaveManagement')"
        type="primary"
        size="large"
        icon="el-icon-house"
        @click="handleLeaveManage()"
        @keyup.prevent
        @keydown.enter.prevent
        >请假管理</el-button
      >
      <el-button
        v-if="getPermissionBtn('attendanceTime')"
        type="primary"
        size="large"
        icon="el-icon-edit"
        @click="dialogVisible = true"
        @keyup.prevent
        @keydown.enter.prevent
        >考勤时间</el-button
      >
      <el-button
        v-if="getPermissionBtn('personnelManagement')"
        type="primary"
        size="large"
        icon="el-icon-user"
        @click="getalreadySelect()"
        @keyup.prevent
        @keydown.enter.prevent
        >人员管理</el-button
      >
    </template>
    <template #radio-content>
      <el-radio-group v-model="type" size="small" @change="initTableHeader">
        <el-radio-button :label="0">日</el-radio-button>
        <el-radio-button :label="1">周</el-radio-button>
      </el-radio-group>
      <div v-if="isWeek" class="switchDate">
        <span v-if="isShowChangeToday" class="blue-color backToday" @click="changeWeek('today')">切回今天</span>
        <i class="cursorPoiner el-icon-arrow-left" @click="changeWeek('forward')" />
        {{ formatDateDay(weeksArray[0]) }}
        -
        {{ formatDateDay(weeksArray[weeksArray.length - 1]) }}
        <i class="cursorPoiner el-icon-arrow-right" @click="changeWeek('backward')" />
      </div>
      <div v-else class="switchDate">
        <el-date-picker v-model="date" type="date" size="small" placeholder="请选择日期" @change="getTableList" />
      </div>
    </template>
    <!-- v-loading="tableLoading" -->
    <el-table
      v-if="!tableLoading && tableData.length > 0"
      ref="tableRef"
      :data="tableData"
      fit
      border
      height="auto"
      :size="tableSize"
      highlight-current-row
      class="dark-table format-height-table base-table dutyTimeTable"
      @header-dragend="drageHeader"
    >
      <el-table-column label="姓名" prop="employeeId" fixed="left" :min-width="isWeek ? 70 : 90">
        <template #default="{ row }">
          <UserTag v-if="row.employeeId" :name="getNameByid(row.employeeId) || '--'" />
        </template>
      </el-table-column>
      <el-table-column
        v-for="(val, index) in tableDataHeader"
        :key="index"
        :label="isWeek ? formatDateDay(weeksArray[index], 'date') + ' ' + val : index % 2 !== 0 ? '' : val"
        :prop="val"
        :min-width="70"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div v-if="isWeek">
            <!-- 0当天休息\1正常考勤\2当天有请假时间 -->
            <div v-if="row.dutyWeakFormat[formatDateDay(weeksArray[index], 'date')] === 1" class="isOnDuty" />
            <div v-if="row.dutyWeakFormat[formatDateDay(weeksArray[index], 'date')] === 2" class="isOnDuty2" />
            <!-- <div v-if="row.dutyWeakTimes[formatDateDay(weeksArray[index], 'date')]" class="isOnDuty" /> -->
          </div>
          <div v-if="!isWeek">
            <div v-if="row.dutyDayTimes[val]" class="isOnDuty" />
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="tableData.length === 0">
      <el-empty description="暂无数据" />
    </div>
    <DialogTimeManage :dialog-visible="dialogVisible" @closeDialog="handleCloseTime" />
    <!-- 请假管理弹出框 -->
    <DialogLeave :dialog-visible="dialogLeave" @closeDialog="handleCloseLeave" />
    <!-- 人员管理弹出框 -->
    <DialogMulDepartPerson
      :dialog-visible="dialogPerson"
      :is-show-record="true"
      :already-select="alreadySelect"
      @selectData="handleClosePerson"
    />
    <!-- 调整截图 -->
    <DialogPageSnapshot :dialog-visiable="dialogSnapshot" :img-url="imgBaseUrl" @closeDialog="handleCloseSnapshot" />
  </ListLayout>
</template>

<script>
import { reactive, ref, getCurrentInstance, toRefs, nextTick } from 'vue';
import UserTag from '@/components/UserTag';
import { getDutyTime, employeeOnDuty, employeeOnDutyUpdate } from '@/api/dutyTime';
import DialogTimeManage from './DialogTimeManage';
import DialogLeave from './DialogLeave';
import DialogMulDepartPerson from '@/components/BusinessComponents/DialogMulDepartPerson';
import DialogPageSnapshot from '@/components/BusinessComponents/DialogPageSnapshot';
import { colWidth } from '@/data/tableStyle';
import ListLayout from '@/components/ListLayout';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { formatDateDay } from '@/utils/formatTime';
import pdf from '@/utils/preview-or-download-pdf';

export default {
  name: 'DutyTimeList',
  components: { ListLayout, UserTag, DialogTimeManage, DialogLeave, DialogPageSnapshot, DialogMulDepartPerson },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // const store = useStore()
    // const route = useRoute()
    const state = reactive({
      drawerType: '',
      dialogVisible: false,
      dialogPerson: false,
      dialogSnapshot: false,
      tableRef: ref(),
      dutyTimeList: ref(),
      selectData: [],
      tableDataHeader: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'], // 表格头
      type: 0,
      date: new Date(),
      alreadySelect: [], // 已经选中的成员
      dialogLeave: false, // 请假管理弹出框
      isWeek: true, // 是否是周视图
      imgBaseUrl: '',
      isShowChangeToday: false, // 是否显示切回今天
      isAdd: false,
      weeksArray: [],
      dayTimes: [], // 日式图时显示的时间
      detailData: {}, // 传递给详情页的内容
      tableData: [],
      condition: '', // 模糊查询关键字
      total: 0,
      tableLoading: false, // 表格加载的loading
      detailDrawer: false,
      dialogFrom: {}, // 操作树节点的弹窗表格
      listQuery: {
        page: 1,
        limit: 20
      },
      tableSize: 'medium'
    });
    // 屏幕截屏快照
    const toImg = async () => {
      state.imgBaseUrl = await pdf.PageSnapshot(state.dutyTimeList.$el);
      nextTick(() => {
        state.dialogSnapshot = true;
      });
    };
    // 获取时间段
    const getDayTime = () => {
      const beginTime = '9:00';
      const endTime = '18:00';
      state.dayTimes = [];
      for (var i = Number(beginTime.split(':')[0]); i <= Number(endTime.split(':')[0]); i++) {
        if (beginTime.split(':')[1] === '00') {
          state.dayTimes.push(i + ':00');
          state.dayTimes.push('');
        } else {
          state.dayTimes.push(i + ':30');
          state.dayTimes.push('');
        }
      }
      state.dayTimes.pop();
      state.tableDataHeader = state.dayTimes;
    };
    // 获取一周的日期
    const getWeekDate = date => {
      const today = date;
      const currenDay = today.getDay();
      state.weeksArray = [];
      for (var i = 0; i < 7; i++) {
        var das = formatDate(today.getTime() + 24 * 60 * 60 * 1000 * (i - ((currenDay + 6) % 7)));
        state.weeksArray.push(das);
      }
      state.isShowChangeToday = !state.weeksArray.some(item => {
        return item === formatDate(new Date());
      });
      return state.weeksArray;
    };
    const getTableList = () => {
      const params = {
        type: state.type,
        date: state.type === 1 ? state.weeksArray[0] : formatDate(state.date)
      };
      state.tableLoading = true;
      getDutyTime(params).then(res => {
        state.tableLoading = false;
        if (res) {
          const data = res.data.data;
          if (state.type === 0) {
            state.tableData = data;
            if (state.tableData.length > 0) {
              state.tableDataHeader = Object.keys(state.tableData[0].dutyDayTimes);
            } else {
              state.tableDataHeader = [];
            }
          } else {
            formatTableData(data);
          }
        }
      });
    };
    const initTableHeader = value => {
      if (state.type === 1) {
        state.isWeek = true;
        state.tableDataHeader = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        getWeekDate(new Date());
      } else {
        state.isWeek = false;
      }
      getTableList();
    };
    initTableHeader();
    const getalreadySelect = () => {
      employeeOnDuty().then(res => {
        if (res) {
          state.alreadySelect = res.data.data;
          state.dialogPerson = true;
        }
      });
    };
    // 请假管理
    const handleLeaveManage = row => {
      state.dialogLeave = true;
    };
    const changeWeek = type => {
      let date = '';
      if (type === 'backward') {
        const timeStamp = new Date(state.weeksArray[state.weeksArray.length - 1]).getTime();
        date = timeStamp + 24 * 60 * 60 * 1000;
        getWeekDate(new Date(date));
      } else if (type === 'today') {
        getWeekDate(new Date());
      } else {
        const timeStamp2 = new Date(state.weeksArray[0]).getTime();
        date = timeStamp2 - 24 * 60 * 60 * 1000;
        getWeekDate(new Date(date));
      }
      getTableList();
    };
    // 格式化周日期的表格 0当天休息\1正常考勤\2当天有请假时间
    const formatTableData = data => {
      state.tableLoading = true;
      data.forEach(item => {
        item.dutyWeakFormat = {};
        for (const key in item.dutyWeakTimes) {
          const valueArray = Object.values(item.dutyWeakTimes[key]);
          var values = [...new Set(valueArray.splice(0, valueArray.length - 1))];
          if (values.length === 0) {
            // 放假
            item.dutyWeakFormat[key] = 0;
          } else if (values.length === 2) {
            // 当天有部分的请假
            item.dutyWeakFormat[key] = 2;
          } else {
            if (values[0]) {
              // 当天正常考勤
              item.dutyWeakFormat[key] = 1;
            } else {
              // 当天全请假
              item.dutyWeakFormat[key] = 0;
            }
          }
        }
      });
      state.tableLoading = false;
      state.tableData = data;
    };
    const handleCloseTime = val => {
      state.dialogVisible = false;
      if (val) {
        getTableList();
      }
    };
    const handleCloseLeave = isRefresh => {
      state.dialogLeave = false;
      if (isRefresh) {
        getTableList();
      }
    };
    const handleClosePerson = val => {
      state.dialogPerson = false;
      if (val) {
        employeeOnDutyUpdate(val).then(res => {
          if (res) {
            proxy.$message.success('调整人员成功!');
            getTableList();
          }
        });
      }
    };

    const handleCloseSnapshot = () => {
      state.dialogSnapshot = false;
    };
    return {
      ...toRefs(state),
      getDayTime,
      getalreadySelect,
      formatTableData,
      handleCloseTime,
      handleCloseLeave,
      handleClosePerson,
      handleCloseSnapshot,
      changeWeek,
      initTableHeader,
      toImg,
      getWeekDate,
      formatDateDay,
      getTableList,
      handleLeaveManage,
      getPermissionBtn,
      formatDate,
      getNameByid,
      drageHeader,
      colWidth
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
.switchDate {
  vertical-align: middle;
  line-height: 32px;
  font-size: 16px;
  float: right;
}
.backToday {
  font-size: 13px;
  margin-right: 10px;
  display: inline-block;
  line-height: 1;
  vertical-align: middle;
}
.cursorPoiner {
  font-weight: bolder;
  cursor: pointer;
  font-size: 16px;
  &:hover {
    color: $tes-primary;
  }
}
.isOnDuty {
  background-color: $tes-primary;
  padding: 10px;
}
.isOnDuty2 {
  background-color: $tes-yellow;
  padding: 10px;
}
.example {
  height: 500px;
  width: 500px;
}

:deep(.format-height-table.el-table .el-table__body-wrapper) {
  max-height: calc(100vh - 280px);
}
:deep(.el-table.dutyTimeTable .cell.el-tooltip) {
  padding: 0;
}
:deep(.el-table.dutyTimeTable th > .cell) {
  padding-left: 0;
}
:deep(.dutyTimeTable th:first-child .cell) {
  padding-left: 10px;
}
</style>
