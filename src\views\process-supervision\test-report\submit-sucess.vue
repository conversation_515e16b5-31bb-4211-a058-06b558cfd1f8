<template>
  <div v-loading="downloading" class="submit-success">
    <el-result title="提交成功" sub-title="检测报告已生成 ">
      <template #icon>
        <img src="@/assets/img/img-report-result.png" alt="" />
      </template>
      <template #extra>
        <div class="btn-group">
          <el-button size="medium" @click="goDetail" @keyup.prevent @keydown.enter.prevent>查 看</el-button>
          <el-button size="medium" @click="preview" @keyup.prevent @keydown.enter.prevent>预 览</el-button>
          <el-button size="medium" @click="download" @keyup.prevent @keydown.enter.prevent>下 载</el-button>
          <el-button type="primary" size="medium" @click="returnList">返回列表</el-button>
        </div>
        <div class="submit-main">
          <div class="submit-main-name">
            <span class="label">样品名称：</span>
            {{ data.sampleName }}
          </div>
          <el-divider />
          <div class="submit-main-from">
            <div class="submit-main-item">
              <span class="label">报告编号：</span>
              {{ data.reportNo }}
            </div>
            <div class="submit-main-item">
              <span class="label">签发日期：</span>
              {{ data.issueDate }}
            </div>
            <div class="submit-main-item">
              <span class="label">主检人：</span>
              <UserTag :name="data.chargeName" />
            </div>
          </div>
        </div>
      </template>
    </el-result>
  </div>
</template>

<script>
import { reactive, toRefs, watch } from 'vue';
import router from '@/router/index.js';
import UserTag from '@/components/UserTag';
// import { formatDate } from '@/utils/formatTime'
// import { ElMessage } from 'element-plus'
// import _ from 'lodash'

export default {
  name: 'SubmitSucess',
  components: { UserTag },
  props: {
    data: {
      type: Object,
      default: function () {
        return {};
      }
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['detail', 'download', 'preview'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    // console.log(props.data)
    const datas = reactive({
      formData: {},
      downloading: false
    });

    watch(
      () => props,
      newValue => {
        datas.downloading = props.loading;
        // console.log(newValue)
      },
      { deep: true }
    );
    // 返回列表
    const returnList = () => {
      router.push({ path: '/test-report' });
    };
    // 详情
    const goDetail = () => {
      context.emit('detail');
    };
    // 下载
    const download = () => {
      context.emit('download');
    };
    // 预览
    const preview = () => {
      context.emit('preview');
    };

    return { ...toRefs(datas), returnList, goDetail, download, preview };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.submit-success {
  margin-top: 20px;
  .el-result {
    display: inline-block;
    background: $background-color;
    box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
    border-radius: 10px;
    padding: 0 50px 10px;
    :deep(.el-result__icon) {
      padding: 30px 0 0;
      img {
        width: 300px;
        margin-bottom: 18px;
      }
    }
    :deep(.el-result__title) {
      font-size: 24px;
      line-height: 36px;
      font-weight: bold;
      margin: 0;
    }
    :deep(.el-result__extra) {
      margin-top: 30px;
    }
    .submit-main {
      margin: 20px 0 40px;
      .submit-main-name {
        text-align: center;
        line-height: 32px;
        font-size: 18px;
        font-weight: bold;
      }
      .submit-main-from {
        width: 620px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        .submit-main-item {
          height: 32px;
          line-height: 32px;
          text-align: left;
          .label {
            display: inline-block;
            width: 70px;
            color: $tes-font2;
          }
        }
      }
    }
    .btn-group {
      display: flex;
      justify-content: center;
      margin: 20px 0 40px;
    }
    .el-divider {
      margin: 16px 0;
    }
  }
}
</style>
