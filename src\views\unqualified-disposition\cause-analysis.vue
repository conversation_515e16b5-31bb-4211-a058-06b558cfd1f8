<template>
  <!-- 原因分析及初步处置方案 -->
  <div v-loading="loading" class="register">
    <div class="titleHeader" style="display: flex">
      原因分析及初步处置方案
      <span v-if="lastCause.endTime == ''" class="name"
        >（待办人：{{ getNameByid(sampleInfoDetail.disposalOwnerId) }}）</span
      >
      <div class="inlineBlock textR" style="flex: 1">
        <el-button
          v-if="
            reasonArray.filter(item => {
              return item.endTime;
            }).length > 0
          "
          class=""
          size="small"
          @click="checkHistory()"
          >历史记录</el-button
        >
        <el-button
          v-if="!isShowCauseForm && getPermissionBtn('CauseAnalysis')"
          type="primary"
          class="analysisBtn"
          :disabled="lastStep.processParameter.assignee != currentAccountId"
          size="small"
          icon="el-icon-plus"
          @click="addReasonArray()"
          >原因分析</el-button
        >
      </div>
    </div>
    <div class="register-form-info info-main">
      <el-form v-if="isShowCauseForm" ref="ruleFormCause" class="formDataOption" :model="formData" label-position="top">
        <div class="register-title">原因分析 <span v-if="!isCheck" class="required-info">（必填）</span></div>
        <el-form-item
          label=""
          prop="reason"
          :rules="{ required: !isCheck, message: '请输入原因分析', trigger: 'change' }"
        >
          <el-input
            v-if="!isCheck"
            v-model="formData.reason"
            type="textarea"
            maxlength="300"
            :rows="2"
            placeholder="请输入原因分析"
          />
          <el-alert v-else :title="formData.reason" :closable="false" type="info" />
        </el-form-item>
        <div class="register-title">处置方式 <span v-if="!isCheck" class="required-info">（必填）</span></div>
        <el-form-item
          label=""
          class="selectFs"
          prop="disposalType"
          :rules="{ required: !isCheck, message: '处置方式', trigger: 'change' }"
        >
          <el-select
            v-if="!isCheck"
            v-model="formData.disposalType"
            :disabled="isCheck"
            filterable
            placeholder="请选择处置方式"
            clearable
          >
            <el-option-group v-for="item in disposalWayList" :key="item.label" :label="item.label">
              <el-option
                v-for="val in item.group"
                :key="val.id"
                :label="val.name"
                :value="val.code"
                :disabled="val.status !== 1"
              >
                <span style="float: left">{{ val.name }}</span>
                <span v-if="val.status !== 1" class="fr" style="color: red">已停用</span>
              </el-option>
            </el-option-group>
          </el-select>
          <el-alert v-else :title="disposalWayAll[formData.disposalType]" :closable="false" type="info" />
        </el-form-item>
        <div class="register-title">初步处置方案 <span v-if="!isCheck" class="required-info">（必填）</span></div>
        <el-form-item
          label=""
          prop="disposalPlan"
          :rules="{ required: !isCheck, message: '请输入初步处置方案', trigger: 'change' }"
        >
          <el-input
            v-if="!isCheck"
            v-model="formData.disposalPlan"
            maxlength="300"
            :disabled="isCheck"
            type="textarea"
            :rows="2"
            placeholder="请输入初步处置方案"
          />
          <el-alert v-else :title="formData.disposalPlan" :closable="false" type="info" />
        </el-form-item>
        <div class="register-title">处置结果<span class="addTitle">（统计数量总和不得超过 生产/入库数量）</span></div>
        <el-row v-if="!isCheck" class="added">
          <el-col :span="6">
            <el-form-item
              label="合格："
              class="inlineBlock"
              prop="qualifiedNum"
              :rules="[
                { required: true, message: '请输入合格数', trigger: 'change' },
                { validator: restrictMaxLength, maxlength: 8, message: '请输入8个字符以内', tigger: 'change' }
              ]"
            >
              <el-input-number v-model="formData.qualifiedNum" :min="0" controls-position="right" /><span
                class="unit"
                >{{ sampleInfoDetail.sampleUnitName }}</span
              >
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="不合格："
              class="inlineBlock"
              prop="unQualifiedNum"
              :rules="[
                { required: true, message: '请输入不合格数', trigger: 'change' },
                { validator: restrictMaxLength, maxlength: 8, message: '请输入8个字符以内', tigger: 'change' }
              ]"
            >
              <el-input-number v-model="formData.unQualifiedNum" :min="0" controls-position="right" /><span
                class="unit"
                >{{ sampleInfoDetail.sampleUnitName }}</span
              >
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="报废："
              class="inlineBlock"
              prop="scrappedNum"
              :rules="[
                { required: true, message: '请输入报废数', trigger: 'change' },
                { validator: restrictMaxLength, maxlength: 8, message: '请输入8个字符以内', tigger: 'change' }
              ]"
            >
              <el-input-number v-model="formData.scrappedNum" :min="0" controls-position="right" /><span class="unit">{{
                sampleInfoDetail.sampleUnitName
              }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="退货："
              class="inlineBlock"
              prop="returnNum"
              :rules="[
                { required: true, message: '请输入退货数', trigger: 'change' },
                { validator: restrictMaxLength, maxlength: 8, message: '请输入8个字符以内', tigger: 'change' }
              ]"
            >
              <el-input-number v-model="formData.returnNum" :min="0" controls-position="right" /><span class="unit">{{
                sampleInfoDetail.sampleUnitName
              }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-else class="czjg">
          <el-col :span="6">
            <el-alert
              :title="'合格：' + formData.qualifiedNum + sampleInfoDetail.sampleUnitName"
              :closable="false"
              type="info"
            />
          </el-col>
          <el-col :span="6">
            <el-alert
              :title="'不合格：' + formData.unQualifiedNum + sampleInfoDetail.sampleUnitName"
              :closable="false"
              type="info"
            />
          </el-col>
          <el-col :span="6">
            <el-alert
              :title="'报废：' + formData.scrappedNum + sampleInfoDetail.sampleUnitName"
              :closable="false"
              type="info"
            />
          </el-col>
          <el-col :span="6">
            <el-alert
              :title="'退货：' + formData.returnNum + sampleInfoDetail.sampleUnitName"
              :closable="false"
              type="info"
            />
          </el-col>
        </el-row>
        <div v-if="!isCheck" class="register-title">审批负责人</div>
        <el-row v-if="!isCheck" class="added">
          <el-col :span="6">
            <el-form-item
              label="技术部："
              class="inlineBlock inlineSelect"
              prop="department"
              :rules="{ required: true, message: '请选择技术部负责人', trigger: 'change' }"
            >
              <el-select v-model="formData.department" filterable placeholder="请选择技术部负责人" clearable>
                <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="质量部："
              class="inlineBlock inlineSelect"
              prop="quality"
              :rules="{ required: true, message: '请选择质量部负责人', trigger: 'change' }"
            >
              <el-select v-model="formData.quality" filterable placeholder="请选择质量部负责人" clearable>
                <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="!isBs || type === 1" :span="6">
            <el-form-item
              :label="isBs ? '采购部：' : '总工程师：'"
              class="inlineBlock inlineSelect"
              prop="chiefEngineer"
              :rules="{ required: false, message: isBs ? '请选择采购部' : '请选择总工程师', trigger: 'change' }"
            >
              <el-select
                v-model="formData.chiefEngineer"
                filterable
                :placeholder="isBs ? '请选择采购部' : '请选择总工程师'"
                clearable
              >
                <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="isCheck" class="qmrq">
          <el-col :span="4" :offset="17"
            >签名：
            <img v-if="qmImage" :src="qmImage" alt="" />
            <div v-else>{{ getNameByid(lastCause.processParameter.assignee) }}</div>
          </el-col>
          <el-col :span="3"
            >日期：
            <div>{{ lastCause.endTime }}</div>
          </el-col>
        </el-row>
        <div v-if="!isCheck && isShowCauseForm" class="textR">
          <el-button size="small" @click="handleCache">取 消</el-button>
          <el-button type="primary" size="small" @click="handleSubmit">提 交</el-button>
        </div>
      </el-form>
      <!-- 处置意见模块 -->
      <Disposalopinion
        v-if="isShowCzyj"
        :sample-info-detail="sampleInfoDetail"
        :history-list="examinationList"
        @fresh="handleFresh"
      />
      <div v-if="isShowEditBtn && getPermissionBtn('ModificationScheme')" class="editFa">
        <el-button
          type="primary"
          icon="el-icon-plus"
          :disabled="lastStep.processParameter.assignee != currentAccountId"
          size="small"
          @click="
            isShowEditBtn = false;
            isCheck = false;
            isShowCzyj = false;
          "
          >修改方案</el-button
        >
      </div>
    </div>
    <!-- 历史记录 -->
    <historical-drawer
      :is-show="isShowDraw"
      :history-list="reasonArray"
      :disposal-way="disposalWayList"
      :sample-unit="sampleInfoDetail.sampleUnitName"
      @close="closeDrawer"
    />
  </div>
</template>

<script>
import { reactive, toRefs, watch, ref, getCurrentInstance, nextTick } from 'vue';
import { useStore } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
import router from '@/router/index.js';
import HistoricalDrawer from './historical-drawer.vue';
import Disposalopinion from './disposal-opinion.vue';
import { processExecute } from '@/api/unqualifiedDisposition';
import { getDictionary } from '@/api/user';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { getAttachment } from '@/api/login';
import { restrictMaxLength } from '@/utils/validate';

export default {
  name: 'CauseAnalysis',
  components: { HistoricalDrawer, Disposalopinion },
  props: {
    reasonArray: {
      type: Array,
      default: function () {
        return [];
      }
    },
    sampleInfoDetail: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['fresh'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    watch(props, newValue => {
      datas.sampleInfoDetail = props.sampleInfoDetail;
      initList();
    });
    const store = useStore().state;
    const datas = reactive({
      radioData: '全部',
      currentAccountId: '',
      loading: false,
      type: 0,
      qmImage: '', // 签名图片
      lastStep: { processParameter: {} }, // 最后一步的信息
      lastCause: { processParameter: {}, approvalList: [] }, // 最后一步的原因分析
      lastOnce: { processParameter: {}, approvalList: [] }, // 上一个原因分析
      isShowOption: false,
      isCheck: false,
      userOptions: store.common.nameList,
      sampleInfoDetail: props.sampleInfoDetail,
      disposalWayAll: {},
      disposalWayList: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ],
      reasonArray: [],
      formInline: {
        param: ''
      },
      isShowDraw: false,
      formData: {
        qualifiedNum: 0,
        unQualifiedNum: 0,
        scrappedNum: 0,
        returnNum: 0,
        disposalType: '',
        department: '',
        quality: '',
        chiefEngineer: ''
      },
      userList: {},
      tenantGroupList: JSON.parse(localStorage.getItem('tenantGroup')),
      clientName: '',
      examinationList: [], // 审批意见数组
      ruleFormCause: ref(),
      isBs: false, // 判断是否是宝胜
      isShowCauseForm: false, // 是否显示原因分析的表单
      isShowCzyj: false, // 是否显示处置意见模块
      isShowEditBtn: false // 是否显示修改方案按钮
    });

    if (getLoginInfo()) {
      datas.currentAccountId = getLoginInfo().accountId;
      datas.clientName = datas.tenantGroupList.filter(item => {
        return item.tenantId === getLoginInfo().tenantId;
      })[0].clientName;
      datas.isBs = datas.clientName.includes('宝胜');
    }

    const initList = () => {
      datas.type = props.sampleInfoDetail.type;
      if (props.reasonArray.length > 0) {
        datas.reasonArray = props.reasonArray;
        datas.lastCause = props.reasonArray[props.reasonArray.length - 1];
        nextTick(() => {
          getQmImage();
        });
        if (datas.lastCause.approvalList.length > 0) {
          datas.lastStep = datas.lastCause.approvalList[datas.lastCause.approvalList.length - 1];
          datas.isShowCzyj = datas.lastCause.approvalList.some(item => {
            return item.name === '技术研发部审批' || item.name === '质量管理部审批' || item.name === '总工程师审批';
          });
          datas.examinationList = datas.lastCause.approvalList;
        } else {
          datas.lastStep = props.reasonArray[props.reasonArray.length - 1];
        }
        if (datas.reasonArray.length > 1 && datas.lastCause.endTime === '') {
          datas.lastOnce = props.reasonArray[props.reasonArray.length - 2];
          datas.examinationList = datas.lastOnce.approvalList;
          datas.isShowCzyj = true;
        }
        // 没有填写过或者填写过但需要修改
        if (datas.lastCause.endTime !== '' || (datas.examinationList.length > 0 && datas.lastCause.endTime === '')) {
          datas.isCheck = true;
          datas.isShowCauseForm = true;
          datas.formData = JSON.parse(JSON.stringify(datas.lastCause.processParameter));
          if (datas.examinationList.length > 0 && datas.lastCause.endTime === '') {
            datas.isShowEditBtn = true;
          }
        }
      }
    };
    initList();
    // 取消按钮
    const handleCache = () => {
      if (datas.examinationList.length > 0 && datas.lastCause.endTime === '') {
        datas.isShowCauseForm = true;
        datas.isCheck = true;
        datas.isShowEditBtn = true;
      } else {
        datas.isCheck = false;
        datas.isShowCauseForm = false;
      }
    };
    const getQmImage = () => {
      datas.qmImage = '';
      datas.loading = true;
      getAttachment([datas.lastCause.processParameter.assignee]).then(res => {
        datas.loading = false;
        if (res) {
          const data = res.data.data;
          if (data.length > 0) {
            datas.qmImage = data[0].signatureUrl;
          }
        }
      });
    };
    // 获取处置方式字典
    const getDisposalWayList = () => {
      datas.loading = true;
      getDictionary(23).then(res => {
        datas.loading = false;
        if (res !== false) {
          datas.disposalWayList[0].group = [];
          datas.disposalWayList[1].group = [];
          datas.disposalWayAll = {};
          res.data.data.dictionaryoption.forEach(item => {
            datas.disposalWayAll[item.code] = item.name;
            if (item.status === 1) {
              datas.disposalWayList[0].group.push(item);
            } else {
              datas.disposalWayList[1].group.push(item);
            }
          });
        }
      });
    };
    getDisposalWayList();
    const handleSubmit = () => {
      datas.ruleFormCause.validate().then(valid => {
        if (valid) {
          var addNumber =
            datas.formData.qualifiedNum +
            datas.formData.returnNum +
            datas.formData.scrappedNum +
            datas.formData.unQualifiedNum;
          var allNumber = datas.sampleInfoDetail.productionQuantity + datas.sampleInfoDetail.inputWarehouseQuantity;
          if (addNumber > allNumber) {
            proxy.$message.error('统计数量总和不得超过 生产/入库数量');
          } else {
            var params = {
              businessKey: datas.sampleInfoDetail.id,
              processInstanceId: datas.sampleInfoDetail.processInstanceId,
              isAssent: 1,
              ...datas.formData
            };
            datas.loading = true;
            processExecute(params).then(res => {
              datas.loading = false;
              if (res.data.code === 200) {
                datas.isCheck = true;
                datas.isShowCauseForm = false;
                proxy.$message.success('提交成功');
                handleFresh();
              }
            });
          }
        }
      });
    };
    const handleFresh = () => {
      context.emit('fresh', {});
    };
    const checkHistory = () => {
      datas.isShowDraw = true;
    };
    const closeDrawer = () => {
      datas.isShowDraw = false;
    };
    // 返回列表
    const goBackList = () => {
      router.push({
        path: '/unqualified-disposition'
      });
    };
    const addReasonArray = () => {
      datas.isShowCauseForm = true;
    };
    return {
      ...toRefs(datas),
      goBackList,
      getQmImage,
      handleSubmit,
      restrictMaxLength,
      getPermissionBtn,
      checkHistory,
      closeDrawer,
      addReasonArray,
      getDisposalWayList,
      getNameByid,
      handleFresh,
      handleCache,
      initList
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
@import './common.scss';
.czjg {
  .el-col {
    padding: 0 10px 0 10px;
    &:last-child {
      padding-right: 0;
    }
    &:first-child {
      padding-left: 0;
    }
  }
}
.addTitle {
  font-size: 14px;
  color: $tes-primary;
}
.name {
  font-size: 14px;
  color: #606266;
  margin-left: 10px;
  font-weight: 400;
}
.selectFs {
  .el-select {
    width: 300px;
  }
}
.unit {
  font-size: 14px;
  display: inline-block;
  margin-left: 8px;
}
.optionTitle {
  display: inline-block;
  font-size: 14px;
  line-height: 20px;
}
.pass {
  color: #67c23a;
}
.noPass {
  color: #f56c6c;
}
.formDataOption {
  padding-bottom: 30px;
}
.info-main {
  .timeLine {
    :deep(.el-icon-close:before) {
      position: absolute;
      left: 1.5px;
      top: 1.7px;
    }
  }
  .addedTitle {
    margin-bottom: 0;
    :deep(.el-form-item__label) {
      font-weight: bold;
    }
  }
  .editFa {
    margin-top: 24px;
    text-align: right;
    padding-bottom: 35px;
  }
  :deep(.el-input.is-disabled .el-input__inner) {
    border: 0;
  }
  :deep(.el-input.is-disabled .el-input__inner) {
    background: #fff;
    color: #606266;
    padding-left: 0;
  }
  :deep(.el-timeline-item__node--large) {
    width: 16px;
    height: 16px;
  }
  :deep(.el-timeline) {
    padding: 0;
  }
  :deep(.el-timeline-item__timestamp) {
    position: absolute;
    right: 0;
  }
}
.register-form-info {
  :deep(.el-alert) {
    margin-bottom: 7px;
  }
}
</style>
