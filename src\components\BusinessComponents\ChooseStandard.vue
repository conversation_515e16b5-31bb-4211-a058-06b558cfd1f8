<template>
  <el-dialog
    :model-value="showDialog"
    custom-class="custom-dialog"
    title="判定标准"
    width="60%"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="closeDialog"
  >
    <div v-loading="dialogLoading" class="dialog-header">
      <div class="header-left">
        <el-input
          ref="inputRef"
          v-model="filterText"
          class="search"
          size="small"
          placeholder="请输入产品名称"
          prefix-icon="el-icon-search"
          clearable
          @keyup.enter="searchItem()"
        />
        <el-button type="primary" size="small" @click="searchItem()">查询</el-button>
      </div>
    </div>
    <div v-loading="dialogLoading" class="dialog-content">
      <el-row>
        <el-col :span="7">
          <div class="tree-container">
            <div class="tree-content">
              <el-tree
                ref="treeRef"
                :data="treeData"
                node-key="id"
                :props="defaultProps"
                default-expand-all
                :expand-on-click-node="false"
                :highlight-current="true"
                draggable
                class="leftTree"
                @node-click="clickNode"
              >
                <template #default="{ node }">
                  <span>{{ node.label }}</span>
                </template>
              </el-tree>
            </div>
          </div>
        </el-col>
        <el-col :span="17">
          <div class="list-container">
            <el-table
              ref="tableRef"
              :key="tableKey"
              v-loading="loading"
              :data="productList"
              fit
              border
              size="medium"
              height="auto"
              highlight-current-row
              :row-style="
                () => {
                  return 'cursor: pointer';
                }
              "
              class="dark-table allocation-table base-table format-height-table"
              @select="selectChange"
              @selection-change="handleSelectionChange"
              @current-change="changeRadio"
            >
              <el-table-column type="index" label="选择" width="70" align="center">
                <template #default="{ row }">
                  <el-radio v-model="row.radio" :label="row.id" @change="changeRadio(row)">{{ '' }}</el-radio>
                </template>
              </el-table-column>
              <el-table-column label="产品名称" prop="name" show-overflow-tooltip>
                <template #default="{ row }">
                  <span style="white-space: pre">{{ row.productName || '--' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="更新时间" prop="lastUpdateDateTime" :width="130" sortable>
                <template #default="{ row }">
                  <span>{{ formatDate(row.lastUpdateDateTime) || '--' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="更新人" prop="user" :width="130">
                <template #default="{ row }">
                  <UserTag :name="getNameByid(row.lastUpdateByuserId) || '--'" />
                </template>
              </el-table-column>
              <el-table-column label="版本" prop="version" :width="100" style="text-align: left">
                <template #default="{ row }">
                  <span>{{ row.version ? 'V' + row.version : row.version }}</span>
                </template>
              </el-table-column>
              <el-table-column label="状态" prop="status" :width="100" style="text-align: left">
                <template #default="{ row }">
                  <el-tag size="small" :type="statusDicClass[row.status]"> {{ statusDic[row.status] }}</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="closeDialog">取 消</el-button>
        <el-button :loading="dialogLoading" type="primary" @click="dialogSuccess">选择标准</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, inject, nextTick } from 'vue';
import _ from 'lodash';
import { formatDate } from '@/utils/formatTime';
import { getNameByid } from '@/utils/common';
import UserTag from '@/components/UserTag';
import { ElMessage } from 'element-plus';
import { formatTree } from '@/utils/formatJson';
import { getTree, getProductList } from '@/api/testBase';

export default {
  name: 'ChooseStandard',
  components: { UserTag },
  props: {
    materialCode: {
      type: String,
      default: ''
    },
    show: {
      type: Boolean,
      default: false
    },
    alreadySelect: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance();
    const lodash = inject('_');
    const tableRef = ref(null);
    const state = reactive({
      showDialog: false,
      dialogLoading: false,
      treeData: [],
      inputRef: ref(),
      treeRef: ref(),
      filterText: '',
      materialCode: '',
      defaultProps: {
        children: 'children',
        label: 'code'
      },
      tags: [],
      oldTags: [],
      standardModelInfo: {},
      matchList: [],
      productList: [],
      loading: false,
      tableKey: 0,
      currentTreeNode: {},
      statusDicClass: {
        0: 'info',
        1: 'success',
        2: 'wait'
      },
      statusDic: {
        2: '停用',
        1: '生效',
        0: '草稿'
      },
      selectedRow: {}
    });

    watch(
      () => props.show,
      newValue => {
        state.showDialog = newValue;
        state.tags = [];
        state.standardModelInfo = props.alreadySelect;
        state.materialCode = props.materialCode;
        if (newValue && state.materialCode) {
          getTreeList();
          state.currentTreeNode = {};
          nextTick(() => {
            state.inputRef.focus();
          });
        }
      },
      { deep: true }
    );
    const getTreeList = async () => {
      state.dialogLoading = true;
      const response = await getTree(state.materialCode).finally(() => {
        state.dialogLoading = false;
      });
      if (response) {
        state.treeData = JSON.parse(JSON.stringify(formatTree(response.data.data)));
        const all = {
          id: '',
          code: '全部',
          materialCategoryCode: state.materialCode
        };
        state.treeData.unshift(all);
        state.currentTreeNode = state.treeData[0];
        if (state.currentTreeNode.materialCategoryCode) {
          getStandardProduct();
        }
        nextTick(() => {
          state.treeRef?.setCurrentKey(state.currentTreeNode.id, true);
        });
      }
    };
    const getStandardProduct = async () => {
      state.dialogLoading = true;
      const response = await getProductList({
        page: '1',
        limit: '-1',
        isReleased: true,
        param: state.filterText,
        standardCategoryId: state.currentTreeNode.id,
        materialCategoryCode: state.currentTreeNode.materialCategoryCode
      }).finally((state.dialogLoading = false));
      if (response) {
        state.productList = response.data.data.list;
      }
    };
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    // 点击树节点
    const clickNode = (data, node) => {
      state.currentTreeNode = data;
      getStandardProduct();
      console.log(data);
    };

    // 确定选择
    const dialogSuccess = () => {
      if (checkSelectedRow()) {
        state.selectedRow.judgmentName = `${getJudgmentName(state.selectedRow.standardCategoryId)} ${
          state.selectedRow.productName
        }`;
        context.emit('close', state.selectedRow);
        state.showDialog = false;
        state.selectedRow = {};
      }
    };
    function readTree(node, resultList = []) {
      if (node && node.length > 0) {
        let count = 0;
        node.forEach(ele => {
          resultList.push({
            id: ele.id,
            parentId: ele.parentId,
            code: ele.code,
            type: ele.standardType
          });
          if (ele.children && ele.children.length > 0) {
            readTree(ele.children, resultList);
          } else {
            count++;
          }
        });
        if (count === node.length) {
          return resultList;
        }
      }
      return resultList;
    }
    // 获取标准名
    function getJudgmentName(standardCategoryId) {
      let resultName = '';
      const standardTreeList = state.treeData;
      standardTreeList.shift();
      standardTreeList.forEach(item => {
        if (JSON.stringify(item).indexOf(standardCategoryId) !== -1) {
          const standardList = readTree([item]);
          standardList.forEach(ele => {
            if (ele.id === standardCategoryId) {
              standardList.forEach(obj => {
                if (obj.id === ele.parentId) {
                  resultName = obj.code;
                }
              });
            }
          });
        }
      });
      return resultName;
    }
    function checkSelectedRow() {
      if (!state.selectedRow.id) {
        ElMessage.warning('请先选择一条数据!');
        return false;
      }

      return true;
    }

    // 取消选择
    const closeDialog = () => {
      state.showDialog = false;
      context.emit('close');
      state.selectedRow = {};
    };
    // 关闭tags
    const closeTag = tag => {
      // console.log(tag)
      const hasitem = _.filter(state.productList, res => {
        if (res.id === tag.id) {
          res.checked = false;
        }
        return res.id === tag.id;
      });
      if (hasitem.length > 0) {
        tableRef.value.toggleRowSelection(hasitem[0]);
      }
      state.tags.splice(state.tags.indexOf(tag), 1);
    };
    // 清空
    const clear = () => {
      state.tags = [];
      if (state.productList && state.productList.length > 0) {
        tableRef.value.clearSelection();
      }
    };
    // searchItem 查询
    const searchItem = value => {
      if (state.currentTreeNode.materialCategoryCode) {
        getStandardProduct();
      } else {
        ElMessage.warning('请先选择树节点!');
      }
    };
    // table 选择事件
    const handleSelectionChange = val => {
      if (val.length > 0 && state.productList.length > 0 && val.length === state.productList.length) {
        state.productList.forEach(tree => {
          tree.checked = true;
        });
      } else if (val.length === 0) {
        state.productList.forEach(tree => {
          tree.checked = false;
        });
        _.pullAll(state.tags, state.productList);
      }
      state.tags = state.tags.concat(val);
      state.tags = lodash.uniqBy(state.tags, 'id');
    };
    const selectChange = (val, row) => {
      row.checked = !row.checked;
      if (row.checked === false) {
        state.tags.splice(state.tags.indexOf(row), 1);
      }
    };

    const changeRadio = row => {
      if (row.id) {
        state.selectedRow = row;
        row.radio = row.id;
        state.productList.forEach(item => {
          if (item.id !== row.id) {
            item.radio = false;
          }
        });
      }
    };

    return {
      ...toRefs(state),
      searchItem,
      dialogSuccess,
      closeDialog,
      filterNode,
      clickNode,
      closeTag,
      clear,
      handleSelectionChange,
      selectChange,
      formatDate,
      getNameByid,
      tableRef,
      changeRadio
    };
  }
};
</script>

<style lang="scss">
@import '@/styles/dialog.scss';
</style>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
.matchItem {
  display: inline-block;
  margin: 0 10px 10px 0;
}
.matchList {
  max-height: 110px;
  overflow-y: auto;
}
.matchContent {
  background-color: #fff;
  padding: 10px 10px 0 10px;
  margin-bottom: 10px;
}
.matchTitle {
  line-height: 20px;
}
.dialog-content {
  margin-bottom: 0;
}
.tree-container {
  margin-bottom: 20px;
  .tree-content {
    height: calc(100vh - 500px);
    overflow-y: auto;
  }
}
.list-container {
  padding-bottom: 0;
  margin-bottom: 20px;
  height: calc(100vh - 460px);
  overflow: hidden;
  .dark-table {
    :deep(.el-table__body-wrapper) {
      max-height: calc(100vh - 545px);
      overflow-y: auto;
      overflow-x: hidden !important;
    }
  }
}
</style>
