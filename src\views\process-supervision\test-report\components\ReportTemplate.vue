<template>
  <baseExcel :json-data="templateJsonData" :device-list="[]" :is-standard-custom="templateJsonData.isStandardCustom" />
</template>
<script>
import { reactive, toRefs, watch } from 'vue';
import baseExcel from '@/views/excelComponents/baseExcel';

export default {
  name: 'ReportTemplate',
  components: { baseExcel },
  props: {
    infoData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  setup(props, context) {
    watch(
      () => props.infoData,
      (newValue, oldValue) => {
        console.log(newValue.id);
      }
    );
    const state = reactive({
      templateJsonData: {
        isStandardCustom: 0 // 判断是否自定义标准
      }
    });

    return {
      ...toRefs(state)
    };
  }
};
</script>
<style lang="scss" scoped></style>
