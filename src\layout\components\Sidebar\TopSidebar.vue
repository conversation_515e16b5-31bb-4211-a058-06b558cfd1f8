<template>
  <div class="menu-top">
    <i v-if="iconFlag === 2" class="el-icon-arrow-left" @click="scrollMenu('left')" />
    <i v-if="iconFlag === 1" class="el-icon-arrow-right" @click="scrollMenu('right')" />
    <el-scrollbar ref="topSidebarScrollRef" wrap-class="scrollbar-wrapper-top">
      <el-menu
        :default-active="activeMenu"
        background-color="#2c2c2c"
        text-color="#fff"
        :active-text-color="variables.menuActiveText"
        mode="horizontal"
      >
        <sidebar-item
          v-for="route in permission_routes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
          class="text-top"
        />
      </el-menu>
      <!-- <i class="el-icon-arrow-right" /> -->
    </el-scrollbar>
  </div>
</template>

<script>
import { reactive, toRefs } from 'vue';
import { mapGetters } from 'vuex';
import variables from '@/styles/variables.module.scss';
// import { getMenuList } from '@/utils/auth'
// import { filterAsyncRoutes } from '@/utils/permission'
import SidebarItem from './SidebarItem';

export default {
  components: { SidebarItem },
  setup() {
    const datas = reactive({
      iconFlag: 0
    });

    return { ...toRefs(datas) };
  },
  computed: {
    ...mapGetters(['permission_routes', 'sidebarMode']),
    activeMenu() {
      const route = this.$route;
      // const asyncRoutes = filterAsyncRoutes([], getMenuList())
      // console.log(asyncRoutes)
      const { meta, path } = route;
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    variables() {
      return variables;
    },
    isTop() {
      return this.sidebarMode === 'horizontal';
    }
  },
  created() {
    this.$nextTick(() => {
      const el = this.$refs['topSidebarScrollRef'].wrap;
      var num = el.scrollWidth - el.offsetWidth;
      if (num < 0) {
        this.iconFlag = 0;
      } else {
        this.iconFlag = 2;
      }
      // console.log(el.offsetWidth, el.scrollWidth, el.scrollLeft, this.iconFlag)
    });
  },
  methods: {
    scrollMenu(flag) {
      const el = this.$refs['topSidebarScrollRef'].wrap;
      var num = el.scrollWidth - el.offsetWidth;
      if (num < 0) {
        this.iconFlag = 0;
        return false;
      }
      if (flag === 'left') {
        if (num <= el.scrollLeft) {
          this.iconFlag = 1;
          el.scrollLeft = el.scrollWidth - el.offsetWidth;
        } else {
          el.scrollLeft = el.scrollLeft + 100;
        }
      } else {
        if (el.scrollLeft <= 0) {
          this.iconFlag = 2;
          el.scrollLeft = 0;
        } else {
          el.scrollLeft = el.scrollLeft - 100;
        }
      }
      // console.log(el.offsetWidth, el.scrollWidth, el.scrollLeft, this.iconFlag)
    }
  }
};
</script>
<style scoped lang="scss">
.menu-top {
  display: inline-flex;
  width: calc(100% - 450px);
  .el-icon-arrow-left,
  .el-icon-arrow-right {
    // position: absolute;
    left: 0px;
    line-height: 50px;
    color: #fff;
    font-size: 20px;
    cursor: pointer;
  }
}
.scrollbar-wrapper-top {
  // padding: 0 15px;
  // .el-icon-arrow-right {
  //   position: absolute;
  //   right: 0px;
  //   line-height: 48px;
  //   color: #fff;
  //   font-size: 20px;
  //   cursor: pointer;
  // }
}

.text-top {
  // width: 100px;
  float: left;
  :deep(.el-menu-item) {
    padding: 0px 15px 0px 0px;
    height: 46px;
    line-height: 46px;
  }
  :deep(.el-sub-menu) {
    .el-sub-menu__title {
      font-size: 14px;
      padding: 0px 15px 0px 0px;
      height: 46px;
      line-height: 46px;
    }
  }
}
</style>
