<template>
  <el-drawer
    v-model="drawerShow"
    title="客户详情"
    direction="rtl"
    :before-close="handleClose"
    size="88%"
    destroy-on-close
    custom-class="page-drawer customerManagement"
  >
    <DrawerLayout
      v-loading="detailLoading"
      :has-left-panel="false"
      :main-offset-top="53"
      :has-button-group="getPermissionBtn('editCustomers') && detailData.isValid"
    >
      <template #drawer-title>
        <span>{{ detailData.name }}</span>
        <el-tag v-if="detailData.isValid" size="mini">生效</el-tag>
        <el-tag v-else type="info" size="mini">作废</el-tag>
      </template>
      <template #button-group>
        <el-button size="small" @click="editDetail" @keyup.prevent @keydown.enter.prevent>编辑客户信息</el-button>
      </template>
      <el-form class="isCheck">
        <el-row>
          <el-col :span="9">
            <el-form-item label="客户编号：" prop="no">
              <div class="formValue">{{ detailData.no }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="英文名：" prop="enName">
              <div class="formValue">{{ detailData.enName || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="公司传真：" prop="faxNo">
              <div class="formValue">{{ detailData.faxNo || '--' }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="9">
            <el-form-item label="公司电话：" prop="phone">
              <div class="formValue">{{ detailData.phone || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="结算方式：" prop="reckoningWay">
              <div class="formValue">{{ customerStatus[detailData.reckoningWay] || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户信誉：" prop="credit">
              <div class="formValue">{{ reputation[detailData.credit] || '--' }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="客户描述：" prop="remark">
              <div class="formValue">{{ detailData.remark || '--' }}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs v-model="activeName" class="marginTop">
        <el-tab-pane label="地址" name="1" style="text-align: left">
          <CustomerAddress
            :active-name="activeName"
            :detail-id="detailData.id"
            :is-valid="detailData.isValid"
            @is-have-revised="
              val => {
                return handleHaveRevise(val, 'address');
              }
            "
          />
        </el-tab-pane>
        <el-tab-pane label="联系人" name="2" style="text-align: left">
          <CustomerContacts
            :active-name="activeName"
            :detail-id="detailData.id"
            :is-valid="detailData.isValid"
            @is-have-revised="
              val => {
                return handleHaveRevise(val, 'contacts');
              }
            "
          />
        </el-tab-pane>
        <el-tab-pane label="发票" name="3" style="text-align: left">
          <CustomerInvoice
            :active-name="activeName"
            :detail-id="detailData.id"
            :is-valid="detailData.isValid"
            @is-have-revised="
              val => {
                return handleHaveRevise(val, 'invoice');
              }
            "
          />
        </el-tab-pane>
      </el-tabs>
      <!-- 编辑客户信息 -->
      <DrawerCustomerInfo
        :drawer="customerDrawer"
        :detail-data="detailData"
        :customer-status="customerStatus"
        :reputation="reputation"
        :drawer-type="'edit'"
        @close="closeCustomerDrawer"
      />
    </DrawerLayout>
  </el-drawer>
</template>
<script>
import { watch, reactive, toRefs, ref } from 'vue';
import { getDetail } from '@/api/customerManagement';
import DrawerLayout from '@/components/DrawerLayout';
import DrawerCustomerInfo from './DrawerCustomerInfo.vue';
import { getPermissionBtn } from '@/utils/common';
import { getNameByid } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import CustomerAddress from './components/CustomerAddress';
import CustomerContacts from './components/CustomerContacts';
import CustomerInvoice from './components/CustomerInvoice';

export default {
  name: 'CustomerDrawer',
  components: { DrawerLayout, DrawerCustomerInfo, CustomerAddress, CustomerContacts, CustomerInvoice },
  props: {
    detailDrawer: {
      type: Boolean,
      default: false
    },
    drawerType: {
      type: String,
      default: ''
    },
    detailInfo: {
      type: Object,
      default: function () {
        return {};
      }
    },
    customerStatus: {
      type: Object,
      default: function () {
        return {};
      }
    },
    reputation: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDrawer'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    const state = reactive({
      drawerShow: false,
      isRefresh: {
        address: false, // 地址模板是否因为修改过需要刷新
        contacts: false,
        invoice: false
      },
      activeName: '',
      ruleForm: ref(),
      formRef: ref(),
      customerDrawer: false,
      detailLoading: false,
      detailData: {},
      cascaderProps: {
        expandTrigger: 'hover',
        children: 'children',
        label: 'name',
        value: 'name'
      },
      customerStatus: {},
      reputation: {}
    });
    const handleClose = () => {
      context.emit('closeDrawer', {
        isRefresh: state.isRefresh.address || state.isRefresh.contacts || state.isRefresh.invoice
      });
    };
    watch(props, newValue => {
      state.drawerShow = props.detailDrawer;
      if (state.drawerShow) {
        state.customerStatus = props.customerStatus;
        state.reputation = props.reputation;
        state.detailData = {};
        state.isRefresh = {
          address: false, // 地址模板是否因为修改过需要刷新
          contacts: false,
          invoice: false
        };
        state.activeName = '1';
        initDetail();
      }
    });

    // 查询详情
    const initDetail = () => {
      state.detailLoading = true;
      getDetail(props.detailInfo.id).then(res => {
        state.detailLoading = false;
        if (res) {
          state.detailData = res.data.data;
        }
      });
    };
    // 编辑客户信息
    const editDetail = () => {
      state.customerDrawer = true;
    };
    const closeCustomerDrawer = val => {
      state.customerDrawer = false;
      if (val.isRefresh) {
        initDetail(props.detailInfo.id);
      }
    };
    const handleHaveRevise = (val, type) => {
      state.isRefresh[type] = val;
    };
    return {
      ...toRefs(state),
      editDetail,
      closeCustomerDrawer,
      drageHeader,
      colWidth,
      initDetail,
      getPermissionBtn,
      handleHaveRevise,
      getNameByid,
      handleClose
    };
  }
};
</script>

<style lang="scss" scoped>
.isCheck {
  margin-top: 12px;
  padding: 10px 20px;
  background: #f5f7fa;
  border-radius: 3px;
}
:deep(.el-cascader) {
  width: 100%;
}
.formValue {
  word-wrap: break-word; /* 旧版浏览器支持 */
  overflow-wrap: break-word; /* 标准属性 */
}
</style>
