
// #region 公用计算方法

function publicNumberDigits(arrayValue) {
  var len = 0;
  var maxNumber = arrayValue.reduce((total, item) => {
    if (item.toString().split('.')[1]) {
      len = item.toString().split('.')[1].length
      if (total < len) {
        total = len;
      }
    }
    return total;
  }, 0);
  return maxNumber;
};
function publicCompare(x, y) { //比较函数
  if (publicCheckNum(x) < publicCheckNum(y)) {
    return -1;
  } else if (publicCheckNum(x) > publicCheckNum(y)) {
    return 1;
  } else {
    return 0;
  }
}
// 根据输入内容取最小值
function publicMin(arrayValue) {
  var newArray = publicProcess(arrayValue).sort(publicCompare);
  if (newArray.length > 0) {
    return publicCheckNum(newArray[0]);
  } else {
    return '/';
  }
};
// 根据输入内容取最大值
function publicMax(arrayValue) {
  var newArray = publicProcess(arrayValue).sort(publicCompare);
  if (newArray.length > 0) {
    return publicCheckNum(newArray[newArray.length - 1]);
  } else {
    return '/';
  }
};
// 取中位数
function middleValue(arrayValue) {
  var newArray = publicProcess(arrayValue).sort(publicCompare);
  var length = newArray.length;
  if (length == 0) {
    return '';
  } else if (length % 2 == 0) {
    return (Number(newArray[length / 2]) + Number(newArray[length / 2 - 1])) / 2;
  } else {
    return newArray[Math.floor(length / 2)];
  }
};
// 取平均数(四舍五入)
function publicAverage(arrayValue, decimal) {
  var newArray = publicProcess(arrayValue);
  var length = newArray.length;
  var sum;
  if (length == 0) {
    return '/';
  } else {
    sum = newArray.reduce((total, item) => {
      total = publicAdd(total, Number(item));
      return total;
    }, 0);
    if (decimal) {
      return toFixed(publicDivide(sum, length), decimal);
    } else {
      return toFixed(publicDivide(sum, length), publicNumberDigits(newArray));
    }
  }
};
// 标准差计算公式
function publicStandardDeviation(arrayValue, decimal) {
  var newArray = publicProcess(arrayValue);
  var averageValue = publicAverage(arrayValue, decimal);
  var length = newArray.length;
  if (length == 0) {
    return '';
  } else if (length == 1) {
    return 0;
  } else {
    var sum2 = newArray.reduce((total, item) => {
      total = publicAdd(total, Math.pow(publicSubtract(publicCheckNum(item), publicCheckNum(averageValue)), 2));
      return total;
    }, 0);
    if (decimal) {
      return toFixed(Math.sqrt(publicDivide(sum2, length - 1), 2), decimal);
    } else {
      return toFixed(Math.sqrt(publicDivide(sum2, length - 1)), publicNumberDigits(newArray));
    }
  }
}
// 清洗数组类型的输入内容
function publicProcess(arrayValue) {
  var newArray = arrayValue.filter(function (item) {
    if (publicCalculate(item)) {
      return publicCheckNum(item);
    }
  })
  return newArray;
}
// 输入的值是否能直接参与计算
function publicCalculate(value) {
  if (value && !isNaN(Number(value))) {
    return true;
  } else {
    return false;
  }
};
// 是否满足输入条件、整数（正负）、小数
function publicCheckNum(value) {
  if (value) {
    if (value === '/' || value === '／') {
      return '/'
    } else if (!isNaN(Number(value))) {
      return Number(value)
    } else {
      return ''
    }
  } else {
    return '';
  }
};

//#endregion

// #region 全局变量

var publicI = -1;
var lengthNum = 0;
var focusDom = [];

// #endregion

// #region 公用DOM操作

function publicXhModule(xhHtml, moduleIndex) {
  var newString = '';
  for (var i = 1; i <= moduleIndex; i++) {
    newString += xhHtml.replace(/moduleIndex/g, i);
  }
  return newString;
}

function publicAutocomplete() {
  document.querySelectorAll(".ipt").forEach((item) => {
    if (item.tagName == 'INPUT') {
      item.classList.remove("textLeft", "textIndent10")
      if (!item.disabled) {
        item.setAttribute("autocomplete", "off");
      } else {
        if (!item.classList.contains("showHistory")) {
          item.className += " showHistory";
        }
      }
      // if (!item.hasAttribute('value')) {
      //     item.setAttribute('value', '/')
      // }
    }
    if (item.tagName == 'SELECT') {
      item.classList.remove("textLeft", "textIndent10")
      if (!item.classList.contains("selectCenter")) {
        item.className += " selectCenter";
      }
      if (!item.options[0].text && !item.options[0].value) {
        item.options[0].text = '/';
      } else if (item.options[0].value) {
        newOption = document.createElement("option");
        newOption.appendChild(document.createTextNode("/"));
        newOption.setAttribute("value", "");
        item.append(newOption);
      }
    }
    if (!item.disabled) {
      if (!item.classList.contains("bg-blue")) {
        item.className += " bg-blue";
      }
    }
  })
};

function publicSelectFocus(i) {
  if (focusDom[i].tagName == 'DIV') {
    focusDom[i].dom.getElementsByTagName("input")[0].focus();
  } else {
    if (focusDom[i].tagName == 'INPUT') {
      focusDom[i].dom.focus();
    } else {
      focusDom[i].dom.focus();
    }
    // focusDom[i].style.backgroundColor="red";
  }
};

//上下键聚焦input
function publicHandleOnKeyDown() {
  document.onkeydown = function (e) {
    if (e.shiftKey && e.key == 'ArrowUp') {
      // 向上移动，如果是第一个回到最后一个
      if (publicI == -1) {
        publicI = 1;
      } else if (publicI == 0) {
        publicI = lengthNum;
      }
      publicI = publicI - 1;
      publicSelectFocus(publicI)
    } else if (e.shiftKey && e.key == 'ArrowDown') {
      // 下移，如果是最后一个回到第一个
      publicI = publicI + 1;
      if (lengthNum == publicI) {
        publicI = 0
      }
      publicSelectFocus(publicI)
    } else if (e.ctrlKey == true && e.key == 's') {
      e.preventDefault();
    }
  };
}


// 验证拥有类名"number"的元素，使其只能输入数字
function publicValidateNumber() {
  const allNumberInput = document.querySelectorAll('.number, .input-number');
  for (let i = 0; i < allNumberInput.length; i++) {
    const item = allNumberInput[i];
    item.addEventListener('change', function () {
      item.value = publicCheckNum(item.value);
    });
  }
}

// #endregion

// #region 初始化DOM操作

function handleInputPublic() {
  document.querySelectorAll(".iptPublic").forEach((item) => {
    if (!item.disabled) {
      lengthNum += 1;
      focusDom.push({
        tagName: item.tagName,
        dom: item
      });
    }
  })
}

function handleInput() {
  document.querySelectorAll(".ipt").forEach((item) => {
    if ((item.tagName == 'INPUT' || item.tagName == 'SELECT') && !item.disabled) {
      lengthNum += 1;
      focusDom.push({
        tagName: item.tagName,
        dom: item
      });
    }
  })
}

// 所有需要初始化执行的DOM操作
function initDOMOpreations() {
  handleInput();
  handleInputPublic();
  publicValidateNumber();
  publicAutocomplete();
  publicHandleOnKeyDown();
}


// #endregion

// #region Commented Out Codes

// function PublicEncode(text ) {
//     console.log(text)
//     var r="";
//     var n;
//     var t;
//     var b=[ "___", "__$", "_$_", "_$$", "$__", "$_$", "$$_", "$$$", "$___", "$__$", "$_$_", "$_$$", "$$__", "$$_$", "$$$_", "$$$$", ];
//     var gv='fpa'
//     var s = "";
//     for( var i = 0; i < text.length; i++ ){
//             n = text.charCodeAt( i );
//     if( n == 0x22 || n == 0x5c ){
//                 s += "\\\\\\" + text.charAt( i ).toString(16);
//             }else if( (0x21 <= n && n <= 0x2f) || (0x3A <= n && n <= 0x40) || ( 0x5b <= n && n <= 0x60 ) || ( 0x7b <= n && n <= 0x7f ) ){
//                 s += text.charAt( i );
//             }else if( (0x30 <= n && n <= 0x39 ) || (0x61 <= n && n <= 0x66 ) ){
//     if( s ) r += "\"" + s +"\"+";
//                 r += gv + "." + b[ n < 0x40 ? n - 0x30 : n - 0x57 ] + "+";
//                 s="";
//             }else if( n == 0x6c ){ // 'l'
//                 if( s ) r += "\"" + s + "\"+";
//                 r += "(![]+\"\")[" + gv + "._$_]+";
//                 s = "";
//             }else if( n == 0x6f ){ // 'o'
//                 if( s ) r += "\"" + s + "\"+";
//                 r += gv + "._$+";
//                 s = "";
//             }else if( n == 0x74 ){ // 'u'
//                 if( s ) r += "\"" + s + "\"+";
//                 r += gv + ".__+";
//                 s = "";
//             }else if( n == 0x75 ){ // 'u'
//                 if( s ) r += "\"" + s + "\"+";
//                 r += gv + "._+";
//                 s = "";
//             }else if( n < 128 ){
//     if( s ) r += "\"" + s;
//     else r += "\"";
//                 r += "\\\\\"+" + n.toString( 8 ).replace( /[0-7]/g, function(c){ return gv + "."+b[ c ]+"+" } );
//                 s = "";
//             }else{
//     if( s ) r += "\"" + s;
//     else r += "\"";
//                 r += "\\\\\"+" + gv + "._+" + n.toString(16).replace( /[0-9a-f]/gi, function(c){ return gv + "."+b[parseInt(c,16)]+"+"} );
//                 s = "";
//             }
//         }
//     if( s ) r += "\"" + s + "\"+";

//         r =
//         gv + "=~[];" +
//         gv + "={___:++" + gv +",$$$$:(![]+\"\")["+gv+"],__$:++"+gv+",$_$_:(![]+\"\")["+gv+"],_$_:++"+
//         gv+",$_$$:({}+\"\")["+gv+"],$$_$:("+gv+"["+gv+"]+\"\")["+gv+"],_$$:++"+gv+",$$$_:(!\"\"+\"\")["+
//         gv+"],$__:++"+gv+",$_$:++"+gv+",$$__:({}+\"\")["+gv+"],$$_:++"+gv+",$$$:++"+gv+",$___:++"+gv+",$__$:++"+gv+"};"+
//         gv+".$_="+
//     "("+gv+".$_="+gv+"+\"\")["+gv+".$_$]+"+
//     "("+gv+"._$="+gv+".$_["+gv+".__$])+"+
//     "("+gv+".$$=("+gv+".$+\"\")["+gv+".__$])+"+
//     "((!"+gv+")+\"\")["+gv+"._$$]+"+
//     "("+gv+".__="+gv+".$_["+gv+".$$_])+"+
//     "("+gv+".$=(!\"\"+\"\")["+gv+".__$])+"+
//     "("+gv+"._=(!\"\"+\"\")["+gv+"._$_])+"+
//         gv+".$_["+gv+".$_$]+"+
//         gv+".__+"+
//         gv+"._$+"+
//         gv+".$;"+
//         gv+".$$="+
//         gv+".$+"+
//     "(!\"\"+\"\")["+gv+"._$$]+"+
//         gv+".__+"+
//         gv+"._+"+
//         gv+".$+"+
//         gv+".$$;"+
//         gv+".$=("+gv+".___)["+gv+".$_]["+gv+".$_];"+
//         gv+".$("+gv+".$("+gv+".$$+\"\\\"\"+" + r + "\"\\\"\")())();";

//     return r;
// }
// function jjdecode(text) {
//     var output = '';
//     var g = text.match(/([^=])=~\[\];/)[1];
//     // Building my scope
//         var lines = text.match(/([^;]*);/g);
//     var lookAhead = false;
//     var finalLine = '';
//     for(var i in lines) {
//         if ( ! lines[i].match(/_.\$\(_.\$\(/) && ! lookAhead) {
//                 eval(lines[i]);
//         } else {
//             lookAhead = true;
//             finalLine = finalLine + lines[i];
//             if (i == lines.length - 1) {
//             // _.\$\((.*)(\)\(\))
//                 var re = new RegExp(g + '.\\$\\((.*)(\\)\\(\\))');
//                 var reString = finalLine.match(re);
//                 output = eval(reString[1]);
//             }
//         }
//     }
//     console.log(eval(g));
//     return output;
// }
// 禁止鼠标右击
//  document.oncontextmenu = function() {
//      return false;
//  };
//   //禁用开发者工具F12
//   document.onkeydown = document.onkeyup = document.onkeypress = function(event) {
//     let e = event || window.event || arguments.callee.caller.arguments[0];
//     if (e && e.keyCode == 123) {
//       e.returnValue = false;
//       return false;
//     }
//   };
//   let userAgent = navigator.userAgent;
//   if (userAgent.indexOf("Firefox") > -1) {
//     let checkStatus;
//     let devtools = /./;
//     devtools.toString = function() {
//       checkStatus = "on";
//     };
//     setInterval(function() {
//       checkStatus = "off";
//       console.log(devtools);
//       console.log(checkStatus);
//       console.clear();
//       if (checkStatus === "on") {
//         let target = "";
//         try {
//           window.open("about:blank", (target = "_self"));
//         } catch (err) {
//           let a = document.createElement("button");
//           a.onclick = function() {
//             window.open("about:blank", (target = "_self"));
//           };
//           a.click();
//         }
//       }
//     }, 200);
//   } else {
//     //禁用控制台
//     let ConsoleManager = {
//       onOpen: function() {
//         alert("Console is opened");
//       },
//       onClose: function() {
//         alert("Console is closed");
//       },
//       init: function() {
//         let self = this;
//         let x = document.createElement("div");
//         let isOpening = false,
//           isOpened = false;
//         Object.defineProperty(x, "id", {
//           get: function() {
//             if (!isOpening) {
//               self.onOpen();
//               isOpening = true;
//             }
//             isOpened = true;
//             return true;
//           }
//         });
//         setInterval(function() {
//           isOpened = false;
//           console.info(x);
//           console.clear();
//           if (!isOpened && isOpening) {
//             self.onClose();
//             isOpening = false;
//           }
//         }, 200);
//       }
//     };
//     ConsoleManager.onOpen = function() {
//       //打开控制台，跳转
//       let target = "";
//       try {
//         window.open("about:blank", (target = "_self"));
//       } catch (err) {
//         let a = document.createElement("button");
//         a.onclick = function() {
//           window.open("about:blank", (target = "_self"));
//         };
//         a.click();
//       }
//     };
//     ConsoleManager.onClose = function() {
//       alert("Console is closed!!!!!");
//     };
//     ConsoleManager.init();
//   }
// xhHtml 循环的模块， moduleIndex 循环的次数

// #endregion

// #region 计算方法相关DOM操作

function handleTimeChange(formula, beginTimeId, endTimeId, timeId) {
  let eventElementArray = [];
  eventElementArray.push(document.getElementById(beginTimeId));
  eventElementArray.push(document.getElementById(endTimeId));
  eventElementArray.forEach(item => {
    item.addEventListener("change", function () {
      change2NumCalculate(formula, beginTimeId, endTimeId, timeId);
    });
  });
}

/**
 * 使用radio切换模块
 * radio group的name和包含所有模块的div的id相同
 * 所有模块对用的div的id都是由{name}_{number}组成
 */
function switchByRadioButton(name) {
  let checkCount = 0;
  let sortModuleArray = [];
  let radioList = document.getElementsByName(name);
  let rootElement = document.getElementById(name);

  // 获取radio数量
  radioList.forEach(item => {
    if (item.checked) {
      checkCount++;
    }
  })
  // 默认选择第一个radio按钮
  if (checkCount !== 1) {
    radioList.forEach(item => {
      item.checked = false;
    })
    radioList[0].checked = true;
  }

  // 将选中radio对应的模块元素push到sortModuleArray
  for (let i = 0; i < radioList.length; ++i) {
    if (radioList[i].checked) {
      sortModuleArray.push(document.getElementById(`${name}_${i + 1}`));
    }
  }

  // 将剩余模块元素push到sortModuleArray
  for (let i = 0; i < radioList.length; ++i) {
    if (radioList[i].checked === false) {
      sortModuleArray.push(document.getElementById(`${name}_${i + 1}`));
    }
  }

  // 对所有模块元素重新排序
  if (checkCount === 1) {
    sortModuleArray.forEach(item => {
      rootElement.removeChild(item);
    });
    sortModuleArray.forEach(item => {
      rootElement.appendChild(item);
    })
    initDOMOpreations();
  }

  // 只显示续重radio对应的模块，隐藏其它模块
  sortModuleArray.forEach(item => {
    item.style.display = "none";
  })
  // 显示选中的radio对应的模块
  sortModuleArray[0].style.display = "block";

}

// 计算时间
function calculateMinutes(beginTime, endTime) {
  let beginTimes = getDateTime(beginTime);
  let endTimes = getDateTime(endTime);
  let totalMinutes = 0;
  if (endTimes.year >= beginTimes.year) {
    totalMinutes += (parseInt(endTimes.year) - parseInt(beginTimes.year)) * 525600;
    totalMinutes += (parseInt(endTimes.month) - parseInt(beginTimes.month)) * 43200;
    totalMinutes += (parseInt(endTimes.day) - parseInt(beginTimes.day)) * 1440;
    totalMinutes += (parseInt(endTimes.hour) - parseInt(beginTimes.hour)) * 60;
    totalMinutes += (parseInt(endTimes.minutes) - parseInt(beginTimes.minutes));
  }

  return totalMinutes.toString();
}

// 获取datetime
function getDateTime(datetime) {
  let beginTimes = ('' + datetime).split('-');
  let dayTimes = (beginTimes[2] + '').split('T');
  let times = (dayTimes[1] + '').split(':');
  let year = beginTimes[0];
  let month = beginTimes[1];
  let day = dayTimes[0];
  let hour = times[0];
  let minutes = times[1];

  return {
    year,
    month,
    day,
    hour,
    minutes
  }
}

// 模拟实现重载
function changeCalculate() {
  switch (arguments.length) {
    case 3:
      changeArrayNumCalculate(arguments[0], arguments[1], arguments[2]);
      break;
    case 4:
      change2NumCalculate(arguments[0], arguments[1], arguments[2], arguments[3]);
      break;
    default:
      console.log('找不到需要的计算方法!');
      break;
  }
}

// 计算两个值，将结果写入指定元素 input: string, output: void
function change2NumCalculate(formula, id1, id2, resultId) {
  let resultElement = document.getElementById(resultId);
  if (document.getElementById(id1).value && document.getElementById(id2).value) {
    let numStr1 = document.getElementById(id1).value.toString().trim();
    let numStr2 = document.getElementById(id2).value.toString().trim();

    resultElement.value = formula2Num(formula, numStr1, numStr2);
  }
}

// 计算多个值，将结果写入指定元素 input: string, output: void
function changeArrayNumCalculate(formula, idArray, resultId) {
  let resultElement = document.getElementById(resultId);
  let elementArray = [];
  idArray.forEach(elementId => {
    elementArray.push(document.getElementById(elementId));
  });
  if (elementArray.length > 0) {
    let errorCount = 0;
    let numArray = [];
    elementArray.forEach(element => {
      if (!element.value) {
        errorCount++;
      }
      numArray.push(Number(element.value));
    });
    if (errorCount === 0) {
      resultElement.value = formulaArrayNum(formula, numArray);
    }
    // else {
    //     console.log('查不到中位数计算所需要的值!');
    // }
  }
}

// input: f32, output: string
function formula2Num(formula, num1, num2) {
  let result = '/';

  switch (formula) {
    case CalculateFormula.dumbbellArea: {
      if (num2 <= 0) {
        alert("输入有误!");
      } else {
        result = publicMultiply(num1, num2).toString();
      }
      break;
    }
    case CalculateFormula.tubeArea: {
      if (publicSubtract(num1, num2) < 0) {
        alert("输入有误，平均厚度应小于平均外径!");
      } else {
        result = publicMultiply(publicMultiply(publicSubtract(num1, num2), num2), 3.14).toString();
      }
      break;
    }
    case CalculateFormula.pressure: {
      if (num2 <= 0) {
        alert("输入有误!");
      } else {
        result = publicDivide(num1, num2, 2).toString();
      }
      break;
    }
    case CalculateFormula.minuteTime: {
      if (num1 > num2) {
        alert("预处理开始时间应小于预处理结束时间");
      } else {
        result = calculateMinutes(num1, num2).toString();
      }
      break;
    }
    default: {
      console.log('找不到需要的计算方法!');
      break;
    }
  }

  return result;

}

function formulaArrayNum(formula, numArray) {
  let result = '/';
  switch (formula) {
    case CalculateFormula.median: {
      let errorCount = 0;
      numArray.forEach(number => {
        if (!Number(number)) {
          errorCount++;
        }
      });
      if (errorCount === 0) {
        result = middleValue(numArray).toString();
      }
      break;
    }
    default: {
      break;
    }
  }

  return result;
}

const CalculateFormula = {
  dumbbellArea: 'dumbbellArea',
  tubeArea: 'tubeArea',
  pressure: 'pressure',
  median: 'median',
  minuteTime: 'minuteTime'
}

// sample type
const SampleType = {
  tube: 'tube',
  dumbbell: 'dumbbell',
}

// #endregion
