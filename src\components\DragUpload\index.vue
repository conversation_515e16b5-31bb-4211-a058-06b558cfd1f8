<template>
  <div
    :class="{ 'drag-active': dragActive }"
    class="drag-container"
    @dragenter.prevent="toggle_active()"
    @dragleave.prevent="toggle_active()"
    @dragover.prevent
    @drop.prevent="drop"
  >
    <div v-if="droppedFile !== null" style="height: 100%; width: 100%">
      <CustomPanel
        :has-margin-bottom="true"
        :panel-margin-width="0.8"
        :has-panel-header="false"
        panel-min-height="100px"
        class="selected-file-panel"
      >
        <el-row class="file-name-container">
          <el-col :span="21">
            <div class="file-name-wrapper">
              <span class="file-name">{{ droppedFile.name }}</span>
            </div>
          </el-col>
          <el-col :span="1" />
          <el-col :span="2">
            <el-button
              circle
              icon="el-icon-delete"
              style="border-color: #ffffff"
              size="small"
              @click.stop.prevent="clearDropped()"
            />
          </el-col>
        </el-row>
      </CustomPanel>
    </div>
    <div v-else class="drag-wrapper">
      <span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          :class="{ 'animate-bounce': droppedFile === null }"
          class="upload-img"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
          />
        </svg>
      </span>
      <!-- File input -->
      <label for="file" class="lbl-tip">
        <span class="drag-tip">点击此处或拖动文件上传</span>
        <input id="file" type="file" name="file" :accept="acceptFileType" class="hidden" @change="selectedFile" />
      </label>
      <p class="btn-tip">
        {{ fileTip }}
      </p>
    </div>
  </div>
</template>
<script>
import { watch } from 'vue';
import useDropZone from './func/dropzone';
import CustomPanel from '@/components/PageComponents/CustomPanel.vue';

export default {
  name: 'DragUpload',
  components: { CustomPanel },
  props: {
    clearFile: {
      type: Boolean,
      default: false
    },
    fileTip: {
      type: String,
      default: '1.文件大小限制在20M以内; 2.只能上传一个文件'
    },
    acceptFileType: {
      type: String,
      default: '*'
    }
  },
  emits: ['getFile', 'setClearFile'],
  setup(props, context) {
    const { dragActive, droppedFile, toggle_active, drop, clearDropped, selectedFile } = useDropZone();

    watch(
      () => droppedFile.value,
      newValue => {
        if (newValue) {
          context.emit('getFile', newValue);
        }
      }
    );

    watch(
      () => props.clearFile,
      newValue => {
        if (newValue) {
          droppedFile.value = null;
          dragActive.value = false;
          context.emit('setClearFile', false);
        }
      }
    );

    return {
      dragActive,
      droppedFile,
      toggle_active,
      drop,
      clearDropped,
      selectedFile
    };
  }
};
</script>
<style lang="scss" scoped>
.drag-active {
  --tw-bg-opacity: 1;
  background-color: rgba(209, 250, 229, var(--tw-bg-opacity));
  border-color: rgba(110, 231, 183, var(--tw-border-opacity));
}

.drag-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 20rem;
  padding: 2rem 3rem;
  border: 4px rgba(209, 213, 219, 0.8) dashed;
  border-radius: 0.25rem;
}

.drag-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  --tw-text-opacity: 1;
  color: rgba(107, 114, 128, var(--tw-text-opacity));
}

.upload-img {
  width: 10rem;
  height: 6rem;
}

.btn-tip {
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 600;
}

.lbl-tip {
  padding: 0.5rem;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 600;
  line-height: 1.25;
  --tw-text-opacity: 1;
  color: rgba(75, 85, 99, var(--tw-text-opacity));
  background-color: rgba(237, 233, 254, var(--tw-bg-opacity));
  border: 1px;
  border-radius: 0.25rem;
  cursor: pointer;
}

.lbl-tip:hover {
  background-color: $tes-primary4;
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hidden {
  display: none;
}

.file-name {
  font-size: 0.75rem;
  line-height: 1rem;
}

.selected-file-panel {
  border: 1px solid #dcdfe6;
  border-radius: 10px;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-file-panel:hover {
  border-color: #000;
}

.file-name-wrapper {
  width: 100%;
  margin: 0 0.5rem;
  text-overflow: ellipsis;
}
</style>
