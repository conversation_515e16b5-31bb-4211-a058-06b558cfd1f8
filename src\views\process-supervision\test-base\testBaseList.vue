<template>
  <div class="h-full w-full overflow-hidden">
    <!-- 检测标准库  -->
    <ListLayout
      :has-page-header="false"
      :has-quick-query="false"
      :has-left-panel="true"
      :aside-max-width="520"
      main-height="100%"
    >
      <template #page-left-side>
        <div class="tree-container">
          <div class="tree-header">
            <div class="header-select">
              <span class="icon el-icon-menu" />
              <el-select
                v-model="materialCode"
                filterable
                size="small"
                class="topSelect"
                placeholder="请选择物资分类"
                @change="changeMaterialCode"
              >
                <el-option-group v-for="item in materialList" :key="item.label" :label="item.label">
                  <el-option v-for="val in item.list" :key="val.value" :label="val.name" :value="val.code" />
                </el-option-group>
              </el-select>
            </div>
            <el-input
              v-model="otherForm.filterText"
              size="small"
              placeholder="请输入标准号/型号"
              prefix-icon="el-icon-search"
            />
          </div>
          <div class="tree-content">
            <el-tree
              ref="refTree"
              :data="otherForm.treeData"
              node-key="id"
              :props="otherForm.defaultProps"
              :expand-on-click-node="false"
              :highlight-current="true"
              :filter-node-method="filterNode"
              class="leftTree"
              draggable
              :allow-drop="allowDrop"
              default-expand-all
              @node-drop="nodeDrop"
              @filter="filterNode"
              @node-click="clickNode"
            >
              <template #default="{ node, data }">
                <span>{{ node.label }}</span>
                <el-dropdown
                  v-if="data.id !== 'all'"
                  trigger="hover"
                  :class="node.showIcon ? 'icon-show' : ''"
                  class="tree-dropdown el-icon"
                  @visible-change="changeIcon(node.showIcon, node)"
                >
                  <i class="el-icon-more" />
                  <template
                    v-if="getPermissionBtn('editTesBaseTreeBtn') || getPermissionBtn('delTesBaseTreeBtn')"
                    #dropdown
                  >
                    <el-dropdown-menu>
                      <el-dropdown-item
                        v-if="node.data.standardType === 1 && getPermissionBtn('editTesBaseTreeBtn')"
                        @click="editTree(node.data, node)"
                        ><i class="iconfont tes-edit" />编辑</el-dropdown-item
                      >
                      <el-dropdown-item
                        v-if="node.data.standardType === 2 && getPermissionBtn('editTesBaseTreeBtn')"
                        @click="handleEditXh(node.data, node)"
                        ><i class="iconfont tes-edit" />编辑</el-dropdown-item
                      >
                      <el-dropdown-item
                        v-if="getPermissionBtn('delTesBaseTreeBtn')"
                        class="color-red"
                        @click="delTree(node.data)"
                        ><i class="iconfont tes-delete" />删除</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-tree>
          </div>
        </div>
      </template>

      <div class="flex flex-col overflow-hidden h-[85vh]">
        <div class="overflow-hidden flex-1 flex flex-col">
          <div class="search-group">
            <div class="search-left">
              <el-input
                v-model="param"
                v-trim
                v-focus
                :placeholder="placeHolderTitle[standardType]"
                size="small"
                prefix-icon="el-icon-search"
                clearable
                @keyup.enter="getTableList('search')"
                @clear="getTableList('search')"
              />
              <el-button type="primary" size="small" @click="getTableList('search')">查询</el-button>
              <el-button size="small" @click="reset">重置</el-button>
            </div>
            <div class="search-right">
              <el-tooltip
                v-if="getPermissionBtn('addTesBaseTreeBtn')"
                :content="standardType == 3 ? '型号分类下不可继续新增' : '同一父级分类下型号和标准不可同时存在'"
                :disabled="standardType !== 3 && standardType !== 2"
                placement="bottom"
              >
                <el-button
                  v-if="getPermissionBtn('addTesBaseTreeBtn')"
                  type="primary"
                  size="small"
                  :class="{ isDisabled: standardType == 3 || standardType == 2 }"
                  @keyup.prevent
                  @keydown.enter.prevent
                  @click="addTreeItem"
                  >添加标准</el-button
                >
              </el-tooltip>
              <el-tooltip
                v-if="getPermissionBtn('gistAddModelNumber')"
                :content="addspecifyTitle[standardType]"
                :disabled="standardType !== 3 && standardType !== 1 && standardType !== 0"
                placement="bottom"
              >
                <el-button
                  v-if="getPermissionBtn('gistAddModelNumber')"
                  type="primary"
                  size="small"
                  :class="{ isDisabled: standardType === 3 || standardType === 1 || standardType === 0 }"
                  @keyup.prevent
                  @keydown.enter.prevent
                  @click="handleAddModule"
                  >添加型号</el-button
                >
              </el-tooltip>
              <el-tooltip
                v-if="getPermissionBtn('gistAdd')"
                :visible="getPermissionBtn('gistAdd')"
                :disabled="standardType === 3"
                placement="bottom"
              >
                <template #content>
                  <span>只允许在型号下新增规格</span>
                </template>
                <el-button
                  v-if="getPermissionBtn('gistAdd')"
                  :class="{ isDisabled: standardType !== 3 }"
                  type="primary"
                  size="small"
                  @click="handleSpecifications"
                  @keyup.prevent
                  @keydown.enter.prevent
                  >添加规格</el-button
                >
              </el-tooltip>
              <el-button
                v-if="getPermissionBtn('release')"
                type="primary"
                size="small"
                :disabled="standardType !== 3"
                @keyup.prevent
                @keydown.enter.prevent
                @click="onPublishBatch()"
                >批量发布</el-button
              >
              <el-dropdown v-if="getPermissionBtn('gistExport')" class="mx-3">
                <el-button
                  v-if="getPermissionBtn('gistExport')"
                  type="primary"
                  :class="{ isDisabled: standardType !== 3 }"
                  size="small"
                >
                  批量导出 <i class="el-icon-arrow-down" />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleClickExport()">勾选的数据</el-dropdown-item>
                    <el-dropdown-item @click="handleClickExport(true)">全部数据</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-tooltip
                v-if="getPermissionBtn('gistImport')"
                :visible="getPermissionBtn('gistImport')"
                :disabled="standardType === 3"
                placement="bottom"
              >
                <template #content>
                  <span>只允许在型号下新增规格</span>
                </template>
                <el-button
                  v-if="getPermissionBtn('gistImport')"
                  :class="{ isDisabled: standardType !== 3 }"
                  type="primary"
                  icon="el-icon-upload"
                  size="small"
                  @click="handleImport()"
                  @keyup.prevent
                  @keydown.enter.prevent
                  >批量导入</el-button
                >
              </el-tooltip>
              <!-- <el-tooltip
          v-if="getPermissionBtn('gistPreview')"
          :visible="getPermissionBtn('gistPreview')"
          :disabled="standardType === 3"
          placement="bottom"
        >
          <template #content>
            <span>只允许在型号下新增规格</span>
          </template>
          <el-button
            v-if="getPermissionBtn('gistPreview')"
            :class="{ isDisabled: standardType !== 3 }"
            type="primary"
            size="small"
            @click="handlePreview()"
            @keyup.prevent
            @keydown.enter.prevent
            >预览</el-button
          >
        </el-tooltip> -->
              <el-dropdown v-if="getPermissionBtn('gistPreview')" class="mx-3">
                <el-button
                  v-if="getPermissionBtn('gistPreview')"
                  type="primary"
                  :class="{ isDisabled: standardType !== 3 }"
                  size="small"
                >
                  预览 <i class="el-icon-arrow-down" />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handlePreview()">勾选的数据</el-dropdown-item>
                    <el-dropdown-item @click="handlePreview(true)">全部数据</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          <div class="flex-1 overflow-hidden">
            <!-- 标准表格 -->
            <StandTable
              :is-show="standardType === 1 || standardType === 0 || standardType === 4"
              :table-data="searchTableData"
              @selectData="handleSelectStandTable($event)"
            />
            <!-- 型号表格 -->
            <ModelTable
              :is-show="standardType === 2"
              :table-data="searchTableData"
              @selectData="handleSelectModelTable($event)"
            />
            <!-- 产品表格 -->
            <ProductTable
              ref="productTableRef"
              :is-show="standardType === 3"
              :search-params="searchParams"
              :specify-list="specifyListData"
              :detail-data="{
                checkTreeNode: checkTreeNode,
                materialCategoryCode: materialCode,
                materialCategoryId: materialId,
                standardTree: otherForm.dialogTreeData
              }"
              @selectProductData="handleSelectProductTable"
              @batchSelectProductData="handleBatchSelectProductData"
            />
          </div>
        </div>
        <div v-if="getPermissionBtn('standardProductRule')" class="h-[300px] flex flex-col overflow-hidden mt-3">
          <MatchRule
            class="flex-1 overflow-hidden"
            :standard-item-id="standardItemId"
            :standard-item-name="standardItemName"
            :standard-item-path="standardItemPath"
            :standard-item-type="standardItemType"
            :batch-select-product-ids="batchSelectProductIds"
            :standard-category-id="standardCategoryId"
          />
        </div>
      </div>
    </ListLayout>
  </div>
  <!-- 标准弹出框 -->
  <el-dialog
    v-model="showEditDialog"
    :title="otherForm.isAddTree === true ? '添加标准' : '编辑标准'"
    width="480px"
    :close-on-click-modal="false"
  >
    <el-form
      v-if="showEditDialog"
      ref="formTree"
      :model="dialogFrom"
      :rules="otherForm.dialogRules"
      size="small"
      label-position="right"
      label-width="120px"
    >
      <el-form-item label="标准号：" prop="standardNo">
        <el-input
          ref="inputRef"
          v-model="dialogFrom.standardNo"
          v-trim
          maxlength="30"
          autocomplete="off"
          placeholder="请输入标准编号"
        />
      </el-form-item>
      <el-form-item
        label="标准名称："
        prop="name"
        :rules="{ required: true, message: '请输入标准名称', trigger: 'change' }"
      >
        <el-input v-model="dialogFrom.name" maxlength="100" autocomplete="off" placeholder="请输入标准名称" />
      </el-form-item>
      <el-form-item label="英文名称：" prop="englishName">
        <el-input v-model="dialogFrom.englishName" maxlength="300" autocomplete="off" placeholder="请输入英文名称" />
      </el-form-item>
      <el-form-item label="生效日期：" prop="standardEffectiveDate">
        <el-date-picker
          v-model="dialogFrom.standardEffectiveDate"
          style="width: 100%"
          type="date"
          placeholder="请选择生效日期"
        />
      </el-form-item>
      <el-form-item label="父级分类：">
        <el-cascader
          v-model="dialogFrom.parentId"
          :options="otherForm.dialogTreeData"
          :disabled="!otherForm.isAddTree"
          :props="otherForm.categoryProps"
          clearable
          style="width: 100%"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="small" @click="showEditDialog = false">取 消</el-button>
        <el-button size="small" type="primary" @click="handleEditTree">确 认</el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 型号弹出框 -->
  <el-dialog
    v-model="moduleDialog"
    :title="isAddModule === true ? '添加型号' : '编辑型号'"
    width="480px"
    :close-on-click-modal="false"
  >
    <el-form
      v-if="moduleDialog"
      ref="modelFormRef"
      :model="dialogModuleFrom"
      :rules="dialogModuleRules"
      size="small"
      label-position="right"
      label-width="90px"
    >
      <el-form-item label="型号：" prop="productModel">
        <el-input
          v-model="dialogModuleFrom.productModel"
          v-trim
          maxlength="200"
          autocomplete="off"
          placeholder="请输入型号"
        />
      </el-form-item>
      <el-form-item
        label="型号名称："
        prop="name"
        :rules="{ required: true, message: '请输入型号名称', trigger: 'change' }"
      >
        <el-input v-model="dialogModuleFrom.name" maxlength="200" autocomplete="off" placeholder="请输入型号名称" />
      </el-form-item>
      <el-form-item
        label="父级分类："
        prop="parentId"
        :rules="{ required: true, message: '请选父级分类', trigger: 'change' }"
      >
        <el-cascader
          v-model="dialogModuleFrom.parentId"
          :options="otherForm.dialogTreeData"
          :props="otherForm.categoryProps"
          clearable
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item
        label="检测依据："
        prop="testBasis"
        :rules="{ required: true, message: '请输入检测依据', trigger: 'change' }"
      >
        <el-input
          v-model="dialogModuleFrom.testBasis"
          type="textarea"
          maxlength="300"
          :rows="2"
          autocomplete="off"
          placeholder="请输入检测依据"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="small" @click="moduleDialog = false">取 消</el-button>
        <el-button type="primary" size="small" @click="handleModel">确 认</el-button>
      </span>
    </template>
  </el-dialog>
  <AddEditSpecifications
    :is-show="isShowProduct"
    :dialog-type="productType"
    :specify-list="specifyListData"
    :detail-data="{
      checkTreeNode: checkTreeNode,
      materialCategoryCode: materialCode,
      materialCategoryId: materialId,
      standardTree: otherForm.dialogTreeData
    }"
    @closeDialog="handleAddDialog"
  />
  <!-- 批量导入 -->
  <DialogImportTestBase
    :visible="dialogImport"
    :detail-data="{
      materialCategoryCode: materialCode,
      standardCategoryId: checkTreeId
    }"
    :specify-list="specifyListData"
    @close="handleCloseImport"
  />
  <!-- 预览 -->
  <DrawerPreview
    :drawer="drawerPreview"
    :select-data="selectData"
    :is-all="isAllPreview"
    :check-tree="checkTreeNode"
    :detail-data="{
      materialCategoryCode: materialCode,
      standardCategoryId: checkTreeId
    }"
    @closeDrawer="handleClosePreview"
  />
</template>

<script>
import { reactive, ref, watch, getCurrentInstance, toRefs, nextTick } from 'vue';
// import Pagination from '@/components/Pagination'
import {
  formatTree,
  formatAllTree,
  formatTreeByIds,
  formatTreeByNames,
  formatBzTree,
  formatXhTree
} from '@/utils/formatJson';
import ModelTable from './modelTable';
import StandTable from './standTable';
import ProductTable from './productTable';
import AddEditSpecifications from './addEditSpecifications';
import {
  getTree,
  deleteProductTree,
  addTreeNode,
  updateTreeNode,
  canDelete,
  updateOrderTree,
  standardcategoryExport,
  standardProductPublishBatch
} from '@/api/testBase';
import { specificationList } from '@/api/material';
import ListLayout from '@/components/ListLayout';
import DialogImportTestBase from './components/DialogImportTestBase.vue';
import DrawerPreview from './components/DrawerPreview.vue';
import { getPermissionBtn } from '@/utils/common';
import { useStore } from 'vuex';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import router from '@/router/index.js';
import { useRoute } from 'vue-router';
import _ from 'lodash';
import { ElMessage, ElMessageBox } from 'element-plus';
import MatchRule from './components/MatchRule/index.vue';
import { cloneDeep } from 'lodash';

export default {
  name: 'TestBaseList',
  components: {
    ListLayout,
    AddEditSpecifications,
    ModelTable,
    StandTable,
    ProductTable,
    DialogImportTestBase,
    DrawerPreview,
    MatchRule
  },
  setup(props, context) {
    const standardType = {
      /** 标准 */
      standard: 1,
      /** 型号 */
      model: 2,
      /** 显示产品 */
      modelProduct: 3
    };
    const { proxy } = getCurrentInstance();
    const store = useStore();
    const route = useRoute();
    const state = reactive({
      isAdd: false,
      dialogImport: false,
      productTableRef: ref(),
      drawerPreview: false,
      selectData: [],
      isAllPreview: false,
      inputRef: ref(),
      allTableData: [], // 右侧表格所有的数据
      searchTableData: [], // 右侧表格查询时的最终数据
      addspecifyTitle: {
        // 新增型号时的按钮提示语
        0: '请选择标准后再新增型号',
        1: '同一父级分类下型号和标准不可同时存在',
        3: '型号分类下不可继续新增'
      },
      mateCodeRecentList: [], // 物资分类最近访问列表
      standardType: '', // 1 右侧表格显示标准列表，2右侧表格显示型号列表，3右侧表格显示产品
      materialList: [
        { label: '最近访问', list: [] },
        { label: '可选择', list: store.state.user.materialList }
      ], // 物资分类
      refTree: ref(null),
      placeHolderTitle: {
        0: '请输入标准号/标准名称',
        1: '请输入标准号/标准名称',
        2: '请输入型号/型号名称',
        3: '请输入产品名称',
        4: '请输入标准号/标准名称'
      }, // 表格筛选框的提示语
      tableData: [],
      searchParams: '', // 查询的关键参数，传递给产品列表
      copyData: {}, // 复制产品的详情
      param: '', // 模糊查询关键字
      total: 0,
      specifyListData: [], // 物资下的规格数据
      materialCode: '', // 选中的物资分类code
      materialId: '', // 选中的物资分类id
      tableLoading: false, // 表格加载的loading
      moduleDialog: false, // 型号弹出框
      modelFormRef: ref(), // 型号表单ref
      dialogModuleRules: {
        productModel: [{ required: true, message: '请输入型号', trigger: 'change' }]
      }, // 型号编号校验
      isAddModule: false, // 是否是新增型号
      isShowProduct: false, // 产品弹出框
      productType: 'add', // 产品弹出框类型
      dialogFrom: {
        standardEffectiveDate: new Date()
      }, // 操作树节点的弹窗表格
      dialogModuleFrom: {}, // 型号弹出框表单
      treeTitle: '', // 选中树节点的name
      checkTreeId: '', // 选中的左侧树节点的id
      checkTreeNode: {}, // 选中的树节点
      listQuery: {
        page: 1,
        limit: 20
      },
      standardItemId: '',
      standardItemName: '',
      standardItemPath: [],
      treeIdPath: [],
      standardItemType: null,
      batchSelectProductIds: [],
      standardCategoryId: '' // 标准分类Tree id
    });

    const otherForm = reactive({
      tableList: [],
      searchKey: '', // 关键字
      list: [],
      content: '',
      treeData: [],
      dialogTreeData: [],
      editData: {},
      newTree: [],
      defaultProps: {
        children: 'children',
        label: 'code'
      },
      category: [],
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'code',
        value: 'id'
      },
      activeIndex: '0',
      currentTabsData: store.state.user.materialList[0],
      moreIndex: 0,
      filterText: '',
      isAddTree: true,
      dialogRules: {
        standardNo: [{ required: true, message: '请输入标准编号', trigger: 'change' }]
      },
      testcapability: [],
      asideWidth: 240,
      showIcon: false
    });
    const reset = () => {
      state.param = '';
      getTableList('search');
    };
    // 树节点编辑
    const showEditDialog = ref(false);
    const editTree = (data, node) => {
      otherForm.dialogTreeData = formatAllTree(data.id, otherForm.dialogTreeData);
      showEditDialog.value = true;
      nextTick(() => {
        state.inputRef.focus();
      });
      otherForm.isAddTree = false;
      state.dialogFrom = JSON.parse(JSON.stringify(data));
      otherForm.category = formatTreeByIds(node.parent);
    };
    const formTree = ref(null);
    // 新增、编辑时保存树节点
    const handleEditTree = () => {
      formTree.value.validate(valid => {
        if (valid) {
          var parentId = state.dialogFrom.parentId;
          if (parentId) {
            if (parentId instanceof Array) {
              if (parentId.length > 0) {
                parentId = parentId[parentId.length - 1].toString();
              } else {
                parentId = 0;
              }
            } else if (parentId === 'all') {
              parentId = 0;
            }
          } else {
            parentId = 0;
          }
          const params = {
            ...state.dialogFrom,
            parentId: parentId,
            materialCategoryCode: state.materialCode
          };
          if (otherForm.isAddTree) {
            addTreeNode(params).then(function (res) {
              if (res) {
                showEditDialog.value = false;
                proxy.$message.success('新增成功!');
                getLeftTree();
              }
            });
          } else {
            updateTreeNode(params).then(function (res) {
              if (res) {
                showEditDialog.value = false;
                proxy.$message.success('编辑成功!');
                getLeftTree();
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    // 规格列表
    const getSpecificationList = () => {
      specificationList({ limit: '999', page: '1', categoryCode: state.materialCode }).then(res => {
        if (res) {
          state.specifyListData = res.data.data.list.filter(item => {
            return item.status !== 0;
          });
          state.productTableRef.setSpecifyList(state.specifyListData);
        }
      });
    };
    // 获取左侧列表树接口
    const getLeftTree = () => {
      state.tableLoading = true;
      getTree(state.materialCode).then(res => {
        state.tableLoading = false;
        const data = res.data.data;
        const all = {
          id: 'all',
          code: '全部',
          materialCategoryCode: state.materialCode,
          categoryId: otherForm.currentTabsData ? otherForm.currentTabsData.id : ''
        };
        if (data.length > 0) {
          otherForm.treeData = JSON.parse(JSON.stringify(formatTree(data)));
        } else {
          otherForm.treeData = formatTree(data);
        }
        otherForm.treeData.unshift(all);
        otherForm.dialogTreeData = data;
        nextTick(() => {
          // 默认高亮选中节点
          if (!state.checkTreeId) {
            // 判断第一次加载时默认选中第一个
            if (data.length > 0) {
              state.refTree.setCurrentKey(otherForm.treeData[0].id, true);
              state.checkTreeId = otherForm.treeData[0].id;
              state.checkTreeNode = otherForm.treeData[0];
              state.treeTitle = otherForm.treeData[0].name;
              getRightTable();
            } else {
              state.treeTitle = '';
              state.tableData = [];
              state.checkTreeNode = {};
              state.allTableData = [];
              state.searchTableData = [];
            }
          } else {
            state.refTree.setCurrentKey(state.checkTreeId, true);
            state.checkTreeNode = state.refTree.getCurrentNode(state.checkTreeId);
            getRightTable();
          }
        });
      });
    };
    // 获取右侧表格数据
    const getRightTable = () => {
      state.allTableData = [];
      if (state.checkTreeNode.id) {
        // 点击全部查看第一层标准分类
        if (state.checkTreeNode.id === 'all') {
          state.standardType = 0;
          otherForm.treeData.forEach(item => {
            if (item.id !== 'all') {
              state.allTableData.push(item);
            }
          });
          getTableList();
        } else {
          // 点击不是第一层的时候
          // 点击的是型号，右侧一定显示产品列表
          if (state.checkTreeNode.standardType === 2) {
            state.standardType = 3;
          } else {
            if (state.checkTreeNode.children && state.checkTreeNode.children.length > 0) {
              state.standardType = state.checkTreeNode.children[0].standardType;
              state.allTableData = state.checkTreeNode.children;
              getTableList();
            } else {
              state.standardType = Number(state.checkTreeNode.standardType) + 3;
              state.allTableData = [];
              state.searchTableData = [];
            }
          }
        }
      }
    };
    // 获取当前节点的产品列表
    const getTableList = type => {
      if (state.standardType === 3 && state.param !== state.searchParams && type === 'search') {
        state.searchParams = state.param;
      }
      if (state.param && state.allTableData.length > 0) {
        // 如果有搜索的参数
        if (state.standardType === 1 || state.standardType === 0 || state.standardType === 4) {
          // 标准库列表
          state.searchTableData = state.allTableData.filter(item => {
            return item.name.indexOf(state.param) !== -1 || item.standardNo.indexOf(state.param) !== -1;
          });
        } else if (state.standardType === 2) {
          // 型号列表
          state.searchTableData = state.allTableData.filter(item => {
            return item.productModel.indexOf(state.param) !== -1 || item.name.indexOf(state.param) !== -1;
          });
        }
      } else {
        // 如果没有搜索的参数
        state.searchTableData = state.allTableData;
      }
    };
    const getTreeFormater = type => {
      getTree(type).then(response => {
        if (response !== false) {
          const data = response.data.data;
          otherForm.treeData = formatTree(data);
          otherForm.newTree = formatTree(data);
          otherForm.dialogTreeData = data;
        }
      });
    };
    // 切换物资分类
    const changeMaterialCode = val => {
      const checkNode = store.state.user.materialList.filter(item => item.code === val)[0];
      if (checkNode) {
        state.materialId = checkNode.id;
      }
      if (route.query.checkTreeId && route.query.materialCategoryCode === checkNode.code) {
        state.checkTreeId = route.query.checkTreeId;
      } else {
        state.checkTreeId = '';
      }
      const isCz = state.mateCodeRecentList.findIndex(item => {
        return item.code === val;
      });
      if (isCz < 0) {
        if (state.mateCodeRecentList.length >= 3) {
          state.mateCodeRecentList.splice(2, 1);
        }
        state.mateCodeRecentList.unshift(checkNode);
        state.materialCode = val;
      }
      if (state.mateCodeRecentList.length > 0) {
        localStorage.setItem('mateCodeRecent', JSON.stringify(state.mateCodeRecentList));
      } else {
        localStorage.setItem('mateCodeRecent', '[]');
      }
      state.materialList[0].list = state.mateCodeRecentList;
      state.materialCode = val;
      if (state.materialCode) {
        getLeftTree();
      }
      // 该物资下的规格
      getSpecificationList();
    };
    // 添加标准
    const addTreeItem = () => {
      if (state.standardType === 3) {
        proxy.$message.warning('型号分类下不可继续新增!');
        return false;
      }
      if (state.standardType === 2) {
        proxy.$message.warning('同一父级分类下型号和标准不可同时存在!');
        return false;
      }
      otherForm.dialogTreeData = formatBzTree(otherForm.dialogTreeData);
      showEditDialog.value = true;
      nextTick(() => {
        state.inputRef.focus();
      });
      otherForm.category = [];
      otherForm.isAddTree = true;
      state.dialogFrom = {
        standardType: 1,
        parentId: state.checkTreeId,
        materialCategoryCode: state.materialCode,
        materialstandardCategoryId: otherForm.currentTabsData.id || ''
      };
    };
    // 新增型号
    const handleAddModule = () => {
      if (state.standardType === 3 || state.standardType === 1 || state.standardType === 0) {
        proxy.$message.warning(state.addspecifyTitle[state.standardType]);
        return false;
      }
      if (otherForm.treeData.length <= 1) {
        proxy.$message.warning('请先添加标准!');
        return false;
      }
      otherForm.dialogTreeData = formatXhTree(otherForm.dialogTreeData);
      state.moduleDialog = true;
      state.isAddModule = true;
      state.dialogModuleFrom = {
        standardType: 2,
        testBasis: state.checkTreeNode.standardNo + ' ' + state.checkTreeNode.name,
        parentId: state.checkTreeId
      };
    };
    // 编辑型号
    const handleEditXh = (data, node) => {
      otherForm.dialogTreeData = formatXhTree(otherForm.dialogTreeData);
      state.moduleDialog = true;
      state.isAddModule = false;
      state.dialogModuleFrom = JSON.parse(JSON.stringify(data));
    };
    // 树节点删除
    const delTree = node => {
      const deleteTitle = {
        1: '是否删除该标准',
        2: '是否删除该型号'
      };
      proxy
        .$confirm(deleteTitle[node.standardType], '提示', {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'warning'
        })
        .then(() => {
          // 判断节点是否能删除
          canDelete(node.id).then(function (res) {
            // console.log(res)
            if (res) {
              if (res.data.data) {
                // 删除树节点
                deleteProductTree(node.id).then(function (res) {
                  if (res) {
                    proxy.$message.success(res.data.message);
                    if (node.id === state.checkTreeId) {
                      state.checkTreeId = '';
                    }
                    getLeftTree();
                  }
                });
              } else {
                proxy.$message.error(res.data.message);
              }
            }
          });
        })
        .catch(() => {});
    };
    // 添加产品规格
    const handleSpecifications = () => {
      if (otherForm.treeData.length === 1) {
        proxy.$message.warning('请先在左侧添加标准分类');
        return false;
      }
      if (state.specifyListData.length === 0) {
        proxy.$message.warning('请先在该物资分类下添加规格');
        return false;
      }
      if (state.standardType !== 3) {
        proxy.$message.warning('只允许在型号下新增规格');
        return false;
      }
      state.isShowProduct = true;
    };
    watch(
      () => router.currentRoute.value.path,
      (newValue, oldValue) => {
        if (router.currentRoute.value.path === '/testBaseList') {
          state.mateCodeRecentList = [];
          if (JSON.parse(localStorage.getItem('mateCodeRecent'))) {
            const mateCodeData = JSON.parse(localStorage.getItem('mateCodeRecent'));
            mateCodeData.forEach(item => {
              if (item?.id) {
                state.mateCodeRecentList.push(item);
              }
            });
          }
          if (route.query.materialCategoryCode) {
            state.param = route.query.standardProductName ? route.query.standardProductName : '';
            state.searchParams = state.param;
            changeMaterialCode(route.query.materialCategoryCode);
          } else {
            if (store.state.user.materialList.length > 0) {
              state.materialCode = store.state.user.materialList[0].code;
              changeMaterialCode(state.materialCode);
            } else {
              state.materialCode = '';
            }
          }
        }
      },
      { immediate: true }
    );
    // 过滤树节点
    watch(
      () => otherForm.filterText,
      newValue => {
        state.refTree.filter(newValue);
      }
    );
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.code.indexOf(value) !== -1;
    };
    const handleAddDialog = val => {
      state.isShowProduct = false;
      if (val.isRefresh) {
        getTableList();
      }
    };
    const changeIcon = (command, node) => {
      if (!command) {
        node.showIcon = true;
      } else {
        node.showIcon = !node.showIcon;
      }
    };
    // 允许拖拽功能 只能同级拖拽
    const allowDrop = (draggingNode, dropNode, type) => {
      if (draggingNode.level === dropNode.level) {
        // parentid是父节点id
        if (draggingNode.data.parentId === dropNode.data.parentId) {
          return type === 'prev' || type === 'next';
        }
      } else {
        // 不同级进行处理
        return false;
      }
    };
    // 树排序
    const nodeDrop = (before, after) => {
      // console.log(after)
      var orderList = [];
      if (after.parent.level === 0) {
        orderList = JSON.parse(JSON.stringify(after.parent.data));
        _.remove(orderList, function (n) {
          return n.id === 'all';
        });
        orderList.forEach((ol, index) => {
          ol.order = index + 1;
        });
      } else {
        orderList = after.parent.data.children;
        orderList.forEach((ol2, index) => {
          ol2.order = index + 1;
        });
      }
      updateOrderTree(orderList).then(res => {
        if (res !== false) {
          proxy.$message.success('排序成功');
        }
      });
    };
    // 新增编辑型号
    const handleModel = () => {
      state.modelFormRef.validate().then(valid => {
        if (valid) {
          var parentId = state.dialogModuleFrom.parentId;
          if (state.dialogModuleFrom.parentId instanceof Array) {
            if (state.dialogModuleFrom.parentId.length > 0) {
              parentId = state.dialogModuleFrom.parentId[state.dialogModuleFrom.parentId.length - 1].toString();
            } else {
              parentId = 0;
            }
          }
          const params = {
            ...state.dialogModuleFrom,
            parentId: parentId,
            materialCategoryCode: state.materialCode
          };
          if (state.isAddModule) {
            addTreeNode(params).then(res => {
              if (res) {
                proxy.$message.success('新增型号成功！');
                state.moduleDialog = false;
                getLeftTree();
              }
            });
          } else {
            updateTreeNode(params).then(res => {
              if (res) {
                proxy.$message.success('编辑型号成功！');
                state.moduleDialog = false;
                getLeftTree();
              }
            });
          }
        }
      });
    };
    const clickNode = (data, node) => {
      state.standardItemId = data.id;
      state.standardCategoryId = data.id;
      state.standardItemName = data.code;
      state.treeIdPath = formatTreeByIds(node);
      state.standardItemPath = cloneDeep(state.treeIdPath);
      state.treeTitle = formatTreeByNames(node).join('/');
      state.standardItemType = data.standardType;
      state.batchSelectProductIds = [];

      if (state.checkTreeId !== data.id) {
        state.checkTreeId = data.id;
        state.checkTreeNode = data;
        getRightTable();
      }
    };
    const handlePreview = isAll => {
      state.selectData = state.productTableRef.getSelectRow();
      state.isAllPreview = isAll;
      if (isAll) {
        state.drawerPreview = true;
      } else if (!state.productTableRef.getSelectRow().length) {
        proxy.$message.warning('请勾选导出数据');
      } else {
        state.drawerPreview = true;
      }
    };
    const handleClickExport = isAll => {
      const params = {
        materialCategoryCode: state.materialCode,
        standardCategoryId: state.checkTreeId
      };
      if (isAll) {
        exportExcel(params);
      } else {
        if (!state.productTableRef.getSelectRow().length) {
          proxy.$message.warning('请勾选导出数据');
        } else {
          params.standardProductIdList = state.productTableRef.getSelectRow().map(item => item.id);
          exportExcel(params);
        }
      }
    };
    const exportExcel = async params => {
      state.tableLoading = true;
      standardcategoryExport(params).then(res => {
        state.tableLoading = false;
        const blob = new Blob([res.data], { type: '' });
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = `${state.checkTreeNode.name}（${state.checkTreeNode.productModel}）.xlsx`;
        a.href = blobUrl;
        a.click();
        proxy.$message.success('下载附件成功');
      });
    };
    // 批量导入
    const handleImport = () => {
      state.dialogImport = true;
    };
    const handleCloseImport = isRefresh => {
      state.dialogImport = false;
      if (isRefresh) {
        state.standardType = 0;
        nextTick(() => {
          state.standardType = 3;
        });
      }
    };
    const handleClosePreview = () => {
      state.drawerPreview = false;
    };

    const onPublishBatch = async () => {
      const selectedProductList = state.productTableRef.getSelectRow();
      if (!Array.isArray(selectedProductList) || selectedProductList.length === 0) {
        ElMessage.warning('请选择需要发布的数据');
        return;
      }
      console.log('dd', selectedProductList);
      // 0: '草稿', 1: '生效', 2: '停用'
      if (selectedProductList.some(x => x.status === 1)) {
        ElMessage.warning('已生效（已发布）的标准，不能重新发布');
        return;
      }
      if (selectedProductList.some(x => x.status === 2)) {
        ElMessage.warning('已停用的标准，不能重新发布');
        return;
      }
      const confirmRes = await ElMessageBox.confirm('发布后，将无法撤回，是否继续发布？', '确认发布', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).catch(() => 'cancel');
      if (confirmRes === 'cancel') {
        return;
      }
      const { data } = await standardProductPublishBatch(
        selectedProductList.map(item => ({
          standardProductVersionId: item.versionId,
          standardProductId: item.id,
          projectCategoryId: item.standardCategoryId
        }))
      );
      if (data.code !== 200) {
        ElMessage.error(data.message);
        return;
      }
      ElMessage.success('批量发布成功');
      state.productTableRef.getTableList();
    };

    const handleSelectStandTable = data => {
      state.standardItemId = data.id;
      state.standardItemName = data.code;
      state.standardItemPath = [...state.treeIdPath, data.id];
      state.standardItemType = standardType.standard;
    };

    const handleSelectModelTable = data => {
      state.standardItemId = data.id;
      state.standardItemName = data.code;
      state.standardItemPath = [...state.treeIdPath, data.id];
      state.standardItemType = standardType.model;
    };

    const handleSelectProductTable = data => {
      state.standardItemId = data.id;
      state.standardItemName = data.productName;
      state.standardItemPath = [...state.treeIdPath, data.id];
      state.standardItemType = standardType.modelProduct;
    };

    const handleBatchSelectProductData = rows => {
      state.batchSelectProductIds = rows.filter(x => x.id);
      state.standardItemType = standardType.modelProduct;
      handleSelectProductTable(rows?.[rows.length - 1] || {});
    };

    return {
      ...toRefs(state),
      clickNode,
      handleCloseImport,
      handleClosePreview,
      exportExcel,
      handleImport,
      handleClickExport,
      handlePreview,
      getRightTable,
      getTreeFormater,
      getTableList,
      getLeftTree,
      changeMaterialCode,
      handleAddModule,
      handleEditXh,
      getSpecificationList,
      handleModel,
      getPermissionBtn,
      allowDrop,
      nodeDrop,
      formatDate,
      changeIcon,
      drageHeader,
      handleSpecifications,
      handleAddDialog,
      formTree,
      addTreeItem,
      filterNode,
      handleEditTree,
      showEditDialog,
      delTree,
      editTree,
      otherForm,
      reset,
      onPublishBatch,
      handleSelectStandTable,
      handleSelectModelTable,
      handleSelectProductTable,
      handleBatchSelectProductData
    };
  },
  created() {
    this.bus.$on('reloadList', msg => {});
  },
  methods: {}
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
.tree-container {
  .tree-header {
    flex-direction: column;
    .header-select {
      display: flex;
      width: 100%;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 10px;
      :deep(.el-select .el-input__inner) {
        padding-left: 0;
        font-size: 16px;
        color: #303133;
        border: none;
      }
      .icon {
        font-size: 16px;
        margin-right: 10px;
      }
      .el-select {
        width: 100%;
      }
    }
  }
  .tree-content {
    height: calc(100vh - 210px);
  }
}

.search-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  .search-left {
    display: flex;
    padding-bottom: 10px;
    .el-input {
      margin-right: 10px;
    }
  }
  .search-right {
    display: flex;
    padding-bottom: 10px;
  }
}

.isDisabled {
  opacity: 0.4;
  cursor: not-allowed;
}
</style>
