import request from '@/utils/request';

// 列表
export function qualitySystemList(data) {
  return request({
    url: '/api-document/document/qualitysystem/list',
    method: 'post',
    data
  });
}
// 删除
export function deleteQualitysystem(id) {
  return request({
    url: `/api-document/document/qualitysystem/deleteById/${id}`,
    method: 'delete'
  });
}
export function getQualitySystem(id) {
  return request({
    url: `/api-document/document/qualitysystem/info/${id}`,
    method: 'get'
  });
}
// 获取发布之后的模板
export function findReleaseData() {
  return request({
    url: `/api-document/document/designtemplate/findReleaseData`,
    method: 'get'
  });
}
// 保存体系记录信息
export function saveOrUpdate(data) {
  return request({
    url: `/api-document/document/qualitysystem/saveOrUpdate`,
    method: 'post',
    data
  });
}

// 保存体系记录信息
export function processSubmit(id) {
  return request({
    url: `/api-document/document/qualitysystem/processSubmit/${id}`,
    method: 'post'
  });
}

// 保存体系记录信息
export function systemProcessHistory(id) {
  return request({
    url: `/api-document/document/qualitysystem/processHistory/${id}`,
    method: 'get'
  });
}
// 执行流程审批
export function processExecute(data) {
  return request({
    url: `/api-document/document/qualitysystem/processExecute`,
    method: 'post',
    data
  });
}
