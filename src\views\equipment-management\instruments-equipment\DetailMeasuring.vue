<template>
  <div v-loading="dialogLoading" class="detail-measuring">
    <div class="textLeft">
      <el-button
        v-if="detailData.isEquipmentMetering && getPermissionBtn('addmeasurementInfoBtn')"
        class="add-btn"
        size="small"
        icon="el-icon-plus"
        @click="handleAdd"
        @keyup.prevent
        @keydown.enter.prevent
        >新增计量信息</el-button
      >
    </div>
    <el-table
      :data="tableList"
      fit
      border
      style="width: 100%"
      highlight-current-row
      size="medium"
      class="detail-table format-height-table dark-table format-height-table2"
      :row-class-name="isInvalid"
      @header-dragend="drageHeader"
    >
      <el-table-column type="index" label="序号" :width="70" align="center" />
      <el-table-column prop="responsibilityId" label="负责人" width="140">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.responsibilityId) || '--'" />
        </template>
      </el-table-column>
      <el-table-column prop="companyAddress" label="单位" width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.companyAddress || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="testCapability" label="日期" width="120" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.measurementDate ? formatDate(row.measurementDate) : '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="validBeginDate" label="检定有效期" width="240" show-overflow-tooltip>
        <template #default="{ row }">
          <div>
            {{ row.validBeginDate ? formatDate(row.validBeginDate) : '--' }}
            /
            {{ row.validEndDate ? formatDate(row.validEndDate) : '--' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="measurementDeviation" label="不确定度" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.measurementDeviation || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="measurementResult" label="结果" width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.measurementResult || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="performanceIndex" label="性能指标" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.performanceIndex || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="measurementRemark" label="备注" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.measurementRemark || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="fileNames" label="附件" width="220">
        <template #default="{ row }">
          <div v-if="row.fileList.length === 0">--</div>
          <el-popover v-else placement="top" :width="200" popper-class="uploadFilePopper" trigger="hover">
            <template #reference>
              <div class="nowrap overHidden blue-color">{{ row.fileList.map(item => item.fileName).toString() }}</div>
            </template>
            <div
              v-for="(item, index) in row.fileList"
              :key="index"
              type="text"
              class="blue-color uploadFileBtn"
              @click="uploadFile(item)"
            >
              {{ item.fileName }}
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
        v-if="detailData.isEquipmentMetering && getPermissionBtn('measuringUnitBtn')"
        fixed="right"
        class-name="fixed-right"
        prop="status"
        label="操作"
        width="80"
      >
        <template #default="{ row }">
          <span v-if="row.status" class="blue-color" @click="handleChangeStatus(row)">作废</span>
          <span v-else>已作废</span>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-model="dialogVisible"
      title="新增计量信息"
      :close-on-click-modal="false"
      width="800px"
      custom-class="submit_dialog"
    >
      <el-form
        v-if="dialogVisible"
        ref="ruleForm"
        v-loading="dialogLoading"
        :model="formData"
        label-position="top"
        label-width="110px"
      >
        <el-row>
          <el-col :span="11">
            <el-form-item
              label="负责人："
              prop="responsibilityId"
              :rules="{ required: true, message: '请选择负责人', trigger: 'change' }"
            >
              <el-select
                v-model="formData.responsibilityId"
                placeholder="请选择负责人"
                clearable
                filterable
                size="small"
              >
                <el-option v-for="item in useList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="1">
            <el-form-item
              label="计量单位："
              prop="companyAddress"
              :rules="{ required: true, message: '请输入单位', trigger: 'change' }"
            >
              <el-input
                v-model="formData.companyAddress"
                v-trim
                v-focus
                type="text"
                maxlength="100"
                size="small"
                placeholder="请输入单位"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item
              label="日期："
              prop="measurementDate"
              :rules="{ required: true, message: '请选择日期', trigger: 'change' }"
            >
              <el-date-picker
                v-model="formData.measurementDate"
                type="date"
                size="small"
                placeholder="请选择日期"
                @change="handleChangeDate"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="detailData.isEquipmentMetering" :span="12" :offset="1">
            <el-form-item
              label="检定有效期："
              prop="submitTime"
              :rules="{ required: true, message: '请选择检定有效期', trigger: 'change' }"
            >
              <el-date-picker
                v-model="formData.submitTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                @change="handleChangeDate2"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <el-form-item
              label="不确定度："
              prop="measurementDeviation"
              :rules="{ required: true, message: '请输入不确定度', trigger: 'change' }"
            >
              <el-input
                v-model="formData.measurementDeviation"
                v-trim
                type="text"
                maxlength="100"
                placeholder="请输入不确定度"
                size="small"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="1">
            <el-form-item
              label="结果："
              prop="measurementResult"
              :rules="{ required: true, message: '请选择结果', trigger: 'change' }"
            >
              <el-radio-group v-model="formData.measurementResult">
                <el-radio :label="'合格'">合格</el-radio>
                <el-radio :label="'矫正后可用'">矫正后可用</el-radio>
                <el-radio :label="'不合格'">不合格</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="性能指标：" prop="performanceIndex">
          <el-input
            v-model="formData.performanceIndex"
            type="textarea"
            maxlength="300"
            :rows="2"
            placeholder="请输入性能指标"
          />
        </el-form-item>
        <el-form-item label="备注：" prop="measurementRemark">
          <el-input
            v-model="formData.measurementRemark"
            type="textarea"
            maxlength="300"
            :rows="2"
            placeholder="请输入备注"
          />
        </el-form-item>
        <el-form-item>
          <el-upload
            class="upload-demo"
            :action="uploadAction"
            :headers="headerconfig"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :on-exceed="handleExceed"
            :on-success="uploadSuccess"
            :file-list="formData.fileList"
            :before-upload="beforeUpload"
          >
            <el-button size="small" type="primary" plain @keyup.prevent @keydown.enter.prevent>上传文件</el-button>
            <template #tip>
              <span class="el-upload__tip"> 单个文件大小不能超过20M </span>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button size="small" :loading="dialogLoading" @click="dialogVisible = false">取 消</el-button>
          <el-button size="small" :loading="dialogLoading" type="primary" @click="onSubmit()">确 认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { reactive, toRefs, watch, getCurrentInstance, ref } from 'vue';
import { getPermissionBtn, getNameByid } from '@/utils/common';
import { changeEqStatus, addMeasuring, downLoadFile } from '@/api/equipment';
import { deviceMeasurementUploadUrl } from '@/api/uploadAction';
import { formatDate, addMonth } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo, getToken } from '@/utils/auth';
import UserTag from '@/components/UserTag';
import store from '@/store';
export default {
  name: 'DetailMeasuring',
  components: { UserTag },
  props: {
    list: {
      type: Array,
      default: function () {
        return [];
      }
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      dialogVisible: false,
      dialogLoading: false,
      formData: {
        responsibilityId: getLoginInfo().accountId,
        fileList: []
      },
      headerconfig: {
        Authorization: getToken()
      },
      uploadAction: deviceMeasurementUploadUrl(),
      detailData: {},
      ruleForm: ref(),
      tableList: [],
      useList: store.state.common.nameList
    });
    watch(props, newValue => {
      state.tableList = props.list;
      state.detailData = props.detailData;
    });
    const isInvalid = (row, rowIndex) => {
      if (row.row.status) {
        return '';
      } else {
        return 'invalided';
      }
    };
    const handleAdd = () => {
      state.dialogVisible = true;
      state.formData = {
        responsibilityId: getLoginInfo().accountId,
        fileList: []
      };
    };
    const handleChangeStatus = row => {
      if (
        state.tableList.filter(item => {
          return item.status;
        }).length === 1
      ) {
        proxy.$message.closeAll();
        proxy.$message.warning('计量信息不可全部作废');
        return false;
      }
      changeEqStatus({ id: row.id, status: row.status ? 0 : 1, deviceId: state.detailData.id }).then(res => {
        if (res) {
          state.tableList = res.data.data;
          proxy.$message.success(res.data.message);
        }
      });
    };
    const handleChangeDate = date => {
      if (date) {
        state.formData.validBeginDate = formatDate(date);
        if (state.detailData.measurementCycleUnitType === 1) {
          if (state.detailData.measurementCycle) {
            const newDate = new Date(addMonth(formatDate(date), state.detailData.measurementCycle));
            state.formData.validEndDate = formatDate(newDate);
          } else {
            state.formData.validEndDate = formatDate(date);
          }
        } else {
          state.formData.validEndDate = new Date(
            date.getTime() + 3600 * 1000 * 24 * 365 * state.detailData.measurementCycle
          );
        }
        state.formData.submitTime = [state.formData.validBeginDate, state.formData.validEndDate];
        state.formData.measurementDate = formatDate(date);
      }
    };
    const handleChangeDate2 = date => {
      if (date) {
        state.formData.validBeginDate = formatDate(date[0]);
        state.formData.validEndDate = formatDate(date[1]);
      }
    };
    const onSubmit = () => {
      proxy.$refs['ruleForm'].validate(valid => {
        if (valid) {
          state.dialogLoading = true;
          addMeasuring({ deviceId: state.detailData.id, ...state.formData }).then(res => {
            state.dialogLoading = false;
            if (res) {
              state.dialogVisible = false;
              state.tableList = res.data.data;
              proxy.$message.success(res.data.message);
            }
          });
        } else {
          return false;
        }
      });
    };
    const uploadSuccess = (res, file, fileList) => {
      if (res.code === 200) {
        state.formData.fileList.push({ name: res.data.fileName, ...res.data });
      } else {
        proxy.$message.error(res.message);
      }
    };
    const beforeUpload = file => {
      const fileSize = file.size / 1024 / 1024 < 20;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过20M');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };
    const handlePreview = file => {};
    const handleExceed = (files, fileList) => {
      proxy.$message.warning('单次只能上传一个附件');
    };
    const beforeRemove = (file, fileList) => {};
    const uploadFile = file => {
      downLoadFile(file.id).then(res => {
        if (res.data.code === 200) {
          window.open(res.data.data.url);
        }
      });
    };
    const handleRemove = (file, fileList) => {
      state.formData.fileList = fileList;
    };
    return {
      ...toRefs(state),
      getNameByid,
      isInvalid,
      uploadFile,
      handleChangeDate,
      handleChangeDate2,
      handleChangeStatus,
      uploadSuccess,
      beforeUpload,
      formatDate,
      addMonth,
      handleAdd,
      drageHeader,
      onSubmit,
      handleRemove,
      handlePreview,
      handleExceed,
      beforeRemove,
      getLoginInfo,
      getPermissionBtn
    };
  }
};
</script>

<style lang="scss" scoped>
.textLeft {
  text-align: left;
}
.add-btn {
  margin-bottom: 20px;
}
.overHidden {
  overflow: hidden;
}
:deep(.el-table) {
  .invalided {
    color: #c0c4cc;
  }
}
.uploadFileBtn {
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  line-height: 32px;
}
.el-upload__tip {
  display: inline-block;
  margin-left: 10px;
}
:deep(.el-form-item__error) {
  padding-top: 0;
}
:deep(.el-form-item--medium .el-form-item__label) {
  line-height: 24px;
}
:deep(.el-select) {
  width: 100%;
}

:deep(.el-date-editor--daterange.el-input__inner) {
  width: 100%;
}
:deep(.el-date-editor.el-input) {
  width: 100%;
}
:deep(.el-upload-list) {
  max-height: 153px;
  overflow-y: auto;
}
</style>
<style lang="scss">
.detail-measuring {
  .format-height-table2.el-table {
    .el-table__body-wrapper {
      max-height: calc(100vh - 565px) !important;
    }
    .el-table__fixed-body-wrapper {
      max-height: calc(100vh - 565px) !important;
    }
  }
}
</style>
