<template>
  <!-- 近30天原材料合格率 -->
  <!-- 近30天半成品合格率 -->
  <!-- 近30天成品合格率 -->
  <el-carousel direction="vertical" :autoplay="true" :interval="5000">
    <el-carousel-item>
      <el-row>
        <el-col :span="24">
          <div class="box-top">
            <h1 class="inBlock">近30天原材料合格率</h1>
            <div class="top-right inBlock">{{ formatDate(recentlyThirty) }} ~ {{ formatDate(new Date()) }}</div>
          </div>
          <div class="box-Center">
            <img class="leftBorder" src="@/assets/img/dataBoard/borderDecorate.png" alt="" />
            <LineBarPieChart :option="qualifiedRateOptionFirst" :width="'100%'" :height="'100%'" />
          </div>
        </el-col>
      </el-row>
    </el-carousel-item>
    <el-carousel-item>
      <el-row>
        <el-col :span="24">
          <div class="box-top">
            <h1 class="inBlock">近30天半成品合格率</h1>
            <div class="top-right inBlock">{{ formatDate(recentlyThirty) }} ~ {{ formatDate(new Date()) }}</div>
          </div>
          <div class="box-Center">
            <img class="leftBorder" src="@/assets/img/dataBoard/borderDecorate.png" alt="" />
            <LineBarPieChart :option="qualifiedRateOptionSecond" :width="'100%'" :height="'100%'" />
          </div>
        </el-col>
      </el-row>
    </el-carousel-item>
    <el-carousel-item>
      <el-row>
        <el-col :span="24">
          <div class="box-top">
            <h1 class="inBlock">近30天成品合格率</h1>
            <div class="top-right inBlock">{{ formatDate(recentlyThirty) }} ~ {{ formatDate(new Date()) }}</div>
          </div>
          <div class="box-Center">
            <img class="leftBorder" src="@/assets/img/dataBoard/borderDecorate.png" alt="" />
            <LineBarPieChart :option="qualifiedRateOptionThird" :width="'100%'" :height="'100%'" />
          </div>
        </el-col>
      </el-row>
    </el-carousel-item>
  </el-carousel>
</template>

<script>
import { reactive, toRefs, ref, watch } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getNameByid } from '@/utils/common';
import { thirtyDataListApi } from '@/api/dataBoard';
import { formatterTips } from '../../func/formatter';
import { getAxisInterval } from '../../func/calculate';
import LineBarPieChart from '@/components/LineBarPieChart';

export default {
  name: 'NearlyThirthDays',
  components: { LineBarPieChart },
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  setup(props, context) {
    const state = reactive({
      recentlyThirty: new Date().getTime() - 3600 * 1000 * 24 * 30 // 最近30天
    });
    watch(props, newValue => {});
    // 近30天原材料合格率
    const qualifiedRateOptionFirst = ref({
      legend: {
        type: 'plain',
        show: true,
        right: 100,
        top: 18,
        textStyle: {
          color: '#7AD0FF',
          fontSize: 12
        }
      },
      tooltip: {
        show: true,
        backgroundColor: '#4791FF',
        borderColor: 'transparent',
        formatter: params => {
          return formatterTips(params);
        },
        textStyle: {
          color: '#fff',
          fontSize: 12,
          fontWeight: 400
        },
        trigger: 'axis'
      },
      grid: {
        width: 'auto',
        left: '6%',
        right: '3%',
        bottom: '14.5%'
      },
      xAxis: {
        type: 'category',
        data: [],
        axisLine: {
          lineStyle: {
            color: '#7AD0FF'
          }
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '',
          splitLine: {
            show: true,
            lineStyle: {
              color: '#5397CE'
            }
          },
          nameTextStyle: {
            color: '#7AD0FF',
            fontSize: '12'
          },
          axisLine: {
            lineStyle: {
              color: '#7AD0FF'
            }
          }
        },
        {
          type: 'value',
          name: '合格率',
          splitLine: {
            show: true,
            lineStyle: {
              color: '#5397CE'
            }
          },
          nameTextStyle: {
            color: '#7AD0FF',
            fontSize: '12'
          },
          axisLine: {
            lineStyle: {
              color: '#7AD0FF'
            }
          }
        }
      ],
      series: [
        {
          name: '提交报告数',
          data: [],
          type: 'bar',
          labelLine: {
            show: true,
            lineStyle: {
              color: '#37a2da'
            }
          },
          itemStyle: {
            borderRadius: [4, 4, 0, 0]
          }
        },
        {
          name: '合格报告数',
          data: [],
          type: 'bar',
          labelLine: {
            show: true,
            lineStyle: {
              color: '#67e0e3'
            }
          },
          itemStyle: {
            borderRadius: [4, 4, 0, 0]
          }
        },
        {
          name: '合格率',
          data: [],
          yAxisIndex: 1,
          type: 'line',
          symbolSize: 5,
          borderColor: '#ffdb5c',
          symbol: 'circle',
          color: '#ffdb5c',
          animation: true,
          lineStyle: {
            color: '#ffdb5c'
          },
          emphasis: {
            scale: true
          },
          itemStyle: {
            color: '#ffdb5c',
            borderWidth: 2,
            borderColor: '#ffdb5c'
          }
        }
      ]
    });
    // 近30天原材料合格率
    const qualifiedRateOptionSecond = ref({
      legend: {
        type: 'plain',
        show: true,
        right: 100,
        top: 18,
        textStyle: {
          color: '#7AD0FF',
          fontSize: 12
        }
      },
      tooltip: {
        show: true,
        backgroundColor: '#4791FF',
        borderColor: 'transparent',
        formatter: params => {
          return formatterTips(params);
        },
        textStyle: {
          color: '#fff',
          fontSize: 12,
          fontWeight: 400
        },
        trigger: 'axis'
      },
      grid: {
        width: 'auto',
        left: '6%',
        right: '3%',
        bottom: '14.5%'
      },
      xAxis: {
        type: 'category',
        data: [],
        axisLine: {
          lineStyle: {
            color: '#7AD0FF'
          }
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '',
          splitLine: {
            show: true,
            lineStyle: {
              color: '#5397CE'
            }
          },
          nameTextStyle: {
            color: '#7AD0FF',
            fontSize: '12'
          },
          axisLine: {
            lineStyle: {
              color: '#7AD0FF'
            }
          }
        },
        {
          type: 'value',
          name: '合格率',
          splitLine: {
            show: true,
            lineStyle: {
              color: '#5397CE'
            }
          },
          nameTextStyle: {
            color: '#7AD0FF',
            fontSize: '12'
          },
          axisLine: {
            lineStyle: {
              color: '#7AD0FF'
            }
          }
        }
      ],
      series: [
        {
          name: '提交报告数',
          data: [],
          type: 'bar',
          labelLine: {
            show: true,
            lineStyle: {
              color: '#37a2da'
            }
          },
          itemStyle: {
            borderRadius: [4, 4, 0, 0]
          }
        },
        {
          name: '合格报告数',
          data: [],
          type: 'bar',
          labelLine: {
            show: true,
            lineStyle: {
              color: '#67e0e3'
            }
          },
          itemStyle: {
            borderRadius: [4, 4, 0, 0]
          }
        },
        {
          name: '合格率',
          data: [],
          yAxisIndex: 1,
          type: 'line',
          symbolSize: 5,
          borderColor: '#ffdb5c',
          symbol: 'circle',
          color: '#ffdb5c',
          animation: true,
          lineStyle: {
            color: '#ffdb5c'
          },
          emphasis: {
            scale: true
          },
          itemStyle: {
            color: '#ffdb5c',
            borderWidth: 2,
            borderColor: '#ffdb5c'
          }
        }
      ]
    });
    // 近30天成品合格率
    const qualifiedRateOptionThird = ref({
      legend: {
        type: 'plain',
        show: true,
        right: 100,
        top: 18,
        textStyle: {
          color: '#7AD0FF',
          fontSize: 12
        }
      },
      tooltip: {
        show: true,
        backgroundColor: '#4791FF',
        borderColor: 'transparent',
        formatter: params => {
          return formatterTips(params);
        },
        textStyle: {
          color: '#fff',
          fontSize: 12,
          fontWeight: 400
        },
        trigger: 'axis'
      },
      grid: {
        width: 'auto',
        left: '6%',
        right: '3%',
        bottom: '14.5%'
      },
      xAxis: {
        type: 'category',
        data: [],
        axisLine: {
          lineStyle: {
            color: '#7AD0FF'
          }
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '',
          splitLine: {
            show: true,
            lineStyle: {
              color: '#5397CE'
            }
          },
          nameTextStyle: {
            color: '#7AD0FF',
            fontSize: '12'
          },
          axisLine: {
            lineStyle: {
              color: '#7AD0FF'
            }
          }
        },
        {
          type: 'value',
          name: '合格率',
          splitLine: {
            show: true,
            lineStyle: {
              color: '#5397CE'
            }
          },
          nameTextStyle: {
            color: '#7AD0FF',
            fontSize: '12'
          },
          axisLine: {
            lineStyle: {
              color: '#7AD0FF'
            }
          }
        }
      ],
      series: [
        {
          name: '提交报告数',
          data: [],
          type: 'bar',
          labelLine: {
            show: true,
            lineStyle: {
              color: '#37a2da'
            }
          },
          itemStyle: {
            borderRadius: [4, 4, 0, 0]
          }
        },
        {
          name: '合格报告数',
          data: [],
          type: 'bar',
          labelLine: {
            show: true,
            lineStyle: {
              color: '#67e0e3'
            }
          },
          itemStyle: {
            borderRadius: [4, 4, 0, 0]
          }
        },
        {
          name: '合格率',
          data: [],
          yAxisIndex: 1,
          type: 'line',
          symbolSize: 5,
          borderColor: '#ffdb5c',
          symbol: 'circle',
          color: '#ffdb5c',
          animation: true,
          lineStyle: {
            color: '#ffdb5c'
          },
          emphasis: {
            scale: true
          },
          itemStyle: {
            color: '#ffdb5c',
            borderWidth: 2,
            borderColor: '#ffdb5c'
          }
        }
      ]
    });
    const getThirtyDate = () => {
      const dateList = [];
      const startDate = new Date();
      const endDate = new Date();
      startDate.setDate(endDate.getDate() - 30);
      while (endDate.getTime() - startDate.getTime() >= 0) {
        const month =
          (startDate.getMonth() + 1).toString().length === 1
            ? '0' + (startDate.getMonth() + 1).toString()
            : startDate.getMonth() + 1;
        const day = startDate.getDate().toString().length === 1 ? '0' + startDate.getDate() : startDate.getDate();
        dateList.push(month + '-' + day);
        startDate.setDate(startDate.getDate() + 1);
      }
      qualifiedRateOptionFirst.value.xAxis.data = dateList;
      qualifiedRateOptionSecond.value.xAxis.data = dateList;
      qualifiedRateOptionThird.value.xAxis.data = dateList;
    };
    getThirtyDate();
    // 获取近30天原材料合格率、近30天半成品合格率、近30天成品合格率
    const getThirtyDataList = () => {
      thirtyDataListApi({ term: 30 }).then(res => {
        if (res) {
          const data = res.data.data;
          // 近30天原材料合格率
          qualifiedRateOptionFirst.value.series[0].data = data.purchaseReceiptResponseList.map(item => {
            return item.totalNum;
          });
          qualifiedRateOptionFirst.value.series[1].data = data.purchaseReceiptResponseList.map(item => {
            return item.qualifiedNum;
          });
          qualifiedRateOptionFirst.value.series[2].data = data.purchaseReceiptResponseList.map(item => {
            return item.qualifiedRate.replace('%', '');
          });
          const length1 = qualifiedRateOptionFirst.value.series[1].data.length;
          const leftArray1 = [
            ...qualifiedRateOptionFirst.value.series[0].data,
            ...qualifiedRateOptionFirst.value.series[1].data
          ];
          const axisInfoFirstY1 = getAxisInterval(Math.min(...leftArray1), Math.max(...leftArray1), length1);
          const axisInfoFirstY2 = getAxisInterval(
            Math.min(...qualifiedRateOptionFirst.value.series[2].data),
            Math.max(...qualifiedRateOptionFirst.value.series[2].data),
            length1
          );
          qualifiedRateOptionFirst.value.yAxis[0].min = axisInfoFirstY1.axisMin;
          qualifiedRateOptionFirst.value.yAxis[0].max = axisInfoFirstY1.axisMax;
          qualifiedRateOptionFirst.value.yAxis[1].min = axisInfoFirstY2.axisMin;
          qualifiedRateOptionFirst.value.yAxis[1].max = axisInfoFirstY2.axisMax;
          qualifiedRateOptionFirst.value.yAxis[0].interval = axisInfoFirstY1.axistInterval;
          qualifiedRateOptionFirst.value.yAxis[1].interval = axisInfoFirstY2.axistInterval;
          // 近30天半成品合格率
          qualifiedRateOptionSecond.value.series[0].data = data.progressTestResponseList.map(item => {
            return item.totalNum;
          });
          qualifiedRateOptionSecond.value.series[1].data = data.progressTestResponseList.map(item => {
            return item.qualifiedNum;
          });
          qualifiedRateOptionSecond.value.series[2].data = data.progressTestResponseList.map(item => {
            return item.qualifiedRate.replace('%', '');
          });
          const length2 = qualifiedRateOptionSecond.value.series[1].data.length;
          const leftArray2 = [
            ...qualifiedRateOptionSecond.value.series[0].data,
            ...qualifiedRateOptionSecond.value.series[1].data
          ];
          const axisInfoSecondY1 = getAxisInterval(Math.min(...leftArray2), Math.max(...leftArray2), length2);
          const axisInfoSecondY2 = getAxisInterval(
            Math.min(...qualifiedRateOptionSecond.value.series[2].data),
            Math.max(...qualifiedRateOptionSecond.value.series[2].data),
            length2
          );
          qualifiedRateOptionSecond.value.yAxis[0].min = axisInfoSecondY1.axisMin;
          qualifiedRateOptionSecond.value.yAxis[0].max = axisInfoSecondY1.axisMax;
          qualifiedRateOptionSecond.value.yAxis[1].min = axisInfoSecondY2.axisMin;
          qualifiedRateOptionSecond.value.yAxis[1].max = axisInfoSecondY2.axisMax;
          qualifiedRateOptionSecond.value.yAxis[0].interval = axisInfoSecondY1.axistInterval;
          qualifiedRateOptionSecond.value.yAxis[1].interval = axisInfoSecondY2.axistInterval;
          // 近30天成品合格率
          qualifiedRateOptionThird.value.series[0].data = data.endProductTestResponseList.map(item => {
            return item.totalNum;
          });
          qualifiedRateOptionThird.value.series[1].data = data.endProductTestResponseList.map(item => {
            return item.qualifiedNum;
          });
          qualifiedRateOptionThird.value.series[2].data = data.endProductTestResponseList.map(item => {
            return item.qualifiedRate.replace('%', '');
          });
          const length = qualifiedRateOptionThird.value.series[1].data.length;
          const leftArray = [
            ...qualifiedRateOptionThird.value.series[0].data,
            ...qualifiedRateOptionThird.value.series[1].data
          ];
          const axisInfoThirdY1 = getAxisInterval(Math.min(...leftArray), Math.max(...leftArray), length);
          const axisInfoThirdY2 = getAxisInterval(
            Math.min(...qualifiedRateOptionThird.value.series[2].data),
            Math.max(...qualifiedRateOptionThird.value.series[2].data),
            length
          );
          qualifiedRateOptionThird.value.yAxis[0].min = axisInfoThirdY1.axisMin;
          qualifiedRateOptionThird.value.yAxis[0].max = axisInfoThirdY1.axisMax;
          qualifiedRateOptionThird.value.yAxis[1].min = axisInfoThirdY2.axisMin;
          qualifiedRateOptionThird.value.yAxis[1].max = axisInfoThirdY2.axisMax;
          qualifiedRateOptionThird.value.yAxis[0].interval = axisInfoThirdY1.axistInterval;
          qualifiedRateOptionThird.value.yAxis[1].interval = axisInfoThirdY2.axistInterval;
        }
      });
    };
    getThirtyDataList();
    return {
      ...toRefs(state),
      getNameByid,
      getThirtyDate,
      getThirtyDataList,
      qualifiedRateOptionFirst,
      qualifiedRateOptionSecond,
      qualifiedRateOptionThird,
      formatDate
    };
  }
};
</script>
<style lang="scss" scoped>
@import '../data-board.scss';
</style>
