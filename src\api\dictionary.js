import request from '@/utils/request';
// 字典列表
export function getDictionaryList(data) {
  return request({
    url: '/api-dictionary/dictionary/dictionary/list',
    method: 'post',
    data
  });
}
// 字典表详情
export function getDictionaryDetail(id) {
  return request({
    url: `/api-dictionary/dictionary/dictionary/info/${id}`,
    method: 'get'
  });
}
// 字典的新增删除修改
export function saveEditDelete(data) {
  return request({
    url: `/api-dictionary/dictionary/dictionary/save`,
    method: 'post',
    data
  });
}

// 获取字典项，包括禁用的
export function getDictionary(code) {
  return request({
    url: `/api-dictionary/dictionary/dictionary/getAllByCode/${code}`,
    method: 'get'
  });
}

// 获取字典项，不包括禁用的
export function getEnabledDictionary(code) {
  return request({
    url: `/api-dictionary/dictionary/dictionary/getByCode/${code}`,
    method: 'get'
  });
}
