<template>
  <!-- 产品服务 -->
  <el-popover
    v-if="show"
    ref="showHeaderRef"
    placement="bottom-start"
    :width="popoverWidth"
    trigger="click"
    :offset="0"
    :show-arrow="false"
    popper-class="test-home-header-popper"
    @show="openMenu"
    @hide="openMenu"
  >
    <template #reference>
      <span class="header-search-btn" :class="showS === false ? 'active' : ''">
        <i class="el-icon-s-grid" />产品服务
      </span>
    </template>
    <template #default>
      <!-- <el-input v-model="submitText" placeholder="请输入名称/关键词查找菜单" class="seach-menu" prefix-icon="el-icon-search" clearable @keydown.enter="onSubmit" @input="onSubmit" @clear="onSubmit" /> -->
      <div class="menu-list" :style="columnCount">
        <div v-for="item in menuOptions" :key="item" class="list-col">
          <template v-if="item.meta.showOneMenu">
            <div class="list-childs" @click="goPage(item.children[0])">
              <span class="icon" :class="item.children[0]?.meta?.icon" />
              {{ item.children[0]?.meta?.visible ? item.children[0]?.meta?.title : '' }}
            </div>
          </template>
          <template v-else>
            <div class="menu-item">
              <span class="icon" :class="item?.meta?.icon" />
              <span v-if="item.children.length > 0">{{ item?.meta?.title }}</span>
              <span v-else class="h-c-s" @click="goPage(item)">{{ item?.meta?.title }}</span>
            </div>
            <div v-for="child in item.children" :key="child" class="list-childs" @click="goPage(child)">
              {{ child?.meta?.visible ? child?.meta?.title : '' }}
            </div>
          </template>
        </div>
      </div>
    </template>
  </el-popover>
</template>

<script>
import { nextTick, reactive, toRefs, watch, ref } from 'vue';
// import { ElMessage } from 'element-plus'
// import { useStore } from 'vuex'
import { getMenuList } from '@/utils/auth';
import { isExternal } from '@/utils/validate';
import router from '@/router';
import { transformMenusToRoutes } from '@/utils/permission';
// import _ from 'lodash'

export default {
  name: 'ProductServe',
  components: {},
  props: {
    show: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    // const { appContext } = getCurrentInstance()
    const datas = reactive({
      showS: true,
      popoverWidth: 190,
      menuOptions: [],
      showHeaderRef: ref(),
      columnCount: {
        'column-count': 5
      }
    });

    watch(
      () => props.show,
      newValue => {
        // console.log(newValue)
        if (newValue) {
          nextTick(() => {
            if (getMenuList().length > 0) {
              const currentWidth = document.documentElement.clientWidth;
              datas.popoverWidth = 190 * getMenuList().length;
              if (datas.popoverWidth > currentWidth) {
                datas.popoverWidth = currentWidth - 10;
              }
              // console.log(currentWidth, datas.popoverWidth)
              var num = (datas.popoverWidth - 120) / 140;
              // console.log(num)
              datas.columnCount = {
                'column-count': parseInt(num)
              };
            }
            datas.menuOptions = transformMenusToRoutes([], getMenuList());
            // console.log(datas.menuOptions)
          });
        }
      },
      { deep: true }
    );

    // 打开产品服务菜单
    const openMenu = () => {
      // console.log(datas.showHeaderRef)
      datas.showS = !datas.showS;
    };
    // 跳转页面
    const goPage = item => {
      if (isExternal(item.path)) {
        window.open(item.path);
      } else {
        router.push({ path: item.path });
      }
      datas.showHeaderRef.hide();
    };
    // 查询
    // const newArr = JSON.parse(JSON.stringify(datas.menuOptions))
    const onSubmit = () => {
      // console.log(datas.submitText)
      const currentArr = [];
      if (datas.submitText) {
        datas.menuOptions.forEach((ps, index) => {
          currentArr.push(ps);
          const childList = [];
          if (ps.subMenus && ps.subMenus.length > 0) {
            ps.subMenus.forEach(sub => {
              if (sub.name.indexOf(datas.submitText) !== -1) {
                childList.push(sub);
              }
            });
          }
          currentArr[index].subMenus = childList;
        });
        datas.menuOptions = currentArr;
      } else {
        datas.menuOptions = getMenuList();
      }
      // console.log(datas.menuOptions)
    };

    return { ...toRefs(datas), openMenu, goPage, onSubmit };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss">
.header-search-btn {
  cursor: pointer;
  line-height: 48px;
  &:hover {
    background: rgba(0, 0, 0, 0.05);
  }
}
.test-home-header-popper {
  // background: #222 !important;
  border: 0px !important;
  border-radius: initial !important;
  // max-height: 500px;
  padding: 40px 60px 0 !important;
  box-shadow: 0 0 10px 4px rgba(0, 0, 0, 0.05) !important;
  .seach-menu {
    .el-input__inner {
      background: #1b1b28;
      border-top: 0px;
      border-left: 0px;
      border-right: 0px;
      border-bottom-color: #606266;
      border-radius: initial;
    }
    input::-webkit-input-placeholder {
      color: #c0c4cc;
    }
    input::-moz-placeholder {
      color: #c0c4cc;
    }

    input:-moz-placeholder {
      color: #c0c4cc;
    }
    input:-ms-input-placeholder {
      color: #c0c4cc;
    }
  }
  .menu-list {
    // display: flex;
    // flex-wrap: wrap;
    // flex-direction: row;
    // justify-content: space-between;
    // max-height: calc(500px - 50px);
    // overflow: auto;
    // align-content: flex-start;
    column-count: 5;
    column-gap: 16px;
    .list-col {
      width: 120px;
      padding: 0 0 40px;
      // flex-direction: column;
      // display: flex;
      break-inside: avoid-column;
      .list-childs {
        position: relative;
        color: $tes-font1;
        font-size: 14px;
        line-height: 28px;
        padding-left: 22px;
        cursor: pointer;
      }
      .list-childs:hover {
        color: $tes-primary;
      }
      .star {
        position: absolute;
        right: -4px;
        top: 7px;
      }
    }
    .menu-item {
      color: $tes-font2;
      font-size: 14px;
      line-height: 22px;
      // padding-bottom: 20px;
      cursor: pointer;
      .h-c-s {
        color: $tes-font1;
      }
      .icon {
        padding-right: 8px;
      }
    }
    .menu-item:hover {
      color: $tes-primary;
    }
  }
}
</style>
