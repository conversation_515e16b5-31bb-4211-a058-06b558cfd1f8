<template>
  <!-- 原材料质保书 -->
  <ListLayout>
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="param">
          <div style="width: 42vw">
            <CombinationQuery
              :field-list="searchFieldList"
              :field-tip="fieldTip"
              @get-query-info="getQueryInfo"
              @reset-search="reset"
            />
          </div>
        </el-form-item>
      </el-form>
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="16">
          <el-radio-group v-model="formInline.status" size="small" @change="getList()">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button label="0">待上传</el-radio-button>
            <el-radio-button label="1">已上传</el-radio-button>
          </el-radio-group>
        </el-col>
        <el-col :span="8" style="text-align: right">
          <TableColumnView binding-menu="MaterialQualityBook" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="tableList"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="item.fieldType === fieldTypesEnum.Link">
              <span class="nowrap blue-color" @click="handlePush(row)">{{ row[item.fieldKey] || '--' }}</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Person">
              <UserTag :name="row[item.fieldKey] || '--'" />
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Status">
              <el-tag size="small" effect="dark" :type="statusJSON?.[row.status.toString()]?.type">{{
                statusJSON?.[row.status.toString()]?.label
              }}</el-tag>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Date">
              <span v-if="row[item.fieldKey]">{{ formatDate(row[item.fieldKey]) }}</span>
              <span v-else>--</span>
            </template>
            <span v-else>{{ row[item.fieldKey] || '--' }}</span>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" :width="colWidth.operation" prop="caozuo" fixed="right" class-name="fixed-right">
        <template #default="{ row }">
          <div>
            <span
              v-if="row.status == 0 && getPermissionBtn('materialQualityBookEdit')"
              class="blue-color"
              @click="handleEdit(row)"
              >编辑</span
            >
            <span v-else class="blue-color" @click="iaDetail(row)">查看</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs } from 'vue';
import router from '@/router/index.js';
// import { useRoute } from 'vue-router'
import Pagination from '@/components/Pagination';
import UserTag from '@/components/UserTag';
import { inspectionrawmaterialquality } from '@/api/material-quality-book';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import ListLayout from '@/components/ListLayout';
import { colWidth } from '@/data/tableStyle';
import CombinationQuery from '@/components/CombinationQuery';
import TableColumnView from '@/components/TableColumnView';
import { getInspectionList } from '@/api/inspection-application';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';

export default {
  name: 'MaterialQualityBook',
  components: { Pagination, UserTag, ListLayout, CombinationQuery, TableColumnView },
  setup() {
    // const { proxy } = getCurrentInstance();
    const editFrom = ref(null);
    const state = reactive({
      currentAccountId: getLoginInfo().accountId,
      statusJSON: {
        0: { label: '待上传', type: 'warning' },
        1: { label: '已上传', type: 'success' }
      },
      searchFieldList: [],
      fieldTip: '',
      formInline: {
        param: '',
        tableQueryParamList: [],
        type: '',
        status: ''
      },
      tableColumns: [],
      tableList: [],
      radioData: '全部',
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: ''
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      tableRef: ref()
    });

    // 重置
    function reset() {
      editFrom.value.resetFields();
      state.formInline = {
        param: '',
        tableQueryParamList: [],
        type: '',
        status: ''
      };
      state.radioData = '全部';
      state.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: null
      };
      getList();
    }
    // 排序
    const sortChange = data => {
      const { prop, order } = data;
      state.listQuery.orderBy = prop;
      if (order === 'ascending') {
        state.listQuery.isAsc = true;
      } else if (order === 'descending') {
        state.listQuery.isAsc = false;
      } else {
        state.listQuery.isAsc = null;
      }
    };

    // 点击编辑
    const handleEdit = row => {
      router.push({ path: '/qualityManagement/materialQualityBook/detail', query: { ...row } });
    };
    // 查看
    const iaDetail = row => {
      router.push({ path: '/qualityManagement/materialQualityBook/check', query: { ...row } });
    };
    // 查看
    const handlePush = row => {
      getInspectionList({ param: `${row.inspectionNo}` }).then(res => {
        if (res && res.status === 200) {
          if (res.data.data.list.length > 0) {
            router.push({
              name: 'SampleOrderApplicationMaterial',
              query: { id: res.data.data.list[0].id, flag: 1 }
            });
          }
        }
      });
    };
    // #region 组合查询

    const getQueryInfo = info => {
      state.formInline.param = info.param;
      state.formInline.tableQueryParamList = info.tableQueryParamList;
      getList();
    };

    // #endregion

    const onUpdateColumns = columns => {
      state.tableKey = state.tableKey + 1;
      state.tableColumns = columns;
      state.searchFieldList = columns.filter(item => {
        return item.isQuery == 1;
      });
      state.fieldTip = state.searchFieldList.map(item => item.fieldName).join('/');
    };

    const getList = async query => {
      const params = { ...state.formInline };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoading = true;
      const { data } = await inspectionrawmaterialquality(params).finally((state.listLoading = false));
      if (data) {
        state.total = data.data.totalCount;
        state.tableList = data.data.list;
      }
    };
    getList();

    return {
      handleEdit,
      drageHeader,
      formatDate,
      getNameByid,
      sortChange,
      editFrom,
      ...toRefs(state),
      reset,
      getPermissionBtn,
      iaDetail,
      handlePush,
      colWidth,
      getQueryInfo,
      getList,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  }
};
</script>
<style lang="scss" scoped>
.el-form {
  .el-form-item {
    margin-bottom: 0;
  }
}
.blue-color {
  color: $tes-primary;
  cursor: pointer;
}
</style>
