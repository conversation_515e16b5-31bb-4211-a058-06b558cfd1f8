<!-- 新增请假 -->
<template>
  <el-dialog v-model="dialogShow" title="新增请假" :close-on-click-modal="false" width="500px" @close="handleClose">
    <el-form
      v-if="dialogShow"
      ref="formRef"
      v-loading="dialogLoading"
      :model="formData"
      label-width="90px"
      label-position="right"
      size="small"
    >
      <el-form-item
        label="请假人员："
        prop="employeeId"
        :rules="{ required: true, message: '请选择请假人员', trigger: 'change' }"
      >
        <el-select
          v-model="formData.employeeId"
          placeholder="请选择请假人员"
          clearable
          filterable
          style="width: 100%"
          @change="handleChangeUser"
        >
          <el-option v-for="item in userList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-row>
        <el-col :span="11">
          <el-form-item
            label="开始时间："
            prop="startTimeDate"
            :rules="{ required: true, message: '请选择开始时间', trigger: 'change' }"
          >
            <el-date-picker
              v-model="formData.startTimeDate"
              type="date"
              placeholder="请选择日期"
              @change="handleChangeTime('startTime')"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" :offset="1">
          <el-form-item
            label=""
            prop="startTimeHours"
            :rules="{ required: true, message: '请选择开始时间', trigger: 'change' }"
          >
            <el-time-select
              v-model="formData.startTimeHours"
              start="00:00"
              step="0:30"
              end="23:30"
              placeholder="开始时间"
              @change="handleChangeTime('startTime')"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item
            label="结束时间："
            prop="endTimeDate"
            :rules="{ required: true, message: '请选择结束时间', trigger: 'change' }"
          >
            <el-date-picker
              v-model="formData.endTimeDate"
              type="date"
              placeholder="请选择日期"
              @change="handleChangeTime('endTime')"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" :offset="1">
          <el-form-item
            label=""
            prop="endTimeHours"
            :rules="{ required: true, message: '请选择结束时间', trigger: 'change' }"
          >
            <el-time-select
              v-model="formData.endTimeHours"
              start="00:00"
              step="0:30"
              end="23:30"
              placeholder="结束时间"
              @change="handleChangeTime('endTime')"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, ref, toRefs, watch, getCurrentInstance } from 'vue';
import { colWidth } from '@/data/tableStyle';
import { getNameByid } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { saveEmployeeLeave } from '@/api/dutyTime';
import store from '@/store';
export default {
  name: 'DialogAddLeave',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      formData: {}, // 表单详情
      dialogLoading: false,
      userList: store.state.common.nameList,
      dialogShow: false,
      formRef: ref(),
      formLoading: false,
      ruleForm: ref()
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.formData = {
          status: 0
        };
      }
    });
    const handleChangeTime = timeType => {
      state.formData[timeType] =
        formatDate(state.formData[timeType + 'Date']) + ' ' + state.formData[timeType + 'Hours'];
    };
    const handleChangeUser = val => {
      if (val) {
        state.formData.name = state.userList.filter(item => {
          return item.id === val;
        })[0].name;
      } else {
        state.formData.name = '';
      }
    };
    const onSubmit = () => {
      proxy.$refs['formRef'].validate(valid => {
        if (valid) {
          const params = JSON.parse(JSON.stringify(state.formData));
          params.startTime = params.startTime + ':00';
          params.endTime = params.endTime + ':00';
          delete params.startTimeDate;
          delete params.startTimeHours;
          delete params.endTimeDate;
          delete params.endTimeHours;
          if (new Date(params.endTime).getTime() <= new Date(params.startTime).getTime()) {
            proxy.$message.warning('结束时间不能小于结束时间');
            return false;
          }
          saveEmployeeLeave(params).then(res => {
            if (res) {
              proxy.$message.success(res.data.message);
              context.emit('closeDialog', true);
            }
          });
        } else {
          return false;
        }
      });
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog', false);
    };
    // 取消请假
    const cancelHoliday = () => {};
    const handleAddHoliday = () => {};
    return {
      ...toRefs(state),
      handleChangeTime,
      handleChangeUser,
      onSubmit,
      handleClose,
      handleAddHoliday,
      cancelHoliday,
      getNameByid,
      colWidth
    };
  }
};
</script>
<style lang="scss" scoped>
.holidayTop {
  margin-bottom: 15px;
}
:deep(.el-date-editor--datetimerange.el-input__inner) {
  width: 100%;
}
</style>
