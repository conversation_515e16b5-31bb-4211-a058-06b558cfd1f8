import request from '@/utils/request';
import qs from 'qs';
const formHeaders = {
  'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
};

/**
 * 获取平台配置列表
 * @returns
 */
export function getPlatformConfigList() {
  return request({
    url: `/api-user/platformConfig/getList`,
    method: 'get'
  });
}

/**
 * 获取平台跳转url参数
 * @returns
 */
export function getPlatformUrlParams(platformId, targetPath = '') {
  const apiUrl = targetPath
    ? `/api-user/platformConfig/skipPlatform?platformId=${platformId}`
    : `/api-user/platformConfig/skipPlatform?platformId=${platformId}&targetUrl=${targetPath}`;
  return request({
    url: apiUrl,
    method: 'get'
  });
}

/**
 * 获取平台参数解密结果
 * http://*************:8800/doc.html#/user/%E5%B9%B3%E5%8F%B0%E9%85%8D%E7%BD%AE%E7%AE%A1%E7%90%86/changeUsingGET
 * @returns
 */
export function getParamDecryption(params) {
  const encodeParams = encodeURIComponent(params);
  return request({
    url: `/api-user/platformConfig/change?ciphertext=${encodeParams}`,
    method: 'get',
    headers: formHeaders
  });
}

export function bindNewPlatform(data) {
  data = qs.stringify(data);
  return request({
    url: `/api-user/platformConfig/bind`,
    method: 'post',
    headers: formHeaders,
    data
  });
}
