import request from '@/utils/request';
// 辅料台账查询列表
export function getList(data) {
  return request({
    url: '/api-diplomat/diplomat/aux-material/product/list',
    method: 'post',
    data
  });
}
// 删除辅料台账
export function deleteMaterialApi(id) {
  return request({
    url: `/api-diplomat/diplomat/aux-material/product/delete/${id}`,
    method: 'get'
  });
}
// 新增编辑辅料台账
export function updateAddMaterialApi(data) {
  return request({
    url: '/api-diplomat/diplomat/aux-material/product/save',
    method: 'post',
    data
  });
}
// 仪器设备详情
export function getDetail(id) {
  return request({
    url: `/api-device/device/device/info/${id}`,
    method: 'get'
  });
}
// 辅料分类树
export function getTree(data) {
  return request({
    url: `/api-diplomat/diplomat/aux-material/category/listTree`,
    method: 'get'
  });
}
// 新增编辑分类树节点
export function addUpdateTreeNode(data) {
  return request({
    url: '/api-diplomat/diplomat/aux-material/category/save',
    method: 'post',
    data
  });
}
// 删除分类树节点
export function deleteTreeNode(id) {
  return request({
    url: `/api-diplomat/diplomat/aux-material/category/delete/${id}`,
    method: 'get'
  });
}
// 辅料台账入库
export function entryStorage(data) {
  return request({
    url: `/api-diplomat/diplomat/aux-material/record/entry`,
    method: 'post',
    data
  });
}
// 辅料台账出库
export function deliveryStorage(data) {
  return request({
    url: `/api-diplomat/diplomat/aux-material/record/delivery`,
    method: 'post',
    data
  });
}
// 下载计量信息附件
export function downLoadFile(id) {
  return request({
    url: `/api-device/device/devicemeasurement/download?fileId=${id}`,
    method: 'get'
  });
}

// 关联仪器设备-新增弹出框-仪器设备列表
export function getDeviceCapabilityList(data) {
  return request({
    url: '/api-device/device/device/deviceCapabilityList',
    method: 'post',
    data
  });
}
// 关联仪器设备 列表
export function getCapabilityDevice(capabilityId) {
  return request({
    url: `/api-capabilitystd/capabilitydevice/findByCapabilityId/${capabilityId}`,
    method: 'get'
  });
}
// 关联仪器设备-保存或更新关联仪器设备
export function saveOrUpdateDevice(data) {
  return request({
    url: '/api-capabilitystd/capabilitydevice/saveOrUpdate',
    method: 'post',
    data
  });
}
// 删除关联仪器设备
export function deleteCapabilityDevice(id) {
  return request({
    url: `/api-capabilitystd/capabilitydevice/delete/${id}`,
    method: 'delete'
  });
}
// 展示分析-设备使用记录列表- 查询所有仪器设备使用记录
export function getDeviceRecordList(data) {
  return request({
    url: '/api-orders/orders/experiment/deviceusage/deviceRecordList',
    method: 'post',
    data
  });
}
// 展示分析-设备使用记录列表- 分页查询所有仪器设备使用记录
export function getDeviceRecordPage(data) {
  return request({
    url: '/api-orders/orders/experiment/deviceusage/deviceRecordPage',
    method: 'post',
    data
  });
}
// 根据Id查询deviceCategoryId
export function getDeviceCategoryId(id) {
  return request({
    url: `/api-device/device/device/getDeviceCategoryId/${id}`,
    method: 'get'
  });
}
// 辅料分类树
export function updateOrderTree(data) {
  return request({
    url: '/api-diplomat/diplomat/aux-material/category/updateOrder',
    method: 'post',
    data
  });
}
// 展示分析-所有样品领用记录列表
export function sampleRecordAllList(data) {
  return request({
    url: '/api-orders/orders/experiment/samplereceive/receiverecordlist',
    method: 'post',
    data
  });
}
// 展示分析-所有样品领用记录列表
export function sampleRecordList(data) {
  return request({
    url: '/api-orders/orders/experiment/samplereceive/receiverecordpage',
    method: 'post',
    data
  });
}
