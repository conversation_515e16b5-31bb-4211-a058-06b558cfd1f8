<template>
  <div class="table-panel">
    <el-table
      ref="tableIERef"
      :key="tableKey"
      v-loading="loading"
      :data="tableData"
      fit
      border
      size="medium"
      class="dark-table base-table format-height-table3"
      @selection-change="handleSelectionChange"
      @select-all="handleSelectAll"
    >
      <el-table-column type="selection" :width="colWidth.checkbox" />
      <el-table-column prop="name" label="仪器设备" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.name }}
        </template>
      </el-table-column>
      <el-table-column prop="model" label="型号规格" :width="colWidth.model" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.model || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="deviceNumber" label="仪器设备编号" :width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.deviceNumber || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="measurementClientName" label="检定/校准机构" :min-width="colWidth.name">
        <template #default="{ row }">
          {{ row.measurementClientName || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="measurementRange" label="测量范围" :min-width="120" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.measurementRange || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="validEndDate" label="有效日期" :width="colWidth.dateranger">
        <template #default="{ row }">
          <span v-if="row.validBeginDate">{{ row.validBeginDate + ' ~ ' + row.validEndDate }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { reactive, toRefs, ref, onMounted, nextTick } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { colWidth } from '@/data/tableStyle';
// import { ElMessage } from 'element-plus'
import _ from 'lodash';
export default {
  name: 'Instruments',
  components: {},
  props: {
    show: {
      type: Boolean,
      default: false
    },
    table: {
      type: Array,
      default: function () {
        return [];
      }
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    const tableIERef = ref();
    const state = reactive({
      tableData: props.table,
      oldtableData: [],
      loading: false,
      tableKey: 'tableKey1',
      paramDevice: {
        deviceysageResponseList: [],
        reportStage: 3
      },
      refeashThis: false
    });

    // 默认选择
    const selectAll = tableData => {
      state.refeashThis = true;
      if (tableData && tableData.length > 0) {
        tableData.forEach((data, index) => {
          if (data.isReport !== false) {
            tableIERef.value.toggleRowSelection(data);
          }
          if (index === tableData.length - 1) {
            state.refeashThis = false;
          }
        });
      }
      context.emit('setInfo', state.tableData);
    };
    const handleSelectAll = val => {
      if (val.length) {
        state.tableData.forEach(item => {
          item.isReport = true;
        });
      } else {
        state.tableData.forEach(item => {
          item.isReport = false;
        });
      }
      context.emit('setInfo', state.tableData);
    };

    // 选择checkbox
    const handleSelectionChange = val => {
      if (state.refeashThis) {
        return false;
      }
      if (state.tableData.length > 0) {
        state.tableData.forEach(r => {
          const hasitem = _.filter(val, res => {
            return res.deviceId === r.deviceId && res.id === r.id;
          });
          if (hasitem.length > 0) {
            r.isReport = true; // 选中
          } else {
            r.isReport = false; // 未选中
          }
        });
      }
      context.emit('setInfo', state.tableData);
    };

    onMounted(() => {
      nextTick(() => {
        if (props.show) {
          state.tableData = props.table;
        }
        selectAll(state.tableData);
      });
    });

    return {
      ...toRefs(state),
      handleSelectionChange,
      formatDate,
      tableIERef,
      colWidth,
      selectAll,
      handleSelectAll
    };
  }
};
</script>

<style lang="scss" scoped>
.table-panel {
  margin-bottom: 60px;
  padding: 20px;
  background: $background-color;
}
:deep(.format-height-table3) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 30.5rem);
    overflow-y: auto;
  }
  .el-table__fixed-body-wrapper {
    max-height: calc(100vh - 31rem);
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  .el-table__fixed-body-wrapper::-webkit-scrollbar {
    display: none;
  }
}
</style>
