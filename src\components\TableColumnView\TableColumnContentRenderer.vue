<template>
  <!-- 文本 -->
  <template v-if="column.fieldType === fieldTypesEnum.Text || !column.fieldType">
    <slot :name="fieldTypesEnum.Text">
      <span>{{ row[column.fieldKey] || '--' }}</span>
    </slot>
  </template>
  <!-- 日期 -->
  <template v-else-if="column.fieldType === fieldTypesEnum.Date">
    <slot :name="fieldTypesEnum.Date">
      <span v-if="row[column.fieldKey]">{{
        format ? formatDateTime(row[column.fieldKey], format) : formatDateTime(row[column.fieldKey])
      }}</span>
      <span v-else>--</span>
    </slot>
  </template>
  <!-- 链接 -->
  <template v-else-if="column.fieldType === fieldTypesEnum.Link">
    <slot :name="fieldTypesEnum.Link">
      <span
        v-if="row[column.fieldKey]"
        v-copy="row[column.fieldKey]"
        class="nowrap blue-color"
        @click="handleLinkClick(column.fieldKey, row)"
        >{{ row[column.fieldKey] }}</span
      >
      <span v-else>--</span>
    </slot>
  </template>
  <!-- 状态 -->
  <template v-else-if="column.fieldType === fieldTypesEnum.Status">
    <slot :name="fieldTypesEnum.Status">
      <el-tag size="small" effect="dark" :type="status.type">{{ status.content }}</el-tag>
    </slot>
  </template>
  <!-- 人员 -->
  <template v-else-if="column.fieldType === fieldTypesEnum.Person">
    <slot :name="fieldTypesEnum.Person">
      <template v-if="row[column.fieldKey]">
        <div v-if="row[column.fieldKey].includes(',')">
          <UserTag v-for="(name, nameIndex) in getNamesByid(row[column.fieldKey])" :key="nameIndex" :name="name" />
        </div>
        <UserTag v-else :name="getNameByid(row[column.fieldKey]) || row[column.fieldKey]" />
      </template>
      <span v-else>--</span>
    </slot>
  </template>
  <!-- 进度 -->
  <template v-else-if="column.fieldType === fieldTypesEnum.Progress">
    <slot :name="fieldTypesEnum.Progress" />
  </template>
  <!-- 自定义 -->
  <template v-else-if="column.fieldType === fieldTypesEnum.Custom">
    <slot :name="fieldTypesEnum.Custom" />
  </template>
</template>

<script>
import { fieldTypesEnum } from './enum';
import { formatDateTime } from '@/utils/formatTime';
import { getNameByid, getNamesByid } from '@/utils/common';
import UserTag from '@/components/UserTag';

export default {
  name: 'TableColumnContentRenderer',
  components: { UserTag },
  props: {
    column: {
      type: Object,
      required: true,
      default: () => ({})
    },
    row: {
      type: Object,
      required: true,
      default: () => ({})
    },
    format: {
      type: String,
      default: ''
    },
    status: {
      type: Object,
      default: () => ({
        type: 'info',
        content: '--'
      })
    }
  },
  emits: ['link-click'],
  setup(props, context) {
    const handleLinkClick = (fieldKey, row) => {
      context.emit('link-click', fieldKey, row);
    };
    return {
      fieldTypesEnum,
      formatDateTime,
      getNameByid,
      getNamesByid,
      handleLinkClick
    };
  }
};
</script>
