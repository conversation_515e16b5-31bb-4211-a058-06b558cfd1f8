<template>
  <el-dialog
    v-model="showDialog"
    custom-class="double-dialog tiny-dialog info-add"
    :title="title"
    top="5vh"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <div class="dialog-main">
      <el-form
        ref="taskInfoRef"
        class="formDataInfo"
        :model="formInline"
        :rules="taskInfoRule"
        :loading="dialogLoading"
        size="small"
        label-width="90px"
        label-position="right"
      >
        <!--委托方-->
        <el-space direction="vertical">
          <el-row :gutter="60">
            <el-col :span="12">
              <el-form-item
                label="委托方："
                prop="entrust.customerId"
                :rules="{ required: true, message: '请选择委托方', trigger: 'change' }"
              >
                <el-select
                  v-model="formInline.entrust.customerId"
                  class="owner-select"
                  placeholder="请选择委托方"
                  clearable
                  filterable
                  @change="changeEntrustCustomer"
                >
                  <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" />
            <el-col :span="12">
              <el-form-item
                label="公司电话："
                prop="entrust.customerPhone"
                :rules="[{ validator: isPhoneMobile, tigger: 'blur' }]"
              >
                <el-input v-model="formInline.entrust.customerPhone" placeholder="输入公司电话" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司传真：" prop="entrust.customerFaxNo">
                <el-input v-model="formInline.entrust.customerFaxNo" placeholder="输入公司传真" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司地址：" prop="entrust.address">
                <el-select
                  v-model="formInline.entrust.address"
                  class="owner-select"
                  placeholder="请选择公司地址"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in entrustAddressList"
                    :key="item.id"
                    :label="item.exactAddress"
                    :value="item.exactAddress"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" />
            <el-col :span="12">
              <el-form-item label="联系人：" prop="entrust.contactsId">
                <el-select
                  v-model="formInline.entrust.contactsId"
                  class="owner-select"
                  placeholder="请选择联系人"
                  clearable
                  filterable
                  @change="changeEntrustContacts"
                >
                  <el-option v-for="item in entrustContactsList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="电话："
                prop="entrust.contactsPhone"
                :rules="[{ validator: isPhoneMobile, tigger: 'blur' }]"
              >
                <el-input v-model="formInline.entrust.contactsPhone" placeholder="输入电话" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="邮箱："
                prop="entrust.contactsEmail"
                :rules="[{ validator: isEmail2, tigger: 'blur' }]"
              >
                <el-input v-model="formInline.entrust.contactsEmail" placeholder="输入邮箱" clearable />
              </el-form-item>
            </el-col>
          </el-row>
        </el-space>
        <!--缴款方-->
        <el-space direction="vertical">
          <el-row :gutter="60">
            <el-col :span="12">
              <el-form-item label="缴款方：" prop="payer.customerId">
                <el-select
                  v-model="formInline.payer.customerId"
                  class="owner-select"
                  placeholder="请选择缴款方"
                  clearable
                  filterable
                  @change="changePayerCustomer"
                >
                  <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" />
            <el-col :span="12">
              <el-form-item label="公司税号：" prop="payer.taxNo">
                <el-input v-model="formInline.payer.taxNo" placeholder="输入公司税号" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电话：" prop="payer.phone" :rules="[{ validator: isPhoneMobile, tigger: 'blur' }]">
                <el-input v-model="formInline.payer.phone" placeholder="输入电话" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户行：" prop="payer.openingBank">
                <el-input v-model="formInline.payer.openingBank" placeholder="输入开户行" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="账号：" prop="payer.acctNo">
                <el-input v-model="formInline.payer.acctNo" placeholder="输入账号" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发票类型：" prop="payer.invoiceType">
                <el-select
                  v-model="formInline.payer.invoiceType"
                  class="owner-select"
                  placeholder="请选择发票类型"
                  clearable
                >
                  <el-option v-for="(val, key) in invoiceTypeJSON" :key="key" :label="val" :value="Number(key)" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="付款方式：" prop="payer.paymentMethod">
                <el-select
                  v-model="formInline.payer.paymentMethod"
                  class="owner-select"
                  placeholder="请选择付款方式"
                  clearable
                >
                  <el-option v-for="(val, key) in paymentMethodTypeJSON" :key="key" :label="val" :value="Number(key)" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="地址：" prop="payer.address">
                <el-input v-model="formInline.payer.address" placeholder="输入地址" clearable />
              </el-form-item>
            </el-col>
          </el-row>
        </el-space>
        <!--发票邮寄-->
        <el-space direction="vertical">
          <el-row :gutter="60">
            <el-col :span="12">
              <el-form-item label="发票邮寄：" props="invoice.customerId">
                <el-select
                  v-model="formInline.invoice.customerId"
                  class="owner-select"
                  placeholder="请选择客户"
                  clearable
                  filterable
                  @change="changeInvoiceCustomer"
                >
                  <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" />
            <el-col :span="12">
              <el-form-item label="联系人：" props="invoice.contactsId">
                <el-select
                  v-model="formInline.invoice.contactsId"
                  class="owner-select"
                  placeholder="请选择联系人"
                  clearable
                  filterable
                  @change="changeInvoiceContacts"
                >
                  <el-option v-for="item in invoiceContactsList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电话：" prop="invoice.phone" :rules="[{ validator: isPhoneMobile, tigger: 'blur' }]">
                <el-input v-model="formInline.invoice.phone" placeholder="输入电话" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="地址：" props="invoice.address">
                <el-select
                  v-model="formInline.invoice.address"
                  class="owner-select"
                  placeholder="请选择公司地址"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in invoiceAddressList"
                    :key="item.id"
                    :label="item.exactAddress"
                    :value="item.exactAddress"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-space>
        <!--报告邮寄-->
        <el-space direction="vertical">
          <el-row :gutter="60">
            <el-col :span="12">
              <el-form-item label="报告邮寄：" prop="report.customerId">
                <el-select
                  v-model="formInline.report.customerId"
                  class="owner-select"
                  placeholder="请选择客户"
                  clearable
                  filterable
                  @change="changeReportCustomer"
                >
                  <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" />
            <el-col :span="12">
              <el-form-item label="联系人：" prop="report.contactsId">
                <el-select
                  v-model="formInline.report.contactsId"
                  class="owner-select"
                  placeholder="请选择联系人"
                  clearable
                  filterable
                  @change="changeReportContacts"
                >
                  <el-option v-for="item in reportContactsList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电话：" prop="report.phone" :rules="[{ validator: isPhoneMobile, tigger: 'blur' }]">
                <el-input v-model="formInline.report.phone" placeholder="输入电话" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="地址：" prop="report.address">
                <el-select
                  v-model="formInline.report.address"
                  class="owner-select"
                  placeholder="请选择公司地址"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in reportAddressList"
                    :key="item.id"
                    :label="item.exactAddress"
                    :value="item.exactAddress"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-space>
        <!--生产商-->
        <el-space direction="vertical">
          <el-row :gutter="60">
            <el-col :span="12">
              <el-form-item label="生产商：" prop="producer.customerId">
                <el-select
                  v-model="formInline.producer.customerId"
                  class="owner-select"
                  placeholder="请选择客户"
                  clearable
                  filterable
                  @change="changeProducerCustomer"
                >
                  <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" />
            <el-col :span="12">
              <el-form-item label="联系人：" prop="producer.contactsId">
                <el-select
                  v-model="formInline.producer.contactsId"
                  class="owner-select"
                  placeholder="请选择联系人"
                  clearable
                  filterable
                  @change="changeProducerContacts"
                >
                  <el-option v-for="item in producerContactsList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="电话："
                prop="producer.phone"
                :rules="[{ validator: isPhoneMobile, tigger: 'blur' }]"
              >
                <el-input v-model="formInline.producer.phone" placeholder="输入电话" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="地址：" prop="producer.address">
                <el-select
                  v-model="formInline.producer.address"
                  class="owner-select"
                  placeholder="请选择公司地址"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in producerAddressList"
                    :key="item.id"
                    :label="item.exactAddress"
                    :value="item.exactAddress"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-space>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="showDialog = false">取 消</el-button>
        <el-button :loading="dialogLoading" type="primary" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref } from 'vue';
import { ElMessage } from 'element-plus';
import router from '@/router/index.js';
// import { useRoute } from 'vue-router'
import {
  getClientContacts,
  getInvoiceInfo,
  getClientAddress,
  saveTaskCustomerInfo,
  getTaskCustomerInfo
} from '@/api/task-registration';
import { isPhoneMobile, isEmail2 } from '@/utils/validate';
import { getList } from '@/api/customerManagement';
import { useStore } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
// import { formatDate } from '@/utils/formatTime'
import { taskType } from '@/data/industryTerm';

export default {
  name: 'DialogTaskRegistration',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '新增客户信息'
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    const store = useStore().state;
    const datas = reactive({
      currentAccountId: getLoginInfo().accountId,
      userOptions: [],
      invoiceTypeJSON: {
        0: '普票',
        1: '专票'
      },
      paymentMethodTypeJSON: {
        0: '现金',
        1: '转账'
      },
      copyUserOptions: [],
      entrustContactsList: [], // 委托方联系人
      entrustAddressList: [], // 委托方公司地址
      invoiceContactsList: [],
      invoiceAddressList: [],
      producerContactsList: [],
      producerAddressList: [],
      reportContactsList: [],
      reportAddressList: [],
      addressOptions: store.user.materialList,
      copyaddressOptions: store.user.materialList,
      showDialog: false,
      formInline: {
        entrust: {},
        invoice: {},
        payer: {},
        producer: {},
        report: {}
      },
      taskInfoRef: ref(),
      taskInfoRule: {},
      typeOptions: taskType,
      showEdit: false,
      dialogLoading: false
    });

    watch(
      () => props.show,
      newValue => {
        if (newValue) {
          datas.showDialog = newValue;
          datas.showEdit = props.isEdit;
          if (props.isEdit) {
            datas.formInline = JSON.parse(JSON.stringify(props.info));
          }
          datas.formInline.superId = props.info.superId;
          setDropList();
        }
      }
    );

    function setDropList() {
      if (datas.formInline.entrust.customerId && datas.formInline.entrust.contactsId) {
        getContactList(datas.formInline.entrust.customerId).then(res => {
          datas.entrustContactsList = res;
        });
      }
      if (datas.formInline.entrust.customerId && datas.formInline.entrust.address) {
        getAddressList(datas.formInline.entrust.customerId).then(res => {
          datas.entrustAddressList = res;
        });
      }
      if (datas.formInline.invoice.customerId && datas.formInline.invoice.contactsId) {
        getContactList(datas.formInline.invoice.customerId).then(res => {
          datas.invoiceContactsList = res;
        });
      }
      if (datas.formInline.invoice.customerId && datas.formInline.invoice.address) {
        getAddressList(datas.formInline.invoice.customerId).then(res => {
          datas.invoiceAddressList = res;
        });
      }
      if (datas.formInline.report.customerId && datas.formInline.report.contactsId) {
        getContactList(datas.formInline.report.customerId).then(res => {
          datas.reportContactsList = res;
        });
      }
      if (datas.formInline.report.customerId && datas.formInline.report.address) {
        getAddressList(datas.formInline.report.customerId).then(res => {
          datas.reportAddressList = res;
        });
      }
      if (datas.formInline.producer.customerId && datas.formInline.producer.contactsId) {
        getContactList(datas.formInline.producer.customerId).then(res => {
          datas.producerContactsList = res;
        });
      }
      if (datas.formInline.producer.customerId && datas.formInline.producer.address) {
        getAddressList(datas.formInline.producer.customerId).then(res => {
          datas.producerAddressList = res;
        });
      }
    }

    // 确定选择
    const dialogSuccess = () => {
      datas.taskInfoRef.validate(valid => {
        if (valid) {
          if (datas.showEdit) {
            datas.dialogLoading = true;
            saveTaskCustomerInfo(datas.formInline).then(res => {
              datas.dialogLoading = false;
              if (res !== false) {
                datas.showDialog = false;
                context.emit('close', datas.formInline);
                ElMessage.success('更新成功');
              }
            });
          } else {
            datas.dialogLoading = true;
            saveTaskCustomerInfo(datas.formInline).then(res => {
              datas.dialogLoading = false;
              if (res !== false) {
                ElMessage.success('新增成功');
                datas.showDialog = false;
                // context.emit('setInfo', true)
                context.emit('close', datas.formInline);
                router.push({
                  name: 'TaskRegistrationDetail',
                  query: { id: res.data.data, flag: 2 }
                });
              }
            });
          }
        }
      });
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };
    // 委托方
    const changeEntrustCustomer = id => {
      const userIndex = datas.userOptions.findIndex(item => item.id === id);
      if (userIndex !== -1) {
        getContactList(id).then(res => {
          datas.entrustContactsList = res;
          const contactsIndex = datas.entrustContactsList.findIndex(
            item => item.id === datas.userOptions[userIndex].defaultContactsId
          );
          datas.formInline.entrust.contactsId = datas.userOptions[userIndex].defaultContactsId;
          datas.formInline.entrust.contactsName = datas.userOptions[userIndex].defaultContacts;
          datas.formInline.entrust.contactsPhone =
            contactsIndex === -1 ? '' : datas.entrustContactsList[contactsIndex].phone;
          datas.formInline.entrust.contactsEmail =
            contactsIndex === -1 ? '' : datas.entrustContactsList[contactsIndex].email;
          datas.formInline.entrust.customerId = datas.userOptions[userIndex].id;
          datas.formInline.entrust.customerName = datas.userOptions[userIndex].name;
          datas.formInline.entrust.customerFaxNo = datas.entrustContactsList[0]?.customerFaxNo;
          datas.formInline.entrust.customerPhone = datas.entrustContactsList[0]?.customerPhone;
          datas.formInline.entrust.latitude = datas.userOptions[userIndex].latitude;
          datas.formInline.entrust.longitude = datas.userOptions[userIndex].longitude;
          getAddressList(id).then(res => {
            datas.entrustAddressList = res;
            const defauleAddressInfo = res.filter(item => {
              return item.isDefault;
            })[0];
            datas.formInline.entrust.address = defauleAddressInfo?.exactAddress;
          });
        });
      }
    };
    const changeEntrustContacts = id => {
      const contactsIndex = datas.entrustContactsList.findIndex(item => item.id === id);
      if (contactsIndex !== -1) {
        const xContacts = datas.entrustContactsList[contactsIndex];
        datas.formInline.entrust.contactsId = xContacts.id;
        datas.formInline.entrust.contactsName = xContacts.name;
        datas.formInline.entrust.contactsPhone = xContacts.phone;
        datas.formInline.entrust.contactsEmail = xContacts.email;
      }
    };
    // 缴款方
    const changePayerCustomer = id => {
      const userIndex = datas.userOptions.findIndex(item => item.id === id);
      if (userIndex !== -1) {
        datas.formInline.payer.contactsId = '';
        datas.formInline.payer.contactsName = '';
        datas.formInline.payer.contactsPhone = '';
        datas.formInline.payer.customerId = datas.userOptions[userIndex].id;
        datas.formInline.payer.customerName = datas.userOptions[userIndex].name;
        datas.formInline.payer.latitude = datas.userOptions[userIndex].latitude;
        datas.formInline.payer.longitude = datas.userOptions[userIndex].longitude;
        getCurrentInvoiceInfo(id).then(res => {
          datas.formInline.payer.acctNo = res.acctNo;
          datas.formInline.payer.taxNo = res.taxNo;
          datas.formInline.payer.paymentMethod = res.paymentMethod;
          datas.formInline.payer.invoiceType = res.invoiceType;
          datas.formInline.payer.address = res.exactAddress
            ? `${res.regionState.join(',')},${res.exactAddress}`
            : `${res.regionState.join(',')}`;
          datas.formInline.payer.openingBank = res.openingBank;
          datas.formInline.payer.phone = res.phone;
        });
      }
    };

    // 发票邮寄
    const changeInvoiceCustomer = id => {
      const userIndex = datas.userOptions.findIndex(item => item.id === id);
      if (userIndex !== -1) {
        getContactList(id).then(res => {
          datas.invoiceContactsList = res;
          const contactsIndex = datas.invoiceContactsList.findIndex(
            item => item.id === datas.userOptions[userIndex].defaultContactsId
          );
          datas.formInline.invoice.contactsId = datas.userOptions[userIndex].defaultContactsId;
          datas.formInline.invoice.contactsName = datas.userOptions[userIndex].defaultContacts;
          datas.formInline.invoice.contactsPhone =
            contactsIndex === -1 ? '' : datas.invoiceContactsList[contactsIndex].phone;
          datas.formInline.invoice.customerId = datas.userOptions[userIndex].id;
          datas.formInline.invoice.customerName = datas.userOptions[userIndex].name;
          datas.formInline.invoice.phone = datas.userOptions[userIndex].phone;
          datas.formInline.invoice.latitude = datas.userOptions[userIndex].latitude;
          datas.formInline.invoice.longitude = datas.userOptions[userIndex].longitude;
          getAddressList(id).then(res => {
            datas.invoiceAddressList = res;
            const defauleAddressInfo = res.filter(item => {
              return item.isDefault;
            })[0];
            datas.formInline.invoice.address = defauleAddressInfo?.exactAddress;
          });
        });
      }
    };
    const changeInvoiceContacts = id => {
      const contactsIndex = datas.invoiceContactsList.findIndex(item => item.id === id);
      if (contactsIndex !== -1) {
        const xContacts = datas.invoiceContactsList[contactsIndex];
        datas.formInline.invoice.contactsId = xContacts.id;
        datas.formInline.invoice.contactsName = xContacts.name;
        datas.formInline.invoice.contactsPhone = xContacts.phone;
      }
    };

    // 报告邮寄
    const changeReportCustomer = id => {
      const userIndex = datas.userOptions.findIndex(item => item.id === id);
      if (userIndex !== -1) {
        getContactList(id).then(res => {
          datas.reportContactsList = res;
          const contactsIndex = datas.reportContactsList.findIndex(
            item => item.id === datas.userOptions[userIndex].defaultContactsId
          );
          datas.formInline.report.contactsId = datas.userOptions[userIndex].defaultContactsId;
          datas.formInline.report.contactsName = datas.userOptions[userIndex].defaultContacts;
          datas.formInline.report.contactsPhone =
            contactsIndex === -1 ? '' : datas.reportContactsList[contactsIndex].phone;
          datas.formInline.report.customerId = datas.userOptions[userIndex].id;
          datas.formInline.report.customerName = datas.userOptions[userIndex].name;
          datas.formInline.report.phone = datas.userOptions[userIndex].phone;
          datas.formInline.report.latitude = datas.userOptions[userIndex].latitude;
          datas.formInline.report.longitude = datas.userOptions[userIndex].longitude;
          getAddressList(id).then(res => {
            datas.reportAddressList = res;
            const defauleAddressInfo = res.filter(item => {
              return item.isDefault;
            })[0];
            datas.formInline.report.address = defauleAddressInfo?.exactAddress;
          });
        });
      }
    };

    const changeReportContacts = id => {
      const contactsIndex = datas.reportContactsList.findIndex(item => item.id === id);
      if (contactsIndex !== -1) {
        const xContacts = datas.reportContactsList[contactsIndex];
        datas.formInline.report.contactsId = xContacts.id;
        datas.formInline.report.contactsName = xContacts.name;
        datas.formInline.report.contactsPhone = xContacts.phone;
      }
    };

    // 生产商

    const changeProducerCustomer = id => {
      const userIndex = datas.userOptions.findIndex(item => item.id === id);
      if (userIndex !== -1) {
        getContactList(id).then(res => {
          datas.producerContactsList = res;
          const contactsIndex = datas.producerContactsList.findIndex(
            item => item.id === datas.userOptions[userIndex].defaultContactsId
          );
          datas.formInline.producer.contactsId = datas.userOptions[userIndex].defaultContactsId;
          datas.formInline.producer.contactsName = datas.userOptions[userIndex].defaultContacts;
          datas.formInline.producer.contactsPhone =
            contactsIndex === -1 ? '' : datas.producerContactsList[contactsIndex].phone;
          datas.formInline.producer.customerId = datas.userOptions[userIndex].id;
          datas.formInline.producer.customerName = datas.userOptions[userIndex].name;
          datas.formInline.producer.phone = datas.userOptions[userIndex].phone;
          datas.formInline.producer.latitude = datas.userOptions[userIndex].latitude;
          datas.formInline.producer.longitude = datas.userOptions[userIndex].longitude;
          getAddressList(id).then(res => {
            datas.producerAddressList = res;
            const defauleAddressInfo = res.filter(item => {
              return item.isDefault;
            })[0];
            datas.formInline.producer.address = defauleAddressInfo?.exactAddress;
          });
        });
      }
    };

    const changeProducerContacts = id => {
      const contactsIndex = datas.producerContactsList.findIndex(item => item.id === id);
      if (contactsIndex !== -1) {
        const xContacts = datas.producerContactsList[contactsIndex];
        datas.formInline.producer.contactsId = xContacts.id;
        datas.formInline.producer.contactsName = xContacts.name;
        datas.formInline.producer.contactsPhone = xContacts.phone;
      }
    };

    // #region 获取客户信息

    function getContactList(customerId) {
      return new Promise(resolve => {
        let result = [];
        getClientContacts(customerId).then(res => {
          if (res) {
            result = res.data.data;
          }
          resolve(result);
        });
      });
    }

    function getAddressList(customerId) {
      return new Promise(resolve => {
        let result = [];
        getClientAddress(customerId).then(res => {
          if (res) {
            result = res.data.data;
            result.forEach(item => {
              if (item.exactAddress) {
                item.exactAddress = `${item.regionState.join(',')},${item.exactAddress}`;
              } else {
                item.exactAddress = `${item.regionState.join(',')}`;
              }
            });
          }
          resolve(result);
        });
      });
    }

    function getCurrentInvoiceInfo(customerId) {
      return new Promise(resolve => {
        let result = [];
        getInvoiceInfo(customerId).then(res => {
          if (res) {
            result = res.data.data;
          }
          resolve(result);
        });
      });
    }

    function getAllClients() {
      const clientParams = {
        condition: '',
        isDesc: false,
        page: '1',
        limit: '-1',
        orderBy: '',
        isAsc: ''
      };
      getList(clientParams).then(res => {
        if (res.data.code === 200) {
          datas.userOptions = res.data.data.list;
          datas.copyUserOptions = JSON.parse(JSON.stringify(res.data.data.list));
        }
      });
    }

    function getCustomerInfo(taskId) {
      getTaskCustomerInfo(taskId).then(res => {
        // if (res.data.code === 200) {
        // }
      });
    }

    // #endregion

    getAllClients();
    return {
      ...toRefs(datas),
      dialogSuccess,
      close,
      isEmail2,
      isPhoneMobile,
      changeEntrustCustomer,
      changeEntrustContacts,
      changeProducerCustomer,
      changeProducerContacts,
      changeReportCustomer,
      changeReportContacts,
      changeInvoiceCustomer,
      changeInvoiceContacts,
      changePayerCustomer,
      getCustomerInfo
    };
  }
};
</script>
<style lang="scss" scoped>
.formDataInfo {
  :deep(.el-input--medium .el-input__inner) {
    height: 32px;
    line-height: 32px;
  }
}
.info-add {
  .dialog-main {
    :deep(.el-space) {
      width: 100%;
      .el-space__item {
        margin: 2px 0;
        width: 100%;
      }
    }
  }
  .el-input--medium .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
  .el-divider--horizontal {
    margin: 15px 0px;
  }
  .el-form-item {
    text-align: left;
    // width: 46%;
    margin-bottom: 15px;
    :deep(.el-form-item__label) {
      font-weight: normal;
      font-size: 14px;
      color: #606266;
      padding: 0px;
    }
    :deep(.el-form-item__content) {
      .el-select {
        width: 100%;
      }
      .el-date-editor.el-input {
        width: 100%;
      }
      .el-radio-group {
        .el-radio {
          margin: 0px;
        }
        .el-radio:nth-child(2) {
          margin: 0px 5px;
        }
      }
    }
  }
}
</style>
