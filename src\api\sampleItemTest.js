import request from '@/utils/request';

// 样品领用列表 /{samplesId}}
export function getSampleCollectionList(samplesId) {
  return request({
    url: `/api-orders/orders/experiment/samplereceive/list/${samplesId}`,
    method: 'get'
  });
}

// 样品领用单位 /{samplesId}} 这个目前是用的字典里面的单位，要是测试没问题，可以删除
// export function getSampleUnit(samplesId) {
//   return request({
//     url: `/api-experiment/experiment/samplesreceive/samplesunit`,
//     method: 'get'
//   })
// }

// 样品领用保存和编辑 /{samplesId}}
export function saveSample(data) {
  return request({
    url: `/api-orders/orders/experiment/samplereceive/samplesreceive`,
    method: 'post',
    data
  });
}

// 样品领用记录删除 /{id}}
export function deleteSample(id) {
  return request({
    url: `/api-orders/orders/experiment/samplereceive/delete/${id}`,
    method: 'DELETE',
    id
  });
}

// 仪器设备使用记录 /{experimentId}}
export function getDeviceList(experimentId) {
  return request({
    url: `/api-orders/orders/experiment/deviceusage/list/${experimentId}`,
    method: 'get'
  });
}

// 项目检验记录试验图片列表 /{experimentId}}
export function imgrecordList(experimentId) {
  return request({
    url: `/api-orders/orders/experiment/imgrecord/list/${experimentId}`,
    method: 'get'
  });
}

// 项目检验记录试验图片保存和编辑
export function imgrecordSave(data) {
  return request({
    url: `/api-orders/orders/experiment/imgrecord/save`,
    method: 'post',
    data
  });
}

// 项目检验记录试验图片删除 /{experimentId}}
export function imgrecordDelete(data) {
  return request({
    url: `/api-orders/orders/experiment/imgrecord/delete/${data}`,
    method: 'delete',
    data
  });
}
// 检测执行获取图片标签信息
export function capabilityImageLabel(capabilityId) {
  return request({
    url: `/api-capabilitystd/capability/capabilityimagelabel/findByCapabilityId/${capabilityId}`,
    method: 'get'
  });
}
