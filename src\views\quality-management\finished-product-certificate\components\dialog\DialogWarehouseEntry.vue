<template>
  <!-- 入库、批量入库 -->
  <el-dialog v-model="dialogShow" title="SAP入库" width="400px" :close-on-click-modal="false" @close="cancelDialog()">
    <el-form ref="formRef" :model="formData" label-position="top" label-width="60px">
      <el-form-item
        label="仓库："
        prop="wareHouseNo"
        :rules="{
          required: true,
          message: '请选择仓库',
          trigger: 'change'
        }"
      >
        <el-select
          v-model="formData.wareHouseNo"
          filterable
          size="small"
          clearable
          placeholder="请选择仓库"
          class="w-full"
        >
          <el-option v-for="(val, key) in dictionaryAll['BYCK'].enable" :key="key" :label="val" :value="key" />
        </el-select>
      </el-form-item>
      <el-form-item
        label="交货单位："
        prop="deliveryInstitutionCode"
        :rules="{
          required: true,
          message: '请选择交货单位',
          trigger: 'change'
        }"
      >
        <el-select
          v-model="formData.deliveryInstitutionCode"
          filterable
          size="small"
          clearable
          placeholder="请选择交货单位"
          class="w-full"
        >
          <el-option v-for="(val, key) in dictionaryAll['JHDW'].enable" :key="key" :label="val" :value="key" />
        </el-select>
      </el-form-item>
      <el-form-item
        label="入库日期："
        prop="wareHouseDate"
        :rules="{
          required: false,
          message: '请选择入库日期',
          trigger: 'change'
        }"
      >
        <el-date-picker v-model="formData.wareHouseDate" type="date" placeholder="请选择入库日期" style="width: 100%" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancelDialog()">取消</el-button>
      <el-button :loading="dialogLoading" type="primary" @click="handleSubmit()">确定</el-button>
    </template>
  </el-dialog>
  <el-dialog
    v-model="batchInStorageDialogVisible"
    title="批量导入结果"
    width="40%"
    :before-close="onCloseBatchInStorageDialog"
  >
    <div class="flex gap-5 mb-2">
      <div>入库总数量：{{ batchInStorageResult.total }}</div>
      <div>入库成功数：{{ batchInStorageResult.successTotal }}</div>
      <div>入库失败数：{{ batchInStorageResult.errorTotal }}</div>
    </div>
    <div class="mb-2">入库失败信息：</div>
    <div v-if="batchInStorageResult.sapErrorMessage.length > 0">
      <div v-for="(item, index) in batchInStorageResult.sapErrorMessage" :key="index">{{ item }}</div>
    </div>
    <template #footer>
      <el-button type="primary" @click="onCloseBatchInStorageDialog()"> 确定 </el-button>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, watch, ref, toRefs } from 'vue';
import { getNameByid } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { ElMessage } from 'element-plus';
import { finishedProductDataReturn } from '@/api/finished-product-certificate';
import { downloadFile } from '@/api/warehousing-record';
export default {
  name: 'DialogWarehouseEntry',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    selectRow: {
      type: Object,
      default: () => ({})
    },
    dictionary: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      formData: {},
      dictionaryAll: {
        BYCK: { enable: {}, all: {} },
        JHDW: { enable: {}, all: {} }
      },
      dialogLoading: false,
      dialogShow: false,
      formRef: ref(),
      batchInStorageDialogVisible: false,
      batchInStorageResult: {
        total: 0,
        errorTotal: 0,
        successTotal: 0,
        sapErrorMessage: []
      }
    });

    const cancelDialog = value => {
      state.dialogShow = false;
      state.dialogLoading = false;
      context.emit('closeDialog', value);
    };

    const submitJudgement = () => {};

    watch(
      () => props.dialogVisible,
      newValue => {
        state.dialogShow = newValue;
        if (newValue) {
          state.formData = {
            wareHouseDate: formatDate(new Date())
          };
          state.dictionaryAll = props.dictionary || { BYCK: { enable: {}, all: {} } };
          const allSameWareHouseNo = props.selectRow?.every(
            item => item.wareHouseNo === props.selectRow[0].wareHouseNo
          );
          if (allSameWareHouseNo) {
            state.formData.wareHouseNo = props.selectRow[0].wareHouseNo;
          }

          const allSameRegisterDepartmentCode = props.selectRow?.every(
            item => item.registerDepartmentCode === props.selectRow[0].registerDepartmentCode
          );
          if (allSameRegisterDepartmentCode) {
            state.formData.deliveryInstitutionCode = props.selectRow[0].registerDepartmentCode;
          }
        }
      }
    );

    const handleSubmit = async () => {
      state.formRef
        .validate()
        .then(async valid => {
          if (valid) {
            const params = [];
            props.selectRow.forEach(item => {
              params.push({
                ...item,
                ...state.formData,
                deliveryInstitution: state.dictionaryAll['JHDW'].enable[state.formData.deliveryInstitutionCode]
              });
            });
            state.dialogLoading = true;
            const { data } = await finishedProductDataReturn({ list: params }).finally(
              () => (state.dialogLoading = false)
            );
            if (data.code === 200) {
              const { certificateWarehouseReceiptEntityId, successTotal, errorTotal } = data.data;
              if (successTotal > 0) {
                handleDownLoad(certificateWarehouseReceiptEntityId);
              }

              if (errorTotal > 0) {
                state.batchInStorageDialogVisible = true;
                state.batchInStorageResult = data.data;
                return;
              }

              ElMessage.success('入库成功');
              cancelDialog(true);
            }
          } else {
            return false;
          }
        })
        .catch(() => {});
    };
    const handleDownLoad = async response => {
      state.listLoading = true;
      const { data } = await downloadFile(response).finally((state.listLoading = false));
      if (data) {
        window.open(data.data);
        ElMessage.success('导出成功!');
      }
    };

    const onCloseBatchInStorageDialog = () => {
      state.batchInStorageDialogVisible = false;
      cancelDialog(true);
    };

    return {
      ...toRefs(state),
      cancelDialog,
      handleSubmit,
      getNameByid,
      formatDate,
      submitJudgement,
      onCloseBatchInStorageDialog
    };
  }
};
</script>
<style lang="scss" scoped></style>
