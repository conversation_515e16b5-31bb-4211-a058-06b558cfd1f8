<template>
  <!-- 系统日志详情 -->
  <ListLayout
    :has-quick-query="false"
    :has-left-panel="true"
    :aside-panel-width="asidePanelWidth"
    :aside-max-width="400"
  >
    <template #search-bar>
      <div class="searchInput">当前系统版本：{{ lastVersion }}</div>
    </template>
    <template #page-left-side>
      <div class="steps-container">
        <el-steps direction="vertical" :active="currentIndex" :space="80" process-status="finish">
          <el-step
            v-for="(item, index) in list"
            :key="item.id"
            :class="currentIndex === index ? 'current-step' : ''"
            @click="selectItem(item, index)"
          >
            <template #title>
              <div :class="currentIndex === index ? 'title-color' : 'text-color'">{{ item.title }}</div>
            </template>
            <template #description>
              <span class="text-color">{{ item.version }}</span>
              <span class="create-time text-color">{{ item.createTime }}</span>
            </template>
          </el-step>
        </el-steps>
      </div>
    </template>
    <div v-loading="loadContant" class="text-contant">
      <div class="contant-title">
        <div class="title">{{ currentItem.title }}</div>
        <el-tag effect="dark" size="small">{{ currentItem.version }}</el-tag>
      </div>
      <MarkdownEditor
        :value="currentItem.content"
        :plugins="plugins"
        :toolbar="toolbar"
        :readonly="true"
        :height="height"
        :disabled="true"
      />
    </div>
  </ListLayout>
</template>

<script>
import { reactive, toRefs, getCurrentInstance } from 'vue';
import { getPermissionBtn } from '@/utils/common';
import { getAllLogInfo, getLogInfoById } from '@/api/logInfo';
import ListLayout from '@/components/ListLayout';
import MarkdownEditor from '@/components/MarkdownEditor/index.vue';

export default {
  name: 'LogDetail',
  components: { ListLayout, MarkdownEditor },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      asidePanelWidth: 300,
      list: [],
      lastVersion: '暂无',
      currentItem: {},
      currentIndex: 0,
      toolbar: '',
      plugins: '',
      height: '100',
      loadContant: false
    });
    // 切换
    const selectItem = (item, index) => {
      // console.log(item)
      state.currentIndex = index;
      state.currentItem = item;
      proxy.getInfosById(item.id);
    };
    return { ...toRefs(state), getPermissionBtn, selectItem };
  },
  async created() {
    if (this.$route.query.id) {
      this.currentIndex = Number(this.$route.query.index);
    }
    await this.getLogLists();
  },
  methods: {
    getLogLists() {
      const _this = this;
      return new Promise((resolve, reject) => {
        getAllLogInfo().then(response => {
          if (response !== false) {
            const data = response.data.data;
            _this.list = data;
            if (_this.list.length > 0) {
              _this.lastVersion = _this.list[0].version;
              _this.currentItem = _this.list[_this.currentIndex];
              _this.getInfosById(_this.list[_this.currentIndex].id);
            }
            resolve(data);
          }
        });
      });
    },
    getInfosById(id) {
      var that = this;
      that.loadContant = true;
      getLogInfoById(id).then(res => {
        if (res !== false) {
          that.currentItem = res.data.data;
        }
        that.loadContant = false;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.searchInput {
  display: flex;
  font-size: 20px;
  height: 40px;
  line-height: 40px;
}
.steps-container {
  height: calc(100vh - 180px);
  overflow-y: auto;
  .create-time {
    margin-left: 5px;
  }
  .el-steps {
    .el-step {
      cursor: pointer;
    }
    :deep(.el-step .el-step__head) {
      .el-step__line {
        margin: 10px 0 -15px;
      }
      .el-step__line-inner {
        border: none;
      }
    }
    :deep(.el-step .el-step__main) {
      width: 100%;
      overflow: hidden;
      // white-space: nowrap;
      text-overflow: ellipsis;
      .el-step__title {
        padding: 3px 0 2px 0;
        .text-color {
          color: $tes-font;
        }
        .title-color {
          color: $tes-primary;
        }
      }
      .el-step__description {
        color: $tes-font2;
        .create-time {
          display: inline-block;
          padding-left: 20px;
        }
      }
    }
    :deep(.el-step__icon.is-text) {
      width: 12px;
      height: 12px;
      border: 2.5px solid #c0c4cc;
      .el-step__icon-inner {
        display: none;
      }
    }
    .current-step {
      :deep(.el-step__main .el-step__title) {
        font-weight: 550;
        // color: $tes-primary;
      }
      :deep(.el-step__icon.is-text) {
        border-color: $tes-primary;
        border: 4px solid;
      }
    }
  }
}
.text-contant {
  // pointer-events: none;
  :deep(.tox) {
    height: calc(100vh - 215px) !important;
  }
  :deep(.tox .tox-statusbar) {
    display: none;
  }
  .contant-title {
    display: flex;
    width: 100%;
    height: 32px;
    line-height: 32px;
    align-items: center;
    .title {
      display: inline-block;
      font-weight: bold;
      font-size: 24px;
    }
    .el-tag {
      margin-left: 10px;
    }
  }
}
.test-item {
  overflow: hidden;
  height: calc(100vh - 6.4rem);
  .test-item-table {
    border-top: 1px solid #e4e7ed;
    margin-right: 0;
    width: auto;
  }
  .blue-color {
    color: $tes-primary;
    cursor: pointer;
  }
}
</style>
