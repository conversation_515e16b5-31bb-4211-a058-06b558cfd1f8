<!-- 收款单 -->
<template>
  <el-drawer
    v-model="drawerVisiable"
    :title="drawerType === 'add' ? '添加收款' : '收款单详情'"
    direction="rtl"
    :before-close="handleClose"
    size="88%"
    destroy-on-close
    custom-class="page-drawer"
  >
    <!-- 收款单 -->
    <DrawerLayout v-loading="drawerLoading" :has-left-panel="false" :main-offset-top="53" :has-button-group="false">
      <el-form
        ref="formRef"
        :model="formData"
        label-position="right"
        size="small"
        label-width="110px"
        :class="{ 'detail-form': drawerType !== 'add' }"
        class="form-height-auto"
      >
        <el-row :gutter="20" class="text-left">
          <el-col :span="8">
            <el-form-item prop="collectionNo" label="收款单编号：">
              <span>{{ formData.collectionNo || '保存后生成编号' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收款日期：">
              <span v-if="drawerType === 'add'">{{ formatDate(new Date()) }}</span>
              <span v-else>{{ formatDate(formData.createTime) || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="payerName" label="付款方：">
              <span>{{ formData.payerName || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="standardName" label="收款人：">
              <UserTag v-if="drawerType === 'add'" :name="getNameByid(getLoginInfo().accountId) || '--'" />
              <UserTag v-else :name="getNameByid(formData.createBy) || '--'" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="receivableCost" label="应收金额(￥)：">
              <span>{{ formData.receivableCost || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="remark" label="备注：">
              <el-input
                v-if="drawerType === 'add'"
                v-model="formData.remark"
                type="textarea"
                :rows="2"
                placeholder="请输入备注"
              />
              <span v-else>{{ formData.remark || '--' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="drawerType === 'add'" class="btnGroup text-right">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd()">合并收款</el-button>
        </el-row>
        <el-table
          ref="tableRef"
          :data="formData.itemList"
          class="dark-table base-table format-height-table2"
          fit
          border
          size="medium"
          height="auto"
          style="margin-top: 5px"
          @header-dragend="drageHeader"
        >
          <el-table-column label="序号" type="index" :min-width="colWidth.serialNo" />
          <el-table-column label="委托编号" prop="entrustNo" :min-width="colWidth.orderNo">
            <template #default="{ row }">
              {{ row.entrustNo || '--' }}
            </template>
          </el-table-column>
          <el-table-column label="委托方" prop="entrustName" :min-width="colWidth.orderNo">
            <template #default="{ row }">
              {{ row.entrustName || '--' }}
            </template>
          </el-table-column>
          <el-table-column label="应收金额(￥)" prop="receivableCost" :min-width="colWidth.money">
            <template #default="{ row }">
              <span v-if="row.receivableCost || row.receivableCost === 0">{{ row.receivableCost }}</span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="已收金额(￥)" prop="receivedCost" :min-width="colWidth.money">
            <template #default="{ row }">
              <span v-if="row.receivedCost || row.receivedCost === 0">{{ row.receivedCost }}</span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column v-if="drawerType === 'add'" label="收款金额" prop="paidCost" :min-width="colWidth.money">
            <template #default="{ row, $index }">
              <el-form-item
                :prop="`itemList.${$index}.paidCost`"
                label-width="0"
                :rules="{
                  validator: amountReceived,
                  validData: { receivableCost: row.receivableCost, receivedCost: row.receivedCost },
                  trigger: 'change'
                }"
              >
                <el-input
                  v-model="row.paidCost"
                  :placeholder="`请输入收款金额`"
                  maxlength="300"
                  style="width: 100%"
                  @keyup.enter="onSubmit"
                  @keyup.prevent
                  @keydown.enter.prevent
                />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            v-if="formData.itemList.length > 1"
            label="操作"
            prop="measurementCycle"
            :min-width="colWidth.operation"
          >
            <template #default="{ $index }">
              <span class="blue-color" @click="handleDelete($index)">删除</span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="drawer-fotter">
        <el-button
          v-if="drawerType === 'add'"
          type="primary"
          :loading="drawerLoading"
          @click="onSubmit"
          @keyup.prevent
          @keydown.enter.prevent
          >确认</el-button
        >
        <el-button
          v-if="drawerType !== 'add' && formData.status === 1 && getPermissionBtn('voidedCollection')"
          type="danger"
          :loading="drawerLoading"
          @click="handleVoid()"
          @keyup.prevent
          @keydown.enter.prevent
          >作废</el-button
        >
        <el-button :loading="drawerLoading" @click="handleClose">取消</el-button>
      </div>
    </DrawerLayout>
  </el-drawer>
  <!-- 合并收款 -->
  <DialogSelectOrder
    :dialog-visible="dialogVisible"
    :detail-info="detailInfo"
    :already-select="formData.itemList"
    @closeDialog="handleSelectData"
  />
</template>
<script>
// getCurrentInstance
import { reactive, ref, toRefs, watch, getCurrentInstance } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import DrawerLayout from '@/components/DrawerLayout';
import { amountReceived } from '@/utils/validate';
import { getLoginInfo } from '@/utils/auth';
import UserTag from '@/components/UserTag';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { publicAdd } from '@/utils/calculatePrecision';
import DialogSelectOrder from './dialog-select-order.vue';
import { entrustCostCollectionSave, entrustCostCollectionInvalid } from '@/api/consignmentCollection';
export default {
  name: 'DrawerCollection',
  components: { DialogSelectOrder, DrawerLayout, UserTag },
  props: {
    drawerShow: {
      type: Boolean,
      default: false
    },
    drawerType: {
      type: String,
      default: ''
    },
    detailInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  emits: ['closeDrawer'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      formData: {
        itemList: []
      },
      formRef: ref(),
      type: '',
      dialogVisible: false,
      drawerLoading: false,
      ruleInfo: {},
      detailInfo: {},
      drawerType: '', // 抽屉类型
      isEdit: false, // 是否是编辑页
      drawerVisiable: false,
      ruleForm: ref(),
      loading: false,
      tableRef: ref(null)
    });
    watch(props, newValue => {
      state.drawerVisiable = newValue.drawerShow;
      if (state.drawerVisiable) {
        state.drawerType = props.drawerType;
        state.detailInfo = props.detailInfo;
        state.formData = {
          itemList: [
            {
              ...props.detailInfo,
              receivableCost: props.detailInfo.entrustCost,
              receivedCost: props.detailInfo.paidCost,
              entrustId: props.detailInfo.id,
              paidCost: state.drawerType === 'add' ? '' : props.detailInfo.paidCost
            }
          ],
          receivableCost: props.detailInfo.entrustCost,
          payerName: props.detailInfo.payerName,
          collectionNo: state.drawerType === 'add' ? '' : props.detailInfo.collectionNo,
          remark: state.drawerType === 'add' ? '' : props.detailInfo.remark,
          collectionId: props.detailInfo.collectionId,
          status: state.drawerType === 'add' ? 1 : props.detailInfo.status,
          createBy: state.drawerType === 'add' ? '' : props.detailInfo.createBy,
          createTime: state.drawerType === 'add' ? '' : props.detailInfo.createTime
        };
      }
    });
    const handleAdd = () => {
      state.dialogVisible = true;
    };
    const getDetailInfo = () => {};
    const onSubmit = () => {
      proxy.$refs['formRef']
        .validate()
        .then(valid => {
          if (valid) {
            state.drawerLoading = true;
            entrustCostCollectionSave(state.formData).then(res => {
              state.drawerLoading = false;
              if (res) {
                context.emit('closeDrawer', true);
                proxy.$message.success('添加收款成功！');
              }
            });
          }
        })
        .catch(error => {
          const fieldName = Object.keys(error)[0];
          proxy.$message.warning(error[fieldName][0].message);
          return false;
        });
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDrawer');
    };
    // 删除
    const handleDelete = index => {
      state.formData.itemList.splice(index, 1);
      calculateReceivableCost();
    };
    const handleSelectData = val => {
      const addItem = [];
      if (val.length) {
        val.forEach(item => {
          addItem.push({
            ...item,
            receivableCost: item.entrustCost,
            receivedCost: item.paidCost,
            entrustId: item.id,
            paidCost: ''
          });
        });
      }
      state.formData.itemList = [...state.formData.itemList, ...addItem];
      state.dialogVisible = false;
      calculateReceivableCost();
    };
    // 自动计算应收金额
    const calculateReceivableCost = () => {
      state.formData.receivableCost = state.formData.itemList
        .map(item => item.receivableCost)
        .reduce((item, total) => {
          return publicAdd(Number(total), Number(item));
        }, 0);
    };
    // 作废
    const handleVoid = () => {
      state.drawerLoading = true;
      entrustCostCollectionInvalid(state.formData.collectionId).then(res => {
        state.drawerLoading = false;
        if (res) {
          context.emit('closeDrawer', true);
          proxy.$message.success('作废收款成功！');
        }
      });
    };

    return {
      ...toRefs(state),
      handleVoid,
      publicAdd,
      getPermissionBtn,
      calculateReceivableCost,
      getLoginInfo,
      getNameByid,
      amountReceived,
      handleSelectData,
      handleDelete,
      handleAdd,
      onSubmit,
      handleClose,
      getDetailInfo,
      formatDate,
      colWidth,
      drageHeader
    };
  }
};
</script>
<style lang="scss" scoped>
.detail-form {
  .el-form-item {
    margin-bottom: 0;
  }
}
.btnGroup {
  margin: 20px 10px 10px 0;
}

::v-deep(.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 31.5rem) !important;
    overflow-y: auto;
  }
}
</style>
