import request from '@/utils/request';

// 老化箱设备查询列表
export function getDeviceBoxList(data) {
  return request({
    url: '/api-device/device/deviceBox/list',
    method: 'post',
    data
  });
}
// 老化箱设备批量新增(根据选择仪器设备)
export function deviceBoxAddBatch(data) {
  return request({
    url: '/api-device/device/deviceBox/addBatch',
    method: 'post',
    data
  });
}
// 老化箱样品新增放样
export function sampleAdd(data) {
  return request({
    url: '/api-device/device/deviceBox/sample/add',
    method: 'post',
    data
  });
}

// 老化箱放样记录
export function sampleDetailList(data) {
  return request({
    url: '/api-device/device/deviceBox/sample/detailList',
    method: 'post',
    data
  });
}

// 老化箱样品详情列表
export function sampleList(data) {
  return request({
    url: '/api-device/device/deviceBox/sample/list',
    method: 'post',
    data
  });
}

// 老化箱样品-批量取出放样
export function sampleOut(data) {
  return request({
    url: '/api-device/device/deviceBox/sample/out',
    method: 'post',
    data
  });
}

// 老化箱样品-批量取出放样
export function sampleOutCheck(data) {
  return request({
    url: '/api-device/device/deviceBox/sample/outCheck',
    method: 'post',
    data
  });
}

// 老化箱看板二分列表
export function sampleSplitList(data) {
  return request({
    url: '/api-device/device/deviceBox/sample/splitList',
    method: 'post',
    data
  });
}

// 老化箱-温度信息(内部详情)
export function deviceBoxRdsData(data) {
  return request({
    url: '/api-device/device/deviceBox/rdsData',
    method: 'post',
    data
  });
}

// 老化箱设备删除(批量和单个)
export function deleteBatch(data) {
  return request({
    url: '/api-device/device/deviceBox/deleteBatch',
    method: 'post',
    data
  });
}
