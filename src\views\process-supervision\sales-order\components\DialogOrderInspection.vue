<template>
  <el-dialog
    v-model="showDialog"
    custom-class="double-dialog tiny-dialog info-add"
    title="报检"
    top="5vh"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <div class="dialog-main">
      <el-form
        ref="inspectionInfoRef"
        class="formDataInfo"
        :model="formInline"
        :rules="inspectionInfoRule"
        label-width="110px"
        label-position="top"
      >
        <el-row :gutter="60">
          <el-col
            v-for="viewItem in pageViewGroup[`2-basicInfo-dialog`]"
            :key="viewItem.fieldKey"
            :span="Number(viewItem.columnWidth)"
          >
            <el-form-item :label="`${viewItem.fieldName}：`" :prop="viewItem.fieldKey" style="position: relative">
              <template v-if="viewItem.fieldType == 'text'">
                <el-input
                  v-model="formInline[viewItem.fieldKey]"
                  :maxlength="100"
                  :placeholder="`请输入${viewItem.fieldName}`"
                  clearable
                />
              </template>
              <template v-if="viewItem.fieldType == 'person'">
                <el-select
                  v-model="formInline[viewItem.fieldKey]"
                  class="owner-select"
                  :placeholder="`请选择${viewItem.fieldName}`"
                  clearable
                  filterable
                  :filter-method="filterUserList"
                  @focus="filterUserList(null)"
                >
                  <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </template>
              <template v-if="viewItem.fieldType == 'date'">
                <el-date-picker
                  v-model="formInline[viewItem.fieldKey]"
                  type="date"
                  :placeholder="`请选择${viewItem.fieldName}`"
                />
              </template>
              <template v-if="viewItem.fieldType == 'custom'">
                <template v-if="viewItem.fieldKey == 'productionOrderNo'">
                  <span class="scdd">
                    <el-tooltip content="部分厂商对应生产指令号/生产工单" placement="top" effect="dark">
                      <i class="iconfont tes-title" />
                    </el-tooltip>
                  </span>
                  <el-input
                    v-model="formInline[viewItem.fieldKey]"
                    maxlength="100"
                    :placeholder="`请输入${viewItem.fieldName}`"
                    clearable
                  />
                </template>
                <template v-else-if="viewItem.fieldKey == 'productionProcedureNo'">
                  <el-select
                    v-model="formInline.productionProcedureNo"
                    class="owner-select"
                    :placeholder="`请选择${viewItem.fieldName}`"
                    clearable
                    filterable
                    :filter-method="filterProcessList"
                    @focus="filterProcessList(null)"
                    @change="changeProductionProcedure"
                  >
                    <el-option
                      v-for="item in processList"
                      :key="item.id"
                      :label="item.no + ' - ' + item.name"
                      :value="item.no"
                    />
                  </el-select>
                </template>
                <template v-else-if="viewItem.fieldKey == 'materialCode'">
                  <el-select
                    v-model="formInline.materialCode"
                    :placeholder="`请选择${viewItem.fieldName}`"
                    clearable
                    filterable
                    @change="changeMaterial"
                  >
                    <el-option v-for="item in materialOptions" :key="item.code" :label="item.name" :value="item.code" />
                  </el-select>
                </template>
                <template v-else-if="viewItem.fieldKey == 'isFirstOutput'">
                  <el-radio-group v-model="formInline.isFirstOutput">
                    <el-radio label="是" size="large">是</el-radio>
                    <el-radio label="否" size="large">否</el-radio>
                  </el-radio-group>
                </template>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent>保 存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref } from 'vue';
import _ from 'lodash';
// import router from '@/router/index.js'
// import { useRoute } from 'vue-router'
import { useStore } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
import { getProcessListNew } from '@/api/mas';
import { formatDate } from '@/utils/formatTime';
import { getDictionaryDetail } from '@/api/dictionary';
import { getNameByid } from '@/utils/common';
import { greaterThanZero2 } from '@/utils/validate';
import inspection_icon_2 from '@/assets/img/inspection_icon_2.svg';
import inspection_icon_3 from '@/assets/img/inspection_icon_3.svg';

export default {
  name: 'AddInfo',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    pageView: {
      type: Object,
      default: function () {
        return {};
      }
    },
    info: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo', 'saveInspection'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    // const lodash = inject('_')
    const store = useStore().state;
    const datas = reactive({
      currentAccountName: getNameByid(getLoginInfo().accountId),
      materialOptions: store.user.materialList,
      userOptions: store.common.nameList,
      copyUserOptions: store.common.nameList,
      pageViewGroup: {
        '2-basicInfo-dialog': {}
      },
      showDialog: false,
      formInline: {
        InspectionUserId: '',
        registerTime: formatDate(new Date()),
        type: 2,
        inspectionUnit: '',
        reelNo: '',
        productionQuantity: '',
        productionProcedureNo: '',
        productionProcedure: '',
        sampleQuantity: ''
      },
      inspectionInfoRef: ref(),
      inspectionInfoRule: {
        registerUserId: [{ required: true, message: '请选择登记人' }],
        registerTime: [{ required: true, message: '请选择登记日期' }],
        materialCode: [{ required: true, message: '请选择物资分类' }],
        sampleLength: [{ validator: greaterThanZero2, tigger: 'change' }],
        segment: [{ validator: greaterThanZero2, tigger: 'change' }]
      },
      typeOptions: [
        { id: 2, name: '过程检验', icon: inspection_icon_2 },
        { id: 3, name: '完工检验', icon: inspection_icon_3 }
      ],
      processList: [],
      copyProcessList: []
    });

    watch(
      () => props.show,
      newValue => {
        if (newValue) {
          datas.showDialog = newValue;
          datas.pageViewGroup = props.pageView;
          datas.formInline = {
            salesOrderDetailId: props.info.salesOrderDetailId,
            customerName: props.info.customerName,
            registerUserId: getLoginInfo().accountId,
            registerTime: formatDate(new Date())
          };
          if (store.user.materialList?.length) {
            const { code, id } = store.user.materialList[0];
            datas.formInline.materialCode = code;
            datas.formInline.materialId = id;
          }
        }
      },
      { deep: true }
    );

    // 确定选择
    const dialogSuccess = () => {
      datas.inspectionInfoRef.validate(valid => {
        if (valid) {
          datas.showDialog = false;
          context.emit('close', true);
          context.emit('saveInspection', datas.formInline);
        }
      });
    };
    const changeMaterial = val => {
      datas.formInline.materialId = datas.materialOptions.filter(item => {
        return item.code == val;
      })[0]?.id;
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };
    // 过滤报检人
    const filterUserList = val => {
      if (val) {
        const list = [];
        datas.copyUserOptions.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        datas.userOptions = list;
      } else {
        datas.userOptions = datas.copyUserOptions;
      }
    };
    // 过滤生产工序
    const filterProcessList = val => {
      if (val) {
        const list = [];
        datas.copyProcessList.forEach(user => {
          const item = _.filter(user.name, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        datas.processList = list;
      } else {
        datas.processList = JSON.parse(JSON.stringify(datas.copyProcessList));
      }
    };
    // 生产工序-change
    const changeProductionProcedure = no => {
      datas.processList.forEach(item => {
        if (item.no === no) {
          datas.formInline.productionProcedure = item.name;
          datas.formInline.productionProcedureNo = item.no;
        }
      });
    };

    return {
      ...toRefs(datas),
      changeMaterial,
      greaterThanZero2,
      dialogSuccess,
      close,
      filterUserList,
      changeProductionProcedure,
      filterProcessList
    };
  },
  created() {
    this.getProcessLists();
    getDictionaryDetail(5).then(res => {
      this.dirList = res.data.data?.dictionaryoption;
    });
  },
  methods: {
    // 获取生产工序列表接口
    getProcessLists() {
      var that = this;
      var param = {
        limit: '-1',
        page: '1',
        content: ''
      };
      getProcessListNew(param).then(res => {
        if (res !== false) {
          that.processList = res.data.data.list;
          that.copyProcessList = res.data.data.list;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.col-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.formDataInfo {
  :deep(.el-input--medium .el-input__inner) {
    height: 32px;
    line-height: 32px;
  }
}
.scdd {
  position: absolute;
  left: 70px;
  top: -34px;
}
.info-add {
  .dialog-main {
    :deep(.el-space) {
      width: 100%;
      .el-space__item {
        margin: 16px 0;
        width: 100%;
      }
    }

    .radio-groups {
      display: flex;
      justify-content: space-around;
      align-items: center;
      .el-radio-button {
        border: 2px solid transparent;
      }
      .el-radio-button.is-active {
        border-color: $tes-primary3;
      }
      .el-radio-button__inner {
        img {
          width: 54px;
          height: 64px;
          margin-right: 20px;
        }
      }
      :deep(.el-radio-button__inner) {
        border-color: transparent;
        // border-radius: 8px;
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
        padding: 16px 40px;
        box-shadow: none;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        line-height: 28px;
      }
      .el-radio-button {
        box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
        // border-radius: 8px;
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
      }
      .el-radio-button__original-radio:checked + .el-radio-button__inner {
        border: 2px solid $tes-primary3 !important;
      }

      .corner {
        opacity: 0;
        position: absolute;
        top: -20px;
        right: -20px;
        width: 0;
        height: 0;
        border: 20px solid $tes-primary;
        border-bottom-color: transparent;
        border-top-color: transparent;
        border-left-color: transparent;
        transform: rotateZ(135deg);
      }
      .el-radio-button.is-active .corner {
        opacity: 1;
      }
    }
  }
  .el-input--medium .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
  .el-divider--horizontal {
    margin: 15px 0px;
  }
  .el-form-item {
    text-align: left;
    // width: 46%;
    margin-bottom: 10px;
    :deep(.el-form-item__label) {
      font-weight: normal;
      font-size: 14px;
      color: #606266;
      padding: 0px;
    }
    :deep(.el-form-item__content) {
      .el-select {
        width: 100%;
      }
      .el-date-editor.el-input {
        width: 100%;
      }
      .el-radio-group {
        .el-radio {
          margin: 0px;
        }
        .el-radio:nth-child(2) {
          margin: 0px 5px;
        }
      }
    }
  }
}
</style>
