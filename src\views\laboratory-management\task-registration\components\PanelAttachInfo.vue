<template>
  <div class="sample-about">
    <div class="sample-about-header">
      <el-row>
        <el-col :span="12"><div class="title">附件信息</div></el-col>
        <el-col :span="12">
          <div class="upload-file">
            <el-upload
              :action="fileAction"
              :show-file-list="false"
              :headers="headerconfig"
              :before-upload="beforeUpload"
              :on-success="
                (res, file, files) => {
                  return handleSuccess(res, file, files);
                }
              "
              :auto-upload="true"
            >
              <el-button
                v-if="
                  showDetail &&
                  getPermissionBtn('RegistrationUploadFile') &&
                  status !== 1 &&
                  status !== 2 &&
                  status !== 3
                "
                id="uploadButton"
                type="primary"
                size="small"
                icon="el-icon-plus"
                @click="addAttachment"
                @keyup.prevent
                @keydown.enter.prevent
                >上传附件</el-button
              >
              <span v-if="showDetail" class="uploadSupplement">文件大小不能超过20M</span>
            </el-upload>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="content">
      <el-table
        v-if="attachInfoDetail && attachInfoDetail.length > 0"
        ref="tableAttachmentRef"
        :key="tableAttachmentKey"
        :data="attachInfoDetail"
        fit
        border
        height="auto"
        class="dark-table sample-about-table"
        @header-dragend="drageHeader"
      >
        <el-table-column label="附件" prop="name" min-width="140px" show-overflow-tooltip>
          <template #default="{ row }">
            <span
              v-if="row.name && getPermissionBtn('RegistrationDownFile')"
              class="blue-color"
              @click="downloadAttachment(row)"
              >{{ row.name }}</span
            >
            <span v-else>{{ row.name || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="上传人" prop="createBy" min-width="100px" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ getNameByid(row.createBy) || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="上传时间" prop="createTime" min-width="120px" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ formatDate(row.createTime) || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="showDetail && getPermissionBtn('RegistrationDeleteFile')"
          label="操作"
          width="170px"
          prop="caozuo"
          fixed="right"
          align="center"
        >
          <template #default="{ row }">
            <span class="blue-color" @click="deleteAttachment(row)">删除</span>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-if="!attachInfoDetail || attachInfoDetail.length === 0" :image="emptyImg" description="暂无数据" />
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, getCurrentInstance, watch } from 'vue';
// import { useStore } from 'vuex'
// import { getLoginInfo } from '@/utils/auth'
// import router from '@/router/index.js'
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { ElMessage, ElMessageBox } from 'element-plus';
import { deleteAttachmentListById, downloadAttachmentById } from '@/api/task-registration';
import { entrustRegAttachmentUploadUrl } from '@/api/uploadAction';
import { getToken } from '@/utils/auth';
import { formatDate } from '@/utils/formatTime';
// import { getAttachmentUnitDict } from '@/api/login'
import emptyImg from '@/assets/img/empty-table.png';

export default {
  name: 'PanelAttachInfo',
  components: {},
  props: {
    taskId: {
      type: String,
      default: ''
    },
    showDetail: {
      type: Boolean,
      default: false
    },
    taskStatus: {
      type: Number,
      default: 0
    },
    attachInfo: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['setInfo'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // console.log(proxy)
    // watch(props, (newValue) => {
    //
    // })
    // const store = useStore().state
    const datas = reactive({
      tableAttachmentKey: 'tableAttachmentKey',
      attachInfoDetail: props.attachInfo,
      addAttachmentTitle: '新增附件',
      status: props.taskStatus,
      fileAction: entrustRegAttachmentUploadUrl(props.taskId),
      headerconfig: {
        Authorization: getToken()
      }
    });

    watch(
      () => props.taskId,
      newValue => {
        if (newValue) {
          datas.fileAction = entrustRegAttachmentUploadUrl(props.taskId);
        }
      },
      { deep: true }
    );
    watch(
      () => props.taskStatus,
      newValue => {
        if (newValue) {
          datas.status = props.taskStatus;
        }
      },
      { deep: true }
    );
    watch(
      () => props.attachInfo,
      newValue => {
        if (newValue) {
          datas.attachInfoDetail = props.attachInfo;
        }
      },
      { deep: true }
    );

    // 添加附件-打开新增附件弹出框
    const addAttachment = () => {
      // console.log(datas.attachInfoDetail)
      datas.addAttachmentTitle = '新增附件';
      // context.emit('setInfo', datas.attachInfoDetail)
    };

    // 查看附件信息
    const downloadAttachment = row => {
      downloadAttachmentById(row.id).then(res => {
        if (res) {
          const blob = new Blob([res.data], { type: '' });
          const blobUrl = window.URL.createObjectURL(blob);
          const aEle = document.createElement('a');
          aEle.download = `${row.name}`;
          aEle.href = blobUrl;
          aEle.click();
          proxy.$message.success(`下载附件${row.name}成功`);
        }
      });
    };
    // 删除附件
    const deleteAttachment = row => {
      console.log(row);
      ElMessageBox({
        title: '提交',
        message: '是否确认删除该附件？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          deleteAttachmentListById(row.id).then(res => {
            if (res !== false) {
              console.log(res);
              ElMessage.success('删除成功');
              context.emit('setInfo', 'delete');
            }
          });
        })
        .catch(() => {});
    };

    // 上传文件的限制
    const beforeUpload = file => {
      const fileSize = file.size / 1024 / 1024 < 20;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过20M');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };

    // 上传成功
    const handleSuccess = (res, file, files) => {
      if (res.code === 200) {
        context.emit('setInfo', 'update');
      } else {
        proxy.$message.error(res.message);
      }
    };

    return {
      ...toRefs(datas),
      emptyImg,
      getNameByid,
      formatDate,
      addAttachment,
      drageHeader,
      deleteAttachment,
      downloadAttachment,
      beforeUpload,
      handleSuccess,
      getPermissionBtn
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.sample-about {
  .sample-about-header {
    margin: 15px 0 15px 0;
    text-align: left;
    .title {
      font-size: 16px;
      font-weight: bold;
      height: 32px;
      line-height: 32px;
      text-align: left;
      display: inline-block;
      margin-right: 16px;
    }
    // #uploadButton {
    //   float: right;
    // }
    .upload-file {
      text-align: right;
    }
    :deep(.el-button) {
      float: right;
    }
  }
  .content {
    background: $background-color;
    text-align: left;
    position: relative;
    .sample-about-table {
      margin-bottom: 15px;
    }
  }
}

.uploadSupplement {
  display: inline-block;
  margin-right: 10px;
  font-size: 12px;
  color: #909399;
}
</style>
