<template>
  <!-- 公告管理列表 -->
  <ListLayout :has-left-panel="false">
    <template #search-bar>
      <div class="searchInput">
        <el-input
          v-model="condition"
          v-trim
          v-focus
          class="ipt-360"
          placeholder="请输入搜索内容"
          clearable
          size="large"
          @keyup.enter="getTableList"
        >
          <template #prefix>
            <i class="el-input__icon el-icon-search" />
          </template>
        </el-input>
        <el-button type="primary" size="large" @click="getTableList">查询</el-button>
        <el-button size="large" @click="reset">重置</el-button>
      </div>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('deleteAnnouncement') && status === 0"
        size="large"
        icon="el-icon-delete"
        @click="deleteNotice()"
        @keyup.prevent
        @keydown.enter.prevent
        >批量删除</el-button
      >
      <el-button
        v-if="getPermissionBtn('addAnnouncement')"
        type="primary"
        size="large"
        icon="el-icon-plus"
        @click="handleAddNotice"
        @keyup.prevent
        @keydown.enter.prevent
        >新增公告</el-button
      >
    </template>
    <template #radio-content>
      <el-radio-group v-model="status" size="small" @change="getTableList">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button v-for="(value, key, index) in noticeTypeJson" :key="index" :label="Number(key)">{{
          value
        }}</el-radio-button>
      </el-radio-group>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      height="auto"
      :size="tableSize"
      highlight-current-row
      class="dark-table format-height-table base-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      @header-dragend="drageHeader"
      @row-click="handleRowClick"
      @selection-change="selectChange"
    >
      <el-table-column v-if="status === 0 || status === ''" type="selection" :selectable="selectable" width="55" />
      <el-table-column label="公告标题" prop="title" :min-width="160" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.title" class="blue-color" @click.stop="handleDetail(row, 'check')">{{ row.title }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="发布人" prop="name" :width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <UserTag v-if="row.publisherName" :name="row.publisherName || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="发布状态" prop="status" :width="colWidth.status">
        <template #default="{ row }">
          <el-tag size="small" :type="noticeType[row.status]">{{ noticeTypeJson[row.status] || '--' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="发布日期" prop="publishTime" :width="colWidth.datetime" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.publishTime || '--' }}
        </template>
      </el-table-column>
      <el-table-column label="下架日期" prop="abrogateTime" :width="colWidth.datetime" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.abrogateTime || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        :width="colWidth.operationMultiple"
        class-name="fixed-right"
        prop="caozuo"
        fixed="right"
      >
        <template #default="{ row }">
          <span
            v-if="getPermissionBtn('editAnnouncement') && row.status === 0"
            class="blue-color"
            @click.stop="handlePublish(row)"
            >发布</span
          >
          <span
            v-if="getPermissionBtn('editAnnouncement') && row.status === 0"
            class="blue-color"
            @click.stop="handleDetail(row, 'edit')"
            >编辑</span
          >
          <span
            v-if="getPermissionBtn('deleteAnnouncement') && row.status === 0"
            class="blue-color"
            @click.stop="deleteNotice(row)"
            >删除</span
          >
          <span
            v-if="getPermissionBtn('deleteAnnouncement') && row.status === 1"
            class="blue-color"
            @click.stop="handleTakeOff(row)"
            >下架</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getTableList" />
    <DrawerNotice :drawer="drawVisiable" :detail-info="detailData" :type="drawerType" @close="closeDrawer" />
  </ListLayout>
</template>

<script>
import { reactive, ref, getCurrentInstance, toRefs } from 'vue';
import Pagination from '@/components/Pagination';
import DrawerNotice from './DrawerNotice';
import UserTag from '@/components/UserTag';
import { getList, releaseNotice, takeOffNotice, deleteNoticeApi } from '@/api/announcement';
import { getDictionary } from '@/api/user';
import { colWidth } from '@/data/tableStyle';
import ListLayout from '@/components/ListLayout';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import router from '@/router/index.js';

export default {
  name: 'Announcement',
  components: { ListLayout, Pagination, UserTag, DrawerNotice },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // const store = useStore()
    // const route = useRoute()
    const state = reactive({
      drawerType: '',
      tableRef: ref(),
      selectData: [],
      status: 0,
      drawVisiable: false,
      searchRef: ref(null),
      dialogLoading: false,
      isAdd: false,
      detailData: {}, // 传递给详情页的内容
      tableData: [],
      condition: '', // 模糊查询关键字
      total: 0,
      tableLoading: false, // 表格加载的loading
      detailDrawer: false,
      isEdit: true, // 详情页的类型
      dialogFrom: {}, // 操作树节点的弹窗表格
      noticeTypeJson: {}, // 公告状态json类型
      noticeType: {
        // 设备状态对应的tag类型
        1: 'success',
        0: 'default'
      },
      listQuery: {
        page: 1,
        limit: 20
      },
      tableSize: 'medium'
    });
    const editFrom = ref(null);
    const activeName = ref('0');
    const showS = ref(true);

    const tableKey = ref(0);
    const getReleaseStatus = () => {
      getDictionary('GGFBZT').then(res => {
        if (res) {
          res.data.data.dictionaryoption.forEach(item => {
            state.noticeTypeJson[item.code] = item.name;
          });
        }
      });
    };
    getReleaseStatus();
    const getTableList = query => {
      var params = {
        condition: state.condition,
        status: state.status
      };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        params.page = state.listQuery.page.toString();
        params.limit = state.listQuery.limit.toString();
      }
      state.tableLoading = true;
      getList(params).then(res => {
        state.tableLoading = false;
        const data = res.data.data;
        state.tableData = data.list;
        state.total = data.totalCount;
      });
    };
    getTableList();
    const reset = () => {
      state.status = 0;
      state.condition = '';
      state.listQuery.page = 1;
      state.listQuery.limit = 20;
      getTableList();
    };

    const handleAddNotice = (type, row) => {
      state.drawerType = 'add';
      state.drawVisiable = true;
    };
    // 删除公告
    const deleteNotice = row => {
      const params = {
        ids: row
          ? [row.id]
          : state.selectData.map(item => {
              return item.id;
            })
      };
      if (params.ids.length === 0) {
        proxy.$message.warning('请先勾选要删除的公告！');
        return false;
      }
      proxy
        .$confirm('是否确认删除？删除后不可恢复', '确认删除', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          state.tableLoading = true;
          deleteNoticeApi(params.ids).then(res => {
            state.tableLoading = false;
            if (res) {
              proxy.$message.success(res.data.message);
              getTableList();
            }
          });
        })
        .catch(() => {});
    };
    // 编辑、查看详情
    const handleDetail = (row, type) => {
      state.drawerType = type;
      state.detailData = row;
      state.drawVisiable = true;
    };
    const selectChange = val => {
      state.selectData = val;
    };
    // 发布
    const handlePublish = row => {
      proxy
        .$confirm('是否确认发布', '确认发布', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          state.tableLoading = true;
          releaseNotice(row).then(res => {
            state.tableLoading = false;
            if (res) {
              proxy.$message.success(res.data.message);
              getTableList();
            }
          });
        })
        .catch(() => {});
    };
    // 是否确认下架
    const handleTakeOff = row => {
      proxy
        .$confirm('是否确认下架？', '确认下架', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          state.tableLoading = true;
          takeOffNotice(row.id).then(res => {
            state.tableLoading = false;
            if (res) {
              proxy.$message.success(res.data.message);
              getTableList();
            }
          });
        })
        .catch(() => {});
    };
    // 过滤不可删除的勾选框
    const selectable = (row, index) => {
      if (row.status === 0) {
        return true;
      } else {
        return false;
      }
    };
    const closeDeatilDrawer = val => {
      state.detailDrawer = false;
      // 由于设备使用记录跳转到详情页面，要是关闭页面会重新刷新列表，因此会再次打开详情页面，这边添加传值来做判断，同时在关闭详情页面的时候为了防止刷新，这边路由会替换到原先模块的路由
      getTableList(null, false);
      router.replace({ query: {} });
    };
    // 选择物资
    const changeIcon = (command, node) => {
      if (!command) {
        node.showIcon = true;
      } else {
        node.showIcon = !node.showIcon;
      }
    };
    const closeDrawer = value => {
      if (value) {
        getTableList();
      }
      state.drawVisiable = false;
    };
    const handleRowClick = row => {
      state.tableRef.toggleRowSelection(
        row,
        !state.selectData.some(item => {
          return row.id === item.id;
        })
      );
    };
    return {
      ...toRefs(state),
      getReleaseStatus,
      handleRowClick,
      selectChange,
      handleTakeOff,
      handlePublish,
      getTableList,
      deleteNotice,
      handleAddNotice,
      closeDrawer,
      selectable,
      getPermissionBtn,
      formatDate,
      changeIcon,
      getNameByid,
      drageHeader,
      closeDeatilDrawer,
      handleDetail,
      tableKey,
      showS,
      editFrom,
      activeName,
      reset,
      colWidth
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
.searchInput {
  display: flex;
  .el-input {
    width: 360px;
    margin-right: 10px;
  }
}
</style>
