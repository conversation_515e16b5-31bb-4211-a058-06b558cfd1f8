<template>
  <!-- 智能图表模块数据看板 -->
  <div class="dataBoard h-full flex flex-col">
    <div class="dataPageTop">
      <!-- <img class="topHeader" src="@/assets/img/dataBoard/topHeader.png" alt="" /> -->
      <div class="dataPageTitle">{{ pageTitle }}</div>
    </div>
    <div class="flex flex-col flex-1 px-3 overflow-auto gap-3">
      <div class="flex flex-row gap-4 h-1/2">
        <SampleList :type="0" :dictionary="dictionary" />
        <SampleList :type="1" :dictionary="dictionary" />
      </div>
      <LineEcharts class="flex-1" />
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, onMounted } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getNameByid } from '@/utils/common';
import SampleList from './components/sample-list.vue';
import LineEcharts from './components/line-echarts.vue';
import { getDictionary } from '@/api/user';

export default {
  name: 'AgingLaboratory',
  components: { SampleList, LineEcharts },
  setup(props, context) {
    const state = reactive({
      pageTitle: '八益线缆老化实验室实时监控大屏',
      dictionary: {
        24: {
          all: {},
          enable: {}
        }
      }
    });
    const getDicList = () => {
      Object.keys(state.dictionary).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          state.dictionary[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              state.dictionary[item].enable[optionItem.code] = optionItem.name;
            }
            state.dictionary[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    onMounted(() => {
      getDicList();
    });
    return {
      ...toRefs(state),
      getNameByid,
      formatDate
    };
  },
  computed: {},
  watch: {}
};
</script>
<style lang="scss" scoped>
@import 'aging-laboratory.scss';
</style>
