<template>
  <DetailLayout :main-offset-top="105" has-full-main-height>
    <template #page-header>
      <div class="header-flex flex-start">
        <el-space :size="40" :spacer="spacer">
          <div class="item-column">
            <span class="item-label">样品编号</span>
            <div class="item-content">{{ detailInfo.secSampleNum || '--' }}</div>
          </div>
          <div class="item-column">
            <span class="item-label">申请单编号</span>
            <div class="item-content">{{ detailInfo.inspectionNo || '--' }}</div>
          </div>
          <div class="item-column">
            <span class="item-label">{{ detailInfo.supplierName || '--' }}</span>
            <div class="item-content">{{ detailInfo.batchNo || '--' }}</div>
          </div>
          <div class="item-column">
            <span class="item-label">原材料质保书</span>
            <div class="item-content">
              <template v-if="detailInfo?.attachmentList?.length">
                <div v-for="item in detailInfo?.attachmentList" :key="item.id" class="file-item">
                  <span class="blue-color" @click="handleDownLoad(item)">{{ item.name }}</span>
                  <span v-if="isEditMode" class="el-icon-close" @click="handleDeleteFile(item)" />
                </div>
              </template>
              <template v-else-if="isEditMode">
                <div class="file-item">
                  <el-upload
                    action=""
                    :accept="accept"
                    :before-upload="beforeUpload"
                    :http-request="uploadReportHttp"
                    :limit="1"
                  >
                    <el-button :loading="detailLoading" icon="el-icon-upload2" round size="mini"> 上传文件 </el-button>
                  </el-upload>
                </div>
              </template>
              <span v-else class="file-name">无</span>
            </div>
          </div>
        </el-space>
        <div>
          <el-button :loading="detailLoading" size="small" @click="handleGoBack">返回</el-button>
          <!-- <el-button :loading="detailLoading" size="small" icon="el-icon-download" type="primary" @click="handleUpload"
            >导出</el-button
          > -->
          <el-button
            v-if="isEditMode"
            :loading="detailLoading"
            type="primary"
            size="small"
            icon="el-icon-receiving"
            @click="saveData()"
          >
            保存
          </el-button>
          <el-button
            v-if="isEditMode"
            :loading="detailLoading"
            type="primary"
            size="small"
            icon="el-icon-circle-check"
            @click="saveData(true)"
          >
            提交
          </el-button>
        </div>
      </div>
    </template>
    <div v-loading="detailLoading" class="container">
      <div class="wrapper">
        <el-aside v-show="showAside" class="flex flex-col justify-between" :style="{ width: asideWidth + 'px' }">
          <div class="flex flex-col">
            <p class="title">检测项目</p>
            <div :key="asideKey" class="left-main flex-1 overflow-y-auto">
              <div
                v-for="(item, index) in tableData"
                :key="item.id"
                class="tree-list select-none py-1 justify-around pr-2"
                :title="item.sourceName"
                @click="scrollJump(index)"
              >
                <div class="flex flex-row items-center flex-1 w-full overflow-hidden">
                  <span
                    :class="{
                      line: true,
                      'is-current': currentIndex === index,
                      'red-border': item.expResult === dictionaryAll['JCXMSHJLB'].enable['1'],
                      'green-border': item.expResult === dictionaryAll['JCXMSHJLB'].enable['0'],
                      'yellow-border':
                        item.expResult === '' || item.expResult === dictionaryAll['JCXMSHJLB'].enable['2']
                    }"
                  />
                  <span v-if="isEditMode" class="sortingIcon tes-move iconfont" />
                  <div class="text flex-1 ellipsis">{{ item.sourceName }}</div>
                </div>
                <el-icon v-if="isEditMode" :size="16" @click="handleDeleteItem(item, index)">
                  <i class="el-icon-delete blue-color" />
                </el-icon>
              </div>
            </div>
          </div>
          <el-button v-if="isEditMode" icon="el-icon-plus" plain type="primary" @click="handleAddStandard"
            >添加标准检验项目</el-button
          >
        </el-aside>
        <el-tooltip effect="dark" :content="collapseTip" :hide-after="0" placement="right">
          <DragHandle
            :class="{ 'close-handle': !showAside }"
            @widthChange="widthChange"
            @mouseup="collapseLeft"
            @mousedown="setOldAsideWidth"
          />
        </el-tooltip>
        <el-main ref="elMainRef">
          <div v-if="tableData.length != 0 || (isEditMode && tableData.length == 0)">
            <div v-for="(item, index) in tableData" :key="index" class="main-content">
              <div class="content-header flex-between expanded">
                <h4 class="title">{{ item.sourceName || '--' }}</h4>
                <div>
                  <span class="label">结论：</span>
                  <el-select
                    v-if="isEditMode"
                    v-model="item.expResult"
                    filterable
                    size="small"
                    clearable
                    placeholder="请选择结论"
                    class="header-select"
                  >
                    <el-option
                      v-for="(val, key) in dictionaryAll['JCXMSHJLB'].enable"
                      :key="key"
                      :label="val"
                      :value="val"
                    />
                  </el-select>
                  <span v-else>{{ item.expResult || '--' }}</span>
                </div>
              </div>
              <el-table
                :data="item.children"
                fit
                border
                height="auto"
                highlight-current-row
                class="dark-table base-table expand-table"
                @header-dragend="drageHeader"
              >
                <el-table-column prop="sourceName" label="关键参数" :width="180" show-overflow-tooltip>
                  <template #default="{ row }">
                    {{ row.sourceName || '--' }}
                  </template>
                </el-table-column>
                <el-table-column prop="expRequirement" label="技术要求" :width="180" show-overflow-tooltip>
                  <template #default="{ row }">
                    <el-input
                      v-if="isEditMode"
                      v-model="row.expRequirement"
                      placeholder="输入技术要求"
                      size="small"
                      :title="row.expRequirement"
                    />
                    <span v-else>{{ row.expRequirement || '--' }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="expValueUnit" label="单位" :width="120">
                  <template #default="{ row }">
                    <el-select
                      v-if="isEditMode"
                      v-model="row.expValueUnit"
                      placeholder="选择"
                      clearable
                      filterable
                      size="small"
                    >
                      <el-option
                        v-for="(val, key) in dictionaryAll['13'].enable"
                        :key="key"
                        :label="val"
                        :value="val"
                      />
                    </el-select>
                    <span v-else>{{ row.expValueUnit || '--' }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="value" label="检测结果" :min-width="180">
                  <template #default="{ row }">
                    <el-row v-if="isEditMode" class="item-input" :gutter="20">
                      <el-col :span="24">
                        <el-input v-model="row.expValue" size="small" placeholder="请输入" />
                      </el-col>
                    </el-row>
                    <template v-else>
                      <span>{{ row.expValue || '--' }}</span>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column prop="expResult" label="结论" :width="130">
                  <template #default="{ row }">
                    <el-select
                      v-if="isEditMode"
                      v-model="row.expResult"
                      placeholder="选择结论"
                      clearable
                      filterable
                      size="small"
                      @change="changeResult(row, index)"
                    >
                      <el-option
                        v-for="(val, key) in dictionaryAll['JCXMSHJLB'].enable"
                        :key="key"
                        :label="val"
                        :value="val"
                      />
                    </el-select>
                    <span v-else>{{ row.expResult || '--' }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <el-empty v-else-if="tableData.length == 0" :image="emptyImg" description="暂无数据" />
        </el-main>
      </div>
    </div>
    <JudgementStandard
      :show="showJudgementStandard"
      :tree="standardTreeData"
      :is-show-btn="false"
      :standard-model="standardModelInfo"
      @close="closeStandardDialog"
      @select-data="selectStandardData"
      @show-standard-item="showStandardItemDialog"
    />
    <AddStandardItem
      :show="showAddStandard"
      :tree="productTreeData"
      :latest-standard-product="latestStandardProduct"
      :data="addStandardItemList"
      @close="closeStandardItemDialog"
      @select-data="selectStandardItems"
    />
  </DetailLayout>
</template>

<script>
import { h, reactive, toRefs, ref, watch, onMounted, onBeforeUnmount, nextTick, getCurrentInstance } from 'vue';
import { useRoute } from 'vue-router';
import { ElDivider, ElMessage, ElMessageBox } from 'element-plus';
import Sortable from 'sortablejs';
import router from '@/router';
import { drageHeader } from '@/utils/formatTable';
import { formatTree } from '@/utils/formatJson';
import { getDictionary } from '@/api/user';
import { getTree } from '@/api/testBase';
import {
  inspectionrawmaterialqualityInfo,
  inspectionrawmaterialqualitySubmit,
  inspectionrawmaterialqualitySave,
  inspectionrawmaterialqualityDown,
  inspectionrawmaterialqualityDeleteFile,
  inspectionrawmaterialqualityUpload,
  inspectionrawmaterialqualityDeleteItem
} from '@/api/material-quality-book';
import { getStandardItemSort, getStandardProductVersion } from '@/api/capability';
import DetailLayout from '@/components/DetailLayout';
import DragHandle from '@/components/DragHandle/handle';
import emptyImg from '@/assets/img/empty-table.png';
// components
import JudgementStandard from '@/components/BusinessComponents/JudgementStandard.vue';
import AddStandardItem from '@/components/BusinessComponents/AddStandardItem';

export default {
  name: 'MaterialQualityBookDetail',
  components: { DetailLayout, DragHandle, JudgementStandard, AddStandardItem },
  setup() {
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    const state = reactive({
      id: route.query.id,
      showJudgementStandard: false,
      deleteItemIds: [],
      showAddStandard: false,
      detailLoading: false,
      accept: '*',
      standardTreeData: [],
      productTreeData: [],
      addStandardItemList: [],
      standardModelInfo: {},
      selectedStandardProduct: {},
      latestStandardProduct: {},
      dictionaryAll: {
        13: {
          enable: {},
          all: {}
        },
        JCXMSHJLB: {
          enable: {},
          all: {}
        }
      },
      detailInfo: {
        attachmentList: [],
        detailDTOList: []
      },
      tableData: [],
      templateJsonData: {
        isStandardCustom: 0
      }, // 模板信息
      tableKey: 'tableKey1',
      units: [],
      results: [],
      noWatch: false,
      currentIndex: 0,
      summarySelect: [],
      loadingStep2: false,
      selectRef: ref(),
      allValues: [],
      showAside: true,
      asideWidth: 280,
      asideMaxWidth: 600,
      asideMinWidth: 200,
      oldAsideWidth: 300,
      collapseTip: '点击折叠左面板',
      asideKey: 0,
      scrollTop: 0,
      isExistFile: false,
      file: null
    });
    const spacer = h(ElDivider, { direction: 'vertical' });
    const isEditMode = ref(route.name === 'MaterialQualityBookDetail');
    const elMainRef = ref();
    const fileRef = ref();

    watch(
      () => state.asideKey,
      () => {
        nextTick(() => {
          rowDrop();
        });
      }
    );

    onMounted(() => {
      getItemResultList();
      getStandardTree();
      if (route.query.id) {
        getData();
      } else {
        state.detailInfo = {
          ...route.query,
          attachmentList: [],
          detailDTOList: []
        };
      }
    });
    // 获取判定标准库左侧树
    async function getStandardTree() {
      await getTree(state.detailInfo.mateType || '266013').then(res => {
        const data = res.data.data;
        state.standardTreeData = formatTree(data);
        state.standardTreeData = [
          {
            id: '',
            isDeleted: false,
            parentId: '0',
            name: '全部',
            code: '全部',
            materialCategoryCode: state.detailInfo.mateType || '266013',
            order: 0
          }
        ].concat(state.standardTreeData);
      });
    }

    onBeforeUnmount(() => {
      beforeDestory();
    });

    const getItemResultList = () => {
      Object.keys(state.dictionaryAll).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          state.dictionaryAll[item] = {
            enable: {},
            all: {}
          };
          response.data.data.dictionaryoption.forEach(valOption => {
            if (valOption.status === 1) {
              state.dictionaryAll[item].enable[valOption.code] = valOption.name;
            }
            state.dictionaryAll[item].all[valOption.code] = valOption.name;
          });
        }
      });
    };

    const getData = async () => {
      const res = await inspectionrawmaterialqualityInfo(state.id);
      if (res) {
        state.detailInfo = res.data.data;
        state.tableData = state.detailInfo.detailDTOList;
        state.isExistFile = state.detailInfo.attachmentList?.length > 0;
        nextTick(afterRender);
      }
    };

    const saveData = async isSubmit => {
      const params = {
        ...state.detailInfo,
        detailDTOList: state.tableData
      };
      state.deleteItemIds.forEach(async item => {
        await inspectionrawmaterialqualityDeleteItem(item);
      });
      state.detailLoading = true;
      const { data } = isSubmit
        ? await inspectionrawmaterialqualitySubmit(params).finally((state.detailLoading = false))
        : await inspectionrawmaterialqualitySave(params).finally((state.detailLoading = false));
      if (data) {
        ElMessage.success(isSubmit ? '提交成功！' : '保存成功！');
        handleGoBack();
      }
    };

    const afterRender = () => {
      if (elMainRef.value?.$el) {
        elMainRef.value.$el.addEventListener('scroll', onScroll);
        // 监听鼠标滚动事件
        window.addEventListener('mousewheel', handleScroll, false) ||
          window.addEventListener('DOMMouseScroll', handleScroll, false);
      }
      rowDrop();
    };

    const beforeDestory = () => {
      if (elMainRef.value.$el) {
        // 卸载前监听鼠标滚动事件
        elMainRef.value.$el.removeEventListener('scroll', onScroll);
        window.removeEventListener('mousewheel', handleScroll, false) ||
          window.removeEventListener('DOMMouseScroll', handleScroll, false);
      }
    };

    const handleDeleteFile = item => {
      ElMessageBox({
        title: '提示',
        message: '是否删除原材料质保书',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      }).then(async () => {
        state.detailLoading = true;
        const { data } = await inspectionrawmaterialqualityDeleteFile(item.id).finally((state.detailLoading = false));
        if (data) {
          ElMessage.success('附件删除成功！');
          state.detailInfo.attachmentList = [];
        }
      });
    };

    const onSuccess = res => {
      if (res.code === 200) {
        state.detailInfo.attachmentList = [res.data];
        ElMessage.success('上传成功！');
      } else {
        ElMessage.error(res.message);
      }
    };

    const beforeUpload = file => {
      const fileSize = file.size / 1024 / 1024 < 20;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过20M');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };

    const uploadReportHttp = async options => {
      const formData = new FormData();
      formData.append('file', options.file);
      const { data } = await inspectionrawmaterialqualityUpload(formData);
      onSuccess(data);
    };

    const handleGoBack = () => {
      router.push({ name: 'MaterialQualityBook' });
    };
    const handleUpload = () => {};

    // 用 class 添加锚点
    const scrollJump = index => {
      state.currentIndex = index;
      const jump = document.querySelectorAll('.main-content');
      let total = 0;
      for (let i = 0; i < index; i++) {
        const marginBottomOffset = parseInt(window.getComputedStyle(jump[i]).getPropertyValue('margin-bottom'));
        total = total + jump[i].offsetHeight + marginBottomOffset;
      }
      const distance = state.scrollTop;
      // 平滑滚动，时长500ms，每10ms一跳，共50跳
      let step = total / 50;
      if (total > distance) {
        smoothDown(distance, step, total);
      } else {
        const newTotal = distance - total;
        step = newTotal / 50;
        smoothUp(distance, step, total);
      }
    };

    // 向下滑动
    const smoothDown = (distance, step, total) => {
      if (distance < total) {
        distance += step;
        elMainRef.value.$el.scrollTop = distance;
        setTimeout(function () {
          smoothDown(distance, step, total);
        }, 10);
      } else {
        elMainRef.value.$el.scrollTop = total;
      }
    };

    // 向上滑动
    const smoothUp = (distance, step, total) => {
      if (distance > total) {
        distance -= step;
        elMainRef.value.$el.scrollTop = distance;
        setTimeout(function () {
          smoothUp(distance, step, total);
        }, 10);
      } else {
        elMainRef.value.$el.scrollTop = total;
      }
    };

    // 结论-change
    const changeResult = (item, index) => {
      judgmentItemResult(state.tableData[index].children).then(res => {
        state.tableData[index].expResult = res;
      });
    };

    // 判断项目结论
    const judgmentItemResult = childArray => {
      return new Promise(resolve => {
        var itemResult = '';
        const childResult = Array.from(
          new Set(
            childArray.map(item => {
              return item.expResult;
            })
          )
        );
        if (childResult.length === 1) {
          itemResult = childResult[0];
        } else if (childResult.length === 2) {
          if (
            childResult.some(item => {
              return item === '不合格';
            })
          ) {
            // 含有不合格的，直接为不合格
            itemResult = '不合格';
          } else if (
            childResult.some(item => {
              return item === '不符合要求';
            })
          ) {
            // 含有不符合要求的，直接为不符合要求
            itemResult = '不符合要求';
          } else if (
            childResult.some(item => {
              return item === '合格';
            })
          ) {
            // 含有合格的，其他为不判定和空，直接为合格
            itemResult = '合格';
          } else if (
            childResult.some(item => {
              return item === '符合要求';
            })
          ) {
            // 含有符合要求的，其他为不判定和空，直接为符合要求
            itemResult = '符合要求';
          } else {
            // 只有不判定、空两种，检测项目结论为空
            itemResult = '';
          }
        } else if (childResult.length === 3) {
          if (
            childResult.some(item => {
              return item === '不合格';
            })
          ) {
            // 含有不合格的，直接为不合格
            itemResult = '不合格';
          } else if (
            childResult.some(item => {
              return item === '不符合要求';
            })
          ) {
            // 含有不符合要求的，直接为不符合要求
            itemResult = '不符合要求';
          } else if (
            childResult.some(item => {
              return item === '合格';
            })
          ) {
            // 只有不判定、空和合格三种，检测项目结论为合格
            itemResult = '合格';
          } else {
            // 只有不判定、空和符合要求三种，检测项目结论为符合要求
            itemResult = '符合要求';
          }
        } else {
          // 四种值都有，结论为不合格
          if (
            childResult.some(item => {
              return item === '不合格';
            })
          ) {
            itemResult = '不合格';
          } else {
            // 四种值都有，结论为不符合要求
            itemResult = '不符合要求';
          }
        }
        resolve(itemResult);
      });
    };

    // 获取滚动高度
    const onScroll = e => {
      state.scrollTop = e.target.scrollTop;
    };

    // 鼠标滚动时候，让判定标准下拉框隐藏
    const handleScroll = () => {
      if (state.tableData.length > 0) {
        state.tableData.forEach((td, index) => {
          const sname = 'selectRef' + 2 * index;
          if (proxy.$refs[sname]) {
            proxy.$refs[sname].blur();
          }
        });
      }
    };

    // 拖拽边框
    const widthChange = m => {
      state.asideWidth = state.asideWidth - m;
      if (state.asideWidth < state.asideMinWidth) {
        state.asideWidth = state.asideMinWidth;
      } else {
        if (state.asideWidth >= state.asideMaxWidth) {
          state.asideWidth = state.asideMaxWidth;
        }
      }
    };

    const setOldAsideWidth = () => {
      state.oldAsideWidth = state.asideWidth;
    };

    const collapseLeft = () => {
      if (state.asideWidth === state.oldAsideWidth) {
        state.showAside = !state.showAside;
        state.collapseTip = state.showAside ? '点击折叠左面板' : '点击展开左面板';
      }
    };

    // 拖拽功能
    const rowDrop = () => {
      if (!isEditMode.value) {
        return;
      }
      const tbody = document.querySelector('.left-main');
      Sortable.create(tbody, {
        handle: '.tes-move',
        draggable: '.tree-list',
        ghostClass: 'ghost',
        dragClass: 'drag',
        forceFallback: true,
        onEnd({ newIndex, oldIndex }) {
          // 移除原来的数据
          const currRow = state.tableData.splice(oldIndex, 1)[0];
          // 移除原来的数据并插入新的数据
          state.tableData.splice(newIndex, 0, currRow);
          state.tableData.forEach((value, index) => {
            value.order = index;
          });
          state.asideKey += 1;
        }
      });
    };
    const handleAddStandard = () => {
      state.showJudgementStandard = true;
    };
    const showStandardItemDialog = standardProduct => {
      state.selectedStandardProduct = standardProduct;
      getStandardProductVersion(standardProduct.id).then(res => {
        if (res && res.data.code === 200) {
          const versionList = res.data.data.versionList;
          if (versionList.length > 0) {
            const lastVersion = versionList[versionList.length - 1];
            lastVersion.productName = standardProduct.productName;
            state.latestStandardProduct = lastVersion;
            getProductTree(lastVersion.id).then(() => {
              state.showAddStandard = true;
            });
          }
        }
      });
    };
    async function getProductTree(productVersionId) {
      await getStandardItemSort(productVersionId).then(res => {
        const data = res.data.data;
        state.productTreeData = formatTree(data);
        state.productTreeData = [
          {
            id: '',
            isDeleted: false,
            parentId: '0',
            name: '全部',
            code: '全部',
            order: 0
          }
        ].concat(state.productTreeData);
      });
    }
    const selectStandardData = data => {
      console.log(data);
    };
    const closeStandardDialog = () => {
      state.showJudgementStandard = false;
    };
    function closeStandardItemDialog() {
      state.showAddStandard = false;
    }
    function selectStandardItems(standardCapabilityItems) {
      if (standardCapabilityItems) {
        let alreadyExistCount = 0;
        let existCount = 0;
        state.showJudgementStandard = false;
        state.showAddStandard = false;
        standardCapabilityItems.forEach(item => {
          const newItem = {};
          newItem.expRequirement = '';
          newItem.expResult = '';
          newItem.expValue = '';
          newItem.expValueUnit = '';
          newItem.inspectionRawMaterialQualityId = route.query.id;
          newItem.sourceId = item.capabilityId;
          newItem.sourceName = item.name;
          newItem.order = state.tableData.length;
          newItem.children = [];
          const newChildren = item.custList.filter(ele =>
            item.capabilityparaVoList.find(para => para.id === ele.capabilityParaId)
          );
          newChildren.forEach((obj, index) => {
            const newObj = {};
            newObj.sourceId = obj.capabilityParaId;
            newObj.sourceName = obj.name;
            newObj.expValueUnit = obj.unitname;
            newObj.expValue = '';
            newObj.expResult = '';
            newObj.expRequirement = obj.requirement;
            newObj.order = index;
            newItem.children.push(newObj);
          });
          if (state.tableData.findIndex(ele => ele.sourceId === newItem.sourceId) === -1) {
            existCount++;
            state.tableData.push(newItem);
          } else {
            alreadyExistCount++;
          }
        });
        ElMessage.success(`有${alreadyExistCount}个检测项目已存在，成功添加${existCount}个标准项目!`);
      }
    }

    const handleDownLoad = async file => {
      state.detailLoading = true;
      const { data } = await inspectionrawmaterialqualityDown(file.id).finally((state.detailLoading = false));
      if (data) {
        const reader = new FileReader();
        reader.addEventListener('loadend', () => {
          try {
            const resdata = JSON.parse(reader.result);
            if (resdata.code === 400) {
              ElMessage({
                message: resdata.message,
                type: 'error',
                duration: 3000
              });
            }
          } catch (error) {
            var fileName = file.name;
            var downloadElement = document.createElement('a');
            var href = window.URL.createObjectURL(data);
            downloadElement.style.display = 'none';
            downloadElement.href = href;
            downloadElement.download = decodeURI(fileName);
            document.body.appendChild(downloadElement);
            downloadElement.click();
            document.body.removeChild(downloadElement);
            window.URL.revokeObjectURL(href);
          }
        });
        reader.readAsText(data, 'utf-8');
      }
    };
    /** 删除检测项目 */
    const handleDeleteItem = (item, index) => {
      state.tableData.splice(index, 1);
      state.deleteItemIds.push(item.id);
    };
    return {
      ...toRefs(state),
      drageHeader,
      handleDeleteItem,
      handleUpload,
      handleDownLoad,
      saveData,
      beforeUpload,
      spacer,
      emptyImg,
      isEditMode,
      elMainRef,
      fileRef,
      handleDeleteFile,
      handleAddStandard,
      showStandardItemDialog,
      closeStandardItemDialog,
      selectStandardItems,
      selectStandardData,
      closeStandardDialog,
      onSuccess,
      uploadReportHttp,
      handleGoBack,
      scrollJump,
      changeResult,
      widthChange,
      setOldAsideWidth,
      collapseLeft
    };
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-divider--vertical) {
  height: 40px;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.flex-start {
  justify-content: space-between !important;
}

.container {
  height: 100%;

  .wrapper {
    display: flex;
    width: 100%;
    height: 100%;
  }

  :deep(.el-table .hidden-row) {
    display: none;
  }
  .exponent {
    width: 44px;
    position: absolute;
    top: -10px;
    left: 26px;
    :deep(.el-input__inner) {
      height: 20px;
      padding: 0 5px;
    }
  }
  .zsNumber {
    display: inline-block;
    position: relative;
    margin-left: 5px;
  }
  .el-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    background: $background-color;
    width: 100%;
  }
  .el-aside {
    background: $background-color;
    margin-bottom: 0;
    padding: 14px 20px 20px;
    p.title {
      text-align: left;
      margin: 0;
      font-size: 16px;
      font-weight: bold;
      padding-bottom: 5px;
      border-bottom: 1px solid #ebeef5;
    }
    .left-main {
      max-height: calc(100vh - 300px);
      overflow: auto;
    }
    .tree-list {
      display: flex;
      align-items: center;
      text-align: left;
      border-bottom: 1px solid #ebeef5;
      cursor: pointer;
      &:hover {
        background: $tes-primary2;
      }
      &:active {
        background: $tes-primary2;
      }
      .line {
        display: inline-block;
        width: 4px;
        height: 16px;
        margin-right: 12px;
      }
      .custom-icon {
        width: 20px;
        height: 20px;
        font-size: 14px;
        margin-right: 12px;
      }
      .red-border {
        background: $red;
      }
      .green-border {
        background: $green;
      }
      .yellow-border {
        background: $yellow;
      }

      .sortingIcon {
        margin-right: 5px;
        font-size: 10px;
        cursor: move;
      }
      .text {
        font-size: 14px;
        // max-width: calc(100% - 40px);
        // overflow: hidden;
        // text-overflow: ellipsis;
        // word-break: break-all;
      }
    }
    .ghost {
      background-color: #e6f8f4 !important;
    }
    .drag {
      background: #e6f8f4 !important;
      background-image: linear-gradient(#e9e9eb, #ffffff) !important;
    }
  }
  .close-handle {
    height: calc(100vh - 24rem);
    position: absolute;
    left: -24px;
  }
  .el-main {
    padding: 0;
    overflow-y: auto;
  }
  .main-content {
    background: $background-color;
    padding: 20px;
    &:not(:last-of-type) {
      margin-bottom: 20px;
    }
    .content-header {
      display: flex;
      width: 100%;
      height: 32px;
      align-items: center;
      &.flex-between {
        width: 100%;
        justify-content: space-between;
      }
      .header-select {
        display: inline-block;
        width: 240px;
      }
      .title {
        font-size: 16px;
        font-weight: bold;
        color: $tes-primary;
        margin: 0;
      }
      .label {
        color: $tes-font2;
      }
      :deep(.el-checkbox__input) {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .expanded {
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
      position: relative;
    }
    .marginBottom {
      margin-bottom: 8px;
    }
    .item-input {
      display: flex;
      .input-group {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &.group:deep(.el-input-group__prepend) {
          padding: 0 4px;
          max-width: 60px;
          overflow: hidden;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        &.group:deep(.el-input__inner) {
          padding: 0 7px;
          min-width: 40px;
        }
      }
    }

    .exp-item-gap {
      margin-left: 10px;
      &:first-child {
        margin-left: 0;
      }
    }
  }
}

.file-item {
  display: flex;
  align-items: center;

  &:hover .el-icon-close {
    opacity: 1;
  }

  .el-icon-close {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    font-size: 18px;
    cursor: pointer;
  }
}

.file-name {
  max-width: 300px;
  line-height: 30px;
  text-overflow: ellipsis;
  font-size: 16px;
  text-align: left;
}
</style>
