<template>
  <el-dialog
    :ref="dialogRef"
    v-model="dialogShow"
    modal-class="dialogTemplateOnline"
    :before-close="handleCancle"
    :close-on-click-modal="false"
    :title="isAdd ? '创建模板' : '编辑模板'"
    :width="520"
  >
    <el-form
      v-if="dialogShow"
      ref="refForm"
      :model="formData"
      :hide-required-asterisk="true"
      :rules="rules"
      label-position="left"
      label-width="82px"
    >
      <el-form-item prop="version">
        <template #label>
          <div class="requireStar">版本号</div>
        </template>
        <el-input v-model="formData.version" :disabled="!isAdd" placeholder="请输入版本号" autocomplete="off" />
      </el-form-item>
      <el-form-item prop="fileNo">
        <template #label>
          <div class="requireStar">文件编号</div>
        </template>
        <el-input v-model="formData.fileNo" placeholder="请输入文件编号" autocomplete="off" />
      </el-form-item>
      <el-form-item label="版本描述" prop="description">
        <el-input v-model="formData.description" placeholder="请输入版本描述" type="textarea" />
      </el-form-item>
      <el-form-item label="关联项目">
        <div class="link-item"><i class="el-icon-link" /> {{ capabilityNumber + ' ' + capabilityName }}</div>
      </el-form-item>
      <div v-if="isAdd" class="operate-type">
        <div class="operate-item online" @click="handleChange(!checkOnline, 'checkOnline')">
          <div class="text">
            <el-checkbox
              v-model="checkOnline"
              @click.stop
              @change.self="
                val => {
                  return handleChangeBox(val, 'checkOnline');
                }
              "
            />
            <span class="text-title">在线设计</span>
            <span>可视化表格编辑器</span>
          </div>
        </div>
        <div class="operate-item copy" @click="handleChange(!checkCopy, 'checkCopy')">
          <div class="text">
            <el-checkbox
              v-model="checkCopy"
              @click.stop
              @change.self="
                val => {
                  return handleChangeBox(val, 'checkCopy');
                }
              "
            />
            <span class="text-title">复制模板</span>
            <span>引用已维护的模板</span>
          </div>
        </div>
      </div>
      <div v-else class="template-online">
        <SvgIcon icon-class="template-online-edit" :width="218" :height="74" />
        <div class="text">
          <span class="text-title">在线设计</span>
          <span>可视化界面，轻松编辑报告模板</span>
        </div>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button text @click="handleCancle()">取 消</el-button>
        <el-button type="primary" @click="onDesign()">确认 <i class="el-icon-right" /></el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 选择检测项目 -->
  <AddSingleItem
    :show="dialogSingleItem"
    :filter-template="true"
    @selectData="selectSingleDialog"
    @close="closeSingleDialog"
  />
</template>

<script>
import { reactive, toRefs, watch, ref, getCurrentInstance } from 'vue';
import { useRoute } from 'vue-router';
import { getPermissionBtn } from '@/utils/common';
import { getGenerator } from '@/api/businessCode';
import router from '@/router';
import SvgIcon from '@/components/SvgIcon';
import AddSingleItem from '@/components/BusinessComponents/AddSingleItem';
import emptyImg from '@/assets/img/empty-template.png';

export default {
  name: 'DialogTemplateOnline',
  components: { SvgIcon, AddSingleItem },
  props: {
    dialogVisiable: {
      type: Boolean,
      default: false
    },
    isAdd: {
      type: Boolean,
      default: false
    },
    formInfo: {
      type: Object,
      default: () => {}
    },
    releasedTemplates: {
      type: Array,
      default: () => {
        return [];
      }
    },
    unreleasedTemplates: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    watch(
      () => props.dialogVisiable,
      newValue => {
        state.dialogShow = newValue;
        if (newValue) {
          state.isAdd = props.isAdd;
          state.releasedTemplates = props.releasedTemplates;
          state.unreleasedTemplates = props.unreleasedTemplates;
          if (state.isAdd) {
            state.checkOnline = true;
            getVersion();
          } else {
            state.formData = JSON.parse(JSON.stringify(props.formInfo));
          }
        }
      }
    );
    const state = reactive({
      dialogShow: false,
      dialogSingleItem: false,
      dialogRef: ref(),
      refForm: ref(),
      checkOnline: false, // 在线设计
      checkCopy: false, // 复制勾选
      isAdd: false,
      isCopy: false, // 是否是复制状态
      capabilityId: route.query.capabilityId,
      capabilityName: route.query.capabilityName,
      capabilityNumber: route.query.capabilityNumber,
      formData: {
        groupNumber: 0,
        isUseStandard: 0,
        status: 1,
        type: 0,
        isDefault: 0,
        coreNumber: 0
      },
      dialogTemplateOnline: false, // 在线设计模板弹出框
      rules: {
        version: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入版本号'
          }
        ],
        fileNo: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入文件编号'
          }
        ]
      },
      releasedTemplates: [],
      unreleasedTemplates: []
    });
    const handleChange = (val, type) => {
      state.checkCopy = false;
      state.checkOnline = false;
      state[type] = val;
    };
    const handleChangeBox = (val, type) => {
      if (type === 'checkCopy') {
        state.checkOnline = false;
      } else {
        state.checkCopy = false;
      }
    };
    const handleCancle = () => {
      state.dialogShow = false;
      context.emit('closeDialog');
    };
    const getVersion = () => {
      getGenerator('19').then(res => {
        if (res.data.code === 200) {
          state.formData.version = res.data.data;
        }
      });
    };

    const onUnreleasedTemplateClick = designTemplateId => {
      gotoDesignTemplatePage('update', { designTemplateId });
    };
    const onDesign = () => {
      state.refForm.validate(valid => {
        if (valid) {
          if (!state.checkCopy && !state.checkOnline && state.isAdd) {
            proxy.$message.warning('请选择创建方式');
            return;
          }
          if (state.checkCopy) {
            state.dialogSingleItem = true;
            return;
          }
          const query = {};
          if (!state.isAdd) {
            const item = state.releasedTemplates.find(item => item.version === state.formData.version);
            if (item) {
              query.designTemplateId = item.designTemplateId;
            } else {
              proxy.$message.warning('非在线创建模板不支持在线编辑');
              return;
            }
          }
          gotoDesignTemplatePage(state.isAdd ? 'create' : 'update', query);
        }
      });
    };

    const gotoDesignTemplatePage = (type, query) => {
      const { DEV, VITE_TEMPLATE_EDITOR, VITE_TEMPLATE_EDITOR_ORIGIN } = import.meta.env;
      const templateEditorPagePath = router.resolve({
        path: `${VITE_TEMPLATE_EDITOR}/design/${type}`,
        query: {
          capabilityId: route.query.capabilityId,
          capabilityName: route.query.capabilityName,
          capabilityNumber: route.query.capabilityNumber,
          version: state.formData.version,
          fileNo: state.formData.fileNo,
          description: state.formData.description,
          ...query
        }
      }).href;
      // router.push({
      //   path: '/experiment/designDetail/'
      // });
      window.location.href = (DEV ? VITE_TEMPLATE_EDITOR_ORIGIN : '') + templateEditorPagePath;
    };

    // 单选中的检测项目
    const selectSingleDialog = val => {
      if (val) {
        gotoDesignTemplatePage('copy', { capabilityIdCopy: val.id, versionCopy: val.templateVersion });
      } else {
        state.dialogSingleItem = false;
      }
    };
    // 关闭单选检测项目弹出框
    const closeSingleDialog = () => {
      state.dialogSingleItem = false;
    };

    return {
      ...toRefs(state),
      emptyImg,
      closeSingleDialog,
      selectSingleDialog,
      handleChangeBox,
      handleChange,
      onUnreleasedTemplateClick,
      onDesign,
      handleCancle,
      getPermissionBtn
    };
  }
};
</script>

<style lang="scss" scoped>
.link-item {
  background-color: #f4f4f5;
  border: 1px solid #dedfe0;
  border-radius: 4px;
  padding: 0 12px;
}

.requireStar::after {
  vertical-align: middle;
  content: ' *';
  color: var(--el-color-danger);
  font-size: 100%;
}

.operate-type {
  display: flex;
  gap: 12px;
  .operate-item {
    flex-grow: 1;
    border-radius: 8px;
    padding: 6px 18px 20px 18px;
    cursor: pointer;
    user-select: none;
    &:hover {
      filter: brightness(102%);
    }
    :deep(.el-checkbox) {
      height: 30px;
    }
    .text {
      display: flex;
      align-items: flex-end;
      flex-direction: column;
      font-size: 12px;
      gap: 4px;
    }
    .text-title {
      font-size: 18px;
    }
  }
  .online {
    border: 1px solid #c7eedb;
    background: url('../../../assets/img/template-online-bg.png') no-repeat;
    background-size: 100% 100%;
    .text {
      color: #61be9d;
    }
    .text-title {
      color: #006644;
      font-weight: 600;
    }
  }
  .copy {
    border: 1px solid #caeaee;
    background: url('../../../assets/img/template-copy-bg.png') no-repeat;
    background-size: 100% 100%;
    .text {
      color: #63bebb;
    }
    .text-title {
      color: #007777;
      font-weight: 600;
    }
  }
}
.template-online {
  padding: 0 32px 0 0;
  display: flex;
  flex-direction: row;
  position: relative;
  justify-content: flex-end;
  border-radius: 8px;
  overflow: hidden;
  user-select: none;
  border: 1px solid #97d8b8;
  background: linear-gradient(90deg, #97d8b8 0%, #c7eedb 45%, #eef9f2 100%);
  .svg-icon {
    position: absolute;
    top: -60px;
    left: 5px;
  }
  .text {
    font-size: 12px;
    gap: 8px;
    padding: 18px 0;
    display: flex;
    align-items: flex-end;
    flex-direction: column;
  }
  .text-title {
    font-size: 18px;
    font-weight: 600;
    color: #006644;
  }
  .text {
    color: #61be9d;
  }
}
</style>
<style lang="scss">
.dialogTemplateOnline {
  .el-dialog {
    border-radius: 12px;
    background: linear-gradient(180deg, #e6f8f4 0%, #ffffff 26%);
    .el-dialog__header {
      padding: 40px 60px;
      font-size: 22px;
      height: 102px;
      font-weight: 500;
      color: #303133;
    }
    .el-dialog__headerbtn {
      top: 41px;
      right: 60px;
    }
    .el-dialog__body {
      padding: 0px 60px;
    }
    .el-dialog__footer {
      padding: 32px 60px;
    }
    .el-dialog__close {
      font-size: 16px;
    }
  }
}
</style>
