<template>
  <!-- 设备报废详情 -->
  <DetailLayout :main-offset-top="104">
    <template #page-header>
      <div class="header-flex flex-between">
        <el-space :size="40" :spacer="spacer">
          <div class="item-column">
            <span class="item-label">申请单号：</span>
            <div class="item-content" style="position: relative">
              {{ detailInfo.applyNo || '--' }}
              <el-tag v-if="detailInfo.status" size="small" effect="dark" :type="statusType[detailInfo.status]">{{
                stateJson[detailInfo.status]
              }}</el-tag>
            </div>
          </div>
          <div v-if="detailInfo.createBy" class="item-column">
            <span class="item-label">创建人：</span>
            <div class="item-content">
              <span class="iconfont tes-user1" />{{ detailInfo.createBy ? getNameByid(detailInfo.createBy) : '--' }}
            </div>
          </div>
          <div v-if="detailInfo.createTime" class="item-column">
            <span class="item-label">创建日期：</span>
            <div class="item-content">
              {{ detailInfo.createTime ? formatDate(detailInfo.createTime) : '--' }}
            </div>
          </div>
        </el-space>
        <div class="btn-group">
          <el-button size="large" @click="goBack()">返回列表</el-button>
          <el-button
            v-if="getPermissionBtn('DeleteDeviceScrap') && isCanEdit && detailInfo.applyNo"
            :loading="detailLoading"
            type="danger"
            size="large"
            icon="el-icon-delete"
            @click="deleteDetail"
            @keyup.prevent
            @keydown.enter.prevent
            >删除</el-button
          >
          <el-button
            v-if="getPermissionBtn('storageEquipmentScrap') && isCanEdit"
            :loading="detailLoading"
            type="primary"
            size="large"
            icon="el-icon-receiving"
            @click="saveDetail('save')"
            @keyup.prevent
            @keydown.enter.prevent
            >保存</el-button
          >
          <el-button
            v-if="getPermissionBtn('submitEquipmentScrap') && isCanEdit"
            :loading="detailLoading"
            type="warning"
            size="large"
            icon="el-icon-position"
            @click="saveDetail('submit')"
            @keyup.prevent
            @keydown.enter.prevent
            >提交</el-button
          >
          <el-button
            v-if="getPermissionBtn('approvalEquipmentScrap') && detailInfo.status === 1"
            type="warning"
            size="large"
            icon="el-icon-position"
            :disabled="!isCanApprove"
            @click="handleApprove"
            @keyup.prevent
            @keydown.enter.prevent
            >审批</el-button
          >
        </div>
      </div>
    </template>
    <div class="page-main">
      <div class="panel-headline">
        <div class="title">报废设备信息</div>
        <el-button
          v-if="isCanEdit"
          size="small"
          icon="el-icon-plus"
          @click="dialogEquipment = true"
          @keyup.prevent
          @keydown.enter.prevent
          >添加设备</el-button
        >
      </div>
      <div class="panel-content">
        <el-table
          :data="detailInfo.deviceList"
          class="dark-table base-table format-height-table"
          fit
          border
          height="auto"
          highlight-current-row
          size="medium"
          @header-dragend="drageHeader"
        >
          <el-table-column label="设备编号" prop="deviceNumber" width="160" align="left" show-overflow-tooltip>
            <template #default="{ row }">
              <div>
                <el-tag v-if="row.fixedFlag" size="mini" class="title-status" type="danger">维修</el-tag>
                {{ row.deviceNumber || '--' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="设备名称" prop="name" width="260">
            <template #default="{ row }">
              <span>{{ row.name || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="型号规格" prop="model" min-width="80" align="center">
            <template #default="{ row }">
              <span>{{ row.model }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="生产厂商"
            prop="equipmentManufactureName"
            :width="colWidth.people"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span>{{ row.equipmentManufactureName || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="使用部门" prop="departmentName" align="left" :min-width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <div>{{ row.departmentName || '--' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="购买/进厂日期" prop="arrivalDate" :width="colWidth.people">
            <template #default="{ row }">
              <div>{{ formatDate(row.arrivalDate) || '--' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="已使用年限/保质期" prop="createTime" :width="colWidth.dateranger">
            <template #default="{ row }">
              <span>{{ row.workingLife || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="isCanEdit" label="操作" :width="colWidth.operation" class-name="fixed-right">
            <template #default="{ $index }">
              <span class="blue-color" @click="deleteRow($index)">删除</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-row style="margin-top: 20px">
      <el-col :span="18">
        <div class="page-main">
          <div class="panel-headline">
            <div class="title">报废说明</div>
          </div>
          <el-form ref="editFromRef" :model="detailInfo" label-width="140px">
            <el-form-item
              prop="scrapReason"
              label="报废原因："
              :rules="{ required: isCanEdit, message: '请输入报废原因', trigger: 'change' }"
            >
              <el-input
                v-if="isCanEdit"
                v-model="detailInfo.scrapReason"
                type="textarea"
                maxlength="300"
                :rows="3"
                placeholder="请输入报废原因"
              />
              <div v-else>{{ detailInfo.scrapReason || '--' }}</div>
            </el-form-item>
            <el-form-item prop="usefulAccessory" label="可利用主要配件：">
              <el-input
                v-if="isCanEdit"
                v-model="detailInfo.usefulAccessory"
                type="textarea"
                maxlength="300"
                :rows="3"
                placeholder="请输入可利用主要配件"
              />
              <div v-else>{{ detailInfo.usefulAccessory || '--' }}</div>
            </el-form-item>
            <el-form-item prop="techIdentify" label="技术鉴定：">
              <el-input
                v-if="isCanEdit"
                v-model="detailInfo.techIdentify"
                type="textarea"
                maxlength="300"
                :rows="3"
                placeholder="请输入技术鉴定"
              />
              <div v-else>{{ detailInfo.techIdentify || '--' }}</div>
            </el-form-item>
            <el-form-item prop="remark" label="备注：">
              <el-input
                v-if="isCanEdit"
                v-model="detailInfo.remark"
                type="textarea"
                maxlength="300"
                :rows="3"
                placeholder="请输入备注"
              />
              <div v-else>{{ detailInfo.remark || '--' }}</div>
            </el-form-item>
            <el-row>
              <el-col :span="10">
                <el-form-item
                  prop="applyBy"
                  label="申请人："
                  :rules="{ required: isCanEdit, message: '请选择申请人', trigger: 'change' }"
                >
                  <el-select v-if="isCanEdit" v-model="detailInfo.applyBy" placeholder="请选择申请人" clearable>
                    <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select>
                  <div v-else>{{ getNameByid(detailInfo.applyBy) || '--' }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="10" :offset="4">
                <el-form-item
                  prop="applyDate"
                  label="申请日期："
                  :rules="{ required: isCanEdit, message: '请选择申请日期', trigger: 'change' }"
                >
                  <el-date-picker
                    v-if="isCanEdit"
                    v-model="detailInfo.applyDate"
                    type="date"
                    clearable
                    placeholder="请选择申请日期"
                  />
                  <div v-else>{{ detailInfo.applyDate || '--' }}</div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-col>
      <el-col :span="6" style="padding-left: 20px">
        <div class="page-main">
          <div class="panel-headline">
            <div class="title">
              审批流程
              <span v-if="approveHistoryList.length > 0" class="historyClass" @click="showDrawer = true">审批历史</span>
            </div>
          </div>
          <el-timeline class="approveTimeLine">
            <el-timeline-item v-for="item in approveList" :key="item.name" :timestamp="item.endTime" placement="bottom">
              <div v-if="item.childList.length === 0">
                <div class="approveItem">
                  <span class="approveName">{{ item.name }}</span>
                  <el-tag
                    v-if="item.processParameter.isAssent === 1"
                    class="approveStatus"
                    size="small"
                    effect="dark"
                    type="success"
                    >{{ approveStatusJson[item.processParameter.isAssent] }}</el-tag
                  >
                  <el-tag
                    v-else-if="item.processParameter.isAssent === 0"
                    class="approveStatus"
                    size="small"
                    effect="dark"
                    type="danger"
                    >{{ approveStatusJson[item.processParameter.isAssent] }}</el-tag
                  >
                  <el-tag v-else class="approveStatus" size="small" effect="dark" type="warning">未审批</el-tag>
                </div>
                <div v-if="item.processParameter.opinion" class="opinionContent">
                  {{ item.processParameter.opinion }}
                </div>
              </div>
              <div v-else>
                <div class="approveItem">
                  <span class="approveName">{{ item.name }}</span>
                </div>
                <div class="childApprove">
                  <div v-for="val in item.childList" :key="val.name">
                    <div class="approveItem">
                      <span class="approveName">{{ val.name }}</span>
                      <el-tag
                        v-if="val.processParameter.isAssent === 1"
                        class="approveStatus"
                        size="mini"
                        effect="dark"
                        type="success"
                        >{{ approveStatusJson[val.processParameter.isAssent] }}</el-tag
                      >
                      <el-tag
                        v-else-if="val.processParameter.isAssent === 0"
                        class="approveStatus"
                        size="mini"
                        effect="dark"
                        type="danger"
                        >{{ approveStatusJson[val.processParameter.isAssent] }}</el-tag
                      >
                      <el-tag v-else class="approveStatus" effect="dark" size="mini" type="warning">未审批</el-tag>
                    </div>
                    <div v-if="val.processParameter.opinion" class="opinionContent">
                      {{ val.processParameter.opinion }}
                    </div>
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-col>
    </el-row>
    <DialogEquipment :dialog-visible="dialogEquipment" :already="detailInfo.deviceList" @closeDialog="closeEquipment" />
    <!-- 审批列表 -->
    <el-dialog
      v-model="dialogApprove"
      title="作废审批"
      :close-on-click-modal="false"
      width="480px"
      custom-class="submit_dialog2"
    >
      <el-form
        v-if="dialogApprove"
        ref="ruleForm"
        v-loading="approveLoading"
        :model="formDataApprove"
        label-position="top"
        label-width="110px"
      >
        <el-form-item
          label="请选择审核结果："
          prop="isAssent"
          :rules="{ required: true, message: '请选择审批结果', trigger: 'change' }"
        >
          <el-radio-group v-model="formDataApprove.isAssent" class="radioGroup">
            <el-radio :label="1" border class="pass">通过</el-radio>
            <el-radio :label="0" border class="sendBack fr">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审批说明：" prop="opinion">
          <el-input
            v-model="formDataApprove.opinion"
            type="textarea"
            maxlength="250"
            :rows="4"
            placeholder="请输入审批说明"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button :loading="approveLoading" @click="dialogApprove = false">取 消</el-button>
          <el-button
            type="primary"
            :loading="approveLoading"
            @click="onSubmitApprove"
            @keyup.prevent
            @keydown.enter.prevent
            >确 认</el-button
          >
        </span>
      </template>
    </el-dialog>
    <!-- 审批历史 -->
    <el-drawer
      v-model="showDrawer"
      title="审批历史"
      direction="rtl"
      :size="500"
      destroy-on-close
      custom-class="page-drawer"
    >
      <DrawerLayout :has-button-group="false" :has-page-header="false">
        <el-timeline class="drawerTime">
          <el-timeline-item
            v-for="item in approveHistoryList"
            :key="item.name"
            :timestamp="item.endTime"
            placement="bottom"
          >
            <div v-if="item.name === '提交流程' || item.name === '重新提交报废申请单'" class="approveItem">
              <span class="approveName">{{ getNameByid(item.startUser) }}</span>
              <el-tag class="approveStatus" effect="dark" size="small" type="info">提交审批</el-tag>
            </div>
            <div v-else-if="item.childList.length === 0">
              <div class="approveItem">
                <span class="approveName">{{ item.name }}</span>
                <el-tag
                  v-if="item.processParameter.isAssent === 1"
                  class="approveStatus"
                  size="small"
                  effect="dark"
                  type="success"
                  >{{ approveStatusJson[item.processParameter.isAssent] }}</el-tag
                >
                <el-tag
                  v-else-if="item.processParameter.isAssent === 0"
                  class="approveStatus"
                  size="small"
                  effect="dark"
                  type="danger"
                  >{{ approveStatusJson[item.processParameter.isAssent] }}</el-tag
                >
                <el-tag v-else class="approveStatus" size="small" effect="dark" type="warning">未审批</el-tag>
              </div>
              <div v-if="item.processParameter.opinion" class="opinionContent">{{ item.processParameter.opinion }}</div>
            </div>
            <div v-else>
              <div class="approveItem">
                <span class="approveName">{{ item.name }}</span>
              </div>
              <div class="childApprove">
                <div v-for="val in item.childList" :key="val.name">
                  <div class="approveItem">
                    <span class="approveName">{{ val.name }}</span>
                    <el-tag
                      v-if="val.processParameter.isAssent === 1"
                      class="approveStatus"
                      size="mini"
                      effect="dark"
                      type="success"
                      >{{ approveStatusJson[val.processParameter.isAssent] }}</el-tag
                    >
                    <el-tag
                      v-if="val.processParameter.isAssent === 0"
                      class="approveStatus"
                      size="mini"
                      effect="dark"
                      type="danger"
                      >{{ approveStatusJson[val.processParameter.isAssent] }}</el-tag
                    >
                    <el-tag
                      v-if="val.processParameter.isAssent === 2"
                      class="approveStatus"
                      size="mini"
                      effect="dark"
                      type="warning"
                      >{{ approveStatusJson[val.processParameter.isAssent] }}</el-tag
                    >
                  </div>
                  <div v-if="val.processParameter.opinion" class="opinionContent">
                    {{ val.processParameter.opinion }}
                  </div>
                </div>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </DrawerLayout>
    </el-drawer>
  </DetailLayout>
</template>

<script>
import { reactive, toRefs, ref, getCurrentInstance, h } from 'vue';
import router from '@/router/index.js';
import { ElMessage, ElMessageBox, ElDivider } from 'element-plus';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { getLoginInfo } from '@/utils/auth';
import { cancelInspection } from '@/api/inspection-application';
import {
  getScrappingDetail,
  findAllUserTaskName,
  saveInfoApi,
  submitInfoApi,
  deleteApply,
  processHistoryApi,
  processExecuteApi
} from '@/api/scrapping';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import DetailLayout from '@/components/DetailLayout';
import DrawerLayout from '@/components/DrawerLayout';
import DialogEquipment from './DialogEquipment';

export default {
  name: 'EquipmentScrappingDetail',
  components: { DetailLayout, DialogEquipment, DrawerLayout },
  setup() {
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    const store = useStore().state;
    const spacer = h(ElDivider, { direction: 'vertical' });
    const state = reactive({
      tableList: [],
      dialogEquipment: false,
      detailInfo: {
        deviceList: [],
        applyDate: formatDate(new Date()),
        applyBy: getLoginInfo().accountId
      },
      formDataApprove: {}, // 审批表单
      dialogApprove: false,
      stateJson: {
        0: '待提交',
        1: '审批中',
        2: '已完成',
        3: '待提交'
      },
      statusType: {
        // 申请单状态对应的tag类型
        2: 'success',
        1: 'warning',
        0: 'info',
        3: 'info'
      },
      approveList: [],
      approveStatusJson: {
        2: '未审批',
        1: '已通过',
        0: '不通过'
      },
      isCanEdit: false, // 判断是否能编辑
      isCanApprove: false, // 是否能审批
      editFromRef: ref(),
      showDrawer: false, // 审批历史
      currentAccountName: getLoginInfo().username,
      accountId: getLoginInfo().accountId,
      approveLoading: false,
      detailLoading: false,
      approveHistoryList: [], // 审批历史记录
      userOptions: store.common.nameList
    });
    // 获取报废信息
    const getDetailInfo = () => {
      state.detailLoading = true;
      getScrappingDetail(route.query.id).then(res => {
        state.detailLoading = false;
        if (res) {
          state.detailInfo = res.data.data;
          if (
            (state.detailInfo.status === 0 || state.detailInfo.status === 3) &&
            state.detailInfo.createBy === state.accountId
          ) {
            state.isCanEdit = true;
          } else {
            state.isCanEdit = false;
          }
          // 获取审批流程
          if (state.detailInfo.processInstanceId) {
            getProcessHistory();
          }
        }
      });
    };
    const getTaskName = () => {
      findAllUserTaskName().then(res => {
        if (res) {
          const data = res.data.data;
          data.forEach(item => {
            if (item.split(',').length > 1) {
              const newJson = {
                name: item,
                childList: [],
                processParameter: { isAssent: 2 }
              };
              item.split(',').forEach(val => {
                newJson.childList.push({
                  name: val,
                  processParameter: { isAssent: 2 },
                  childList: []
                });
              });
              state.approveList.push(newJson);
            } else {
              state.approveList.push({ name: item, processParameter: { isAssent: 2 }, childList: [] });
            }
          });
        }
      });
    };
    const getProcessHistory = () => {
      processHistoryApi(state.detailInfo.processInstanceId).then(res => {
        if (res) {
          const data = res.data.data;
          // // 总的审批历史
          state.approveHistoryList = JSON.parse(JSON.stringify(data));
          if (state.detailInfo.status === 2) {
            // 审批流程已完结
            state.approveList = data.slice(-state.approveList.length);
          } else {
            const indexArray = [];
            state.approveHistoryList.forEach((item, index) => {
              if (item.name === '提交流程' || item.name === '重新提交报废申请单') {
                indexArray.push(index);
              }
            });
            // 新一轮审批流程的数组
            const newHistoryAll = JSON.parse(JSON.stringify(data));
            const newHistory = newHistoryAll.splice(indexArray[indexArray.length - 1]);
            if (newHistory.length > 0) {
              newHistory.shift();
              for (var i = 0; i < newHistory.length; i++) {
                state.approveList[i] = newHistory[i];
              }
              const lastApproveNode = newHistory[newHistory.length - 1];
              if (lastApproveNode) {
                if (lastApproveNode.childList.length > 0) {
                  state.isCanApprove = lastApproveNode.childList.some(val => {
                    return val.assignee === state.currentAccountName && !val.endTime;
                  });
                } else {
                  state.isCanApprove = lastApproveNode.assignee === state.currentAccountName;
                }
              }
            }
          }
        }
      });
    };
    // 判断是否是新增, 要是有id调用详情接口
    if (route.query.id) {
      getDetailInfo();
    } else {
      state.isCanEdit = true;
    }
    // 获取审批节点名称
    getTaskName();
    // 作废
    const cancleIA = () => {
      // console.log(state.detailInfo)
      ElMessageBox({
        title: '提示',
        message: '确定作废当前检验申请吗？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          cancelInspection(state.detailInfo.id).then(res => {
            if (res !== false) {
              ElMessage.success('编号:' + state.detailInfo.no + ',已作废');
              router.push({
                query: { ...route.query, flag: 1 }
              });
              getDetailInfo();
            }
          });
        })
        .catch(() => {});
    };
    // 编辑
    const editInfo = () => {
      // console.log(state.detailInfo)
      state.showEditInfoDialog = true;
    };
    // 编辑弹出框-关闭
    const closeEditInfo = v => {
      // console.log(v)
      state.showEditInfoDialog = false;
      getDetailInfo();
    };
    const closeEquipment = data => {
      state.dialogEquipment = false;
      if (data) {
        state.detailInfo.deviceList = data;
      }
    };
    const goBack = status => {
      router.push({
        path: '/equipmentManagement/equipmentScrapping',
        query: {
          status: status || route.query.status
        }
      });
    };
    // 保存/提交详情
    const saveDetail = type => {
      if (state.detailInfo.deviceList.length === 0) {
        proxy.$message.error('请先选择报废设备');
        return false;
      }
      proxy.$refs['editFromRef'].validate(valid => {
        if (valid) {
          if (type === 'save') {
            state.detailLoading = true;
            saveInfoApi(state.detailInfo).then(res => {
              state.detailLoading = false;
              if (res) {
                proxy.$message.success('保存成功！');
                goBack(0);
              }
            });
          } else if (state.detailInfo.status === 3) {
            state.detailLoading = true;
            processExecuteApi({ ...state.detailInfo, businessKey: state.detailInfo.id, isAssent: 1 }).then(res => {
              state.detailLoading = false;
              if (res) {
                proxy.$message.success('提交成功！');
                goBack(1);
              }
            });
          } else {
            state.detailLoading = true;
            submitInfoApi(state.detailInfo).then(res => {
              state.detailLoading = false;
              if (res) {
                proxy.$message.success('提交成功！');
                goBack(1);
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    const deleteRow = index => {
      state.detailInfo.deviceList.splice(index, 1);
    };
    // 删除申请单
    const deleteDetail = () => {
      state.detailLoading = true;
      deleteApply(state.detailInfo.id).then(res => {
        state.detailLoading = false;
        if (res) {
          proxy.$message.success(res.data.message);
          goBack();
        }
      });
    };
    // 提交审批信息
    const onSubmitApprove = () => {
      state.approveLoading = true;
      processExecuteApi(state.formDataApprove).then(res => {
        state.approveLoading = false;
        if (res) {
          state.dialogApprove = false;
          proxy.$message.success('审批完成！');
          state.isCanApprove = false;
          goBack();
        }
      });
    };
    const handleApprove = () => {
      state.dialogApprove = true;
      state.formDataApprove = {
        isAssent: 1,
        businessKey: state.detailInfo.id,
        processInstanceId: state.detailInfo.processInstanceId
      };
    };
    return {
      ...toRefs(state),
      colWidth,
      spacer,
      handleApprove,
      deleteDetail,
      onSubmitApprove,
      getTaskName,
      deleteRow,
      saveDetail,
      closeEquipment,
      formatDate,
      getNameByid,
      drageHeader,
      getDetailInfo,
      editInfo,
      cancleIA,
      closeEditInfo,
      goBack,
      getPermissionBtn
    };
  }
};
</script>
<style lang="scss" scoped>
.el-select {
  width: 100%;
}
:deep(.el-divider--vertical) {
  height: 40px;
}
.childApprove {
  background-color: #f5f7fa;
  padding: 5px 10px;
  margin-top: 5px;
  position: relative;
  .approveItem {
    line-height: 28px;
    height: 28px;
    font-size: 12px;
  }
  .opinionContent {
    font-size: 12px;
  }
}
.historyClass {
  float: right;
  font-size: 13px;
  cursor: pointer;
  color: var(--tesPrimary);
}
.drawerTime {
  padding-top: 5px;
}
.title {
  width: 100%;
  text-align: left;
}
:deep(.el-date-editor.el-input) {
  width: 100%;
}
:deep(.el-form .el-form-item .el-form-item__label) {
  color: #909399;
}
:deep(.el-form-item__content) {
  text-align: left;
}
.approveItem {
  line-height: 24px;
  height: 24px;
  text-align: left;
  position: relative;
  // .approveName {}
  .approveStatus {
    // float: right;
    position: absolute;
    right: 2px;
    top: 50%;
    transform: translateY(-50%);
  }
}
:deep(.el-timeline) {
  padding-left: 10px;
  padding-right: 10px;
}
.approveTimeLine {
  padding: 20px 10px 0 10px;
  max-height: 34rem;
  overflow-y: auto;
}
.opinionContent {
  padding: 8px;
  border-radius: 4px;
  margin-top: 5px;
  text-align: left;
  line-height: 20px;
  background-color: #f0f2f5;
}

.submit_dialog2 {
  .el-form-item {
    margin-bottom: 20px;
  }
  .el-form-item__error {
    top: 85%;
  }
  .el-radio {
    margin-right: 0;
    background: #f4f4f5;
  }
  .sendBack.is-checked {
    background: $tes-red;
  }
  .pass.is-checked {
    background: $green;
  }
  .sendBack {
    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #fff;
    }
  }
  .pass {
    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #fff;
    }
  }
  .el-radio.is-bordered {
    border: 0;
    border-radius: 4px;
    width: 49%;
    text-align: center;
  }
  .radioGroup {
    width: 100%;
    display: flex;
    align-items: center;
    :deep(.el-radio__input) {
      display: none;
    }
  }
}
:deep(.el-timeline-item__timestamp) {
  text-align: left;
}
:deep(.el-timeline-item) {
  padding-bottom: 15px;
}
</style>
