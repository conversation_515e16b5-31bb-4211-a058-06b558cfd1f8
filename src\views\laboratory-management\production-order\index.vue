<template>
  <!-- 生产订单列表 -->
  <ListLayout
    :has-quick-query="true"
    :has-right-panel="true"
    :aside-panel-width="600"
    :aside-min-width="400"
    :aside-max-width="700"
    :main-offset-top="84"
  >
    <template #search-bar>
      <div class="searchInput">
        <div style="width: 42vw">
          <CombinationQuery
            :field-list="tableColumns"
            field-tip="生产订单号/生产制令号/物料编号"
            @get-query-info="getQueryInfo"
            @reset-search="reset"
          />
        </div>
        <el-button size="large" type="text" class="advanced-btn" @click="search" @keyup.prevent @keydown.enter.prevent
          >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
        /></el-button>
      </div>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('submitOrderInspection')"
        class="fr"
        :disabled="tableData.length === 0"
        :loading="tableLoading"
        type="primary"
        size="large"
        @click="openInspectionDialog()"
        @keyup.prevent
        @keydown.enter.prevent
        >送检</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeSearchName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" :model="searchForm" label-width="110px" label-position="right">
            <el-form-item label="物料编号：">
              <el-row>
                <el-col :span="4">
                  <el-input v-model="searchForm.materialNo" size="small" placeholder="请输入" clearable />
                </el-col>
                <el-col :span="20" />
              </el-row>
            </el-form-item>
            <el-form-item label="生产机台：">
              <el-row>
                <el-col :span="4">
                  <el-input v-model="searchForm.station" size="small" placeholder="请输入" clearable />
                </el-col>
                <el-col :span="20" />
              </el-row>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="16">
          <el-radio-group v-model="radioData" size="small" @change="changeQuickSearch">
            <el-radio-button label="全部" />
            <el-radio-button label="待送检" />
            <el-radio-button label="已送检" />
          </el-radio-group>
        </el-col>
        <el-col :span="8" style="text-align: right">
          <TableColumnView binding-menu="ProductionOrder" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      height="auto"
      :size="otherForm.tableSize"
      highlight-current-row
      class="dark-table format-height-table base-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      @header-dragend="drageHeader"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
      @current-change="changeRadio"
    >
      <el-table-column v-if="false" type="selection" :width="55" />
      <el-table-column type="index" label="选择" width="70" align="center">
        <template #default="{ row }">
          <el-radio v-model="row.radio" :label="row.productionOrderDetailId" @change="changeRadio(row)">{{
            ''
          }}</el-radio>
        </template>
      </el-table-column>
      <el-table-column label="序号" type="index" width="50" />

      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          :fixed="
            item.columnFixedType === columnFixedTypesEnum.Left
              ? 'left'
              : item.columnFixedType === columnFixedTypesEnum.Right
              ? 'right'
              : false
          "
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div v-if="item.fieldType === fieldTypesEnum.Status">
              <el-tag size="small" effect="dark" :type="handleTag(item.fieldKey, row[item.fieldKey])[0]">{{
                handleTag(item.fieldKey, row[item.fieldKey])[1]
              }}</el-tag>
            </div>
            <div v-else-if="item.fieldType === fieldTypesEnum.Custom">
              <div>{{ row.productionQuantity || '--' }}{{ row.productionUnit }}</div>
            </div>
            <div v-else>
              {{ row[item.fieldKey] || '--' }}
            </div>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <pagination :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getOrderList" />
    <template #page-right-side>
      <el-tabs v-model="activeName" @tab-change="handleChangeTabs">
        <el-tab-pane label="生产信息" name="1">
          <el-descriptions :column="1" direction="horizontal">
            <el-descriptions-item v-for="(item, index) in productionInfoList" :key="index" label-align="left">
              <template #label>
                <OverflowTooltip :tooltip-content="`${item.label}:`" />
              </template>
              <OverflowTooltip :tooltip-content="item.value || '--'" />
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="送检记录" name="2">
          <el-table
            ref="tableRef"
            :key="tableKey"
            :data="inspectionList"
            fit
            border
            height="auto"
            :size="otherForm.tableSize"
            highlight-current-row
            :default-sort="{ prop: 'registerTime', order: 'descending' }"
            class="dark-table format-height-table base-table"
            @header-dragend="drageHeader"
          >
            <el-table-column label="序号" type="index" width="50" />
            <el-table-column label="申请单号" prop="no" :min-width="colWidth.orderNo" sortable show-overflow-tooltip>
              <template #default="{ row }">
                <div class="blue-color" @click="jumpToApplication(row)">{{ row.no || '--' }}</div>
              </template>
            </el-table-column>
            <el-table-column label="送检人" prop="registerUserId" :width="colWidth.person" show-overflow-tooltip>
              <template #default="{ row }">
                <div>{{ getNameByid(row.registerUserId) || row.registerUserId || '--' }}</div>
              </template>
            </el-table-column>
            <el-table-column label="送检时间" prop="registerTime" :width="colWidth.date" sortable show-overflow-tooltip>
              <template #default="{ row }">
                <div>{{ formatDate(row.registerTime) || row.registerTime || '--' }}</div>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              :width="colWidth.operation"
              class-name="fixed-right"
              prop="operation"
              fixed="right"
            >
              <template #default="{ row }">
                <span class="blue-color" @click="jumpToApplication(row)">查看</span>
                <span
                  v-if="getPermissionBtn('voiInspectionBtn') && row.isInvalidated === 0"
                  class="blue-color"
                  @click="cancelApplication(row)"
                  >作废</span
                >
                <span
                  v-if="getPermissionBtn('voiInspectionBtn') && row.isInvalidated === 1"
                  class="blue-color"
                  @click="handleRestore(row)"
                  >还原</span
                >
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </template>

    <template #other>
      <DialogOrderInspection
        :show="showInspectionDialog"
        :info="currentRowInfo"
        @close="closeInspectionDialog"
        @save-inspection="submitOrderInspection"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, onMounted, toRefs, computed } from 'vue';
import Pagination from '@/components/Pagination';
import { getDictionary } from '@/api/user';
import { colWidth } from '@/data/tableStyle';
import ListLayout from '@/components/ListLayout';
import OverflowTooltip from '@/components/OverflowTooltip';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import router from '@/router/index.js';
import DialogOrderInspection from './components/DialogOrderInspection';
import { getProductionOrderDetailList, getInspectionRecordList, saveInspectionRecord } from '@/api/diplomat';
import { ElMessage, ElMessageBox } from 'element-plus';
import { cancelInspection, restoreInspection } from '@/api/inspection-application';
import { getColWidth } from '@/utils/func/customTable';
import { orderFieldList, handleTag } from './func/productionOrderInfo';
import CombinationQuery from '@/components/CombinationQuery';
import TableColumnView from '@/components/TableColumnView';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';

export default {
  name: 'ProductionOrder',
  components: { ListLayout, Pagination, DialogOrderInspection, OverflowTooltip, CombinationQuery, TableColumnView },
  setup(props, context) {
    // const store = useStore()
    // const route = useRoute()
    const state = reactive({
      searchForm: {
        isAsc: true,
        param: '',
        submitStatus: '',
        materialNo: '',
        station: '',
        tableQueryParamList: []
      },
      tableColumns: [],
      tableKey: 0,
      tableData: [],
      total: 0,
      tableLoading: false, // 表格加载的loading
      dialogFrom: {}, // 操作树节点的弹窗表格
      treeTitle: '', // 选中树节点的name
      checkTreeId: '', // 选中的左侧树节点的id
      unitList: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ],
      unitJson: {},
      listQuery: {
        page: 1,
        limit: 20
      },
      radioData: '全部',
      showInspectionDialog: false,
      currentRowInfo: {
        productionQuantity: '',
        productionUnit: '',
        thirdNo: '',
        productionProcedure: '',
        salesOrderNo: '',
        salesOrderItemNo: '',
        customerNo: '',
        customerName: '',
        productionStation: '',
        productionDate: '',
        workingTeam: '',
        materialNo: '',
        materialCode: '',
        materialGroup: '',
        materialGroupNo: '',
        materialDesc: '', // 物料名称
        materialSpecification: '',
        parentNo: '',
        factory: '',
        completeAmount: '',
        completeUnit: '',
        batchNo: '',
        reelNo: '',
        remark: '',
        productionOrderDetailId: '',
        projectName: ''
      },
      inspectionList: [],
      selectedOrderDetailId: '',
      activeSearchName: '0',
      showS: false
    });
    const editFrom = ref(null);
    const activeName = ref('1');
    const productionInfoList = ref([]);
    const multipleSelection = ref([]);

    const otherForm = reactive({
      tableSize: 'medium'
    });

    const reset = () => {
      state.searchForm.param = '';
      state.searchForm.tableQueryParamList = [];
      state.searchForm.materialNo = '';
      state.searchForm.station = '';
      state.listQuery.page = 1;
      state.listQuery.limit = 20;
      getOrderList();
    };

    // #region deprecated

    // 获取库存单位
    const getDictionaryList = () => {
      state.unitJson = {};
      getDictionary('5').then(res => {
        res.data.data.dictionaryoption.forEach(item => {
          state.unitJson[item.code] = item.name;
          if (item.status === 1) {
            state.unitList[0].group.push(item);
          } else {
            state.unitList[1].group.push(item);
          }
        });
      });
    };

    // 列表排序
    const sortChange = column => {
      state.searchForm.orderBy = column.prop;
      state.searchForm.isAsc = column.order === 'ascending';
      getOrderList();
    };

    // mounted
    onMounted(() => {});
    // #endregion

    // #region 高级搜索

    // 打开 高级搜索
    const search = () => {
      state.showS = !state.showS;
      if (state.activeSearchName === '0') {
        state.activeSearchName = '1';
      } else {
        state.activeSearchName = '0';
      }
    };

    // #endregion

    // #region 生产订单

    const getOrderList = query => {
      const params = {
        ...state.searchForm
      };
      if (query && query.page) {
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      }
      params.page = state.listQuery.page.toString();
      params.limit = state.listQuery.limit.toString();
      state.tableLoading = true;
      getProductionOrderDetailList(params).then(res => {
        state.tableLoading = false;
        if (res && res.data.code === 200) {
          state.tableData = res.data.data.list;
          state.total = res.data.data.totalCount;
          if (state.tableData.length > 0) {
            if (state.selectedOrderDetailId) {
              const findIndex = state.tableData.findIndex(
                item => item.productionOrderDetailId === state.selectedOrderDetailId
              );
              if (findIndex !== -1) {
                changeRadio(state.tableData[findIndex]);
              }
              state.selectedOrderDetailId = '';
            } else {
              changeRadio(state.tableData[0]);
            }
          }
        }
      });
    };

    const changeQuickSearch = value => {
      const param = {
        全部: '',
        待送检: '0',
        已送检: '1'
      };
      state.searchForm.submitStatus = param[value];
      state.listQuery.page = 1;
      getOrderList();
    };

    const changeRadio = row => {
      if (row && row.productionOrderDetailId) {
        state.currentRowInfo = row;
        setProductionInfoList();
        setInspectionRecordList();
        row.radio = row.productionOrderDetailId;
        state.tableData.forEach(item => {
          if (item.productionOrderDetailId !== row.productionOrderDetailId) {
            item.radio = false;
          }
        });
      }
    };

    const handleChangeTabs = value => {};

    const openInspectionDialog = () => {
      state.showInspectionDialog = true;
    };

    const closeInspectionDialog = val => {
      state.showInspectionDialog = false;
    };

    const handleSelectionChange = val => {
      multipleSelection.value = val;
    };

    const inspectionDisabled = computed(() => multipleSelection.value.length === 0);

    const jumpToApplication = row => {
      if (row.status?.toString() === '0') {
        // 待提交状态可以编辑
        router.push({ name: 'ProductionOrderApplication', query: { id: row.id, flag: 2 } });
      } else {
        // 非待提交，只能看详情
        router.push({ name: 'ProductionOrderApplication', query: { id: row.id, flag: 1 } });
      }
    };

    const cancelApplication = row => {
      ElMessageBox({
        title: '提示',
        message: '确定作废当前检验申请单吗？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          cancelInspection(row.id).then(res => {
            if (res !== false) {
              ElMessage.success(`编号：${row.no}已作废`);
              setInspectionRecordList();
            }
          });
        })
        .catch(() => {});
    };

    const handleRestore = row => {
      ElMessageBox({
        title: '提示',
        message: '确定还原当前检验申请单吗？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          restoreInspection(row.id).then(res => {
            if (res) {
              ElMessage.success(`编号${row.no}已还原`);
              setInspectionRecordList();
            }
          });
        })
        .catch(() => {});
    };

    // #endregion

    // #region 送检
    const submitOrderInspection = val => {
      const info = state.currentRowInfo;
      const inspectionParams = {
        applicantName: val.InspectionUserId,
        batchNo: info.batchNo,
        completeAmount: info.completeAmount,
        completeUnit: val.inspectionUnit,
        customerName: info.customerName,
        customerNo: info.customerNo,
        factory: info.factory,
        materialCode: info.materialCode,
        materialDesc: info.materialDesc,
        materialGroup: info.materialGroup,
        materialGroupNo: info.materialGroupNo,
        materialNo: info.materialNo,
        materialSpecification: info.materialSpecification,
        parentNo: info.parentNo,
        productStatus: info.productStatus,
        productionDate: info.productionDate,
        productionOrderDetailId: info.productionOrderDetailId,
        productionOrderNo: info.productionOrderNo,
        productionProcedure: val.productionProcedure,
        productionProcedureNo: val.productionProcedureNo,
        productionQuantity: val.productionQuantity,
        productionStation: info.productionStation,
        productionUnit: val.inspectionUnit,
        reelNo: val.reelNo,
        registerTime: val.registerTime,
        remark: info.remark,
        salesOrderItemNo: info.salesOrderItemNo,
        salesOrderNo: info.salesOrderNo,
        sampleQuantity: val.sampleQuantity,
        sampleUnit: val.inspectionUnit,
        submitStatus: info.submitStatus,
        thirdPartId: info.thirdPartId,
        type: val.type,
        workingTeam: info.workingTeam,
        projectName: info.projectName
      };
      saveInspectionRecord(inspectionParams).then(res => {
        if (res && res.data.code === 200) {
          ElMessage.success(`送检成功!`);
          state.selectedOrderDetailId = state.currentRowInfo.productionOrderDetailId;
          getOrderList();
        }
      });
    };
    // #endregion

    // #region 立即执行的方法

    getDictionaryList();
    getOrderList();

    function setProductionInfoList() {
      productionInfoList.value = [
        {
          label: '生产数量',
          value: `${state.currentRowInfo.productionQuantity ?? '--'}${state.currentRowInfo.productionUnit}`
        },
        { label: '第三方单号', value: `${state.currentRowInfo.thirdPartId}` },
        { label: '生产工序', value: `${state.currentRowInfo.productionProcedure}` },
        { label: '销售订单号', value: `${state.currentRowInfo.salesOrderNo}` },
        { label: '销售订单明细号', value: `${state.currentRowInfo.salesOrderItemNo}` },
        { label: '客户简称/编号', value: `${state.currentRowInfo.customerNo}` },
        { label: '客户名称', value: `${state.currentRowInfo.customerName}` },
        { label: '生产机台', value: `${state.currentRowInfo.productionStation}` },
        { label: '生产日期', value: `${state.currentRowInfo.productionDate}` },
        { label: '生产班组', value: `${state.currentRowInfo.workingTeam}` },
        { label: '物料编号', value: `${state.currentRowInfo.materialNo}` },
        { label: '物料分组', value: `${state.currentRowInfo.materialGroup}` },
        { label: '物料名称', value: `${state.currentRowInfo.materialDesc}` },
        { label: '物料型号规格', value: `${state.currentRowInfo.materialSpecification}` },
        { label: '工厂名称', value: `${state.currentRowInfo.factory}` },
        { label: '已完成数量', value: `${state.currentRowInfo.completeAmount}${state.currentRowInfo.completeUnit}` },
        { label: '批号', value: `${state.currentRowInfo.batchNo}` },
        { label: '盘号', value: `${state.currentRowInfo.reelNo}` },
        { label: '备注', value: `${state.currentRowInfo.remark}` }
      ];
    }

    function setInspectionRecordList() {
      state.tableLoading = true;
      getInspectionRecordList(state.currentRowInfo.productionOrderDetailId).then(res => {
        state.tableLoading = false;
        if (res && res.data.code === 200) {
          state.inspectionList = res.data.data;
        }
      });
    }
    // #endregion

    // #region 组合查询

    function getQueryInfo(info) {
      state.searchForm.param = info.param;
      state.searchForm.tableQueryParamList = info.tableQueryParamList;
      getOrderList();
    }

    // 更新表格字段
    const onUpdateColumns = columns => {
      state.tableKey = state.tableKey + 1;
      state.tableColumns = columns;
    };

    // #endregion
    return {
      ...toRefs(state),
      inspectionDisabled,
      sortChange,
      getDictionaryList,
      getPermissionBtn,
      formatDate,
      getNameByid,
      drageHeader,
      editFrom,
      otherForm,
      activeName,
      reset,
      colWidth,
      changeQuickSearch,
      handleChangeTabs,
      openInspectionDialog,
      closeInspectionDialog,
      productionInfoList,
      getOrderList,
      handleSelectionChange,
      jumpToApplication,
      cancelApplication,
      handleRestore,
      changeRadio,
      submitOrderInspection,
      search,
      getColWidth,
      orderFieldList,
      handleTag,
      getQueryInfo,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.searchInput {
  display: flex;
  .el-input {
    width: 360px;
    margin-right: 10px;
  }
  .advanced-btn {
    margin-left: 0.25rem;
  }
}

.el-descriptions {
  :deep(.el-descriptions--medium) {
    display: block;
    width: auto;
    min-width: none;
    tbody,
    tr,
    td {
      display: block;
      width: auto;
    }
  }
  :deep(.el-descriptions__label) {
    display: inline-block;
    color: $tes-font1;
    font-weight: normal;
    width: 120px;
    text-align: right;
  }
  :deep(.el-descriptions__content) {
    color: $tes-font;
  }
  :deep(.el-descriptions__body) {
    white-space: nowrap;
  }
  :deep(.el-descriptions__content) {
    display: inline-block;
    width: calc(100% - 130px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 8px;
  }
}

:deep(.left-panel) {
  height: 100%;
}
</style>
