export default async function MapLoader() {
  return new Promise((resolve, reject) => {
    const key = '4e70ec8df99cdf75c4a1c572747cf90f';
    if (window.AMap) {
      window.onload = function () {
        resolve(window.AMap);
      };
    } else {
      var script = document.createElement('script');
      script.type = 'text/javascript';
      script.axync = true;
      script.src = 'https://webapi.amap.com/maps?v=1.4.15&key=' + key;
      script.onerror = reject;
      document.head.appendChild(script);
      window.onload = function () {
        resolve(window.AMap);
      };
    }
  });
}
