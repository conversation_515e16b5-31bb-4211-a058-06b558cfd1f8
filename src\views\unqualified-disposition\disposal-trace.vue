<template>
  <!-- 处置意见页面 -->
  <div v-loading="loading" class="register register-bottom">
    <div v-if="isShowModule" class="titleHeader" style="display: flex">
      处置跟踪情况
      <div v-if="lastStep.endTime == ''" class="name">
        （待办人：{{ getNameByid(lastStep.processParameter.assignee) }}）
      </div>
      <div class="inlineBlock textR" style="flex: 1">
        <el-button
          v-if="!isShowFormGz && getPermissionBtn('DisposalTracking')"
          type="primary"
          size="small"
          :disabled="lastStep.processParameter.assignee != currentAccountId"
          icon="el-icon-plus"
          @click="
            isShowFormGz = true;
            isCheck = false;
          "
          >处置跟踪情况</el-button
        >
      </div>
    </div>
    <div v-if="isShowModule" class="register-form-info info-main">
      <el-form v-if="isShowFormGz" ref="ruleFormGz" class="formDataOption" :model="formDataGz" label-position="top">
        <div class="register-title">处置情况 <span v-if="!isCheck" class="required-info">（必填）</span></div>
        <el-form-item
          label=""
          prop="opinion"
          :rules="{ required: !isCheck, message: '请输入处置情况', trigger: 'change' }"
        >
          <el-input
            v-if="!isCheck"
            v-model="formDataGz.opinion"
            maxlength="300"
            type="textarea"
            :rows="2"
            placeholder="请输入处置情况"
          />
          <el-alert v-if="isCheck" :title="formDataGz.opinion" :closable="false" type="info" />
        </el-form-item>
        <div class="register-title">验收结果 <span v-if="!isCheck" class="required-info">（必填）</span></div>
        <el-form-item
          label=""
          prop="isAssent"
          :rules="{ required: !isCheck, message: '请选择验收结果', trigger: 'change' }"
        >
          <el-radio-group v-if="!isCheck" v-model="formDataGz.isAssent">
            <el-radio :label="1">合格</el-radio>
            <el-radio :label="0">不合格</el-radio>
          </el-radio-group>
          <el-alert
            v-if="isCheck"
            :title="formDataGz.isAssent == 1 ? '合格' : '不合格'"
            :closable="false"
            type="info"
          />
        </el-form-item>
      </el-form>
      <div v-if="isShowFormGz && !isCheck" class="formBtn">
        <el-button size="small" @click="isShowFormGz = false">取 消</el-button>
        <el-button type="primary" size="small" @click="handleSubmit">提 交</el-button>
      </div>
      <el-row v-if="isShowQm" class="qmrq">
        <el-col :span="4" :offset="17"
          >签名：
          <img v-if="qmImage" :src="qmImage" alt="" />
          <div v-else>{{ getNameByid(lastStep.processParameter.assignee) }}</div>
        </el-col>
        <el-col :span="3"
          >日期：
          <div>{{ lastStep.endTime }}</div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch, getCurrentInstance, nextTick } from 'vue';
import { getLoginInfo } from '@/utils/auth';
import { processExecute } from '@/api/unqualifiedDisposition';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { getAttachment } from '@/api/login';

export default {
  name: 'Disposaltrace',
  components: {},
  props: {
    reasonArray: {
      type: Array,
      default: function () {
        return [];
      }
    },
    sampleInfoDetail: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['fresh'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    watch(props, newValue => {
      datas.sampleInfoDetail = props.sampleInfoDetail;
      initList();
    });
    const datas = reactive({
      sampleInfoDetail: props.sampleInfoDetail,
      currentAccountId: '',
      loading: false,
      qmImage: '', // 签名照
      isCheck: true, // 只读，表单不可修改
      isShowQm: false, // 是否显示签名
      lastStep: {
        processParameter: {}
      },
      lastCause: { processParameter: {}, approvalList: [] }, // 最后一步的原因分析
      optionList: [],
      formDataGz: {
        isAssent: 1
      },
      userList: {},
      ruleFormGz: ref(),
      isShowFormGz: false, // 是否需要填写表单
      isShowModule: false // 是否显示处置跟踪情况模块
    });

    if (getLoginInfo()) {
      datas.currentAccountId = getLoginInfo().accountId;
    }

    const initList = () => {
      if (props.reasonArray.length > 0) {
        datas.lastCause = props.reasonArray[props.reasonArray.length - 1];
        if (datas.lastCause.approvalList.length > 0) {
          datas.lastStep = datas.lastCause.approvalList[datas.lastCause.approvalList.length - 1];
        } else {
          datas.lastStep = props.reasonArray[props.reasonArray.length - 1];
        }
        datas.isShowModule = datas.lastCause.approvalList.some(item => {
          return item.name === '处置验收';
        });
        nextTick(() => {
          getQmImage();
        });
        // 最后一条数据没有填过并且待办人是当前登录人
        if (datas.lastStep.endTime === '') {
          datas.isShowFormGz = false;
        } else {
          datas.isShowFormGz = true;
          datas.isShowQm = true;
          datas.isCheck = true;
          datas.formDataGz = JSON.parse(JSON.stringify(datas.lastStep.processParameter));
        }
      }
    };
    initList();
    const handleSubmit = () => {
      datas.ruleFormGz.validate().then(valid => {
        if (valid) {
          var params = {
            businessKey: datas.sampleInfoDetail.id,
            processInstanceId: datas.sampleInfoDetail.processInstanceId,
            ...datas.formDataGz
          };
          datas.loading = true;
          processExecute(params).then(res => {
            datas.loading = false;
            if (res.data.code === 200) {
              datas.isCheck = true;
              proxy.$message.success('提交成功');
              context.emit('fresh', {});
            }
          });
        }
      });
    };

    // 获取签名图片
    const getQmImage = () => {
      datas.qmImage = '';
      datas.loading = true;
      getAttachment([datas.lastStep.processParameter.assignee]).then(res => {
        datas.loading = false;
        if (res) {
          const data = res.data.data;
          if (data.length > 0) {
            datas.qmImage = data[0].signatureUrl;
          }
        }
      });
    };

    return {
      ...toRefs(datas),
      handleSubmit,
      getQmImage,
      getPermissionBtn,
      getNameByid,
      initList
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
@import './common.scss';
.register.register-bottom {
  margin-bottom: 0;
  .qmrq {
    margin-bottom: 0;
  }
}
.name {
  font-size: 14px;
  color: #606266;
  margin-left: 10px;
  font-weight: 400;
}
.formBtn {
  padding-bottom: 35px;
  text-align: right;
}
.disposalTrace {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-top: 27px;
  .register-header {
    text-align: left;
    .title {
      font-size: 18px;
      font-weight: bold;
      height: 21px;
      line-height: 21px;
      display: inline-block;
      text-align: left;
      margin-right: 16px;
    }
    .name {
      font-size: 12px;
      height: 25px;
      line-height: 25px;
      text-align: left;
      color: #909399;
      display: inline-block;
    }
  }
  .content {
    background: #fff;
    padding: 15px;
    border-radius: 8px;
    text-align: left;
    position: relative;
    margin-top: 8px;
  }
  .formBtn {
    position: absolute;
    right: 15px;
    bottom: 24px;
  }
  .formDataOption {
    padding-bottom: 58px;
  }
  .optionTitle {
    display: inline-block;
    font-size: 14px;
    line-height: 20px;
  }
  .pass {
    color: #67c23a;
  }
  .noPass {
    color: #f56c6c;
  }
  .optionDetail {
    background: #f6f6f6;
    padding: 9px 10px;
    color: #909399;
    line-height: 20px;
    border-radius: 2px;
    width: 100%;
  }
  .approver {
    color: #606266;
    line-height: 20px;
    font-weight: normal;
    margin: 4px 0;
  }
  .qmrq {
    position: absolute;
    color: #606266;
    font-size: 14px;
    right: 16px;
    bottom: 22px;
    line-height: 30px;
    div {
      display: inline-block;
    }
    .qm {
      margin-right: 50px;
    }
  }
  :deep(.el-form .el-form-item .el-form-item__label) {
    font-size: 14px !important;
    font-weight: bold;
    color: #303133;
  }
}
</style>
