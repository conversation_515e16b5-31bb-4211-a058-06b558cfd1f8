<template>
  <div class="centerTop">{{ title }}</div>
  <div class="centerBox">
    <RecycleScroller class="boxTable" :items="list" :item-size="42" key-field="index">
      <template #default="{ item }">
        <!-- <div class="item">{{ item.date }}{{ item.value }}</div> -->
        <el-row class="listLi">
          <el-col :span="2">{{ item.index }}</el-col>
          <el-col :span="10">{{ item.date }}</el-col>
          <el-col :span="12">{{ item.value }}</el-col>
        </el-row>
      </template>
    </RecycleScroller>
  </div>
</template>

<script>
import { reactive, toRefs, computed } from 'vue';
import { formatDateTime } from '@/utils/formatTime';
import { RecycleScroller } from 'vue-virtual-scroller';
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css';

export default {
  name: 'SingleData',
  components: { RecycleScroller },
  props: {
    title: {
      type: String,
      default: ''
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  setup(props, context) {
    const state = reactive({
      loading: false
    });

    const list = computed(() =>
      (props.data || [])
        .map((item, index) => ({
          index: (index + 1).toString(),
          value: Number(item.value),
          date: formatDateTime(item.date)
        }))
        .reverse()
    );

    return {
      ...toRefs(state),
      list
    };
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/intelligentChart.scss';

.centerTop {
  width: 100%;
  height: 44px;
  line-height: 26px;
  color: $titleColor;
  padding: 9px 0 9px 59px;
  font-size: 18px;
  font-weight: 700;
  position: relative;
  background: url(../../../../assets/img/dataBoard/center-left-top.png) no-repeat;
  background-size: 100% 100%;
}

.centerBox {
  line-height: 36px;
  color: $scrollListColor;
  text-align: center;
  width: 100%;
  height: 360px;
  .boxTable {
    height: 100%;
    border: 2px solid $borderColor;
    padding: 3px 10px 10px 10px;
  }
  .listContent {
    height: 202px;
    overflow-y: auto;
  }
  .listContent::-webkit-scrollbar {
    width: 0 !important;
  }
  .textLeft {
    text-align: left;
  }
  .exceedTime {
    color: $scrollListColor;
    background-color: #f18ba2;
    border-radius: 4px;
    display: inline-block;
    padding: 0px 4px;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    position: absolute;
    right: 2px;
    top: 50%;
    transform: translateY(-50%);
  }
}

.listLi {
  line-height: 36px;
  font-size: 14px;
  color: #fff;
  text-align: center;
  .el-col {
    width: 100%;
    padding: 0 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    position: relative;
  }
  .el-col:first-child {
    padding: 0;
  }
  span {
    margin-right: 5px;
    display: inline-block;
  }
  span:last-child {
    margin-right: 0;
  }
}
.listLi .el-col:nth-child(2n + 1) {
  background-color: $scrollListFirst;
}
.listLi .el-col:nth-child(2n) {
  background-color: $scrollListSecond;
}
</style>
