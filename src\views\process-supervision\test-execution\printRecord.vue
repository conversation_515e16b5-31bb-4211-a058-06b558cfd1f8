<template>
  <div v-loading="loading" class="addRecord">
    <el-row class="fixed-hd">
      <el-col class="btnGroupRight" :span="24">
        <el-button class="brn-fr" size="small" type="primary" @click="printNew()">确定打印</el-button>
      </el-col>
    </el-row>
    <div class="moban-wrap">
      <div v-if="showPrintbox" id="print-box-show" :class="{ 'print-box-transverse': showType === 1 }">
        <div
          v-for="(it, index) in imgList"
          :key="index"
          style="padding: 0; border: 0; height: auto"
          class="box"
          :class="{ boxTransverse: showType === 1 }"
        >
          <img
            :key="index"
            :src="it"
            style="display: block"
            class="imgStyle"
            :class="{ transverse: showType === 1 }"
            alt=""
          />
        </div>
      </div>
      <div v-if="!showPrintbox" id="moban" ref="excel" class="moban" :class="{ transverse: showType === 1 }">
        <print-excel
          ref="ItemRef"
          :curr-index="currIndex"
          :json-data="jsonDataListItem"
          @handleData="handleData"
          @setImg="setImg"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, ref, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { getNameByid } from '@/utils/common';
// import router from '@/router/index.js'
import { getLoginInfo } from '@/utils/auth';
// import html2canvas from 'html2canvas'
// import { samplesDetails } from '@/api/order'
// import { formatDate } from '@/utils/formatTime'
import printExcel from '@/views/excelComponents/printExcel';
import store from '@/store';
import { batchPrint } from '@/api/excel';
import printJS from 'print-js';
import { decryptCBC } from '@/utils/ASE';
// import html2canvas from 'html2canvas';

export default {
  name: 'PrintRecord',
  components: { printExcel },
  setup(props, ctx) {
    const route = useRoute();
    const ruleForm = ref('');
    const state = reactive({
      experimentId: route.query.experimentId,
      showType: Number(route.query.showType),
      disable: route.query.type,
      accountId: getLoginInfo().accountId,
      dialogVisible: false,
      loading: true,
      experimentData: {},
      i: 0,
      currIndex: 0,
      printList: route.query.printlist.split(','),
      imgList: [],
      printListAt: [],
      jsonDataListItem: {},
      jsonDataList: [],
      isShowPicture: false,
      jsonData: {},
      postDeviceData: [],
      dataValue: {
        dialogSubmit: false
      },
      url: '',
      dialogSubmit: false,
      isCheck: route.query.type === 'check',
      formData: {
        realOwnerIds: [],
        reviewerId: '',
        date: []
      },
      showPrintbox: false,
      dictionary: {
        1: '正常',
        3: '停用',
        0: '异常'
      },
      selected: [], // 已经选中的编号集合
      userList: store.state.common.nameList,
      deviceList: []
    });

    // 保存
    const excel = ref(null);
    const ItemRef = ref(null);
    const handleData = thisValue => {
      state.experimentData = thisValue;
    };
    const setImg = detail => {
      state.imgList.push(detail.imageBase64);
      if (state.imgList.length !== state.jsonDataList.length) {
        state.currIndex += 1;
        state.jsonDataListItem = state.jsonDataList[state.currIndex];
      } else {
        printNew();
      }
    };
    const printus = async list => {
      state.jsonDataListItem = list[state.currIndex];
      state.loading = false;
      state.dialogPrint = false;
    };
    // 获取数据
    const getAllList = () => {
      return new Promise((resolve, reject) => {
        batchPrint(state.printList).then(res => {
          if (res !== false) {
            state.printListAt = res.data.data;
            state.imgList = [];
            var len = state.printList.length;
            state.printListAt.forEach(async (item, index) => {
              const jsonDataitem = { ...item.value };
              jsonDataitem.excelHtml = decryptCBC(item.html);
              jsonDataitem.showType = item.showType;
              jsonDataitem.index = item.index;
              jsonDataitem.experimentData = item.value;
              // jsonDataitem.experimentProdTypeList = item.experimentProdTypeList
              jsonDataitem.realReviewerIdimg = jsonDataitem.experimentData.reviewerSignUrl?.split(',');
              jsonDataitem.realOwnerIdsimgs = jsonDataitem.experimentData.ownerSignUrls?.split(',');
              state.jsonDataList.push(jsonDataitem);
              if (index === len - 1) {
                resolve(state.jsonDataList);
              }
            });
          }
        });
      });
    };
    // 打印
    const printNew = () => {
      state.showPrintbox = true;
      const focuser = setInterval(() => window.dispatchEvent(new Event('focus')), 500);
      nextTick(() => {
        printJS({
          printable: 'print-box-show',
          style: `body { margin: 0; padding: 0; border: 0;} img { width: 100%; display: block; } @page{margin: 0px ${55}px; size: ${
            state.showType === 1 ? 'A4 landscape' : 'A4 portrait'
          }; overflow: hidden; }`,
          type: 'html',
          scanStyles: false,
          documentTitle: '报告打印',
          onPrintDialogClose: () => {
            state.loading = false;
            window.opener = null; // 禁止某些浏览器的一些弹窗
            window.open('', '_self');
            // window.close()
            // window.close()
            nextTick(() => {
              clearInterval(focuser);
            });
          }
        });
      });
    };

    return {
      ...toRefs(state),
      excel,
      ItemRef,
      printNew,
      ruleForm,
      handleData,
      getLoginInfo,
      setImg,
      getNameByid,
      getAllList,
      printus
    };
  },
  computed: {},
  async created() {
    const list = await this.getAllList();
    this.printus(list);
  }
};
</script>

<style lang="scss" scoped>
.fgline {
  margin: 0 5px;
}

ul {
  padding: 0;
}

.imgStyle {
  width: 100%;
  height: auto !important;
  display: block;
}
li {
  list-style: none;
  margin: 8px 0;
}

.base-table {
  margin-top: 12px;

  .el-select--medium {
    width: 80%;
  }

  .status.el-select--medium {
    width: 30%;
  }

  :deep(.el-input--medium .el-input__icon) {
    line-height: 24px;
  }

  :deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
    width: 64.5%;
  }

  :deep(.el-input--medium .el-input__inner) {
    height: 24px;
    line-height: 24px;
  }

  :deep(.el-select--medium),
  :deep(.el-input--medium) {
    line-height: 24px;
  }
}

:deep(.el-table--medium td) {
  padding: 0;
}

.addRecord {
  margin: 0 1rem;
  padding: 10px 14px 10px 1rem;

  :deep(.el-form .el-form-item) {
    margin-bottom: 20px;
  }

  // .dialogRecord {
  :deep(.dialogRecord .el-dialog__body) {
    padding-top: 35px;
    min-height: 500px;
  }

  // }
  :deep(.el-select--medium),
  :deep(.el-range-editor.el-input__inner) {
    width: 80%;
  }

  .btnGroupLeft {
    text-align: left;
  }

  .btnGroupRight {
    text-align: right;
  }

  .moban {
    width: 794px;
    margin: 10px auto;

    .el-form {
      height: auto;
    }
  }

  :deep(.el-button--primary:hover, .el-button--primary:focus) {
    background: $tes-primary;
  }

  :deep(.el-table__body-wrapper) {
    margin-bottom: 0;
  }
}

.moban-wrap {
  width: 100%;
  height: calc(100vh - 202px);
  margin-top: 30px;
  overflow-y: auto;
}

.moban {
  min-height: 500px;
}

.brn-fr {
  float: right;
}

#print-box-show {
  width: 684px !important;
  margin: 0 auto;
  background-color: #fff;
  box-shadow: 0 0.285714rem 0.857143rem 0.285714rem rgba(0, 0, 0, 0.08),
    0 0.142857rem 0.285714rem -0.142857rem rgba(0, 0, 0, 0.16);
  filter: drop-shadow(0 0.214286rem 0.571429rem rgba(0, 0, 0, 0.12));
}
.print-box-transverse {
  width: 1013px !important;
}
.box {
  width: 100%;
  // height: 1123px;
  display: block;
}
</style>
