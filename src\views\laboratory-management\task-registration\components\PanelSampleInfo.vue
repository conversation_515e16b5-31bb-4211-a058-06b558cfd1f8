<template>
  <div class="sample-about">
    <div class="sample-about-header">
      <div class="title">样品相关</div>
      <el-button
        v-if="
          showDetail &&
          isInvalidated === 0 &&
          getPermissionBtn('RegistrationAddSample') &&
          status !== 1 &&
          status !== 2 &&
          status !== 3
        "
        type="primary"
        size="small"
        icon="el-icon-plus"
        @click="addSample"
        @keyup.prevent
        @keydown.enter.prevent
        >添加样品</el-button
      >
    </div>
    <div class="content">
      <el-table
        v-if="sampleInfoDetail && sampleInfoDetail.length > 0"
        ref="tableSampleRef"
        :key="tableSampleKey"
        :data="sampleInfoDetail"
        fit
        border
        height="auto"
        class="dark-table sample-about-table"
        @header-dragend="drageHeader"
      >
        <el-table-column type="index" label="序号" :width="70" align="center" />
        <el-table-column
          v-for="viewItem in pageView['sampleRelated']"
          :key="viewItem.fieldKey"
          :label="viewItem.fieldName"
          :prop="viewItem.fieldKey"
          :sortable="Number(viewItem.isSortable) === 1"
          :width="viewItem.isMinWidth ? '' : viewItem.columnWidth"
          :min-width="viewItem.isMinWidth ? viewItem.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="viewItem.fieldType == 'text'">
              {{ row[viewItem.fieldKey] || '--' }}
            </template>
            <template v-if="viewItem.fieldType == 'custom'">
              <template v-if="viewItem.fieldKey == 'sampleNum'">
                <span>{{ row.sampleNum || '--' }}{{ filterSampleUnitToName(row.sampleUnit) || '' }}</span>
              </template>
              <template v-if="viewItem.fieldKey == 'inspectType'">
                <span>{{ formatTestType(row.inspectType) || row.inspectType || '--' }}</span>
              </template>
              <template v-if="viewItem.fieldKey == 'sampleInfoQrCode'">
                <QRCodeTrigger :value="{ type: 'sample', id: row.id, orderId: row.orderId }" />
              </template>
            </template>
          </template>
        </el-table-column>
        <el-table-column
          v-if="
            (showDetail &&
              isInvalidated === 0 &&
              (getPermissionBtn('RegistrationDeleteSample') || getPermissionBtn('RegistrationCopySample'))) ||
            (getPermissionBtn('SampleEditingConfirmed') && status === 3)
          "
          label="操作"
          width="180px"
          prop="caozuo"
          fixed="right"
          align="center"
        >
          <template #default="{ row }">
            <span
              v-if="
                (showDetail && isInvalidated === 0 && getPermissionBtn('SampleEditingUnConfirm')) ||
                (getPermissionBtn('SampleEditingConfirmed') && status === 3)
              "
              class="blue-color"
              @click="editSampleIA(row, status)"
              >编辑</span
            >
            <span
              v-if="showDetail && isInvalidated === 0 && getPermissionBtn('RegistrationDeleteSample')"
              class="blue-color"
              @click="deleteSampleIA(row)"
              >删除</span
            >
            <span
              v-if="
                showDetail &&
                isInvalidated === 0 &&
                getPermissionBtn('RegistrationCopySample') &&
                status !== 1 &&
                status !== 2 &&
                status !== 3
              "
              class="blue-color"
              @click="copySampleInfo(row)"
              >复制</span
            >
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-if="!sampleInfoDetail || sampleInfoDetail.length === 0" :image="emptyImg" description="暂无数据" />
    </div>
    <!-- 二维码弹出框 -->
    <QRCodePopup title="样品二维码" />
    <DrawerAddSample
      :drawer="showDrawer"
      :title="addSampleTitle"
      :task-status="status"
      :edit-data="editSampleData"
      :task-id="currentTaskId"
      :material-code="materialCode"
      @set-info="getAddSampleData"
      @close="closeAddSampleDrawer"
    />
  </div>
</template>

<script>
import { reactive, toRefs, watch } from 'vue';
import { useStore } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
// import router from '@/router/index.js'
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import DrawerAddSample from './DrawerAddSample.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
// import { getSampleUnitDict } from '@/api/login'
import { formatTestType } from '@/utils/formatIndustryTerm';
import { deleteSampleInfoById, copySampleInfoById } from '@/api/task-registration';
import { filterSampleUnitToName } from '@/utils/formatJson';
import { QRCodePopup, QRCodeTrigger } from '@/components/QRCodePopup';
import emptyImg from '@/assets/img/empty-table.png';

export default {
  name: 'SampleAbout',
  components: { DrawerAddSample, QRCodePopup, QRCodeTrigger },
  props: {
    taskId: {
      type: String,
      default: ''
    },
    taskStatus: {
      type: Number,
      default: 0
    },
    materialCode: {
      type: String,
      default: ''
    },
    isInvalidated: {
      type: Number,
      default: 0
    },
    showDetail: {
      type: Boolean,
      default: false
    },
    pageView: {
      type: Object,
      default: function () {
        return {};
      }
    },
    sampleInfo: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['setInfo'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    // console.log(proxy)
    // watch(props, (newValue) => {
    //
    // })
    const store = useStore().state;
    const datas = reactive({
      tableSampleKey: 'tableSampleKey',
      currentAccountId: getLoginInfo().accountId,
      userOptions: store.common.nameList,
      sampleInfoDetail: props.sampleInfo,
      showDrawer: false,
      status: props.taskStatus,
      addSampleTitle: '新增样品',
      isInvalidated: props.isInvalidated,
      pageViewGroup: props.pageView,
      editSampleData: {},
      currentTaskId: '',
      options: store.user.sampleUnit,
      qrCodeRow: {}
    });
    watch(
      () => props.taskId,
      newValue => {
        if (newValue) {
          datas.currentTaskId = newValue;
          datas.sampleInfoDetail = props.sampleInfo;
        }
      },
      { deep: true }
    );

    watch(
      () => props.sampleInfo,
      newValue => {
        if (newValue) {
          datas.sampleInfoDetail = props.sampleInfo;
          datas.status = props.taskStatus;
        }
      },
      { deep: true }
    );
    watch(
      () => props.taskStatus,
      newValue => {
        if (newValue) {
          datas.status = props.taskStatus;
        }
      },
      { deep: true }
    );
    watch(
      () => props.pageView,
      newValue => {
        if (newValue) {
          datas.pageViewGroup = props.pageView;
        }
      },
      { deep: true }
    );
    // 添加样品-打开新增样品弹出框
    const addSample = () => {
      // console.log(datas.sampleInfoDetail)
      datas.addSampleTitle = '新增样品';
      datas.editSampleData = {};
      datas.showDrawer = true;
      // context.emit('setInfo', datas.sampleInfoDetail)
    };
    // 查看样品二维码
    const handleClickQrcode = row => {
      datas.qrCodeDialog = true;
      datas.qrCodeRow = row;
    };
    // 关闭新增样品页面弹出框
    const closeAddSampleDrawer = value => {
      datas.showDrawer = value;
      context.emit('setInfo', 'close');
    };
    // 获取新增样品数据
    const getAddSampleData = data => {
      // console.log(data)
      // datas.sampleInfoDetail.push(data)
      context.emit('setInfo', datas.sampleInfoDetail);
    };
    // 编辑样品
    const editSampleIA = row => {
      datas.addSampleTitle = '编辑样品';
      datas.editSampleData = row;
      datas.showDrawer = true;
    };
    // 样品信息
    const chenckSampleIA = row => {
      datas.addSampleTitle = '查看样品';
      datas.editSampleData = row;
      datas.showDrawer = true;
    };

    const copySampleInfo = row => {
      ElMessageBox.prompt('样品编号不可复制，复制前请先修改编号！', '复制', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        inputPlaceholder: '请修改样品编号',
        inputValue: row.sealNum,
        closeOnClickModal: true,
        inputPattern: /\S/,
        inputErrorMessage: '请输入样品编号',
        type: 'warning'
      })
        .then(({ value }) => {
          copySampleInfoById({ id: row.id, sealNum: value }).then(res => {
            if (res !== false) {
              ElMessage.success('复制成功');
              context.emit('setInfo', 'copy');
            }
          });
        })
        .catch(() => {});
    };

    // 删除样品
    const deleteSampleIA = row => {
      // console.log(row)
      ElMessageBox({
        title: '删除样品',
        message: '是否确认删除该样品？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          deleteSampleInfoById(row.id).then(res => {
            if (res !== false) {
              // console.log(res)
              ElMessage.success('删除成功');
              context.emit('setInfo', 'delete');
            }
          });
        })
        .catch(() => {});
    };
    // 过滤单位
    const filterUnit = unitId => {
      var unitName = '';
      if (unitId && datas.options.length > 0) {
        datas.options.forEach(opt => {
          if (opt.code === unitId) {
            unitName = opt.name;
          }
        });
      }
      return unitName;
    };

    return {
      ...toRefs(datas),
      emptyImg,
      handleClickQrcode,
      formatTestType,
      getNameByid,
      addSample,
      drageHeader,
      closeAddSampleDrawer,
      getAddSampleData,
      editSampleIA,
      deleteSampleIA,
      copySampleInfo,
      filterUnit,
      chenckSampleIA,
      getPermissionBtn,
      filterSampleUnitToName
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.sample-about {
  .sample-about-header {
    margin: 15px 0 15px 0;
    text-align: left;
    .title {
      font-size: 16px;
      font-weight: bold;
      height: 32px;
      line-height: 32px;
      text-align: left;
      display: inline-block;
      margin-right: 16px;
    }
    .el-button {
      float: right;
    }
  }
  .sample-about-table {
    margin-bottom: 15px;
    :deep(.el-table__fixed-right) {
      top: -1px;
    }
  }

  .content {
    background: $background-color;
    text-align: left;
    position: relative;
  }
}
</style>
