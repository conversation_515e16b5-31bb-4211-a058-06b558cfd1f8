import request from '@/utils/request';
// 客户管理

// 查询列表
export function getList(data) {
  return request({
    url: `/api-diplomat/diplomat/customer/list`,
    method: 'post',
    data
  });
}
// 新增客户信息
export function saveCustomer(data) {
  return request({
    url: `/api-diplomat/diplomat/customer/save`,
    method: 'post',
    data
  });
}
// 作废客户信息
export function invalidCustomer(customerId) {
  return request({
    url: `/api-diplomat/diplomat/customer/invalid/${customerId}`,
    method: 'get'
  });
}

// 编辑客户信息
export function updateCustomer(data) {
  return request({
    url: `/api-diplomat/diplomat/customer/update`,
    method: 'post',
    data
  });
}
// 查询客户信息
export function getDetail(id) {
  return request({
    url: `/api-diplomat/diplomat/customer/info/${id}`,
    method: 'get'
  });
}
// 发票列表
export function getInvoiceList(customerId) {
  return request({
    url: `/api-diplomat/diplomat/customer/invoice/info/${customerId}`,
    method: 'get'
  });
}
// 新增发票
export function saveInvoiceApi(data) {
  return request({
    url: `/api-diplomat/diplomat/customer/invoice/save`,
    method: 'post',
    data
  });
}
// 地址列表
export function getAddress(customerId) {
  return request({
    url: `/api-diplomat/diplomat/customer/address/list/${customerId}`,
    method: 'get'
  });
}
// 新增地址
export function saveAddressApi(data) {
  return request({
    url: `/api-diplomat/diplomat/customer/address/save`,
    method: 'post',
    data
  });
}

// 删除地址
export function deleteAddressApi(id) {
  return request({
    url: `/api-diplomat/diplomat/customer/address/delete/${id}`,
    method: 'get'
  });
}
// 联系人列表
export function getContacts(customerId) {
  return request({
    url: `/api-diplomat/diplomat/customer/contacts/list/${customerId}`,
    method: 'get'
  });
}
// 新增联系人
export function saveContactsApi(data) {
  return request({
    url: `/api-diplomat/diplomat/customer/contacts/save`,
    method: 'post',
    data
  });
}

// 删除联系人
export function deleteContactsApi(id) {
  return request({
    url: `/api-diplomat/diplomat/customer/contacts/delete/${id}`,
    method: 'get'
  });
}
