// #region Basic Math Function

var Operation;
(function (Operation) {
  Operation[(Operation['add'] = 0)] = 'add';
  Operation[(Operation['subtract'] = 1)] = 'subtract';
  Operation[(Operation['multiply'] = 2)] = 'multiply';
  Operation[(Operation['divide'] = 3)] = 'divide';
})(Operation || (Operation = {}));

/**
 * 基本的计算操作
 *
 * @since 1.0 Beta
 * @category Math
 * @param {number} inputNumber  输入的浮点数
 * @param {number} inputNumber  输入的浮点数
 * @param {Operation} op  要做的操作
 * @returns {number} 计算后得到的浮点数
 * @example
 *
 * mathOperation(6, 4, Operation.add)
 * // => 10
 */
function mathOperation(inputNumber1, inputNumber2, op) {
  const floatToInteger1 = mathToInteger(inputNumber1);
  const floatToInteger2 = mathToInteger(inputNumber2);
  const magnification1 = floatToInteger1.magnification;
  const magnification2 = floatToInteger2.magnification;
  const gtMagnification = magnification1 > magnification2;

  const enlargedNumber1 = gtMagnification
    ? floatToInteger1.enlargedNumber
    : floatToInteger1.enlargedNumber * (magnification2 / magnification1);
  const enlargedNumber2 = gtMagnification
    ? floatToInteger2.enlargedNumber * (magnification1 / magnification2)
    : floatToInteger2.enlargedNumber;
  const maxMagnification = gtMagnification ? magnification1 : magnification2;
  let result = 0;

  switch (op) {
    case Operation.add:
      result = (enlargedNumber1 + enlargedNumber2) / maxMagnification;
      break;
    case Operation.subtract:
      result = (enlargedNumber1 - enlargedNumber2) / maxMagnification;
      break;
    case Operation.multiply:
      result = (enlargedNumber1 * enlargedNumber2) / (maxMagnification * maxMagnification);
      break;
    case Operation.divide:
      result = inputNumber2 === 0 ? -1 : enlargedNumber1 / enlargedNumber2;
      break;
    default:
      result = -1;
      break;
  }

  return result;
}

/**
 * Determine whether the input number is an integer
 * 判断输入数是否为整数
 *
 * @since 1.0 Beta
 * @category Math
 * @param {number} number the number need to be determined.
 * @returns {bool} Returns the boolean result.
 * @example
 *
 * mathIsInteger(6.4)
 * // => false
 */
function mathIsInteger(number) {
  return Math.floor(number) === number;
}

/**
 * 将浮点数通过放大变成整数
 *
 * @since 1.0 Beta
 * @category Math
 * @param {number} inputNumber  输入的浮点数
 * @returns {FloatToInteger} 返回放大后得到的数以及放大的倍数
 * @example
 *
 * mathToInteger(6.4)
 * // => { magnification: 10, enlargedNumber 64 }
 */
export function mathToInteger(inputNumber) {
  const floatToInteger = { magnification: 1, enlargedNumber: 0 };
  if (mathIsInteger(inputNumber)) {
    floatToInteger.enlargedNumber = inputNumber;
  } else {
    const numStr = inputNumber.toString();
    const dotPosition = numStr.indexOf('.');
    const decimalPartLength = numStr.substring(dotPosition + 1).length;
    const magnification = Math.pow(10, decimalPartLength);
    const enlargedNumber = Math.round(inputNumber * magnification);
    floatToInteger.magnification = magnification;
    floatToInteger.enlargedNumber = enlargedNumber;
  }

  return floatToInteger;
}

/**
 * Adds two numbers.
 * 两数相加
 *
 * @since 1.0 Beta
 * @category Math
 * @param {number} augend The first number in an addition.
 * @param {number} addend The second number in an addition.
 * @returns {number} Returns the total.
 * @example
 *
 * add(6, 4)
 * // => 10
 */
export function mathAdd(augend, addend) {
  return mathOperation(augend, addend, Operation.add);
}

/**
 * Subtract two numbers.
 * 两数相减
 *
 * @since 1.0 Beta
 * @category Math
 * @param {number} minuend The first number in a subtraction.
 * @param {number} subtrahend The second number in a subtraction.
 * @returns {number} Returns the difference.
 * @example
 *
 * subtract(6, 4)
 * // => 2
 */
export function mathSubtract(minuend, subtrahend) {
  return mathOperation(minuend, subtrahend, Operation.subtract);
}

/**
 * Divide two numbers.
 * 两数相除
 *
 * @since 1.0 Beta
 * @category Math
 * @param {number} dividend The first number in a division.
 * @param {number} divisor The second number in a division.
 * @returns {number} Returns the quotient.
 * @example
 *
 * divide(6, 4)
 * // => 1.5
 */
export function mathDivide(dividend, divisor) {
  return mathOperation(dividend, divisor, Operation.divide);
}

/**
 * Multiply two numbers.
 * 两数相乘
 *
 * @since 1.0 Beta
 * @category Math
 * @param {number} multiplier The first number in a multiplication.
 * @param {number} multiplicand The second number in a multiplication.
 * @returns {number} Returns the product.
 * @example
 *
 * multiply(6, 4)
 * // => 24
 */
export function mathMultiply(multiplier, multiplicand) {
  return mathOperation(multiplier, multiplicand, Operation.multiply);
}

// #endregion
