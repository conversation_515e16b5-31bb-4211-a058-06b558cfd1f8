<template>
  <!-- 近30天不合格样品 -->
  <div class="box-top">
    <h1 class="inBlock">近30天不合格样品</h1>
    <div class="top-right inBlock">{{ formatDate(recentlyThirty) }} ~ {{ formatDate(new Date()) }}</div>
  </div>
  <div v-loading="failLoading" class="box-Center">
    <LineBarPieChart :option="pieChartOption" :width="'100%'" :height="'100%'" />
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch, onBeforeUnmount } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getNameByid } from '@/utils/common';
import { getFailedTestSample } from '@/api/dataBoard';
import { formatterTips } from '../../func/formatter';
import LineBarPieChart from '@/components/LineBarPieChart';

export default {
  name: 'NearlyThirthDays',
  components: { LineBarPieChart },
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  setup(props, context) {
    const state = reactive({
      recentlyThirty: new Date().getTime() - 3600 * 1000 * 24 * 30, // 最近30天
      failLoading: false,
      timer: null,
      failList: []
    });
    watch(props, newValue => {});
    // 近30天原材料合格率
    const pieChartOption = ref({
      title: {
        text: '物料分组',
        textStyle: {
          fontSize: 18,
          color: '#fff',
          fontWeight: 700
        },
        subtextStyle: {
          fontSize: 20,
          color: '#000000',
          fontWeight: 500
        },
        textAlign: 'center', // 图例文字居中显示
        x: '29.5%', // 距离左边的距离
        y: '45%' // 距离上边的距离
      },
      tooltip: {
        show: true,
        backgroundColor: '#4791FF',
        borderColor: 'transparent',
        formatter: params => {
          return formatterTips(params);
        },
        textStyle: {
          color: '#fff',
          fontSize: 12,
          fontWeight: 400
        },
        trigger: 'item'
      },
      legend: {
        top: '15%',
        right: '25%',
        orient: 'vertical',
        icon: 'circle',
        textStyle: {
          color: '#fff',
          fontSize: '15',
          lineHeight: '24'
        }
      },
      series: [
        {
          // name: '物料分组',
          type: 'pie',
          radius: ['45%', '65%'],
          center: ['30%', '50%'],
          avoidLabelOverlap: false,
          label: {
            show: false
          },
          emphasis: {
            label: {
              show: false
            }
          },
          labelLine: {
            show: false
          },
          data: []
        }
      ]
    });
    // 近30天不合格样品
    const getFailedList = isFirst => {
      state.failLoading = true;
      getFailedTestSample().then(res => {
        state.failLoading = false;
        if (res) {
          const data = res.data.data;
          state.failList = [];
          for (var key in data) {
            state.failList.push({
              name: key,
              value: data[key]
            });
          }
          pieChartOption.value.series[0].data = state.failList;
          if (isFirst) {
            setTime();
          }
        }
      });
    };
    getFailedList();
    const setTime = () => {
      state.timer = setInterval(() => {
        getFailedList();
      }, 10000);
    };
    const removeTimer = () => {
      if (state.timer) {
        clearInterval(state.timer);
        state.timer = null;
      }
    };
    onBeforeUnmount(() => {
      removeTimer();
    });
    return {
      ...toRefs(state),
      getNameByid,
      setTime,
      removeTimer,
      pieChartOption,
      getFailedList,
      formatDate
    };
  }
};
</script>
<style lang="scss" scoped>
@import '../data-board.scss';
</style>
