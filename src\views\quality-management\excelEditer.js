// import 'luckysheet/dist/plugins/js/plugin.js';
import '../../../public/jquery.mousewheel.min.js';
import LuckySheet from '@cxist/luckysheet/dist/luckysheet.esm.js';
import '@cxist/luckysheet/dist/css/luckysheet.css';
import '@cxist/luckysheet/dist/plugins/css/pluginsCss.css';
import '@cxist/luckysheet/dist/assets/iconfont/iconfont.css';

import LuckyExcel from 'luckyexcel';
import { read } from 'xlsx/xlsx.esm.mjs';

class ExcelEditer {
  static columeHeaderWordIndex = {
    A: 0,
    B: 1,
    C: 2,
    D: 3,
    E: 4,
    F: 5,
    G: 6,
    H: 7,
    I: 8,
    J: 9,
    K: 10,
    L: 11,
    M: 12,
    N: 13,
    O: 14,
    P: 15,
    Q: 16,
    R: 17,
    S: 18,
    T: 19,
    U: 20,
    V: 21,
    W: 22,
    X: 23,
    Y: 24,
    Z: 25
  };

  exportJson = null;

  constructor(isCreateExcel, options = {}) {
    this.luckyExcelJson = null;
    this.xlsxWorkbook = null;
    this.LuckyExcelOptions = {
      container: 'luckysheet', // luckysheet is the container id
      showinfobar: false,
      lang: 'zh',
      showtoolbarConfig: {
        postil: false, // 批注
        print: false, // 打印
        chart: false, // 图表
        pivotTable: false, // 数据透视表
        screenshot: false, // 截图
        function: false, // 公式
        frozenMode: false, // 冻结方式
        sortAndFilter: false, // 排序和筛选
        conditionalFormat: false, // 条件格式
        dataVerification: false, // 数据验证
        protection: false // 工作表保护
      },
      // plugins: [LuckySheetPlugin],
      ...options
    };
    this.screenshots = [];
    if (isCreateExcel) {
      this.createExcel();
    }
  }

  createExcel() {
    LuckySheet.create(this.LuckyExcelOptions);
  }

  loadExcel(url) {
    return new Promise(async resolve => {
      const file = await fetch(url).then(response => response.blob());
      const fileBuffer = await file.arrayBuffer();
      const workbook = read(fileBuffer, { type: 'buffer' });
      this.xlsxWorkbook = workbook;
      console.log('workbook', workbook);
      LuckyExcel.transformExcelToLucky(file, (luckyExcelJson, luckysheetfile) => {
        // LuckyExcel.transformExcelToLuckyByUrl(url, 'abc', (luckyExcelJson, luckysheetfile) => {
        if (luckyExcelJson.sheets === null || luckyExcelJson.sheets.length === 0) {
          console.error('Failed to read the content of the excel file', luckyExcelJson);
          return;
        }

        this.luckyExcelJson = luckyExcelJson;

        // this.loadExcel(files[0])
        console.log('luckyExcelJson', luckyExcelJson);

        LuckySheet.destroy();
        LuckySheet.create({
          ...this.LuckyExcelOptions,
          data: luckyExcelJson.sheets
          // title: luckyExcelJson.info.name,
          // userInfo: luckyExcelJson.info?.creator || luckyExcelJson.info?.name?.creator
        });
        LuckySheet.setRangeShow('A9999', { show: false });
        resolve();
      });
    });
  }

  destroyExcel() {
    LuckySheet.destroy();
  }

  getCurrentSheet() {
    return LuckySheet.getSheet();
  }

  getValuesByDefinedName() {
    const workbook = this.xlsxWorkbook;
    const luckyExcelSheets = LuckySheet.getAllSheets();
    const values = workbook.Workbook.Sheets.reduce((accumulator, sheet) => {
      if (sheet.Hidden === 0) {
        accumulator[sheet.name] = [];
      }
      return accumulator;
    }, {});
    // const notFoundCells = []
    workbook.Workbook.Names.forEach(item => {
      const definedName = item.Name;
      const keys = item.Ref.replaceAll("'", '').split(/!?\$/);
      if (keys && keys.length === 3) {
        const [sheetName, column, row] = keys;
        if (values[sheetName]) {
          const columnIndex = ExcelEditer.columeHeaderWordIndex[column];
          const rowIndex = Number(row) - 1;
          const sheetJson = luckyExcelSheets.find(sheet => sheet.name === sheetName);
          const locatedCell = sheetJson.data?.[rowIndex]?.[columnIndex];
          if (locatedCell?.mc?.c === columnIndex && locatedCell?.mc?.r === rowIndex) {
            values[sheetName].push({
              definedName,
              column,
              columnIndex,
              row,
              rowIndex,
              cell: locatedCell,
              rawValue:
                locatedCell.v !== undefined
                  ? locatedCell.v
                  : locatedCell.ct?.s
                  ? locatedCell.ct.s.map(textItem => textItem.v).join('')
                  : '',
              formattedText: locatedCell.m || ''
            });
          } else if (!locatedCell.mc && columnIndex > -1 && rowIndex > -1) {
            values[sheetName].push({
              definedName,
              column,
              columnIndex,
              row,
              rowIndex,
              cell: locatedCell,
              rawValue: locatedCell.v || '',
              formattedText: locatedCell.m || ''
            });
            // notFoundCells.push({
            //   definedName,
            //   column,
            //   columnIndex,
            //   row,
            //   rowIndex,
            //   cell: locatedCell
            // })
          }
        }
      } else {
        return new Error('failed to parse workbook ref', keys, item.Ref);
      }
    });
    // if (notFoundCells.length > 0) {
    //   notFoundCells.sort((a, b) => a.rowIndex - b.rowIndex)
    //   console.log('notFoundCells', notFoundCells)
    //   for (let index = 0; index < sheetJson.celldata.length; index++) {
    //     const item = sheetJson.celldata[index]
    //     const checkItem = notFoundCells[0]
    //     if (item.r === checkItem.rowIndex && item.c === checkItem.columnIndex) {
    //       values.push({
    //         ...checkItem,
    //         rawValue: item?.v?.v || '',
    //         formattedText: item?.v?.m || '',
    //       })
    //       notFoundCells.splice(0, 1)
    //       if (notFoundCells.length === 0) {
    //         break
    //       }
    //     }
    //   }
    // }
    return values;
  }

  setValuesByDefinedName(data) {
    const workbook = this.xlsxWorkbook;
    const values = workbook.Workbook.Sheets.reduce((accumulator, sheet) => {
      if (sheet.Hidden === 0) {
        accumulator[sheet.name] = [];
      }
      return accumulator;
    }, {});
    workbook.Workbook.Names.forEach(item => {
      const definedName = item.Name;
      const keys = item.Ref.replaceAll("'", '').split(/!?\$/);
      if (keys && keys.length === 3) {
        const [sheetName, column, row] = keys;
        if (values[sheetName]) {
          const columnIndex = ExcelEditer.columeHeaderWordIndex[column];
          const rowIndex = Number(row) - 1;
          if (data[definedName] !== undefined && data[definedName] !== '') {
            LuckySheet.setCellValue(rowIndex, columnIndex, data[definedName]);
          }
        }
      } else {
        return new Error('failed to parse workbook ref', keys, item.Ref);
      }
    });
  }

  getExcelData() {
    return {
      luckyExcelJson: this.luckyExcelJson,
      xlsxWorkbook: this.xlsxWorkbook
    };
  }

  getScreenshots() {
    const currentSheet = LuckySheet.getSheet();
    // const workbookSheet = this.xlsxWorkbook.Sheets[currentSheet.name]
    // console.log('currentSheet', currentSheet)
    // console.log('workbookSheet', workbookSheet)
    // console.log('workbookSheet getKeys', Object.keys(workbookSheet))
    if (currentSheet && currentSheet.images) {
      for (const imageKey in currentSheet.images) {
        const imageItem = currentSheet.images[imageKey];
        this.screenshots.push({ ...imageItem.default, src: imageItem.src });
      }
    }
    // if (!workbookSheet) {
    //   console.log('Not found the workbook sheet!')
    //   return this.screenshots
    // }
    // const sheetKeys = Object.keys(workbookSheet)
    // let mini = 9999999
    // let max = -1
    // let startCoord = ''
    // let endCoord = ''
    // sheetKeys.forEach(key => {
    //   if (!key.includes('!')) {
    //     const num = Number(key.replace(/^[A-Z]+/, (match) => ExcelEditer.columeHeaderWordIndex[match]))
    //     console.log('num', num)
    //     if (num < mini) {
    //       mini = num
    //       startCoord = key
    //     } else if (num > max) {
    //       max = num
    //       endCoord = key
    //     }
    //   }
    // })
    // LuckySheet.setRangeShow(startCoord + ':' + endCoord)
    // console.log('mini', mini)
    // console.log('max', max)
    // console.log('startCoord', startCoord)
    // console.log('endCoord', endCoord)
    let maxRow = 0;
    let maxColumn = 0;
    for (let rowIndex = 0; rowIndex < currentSheet.data.length; rowIndex++) {
      const rowArr = currentSheet.data[rowIndex];
      if (rowArr[0] && rowArr[1] && rowArr[2]) {
        for (let itemIndex = 0; itemIndex < rowArr.length; itemIndex++) {
          if (!rowArr[itemIndex] && !rowArr[itemIndex + 1] && !rowArr[itemIndex + 2]) {
            if (itemIndex - 1 > maxColumn) {
              maxColumn = itemIndex - 1;
            }
            console.log('itemIndex', itemIndex);
            break;
          }
        }
      } else {
        maxRow = rowIndex - 1;
        break;
      }
    }
    // console.log('maxRow', maxRow)
    // console.log('maxColumn', maxColumn)
    LuckySheet.setRangeShow({ row: [0, maxRow], column: [0, maxColumn] });
    const screenshot = LuckySheet.getScreenshot();
    this.screenshots.unshift({ top: 0, left: 0, src: screenshot });
    return this.screenshots;
  }
}

export default ExcelEditer;
