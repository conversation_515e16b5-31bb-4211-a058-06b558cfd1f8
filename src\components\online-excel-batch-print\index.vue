<template>
  <div class="online-excel">
    <div class="online-excel__container">
      <div id="luckysheet" />
    </div>
    <div id="print-preview">
      <div class="online-excel__btn">
        <el-button size="small" @click="goBack">返 回</el-button>
        <el-button size="small" @click="print">打 印</el-button>
      </div>
      <el-scrollbar>
        <div id="print-preview__container">
          <div
            v-for="(images, sIndex) in screenshotImages"
            :key="sIndex"
            class="online-excel__screenshots"
            :style="{
              width: mmToPx(paperWidth) + 'px',
              height: mmToPx(paperHeight) + 'px'
            }"
          >
            <div class="online-excel__image_box">
              <img
                v-for="(image, index) in images"
                :key="index"
                :src="image.src"
                :style="{
                  position: 'absolute',
                  top: image.top + 'px',
                  left: image.left + 'px',
                  width: typeof image.width === 'number' ? image.width + 'px' : image.width,
                  height: typeof image.height === 'number' ? image.height + 'px' : image.height,
                  'max-height': mmToPx(paperHeight) + 'px',
                  'max-width': mmToPx(paperWidth) + 'px'
                }"
                alt=""
              />
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
import { onBeforeUnmount, onMounted, reactive, ref, nextTick } from 'vue';
import PrintSetting from './print-setting';
import { useRoute } from 'vue-router';
import router from '@/router/index.js';
import ExcelEditor from './excelEditor';
import printJS from 'print-js';
import html2canvas from 'html2canvas';
import QRCode from 'qrcode';
import VanillaCode128 from '@/utils/vanilla-code128.js';

const emit = defineEmits(['print-success']);

const route = useRoute();
const state = reactive({
  paperSize: route.query.paperSize,
  paperPrintWidth: route.query.paperPrintWidth,
  paperPrintHeight: route.query.paperPrintHeight,
  paperOrientation: route.query.paperOrientation,
  detail: {},
  excelTemplateDataList: []
});
const paperWidth = ref(0);
const paperHeight = ref(0);
const screenshotImages = ref([]);

const excelEditor = ref();

// 加载Excel模板
const loadExcelTemplate = async templateUrl => {
  console.log('加载Excel模板', templateUrl);
  excelEditor.value = new ExcelEditor();
  await excelEditor.value.loadExcel(templateUrl);
};

// 加载预览数据
const loadPreviewData = async previewData => {
  const excelCellBindFields = excelEditor.value.getAllCellFields();
  console.log('excelCellBindFields', excelCellBindFields);
  for (const data of previewData) {
    // 获取当前对象的所有 key
    for (const key of excelCellBindFields) {
      if (key.startsWith('img_qrcode')) {
        data[key] = await generateQRCode(data[key]);
      }

      if (key.startsWith('img_barcode')) {
        const originKey = key.replaceAll('img_barcode_', '');
        const barcode = await generateBarcode(data[originKey]);
        console.log('条形码生成结果', key, data[originKey], barcode);
        data[key] = barcode;
      }
    }
  }
  console.log('加载预览数据', previewData);
  state.excelTemplateDataList = previewData;
};

// 加载预览图片
const loadPreviewImage = async () => {
  await new Promise(resolve => setTimeout(resolve, 500));
  console.log('加载预览图片', state.excelTemplateDataList);
  for (const excelData of state.excelTemplateDataList) {
    excelEditor.value.setValuesByDefinedName(excelData);

    // 强制等待500ms
    await new Promise(resolve => setTimeout(resolve, 500));

    // 截图生成打印图片
    const screenshots = excelEditor.value.getScreenshots();
    screenshotImages.value.push(screenshots);

    // 等待dom刷新
    await nextTick();

    // 计算打印页面大小
    loadPrintSize();
    console.log('loadPreviewImage', screenshotImages);
  }
};

const loadPrintSize = () => {
  const documents = document.getElementsByClassName('online-excel__image_box');
  for (const dom of documents) {
    const images = dom.getElementsByTagName('img');

    // 处理这些img标签
    let height = 0;
    let width = 0;
    for (const image of images) {
      console.log(image.height); // 这里可以对每个img标签进行操作
      if (height < image.height) {
        height = image.height;
      }

      if (width < image.width) {
        width = image.width;
      }
    }
    dom.style.width = width + 'px';
    dom.style.height = height + 'px';
  }
};

// 打印
const print = async () => {
  const printables = [];
  const documents = document.getElementsByClassName('online-excel__screenshots');
  for (const dom of documents) {
    const canvas = await html2canvas(dom, {
      dpi: 600,
      scale: 2,
      background: '#FFFFFF',
      useCORS: true
    });

    const pageData = canvas.toDataURL('image/jpeg', 2.0);

    printables.push(pageData);
  }

  let size = '';
  if (state.paperSize === '自定义') {
    size = state.paperPrintWidth + 'mm ' + state.paperPrintHeight + 'mm ' + state.paperOrientation.toLowerCase();
  } else {
    size = state.paperSize + ' ' + state.paperOrientation.toLowerCase();
  }

  console.log('@page size', size);

  printJS({
    printable: printables,
    style: `body { margin: 0; padding: 0; border: 0;} img { width: 100%; display: block; } @page{size: ${size}; overflow: hidden; margin: 0 !important; padding: 10px; }`,
    type: 'image',
    scanStyles: false,
    documentTitle: '产品合格证'
  });

  emit('print-success');
};

// 生成二维码
const generateQRCode = async text => {
  try {
    return await QRCode.toDataURL(text, {
      width: 80,
      height: 80,
      margin: 0
    });
  } catch (err) {
    console.error('生成二维码失败:', err);
  }
};

// 生成条形码
const generateBarcode = async (value, options = {}) => {
  try {
    if (!value) {
      return '';
    }

    return await VanillaCode128.toDataURL(value, {
      displayValue: false,
      margin: 10,
      width: 2,
      height: 80,
      ...options
    });
  } catch (err) {
    console.error('生成条形码失败:', err);
  }
};

// 计算页面纸张大小，单位mm
const calculatePaperSize = () => {
  console.log(
    '页面大小',
    PrintSetting.paperSize[state.paperSize],
    PrintSetting.paperOrientation[state.paperOrientation]
  );
  if (PrintSetting.paperOrientation[state.paperOrientation] === PrintSetting.paperOrientation.Portrait) {
    paperWidth.value = PrintSetting.paperSize[state.paperSize][0];
    paperHeight.value = PrintSetting.paperSize[state.paperSize][1];
  } else {
    paperWidth.value = PrintSetting.paperSize[state.paperSize][1];
    paperHeight.value = PrintSetting.paperSize[state.paperSize][0];
  }
};

const mmToPx = (mm, dpi = 96) => {
  const pxPerMm = dpi / 25.4;
  return mm * pxPerMm * window.devicePixelRatio;
};

// 返回
const goBack = () => {
  router.go(-1);
};

// 暴露方法给父组件
defineExpose({
  loadPreviewData,
  loadExcelTemplate,
  loadPreviewImage
});

// 生命周期
onMounted(async () => {
  calculatePaperSize();
});

onBeforeUnmount(() => {
  excelEditor.value?.destroyExcel();
});
</script>

<style lang="scss" scoped>
.online-excel {
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  margin: 10px;
}

.online-excel__container {
  position: relative;
  width: 100%;
  height: 100%;
  // z-index: 1;
}

#luckysheet {
  width: 100%;
  height: 100%;
  visibility: hidden;
}

#print-preview {
  position: absolute;
  width: 100%;
  min-height: 100%;
  top: 6px;
  opacity: 1;
  background-color: #fff;
  // z-index: 9999;
}

#print-preview__container {
  position: relative;
  top: 0;
  width: 300mm;
  margin: 0 auto;
  background-color: #fff;
  box-shadow: 0 4px 12px 4px rgba(0, 0, 0, 0.08), 0 2px 4px -2px rgba(0, 0, 0, 0.16);
  border-radius: 5px;
  filter: drop-shadow(0px 3px 8px rgba(0, 0, 0, 0.12));

  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 40px;
}

.online-excel__btn {
  position: fixed;
  top: 60px;
  right: 20px;
  z-index: 99;
}

.online-excel__image_box {
  position: relative;
}

.online-excel__screenshots {
  position: relative;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
