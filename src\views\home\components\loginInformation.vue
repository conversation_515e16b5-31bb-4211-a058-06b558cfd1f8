<template>
  <!-- 近30天用户登录情况 -->
  <div class="LoginInformation">
    <div class="top-agency">
      <div class="title">近30天用户登录情况</div>
    </div>
    <div class="echartsContent">
      <LineBarPieChart v-if="showChart" :option="barOption" :width="'100%'" height="600px" />
      <el-empty v-else :image="emptyImg" />
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, ref } from 'vue';
import LineBarPieChart from '@/components/LineBarPieChart';
import { getBarApi } from '@/api/home';
import emptyImg from '@/assets/img/empty-chart.png';

export default {
  name: 'LoginInformation',
  components: { LineBarPieChart },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    // const { appContext } = getCurrentInstance()
    const state = reactive({
      barLineJson: [],
      barJson: [],
      showChart: true
    });
    const barOption = ref({
      legend: {
        top: '5%',
        itemGap: 20
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: params => {
          var text = `<div>${params[0].name}</div>`;
          var textTime = '';
          params.forEach(item => {
            if (item.value !== 0) {
              textTime = formatterMinue(item.value);
              text += `<div>${item.marker}${item.seriesName}：${textTime}</div> `;
            }
          });
          return text;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        boundaryGap: [0, 0.1],
        axisLabel: {
          rotate: 30
        }
      },
      yAxis: {
        type: 'category',
        data: []
      },
      series: [
        {
          name: '环比',
          type: 'bar',
          data: [],
          barWidth: '50%',
          barMaxWidth: 50,
          itemStyle: {
            color: '#B3E09C',
            borderColor: '#67C23A',
            borderWidth: 1,
            borderType: 'solid'
          }
        },
        {
          name: '当前',
          type: 'bar',
          data: [],
          barWidth: '50%',
          barMaxWidth: 50,
          itemStyle: {
            color: '#F2D09D',
            borderColor: '#E6A23C',
            borderWidth: 1,
            borderType: 'solid'
          }
        }
      ]
    });
    // 近30天用户登录情况
    const getBarOption = () => {
      getBarApi().then(res => {
        if (res) {
          state.barJson = res.data.data;
          barOption.value.yAxis.data = state.barJson
            .map(item => {
              return item.userName;
            })
            .reverse();
          // 环比
          barOption.value.series[0].data = state.barJson
            .map(item => {
              return item.lastLength;
            })
            .reverse();
          // 当前
          barOption.value.series[1].data = state.barJson
            .map(item => {
              return item.currentLength;
            })
            .reverse();
        } else {
          state.showChart = false;
        }
      });
    };
    getBarOption();
    const formatterMinue = value => {
      var textTime = '';
      if (value !== 0) {
        // 分钟数
        var minuteNumber = Number(value % 60);
        // 小时数
        var hourNumber = parseInt(value / 60);
        // 天数
        var days = parseInt(hourNumber / 24);
        // 周数
        var weeks = parseInt(hourNumber / 24 / 7);
        if (hourNumber < 24 && hourNumber > 0) {
          // 不满一天
          textTime = minuteNumber ? `${hourNumber}小时 ${minuteNumber}分钟` : `${hourNumber}小时`;
        } else if (hourNumber >= 24 && hourNumber < 168) {
          // 满一天不满一周
          textTime = `${days}天 ${parseInt(hourNumber % 24)}小时 ${minuteNumber}分钟`;
        } else if (hourNumber >= 168) {
          // 满一周
          textTime = `${weeks}周 ${parseInt((hourNumber % 168) / 24)}天 ${parseInt(
            (hourNumber % 168) % 24
          )}小时 ${minuteNumber}分钟`;
        } else {
          // 不满一小时
          textTime = `${minuteNumber}分钟`;
        }
      }
      return textTime;
    };
    return { ...toRefs(state), emptyImg, barOption, getBarOption, formatterMinue };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss">
.LoginInformation {
  margin-bottom: 15px;
}
.echartsContent {
  background-color: $background-color;
}
.top-agency {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 35px;
  margin-bottom: 0;
}
.top-agency .title {
  font-size: 16px;
  color: $tes-font;
  line-height: 1.5rem;
  margin-left: -1rem;
  font-weight: 500;
  &::before {
    background: $tes-primary;
    content: '';
    display: inline-block;
    height: 0.9rem;
    margin-bottom: -0.03125rem;
    margin-right: 0.625rem;
    width: 0.25rem;
  }
}
</style>
