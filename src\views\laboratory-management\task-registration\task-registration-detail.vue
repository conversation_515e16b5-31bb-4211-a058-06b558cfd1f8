<template>
  <!-- 检验申请详情 -->
  <DetailLayout>
    <template #page-header>
      <div class="header-flex flex-between">
        <div class="page-title">
          编号：{{ taskInfo.entrustNo ? taskInfo.entrustNo : '保存基本信息后生成' }}
          <el-tag size="small" :type="filterStatus(taskInfo.status)[0]">{{ filterStatus(taskInfo.status)[1] }}</el-tag>
        </div>
        <div class="btn-group">
          <el-button
            v-if="
              (taskInfo.status === 0 || taskInfo.status === 4) && isEdit && getPermissionBtn('SubmissionCommission')
            "
            size="small"
            type="primary"
            @click="submitTask"
            @keyup.prevent
            @keydown.enter.prevent
            >{{ taskInfo.status === 4 ? '重新提交' : '提交委托' }}</el-button
          >
        </div>
      </div>
    </template>
    <el-collapse v-model="activeNames" class="collapse-wrap">
      <el-collapse-item name="1">
        <template #title>
          <div class="collapse-header-title">
            <div class="title">基本信息</div>
          </div>
        </template>
        <div class="panel-header">
          <el-button
            v-if="isEdit && getPermissionBtn('BasicInformationEditing')"
            size="small"
            icon="el-icon-edit"
            @click="editInfo"
            @keyup.prevent
            @keydown.enter.prevent
            >编辑</el-button
          >
        </div>
        <div class="panel-content">
          <el-form ref="taskInfoRef" :model="formInline" label-width="110px">
            <el-row :gutter="20">
              <el-col
                v-for="item in pageViewGroup[`${taskInfo.entrustType}-basicInfo`]"
                :key="item.fieldKey"
                :span="Number(item.columnWidth)"
              >
                <el-form-item :label="`${item.fieldName}：`" :prop="item.fieldKey">
                  <!-- 文本 -->
                  <template v-if="item.fieldType == 'text'">
                    <span>{{ taskInfo[item.fieldKey] || '--' }}</span>
                  </template>
                  <!-- 人员 -->
                  <template v-if="item.fieldType == 'person'">
                    <UserTag
                      v-if="item.fieldKey == 'regUserId'"
                      :name="taskInfo.regUserName || getNameByid(taskInfo.regUserId) || '--'"
                    />
                    <UserTag v-else :name="getNameByid(taskInfo[item.fieldKey]) || taskInfo[item.fieldKey] || '--'" />
                  </template>
                  <!-- 日期 -->
                  <template v-if="item.fieldType == 'date'">
                    <span>{{ formatDate(taskInfo[item.fieldKey]) || '--' }}</span>
                  </template>
                  <!-- 自定义 -->
                  <template v-if="item.fieldType == 'custom'">
                    <!-- 委托类型 -->
                    <span v-if="item.fieldKey == 'entrustType'">
                      {{ filterType(taskInfo[item.fieldKey]) }}
                    </span>
                    <span v-if="item.fieldKey == 'materialCode'">
                      {{ formatterCode(taskInfo[item.fieldKey]) || taskInfo[item.fieldKey] || '--' }}
                    </span>
                    <!-- 服务类型 -->
                    <span v-if="item.fieldKey == 'serviceType'">
                      {{ serviceTypeJSON[taskInfo[item.fieldKey]] }}
                    </span>
                    <!-- 样品回收 -->
                    <span v-if="item.fieldKey == 'sampleRecycle'">
                      {{ taskInfo[item.fieldKey] === 0 ? '否' : '是' }}
                    </span>
                    <!-- 报告语言 -->
                    <span v-if="item.fieldKey == 'bgyy'">
                      {{ taskReportInfo() }}
                    </span>
                    <!-- 盖章范围 -->
                    <span v-if="item.fieldKey == 'gzfw'">
                      {{ `${taskInfo.isCnas ? 'CNAS' : ''} ${taskInfo.isCma ? 'CMA' : ''}` }}
                    </span>
                    <!-- 领取方式 -->
                    <span v-if="item.fieldKey == 'receiveMethod'">
                      {{ receiveMethodJSON[taskInfo[item.fieldKey]] || '--' }}
                    </span>
                    <!-- 样品验收 -->
                    <span v-if="item.fieldKey == 'sampleAccept'">
                      {{ sampleAcceptJSON[taskInfo[item.fieldKey]] || '--' }}
                    </span>
                    <!-- 报告编制 -->
                    <span v-if="item.fieldKey == 'reportPreparation'">
                      <span v-if="taskInfo.reportPreparation === 4"
                        >{{ reportPreparationJSON[taskInfo.reportPreparation] }}&nbsp;&nbsp;&nbsp;{{
                          taskInfo.reportPreparationContent
                        }}
                      </span>
                      <span v-else>{{ reportPreparationJSON[taskInfo.reportPreparation] || '--' }}</span>
                    </span>
                  </template>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-collapse-item>
      <el-collapse-item name="2">
        <!--样品相关 -->
        <template #title>
          <div class="collapse-header-title">客户信息</div>
        </template>
        <div class="panel-header">
          <el-button
            v-if="isEdit && getPermissionBtn('RegisterCustomerEdit')"
            size="small"
            icon="el-icon-edit"
            @click="editClientInfo"
            @keyup.prevent
            @keydown.enter.prevent
            >编辑</el-button
          >
        </div>
        <div class="collapse-content">
          <!--委托方-->
          <div class="collapse-item">
            <div class="collapse-content-title"><span class="line-space" />委托方信息</div>
            <el-row :gutter="20" class="collapse-item">
              <el-col v-for="item in pageViewGroup[`entrust`]" :key="item.fieldKey" :span="Number(item.columnWidth)">
                <span class="title">{{ item.fieldName }}：</span>
                <span class="txt">
                  {{ clientInfo.entrust[item.fieldKey] ? clientInfo.entrust[item.fieldKey] : '--' }}
                </span>
              </el-col>
            </el-row>
            <el-divider />
          </div>
          <!--缴款方-->
          <div class="collapse-item">
            <div class="collapse-content-title"><span class="line-space" />缴款方信息</div>
            <el-row :gutter="20" class="collapse-item">
              <el-col v-for="item in pageViewGroup[`payer`]" :key="item.fieldKey" :span="Number(item.columnWidth)">
                <span class="title">{{ item.fieldName }}：</span>
                <span v-if="item.fieldType == 'text'" class="txt">
                  {{ clientInfo.payer[item.fieldKey] ? clientInfo.payer[item.fieldKey] : '--' }}
                </span>
                <span v-if="item.fieldType == 'custom'" class="txt">
                  <span v-if="item.fieldKey == 'invoiceType'">
                    {{ invoiceTypeJSON[clientInfo.payer.invoiceType] || '--' }}
                  </span>
                  <span v-if="item.fieldKey == 'paymentMethod'">
                    {{ paymentMethodTypeJSON[clientInfo.payer.paymentMethod] || '--' }}
                  </span>
                </span>
              </el-col>
            </el-row>
            <el-divider />
          </div>
          <!--发票邮寄信息-->
          <div class="collapse-item">
            <div class="collapse-content-title"><span class="line-space" />发票邮寄信息</div>
            <el-row :gutter="20" class="collapse-item">
              <el-col v-for="item in pageViewGroup[`invoice`]" :key="item.fieldKey" :span="Number(item.columnWidth)">
                <span class="title">{{ item.fieldName }}：</span>
                <span v-if="item.fieldType == 'text'" class="txt">
                  {{ clientInfo.invoice[item.fieldKey] ? clientInfo.invoice[item.fieldKey] : '--' }}
                </span>
              </el-col>
            </el-row>
            <el-divider />
          </div>
          <!--报告邮寄信息-->
          <div class="collapse-item">
            <div class="collapse-content-title"><span class="line-space" />报告邮寄信息</div>
            <el-row :gutter="20" class="collapse-item">
              <el-col v-for="item in pageViewGroup[`report`]" :key="item.fieldKey" :span="Number(item.columnWidth)">
                <span class="title">{{ item.fieldName }}：</span>
                <span v-if="item.fieldType == 'text'" class="txt">
                  {{ clientInfo.report[item.fieldKey] ? clientInfo.report[item.fieldKey] : '--' }}
                </span>
              </el-col>
            </el-row>
            <el-divider />
          </div>
          <!--生产商信息-->
          <div class="collapse-item">
            <div class="collapse-content-title"><span class="line-space" />生产商信息</div>
            <el-row :gutter="20" class="collapse-item">
              <el-col v-for="item in pageViewGroup[`producer`]" :key="item.fieldKey" :span="Number(item.columnWidth)">
                <span class="title">{{ item.fieldName }}：</span>
                <span v-if="item.fieldType == 'text'" class="txt">
                  {{ clientInfo.producer[item.fieldKey] ? clientInfo.producer[item.fieldKey] : '--' }}
                </span>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-collapse-item>
      <el-collapse-item name="3">
        <!-- 样品相关 -->
        <template #title>
          <div class="collapse-header-title">样品信息</div>
        </template>
        <PanelSampleInfo
          :is-invalidated="taskInfo.isInvalidated"
          :task-id="formInline.id"
          :task-status="taskInfo.status"
          :page-view="pageViewGroup"
          :material-code="formInline.materialCode"
          :show-detail="isEdit"
          :sample-info="sampleInfoList"
          @set-info="getSampleInfoList"
        />
      </el-collapse-item>
      <el-collapse-item name="4">
        <!-- 费用信息 -->
        <template #title>
          <div class="collapse-header-title">费用信息</div>
        </template>
        <PanelFeeInfo
          :task-id="formInline.id"
          :show-detail="isEdit"
          :task-status="taskInfo.status"
          :fee-info-data="feeInfoData"
          @set-info="getFeeInfoList"
        />
      </el-collapse-item>
      <el-collapse-item name="5">
        <!-- 费用信息 -->
        <template #title>
          <div class="collapse-header-title">附件信息</div>
        </template>
        <PanelAttachInfo
          :task-id="formInline.id"
          :show-detail="isEdit"
          :task-status="taskInfo.status"
          :attach-info="attachInfoList"
          @set-info="getAttachInfoList"
        />
      </el-collapse-item>
      <el-collapse-item name="6">
        <!-- 费用信息 -->
        <template #title>
          <div class="collapse-header-title">技术评审</div>
        </template>
        <PanelReviewInfo
          :show-detail="isEdit"
          :process-id="formInline.processInstanceId"
          :task-status="taskInfo.status"
          :review-info="reviewInfoList"
          @set-info="getReviewInfo"
        />
      </el-collapse-item>
    </el-collapse>

    <template #other>
      <!-- 编辑委托登记 -->
      <DialogTaskRegistration
        :show="showEditInfoDialog"
        title="编辑基本信息"
        :is-edit="true"
        :info="taskInfo"
        @close="closeEditInfo"
      />
      <!-- 编辑客户信息 -->
      <DialogClientInfo
        :show="showEditClientInfoDialog"
        title="编辑客户信息"
        :is-edit="true"
        :info="clientInfo"
        @close="closeEditClientInfo"
      />
    </template>
  </DetailLayout>
</template>

<script>
import { reactive, toRefs, ref, computed, watch } from 'vue';
// import router from '@/router/index.js'
// import { ElMessage, ElMessageBox } from 'element-plus'
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { getLoginInfo } from '@/utils/auth';
// import { drageHeader } from '@/utils/formatTable'
import _ from 'lodash';
import DetailLayout from '@/components/DetailLayout';
import UserTag from '@/components/UserTag';
import {
  getTaskRegistrationInfo,
  getTaskCustomerInfo,
  getTaskSampleList,
  getFeeInfoListByTaskId,
  getAttachmentListByTaskId,
  getTechnicalReviewList,
  submitTaskRegistrationInfo,
  saveTechnicalReviewInfo
} from '@/api/task-registration';
import { formatTaskType } from '@/utils/formatIndustryTerm';
import DialogTaskRegistration from './components/DialogTaskRegistration.vue';
import DialogClientInfo from './components/DialogClientInfo.vue';
import PanelSampleInfo from './components/PanelSampleInfo.vue';
import PanelFeeInfo from './components/PanelFeeInfo.vue';
import PanelAttachInfo from './components/PanelAttachInfo.vue';
import PanelReviewInfo from './components/PanelReviewInfo.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { filterStatus } from './func/format';
import { getViewByBindingMenu } from '@/api/tableView';
import { formatViewData } from '@/utils/formatJson';

export default {
  name: 'TaskRegistrationDetail',
  components: {
    DetailLayout,
    PanelSampleInfo,
    UserTag,
    PanelFeeInfo,
    PanelAttachInfo,
    PanelReviewInfo,
    DialogTaskRegistration,
    DialogClientInfo
  },
  setup() {
    // const { proxy } = getCurrentInstance()
    const route = useRoute();
    // console.log(route.query)
    const store = useStore().state;
    const datas = reactive({
      activeNames: route.query.flag === '3' ? ['1', '6'] : ['1', '2', '3', '4', '5', '6'],
      clientInfo: {
        entrust: {},
        invoice: {},
        payer: {},
        producer: {},
        report: {},
        superId: ''
      },
      invoiceTypeJSON: {
        0: '普票',
        1: '专票'
      },
      paymentMethodTypeJSON: {
        0: '现金',
        1: '转账'
      },
      receiveMethodJSON: {
        0: '自取',
        1: '到付',
        2: '顺丰'
      },
      sampleAcceptJSON: {
        0: '符合',
        1: '不符合',
        2: '转实验室验收'
      },
      reportPreparationJSON: {
        0: '一份委托单出一份报告',
        1: '相同项目出一份报告',
        2: '一个样品出一份报告',
        3: '不出具报告',
        4: '其他要求'
      },
      serviceTypeJSON: {
        0: '标准服务',
        1: '加急服务',
        2: '特急服务'
      },
      sampleInfoList: [],
      feeInfoData: [],
      attachInfoList: [],
      reviewInfoList: [],
      currentAccountId: getLoginInfo().accountId,
      userOptions: store.common.nameList,
      copyUserOptions: store.common.nameList,
      showDetail: route.query.id !== '0',
      formInline: {
        registerUserId: '',
        registerDepartment: '',
        registerTime: '',
        type: 1,
        inputWarehouseNo: '',
        wareHouseNo: '',
        wareHouseName: '',
        wareHousePerson: '',
        inputWarehouseDate: '',
        applyStatus: 1
      },
      taskInfo: {},
      taskInfoRef: ref(),
      taskInfoRule: {
        registerUserId: [{ required: true, message: '请输入登记人' }],
        registerTime: [{ required: true, message: '请选择登记日期' }],
        type: [{ required: true, message: '请输入检验类型' }]
      },
      typeOptions: [
        { id: 1, name: '采购入库' },
        { id: 2, name: '过程检验' },
        { id: 3, name: '完工检验' }
      ],
      showEditInfoDialog: false,
      detailLoading: false,
      showEditClientInfoDialog: false,
      backUrl: '/inspection-application',
      pageViewGroup: {
        '1-basicInfo': {},
        '2-basicInfo': {},
        '3-basicInfo': {},
        sampleRelated: {}
      }
    });
    const formatterCode = val => {
      const params = store.user.materialList.filter(item => {
        return item.code == val;
      })[0];
      return params?.name;
    };
    const getDetailView = async () => {
      datas.detailLoading = true;
      const res = await getViewByBindingMenu('TaskRegistrationDetail');
      datas.detailLoading = false;
      if (res) {
        datas.pageViewGroup = formatViewData(res.data.data[0].sysEmployeeListConfigList);
      }
    };
    getDetailView();
    // 获取委托登记信息
    const getTaskRegInfo = async isDialog => {
      const response = await getTaskRegistrationInfo(route.query.id);
      if (response) {
        datas.taskInfo = response.data.data;
        datas.formInline = datas.taskInfo;
        if (!isDialog) {
          if (datas.taskInfo.id) {
            getTaskCustomerInfo(datas.taskInfo.id).then(response => {
              if (response && response.data.data) {
                const result = response.data.data;
                if (result.entrust?.id) {
                  datas.clientInfo.entrust = result.entrust;
                }
                if (result.invoice?.id) {
                  datas.clientInfo.invoice = result.invoice;
                }
                if (result.payer?.id) {
                  datas.clientInfo.payer = result.payer;
                }
                if (result.report?.id) {
                  datas.clientInfo.report = result.report;
                }
                if (result.producer?.id) {
                  datas.clientInfo.producer = result.producer;
                }
              }
              datas.clientInfo.superId = datas.taskInfo.id;
            });
          }
          getSampleInfoList();
          getAttachInfoList();
          getReviewList();
        }
      }
    };

    // 判断是否是新增, 要是有id调用详情接口
    getTaskRegInfo();
    // 过滤检验类型
    const filterType = type => {
      // 检验类型 1、采购入库 2、过程检验 3、完工检验
      return formatTaskType(type);
    };
    // 过滤登记人
    const filterUserList = val => {
      if (val) {
        const list = [];
        datas.copyUserOptions.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        datas.userOptions = list;
      } else {
        datas.userOptions = datas.copyUserOptions;
      }
    };
    // 登记人-change
    const changeUser = id => {
      datas.formInline.registerUserId = id;
    };
    // 选择登记日期
    const changeRegisterTime = time => {
      // console.log(time)
    };
    // 检验类型-change
    const changeType = type => {
      datas.formInline.type = type;
    };
    // 生产工序-change
    const changeProductionProcedure = name => {
      // console.log(name)
      datas.formInline.productionProcedure = name;
    };
    // 选择入库日期
    const changeInputWarehouseDate = time => {
      // console.log(time)
    };
    // 获取样品相关列表
    const getSampleInfoList = list => {
      if (datas.taskInfo.id) {
        getTaskSampleList(datas.taskInfo.id).then(resp => {
          if (resp && resp.data.data) {
            datas.sampleInfoList = resp.data.data;
            getFeeInfoList();
          }
        });
      }
    };

    const getFeeInfoList = list => {
      if (datas.taskInfo.id) {
        getFeeInfoListByTaskId(datas.taskInfo.id).then(resp => {
          if (resp && resp.data.data) {
            datas.feeInfoData = resp.data.data;
          }
        });
      }
    };

    const getAttachInfoList = list => {
      if (datas.taskInfo.id) {
        getAttachmentListByTaskId(datas.taskInfo.id).then(resp => {
          if (resp && resp.data.data) {
            datas.attachInfoList = resp.data.data;
          }
        });
      }
    };

    const getReviewList = list => {
      if (datas.taskInfo.processInstanceId) {
        getTechnicalReviewList(datas.taskInfo.processInstanceId).then(resp => {
          if (resp && resp.data.data) {
            const processList = resp.data.data;
            datas.reviewInfoList = processList;
          }
        });
      }
    };

    function getReviewInfo(value) {
      getTaskRegistrationInfo(route.query.id).then(res => {
        if (res !== false) {
          datas.taskInfo = res.data.data;
          datas.formInline = datas.taskInfo;
          getReviewList();
          if (value.hasFile) {
            // 上传过附件，刷新附件列表
            getAttachInfoList();
          }
        }
      });
    }

    // 编辑
    const editInfo = () => {
      // console.log(datas.taskInfo)
      datas.showEditInfoDialog = true;
    };

    // 编辑弹出框-关闭
    const closeEditInfo = isRefresh => {
      datas.showEditInfoDialog = false;
      if (isRefresh) {
        getTaskRegInfo(true);
      }
    };

    // #region 客户信息

    // 编辑
    const editClientInfo = () => {
      // console.log(datas.taskInfo)
      datas.showEditClientInfoDialog = true;
    };

    // 编辑弹出框-关闭
    const closeEditClientInfo = clientInfo => {
      if (clientInfo && clientInfo.superId) {
        datas.clientInfo = clientInfo;
      }
      datas.showEditClientInfoDialog = false;
      // getClientInfo()
    };

    // #endregion

    // #region 样品信息

    // #endregion

    // #region 费用信息

    // #endregion

    // #region 附件信息

    // #endregion

    // #region 技术评审

    // #endregion

    function submitTask() {
      ElMessageBox({
        title: '提交委托',
        message: '是否提交该委托？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          if (datas.taskInfo.status === 0) {
            submitTaskRegistrationInfo(datas.formInline.id).then(res => {
              if (res !== false) {
                getTaskRegInfo();
                ElMessage.success('提交成功');
                datas.activeNames = ['1', '6'];
              }
            });
          } else {
            const resultParams = {
              devAbility: '',
              envAbility: '',
              staffAbility: '',
              isAssent: '1',
              opinion: ''
            };
            const executeParams = {
              businessKey: datas.taskInfo.id,
              processInstanceId: datas.taskInfo.processInstanceId,
              processParameter: resultParams
            };
            saveTechnicalReviewInfo(executeParams).then(res => {
              if (res !== false) {
                getTaskRegInfo();
                ElMessage.success('提交成功');
                datas.activeNames = ['1', '6'];
              }
            });
          }
        })
        .catch(() => {});
    }

    const isEdit = computed({
      get: () =>
        ((route.query.flag === '2' || route.query.flag === '3') &&
          getPermissionBtn('TaskEditBtn') &&
          (datas.taskInfo.status === 0 || datas.taskInfo.status === 4)) ||
        ((datas.taskInfo.status === 1 || datas.taskInfo.status === 2 || datas.taskInfo.status === 3) &&
          getPermissionBtn('CommissionedEditor'))
    });

    const taskReportInfo = () => {
      let result = '';
      if (!datas.taskInfo.reportCn && !datas.taskInfo.reportEn && !datas.taskInfo.reportBilingual) {
        result = '只需结果，不需报告,';
      } else {
        if (datas.taskInfo.reportCn) {
          result += `中文报告${datas.taskInfo.reportCnNum}份,`;
        }
        if (datas.taskInfo.reportEn) {
          result += `英文报告${datas.taskInfo.reportEnNum}份,`;
        }
        if (datas.taskInfo.reportBilingual) {
          result += `中英文报告${datas.taskInfo.reportBilingualNum}份,`;
        }
      }
      return result ? result.substring(0, result.length - 1) : '--';
    };

    watch(
      () => datas.taskInfo.status,
      newValue => {
        if (newValue === 1 || newValue === 2) {
          datas.activeNames = ['1', '6'];
        }
      },
      { deep: true }
    );

    return {
      ...toRefs(datas),
      isEdit,
      formatterCode,
      taskReportInfo,
      formatDate,
      getNameByid,
      filterStatus,
      filterUserList,
      changeRegisterTime,
      filterType,
      getTaskRegInfo,
      editInfo,
      changeType,
      changeInputWarehouseDate,
      changeUser,
      changeProductionProcedure,
      getSampleInfoList,
      getFeeInfoList,
      getAttachInfoList,
      closeEditInfo,
      getPermissionBtn,
      editClientInfo,
      closeEditClientInfo,
      getReviewList,
      getReviewInfo,
      submitTask
    };
  },
  async created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.panel-header {
  margin-top: 0.5rem;
  height: 1rem;
  .el-button {
    float: right;
  }
}

.tree-icon {
  margin-left: 20px;
  transform: rotateZ(-90deg);
  fill: var(--tesPrimary);
}
</style>
