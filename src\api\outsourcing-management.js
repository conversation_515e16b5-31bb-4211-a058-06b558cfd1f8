import request from '@/utils/request';

export const UPLOAD_ENTRUST_FILE = '/api-orders/orders/experimententrust/updateStatus';

// 委外管理列表
export function getExternalEntrustList(data) {
  return request({
    url: '/api-orders/orders/experimententrust/list',
    method: 'post',
    data
  });
}

// 委外管理信息
export function getExternalInfoByExperimentId(experimentId) {
  return request({
    url: `/api-orders/orders/experimententrust/info/${experimentId}`,
    method: 'get'
  });
}

export function uploadExternalEntrustFile(data, id) {
  return request({
    url: `${UPLOAD_ENTRUST_FILE}/${id}`,
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: function (progressEvent) {
      // 原生获取上传进度的事件
      console.log(progressEvent);
      // callback(progressEvent)
    },
    data
  });
}

// 根据entrustId查询委外登记明细信息
export function queryExperimentEntrust(id) {
  return request({
    url: `/api-orders/orders/experimententrust/findByEntrustId/${id}`,
    method: 'get'
  });
}

// 保存委外项目结果值
export function saveExperimentEntrust(data) {
  return request({
    url: '/api-orders/orders/experimententrust/saveExpValue',
    method: 'post',
    data
  });
}

// 提交委外项目
export function submitExperimentEntrust(data) {
  return request({
    url: '/api-orders/orders/experimententrust/submitExpValue',
    method: 'post',
    data
  });
}

// 委外证书上传
export function uploadExperimentEntrustFile(data, id) {
  return request({
    url: `/api-orders/orders/experimententrust/uploadFile/${id}`,
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data
  });
}

// 委外证书删除
export function deleteExperimentEntrustFile(id) {
  return request({
    url: `/api-orders/orders/experimententrust/deleteFile/${id}`,
    method: 'get'
  });
}
