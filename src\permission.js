import router from './router';
import store from './store';
// import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css'; // progress bar style
import { getToken, getMenuList, getLIMSConfig } from '@/utils/auth'; // get token from cookie
import getPageTitle from '@/utils/get-page-title';
import bus from './bus.js';
import { ElMessageBox, ElMessage } from 'element-plus';
// import { getParamDecryption } from '@/api/platform'
// const { appContext } = getCurrentInstance()
// const bus = appContext.config.globalProperties.bus

NProgress.configure({ showSpinner: false }); // NProgress Configuration

const whiteList = ['/login', '/auth-redirect', '/gotoAuth', '/uniappTemplate']; // no redirect whitelist

const bussinessCodePathList = [
  'samplestorage',
  'sampleinventory',
  'test-allocation',
  'test-report',
  'sample',
  'SampleOrder',
  'TestAllocation',
  'TestReport'
];

router.beforeEach(async (to, from, next) => {
  // console.log(router.getRoutes())
  // start progress bar
  NProgress.start();
  // set page title
  document.title = getPageTitle(to.meta.title);
  var hasToken = getToken();
  var menuList = getMenuList();
  // if (hasToken && menuList.length > 0) {
  // if (hasToken && menuList.length > 0) {
  if (hasToken && menuList) {
    // 添加页面权限判定
    const pagePermission = await store.dispatch('permission/getPagePermission', to);
    if (pagePermission === false) {
      ElMessage.error('您没有权限访问该页面！');
      next({ path: from.path });
      NProgress.done();
      return false;
    }
    if (to.path === '/login' && to?.query?.redirect && to?.query?.redirect.indexOf('mkCode=') > -1) {
      // 八益MK登录
      next();
      NProgress.done();
    } else if (to.path === '/login') {
      next({ path: '/' });
      NProgress.done();
    } else if (to.path === '/gotoAuth') {
      if (to.query?.ciphertext) {
        next();
        NProgress.done();
      } else {
        next({ path: '/' });
        NProgress.done();
      }
    } else {
      // const hasMenus = menuList && menuList.length > 0
      // if (hasMenus) {
      // if (to.name === 'Home' && to.params.token !== '0' && to.params.token !== ':token') {
      //   window.localStorage.clear()
      //   await store.dispatch('user/clearAllStorage')
      //   hasToken = getToken()
      //   menuList = getMenuList()
      //   // window.location.href = window.location.origin + to.path
      //   next(to.path)
      //   NProgress.done()
      //   return false
      // }
      const menus = menuList;
      await store.dispatch('permission/generateRoutes', menus);
      await store.dispatch('user/getMaterialCategoryList');
      await store.dispatch('common/setNameList');
      // 调用租户列表接口
      await store.dispatch('user/getSampleUnit'); // 样品单位列表
      // if (to.name === undefined) {
      //   // next({ path: '/404', name: '404' })
      //   next(to.path)
      //   NProgress.done()
      //   return false
      // }
      if (
        to.name === 'TestItem' ||
        to.name === 'EditReport' ||
        to.name === 'Equipment' ||
        to.name === 'SgTestItem' ||
        to.name === 'OutsourcingManagementEdit'
      ) {
        await store.dispatch('user/getUnit');
      }

      if (bussinessCodePathList.includes(to) || bussinessCodePathList.includes(to.name)) {
        await store.dispatch('common/getBusinessCodeStoreList');
      }

      if (from.name === 'Allocation' && to.name !== 'Allocation') {
        if (bus.reloadAllocation === true) {
          const test = await ElMessageBox({
            title: '提示',
            message: '当前页面数据已更新，是否确认离开？',
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            showCancelButton: true,
            closeOnClickModal: false,
            type: 'info'
          })
            .then(() => {
              // bus.$emit('saveAllocation', true)
              return true;
            })
            .catch(() => {
              return false;
            });
          if (test === false) {
            next(from);
            NProgress.done();
            return false;
          }
        }
      }
      if (from.name === 'collectiondetail') {
        if (bus.startRds === true) {
          const test = await ElMessageBox({
            title: '提示',
            message: '离开此页面会停止实时数采，是否确认离开？',
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            showCancelButton: true,
            closeOnClickModal: false,
            type: 'info'
          })
            .then(() => {
              bus.$emit('stopRds', true);
              return true;
            })
            .catch(() => {
              return false;
            });
          if (test === false) {
            next(from);
            NProgress.done();
            return false;
          }
        }
      }
      next();
      NProgress.done();
      // } else {
      //   next({ path: '/login' })
      //   NProgress.done()
      // }
    }
  } else if (
    (to.name === 'Home' || to.name === 'InspectionApplication') &&
    to.params.token &&
    to.params.token !== '0' &&
    to.params.token !== ':token'
  ) {
    await store.dispatch('user/loginLimsToTes', to.params.token);
    await store.dispatch('common/setNameList');
    await store.dispatch('permission/getcheckPermissionBtn');
    await store.dispatch('permission/getLIMSConfig');
    next(getLIMSConfig().VITE_HOME_PAGE);
    NProgress.done();
  } else if (to.name === 'Register') {
    await store.dispatch('user/loginLimsToTes', to.params.token);
    await store.dispatch('common/setNameList');
    await store.dispatch('permission/getcheckPermissionBtn');
    await store.dispatch('permission/getLIMSConfig');
    next(to.path);
    NProgress.done();
  } else {
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next();
      NProgress.done();
    } else {
      next(`/login?redirect=${to.fullPath}`);
      // next(`/login`)
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  // finish progress bar
  NProgress.done();
});

/* 路由异常错误处理，尝试解析一个异步组件时发生错误，重新渲染目标页面 */
router.onError(error => {
  console.log('路由异常：', error);
  // const pattern = /Loading chunk (\d)+ failed/g
  // const isChunkLoadFailed = error.message.match(pattern)
  // const targetPath = router.history.pending.fullPath
  // if (isChunkLoadFailed) {
  //   router.replace(targetPath)
  // }
});
