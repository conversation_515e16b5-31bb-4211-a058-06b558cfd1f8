<template>
  <!-- 文件清单列表 -->
  <ListLayout
    :has-quick-query="true"
    :has-left-panel="true"
    :aside-panel-width="asidePanelWidth"
    :aside-max-width="520"
  >
    <template #search-bar>
      <div class="searchInput">
        <el-input
          v-model="formInline.condition"
          v-trim
          v-focus
          class="ipt-360"
          placeholder="请输入文件名/标签搜索"
          clearable
          size="large"
          @keyup.enter="onSubmit"
        >
          <template #prefix>
            <i class="el-input__icon el-icon-search" />
          </template>
        </el-input>
        <el-button type="primary" size="large" @click="onSubmit">查询</el-button>
        <el-button size="large" @click="reset">重置</el-button>
      </div>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('UploadFile')"
        class="fr"
        type="primary"
        size="large"
        icon="el-icon-upload"
        @click="uploadFile"
        @keyup.prevent
        @keydown.enter.prevent
        >上传文档</el-button
      >
    </template>
    <template #radio-content>
      <el-checkbox v-model="formInline.isMyFavorite" label="我的收藏" class="myCollect" @change="handlCollect" />
    </template>
    <template #page-left-side>
      <div class="tree-container">
        <div class="tree-header">
          <el-input v-model="filterText" size="small" placeholder="请输入分类名称" prefix-icon="el-icon-search" />
          <el-button
            v-if="asideWidth > 195 && getPermissionBtn('AddFileTree')"
            size="small"
            icon="el-icon-plus"
            class="addTreeBtn"
            @click="addTreeItem"
            @keyup.prevent
            @keydown.enter.prevent
          />
        </div>
        <div class="tree-content">
          <el-tree
            v-if="asideWidth > 80"
            ref="treeRef"
            :data="treeData"
            node-key="id"
            :props="defaultProps"
            :expand-on-click-node="false"
            highlight-current
            draggable
            :filter-node-method="filterNode"
            :current-node-key="currentNodeKey"
            class="leftTree"
            @filter="filterNode"
            @node-click="clickNode"
          >
            <template #default="{ node, data }">
              <span>{{ node.label }}</span>
              <el-dropdown
                v-if="data.id !== 'all'"
                trigger="hover"
                class="tree-dropdown el-icon"
                :class="node.showIcon ? 'icon-show' : ''"
                @visible-change="changeIcon(node.showIcon, node)"
              >
                <i class="el-icon-more" />
                <template
                  v-if="
                    getPermissionBtn('EditFileTree') ||
                    getPermissionBtn('DeleteFileTree') ||
                    getPermissionBtn('PowerFileTree')
                  "
                  #dropdown
                >
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="getPermissionBtn('EditFileTree')" @click="editTree(node.data, node)"
                      ><i class="iconfont tes-edit" />编辑</el-dropdown-item
                    >
                    <el-dropdown-item
                      v-if="getPermissionBtn('DeleteFileTree')"
                      class="color-red"
                      @click="delTree(node.data)"
                      ><i class="iconfont tes-delete" />删除</el-dropdown-item
                    >
                    <el-dropdown-item v-if="getPermissionBtn('PowerFileTree')" @click="permissionTree(node.data)"
                      ><i class="tes-iconfont el-icon-lock" />权限</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-tree>
        </div>
      </div>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="auto"
      highlight-current-row
      class="dark-table format-height-table no-quick-query base-table"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column label="文件名" prop="name" show-overflow-tooltip :min-width="colWidth.fileName">
        <template #default="{ row }">
          <span v-if="!row.hasPermission"><i class="el-icon-lock file-no-permission" />{{ row.fileName || '--' }}</span>
          <span v-else class="file-permission" @dblclick="filePreview(row)">{{ row.fileName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="文件标签" prop="tags" :min-width="colWidth.fileName">
        <template #default="{ row }">
          <div v-if="row.tags">
            <el-tag v-for="tag in row.tags.split(',')" :key="tag + Math.random()" size="small" effect="dark">
              {{ tag }}
            </el-tag>
          </div>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="收藏" prop="tags" :min-width="colWidth.status">
        <template #default="{ row }">
          <i
            class="icon star el-icon-star-off cursorPointer"
            :class="{ 'el-icon-star-on starInit': row.isFavorite === true }"
            @click="handleFavorite(row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" class-name="fixed-right" :min-width="colWidth.operationMultiple">
        <template #default="{ row }">
          <span v-if="filterCaoZuo(row).list[0].show" class="blue-color" @click="filePreview(row)">预览</span>
          <span v-if="filterCaoZuo(row).list[1].show" class="margin-right-10 blue-color" @click="downloadFile(row)"
            >下载</span
          >
          <span
            v-if="filterCaoZuo(row).list[2].show && filterCaoZuo(row).list[2].num <= 3 && filterCaoZuo(row).flag <= 3"
            class="blue-color"
            @click="fileDetail(row)"
            >详情</span
          >
          <span
            v-if="filterCaoZuo(row).list[3].show && filterCaoZuo(row).list[3].num <= 3 && filterCaoZuo(row).flag <= 3"
            class="blue-color"
            @click="fileEdit(row)"
            >编辑</span
          >
          <span
            v-if="filterCaoZuo(row).list[4].show && filterCaoZuo(row).list[4].num <= 3 && filterCaoZuo(row).flag <= 3"
            class="blue-color"
            @click="fileDelete(row)"
            >删除</span
          >
          <el-dropdown v-if="filterCaoZuo(row).flag > 3" trigger="click">
            <span class="el-dropdown-link blue-color">
              <i class="el-icon-more el-icon--right" />
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-if="filterCaoZuo(row).list[2].show && filterCaoZuo(row).list[2].num > 2"
                  @click="fileDetail(row)"
                  ><span class="blue-color">详情</span></el-dropdown-item
                >
                <el-dropdown-item
                  v-if="filterCaoZuo(row).list[3].show && filterCaoZuo(row).list[3].num > 2"
                  @click="fileEdit(row)"
                  ><span class="blue-color">编辑</span></el-dropdown-item
                >
                <el-dropdown-item
                  v-if="filterCaoZuo(row).list[4].show && filterCaoZuo(row).list[4].num > 2"
                  @click="fileDelete(row)"
                  ><span class="blue-color">删除</span></el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getFilesLists"
    />
    <!-- 预览 -->
    <!-- <preview-pdf :drawer="previewPdfDrawer" :file-id="currentSelectFileInfo.fileId" @close="closePreviewPdf" /> -->
    <!-- 上传文档 -->
    <upload-file
      :show="showUploadDialog"
      :is-add="isAddUploadFile"
      :tree="treeData"
      :file-data="fileData"
      @setInfo="setUploadFileInfo"
      @close="closeUploadFile"
    />
    <!-- 添加权限弹出框 -->
    <add-permission
      :show="addPermissionDialog"
      :permission-data="permissionData"
      @close="closeAddPermission"
      @set-info="setAddPermissionInfo"
    />
    <!-- 新增目录树弹出框 -->
    <el-dialog
      v-model="showEditDialog"
      :title="isAddTree === true ? '新增类目' : '编辑类目'"
      width="480px"
      :close-on-click-modal="false"
    >
      <el-form
        v-if="showEditDialog"
        ref="formTree"
        :model="dialogFrom"
        :rules="dialogRules"
        label-position="right"
        label-width="110px"
        size="small"
      >
        <el-form-item label="类目名称：" prop="name" :label-width="formLabelWidth" style="margin-bottom: 20px">
          <el-input
            v-model="dialogFrom.name"
            autocomplete="off"
            maxlength="30"
            :input="(dialogFrom.name = dialogFrom.name.replace(/\s+/g, ''))"
            placeholder="请输入类目名称"
          />
        </el-form-item>
        <el-form-item label="父级分类：" :label-width="formLabelWidth">
          <el-cascader
            v-model="category"
            :options="dialogTreeData"
            :props="categoryProps"
            clearable
            style="width: 100%"
            @change="changeCategory"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeTreeDialog">取 消</el-button>
          <el-button type="primary" @click="editDialogSuccess">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 类目权限弹出框 -->
    <el-dialog
      v-model="showPermissionDialog"
      title="类目权限"
      width="480px"
      custom-class="file-permission-dialog"
      :close-on-click-modal="false"
    >
      <el-form ref="formTree" :model="dialogPermissionFrom" label-width="110px">
        <el-form-item label="类目名称：" prop="categoryName" style="margin-bottom: 20px">
          <div>{{ dialogPermissionFrom.categoryName }}</div>
        </el-form-item>
        <el-form-item label="权限：">
          <el-tag
            v-for="tag in dialogPermissionFrom.permissions"
            :key="tag"
            closable
            size="small"
            type="info"
            :disable-transitions="false"
            style="margin-right: 5px"
            @close="handleClose(tag)"
          >
            {{ tag.permissionGroupName || tag.permissionGroupId }}
          </el-tag>
        </el-form-item>
        <el-form-item>
          <el-button size="small" icon="el-icon-plus" @click="showInput">添加权限</el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closePermissionDialog">取 消</el-button>
          <el-button type="primary" @click="permissionDialogSuccess">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </ListLayout>
</template>

<script>
import { reactive, toRefs, ref, watch, onMounted, getCurrentInstance } from 'vue';
import { getPermissionBtn } from '@/utils/common';
import Pagination from '@/components/Pagination';
import { formatTree, formatAllTree, formatTreeByIds, formatTreeByNames } from '@/utils/formatJson';
import {
  getFileListTree,
  getFileList,
  editFileTree,
  fileFavorite,
  cancleFileFavorite,
  addFileTree,
  deleteFileTree,
  canDelete,
  getFilePermission,
  editFilePermission,
  downloadFileList,
  detailFileList,
  deleteFile
} from '@/api/file-list';
import { ElMessage, ElMessageBox } from 'element-plus';
// import DrageHandle from '@/components/DragHandle/handle.vue'
import router from '@/router/index.js';
// import { useStore } from 'vuex'
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import UploadFile from './components/UploadFile';
import AddPermission from './components/AddPermission';
import _ from 'lodash';
// import PreviewPdf from '@/components/PreviewPdf'
import ListLayout from '@/components/ListLayout';
import { formatUploadFileTree } from '@/utils/formatJson';

export default {
  name: 'FileList',
  components: { Pagination, UploadFile, AddPermission, ListLayout },
  setup(props, context) {
    // const _ = inject('_')
    // 添加监控reloadList
    const { proxy } = getCurrentInstance();
    // const store = useStore()
    const otherForm = reactive({
      asidePanelWidth: 300,
      collect: false,
      tableList: [],
      list: [],
      content: '',
      treeRef: ref(),
      treeData: [],
      dialogTreeData: [],
      editData: {},
      newTree: [],
      treeTitle: '', // 选中树节点的name
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      category: [],
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      filterText: '',
      isAddTree: true,
      dialogRules: {
        name: [{ required: true, message: '请输入类目名称' }],
        parentId: [{ required: true, message: '请选择父级目录' }]
      },
      asideWidth: 240,
      showIcon: false,
      currentNodeKey: '',
      currentCategoryids: [],
      detaildrawer: false,
      formInline: {
        condition: '',
        categoryId: ''
      },
      addTitle: '新增项目',
      total: 0,
      listLoading: false,
      tableKey: '0',
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: false
      },
      dialogFrom: {
        name: '',
        parentId: ''
      },
      showEditDialog: false,
      formTree: ref(null),
      showUploadDialog: false,
      fileData: {},
      showPermissionDialog: false,
      dialogPermissionFrom: {
        categoryName: '',
        permissions: []
      },
      addPermissionDialog: false,
      permissionData: null,
      isAddUploadFile: 'add',
      currentSelectFileInfo: {},
      previewPdfDrawer: false
    });

    // 重置
    function reset() {
      otherForm.listQuery.page = 1;
      otherForm.listQuery.limit = 20;
      otherForm.listQuery.orderBy = '';
      otherForm.listQuery.isAsc = false;
      otherForm.formInline.condition = '';
      proxy.getFilesLists();
    }
    // 排序
    const sortChange = data => {
      const { prop, order } = data;
      otherForm.listQuery.orderBy = prop;
      if (order === 'ascending') {
        otherForm.listQuery.isAsc = true;
      } else if (order === 'descending') {
        otherForm.listQuery.isAsc = false;
      } else {
        otherForm.listQuery.isAsc = null;
      }
    };
    // 树节点编辑
    const formLabelWidth = ref('120px');
    const editTree = (data, node) => {
      otherForm.dialogTreeData = formatAllTree(data.id, otherForm.dialogTreeData);
      otherForm.showEditDialog = true;
      otherForm.isAddTree = false;
      otherForm.dialogFrom = data;
      otherForm.category = formatTreeByIds(node.parent);
    };
    // 保存树节点
    const editDialogSuccess = () => {
      otherForm.formTree.validate(valid => {
        if (valid) {
          if (otherForm.isAddTree !== true) {
            editFileTree(otherForm.dialogFrom).then(function (res) {
              if (res !== false && res.data.code === 200) {
                ElMessage.success('编辑成功!');
                otherForm.showEditDialog = false;
              }
              proxy.getFileListTrees();
            });
          } else {
            addFileTree(otherForm.dialogFrom).then(function (res) {
              if (res !== false && res.data.code === 200) {
                ElMessage.success('新增成功!');
                proxy.getFileListTrees();
                otherForm.showEditDialog = false;
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    // 关闭树的新增编辑的弹出框
    const closeTreeDialog = () => {
      otherForm.showEditDialog = false;
      if (otherForm.formTree) {
        otherForm.formTree.clearValidate();
      }
      proxy.getFileListTrees();
    };
    // 新增树节点
    const addTreeItem = () => {
      otherForm.showEditDialog = true;
      otherForm.dialogFrom = {
        name: '',
        parentId: otherForm.treeRef.getCurrentNode()?.id || ''
      };
      if (otherForm.formTree) {
        otherForm.formTree.clearValidate();
      }
      if (otherForm.treeRef.getCurrentNode()?.id && otherForm.treeRef.getCurrentNode()?.id !== '0') {
        otherForm.category = formatUploadFileTree(otherForm.treeData, otherForm.treeRef.getCurrentNode()?.id);
      } else {
        otherForm.category = [];
      }
      otherForm.isAddTree = true;
    };
    // 所属分类change
    const changeCategory = value => {
      if (value) {
        const len = value.length - 1;
        otherForm.dialogFrom.parentId = value[len];
      } else {
        otherForm.dialogFrom.parentId = 0;
      }
    };
    // 筛选收藏
    const handlCollect = () => {
      proxy.getFilesLists();
    };
    // 树节点删除
    const delTree = node => {
      // var id = []
      // id.push(node.id)
      ElMessageBox({
        title: '提示',
        message: '是否删除该类目?',
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(() => {
          canDelete({ id: node.id }).then(function (r) {
            if (r !== false) {
              if (r.data.data) {
                deleteFileTree({ id: node.id }).then(function (res) {
                  if (res !== false) {
                    ElMessage.success('删除成功!');
                    proxy.getFileListTrees();
                  }
                });
              } else {
                ElMessage.error('该目录下文件不为空, 无法删除！');
              }
            }
          });
        })
        .catch(() => {
          // ElMessage.info('已取消删除!')
        });
    };
    // 点击目录树的权限
    const permissionTree = node => {
      otherForm.showPermissionDialog = true;
      getFilePermission({ id: node.id }).then(res => {
        if (res !== false) {
          const { data } = res.data;
          otherForm.permissionData = data.permissions ? data.permissions : [];
          otherForm.dialogPermissionFrom.permissions = data.permissions;
          otherForm.dialogPermissionFrom.categoryId = node.id;
          otherForm.dialogPermissionFrom.categoryName = node.name;
        }
      });
    };
    // 类目权限弹出框-关闭tag
    const handleClose = tag => {
      _.remove(otherForm.dialogPermissionFrom.permissions, function (n) {
        return n.permissionGroupId === tag.permissionGroupId;
      });
      _.remove(otherForm.permissionData, function (n) {
        return n.permissionGroupId === tag.permissionGroupId;
      });
    };
    // 类目权限弹出框-点击添加权限
    const showInput = () => {
      otherForm.addPermissionDialog = true;
    };
    // 类目权限弹出框-确定
    const permissionDialogSuccess = () => {
      const param = {
        id: otherForm.dialogPermissionFrom.categoryId,
        permissions: otherForm.dialogPermissionFrom.permissions
      };
      editFilePermission(param).then(res => {
        if (res !== false) {
          otherForm.showPermissionDialog = false;
          ElMessage.success('权限配置成功');
        }
      });
    };
    // 类目权限弹出框-取消
    const closePermissionDialog = () => {
      otherForm.showPermissionDialog = false;
    };
    // 添加权限弹出框-取消
    const closeAddPermission = value => {
      otherForm.addPermissionDialog = value;
    };
    // 添加权限弹出框-确定
    const setAddPermissionInfo = data => {
      otherForm.dialogPermissionFrom.permissions = data;
      otherForm.permissionData = data;
      otherForm.addPermissionDialog = false;
    };
    // 上传文档
    const uploadFile = () => {
      if (otherForm.treeData.length === 0) {
        ElMessage.warning('请选择项目树');
        return false;
      }
      otherForm.showUploadDialog = true;
      otherForm.isAddUploadFile = 'add';
      otherForm.fileData = {
        categoryId: otherForm.formInline.categoryId
      };
    };
    // 关闭上传文档弹出框
    const closeUploadFile = value => {
      otherForm.showUploadDialog = value;
    };
    // 上传文件弹出框-确定
    const setUploadFileInfo = info => {
      // 刷新文件列表
      if (info === 'delete') {
        fileDelete(otherForm.currentSelectFileInfo, 1);
      } else if (info === 'preview') {
        filePreview(otherForm.currentSelectFileInfo, 2);
      } else if (info === 'download') {
        downloadFile(otherForm.currentSelectFileInfo, 3);
      } else if (info === 'update') {
        // fileDelete(otherForm.currentSelectFileInfo, 4)
      } else {
        otherForm.showUploadDialog = false;
      }
    };
    // mounted
    onMounted(() => {});
    // 过滤树节点
    watch(
      () => otherForm.filterText,
      newValue => {
        otherForm.treeRef.filter(newValue);
      }
    );
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    // 拖拽边框
    const widthChange = m => {
      otherForm.asideWidth -= m;
      if (otherForm.asideWidth <= 80) {
        otherForm.asideWidth = 80;
      }
      if (otherForm.asideWidth >= 600) {
        otherForm.asideWidth = 600;
      }
    };

    const changeIcon = (command, node) => {
      if (!command) {
        node.showIcon = true;
      } else {
        node.showIcon = !node.showIcon;
      }
    };
    // 预览
    const filePreview = (row, flag) => {
      // otherForm.currentSelectFileInfo = row
      // otherForm.previewPdfDrawer = true
      const newRouter = router.resolve({
        path: '/preview-file',
        query: {
          fileId: row.fileId
        }
      });
      window.open(newRouter.href, '_blank');
    };
    // 关闭预览
    const closePreviewPdf = value => {
      otherForm.previewPdfDrawer = false;
    };
    // 下载
    const downloadFile = (row, flag) => {
      downloadFileList(row.fileId).then(res => {
        if (res !== false) {
          const blob = res.data;
          var fileName = res.headers.filename; // formatDateTime() + '.doc'
          var downloadElement = document.createElement('a');
          var href = window.URL.createObjectURL(blob);
          downloadElement.style.display = 'none';
          downloadElement.href = href;
          downloadElement.download = decodeURI(fileName);
          document.body.appendChild(downloadElement);
          downloadElement.click();
          document.body.removeChild(downloadElement);
          window.URL.revokeObjectURL(href);
          if (flag === 3) {
            otherForm.showUploadDialog = false;
          }
        }
      });
    };
    // 详情
    const fileDetail = row => {
      otherForm.currentSelectFileInfo = row;
      detailFileList({ id: row.fileId }).then(res => {
        if (res !== false) {
          otherForm.fileData = res.data.data;
          otherForm.showUploadDialog = true;
          otherForm.isAddUploadFile = 'detial';
        }
      });
    };
    // 编辑
    const fileEdit = row => {
      otherForm.currentSelectFileInfo = row;
      detailFileList({ id: row.fileId }).then(res => {
        if (res !== false) {
          otherForm.fileData = res.data.data;
          otherForm.showUploadDialog = true;
          otherForm.isAddUploadFile = 'edit';
        }
      });
    };
    // 删除
    const fileDelete = (row, flag) => {
      ElMessageBox({
        title: '提示',
        message: '是否删除该文件?',
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(() => {
          deleteFile({ id: row.fileId }).then(res => {
            if (res !== false) {
              ElMessage.success('删除成功');
              if (flag === 1) {
                otherForm.showUploadDialog = false;
              }
              proxy.getFilesLists();
            }
          });
        })
        .catch(() => {
          // ElMessage.info('已取消删除!')
        });
    };
    // 操作过滤
    const filterCaoZuo = row => {
      var list = [];
      var flag = 0;
      // 预览 0
      if (row.hasPermission && getPermissionBtn('PreviewFile')) {
        list.push({ show: true, num: 1 });
        flag += 1;
      } else {
        list.push({ show: false });
      }
      // 下载 1
      if (row.hasPermission) {
        list.push({ show: true, num: flag + 1 });
        flag += 1;
      } else {
        list.push({ show: false });
      }
      // 详情 2
      if (row.hasPermission && getPermissionBtn('DetailFile')) {
        list.push({ show: true, num: flag + 1 });
        flag += 1;
      } else {
        list.push({ show: false });
      }
      // 编辑 3
      if (row.hasPermission && getPermissionBtn('EditFile')) {
        list.push({ show: true, num: flag + 1 });
        flag += 1;
      } else {
        list.push({ show: false });
      }
      // 删除 4
      if (row.hasPermission && getPermissionBtn('DeleteFile')) {
        list.push({ show: true, num: flag + 1 });
        flag += 1;
      } else {
        list.push({ show: false });
      }
      return { list: list, flag: flag };
    };
    const handleFavorite = row => {
      if (row.isFavorite) {
        // 取消收藏
        otherForm.listLoading = true;
        cancleFileFavorite(row.fileId).then(res => {
          otherForm.listLoading = false;
          if (res) {
            proxy.$message.success('取消收藏成功!');
            proxy.getFilesLists();
          }
        });
      } else {
        // 添加收藏
        otherForm.listLoading = true;
        fileFavorite(row.fileId).then(res => {
          otherForm.listLoading = false;
          if (res) {
            proxy.$message.success('添加收藏成功!');
            proxy.getFilesLists();
          }
        });
      }
    };

    return {
      ...toRefs(otherForm),
      handleFavorite,
      handlCollect,
      getPermissionBtn,
      closeTreeDialog,
      drageHeader,
      changeIcon,
      handleClose,
      showInput,
      widthChange,
      changeCategory,
      addTreeItem,
      filterNode,
      editDialogSuccess,
      permissionTree,
      closeUploadFile,
      setUploadFileInfo,
      formLabelWidth,
      delTree,
      editTree,
      uploadFile,
      sortChange,
      reset,
      filePreview,
      downloadFile,
      fileDetail,
      fileEdit,
      fileDelete,
      permissionDialogSuccess,
      closePermissionDialog,
      closeAddPermission,
      setAddPermissionInfo,
      closePreviewPdf,
      filterCaoZuo,
      colWidth
    };
  },
  async created() {
    await this.getFileListTrees();
    this.getFilesLists();
    // 刷新列表
    this.bus.$on('reloadFileList', msg => {
      this.getFilesLists();
    });
  },
  methods: {
    getFilesLists(pdata) {
      const _this = this;
      _this.listLoading = true;
      if (pdata && pdata !== undefined) {
        _this.listQuery.page = pdata.page;
        _this.listQuery.limit = pdata.limit;
      }
      const param = Object.assign(_this.formInline, _this.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      getFileList(param).then(response => {
        if (response !== false) {
          const data = response.data.data;
          _this.list = data.list;
          _this.total = data.totalCount;
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
    },
    getFileListTrees() {
      const _this = this;
      return new Promise(resolve => {
        getFileListTree({ type: 0 }).then(response1 => {
          if (response1 !== false) {
            const data = response1.data.data;
            _this.treeData = formatTree(data);
            _this.newTree = formatTree(data);
            _this.dialogTreeData = data;
            if (_this.treeData.length === 0) {
              _this.listLoading = false;
              _this.treeTitle = '';
              _this.list = [];
              _this.total = 0;
            } else {
              // 在_this.treeData树列表第一个部分添加‘全部’的根目录
              const allParam = {
                id: '0',
                name: '全部',
                categoryId: ''
              };
              _this.treeData.unshift(allParam);
              if (_this.formInline.categoryId === '' && _this.treeData[0].id === 'all') {
                _this.formInline.categoryId = '';
                _this.treeTitle = _this.treeData[0].name;
              }
              // 默认选中树节点
              if (_this.currentNodeKey === '') {
                _this.currentNodeKey = _this.treeData[0].id;
                _this.currentCategoryids = [_this.treeData[0].id];
                _this.treeTitle = _this.treeData[0].name;
                _this.$nextTick(function () {
                  _this.$refs.treeRef.setCurrentKey(_this.currentNodeKey);
                });
              } else {
                _this.$nextTick(function () {
                  _this.$refs.treeRef.setCurrentKey(_this.currentNodeKey);
                });
              }
            }
            resolve(true);
          } else {
            resolve(false);
          }
        });
      });
    },
    onSubmit() {
      this.getFilesLists();
    },
    clickNode(data, node) {
      if (data.id === 'all') {
        this.formInline.categoryId = '';
        this.currentNodeKey = data.id;
      } else {
        this.formInline.categoryId = data.id;
        this.currentNodeKey = data.id;
      }
      this.treeTitle = formatTreeByNames(node)
        .filter(item => {
          return item;
        })
        .join('/');
      // this.formInline.categoryId = data.id
      // this.currentNodeKey = data.id
      this.currentCategoryids = formatTreeByIds(node);
      this.getFilesLists();
    }
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
.star {
  font-size: 22px;
}
.cursorPointer {
  cursor: pointer;
}
.starInit {
  color: $yellow;
}
.searchInput {
  display: flex;
  .el-input {
    width: 360px;
    margin-right: 10px;
  }
}

:deep(.radio-content) {
  text-align: right;
}
:deep(.el-form--inline .el-form-item) {
  margin-right: 0;
}
.margin-right-10 {
  margin-right: 10px !important;
}
.myCollect {
  margin-left: 0 !important;
  align-self: flex-end;
}
.tree-container {
  .tree-content {
    height: calc(100vh - 235px);
  }
}
:deep(.el-table.format-height-table) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 310px);
  }
}
</style>
<style lang="scss">
.file-no-permission {
  color: $tes-yellow;
  margin-right: 10px;
}
.file-permission {
  padding-left: 24px;
  cursor: pointer;
}
</style>
