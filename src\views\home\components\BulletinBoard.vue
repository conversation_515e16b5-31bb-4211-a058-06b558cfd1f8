<template>
  <!-- 公告栏 -->
  <div class="BulletinBoards">
    <div class="top-agency">
      <div class="title">公告栏</div>
    </div>
    <div class="bulletinContents">
      <div v-if="topNotice.id || bulletinBoardList.length > 0">
        <div v-if="topNotice.id" class="item">
          <img class="topStandard" src="@/assets/img/topStandard.png" alt="" />
          <div class="name-icon itemLine">{{ returnAbbreviation(userJson[topNotice.publisherId]) }}</div>
          <div class="itemLine">
            <div class="flex items-center">
              <span style="font-weight: bold">{{ topNotice.publisherName }}</span> 发布了
              <span class="title" @click="handleDetail(topNotice)">{{ topNotice.title }}</span>
            </div>
            <div>{{ topNotice.publishTime }}</div>
          </div>
        </div>
        <div ref="scrollRef" class="scrollList" @mouseenter="monseenter" @mouseleave="mouseleave" @mouseenter.prevent>
          <div v-for="item in bulletinBoardList" :key="item.id" class="item">
            <div class="name-icon itemLine">{{ returnAbbreviation(userJson[item.publisherId]) }}</div>
            <div class="itemLine">
              <div class="flex items-center">
                <span style="font-weight: bold">{{ item.publisherName }}</span> 发布了
                <span class="title" @click="handleDetail(item)">{{ item.title }}</span>
              </div>
              <div>{{ item.publishTime }}</div>
            </div>
          </div>
        </div>
      </div>
      <img v-else class="marginAuto" src="@/assets/img/empty-data.png" alt="no-data" />
    </div>
    <DrawerNotice :drawer="drawVisiable" :detail-info="detailData" :type="drawerType" @close="closeDrawer" />
  </div>
</template>

<script>
import { reactive, toRefs, ref, nextTick } from 'vue';
import DrawerNotice from '../../comprehensive-management/announcement-management/DrawerNotice.vue';
import { makePy } from '@/utils/chinese-to-str';
import { getHomeList } from '@/api/announcement';
import store from '@/store';

export default {
  name: 'BulletinBoard',
  components: { DrawerNotice },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    const state = reactive({
      topNotice: {},
      scrollRef: ref(),
      drawerType: 'check',
      detailData: {},
      timer: null,
      drawVisiable: false,
      userJson: {},
      bulletinBoardList: []
    });
    const formatterUser = () => {
      store.state.common.nameList.forEach(item => {
        state.userJson[item.id] = item.username;
      });
    };
    formatterUser();
    const returnAbbreviation = name => {
      if (name) {
        return makePy(name.charAt(0))[0].toUpperCase();
      }
    };
    const getList = () => {
      getHomeList().then(res => {
        if (res) {
          state.bulletinBoardList = res.data.data.noticeList;
          state.topNotice = res.data.data.topNotice;
          if (state.bulletinBoardList.length > 0) {
            nextTick(() => {
              scrollEvent();
            });
          }
        }
      });
    };
    getList();
    const handleDetail = detail => {
      state.drawVisiable = true;
      state.detailData = detail;
      setTimeout(() => {
        clearInterval(state.timer);
        state.timer = null;
      }, 1);
    };
    const closeDrawer = value => {
      state.drawVisiable = false;
      setTimeout(() => {
        scrollEvent();
      });
    };
    const monseenter = () => {
      clearInterval(state.timer);
      state.timer = null;
    };
    const mouseleave = () => {
      scrollEvent();
    };
    // 滚动条轮播事件
    const scrollEvent = () => {
      const elementWrapper = state.scrollRef;
      if (state.timer === null) {
        state.timer = setInterval(() => {
          elementWrapper.scrollTop++;
          if (elementWrapper.scrollTop + elementWrapper.clientHeight === elementWrapper.scrollHeight) {
            elementWrapper.scrollTop = 0;
          }
        }, 60);
      }
    };
    return {
      ...toRefs(state),
      getList,
      formatterUser,
      monseenter,
      mouseleave,
      scrollEvent,
      handleDetail,
      closeDrawer,
      returnAbbreviation
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss">
.marginAuto {
  display: block;
  margin: 0 auto;
}
.bulletinContents {
  background-color: $background-color;
  text-align: left;
  padding: 0 20px 10px 20px;
  margin-bottom: 15px;
  .topStandard {
    width: 38px;
    position: absolute;
    left: -22px;
    top: -1px;
  }
  .item {
    display: flex;
    flex-direction: row;
    position: relative;
    padding: 5px 0;
    border-bottom: 1px solid #ebeef5;
  }
  .itemLine {
    // line-height: 20px;
    font-size: 12px;
    width: calc(100% - 32px);
    div {
      line-height: 17px;
    }
  }
  .title {
    max-width: 60%;
    color: $tes-primary;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    line-height: 20px;
    display: inline-block;
    cursor: pointer;
  }
  .name-icon {
    height: 24px;
    width: 24px;
    align-self: center;
    line-height: 24px;
    text-align: center;
    display: inline-block;
    background: $tes-primary;
    border-radius: 50%;
    color: #fff;
    margin-right: 8px;
  }
  .scrollList {
    max-height: 192px;
    overflow-y: auto;
  }
  .scrollList::-webkit-scrollbar {
    width: 0 !important;
  }
}
</style>
