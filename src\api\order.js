// 修改检测项目模板信息
import request from '@/utils/request';

// orders查询列表
export function samplesList(data) {
  return request({
    url: '/api-orders/orders/samples/list',
    method: 'post',
    data
  });
}
// 检测报告样品查询列表
export function sampleReportList(data) {
  return request({
    url: '/api-orders/orders/samples/sampleReportList',
    method: 'post',
    data
  });
}
// 保存或修改样品下达信息
export function samplesOrder(data) {
  return request({
    url: '/api-orders/orders/samples/samples',
    method: 'post',
    data
  });
}
// 根据Id查询详细信息
export function samplesInfo(id) {
  return request({
    url: '/api-orders/orders/samples/info/' + id,
    method: 'get'
  });
}

// 根据Id查询详细信息
export function samplesDetails(data) {
  return request({
    url: '/api-orders/orders/samples/info',
    method: 'post',
    data
  });
}

// 查询样品下的所有的检测项目
export function capabilityBysampleId(sampleId) {
  return request({
    url: '/api-orders/orders/experiment/experiment/capability/' + sampleId,
    method: 'get'
  });
}

// 样品详情-入库信息
export function warehousingList(data) {
  return request({
    url: '/api-orders/orders/warehousing/warehousingList',
    method: 'post',
    data
  });
}
// 样品详情-不良品信息
export function findBySampleIdblp(sampleId) {
  return request({
    url: '/api-defectiveproduct/defectiveproduct/defectiveproduct/findBySampleId/' + sampleId,
    method: 'get'
  });
}
// 样品详情-检测报告
export function findBySampleId(sampleId) {
  return request({
    url: '/api-orders/orders/report/findBySampleId/' + sampleId,
    method: 'get'
  });
}
// 样品详情tab页检查
export function checkTabMenu(data) {
  return request({
    url: '/api-user/user/oauthinformation/checkTabMenu',
    method: 'post',
    data
  });
}

/**
 * 获取指定关键参数的历史数据
 * @param {{
 * 	"capabilityId": "", "capabilityParaId": "", "capabilityParaName": "", "endDate": "", "mateType": "", "materialGroupNo": "", "productionProcedureNo": "", "startDate": "", "templateKey": "", "type": 1, "voltName": ""
 * }} data
 * @returns
 * 请求类型 application/json
 */
export function getKeyParamData(data) {
  return request({
    url: '/api-orders/orders/samples/findSPCData',
    method: 'post',
    data
  });
}

/**
 * 获取合格率信息
 * @param {{
 * 	 "reportDateEnd": "", "reportDateStart": "", "type": 1, "materialGroupCode": "", "procedureCode": ""
 * }} data
 * @returns
 * 请求类型 application/json
 */
export function getQualifiedRate(data) {
  return request({
    url: '/api-orders/orders/rate/qualifiedRate',
    method: 'post',
    data
  });
}

/**
 * 合格率导出
 * @param {{
 * 	 "reportDateEnd": "", "reportDateStart": "", "type": 1, "materialGroupCode": "", "procedureCode": ""
 * }} data
 * @returns
 * 请求类型 application/json
 */
export function exportQualifiedRate(data) {
  return request({
    url: '/api-orders/orders/rate/qualified/export',
    method: 'post',
    data
  });
}

/**
 * 获取工作统计信息
 * @param {{
 * 	 "startDate": "", "endDate": "", "type": 1, "materialGroupId": "", "productionProcedureNo": ""
 * }} data
 * @returns
 * 请求类型 application/json
 */
export function getWorkStatistics(data) {
  return request({
    url: '/api-orders/orders/workload/query',
    method: 'post',
    data
  });
}

/**
 * 获取工作统计需要导出的数据信息
 * @param {{
 * 	 "startDate": "", "endDate": "", "type": 1, "materialGroupId": "", "productionProcedureNo": ""
 * }} data
 * @returns
 * 请求类型 application/json
 */
export function getWorkStatisticsExcel(data) {
  return request({
    url: '/api-orders/orders/workload/export',
    method: 'post',
    data
  });
}

/**
 * 快速分配列表
 * @param {{
 *  "limit": "1", "page": "10", "key": ""
 * }} data
 * @returns
 */
export function getQuickAllocationList(data) {
  return request({
    url: `/api-orders/orders/distribution/quickList`,
    method: 'post',
    data
  });
}

/**
 * 快速分配检测项目
 * @param { string } sampleId
 * @returns
 */
export function getQuickAllocationInspectionItem(sampleId) {
  return request({
    url: `/api-orders/orders/distribution/quickFindCapability/${sampleId}`,
    method: 'get'
  });
}

/**
 * 检测分配-检测结果的批量判定
 * 样品检测结果（0、合格1、不合格）
 * http://192.168.69.72:8800/doc.html#/orders/%E6%A3%80%E6%B5%8B%E5%88%86%E9%85%8DApi/decideUsingPOST
 * @param {{
 * "reportType": 0, "sampleIdList": []
 * }} data
 * @returns
 */
export function saveBatchJudgement(data) {
  return request({
    url: `/api-orders/orders/distribution/decide`,
    method: 'post',
    data
  });
}

/**
 * 检测分配-样品的批量完成
 * http://192.168.69.72:8800/doc.html#/orders/%E6%A3%80%E6%B5%8B%E5%88%86%E9%85%8DApi/sampleBatchFinishUsingPOST
 * @param {[
 * {
 *   "sampleId": 0,
 *   "secSampleNum": "",
 *   "status": 0
 * }
 * ]} data
 * @returns
 */
export function saveBatchFinish(data) {
  return request({
    url: `/api-orders/orders/distribution/sampleBatchFinish`,
    method: 'post',
    data
  });
}

// 不合格项目下转表格
export function nonconformingList(data) {
  return request({
    url: '/api-orders/orders/rate/qualified/list',
    method: 'post',
    data
  });
}
// 样品归档
export function samplesArchivalApi(data) {
  return request({
    url: '/api-orders/orders/samples/archival',
    method: 'post',
    data
  });
}

// 样品归档
export function archivalExperimentFileUpload(data) {
  return request({
    url: '/api-orders/orders/samples/archivalExperimentFileUpload',
    method: 'post',
    responseType: 'blob',
    data
  });
}

// 全文检索
export function archivalQuery(data) {
  return request({
    url: '/api-orders/orders/samples/archivalQuery',
    method: 'post',
    data
  });
}
// 修改检测信息
export function saveOrUpdate(data) {
  return request({
    url: '/api-orders/orders/certificateprint/saveOrUpdate',
    method: 'post',
    data
  });
}
// 保存样品和检测标准关联关系表信息
export function samplestandardSave(data) {
  return request({
    url: '/api-orders/ordres/samplestandard/save',
    method: 'post',
    data
  });
}
// 查找样品关联的检测标准
export function findBySampleIdStandard(sampleId) {
  return request({
    url: `/api-orders/ordres/samplestandard/findBySampleId/${sampleId}`,
    method: 'get'
  });
}
