import request from '@/utils/request';

/**
 * 通过视图id来获取视图
 * @param {*} id
 * @returns
 */
export function getViewByViewId(id) {
  return request({
    url: `/api-user/user/sysemployeelistview/getInfoById/${id}`,
    method: 'get'
  });
}

/**
 * 通过用户id来获取创建的视图
 * @param {*} userId
 * @returns
 */
export function getViewByUserId(userId) {
  return request({
    url: `/api-user/user/sysemployeelistview/findInfoByUserId/${userId}`,
    method: 'get'
  });
}

export function saveOrUpdateView(data) {
  return request({
    url: `/api-user/user/sysemployeelistview/saveOrUpdate`,
    method: 'post',
    data
  });
}

/**
 * 重置用户视图
 * @param {*} id
 * @returns
 */
export function resetViewById(id) {
  return request({
    url: `/api-user/user/sysemployeelistview/reset/${id}`,
    method: 'get'
  });
}

/**
 * 删除用户视图
 * @param {*} id
 * @returns
 */
export function deleteViewById(id) {
  return request({
    url: `/api-user/user/sysemployeelistview/delete/${id}`,
    method: 'delete'
  });
}
