import request from '@/utils/request';
import qs from 'qs';
import { getGWURL, getERPURL } from '@/utils/base-url';

const postHeaders = {
  'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
};
const Headers = {
  'Content-Type': 'application/json; charset=utf-8'
};

var gwUrl = getGWURL;
var erpUrl = getERPURL();

// export function login(data) {
//   data.grant_type = 'password'
//   data.scope = 'lims_api tes_api workflow attachment accessory device material report capability experiment document capabilitystd contracts cnastask mirror number company offline_access user'
//   data.client_id = 'TES_WEB'
//   data = qs.stringify(data)
//   return request({
//     url: gwUrl + '/ids/connect/token',
//     method: 'post',
//     headers: postHeaders,
//     data
//   })
// }
export function login(data) {
  // data.scope = 'lims_api tes_api workflow attachment accessory device material report capability experiment document capabilitystd contracts cnastask mirror number company offline_access user'
  // data.client_id = 'TES_WEB'
  if (data.tenant_id) {
    postHeaders.tenant_id = data.tenant_id;
  }
  data = qs.stringify(data);
  return request({
    url: '/api-uaa/oauth/token',
    method: 'post',
    headers: postHeaders,
    data
  });
}

// 个人信息
export function getUserInfo() {
  return request({
    url: '/api-user/user/sysuser/users/current',
    method: 'get',
    headers: postHeaders
  });
}

// 菜单权限
export function getSysMenu() {
  return request({
    url: '/api-user/user/sysmenu/current',
    method: 'get',
    headers: postHeaders
  });
}
// -------------------------------------------
export function loginAgain(data) {
  data.grant_type = 'refresh_token';
  // data.refresh_token=
  data.client_id = 'TES_WEB';
  data = qs.stringify(data);
  return request({
    url: gwUrl + '/ids/connect/token',
    method: 'post',
    headers: postHeaders,
    data
  });
}

export function getMenu() {
  return request({
    url: gwUrl + '/user/Permission',
    method: 'get',
    headers: Headers
  });
}
// 仪器设备分类
export function getGuoWangDictionary() {
  const data = {
    limit: 10000,
    dictionaryId: '0012'
  };
  return request({
    url: gwUrl + '/dictionary/DictionaryOption',
    method: 'get',
    headers: Headers,
    params: data
  });
}
// 根据设备分类查询国网列表
export function getGuoWangByMaterialCategory(materialCategoryIds) {
  return request({
    url: gwUrl + '/capability/ExternalCapability/MaterialCategory',
    method: 'get',
    headers: Headers,
    params: materialCategoryIds
  });
}

// 入库信息
export function GetInventorySampleBySampleId(sampleid) {
  return request({
    url: gwUrl + '/lims/v1.0/InventorySample/GetInventorySampleBySampleId',
    method: 'post',
    headers: Headers,
    data: sampleid
  });
}
// 根据检测项目id查询关键参数配置列表 例：http://192.168.69.58:9100/capability/ExternalCapabilityPara?limit=50&capabilityIds=590242470940381184
export function getExternalCapabilityPara(capabilityIds) {
  const params = {
    limit: 500,
    capabilityIds: capabilityIds
  };
  return request({
    url: gwUrl + '/capability/ExternalCapability/MaterialCategory',
    method: 'get',
    headers: Headers,
    params: params
  });
}
// 人员加载
// export function getName(data) {
//   return request({
//     url: gwUrl + '/user/Account',
//     method: 'get',
//     headers: Headers,
//     params: data
//   })
// }
export function getName(data) {
  if (!data) {
    data = {
      limit: '-1',
      page: '1',
      condition: ''
    };
  }
  return request({
    url: '/api-user/user/sysuser/users/all',
    method: 'post',
    data
  });
}
// 样品单位  http://192.168.69.58:9100/dictionary/DictionaryOption?limit=100&dictionaryId=5
export function getSampleUnitDict() {
  const data = {
    limit: 10000,
    dictionaryId: '5'
  };
  return request({
    url: gwUrl + '/dictionary/DictionaryOption',
    method: 'get',
    headers: Headers,
    params: data
  });
}

export function logout() {
  return request({
    url: '/api-uaa/oauth/remove/token',
    method: 'post'
  });
}
// 首页
/*
 * */
export function getHome(data) {
  return request({
    url: '/cxist/index/home',
    method: 'get',
    params: { data }
  });
}

// 线芯颜色字典
export function getColor() {
  return request({
    url: gwUrl + '/dictionary/DictionaryOption?limit=10000&dictionaryId=0014',
    method: 'get'
  });
}

// 获取所有租户列表 http://192.168.69.72:9100/user/Tenant/Access?limit=10
export function getTenantAccess() {
  const param = {
    limit: 1000
  };
  return request({
    url: gwUrl + '/user/Tenant/Access',
    method: 'get',
    params: param
  });
}
// 获取签名
// http://192.168.69.58:9100/Attachment/Attachment/Preview/642672602090835968

// export function getAttachment(list) {
//   console.log(list)
//   return request({
//     url: `/api-user/user/sysuser/signature`,
//     method: 'post',
//     // headers: Headers,
//     list
//   })
// }
export function getAttachment(data) {
  return request({
    url: '/api-user/user/sysuser/signature',
    method: 'post',
    data
  });
}
// export function getAttachment(id) {
//   return request({
//     url: '/api-experiment/experiment/template/getUrl',
//     method: 'post',
//     data: list
//   })
// }
// 检测项目-项目参数单位(数值型)
export function getItemParaUnitDict() {
  const data = {
    limit: 10000,
    dictionaryId: '13'
  };
  return request({
    url: gwUrl + '/dictionary/DictionaryOption',
    method: 'get',
    headers: Headers,
    params: data
  });
}
// 修改密码 http://192.168.69.58:9100/user/Account/changePassword
// const data = {
//   id: "",
//   newPassword: "123qwER",
//   originalPassword: "123qweR",
//   rePassword: "123qwER"
// }
export function changePWD(data) {
  return request({
    url: '/api-user/user/sysuser/client/password',
    method: 'post',
    headers: Headers,
    data
  });
}
// 获取ERP数据
export function getERP(data) {
  return request({
    url: `${erpUrl}/api/tai-shan/sync`,
    method: 'post',
    headers: Headers,
    data
  });
}
// 同步数据至LIMS
export function getSyncToLims() {
  return request({
    url: `${erpUrl}/api/tai-shan/sync-to-lims`,
    method: 'post',
    headers: Headers
  });
}
// lims 获取部门列表
export function getDepartment() {
  const data = {
    limit: 10000
  };
  return request({
    url: gwUrl + '/user/Department',
    method: 'get',
    headers: Headers,
    params: data
  });
}
// 根据部门id查询员工需要调用下面3个接口，三步走
// 1、先通过departmentId查询到employeeId的列表;
// 2、然后根据employeeId拼成逗号隔开的ids，查询到accountId的列表;
// 3、最后根据accountId拼成逗号隔开的ids，查询员工列表;
// http://192.168.69.58:9100/user/DepartmentEmployee?limit=10&departmentId=339058918420918269&name=&email=&cellPhone=
export function getDepartmentEmployee(departmentId) {
  const data = {
    limit: 10000,
    departmentId: departmentId,
    name: '',
    email: '',
    cellPhone: ''
  };
  return request({
    url: gwUrl + '/user/DepartmentEmployee',
    method: 'get',
    headers: Headers,
    params: data
  });
}
// http://192.168.69.58:9100/user/Employee/Ids/637323315823775744
export function getEmployeeByIds(employeeIds) {
  return request({
    url: gwUrl + '/user/Employee/Ids/' + employeeIds,
    method: 'get',
    headers: Headers
  });
}
// http://192.168.69.58:9100/user/Account/Ids/637323212086054912
export function getAccountByIds(accountIds) {
  return request({
    url: gwUrl + '/user/Account/Ids/' + accountIds,
    method: 'get',
    headers: Headers
  });
}
// 获取角色列表 http://192.168.69.58:9100/user/Role?limit=10&sortExpression=CreateDateTime%20Desc
export function getRole() {
  const data = {
    limit: 10000,
    sortExpression: 'CreateDateTime Desc'
  };
  return request({
    url: gwUrl + '/user/Role',
    method: 'get',
    headers: Headers,
    params: data
  });
}
// 获取员工列表需要调用2个接口来折腾
// 1、获取accountId的列表；http://192.168.69.58:9100/user/Employee?limit=10&accountLogin=&accountName=&accountCellPhone=
// 2、根据accountId拼成逗号隔开的ids，查询到具体员工列表；接口在上面getAccountByIds； http://192.168.69.58:9100/user/Account/Ids/637323212086054912,592745775046332416
export function getEmployee() {
  const data = {
    limit: 10000,
    accountLogin: '',
    accountName: '',
    accountCellPhone: ''
  };
  return request({
    url: gwUrl + '/user/Employee',
    method: 'get',
    headers: Headers,
    params: data
  });
}
