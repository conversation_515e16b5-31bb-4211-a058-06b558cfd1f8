<template>
  <div class="quick-allocation">
    <el-dialog
      v-model="dialogShow"
      :title="dialogTitle"
      :close-on-click-modal="false"
      width="1000px"
      custom-class="quick-allocation-dialog"
      @close="cancel"
    >
      <div class="searchInput">
        <el-input
          v-model="key"
          v-trim
          v-focus
          placeholder="请输样品编号/样品名称/型号规格进行搜索"
          clearable
          size="small"
          @keyup.enter="searchTable"
        >
          <template #prefix>
            <i class="el-input__icon el-icon-search" />
          </template>
        </el-input>
        <el-button type="primary" size="small" @click="searchTable">查询</el-button>
      </div>
      <el-table
        ref="singleTable"
        v-loading="singleLoading"
        :data="tableData"
        size="medium"
        highlight-current-row
        border
        style="width: 100%"
        class="dark-table base-table add-process-table"
        @current-change="changeRadio"
      >
        <el-table-column type="index" label="选择" :width="colWidth.checkbox">
          <template #default="{ row }">
            <el-radio v-model="row.radio" :label="row.samplesId" @change="changeRadio(row)">{{ '' }}</el-radio>
          </template>
        </el-table-column>
        <el-table-column label="样品编号" property="secSampleNum" :width="colWidth.orderNo">
          <template #default="{ row }">
            {{ row.secSampleNum || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="样品名称" property="sampleName" :min-width="colWidth.name">
          <template #default="{ row }">
            {{ row.sampleName || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="型号规格" property="prodType" :min-width="colWidth.model">
          <template #default="{ row }">
            {{ row.prodType || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="时间周期" property="cycleTime" :width="colWidth.cycle">
          <template #default="{ row }"> {{ row.cycleTime || '--' }} 天 </template>
        </el-table-column>
        <el-table-column label="历史试验时间" property="finishDate" :width="colWidth.dates">
          <template #default="{ row }">
            {{ row.startDate || '--' }} ~ {{ row.finishDate || row.finishedDate || '--' }}
          </template>
        </el-table-column>
      </el-table>
      <pagination :page="page" :limit="limit" :total="total" @pagination="changePage" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="onSubmit" @keyup.prevent @keydown.enter.prevent>确 认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch, getCurrentInstance } from 'vue';
import { getQuickAllocationList } from '@/api/order';
import Pagination from '@/components/Pagination';
import { colWidth } from '@/data/tableStyle';
export default {
  name: 'QuickAllocation',
  components: { Pagination },
  props: {
    dialogVisiable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['closeDialog', 'selectRow'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();

    const state = reactive({
      dialogShow: false,
      key: '',
      dialogTitle: '快速分配',
      limit: 20,
      page: 1,
      total: 0,
      singleLoading: false,
      singleTable: ref(),
      selectedRow: {}, // 选中的行数据
      tableData: []
    });
    // 获取物料分组列表接口
    const getTableList = params => {
      state.singleLoading = true;
      getQuickAllocationList(params).then(res => {
        if (res.data.code === 200) {
          state.singleLoading = false;
          state.tableData = res.data.data.list;
          state.tableData.forEach(item => {
            item.radio = false;
          });
          state.total = res.data.data.totalCount;
        }
      });
    };
    const changePage = value => {
      state.limit = value.limit;
      state.page = value.page;
      var params = {
        key: state.key,
        limit: state.limit.toString(),
        page: state.page.toString()
      };
      getTableList(params);
    };
    const changeRadio = row => {
      if (row && row.samplesId) {
        state.selectedRow = row;
        state.tableData.forEach(item => {
          if (item.samplesId !== row.samplesId) {
            item.radio = false;
          } else {
            item.radio = item.samplesId;
          }
        });
      }
    };
    const searchTable = () => {
      var params = {
        limit: state.limit.toString(),
        key: state.key,
        page: '1'
      };
      getTableList(params);
    };
    const onSubmit = () => {
      if (state.selectedRow.samplesId) {
        state.dialogShow = false;
        context.emit('selectRow', state.selectedRow);
      } else {
        proxy.$message.warning('请选择样品！');
      }
    };
    const cancel = () => {
      state.dialogShow = false;
      context.emit('closeDialog', false);
    };

    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisiable;
      if (state.dialogShow) {
        state.key = '';
        state.page = 1;
        state.limit = 20;
        state.selectedRow = {};
        // state.total = 0
        getTableList({ limit: state.limit.toString(), page: '1', key: '' });
      }
    });

    return {
      ...toRefs(state),
      onSubmit,
      getTableList,
      changePage,
      searchTable,
      changeRadio,
      cancel,
      colWidth
    };
  }
};
</script>

<style lang="scss" scoped>
.searchInput {
  width: 100%;
  margin-bottom: 15px;
  display: inline-block;
  .el-input {
    margin-right: 8px;
    width: 360px;
  }
}
</style>
<style lang="scss">
.quick-allocation-dialog {
  .add-process-table {
    .el-table thead th {
      background-color: #f6f6f6;
    }

    .el-table__body-wrapper {
      max-height: calc(100vh - 34.5rem);
      overflow-y: auto;
      overflow-x: hidden !important;
    }
  }
  .el-table thead th {
    background: #f6f6f6;
  }
  .el-dialog__body {
    padding-bottom: 0;
  }
}
</style>
