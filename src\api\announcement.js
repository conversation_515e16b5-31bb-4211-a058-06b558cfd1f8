import request from '@/utils/request';

// 公告列表
export function getList(data) {
  return request({
    url: '/api-message/message/notice/list',
    method: 'post',
    data
  });
}

// 首页公告列表
export function getHomeList(data) {
  return request({
    url: '/api-message/message/notice/list',
    method: 'get',
    data
  });
}

// 发布公告
export function releaseNotice(data) {
  return request({
    url: '/api-message/message/notice/publish',
    method: 'post',
    data
  });
}

// 保存公告
export function saveNotice(data) {
  return request({
    url: '/api-message/message/notice/save',
    method: 'post',
    data
  });
}

// 下架公告
export function takeOffNotice(id) {
  return request({
    url: `/api-message/message/notice/abrogate/${id}`,
    method: 'get'
  });
}

// 删除公告
export function deleteNoticeApi(data) {
  return request({
    url: `/api-message/message/notice/delete`,
    method: 'post',
    data
  });
}
