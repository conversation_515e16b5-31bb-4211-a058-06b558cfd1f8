import { isArray, isObject } from 'lodash';

const XMLHeader =
  '<?xml version="1.0" encoding="UTF-8"?><dcc:reportData xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="https://ptb.de/dcc https://ptb.de/dcc/v3.2.1/dcc.xsd" xmlns:dcc="https://ptb.de/dcc" xmlns:si="https://ptb.de/si" schemaVersion="3.2.1">';
const XMLFooter = '</dcc:reportData>';

export function toXML(obj) {
  let xml = '';
  for (const key in obj) {
    const value = obj[key];
    if (isArray(value)) {
      value.forEach(item => {
        xml += `<dcc:${key}>`;
        if (isObject(item)) {
          xml += toXML(item);
        } else {
          xml += item;
        }
        xml += `</dcc:${key}>`;
      });
    } else if (isObject(value)) {
      xml += `<dcc:${key}>${toXML(value)}</dcc:${key}>`;
    } else {
      xml += `<dcc:${key}>${value}</dcc:${key}>`;
    }
  }
  return xml;
}

export function toDCCXML(obj) {
  let xml = XMLHeader;
  xml += toXML(obj);
  return xml + XMLFooter;
}

// 通过FileReader转化为base64字符串下载
export function downloadByBlob(fileName, content) {
  const blob = new Blob([content], {
    type: 'text/plain;charset=utf-8'
  });
  const reader = new FileReader();
  reader.readAsDataURL(blob);
  reader.onload = function (e) {
    const a = document.createElement('a');
    a.download = fileName;
    a.href = e.target.result;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };
}
