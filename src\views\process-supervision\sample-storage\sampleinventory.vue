<template>
  <!-- 实验室管理-样品库存 -->
  <ListLayout :has-button-group="getPermissionBtn('inventoryPrints')">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="param">
          <el-input
            v-model="formInline.param"
            v-focus
            v-trim
            placeholder="请输入编号/样品名称/型号规格"
            class="ipt-360"
            size="large"
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter="onSubmit"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="onSubmit">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
          <el-button class="searchBtn" type="text" size="large" @click="search" @keyup.prevent @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']" />
          </el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('inventoryPrints')"
        type="primary"
        :disabled="selected.length === 0"
        size="large"
        icon="el-icon-printer"
        @click="handlePrint"
        @keyup.prevent
        @keydown.enter.prevent
        >二维码打印</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" :model="searchForm" label-width="110px" label-position="right">
            <el-form-item label="登记日期：" prop="DateRange">
              <el-date-picker
                v-model="searchForm.DateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="changeTimeRange"
              />
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="16" class="flex gap-6">
          <el-radio-group v-model="radioData" size="small" @change="changeRadio">
            <el-radio-button label="全部" />
            <el-radio-button label="检测中" />
            <el-radio-button label="可处理" />
            <el-radio-button label="已处理" />
          </el-radio-group>
          <el-select v-model="formInline.type" placeholder="请选择检验类型" size="small" clearable @change="changeType">
            <el-option v-for="(val, key) in dictionary['JYLX'].enable" :key="key" :label="val" :value="key" />
          </el-select>
        </el-col>
        <el-col :span="8" style="text-align: right">
          <TableColumnView binding-menu="sampleinventory" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table format-height-table base-table"
      @header-dragend="drageHeader"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
      @sort-change="sortChange"
    >
      <el-table-column
        v-if="getPermissionBtn('inventoryPrints')"
        type="selection"
        fixed="left"
        prop="checkbox"
        :width="colWidth.checkbox"
        align="center"
      />
      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          v-if="
            source === 1 ? true : !/^(sampleQuantity|inputWarehouseQuantity,inputWarehouseUnit)$/.test(item.fieldKey)
          "
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="item.fieldType === fieldTypesEnum.Link">
              <span
                v-if="item.fieldKey === 'presentationCode' && row[item.fieldKey]"
                v-copy="row[item.fieldKey]"
                class="nowrap blue-color"
                @click.stop="jumpApplicationDetail(row)"
              >
                {{ row[item.fieldKey] || '--' }}
              </span>
              <span
                v-else-if="item.fieldKey === 'secSampleNum' && row[item.fieldKey]"
                v-copy="row[item.fieldKey]"
                class="blue-color"
                @click.stop="handleDetail(row)"
              >
                {{ row[item.fieldKey] || '--' }}
              </span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Person">
              <UserTag :name="getNameByid(row.createdPersonId) || row.createdPersonId || '--'" />
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Status">
              <template v-if="item.fieldKey === 'inventoryStatus'">
                <span v-if="row[item.fieldKey] === 0">检测中</span>
                <span v-else>已处理</span>
              </template>
              <span v-else-if="item.fieldKey === 'isLoaned'">{{ row.isLoaned ? '是' : '否' }}</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Date">
              <span>{{ formatDate(row.registerTime) || '--' }}</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Custom">
              <template v-if="item.fieldKey === 'sampleNum' || item.fieldKey === 'sampleQuantity'">
                <span v-if="row[item.fieldKey]"
                  >{{ row[item.fieldKey] }}{{ filterSampleUnitToName(row.sampleUnit) || row.sampleUnit }}</span
                >
                <span v-else>--</span>
              </template>
              <template v-else-if="item.fieldKey === 'type'">
                <span>{{ dictionary['JYLX'].all[row[item.fieldKey]] || '--' }}</span>
              </template>
              <div v-else-if="item.fieldKey === 'inputWarehouseQuantity,inputWarehouseUnit'">
                <span>{{ row.type === 1 ? row.inputWarehouseQuantity || '--' : row.productionQuantity || '--' }}</span>
                <span>{{
                  row.type === 1
                    ? filterSampleUnitToName(row.inputWarehouseUnit) || row.inputWarehouseUnit
                    : filterSampleUnitToName(row.productionUnit) || row.productionUnit
                }}</span>
              </div>
            </template>
            <span v-else>{{ row[item.fieldKey] || '--' }}</span>
          </template>
        </el-table-column>
      </template>
      <el-table-column
        label="操作"
        prop="caozuo"
        fixed="right"
        :width="colWidth.operationMultiples"
        class-name="fixed-right"
      >
        <template #default="scope">
          <span v-if="getPermissionBtn('detailSampleinventoryBtn')" class="blue-color" @click.stop="getInfo(scope.row)"
            >查看</span
          >
          <span
            v-if="scope.row.inventoryStatus === 0 && getPermissionBtn('returnStorage')"
            class="blue-color"
            @click.stop="handleRetureStorage(scope.row)"
            >回库</span
          >
          <span
            v-if="scope.row.inventoryStatus === 2 && getPermissionBtn('handleSampleinventoryBtn')"
            class="blue-color"
            @click.stop="handleStorage(scope.row, 2)"
            >处理</span
          >
          <span
            v-if="scope.row.inventoryStatus === 0 && !scope.row.isLoaned && getPermissionBtn('lendBtn')"
            class="blue-color"
            @click.stop="handleLoanStorage(scope.row)"
            >借出</span
          >
          <span
            v-if="scope.row.inventoryStatus === 0 && !scope.row.isLoaned && getPermissionBtn('addSampleinventoryBtn')"
            class="blue-color"
            @click.stop="handleStorage(scope.row, 1, 'add')"
            >补样</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
    <template #other>
      <!--入库弹屏-->
      <module-storage
        :module-title="moduleTitle"
        :visible="ModuleStorageVisible"
        :lists="lists"
        :isadd="isAdd"
        @close="close"
        @setinfo="setInfo"
      />
      <module-handle :visible="ModuleHandleVisible" :lists="lists" @close="close" />
      <module-storage-detail
        :visible="ModuleDetailVisible"
        :dictionary="dictionary['5'].enable"
        :detail="dialogDetail"
        @close="closeDetail"
      />
      <!-- 回库dialog -->
      <module-return-storage :visible="ModuleReturnVisible" :detail="dialogDetail" @close="closeReturn" />
      <!-- 借出dialog -->
      <module-lend :visible="ModuleLendVisible" :status="inventoryStatus" :detail="dialogDetail" @close="closeLend" />
      <!-- 打印弹出框 -->
      <el-dialog v-model="dialogCode" title="打印设置" :close-on-click-modal="false" width="400px" top="50px">
        <el-row>
          <el-col :span="24">
            <el-select v-model="printerName" placeholder="请选择打印机" size="small" clearable style="width: 100%">
              <el-option v-for="item in qrSprintList" :key="item.name" :label="item.name" :value="item.name">
                <span style="float: left">{{ item.name }}</span>
                <span style="float: right; color: #909399; font-size: 13px">{{ item.description }}</span>
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogCode = false">取 消</el-button>
            <el-button type="primary" @click="onSubmitPrint">打 印</el-button>
          </span>
        </template>
      </el-dialog>
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance, computed } from 'vue';
import router from '@/router/index.js';
import Pagination from '@/components/Pagination';
import ModuleStorage from './components/ModuleStorage';
import ModuleStorageDetail from './components/ModuleStorageDetail';
import ModuleHandle from './components/ModuleHandle';
import ModuleReturnStorage from './components/ModuleReturnStorage';
import ModuleLend from './components/ModuleLend';
import UserTag from '@/components/UserTag';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import ListLayout from '@/components/ListLayout';
import { formatDate } from '@/utils/formatTime';
import { mapGetters, useStore } from 'vuex';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import _ from 'lodash';
import { checkPermissionList } from '@/api/permission';
// import { permissionTypeList } from '@/utils/permissionList'
import { warehousingList, getPringList, printCode } from '@/api/samplestorage';
import { filterSampleUnitToName } from '@/utils/formatJson';
import { colWidth } from '@/data/tableStyle';
import store from '@/store';
import { getInspectionList } from '@/api/inspection-application';
import { getTaskRegistrationList } from '@/api/task-registration';
import { getDictionary } from '@/api/user';
import TableColumnView from '@/components/TableColumnView';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';

export default {
  name: 'SampleInventoryDetail',
  components: {
    Pagination,
    ModuleStorage,
    ModuleStorageDetail,
    ModuleHandle,
    ListLayout,
    UserTag,
    ModuleReturnStorage,
    ModuleLend,
    TableColumnView
  },
  setup() {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const editFrom = ref(null);
    const otherForm = reactive({
      moduleTitle: '样品入库',
      source: 0,
      selected: [],
      isAdd: false,
      isUpdate: false,
      inventoryStatus: 0,
      dialogDetail: {}, // 回库,借出弹出框详情信息
      listsDetails: [],
      dictionary: {
        JYLX: {
          enable: {},
          all: {}
        },
        5: {
          enable: {},
          all: {}
        }
      },
      accountId: getLoginInfo().accountId,
      multipleSelection: [],
      ModuleHandleVisible: false,
      ModuleReturnVisible: false, // 回库
      ModuleLendVisible: false, // 借出
      codeLoading: false,
      dialogCode: false,
      printerName: '',
      qrSprintList: [],
      qrCodeList: [],
      ModuleDetailVisible: false,
      tableRef: ref(),
      activeName: '0',
      ModuleStorageVisible: false,
      showS: false,
      mangeList: [],
      lists: [],
      formInline: {
        param: '',
        warehousingStatus: '1',
        endRegisterDateTime: '',
        startRegisterDateTime: ''
      },
      searchForm: {
        DateRange: ''
      },
      types: store.user.materialList,
      userOptions: store.common.nameList,
      copyUserOptions: store.common.nameList,
      tableColumns: [],
      list: [],
      content: '',
      radioData: '全部',
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        sort: 'DESC'
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ]
    });

    function onSubmit() {
      proxy.getList();
    }
    const getInfo = row => {
      otherForm.dialogDetail = row;
      otherForm.ModuleDetailVisible = true;
    };
    const handleSelectionChange = val => {
      otherForm.selected = val;
    };
    function reset() {
      editFrom.value.resetFields();
      otherForm.formInline = {
        param: '',
        warehousingStatus: '1',
        endRegisterDateTime: '',
        startRegisterDateTime: ''
      };
      otherForm.radioData = '全部';
      otherForm.searchForm = {
        ownerId: '',
        DateRange: ''
      };
      otherForm.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        sort: 'DESC'
      };
      proxy.getList();
    }

    const search = () => {
      otherForm.showS = !otherForm.showS;
      if (otherForm.activeName === '0') {
        otherForm.activeName = '1';
      } else {
        otherForm.activeName = '0';
      }
    };

    const sortChange = data => {
      const { prop, order } = data;
      otherForm.listQuery.orderBy = prop;
      if (order === 'ascending') {
        otherForm.listQuery.isAsc = true;
      } else if (order === 'descending') {
        otherForm.listQuery.isAsc = false;
      } else {
        otherForm.listQuery.isAsc = null;
      }
    };
    // 高级搜索
    const changeTimeRange = date => {
      if (date) {
        otherForm.formInline.startRegisterDateTime = formatDate(date[0]);
        otherForm.formInline.endRegisterDateTime = formatDate(date[1]);
      } else {
        otherForm.formInline.startRegisterDateTime = '';
        otherForm.formInline.endRegisterDateTime = '';
      }
    };
    // 点击样品编号跳转到样品详情页面
    const handleSampleOrdersDetail = row => {
      router.push({
        path: '/experiment/detail',
        query: {
          orderId: row.orderId,
          sampleId: row.sampleId
        }
      });
    };

    const inputValue = data => {
      if (typeof data === 'string') {
        otherForm.content = data;
      } else {
        otherForm.content = '';
      }
    };
    const getDictionaryList = () => {
      Object.keys(otherForm.dictionary).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          otherForm.dictionary[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              otherForm.dictionary[item].enable[optionItem.code] = optionItem.name;
            }
            otherForm.dictionary[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    getDictionaryList();
    const changeRadio = value => {
      if (value === '检测中') {
        otherForm.formInline.inventoryStatus = '0';
      } else if (value === '可处理') {
        otherForm.formInline.inventoryStatus = '2';
      } else if (value === '已处理') {
        otherForm.formInline.inventoryStatus = '1';
      } else {
        otherForm.formInline.inventoryStatus = '';
      }
      proxy.getList();
    };
    const changeType = () => {
      proxy.getList();
    };
    // 跳转样品详情
    const handleDetail = row => {
      router.push({
        path: '/sampleInventoryDetail/detail',
        query: {
          orderId: row.orderId,
          sampleId: row.sampleId
        }
      });
    };
    // 二维码打印
    const handlePrint = () => {
      otherForm.dialogCode = true;
      otherForm.codeLoading = true;
      getPringList().then(res => {
        otherForm.codeLoading = false;
        if (res) {
          otherForm.qrSprintList = res.data.data;
          otherForm.printerName = otherForm.qrSprintList[0].name;
        }
      });
    };
    // 打印
    const onSubmitPrint = () => {
      if (!otherForm.printerName) {
        proxy.$message.error('请先选择打印机');
        return false;
      }
      const params = {
        printerName: otherForm.printerName,
        samplePrintParamList: []
      };
      otherForm.selected.forEach(item => {
        params.samplePrintParamList.push({
          sampleId: item.sampleId,
          internalId: item.id
        });
      });
      otherForm.codeLoading = true;
      printCode(params).then(res => {
        otherForm.codeLoading = false;
        if (res) {
          if (res.data.data) {
            otherForm.selected = [];
            proxy.$message.success('打印成功');
            otherForm.dialogCode = false;
            proxy.getList();
          }
        }
      });
    };
    const handleRowClick = row => {
      if (
        otherForm.selected.some(item => {
          return item.sampleId === row.sampleId;
        })
      ) {
        otherForm.selected = otherForm.selected.filter(item => {
          return item.sampleId !== row.sampleId;
        });
      } else {
        otherForm.selected.push(row);
      }
      otherForm.tableRef.toggleRowSelection(row);
    };
    // 过滤试验员
    const filterUserList = val => {
      if (val) {
        const list = [];
        otherForm.copyUserOptions.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        otherForm.userOptions = list;
      } else {
        otherForm.userOptions = otherForm.copyUserOptions;
      }
    };
    const handleEdit = item => {
      handleStorage(item, 3);
    };
    // 分配入库回库
    const handleStorage = (rows, type, isadd) => {
      var itt = JSON.parse(JSON.stringify(rows));
      otherForm.lists = [];
      otherForm.lists.push(itt);
      otherForm.isAdd = false;
      if (type === 1) {
        otherForm.ModuleStorageVisible = true;
        otherForm.moduleTitle = '样品补样';
        otherForm.isUpdate = false;
        if (isadd === 'add') {
          otherForm.isAdd = true;
        }
      }
      if (type === 2) {
        otherForm.ModuleHandleVisible = true;
      }
      if (type === 3) {
        otherForm.ModuleStorageVisible = true;
        otherForm.isUpdate = true;
        otherForm.moduleTitle = '样品入库';
        if (otherForm.listsDetails.length > 1) {
          otherForm.isAdd = true;
          otherForm.moduleTitle = '样品补样';
        }
      }
    };
    // 回库
    const handleRetureStorage = row => {
      otherForm.ModuleReturnVisible = true;
      otherForm.dialogDetail = row;
    };
    // 借出
    const handleLoanStorage = row => {
      otherForm.dialogDetail = row;
      otherForm.ModuleLendVisible = true;
    };
    const close = type => {
      if (type) {
        proxy.getList();
      }
      otherForm.ModuleStorageVisible = false;
      otherForm.ModuleHandleVisible = false;
      otherForm.lists = [];
    };
    const closeDetail = () => {
      otherForm.ModuleDetailVisible = false;
    };
    const closeReturn = val => {
      otherForm.ModuleReturnVisible = false;
      if (val) {
        proxy.getList();
      }
    };
    const closeLend = val => {
      otherForm.ModuleLendVisible = false;
      if (val) {
        proxy.getList();
      }
    };
    const setInfo = info => {
      const num = _.findIndex(otherForm.listsDetails, function (o) {
        return o.id === info[0].id;
      });
      otherForm.listsDetails[num] = info[0];
      otherForm.listsDetails[num].sampleTemplateFileEntity = info[0].fileinfo;
    };

    // 查看申请详情
    const jumpApplicationDetail = row => {
      if (row.type === 10 || row.type === 11 || row.type === 12) {
        getTaskRegistrationList({
          condition: `${row.presentationCode}`
        }).then(res => {
          if (res && res.status === 200) {
            if (res.data.data.list.length > 0) {
              router.push({
                name: 'SampleInventoryRegistration',
                query: { id: res.data.data.list.find(item => item.entrustNo === row.presentationCode)?.id, flag: 1 }
              });
            }
          }
        });
      } else {
        getInspectionList({ param: `${row.presentationCode}` }).then(res => {
          if (res && res.status === 200) {
            if (res.data.data.list.length > 0) {
              router.push({
                name: 'SampleInventoryApplication',
                query: { id: res.data.data.list[0].id, flag: 1 }
              });
            }
          }
        });
      }
    };

    const onUpdateColumns = columns => {
      otherForm.tableKey = otherForm.tableKey + 1;
      otherForm.tableColumns =
        otherForm.source === 1
          ? columns
          : columns.filter(
              column => !/^(sampleQuantity|inputWarehouseQuantity,inputWarehouseUnit)$/.test(column.fieldKey)
            );
    };

    const tenantType = computed({
      get: () => store.user.tenantInfo.type
    });

    return {
      handlePrint,
      handleDetail,
      handleRetureStorage,
      handleLoanStorage,
      handleRowClick,
      filterUserList,
      handleSelectionChange,
      getPermissionBtn,
      drageHeader,
      setInfo,
      closeDetail,
      closeReturn,
      closeLend,
      changeTimeRange,
      handleStorage,
      handleSampleOrdersDetail,
      formatDate,
      filterSampleUnitToName,
      getNameByid,
      changeRadio,
      changeType,
      inputValue,
      handleEdit,
      close,
      getInfo,
      sortChange,
      editFrom,
      ...toRefs(otherForm),
      search,
      onSubmit,
      onSubmitPrint,
      reset,
      colWidth,
      tenantType,
      jumpApplicationDetail,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  },
  computed: {
    ...mapGetters(['tenantGroup'])
  },
  created() {
    this.getList();
    this.getnamelist();
    // 刷新列表
    this.bus.$on('reloadTestAllocationList', msg => {
      this.getList();
    });
    this.copyUserOptions = JSON.parse(JSON.stringify(this.userOptions));
  },
  methods: {
    getList(data) {
      const _this = this;
      _this.listLoading = true;
      if (data && data !== undefined) {
        _this.listQuery.page = data.page;
        _this.listQuery.limit = data.limit;
      }
      _this.formInline.param = _this.formInline.param.trim();
      const param = Object.assign(_this.formInline, _this.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      warehousingList(param).then(async res => {
        await store.dispatch('common/getProcessModeList');
        if (res !== false && res.data.code === 200) {
          const { data } = res.data;
          _this.list = data.list;
          if (data.list[0]) {
            _this.source = data.list[0].source;
          }
          _this.total = data.totalCount;
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
    },
    getnamelist() {
      checkPermissionList('allocation').then(res => {
        this.mangeList = res.data.data;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.blue-color {
  color: $tes-primary;
  cursor: pointer;
}
</style>
