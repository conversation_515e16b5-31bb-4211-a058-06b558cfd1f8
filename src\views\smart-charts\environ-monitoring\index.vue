<template>
  <!-- 环境监测 -->
  <ListLayout :has-custom-header="true" :has-custom-main="true" :main-offset-top="topHeight">
    <template #page-custom-header>
      <SingleLineHeader>
        <template #right-button-group>
          <el-button
            key="sample"
            type="text"
            size="small"
            @click="handleRenderSampleData()"
            @keyup.prevent
            @keydown.enter.prevent
          >
            <span class="el-icon-data-analysis" />
          </el-button>
          <el-button
            v-if="getPermissionBtn('AddEquipmentEnvior')"
            type="primary"
            size="small"
            @click="handleAddEquipment()"
            @keyup.prevent
            @keydown.enter.prevent
            >添加设备</el-button
          >
        </template>
      </SingleLineHeader>
      <AddMultipleEquipment
        :show="dialogEquipment"
        :select-info="deviceList"
        @select-data="handleSelectData"
        @close="handleCloseDialog"
      />
    </template>

    <template #page-custom-main>
      <el-row>
        <el-col v-for="item in deviceList" :key="item.deviceId" :span="12">
          <CustomPanel
            :has-margin-top="true"
            :has-margin-right="true"
            :has-margin-bottom="true"
            :has-margin-left="true"
          >
            <template #panel-title>
              <div class="flex justify-between items-center">
                <div class="title">
                  <span>{{ item.deviceName }}</span>
                </div>
                <div>
                  <el-icon :size="16" class="mr-3 icon" @click="handleDownLoad(item)">
                    <i class="el-icon-download" />
                  </el-icon>
                  <el-tag :type="echartsJSON[item.deviceId] ? 'success' : 'info'">{{
                    echartsJSON[item.deviceId] ? '正常运行' : '设备离线'
                  }}</el-tag>
                </div>
              </div>
              <div class="flex justify-between items-center">
                <span class="title-address">{{ item.deviceLocation || '--' }}</span>
              </div>
              <div class="flex justify-around items-center">
                <span class="value">{{ echartsJSON[item.deviceId]?.pointNameValueMap?.temperature || '--' }} ℃</span>
                <span class="value">{{ echartsJSON[item.deviceId]?.pointNameValueMap?.humidness || '--' }} %</span>
              </div>
              <div class="flex justify-around items-center">
                <span class="highlight"><SvgIcon icon-class="temperature" :width="18" :height="18" />温度</span>
                <span class="highlight"><SvgIcon icon-class="humidness" :width="18" :height="18" />湿度</span>
              </div>
            </template>
            <template #panel-content>
              <div v-if="historyRecord[item.deviceId]">
                <v-chart :option="optionAll[item.deviceId]" :width="'100%'" :height="'30vh'" />
              </div>
              <el-empty v-else :image="emptyImg" style="height: 30vh" description="暂无图表" />
              <div v-if="!isDemons" class="flex justify-between items-center time">
                <el-icon :size="16" class="icon" @click="handleDelete(item.id)">
                  <i class="el-icon-delete" />
                </el-icon>
                <span class="time"> 更新时间：{{ echartsJSON[item.deviceId]?.acquisitionTime || '--' }} </span>
              </div>
            </template>
          </CustomPanel>
        </el-col>
      </el-row>
      <DialogDownLoad :dialog-show="dialogLoad" :detail-info="selectDialog" @closeDialog="closeDialog" />
    </template>
  </ListLayout>
</template>

<script>
// Basic
import { onMounted, reactive, toRefs, onUnmounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
// Utils
import { formatDate, formatCalendar, formatDateTime } from '@/utils/formatTime';
// import LineBarPieChart from '@/components/LineBarPieChart';
import ListLayout from '@/components/ListLayout';
// formatDateTime
import SingleLineHeader from '@/components/PageComponents/SingleLineHeader';
import CustomPanel from '@/components/PageComponents/CustomPanel';
// import { formatterTips } from '../func/formatter';
import { getNameByid, calculator, getPermissionBtn } from '@/utils/common';
import emptyImg from '@/assets/img/empty-chart.png';
import emptyData from '@/assets/img/empty-data.png';
// api
import { saveOrUpdate, deleteDevice, environmentalList, getRdsData } from '@/api/environ-monitoring';
// data
import { deviceAllList, echartsData } from './presentation-data';

// SvgIcon
import SvgIcon from '@/components/SvgIcon';

// components
import AddMultipleEquipment from '@/components/BusinessComponents/AddMultipleEquipment';
import DialogDownLoad from './components/dialog-down-load.vue';
import VChart from '@/components/VChart';

export default {
  name: 'EnvironMonitoring',
  components: {
    ListLayout,
    SingleLineHeader,
    CustomPanel,
    DialogDownLoad,
    VChart,
    SvgIcon,
    AddMultipleEquipment
  },
  setup(props, context) {
    // const { proxy } = getCurrentInstance();
    const state = reactive({
      tableData: [],
      dialogLoad: false,
      selectDialog: {},
      isDemons: false, // 是否是演示数据
      deviceList: [], // 选中的仪器设备
      echartsJSON: {},
      xAxisArray: [], // x轴数据
      historyRecord: {}, // 历史记录
      dialogEquipment: false,
      timer: null,
      topHeight: 90,
      loading: false,
      optionAll: {}
    });
    const optionJSON = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999'
          }
        },
        formatter: params => {
          var text = `<div>${params[0].name}</div>`;
          params.forEach(item => {
            if (item.value !== 0) {
              text += `<div class="echartsFormatter">${item.marker}<span class="label">${item.seriesName}</span>：<span class="value">${item.value}项</span></div> `;
            }
          });
          return text;
        }
      },
      toolbox: {
        feature: {
          dataView: { show: false, readOnly: false },
          magicType: { show: false, type: ['line', 'bar'] },
          restore: { show: false }
        },
        top: '8%',
        right: '3%'
      },
      grid: {
        width: 'auto',
        top: '20%',
        bottom: '15%'
      },
      legend: {
        data: ['温度', '湿度'],
        top: '8%',
        itemGap: 20
      },
      dataZoom: [
        {
          type: 'slider',
          show: false,
          rangeMode: 'value',
          height: 15,
          bottom: 5,
          zoomLock: false,
          brushSelect: false,
          textStyle: {
            color: '#ffffff'
          }
        }
      ],
      xAxis: [
        {
          type: 'category',
          data: [],
          axisLabel: {
            interval: 0
          },
          axisPointer: {
            type: 'shadow'
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '温度',
          alignTicks: true,
          axisLabel: {
            formatter: '{value} ℃'
          }
        },
        {
          type: 'value',
          name: '湿度',
          alignTicks: true,
          axisLabel: {
            formatter: '{value} %'
          }
        }
      ],
      series: [
        {
          name: '温度',
          type: 'line',
          data: [],
          lineStyle: {
            // 阴影部分
            shadowOffsetX: 0, // 折线的X偏移
            shadowOffsetY: 7, // 折线的Y偏移
            shadowBlur: 6, // 折线模糊
            shadowColor: 'rgba(199, 213, 231, 1)' // 折线颜色
          },
          itemStyle: {
            borderWidth: 2,
            color: '#409EFF',
            shadowOffsetX: 0, // 折线的X偏移
            shadowOffsetY: 7, // 折线的Y偏移
            shadowBlur: 6, // 折线模糊
            shadowColor: 'rgba(199, 213, 231, 1)'
          },
          emphasis: {
            itemStyle: {
              color: '#D1EDC4',
              borderColor: '#409EFF'
            }
          }
        },
        {
          name: '湿度',
          type: 'line',
          data: [],
          yAxisIndex: 1,
          lineStyle: {
            // 阴影部分
            shadowOffsetX: 0, // 折线的X偏移
            shadowOffsetY: 7, // 折线的Y偏移
            shadowBlur: 6, // 折线模糊
            shadowColor: 'rgba(231, 199, 199, 1)' // 折线颜色
          },
          itemStyle: {
            borderWidth: 2,
            color: '#F56C6C',
            shadowOffsetX: 0, // 折线的X偏移
            shadowOffsetY: 7, // 折线的Y偏移
            shadowBlur: 6, // 折线模糊
            shadowColor: 'rgba(231, 199, 199, 1)'
          },
          emphasis: {
            itemStyle: {
              color: '#D1EDC4',
              borderColor: '#F56C6C'
            }
          }
        }
      ]
    };
    const handleSelectData = async val => {
      const params = [];
      val.forEach(item => {
        params.push({
          deviceId: item.id,
          deviceName: item.name,
          deviceNumber: item.model
        });
      });
      if (params.length) {
        state.loading = true;
        const response = await saveOrUpdate({ deviceList: params }).finally((state.loading = false));
        if (response) {
          ElMessage.success('保存成功！');
          initData();
        }
      }
    };
    const handleCloseDialog = isRefresh => {
      state.dialogEquipment = false;
      if (isRefresh) {
        initData();
      }
    };
    // 开启演示数据
    const handleRenderSampleData = () => {
      state.deviceList = deviceAllList;
      state.echartsJSON = {};
      state.xAxisArray = [];
      state.isDemons = true;
      fetchData();
      if (state.timer) clearInterval(state.timer);
      nextTick(() => {
        // state.timer = setInterval(fetchData, 300000);
        state.timer = setInterval(fetchData, 300000);
      });
    };
    // 演示数据
    const renderSampleData = () => {
      echartsData.forEach(item => {
        state.echartsJSON[item.deviceId] = { ...item, acquisitionTime: formatDateTime(new Date()) };
        if (state.historyRecord[item.deviceId]) {
          state.historyRecord[item.deviceId].push({
            ...item,
            acquisitionTime: formatDateTime(new Date())
          });
        } else {
          state.historyRecord[item.deviceId] = [];
          state.historyRecord[item.deviceId].push({
            ...item,
            acquisitionTime: formatDateTime(new Date())
          });
        }
      });
      state.xAxisArray.push(formatDateTime(new Date()));
      setEcharts();
    };
    // 获取已选择设备
    const initData = async () => {
      state.loading = true;
      const response = await environmentalList().finally((state.loading = false));
      if (response) {
        state.deviceList = response.data.data;
        state.historyRecord = [];
        fetchData();
        if (state.timer) clearInterval(state.timer);
        nextTick(() => {
          state.timer = setInterval(fetchData, 300000);
        });
      }
    };
    // 获取当前所有设备的温湿度数据
    const initEcharts = async () => {
      state.loading = true;
      const response = await getRdsData().finally((state.loading = false));
      if (response) {
        const { data } = response.data;
        if (JSON.stringify(data) != '{}') {
          response.data.data?.forEach(item => {
            state.echartsJSON[item.deviceId] = item;
            if (state.historyRecord[item.deviceId]) {
              state.historyRecord[item.deviceId].push(item);
            } else {
              state.historyRecord[item.deviceId] = [];
              state.historyRecord[item.deviceId].push(item);
            }
          });
          state.xAxisArray.push(response.data.data[0].acquisitionTime);
        } else {
          state.xAxisArray.push(formatDateTime(new Date()));
        }
        setEcharts();
      }
    };
    // 设置整体的echarts
    const setEcharts = () => {
      state.deviceList.forEach(item => {
        renderChart(item.deviceId);
      });
    };
    // 设置每一个设备的echarts的option
    const renderChart = deviceId => {
      optionJSON.xAxis[0].data = state.xAxisArray.map(item => formatCalendar(item, 'hour'));
      optionJSON.series[0].data = state.historyRecord?.[deviceId]?.map(item => item.pointNameValueMap.temperature);
      optionJSON.series[1].data = state.historyRecord?.[deviceId]?.map(item => item.pointNameValueMap.humidness);
      optionJSON.dataZoom[0].show = true;
      optionJSON.dataZoom[0].start = calculateZoomStart();
      optionJSON.dataZoom[0].end = 100;
      state.optionAll[deviceId] = JSON.parse(JSON.stringify(optionJSON));
    };
    // 动态计算起始位置
    const calculateZoomStart = () => {
      const total = state.xAxisArray.length;
      if (total >= 10) {
        const visibleCount = 10;
        return Math.max(0, ((total - visibleCount) / total) * 100);
      }
    };
    const fetchData = () => {
      if (state.isDemons) {
        renderSampleData();
      } else {
        initEcharts();
      }
    };
    onMounted(() => {
      initData();
    });
    onUnmounted(() => {
      // 组件卸载时清除定时器
      if (state.timer) clearInterval(state.timer);
    });
    const handleAddEquipment = () => {
      state.dialogEquipment = true;
    };
    const handleDelete = id => {
      ElMessageBox({
        title: '提示',
        message: '是否确认删除该设备',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(async () => {
          const response = await deleteDevice([id]);
          if (response) {
            ElMessage.success('删除成功！');
            initData();
          }
        })
        .catch(() => {});
    };
    const handleDownLoad = row => {
      state.selectDialog = row;
      state.dialogLoad = true;
    };
    const closeDialog = () => {
      state.dialogLoad = false;
    };
    return {
      ...toRefs(state),
      emptyImg,
      renderChart,
      handleDownLoad,
      handleRenderSampleData,
      handleDelete,
      handleSelectData,
      handleCloseDialog,
      closeDialog,
      getNameByid,
      initEcharts,
      formatDate,
      emptyData,
      getPermissionBtn,
      calculator,
      // unqualifiedFreqChartOption,
      initData,
      renderSampleData,
      handleAddEquipment
    };
  }
};
</script>
<style lang="scss" scoped>
.title {
  font-size: 18px;
  color: $tes-font;
  cursor: pointer;
  user-select: none;
}

.icon {
  color: $tes-primary;
  cursor: pointer;
}
.highlight {
  color: $tes-primary;
  font-size: 15px;
  user-select: none;
}
.value {
  font-size: 18px;
  font-weight: 600;
  user-select: none;
}
.time {
  color: $tes-font2;
}
.title-address {
  color: $tes-font1;
  font-size: 14px;
  user-select: none;
}
</style>
