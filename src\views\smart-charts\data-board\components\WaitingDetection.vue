<template>
  <!-- 未来15天待检样品 -->
  <div class="box-top">
    <h1 class="inBlock">未来15天待检样品</h1>
    <div class="top-right inBlock">{{ formatDate(new Date()) }} ~ {{ formatDate(recentlyThirty) }}</div>
  </div>
  <div v-loading="failLoading" class="box-Center">
    <LineBarPieChart :option="histogramOption" :width="'100%'" :height="'100%'" />
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch, onBeforeUnmount } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getNameByid } from '@/utils/common';
import { getPredictionList } from '@/api/dataBoard';
import { formatterTips } from '../../func/formatter';
import LineBarPieChart from '@/components/LineBarPieChart';

export default {
  name: 'WaitingDetection',
  components: { LineBar<PERSON>ie<PERSON>hart },
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  setup(props, context) {
    const state = reactive({
      recentlyThirty: new Date().getTime() + 3600 * 1000 * 24 * 14, // 未来15天
      histogramJson: {},
      failLoading: false,
      timer: null // 定时器
    });
    watch(props, newValue => {});
    // 左上柱状图
    const histogramOption = ref({
      legend: {
        type: 'plain',
        show: true,
        right: 18,
        top: 18,
        textStyle: {
          color: '#7AD0FF',
          fontSize: 12
        }
      },
      tooltip: {
        show: true,
        backgroundColor: '#4791FF',
        borderColor: 'transparent',
        formatter: params => {
          return formatterTips(params);
        },
        textStyle: {
          color: '#fff',
          fontSize: 12,
          fontWeight: 400
        },
        trigger: 'axis'
      },
      grid: {
        width: 'auto',
        left: '10%',
        right: '2%',
        bottom: '14.5%'
      },
      xAxis: {
        type: 'category',
        data: Object.keys(state.histogramJson),
        axisLine: {
          lineStyle: {
            color: '#7AD0FF'
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '单位：个',
        splitLine: {
          show: true,
          lineStyle: {
            color: '#5397CE'
          }
        },
        nameTextStyle: {
          color: '#7AD0FF',
          fontSize: '12'
        },
        axisLine: {
          lineStyle: {
            color: '#7AD0FF'
          }
        }
      },
      color: [
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#FDE9A2' }, // 设置颜色渐变
            { offset: 1, color: '#7CEFF6' }
          ]
        }
      ],
      series: [
        {
          name: '样品数',
          data: Object.values(state.histogramJson),
          type: 'bar',
          labelLine: {
            show: true,
            lineStyle: {
              color: '#5397CE'
            }
          },
          itemStyle: {
            borderRadius: [4, 4, 0, 0]
          }
        }
      ]
    });
    // 未来15天待检样品
    const getFuturePrediction = isFirst => {
      getPredictionList().then(res => {
        if (res) {
          state.histogramJson = res.data.data;
          histogramOption.value.xAxis.data = formatDates(Object.keys(state.histogramJson));
          histogramOption.value.series[0].data = Object.values(state.histogramJson);
          if (isFirst) {
            setTime();
          }
        }
      });
    };
    getFuturePrediction(true);
    const setTime = () => {
      state.timer = setInterval(() => {
        getFuturePrediction();
      }, 10000);
    };
    const removeTimer = () => {
      if (state.timer) {
        clearInterval(state.timer);
        state.timer = null;
      }
    };
    onBeforeUnmount(() => {
      removeTimer();
    });
    // 过滤时间数组，去掉年份以/拼接
    const formatDates = array => {
      var newArray = [];
      newArray = array.map(item => {
        return item.substring(5).replace('-', '/');
      });
      return newArray;
    };
    getFuturePrediction(true);
    return {
      ...toRefs(state),
      getNameByid,
      removeTimer,
      setTime,
      formatDates,
      histogramOption,
      getFuturePrediction,
      formatDate
    };
  }
};
</script>
<style lang="scss" scoped>
@import '../data-board.scss';
</style>
