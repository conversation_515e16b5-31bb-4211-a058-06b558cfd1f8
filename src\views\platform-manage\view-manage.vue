<template>
  <!-- 视图管理 -->
  <ListLayout :has-quick-query="true" :has-button-group="true">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="code">
          <el-input
            v-model="formInline.condition"
            v-trim
            v-focus
            placeholder="请输入视图名称/编码KEY"
            class="ipt-360"
            prefix-icon="el-icon-search"
            size="large"
            clearable
            @keyup.enter="getList"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="getList">查询</el-button>
          <el-button size="large" @click="reset"> 重置 </el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('addFixedView')"
        type="primary"
        icon="el-icon-plus"
        size="large"
        @click="openDrawer('addFixed')"
        @keyup.prevent
        @keydown.enter.prevent
      >
        新增默认视图
      </el-button>
    </template>
    <template #radio-content>
      <el-radio-group v-model="viewType" size="small" style="float: left" @change="updateViewList">
        <el-radio-button label="all">全部</el-radio-button>
        <el-radio-button label="custom">自定义视图</el-radio-button>
        <el-radio-button label="fixed">默认视图</el-radio-button>
      </el-radio-group>
    </template>
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      size="medium"
      fit
      border
      height="auto"
      highlight-current-row
      style="width: auto"
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
    >
      <el-table-column label="所属资源" prop="bindingMenuName" width="120" />
      <el-table-column label="资源KEY" prop="bindingMenu" width="180" />
      <el-table-column label="视图名称" prop="viewName" width="200" />
      <el-table-column label="是否为默认视图" prop="isFixedView" width="150" align="center">
        <template #default="{ row }">{{ row.isFixedView == 1 ? '是' : '否' }}</template>
      </el-table-column>
      <el-table-column label="是否为默认展示视图" prop="isDefault" width="150" align="center">
        <template #default="{ row }">{{ row.isFixedView == 1 ? '-' : row.isDefault == 1 ? '是' : '否' }}</template>
      </el-table-column>
      <el-table-column label="创建人" prop="createBy" width="120">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.createBy) || row.createBy" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="180" />
      <el-table-column label="更新人" prop="updateBy" width="120">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.updateBy) || row.updateBy" />
        </template>
      </el-table-column>
      <el-table-column label="更新时间" prop="updateTime" width="200" />
      <el-table-column label="操作" min-width="180" fixed="right" :resizable="false" class-name="fixed-right">
        <template #default="{ row }">
          <span
            v-if="!row.isFixedView || (row.isFixedView && getPermissionBtn('editFixedView'))"
            class="blue-color"
            @click="openDrawer('edit', row)"
            >编辑</span
          >
          <span v-if="row.isFixedView && row.isList == 1" class="blue-color" @click="openDrawer('add', row)"
            >新增自定义视图</span
          >
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-show="total > 0"
      :page="pagination.page"
      :limit="pagination.limit"
      :total="total"
      @pagination="getList"
    />
    <template #other>
      <table-column-editer
        v-model="showDrawer"
        :type="editerType"
        :user-id="userId"
        :binding-menu="bindingMenu"
        :view="currentView"
        @update:view="updateViewList"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, toRefs, onMounted } from 'vue';
import { getViewListByType } from '@/api/tableView';
import { getLoginInfo } from '@/utils/auth';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import Pagination from '@/components/Pagination';
import ListLayout from '@/components/ListLayout';
import TableColumnEditer from '@/components/TableColumnView/TableColumnEditer';
import UserTag from '@/components/UserTag';

export default {
  name: 'ViewManage',
  components: { ListLayout, Pagination, TableColumnEditer, UserTag },
  setup() {
    const state = reactive({
      userId: getLoginInfo().accountId,
      formInline: {
        condition: '' // 搜索的关键字
      },
      viewType: 'all',
      tableData: [],
      total: 1,
      tableLoading: false,
      pagination: {
        limit: 20,
        page: 1
      },
      showDrawer: false,
      editerType: 'add',
      bindingMenu: '',
      currentView: {}
    });

    onMounted(() => {
      getList();
    });

    const getList = query => {
      const params = {
        condition: state.formInline.condition.trim(),
        isFixedView: state.viewType === 'all' ? '' : state.viewType === 'fixed' ? '1' : '0',
        page: query?.page?.toString() || state.pagination.page.toString(),
        limit: query?.limit?.toString() || state.pagination.limit.toString()
      };
      state.tableLoading = true;
      getViewListByType(params).then(res => {
        state.tableLoading = false;
        if (res) {
          state.pagination.page = res.data.data.currPage;
          state.pagination.limit = res.data.data.pageSize;
          state.total = res.data.data.totalCount;
          state.tableData = res.data.data.list;
        }
      });
    };
    const reset = () => {
      state.formInline.condition = '';
      state.pagination = {
        limit: 20,
        page: 1
      };
      getList();
    };

    const changeViewType = value => {
      const params = {
        page: '1',
        limit: state.pagination.limit.toString()
      };
      if (value !== 'all') {
        params.isFixedView = value === 'fixed' ? '1' : '0';
      }
      getList(params);
    };

    const openDrawer = (type, row) => {
      state.showDrawer = true;
      state.editerType = type;
      if (row) {
        state.bindingMenu = row.bindingMenu;
        state.currentView = row;
      } else {
        state.bindingMenu = '';
        state.currentView = {};
      }
    };

    const updateViewList = () => {
      getList({ page: '1' });
    };

    return {
      ...toRefs(state),
      getNameByid,
      getPermissionBtn,
      getList,
      reset,
      changeViewType,
      openDrawer,
      updateViewList,
      drageHeader
    };
  }
};
</script>
<style lang="scss" scoped></style>
