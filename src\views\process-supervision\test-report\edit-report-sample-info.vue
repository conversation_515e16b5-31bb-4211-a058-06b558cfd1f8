<template>
  <div class="edit-report-sample-info">
    <el-form
      ref="reportSampleInfoRef"
      :model="formData"
      size="small"
      :rules="infoRules"
      label-position="right"
      label-width="110px"
      class="description-form"
    >
      <el-row :gutter="20">
        <el-col v-if="tenantInfo.type === 1" :span="8">
          <el-form-item label="检验对象：" prop="inputWarehouseNo">
            <div class="nowrap">
              {{ formData.type === 1 ? formData.inputWarehouseNo || '--' : formData.productionOrderNo || '--' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col v-if="tenantInfo.type === 1" :span="8">
          <el-form-item label="对象位置：" prop="wareHouseName">
            <div class="nowrap">
              {{
                formData.type === 1
                  ? formData.wareHouseName || '--'
                  : (formData.productionProcedure || '--') + ' - ' + (formData.productionStation || '--')
              }}
            </div>
          </el-form-item>
        </el-col>
        <el-col v-if="tenantInfo.type === 1" :span="8">
          <el-form-item label="对象名称：" prop="supplierName">
            <div class="nowrap">
              {{ formData.type === 1 ? formData.supplierName || '--' : formData.customerName || '--' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col v-if="formData.type !== 1 && tenantInfo.type === 1" :span="8">
          <el-form-item label="生产批次：" prop="productionBatchNo">
            <div class="nowrap" :title="formData.productionBatchNo">{{ formData.productionBatchNo || '--' }}</div>
          </el-form-item>
        </el-col>
        <el-col v-if="formData.type !== 1 && tenantInfo.type === 1" :span="8">
          <el-form-item label="生产盘号：" prop="productionReelNo">
            <div class="nowrap" :title="formData.productionReelNo">{{ formData.productionReelNo || '--' }}</div>
          </el-form-item>
        </el-col>
        <el-col v-if="formData.type !== 1 && tenantInfo.type === 1" :span="8">
          <el-form-item label="生产数量：" prop="productionQuantity">
            <div class="nowrap">
              {{ formData.productionQuantity || '--' }} {{ filterSampleUnitToName(formData.productionUnit) }}
            </div>
          </el-form-item>
        </el-col>
        <el-col v-if="formData.type === 1 && tenantInfo.type === 1" :span="8">
          <el-form-item label="入库数量：" prop="inputWarehouseQuantity">
            <div class="nowrap">
              {{ formData.inputWarehouseQuantity || '--'
              }}{{ filterSampleUnitToName(formData.inputWarehouseUnit) || formData.inputWarehouseUnit }}
            </div>
          </el-form-item>
        </el-col>
        <el-col v-if="tenantInfo.type !== 1" :span="8">
          <el-form-item label="委托方：">
            <div class="nowrap">{{ formData.presentationCorporation ? formData.presentationCorporation : '--' }}</div>
          </el-form-item>
        </el-col>
        <el-col v-if="tenantInfo.type !== 1" :span="8">
          <el-form-item label="生产方：" prop="suppName">
            <div class="nowrap" :title="formData.suppName">{{ formData.suppName || '--' }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="来样信息：">
            <div class="nowrap">{{ formData.sampleInformation ? formData.sampleInformation : '--' }}</div>
          </el-form-item>
        </el-col>
        <el-col v-if="formData.type === 1" :span="8">
          <el-form-item label="供应商名称：" prop="supplierName">
            <div class="nowrap" :title="formData.supplierName">{{ formData.supplierName || '--' }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="主检人：">
            <UserTag :name="getNameByid(formData.chargeId)" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form
      ref="reportSampleInfoRef"
      :model="formData"
      size="small"
      :rules="infoRules"
      label-position="right"
      label-width="110px"
      class="description-form-2"
    >
      <el-row :gutter="20">
        <el-col v-if="tenantInfo.type === 1" :span="8">
          <el-form-item label="盘号：" prop="reelNo">
            <el-input
              v-model="formData.reelNo"
              placeholder="请输入内容"
              clearable
              size="small"
              maxlength="100"
              @change="
                val => {
                  return changeFormData(val, 'reelNo');
                }
              "
            />
          </el-form-item>
        </el-col>
        <el-col v-if="tenantInfo.type === 1" :span="8">
          <el-form-item label="批次：" prop="batchNo">
            <el-input
              v-model="formData.batchNo"
              placeholder="请输入内容"
              clearable
              size="small"
              maxlength="100"
              @change="
                val => {
                  return changeFormData(val, 'batchNo');
                }
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="检测日期：" prop="dateRange">
            <el-date-picker
              v-model="formData.dateRange"
              type="daterange"
              size="small"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="auto-width"
              style="width: 100%"
              @change="changeDate(formData.dateRange)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="样品名称：" prop="sampleName">
            <el-input
              v-model="formData.sampleName"
              placeholder="请输入内容"
              clearable
              size="small"
              maxlength="100"
              @change="
                val => {
                  return changeFormData(val, 'sampleName');
                }
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="样品型号：" prop="prodType">
            <el-input
              v-model="formData.prodType"
              placeholder="请输入内容"
              clearable
              size="small"
              maxlength="100"
              @change="
                val => {
                  return changeFormData(val, 'prodType');
                }
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="样品数量：" prop="sampleNum">
            <el-input-number
              v-model="formData.sampleNum"
              controls-position="right"
              :min="0"
              maxlength="8"
              size="small"
              style="width: 60%"
              @change="changeSampleNum(formData.sampleNum, 1)"
            />
            <el-select
              v-model="formData.sampleUnit"
              placeholder="请选择"
              size="small"
              style="width: 40%; padding-left: 8px"
              @change="changeSampleNum(formData.sampleUnit, 2)"
            >
              <el-option v-for="(val, key) in dictionary['5'].enable" :key="key" :label="val" :value="key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="报告语言：" prop="languageType">
            <el-select
              v-model="formData.languageType"
              placeholder="请选择报告语言"
              size="small"
              style="width: 100%"
              @change="
                val => {
                  return changeFormData(val, 'languageType');
                }
              "
            >
              <el-option v-for="(val, key) in dictionary['BGYY'].enable" :key="key" :label="val" :value="Number(key)" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="报告类型：" prop="reportType">
            <el-select
              v-model="formData.reportType"
              placeholder="请选择报告类型"
              size="small"
              style="width: 100%"
              @change="
                val => {
                  return changeFormData(val, 'reportType');
                }
              "
            >
              <el-option v-for="(val, key) in dictionary['BGLX'].enable" :key="key" :label="val" :value="key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="原材料质保书：" prop="attachmentList">
            <el-upload
              v-model:file-list="formData.attachmentList"
              :action="uploadAction"
              :headers="headerconfig"
              :before-upload="beforeUpload"
              :on-success="uploadSuccess"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
            >
              <el-button type="primary">点击上传</el-button>
              <template #tip>
                <div class="el-upload__tip">单文件大小不超过20M</div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="判定标准：" style="margin-bottom: 12px">
            <div class="test-base">
              <el-button size="small" @click="selectJS" @keyup.prevent @keydown.enter.prevent>请选择</el-button>
              <el-tag
                v-for="tag in addList"
                :key="tag.standardProductName"
                closable
                size="small"
                @close="closeTag(tag)"
              >
                {{ tag.standardProductName + '（V' + tag.standardProductVersion + '）' }}
              </el-tag>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="检测依据：">
            <el-input
              v-model="formData.testBasis"
              type="textarea"
              maxlength="300"
              show-word-limit
              resize="none"
              :autosize="{ minRows: 2, maxRows: 4 }"
              placeholder="请输入内容"
              @change="changeTBOrJB"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="判定依据：">
            <el-input
              v-model="formData.judgmentBasis"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              maxlength="300"
              show-word-limit
              resize="none"
              placeholder="请输入内容"
              @change="changeTBOrJB"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <judgement-standards
      :show="showAdd"
      :tree="treeData"
      :data="addList"
      @close="closeDialog"
      @selectData="selectData"
    />
  </div>
</template>

<script>
import { reactive, toRefs, watch, getCurrentInstance } from 'vue';
import { getNameByid } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { getTree, standardcategoryApi } from '@/api/testBase';
import { formatTree } from '@/utils/formatJson';
import JudgementStandards from '@/components/BusinessComponents/JudgementStandards.vue';
import { mapGetters } from 'vuex';
import { filterSampleUnitToName } from '@/utils/formatJson';
import UserTag from '@/components/UserTag';
import { restrictMaxLength } from '@/utils/validate';
import { getToken } from '@/utils/auth';
import { fileUploadApi } from '@/api/uploadAction';
import { downloadById, deleteId } from '@/api/testReport';

// import { ElMessage } from 'element-plus'
// import _ from 'lodash'

export default {
  name: 'EditReportSampleInfo',
  components: { JudgementStandards, UserTag },
  props: {
    data: {
      type: Object,
      default: function () {
        return {};
      }
    },
    dictionary: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const datas = reactive({
      formData: {
        dateRange: [],
        startDate: '',
        finishedDate: '',
        sampleName: '',
        sampleId: props.data.sampleId,
        sampleNum: 0,
        sampleUnit: '',
        reportId: props.data.id,
        reportStage: 1,
        languageType: 1,
        prodType: '',
        testBasis: '',
        judgmentBasis: '',
        reportStandardProductList: [],
        attachmentList: []
      },
      headerconfig: {
        Authorization: getToken()
      },
      uploadAction: fileUploadApi(),
      options: [],
      treeData: [],
      addList: [],
      dictionary: {
        BGYY: {
          all: {},
          enable: {}
        },
        5: {
          all: {},
          enable: {}
        }
      },
      languageTypeJson: {
        1: '中文报告',
        2: '英文报告',
        3: '中英文报告'
      },
      standardCategoryIds: [],
      oldAddList: [],
      showAdd: false,
      infoRules: {
        dateRange: [{ required: true, message: '请选择检测日期', trigger: 'change' }],
        sampleName: [{ required: true, message: '请输入样品名称', trigger: 'change' }],
        prodType: [{ required: true, message: '请输入样品型号', trigger: 'change' }],
        sampleNum: [
          { required: true, message: '请输入样品数量', trigger: 'blur' },
          { validator: restrictMaxLength, maxlength: 8, message: '请输入8个字符以内', tigger: 'change' }
        ],
        languageType: [{ required: true, message: '请选择报告语言', trigger: 'change' }]
      }
    });

    watch(
      () => props.data,
      newValue => {
        // console.log(newValue)
        if (newValue) {
          // datas.formData = Object.assign(datas.formData, newValue)
          datas.formData = JSON.parse(JSON.stringify(newValue));
          datas.formData.dateRange = [];
          datas.mateType = newValue.mateType;
          datas.standardCategoryIds = [];
          if (newValue.reportStage > 1) {
            datas.formData.reportStage = newValue.reportStage;
          } else {
            datas.formData.reportStage = 1;
          }
          datas.formData.sampleNum = datas.formData.sampleNum ? datas.formData.sampleNum : 0;
          if (newValue.startDate && newValue.finishedDate) {
            datas.formData.dateRange[0] = newValue.startDate;
            datas.formData.dateRange[1] = newValue.finishedDate;
            datas.formData.startDate = formatDate(newValue.startDate);
            datas.formData.finishedDate = formatDate(newValue.finishedDate);
          }
          if (newValue.reportStandardProductList) {
            datas.oldAddList = newValue.reportStandardProductList;
            datas.addList = [].concat(datas.oldAddList.filter(item => item.operationType !== 3));
          }
          if (newValue.sampleUnit) {
            datas.formData.sampleUnitName = datas.dictionary['5'].all[newValue.sampleUnit];
          }
          proxy.getLeftTree(newValue.mateType);
        }
      },
      { deep: true }
    );
    watch(
      () => props.dictionary,
      newValue => {
        if (newValue) {
          datas.dictionary = newValue || { BGYY: { all: {}, enable: {} }, 5: { all: {}, enable: {} } };
        }
      },
      { immediate: true }
    );
    // 选择判定标准
    const selectJS = () => {
      datas.showAdd = true;
    };
    const uploadSuccess = res => {
      if (res.code === 200) {
        datas.formData.attachmentList.push(res.data);
        changeFormData();
        proxy.$message.success('上传成功!');
      } else {
        proxy.$message.error(res.message);
      }
    };
    const beforeUpload = file => {
      const fileSize = file.size / 1024 / 1024 < 20;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过20M');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };
    const handlePreview = async file => {
      const response = await downloadById(file.id);
      if (response) {
        const blob = new Blob([response.data], { type: '' });
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = `${file.name}`;
        a.href = blobUrl;
        a.click();
        proxy.$message.success('下载附件成功');
      }
    };
    const handleRemove = async file => {
      if (file?.id) {
        const response = await deleteId(file.id);
        if (response) {
          const index = datas.formData.attachmentList.findIndex(item => file.id == item.id);
          datas.formData.attachmentList.splice(index, 1);
          changeFormData();
          proxy.$message.success('删除成功！');
        }
      }
    };
    // 关闭判定标准弹出框
    const closeDialog = value => {
      datas.showAdd = value;
    };
    // 获取新增的判定标准
    const selectData = data => {
      datas.addList = [];
      data.forEach(item => {
        const newStandard = {
          id: '',
          reportId: datas.formData.reportId,
          standardProductId: item.id,
          standardProductName: item.productName,
          standardProductVersion: item.version,
          standardCategoryId: item.standardCategoryId,
          operationType: 1
        };
        const standardIndex = datas.formData.reportStandardProductList.findIndex(
          obj =>
            obj.standardProductId === newStandard.standardProductId &&
            obj.standardProductVersion === newStandard.standardProductVersion
        );
        if (standardIndex === -1) {
          datas.formData.reportStandardProductList.push(newStandard);
        } else {
          datas.formData.reportStandardProductList[standardIndex].operationType = 1;
        }
        datas.addList.push(newStandard);
      });
      datas.standardCategoryIds = JSON.parse(
        JSON.stringify([
          ...new Set(
            data.map(item => {
              return item.standardCategoryId;
            })
          )
        ])
      );
      getTestBasis();
      changeSampleInfoParam();
    };
    const getTestBasis = () => {
      standardcategoryApi(datas.standardCategoryIds).then(res => {
        if (res) {
          const resultData = res.data.data.map(item => item.testBasis).toString();
          datas.formData.testBasis = resultData;
        }
      });
    };
    function changeSampleInfoParam() {
      // 标记已删除的选项
      datas.oldAddList.forEach(item => {
        if (datas.addList.findIndex(obj => obj.standardProductId === item.standardProductId) === -1) {
          datas.formData.reportStandardProductList.forEach(ele => {
            if (ele.standardProductId === item.standardProductId) {
              ele.operationType = 3;
            }
          });
        }
      });
      context.emit('setInfo', datas.formData);
    }

    // 选择检测日期
    const changeDate = date => {
      datas.formData.startDate = date ? formatDate(date[0]) : '';
      datas.formData.finishedDate = date ? formatDate(date[1]) : '';
      context.emit('setInfo', datas.formData);
    };
    // change检测依据、判定依据
    const changeTBOrJB = () => {
      context.emit('setInfo', datas.formData);
    };
    const changeFormData = (value, fieldName) => {
      // datas.formData[fieldName] = value;
      context.emit('setInfo', datas.formData);
    };
    // change样品数量 样品单位
    const changeSampleNum = (value, flag) => {
      if (flag === 1) {
        datas.formData.sampleNum = value;
      } else {
        datas.formData.sampleUnit = value;
        datas.formData.sampleUnitName = datas.dictionary['5'].all[value];
      }
      context.emit('setInfo', datas.formData);
    };
    // closeTag
    const closeTag = tag => {
      datas.addList.splice(datas.addList.indexOf(tag), 1);
      changeSampleInfoParam();
    };

    return {
      ...toRefs(datas),
      handleRemove,
      beforeUpload,
      uploadSuccess,
      handlePreview,
      changeFormData,
      changeSampleNum,
      changeTBOrJB,
      close,
      getNameByid,
      getTestBasis,
      formatDate,
      selectJS,
      closeDialog,
      selectData,
      changeDate,
      closeTag,
      filterSampleUnitToName
    };
  },
  computed: {
    ...mapGetters(['tenantInfo'])
  },
  created() {},
  methods: {
    // 获取左侧列表树接口
    getLeftTree(code) {
      const vm = this;
      getTree(code).then(res => {
        const data = res.data.data;
        vm.treeData = formatTree(data);
        if (vm.treeData.length > 0) {
          vm.treeData = [
            {
              id: '',
              isDeleted: false,
              parentId: '0',
              name: '全部',
              code: '全部',
              materialCategoryCode: vm.treeData[0].materialCategoryCode,
              order: 0
            }
          ].concat(vm.treeData);
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.edit-report-sample-info {
  margin-bottom: 60px;
  :deep(.el-upload-list) {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
  }
  :deep(.el-upload-list__item) {
    flex: 0 0 50%;
  }
  :deep(.el-icon-close-tip) {
    display: none !important;
  }
  .description-form,
  .description-form-2 {
    background: $background-color;
    margin-top: 20px;
    padding: 15px 120px;
    .el-form-item {
      margin-bottom: 0;
      :deep(.el-form-item__label) {
        color: $tes-font2;
      }
      :deep(.el-form-item__content) {
        text-align: left;
      }
    }
  }
  .description-form-2 {
    padding: 30px 120px 10px;
    overflow: auto;
    max-height: 520px;
    .el-form-item {
      margin-bottom: 20px;
      :deep(.el-form-item__label) {
        color: $tes-font1;
      }
    }
    .test-base {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: center;
      .el-button {
        margin-right: 8px;
        margin-bottom: 8px;
      }
      .el-tag {
        margin: 0 8px 8px 0;
      }
    }
  }
}
</style>
