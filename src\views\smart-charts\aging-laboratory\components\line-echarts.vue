<template>
  <!-- 老化箱温度趋势（最近8小时） -->
  <div class="sample-list flex flex-col overflow-auto">
    <div class="item-title">
      <div class="title">
        <span>老化箱温度趋势（最近8小时）</span>
        <i class="el-icon-setting cursor-pointer" @click="dialogAgingDevice = true" />
      </div>
    </div>
    <div class="auto-scroll-table-container flex-1 overflow-y-auto flex flex-col">
      <LineBarPieChart :option="qualifiedRateOptionFirst" :width="'100%'" :height="'100%'" />
    </div>
    <div class="last-time">最后更新时间：{{ echartsParams.endDate }}</div>
    <DialogAgingDevice :is-show="dialogAgingDevice" :table-list="tableData" @close="handleClose" />
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount, toRefs } from 'vue';
import LineBarPieChart from '@/components/LineBarPieChart';
import DialogAgingDevice from './DialogAgingDevice.vue';
import { deviceBoxAllBox, boxListRdsData } from '@/api/aging-laboratory';
import { calculateDaysBefore, formatDateTime, formatDateDay } from '@/utils/formatTime';
import { generateRandomColor } from '@/utils/common';
import { reactive } from 'vue';

export default {
  components: { LineBarPieChart, DialogAgingDevice },
  setup() {
    const state = reactive({
      dialogAgingDevice: false,
      /** 弹出框所有数据 */
      tableData: [],
      echartsParams: {
        boxIds: []
      },
      timer: null,
      detailLoading: false
    });
    const qualifiedRateOptionFirst = ref({
      legend: {
        type: 'scroll',
        orient: 'horizontal',
        show: true,
        right: 100,
        top: 13,
        textStyle: {
          color: '#7AD0FF',
          fontSize: 12
        }
      },
      tooltip: {
        show: true,
        backgroundColor: '#4791FF',
        borderColor: 'transparent',
        textStyle: {
          color: '#fff',
          fontSize: 12,
          fontWeight: 400
        },
        trigger: 'axis',
        formatter: function (params) {
          return `${params[0].axisValueLabel}<br />${params[0].seriesName}：${params[0].value}℃`;
        }
      },
      grid: {
        width: 'auto',
        left: '3%',
        right: '3%',
        bottom: '17.5%',
        top: '17%'
      },
      xAxis: {
        type: 'category',
        data: [],
        axisLabel: {
          interval: 0,
          width: 100,
          formatter: function (value) {
            return value.substring(0, 5) + '\n' + value.substring(5);
          }
        },
        axisLine: {
          lineStyle: {
            color: '#7AD0FF'
          }
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '温度',
          splitLine: {
            show: true,
            lineStyle: {
              color: '#5397CE'
            }
          },
          nameTextStyle: {
            color: '#7AD0FF',
            fontSize: '12'
          },
          axisLine: {
            lineStyle: {
              color: '#7AD0FF'
            }
          }
        }
      ],
      series: [],
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: 0,
          height: 12,
          start: 0,
          end: 30,
          left: '10%', // 左侧留白
          right: '10%', // 右侧留白
          bottom: 8,
          maxSpan: 40,
          minSpan: 10,
          textStyle: {
            color: '#fff'
          }
        }
      ]
    });
    const initData = async isCycle => {
      state.detailLoading = true;
      const { data } = await deviceBoxAllBox().finally((state.detailLoading = false));
      if (data) {
        state.tableData = data.data;
        state.echartsParams.boxIds = state.tableData
          .filter(item => {
            return item.boardStatus == '1';
          })
          .map(item => item.id);
        initEcharts(isCycle);
      }
    };
    const initEcharts = async isCycle => {
      state.echartsParams.endDate = formatDateTime(new Date());
      if (isCycle) {
        state.echartsParams.startDate = formatDateTime(
          new Date(new Date(state.echartsParams.endDate).getTime() - 2 * 60 * 1000)
        );
      } else {
        state.echartsParams.startDate = calculateDaysBefore(new Date(), 8);
      }
      state.detailLoading = true;
      const { data } = await boxListRdsData(state.echartsParams).finally((state.detailLoading = false));
      if (data) {
        const echartsData = data.data;
        if (echartsData.length) {
          const xAxisData =
            echartsData
              .filter(item => {
                return item.rdsList.length > 0;
              })[0]
              ?.rdsList.map(item => {
                return formatDateDay(item.acquisitionTime, 'dayTime');
              }) || [];
          if (!isCycle) {
            qualifiedRateOptionFirst.value.series = [];
            qualifiedRateOptionFirst.value.xAxis.data = xAxisData;
          } else {
            qualifiedRateOptionFirst.value.xAxis.data = [...qualifiedRateOptionFirst.value.xAxis.data, ...xAxisData];
          }
          echartsData.forEach(item => {
            item.temperature = item.rdsList.map(val => val.pointNameValueMap.temperature);
            const params = {
              type: 'line',
              name: item.boxName,
              itemStyle: {
                color: generateRandomColor(item.deviceId)
              },
              data: item.temperature
            };
            const index = qualifiedRateOptionFirst.value.series.findIndex(
              seriesItem => seriesItem.name == item.boxName
            );
            if (index == -1) {
              qualifiedRateOptionFirst.value.series.push(params);
            } else {
              qualifiedRateOptionFirst.value.series[index].data = [
                ...qualifiedRateOptionFirst.value.series[index].data,
                ...item.temperature
              ];
            }
          });
        }
      }
    };
    const handleClose = isRefresh => {
      state.dialogAgingDevice = false;
      if (isRefresh) {
        initData();
      }
    };
    const setTimer = () => {
      if (state.timer === null) {
        state.timer = setInterval(() => {
          initData(true);
        }, 2000 * 60);
      }
    };
    onMounted(() => {
      initData();
      setTimer();
    });

    onBeforeUnmount(() => {});

    return {
      ...toRefs(state),
      qualifiedRateOptionFirst,
      handleClose
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/intelligentChart.scss';
.last-time {
  color: #fff;
  padding: 5px 0;
}
.item-title {
  background: #1a2a47;
  border-radius: 8px 8px 0px 0px;
  font-size: 18px;
  color: #4ec3f7;
  height: 52px;
  line-height: 30px;
  font-weight: 700;
  padding: 10px 10px 10px 10px;
}
.title {
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #87b7ff;
  padding: 5px 5px 5px 5px;
}
.auto-scroll-table-container {
  width: 100%;
  height: 100%;
  border: 2px solid #1a2a47;
  padding: 3px 10px 10px 10px;
  background-color: #1a2a47;
  border-radius: 0 0 4px 4px;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}
</style>
