<template>
  <!-- 实验室管理-样品入库 -->
  <ListLayout
    :has-search-panel="false"
    :has-quick-query="true"
    :has-button-group="getPermissionBtn('moreSamplestorageBtn') ? true : false"
  >
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" @submit.prevent>
        <el-form-item prop="param">
          <el-input
            v-model="formInline.param"
            v-focus
            v-trim
            size="large"
            placeholder="请输入编号/样品名称/型号规格"
            class="ipt-360"
            autofocus
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter="onSubmit"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button size="large" type="primary" @click="onSubmit">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        size="large"
        type="primary"
        icon="el-icon-document-copy"
        :disabled="multipleSelection.length === 0"
        @click="handleStorage(multipleSelection, 2)"
        >批量入库</el-button
      >
      <el-button
        v-if="getPermissionBtn('storagePrints')"
        type="primary"
        :disabled="multipleSelection.length === 0"
        size="large"
        icon="el-icon-printer"
        @click="handlePrint"
        @keyup.prevent
        @keydown.enter.prevent
        >二维码打印</el-button
      >
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="12">
          <el-select v-model="formInline.type" placeholder="请选择检验类型" size="small" clearable @change="changeType">
            <el-option v-for="(val, key) in dictionary['JYLX'].enable" :key="key" :label="val" :value="key" />
          </el-select>
        </el-col>
        <el-col :span="12" style="text-align: right">
          <TableColumnView binding-menu="samplestorage" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table format-height-table base-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      @header-dragend="drageHeader"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column type="selection" width="55" align="center" fixed="left" />
      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <TableColumnContentRenderer :column="item" :row="row" format="yyyy-MM-dd">
              <template #link>
                <span
                  v-if="row[item.fieldKey]"
                  v-copy="row[item.fieldKey]"
                  class="nowrap blue-color"
                  @click.stop="jumpApplicationDetail(row)"
                >
                  {{ row[item.fieldKey] || '--' }}
                </span>
              </template>
              <template #custom>
                <template v-if="item.fieldKey === 'sampleNum'">
                  <span v-if="row.sampleNum"
                    >{{ row.sampleNum }}{{ filterSampleUnitToName(row.sampleUnit) || row.sampleUnit }}</span
                  >
                  <span v-else>--</span>
                </template>
                <template v-if="item.fieldKey === 'type'">
                  <span v-if="row.type">{{ dictionary['JYLX'].all[row.type] }}</span>
                  <span v-else>--</span>
                </template>
                <div v-else-if="item.fieldKey === 'inputWarehouseQuantity,inputWarehouseUnit'">
                  <span>{{
                    row.type === 1 ? row.inputWarehouseQuantity || '--' : row.productionQuantity || '--'
                  }}</span>
                  <span>{{
                    row.type === 1
                      ? filterSampleUnitToName(row.inputWarehouseUnit) || row.inputWarehouseUnit
                      : filterSampleUnitToName(row.productionUnit) || row.productionUnit
                  }}</span>
                </div>
              </template>
            </TableColumnContentRenderer>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" prop="caozuo" fixed="right" :width="colWidth.operationMultiple">
        <template #default="scope">
          <span v-if="getPermissionBtn('storagePrints')" class="blue-color" @click.stop="handleCheckQRCode(scope.row)"
            >查看二维码</span
          >
          <span v-if="getPermissionBtn('samplestorageBtn')" class="blue-color" @click.stop="handleStorage(scope.row, 1)"
            >入库</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />

    <template #other>
      <!--入库弹屏-->
      <module-storage :module-title="moduleTitle" :visible="ModuleStorageVisible" :lists="lists" @close="close" />
      <!-- 打印弹出框 -->
      <el-dialog v-model="dialogCode" title="打印设置" :close-on-click-modal="false" width="400px" top="50px">
        <el-row>
          <el-col :span="24">
            <el-select v-model="printerName" placeholder="请选择打印机" size="small" clearable style="width: 100%">
              <el-option v-for="item in qrSprintList" :key="item.name" :label="item.name" :value="item.name">
                <span style="float: left">{{ item.name }}</span>
                <span style="float: right; color: #909399; font-size: 13px">{{ item.description }}</span>
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogCode = false">取 消</el-button>
            <el-button type="primary" @click="onSubmitPrint">打 印</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 二维码弹出框 -->
      <QRCodePopup v-model:visible="qrCodeDialog" :value="qrCodeData" title="样品二维码" />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance, computed, onMounted } from 'vue';
import router from '@/router/index.js';
import Pagination from '@/components/Pagination';
import ModuleStorage from './components/ModuleStorage';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { mapGetters, useStore } from 'vuex';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import _ from 'lodash';
import { checkPermissionList } from '@/api/permission';
import { permissionTypeList } from '@/utils/permissionList';
import { warehousingList, getPringList, printCode } from '@/api/samplestorage';
import { filterSampleUnitToName } from '@/utils/formatJson';
import ListLayout from '@/components/ListLayout';
import { colWidth } from '@/data/tableStyle';
import { getInspectionList } from '@/api/inspection-application';
import { getTaskRegistrationList } from '@/api/task-registration';
import TableColumnView from '@/components/TableColumnView';
import TableColumnContentRenderer from '@/components/TableColumnView/TableColumnContentRenderer';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';
import { QRCodePopup } from '@/components/QRCodePopup';
import { getDictionary } from '@/api/user';

export default {
  name: 'Samplestorage',
  components: {
    Pagination,
    ModuleStorage,
    ListLayout,
    TableColumnView,
    TableColumnContentRenderer,
    QRCodePopup
  },
  setup() {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const editFrom = ref(null);
    const otherForm = reactive({
      accountId: getLoginInfo().accountId,
      multipleSelection: [],
      tableRef: ref(),
      dialogCode: false,
      printerName: '',
      qrSprintList: [],
      activeName: '0',
      moduleTitle: '', // 样品入库弹窗标题
      ModuleStorageVisible: false,
      showS: false,
      mangeList: [],
      lists: [],
      dictionary: {
        JYLX: {
          enable: {},
          all: {}
        }
      },
      formInline: {
        param: '',
        assignedEndTime: '',
        assignedStartTime: '',
        startTime: '',
        endTime: '',
        ownerId: '',
        prodType: '',
        type: '',
        status: '1'
      },
      searchForm: {
        prodType: '',
        ownerId: '',
        rukuDateRange: '',
        xiadaDateRange: ''
      },
      types: store.user.materialList,
      userOptions: store.common.nameList,
      copyUserOptions: store.common.nameList,
      tableColumns: [],
      list: [],
      content: '',
      radioData: '待分配',
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        sort: 'DESC'
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ],
      qrCodeDialog: false,
      qrCodeData: {}
    });
    function onSubmit() {
      proxy.getList();
    }
    function changeType() {
      proxy.getList();
    }

    function reset() {
      editFrom.value.resetFields();
      otherForm.formInline = {
        param: '',
        assignedEndTime: '',
        assignedStartTime: '',
        startTime: '',
        endTime: '',
        ownerId: '',
        mate_type: '',
        status: '1'
      };
      otherForm.radioData = '待分配';
      otherForm.searchForm = {
        prodType: '',
        ownerId: '',
        rukuDateRange: '',
        xiadaDateRange: ''
      };
      otherForm.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        sort: 'DESC'
      };
      proxy.getList();
    }
    // 二维码打印
    const handlePrint = () => {
      otherForm.dialogCode = true;
      otherForm.codeLoading = true;
      getPringList().then(res => {
        otherForm.codeLoading = false;
        if (res) {
          otherForm.qrSprintList = res.data.data;
          otherForm.printerName = otherForm.qrSprintList[0].name;
        }
      });
    };
    const search = () => {
      otherForm.showS = !otherForm.showS;
      if (otherForm.activeName === '0') {
        otherForm.activeName = '1';
      } else {
        otherForm.activeName = '0';
      }
    };
    const sortChange = data => {
      const { prop, order } = data;
      otherForm.listQuery.orderBy = prop;
      if (order === 'ascending') {
        otherForm.listQuery.isAsc = true;
      } else if (order === 'descending') {
        otherForm.listQuery.isAsc = false;
      } else {
        otherForm.listQuery.isAsc = null;
      }
    };
    // 点击样品编号跳转到样品详情页面
    const handleSampleOrdersDetail = row => {
      router.push({
        path: '/experiment/detail',
        query: {
          orderId: row.orderId,
          sampleId: row.sampleId
        }
      });
    };

    const inputValue = data => {
      if (typeof data === 'string') {
        otherForm.content = data;
      } else {
        otherForm.content = '';
      }
    };

    // 打印
    const onSubmitPrint = () => {
      if (!otherForm.printerName) {
        proxy.$message.error('请先选择打印机');
        return false;
      }
      const params = {
        printerName: otherForm.printerName,
        samplePrintParamList: []
      };
      otherForm.multipleSelection.forEach(item => {
        params.samplePrintParamList.push({
          sampleId: item.sampleId,
          internalId: item.id
        });
      });
      otherForm.codeLoading = true;
      printCode(params).then(res => {
        otherForm.codeLoading = false;
        if (res) {
          if (res.data.data) {
            otherForm.multipleSelection = [];
            proxy.$message.success('打印成功');
            otherForm.dialogCode = false;
            proxy.getList();
          }
        }
      });
    };

    const changeRadio = value => {
      if (value === '待分配') {
        otherForm.formInline.status = '1';
      } else if (value === '已分配') {
        otherForm.formInline.status = '2';
      } else if (value === '超期待分配') {
        otherForm.formInline.status = '11';
      } else if (value === '超期已分配') {
        otherForm.formInline.status = '21';
      } else {
        otherForm.formInline.status = '';
      }
      proxy.getList();
    };

    // 过滤试验员
    const filterUserList = val => {
      if (val) {
        const list = [];
        otherForm.copyUserOptions.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        otherForm.userOptions = list;
      } else {
        otherForm.userOptions = otherForm.copyUserOptions;
      }
    };
    // 选择入库数据
    const handleSelectionChange = val => {
      otherForm.multipleSelection = val;
    };
    // 行点击
    const handleRowClick = row => {
      otherForm.tableRef.toggleRowSelection(
        row,
        !otherForm.multipleSelection.some(item => {
          return row.id === item.id;
        })
      );
    };
    // 分配入库
    const handleStorage = (rows, type) => {
      var itt = JSON.parse(JSON.stringify(rows));
      if (type === 1) {
        otherForm.lists = [];
        otherForm.tableRef.clearSelection();
        otherForm.lists.push(itt);
        otherForm.moduleTitle = '样品入库';
      } else {
        otherForm.lists = JSON.parse(JSON.stringify(otherForm.multipleSelection));
        otherForm.moduleTitle = '批量入库';
      }
      otherForm.ModuleStorageVisible = true;
    };
    // 查看二维码
    const handleCheckQRCode = row => {
      otherForm.qrCodeDialog = true;
      otherForm.qrCodeData = {
        type: 'sample',
        id: row.id,
        orderId: row.orderId
      };
    };
    const close = type => {
      if (type) {
        proxy.getList();
      }
      otherForm.ModuleStorageVisible = false;
      otherForm.lists = [];
    };
    // 查看申请详情
    const jumpApplicationDetail = row => {
      if (row.type === 10 || row.type === 11 || row.type === 12) {
        getTaskRegistrationList({
          condition: `${row.presentationCode}`
        }).then(res => {
          if (res && res.status === 200) {
            if (res.data.data.list.length > 0) {
              router.push({
                name: 'SampleStorageRegistration',
                query: { id: res.data.data.list.find(item => item.entrustNo === row.presentationCode)?.id, flag: 1 }
              });
            }
          }
        });
      } else {
        getInspectionList({ param: `${row.presentationCode}` }).then(res => {
          if (res && res.status === 200) {
            if (res.data.data.list.length > 0) {
              router.push({
                name: 'SampleStorageApplication',
                query: { id: res.data.data.list[0].id, flag: 1 }
              });
            }
          }
        });
      }
    };

    const onUpdateColumns = columns => {
      otherForm.tableKey = otherForm.tableKey + 1;
      otherForm.tableColumns = columns;
    };

    const tenantType = computed({
      get: () => store.user.tenantInfo.type
    });

    const getDictionaryList = () => {
      Object.keys(otherForm.dictionary).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          otherForm.dictionary[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              otherForm.dictionary[item].enable[optionItem.code] = optionItem.name;
            }
            otherForm.dictionary[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    onMounted(() => {
      getDictionaryList();
    });

    return {
      filterUserList,
      handlePrint,
      changeType,
      onSubmitPrint,
      handleRowClick,
      getPermissionBtn,
      drageHeader,
      handleStorage,
      handleCheckQRCode,
      handleSampleOrdersDetail,
      formatDate,
      filterSampleUnitToName,
      getNameByid,
      changeRadio,
      inputValue,
      handleSelectionChange,
      close,
      sortChange,
      editFrom,
      ...toRefs(otherForm),
      search,
      onSubmit,
      reset,
      colWidth,
      tenantType,
      jumpApplicationDetail,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  },
  computed: {
    ...mapGetters(['tenantGroup'])
  },
  created() {
    this.getList();
    this.getnamelist();
    // 刷新列表
    this.bus.$on('reloadTestAllocationList', msg => {
      this.getList();
    });
    this.copyUserOptions = JSON.parse(JSON.stringify(this.userOptions));
  },
  methods: {
    getList(data) {
      const _this = this;
      _this.listLoading = true;
      if (data && data !== undefined) {
        _this.listQuery.page = data.page;
        _this.listQuery.limit = data.limit;
      }
      _this.formInline.param = _this.formInline.param.trim();
      const param = Object.assign(_this.formInline, _this.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      warehousingList(param).then(res => {
        if (res !== false && res.data.code === 200) {
          const { data } = res.data;
          _this.list = data.list;
          _this.total = data.totalCount;
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
    },
    getnamelist() {
      checkPermissionList(permissionTypeList.sampleOwner).then(res => {
        this.mangeList = res.data.data;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.blue-color {
  color: $tes-primary;
  cursor: pointer;
}
::v-deep(.el-table__body td.is-center .cell) {
  justify-content: center;
}
</style>
