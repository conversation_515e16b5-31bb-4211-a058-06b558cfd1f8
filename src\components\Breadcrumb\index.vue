<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <span class="icon el-icon-s-home no-select" />
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="(item, index) in levelList" :key="item.path" class="no-select">
        <span v-if="item.redirect === 'noRedirect' || index == levelList.length - 1" class="no-redirect no-select">{{
          item.meta.title
        }}</span>
        <a v-else class="breadcrumb-title no-select" @click.prevent="handleLink(item)">{{ item.meta.title }}</a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script>
import * as pathToRegexp from 'path-to-regexp';
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      levelList: null
    };
  },
  computed: {
    ...mapGetters(['permission_routes'])
  },
  watch: {
    $route(route) {
      // if you go to the redirect page, do not update the breadcrumbs
      if (route.path.startsWith('/redirect/')) {
        return;
      }
      this.getBreadcrumb();
    }
  },
  created() {
    this.getBreadcrumb();
  },
  methods: {
    getBreadcrumb() {
      // only show routes with meta.title
      // console.log(this.permission_routes)
      const matched = this.$route.matched.filter(item => item.meta && item.meta.title);
      const first = matched[0];

      if (!this.isDashboard(first)) {
        // matched = [{ path: '/home', meta: { title: '首页' }}].concat(matched)
      }

      if (matched.length > 1) {
        matched.forEach((item, index) => {
          if (item.meta && item.meta.activeMenu && item.meta.parentName) {
            const pItem = matched[0].children.filter(citem => citem.name === item.meta.parentName);
            matched.splice(index, 0, pItem[0]);
          }
        });
      }

      this.levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false);

      // console.log(this.levelList)
    },
    isDashboard(route) {
      const name = route && route.name;
      if (!name) {
        return false;
      }
      return name.trim().toLocaleLowerCase() === 'Home'.toLocaleLowerCase();
    },
    pathCompile(path) {
      const { params } = this.$route;
      var toPath = pathToRegexp.compile(path);
      return toPath(params);
    },
    handleLink(item) {
      if (item.name === this.levelList[0].name) {
        return;
      }
      const { redirect, path } = item;
      if (redirect) {
        this.$router.push(redirect);
        return;
      }
      this.$router.push(this.pathCompile(path));
    }
  }
};
</script>

<style lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  display: flex;
  padding: 12px 24px;
  .icon {
    display: inline-block;
    color: #606266;
    margin-right: 8px;
  }
  .no-redirect {
    cursor: text;
    color: #303133;
  }
  .breadcrumb-title {
    color: $tes-font2;
  }

  :deep(.el-breadcrumb__separator) {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}
</style>
