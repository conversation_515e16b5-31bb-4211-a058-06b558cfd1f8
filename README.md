该项目是基于 vue3 和 element-plus 进行开发

全局安装 npm install -g vue-cli

# element-plus-test1

创建项目 vue create ProjectName

## Project setup

```
安装依赖 npm install
```

### Compiles and hot-reloads for development

```
 运行项目  npm run serve
```

### Compiles and minifies for production

```
打包项目  npm run build
```

### Run your unit tests

```
npm run test:unit
```

### Run your end-to-end tests

```
npm run test:e2e
```

### Lints and fixes files

```
npm run lint
```

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).

components: AddItem,AdvancedSearch,DragHandle,filterForm,InputTag,Pagination,WordToHtml
