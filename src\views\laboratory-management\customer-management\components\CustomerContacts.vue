<template>
  <div>
    <div v-if="isValid" class="btn-group">
      <el-button
        :loading="tableLoading"
        icon="el-icon-plus"
        type="primary"
        size="small"
        @click="addContacts"
        @keyup.prevent
        @keydown.enter.prevent
        >新增</el-button
      >
      <el-button
        v-if="formData.tableList.length > 0 && !isEdit && !isAdd"
        icon="el-icon-edit"
        :loading="tableLoading"
        size="small"
        @click="isEdit = true"
        @keyup.prevent
        @keydown.enter.prevent
        >编辑</el-button
      >
      <el-button
        v-if="isEdit || isAdd"
        size="small"
        :loading="tableLoading"
        type="primary"
        @click="saveContacts"
        @keyup.prevent
        @keydown.enter.prevent
        >保存</el-button
      >
      <el-button
        v-if="isEdit || isAdd"
        size="small"
        :loading="tableLoading"
        @click="calceContacts"
        @keyup.prevent
        @keydown.enter.prevent
        >取消</el-button
      >
    </div>
    <el-form ref="ruleFormTable" :model="formData">
      <el-table
        v-loading="tableLoading"
        :data="formData.tableList"
        fit
        border
        height="auto"
        highlight-current-row
        class="detail-table dark-table base-table format-height-table2"
      >
        <el-table-column label="序号" :width="colWidth.serialNo" align="center">
          <template #default="{ $index }">
            <span> {{ $index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <div v-if="!isEdit && row.id">{{ row.name || '--' }}</div>
            <el-form-item
              v-else
              :prop="`tableList.${$index}.name`"
              :rules="{ required: true, message: '请输入姓名', trigger: 'change' }"
              style="margin: 0px"
            >
              <el-input v-model="row.name" v-trim maxlength="100" placeholder="请输入姓名" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="job" label="职务" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <div v-if="!isEdit && row.id">{{ row.job || '--' }}</div>
            <el-form-item v-else :prop="`tableList.${$index}.job`" style="margin: 0px">
              <el-input v-model="row.job" v-trim maxlength="100" placeholder="请输入职务" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="电话" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <div v-if="!isEdit && row.id">{{ row.phone || '--' }}</div>
            <el-form-item
              v-else
              :prop="`tableList.${$index}.phone`"
              :rules="[{ validator: isPhoneMobile, tigger: 'blur' }]"
              style="margin: 0px"
            >
              <el-input v-model="row.phone" placeholder="请输入电话" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="email" label="邮箱" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <div v-if="!isEdit && row.id">{{ row.email }}</div>
            <el-form-item
              v-else
              :prop="`tableList.${$index}.email`"
              :rules="[{ validator: isEmail2, tigger: 'blur' }]"
              style="margin: 0px"
            >
              <el-input v-model="row.email" placeholder="请输入邮箱" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="isDefault" label="默认联系人" width="180">
          <template #default="{ row, $index }">
            <el-tag v-if="!isEdit && row.id" :type="row.isDefault ? 'success' : 'info'" size="small">
              {{ row.isDefault ? '是' : '否' }}
            </el-tag>
            <el-form-item v-else :prop="`tableList.${$index}.isDefault`">
              <el-switch v-model="row.isDefault" class="inner-switch" :active-text="row.isDefault ? '是' : '否'" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column v-if="isValid" label="操作" :width="colWidth.operationSingle">
          <template #default="{ row, $index }">
            <span class="blue-color" @click="deleteContacts(row, $index)">删除</span>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch, getCurrentInstance } from 'vue';
import { isPhoneMobile, isEmail2 } from '@/utils/validate';
import { getContacts, saveContactsApi, deleteContactsApi } from '@/api/customerManagement';
import { colWidth } from '@/data/tableStyle';

export default {
  name: 'CustomerContacts',
  props: {
    activeName: {
      type: String,
      default: ''
    },
    isValid: {
      type: Boolean,
      default: false
    },
    detailId: {
      type: String,
      default: ''
    }
  },
  emits: ['isHaveRevised'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      infoId: '', // 详情id
      isValid: false,
      ruleFormTable: ref(),
      tableLoading: false,
      isEdit: false,
      isAdd: false,
      formData: {
        tableList: []
      },
      oldTableList: []
    });
    watch(props, newValue => {
      if (newValue.activeName === '2' && props.detailId) {
        state.isValid = props.isValid;
        state.infoId = props.detailId;
        getList();
      }
    });
    const getList = () => {
      getContacts(state.infoId).then(res => {
        state.tableLoading = false;
        if (res) {
          state.isEdit = false;
          state.isAdd = false;
          state.formData.tableList = JSON.parse(JSON.stringify(res.data.data));
          state.oldTableList = JSON.parse(JSON.stringify(res.data.data));
        }
      });
    };
    // 删除联系人
    const deleteContacts = (row, index) => {
      if (row.id) {
        state.tableLoading = true;
        deleteContactsApi(row.id).then(res => {
          state.tableLoading = false;
          if (res) {
            context.emit('isHaveRevised', true);
            proxy.$message.success(res.data.message);
            state.formData.tableList.splice(index, 1);
            state.oldTableList.splice(
              state.oldTableList.findIndex(item => item.id === row.id),
              1
            );
          }
        });
      } else {
        state.formData.tableList.splice(index, 1);
      }
    };
    // 保存联系人
    const saveContacts = () => {
      state.ruleFormTable
        .validate()
        .then(valid => {
          if (valid) {
            saveContactsApi(state.formData.tableList).then(res => {
              state.tableLoading = false;
              if (res) {
                context.emit('isHaveRevised', true);
                proxy.$message.success(res.data.message);
                getList('contacts');
              }
            });
          } else {
            return false;
          }
        })
        .catch(error => {
          const fieldName = Object.keys(error)[0];
          proxy.$message.warning(error[fieldName][0].message);
          return false;
        });
    };
    // 取消保存
    const calceContacts = () => {
      state.isEdit = false;
      state.isAdd = false;
      if (state.oldTableList.length > 0) {
        state.formData.tableList = JSON.parse(JSON.stringify(state.oldTableList));
      } else {
        state.formData.tableList = [];
      }
    };
    // 新增联系人
    const addContacts = () => {
      state.isAdd = true;
      state.formData.tableList.push({
        isDefault: true,
        name: '',
        customerId: state.infoId
      });
    };
    return {
      ...toRefs(state),
      getList,
      isEmail2,
      isPhoneMobile,
      colWidth,
      addContacts,
      calceContacts,
      saveContacts,
      deleteContacts
    };
  }
};
</script>

<style scoped lang="scss">
.btn-group {
  margin-bottom: 16px;
}
</style>
