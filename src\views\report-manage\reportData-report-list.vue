<template>
  <!-- 检测项目数据 -->
  <ListLayout v-loading="expLoading" :has-button-group="getPermissionBtn('gistAdd') ? true : false">
    <template #search-bar>
      <div class="searchInput">
        <el-input
          v-model="searchForm.content"
          v-trim
          v-focus
          placeholder="请输入编号/模糊搜索"
          size="large"
          @keyup.enter="getTableList"
        >
          <template #prefix>
            <i class="el-input__icon el-icon-search" />
          </template>
        </el-input>
        <el-button type="primary" size="large" @click="getTableList">查询</el-button>
        <el-button size="large" @click="reset">重置</el-button>
        <el-button class="searchBtn" type="text" @click="advancedSearch" @keyup.prevent @keydown.enter.prevent
          >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
        /></el-button>
      </div>
    </template>
    <template #button-group>
      <el-button type="primary" size="large" @click="handleTestItem">选择检测项目</el-button>
      <el-button size="large" :loading="tableLoading" @click="handleExport" @keyup.prevent @keydown.enter.prevent
        ><span class="iconfont tes-task-issued" /> 导出</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="searchFrom" :model="searchForm" label-width="110px" label-position="right">
            <el-form-item label="物资分类：" prop="mateType">
              <el-radio-group v-model="searchForm.mateType" size="small" @change="handleChangeMate">
                <el-radio-button v-for="type in tabsData" :key="type" :label="type.code" class="label-type">
                  {{ type.name }}
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="签发日期：">
              <el-date-picker
                v-model="submitTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="handleSubmitTime"
              />
            </el-form-item>
            <el-form-item label="检测项目：">
              <el-tag v-for="(val, key) in itemProjects" :key="key" closable @close="deleteItem(key)">
                {{ val }}
              </el-tag>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-radio-group v-model="result" size="small" @change="changeStatus">
        <el-radio-button label="-1"
          >全部<span v-if="totalNumber['-1']">({{ totalNumber['-1'] }})</span></el-radio-button
        >
        <el-radio-button v-for="(value, key, index) in statusList" :key="index" :label="key"
          >{{ value }}
          <span v-if="totalNumber[key]">({{ totalNumber[key] }})</span>
        </el-radio-button>
      </el-radio-group>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      height="auto"
      size="medium"
      highlight-current-row
      class="dark-table format-height-table base-table data-table"
      @header-dragend="drageHeader"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="样品编号" prop="secSampleNum" width="180px" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.secSampleNum" class="blue-color" @click="handleTz(row, 'yp')">
            {{ row.secSampleNum || '--' }}
          </div>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="报告编号" prop="reportNo" width="180px" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.reportNo" class="blue-color" @click="handleTz(row, 'bg')">{{ row.reportNo }}</div>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="样品名称" prop="sampleName" min-width="280px" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.sampleName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="型号规格" prop="prodType" min-width="150px" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.prodType || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物料编号" prop="materialNo" width="150px" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.materialNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="批次" prop="batchNo" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.batchNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="盘号" prop="reelNo" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.reelNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="检验对象" prop="productionOrderNo" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.type === 1">{{ row.inputWarehouseNo || '--' }}</span>
          <span v-if="row.type !== 1">{{ row.productionOrderNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="对象位置" prop="wareHouseName" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.type == 1">{{ row.wareHouseName || '--' }}</span>
          <span v-else-if="row.type !== 1 && (row.productionProcedure || row.productionStation)"
            >{{ row.productionProcedure + ' ' + row.productionStation }}
          </span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="对象名称" prop="supplierName" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.type == 1">{{ row.supplierName || '--' }}</span>
          <span v-if="row.type !== 1">{{ row.customerName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="检验结果" prop="reelNo" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.result === '0'">合格</span>
          <span v-else-if="row.result === '1'">不合格</span>
          <span v-else-if="row.result === '2'">不判定</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="判定标准" prop="standardProduct" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.standardProduct || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="试验负责人" prop="ownerId" min-width="120px">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.ownerId) || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="申请人" prop="ownerId" min-width="120px">
        <template #default="{ row }">
          <UserTag :name="row.applicantName || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="签发日期" prop="issueDate" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.issueDate || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-for="item in tableHeader"
        :key="item.props"
        :prop="item.props"
        :label="item.label"
        align="center"
        show-overflow-tooltip
      >
        <el-table-column
          v-for="val in item.children"
          :key="val.props"
          :prop="val.props"
          :label="val.label"
          min-width="200px"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ row.expValues[val.props] || '--' }}
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <AddInspectionItems
      :show="showAdd"
      :data="alreadyList"
      :material-select-code="searchForm.mateType"
      :can-select-material="false"
      @close="closeDialog"
      @selectData="selectData"
    />
    <pagination :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getTableList" />
  </ListLayout>
</template>

<script>
import { reactive, ref, onMounted, toRefs, getCurrentInstance, onBeforeUnmount } from 'vue';
import Pagination from '@/components/Pagination';
import UserTag from '@/components/UserTag';
import ListLayout from '@/components/ListLayout';
import router from '@/router/index.js';
import { getList, getRowNumber, getExport } from '@/api/reportDataReport';
import AddInspectionItems from '@/components/BusinessComponents/AddInspectionItems';
import { getNameByid, getNamesByid, getPermissionBtn } from '@/utils/common';
import { useStore } from 'vuex';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { getPageRequestParam, setPageRequestParam } from '@/utils/auth';
import { getDictionary } from '@/api/user';

export default {
  name: 'ReportDataReport',
  components: { Pagination, ListLayout, UserTag, AddInspectionItems },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const store = useStore();
    const state = reactive({
      tableData: [],
      alreadyList: [],
      key: '', // 模糊查询关键字
      showAdd: false,
      total: 0,
      activeCollapse: '',
      itemProjects: {},
      activeCollapse2: '',
      expLoading: false, // 导出的loading
      strategyList: [], // 策略列表
      tableHeader: [], // 表格列
      showS: false,
      activeName: '0',
      uploadSearchForm: {
        capabilityIdList: []
      },
      submitTime: [],
      searchForm: {
        mateType: store.state.user.materialList[0]?.code || ''
      }, // 高级筛选的表单
      result: '-1', // 查询
      tableLoading: false, // 表格加载的loading
      isShowProduct: false, // 产品弹出框
      exportData: [], // 需要导出的数据
      rowNumberDetail: {},
      checkTreeId: '', // 选中的左侧树节点的id
      listQuery: {
        page: 1,
        limit: 20
      },
      totalNumber: {
        '-1': '',
        0: '',
        1: '',
        2: '',
        3: '',
        4: ''
      },
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ],
      activeIndex: '0',
      activeMoreIndex: null,
      tabsData: store.state.user.materialList,
      currentTabsData: store.state.user.materialList[0],
      tabsMoreData: [],
      statusList: {
        0: '不合适',
        1: '合适'
      }, // 类型
      moreIndex: 0
    });
    const tableKey = ref(0);
    const getRadioType = () => {
      getDictionary('JCXMSHJLB').then(res => {
        if (res) {
          state.statusList = [];
          res.data.data.dictionaryoption.forEach(val => {
            if (val.status === 1) {
              state.statusList[val.code] = val.name;
            }
          });
        }
      });
    };
    onBeforeUnmount(() => {});
    // 处理签发日期
    const handleSubmitTime = date => {
      state.searchForm.beginIssueDate = date ? formatDate(date[0]) : '';
      state.searchForm.endIssueDate = date ? formatDate(date[1]) : '';
    };
    // 切换物资分类
    const handleChangeMate = val => {
      state.currentTabsData = state.tabsData.filter(item => item.code === val)[0];
    };
    getRadioType();
    const changeStatus = val => {
      getTableList();
    };
    const reset = () => {
      state.listQuery.page = 1;
      state.listQuery.limit = 20;
      state.result = '-1';
      state.searchForm = {
        mateType: state.currentTabsData.code
      };
      state.submitTime = [];
      getTableList();
    };

    const handleSelectionChange = val => {};

    // 查询检测中的数据
    const checkRowNumber = row => {
      getRowNumber(row.sampleId).then(res => {
        state.rowNumberDetail = res.data.data;
      });
    };
    const clickArrow = () => {
      state.activeIndex = parseInt(state.activeIndex) + 1 + '';
    };
    const setQueryParams = () => {
      const params = {
        searchForm: state.searchForm,
        result: state.result,
        itemProjects: state.itemProjects
      };
      const localStorageParams = getPageRequestParam() ? JSON.parse(getPageRequestParam()) : {};
      localStorageParams['ReportDataReportList'] = params;
      setPageRequestParam(localStorageParams);
    };
    const getTableList = query => {
      var params = {
        ...state.searchForm,
        result: state.result === '-1' ? '' : state.result.toString()
      };
      params.capabilityIdList = Object.keys(state.itemProjects);
      state.uploadSearchForm = JSON.parse(JSON.stringify(params));
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        params.page = state.listQuery.page.toString();
        params.limit = state.listQuery.limit.toString();
      }
      state.tableLoading = true;
      if (state.searchForm.mateType) {
        getList(params).then(res => {
          setQueryParams();
          state.tableLoading = false;
          if (res) {
            const data = res.data.data;
            state.tableData = data.list;
            if (state.tableData.length > 0) {
              state.tableHeader = data.list[0].tableHeader;
            }
            state.total = data.totalCount;
          }
        });
      }
    };

    // 查看详情
    onMounted(() => {
      const localStorageParams = getPageRequestParam() ? JSON.parse(getPageRequestParam()) : {};
      const queryParamsSearch = localStorageParams['ReportDataReportList']?.searchForm || {
        mateType: store.state.user.materialList[0]?.code || ''
      };
      if (queryParamsSearch?.beginIssueDate) {
        state.submitTime = [queryParamsSearch?.beginIssueDate, queryParamsSearch?.endIssueDate];
      }
      state.searchForm = queryParamsSearch;
      state.result = localStorageParams['ReportDataReportList']?.result || '-1';
      state.itemProjects = localStorageParams['ReportDataReportList']?.itemProjects || {};
      getTableList();
    });
    // 高级搜索
    const advancedSearch = () => {
      state.showS = !state.showS;
      if (state.activeName === '0') {
        state.activeName = '1';
      } else {
        state.activeName = '0';
      }
    };
    // 导出
    const handleExport = () => {
      if (state.currentTabsData) {
        getExport(state.uploadSearchForm).then(res => {
          state.exportData = [];
          res.data.data.forEach((row, index) => {
            // 检验对象
            row.jydx = row.type === 1 ? row.inputWarehouseNo : row.productionOrderNo;
            // 对象位置
            row.dxwz = row.type === 1 ? row.wareHouseName : row.productionProcedure + ' ' + row.productionStation;
            // 对象名称
            row.dxmc = row.type === 1 ? row.supplierName : row.customerName;
            // 试验负责人
            row.ownName = row.ownerId ? getNameByid(row.ownerId) : row.ownerId;
            // 检验结果
            if (row.result === '0') {
              row.resultTitle = '合格';
            } else if (row.result === '1') {
              row.resultTitle = '不合格';
            } else {
              row.resultTitle = '不判定';
            }
            state.exportData.push({ ...row, ...row.expValues });
          });
          export2Excel();
          proxy.$message.success('导出成功！');
        });
      }
    };
    const export2Excel = () => {
      // state.expLoading = true
      const fileName = '样品项目报表';
      const cellFlag = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ];
      var header = [
        '样品编号',
        '报告编号',
        '样品名称',
        '型号规格',
        '物料编号',
        '批次',
        '盘号',
        '检验对象',
        '对象位置',
        '对象名称',
        '检验结果',
        '判定标准',
        '试验负责人',
        '签发日期'
      ];
      var multiHeader = [];
      var merges = [];
      const newLength = header.length;
      for (var i = 0; i < newLength; i++) {
        multiHeader.push('');
      }
      const filterVal = [
        'secSampleNum',
        'reportNo',
        'sampleName',
        'prodType',
        'materialNo',
        'batchNo',
        'reelNo',
        'jydx',
        'dxwz',
        'dxmc',
        'resultTitle',
        'standardProduct',
        'ownName',
        'issueDate'
      ];
      if (state.tableHeader.length > 0) {
        merges = [
          'A1:A2',
          'B1:B2',
          'C1:C2',
          'D1:D2',
          'E1:E2',
          'F1:F2',
          'G1:G2',
          'H1:H2',
          'I1:I2',
          'J1:J2',
          'K1:K2',
          'L1:L2',
          'M1:M2',
          'N1:N2'
        ]; // 合并单元格固定的几个
      }
      state.tableHeader.forEach(item => {
        header.push(item.label);
        var lastMerages = merges[merges.length - 1].split(':')[1]; // 最后一个N2
        var lastChart = ''; // 合并的最后一个的第一个标记例如'N'
        var lastChart2 = ''; // 合并的最后一个的第二个标记例如'2'
        var startIndex = 0;
        var firstStartIndex = 0;
        if (lastMerages.length === 2) {
          lastChart = lastMerages.charAt(0);
          lastChart2 = lastMerages.charAt(1);
          startIndex = cellFlag.findIndex(item => item === lastChart); // 出现的索引
        } else {
          lastChart = lastMerages.charAt(0);
          lastChart2 = lastMerages.charAt(1);
          firstStartIndex = cellFlag.findIndex(item => item === lastChart);
          startIndex = cellFlag.findIndex(item => item === lastChart2); // 出现的索引
        }
        var haveSurplus = 0;
        // // 如果剩余的不够子元素的一个循环
        haveSurplus = Number(startIndex) + Number(item.children.length) - Number(cellFlag.length - 1); // 多出来多少
        if (lastMerages.length === 2 && startIndex !== 25) {
          if (haveSurplus > 0) {
            merges.push(`${cellFlag[startIndex + 1]}1:A${cellFlag[haveSurplus - 1]}1`);
          } else {
            // 合并的A1-Z1
            merges.push(`${cellFlag[startIndex + 1]}1:${cellFlag[startIndex + item.children.length]}1`);
          }
        } else if (lastMerages.length === 2 && startIndex === 25) {
          merges.push(`AA1:A${cellFlag[haveSurplus - 1]}1`);
        } else if (lastMerages.length > 2) {
          var newFirstChart = cellFlag[firstStartIndex + 1];
          if (startIndex === 25) {
            merges.push(`${newFirstChart}A1:${newFirstChart}${cellFlag[haveSurplus - 1]}1`);
          } else {
            if (haveSurplus > 0) {
              merges.push(`${lastChart}${cellFlag[startIndex + 1]}1:${newFirstChart}${cellFlag[haveSurplus - 1]}1`);
            } else {
              merges.push(
                `${lastChart}${cellFlag[startIndex + 1]}1:${lastChart}${cellFlag[startIndex + item.children.length]}1`
              );
            }
          }
        }
        for (var i = 0; i < item.children.length; i++) {
          if (i < item.children.length - 1) {
            header.push('');
          }
          multiHeader.push(item.children[i].label);
          filterVal.push(item.children[i].props);
        }
      });
      import('@/utils/Export3Excel').then(excel => {
        const data = formatJson(filterVal, state.exportData);
        excel.export_json_to_excel({
          multiHeader,
          header,
          data,
          merges,
          filename: fileName,
          autoWidth: true,
          bookType: 'xlsx'
        });
        state.expLoading = false;
      });
    };
    const formatJson = (filterVal, jsonData) => {
      return jsonData.map(v =>
        filterVal.map(j => {
          return v[j];
        })
      );
    };
    // 样品、报告详情跳转
    const handleTz = (row, type) => {
      if (type === 'yp') {
        router.push({
          path: '/sample/report/detail',
          query: {
            orderId: row.orderId,
            sampleId: row.sampleId
          }
        });
      } else {
        router.push({
          path: '/sample/report/detail/report',
          query: {
            reportId: row.reportId,
            sampleId: row.sampleId,
            reportStage: 6
          }
        });
      }
    };
    // 选择检测项目
    const handleTestItem = () => {
      if (!state.searchForm.mateType) {
        proxy.$message.closeAll();
        proxy.$message.warning('请先选择物资分类');
      } else {
        state.showAdd = true;
        state.alreadyList = [];
        for (var key in state.itemProjects) {
          state.alreadyList.push({
            name: state.itemProjects[key],
            id: key
          });
        }
      }
    };
    const closeDialog = () => {
      state.showAdd = false;
    };
    const selectData = val => {
      state.showAdd = false;
      val.forEach(item => {
        state.itemProjects[item.capabilityId] = item.name;
      });
      getTableList();
    };
    const deleteItem = id => {
      delete state.itemProjects[id];
    };
    return {
      ...toRefs(state),
      handleTestItem,
      deleteItem,
      closeDialog,
      selectData,
      export2Excel,
      formatJson,
      getPermissionBtn,
      handleTz,
      handleChangeMate,
      handleSubmitTime,
      advancedSearch,
      handleExport,
      getRadioType,
      changeStatus,
      getTableList,
      formatDate,
      checkRowNumber,
      getNameByid,
      getNamesByid,
      drageHeader,
      clickArrow,
      handleSelectionChange,
      tableKey,
      reset
    };
  }
};
</script>
<style lang="scss" scoped>
.el-tag {
  margin-right: 10px;
}
:deep(.el-table--border th) {
  border-right: 1px solid #ebeef5 !important;
}
:deep(.el-table--border td) {
  border-right: 1px solid #ebeef5 !important;
}
.searchInput {
  display: flex;
  .el-input {
    width: 360px;
    margin-right: 10px;
  }
}
:deep(.el-table.data-table.format-height-table) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 388px);
  }
}
</style>
