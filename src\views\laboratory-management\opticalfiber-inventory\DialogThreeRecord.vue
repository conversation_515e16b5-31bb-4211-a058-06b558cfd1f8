<template>
  <el-dialog v-model="dialogShow" title="光纤记录" :close-on-click-modal="true" width="1180px" @close="handleClose">
    <el-timeline v-if="tableData.length" class="opticalRecording">
      <el-timeline-item v-for="item in tableData" :key="item.id" placement="top" center>
        <div class="content">
          <div class="top">
            <span class="type">{{ typeJson[item.operationType] }}</span>
            <span class="time">{{ item.createTime }}</span>
          </div>
          <el-row>
            <el-col :span="8"
              >{{ typeJson[item.operationType] }}日期：<span class="value">{{
                item.operationDate || '--'
              }}</span></el-col
            >
            <el-col :span="8"
              >{{ typeJson[item.operationType] }}人：<span class="value">{{
                item.operationWorker || '--'
              }}</span></el-col
            >
            <el-col v-if="item.operationType === 'COLOR'" :span="8"
              >机台：<span class="value">{{ item.machinePlatform || '--' }}</span></el-col
            >
            <el-col v-if="item.operationType === 'COLOR'" :span="8"
              >着色纤色谱：<span class="value">{{ item.chromatography || '--' }}</span></el-col
            >
            <el-col :span="8"
              >长度：<span class="value">{{ item.actualLength || '--' }}</span></el-col
            >
            <el-col :span="8"
              >测试衰减1 LOSS1310：<span class="value">{{ item.inspectionAttenuation1 || '--' }}</span></el-col
            >
            <el-col :span="8"
              >测试衰减2 LOSS1550：<span class="value">{{ item.inspectionAttenuation2 || '--' }}</span></el-col
            >
          </el-row>
        </div>
      </el-timeline-item>
    </el-timeline>
    <div v-else style="width: 100%; text-align: center">
      <img src="@/assets/img/empty-data.png" alt="" />
      <div style="line-height: 24px; margin-bottom: 150px">暂无数据</div>
    </div>
  </el-dialog>
</template>
<script>
import { reactive, toRefs, watch, ref } from 'vue';
import { colWidth } from '@/data/tableStyle';
// import UserTag from '@/components/UserTag'
import { drageHeader } from '@/utils/formatTable';
import { formatDate } from '@/utils/formatTime';
import { operationRecordList } from '@/api/opticalfiberInventory';
import { getNameByid } from '@/utils/common';

export default {
  name: 'DialogThreeRecord',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    status: {
      type: String,
      default: ''
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    const state = reactive({
      tableLoading: false, // 弹出窗loading
      tableRef: ref(),
      tableData: [],
      rowDetail: {},
      dialogShow: false,
      listQuery: {
        page: 1,
        limit: 20
      },
      typeJson: {
        COLOR: '着色',
        SWITCH_REEL: '倒盘',
        RETURN_WAREHOUSE: '回仓'
      }
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.rowDetail = props.detailData;
        state.tableData = [];
        console.log();
        getTableList();
      }
    });
    const getTableList = query => {
      state.tableLoading = true;
      const paramsId = props.status === 'DR' ? state.rowDetail.id : state.rowDetail.mainDataId;
      operationRecordList(paramsId).then(res => {
        state.tableLoading = false;
        if (res) {
          state.tableData = res.data.data;
        }
      });
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog', { isRefresh: false });
    };
    return {
      ...toRefs(state),
      getTableList,
      formatDate,
      getNameByid,
      handleClose,
      colWidth,
      drageHeader
    };
  }
};
</script>
<style lang="scss" scoped>
.opticalRecording {
  height: 600px;
  overflow-y: auto;
  padding: 0 20px 0 20px;
  margin-bottom: 20px;
}
.content {
  line-height: 24px;
  background-color: #f4f4f5;
  border-radius: 5px;
  padding: 12px;
}

:deep(.el-timeline-item__node) {
  background-color: $tes-primary;
}
:deep(.el-timeline-item__tail) {
  border-left-color: $tes-primary1;
}
.top {
  position: absolute;
  top: 0;
  left: 28px;
  .type {
    color: $tes-primary;
    margin-right: 10px;
  }
  .time {
    color: #9d9d9d;
    font-size: 13px;
  }
}
:deep(.el-timeline-item__timestamp.is-top) {
  margin-bottom: 20px;
}
</style>
