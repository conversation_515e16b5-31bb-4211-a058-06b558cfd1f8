<template>
  <!-- 消息模板 -->
  <ListLayout :has-quick-query="false" :has-button-group="true">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="code">
          <el-input
            v-model="formInline.condition"
            v-trim
            v-focus
            placeholder="请输入消息编号/标题/消息模板"
            class="ipt-360"
            prefix-icon="el-icon-search"
            size="large"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="handleSearch">查询</el-button>
          <el-button size="large" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('addFixedView')"
        type="primary"
        icon="el-icon-plus"
        size="large"
        @click="handleAddMessage"
        @keyup.prevent
        @keydown.enter.prevent
      >
        新增消息模板
      </el-button>
    </template>
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      size="medium"
      fit
      border
      height="auto"
      highlight-current-row
      style="width: auto"
      class="dark-table base-table format-height-table no-quick-query"
      @header-dragend="drageHeader"
    >
      <el-table-column label="消息分组" prop="messageGroupKey" />
      <el-table-column label="事件编号" prop="eventCode" />
      <el-table-column label="触发事件" prop="eventName" width="200" />
      <el-table-column label="消息类型" prop="templateType" align="center">
        <template #default="{ row }">{{ row.templateType === 1 ? '通知' : '代办' }}</template>
      </el-table-column>
      <el-table-column label="消息标题" prop="messageTitle" width="200" />
      <el-table-column label="消息模板" prop="messageContent" width="500" />
      <el-table-column label="跳转地址" prop="forwardUri" width="240" />
      <el-table-column label="消息状态" prop="status" align="center">
        <template #default="{ row }">{{ row.status === 1 ? '启用' : '禁用' }}</template>
      </el-table-column>
      <el-table-column label="接收对象" prop="targetType" align="center">
        <template #default="{ row }">{{ row.targetType === 1 ? '个人' : '群组' }}</template>
      </el-table-column>
      <el-table-column label="操作" width="60" fixed="right" class-name="fixed-right">
        <template #default="{ row }">
          <span class="blue-color" @click="handleEditMessage(row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-show="total > 0"
      :page="pagination.page"
      :limit="pagination.limit"
      :total="total"
      @pagination="onPagination"
    />
    <template #other>
      <el-dialog
        v-model="visible"
        title="编辑"
        :close-on-click-modal="false"
        width="600px"
        custom-class="submit_dialog"
        @opened="onDialogOpened"
      >
        <el-form ref="editFormRef" :model="formData" :rules="editFormRules" label-position="top" label-width="110px">
          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="消息类型：" prop="templateType">
                <el-radio-group v-model="formData.templateType">
                  <el-radio :label="1">通知</el-radio>
                  <el-radio :label="2">代办</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="消息状态：" prop="status">
                <el-radio-group v-model="formData.status">
                  <el-radio :label="1">启用</el-radio>
                  <el-radio :label="0">停用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="消息分组：" prop="messageGroupKey">
                <el-select v-model="formData.messageGroupKey" size="small" style="width: 100%">
                  <el-option
                    v-for="item in messageGroupList"
                    :key="item.id"
                    :label="item.groupKey"
                    :value="item.groupKey"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="消息标题：" prop="messageTitle">
                <el-input v-model="formData.messageTitle" v-trim size="small" placeholder="请输入消息标题" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="事件编号：" prop="eventCode">
                <el-input v-model="formData.eventCode" v-trim size="small" placeholder="请输入事件编号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="触发事件：" prop="eventName">
                <el-input v-model="formData.eventName" v-trim size="small" placeholder="请输入触发事件" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="跳转地址：" prop="forwardUri">
                <el-input v-model="formData.forwardUri" v-trim size="small" placeholder="请输入跳转地址" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="消息模板：" prop="messageContent">
                <el-input
                  v-model="formData.messageContent"
                  type="textarea"
                  :rows="2"
                  size="small"
                  placeholder="请输入消息模板"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="接收对象：" prop="targetType">
                <el-radio-group v-model="formData.targetType">
                  <el-radio :label="1">个人</el-radio>
                  <el-radio :label="0">群组</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="handleDialogCancel">取 消</el-button>
            <el-button :loading="saving" type="primary" @click="handleDialogSave" @keyup.prevent @keydown.enter.prevent>
              保 存
            </el-button>
          </span>
        </template>
      </el-dialog>
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, onMounted } from 'vue';
import { queryMessageTemplateList, saveMessageTemplate, queryMessageGroupList } from '@/api/messageTemplates';
import { getLoginInfo } from '@/utils/auth';
import { getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import Pagination from '@/components/Pagination';
import ListLayout from '@/components/ListLayout';

export default {
  name: 'MessageTemplates',
  components: { ListLayout, Pagination },
  setup() {
    const dialogTypeEnum = {
      Add: 'add',
      Edit: 'edit'
    };

    const state = reactive({
      userId: getLoginInfo().accountId,
      formInline: {
        condition: '' // 搜索的关键字
      },
      tableData: [],
      tableLoading: false,
      total: 1,
      pagination: {
        limit: 20,
        page: 1
      },
      currentRowData: {},
      visible: false,
      dialogType: dialogTypeEnum.Add,
      formData: {
        templateType: 1,
        status: 1,
        messageGroupKey: '',
        messageTitle: '',
        eventCode: '',
        eventName: '',
        forwardUri: '',
        messageContent: '',
        targetType: 1
      },
      messageGroupList: [],
      saving: false
    });

    const editFormRef = ref();

    const editFormRules = {
      templateType: { required: true, message: '请选择消息类型', trigger: 'change' },
      messageGroupKey: { required: true, message: '请选择消息分组', trigger: 'change' },
      eventCode: { required: true, message: '请输入事件编号', trigger: 'change' },
      eventName: { required: true, message: '请输入触发事件', trigger: 'change' },
      messageTitle: { required: true, message: '请输入消息标题', trigger: 'change' },
      messageContent: { required: true, message: '请输入消息模板', trigger: 'change' },
      targetType: { required: true, message: '请选择接收对象', trigger: 'change' }
    };

    onMounted(() => {
      getList();
    });

    const getList = async query => {
      const params = {
        condition: state.formInline.condition.trim(),
        page: query?.page?.toString() || state.pagination.page.toString(),
        limit: query?.limit?.toString() || state.pagination.limit.toString()
      };
      state.tableLoading = true;
      const res = await queryMessageTemplateList(params);
      state.tableLoading = false;
      if (res) {
        const data = res.data.data;
        state.pagination.page = data.currPage;
        state.pagination.limit = data.pageSize;
        state.total = data.totalCount;
        state.tableData = data.list;
      }
    };

    const getGroupList = async () => {
      if (state.messageGroupList.length === 0) {
        const res = await queryMessageGroupList();
        if (res) {
          state.messageGroupList = res.data.data;
        }
      }
    };

    const saveData = async () => {
      await editFormRef.value.validate();
      const params = {
        ...state.formData,
        messageGroupId: state.messageGroupList.find(item => item.groupKey === state.formData.messageGroupKey)?.id,
        targetObjectId: state.formData.targetType === 1 ? 'ownerId' : '1'
      };
      if (state.dialogType === dialogTypeEnum.Edit && state.currentRowData.id) {
        params.id = state.currentRowData.id;
      }
      state.saving = true;
      const res = await saveMessageTemplate(params);
      state.saving = false;
      if (res && res.data.code === 200) {
        handleDialogCancel();
        getList();
      }
    };

    const setFormData = data => {
      state.formData.templateType = data.templateType ?? 1;
      state.formData.status = data.status ?? 1;
      state.formData.messageGroupKey = data.messageGroupKey ?? '';
      state.formData.messageTitle = data.messageTitle ?? '';
      state.formData.eventCode = data.eventCode ?? '';
      state.formData.eventName = data.eventName ?? '';
      state.formData.forwardUri = data.forwardUri ?? '';
      state.formData.messageContent = data.messageContent ?? '';
      state.formData.targetType = data.targetType ?? 1;
    };

    const handleSearch = () => {
      getList();
    };

    const handleReset = () => {
      state.formInline.condition = '';
      state.pagination = {
        limit: 20,
        page: 1
      };
      getList();
    };

    const handleAddMessage = () => {
      state.dialogType = dialogTypeEnum.Add;
      state.visible = true;
    };

    const handleEditMessage = row => {
      state.dialogType = dialogTypeEnum.Edit;
      getGroupList();
      state.visible = true;
      state.currentRowData = row;
    };

    const onPagination = query => {
      getList(query);
    };

    const onDialogOpened = () => {
      switch (state.dialogType) {
        case dialogTypeEnum.Add: {
          editFormRef.value.resetFields();
          break;
        }

        case dialogTypeEnum.Edit: {
          setFormData(state.currentRowData);
          break;
        }
      }
    };

    const handleDialogCancel = () => {
      state.visible = false;
    };

    const handleDialogSave = () => {
      saveData();
    };

    return {
      getPermissionBtn,
      drageHeader,
      ...toRefs(state),
      editFormRef,
      editFormRules,
      handleSearch,
      handleReset,
      handleAddMessage,
      handleEditMessage,
      onPagination,
      onDialogOpened,
      handleDialogCancel,
      handleDialogSave
    };
  }
};
</script>
<style lang="scss" scoped>
:deep(.format-height-table.el-table .el-table__body-wrapper) {
  max-height: calc(100vh - 270px);
}
</style>
