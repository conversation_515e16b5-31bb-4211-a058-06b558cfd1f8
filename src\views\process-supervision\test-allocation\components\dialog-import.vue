<template>
  <el-dialog v-model="dialogShow" title="数据导入" :close-on-click-modal="false" width="680px" @close="handleClose">
    <div class="title">为确保数据导入的准确性，请按照以下规则进行导入</div>
    <div class="bt">下载表单模板</div>
    <ul class="uploadRules">
      <li>
        请按照<span class="blue-color" @click="downLoadFile('样品导入模板.xlsx')">样品导入模板.xlsx</span
        >在模板内录入数据
      </li>
      <li>只导入第一张工作表（sheet1）</li>
      <li>请勿修改表格标题，防止导入失败;</li>
      <li>导入模板中标红的字段为必填字段;</li>
      <li>”批次“如不清楚，可按照时间填值，方便后续查询</li>
    </ul>
    <el-row v-if="dialogShow">
      <el-col :span="12">
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="headerconfig"
          :auto-upload="false"
          :limit="1"
          :accept="fileAcceptExcel"
          :data="{ code: code }"
          :on-exceed="handleExceed"
          :before-upload="beforeUpload"
          :on-success="handleFileSuccess"
        >
          <el-button size="small" type="primary" plain>选择上传文件</el-button>
        </el-upload>
      </el-col>
      <el-col :span="12">
        <el-select v-model="code" placeholder="请选择物资分类" size="small" style="width: 100%">
          <el-option v-for="item in materialList" :key="item.code" :label="item.name" :value="item.code" />
        </el-select>
      </el-col>
    </el-row>
    <ul class="uploadRules">
      <li>请上传*.xls，*.xlsx格式文件；</li>
      <li>目前一次性最多上传5000条数据；</li>
      <li>文件大小不超过10M；</li>
    </ul>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="dialogShow = false">取 消</el-button>
        <el-button type="primary" @click="submitUpload">确认上传</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, ref, toRefs, watch, getCurrentInstance } from 'vue';
import { getToken } from '@/utils/auth';
import { useStore } from 'vuex';
import { distributionImportUploadUrl } from '@/api/uploadAction';
import { fileAcceptExcel } from '@/utils/fileAccept';
export default {
  name: 'DialogImport',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const state = reactive({
      dialogLoading: false, // 弹出窗loading
      rowDetail: {},
      uploadAction: distributionImportUploadUrl(),
      headerconfig: {
        Authorization: getToken()
      },
      uploadRef: ref(),
      materialList: store.user.materialList,
      costTypeList: [],
      dialogShow: false,
      ruleForm: ref(),
      code: '',
      listLoading: false
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.rowDetail = props.detailData;
        if (store.user.materialList.length) {
          state.code = store.user.materialList[0].code;
        }
      }
    });
    const handleExceed = files => {
      state.uploadRef.clearFiles(['success', 'fail', 'ready']);
      state.uploadRef.handleStart(files[0]);
    };
    // 上传成功的钩子
    const handleFileSuccess = (res, file) => {
      if (res.code === 200) {
        proxy.$message.success(res.message);
        state.dialogShow = false;
        context.emit('closeDialog', true);
      } else {
        proxy.$message.error(res.message);
      }
    };
    // 上传文件的限制
    const beforeUpload = file => {
      var fileName = '';
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过10M');
        return false;
      } else if (fileName !== 'xls' && fileName !== 'xlsx') {
        proxy.$message.error('仅支持.xls，.xlsx文件扩展名');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };
    const submitUpload = () => {
      if (state.code) {
        state.uploadRef.submit();
      } else {
        proxy.$message.error('请选择物资分类');
      }
    };
    // 下载附件
    const downLoadFile = fileName => {
      const a = document.createElement('a');
      a.href = '/staticFile/' + fileName;
      a.download = fileName;
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      a.remove();
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog', false);
    };
    return {
      ...toRefs(state),
      handleExceed,
      downLoadFile,
      beforeUpload,
      handleFileSuccess,
      submitUpload,
      handleClose,
      fileAcceptExcel
    };
  }
};
</script>
<style lang="scss" scoped>
.uploadRules {
  padding: 5px 10px;
  background: #f0f2f5;
  margin: 14px 0;
  li {
    list-style: none;
    line-height: 22px;
    font-size: 13px;
  }
  .blue-color {
    margin-right: 0;
  }
}
.title {
  background-color: #f0f2f5;
  line-height: 30px;
  padding: 0 10px;
  margin-bottom: 15px;
}
.bt {
  font-size: 15px;
  line-height: 16px;
  font-weight: bold;
  // padding: 0 10px;
}
</style>
