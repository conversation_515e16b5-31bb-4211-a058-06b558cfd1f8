import request from '@/utils/request';
import { getGWURL } from '@/utils/base-url';

var baseUrl = getGWURL();

const Headers = {
  'Content-Type': 'application/json; charset=utf-8'
};

// 查按钮权限
// export function checkPermissionBtnApi(params) {
//   return request({
//     // url: baseUrl + `/user/Permission/Check/?id=${params.btnName}&type=${params.type}`,
//     url: `/api-user/user/sysuser/users/current`,
//     method: 'get',
//     headers: Headers
//   })
// }
export function checkPermissionBtnApi() {
  return request({
    // url: baseUrl + `/user/Permission/Check/?id=${params.btnName}&type=${params.type}`,
    url: `/api-user/user/sysmenu/buttonortext`,
    method: 'get',
    headers: Headers
  });
}
// 试验负责人
export function checkPermissionList(id) {
  return request({
    // url: baseUrl + `/user/Permission/Check/?id=${params.btnName}&type=${params.type}`,
    url: `/api-user/user/sysuser/users/findbypermission`,
    method: 'post',
    headers: Headers,
    data: {
      menuId: id
    }
  });
}
// 资源控制
export function reportAudit(key) {
  return request({
    // url: baseUrl + `/user/Permission/Check/?id=${params.btnName}&type=${params.type}`,
    url: `api-user/user/sysuser/users/findbypermission`,
    method: 'post',
    headers: Headers,
    data: {
      key: key
    }
  });
}

// 获取页面权限  http://192.168.69.58:9100/user/Permission/Check?id=sampleAssignListAssignBatch&type=3
export function checkPagePermission(id) {
  const param = {
    id: id,
    type: 3
  };
  return request({
    url: baseUrl + `/user/Permission/Check`,
    method: 'get',
    headers: Headers,
    params: param
  });
}

export function checkConfigJSON() {
  return request({
    url: `${window.location.origin}/serverConfig.json`,
    method: 'get'
  });
}
