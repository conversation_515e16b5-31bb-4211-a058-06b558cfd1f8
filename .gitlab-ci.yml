variables:
  MODULE_NAME: cxist-tes
  IMAGE_NAME: web-cxist-tes
  REGISTRY_HOST: hub-internal.cxist.com:5000/
  IMAGE_PREFIX: byzan/xlab
  DEPLOY_DIR_BASE: byzan-deploy/web-cxist-tes
  DEPLOY_TARGET: root@*************
  DEPLOY_TARGET_PORT: '22'
  STACK_NAME: cx-main

stages:
  - prepare
  - lint
  - build_src
  - build_image
  - deploy

prepare:
  stage: prepare
  rules:
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
  tags:
    - node-18
  script:
    - node --version

cache: &global_cache
  key:
    files:
      - package-lock.json
  paths:
    - node_modules/
  policy: pull-push

lint:
  stage: lint
  interruptible: true
  rules:
    - if: '$CI_COMMIT_BRANCH == "hans/ci"'
    - if: '$CI_COMMIT_BRANCH == "dev"'
    - if: '$CI_COMMIT_BRANCH == "test"'
    - if: '$CI_COMMIT_BRANCH == "release"'
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: never
  tags:
    - node-18
  cache:
    <<: *global_cache
  # 使用 before_script 来准备环境
  before_script:
    # -f = 强制, -d = 包含目录, -x = 包含被 .gitignore 忽略的文件
    # 这是确保工作区绝对干净的最强力方法
    - git clean -fdx
  script:
    - node --version
    - npm --version
    - npm cache clear --force
    - npm install --no-audit --unsafe-perm --allow-root --loglevel=verbose
    - npm run lint

build_src:
  stage: build_src
  interruptible: true
  rules:
    - if: '$CI_COMMIT_BRANCH == "hans/ci"'
    - if: '$CI_COMMIT_BRANCH == "dev"'
    - if: '$CI_COMMIT_BRANCH == "test"'
    - if: '$CI_COMMIT_BRANCH == "release"'
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: $CI_COMMIT_TAG
  tags:
    - node-18
  cache:
    <<: *global_cache
  script:
    - npm run build
    - cp -r ./tools/CI/* ./dist/
    - du -sh ./dist/*
  artifacts:
    name: 'application-lib-$CI_COMMIT_REF_SLUG-$CI_PIPELINE_ID'
    paths:
      - ./dist
  dependencies:
    - lint

build_image:
  stage: build_image
  interruptible: true
  rules:
    - if: '$CI_COMMIT_BRANCH == "hans/ci"'
    - if: '$CI_COMMIT_BRANCH == "dev"'
    - if: '$CI_COMMIT_BRANCH == "test"'
    - if: '$CI_COMMIT_BRANCH == "release"'
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: $CI_COMMIT_TAG
  tags:
    - docker
  variables:
    BASE_IMAGE: byzan/nginx:byz-nightly
    IMAGE_TAG: test
    PUSH_LATEST: 'false'
  script:
    - du -sh ./dist/*
    - if [ $CI_COMMIT_REF_NAME == dev ]; then IMAGE_TAG=dev; DEPLOY_TARGET=root@*************; fi
    - if [ $CI_COMMIT_REF_NAME == test ]; then IMAGE_TAG=test; DEPLOY_TARGET=root@**************; fi
    - if [ $CI_COMMIT_REF_NAME == release ]; then IMAGE_TAG=release; DEPLOY_TARGET=root@**************; fi
    - if [ $CI_COMMIT_REF_NAME == master ]; then BASE_IMAGE=byzan/nginx:byz-prod; IMAGE_TAG=prod; PUSH_LATEST=true; fi
    - IMAGE_FULL_NAME=$IMAGE_PREFIX/$IMAGE_NAME
    - bash ./dist/build-image.sh --base-image $BASE_IMAGE --image-name $IMAGE_FULL_NAME --env $IMAGE_TAG --ci-commit-sha $CI_COMMIT_SHA --ci-pipeline-id $CI_PIPELINE_ID --registry "$REGISTRY_HOST"
    - bash ./dist/push-image.sh --name $IMAGE_FULL_NAME --tag $IMAGE_TAG --latest $PUSH_LATEST --registry "$REGISTRY_HOST"
  dependencies:
    - build_src

deploy_dev:
  stage: deploy
  tags:
    - node-18
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
  variables:
    GIT_STRATEGY: none
    IMAGE_TAG: dev-$CI_PIPELINE_ID
    TARGET_PROD: 'false'
    STACK_NAME: $STACK_NAME
    API_GATEWAY: http://*************:8800
  script:
    - if [ $CI_COMMIT_REF_NAME == dev ]; then IMAGE_TAG=dev-$CI_PIPELINE_ID; API_GATEWAY=http://*************:8800; DEPLOY_TARGET=root@*************; fi
    - ssh -q -p $DEPLOY_TARGET_PORT $DEPLOY_TARGET "mkdir -p ~/$DEPLOY_DIR_BASE/$IMAGE_TAG;rm -rf ~/$DEPLOY_DIR_BASE/$IMAGE_TAG/*"
    - scp -q -P $DEPLOY_TARGET_PORT ./tools/CI/deploy.sh ./tools/CI/stack.yml $DEPLOY_TARGET:~/$DEPLOY_DIR_BASE/$IMAGE_TAG
    - ssh -p $DEPLOY_TARGET_PORT $DEPLOY_TARGET "bash $DEPLOY_DIR_BASE/$IMAGE_TAG/deploy.sh --registry \"$REGISTRY_HOST\" --name $IMAGE_NAME --tag $IMAGE_TAG --stack $STACK_NAME"
  environment:
    name: dev
  dependencies: []

deploy_test:
  stage: deploy
  tags:
    - node-18
  rules:
    - if: '$CI_COMMIT_BRANCH == "test"'
    - if: '$CI_COMMIT_BRANCH == "release"'
    - if: '$CI_COMMIT_BRANCH == "master"'
  variables:
    GIT_STRATEGY: none
    IMAGE_TAG: test-$CI_PIPELINE_ID
    TARGET_PROD: 'false'
    STACK_NAME: $STACK_NAME
    API_GATEWAY: http://**************:8800
  script:
    - if [ $CI_COMMIT_REF_NAME == test ]; then IMAGE_TAG=test-$CI_PIPELINE_ID; API_GATEWAY=http://**************:8800; DEPLOY_TARGET=root@**************; fi
    - if [ $CI_COMMIT_REF_NAME == release ]; then IMAGE_TAG=release-$CI_PIPELINE_ID; API_GATEWAY=http://**************:8800; DEPLOY_TARGET=root@**************; fi
    - if [ $CI_COMMIT_REF_NAME == master ]; then IMAGE_TAG=prod; TARGET_PROD=true; fi
    - ssh -q -p $DEPLOY_TARGET_PORT $DEPLOY_TARGET "mkdir -p ~/$DEPLOY_DIR_BASE/$IMAGE_TAG;rm -rf ~/$DEPLOY_DIR_BASE/$IMAGE_TAG/*"
    - scp -q -P $DEPLOY_TARGET_PORT ./tools/CI/deploy.sh ./tools/CI/stack.yml $DEPLOY_TARGET:~/$DEPLOY_DIR_BASE/$IMAGE_TAG
    - ssh -p $DEPLOY_TARGET_PORT $DEPLOY_TARGET "bash $DEPLOY_DIR_BASE/$IMAGE_TAG/deploy.sh --registry \"$REGISTRY_HOST\" --name $IMAGE_NAME --tag $IMAGE_TAG --prod $TARGET_PROD --api-gateway $API_GATEWAY --stack $STACK_NAME"
  environment:
    name: test
  dependencies: []
