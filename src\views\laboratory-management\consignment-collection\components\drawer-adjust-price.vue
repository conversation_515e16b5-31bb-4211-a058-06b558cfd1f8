<!-- 委托调价 -->
<template>
  <el-drawer
    v-model="drawerShow"
    title="委托调价"
    direction="rtl"
    :before-close="handleClose"
    size="88%"
    destroy-on-close
    custom-class="page-drawer"
  >
    <DrawerLayout v-loading="drawerLoading" :has-left-panel="false" :main-offset-top="53" :has-button-group="false">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="ruleInfo"
        class="form-height-auto"
        label-position="right"
        size="small"
        label-width="110px"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item prop="adjustNo" label="调价单编号：">
              <span>{{ formData.adjustNo || '保存后生成编号' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="standardName" label="调价日期：">
              <span>{{ formatDate(new Date()) }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="payerName" label="付款方：">
              <span>{{ formData.payerName || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="调价人：">
              <UserTag :name="getNameByid(getLoginInfo().accountId) || '--'" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="receivableCost" label="应收金额(￥)：">
              <span>{{ formData.receivableCost || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="discountType" label="折扣方式：">
              <el-row :gutter="10">
                <el-col :span="8">
                  <el-select
                    v-model="formData.discountType"
                    placeholder="请选择折扣方式"
                    clearable
                    @change="handleChangeDiscount"
                  >
                    <el-option label="无折扣" value="" />
                    <el-option label="等额折扣" value="amount" />
                    <el-option label="等比折扣" value="percentage" />
                  </el-select>
                </el-col>
                <el-col v-if="formData.discountType" :span="8">
                  <el-input-number
                    v-if="formData.discountType === 'amount'"
                    v-model="formData.adjustTypeValue"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                    @change="computeAfterDiscount"
                  />
                  <el-input-number
                    v-if="formData.discountType === 'percentage'"
                    v-model="formData.adjustTypeValue"
                    :min="0"
                    :max="1"
                    :step="0.1"
                    controls-position="right"
                    style="width: 100%"
                    @change="computeAfterDiscount"
                  />
                </el-col>
                <el-col :span="8">
                  {{ discountTitleJSON[formData.discountType] || '当前无折扣' }}
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="btnGroup text-right">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd()">合并调价</el-button>
        </el-row>
        <el-table
          ref="tableRef"
          :data="formData.itemList"
          class="dark-table base-table format-height-table2"
          fit
          border
          size="medium"
          height="auto"
          style="margin-top: 5px"
          @header-dragend="drageHeader"
        >
          <el-table-column label="序号" type="index" :min-width="colWidth.serialNo" />
          <el-table-column label="委托单号" prop="entrustNo" :min-width="colWidth.orderNo">
            <template #default="{ row }">
              {{ row.entrustNo }}
            </template>
          </el-table-column>
          <el-table-column label="委托方" prop="entrustName" :min-width="colWidth.orderNo">
            <template #default="{ row }">
              {{ row.entrustName }}
            </template>
          </el-table-column>
          <el-table-column label="应收金额(￥)" prop="receivableCost" :min-width="colWidth.money">
            <template #default="{ row }">
              {{ row.receivableCost }}
            </template>
          </el-table-column>
          <el-table-column label="已收金额(￥)" prop="paidCost" :min-width="colWidth.money">
            <template #default="{ row }">
              {{ row.paidCost }}
            </template>
          </el-table-column>
          <el-table-column label="折扣后金额(￥)" prop="afterDiscount" :min-width="colWidth.money">
            <template #default="{ row, $index }">
              <el-form-item
                :prop="`itemList.${$index}.afterDiscount`"
                style="margin-bottom: 0"
                label-width="0"
                :rules="{
                  validator: discountCount,
                  validData: { paidCost: row.paidCost, receivableCost: row.receivableCost },
                  trigger: 'change'
                }"
              >
                <el-input-number
                  v-model="row.afterDiscount"
                  :min="0"
                  controls-position="right"
                  placeholder="请输入折扣后金额"
                  style="width: 100%"
                />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column v-if="formData.itemList.length > 1" label="操作" :min-width="colWidth.operation">
            <template #default="{ $index }">
              <span class="blue-color" @click="handleDelete($index)">删除</span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="drawer-fotter">
        <el-button type="primary" :loading="drawerLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确认</el-button
        >
        <el-button :loading="drawerLoading" @click="handleClose">取消</el-button>
      </div>
    </DrawerLayout>
  </el-drawer>
  <!-- 合并收款 -->
  <DialogSelectOrder
    :dialog-visible="dialogVisible"
    :detail-info="detailInfo"
    :already-select="formData.itemList"
    @closeDialog="handleSelectData"
  />
</template>
<script>
// getCurrentInstance
import { reactive, ref, toRefs, watch, getCurrentInstance } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import DrawerLayout from '@/components/DrawerLayout';
import { isNumberNot } from '@/utils/validate';
import UserTag from '@/components/UserTag';
import { getLoginInfo } from '@/utils/auth';
import { getNameByid } from '@/utils/common';
import { publicSubtract, publicMultiply } from '@/utils/calculatePrecision';
import { discountCount } from '@/utils/validate';
import DialogSelectOrder from './dialog-select-order.vue';
import { entrustCostAdjustSave } from '@/api/consignmentCollection';

export default {
  name: 'DrawerAdjustPrice',
  components: { DialogSelectOrder, DrawerLayout, UserTag },
  props: {
    drawerVisible: {
      type: Boolean,
      default: false
    },
    detailInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  emits: ['closeDrawer'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      formData: {
        itemList: []
      },
      formRef: ref(),
      type: '',
      dialogVisible: false,
      discountTitleJSON: {
        amount: '每单按相同金额优惠',
        percentage: '每单按相同折扣优惠'
      },
      drawerLoading: false,
      ruleInfo: {},
      detailInfo: {},
      drawerShow: false,
      ruleForm: ref(),
      loading: false,
      tableRef: ref(null)
    });
    watch(props, newValue => {
      state.drawerShow = newValue.drawerVisible;
      if (state.drawerShow) {
        state.detailInfo = props.detailInfo;
        state.formData = {
          itemList: [
            {
              entrustNo: props.detailInfo.entrustNo,
              entrustName: props.detailInfo.entrustName,
              receivableCost: props.detailInfo.entrustCost,
              paidCost: props.detailInfo.paidCost,
              entrustId: props.detailInfo.id
            }
          ],
          payerName: props.detailInfo.payerName,
          receivableCost: props.detailInfo.entrustCost,
          status: 1,
          discountType: ''
        };
      }
    });
    const handleAdd = () => {
      state.dialogVisible = true;
    };
    const getDetailInfo = () => {};
    const onSubmit = () => {
      proxy.$refs['formRef']
        .validate()
        .then(valid => {
          if (valid) {
            state.drawerLoading = true;
            entrustCostAdjustSave(state.formData).then(res => {
              state.drawerLoading = false;
              if (res) {
                proxy.$message.success('调价成功！');
                context.emit('closeDrawer', true);
              }
            });
          }
        })
        .catch(error => {
          const fieldName = Object.keys(error)[0];
          proxy.$message.warning(error[fieldName][0].message);
          return false;
        });
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDrawer');
    };
    // 删除
    const handleDelete = index => {
      state.formData.itemList.splice(index, 1);
    };
    const handleSelectData = val => {
      const addItem = [];
      if (val.length) {
        val.forEach(item => {
          addItem.push({
            ...item,
            receivableCost: item.entrustCost,
            entrustId: item.id,
            paidCost: item.paidCost
          });
        });
      }
      state.formData.itemList = [...state.formData.itemList, ...addItem];
      state.dialogVisible = false;
    };
    // 切换折扣方式
    const handleChangeDiscount = val => {
      if (val === 'amount') {
        state.formData.adjustTypeValue = 0;
      } else if (val === 'percentage') {
        state.formData.adjustTypeValue = 1;
      } else {
        state.formData.adjustTypeValue = '';
      }
      computeAfterDiscount();
    };
    //  计算折扣后金额
    const computeAfterDiscount = () => {
      if (state.formData.discountType === 'amount') {
        // 等额折扣
        state.formData.itemList.forEach(item => {
          item.afterDiscount = publicSubtract(item.receivableCost, state.formData.adjustTypeValue);
          item.discountCost = publicSubtract(item.receivableCost, item.afterDiscount);
        });
      } else if (state.formData.discountType === 'percentage') {
        // 等比折扣
        state.formData.itemList.forEach(item => {
          item.afterDiscount = publicMultiply(item.receivableCost, state.formData.adjustTypeValue);
          item.discountCost = publicSubtract(item.receivableCost, item.afterDiscount);
        });
      }
    };
    return {
      ...toRefs(state),
      computeAfterDiscount,
      discountCount,
      handleChangeDiscount,
      getNameByid,
      isNumberNot,
      getLoginInfo,
      handleSelectData,
      handleDelete,
      handleAdd,
      onSubmit,
      handleClose,
      getDetailInfo,
      formatDate,
      colWidth,
      drageHeader
    };
  }
};
</script>
<style lang="scss" scoped>
.btnGroup {
  margin: 20px 10px 10px 0;
}

::v-deep(.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 31.5rem) !important;
    overflow-y: auto;
  }
}
</style>
