<template>
  <el-tooltip :content="tooltipContent" :disabled="tooltipShow" placement="top-start">
    <span ref="tooltipSpan" class="tooltip-span" @mouseenter="isShowTooltip">
      <span ref="content">{{ tooltipContent }}</span>
    </span>
  </el-tooltip>
</template>

<script>
import { reactive, toRefs, ref } from 'vue-demi';
export default {
  name: 'OverflowTooltip',
  components: {},
  props: {
    tooltipContent: {
      type: String,
      default: ''
    }
  },
  setup() {
    const data = reactive({
      tooltipShow: false
    });
    const content = ref(null);
    const tooltipSpan = ref(null);

    function isShowTooltip() {
      if (content && tooltipSpan) {
        const bool = content.value.offsetWidth < tooltipSpan.value.parentNode.offsetWidth;
        data.tooltipShow = bool;
      }
    }

    return {
      ...toRefs(data),
      isShowTooltip,
      content,
      tooltipSpan
    };
  }
};
</script>
<style lang="scss" scoped>
.tooltip-span {
  display: block;
  width: auto;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
