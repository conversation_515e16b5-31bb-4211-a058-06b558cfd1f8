<template>
  <el-dialog
    v-model="dialogShow"
    title="复制创建新样品"
    width="480px"
    :close-on-click-modal="false"
    @close="handleClose()"
  >
    <div v-loading="dialogLoading" class="flex items-center">
      <span class="w-28">检验类型：</span>
      <el-select
        v-model="formData.type"
        placeholder="请选择检验类型"
        size="small"
        clearable
        filterable
        style="width: 100%"
      >
        <el-option v-for="(val, key) in dictionaryAll['JYLX'].enable" :key="key" :label="val" :value="key" />
      </el-select>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose()">取 消</el-button>
        <el-button :loading="dialogLoading" type="primary" @click="handleSubmit">确 认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ElMessage } from 'element-plus';
import { reactive, ref, toRefs, watch } from 'vue';
import { copyAndSubmit } from '@/api/allocation';
export default {
  name: 'DialogCopySample',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dictionary: {
      type: Object,
      default: () => {}
    },
    sampleId: {
      type: String,
      default: ''
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      dialogLoading: false, // 弹出窗loading
      formData: {},
      dictionaryAll: {
        JYLX: {
          all: {},
          enable: {}
        }
      },
      dialogShow: false,
      ruleForm: ref()
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.dialogLoading = false;
        state.formData = {
          inspectionOrderId: props.sampleId
        };
        state.dictionaryAll = props.dictionary;
      }
    });
    const handleSubmit = async () => {
      if (!state.formData.type) {
        ElMessage.error('请选择检验类型');
      }
      state.dialogLoading = true;
      const { data } = await copyAndSubmit(state.formData).finally((state.dialogLoading = false));
      if (data) {
        ElMessage.success('复制成功');
        handleClose(true);
      }
    };
    // 关闭弹出窗
    const handleClose = isRefresh => {
      context.emit('closeDialog', isRefresh);
    };
    return { ...toRefs(state), handleSubmit, handleClose };
  }
};
</script>
<style lang="scss" scoped></style>
