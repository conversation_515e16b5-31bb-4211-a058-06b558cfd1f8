<template>
  <!-- 复杂弹窗 -->
  <el-dialog
    v-model="showDialog"
    custom-class="custom-dialog"
    title="选择单个检测项目"
    width="60%"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <div class="dialog-header">
      <div class="header-left">
        <el-input
          ref="inputRef"
          v-model="filterText"
          v-trim
          class="search"
          size="small"
          placeholder="请输入项目名称"
          prefix-icon="el-icon-search"
          clearable
          @clear="filterClear()"
          @keyup.enter="searchItem(filterText)"
        />
        <el-button type="primary" size="small" @click="searchItem(filterText)">查询</el-button>
      </div>
      <div class="header-right">
        <!-- <el-button size="small" @click="selectNone(newTreeDetail)" @keyup.prevent @keydown.enter.prevent>反选</el-button>
        <el-button size="small" @click="selectAll(treeDetail)" @keyup.prevent @keydown.enter.prevent>全选</el-button> -->
      </div>
    </div>
    <div class="dialog-content">
      <el-row>
        <el-col :span="6">
          <el-select
            v-model="materialCode"
            filterable
            size="small"
            class="topSelect"
            placeholder="请选择物资分类"
            @change="changeMaterialCode"
          >
            <el-option v-for="val in materialList" :key="val.value" :label="val.name" :value="val.code" />
          </el-select>
          <div class="tree-container">
            <div class="tree-content">
              <el-tree
                ref="leftTreeRef"
                :data="treeData"
                node-key="id"
                :props="defaultProps"
                default-expand-all
                :expand-on-click-node="false"
                :highlight-current="true"
                draggable
                class="leftTree"
                @node-click="clickNode"
              >
                <template #default="{ node }">
                  <span>{{ node.label }}</span>
                </template>
              </el-tree>
            </div>
          </div>
        </el-col>
        <el-col :span="18">
          <div v-loading="loading" class="list-container">
            <el-radio-group v-model="selectedItemId">
              <div
                v-for="(item, index) in newTreeDetail"
                :key="index"
                class="item-content"
                style="width: 100%"
                @click="changeCheckBox(item)"
              >
                <el-row class="main">
                  <div class="radio-item">
                    <el-radio
                      :label="item.id"
                      :disabled="filterTemplate && (!item.templateVersion || item.source !== 1)"
                      style="width: 100%"
                      @change="changeCheckBox(item)"
                    >
                      <span>{{ item.name }}</span>
                      <span class="iconStyle">
                        <SvgIcon v-if="item.source !== 1" :icon-class="'old-template'" :width="14" :height="14" />
                        <i
                          v-if="item.templateVersion"
                          style="color: green"
                          class="el-icon--right el-icon-document-checked"
                        /><i v-else style="color: red" class="el-icon--right el-icon-document-delete" />
                      </span>
                    </el-radio>
                  </div>
                  <div class="item-list">
                    <el-tag
                      v-for="(list, index1) in item.capabilityparaVoList"
                      :key="index1"
                      :type="item.id === selectedItemId ? 'primary' : 'info'"
                      >{{ list.name }}</el-tag
                    >
                  </div>
                </el-row>
              </div>
            </el-radio-group>
            <el-empty v-if="newTreeDetail.length === 0" :image="emptyImg" description="暂无数据" />
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="dialog-other">
      <div class="title">
        <label>已选项目</label>
        <!-- <el-button v-if="tags.length>0" size="small" icon="el-icon-delete" @click="clear">清空</el-button> -->
      </div>
      <div v-if="oldTags.length > 0 || tags.length > 0" class="select-items">
        <el-tag
          v-for="tag in oldTags"
          :key="tag.name || tag.sourceName"
          :closable="tag.closable"
          size="small"
          @close="closeTag(tag)"
        >
          {{ tag.name || tag.sourceName }}
        </el-tag>
        <el-tag v-for="tag in tags" :key="tag.name || tag.sourceName" size="small" @close="closeTag(tag)">
          {{ tag.name || tag.sourceName }}
        </el-tag>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="dialogSuccess">确定选择</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, getCurrentInstance, nextTick, computed } from 'vue';
import { getCapabilityUplist } from '@/api/user';
import _ from 'lodash';
import { useStore } from 'vuex';
import SvgIcon from '@/components/SvgIcon';
import { getCapabilityTree } from '@/api/user';
import { formatTree } from '@/utils/formatJson';
import { ElMessage } from 'element-plus';
import emptyImg from '@/assets/img/empty-data.png';

export default {
  name: 'AddSingleItem',
  components: { SvgIcon },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    filterTemplate: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['selectData'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // const lodash = inject('_')
    const store = useStore();
    const datas = reactive({
      inputRef: ref(),
      leftTreeRef: ref(),
      materialCode: '',
      materialList: store.state.user.materialList,
      filterText: '',
      filterTemplate: false,
      pageType: '',
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      tags: [],
      oldTags: [],
      treeData: [],
      treeDetail: [],
      newTreeDetail: [],
      loading: false,
      selectedItemId: ''
    });

    watch(props, newValue => {
      if (newValue.show) {
        datas.filterText = '';
        datas.selectedItemId = '';
        datas.pageType = newValue.type;
        if (store.state.user.materialList.length > 0) {
          datas.materialCode = store.state.user.materialList[0].code;
        }
        getLeftTree();
        nextTick(() => {
          datas.inputRef.focus();
        });
      }
      datas.tags = [];
      datas.filterTemplate = newValue.filterTemplate;
      datas.oldTags = JSON.parse(JSON.stringify(newValue.data));
      if (newValue.show && newValue.tree && newValue.tree.length > 0) {
        proxy.getCapabilityList(newValue.tree[0].id, newValue.tree[0].materialCategoryCode);
      }
    });
    // 初始化树节点
    const getLeftTree = () => {
      getCapabilityTree(datas.materialCode).then(res => {
        const treeData = formatTree(res.data.data);
        if (treeData.length > 0) {
          datas.treeData = JSON.parse(JSON.stringify(treeData));
          const all = { id: '-1', name: '全部', materialCategoryCode: datas.materialCode };
          datas.treeData.unshift(all);
          nextTick(() => {
            datas.leftTreeRef.setCurrentKey('-1', true);
          });
          clickNode(datas.treeData[0]);
        } else {
          datas.treeData = [];
        }
      });
    };
    // 过滤树节点
    const treeRef = ref(null);
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    // 点击树节点
    const clickNode = data => {
      proxy.getCapabilityList(data.id, data.materialCategoryCode);
    };

    // 确定选择
    const dialogSuccess = () => {
      if (datas.tags.length === 1) {
        datas.tags[0].materialCode = datas.materialCode;
        datas.tags[0].materialCategoryName = store.state.user.materialList.find(
          ele => ele.code === datas.materialCode
        )?.name;
        context.emit('selectData', datas.tags[0]);
      } else {
        ElMessage.warning('请先选择一个检测项目!');
      }
    };
    // 取消选择
    const close = () => {
      context.emit('selectData');
    };

    // 数据过滤
    const filterItem = item => {
      const newItem = {
        name: item.name,
        number: item.number,
        capabilityId: item.id,
        categoryName: item.categoryName
      };
      datas.tags.push(newItem);
    };
    // changeCheckBox
    const changeCheckBox = (item, flag) => {
      console.log(1);
      if (datas.filterTemplate && (!item.templateVersion || item.source !== 1)) {
        return false;
      }
      datas.selectedItemId = item.id;
      datas.tags = [item];
    };
    const changeMaterialCode = val => {
      // console.log(val)
      getLeftTree();
    };
    // 关闭tags
    const closeTag = tag => {
      datas.tags.splice(datas.tags.indexOf(tag), 1);
      datas.newTreeDetail.forEach(item => {
        const hasitem = _.filter(datas.oldTags, res => {
          res.closable = false;
          return res.categoryid === item.id || res.capabilityId === item.id;
        });
        // 判断是否已经勾选过但未曾确认选择
        const hasitem2 = _.filter(datas.tags, res => {
          res.closable = false;
          return res.categoryid === item.id || res.capabilityId === item.id;
        });
        if (hasitem.length === 1) {
          item.checked = true;
          item.disabled = true;
        } else {
          if (hasitem2.length === 1) {
            item.checked = true;
          } else {
            item.checked = false;
          }
        }
      });
    };
    // searchItem
    const searchItem = value => {
      if (value) {
        datas.newTreeDetail = [];
        datas.newTreeDetail = datas.newTreeDetail.concat(
          datas.treeDetail.filter(item => {
            return JSON.stringify(item).indexOf(value) !== -1;
          })
        );
      } else {
        datas.newTreeDetail = datas.treeDetail;
      }
    };

    const filterClear = () => {
      datas.newTreeDetail = datas.treeDetail;
    };

    const showDialog = computed({
      get: () => props.show,
      set: val => context.emit('selectData')
    });

    return {
      ...toRefs(datas),
      emptyImg,
      searchItem,
      dialogSuccess,
      close,
      filterNode,
      filterItem,
      clickNode,
      treeRef,
      closeTag,
      changeMaterialCode,
      changeCheckBox,
      filterClear,
      showDialog
    };
  },
  methods: {
    getCapabilityList(id, materialCategoryCode) {
      // 获取检测项目list
      const vm = this;
      vm.loading = true;
      getCapabilityUplist(id, materialCategoryCode).then(response => {
        vm.loading = false;
        if (response !== false && response.data.code === 200) {
          const { data } = response.data;
          vm.treeDetail = [];
          if (vm.pageType === 'testBase') {
            data.forEach(item => {
              const newChild = [];
              item.capabilityparaVoList.forEach(val => {
                if (val.applylabel.includes('2')) {
                  newChild.push(val);
                }
              });
              vm.treeDetail.push({ ...item, capabilityparaVoList: newChild });
            });
          } else {
            vm.treeDetail = data;
          }
          if (vm.filterText && vm.treeDetail.length > 0) {
            vm.newTreeDetail = vm.treeDetail.filter(item => {
              return JSON.stringify(item).indexOf(vm.filterText) !== -1;
            });
          } else {
            vm.newTreeDetail = vm.treeDetail;
          }
          vm.newTreeDetail.forEach(item => {
            const hasitem = _.filter(vm.oldTags, res => {
              res.closable = false;
              return res.categoryid === item.id || res.capabilityId === item.id;
            });
            // 判断是否已经勾选过但未曾确认选择
            const hasitem2 = _.filter(vm.tags, res => {
              res.closable = false;
              return res.categoryid === item.id || res.capabilityId === item.id;
            });
            if (hasitem.length === 1) {
              item.checked = true;
              item.disabled = true;
            } else {
              if (hasitem2.length === 1) {
                item.checked = true;
              } else {
                item.checked = false;
              }
            }
          });
        }
      });
    }
  }
};
</script>
<style lang="scss">
@import '@/styles/dialog.scss';
</style>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
.radio-item {
  width: 100%;
  margin: 0px 0px 6px 0px;
  .iconStyle {
    float: right;
    margin-top: 2px;
  }
  :deep(.el-radio__label) {
    width: 100%;
  }
}

.dialog-content {
  .tree-container {
    .tree-content {
      height: calc(100vh - 500px);
      overflow-y: auto;
      padding-left: 0;
    }
  }
  .list-container {
    height: calc(100vh - 418px);
    overflow-y: auto;
    .item-content {
      padding-bottom: 0 !important;
      .main {
        width: 100%;
        .title {
          padding-bottom: 8px;
        }
        .item-list {
          width: 100%;
          border-left: none;
          .item-box {
            height: 16px;
            margin-right: 20px !important;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
  .topSelect {
    width: 100%;
    margin-bottom: 10px;
  }
}
</style>
