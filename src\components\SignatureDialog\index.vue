<template>
  <div class="signature-panel">
    <el-dialog
      v-model="dialogShow"
      title="在线签名"
      width="850px"
      custom-class="signature-dialog"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <VueSignaturePad
        ref="signaturePad"
        class="signature-pad"
        :width="padWidth"
        :height="padHeight"
        :options="options"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button class="fl" size="small" @click="handleHistory" @keyup.prevent @keydown.enter.prevent
            >签名记录</el-button
          >
          <el-button size="small" @click="handleUndo" @keyup.prevent @keydown.enter.prevent>撤回一步</el-button>
          <el-button size="small" @click="handleReset" @keyup.prevent @keydown.enter.prevent>清空签名</el-button>
          <el-button size="small" type="primary" @click="handleGenerate" @keyup.prevent @keydown.enter.prevent
            >保存签名</el-button
          >
        </span>
      </template>
      <el-dialog
        v-model="dialogHistory"
        title="签名记录"
        width="750px"
        custom-class="signature-dialog"
        :before-close="handleCloseHistory"
        :close-on-click-modal="false"
      >
        <el-table
          v-loading="tableLoading"
          :data="tableDataHistory"
          fit
          border
          size="medium"
          height="auto"
          highlight-current-row
          class="dark-table format-height-table base-table"
        >
          <el-table-column label="序号" type="index" :min-width="colWidth.serialNo" />
          <el-table-column label="保存时间" prop="createTime" :min-width="colWidth.datetime" show-overflow-tooltip>
            <template #default="{ row }">
              <div v-if="row.createTime">{{ row.createTime }}</div>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="历史签名" prop="endTime" :min-width="colWidth.datetime" show-overflow-tooltip>
            <template #default="{ row, $index }">
              <el-image
                style="max-width: 100px"
                :src="row.signImgUrl"
                :zoom-rate="1.2"
                :preview-src-list="imgList"
                :initial-index="$index"
                fit="cover"
              />
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :page="listQuery.page"
          :limit="listQuery.limit"
          :total="total"
          @pagination="getHistory"
        />
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
import { ElButton, ElMessage } from 'element-plus';
import { computed, reactive, toRefs, ref, onMounted, nextTick, watch } from 'vue';
import { colWidth } from '@/data/tableStyle';
import { getSignHis } from '@/api/sysConfig';
import Pagination from '@/components/Pagination';

export default {
  name: 'SignatureDialog',
  components: { ElButton, Pagination },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    padWidth: {
      type: String,
      default: '800px'
    },
    padHeight: {
      type: String,
      default: '300px'
    }
  },
  emits: ['signImg', 'closeSignDialog'],
  setup(props, context) {
    const datas = reactive({
      resultImg: '',
      dialogHistory: false,
      tableDataHistory: [],
      imgList: [],
      tableLoading: false,
      listQuery: {
        page: 1,
        limit: 20
      },
      total: 0,
      options: {
        dotSize: (2.5 + 7.5) / 2,
        minWidth: 2.5,
        maxWidth: 7.5,
        throttle: 0,
        penColor: '#000'
      }
    });
    const signaturePad = ref(null);
    const handleClose = done => {
      signaturePad.value.clearSignature();
      done();
    };
    const handleReset = () => {
      signaturePad.value.clearSignature();
    };
    const handleUndo = () => {
      signaturePad.value.undoSignature();
    };
    const handleGenerate = async () => {
      try {
        // result: { isEmpty, data}
        const result = signaturePad.value.saveSignature();
        datas.resultImg = result.data;
        if (result.isEmpty) {
          ElMessage.warning('签名不能为空!');
        } else {
          if (datas.resultImg) {
            const imgBase = datas.resultImg;
            context.emit('signImg', imgBase);
            signaturePad.value.clearSignature();
          }
        }
      } catch (err) {
        ElMessage.error('签名不能为空!');
      }
    };
    const getHistory = query => {
      const params = {
        page: query ? query.page : datas.listQuery.page,
        limit: query ? query.limit : datas.listQuery.limit
      };
      getSignHis(params).then(res => {
        if (res) {
          datas.tableDataHistory = res.data.data.records;
          datas.imgList = datas.tableDataHistory.map(item => {
            return item.signImgUrl;
          });
          datas.total = res.data.data.total;
        }
      });
    };
    const handleHistory = () => {
      datas.dialogHistory = true;
      datas.imgList = [];
      getHistory();
    };
    const handleCloseHistory = () => {
      datas.dialogHistory = false;
    };
    const dialogShow = computed({
      get: () => props.dialogVisible,
      set: val => context.emit('closeSignDialog', val)
    });

    watch(
      () => props.dialogVisible,
      newValue => {
        if (newValue) {
          nextTick(() => {
            signaturePad.value.$el.children[0].width = parseInt(props.padWidth);
            signaturePad.value.$el.children[0].height = parseInt(props.padHeight);
          });
        }
      }
    );

    onMounted(() => {});

    return {
      ...toRefs(datas),
      handleClose,
      getHistory,
      colWidth,
      handleCloseHistory,
      handleHistory,
      handleReset,
      handleGenerate,
      dialogShow,
      signaturePad,
      handleUndo
    };
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-image-viewer__canvas) {
  img {
    background-color: #fff;
  }
}
.signature-pad {
  border: 1px solid #e4e7ed;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

:deep(.el-dialog.signature-dialog) {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  .el-dialog__title {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}

/* .dialog-footer button:first-child {
  margin-right: 10px;
} */
</style>
