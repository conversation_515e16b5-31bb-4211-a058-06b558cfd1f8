<template>
  <el-form
    ref="formRef"
    v-loading="formLoading"
    :model="formDataAll"
    :rules="rules"
    label-position="left"
    class="formAll"
    hide-required-asterisk
  >
    <el-container style="height: 100%">
      <el-header style="height: auto; padding: 0">
        <div ref="itemDetail" class="itemDetail">
          <div v-if="!inputVisible" class="itemName">
            <span>{{ formDataAll.name || '--' }}</span>
            <i class="el-icon-edit" style="margin-left: 10px; cursor: pointer" @click="handleEditName" />
          </div>
          <el-form-item
            v-if="inputVisible"
            label=""
            label-width="0"
            prop="name'"
            class="itemFormClass"
            :rules="{ required: true, message: '项目名不能为空', trigger: 'blur' }"
          >
            <el-input
              ref="inputRef"
              v-model="formDataAll.name"
              size="small"
              maxlength="50"
              @change="
                val => {
                  handleChange(val, 'name');
                }
              "
              @blur="inputVisible = false"
            />
          </el-form-item>
          <div v-if="!inputVisibleEng" class="itemName itemEnName">
            <span>{{ formDataAll.englishname || '--' }}</span>
            <i class="el-icon-edit" style="margin-left: 10px; cursor: pointer" @click="handleEditEngName" />
          </div>
          <el-form-item
            v-if="inputVisibleEng"
            label=""
            label-width="0"
            prop="englishname'"
            class="itemFormClass itemEngFormClass"
            :rules="{ required: true, message: '项目名不能为空', trigger: 'blur' }"
          >
            <el-input
              ref="inputRefEng"
              v-model="formDataAll.englishname"
              size="small"
              maxlength="50"
              placeholder="项目英文名称"
              @change="
                val => {
                  handleChange(val, 'englishname');
                }
              "
              @blur="inputVisibleEng = false"
            />
          </el-form-item>
          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="试验方法：" label-width="110px" prop="empiricalApproach">
                <el-select
                  v-model="formDataAll.empiricalApproach"
                  multiple
                  collapse-tags
                  default-first-option
                  placeholder="请选择实验方法"
                  style="width: 100%"
                  @change="
                    val => {
                      handleChange(val, 'empiricalApproach');
                    }
                  "
                >
                  <el-option
                    v-for="(item, index) in formDataAll.parameterlabal"
                    :key="index"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item
                label="项目分类："
                class="required"
                label-width="110px"
                prop="projectCategoryId"
                :rules="{ required: true, message: '请选择项目分类', trigger: 'change' }"
              >
                <el-cascader
                  v-model="formDataAll.projectCategoryId"
                  :options="itemTreeData"
                  :props="categoryProps"
                  placeholder="请选择项目分类"
                  style="width: 100%"
                  @change="
                    val => {
                      handleChange(val, 'projectCategoryId');
                    }
                  "
                />
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item label="试验要求：" label-width="110px" prop="requirement">
                <el-input
                  v-model="formDataAll.requirement"
                  type="textarea"
                  :autosize="{ minRows: 1, maxRows: 3 }"
                  @change="
                    val => {
                      handleChange(val, 'requirement');
                    }
                  "
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检测依据：" label-width="110px" prop="capabilityBasisId">
                <el-select
                  v-model="formDataAll.capabilityBasisId"
                  placeholder="请选择检测依据"
                  clearable
                  style="width: 100%"
                  @change="handleChangeBasis"
                >
                  <el-option v-for="(val, key) in basisListJSON" :key="key" :label="val.basisName" :value="key" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="更新人：" label-width="110px" prop="empiricalApproach" class="margin-bottom-none">
                <UserTag :name="getNameByid(formDataAll.lastUpdateByUserId) || getNameByid(accountId)" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="更新时间：" label-width="110px" prop="lastupdatedatetime" class="margin-bottom-none">
                {{ formDataAll.lastupdatedatetime || formatDate(new Date()) }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-header>
      <el-main style="padding: 0">
        <div v-if="formDataAll.custList.length === 0" class="noData gjcsEdit">
          <el-empty :image="emptyImg" description="暂无数据" />
        </div>
        <div v-if="formDataAll.custList.length > 0 && isShow" id="gjcsEditList" class="gjcsEdit">
          <div
            v-for="(row, i) in formDataAll.custList"
            :key="i + Math.random()"
            :class="'formDataClass rowType' + row.resulttype"
          >
            <div class="top">
              <div class="top-title">
                <span class="sortingIcon tes-move iconfont" style="font-size: 10px; cursor: move; margin-right: 5px" />
                <span class="xh">{{ i + 1 }}</span>
                <el-tooltip v-if="!row.inputShow" :content="row.name" placement="top" effect="light">
                  <span v-if="!row.inputShow" class="sonItemName">{{ row.name }}</span>
                </el-tooltip>
                <el-form-item
                  v-if="row.inputShow"
                  label=""
                  label-width="0"
                  :prop="'custList.' + i + '.name'"
                  class="paramsNameClass"
                  :rules="{ required: true, message: '参数名不能为空', trigger: 'blur' }"
                >
                  <el-input
                    :ref="'itemInputRef' + i"
                    v-model="row.name"
                    maxlength="50"
                    size="small"
                    @change="
                      val => {
                        handleChange(val, 'name', i);
                      }
                    "
                    @blur="row.inputShow = false"
                  />
                </el-form-item>
                <i
                  v-if="!row.inputShow"
                  class="el-icon-edit"
                  style="margin-left: 10px; cursor: pointer"
                  @click="handleEditItemName(i)"
                />
                <el-tooltip
                  v-if="!row.inputShowEng"
                  :content="row.englishname || '暂无英文名称'"
                  placement="top"
                  effect="light"
                >
                  <span v-if="!row.inputShowEng" class="sonItemName sonItemEngName">{{ row.englishname || '--' }}</span>
                </el-tooltip>
                <i
                  v-if="!row.inputShowEng"
                  class="el-icon-edit"
                  style="margin-left: 10px; cursor: pointer"
                  @click="handleEditItemEngName(i)"
                />
                <el-form-item
                  v-if="row.inputShowEng"
                  label=""
                  label-width="0"
                  :prop="'custList.' + i + '.englishname'"
                  class="paramsNameClass"
                >
                  <el-input
                    ref="itemEnInputRefs"
                    v-model="row.englishname"
                    maxlength="50"
                    size="mini"
                    placeholder="请输入英文名称"
                    style="margin-left: 10px"
                    @change="
                      val => {
                        handleChange(val, 'englishname', i);
                      }
                    "
                    @blur="row.inputShowEng = false"
                  />
                </el-form-item>
              </div>
              <i
                class="blue-color iconfont tes-delete"
                style="position: absolute; right: 92px"
                @click="handleDelete(i)"
              />
              <el-tag size="small" :type="tagType[row.resulttype]" class="fr">{{ typeOfData[row.resulttype] }}</el-tag>
            </div>
            <el-row :gutter="40">
              <el-col v-if="row.resulttype == 1" :span="12">
                <el-form-item label="类型：" label-width="110px" :prop="'custList.' + i + '.standardmathtype'">
                  <el-radio-group
                    v-model="row.standardmathtype"
                    @change="
                      val => {
                        handleChange(val, 'standardmathtype', i);
                      }
                    "
                  >
                    <el-radio :label="1">标准项</el-radio>
                    <el-radio :label="0">非标准</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否判定：" label-width="110px" class="">
                  <el-checkbox
                    v-model="row.okflag"
                    @change="
                      val => {
                        handleChange(val, 'okflag', i);
                      }
                    "
                    >判 定</el-checkbox
                  >
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单位：" label-width="110px" :prop="'custList.' + i + '.unitname'">
                  <el-select
                    v-model="row.unitname"
                    placeholder="请选择单位"
                    clearable
                    filterable
                    style="width: auto"
                    @change="
                      val => {
                        handleChange(val, 'unitname', i);
                      }
                    "
                  >
                    <el-option-group v-for="item in unitList" :key="item.label" :label="item.label">
                      <el-option
                        v-for="val in item.group"
                        :key="val.id"
                        :label="val.name"
                        :value="val.name"
                        :disabled="val.status !== 1"
                      >
                        <span style="float: left">{{ val.name }}</span>
                        <span v-if="val.status !== 1" class="fr" style="color: red">已停用</span>
                      </el-option>
                    </el-option-group>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="row.resulttype == 1 && row.standardmathtype != 1" :span="12">
                <el-form-item
                  v-if="row.resulttype == 1"
                  label-width="110px"
                  label="约束小数位："
                  :prop="'custList.' + i + '.smallnumber'"
                  :rules="{ validator: isInteger2, trigger: 'change' }"
                >
                  <el-input-number
                    v-model="row.smallnumber"
                    :min="0"
                    placeholder=""
                    controls-position="right"
                    @change="
                      val => {
                        handleChange(val, 'smallnumber', i);
                      }
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col v-if="row.standardmathtype == 1" :span="12">
                <el-form-item
                  label-width="110px"
                  :label="row.resultOptionType === 1 ? '有效数字约束：' : '约束小数位：'"
                  :prop="'custList.' + i + '.smallnumber'"
                  :rules="{ validator: isInteger2, trigger: 'change' }"
                >
                  <el-input-number
                    v-model="row.smallnumber"
                    :min="0"
                    placeholder=""
                    controls-position="right"
                    @change="
                      val => {
                        handleChange(val, 'smallnumber', i);
                      }
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col v-if="row.standardmathtype == 1" :span="12">
                <el-form-item
                  label-width="110px"
                  label="最小值："
                  :prop="'custList.' + i + '.minNum'"
                  :rules="[
                    {
                      required: row.okflag && row.requirement && !row.maxNum && row.maxNum != 0,
                      message: '最小值最大值至少必填一项',
                      trigger: 'change'
                    },
                    { validator: isDigital2 }
                  ]"
                >
                  <div class="flex gap-3 pr-16">
                    <el-input
                      v-model="row.minNum"
                      placeholder=""
                      @change="
                        val => {
                          handleChange(val, 'minNum', i);
                        }
                      "
                    />
                    <el-checkbox
                      v-model="row.minselected"
                      class="checkRadio"
                      @change="
                        val => {
                          handleChange(val, 'minselected', i);
                        }
                      "
                      >包含</el-checkbox
                    >
                  </div>
                </el-form-item>
              </el-col>
              <el-col v-if="row.standardmathtype == 1" :span="12">
                <el-form-item
                  label-width="110px"
                  label="最大值："
                  :prop="'custList.' + i + '.maxNum'"
                  :rules="[
                    {
                      required: row.okflag && row.requirement && !row.minNum && row.minNum != 0,
                      message: '最小值最大值至少必填一项',
                      trigger: 'change'
                    },
                    { validator: isDigital2 }
                  ]"
                >
                  <div class="flex gap-3 pr-16">
                    <el-input
                      v-model="row.maxNum"
                      placeholder=""
                      @change="
                        val => {
                          handleChange(val, 'maxNum', i);
                        }
                      "
                    />
                    <el-checkbox
                      v-model="row.maxseleced"
                      class="checkRadio"
                      @change="
                        val => {
                          handleChange(val, 'maxseleced', i);
                        }
                      "
                      >包含</el-checkbox
                    >
                  </div>
                </el-form-item>
              </el-col>
              <el-col v-if="row.resulttype == 1 && row.standardmathtype == 0" :span="24">
                <el-form-item
                  label="表达式："
                  label-width="110px"
                  :prop="'custList.' + i + '.javascriptmath'"
                  :rules="{ required: row.okflag, message: '请编辑表达式', trigger: 'change' }"
                >
                  <el-button size="small" @click="handleChange(row, 'javascriptmath', i)">编辑表达式</el-button>
                  <el-button
                    size="small"
                    type="text"
                    :icon="showCode ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                    @click="showCode = !showCode"
                    >{{ showCode ? '收起' : '展开' }}</el-button
                  >
                  <div v-if="showCode" class="code-textarea">
                    <el-input v-model.trim="row.javascriptmath" type="textarea" autosize readonly />
                  </div>
                  <div v-else class="code-textarea">
                    <el-input v-model.trim="row.javascriptmath" type="textarea" :rows="4" readonly />
                  </div>
                </el-form-item>
              </el-col>
              <el-col v-if="row.resulttype == 2 || row.resulttype == 5" :span="24">
                <el-form-item
                  label-width="110px"
                  :prop="'custList.' + i + '.qualifiedOption'"
                  label="合格选项："
                  :rules="{
                    required: row.okflag && row.requirement && row.noQualifiedOption.length == 0,
                    message: '合格选项和不合格项至少必填一项',
                    trigger: 'change'
                  }"
                >
                  <!-- 枚举型 -->
                  <el-select
                    v-if="row.resulttype == 2"
                    v-model="row.qualifiedOption"
                    placeholder="请选择合格选项"
                    multiple
                    clearable
                    filterable
                    @change="
                      val => {
                        handleChange(val, 'qualifiedOption', i);
                      }
                    "
                  >
                    <el-option-group
                      v-for="item in selectAllOptions[row.custlabel]"
                      :key="item.label"
                      :label="item.label"
                    >
                      <el-option
                        v-for="val in item.group"
                        :key="val.id"
                        :label="val.name"
                        :value="val.code"
                        :disabled="
                          val.status !== 1 ||
                          row.noQualifiedOption.some(value => {
                            return value == val.code;
                          })
                        "
                      >
                        <span style="float: left">{{ val.name }}</span>
                        <span v-if="val.status !== 1" class="fr" style="color: red">已停用</span>
                      </el-option>
                    </el-option-group>
                  </el-select>
                  <!-- 自定义枚举 -->
                  <el-select
                    v-if="row.resulttype == 5"
                    v-model="row.qualifiedOption"
                    placeholder="请选择合格选项"
                    multiple
                    clearable
                    filterable
                    @change="
                      val => {
                        handleChange(val, 'qualifiedOption', i);
                      }
                    "
                  >
                    <el-option
                      v-for="val in JSON.parse(row.custlabel)"
                      :key="val.value"
                      :label="val.label"
                      :value="val.value"
                      :disabled="
                        row.noQualifiedOption.some(value => {
                          return value == val.value;
                        })
                      "
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="row.resulttype == 2 || row.resulttype == 5" :span="24">
                <el-form-item
                  label-width="110px"
                  :prop="'custList.' + i + '.noQualifiedOption'"
                  :label="'不合格项：'"
                  :rules="{
                    required: row.okflag && row.requirement && row.qualifiedOption.length == 0,
                    message: '合格选项和不合格项至少必填一项',
                    trigger: 'change'
                  }"
                >
                  <!-- 枚举型 -->
                  <el-select
                    v-if="row.resulttype == 2"
                    v-model="row.noQualifiedOption"
                    multiple
                    placeholder="请选择不合格项"
                    clearable
                    filterable
                    @change="
                      val => {
                        handleChange(val, 'noQualifiedOption', i);
                      }
                    "
                  >
                    <el-option-group
                      v-for="item in selectAllOptions[row.custlabel]"
                      :key="item.label"
                      :label="item.label"
                    >
                      <el-option
                        v-for="val in item.group"
                        :key="val.id"
                        :label="val.name"
                        :value="val.code"
                        :disabled="
                          val.status !== 1 ||
                          row.qualifiedOption.some(value => {
                            return value == val.code;
                          })
                        "
                      >
                        <span style="float: left">{{ val.name }}</span>
                        <span v-if="val.status !== 1" class="fr" style="color: red">已停用</span>
                      </el-option>
                    </el-option-group>
                  </el-select>
                  <!-- 自定义枚举型 -->
                  <el-select
                    v-if="row.resulttype == 5"
                    v-model="row.noQualifiedOption"
                    multiple
                    placeholder="请选择不合格项"
                    clearable
                    filterable
                    @change="
                      val => {
                        handleChange(val, 'noQualifiedOption', i);
                      }
                    "
                  >
                    <el-option
                      v-for="val in JSON.parse(row.custlabel)"
                      :key="val.value"
                      :label="val.label"
                      :value="val.value"
                      :disabled="
                        row.qualifiedOption.some(value => {
                          return value == val.value;
                        })
                      "
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="技术要求："
                  label-width="110px"
                  :prop="'custList.' + i + '.requirement'"
                  :rules="{
                    required:
                      row.okflag &&
                      ((row.resulttype == 1 && (row.minNum || row.minNum == 0 || row.maxNum || row.maxNum == 0)) ||
                        ((row.resulttype == 2 || row.resulttype == 5) &&
                          (row.qualifiedOption.length || row.noQualifiedOption.length))),
                    message: '请输入技术要求',
                    trigger: 'change'
                  }"
                >
                  <el-input
                    v-model.trim="row.requirement"
                    type="textarea"
                    placeholder="请输入技术要求"
                    :rows="2"
                    @change="
                      val => {
                        handleChange(val, 'requirement', i);
                      }
                    "
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-main>
      <el-footer style="height: 45px; padding: 0 10px">
        <div class="addKeyParameter">
          <el-dropdown popper-class="dropdownSelect" trigger="click" @command="handleAddKeyParameter">
            <div class="blue-color">添加关键参数</div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="item in rowDetail2.capabilityparaEntityList"
                  :key="item.id"
                  :disabled="
                    formDataAll.custList.some(val => {
                      return val.capabilityParaId === item.capabilityParaId;
                    }) || !item.applylabel.includes('2')
                  "
                  :command="item"
                  >{{ item.name }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-footer>
    </el-container>
    <code-editor
      :show="showCodeEditor"
      :data="codeEditorData"
      :material-code="materialCode"
      :list="itemLists"
      @close="closeCodeEditor"
      @set-data="getCodeEditor"
    />
  </el-form>
</template>

<script>
import { reactive, toRefs, getCurrentInstance, watch, ref, nextTick } from 'vue';
import { getDictionary } from '@/api/user';
import { getNameByid } from '@/utils/common';
import { getLoginInfo } from '@/utils/auth';
import CodeEditor from '@/components/CodeEditor/index1.vue';
import UserTag from '@/components/UserTag';
import { formatDate } from '@/utils/formatTime';
import Sortable from 'sortablejs';
import { useRoute } from 'vue-router';
import { findByFeats } from '@/api/testBase';
import { isInteger2 } from '@/utils/validate';
import emptyImg from '@/assets/img/empty-data.png';
import { isDigital2 } from '@/utils/validate';

export default {
  name: 'TableForm',
  components: { CodeEditor, UserTag },
  props: {
    treeData: {
      type: Array,
      default: function () {
        return [];
      }
    },
    unitList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    rowDetail: {
      type: Object,
      default: function () {
        return {};
      }
    },
    materialCode: {
      type: String,
      default: ''
    }
  },
  emits: ['detail'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    const state = reactive({
      unitList: props.unitList,
      inputVisible: false,
      inputVisibleEng: false,
      inputRef: ref(),
      inputRefEng: ref(),
      accountId: getLoginInfo().accountId, // 当前登录人的id
      rowDetail2: {},
      specifications: route.query.specifications, // 检测依据匹配
      isShow: true,
      formLoading: false,
      itemDetail: ref(),
      // tableIndex: props.tableIndex,
      itemHeight: '',
      rules: {},
      formDataAll: {
        custList: []
      },
      currentJMIndex: null,
      currentJMDetail: {},
      basisListJSON: {}, // 检测依据列表
      selectAllOptions: {}, // 字典集合
      dictionarys: [], // 出现的字典code
      itemTreeData: props.treeData, // 项目分类树结构
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      typeOfData: {
        1: '数值型',
        2: '枚举型',
        3: '字符串',
        4: '日期型',
        5: '自定义枚举'
      },
      tagType: {
        1: 'primary',
        2: 'warning',
        3: 'primary',
        4: 'success',
        5: 'warning'
      },
      typeOfData2: {
        数值型: 1,
        枚举型: 2,
        字符串: 3,
        日期型: 4,
        自定义枚举: 5
      },
      tableList: [],
      showCode: false,
      showCodeEditor: false,
      codeEditorData: {
        capabilityjson: [],
        javascriptmath: ''
      },
      itemLists: []
    });
    const itemInputRefs = ref([]);
    const itemEnInputRefs = ref([]);
    watch(props, newValue => {
      state.dictionarys = [];
      state.selectAllOptions = {};
      state.basisListJSON = {};
      getSelectAll(props.rowDetail);
      getSpecifications(props.rowDetail);
    });
    // 获取匹配的检测依据
    const getSpecifications = selectItem => {
      const params = {
        capabilityId: selectItem.capabilityId,
        specIdValueMap: JSON.parse(route.query.specifications)
      };
      findByFeats(params).then(res => {
        const response = res.data.data;
        response.forEach(item => {
          state.basisListJSON[item.basisId] = item;
        });
      });
    };
    const formatParams = () => {
      state.formDataAll.custList.forEach((row, index) => {
        if (Number(row.smallnumber) || row.smallnumber == 0) {
          row.smallnumber = Number(row.smallnumber);
        } else {
          row.smallnumber = undefined;
        }
        if (
          row.resulttype == '1' &&
          (row.standardmathtype == null || row.standardmathtype == '' || row.standardmathtype == undefined)
        ) {
          row.standardmathtype = 1;
          handleChange(1, 'standardmathtype', index);
        }
        if (!row.qualifiedOption) {
          row.qualifiedOption = [];
        }
        if (!row.noQualifiedOption) {
          row.noQualifiedOption = [];
        }
      });
    };
    const formatParamsRow = () => {
      state.rowDetail2.custList.forEach((row, index) => {
        if (Number(row.smallnumber) || row.smallnumber == 0) {
          row.smallnumber = Number(row.smallnumber);
        } else {
          row.smallnumber = undefined;
        }
        if (
          row.resulttype == '1' &&
          (row.standardmathtype == null || row.standardmathtype == '' || row.standardmathtype == undefined)
        ) {
          row.standardmathtype = 1;
          handleChange(1, 'standardmathtype', index);
        }
        if (!row.qualifiedOption) {
          row.qualifiedOption = [];
        }
        if (!row.noQualifiedOption) {
          row.noQualifiedOption = [];
        }
      });
    };
    // 获取所有的字典选项
    const getSelectAll = detail => {
      state.formLoading = true;
      detail.custList.forEach(item => {
        if ((item.resulttype === '2' && item.custlabel) || (item.resulttype === 2 && item.custlabel)) {
          state.dictionarys.push(item.custlabel);
        }
      });
      // 去重并请求字典
      Array.from(state.dictionarys).forEach(value => {
        state.selectAllOptions[value] = getSelectDictionary(value);
      });
      setTimeout(() => {
        state.formLoading = false;
        state.formDataAll = JSON.parse(JSON.stringify(props.rowDetail));
        state.rowDetail2 = JSON.parse(JSON.stringify(props.rowDetail));
        state.itemTreeData = props.treeData;
        state.isShow = true;
        formatParams();
        formatParamsRow();
        // getSpecifications()
        showTable();
      }, 200);
    };
    // 获取合格不合格选项
    const getSelectDictionary = custlabel => {
      var selectOption = [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ];
      getDictionary(custlabel).then(res => {
        if (res) {
          res.data.data.dictionaryoption.forEach(item => {
            if (item.status === 1) {
              selectOption[0].group.push(item);
            } else {
              selectOption[1].group.push(item);
            }
          });
        }
      });
      return selectOption;
    };
    const handleChange = (val, key, i) => {
      if (key === 'okflag' && !val) {
        state.formDataAll.custList[i].requirement = '/';
        handleChange('/', 'requirement', i);
      }
      var detail = {
        // tableIndex: props.tableIndex,
        fieldName: key,
        fieldValue: val,
        chileIndex: i
      };
      if (detail.fieldName === 'minNum' || detail.fieldName === 'maxNum') {
        // 如果改变的参数是最大值或者是最小值
        if (detail.fieldValue === undefined) {
          detail.fieldValue = '';
        }
        handleCalucate(i);
      } else if (
        detail.fieldName === 'qualifiedOption' ||
        detail.fieldName === 'noQualifiedOption' ||
        detail.fieldName === 'empiricalApproach'
      ) {
        // detail.fieldValue = val.toString()
      } else if (detail.fieldName === 'okflag') {
        detail.fieldValue = val;
      } else if (detail.fieldName === 'minselected' || detail.fieldName === 'maxseleced') {
        handleCalucate(i);
      } else if (detail.fieldName === 'javascriptmath') {
        state.codeEditorData.capabilityjson = detail.fieldValue.capabilityjson
          ? JSON.parse(detail.fieldValue.capabilityjson)
          : [];
        state.codeEditorData.javascriptmath = detail.fieldValue.javascriptmath;
        state.showCodeEditor = true;
        state.currentJMIndex = i;
        state.currentJMDetail = detail;
      }
      if (detail.fieldName !== 'javascriptmath') {
        if (i || i === 0) {
          state.rowDetail2.custList[i][detail.fieldName] = detail.fieldValue;
        } else {
          state.rowDetail2[detail.fieldName] = detail.fieldValue;
        }
      }
      state.rowDetail2.updateflag = 1;
      context.emit('detail', state.rowDetail2);
    };
    // 如果改变了最大值或者最小值触发技术要求的
    const handleCalucate = i => {
      var rowDetail = state.formDataAll.custList[i];
      if ((rowDetail.minNum || rowDetail.minNum == '0') && (rowDetail.maxNum || rowDetail.maxNum == '0')) {
        // 有最大值、最小值
        var minText = '';
        var maxText = '';
        if (rowDetail.minselected) {
          minText = '[' + rowDetail.minNum;
        } else {
          minText = '(' + rowDetail.minNum;
        }
        if (rowDetail.maxseleced) {
          maxText = rowDetail.maxNum + ']';
        } else {
          maxText = rowDetail.maxNum + '）';
        }
        if (rowDetail.minNum == rowDetail.maxNum && rowDetail.minselected && rowDetail.maxseleced) {
          state.formDataAll.custList[i].requirement = '=' + rowDetail.minNum;
        } else {
          state.formDataAll.custList[i].requirement = minText + '，' + maxText;
        }
        handleChange(state.formDataAll.custList[i].requirement, 'requirement', i);
      } else if ((rowDetail.minNum || rowDetail.minNum == 0) && !rowDetail.maxNum) {
        // 只有最小值没有最大值
        if (rowDetail.minselected) {
          state.formDataAll.custList[i].requirement = '≥' + rowDetail.minNum;
        } else {
          state.formDataAll.custList[i].requirement = '>' + rowDetail.minNum;
        }
        handleChange(state.formDataAll.custList[i].requirement, 'requirement', i);
      } else if (!rowDetail.minNum && (rowDetail.maxNum || rowDetail.maxNum == 0)) {
        // 只有最大值没有最小值
        if (rowDetail.maxseleced) {
          state.formDataAll.custList[i].requirement = '≤' + rowDetail.maxNum;
        } else {
          state.formDataAll.custList[i].requirement = '＜' + rowDetail.maxNum;
        }
        handleChange(state.formDataAll.custList[i].requirement, 'requirement', i);
      }
    };
    const onSubmit = () => {
      if (proxy.$refs['formRef']) {
        return proxy.$refs['formRef'].validate();
      }
    };
    // code编辑器
    const closeCodeEditor = () => {
      state.showCodeEditor = false;
    };
    const getCodeEditor = value => {
      value.capabilityjson = JSON.stringify(value.capabilityjson);
      state.codeEditorData = value;
      state.showCodeEditor = false;
      state.currentJMDetail.fieldValue.qualifiedOption = [];
      state.currentJMDetail.fieldValue.noQualifiedOption = [];
      Object.assign(state.currentJMDetail.fieldValue, value);
      if (state.currentJMIndex || state.currentJMIndex === 0) {
        state.rowDetail2.custList[state.currentJMIndex] = state.currentJMDetail.fieldValue;
        context.emit('detail', state.rowDetail2);
      }
    };
    const handleBlurName = () => {
      if (state.formDataAll.name === props.rowDetail.name) {
        state.inputVisible = false;
      }
    };
    const handleEditName = () => {
      state.inputVisible = true;
      nextTick(() => {
        state.inputRef.focus();
      });
    };
    const handleEditEngName = () => {
      state.inputVisibleEng = true;
      nextTick(() => {
        state.inputRefEng.focus();
      });
    };
    const handleEditItemName = i => {
      state.formDataAll.custList[i].inputShow = true;
      nextTick(() => {
        if (itemInputRefs.value[i]) {
          itemInputRefs.value[i].focus();
        }
      });
    };
    const handleEditItemEngName = i => {
      state.formDataAll.custList[i].inputShowEng = true;
      nextTick(() => {
        if (itemEnInputRefs.value[i]) {
          console.log(itemEnInputRefs.value[i]);
          itemEnInputRefs.value[i].focus();
        }
      });
    };
    const handleDelete = index => {
      state.rowDetail2.custList.splice(index, 1);
      context.emit('detail', state.rowDetail2);
      state.rowDetail2.updateflag = 1;
      state.formDataAll.custList = state.rowDetail2.custList;
    };
    const rowDrop = () => {
      // 获取当前表格
      const el = document.querySelector('.gjcsEdit');
      Sortable.create(el, {
        animation: 300,
        handle: '.tes-move',
        draggable: '.formDataClass',
        ghostClass: 'ghost',
        dragClass: 'drag',
        forceFallback: true,
        onEnd({ newIndex, oldIndex }) {
          if (oldIndex !== newIndex) {
            const currRow = state.rowDetail2.custList.splice(oldIndex, 1)[0];
            state.rowDetail2.custList.splice(newIndex, 0, currRow);
            state.rowDetail2.custList.forEach((value, index) => {
              value.order = index;
            });
            state.rowDetail2.updateflag = 1;
            state.formDataAll.custList = state.rowDetail2.custList;
            context.emit('detail', state.rowDetail2);
            showTable();
          }
        }
      });
    };
    const showTable = () => {
      state.isShow = false;
      setTimeout(() => {
        state.isShow = true;
        nextTick(() => {
          rowDrop();
        });
      }, 0);
    };
    const handleAddKeyParameter = item => {
      if (state.typeOfData2[item.resulttype] === 1 || item.resulttype === 1) {
        item.standardmathtype = 1;
      }
      state.rowDetail2.custList.push({
        ...item,
        qualifiedOption: [],
        noQualifiedOption: [],
        custlabel: item.resultoption,
        resulttype: state.typeOfData2[item.resulttype] ? state.typeOfData2[item.resulttype] : item.resulttype
      });
      context.emit('detail', state.rowDetail2);
      state.rowDetail2.updateflag = 1;
      state.formDataAll.custList = state.rowDetail2.custList;
    };
    // 修改检测依据
    const handleChangeBasis = val => {
      handleChange(val, 'capabilityBasisId');
      if (val) {
        const selectBasisInfo = state.basisListJSON[val];
        state.formDataAll.custList.forEach((item, index) => {
          // 循环给关键参数赋值
          const paraMapInfo = selectBasisInfo.paraMap[item.capabilityParaId];
          item.standardmathtype = paraMapInfo?.standardMathType;
          handleChange(item.standardmathtype, 'standardmathtype', index);
          item.requirement = paraMapInfo?.requirement;
          handleChange(item.requirement, 'requirement', index);
          item.minselected = paraMapInfo?.minSelected;
          handleChange(item.minselected, 'minselected', index);
          item.maxseleced = paraMapInfo?.maxSelected;
          handleChange(item.maxseleced, 'maxseleced', index);
          item.maxNum = paraMapInfo?.maxNum;
          handleChange(item.maxNum, 'maxNum', index);
          item.minNum = paraMapInfo?.minNum;
          handleChange(item.minNum, 'minNum', index);
        });
      }
    };
    return {
      ...toRefs(state),
      emptyImg,
      isInteger2,
      isDigital2,
      handleEditItemEngName,
      handleChangeBasis,
      formatParamsRow,
      rowDrop,
      handleAddKeyParameter,
      showTable,
      handleBlurName,
      handleEditItemName,
      handleDelete,
      handleEditName,
      handleEditEngName,
      handleChange,
      formatDate,
      getNameByid,
      getSelectAll,
      getSelectDictionary,
      handleCalucate,
      onSubmit,
      closeCodeEditor,
      getCodeEditor
    };
  }
};
</script>

<style lang="scss" scoped>
@import '../baseDetailCommom.scss';
.paramsNameClass {
  margin-bottom: 0 !important;
}
.required {
  :deep(.el-form-item__label::before) {
    content: '*';
    color: #f56c6c;
    position: relative;
    top: -1px;
    left: -3px;
  }
}
.checkRadio {
  margin-left: 6px;
}
.el-input-number--medium {
  width: 109px;
}
:deep(.el-input-number--medium .el-input-number__increase) {
  width: 16.7px;
  background: #fff;
}
:deep(.el-input-number--medium .el-input-number__decrease) {
  width: 16.7px;
  background: #fff;
}
.pdClass {
  position: absolute;
  right: 11px;
}
:deep(.el-checkbox__label) {
  padding-left: 4px;
}
:deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  color: #606266;
}
:deep(.el-input-number.is-controls-right .el-input-number__decrease) {
  top: 16px;
}
:deep(.el-input-number.is-controls-right[class*='medium'] [class*='increase']) {
  line-height: 15px;
}
:deep(.el-input-number.is-controls-right .el-input-number__increase) {
  height: 15px;
}
:deep(.el-input-number.is-controls-right .el-input-number__decrease) {
  height: 15px;
}
:deep(.el-input-number.is-controls-right[class*='medium'] [class*='decrease']) {
  line-height: 15px;
}
:deep(.el-input-number.is-controls-right .el-input__inner) {
  padding: 0 20px 0 12px;
}
.el-drawer .el-form .el-form-item {
  margin-bottom: 12px;
}
.red {
  color: #f56c6c;
}

:deep(.el-form-item__error) {
  padding-top: 0;
  white-space: nowrap;
}
.el-form-item.margin-bottom-none {
  margin-bottom: 0 !important;
}

.el-select {
  :deep(.el-tag) {
    height: 28px;
    line-height: 28px;
  }
  :deep(.el-input__inner) {
    vertical-align: bottom !important;
  }
}
</style>
<style lang="scss">
.dropdownSelect {
  .el-dropdown-menu {
    max-height: 310px;
    overflow-y: auto;
  }
}
</style>
