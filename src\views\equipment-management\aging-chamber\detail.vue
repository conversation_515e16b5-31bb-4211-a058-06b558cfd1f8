<template>
  <!-- 老化箱管理详情页 -->
  <DetailLayout :main-offset-top="94">
    <template #page-header>
      <div class="header-flex flex-between">
        <el-space :size="40" :spacer="spacer">
          <div class="item-column">
            <span class="item-label">设备名称：</span>
            <div class="item-content" style="position: relative">
              {{ routeData.boxName || '--' }}
              <el-tag v-if="routeData.boxStatus" size="small" effect="dark" :type="statusType[routeData.boxStatus]">{{
                dictionary['24'].all[routeData.boxStatus]
              }}</el-tag>
            </div>
          </div>
          <div class="item-column">
            <span class="item-label">设备编号：</span>
            <div class="item-content" style="position: relative">
              {{ routeData.deviceNumber || '--' }}
            </div>
          </div>
          <div class="item-column">
            <span class="item-label">已放置样品数：</span>
            <div class="item-content" style="position: relative">
              {{ tableList.length || '--' }}
            </div>
          </div>
        </el-space>
        <div class="btn-group">
          <el-button size="large" @click="goBack()">返回列表</el-button>
        </div>
      </div>
    </template>
    <div class="flex flex-col gap-2 h-full">
      <div class="module">
        <div class="title flex flex-row justify-between items-center">
          <span>已放置样品信息</span>
          <div>
            <el-button
              v-if="getPermissionBtn('BatchExtraction')"
              type="primary"
              :loading="detailLoading"
              :disabled="selectInfo.length == 0"
              plain
              size="small"
              @click="handleExtraction()"
              >批量取出</el-button
            >
            <el-button
              v-if="getPermissionBtn('AddLayout')"
              :loading="detailLoading"
              type="primary"
              size="small"
              @click="handleAddLayout()"
              >新增放样</el-button
            >
          </div>
        </div>
        <el-table
          ref="tableRef"
          v-loading="detailLoading"
          :data="tableList"
          class="dark-table base-table format-height-table"
          fit
          border
          height="auto"
          highlight-current-row
          size="medium"
          :row-style="
            () => {
              return 'cursor: pointer';
            }
          "
          @header-dragend="drageHeader"
          @row-click="handleRowClick"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" :width="colWidth.checkbox" align="center" fixed="left" />
          <el-table-column
            label="样品信息"
            prop="sampleName"
            :min-width="colWidth.name"
            align="left"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.sampleName || '--' }}
            </template>
          </el-table-column>
          <el-table-column label="试样数量" prop="sampleCount" :width="colWidth.status">
            <template #default="{ row }">
              <span>{{ row.sampleCount || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="试样温度℃" prop="sampleTemperature" :width="colWidth.status" align="center">
            <template #default="{ row }">
              <span>{{ row.sampleTemperature }}</span>
            </template>
          </el-table-column>
          <el-table-column label="放置时间" prop="equipmentManufactureName" :min-width="220" show-overflow-tooltip>
            <template #default="{ row }">
              <div>{{ row.startDateTime }} ~ {{ row.endDateTime }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="放置周期"
            prop="departmentName"
            align="left"
            :min-width="colWidth.date"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span v-if="row.periodHour">{{ row.periodHour }}小时</span>
              <span v-if="row.periodMinute">{{ row.periodMinute }}分钟</span>
            </template>
          </el-table-column>
          <el-table-column label="放样人" prop="arrivalDate" :width="colWidth.people">
            <template #default="{ row }">
              <UserTag :name="getNameByid(row.createBy) || row.createBy || '--'" />
            </template>
          </el-table-column>
          <el-table-column label="已放置时间" prop="createTime" :width="colWidth.dateranger">
            <template #default="{ row }">
              <span>{{ formateTimeDiff(row.startDateTime) || '--' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="module">
        <EchartsTemperature />
      </div>
      <!-- 快速放样 -->
      <DialogQuickLayout :dialog-visible="dialogLayout" :box-id="boxId" @closeDialog="closeLayoutDialog" />
    </div>
  </DetailLayout>
</template>

<script>
import { reactive, toRefs, ref, h, onMounted } from 'vue';
import router from '@/router/index.js';
import { ElDivider, ElMessage, ElMessageBox } from 'element-plus';
import UserTag from '@/components/UserTag';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate, differenceTimes } from '@/utils/formatTime';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { getLoginInfo } from '@/utils/auth';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import DetailLayout from '@/components/DetailLayout';
import EchartsTemperature from './components/echarts-temperature.vue';
import DialogQuickLayout from './components/DialogQuickLayout.vue';
import { sampleList, sampleOut } from '@/api/aging-chamber';
import { getDictionary } from '@/api/user';

export default {
  name: 'AgingChamberDetail',
  components: { DetailLayout, EchartsTemperature, DialogQuickLayout, UserTag },
  setup() {
    const route = useRoute();
    const store = useStore().state;
    const spacer = h(ElDivider, { direction: 'vertical' });
    const state = reactive({
      tableList: [],
      dialogLayout: false,
      detailInfo: {
        deviceList: [],
        applyDate: formatDate(new Date()),
        applyBy: getLoginInfo().accountId
      },
      statusType: {
        Running: 'success',
        Standby: 'warning',
        Maintenance: 'default',
        Fault: 'danger',
        Scrapped: 'info'
      },
      routeData: {
        boxName: route.query.boxName,
        deviceNumber: route.query.deviceNumber,
        boxStatus: route.query.boxStatus
      },
      editFromRef: ref(),
      dictionary: {
        24: {
          all: {},
          enable: {}
        }
      },
      tableRef: ref(),
      selectInfo: [],
      boxId: route.query.boxId,
      detailLoading: false,
      userOptions: store.common.nameList
    });
    const getList = async () => {
      state.detailLoading = true;
      const { data } = await sampleList({ boxId: state.boxId, sampleStatus: 0 }).finally((state.detailLoading = false));
      if (data) {
        state.tableList = data.data;
      }
    };
    const getDictionaryList = () => {
      Object.keys(state.dictionary).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          state.dictionary[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              state.dictionary[item].enable[optionItem.code] = optionItem.name;
            }
            state.dictionary[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    const formateTimeDiff = startDate => {
      const { hours, minutes } = differenceTimes(startDate, new Date());
      return `${hours}小时${minutes}分钟`;
    };
    /** 新增放样 */
    const handleAddLayout = () => {
      state.dialogLayout = true;
    };
    const handleRowClick = row => {
      state.tableRef.toggleRowSelection(
        row,
        !state.selectInfo.some(item => {
          return row.id === item.id;
        })
      );
    };
    /** 批量取出 */
    const handleExtraction = () => {
      const notReachedList = state.selectInfo
        .filter(item => {
          return new Date(item.endDateTime).getTime() > new Date().getTime();
        })
        .map(item => item.sampleName);
      if (notReachedList.length) {
        ElMessageBox({
          title: '是否取出当前样品',
          message: '当前样品未到达放置结束时间，是否确认提前取出<br/>' + notReachedList.join('，'),
          confirmButtonText: '确定',
          dangerouslyUseHTMLString: true,
          showCancelButton: false,
          closeOnClickModal: false,
          type: 'warning'
        })
          .then(() => {
            onExtraction();
          })
          .catch(() => {});
      } else {
        onExtraction();
      }
    };
    /** 批量取出放样 */
    const onExtraction = async () => {
      ElMessageBox({
        title: '批量取出',
        message: '是否确认取出？',
        confirmButtonText: '确定',
        showCancelButton: false,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(async () => {
          state.detailLoading = true;
          const params = state.selectInfo.map(item => item.id);
          const { data } = await sampleOut(params).finally((state.detailLoading = false));
          if (data) {
            ElMessage.success('操作成功！');
            getList();
          }
        })
        .catch(() => {});
    };
    // 选择
    const handleSelectionChange = val => {
      state.selectInfo = val;
    };
    /** 关闭快速放样 */
    const closeLayoutDialog = isRefresh => {
      state.dialogLayout = false;
      if (isRefresh) {
        getList();
      }
    };
    const goBack = () => {
      router.go(-1);
    };
    onMounted(() => {
      getList();
      getDictionaryList();
    });
    return {
      ...toRefs(state),
      colWidth,
      getList,
      handleRowClick,
      handleSelectionChange,
      handleExtraction,
      closeLayoutDialog,
      handleAddLayout,
      spacer,
      formatDate,
      getNameByid,
      differenceTimes,
      formateTimeDiff,
      drageHeader,
      goBack,
      getPermissionBtn
    };
  }
};
</script>
<style lang="scss" scoped>
.el-select {
  width: 100%;
}
:deep(.el-divider--vertical) {
  height: 40px;
}
.module {
  background-color: #fff;
  text-align: left;
  border-radius: 4px;
  padding: 10px 15px;
  flex: 1;
  .title {
    font-size: 17px;
    font-weight: 500;
    margin-bottom: 10px;
  }
}
</style>
