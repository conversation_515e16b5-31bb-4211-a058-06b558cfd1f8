<template>
  <el-dialog
    v-model="dialogVisible"
    title="钉钉授权登录"
    width="400px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div id="loginDT" style="transform: scale(0.8); text-align: center" />
  </el-dialog>
</template>
<script>
import { reactive, toRefs, watch, onBeforeUnmount, nextTick } from 'vue';

export default {
  name: 'DialogDingTalk',
  components: {},
  props: {
    dialog: {
      type: Boolean,
      default: false
    },
    gotoUrl: {
      type: String,
      default: ''
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const datas = reactive({
      dialogVisible: false,
      goto: '',
      appKey: 'dingauliwsnxljehd0si',
      appId: '1846114945',
      appSecret: ''
    });
    watch(props, newValue => {
      datas.dialogVisible = newValue.dialog;
      if (datas.dialogVisible) {
        datas.goto = newValue.gotoUrl;
        nextTick(() => {
          window.DDLogin({
            id: 'loginDT',
            goto: encodeURIComponent(newValue.gotoUrl), // 请参考注释里的方式
            style: 'border:none;background-color:#FFFFFF;',
            width: '365',
            height: '400'
          });
        });
      }
    });
    /** 钉钉登录 */
    const DDMessage = event => {
      const origin = event.origin;
      if (origin === 'https://login.dingtalk.com') {
        const loginTmpCode = event.data;
        console.log(datas.goto);
        console.log(loginTmpCode);
        const srcUrl = `https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=${datas.appKey}&response_type=code&scope=snsapi_login&redirect_uri=${datas.goto}&loginTmpCode=${loginTmpCode}`;
        window.open(srcUrl, '_self');
        // closeMessage()
      }
    };
    const closeMessage = () => {
      if (typeof window.addEventListener !== 'undefined') {
        window.addEventListener('message', DDMessage, false);
      }
    };
    const handleClose = () => {
      context.emit('closeDialog');
      datas.dialogVisible = false;
    };
    window.addEventListener('message', DDMessage, false);
    onBeforeUnmount(() => {
      window.removeEventListener('message', DDMessage, false);
    });
    return { ...toRefs(datas), DDMessage, handleClose, closeMessage };
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 100%;
}
:deep(.el-select) {
  width: 100%;
}
</style>
