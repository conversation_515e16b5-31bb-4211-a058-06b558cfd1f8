// base color
$blue: #409eff; // color-primary color-link
$light-blue: #0077db;
$red: #f56c6c; // color-danger
$pink: #f56c6c;
$green: #67c23a; // color-success
$tiffany: #4ab7bd;
$yellow: #e6a23c; // color-warning
$panGreen: #30b08f;

// sidebar
$menuText: #fff;
$menuActiveText: var(--tesPrimary, #00b38a); // #0077db;
$subMenuActiveText: #fff;

$menuBg: #19293d;
$menuHover: var(--menuHover); // #1c4364;

$subMenuBg: #142131;
$subMenuHover: var(--menuHover); // #1c4364;
$sideBarWidth: 220px;

//add
//-----------------------------------------------
// #0077db
// #99c9f1
// #e6f5ff
// primary-1: #E6F7F3
// primary-2: #CCF0E8
// primary-3: #99E1D0
// primary-4: #33C2A1
// primary-5: #00B38A
// primary-6: #00A17C
$tes-primary: var(--tesPrimary);
$tes-primary1: var(--tesPrimary1); // border-color primary-5
$tes-primary2: var(--tesPrimary2); // bg-color primary-9
$tes-primary3: var(--tesPrimary3); // light-color primary-7
$tes-primary4: var(--tesPrimary4); // secondary-color primary-3
$tes-blue: #409eff; // color-link
$tes-green: #67c23a; // color-success
$tes-yellow: #e6a23c; // color-warning
$tes-red: #f56c6c; // color-danger
$tes-grey: #909399; // color-info

// 主题背景色
$background-color: var(--backgroundColor, #fff);
// 主题背景色
$background-color2: var(--backgroundColor2);

// font color
$tes-font: #303133; // text-color-primary
$tes-font1: #606266; // text-color-regular
$tes-font2: #909399; // text-color-secondary
$tes-font3: #c0c4cc; // text-color-disabled
$tes-font4: #a8abb2; // text-color-placeholder

$user-tag-backGroundColor: #f4f4f5; // 人名tag背景色
$user-tag-color: $tes-font; // 人名tag字体色

// border color
$tes-border: #dcdfe6; // border-color
$tes-border1: #e4e7ed; // border-color-light
$tes-border2: #ebeef5; // border-color-ligter
$tes-border3: #f2f6fc; // border-color-extra-light

$border-color: #dcdfe6;
$border-color1: #c0c4cc;
$border-color2: #909399;
$border-color3: #606266;

// background color
$tes-bgB: #000000;
$tes-bgW: #ffffff;
$tes-bgC: #214a6d;
$tes-bgE: #cce4f8;
$tes-bgA: #fafafa;

// tag color
$tes-tag-important: #ff5555;
$tes-tag-general: #ffbb00;
$tes-tag-weaker: #33c2a1;

// tag border color
$tes-tagBorder-important: #ffbbbb;
$tes-tagBorder-general: #ffe499;
$tes-tagBorder-weaker: #ccf0e8;

// tag background color
$tes-tagBackground-important: #ffeeee;
$tes-tagBackground-general: #fff8e6;
$tes-tagBackground-weaker: #e6f7f3;

@mixin color_primary($color) {
  color: $color;
  /*判断匹配*/
  // [data-theme="primary1"] & {
  //     color:$color-primary1;
  // }
}
