<template>
  <!-- 打印 -->
  <el-dialog v-model="dialogShow" title="打印合格证" width="500px" :close-on-click-modal="false">
    <el-form ref="printTemplateFormRef" :model="printTemplateForm" :rules="printTemplateFormRules" label-position="top">
      <el-form-item label="合格证模板：" prop="printTemplate">
        <el-select v-model="printTemplateForm.printTemplateId" placeholder="" size="small" style="width: 100%">
          <el-option v-for="item in certificateTemplates" :key="item.id" :label="item.name" :value="item.id">
            <span style="float: left">{{ item.name }}</span>
            <span
              style="
                float: right;
                display: inline-block;
                color: #909399;
                font-size: 13px;
                max-width: 200px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              "
              >{{ item.description }}</span
            >
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancelDialog()">取消</el-button>
      <el-button type="primary" @click="handlePrint()">确定</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { ElMessage } from 'element-plus';
import { reactive, watch, toRefs, ref, onMounted } from 'vue';
import { certificateList } from '@/api/material';
import PrintSetting from '@/components/online-excel-batch-print/print-setting';
import router from '@/router';

export default {
  name: 'DialogBatchPrint',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    selectRow: {
      type: Array,
      default: () => []
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      selectData: [],
      printNumber: null,
      dialogLoading: false,
      dialogShow: false,
      certificateTemplates: [],
      printTemplateForm: {
        printTemplateId: '',
        paperSize: 'A5',
        paperOrientation: 'Portrait'
      }
    });

    const printTemplateFormRef = ref();

    const printTemplateFormRules = {
      printTemplateId: [{ required: true, message: '请选择要打印的模板', trigger: 'change' }]
    };

    const gotoTemplatePrint = async () => {
      const templateItem = state.certificateTemplates.find(item => item.id === state.printTemplateForm.printTemplateId);
      if (!templateItem) {
        ElMessage.warning('未找到模板！');
        return;
      }
      const fileUrl = templateItem.fileUrl;

      const ids = props.selectRow.map(item => item.sampleId);

      const certificatePrintIds = props.selectRow.map(item => item.id);

      await printTemplateFormRef.value.validate();
      router.push({
        path: '/qualityManagement/online-excel-batch-print',
        query: {
          ids: JSON.stringify(ids),
          certificatePrintIds: JSON.stringify(certificatePrintIds),
          paperSize: templateItem.paperSize,
          paperPrintWidth: templateItem.paperPrintWidth,
          paperPrintHeight: templateItem.paperPrintHeight,
          paperOrientation: templateItem.paperPrintOrientation,
          url: import.meta.env.DEV ? fileUrl.replace(window.location.host, '*************') : fileUrl
        }
      });
    };

    // 打印按钮
    const handlePrint = () => {
      gotoTemplatePrint();
    };

    // 获取合格证模板
    const getCertificateTemplates = async () => {
      const res = await certificateList({ page: '1', limit: '-1', categoryId: '' });
      if (res) {
        state.originCertificateTemplates = res.data.data.list.filter(item => item.category === 1 && item.status === 1);

        state.certificateTemplates = state.originCertificateTemplates;
      }
    };

    // 取消打印
    const cancelDialog = value => {
      state.dialogShow = false;
      context.emit('closeDialog', value);
    };

    onMounted(() => {
      getCertificateTemplates();
    });

    watch(
      () => props.dialogVisible,
      newValue => {
        state.dialogShow = props.dialogVisible;
        if (newValue) {
          state.printNumber = null;
          state.selectData = props.selectRow;
        }
      }
    );

    return {
      ...toRefs(state),
      cancelDialog,
      handlePrint,
      printTemplateFormRules,
      printTemplateFormRef
    };
  },
  data() {
    return { PrintSetting };
  }
};
</script>
<style lang="scss" scoped></style>
