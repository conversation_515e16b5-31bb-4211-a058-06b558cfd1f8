<template>
  <!-- 二维码弹出框 -->
  <el-dialog v-model="popupVisible" :title="title" :width="popupWidth" @close="hide">
    <div class="qr-code-container">
      <QrcodeVue :value="qrCodeValue" :size="qrCodeSize" class="mx-auto" />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="hide">关 闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, watch, getCurrentInstance, onBeforeUnmount } from 'vue';
import QrcodeVue from 'qrcode.vue';
import qs from 'qs';

export default {
  name: 'QRCodePopup',
  components: { QrcodeVue },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    value: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: ''
    },
    popupWidth: {
      type: [Number, String],
      default: 400
    },
    qrCodeSize: {
      type: Number,
      default: 280
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const { appContext } = getCurrentInstance();
    const bus = appContext.config.globalProperties.bus;

    const popupVisible = ref(false);
    const qrCodeValue = ref(qs.stringify(props.value));

    bus.$on('showQRCodePopup', data => {
      qrCodeValue.value = qs.stringify(data);
      show();
    });

    watch(
      () => props.visible,
      visible => {
        visible ? show() : hide();
      }
    );

    watch(
      () => props.value,
      value => {
        qrCodeValue.value = qs.stringify(value);
      }
    );

    onBeforeUnmount(() => {
      bus.$off('showQRCodePopup');
    });

    const show = () => {
      popupVisible.value = true;
      emit('update:visible', true);
    };

    const hide = () => {
      popupVisible.value = false;
      emit('update:visible', false);
    };
    return {
      popupVisible,
      qrCodeValue,
      show,
      hide
    };
  }
};
</script>
<style lang="scss" scoped>
.qr-code-container {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
