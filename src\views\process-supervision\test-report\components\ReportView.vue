<template>
  <el-table
    v-show="showCustomTable"
    ref="customTableRef"
    :key="dataTableKey"
    v-loading="dataLoading"
    :data="dataList"
    fit
    border
    size="medium"
    class="dark-table allocation-table base-table format-height-table"
    @header-dragend="drageHeader"
    @sort-change="sortChange"
    @selection-change="handleTableSelectionChange"
  >
    <el-table-column
      type="selection"
      align="center"
      :width="colWidth.checkbox"
      fixed="left"
      :selectable="checkSelectable"
    />
    <template v-for="(item, index) in list" :key="index">
      <el-table-column
        v-if="item.checkbox && item.isHide !== 1"
        :prop="item.field"
        :label="item.name"
        :sortable="Number(item.isOrder) === 1"
        :width="item.isMinWidth ? '' : getColWidth(item.colWidth)"
        :min-width="item.isMinWidth ? getColWidth(item.colWidth) : ''"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div v-if="item.type === 0">
            {{ row[item.field] || '--' }}
          </div>
          <div v-else-if="item.type === 1">
            <span class="nowrap blue-color" @click="handleRouteJump(row, item.styleContent)">{{
              row[item.field] || '--'
            }}</span>
          </div>
          <div v-else-if="item.type === 2">
            <UserTag :name="getNameByid(row[item.field]) || row[item.field] || '--'" />
          </div>
          <div v-else-if="item.type === 3">
            <el-tag size="small" effect="dark" :type="filterExamineStatus(row[item.field])[0]">
              {{ filterExamineStatus(row[item.field])[1] }}</el-tag
            >
          </div>
          <div v-else-if="item.type === 4">
            {{ row[item.field] || '--' }}{{ filterSampleUnitToName(row.sampleUnit) || row.unitName }}
          </div>
          <div v-else-if="item.type === 5">
            <span class="nowrap blue-color" @click="jumpApplicationDetail(row, item.styleContent)">{{
              row[item.field] || '--'
            }}</span>
          </div>
          <div v-else-if="item.type === 6">
            <span>{{ filterMaterialCode(row[item.field]) || '--' }}</span>
          </div>
          <div v-else-if="item.type === 7">
            <span>{{ filterResult(row[item.field])[1] }}</span>
          </div>
          <div v-else>
            {{ row[item.field] || '--' }}
          </div>
        </template>
      </el-table-column>
    </template>
    <el-table-column
      label="操作"
      :width="colWidth.operationMultiple"
      prop="caozuo"
      fixed="right"
      class-name="fixed-right"
    >
      <template #default="scope">
        <!-- <span class="blue-color" @click="handlePreview(scope.row)">预览</span> -->
        <div v-if="formInline.appoint === '1' && formInline.examineStatus === '1'">
          <span class="blue-color margin-right-10" @click="editDetail(scope.row)">编辑</span>
          <span class="blue-color margin-right-10" @click="handlePreview(scope.row)">预览</span>
          <el-dropdown trigger="click">
            <span class="el-dropdown-link blue-color" style="margin-left: 15px"><span class="el-icon-more" /></span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="submitReport(scope.row)"
                  ><span class="blue-color">提交</span></el-dropdown-item
                >
                <el-dropdown-item class="blue-color" @click="downloadReport(scope.row)">下载</el-dropdown-item>
                <el-dropdown-item @click="progressReport(scope.row)"
                  ><span class="blue-color">进度</span></el-dropdown-item
                >
                <el-dropdown-item @click="removeReport(scope.row)"
                  ><span class="blue-color">删除</span></el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <div
          v-else-if="
            formInline.appoint === '1' &&
            (formInline.examineStatus === '2' ||
              formInline.examineStatus === '3' ||
              formInline.examineStatus === '4' ||
              formInline.examineStatus === '5')
          "
        >
          <span class="blue-color margin-right-10" @click="approvalDetail(scope.row)">审批</span>
          <span class="blue-color margin-right-10" @click="handlePreview(scope.row)">预览</span>
          <el-dropdown trigger="click">
            <span class="el-dropdown-link blue-color" style="margin-left: 15px"><span class="el-icon-more" /></span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item class="blue-color" @click="downloadReport(scope.row)">下载</el-dropdown-item>
                <el-dropdown-item @click="progressReport(scope.row)"
                  ><span class="blue-color">进度</span></el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <div v-else-if="formInline.examineStatus === '6'">
          <span class="blue-color margin-right-10" @click="handlePreview(scope.row)">预览</span>
          <span class="blue-color margin-right-10" @click="downloadReport(scope.row)">下载</span>
          <span class="blue-color" @click="progressReport(scope.row)">进度</span>
        </div>
        <div v-else>
          <span class="blue-color margin-right-10" @click="handlePreview(scope.row)">预览</span>
          <span class="blue-color margin-right-10" @click="downloadReport(scope.row)">下载</span>
          <span v-if="!haveMoreOperation(scope.row)" @click="progressReport(scope.row)"
            ><span class="blue-color">进度</span></span
          >
          <el-dropdown v-else trigger="click" @visible-change="val => handleVisible(val, scope.row)">
            <span class="el-dropdown-link blue-color" style="margin-left: 15px"><span class="el-icon-more" /></span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="progressReport(scope.row)"
                  ><span class="blue-color">进度</span></el-dropdown-item
                >
                <el-dropdown-item v-if="rowOperation.edit" class="blue-color" @click="editDetail(scope.row)"
                  >编辑</el-dropdown-item
                >
                <!-- 审批不同权限的用户拥有不同的操作权限，后续经过调研再做审批 -->
                <el-dropdown-item v-if="rowOperation.approval" class="blue-color" @click="approvalDetail(scope.row)"
                  >审批</el-dropdown-item
                >
                <el-dropdown-item v-if="rowOperation.submit" @click="submitReport(scope.row)"
                  ><span class="blue-color">提交</span></el-dropdown-item
                >
                <el-dropdown-item v-if="rowOperation.remove" @click="removeReport(scope.row)"
                  ><span class="blue-color">删除</span></el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>
    </el-table-column>
  </el-table>

  <!-- <div v-show="!showSettings" class="settings-button">
    <el-button @click="showSettings = true">设置</el-button>
  </div> -->

  <el-drawer
    v-model="showDrawer"
    direction="rtl"
    title="编辑当前视图"
    @open="handleOpen"
    @close="handleClose"
    @opened="handleOpened"
  >
    <DrawerLayout :has-button-group="false" :has-page-header="false">
      <el-form
        id="view-form"
        label-width="80px"
        label-position="left"
        size="small"
        :model="viewForm"
        :rules="viewFormRules"
      >
        <el-row :gutter="40">
          <el-col :span="24">
            <el-form-item label="视图名称" size="small" prop="name" required>
              <span v-if="!isEditViewName">{{ viewForm.name }}</span>
              <el-input v-else v-model="viewForm.name" size="small" style="width: 70%" maxlength="20" />
              <el-button v-show="isEditViewName" size="small" @click="isEditViewName = !isEditViewName">确认</el-button>
              <el-button
                v-show="!isEditViewName"
                circle
                icon="el-icon-edit"
                style="border-color: #ffffff"
                size="small"
                @click="isEditViewName = !isEditViewName"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="默认视图"
              size="small"
              prop="isDefault"
              required
              style="margin-top: 12px; margin-bottom: 12px"
            >
              <el-radio-group v-model="viewForm.isDefault">
                <el-radio label="0" size="small">否</el-radio>
                <el-radio label="1" size="small">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-tabs tab-position="left" class="demo-tabs" :stretch="true">
        <el-tab-pane label="字段排序">
          <el-table
            id="fieldTable"
            ref="tableRef"
            :key="tableKey"
            :data="list"
            class="dark-table base-table format-height-table"
            @select="handleSelectBox"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" :width="55" />
            <el-table-column label="序号" width="50">
              <template #default="{ row }">
                <div>
                  {{ row.order + 1 }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="字段名" min-width="160" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.name }}
              </template>
            </el-table-column>
            <el-table-column label="排序" :width="65">
              <i class="tes-move iconfont" style="font-size: 12px; cursor: move" />
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="数据过滤">
          <span>待开发</span>
        </el-tab-pane>
      </el-tabs>
      <BottomPanel>
        <template #panel-content>
          <div style="text-align: right">
            <el-button size="small" @click="resetView()" @keyup.prevent @keydown.enter.prevent>重置</el-button>
            <el-button
              type="primary"
              size="small"
              :loading="saveLoading"
              @click="saveOrUpdateView()"
              @keyup.prevent
              @keydown.enter.prevent
              >保存</el-button
            >
          </div>
        </template>
      </BottomPanel>
    </DrawerLayout>
  </el-drawer>
</template>
<script>
import { colWidth } from '@/data/tableStyle';
import { ref, reactive, toRefs, onMounted, watch, nextTick, computed } from 'vue';
import { getColWidth } from '@/utils/func/customTable';
import Sortable from 'sortablejs';
import UserTag from '@/components/UserTag';
import { getNameByid } from '@/utils/common';
import router from '@/router/index.js';
import { getInspectionList } from '@/api/inspection-application';
import { filterMaterialCode, filterSampleUnitToName } from '@/utils/formatJson';
import { drageHeader } from '@/utils/formatTable';
import DrawerLayout from '@/components/DrawerLayout';
import BottomPanel from '@/components/PageComponents/BottomPanel';
import { viewFormRules } from '@/utils/func/customTable';

/**
 * 0.默认，没人样式
 * 1.样品编号/报告编号 跳转
 * 2.用户名显示
 * 3.状态标签显示
 * 4.样品单位
 * 5.申请单号跳转
 * 6.物资分类
 */

export default {
  name: 'CustomTable',
  components: { UserTag, DrawerLayout, BottomPanel },
  props: {
    viewInfo: {
      type: Object,
      default: function () {
        return {
          name: '',
          isDefault: false
        };
      }
    },
    dataLoading: {
      type: Boolean,
      default: false
    },
    showSettings: {
      type: Boolean,
      default: false
    },
    tableHead: {
      type: Array,
      default: function () {
        return [];
      }
    },
    tableData: {
      type: Array,
      default: function () {
        return [];
      }
    },
    currentAccountId: {
      type: String,
      default: function () {
        return '';
      }
    },
    formInline: {
      type: Object,
      default: function () {
        return {
          param: '',
          endFormationDateTime: '',
          startFormationDateTime: '',
          startUpdateDateTime: '',
          endUpdateDateTime: '',
          reportFormationByUserId: '',
          mateType: '',
          examineStatus: '1',
          appoint: '1'
        };
      }
    }
  },
  emits: [
    'submitReport',
    'progressReport',
    'approvalDetail',
    'downloadReport',
    'removeReport',
    'haveMoreOperation',
    'handleVisible',
    'postView',
    'tableSelect',
    'loadingComplete',
    'resetView',
    'closeView',
    'handlePreview',
    'editDetail'
  ],
  setup(props, context) {
    const tableRef = ref(null);
    const data = reactive({
      showCustomTable: true,
      list: props.tableHead,
      dataList: props.tableData,
      tableKey: 0,
      dataTableKey: 0,
      noSelectList: [],
      totalCount: props.tableHead.length,
      hideList: [],
      saveLoading: false,
      selectedList: [],
      isEditViewName: false,
      viewForm: {
        name: props.viewInfo.name,
        isDefault: props.viewInfo.isDefault
      }
    });

    // #region 特殊样式列

    function handleRouteJump(row, styleContent) {
      const queryParams = {};
      styleContent.params.forEach(item => {
        if (item.name) {
          if (row[item.code] && (item.value === 0 || !item.value)) {
            queryParams[item.name] = row[item.code];
          } else {
            queryParams[item.name] = item.value;
          }
        } else {
          if (row[item.code] && (item.value === 0 || !item.value)) {
            queryParams[item.code] = row[item.code];
          } else {
            queryParams[item.code] = item.value;
          }
        }
      });
      router.push({
        path: styleContent.path,
        query: queryParams
      });
    }

    // 查看申请详情
    const jumpApplicationDetail = row => {
      getInspectionList({ param: `${row.presentationCode}` }).then(res => {
        if (res && res.status === 200) {
          router.push({
            name: 'TestReportApplication',
            query: { id: res.data.data.list[0].id, flag: 1 }
          });
        }
      });
    };

    // 过滤审批状态颜色
    const filterExamineStatus = status => {
      // 审批状态  1、待提交2、待审核、3、待签字4、待盖章5、待发送6、已完成
      if (status) {
        const classMap = {
          1: ['info', '待提交'],
          2: ['warning', '待审核'],
          3: ['warning', '待签字'],
          4: ['warning', '待盖章'],
          5: ['warning', '待发送'],
          6: ['success', '已完成']
        };
        return classMap[status];
      } else {
        return ['info', '--'];
      }
    };

    // 过滤检验结论
    const filterResult = status => {
      if (status) {
        const classMap = {
          0: ['success', '合格'],
          1: ['warning', '不合格'],
          2: ['info', '不判定']
        };
        return classMap[status];
      } else {
        return ['info', '--'];
      }
    };

    // #endregion

    // #region 操作列

    // 预览点击
    const handlePreview = row => {
      context.emit('handlePreview', row);
    };

    // 点击编辑
    const editDetail = row => {
      context.emit('editDetail', row);
    };

    // 点击提交
    const submitReport = row => {
      context.emit('submitReport', row);
    };

    // 点击进度
    const progressReport = row => {
      context.emit('progressReport', row);
    };
    // 点击审批
    const approvalDetail = async row => {
      context.emit('approvalDetail', row);
    };
    // 下载报告
    const downloadReport = row => {
      context.emit('downloadReport', row);
    };
    // 删除报告
    const removeReport = row => {
      context.emit('removeReport', row);
    };

    const haveMoreOperation = row => {
      context.emit('haveMoreOperation', row);
    };

    const handleVisible = row => {
      context.emit('handleVisible', row);
    };

    // #endregion

    // #region 拖拽勾选

    function updateSelectionBox() {
      nextTick(() => {
        tableRef.value.clearSelection();
        data.list.forEach(item => {
          if (data.noSelectList.findIndex(obj => obj.field === item.field) === -1) {
            tableRef.value.toggleRowSelection(item, true);
            item.checkbox = true;
          } else {
            tableRef.value.toggleRowSelection(item, false);
            item.checkbox = false;
          }
        });
      });
    }

    function updateDataList() {
      const oldDataList = [].concat(data.dataList);
      const newDataList = [];
      oldDataList.forEach(item => {
        const newData = {};
        data.list.concat(data.hideList).forEach(columns => {
          newData[columns.field] = item[columns.field];
        });
        newDataList.push(newData);
      });
      data.dataList = newDataList;
      data.dataTableKey += 1;
      nextTick(() => {
        context.emit('loadingComplete', true);
      });
    }

    function handleSelectionChange(selectedRows) {
      if (selectedRows.length === 0) {
        data.noSelectList = [].concat(data.list);
        data.showCustomTable = false;
        data.list.forEach(item => {
          item.checkbox = false;
        });
        updateDataList();
      } else if (selectedRows.length === data.selectedList.length) {
        selectedRows.forEach(item => {
          deleteNoSelectList(item);
        });
        data.list.forEach(item => {
          if (selectedRows.findIndex(obj => obj.field === item.field) !== -1) {
            item.checkbox = true;
          } else {
            item.checkbox = false;
          }
        });
        data.showCustomTable = true;
        updateDataList();
      } else if (selectedRows.length === data.list.length) {
        data.noSelectList = [];
        data.showCustomTable = true;
        data.list.forEach(item => {
          item.checkbox = true;
        });
        updateDataList();
      } else {
        // selectedRows.forEach(item => {
        //   const rowIndex = data.noSelectList.findIndex(obj => obj.field === item.field)
        //   if (rowIndex !== -1) {
        //     data.noSelectList.splice(rowIndex, 1)
        //   }
        // })
        // data.showCustomTable = true
        // updateDataList()
      }
    }

    function handleSelectBox(selection, row) {
      row.checkbox = !row.checkbox;
      data.showCustomTable = selection.length > 0;
      if (row.checkbox) {
        deleteNoSelectList(row);
      } else {
        addNoSelectList(row);
      }
      updateDataList();
    }

    function addNoSelectList(row) {
      if (data.noSelectList.findIndex(obj => obj.field === row.field) === -1) {
        data.noSelectList.push(row);
      }
    }

    function deleteNoSelectList(row) {
      const rowIndex = data.noSelectList.findIndex(obj => obj.field === row.field);
      if (rowIndex !== -1) {
        data.noSelectList.splice(rowIndex, 1);
      }
    }

    function handleOpen() {
      nextTick(() => {
        data.tableKey += 1;
        changeFieldTableSelection();
      });
      // updateDataList()
    }

    function handleClose() {
      context.emit('closeView', false);
    }

    function handleOpened() {
      // updateDataList()
    }

    // 行拖拽
    function rowDrop() {
      // 获取当前表格
      const tbody = document.getElementById('fieldTable').querySelector('.el-table__body-wrapper tbody');
      Sortable.create(tbody, {
        animation: 150,
        handle: '.tes-move',
        draggable: '.el-table__row',
        ghostClass: 'ghost',
        // 拖动对象移动样式
        dragClass: 'drag',
        forceFallback: true,
        onChoose(evt) {
          // console.log(evt)
        },
        onEnd(evt) {
          // console.log(evt.oldIndex, evt.newIndex)
          if (evt.oldIndex !== evt.newIndex) {
            // 移除原来的数据
            const currRow = data.list.splice(evt.oldIndex, 1)[0];
            // 移除原来的数据并插入新的数据
            data.list.splice(evt.newIndex, 0, currRow);
            data.list.forEach((value, index) => {
              value.order = index;
            });
          }
          updateSelectionBox();
          data.tableKey += 1;
          updateDataList();
        }
      });
    }

    // 是否禁用checkbox
    const checkSelectable = (row, index) => {
      if (row.examineByUserId !== props.currentAccountId && row.examineStatus !== 1) {
        return false;
      } else {
        return true;
      }
    };

    // #endregion

    // #region 新增或修改视图

    function resetView() {
      const resetData = true;
      context.emit('resetView', resetData);
    }

    function saveOrUpdateView() {
      data.saveLoading = true;
      const totalList = data.list.concat(data.hideList);
      const configList = [];
      totalList.forEach(item => {
        configList.push({
          columnWidth: item.colWidth || 200,
          cssContent: item.styleContent,
          cssType: item.type,
          fieldKey: item.field,
          fieldName: item.name,
          id: item.configId || '',
          isHide: item.isHide,
          isMinWidth: item.isMinWidth,
          isOrder: 1,
          isShow: item.checkbox ? 1 : 0,
          order: item.order,
          viewId: item.viewId
        });
      });

      const viewData = {
        viewInfo: { name: data.viewForm.name, isDefault: data.viewForm.isDefault },
        configList: configList
      };
      context.emit('postView', viewData);
      data.saveLoading = false;
    }

    function handleTableSelectionChange(selection) {
      context.emit('tableSelect', selection);
    }

    function sortChange(data) {
      // const { prop, order } = data
    }

    function changeFieldTableSelection() {
      if (tableRef.value) {
        tableRef.value?.clearSelection();
        data.selectedList.forEach(item => {
          nextTick(() => {
            data.list.find(obj => {
              if (obj.field === item.field) {
                tableRef.value?.toggleRowSelection(obj, true);
              }
            });
          });
        });
      }
    }

    // #endregion

    onMounted(() => {});

    watch(
      () => props.tableHead,
      (newValue, oldValue) => {
        data.totalCount = newValue.length;
        data.list = newValue.filter(item => item.isHide !== 1);
        data.hideList = newValue.filter(item => item.isHide === 1);
        data.noSelectList = [];
        const selectedItems = data.list.filter(item => item.checkbox);
        const noSelectedItems = data.list.filter(item => !item.checkbox);
        data.noSelectList = noSelectedItems;
        data.selectedList = selectedItems;
        changeFieldTableSelection();
        updateDataList();
      }
    );

    watch(
      () => props.tableData,
      (newValue, oldValue) => {
        data.dataList = newValue;
        updateDataList();
      }
    );

    watch(
      () => props.viewInfo.name,
      (newValue, oldValue) => {
        data.viewForm.name = newValue;
      }
    );
    watch(
      () => props.viewInfo.isDefault,
      (newValue, oldValue) => {
        data.viewForm.isDefault = newValue;
      }
    );

    watch(
      () => data.tableKey,
      (newValue, oldValue) => {
        nextTick(() => {
          rowDrop();
        });
      }
    );

    const showDrawer = computed({
      get: () => props.showSettings,
      set: val => context.emit('closeView', val)
    });

    return {
      ...toRefs(data),
      colWidth,
      tableRef,
      updateDataList,
      getColWidth,
      getNameByid,
      handleRouteJump,
      jumpApplicationDetail,
      filterExamineStatus,
      filterResult,
      removeReport,
      downloadReport,
      approvalDetail,
      handlePreview,
      editDetail,
      submitReport,
      progressReport,
      haveMoreOperation,
      handleVisible,
      handleSelectBox,
      handleSelectionChange,
      deleteNoSelectList,
      saveOrUpdateView,
      handleTableSelectionChange,
      checkSelectable,
      resetView,
      filterMaterialCode,
      filterSampleUnitToName,
      drageHeader,
      sortChange,
      handleOpen,
      handleOpened,
      handleClose,
      showDrawer,
      viewFormRules
    };
  }
};
</script>
<style lang="scss" scoped>
.pop-zr {
  width: 400px;
  position: fixed;
  top: 200px;
  right: 20px;
  transition: all 0.2s ease 0s;
  z-index: 1000;

  .pop-content {
    max-height: 500px;
    overflow-y: auto;
  }

  #title {
    height: 1rem;
    width: 100%;
    text-align: left;
    margin-bottom: 5px;
    font-size: 1rem;
    font-weight: bold;
    line-height: 1rem;
  }

  .i-close {
    position: absolute;
    top: 5px;
    right: 5px;
    cursor: pointer;
  }
}

.settings-button {
  width: 280px;
  position: fixed;
  top: 180px;
  right: 50px;
  transition: all 0.2s ease 0s;
  z-index: 1000;
}

.blue-color {
  color: $tes-primary;
  cursor: pointer;
}

#custom-dashboard.el-card {
  :deep(.el-card__body) {
    padding: 15px 5px 5px;
  }
}

#view-form {
  :deep(.el-form-item.el-form-item--small) {
    margin: 0px;
  }
}
</style>
