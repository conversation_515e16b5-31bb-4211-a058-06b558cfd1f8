<template>
  <div class="bottom-panel" style="height: auto">
    <div v-if="hasPanelTitle" class="panel-header">
      <slot name="panel-title" />
    </div>
    <div class="panel-content">
      <slot name="panel-content" />
    </div>
  </div>
</template>
<script>
import { reactive, toRefs, computed } from 'vue';
import store from '@/store';

export default {
  name: 'BottomPanel',
  props: {
    panelHeight: {
      type: String,
      default: 'auto'
    },
    hasPanelTitle: {
      type: Boolean,
      default: true
    }
  },
  emits: ['setMainOffset'],
  setup(props, context) {
    const data = reactive({
      showPanel: false,
      show: false,
      topHeight: 80,
      headerPadding: 0,
      actionWord: '展开',
      marginWidth: 1.714286
    });

    const showSideBar = computed({
      get: () => store.state.app.sidebar
    });
    return {
      ...toRefs(data),
      showSideBar
    };
  }
};
</script>

<style lang="scss" scoped>
.bottom-panel {
  height: 100px;
  //margin-left: $sideBarWidth;
  position: absolute;
  background: $background-color;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
  bottom: 0;
  right: 0;
  width: 100%;
  z-index: 1000;
}
.panel-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px;
  :slotted(.panel-header-left) {
    display: flex;
    align-items: center;
    .title {
      font-size: 18px;
      height: 32px;
      line-height: 32px;
      color: $tes-font;
    }
    .standard-controller {
      padding-left: 20px;
      .standard-label {
        width: 80px;
        font-size: 12px;
      }
      .el-input--small {
        width: 88px;
        .el-input__inner {
          height: 24px;
          line-height: 24px;
        }
      }
      .standard-space {
        padding: 0 4px;
        color: #a8abb2;
      }
    }
  }
}
.panel-content {
  padding: 0.5rem 2rem;
  height: calc(100% - 64px);
  width: 100%;
  text-align: center;
  :slotted(.no-data) {
    margin: 0 auto;
    color: $tes-font2;
  }
  :slotted(.el-descriptions) {
    .el-descriptions__label {
      background: #f5f7fa;
      color: $tes-font1;
      font-weight: normal;
      width: 150px;
    }
    .el-descriptions__content {
      color: $tes-font;
    }
  }

  :slotted(.no-data) {
    height: 32px;
    line-height: 32px;
  }
}
</style>
