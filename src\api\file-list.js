import request from '@/utils/request';

// 文件分类-结构树
export function getFileListTree(data) {
  return request({
    url: '/api-document/document/category/listTree',
    method: 'post',
    data
  });
}
// 文件分类-添加
export function addFileTree(data) {
  return request({
    url: '/api-document/document/category/add',
    method: 'post',
    data
  });
}
// 文件分类-编辑
export function editFileTree(data) {
  return request({
    url: '/api-document/document/category/edit',
    method: 'post',
    data
  });
}
// 文件分类-删除
export function deleteFileTree(data) {
  return request({
    url: '/api-document/document/category/delete',
    method: 'post',
    data
  });
}
// 删除之前的判断是否能删除该目录
export function canDelete(data) {
  return request({
    url: '/api-document/document/category/candelete',
    method: 'post',
    data
  });
}
// 文件分类-获取文件分类当前权限
export function getFilePermission(data) {
  return request({
    url: '/api-document/document/category/permission/info',
    method: 'post',
    data
  });
}
// 文件分类-设置权限
export function editFilePermission(data) {
  return request({
    url: '/api-document/document/category/permission/edit',
    method: 'post',
    data
  });
}
// 文件管理-查询
export function getFileList(data) {
  return request({
    url: '/api-document/document/file/list',
    method: 'post',
    data
  });
}
// 文件管理-上传/更新
export function uploadFileList(data, callback) {
  return request({
    url: '/api-document/document/file/upload',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: function (progressEvent) {
      // 原生获取上传进度的事件
      callback(progressEvent);
    },
    data
  });
}
// 文件管理-新增
export function addFileList(data) {
  return request({
    url: '/api-document/document/file/add',
    method: 'post',
    data
  });
}
// 文件管理-预览
export function previewFileList(fileId) {
  return request({
    url: `/api-document/document/file/preview/${fileId}`,
    responseType: 'blob',
    headers: { 'Content-Type': 'application/octet-stream; charset=utf-8' },
    method: 'get'
  });
}
// 文件管理-下载
export function downloadFileList(fileId) {
  return request({
    url: `/api-document/document/file/download/${fileId}`,
    responseType: 'blob',
    headers: { 'Content-Type': 'application/octet-stream; charset=utf-8' },
    method: 'get'
  });
}
// 文件管理-详情
export function detailFileList(data) {
  return request({
    url: '/api-document/document/file/detail',
    method: 'post',
    data
  });
}
// 文件管理-编辑
export function editFileList(data) {
  return request({
    url: '/api-document/document/file/edit',
    method: 'post',
    data
  });
}
// 文件管理-标签保存
export function saveTags(data) {
  return request({
    url: '/api-document/document/tags/save',
    method: 'post',
    data
  });
}
// 文件管理-标签提醒
export function tipsTags(data) {
  return request({
    url: '/api-document/document/tags/tips',
    method: 'post',
    data
  });
}
// 文件管理-删除
export function deleteFile(data) {
  return request({
    url: '/api-document/document/file/delete',
    method: 'post',
    data
  });
}
// 文件管理-收藏文件
export function fileFavorite(fileId) {
  return request({
    url: `/api-document/document/favorite/add/${fileId}`,
    method: 'get'
  });
}
// 文件管理-取消收藏文件
export function cancleFileFavorite(fileId) {
  return request({
    url: `/api-document/document/favorite/cancel/${fileId}`,
    method: 'get'
  });
}
