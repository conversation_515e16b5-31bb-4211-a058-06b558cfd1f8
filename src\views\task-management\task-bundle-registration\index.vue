<template>
  <!-- 委托登记列表 -->
  <ListLayout>
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="condition">
          <el-input
            v-model="formInline.condition"
            v-focus
            v-trim
            placeholder="请输入委托编号/委托方/缴款方"
            class="ipt-360"
            size="large"
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter="onSubmit"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0px">
          <el-button size="large" type="primary" @click="onSubmit">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
          <el-button size="large" type="text" class="searchBtn" @click="search" @keyup.prevent @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
          /></el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('AddTaskBtn')"
        size="large"
        type="primary"
        icon="el-icon-plus"
        @click="addIA"
        @keyup.prevent
        @keydown.enter.prevent
        >新增委托</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" :model="searchForm" label-width="110px" label-position="right">
            <el-form-item label="登记日期：">
              <el-date-picker
                v-model="searchForm.bzDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="changeBZTime"
              />
            </el-form-item>
            <el-form-item label="登记人：">
              <el-select
                v-model="searchForm.registerUserId"
                class="owner-select"
                placeholder="选择登记人"
                size="small"
                clearable
                filterable
                :filter-method="filterUserList"
                @focus="filterUserList(null)"
                @change="changeUser"
              >
                <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-radio-group v-model="radioData" size="small" @change="changeRadio">
        <el-radio-button label="全部" />
        <el-radio-button label="待提交" />
        <el-radio-button label="待评审" />
        <el-radio-button label="待确认" />
        <el-radio-button label="已确认" />
        <el-radio-button label="已拒绝" />
      </el-radio-group>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column label="序号" type="index" width="50" />
      <el-table-column label="委托编号" prop="entrustNo " :width="colWidth.orderNo" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <span class="nowrap blue-color" @click="iaDetail(row)">{{ row.entrustNo || '--' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="委托单位" prop="entrustName " :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.entrustName || '--' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="所属运维单位" prop="entrustName " :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.entrustName || '--' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="任务来源" prop="entrustName " :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.entrustName || '--' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="委托类型" prop="entrustName " :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.entrustName || '--' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="样品数量" prop="entrustCost" :width="120" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="Number(row.entrustCost) > 0 || row.entrustCost === '0'" size="small">
            {{ `${row.entrustCost}` }}
          </span>
          <span v-else>--</span>
        </template>
      </el-table-column>

      <el-table-column label="截止日期" prop="regDate" :width="colWidth.date" sortable>
        <template #default="{ row }">
          <span v-if="row.regDate">{{ formatDate(row.regDate) }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        :width="colWidth.operationMultiple"
        prop="caozuo"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="scope">
          <span class="blue-color" @click="iaDetail(scope.row)">查看</span>
          <span
            v-if="(scope.row.status === 0 || scope.row.status === 4) && getPermissionBtn('TaskEditBtn')"
            class="blue-color"
            @click="editTask(scope.row)"
            >编辑</span
          >
          <span
            v-if="(scope.row.status === 1 || scope.row.status == 2) && getPermissionBtn('TaskReviewBtn')"
            class="blue-color"
            @click="reviewTask(scope.row)"
            >审批</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
    <template #other>
      <el-dialog
        v-model="dialogImport"
        title="数据导入"
        :close-on-click-modal="false"
        width="680px"
        @close="dialogImport = false"
      >
        <div class="title">为确保数据导入的准确性，请按照以下规则进行导入</div>
        <div class="bt">下载表单模板</div>
        <ul class="uploadRules">
          <li>
            请按照<span class="blue-color" @click="downLoadFile('设备台账导入.xls')">设备台账导入.xlsx</span
            >在模板内录入数据
          </li>
          <li>只导入第一张工作表（sheet1）</li>
        </ul>
        <div>
          <el-upload
            ref="uploadRef"
            :action="uploadAction"
            :headers="headerconfig"
            :auto-upload="false"
            :limit="1"
            :on-exceed="handleExceed"
            :before-upload="beforeUpload"
            :on-success="handleFileSuccess"
          >
            <el-button size="small" type="primary" plain>选择上传文件</el-button>
          </el-upload>
        </div>
        <ul class="uploadRules">
          <li>请上传*.xls，*.xlsx格式文件；</li>
          <li>目前一次性最多上传5000条数据；</li>
          <li>文件大小不超过10M；</li>
        </ul>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogImport = false">取 消</el-button>
            <el-button type="primary" @click="submitUpload">确认上传</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 新增委托 -->
      <DialogTaskRegistration :show="showAddInfoDialog" @close="closeAddInfo" />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import router from '@/router/index.js';
// import { useRoute } from 'vue-router'
import Pagination from '@/components/Pagination';
// import UserTag from '@/components/UserTag'
import { cancelInspection, restoreInspection, submitInspection } from '@/api/inspection-application';
import { getTaskRegistrationList } from '@/api/task-registration';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { useStore } from 'vuex';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import _ from 'lodash';
// import { reportAudit } from '@/api/permission'
import { addByTemp } from '@/api/messageAgent';
import { mapGetters } from 'vuex';
import DialogTaskRegistration from './components/DialogTaskRegistration.vue';
import ListLayout from '@/components/ListLayout';
import { colWidth } from '@/data/tableStyle';
import { filterStatus } from './func/format';
import { getToken } from '@/utils/auth';
import { fiberInventoryReportUploadUrl } from '@/api/uploadAction';

export default {
  name: 'TaskRegistration',
  components: { Pagination, DialogTaskRegistration, ListLayout },
  setup() {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    // const route = useRoute()
    // console.log(route.query)
    const editFrom = ref(null);
    const otherForm = reactive({
      currentAccountId: getLoginInfo().accountId,
      auditNameList: [],
      dialogImport: false,
      uploadAction: fiberInventoryReportUploadUrl(),
      headerconfig: {
        Authorization: getToken()
      },
      activeName: '0',
      showS: false,
      type: 'info',
      formInline: {
        condition: '',
        endTime: '',
        startTime: '',
        registerUserId: '',
        type: ''
      },
      searchForm: {
        registerUserId: '',
        bzDateRange: ''
      },
      types: store.user.materialList,
      userOptions: store.common.nameList,
      copyUserOptions: store.common.nameList,
      list: [],
      content: '',
      radioData: '全部',
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: ''
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ],
      showAddReport: false,
      tableRef: ref(),
      showAddInfoDialog: false
    });

    // 查询
    function onSubmit() {
      // console.log(otherForm.formInline)
      // console.log(otherForm.searchForm)
      proxy.getList();
    }
    // 重置
    function reset() {
      // console.log('reset')
      editFrom.value.resetFields();
      otherForm.formInline = {
        condition: '',
        endTime: '',
        startTime: '',
        registerUserId: '',
        type: ''
      };
      otherForm.radioData = '全部';
      otherForm.searchForm = {
        registerUserId: '',
        bzDateRange: ''
      };
      otherForm.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: null
      };
      proxy.getList();
    }
    // 打开 高级搜索
    const search = () => {
      otherForm.showS = !otherForm.showS;
      if (otherForm.activeName === '0') {
        otherForm.activeName = '1';
      } else {
        otherForm.activeName = '0';
      }
    };
    // 排序
    const sortChange = data => {
      const { prop, order } = data;
      // console.log(prop)
      // console.log(order)
      otherForm.listQuery.orderBy = prop;
      if (order === 'ascending') {
        otherForm.listQuery.isAsc = true;
      } else if (order === 'descending') {
        otherForm.listQuery.isAsc = false;
      } else {
        otherForm.listQuery.isAsc = null;
      }
    };
    // 添加消息待办
    const addMsg = () => {
      // 添加消息待办
      const params = {
        eventCode: 'M012',
        receiverType: '1',
        senderName: getNameByid(otherForm.currentAccountId),
        receiverIds: '',
        receiverNames: '',
        c_ids: '',
        c_b_samplesIdArray: '',
        c_b_sampleNoArray: '',
        c_b_reportNoArray: ''
      };
      addByTemp(params).then(res => {
        if (res !== false) {
          // console.log(res.data)
        }
      });
    };

    // 新增
    const addIA = () => {
      // console.log(otherForm.tableRef)
      // router.push({ name: 'TaskBundleRegistrationDetail', query: { id: 0, flag: 0 }})
      otherForm.showAddInfoDialog = true;
    };
    // 关闭-新增
    const closeAddIA = value => {};
    // 关闭-新增检验单弹出框
    const closeAddInfo = value => {
      otherForm.showAddInfoDialog = value;
    };

    // 点击审批
    const reviewTask = row => {
      // console.log(row)
      router.push({ name: 'TaskBundleRegistrationDetail', query: { id: row.id, flag: 3 } });
    };

    // 点击编辑
    const editTask = row => {
      // console.log(row)
      router.push({ name: 'TaskBundleRegistrationDetail', query: { id: row.id, flag: 2 } });
    };
    // 查看
    const iaDetail = row => {
      // console.log(row)
      if (row.status === 0 || row.status === 4) {
        // 待提交状态可以编辑
        router.push({ name: 'TaskBundleRegistrationDetail', query: { id: row.id, flag: 2 } });
      } else {
        // 非待提交，只能看详情
        router.push({ name: 'TaskBundleRegistrationDetail', query: { id: row.id, flag: 1 } });
      }
    };
    // 点击提交
    const submitIA = row => {
      // console.log(row)
      if (row.list.length > 0) {
        ElMessageBox({
          title: '提示',
          message: '确认提交吗？提交后不可编辑',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: true,
          type: 'warning'
        })
          .then(() => {
            submitInspection(row.id).then(res => {
              if (res !== false) {
                ElMessage.success('编号：' + row.no + '，提交成功');
                proxy.getList();
              }
            });
          })
          .catch(() => {});
      } else {
        ElMessage.warning('提交前请先添加样品');
      }
    };

    // 作废
    const cancleIA = row => {
      // console.log(row)
      ElMessageBox({
        title: '提示',
        message: '确定作废当前检验申请单吗？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          cancelInspection(row.id).then(res => {
            if (res !== false) {
              ElMessage.success('编号：' + row.no + '，已作废');
              proxy.getList();
            }
          });
        })
        .catch(() => {});
    };
    const handleRestore = row => {
      ElMessageBox({
        title: '提示',
        message: '确定还原当前检验申请单吗？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          restoreInspection(row.id).then(res => {
            if (res) {
              ElMessage.success('编号：' + row.no + '，已还原');
              proxy.getList();
            }
          });
        })
        .catch(() => {});
    };
    // 切换tab
    const changeRadio = value => {
      // console.log(value)
      const param = {
        全部: '',
        待提交: '0',
        待评审: '1',
        待确认: '2',
        已确认: '3',
        已拒绝: '4'
      };
      otherForm.formInline.status = param[value];
      proxy.getList();
    };

    // 高级搜索-登记日期-change
    const changeBZTime = date => {
      otherForm.formInline.startTime = date ? formatDate(date[0]) : '';
      otherForm.formInline.endTime = date ? formatDate(date[1]) : '';
    };
    // 高级搜索-登记人-change
    const changeUser = id => {
      otherForm.formInline.registerUserId = id;
    };
    // 高级搜索-检验类型-change
    const changeType = type => {
      otherForm.formInline.type = type + '';
    };
    // 过滤登记人
    const filterUserList = val => {
      if (val) {
        const list = [];
        otherForm.copyUserOptions.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        otherForm.userOptions = list;
      } else {
        otherForm.userOptions = otherForm.copyUserOptions;
      }
    };
    const handleImport = () => {
      otherForm.dialogImport = true;
    };
    // 上传文件的限制
    const beforeUpload = file => {
      var fileName = '';
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过10M');
        return false;
      } else if (fileName !== 'xls' && fileName !== 'xlsx') {
        proxy.$message.error('仅支持.xls，.xlsx文件扩展名');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };
    // 下载附件
    const downLoadFile = fileName => {
      const a = document.createElement('a');
      a.href = '/staticFile/' + fileName;
      a.download = fileName;
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      a.remove();
    };
    const submitUpload = () => {
      otherForm.uploadRef.submit();
    };
    const handleExceed = files => {
      otherForm.uploadRef.clearFiles();
      otherForm.uploadRef.handleStart(files[0]);
    };
    // 上传成功的钩子
    const handleFileSuccess = (res, file) => {
      if (res.code === 200) {
        proxy.$message.success(res.message);
        otherForm.dialogImport = false;
        proxy.getLeftTree();
      } else {
        proxy.$message.error(res.message);
      }
    };
    return {
      filterStatus,
      beforeUpload,
      handleFileSuccess,
      handleExceed,
      submitUpload,
      downLoadFile,
      handleImport,
      closeAddIA,
      handleRestore,
      addIA,
      addMsg,
      changeType,
      editTask,
      reviewTask,
      drageHeader,
      formatDate,
      changeUser,
      changeBZTime,
      getNameByid,
      changeRadio,
      filterUserList,
      closeAddInfo,
      sortChange,
      editFrom,
      ...toRefs(otherForm),
      search,
      onSubmit,
      reset,
      cancleIA,
      submitIA,
      getPermissionBtn,
      iaDetail,
      colWidth
    };
  },
  computed: {
    ...mapGetters(['tenantGroup'])
  },
  created() {
    this.getList();
    // 刷新列表
    this.bus.$on('reloadInspectionList', msg => {
      this.getList();
    });
  },
  methods: {
    // 获取检测报告列表
    getList(data) {
      const _this = this;
      _this.listLoading = true;
      if (data && data !== undefined) {
        _this.listQuery.page = data.page ? data.page : 1;
        _this.listQuery.limit = data.limit;
      }
      const param = Object.assign(_this.formInline, _this.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      // console.log(param)
      // 检验单列表接口
      getTaskRegistrationList(param).then(res => {
        // console.log(res.data)
        if (res !== false) {
          const { data } = res.data;
          _this.list = data.list;
          _this.total = data.totalCount;
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.el-form {
  .el-form-item {
    margin-bottom: 0;
  }
}

.small-dialog {
  .dialog-main {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    img {
      width: 78%;
      margin-bottom: 20px;
    }
  }
}
.blue-color {
  color: $tes-primary;
  cursor: pointer;
}
</style>
