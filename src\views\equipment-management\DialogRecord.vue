<template>
  <el-dialog
    v-model="dialogShow"
    :title="dialogTitle[type]"
    :close-on-click-modal="false"
    width="480px"
    @close="handleClose"
  >
    <el-form
      v-if="dialogShow"
      ref="ruleForm"
      v-loading="dialogLoading"
      :model="formData"
      label-position="right"
      label-width="100px"
      size="small"
    >
      <el-form-item
        label="仪器设备："
        prop="deviceId"
        :rules="{ required: true, message: '请选择仪器设备', trigger: 'change' }"
      >
        <el-select
          v-if="pageType === 'record'"
          v-model="formData.deviceId"
          clearable
          filterable
          placeholder="请选择仪器设备"
        >
          <el-option
            v-for="item in allEquipmentList"
            :key="item.id"
            :label="item.deviceNumber + ' ' + item.name"
            :value="item.id"
          />
        </el-select>
        <span v-else> {{ formData.deviceNumber + '-' + formData.name }}</span>
      </el-form-item>
      <el-form-item
        label="维修人："
        prop="fixedBy"
        :rules="{ required: true, message: '请选择维修人', trigger: 'change' }"
      >
        <el-select v-model="formData.fixedBy" clearable filterable multiple placeholder="请选择维修人">
          <el-option v-for="item in nameList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item
        label="维修日期："
        prop="fixedDate"
        :rules="{ required: true, message: '请选择维修日期', trigger: 'change' }"
      >
        <el-date-picker v-model="formData.fixedDate" type="date" placeholder="请选择维修日期" />
      </el-form-item>
      <el-form-item
        label="维修结果："
        prop="fixedResult"
        :rules="{ required: true, message: '请选择维修结果', trigger: 'change' }"
      >
        <el-radio-group v-model="formData.fixedResult" @change="handleRadioResult">
          <el-radio v-for="(val, key) in statusJson" :key="key" :label="key"> {{ val }} </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="formData.fixedResult !== 'Scrapped'"
        label="重新计量："
        prop="isRemeasured"
        :rules="{ required: true, message: '请选择维修后是否需重新计量', trigger: 'change' }"
      >
        <el-radio-group v-model="formData.isRemeasured">
          <el-radio :label="1">需要</el-radio>
          <el-radio :label="0">不需要</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.fixedResult !== 'Scrapped'" prop="description" label="维修描述：">
        <el-input
          v-model="formData.description"
          type="textarea"
          maxlength="300"
          :rows="2"
          placeholder="请输入故障原因/维修内容等..."
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, ref, toRefs, watch, getCurrentInstance } from 'vue';
import { useStore } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
import { addRecordApi, editRecordApi } from '@/api/maintenanceRecordList';
import { formatDate } from '@/utils/formatTime';
export default {
  name: 'DialogRecord',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dialogType: {
      type: String,
      default: ''
    },
    pageType: {
      type: String,
      default: ''
    },
    equipmentList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    statusJson: {
      type: Object,
      default: function () {
        return {};
      }
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const state = reactive({
      dialogTitle: {
        // 弹出窗标题
        add: '新增维修记录',
        edit: '编辑维修记录'
      },
      type: '', // 弹出窗类型
      dialogLoading: false, // 弹出窗loading
      formData: {
        fixedBy: []
      }, // 表单数据
      allEquipmentList: [],
      statusJson: {}, // 维修状态类型
      detailData: {}, // 表单详情
      dialogShow: false,
      ruleForm: ref(),
      pageType: '', // 页面类型
      listLoading: false,
      nameList: store.common.nameList,
      currentAccountId: getLoginInfo().accountId
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.type = newValue.dialogType;
        state.allEquipmentList = newValue.equipmentList;
        state.statusJson = newValue.statusJson;
        state.pageType = newValue.pageType;
        if (state.type === 'add') {
          state.formData = {
            fixedBy: [state.currentAccountId],
            fixedDate: new Date(),
            deviceId: newValue.detailData.id || '',
            name: newValue.detailData.name || '',
            deviceNumber: newValue.detailData.deviceNumber || '',
            fixedResult: 'Running',
            isRemeasured: 0
          };
        } else {
          state.formData = JSON.parse(JSON.stringify(newValue.detailData));
          state.formData.fixedBy = state.formData.fixedBy.split(',');
        }
      }
    });
    const onSubmit = () => {
      proxy.$refs['ruleForm'].validate(valid => {
        if (valid) {
          const params = {
            ...state.formData,
            fixedBy: state.formData.fixedBy.toString()
          };
          if (state.type === 'add') {
            state.dialogLoading = true;
            addRecordApi({ ...params, fixedDate: formatDate(state.formData.fixedDate) }).then(res => {
              state.dialogLoading = false;
              if (res.data.code === 200) {
                proxy.$message.success(res.data.message);
                context.emit('closeDialog', { isRefresh: true });
              }
            });
          } else {
            state.dialogLoading = true;
            editRecordApi({ ...params, fixedDate: formatDate(state.formData.fixedDate) }).then(res => {
              state.dialogLoading = false;
              if (res.data.code === 200) {
                proxy.$message.success(res.data.message);
                context.emit('closeDialog', { isRefresh: true });
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog', { isRefresh: false });
    };
    const handleRadioResult = val => {
      if (val === 'Scrapped') {
        state.formData.isRemeasured = 0;
        state.formData.description = '';
      }
    };
    return { ...toRefs(state), onSubmit, handleClose, handleRadioResult };
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 100%;
}
:deep(.el-select) {
  width: 100%;
}
</style>
