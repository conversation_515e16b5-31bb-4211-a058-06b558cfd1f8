<template>
  <!-- 业务编码 -->
  <ListLayout
    :has-search-panel="false"
    :has-quick-query="false"
    :has-button-group="getPermissionBtn('addBCBtn') ? true : false"
  >
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="condition">
          <el-input
            v-model="formInline.condition"
            v-trim
            v-focus
            size="large"
            placeholder="请输入业务单据/服务名称/关键KEY搜索"
            prefix-icon="el-icon-search"
            clearable
            style="width: 360px"
            @clear="getTableList()"
            @keyup.enter="getTableList()"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="getTableList()">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        size="large"
        icon="el-icon-plus"
        type="primary"
        @click="handleOperate('add')"
        @keyup.prevent
        @keydown.enter.prevent
        >新增编码</el-button
      >
    </template>
    <el-table
      ref="tableRef"
      v-loading="listLoading"
      :data="tableData"
      size="medium"
      fit
      border
      height="auto"
      class="dark-table base-table format-height-table no-quick-query"
      @header-dragend="drageHeader"
    >
      <el-table-column label="关键key" prop="index" :width="colWidth.batch" align="left" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="">{{ row.key }}</div>
        </template>
      </el-table-column>
      <el-table-column label="业务单据" prop="name" :min-width="colWidth.orderNo" align="left" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-copy="row.name" class="blue" @click="handleOperate('check', row)">{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="服务名称"
        prop="serviceName"
        align="left"
        :min-width="colWidth.name"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="nowrap">{{ row.serviceName || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="编号规则" prop="rule" :min-width="colWidth.orderNo" align="left" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="nowrap">{{ row.rule || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最新编码" prop="newcode" :min-width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.newcode || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="更新人" prop="nickname" :width="colWidth.people">
        <template #default="{ row }">
          <UserTag :name="row.nickname || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="更新日期" prop="updateTime" :width="colWidth.date">
        <template #default="{ row }">
          <div>{{ row.updateTime }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="getPermissionBtn('editBCBtn')"
        label="操作"
        prop="caozuo"
        :width="colWidth.operationSingle"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <span class="blue-color" @click="handleOperate('edit', row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getTableList"
    />
    <template #other>
      <el-dialog
        v-model="dialogCode"
        :title="dialogTitle"
        :close-on-click-modal="false"
        width="980px"
        top="50px"
        custom-class="submit_dialog"
      >
        <el-form
          v-if="dialogCode"
          ref="ruleForm"
          v-loading="dialogLoading"
          :model="formDataCode"
          :rules="dialogRules"
          label-width="90px"
          :label-position="dialogType === 'add' ? 'top' : 'right'"
        >
          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item
                label="服务名称："
                prop="serviceName"
                :rules="{ required: true, message: '请输入服务名称', trigger: 'change' }"
              >
                <el-input
                  v-if="dialogType === 'add'"
                  v-model="formDataCode.serviceName"
                  autocomplete="off"
                  placeholder="请输入服务名称"
                />
                <span v-else>{{ formDataCode.serviceName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="业务单据："
                prop="name"
                :rules="{ required: true, message: '请输入业务单据', trigger: 'change' }"
              >
                <el-input
                  v-if="dialogType === 'add'"
                  v-model="formDataCode.name"
                  :disabled="dialogType !== 'add'"
                  autocomplete="off"
                  placeholder="请输入业务单据"
                />
                <span v-else>{{ formDataCode.name }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="关键key："
                prop="key"
                :rules="{ required: true, message: '请输入关键key', trigger: 'change' }"
              >
                <el-input
                  v-if="dialogType === 'add'"
                  v-model="formDataCode.key"
                  :disabled="dialogType !== 'add'"
                  autocomplete="off"
                  placeholder="请输入关键key"
                />
                <span v-else>{{ formDataCode.key }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="资源key："
                prop="menuKey"
                :rules="{ required: true, message: '请输入资源key', trigger: 'change' }"
              >
                <el-input
                  v-if="dialogType === 'add'"
                  v-model="formDataCode.menuKey"
                  :disabled="dialogType !== 'add'"
                  autocomplete="off"
                  placeholder="请输入资源key"
                />
                <span v-else>{{ formDataCode.menuKey }}</span>
              </el-form-item>
            </el-col>
            <el-col v-if="dialogType !== 'add'" :span="12">
              <el-form-item label="最新编码：" prop="newcode">
                <span>{{ formDataCode.newcode }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <div v-if="dialogType != 'check'" class="textLeft">
            <el-button size="small" icon="el-icon-plus" @click="addCode()">添加编码</el-button>
            <span v-if="formDataCode.ruleConfigs.length > 0" class="codeTitle"
              >新编码实例：<span>{{ newRuleCode }}</span></span
            >
          </div>
          <div v-if="isShowCodeList" id="sortList" class="codeData">
            <el-row v-for="(row, index) in formDataCode.ruleConfigs" :key="index" class="codeRow">
              <el-col v-if="dialogType != 'check'" :span="1" class="prfix">
                <i class="tes-move iconfont" style="font-size: 12px; cursor: move" />
              </el-col>
              <el-col v-if="dialogType != 'check'" :span="1" class="prfix">
                <i class="iconfont tes-delete" style="font-size: 16px; cursor: pointer" @click="deleteCode(index)" />
              </el-col>
              <el-col :span="1" class="prfix">
                <div>{{ row.orders + 1 }}</div>
              </el-col>
              <el-col :span="4">
                <el-form-item
                  label="类型："
                  :prop="`ruleConfigs.${index}.type`"
                  label-width="60px"
                  :rules="{ required: true, message: '请选择关键key', trigger: 'change' }"
                >
                  <el-select v-model="row.type" :disabled="dialogType == 'check'" placeholder="类型">
                    <el-option :label="'字符'" value="1" />
                    <el-option :label="'日期'" value="2" />
                    <el-option :label="'序号'" value="3" />
                    <el-option :label="'随机'" value="4" />
                    <el-option :label="'引用'" value="5" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                v-if="row.type == 1 || row.type == 2 || row.type == 4 || row.type == 5"
                :span="10"
                class="labelMargin"
              >
                <el-form-item
                  v-if="row.type == 1 || row.type == 5"
                  :label="row.type == 1 ? '字 符：' : '字 段：'"
                  :prop="`ruleConfigs.${index}.letter`"
                  label-width="110px"
                  :rules="[
                    {
                      required: dialogType != 'check',
                      message: row.type == 1 ? '请输入字符' : '请输入字段',
                      trigger: 'change'
                    },
                    { validator: validatorBackslash, trigger: 'blur' }
                  ]"
                >
                  <el-input
                    v-model.trim="row.letter"
                    :disabled="dialogType == 'check'"
                    autocomplete="off"
                    :placeholder="row.type == 1 ? '例如DD-' : '请输入引用字段'"
                    @input="getNewCode"
                  />
                </el-form-item>
                <el-form-item
                  v-if="row.type == 2"
                  label="日期类型："
                  :prop="`ruleConfigs.${index}.ymd`"
                  label-width="110px"
                  :rules="{ required: dialogType != 'check', message: '请选择日期类型', trigger: 'change' }"
                >
                  <el-select
                    v-model="row.ymd"
                    placeholder="日期类型"
                    :disabled="dialogType == 'check'"
                    @change="getNewCode"
                  >
                    <el-option :label="'年两位：YY'" value="YY" />
                    <el-option :label="'年四位：YYYY'" value="YYYY" />
                    <el-option :label="'月：MM'" value="MM" />
                    <el-option :label="'日：dd'" value="dd" />
                    <el-option :label="'小时：HH'" value="HH" />
                    <el-option :label="'分钟： mm'" value="mm" />
                    <el-option :label="'秒： ss'" value="ss" />
                  </el-select>
                </el-form-item>
                <el-form-item
                  v-if="row.type == 4"
                  label="随机位数："
                  :prop="`ruleConfigs.${index}.randomnumber`"
                  label-width="110px"
                  :rules="[
                    { required: dialogType != 'check', message: '请输入随机位数', trigger: 'change' },
                    { validator: randomdigits }
                  ]"
                >
                  <el-input
                    v-model.trim="row.randomnumber"
                    :disabled="dialogType == 'check'"
                    autocomplete="off"
                    placeholder="请输入随机位数"
                    @input="getNewCode"
                  />
                </el-form-item>
              </el-col>
              <el-col v-if="row.type == 3" :span="6" class="labelMargin">
                <el-form-item
                  label="序号位数："
                  :prop="`ruleConfigs.${index}.seqlength`"
                  label-width="82px"
                  :rules="[
                    { required: dialogType != 'check', message: '请输入序号位数', trigger: 'change' },
                    { validator: isInteger1 }
                  ]"
                >
                  <el-input
                    v-model.trim="row.seqlength"
                    :disabled="dialogType == 'check'"
                    autocomplete="off"
                    placeholder="序号位数"
                  />
                </el-form-item>
              </el-col>
              <el-col v-if="row.type == 3" :span="5" class="labelMargin">
                <el-form-item
                  label="轮序方式："
                  :prop="`ruleConfigs.${index}.fortype`"
                  label-width="82px"
                  :rules="{ required: dialogType != 'check', message: '请选择轮序方式', trigger: 'change' }"
                >
                  <el-select v-model="row.fortype" :disabled="dialogType == 'check'" placeholder="方式">
                    <el-option :label="'日'" value="1" />
                    <el-option :label="'月'" value="2" />
                    <el-option :label="'小时'" value="3" />
                    <el-option :label="'年'" value="4" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="row.type == 3" :span="6" class="labelMargin">
                <el-form-item
                  label="起始序号："
                  :prop="`ruleConfigs.${index}.startseq`"
                  label-width="82px"
                  :rules="[
                    { required: dialogType != 'check', message: '请输入起始序号', trigger: 'change' },
                    { validator: initialSequence, value: row.seqlength }
                  ]"
                >
                  <el-input
                    v-model.trim="row.startseq"
                    :disabled="dialogType == 'check'"
                    autocomplete="off"
                    placeholder="起始序号"
                    @input="getNewCode"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button :loading="dialogLoading" @click="dialogCode = false">取 消</el-button>
            <el-button v-if="dialogType != 'check'" :loading="dialogLoading" type="primary" @click="onSubmit"
              >确 认</el-button
            >
          </span>
        </template>
      </el-dialog>
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance, nextTick } from 'vue';
import Pagination from '@/components/Pagination';
import UserTag from '@/components/UserTag';
import { formatDate } from '@/utils/formatTime';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { getBusinessCodeList, checkCount, addRuleCode, updateRuleCode, getDetail } from '@/api/businessCode';
import { drageHeader } from '@/utils/formatTable';
import { getDictionaryList } from '@/api/dictionary';
import { isInteger1, initialSequence, randomdigits } from '@/utils/validate';
import ListLayout from '@/components/ListLayout';
import { colWidth } from '@/data/tableStyle';
import Sortable from 'sortablejs';

export default {
  name: 'BusinessCode',
  components: { Pagination, ListLayout, UserTag },
  props: {},
  setup(props) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableRef: ref(),
      formInline: {
        condition: ''
      },
      isShowCodeList: true,
      dictionaryList: [], // 字典列表
      newRuleCode: '', // 新编码示例
      dialogCode: false, // 新增成员
      dialogLoading: false, // 业务编码弹出窗loading
      dialogType: '', // 编码弹出框类型
      dialogTitle: '', // 编码弹出框标题
      ruleForm: ref(),
      listLoading: false,
      isEdit: true, // 规格弹出框是否是编辑页
      listQuery: {
        limit: 20,
        page: 1
      },
      formDataCode: {
        ruleConfigs: [] // 弹出框的编码信息
      },
      dialogRules: {},
      tableData: [],
      total: 0,
      statusDic: {
        1: '已启用',
        0: '已停用'
      }
    });
    const validatorBackslash = (rule, value, callback) => {
      if (value.indexOf('/') !== -1) {
        callback(new Error('请输入不含/的内容'));
      } else {
        callback();
      }
    };
    const getTableList = query => {
      const params = { condition: state.formInline.condition };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoading = true;
      getBusinessCodeList(params).then(res => {
        state.listLoading = false;
        if (res) {
          state.tableData = res.data.data.list;
          state.total = res.data.data.totalCount;
        }
      });
    };
    getTableList();
    const getDictionary = () => {
      getDictionaryList({ limit: '10000', page: '1', dictionaryType: '1' }).then(res => {
        state.dictionaryList = res.data.data.list;
      });
    };
    const rowDrop = () => {
      // 获取当前表格
      const el = document.getElementById('sortList');
      Sortable.create(el, {
        animation: 300,
        handle: '.tes-move',
        draggable: '.el-row',
        ghostClass: 'ghost',
        // 拖动对象移动样式
        dragClass: 'drag',
        forceFallback: true,
        onEnd: function (evt) {
          if (evt.oldIndex !== evt.newIndex) {
            const currRow = state.formDataCode.ruleConfigs.splice(evt.oldIndex, 1)[0];
            state.formDataCode.ruleConfigs.splice(evt.newIndex, 0, currRow);
            state.formDataCode.ruleConfigs.forEach((value, index) => {
              value.orders = index;
            });
            showCodeList();
            getNewCode();
          }
        }
      });
    };
    const showCodeList = () => {
      state.isShowCodeList = false;
      setTimeout(() => {
        state.isShowCodeList = true;
        nextTick(() => {
          rowDrop();
        });
      }, 0);
    };
    getDictionary();
    const handleSizeChange = val => {
      state.listQuery.limit = val;
      getTableList();
    };
    // 新增、编辑、查看编码
    const handleOperate = (type, row) => {
      state.dialogCode = true;
      state.dialogType = type;
      state.newRuleCode = '';
      if (type === 'add') {
        state.dialogTitle = '新增编码';
        state.formDataCode = {
          ruleConfigs: []
        };
      } else if (type === 'edit') {
        state.dialogTitle = '编辑编码';
        checkDetail(row.id);
      } else {
        state.dialogTitle = '查看编码';
        checkDetail(row.id);
      }
      nextTick(() => {
        rowDrop();
      });
    };
    const getNewCode = () => {
      state.newRuleCode = '';
      state.formDataCode.ruleConfigs.forEach(item => {
        if (item.type === '1') {
          // 字符类型
          if (item.letter) {
            state.newRuleCode += item.letter;
          }
        } else if (item.type === '2') {
          const dateType = {
            YY: new Date().getFullYear().toString().substring(2),
            YYYY: new Date().getFullYear().toString(),
            MM: (new Date().getMonth() + 1).toString(),
            dd: new Date().getDate().toString(),
            HH: new Date().getHours().toString(),
            mm: new Date().getMinutes().toString(),
            ss: new Date().getSeconds().toString()
          };
          if (item.ymd) {
            state.newRuleCode += dateType[item.ymd];
          }
        } else if (item.type === '3') {
          if (item.startseq) {
            state.newRuleCode += item.startseq;
          }
        } else if (item.type === '4') {
          if (item.randomnumber) {
            state.newRuleCode += item.randomnumber;
          }
        }
      });
    };
    const addCode = () => {
      state.formDataCode.ruleConfigs.push({ type: '', orders: state.formDataCode.ruleConfigs.length });
    };
    // 删除编码
    const deleteCode = index => {
      state.formDataCode.ruleConfigs.splice(index, 1);
      state.formDataCode.ruleConfigs.forEach((value, index) => {
        value.orders = index;
      });
      getNewCode();
    };
    const reset = () => {
      state.formInline.condition = '';
      getTableList();
    };
    const checkDetail = id => {
      state.dialogLoading = true;
      getDetail({ id: id }).then(res => {
        state.dialogLoading = false;
        if (res) {
          state.formDataCode = res.data.data.rule;
          state.formDataCode.ruleConfigs = res.data.data.ruleConfigs;
          getNewCode();
        }
      });
    };
    const handleSelectionChange = () => {};
    // 新增、编辑业务编码
    const onSubmit = () => {
      state.ruleForm.validate().then(valid => {
        if (valid) {
          if (state.dialogType === 'add') {
            // 新增
            Promise.all([checkWy('name', state.formDataCode.name), checkWy('key', state.formDataCode.key)])
              .then(result => {
                if (result[0] && result[1]) {
                  addRuleCode(state.formDataCode).then(res => {
                    if (res) {
                      state.dialogCode = false;
                      getTableList();
                      proxy.$message.success(res.data.message);
                    }
                  });
                }
              })
              .catch(() => {});
          } else {
            // 编辑
            updateRuleCode(state.formDataCode).then(res => {
              if (res) {
                state.dialogCode = false;
                getTableList();
                proxy.$message.success(res.data.message);
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    const checkWy = (type, value) => {
      const errorMessage = {
        name: '业务单据重复，请先修改',
        key: '关键key重复，请先修改'
      };
      return new Promise((resolve, reject) => {
        checkCount({ [type]: value }).then(res => {
          if (res.data.data === 0) {
            resolve(true);
          } else {
            proxy.$message.error(errorMessage[type]);
            resolve(false);
          }
        });
      });
    };
    return {
      ...toRefs(state),
      reset,
      checkWy,
      validatorBackslash,
      isInteger1,
      randomdigits,
      rowDrop,
      showCodeList,
      initialSequence,
      getDictionary,
      handleOperate,
      getNewCode,
      checkDetail,
      addCode,
      deleteCode,
      drageHeader,
      getNameByid,
      handleSizeChange,
      handleSelectionChange,
      getNamesByid,
      onSubmit,
      getPermissionBtn,
      formatDate,
      getTableList,
      colWidth
    };
  },
  computed: {},
  created() {}
};
</script>
<style lang="scss" scoped>
.textLeft {
  text-align: left;
}
.codeData {
  max-height: 300px;
  overflow-y: auto;
  margin-top: 15px;
}
.prfix {
  line-height: 30px;
}
.codeTitle {
  margin: 0 10px;
  position: relative;
  top: 5px;
}
.labelMargin {
  padding-left: 15px;
}
.el-select {
  width: 100%;
}
.btn-mg20 {
  margin-right: 20px;
}
.el-radio {
  margin-right: 5px;
}
.submit_dialog {
  .el-upload__tip {
    color: #909399;
    display: inline-block;
    margin-left: 12px;
    font-size: 14px;
  }
  .el-form-item {
    margin-bottom: 15px;
  }
  .codeRow {
    padding: 13px 10px 0 10px;
    background: #eff1f4;
    margin-top: 15px;
    &:first-child {
      margin-top: 0;
    }
    :deep(.el-form-item__error) {
      padding-top: 0px;
    }
    .el-form-item {
      margin-bottom: 13px;
    }
    :deep(.el-input--medium .el-input__inner) {
      line-height: 30px;
      height: 30px;
    }
    :deep(.el-input--medium .el-input__icon) {
      line-height: 30px;
    }
    :deep(.el-form-item--medium .el-form-item__content) {
      line-height: 30px;
    }
    :deep(.el-form-item--medium .el-form-item__label) {
      line-height: 30px;
    }
    :deep(.el-input--medium) {
      line-height: 30px;
    }
  }
}
:deep(.el-form-item__error) {
  padding-top: 2px;
}
</style>
