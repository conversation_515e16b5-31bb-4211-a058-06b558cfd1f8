<template>
  <!-- 原材料检验 -->
  <div v-loading="loading" class="RawMaterials textLeft">
    <el-row class="header">
      <el-col :span="14">
        <el-radio-group v-model="rawMaterial" size="small" @change="changeMaterial">
          <el-radio-button v-for="item in rawMaterialList" :key="item.code" :label="item.code">{{
            item.name
          }}</el-radio-button>
        </el-radio-group>
      </el-col>
      <el-col :span="10" class="textRight">
        <el-button
          v-if="oldTableProductList.length > 0 && oldTableTestList.length > 0"
          class="add-btn"
          size="small"
          type="warning"
          icon="el-icon-upload"
          @click="uploadSdcc"
          @keyup.prevent
          @keydown.enter.prevent
          >上传SDCC</el-button
        >
        <el-button v-if="isAddProduct || isEditProduct" type="primary" size="small" @click="onSaveProduction">
          保存
        </el-button>
        <el-button v-if="isAddProduct || isEditProduct" size="small" @click="calceProduction"> 取消 </el-button>
        <el-button
          v-if="!isEditProduct && !isAddProduct && oldTableProductList.length > 0"
          type="primary"
          icon="el-icon-edit"
          size="small"
          @click="handleEditProduct"
        >
          编辑
        </el-button>
        <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAddProduction"> 新增 </el-button>
      </el-col>
    </el-row>
    <el-form
      ref="tableProductRef"
      :model="tableProductForm"
      :rules="productionRules"
      size="small"
      style="margin-top: 10px"
    >
      <el-table
        ref="tableLeftRef"
        :data="tableProductForm.tableData"
        fit
        border
        highlight-current-row
        size="medium"
        max-height="440px"
        class="detail-table format-height-table2 dark-table"
        @row-click="rowClick"
        @header-dragend="drageHeader"
      >
        <el-table-column type="index" label="序号" fixed="left" :width="colWidth.serialNo" align="center" />
        <el-table-column prop="materialNo" label="原材料编号" :width="colWidth.orderNo" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="
                !row.id || (row.sourcePath !== 'M' && isEditProduct && row.reportSubmitStatus.split('/')[0] === '0')
              "
              :prop="'tableData.' + $index + '.materialNo'"
              :rules="productionRules.materialNo"
              style="margin: 0px"
            >
              <el-input
                v-model="row.materialNo"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="原材料编号"
                @click.stop.prevent
              />
            </el-form-item>
            <span v-else>{{ row.materialNo || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="materialName" label="原材料名称" :width="colWidth.orderNo" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="
                !row.id || (row.sourcePath !== 'M' && isEditProduct && row.reportSubmitStatus.split('/')[0] === '0')
              "
              :prop="'tableData.' + $index + '.materialName'"
              :rules="productionRules.materialName"
              style="margin: 0px"
            >
              <el-input
                v-model="row.materialName"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="原材料名称"
                @click.stop.prevent
              />
            </el-form-item>
            <span v-else>{{ row.materialName || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="投料时间" :min-width="colWidth.datetime">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="
                !row.id || (row.sourcePath !== 'M' && isEditProduct && row.reportSubmitStatus.split('/')[0] === '0')
              "
              :prop="'tableData.' + $index + '.date'"
              :rules="productionRules.date"
              style="margin: 0px"
            >
              <div @click.stop.prevent>
                <el-date-picker
                  v-model="row.date"
                  type="date"
                  size="small"
                  placeholder="投料时间"
                  @change="
                    val => {
                      return handleChangeDate(val, $index, 'date');
                    }
                  "
                />
              </div>
            </el-form-item>
            <div v-else>{{ formatDate(row.date) || '--' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="reportSubmitStatus" label="上传状态" :width="colWidth.plate">
          <template #header>
            <span
              >上传状态
              <el-tooltip content="此生产数据下关联的未上传报告/总报告数量" placement="top" effect="light">
                <i class="iconfont tes-title" />
              </el-tooltip>
            </span>
          </template>
          <template #default="{ row }">
            <span>{{ row.reportSubmitStatus || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="productionBatchNo" label="抽样序号" :width="colWidth.orderNo" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="
                !row.id || (row.sourcePath !== 'M' && isEditProduct && row.reportSubmitStatus.split('/')[0] === '0')
              "
              :prop="'tableData.' + $index + '.productionBatchNo'"
              :rules="productionRules.productionBatchNo"
              style="margin: 0px"
            >
              <el-input
                v-model="row.productionBatchNo"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="抽样序号"
                @click.stop.prevent
              />
            </el-form-item>
            <span v-else>{{ row.productionBatchNo || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="materialSpecification" label="型号规格" :width="colWidth.orderNo" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="
                !row.id || (row.sourcePath !== 'M' && isEditProduct && row.reportSubmitStatus.split('/')[0] === '0')
              "
              :prop="'tableData.' + $index + '.materialSpecification'"
              :rules="productionRules.materialSpecification"
              style="margin: 0px"
            >
              <el-input
                v-model="row.materialSpecification"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="型号规格"
                @click.stop.prevent
              />
            </el-form-item>
            <span v-else>{{ row.materialSpecification || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="materialBrand" label="原材料品牌" :width="colWidth.orderNo" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="
                !row.id || (row.sourcePath !== 'M' && isEditProduct && row.reportSubmitStatus.split('/')[0] === '0')
              "
              :prop="'tableData.' + $index + '.materialBrand'"
              :rules="productionRules.materialBrand"
              style="margin: 0px"
            >
              <el-input
                v-model="row.materialBrand"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="原材料品牌"
                @click.stop.prevent
              />
            </el-form-item>
            <span v-else>{{ row.materialBrand || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="produceDate" label="原材料生产日期" :min-width="colWidth.dateranger">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="
                !row.id || (row.sourcePath !== 'M' && isEditProduct && row.reportSubmitStatus.split('/')[0] === '0')
              "
              :prop="'tableData.' + $index + '.produceDate'"
              :rules="productionRules.produceDate"
              style="margin: 0px"
            >
              <div @click.stop.prevent>
                <el-date-picker
                  v-model="row.produceDate"
                  type="date"
                  size="small"
                  placeholder="原材料生产日期"
                  @change="
                    val => {
                      return handleChangeDate(val, $index, 'produceDate');
                    }
                  "
                />
              </div>
            </el-form-item>
            <div v-else>{{ formatDate(row.produceDate) || '--' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="manufacturer" label="原材料制造商" :width="colWidth.orderNo" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="
                !row.id || (row.sourcePath !== 'M' && isEditProduct && row.reportSubmitStatus.split('/')[0] === '0')
              "
              :prop="'tableData.' + $index + '.manufacturer'"
              :rules="productionRules.manufacturer"
              style="margin: 0px"
            >
              <el-input
                v-model="row.manufacturer"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="原材料制造商"
                @click.stop.prevent
              />
            </el-form-item>
            <span v-else>{{ row.manufacturer || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="materialInspectionBatchNo"
          label="原材料检验批次"
          :width="colWidth.orderNo"
          show-overflow-tooltip
        >
          <template #default="{ row, $index }">
            <el-form-item
              v-if="
                !row.id || (row.sourcePath !== 'M' && isEditProduct && row.reportSubmitStatus.split('/')[0] === '0')
              "
              :prop="'tableData.' + $index + '.materialInspectionBatchNo'"
              :rules="productionRules.materialInspectionBatchNo"
              style="margin: 0px"
            >
              <el-input
                v-model="row.materialInspectionBatchNo"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="原材料检验批次"
                @click.stop.prevent
              />
            </el-form-item>
            <span v-else>{{ row.materialInspectionBatchNo || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="materialBatchNo" label="原材料批次号" :width="colWidth.orderNo" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="
                !row.id || (row.sourcePath !== 'M' && isEditProduct && row.reportSubmitStatus.split('/')[0] === '0')
              "
              :prop="'tableData.' + $index + '.materialBatchNo'"
              :rules="productionRules.materialBatchNo"
              style="margin: 0px"
            >
              <el-input
                v-model="row.materialBatchNo"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="原材料批次号"
                @click.stop.prevent
              />
            </el-form-item>
            <span v-else>{{ row.materialBatchNo || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="materialInspectionDateTime" label="原材料检验日期" :min-width="colWidth.dateranger">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="
                !row.id || (row.sourcePath !== 'M' && isEditProduct && row.reportSubmitStatus.split('/')[0] === '0')
              "
              :prop="'tableData.' + $index + '.materialInspectionDateTime'"
              :rules="productionRules.materialInspectionDateTime"
              style="margin: 0px"
            >
              <div @click.stop.prevent>
                <el-date-picker
                  v-model="row.materialInspectionDateTime"
                  type="date"
                  size="small"
                  placeholder="原材料检验日期"
                  @change="
                    val => {
                      return handleChangeDate(val, $index, 'materialInspectionDateTime');
                    }
                  "
                />
              </div>
            </el-form-item>
            <span v-else>{{ formatDate(row.materialInspectionDateTime) || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column class-name="fixed-right" fixed="right" prop="status" label="操作" width="120">
          <template #default="{ row, $index }">
            <span
              v-if="row.id && row.reportSubmitStatus.split('/')[0] === '0'"
              class="blue-color"
              @click.stop="handleDeleteLeft(row, $index)"
              >删除</span
            >
            <span
              v-if="!row.id && tableProductForm.tableData.length > 1"
              class="blue-color"
              @click.stop="handleDeleteNew($index)"
              >删除</span
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <div v-if="selectRow.id" class="textRight topBtn">
      <el-button
        class="add-btn"
        size="small"
        icon="el-icon-download"
        type="warning"
        @click="dialogSelect = true"
        @keyup.prevent
        @keydown.enter.prevent
        >获取数据</el-button
      >
      <el-button
        v-if="(isAddTestData || isEditTestData || isGetData) && tableTestForm.tableData.length > 0"
        type="primary"
        size="small"
        @click="onSaveTestData"
      >
        保存
      </el-button>
      <el-button
        v-if="(isAddTestData || isEditTestData || isGetData) && tableTestForm.tableData.length > 0"
        size="small"
        @click="calceTestData"
      >
        取消
      </el-button>
      <el-button
        v-if="!isEditTestData && !isAddTestData && oldTableTestList.length > 0"
        type="primary"
        icon="el-icon-edit"
        size="small"
        @click="isEditTestData = true"
      >
        编辑
      </el-button>
      <el-button
        v-if="!isAddTestData && oldTableProductList.length > 0"
        class="add-btn"
        size="small"
        icon="el-icon-plus"
        type="primary"
        @click="handleAddTestData"
        @keyup.prevent
        @keydown.enter.prevent
        >新增</el-button
      >
    </div>
    <el-form v-if="selectRow.id" ref="tableTestRef" :model="tableTestForm" :rules="testRules" size="small">
      <el-table
        :data="tableTestForm.tableData"
        fit
        border
        highlight-current-row
        size="medium"
        max-height="440px"
        class="detail-table format-height-table2 dark-table"
        :span-method="objectSpanMethod"
        @header-dragend="drageHeader"
      >
        <el-table-column type="index" label="序号" :width="colWidth.serialNo" fixed="left" align="center" />
        <el-table-column prop="secSampleNum" label="样品编号" :min-width="colWidth.orderNo">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
              :prop="'tableData.' + $index + '.secSampleNum'"
              :rules="testRules.secSampleNum"
              style="margin: 0px"
            >
              <el-input
                v-model="row.secSampleNum"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="样品编号"
                @input="
                  val => {
                    return handleEditGroup(val, $index, 'secSampleNum');
                  }
                "
              />
            </el-form-item>
            <span v-else>{{ row.secSampleNum || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="reportNo" label="报告编码" :min-width="colWidth.orderNo">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
              :prop="'tableData.' + $index + '.reportNo'"
              :rules="testRules.reportNo"
              style="margin: 0px"
            >
              <el-input
                v-model="row.reportNo"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="报告编码"
                @input="
                  val => {
                    return handleEditGroup(val, $index, 'reportNo');
                  }
                "
              />
            </el-form-item>
            <span v-else>{{ row.reportNo || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="submitStatus" label="上传状态" :width="colWidth.status">
          <template #default="{ row }">
            <el-tag :type="row.submitStatus ? 'success' : 'warning'" size="small">{{
              row.submitStatus ? '已上传' : '待上传'
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="externalCapabilityParaName"
          label="项目名称"
          :min-width="colWidth.orderNo"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ row.externalCapabilityParaName || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="unitName" label="单位" :width="colWidth.inputSelect" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.unitName || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="resultType" label="数据类型" :width="colWidth.inputSelect" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.resultType || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="paraValue" label="报告结果" :min-width="colWidth.result">
          <template #default="{ row }">
            <span>{{ row.paraValue || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="sdccParaValue" label="上传结果" :min-width="colWidth.xxys">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
              :prop="'tableData.' + $index + '.sdccParaValue'"
              :rules="testRules.sdccParaValue[row.resultType]"
              style="margin: 0px"
            >
              <el-input
                v-if="row.resultType === '数值型'"
                v-model="row.sdccParaValue"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="上传结果"
              />
              <el-input
                v-else-if="row.resultType === '字符串'"
                v-model="row.sdccParaValue"
                v-trim
                size="small"
                type="text"
                maxlength="100"
                placeholder="上传结果"
              />
              <el-select v-else v-model="row.sdccParaValue" placeholder="请选择" size="small">
                <el-option
                  v-for="(val, key) in dictionaryJson[row.resultOption]"
                  :key="key"
                  :label="val"
                  :value="key"
                />
              </el-select>
            </el-form-item>
            <div v-else>
              <span v-if="row.resultType === '枚举型'">{{
                dictionaryJson[row.resultOption][row.sdccParaValue] || '--'
              }}</span>
              <span v-else>{{ row.sdccParaValue || '--' }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="result" label="结论" :width="colWidth.inputSelect">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
              :prop="'tableData.' + $index + '.result'"
              :rules="testRules.result"
              style="margin: 0px"
            >
              <el-select v-model="row.result" placeholder="结论" size="small">
                <el-option label="合格" value="合格" />
                <el-option label="不合格" value="不合格" />
              </el-select>
            </el-form-item>
            <div v-else>{{ row.result || '--' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="报告" :min-width="colWidth.fileName">
          <template #default="{ row, $index }">
            <div>
              <div>
                出厂报告：
                <div
                  v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
                  class="displayInlineBlock"
                >
                  <el-upload
                    v-if="!row.outFactoryInspectionReport"
                    :action="fileAction"
                    :show-file-list="false"
                    :headers="headerconfig"
                    :data="{ fileType: 'OUT' }"
                    :before-upload="beforeUpload"
                    :limit="1"
                    :on-success="
                      (res, file, files) => {
                        return handleSuccess(res, file, files, $index, 'outFactoryInspectionReport');
                      }
                    "
                    :auto-upload="true"
                  >
                    <span class="blue-color">上传文件</span>
                  </el-upload>
                  <span v-if="row.outFactoryInspectionReport" class="fileName blue-color">{{
                    row.outFactoryInspectionReport
                  }}</span>
                  <i
                    v-if="row.outFactoryInspectionReport"
                    class="el-icon-close blue-color"
                    @click="
                      deleteFile(
                        $index,
                        'outFactoryInspectionReport',
                        'outFactoryInspectionReportId',
                        'outFactoryInspectionReportSdccId'
                      )
                    "
                  />
                </div>
                <span
                  v-else-if="row.outFactoryInspectionReport"
                  class="fileName blue-color"
                  @click="downLoadFile(row.outFactoryInspectionReportId, row.outFactoryInspectionReport)"
                >
                  {{ row.outFactoryInspectionReport }}
                </span>
                <span v-else>--</span>
              </div>
              <div>
                抽检报告：
                <div
                  v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData && !row.submitStatus)"
                  class="displayInlineBlock"
                >
                  <el-upload
                    v-if="row.isAddRow || (row.sourcePath !== 'M' && isEditTestData)"
                    class="displayInlineBlock"
                    :action="fileAction"
                    :show-file-list="false"
                    :headers="headerconfig"
                    :data="{ fileType: 'INSPECTION' }"
                    :before-upload="beforeUpload"
                    :on-success="
                      (res, file, files) => {
                        return handleSuccess(res, file, files, $index, 'spotCheckInspectionReport');
                      }
                    "
                    :auto-upload="true"
                  >
                    <span v-if="!row.spotCheckInspectionReport" class="blue-color">上传文件</span>
                  </el-upload>
                  <span v-if="row.spotCheckInspectionReport" class="fileName blue-color">{{
                    row.spotCheckInspectionReport
                  }}</span>
                  <i
                    v-if="row.spotCheckInspectionReport"
                    class="el-icon-close blue-color"
                    @click="
                      deleteFile(
                        $index,
                        'spotCheckInspectionReport',
                        'spotCheckInspectionReportId',
                        'spotCheckInspectionReportSdccId'
                      )
                    "
                  />
                </div>
                <span
                  v-else-if="row.spotCheckInspectionReport"
                  class="fileName blue-color"
                  @click="downLoadFile(row.spotCheckInspectionReportId, row.spotCheckInspectionReport)"
                >
                  {{ row.spotCheckInspectionReport }}
                </span>
                <span v-else>--</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="操作" fixed="right" :width="colWidth.operationSingle">
          <template #default="{ row, $index }">
            <span
              v-if="row.sourcePath !== 'M' && !row.isAddRow && !row.submitStatus"
              class="blue-color"
              @click="handleDeleteTestData(row, $index)"
              >删除</span
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <!-- 获取数据的弹出框 -->
    <DialogFinishData
      :dialog-show="dialogSelect"
      :select-row="{ type: 'raw', data: obtainingData }"
      @closeDialog="closeDialog"
    />
  </div>
</template>

<script>
import { reactive, toRefs, getCurrentInstance, nextTick, ref } from 'vue';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import DialogFinishData from './DialogFinishData';
import { getToken } from '@/utils/auth';
import { ElMessage } from 'element-plus';
import { isDigital } from '@/utils/validate';
import { getDictionary } from '@/api/user';
import {
  saveRawProduction,
  getRawProductList,
  saveTestData,
  getInspectionApi,
  deleteTestData,
  getTestListProcess,
  deleteRawProduction,
  submitSDCC,
  downLoadFileApi
} from '@/api/sdcc';
import { inspectionResultReportUploadUrl } from '@/api/uploadAction';

// import { method } from 'lodash'
export default {
  name: 'RawMaterials',
  components: { DialogFinishData },
  props: {
    unitList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    externalList: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['isModify'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      isCanEdit: false,
      tableLeftRef: ref(),
      loading: false,
      experimentProjectCode: [], // 提交SDCC需要传的code
      tableProductForm: {
        tableData: [] // 生产数据
      },
      tableTestForm: {
        tableData: []
      }, // 检测数据
      oldTableProductList: [], // 修改之前的生产数据
      headerconfig: {
        Authorization: getToken()
      },
      fileAction: inspectionResultReportUploadUrl(),
      oldTableTestList: [],
      dictionaryJson: {},
      spanArr: [],
      tableProductRef: ref(),
      unitList: [],
      productionRules: {
        materialNo: { required: true, tigger: 'blur', message: '原材料编号' },
        materialName: { required: true, tigger: 'blur', message: '请输入原材料名称' },
        materialModel: { required: true, tigger: 'blur', message: '请输入原材料型号' },
        date: { required: true, tigger: 'blur', type: 'date', message: '投料时间' },
        productionBatchNo: { required: true, tigger: 'blur', message: '请输入抽样序号' },
        materialSpecification: { required: true, tigger: 'blur', message: '请输入型号规格' },
        materialBrand: { required: true, tigger: 'blur', message: '请输入原材料品牌' },
        produceDate: { required: true, tigger: 'blur', type: 'date', message: '原材料生产日期' },
        manufacturer: { required: true, tigger: 'blur', message: '请输入原材料制造商' },
        materialInspectionBatchNo: { required: true, tigger: 'blur', message: '原材料检验批次' },
        materialBatchNo: { required: true, tigger: 'blur', message: '原材料批次号' },
        materialInspectionDateTime: { required: true, tigger: 'blur', type: 'date', message: '原材料检验日期' }
      },
      isEditProduct: false,
      isAddProduct: false,
      isEditTestData: false,
      isAddTestData: false,
      isGetData: false, // 已获取数据
      selectRow: {}, // 选中的生产信息
      obtainingData: {},
      tableTestRef: ref(),
      testRules: {
        // 测试数据的必填校验
        secSampleNum: { required: true, tigger: 'blur', message: '请输入样品编号' },
        reportNo: { required: true, tigger: 'blur', message: '请输入报告编码' },
        externalCapabilityParaName: { required: true, tigger: 'blur', message: '请输入项目名称' },
        sdccParaValue: {
          字符串: { required: true, tigger: 'blur', message: '请输入上传结果' },
          枚举型: { required: true, tigger: 'blur', message: '请输入上传结果' },
          数值型: [{ required: true, tigger: 'blur', message: '请输入上传结果' }, { validator: isDigital }]
        },
        result: { required: true, tigger: 'blur', message: '请选择试验结果' },
        colourValue: { required: true, tigger: 'blur', message: '请输入线芯颜色' },
        startDate: { required: false },
        finishedDate: { required: false }
      },
      dialogSelect: false,
      detailData: JSON.parse(localStorage.getItem('productionOrderInfo')),
      rawMaterial: '',
      rawMaterialList: [],
      selectRawMaterial: {}
    });
    // 获取检测数据
    const getInspectionList = () => {
      state.loading = true;
      getInspectionApi({
        inspectionType: 'RAW_MATERIAL',
        mainDataId: state.selectRow.id,
        productionOrderId: state.selectRow.productionOrderId
      }).then(res => {
        state.loading = false;
        state.isAddTestData = false;
        state.isEditTestData = false;
        state.isGetData = false;
        if (res) {
          state.tableTestForm.tableData = JSON.parse(JSON.stringify(res.data.data));
          state.oldTableTestList = JSON.parse(JSON.stringify(res.data.data));
          getSpanArr(state.tableTestForm.tableData);
        }
      });
    };
    const getSelectTable = row => {
      nextTick(() => {
        state.tableLeftRef.setCurrentRow(row, true);
      });
      state.selectRow = row;
      state.obtainingData = {
        materialNo: state.selectRow.materialNo,
        batchNo: state.selectRow.materialBatchNo,
        reelNo: state.selectRow.reelNo
      };
      // 获取检验数据
      if (state.selectRow.id) {
        getInspectionList();
      } else {
        state.tableTestForm.tableData = [];
      }
    };
    // 获取生产数据
    const getTableList = () => {
      state.loading = true;
      getRawProductList({ productionOrderId: state.detailData.id, materialSdccType: state.rawMaterial }).then(res => {
        state.loading = false;
        if (res) {
          state.tableProductForm.tableData = res.data.data;
          state.oldTableProductList = JSON.parse(JSON.stringify(res.data.data));
          if (state.oldTableProductList.length > 0) {
            getSelectTable(state.tableProductForm.tableData[0]);
          } else {
            state.selectRow = {};
            state.tableTestForm.tableData = [];
          }
        }
      });
    };
    // 获取字典数据
    const getDictionaryList = () => {
      const array = state.selectRawMaterial.externalCapabilityMapList.filter(item => {
        return item.resultType === '枚举型';
      });
      array.forEach(val => {
        getDictionary(val.resultOption).then(res => {
          if (res) {
            const dictionaryJson = {};
            res.data.data.dictionaryoption.forEach(item => {
              if (item.status === 1) {
                dictionaryJson[item.code] = item.name;
              }
            });
            state.dictionaryJson[val.resultOption] = dictionaryJson;
          }
        });
      });
    };
    // 获取原材料
    const getRawMaterials = () => {
      state.unitList = props.unitList;
      state.rawMaterialList = props.externalList;
      state.experimentProjectCode = props.externalList.map(item => {
        return item.code;
      });
      state.selectRawMaterial = state.rawMaterialList[0];
      state.rawMaterial = state.selectRawMaterial.code;
      getDictionaryList();
      getTableList();
    };
    getRawMaterials();
    // 切换原材料
    const changeMaterial = val => {
      state.selectRawMaterial = state.rawMaterialList.filter(item => {
        return item.code === val;
      })[0];
      state.rawMaterial = val;
      calceProduction();
      calceTestData();
      state.selectRow = {};
      getDictionaryList();
    };
    const rowClick = row => {
      getSelectTable(row);
    };
    // 上传SDCC
    const uploadSdcc = () => {
      if (state.isAddTestData || state.isEditTestData || state.isGetData) {
        proxy.$message.error('请先完成测试数据的编辑或新增');
        return false;
      }
      proxy
        .$confirm('是否确认上传SDCC', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          showClose: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        })
        .then(() => {
          state.loading = true;
          submitSDCC({
            ids: state.detailData.id,
            inspectionType: 'RAW_MATERIAL',
            materialSdccType: state.rawMaterial,
            experimentProjectCode: state.experimentProjectCode
          }).then(res => {
            state.loading = false;
            if (res) {
              proxy.$message.success('上传成功');
              context.emit(
                'isModify',
                state.isAddProduct ||
                  state.isEditProduct ||
                  state.isAddTestData ||
                  state.isEditTestData ||
                  state.isGetData
              );
              getTableList();
            }
          });
        })
        .catch(() => {});
    };
    // 保存生产数据
    const handleAddProduction = () => {
      state.isAddProduct = true;
      state.tableProductForm.tableData.push({
        reportSubmitStatus: '',
        sourcePath: 'I',
        materialSdccType: state.rawMaterial
      });
    };
    // 保存生产数据
    const onSaveProduction = () => {
      proxy.$refs['tableProductRef'].validate(valid => {
        if (valid) {
          state.loading = true;
          saveRawProduction({
            no: state.detailData.no,
            id: state.detailData.id,
            productionOrderRawMaterialSdccDTOList: state.tableProductForm.tableData
          }).then(res => {
            state.loading = false;
            if (res) {
              proxy.$message.success('保存成功');
              state.isAddProduct = false;
              state.isEditProduct = false;
              context.emit(
                'isModify',
                state.isAddProduct ||
                  state.isEditProduct ||
                  state.isAddTestData ||
                  state.isEditTestData ||
                  state.isGetData
              );
              getTableList();
            }
          });
        } else {
          proxy.$message.warning('请先完成必填项');
        }
      });
    };
    const closeDialog = val => {
      if (val.data.length > 0) {
        const params = {
          reportIdList: val.data.map(item => {
            return item.reportId;
          }),
          externalCapabilityMapList: state.selectRawMaterial.externalCapabilityMapList
        };
        var reportNoArray = [];
        state.tableTestForm.tableData.forEach(item => {
          reportNoArray.push({
            reportNo: item.reportNo,
            externalCapabilityParaName: item.externalCapabilityParaName
          });
        });
        state.loading = true;
        getTestListProcess(params).then(res => {
          state.loading = false;
          if (res) {
            state.dialogSelect = false;
            const detailDataInfo = JSON.parse(JSON.stringify(res.data.data));
            detailDataInfo.forEach(val => {
              const valItem = { ...state.selectRow, ...val };
              const isExistence = reportNoArray.some(item => {
                return (
                  item.reportNo === valItem.reportNo &&
                  item.externalCapabilityParaName === valItem.externalCapabilityParaName
                );
              });
              if (!isExistence) {
                if (valItem.paravalItemue && val.resultType !== '枚举值') {
                  const sdccParaValue = valItem.paraValue.split(',')[0];
                  // 有报告结果
                  if (sdccParaValue.split(':').length > 1) {
                    valItem.sdccParaValue = sdccParaValue.split(':')[1];
                  } else {
                    valItem.sdccParaValue = sdccParaValue;
                  }
                } else {
                  valItem.sdccParaValue = '';
                }
                state.tableTestForm.tableData.push({
                  ...valItem,
                  submitStatus: 0,
                  sourcePath: 'I',
                  inspectionType: 'RAW_MATERIAL',
                  mainDataId: state.selectRow.id,
                  materialSdccType: state.rawMaterial,
                  isAddRow: true,
                  productionOrderNo: state.detailData.no
                });
              } else {
                setTimeout(() => {
                  ElMessage({
                    message: `报告编号：${val.reportNo}, 项目名称：${val.externalCapabilityParaName}已存在`,
                    grouping: true,
                    type: 'error'
                  });
                }, 10);
              }
              delete state.tableTestForm.tableData[state.tableTestForm.tableData.length - 1].id;
              delete state.tableTestForm.tableData[state.tableTestForm.tableData.length - 1].deviceUsageVoList;
            });
            state.isGetData = true;
            getSpanArr(state.tableTestForm.tableData);
            context.emit(
              'isModify',
              state.isAddProduct ||
                state.isEditProduct ||
                state.isAddTestData ||
                state.isEditTestData ||
                state.isGetData
            );
          }
        });
      } else {
        state.dialogSelect = false;
      }
    };
    // 取消生产信息的保存和编辑
    const calceProduction = () => {
      getTableList();
      state.isAddProduct = false;
      state.isEditProduct = false;
      context.emit(
        'isModify',
        state.isAddProduct || state.isEditProduct || state.isAddTestData || state.isEditTestData || state.isGetData
      );
    };
    // 格式化日期格式
    const handleChangeDate = (val, index, type) => {
      if (val) {
        state.tableProductForm.tableData[index][type] = formatDate(val);
      } else {
        state.tableProductForm.tableData[index][type] = '';
      }
    };
    const handleDeleteLeft = (row, index) => {
      proxy
        .$confirm('是否确认删除', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          showClose: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        })
        .then(() => {
          state.loading = true;
          deleteRawProduction([row.id]).then(res => {
            state.loading = false;
            if (res) {
              proxy.$message.success('删除成功');
              state.tableProductForm.tableData.splice(index, 1);
              state.oldTableProductList.splice(index, 1);
              if (state.tableProductForm.tableData.length > 0) {
                getSelectTable(state.tableProductForm.tableData[0]);
              } else {
                state.selectRow = {};
                state.tableTestForm.tableData = [];
              }
            }
          });
        })
        .catch(() => {});
    };
    // 删除新增的
    const handleDeleteNew = index => {
      state.tableProductForm.tableData.splice(index, 1);
      if (state.tableProductForm.tableData.length > 0) {
        getSelectTable(state.tableProductForm.tableData[0]);
      }
    };
    // 保存测试数据
    const onSaveTestData = () => {
      proxy.$refs['tableTestRef'].validate(valid => {
        if (valid) {
          state.loading = true;
          saveTestData(state.tableTestForm.tableData).then(res => {
            state.loading = false;
            if (res) {
              proxy.$message.success('保存成功');
              state.isAddTestData = false;
              state.isEditTestData = false;
              state.isGetData = false;
              context.emit(
                'isModify',
                state.isAddProduct ||
                  state.isEditProduct ||
                  state.isAddTestData ||
                  state.isEditTestData ||
                  state.isGetData
              );
              getInspectionList();
            }
          });
        } else {
          proxy.$message.warning('请先完成必填项');
        }
      });
    };
    // 新增测试数据
    const handleAddTestData = () => {
      state.selectRawMaterial.externalCapabilityMapList.forEach((item, index) => {
        state.isAddTestData = true;
        state.tableTestForm.tableData.push({
          ...state.selectRow,
          submitStatus: 0,
          isAddRow: true,
          sourcePath: 'I',
          inspectionType: 'RAW_MATERIAL',
          isCanEditReport: index === 0,
          mainDataId: state.selectRow.id,
          materialSdccType: state.rawMaterial,
          reportNo: '',
          result: '合格',
          productionOrderId: state.detailData.id,
          ...item
        });
        delete state.tableTestForm.tableData[state.tableTestForm.tableData.length - 1].id;
      });
      context.emit(
        'isModify',
        state.isAddProduct || state.isEditProduct || state.isAddTestData || state.isEditTestData || state.isGetData
      );
      getSpanArr(state.tableTestForm.tableData);
    };
    // 取消测试数据编辑新增
    const calceTestData = () => {
      getInspectionList();
      state.isAddTestData = false;
      state.isEditTestData = false;
      state.isGetData = false;
      context.emit(
        'isModify',
        state.isAddProduct || state.isEditProduct || state.isAddTestData || state.isEditTestData || state.isGetData
      );
    };
    // 上传文件的限制
    const beforeUpload = file => {
      const fileSize = file.size / 1024 / 1024 < 20;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过20M');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };
    // 上传成功的钩子
    const handleSuccess = (res, file, files, index) => {
      if (res.code === 200) {
        for (var i = 0; i < state.spanArr[index]; i++) {
          state.tableTestForm.tableData[index + i] = { ...state.tableTestForm.tableData[index + i], ...res.data };
        }
      } else {
        proxy.$message.error(res.message);
      }
    };
    // 删除测试数据
    const handleDeleteTestData = (row, index) => {
      var deleteArrayId = [];
      for (var i = 0; i < state.spanArr[index]; i++) {
        deleteArrayId.push(state.tableTestForm.tableData[index + i].id);
      }
      proxy
        .$confirm('是否确认删除', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          showClose: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        })
        .then(() => {
          state.loading = true;
          deleteTestData(deleteArrayId).then(res => {
            state.loading = false;
            if (res) {
              proxy.$message.success('删除成功');
              state.tableTestForm.tableData.splice(index, deleteArrayId.length);
              state.oldTableTestList.splice(index, deleteArrayId.length);
            }
          });
        })
        .catch(() => {});
    };
    // 编辑样品编号和报告编号
    const handleEditGroup = (val, index, field) => {
      for (var i = 0; i < state.spanArr[index]; i++) {
        state.tableTestForm.tableData[index + i][field] = val;
      }
    };
    const getSpanArr = data => {
      state.spanArr = [];
      // data就是我们从后台拿到的数据
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          state.spanArr.push(1);
          state.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].reportId && data[i].reportId === data[i - 1].reportId) {
            state.spanArr[state.pos] += 1;
            state.spanArr.push(0);
          } else if (data[i].reportNo === data[i - 1].reportNo) {
            state.spanArr[state.pos] += 1;
            state.spanArr.push(0);
          } else {
            state.spanArr.push(1);
            state.pos = i;
          }
        }
      }
    };
    const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
      if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2 || columnIndex === 10 || columnIndex === 11) {
        const _row = state.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        };
      }
    };
    // 删除附件
    const deleteFile = (index, type, type1, type2) => {
      for (var i = 0; i < state.spanArr[index]; i++) {
        state.tableTestForm.tableData[index + i][type] = '';
        state.tableTestForm.tableData[index + i][type1] = '';
        state.tableTestForm.tableData[index + i][type2] = '';
      }
    };
    // 下载附件
    const downLoadFile = (fileId, fileName) => {
      downLoadFileApi({ fileId: fileId, fileName: fileName }).then(res => {
        const blob = new Blob([res.data], { type: '' });
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = fileName;
        a.href = blobUrl;
        a.click();
        proxy.$message.success('下载附件成功');
      });
    };
    // 编辑生产数据
    const handleEditProduct = () => {
      state.isEditProduct = true;
      context.emit(
        'isModify',
        state.isAddProduct || state.isEditProduct || state.isAddTestData || state.isEditTestData || state.isGetData
      );
    };
    return {
      ...toRefs(state),
      deleteFile,
      getDictionaryList,
      isDigital,
      handleEditProduct,
      downLoadFile,
      changeMaterial,
      objectSpanMethod,
      getSpanArr,
      handleEditGroup,
      handleDeleteTestData,
      handleSuccess,
      beforeUpload,
      calceTestData,
      handleAddTestData,
      onSaveTestData,
      handleDeleteLeft,
      handleDeleteNew,
      calceProduction,
      handleChangeDate,
      handleAddProduction,
      onSaveProduction,
      colWidth,
      closeDialog,
      uploadSdcc,
      rowClick,
      getTableList,
      getPermissionBtn,
      drageHeader,
      formatDate,
      getNameByid
    };
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  color: $tes-primary;
}
.header {
  line-height: 40px;
}
.textRight {
  text-align: right;
}
.topBtn {
  margin: 10px 0;
}
:deep(.el-date-editor.el-input) {
  width: 100%;
}
.displayInlineBlock {
  display: inline-block;
}
.fileName {
  display: inline-block;
  width: 100px;
  line-height: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
:deep(.el-upload-list__item-name) {
  width: 90px;
}
:deep(.el-upload-list__item:first-child) {
  margin-top: 0;
  line-height: 22px;
}
</style>
