<template>
  <!-- 设备维修记录 -->
  <ListLayout :has-quick-query="false" :has-button-group="getPermissionBtn('addMaintainRecord')">
    <template #search-bar>
      <div class="searchInput">
        <el-input
          v-model="formInline.condition"
          v-trim
          v-focus
          size="large"
          class="ipt-360"
          clearable
          prefix-icon="el-icon-search"
          placeholder="请输入编号/名称/描述"
          @clear="getTableList"
          @keyup.enter="getTableList"
        />
        <el-button size="large" type="primary" @click="getTableList">查询</el-button>
        <el-button size="large" @click="reset">重置</el-button>
        <el-button
          class="searchBtn"
          size="large"
          type="text"
          @click="advancedSearch"
          @keyup.prevent
          @keydown.enter.prevent
          >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
        /></el-button>
      </div>
    </template>
    <template #button-group>
      <el-button
        size="large"
        icon="el-icon-plus"
        type="primary"
        @click="addRecord"
        @keyup.prevent
        @keydown.enter.prevent
        >新增维修记录</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" :model="formInline" label-width="110px" label-position="right">
            <el-form-item label="维修结果：">
              <el-radio-group v-model="formInline.fixedResult" size="small" max="1">
                <el-radio-button @change="changeMateType">不限</el-radio-button>
                <el-radio-button v-for="(val, key) in statusJson" :key="key" :label="key" @change="changeMateType">{{
                  val
                }}</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="维修人：">
              <el-select
                v-model="formInline.fixedBy"
                class="owner-select"
                clearable
                filterable
                placeholder="请选择"
                size="small"
              >
                <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="维修日期：">
              <el-date-picker
                v-model="reviewTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="handleReview"
              />
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="tableList"
      size="medium"
      height="auto"
      fit
      border
      class="dark-table base-table format-height-table no-quick-query"
      @header-dragend="drageHeader"
    >
      <el-table-column label="仪器设备编号" prop="deviceNumber" width="160px" align="left" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-copy="row.deviceNumber" class="blue" @click="handleDetail(row)">{{ row.deviceNumber || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="仪器设备名称" prop="name" :width="colWidth.material" align="left" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="型号规格" prop="model" align="left" :min-width="colWidth.model" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.model || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="维修结果" prop="fixedResult" :width="colWidth.result">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="statusType[row.fixedResult]">{{
            statusJson[row.fixedResult]
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="维修后需重新计量" prop="isRemeasured" align="left" :width="colWidth.material">
        <template #default="{ row }">
          <span>{{ row.isRemeasured ? '需要' : '不需要' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="维修描述" prop="description" :min-width="colWidth.remark" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.description || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="维修人" prop="fixedBy" :min-width="colWidth.people" show-overflow-tooltip>
        <template #default="{ row }">
          <UserTag v-for="item in row.fixedBy.split(',')" :key="item" :name="getNameByid(item) || item || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="维修日期" prop="fixedDate" align="left" :width="colWidth.date">
        <template #default="{ row }">
          <div class="nowrap">{{ row.fixedDate || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="getPermissionBtn('editMaintainRecord') || getPermissionBtn('deleteMaintainRecord')"
        label="操作"
        fixed="right"
        :width="colWidth.operation"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <span v-if="getPermissionBtn('editMaintainRecord')" class="blue-color" @click="editRecord(row)">编辑</span>
          <span v-if="getPermissionBtn('deleteMaintainRecord')" class="blue-color" @click="deleteRecord(row)"
            >删除</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getTableList"
    />
    <template #other>
      <!-- 新增编辑记录弹出窗 -->
      <DialogRecord
        :detail-data="detailData"
        :dialog-visible="dialogVisible"
        :dialog-type="dialogType"
        :page-type="'record'"
        :equipment-list="equipmentList"
        :status-json="statusJson"
        @closeDialog="closeDialog"
      />
      <!-- 仪器设备编号详情抽屉 -->
      <Detail
        :drawer="detailDrawer"
        title="仪器设备详情"
        :equip-unit="equipUnitJson"
        :equip-list="equipList"
        :tree-data="treeData"
        :device-id="deviceId"
        @close="closeDeatilDrawer"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import Pagination from '@/components/Pagination';
import ListLayout from '@/components/ListLayout';
import { formatDate } from '@/utils/formatTime';
import { useStore } from 'vuex';
import UserTag from '@/components/UserTag';
import DialogRecord from './DialogRecord.vue';
import Detail from './instruments-equipment/detail.vue';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { getList, getTree } from '@/api/equipment';
import { getDictionary } from '@/api/user';
import { recordList, deleteRecordApi } from '@/api/maintenanceRecordList';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import { colWidth } from '@/data/tableStyle';
export default {
  name: 'MaintenanceRecordList',
  components: { Pagination, ListLayout, UserTag, DialogRecord, Detail },
  setup() {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const state = reactive({
      tableRef: ref(),
      activeName: '0',
      equipmentList: [], // 仪器设备列
      dialogType: '', // 弹出窗类型
      dialogVisible: false, // 维修记录弹出框
      detailDrawer: false, // 仪器设备编号抽屉
      ruleForm: ref(),
      deviceId: '', // 选中的设备id
      listLoading: false,
      showS: false,
      statusType: {
        Fault: 'danger',
        Running: 'success',
        Scrapped: 'info'
      },
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ],
      userOptions: store.common.nameList,
      detailData: {}, // 传递给弹出窗的详情
      types: [],
      listQuery: {
        limit: 20,
        page: 1
      },
      reviewTime: [],
      searchForm: {
        reviewerid: '', // 审核人员
        submitTime: [],
        ownerId: '', // 试验员
        key: '' // 关键字搜索
      },
      formData: {
        // 审核弹框列表
        result: '1'
      },
      formInline: {},
      tableList: [],
      nameList: store.common.nameList,
      total: 0,
      statusJson: {}, // 仪器设备维修记录状态
      equipUnitJson: {}, // 设备状态-抽屉
      treeData: [], // 仪器设备所属分类树
      equipList: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ], // 设备状态list类型
      currentAccountId: getLoginInfo().accountId
    });
    const activeTabsName = ref('first');
    const tableKey = ref(0);
    const getTableList = query => {
      const params = { ...state.formInline };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoading = true;
      recordList(params).then(res => {
        state.listLoading = false;
        if (res.data.code === 200) {
          state.total = res.data.data.totalCount;
          state.tableList = res.data.data.list;
        }
      });
    };
    getTableList();
    // 从字典获取仪器设备状态
    const getDictionaryStatus = () => {
      getDictionary(24).then(res => {
        if (res) {
          const data = res.data.data.dictionaryoption;
          state.equipList[0].group = [];
          state.equipList[1].group = [];
          data.forEach(item => {
            if (item.status === 1) {
              state.equipList[0].group.push(item);
            } else {
              state.equipList[1].group.push(item);
            }
            // 仪器设备所有状态
            state.equipUnitJson[item.code] = item.name;
            if (item.code !== 'Standby' && item.code !== 'Maintenance') {
              // 用在维修模块的状态
              state.statusJson[item.code] = item.name;
            }
          });
        }
      });
    };
    // 获取所有的仪器设备
    const getEquipment = () => {
      getList({ page: '1', limit: '999999' }).then(res => {
        state.equipmentList = res.data.data.list;
      });
    };
    // 获取仪器设备所有的所属分类
    const getAllTree = () => {
      getTree().then(res => {
        state.treeData = res.data.data;
      });
    };
    getAllTree();
    getEquipment();
    getDictionaryStatus();
    const reset = () => {
      state.formInline = {};
      state.reviewTime = [];
      state.searchForm = {
        reviewerid: '', // 审核人员
        submitTime: [],
        ownerId: '', // 试验员
        key: '' // 关键字搜索
      };
      getTableList();
    };
    // 列表操作
    const handleOperate = () => {};
    // 新增审核记录
    const addRecord = () => {
      state.dialogVisible = true;
      state.dialogType = 'add';
      state.detailData = {};
    };
    // 编辑审核记录
    const editRecord = row => {
      state.detailData = row;
      state.dialogVisible = true;
      state.dialogType = 'edit';
    };
    // 删除设备维修记录
    const deleteRecord = row => {
      proxy
        .$confirm('是否删除该条维修记录？', '删除确认', {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'warning'
        })
        .then(() => {
          deleteRecordApi(row.id).then(function (res) {
            if (res) {
              proxy.$message.success(res.data.message);
              getTableList();
            }
          });
        })
        .catch(() => {});
    };
    // 关闭弹出窗
    const closeDialog = val => {
      state.dialogVisible = false;
      if (val.isRefresh) {
        getTableList();
      }
    };
    // 仪器设备编号详情
    const handleDetail = row => {
      state.detailDrawer = true;
      state.deviceId = row.deviceId;
    };
    // 高级筛选
    const advancedSearch = () => {
      state.showS = !state.showS;
      if (state.activeName === '0') {
        state.activeName = '1';
      } else {
        state.activeName = '0';
      }
    };
    // 高级搜索-维修日期-change
    const handleReview = date => {
      state.formInline.startDate = date ? formatDate(date[0]) : '';
      state.formInline.endDate = date ? formatDate(date[1]) : '';
    };
    // 切换维修结果
    const changeMateType = () => {
      getTableList();
    };
    // 关闭仪器设备弹出窗
    const closeDeatilDrawer = val => {
      state.detailDrawer = false;
      getTableList();
    };
    return {
      ...toRefs(state),
      getDictionaryStatus,
      getEquipment,
      getPermissionBtn,
      changeMateType,
      handleDetail,
      drageHeader,
      getNameByid,
      getNamesByid,
      addRecord,
      editRecord,
      deleteRecord,
      getAllTree,
      closeDialog,
      closeDeatilDrawer,
      reset,
      handleOperate,
      formatDate,
      activeTabsName,
      getTableList,
      tableKey,
      advancedSearch,
      handleReview,
      colWidth
    };
  }
};
</script>
<style lang="scss" scoped>
.searchInput {
  display: flex;
  .el-input {
    width: 360px;
    margin-right: 10px;
  }
}
</style>
