<template>
  <div class="register-form-info info-main">
    <div class="register-title">样品信息</div>
    <el-row>
      <el-col
        v-for="item in pageView[`${sampleInfo.type}-sampleInfo`]"
        :key="item.fieldKey"
        :span="Number(item.columnWidth)"
        ><span class="label">{{ item.fieldName }}：</span>
        <template v-if="item.fieldType == 'text'">
          {{ sampleInfo[item.fieldKey] || '--' }}
        </template>
        <template v-if="item.fieldType == 'custom'">
          <template v-if="item.fieldKey == 'type'">
            <span v-if="sampleInfo[item.fieldKey]">{{ dictionaryTypeJson[sampleInfo[item.fieldKey].toString()] }}</span>
            <span v-else>--</span>
          </template>
          <template v-if="item.fieldKey == 'sampleNum'">
            {{ sampleInfo[item.fieldKey] }}
            {{ sampleInfo.sampleUnitName || filterSampleUnitToName(sampleInfo.sampleUnit) || '--' }}
          </template>
          <template v-if="item.fieldKey == 'productionProcedure'">
            {{ (sampleInfo.productionProcedure || '--') + '-' + (sampleInfo.productionStation || '--') }}
          </template>
          <template v-if="item.fieldKey == 'productionQuantity'">
            {{ filterNumber(sampleInfo.type, sampleInfo.productionQuantity, sampleInfo.inputWarehouseQuantity)
            }}{{ sampleInfo.sampleUnitName || filterSampleUnitToName(sampleInfo.productionUnit) || '--' }}
          </template>
          <template v-if="item.fieldKey == 'sampleName'">
            {{ sampleInfo.materialDesc || sampleInfo[item.fieldKey] || '--' }}
          </template>
        </template>
      </el-col>
    </el-row>
    <el-divider />
  </div>
  <div class="unqualified-item info-main margin-b-20">
    <div class="register-title">不合格项目</div>
    <el-table
      ref="tableRef"
      :key="tableKey"
      :data="unqualifiedList"
      border
      stripe
      size="medium"
      class="dark-table unqualified-table"
      :span-method="objectSpanMethod"
    >
      <el-table-column
        v-for="item in pageView[`unqualifiedItems`]"
        :key="item.fieldKey"
        :span="Number(item.columnWidth)"
        :label="item.fieldName"
        :sortable="Number(item.isSortable) === 1"
        :width="item.isMinWidth ? '' : item.columnWidth"
        :min-width="item.isMinWidth ? item.columnWidth : ''"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <template v-if="item.fieldType == 'text'">
            {{ row[item.fieldKey] || '--' }}
          </template>
          <template v-if="item.fieldType == 'person'">
            <UserTag
              v-for="personItem in row[item.fieldKey].split(',')"
              :key="personItem"
              :name="getLimsNamesByIds(personItem) || personItem || '--'"
            />
          </template>
          <template v-if="item.fieldType == 'custom'">
            <template v-if="item.fieldKey == 'paraValue'">
              <span>{{ filterResult(row[item.fieldKey]) || '--' }}</span>
            </template>
          </template>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <div class="unqualified-desc info-main margin-b-20">
    <div v-if="isHengTong">
      <div v-if="sampleInfo.type?.toString() === '1'">
        <div class="register-title">异常信息（原材料）:</div>
        <HengTongRawForm
          ref="HengTongRawFormRef"
          :show-detail="showDetail"
          :init-info="hengTongRawFormData"
          @set-info="setHengTongRawFormInfo"
        />
      </div>
      <div v-else>
        <div class="register-title">异常信息（过程、成品）:</div>
        <HengTongProductionForm
          ref="HengTongProductionFormRef"
          :show-detail="showDetail"
          :init-info="hengTongProductFormData"
          @set-info="setHengTongProductFormInfo"
        />
      </div>
    </div>
    <div v-else>
      <div class="register-title">
        不合格现象描述
        <span v-if="!showDetail" class="required-info">（必填）</span>
      </div>
      <el-input
        v-if="!showDetail"
        ref="descriptionRef"
        v-model="baseFrom.description"
        :rows="2"
        type="textarea"
        placeholder="请输入"
        maxlength="300"
        @change="changeDescription"
      />
      <el-alert v-if="showDetail" :title="baseFrom.description" :closable="false" type="error" />
    </div>
    <!-- <el-divider /> -->
  </div>
  <div class="unqualified-upload info-main info-img margin-b-20">
    <div class="register-title">
      上传照片
      <el-button
        v-if="!showDetail && imgLists.length > 0"
        size="small"
        type="primary"
        style="margin-left: 20px"
        @click="addImg"
        >选择文件</el-button
      >
    </div>
    <div ref="mainRef" class="img-main">
      <el-empty v-if="imgLists.length === 0" :image="emptyImg" :image-size="144" description="暂无图片">
        <el-button v-if="!showDetail" size="small" @click="addImg">选择文件</el-button>
      </el-empty>
      <!-- <div v-if="imgLists.length === 0" class="no-img">暂无图片</div> -->
      <el-image-viewer v-if="showViewer" :url-list="srcList" :initial-index="currentIndex" @close="closeViewer" />
      <div
        v-for="(img, index) in imgLists"
        :key="img"
        class="img-border img-list"
        :style="{ 'margin-right': marginR + 'px' }"
      >
        <el-image :src="img.imageUrl" fit="fill" @click="zoominImg(index)">
          <template #placeholder>
            <div class="image-slot">
              <i class="el-icon-loading" />
            </div>
          </template>
          <template #error>
            <div class="image-slot">
              <i class="el-icon-picture-outline" />
            </div>
          </template>
        </el-image>
        <div v-if="!showDetail" class="img-contant">
          <i class="el-icon-delete" @click="deleteImg(img)" />
        </div>
      </div>
      <input
        ref="imguploadinputRef"
        class="img-upload-input"
        type="file"
        accept=".jpg, .png"
        multiple
        @change="handleClick"
      />
    </div>
  </div>
  <div v-if="!showDetail" class="unqualified-reson info-main">
    <div class="register-title">
      原因分析负责人
      <span v-if="!showDetail" class="required-info">（必填）</span>
    </div>
    <el-select
      ref="disposalOwnerRef"
      v-model="baseFrom.disposalOwnerId"
      class="owner-select"
      placeholder="请选择负责人"
      clearable
      filterable
      @change="changeOwner"
    >
      <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
    </el-select>
  </div>
  <div v-if="!showDetail" class="bottom-btn">
    <el-button @click="closeBaseInfo">取 消</el-button>
    <el-button type="primary" @click="submitBaseInfo">提 交</el-button>
  </div>
</template>

<script>
import { ref, reactive, toRefs, onMounted, nextTick, getCurrentInstance, computed } from 'vue';
// import router from '@/router/index.js'
import { ElMessage } from 'element-plus';
import { getLimsNamesByIds } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { getLoginInfo } from '@/utils/auth';
import { uploadReportImg, delReportImgList } from '@/api/unqualifiedDisposition';
import UserTag from '@/components/UserTag';
import _ from 'lodash';
import { filterSampleUnitToName } from '@/utils/formatJson';
import { colWidth } from '@/data/tableStyle';
import { getDictionary } from '@/api/user';
import HengTongRawForm from './components/HengTongRawForm.vue';
import HengTongProductionForm from './components/HengTongProductionForm.vue';
import { objectMapper } from '@/utils/objectMapper';
import emptyImg from '@/assets/img/empty-chart.png';

export default {
  name: 'RegisterSampleInfo',
  components: { UserTag, HengTongRawForm, HengTongProductionForm },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    pageView: {
      type: Object,
      default: function () {
        return {};
      }
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setData'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    const store = useStore().state;
    const datas = reactive({
      showInfo: props.show,
      HengTongRawFormRef: ref(),
      HengTongProductionFormRef: ref(),
      showDetail: route.params.title === 'detail',
      dictionaryTypeJson: {},
      currentAccountId: '',
      userOptions: store.common.nameList,
      sampleInfo: props.data,
      tableKey: '111',
      unqualifiedList: props.data.expList || [],
      baseFrom: {
        description: props.data.description ? props.data.description : '',
        disposalOwnerId: props.data.disposalOwnerId ? props.data.disposalOwnerId : ''
      },
      imgLists: [],
      srcList: [],
      showViewer: false,
      currentIndex: 0,
      marginR: 15,
      spanArr: [],
      pos: 0,
      descriptionRef: ref(),
      disposalOwnerRef: ref(),
      hengTongProductFormData: {
        region: '',
        // 缺陷分类 字典code
        flawClassify: '',
        productClassify: '',
        qualityLevel: '',
        productionPersonnel: '',
        productionDate: '',
        incomingStation: '',
        contractOffice: '',
        contractOrderNo: '',
        contractClassify: '',
        remark: '',
        unqualifiedDescription: '',
        correctiveAction: ''
      },
      hengTongRawFormData: {
        region: '',
        // 缺陷分类 字典code
        flawClassify: '',
        qualityLevel: '',
        arrivalDate: '',
        contractOffice: '',
        contractOrderNo: '',
        contractClassify: '',
        remark: '',
        unqualifiedDescription: '',
        correctiveAction: '',
        pageViewGroup: props.pageView
      }
    });
    if (getLoginInfo()) {
      datas.currentAccountId = getLoginInfo().accountId;
    }

    // 过滤关键参数
    const filterExpList = list => {
      if (list && list.length > 0) {
        const testItems = [];
        list.forEach(i => {
          if (i.expParentId && i.expParentId !== '0' && i.status !== -2) {
            testItems.push(i);
          }
        });
        if (testItems.length > 0) {
          testItems.forEach(ti => {
            const hasitem = _.filter(list, res2 => {
              return res2.expId === ti.expParentId;
            });
            if (hasitem.length > 0) {
              ti.parentName = hasitem[0].expName;
              ti.parentObj = hasitem[0];
            } else {
              ti.parentObj = {};
            }
          });
        }
        var newTestItems = _.orderBy(testItems, ['parentName']);
        return newTestItems;
      }
      return [];
    };

    const filterNewExpList = list => {
      var newTestItems = [];
      if (list && list.length > 0) {
        list.forEach(item => {
          if (item.paraVoList && item.paraVoList.length > 0) {
            item.paraVoList.forEach(volist => {
              volist.capabilityName = item.capabilityName;
              volist.ownerId = item.ownerId;
              newTestItems.push(volist);
            });
          }
        });
        return newTestItems;
      }
      return [];
    };

    if (datas.unqualifiedList && datas.unqualifiedList.length > 0) {
      datas.unqualifiedList = filterNewExpList(datas.unqualifiedList);
    }

    if (props.data.imageList && props.data.imageList.length > 0) {
      // eslint-disable-next-line vue/no-setup-props-destructure
      datas.imgLists = props.data.imageList;
      datas.imgLists.forEach(img => {
        img.imageUrl = img.url;
        datas.srcList.push(img.url);
      });
    }

    // 过滤生产/入库数量
    const filterNumber = (type, number, number1) => {
      if (type === 2 || type === 3) {
        return number;
      } else {
        return number1;
      }
    };
    const getDictionaryList = () => {
      getDictionary('JYLX').then(res => {
        if (res) {
          res.data.data.dictionaryoption.forEach(item => {
            datas.dictionaryTypeJson[item.code] = item.name;
          });
        }
      });
    };
    getDictionaryList();
    // 过滤检测结果
    const filterResult = result => {
      var expValueStr = [];
      if (result) {
        var expValue = result;
        if (expValue && expValue.length > 0) {
          expValue.forEach(v => {
            if (v.paraValue) {
              const str = v.colourValue + ' ' + v.paraValue;
              expValueStr.push(str);
            }
          });
        }
      }
      return expValueStr.join(';');
    };
    // 上传图片模块
    const mainRef = ref();
    const imguploadinputRef = ref();
    onMounted(() => {
      proxy.getSpanArr(datas.unqualifiedList);
      nextTick(() => {
        datas.srcList = formatImgUrl(datas.imgLists);
        const mainWidth = mainRef.value.offsetWidth;
        const num = parseInt(mainWidth / (146 + 5));
        const otherWidth = mainWidth - num * (146 + 0);
        datas.marginR = otherWidth / (num * 2);
      });
    });
    const addImg = () => {
      imguploadinputRef.value.click();
    };
    // 选择图片
    const handleClick = e => {
      const files = e.target.files;
      [...files].forEach(file => {
        const params = new FormData();
        params.append('file', file);
        params.append('name', file.name);
        uploadReportImg(params, callback => {}).then(res => {
          if (res !== false) {
            const imgData = res.data.data;
            const param = {
              imageId: imgData.imageId,
              imageUrl: imgData.url,
              name: imgData.name,
              remoteName: imgData.remoteName,
              id: imgData.id
            };
            datas.imgLists.push(param);
            datas.srcList.push(imgData.url);
            datas.sampleInfo.imageList = datas.imgLists;
          } else {
            return false;
          }
        });
        if (!file) {
          return false;
        }
        readerData(file);
      });
      e.target.value = '';
    };
    const readerData = rawFile => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => {
          resolve();
        };
        reader.readAsDataURL(rawFile);
      });
    };
    // formatImgUrl
    const formatImgUrl = list => {
      const urlArr = [];
      if (list.length > 0) {
        list.forEach(img => {
          urlArr.push(img.imageUrl);
        });
      }
      return urlArr;
    };
    // 图片预览
    const zoominImg = index => {
      datas.currentIndex = index;
      datas.showViewer = true;
    };
    // 关闭预览
    const closeViewer = () => {
      datas.showViewer = false;
    };
    // 删除图片
    const deleteImg = img => {
      const param = {
        id: img.id,
        imageId: img.imageId,
        remoteName: img.remoteName
      };
      delReportImgList(param).then(res => {
        if (res !== false) {
          ElMessage.success('删除成功');
          _.pullAll(datas.imgLists, [img]);
          _.pullAll(datas.srcList, [img.imageUrl]);
        }
      });
    };

    // 提交-样品信息
    const submitBaseInfo = () => {
      if (isHengTong.value) {
        if (datas.sampleInfo.type?.toString() === '1') {
          Object.assign(datas.sampleInfo, datas.hengTongRawFormData);
        } else {
          Object.assign(datas.sampleInfo, datas.hengTongProductFormData);
        }
      } else {
        datas.sampleInfo.description = datas.baseFrom.description;
      }
      datas.sampleInfo.disposalOwnerId = datas.baseFrom.disposalOwnerId;
      datas.sampleInfo.imageIds = [];
      if (datas.imgLists.length > 0) {
        datas.imgLists.forEach(img => {
          datas.sampleInfo.imageIds.push(img.id);
        });
      }
      context.emit('setData', datas.sampleInfo);
    };
    // 取消-样品信息
    const closeBaseInfo = () => {
      datas.showInfo = false;
      context.emit('close', false);
    };
    // 不合格现象描述-change
    const changeDescription = value => {
      if (value) {
        datas.descriptionRef.$refs.textarea.style = 'border: 1px solid #EBEEF5;border-radius:8px;';
      }
    };
    // 原因负责人-change
    const changeOwner = value => {
      if (value) {
        datas.disposalOwnerRef.$refs.reference.$refs.input.style = 'border: 1px solid #EBEEF5;border-radius:8px;';
      }
    };

    const setHengTongRawFormInfo = info => {
      Object.assign(datas.hengTongRawFormData, info);
    };

    const setHengTongProductFormInfo = info => {
      Object.assign(datas.hengTongProductFormData, info);
    };
    const onSubmit = () => {
      if (datas.sampleInfo.type?.toString() === '1') {
        if (proxy.$refs['HengTongRawFormRef']) {
          return proxy.$refs['HengTongRawFormRef'].onSubmit();
        }
      } else {
        if (proxy.$refs['HengTongProductionFormRef']) {
          return proxy.$refs['HengTongProductionFormRef'].onSubmit();
        }
      }
    };
    const isHengTong = computed({
      get: () => store?.user?.tenantInfo?.clientCode === 'hengtong001'
    });

    const initData = () => {
      if (isHengTong.value) {
        if (datas.sampleInfo.type?.toString() === '1') {
          objectMapper(datas.hengTongRawFormData, datas.sampleInfo);
        } else {
          objectMapper(datas.hengTongProductFormData, datas.sampleInfo);
        }
        if (datas.sampleInfo.processInstanceId === '') {
          datas.showDetail = false;
        }
      }
    };

    initData();

    return {
      ...toRefs(datas),
      formatDate,
      onSubmit,
      getLimsNamesByIds,
      getDictionaryList,
      submitBaseInfo,
      closeBaseInfo,
      filterNumber,
      filterResult,
      emptyImg,
      isHengTong,
      mainRef,
      imguploadinputRef,
      zoominImg,
      closeViewer,
      handleClick,
      addImg,
      deleteImg,
      filterExpList,
      changeDescription,
      changeOwner,
      filterSampleUnitToName,
      colWidth,
      setHengTongRawFormInfo,
      setHengTongProductFormInfo
    };
  },
  created() {},
  methods: {
    getSpanArr(data) {
      // data就是我们从后台拿到的数据
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].capabilityName === data[i - 1].capabilityName) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        };
      }
    }
  }
};
</script>
<style lang="scss" scoped>
@import './common.scss';
.label {
  color: $tes-font2;
  display: inline-block;
  width: 10rem;
  text-align: right;
}
.title {
  height: 25px;
  line-height: 25px;
  text-align: left;
  font-weight: bold;
  .title-require {
    color: #f56c6c;
  }
}
.margin-b-20 {
  margin-bottom: 20px;
}
.register-form-info {
  .el-row {
    line-height: 32px;
    // margin-left: 13px;
    text-align: left;
    font-size: 14px;
    .el-col {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .el-form-item {
    width: 33%;
    margin-right: 0px;
    padding-left: 20px;
    text-align: left;
    :deep(.el-form-item__content) {
      width: inherit;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .is-required {
    padding-left: 10px;
  }
  .add-item-btn {
    width: 100%;
    padding-left: 16px;
  }
}
.unqualified-upload {
  .img-main {
    overflow: auto;
    padding-left: 12px;
  }
  .img-border {
    border: 1px solid #e6e8ee !important;
  }
  .img-border:hover {
    .img-contant {
      left: 0px;
      display: inline-block;
    }
  }
  .img-list {
    width: 146px;
    height: 146px;
    border: 1px dashed #e6e8ee;
    text-align: center;
    border-radius: 4px;
    float: left;
    margin: 15px 15px 15px 0px;
    position: relative;
    .el-image {
      width: 100%;
      height: 100%;
      border-radius: 4px;
    }
    .el-icon-plus {
      line-height: 146px;
      font-size: 26px;
      color: #909399;
      cursor: pointer;
    }
    .el-icon-picture-outline,
    .el-icon-loading {
      line-height: 146px;
      font-size: 26px;
      color: #909399;
    }
    .el-checkbox {
      float: right;
      margin: 5px;
      :deep(.el-checkbox__inner) {
        border-radius: 14px;
      }
    }
    .img-contant {
      display: none;
      background: #000000;
      opacity: 0.5;
      color: #ffffff;
      position: absolute;
      bottom: 0;
      width: 100%;
      border-radius: 0px 0px 4px 4px;
      height: 40px;
      line-height: 40px;
      font-size: 16px;
      i {
        margin: 0px 10px;
        cursor: pointer;
      }
    }
  }
  .img-upload-input {
    display: none;
    z-index: -9999;
  }
}
.unqualified-reson {
  // border-top: 1px solid #EBEEF5;
  .owner-select {
    width: 20%;
    margin-bottom: 16px;
  }
}
.bottom-btn {
  text-align: right;
  margin-top: 10px;
  width: 100%;
  padding: 1rem;
}
.info-img {
  padding-bottom: 20px;
  .el-empty {
    padding: 0;
    .el-button {
      margin-top: 10px;
    }
  }
}
</style>
