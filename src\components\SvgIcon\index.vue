<template>
  <div v-if="isExternalIcon" :style="styleExternalIcon" class="svg-external-icon svg-icon" v-on="$attrs" />
  <svg v-else ref="svgRef" class="svg-icon" :width="width" :height="width">
    <use />
  </svg>
</template>

<script>
import { ref, computed } from 'vue';
import { isExternal } from '@/utils/validate';
import Icons from '@/icons';

export default {
  name: 'SvgIcon',
  props: {
    iconClass: {
      type: String,
      required: true
    },
    className: {
      type: String,
      default: ''
    },
    width: {
      type: Number,
      default: 10
    },
    height: {
      type: Number,
      default: 10
    }
  },
  setup(props) {
    const isExternalIcon = computed(() => isExternal(props.iconClass));
    const styleExternalIcon = computed(() => ({
      mask: `url(${props.iconClass}) no-repeat 50% 50%`,
      '-webkit-mask': `url(${props.iconClass}) no-repeat 50% 50%`
    }));
    // const svgClass = computed(() => (props.className ? 'svg-icon ' + props.className : 'svg-icon'));
    // const icon = defineAsyncComponent(Icons[`./svg/${props.iconClass}.svg`])
    const svgRef = ref();
    const iconPromise = Icons[`./svg/${props.iconClass}.svg`];
    if (iconPromise) {
      iconPromise().then(res => {
        if (res && typeof res === 'string') {
          const str = res
            .replace(/<svg[^]+?(viewBox="[\d\.\s]+")[^]*?>/, `<symbol id="${props.iconClass}" $1>`)
            .replace(/<\/svg>/, '</symbol>');
          const useElement = document.createElementNS('http://www.w3.org/2000/svg', 'use');
          useElement.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', '#' + props.iconClass);
          if (svgRef.value) {
            svgRef.value.innerHTML = str;
            svgRef.value.appendChild(useElement);
          }
        }
      });
    }
    return {
      isExternalIcon,
      styleExternalIcon,
      svgRef
    };
  }
};
</script>

<style lang="scss">
.svg-icon {
  vertical-align: -0.15em;
  /* fill: currentColor !important; */
  overflow: hidden;
}

.svg-color-first-stop-color {
  stop-color: var(--tesPrimary3);
}

.svg-color-second-stop-color {
  stop-color: var(--menuHover);
}

.svg-color-first-fill {
  fill: var(--tesPrimary3);
}

.svg-color-first-stroke {
  stroke: var(--tesPrimary4) !important;
  fill: var(--tesPrimary3) !important;
}

.svg-color-rect-fill {
  fill: var(--tesPrimary3);
}

.svg-external-icon {
  background-color: currentColor;
  mask-size: cover !important;
  display: inline-block;
}
</style>
