<template>
  <!-- 辅料出入记录 -->
  <ListLayout v-loading="expLoading">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="searchForm" class="page-searchbar" @submit.prevent>
        <el-form-item prop="condition">
          <el-input
            v-model="searchForm.submit.condition"
            v-trim
            v-focus
            placeholder="请输入辅料编号/名称/描述/批号"
            class="ipt-360"
            prefix-icon="el-icon-search"
            size="large"
            clearable
            @keyup.enter="getTableList()"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="getTableList()">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
          <el-button
            class="searchBtn"
            size="large"
            type="text"
            @click="advancedSearch"
            @keyup.prevent
            @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']" />
          </el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('exportMaterialExcel')"
        type="primary"
        size="large"
        @click="handleExport"
        @keyup.prevent
        @keydown.enter.prevent
        ><span class="iconfont tes-task-issued" /> 导出</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" :model="searchForm" label-width="110px" label-position="right">
            <el-form-item label="登记人：" prop="userId" class="seal-staff-name">
              <el-select
                v-model="searchForm.submit.userId"
                placeholder="请选择登记人"
                size="small"
                clearable
                filterable
              >
                <el-option v-for="item in nameList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="登记日期：">
              <el-date-picker
                v-model="searchForm.recordDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="
                  val => {
                    return changeDate(val, 'recordStartDate', 'recordEndDate');
                  }
                "
              />
            </el-form-item>
            <el-form-item label="有效期至：">
              <el-date-picker
                v-model="searchForm.expireDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="
                  val => {
                    return changeDate(val, 'expireStartDate', 'expireEndDate');
                  }
                "
              />
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-radio-group v-model="searchForm.submit.type" size="small" style="float: left" @change="changeStatus">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button v-for="(value, key, index) in statusList" :key="index" :label="key">{{
          value
        }}</el-radio-button>
      </el-radio-group>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      height="auto"
      size="medium"
      highlight-current-row
      class="dark-table format-height-table base-table"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column label="辅料编号" prop="no" :width="colWidth.orderNo" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <div
            v-copy="row.no"
            class="blue-color"
            @click="
              drawerVisiable = true;
              detailData = row;
            "
          >
            {{ row.no || '--' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="辅料名称" prop="name" :width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.name || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="型号规格" prop="model" :width="colWidth.model" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.model || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="辅料描述" prop="remark" :width="colWidth.description" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.remark || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="有效期至" prop="expireDate" :width="colWidth.date" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ formatDate(row.expireDate) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="批号" prop="batchNo" :width="colWidth.orderNo" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.batchNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="供应商" prop="provider" :width="colWidth.departmentUnit" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.provider || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="关联样品编号" prop="sampleNo" :width="colWidth.orderNo" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.sampleNo" class="blue-color" @click="handleRouter(row)">{{ row.sampleNo }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="操作类型" prop="type" :width="colWidth.typeGroup" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ operateType[row.type] || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数量" prop="num" :width="colWidth.amount" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.num || '--' }}</span>
          <span v-if="row.unit">{{ unitJson[row.unit] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="登记日期" prop="recordDate" :width="colWidth.date" sortable>
        <template #default="{ row }">
          <span>{{ formatDate(row.recordDate) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="登记人" prop="userId" :width="colWidth.person">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.userId) || row.userId || '--'" />
        </template>
      </el-table-column>
    </el-table>
    <pagination :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getTableList" />
    <DrawerStandingBook
      :drawer="drawerVisiable"
      :unit-list="unitList"
      :unit-json="unitJson"
      :detail-data="detailData"
      :tree-data="dialogTreeData"
      :type="'check'"
      @close="closeDrawer"
    />
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import Pagination from '@/components/Pagination';
import router from '@/router/index.js';
import { getList } from '@/api/materialsStatistical';
import { getNameByid, getNamesByid, getPermissionBtn } from '@/utils/common';
import DrawerStandingBook from '../laboratory-management/standing-book/DrawerStandingBook';
import { useStore } from 'vuex';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { getDictionary } from '@/api/user';
import ListLayout from '@/components/ListLayout';
import UserTag from '@/components/UserTag';
import { getTree } from '@/api/standingBook';
import { formatTree } from '@/utils/formatJson';
import { colWidth } from '@/data/tableStyle';

export default {
  name: 'MaterialsStatistical',
  components: { Pagination, ListLayout, UserTag, DrawerStandingBook },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const store = useStore();
    const state = reactive({
      tableData: [],
      editFrom: ref(0),
      unitList: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ],
      unitJson: {},
      drawerVisiable: false,
      detailData: {},
      drawerType: 'check',
      dialogTreeData: [],
      operateType: {
        Entry: '入库',
        Delivery: '出库'
      },
      total: 0,
      nameList: store.state.common.nameList,
      searchForm: {
        recordDate: [],
        expireDate: [],
        submit: {
          type: ''
        }
      },
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ],
      showS: false,
      activeName: '0',
      expLoading: false, // 导出的loading
      tableLoading: false, // 表格加载的loading
      exportData: [], // 需要导出的数据
      listQuery: {
        page: 1,
        limit: 20
      },
      statusList: {
        Entry: '入库',
        Delivery: '出库'
      } // 类型
    });
    const tableKey = ref(0);
    const changeStatus = val => {
      getTableList();
    };
    const reset = () => {
      state.listQuery.page = 1;
      state.listQuery.limit = 20;
      state.searchForm = {
        recordDate: [],
        expireDate: [],
        submit: {
          type: ''
        }
      };
      getTableList();
    };
    const getTableList = query => {
      var params = JSON.parse(JSON.stringify(state.searchForm.submit));
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        params.page = state.listQuery.page.toString();
        params.limit = state.listQuery.limit.toString();
      }
      state.tableLoading = true;
      getList(params).then(res => {
        state.tableLoading = false;
        if (res) {
          state.tableData = res.data.data.list;
          state.total = res.data.data.totalCount;
        }
      });
    };
    getTableList();
    // 获取库存单位
    const getDictionaryList = () => {
      state.unitJson = {};
      getDictionary('5').then(res => {
        res.data.data.dictionaryoption.forEach(item => {
          state.unitJson[item.code] = item.name;
          if (item.status === 1) {
            state.unitList[0].group.push(item);
          } else {
            state.unitList[1].group.push(item);
          }
        });
      });
    };
    getDictionaryList();
    // 高级搜索
    const advancedSearch = () => {
      state.showS = !state.showS;
      if (state.activeName === '0') {
        state.activeName = '1';
      } else {
        state.activeName = '0';
      }
    };
    const closeDrawer = value => {
      state.drawerVisiable = false;
    };
    // 导出
    const handleExport = () => {
      getList({ page: 1, limit: -1, ...state.searchForm.submit }).then(res => {
        state.exportData = [];
        res.data.data.list.forEach((row, index) => {
          // 操作类型
          row.operateType = row.type === 'Entry' ? '入库' : '出库';
          // 登记日期
          row.recordDate = formatDate(row.recordDate);
          // 有效期
          row.expireDate = formatDate(row.expireDate);
          // 登记人
          row.userName = row.userId ? getNameByid(row.userId) : row.userId;
          // 数量
          row.num = state.unitJson[row.unit] ? row.num + state.unitJson[row.unit] : row.num;
          state.exportData.push(row);
        });
        export2Excel();
        proxy.$message.success('导出成功！');
      });
    };
    const export2Excel = () => {
      state.expLoading = true;
      const fileName = '辅料管理统计报表';
      const tHeader = [
        '辅料编号',
        '辅料名称',
        '型号规格',
        '辅料描述',
        '有效期',
        '批号',
        '供应商',
        '关联样品编号',
        '操作类型',
        '数量',
        '登记人',
        '登记日期'
      ];
      const filterVal = [
        'no',
        'name',
        'model',
        'remark',
        'expireDate',
        'batchNo',
        'provider',
        'sampleNo',
        'operateType',
        'num',
        'userName',
        'recordDate'
      ];
      import('@/utils/Export2Excel').then(excel => {
        const data = formatJson(filterVal, state.exportData);
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: fileName,
          autoWidth: true,
          bookType: 'xlsx'
        });
        state.expLoading = false;
      });
    };
    const formatJson = (filterVal, jsonData) => {
      return jsonData.map(v =>
        filterVal.map(j => {
          return v[j];
        })
      );
    };
    // 样品、报告详情跳转
    const handleRouter = (row, type) => {
      router.push({
        path: '/sample/material/detail',
        query: {
          orderId: row.orderId,
          sampleId: row.sampleId
        }
      });
    };
    // 格式化日期
    const changeDate = (val, startTime, endTime) => {
      if (val) {
        state.searchForm.submit[startTime] = formatDate(val[0]);
        state.searchForm.submit[endTime] = formatDate(val[1]);
      } else {
        state.searchForm.submit[startTime] = '';
        state.searchForm.submit[endTime] = '';
      }
    };
    // 列表排序
    const sortChange = column => {
      state.searchForm.submit.orderBy = column.prop;
      state.searchForm.submit.isAsc = !state.searchForm.isAsc;
      getTableList();
    };
    // 获取左侧列表树接口
    const getDrawerTree = () => {
      getTree().then(res => {
        if (res) {
          const data = res.data.data;
          state.dialogTreeData = formatTree(data);
        }
      });
    };
    getDrawerTree();
    return {
      ...toRefs(state),
      formatTree,
      sortChange,
      closeDrawer,
      colWidth,
      getDrawerTree,
      advancedSearch,
      getDictionaryList,
      export2Excel,
      formatJson,
      changeDate,
      getPermissionBtn,
      handleRouter,
      handleExport,
      changeStatus,
      getTableList,
      formatDate,
      getNameByid,
      getNamesByid,
      drageHeader,
      tableKey,
      reset
    };
  }
};
</script>
<style lang="scss" scoped>
.collapseTable {
  margin-top: 10px;
  .item {
    display: flex;
    align-items: center;
    .custom-icon {
      padding: 2px 5px;
      margin-right: 10px;
    }
  }
}
:deep(.el-collapse-item__header) {
  height: 20px;
  background-color: transparent;
  font-size: 16px;
  margin-bottom: 5px;
  border: 0;
}
:deep(.el-collapse) {
  border: 0;
}
:deep(.el-collapse-item__content) {
  padding-bottom: 0;
}
:deep(.el-collapse-item__wrap) {
  border-bottom: 0;
}
.inlineBlock {
  display: inline-block;
}
.isShowHand {
  cursor: pointer;
}

.el-button--primary {
  border: 0;
  border-radius: 4px;
}
.searchLeft {
  width: 70%;
  display: inline-block;
  .el-input {
    margin-right: 8px;
    width: 38%;
  }
}
</style>
