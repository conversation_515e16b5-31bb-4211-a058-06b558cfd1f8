<template>
  <el-dialog
    v-model="dialogShow"
    title="重点设备"
    :close-on-click-modal="false"
    width="780px"
    custom-class="dialog_key_equipment"
    @close="handleDialogClose"
  >
    <el-row>
      <el-col :span="24" class="btnGroup">
        <el-button type="primary" size="small" class="el-icon-plus" @click="handleAddEquipment()"> 新增</el-button>
        <el-button type="danger" size="small" class="el-icon-delete" @click="handleDelete()"> 删除</el-button>
      </el-col>
    </el-row>
    <el-table
      v-if="dialogShow"
      id="sortableList"
      ref="tableRef"
      :key="tableKey"
      v-loading="dialogLoading"
      :data="tableData"
      fit
      border
      size="medium"
      highlight-current-row
      class="dark-table base-table no-quick-query format-height-table3"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      @header-dragend="drageHeader"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
    >
      <el-table-column v-if="!isDelete" label="排序" :width="colWidth.serialNo" align="center">
        <i class="tes-move iconfont" style="font-size: 12px; cursor: move" />
      </el-table-column>
      <el-table-column v-if="!isDelete" label="序号" :width="colWidth.serialNo" align="center">
        <template #default="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column v-if="isDelete" type="selection" prop="checkbox" :width="colWidth.checkbox" align="center" />
      <el-table-column label="设备编号" prop="deviceNumber" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.deviceNumber || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="设备名称" prop="deviceName" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.deviceName || '--' }}</div>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose()">关 闭</el-button>
        <el-button
          v-if="isDelete"
          type="danger"
          :loading="dialogLoading"
          @click="onSubmit"
          @keyup.prevent
          @keydown.enter.prevent
          >删除确认</el-button
        >
      </span>
    </template>
    <!-- 添加重点设备 -->
    <AddMultipleEquipment
      :show="dialogSelectEquipment"
      :select-info="tableData"
      @select-data="handleSelectData"
      @close="handleCloseDialog"
    />
  </el-dialog>
</template>
<script>
import { reactive, ref, toRefs, watch, getCurrentInstance, nextTick } from 'vue';
import { colWidth } from '@/data/tableStyle';
import Sortable from 'sortablejs';
import { drageHeader } from '@/utils/formatTable';
import { deviceSaveKey, deviceListKey, deviceDeleteKey, updateKeyOrder } from '@/api/equipmentUsagePlan';
import AddMultipleEquipment from '../../../../components/BusinessComponents/AddMultipleEquipment.vue';

export default {
  name: 'DialogKeyEquipment',
  components: { AddMultipleEquipment },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    deviceList: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      dialogLoading: false, // 弹出窗loading
      dialogSelectEquipment: false, // 添加设备弹出框
      isRefresh: false, // 关闭弹出框列表是否需要刷新
      isDelete: false,
      tableKey: 0,
      tableRef: ref(),
      selectDevice: [], // 删除的设备id
      tableData: [], // 重点设备列表
      dialogShow: false
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.tableData = props.deviceList;
        nextTick(() => {
          rowDrop();
        });
        state.isDelete = false;
        state.isRefresh = false;
        state.selectDevice = [];
        state.tableRef?.clearSelection();
      }
    });
    const handleDelete = () => {
      state.isDelete = true;
    };
    // 获取重点设备列表
    const getDeviceKeyPoint = () => {
      deviceListKey().then(res => {
        if (res) {
          state.tableData = res.data.data;
        }
      });
    };
    // 行拖拽
    const rowDrop = () => {
      // 获取当前表格
      const el = document.getElementById('sortableList').querySelector('.el-table__body-wrapper tbody');
      Sortable.create(el, {
        animation: 300,
        handle: '.tes-move',
        draggable: '.el-table__row',
        ghostClass: 'ghost',
        // 拖动对象移动样式
        dragClass: 'drag',
        forceFallback: true,
        onEnd: function (evt) {
          if (evt.oldIndex !== evt.newIndex) {
            const currRow = state.tableData.splice(evt.oldIndex, 1)[0];
            state.tableData.splice(evt.newIndex, 0, currRow);
            state.tableData.forEach((value, index) => {
              value.order = index;
            });
            handleSaveOrder();
            state.tableKey += 1;
          }
        }
      });
    };
    // 拖拽排序
    const handleSaveOrder = () => {
      updateKeyOrder(state.tableData).then(res => {
        if (res) {
          state.isRefresh = true;
          proxy.$message.success('设置成功！');
        }
      });
    };
    const handleSelectData = val => {
      if (val?.length) {
        const params = [];
        val.forEach((item, index) => {
          params.push({
            deviceId: item.id,
            deviceName: item.name,
            deviceNumber: item.deviceNumber,
            order: state.tableData.length + index
          });
        });
        deviceSaveKey(params).then(res => {
          if (res) {
            state.isRefresh = true;
            proxy.$message.success('设置重点设备成功！');
            getDeviceKeyPoint();
          }
        });
      }
      state.dialogSelectEquipment = false;
    };
    const handleCloseDialog = () => {
      state.dialogSelectEquipment = false;
    };
    const onSubmit = () => {
      if (!state.selectDevice.length) {
        proxy.$message.warning('请先勾选要删除的设备');
        return false;
      }
      deviceDeleteKey(
        state.selectDevice.map(item => {
          return item.deviceId;
        })
      ).then(res => {
        if (res) {
          state.isRefresh = true;
          proxy.$message.success('删除成功！');
          handleClose();
        }
      });
    };
    const handleRowClick = row => {
      if (state.isDelete) {
        state.tableRef.toggleRowSelection(
          row,
          !state.selectDevice.some(item => {
            return row.id === item.id;
          })
        );
      }
    };
    // 添加设备
    const handleAddEquipment = () => {
      state.dialogSelectEquipment = true;
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog', state.isRefresh);
      state.dialogShow = false;
    };
    const handleDialogClose = () => {
      context.emit('closeDialog', state.isRefresh);
      state.dialogShow = false;
    };
    // 选择
    const handleSelectionChange = val => {
      state.selectDevice = val;
    };
    return {
      ...toRefs(state),
      rowDrop,
      handleDialogClose,
      handleAddEquipment,
      handleSelectData,
      handleCloseDialog,
      handleRowClick,
      handleSelectionChange,
      handleDelete,
      colWidth,
      drageHeader,
      onSubmit,
      handleClose
    };
  }
};
</script>
<style lang="scss" scoped>
.btnGroup {
  margin-bottom: 10px;
  text-align: right;
}
</style>
<style lang="scss">
.dialog_key_equipment {
  .format-height-table3 {
    .el-table__body-wrapper {
      max-height: calc(100vh - 37.5rem) !important;
      overflow-y: auto;
    }
    .el-table__fixed-body-wrapper {
      max-height: calc(100vh - 38rem) !important;
      overflow-y: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;
    }
    .el-table__fixed-body-wrapper::-webkit-scrollbar {
      display: none;
    }
  }
}
</style>
