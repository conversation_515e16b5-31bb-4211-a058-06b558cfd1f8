<template>
  <el-drawer
    v-model="showDrawer"
    :title="drawerTitles"
    direction="rtl"
    :before-close="handleClose"
    :size="750"
    destroy-on-close
    :close-on-click-modal="false"
    @opened="handleOpened"
  >
    <DrawerLayout :has-left-panel="false" :has-button-group="false" :has-page-header="false">
      <div class="form-height-auto">
        <el-form v-if="showDrawer" ref="formRef" :class="{ isCheck: isCheck && type !== 'add' }" :model="formData">
          <el-form-item
            label="字典名称："
            prop="name"
            :rules="{ required: !isCheck, message: '请输入字典名称', trigger: 'change' }"
          >
            <el-input
              v-if="!isCheck"
              ref="dictionaryName"
              v-model="formData.name"
              v-trim
              v-focus
              maxlength="30"
              placeholder="请输入字典名称"
              tabindex="0"
            />
            <span v-else>{{ formData.name }}</span>
          </el-form-item>
          <el-form-item
            label="字典编号："
            prop="code"
            :rules="[{ required: type === 'add', message: '请输入字典编号', trigger: 'change' }]"
          >
            <el-input
              v-if="type === 'add'"
              v-model.trim="formData.code"
              :disabled="type !== 'add'"
              maxlength="30"
              placeholder="请输入字典编号"
            />
            <span v-else>{{ formData.code }}</span>
          </el-form-item>
          <el-form-item
            label="字典类型："
            prop="dictionaryType"
            :rules="{ required: type === 'add', message: '请选择字典类型', trigger: 'change' }"
          >
            <el-select
              v-if="type === 'add'"
              v-model="formData.dictionaryType"
              :disabled="type !== 'add'"
              placeholder="请选择字典类型"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option v-for="(val, key, index) in dictionaryTypeJson" :key="index" :label="val" :value="key" />
            </el-select>
            <span v-else>{{ dictionaryTypeJson[formData.dictionaryType] }}</span>
          </el-form-item>
          <el-form-item label="字典描述：" prop="description">
            <el-input
              v-if="!isCheck"
              v-model="formData.description"
              type="textarea"
              maxlength="300"
              :rows="3"
              placeholder="请输入字典描述"
            />
            <span v-else>{{ formData.description }}</span>
          </el-form-item>
        </el-form>
        <div class="dictionaryDrawer">
          <div class="flex items-center justify-between">
            <el-input
              v-model="keyword"
              v-trim
              placeholder="请输入字典名称/选项编号"
              size="small"
              class="!w-[250px]"
              prefix-icon="el-icon-search"
              clearable
              @input="onChangeKeywordFilter()"
            />
            <el-button
              v-show="!isCheck"
              type="primary"
              size="small"
              class="addDictionaryBtn"
              @click="handleTableRow"
              @keyup.prevent
              @keydown.enter.prevent
              >添加字典选项</el-button
            >
          </div>
          <el-table
            id="sortableList"
            ref="tableRef"
            :key="tableKey"
            v-loading="tableLoading"
            :data="tableData"
            fit
            border
            height="auto"
            size="medium"
            class="dark-table base-table format-height-table2 dictionary-table"
            @header-dragend="drageHeader"
          >
            <el-table-column v-if="!isCheck" label="排序" width="50px" align="center">
              <i class="tes-move iconfont" style="font-size: 12px; cursor: move" />
            </el-table-column>
            <el-table-column label="序号" prop="name" min-width="40px" show-overflow-tooltip>
              <template #default="{ $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="选项名称" prop="name" min-width="110px" show-overflow-tooltip>
              <template #default="{ row, $index }">
                <div v-if="isCheck">{{ row.name || '--' }}</div>
                <el-input
                  v-if="!isCheck"
                  :ref="'nameRef' + $index"
                  v-model.trim="row.name"
                  maxlength="100"
                  placeholder="选项名称"
                />
              </template>
            </el-table-column>
            <el-table-column label="选项编号" prop="code" min-width="90px" show-overflow-tooltip>
              <template #default="{ row }">
                <el-input v-if="!row.id" v-model.trim="row.code" maxlength="100" placeholder="选项编号" />
                <div v-else class="nowrap">{{ row.code || '--' }}</div>
              </template>
            </el-table-column>
            <el-table-column label="选项描述" prop="description" min-width="100px" show-overflow-tooltip>
              <template #default="{ row }">
                <div v-if="isCheck" class="nowrap">{{ row.description || '--' }}</div>
                <el-input v-if="!isCheck" v-model.trim="row.description" maxlength="5000" placeholder="选项描述" />
              </template>
            </el-table-column>
            <el-table-column label="状态" prop="switch" min-width="50px">
              <template #default="{ row }">
                <span v-if="isCheck" :class="statusDicClass[row.status]">{{ row.status === 1 ? '启用' : '停用' }}</span>
                <el-switch
                  v-if="!isCheck"
                  v-model="row.status"
                  class="inner-switch"
                  :active-value="1"
                  :inactive-value="0"
                  :active-text="row.status === 1 ? '启用' : '停用'"
                />
              </template>
            </el-table-column>
            <el-table-column v-if="!isCheck && isShowBtn" label="操作" width="70px" prop="caozuo">
              <template #default="{ row, $index }">
                <span v-if="!row.id" class="blue-color" @click="deleteRow(row, $index)">删除</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div v-if="!isCheck" class="drawerButton">
        <el-button size="small" @click="handleClose" @keyup.prevent @keydown.enter.prevent>取消</el-button>
        <el-button
          type="primary"
          size="small"
          :loading="saveLoading"
          @click="onSubmit"
          @keyup.prevent
          @keydown.enter.prevent
          >保存</el-button
        >
      </div>
      <div v-if="isCheck && getPermissionBtn('editDictionary')" class="drawerButton">
        <el-button size="small" @click="handleClose" @keyup.prevent @keydown.enter.prevent>取消</el-button>
        <el-button
          type="primary"
          icon="el-icon-edit"
          size="small"
          @click="handleEdit"
          @keyup.prevent
          @keydown.enter.prevent
          >编辑</el-button
        >
      </div>
    </DrawerLayout>
  </el-drawer>
</template>

<script>
import { ref, watch, reactive, getCurrentInstance, toRefs, nextTick } from 'vue';
import { getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import store from '@/store';
import { getDictionaryDetail, saveEditDelete } from '@/api/dictionary';
import Sortable from 'sortablejs';
import DrawerLayout from '@/components/DrawerLayout';
import { isInteger } from '@/utils/validate';
import { cloneDeep } from 'lodash';
// import { method } from 'lodash'

export default {
  name: 'Dictionarydrawer',
  components: { DrawerLayout },
  props: {
    type: {
      type: String,
      required: true
    },
    dictionaryType: {
      type: String,
      required: true
    },
    drawer: {
      type: Boolean,
      default: false
    },
    rowDetail: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // 抽屉事件
    const state = reactive({
      titles: {
        add: '新增字典',
        edit: '字典编辑',
        check: '字典详情'
      },
      dictionaryTypeJson: {
        1: '业务字典',
        2: '系统字典',
        3: '模板字典'
      },
      dictionaryName: ref(),
      statusDicClass: {
        0: 'icon-tes-info',
        1: 'icon-tes-success'
      },
      rowDetail: {}, // 选中的行数据
      tableKey: ref(0),
      oldTableData: [], // 原始的选项数据
      oldFormData: {}, // 原始的字典数据
      showDrawer: false,
      drawerTitles: '',
      isCheck: false, // 是否是查看页面
      type: '', // add 新增，edit 编辑，check 查看
      isShowBtn: false, // 是否显示操作列，true显示，false不显示
      isModified: false, // 是否编辑过
      userList: store.state.common.nameList,
      tableLoading: false,
      tableData: [],
      allTableData: [],
      formRef: ref(),
      formData: {},
      saveLoading: false,
      keyword: ''
    });
    // 关闭抽屉
    const handleClose = () => {
      if (isModify()) {
        proxy
          .$confirm('当前页面数据已更新，是否确认离开？', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false
          })
          .then(() => {
            if (state.type === 'edit' && props.type === 'check') {
              state.isCheck = true;
              state.type = 'check';
              state.drawerTitles = state.titles[state.type];
              state.formData = JSON.parse(JSON.stringify(state.oldFormData));
              state.tableData = JSON.parse(JSON.stringify(state.oldTableData));
              state.allTableData = cloneDeep(state.tableData);
              return false;
            } else {
              context.emit('close', { drawer: false, isResh: false });
            }
          })
          .catch(() => {});
      } else {
        // 如果没有修改过
        if (state.type === 'edit' && props.type === 'check') {
          state.isCheck = true;
          state.type = 'check';
          state.drawerTitles = state.titles[state.type];
          state.formData = JSON.parse(JSON.stringify(state.oldFormData));
          state.tableData = JSON.parse(JSON.stringify(state.oldTableData));
          state.allTableData = cloneDeep(state.tableData);
          return false;
        } else {
          context.emit('close', { drawer: false, isResh: false });
        }
      }
    };
    const isModify = () => {
      if (
        proxy.isChangeDatas(state.tableData, state.oldTableData) ||
        proxy.isChangeForm(state.formData, state.oldFormData)
      ) {
        return true;
      } else {
        return false;
      }
    };
    const handleOpened = () => {
      if (state.dictionaryName) {
        state.dictionaryName.focus();
      }
    };
    watch(props, newValue => {
      state.showDrawer = newValue.drawer;
      if (state.showDrawer) {
        state.type = props.type;
        state.drawerTitles = state.titles[state.type];
        state.isShowBtn = false;
        if (state.type === 'check') {
          // 查看页面
          state.isCheck = true;
          state.rowDetail = props.rowDetail;
          getDetail();
        } else {
          state.isCheck = false;
          state.tableKey = ref(0);
          if (state.type === 'edit') {
            // 编辑页面
            state.rowDetail = props.rowDetail;
            getDetail();
          } else {
            // 新增页面
            state.formData = {
              name: '',
              code: '',
              description: '',
              dictionaryType: props.dictionaryType
            };
            state.tableData = [];
            state.allTableData = cloneDeep(state.tableData);
            state.oldTableData = [];
            state.oldFormData = {
              name: '',
              code: '',
              description: '',
              dictionaryType: props.dictionaryType
            };
          }
          nextTick(() => {
            rowDrop();
          });
        }
      }
    });
    const getDetail = () => {
      getDictionaryDetail(state.rowDetail.id).then(res => {
        if (res) {
          state.formData = JSON.parse(JSON.stringify(res.data.data.dictionary));
          state.tableData = JSON.parse(JSON.stringify(res.data.data.dictionaryoption));
          state.allTableData = cloneDeep(state.tableData);
          state.oldTableData = JSON.parse(JSON.stringify(res.data.data.dictionaryoption));
          state.oldFormData = JSON.parse(JSON.stringify(res.data.data.dictionary));
          state.isModified = false;
        }
      });
    };
    const handleModify = value => {
      state.isModified = true;
    };
    const handleEdit = () => {
      state.type = 'edit';
      state.isCheck = false;
      state.drawerTitles = state.titles[state.type];
      state.isShowBtn = false;
    };
    // 行拖拽
    const rowDrop = () => {
      // 获取当前表格
      const el = document.getElementById('sortableList').querySelector('.el-table__body-wrapper tbody');
      Sortable.create(el, {
        animation: 300,
        handle: '.tes-move',
        draggable: '.el-table__row',
        ghostClass: 'ghost',
        // 拖动对象移动样式
        dragClass: 'drag',
        forceFallback: true,
        onEnd: function (evt) {
          if (evt.oldIndex !== evt.newIndex) {
            const currRow = state.tableData.splice(evt.oldIndex, 1)[0];
            state.tableData.splice(evt.newIndex, 0, currRow);
            state.tableData.forEach((value, index) => {
              value.order = index;
            });
            state.allTableData = cloneDeep(state.tableData);
            state.tableKey += 1;
            nextTick(() => {
              rowDrop();
            });
          }
        }
      });
    };
    const deleteRow = (row, index) => {
      state.tableData.splice(index, 1);
      state.allTableData = cloneDeep(state.tableData);
      state.isShowBtn = state.tableData.some(item => {
        return !item.id;
      });
      state.isModified = true;
    };
    const handleTableRow = () => {
      state.tableData.push({
        status: 1,
        order: Number(state.tableData.length) + 1,
        code: '000' + (Number(state.tableData.length) + 1)
      });
      state.allTableData = cloneDeep(state.tableData);
      state.isShowBtn = true;
      state.isModified = true;
      nextTick(() => {
        proxy.$refs['nameRef' + (Number(state.tableData.length) - 1)]?.focus();
      });
    };
    // 保存
    const onSubmit = () => {
      proxy.$refs['formRef'].validate(valid => {
        if (valid) {
          if (
            state.tableData.some(item => {
              return !item.name;
            })
          ) {
            proxy.$message.error('选项名称不能为空');
          } else if (
            state.tableData.some(item => {
              return !item.code;
            })
          ) {
            proxy.$message.error('选项编号不能为空');
          } else {
            if (state.tableData.length > 0) {
              var codeArray = state.tableData.map(item => {
                return item.code;
              });
              var nameArray = state.tableData.map(item => {
                return item.name;
              });
              if (
                new Set(codeArray).size !== state.tableData.length ||
                new Set(nameArray).size !== state.tableData.length
              ) {
                proxy.$message.error('选项名称或编号不能重复');
                return false;
              }
            }
            var params = {
              ...state.formData,
              dictionaryoption: state.tableData
            };
            state.saveLoading = true;
            saveEditDelete(params).then(res => {
              if (res) {
                proxy.$message.success('保存成功');
                state.isModified = false;
                context.emit('close', { drawer: false, isResh: true });
              }
              state.saveLoading = false;
            });
          }
        } else {
          return false;
        }
      });
    };
    const onChangeKeywordFilter = () => {
      const value = state.keyword?.toLowerCase();
      state.tableData = state.allTableData.filter(
        x => x.name?.toLowerCase().includes(value) || x.code?.toLowerCase().includes(value)
      );
    };
    return {
      ...toRefs(state),
      getDetail,
      isModify,
      rowDrop,
      isInteger,
      handleEdit,
      handleTableRow,
      getPermissionBtn,
      deleteRow,
      drageHeader,
      handleModify,
      onSubmit,
      handleClose,
      handleOpened,
      onChangeKeywordFilter
    };
  },
  methods: {
    isChangeDatas(newList, oldList) {
      if (newList.length !== oldList.length) {
        return true;
      } else if (newList.length > 0) {
        return newList.some((list, index) => {
          return (
            list.name !== oldList[index].name ||
            list.description !== oldList[index].description ||
            list.id !== oldList[index].id ||
            list.code !== oldList[index].code ||
            list.status !== oldList[index].status
          );
        });
      } else {
        return false;
      }
    },
    isChangeForm(newForm, oldForm) {
      var newProps = Object.getOwnPropertyNames(newForm);
      var oldProps = Object.getOwnPropertyNames(oldForm);
      // 判断属性名的length是否一致
      if (newProps.length !== oldProps.length) {
        return true;
      }
      // 循环取出属性名，再判断属性值是否一致
      for (var i = 0; i < newProps.length; i++) {
        var propName = newProps[i];
        if (newForm[propName] !== oldForm[propName]) {
          return true;
        }
      }
      return false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dictionaryDrawer {
  text-align: right;
}
.dictionary-table {
  margin-top: 10px;
}
.drawerButton {
  position: fixed;
  bottom: 20px;
  right: 20px;
}
:deep(.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 35.5rem) !important;
    overflow-x: hidden !important;
    overflow-y: auto;
  }
}
</style>
