import { openDB } from 'idb';
import { formatDateTime } from '@/utils/formatTime';

export async function storeToDB(dbName, storeName, data, dateTime = formatDateTime(new Date())) {
  const db = await openDB(dbName, 1, {
    upgrade(db) {
      // Create a store of objects
      const store = db.createObjectStore(storeName, {
        // The 'id' property of the object will be the key.
        keyPath: 'id',
        // If it isn't explicitly set, create a value by auto incrementing.
        autoIncrement: true
      });
      // Create an index on the 'date' property of the objects.
      store.createIndex('date', 'date');
    }
  });

  // Add an article:
  await db.add(storeName, {
    date: dateTime,
    body: data
  });
}

// Add multiple articles in one transaction:
// {
//   const tx = db.transaction('articles', 'readwrite')
//   await Promise.all([
//     tx.store.add({
//       title: 'Article 2',
//       date: new Date('2019-01-01'),
//       body: '…'
//     }),
//     tx.store.add({
//       title: 'Article 3',
//       date: new Date('2019-01-02'),
//       body: '…'
//     }),
//     tx.done
//   ])
// }

// Get all the articles in date order:
// console.log(await db.getAllFromIndex(storeName, 'date'))

// Add 'And, happy new year!' to all articles on 2019-01-01:
// {
//   const tx = db.transaction('articles', 'readwrite')
//   const index = tx.store.index('date')

//   for await (const cursor of index.iterate(new Date('2019-01-01'))) {
//     const article = { ...cursor.value }
//     article.body += ' And, happy new year!'
//     cursor.update(article)
//   }

//   await tx.done
// }

export async function getListByDate(dbName, storeName, startDate) {
  const db = await openDB(dbName, 1);
  const keyRange = IDBKeyRange.lowerBound(startDate);
  return db.getAllFromIndex(storeName, 'date', keyRange);
}

// 删除7天前的数据
export async function deleteListByDate(dbName, storeName) {
  const db = await openDB(dbName, 1);
  const endDateTime = new Date();
  endDateTime.setTime(endDateTime.getTime() - 3600 * 1000 * 24 * 7);
  const keyRange = IDBKeyRange.upperBound(formatDateTime(endDateTime));
  db.getAllFromIndex(storeName, 'date', keyRange).then(resp => {
    if (resp && resp.length > 0) {
      db.delete(storeName, IDBKeyRange.upperBound(resp[resp.length - 1].id));
    }
  });
}
