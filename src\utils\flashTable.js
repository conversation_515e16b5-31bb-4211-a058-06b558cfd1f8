import axios from 'axios';
import { getLIMSConfig } from './auth';

export async function getFlashTableToken() {
  const formData = new FormData();
  formData.append('username', 'cxist');
  formData.append('password', 'cxist');
  const flashTableUrl =
    window.location.hostname == 'localhost'
      ? '/FlashTable/api/login'
      : `${getLIMSConfig().VITE_FLASH_TABLE}/FlashTable/api/login`;
  const res = await axios.post(flashTableUrl, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
  if (res.status == 200 && res.data.code == 200) {
    return res.data.data;
  }
  return '';
}
