<template>
  <!-- 页面自定义视图 -->
  <el-drawer
    :model-value="modelValue"
    :title="
      isEditCustomView
        ? '编辑当前视图'
        : isNewCustomView
        ? '新增自定义视图'
        : isNewFixedView
        ? '新建默认视图'
        : isEditFixedView
        ? '编辑默认视图'
        : ''
    "
    size="400px"
    direction="rtl"
    :before-close="handleBeforeClose"
    @open="handleOpen"
    @close="handleClose"
    @opened="handleOpened"
  >
    <DrawerLayout :has-button-group="false" :has-page-header="false">
      <el-form
        ref="formRef"
        :label-width="isFixedView ? '80px' : '110px'"
        label-position="top"
        :model="viewForm"
        :rules="viewFormRules"
      >
        <el-row :gutter="40">
          <el-col :span="24">
            <el-form-item label="视图名称：" prop="viewName">
              <el-input
                ref="viewNameRef"
                v-model="viewForm.viewName"
                v-trim
                maxlength="10"
                @blur="formRef.value?.clearValidate()"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="默认展示视图：" prop="isDefault">
              <el-radio-group v-model="viewForm.isDefault">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :key="selectKey" label="列设置：">
              <el-checkbox-group v-model="tableSelectedData" class="table_column_custom_checkbox">
                <el-checkbox
                  v-for="item in tableColumnData"
                  :key="item.id"
                  :label="item.id"
                  border
                  class="check_item_drop"
                >
                  {{ item.fieldName }}
                  <i class="tes-move iconfont" style="font-size: 10px; cursor: move; position: absolute; right: 10px" />
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <BottomPanel>
        <template #panel-content>
          <div style="overflow: hidden">
            <div style="float: right">
              <el-button size="small" @click="resetView" @keyup.prevent @keydown.enter.prevent> 重置 </el-button>
              <el-button
                type="primary"
                size="small"
                :loading="saving"
                @click="saveView"
                @keyup.prevent
                @keydown.enter.prevent
              >
                保存
              </el-button>
            </div>
          </div>
        </template>
      </BottomPanel>
    </DrawerLayout>
  </el-drawer>
</template>

<script>
import { ref, reactive, toRefs, computed, watch, onMounted, nextTick, getCurrentInstance } from 'vue';
import { useStore } from 'vuex';
import { ElMessage } from 'element-plus';
import { getViewByViewId, saveOrUpdateView } from '@/api/tableView';
import { getMenuList } from '@/utils/auth';
import { viewFormRules } from '@/utils/func/customTable';
import { tenantTypeOptions } from '@/data/industryTerm';
import Sortable from 'sortablejs';
import { cloneDeep, isEqual } from 'lodash';
import { filterTableColumnDataByTenantType } from './tools';
import { fieldTypesEnum, columnFixedTypesEnum } from './enum';
import DrawerLayout from '@/components/DrawerLayout';
import BottomPanel from '@/components/PageComponents/BottomPanel';

export default {
  name: 'TableColumnCustom',
  components: { DrawerLayout, BottomPanel },
  props: {
    type: {
      type: String,
      default: 'add',
      validator(value) {
        return ['add', 'edit', 'addFixed'].includes(value);
      }
    },
    modelValue: {
      type: Boolean,
      default: false
    },
    userId: {
      type: String,
      default: ''
    },
    bindingMenu: {
      type: String,
      default: ''
    },
    view: {
      type: Object,
      default: () => {
        return {
          id: '',
          viewName: '',
          isDefault: 0,
          tableColumnData: []
        };
      }
    }
  },
  emits: ['update:model-value', 'update:view'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const store = useStore();
    const state = reactive({
      isEditViewName: false,
      viewNameRef: ref(),
      menuKeys: [],
      viewForm: {
        bindingMenuName: '',
        bindingMenu: '',
        viewName: '',
        isFixedView: '',
        isDefault: ''
      },
      tableLoading: false,
      tableColumnData: [],
      tableSelectedData: [],
      selectKey: 0,
      isModified: false,
      originViewForm: {},
      originTableColumnData: [],
      originTableSelectedData: [],
      saving: false,
      isCheckDataChanged: true,
      menuList: []
    });

    const formRef = ref(null);
    const tableRef = ref(null);

    const isNewFixedView = computed(() => props.type === 'addFixed');
    const isEditFixedView = computed(() => props.type === 'edit' && props.view?.isFixedView?.toString() === '1');
    const isFixedView = computed(() => isNewFixedView.value || isEditFixedView.value);
    const isNewCustomView = computed(() => props.type === 'add');
    const isEditCustomView = computed(() => props.type === 'edit' && props.view?.isFixedView?.toString() === '0');
    const isCustomView = computed(() => isNewCustomView.value || isEditCustomView.value);
    const tenantInfo = computed(() => store.getters.tenantInfo);

    watch(
      () => state.selectKey,
      (newValue, oldValue) => {
        nextTick(() => {
          rowDrop();
          toggleSelection(state.tableColumnData);
        });
      }
    );

    const fieldTypes = [
      { type: fieldTypesEnum.Text, name: '文本' },
      { type: fieldTypesEnum.Date, name: '日期' },
      { type: fieldTypesEnum.Link, name: '链接' },
      { type: fieldTypesEnum.Status, name: '状态' },
      { type: fieldTypesEnum.Person, name: '人员' },
      { type: fieldTypesEnum.Progress, name: '进度' },
      { type: fieldTypesEnum.Custom, name: '自定义' }
    ];

    const columnFixedTypes = [
      { code: columnFixedTypesEnum.None, name: '不启用' },
      { code: columnFixedTypesEnum.Left, name: '左固定' },
      { code: columnFixedTypesEnum.Right, name: '右固定' }
    ];

    const fieldTenantTypes = [...tenantTypeOptions];

    onMounted(() => {
      const menuList = getMenuList();
      const newMenuList = [];
      menuList.forEach(menuItem => {
        if (menuItem.children.length > 0 && !/^(BusinessManage|userManage|PlatformManage)$/.test(menuItem.key)) {
          newMenuList.push({
            title: menuItem.title,
            key: menuItem.key,
            children: menuItem.children
              .filter(item => item.visible)
              .map(item => ({
                title: item.title,
                key: item.key
              }))
          });
        }
      });
      state.menuList = newMenuList;
    });

    // 查询视图详情
    const getViewDetail = async () => {
      state.tableLoading = true;
      const res = await getViewByViewId(props.view?.id);
      state.tableLoading = false;
      if (res && res.data.code === 200) {
        setTableColumnData(res.data.data.sysEmployeeListConfigList);
        state.selectKey += 1;
      }
    };

    // 初始化视图数据
    const initView = () => {
      state.isEditViewName = false;
      state.menuKeys = [];
      state.viewForm.bindingMenu = props.bindingMenu || '';
      const bindingMenuItems = getBindingMenuItems([null, props.bindingMenu]);
      if (bindingMenuItems.length > 1) {
        state.menuKeys = [bindingMenuItems[0].key, bindingMenuItems[1].key];
        state.viewForm.bindingMenuName = bindingMenuItems[1].title;
      }
      state.viewForm.viewName = isFixedView.value
        ? '固定视图'
        : isNewCustomView.value
        ? ''
        : props.view?.viewName || '';
      state.viewForm.isFixedView = isFixedView.value ? '1' : props.view?.isFixedView?.toString();
      state.viewForm.isDefault = props.view?.isDefault?.toString() || '0';
      state.originViewForm = cloneDeep(state.viewForm);
      state.isCheckDataChanged = true;
      if (!props.view?.tableColumnData?.length > 0 && props.view?.id) {
        getViewDetail();
      } else {
        setTableColumnData(props.view?.tableColumnData && cloneDeep(props.view.tableColumnData));
      }
      nextTick(() => {
        formRef.value?.clearValidate();
      });
    };

    // 查找所属页面的路径数据
    const getBindingMenuItems = ([parentKey, childKey]) => {
      if (childKey) {
        for (let index = 0; index < state.menuList.length; index++) {
          const menuItem = state.menuList[index];
          if (parentKey && menuItem.key !== parentKey) {
            continue;
          }
          const childItem = menuItem.children.find(child => child.key === childKey);
          if (childItem) {
            return [menuItem, childItem];
          }
        }
      }
      return [];
    };

    // 设置 tableColumnData
    const setTableColumnData = tableColumnData => {
      if (tableColumnData?.length > 0) {
        if (isCustomView.value) {
          state.tableColumnData = filterTableColumnDataByTenantType(tableColumnData, tenantInfo.value.type);
        } else {
          state.tableColumnData = tableColumnData;
        }
        state.tableColumnData = state.tableColumnData.map(item => ({
          ...item,
          isMinWidth: Boolean(item.isMinWidth),
          isQuery: Boolean(item.isQuery),
          isSortable: Boolean(item.isSortable)
        }));
        state.tableSelectedData = state.tableColumnData
          .filter(row => Boolean(row.isShow))
          .map(item => {
            return item.id;
          });
        state.originTableColumnData = cloneDeep(state.tableColumnData);
        state.originTableSelectedData = cloneDeep(state.tableSelectedData);
      } else {
        state.tableColumnData = [];
        state.tableSelectedData = [];
        state.originTableColumnData = [];
        state.originTableSelectedData = [];
      }
    };

    // 每当 Drawer 打开并重新赋值
    const handleOpen = () => {
      initView();
      state.selectKey += 1;
    };
    const handleOpened = () => {
      if (state.viewNameRef) {
        state.viewNameRef.focus();
      }
    };

    // Drawer 关闭之前
    const handleBeforeClose = done => {
      if (state.isCheckDataChanged && isModified()) {
        proxy
          .$confirm('当前页面数据已更新，是否确认离开？', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false
          })
          .then(done.bind(null, false))
          .catch(done.bind(null, true));
      } else {
        formRef.value?.clearValidate();
        done();
      }
    };

    // Drawer 关闭
    const handleClose = () => {
      if (props.modelValue) {
        setTableColumnData([]);
        context.emit('update:model-value', false);
      }
    };

    // 处理 Cascader 事件
    const handleMenuKeysChange = values => {
      if (values instanceof Array && values.length > 1) {
        const bindingMenuItems = getBindingMenuItems(values);
        if (bindingMenuItems.length > 1) {
          state.viewForm.bindingMenu = bindingMenuItems[1].key;
          state.viewForm.bindingMenuName = bindingMenuItems[1].title;
        }
      }
    };

    // 是否修改了数据
    const isModified = () => {
      return (
        !isEqual(state.viewForm, state.originViewForm) ||
        !isEqual(state.tableColumnData, state.originTableColumnData) ||
        !isEqual(state.tableSelectedData, state.originTableSelectedData)
      );
    };

    // 确认视图名称
    const confirmViewName = () => {
      formRef.value.validateField(['viewName'], error => {
        if (!error) {
          state.isEditViewName = false;
        }
      });
    };

    // 删除字段
    const removeField = index => {
      state.tableColumnData.splice(index, 1);
    };

    // 设置 table checkbox 状态
    const toggleSelection = rows => {
      if (rows) {
        rows.forEach(row => {
          tableRef.value?.toggleRowSelection(row, Boolean(row.isShow));
        });
      } else {
        tableRef.value?.clearSelection();
      }
    };

    // 重置视图值
    const resetView = () => {
      initView();
      if (state.selectKey !== 1) {
        state.selectKey = 1;
        nextTick(() => {
          toggleSelection(state.tableColumnData);
        });
      }
    };

    // 保存视图
    const saveView = async () => {
      await formRef.value.validate();
      if (state.tableSelectedData.length === 0) {
        ElMessage.warning(`至少勾选一项！`);
        return false;
      }
      const tableColumnData = state.tableColumnData.map(item => {
        const newItem = { ...item };
        const isChecked = state.tableSelectedData.some(selectedItem => newItem.id === selectedItem);
        newItem.isShow = isChecked ? 1 : 0;
        newItem.isMinWidth = newItem.isMinWidth ? 1 : 0;
        newItem.isQuery = newItem.isQuery ? 1 : 0;
        newItem.isSortable = newItem.isSortable ? 1 : 0;
        if (isNewCustomView.value) {
          newItem.id = '';
          newItem.viewId = '';
        }
        if (newItem.isNewRow) {
          delete newItem.isNewRow;
        }
        return newItem;
      });
      const saveParams = {
        userId: props.userId,
        bindingMenuName: state.viewForm.bindingMenuName,
        bindingMenu: state.viewForm.bindingMenu,
        id: isNewFixedView.value || isNewCustomView.value ? '' : props.view.id,
        viewName: state.viewForm.viewName,
        isFixedView: isFixedView.value ? '1' : '0',
        isDefault: isFixedView.value ? '0' : state.viewForm.isDefault,
        sysEmployeeListConfigList: tableColumnData
      };
      state.saving = true;
      const res = await saveOrUpdateView(saveParams);
      state.saving = false;
      if (res && res.data.code === 200) {
        ElMessage.success(`${saveParams.viewName}-保存成功!`);
        const updatedViewData = res.data.data;
        context.emit('update:view', {
          id: updatedViewData.id,
          viewName: updatedViewData.viewName,
          isFixedView: updatedViewData.isFixedView,
          isDefault: Number(updatedViewData.isDefault),
          sysEmployeeListConfigList: updatedViewData.sysEmployeeListConfigList
        });
        state.isCheckDataChanged = false;
        handleClose();
      }
    };

    // 行拖拽
    const rowDrop = () => {
      // 获取当前表格
      const tbody = document.querySelector('.table_column_custom_checkbox');
      Sortable.create(tbody, {
        animation: 150,
        handle: '.tes-move',
        draggable: '.check_item_drop',
        ghostClass: 'ghost',
        // 拖动对象移动样式
        dragClass: 'drag',
        forceFallback: true,
        onEnd(evt) {
          if (evt.oldIndex !== evt.newIndex) {
            // 移除原来的数据
            const currRow = state.tableColumnData.splice(evt.oldIndex, 1)[0];
            // 移除原来的数据并插入新的数据
            state.tableColumnData.splice(evt.newIndex, 0, currRow);
            state.tableColumnData.forEach((item, index) => {
              // const isChecked = state.tableSelectedData.some(selectedItem => item.id === selectedItem.id)
              // item.isShow = isChecked ? 1 : 0
              item.order = index;
            });
            state.selectKey += 1;
            rowDrop();
          }
        }
      });
    };

    return {
      ...toRefs(state),
      formRef,
      tableRef,
      isNewFixedView,
      isEditFixedView,
      isFixedView,
      isNewCustomView,
      isEditCustomView,
      isCustomView,
      viewFormRules,
      fieldTypes,
      columnFixedTypes,
      fieldTenantTypes,
      handleOpen,
      handleOpened,
      handleBeforeClose,
      handleClose,
      handleMenuKeysChange,
      confirmViewName,
      removeField,
      resetView,
      saveView,
      rowDrop
    };
  }
};
</script>

<style lang="scss">
.check_item_drop {
  .tes-move {
    display: none;
  }
  &:hover .tes-move {
    display: inline-block;
  }
}
.table_column_custom_checkbox {
  .el-checkbox {
    width: 100%;
    margin: 0 0 10px 0;
  }
  .el-checkbox.is-bordered + .el-checkbox.is-bordered {
    margin: 0 0 10px 0;
  }
}
.table-column-editer__list.el-table .el-table__body-wrapper {
  max-height: calc(100vh - 421px);
  overflow-y: auto;
}

.table-column-editer__list .el-form-item {
  margin-bottom: 0 !important;
}

.table-column-editer__list .el-table-column--selection > .cell {
  padding-left: 5px;
  padding-right: 5px;
}
</style>
