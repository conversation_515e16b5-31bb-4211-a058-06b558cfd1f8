<template>
  <el-dialog
    :model-value="showDialog"
    custom-class="batch-approval"
    title="批量审批"
    width="400px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :before-close="close"
  >
    <el-form ref="batchApprovalRef" v-loading="loading" :model="formData" label-position="top">
      <el-form-item label="结论：" prop="isAssent">
        <el-radio-group v-model="formData.isAssent" class="radioGroup" @change="changeAssent">
          <el-radio :label="1" border class="pass">同意</el-radio>
          <el-radio :label="0" border class="sendBack fr">拒绝</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="examineStatus === 5 && disableOthers" label="发送日期：" prop="sendDate">
        <el-date-picker
          v-model="formData.sendDate"
          type="date"
          clearable
          placeholder="选择日期"
          style="width: 100%"
          @change="changeSendTime(formData.sendDate)"
        />
      </el-form-item>
      <el-form-item label="审批意见：" prop="opinion">
        <el-input v-model="formData.opinion" size="small" type="textarea" :rows="2" />
      </el-form-item>
      <el-form-item
        v-if="examineStatus !== 5 && disableOthers && lastName.includes(lableName)"
        :label="`下一步${lableName}人：`"
        prop="assignee"
      >
        <el-select
          v-model="formData.assignee"
          class="owner-select"
          placeholder="请选择"
          clearable
          filterable
          size="small"
          style="width: 100%"
        >
          <el-option v-for="item in userOptions" :key="item.id" :label="item.nickname" :value="item.id" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="loading" @click="close">取消</el-button>
        <el-button :loading="loading" type="primary" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, getCurrentInstance } from 'vue';
import { ElMessage } from 'element-plus';
import { processExecute } from '@/api/testReport';
// import { getCurrentReportInfo } from '@/utils/auth'
import { getLoginInfo } from '@/utils/auth';
import { formatDate } from '@/utils/formatTime';
import { addByTemp } from '@/api/messageAgent';
import { getNameByid } from '@/utils/common';
// import _ from 'lodash'
import { getReportUserList } from '@/api/user';

export default {
  name: 'BatchApproval',
  components: {},
  props: {
    show: {
      type: Boolean,
      default: false
    },
    approvalList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    const { appContext } = getCurrentInstance();
    const bus = appContext.config.globalProperties.bus;
    // const currentInfo = getCurrentReportInfo()
    // console.log(currentInfo)
    const datas = reactive({
      showDialog: props.show,
      examineStatus: null,
      lableName: '签字',
      lastName: '',
      loading: false,
      currentAccountId: getLoginInfo().accountId,
      formData: {
        assignee: '',
        businessKey: '',
        isAssent: 1,
        opinion: '',
        processInstanceId: '',
        sendDate: '',
        startUser: ''
      },
      disableOthers: true,
      detailDataList: [],
      userOptions: [],
      sampleIds: [],
      secSampleNums: [],
      reportNos: [],
      rejectStepNames: [],
      reportFormationByUserId: ''
    });

    // 获取具有检测报告权限的人员列表
    getReportUserList({}).then(res => {
      if (res.data.code === 200) {
        datas.userOptions = res.data.data;
      }
    });

    watch(
      () => props.show,
      async newValue => {
        // console.log(newValue)
        if (newValue) {
          datas.showDialog = newValue;
          if (props.approvalList.length > 0) {
            datas.lastName = props.approvalList.toString();
          }
          datas.detailDataList = props.data;
          if (datas.detailDataList.length > 0) {
            var businessKeyList = [];
            var processInstanceIdList = [];
            datas.sampleIds = [];
            datas.secSampleNums = [];
            datas.reportNos = [];
            datas.detailDataList.forEach(dl => {
              datas.examineStatus = dl.examineStatus;
              switch (dl.examineStatus) {
                case 2:
                  datas.lableName = '签字';
                  break;
                case 3:
                  datas.lableName = '盖章';
                  break;
                case 4:
                  datas.lableName = '发送';
                  break;
              }
              businessKeyList.push(dl.id);
              processInstanceIdList.push(dl.processInstanceId);
              datas.sampleIds.push(dl.sampleId);
              datas.secSampleNums.push(dl.secSampleNum);
              datas.reportNos.push(dl.reportNo);
              datas.reportFormationByUserId = dl.reportFormationByUserId;
            });
            datas.formData.businessKey = businessKeyList.join(',');
            datas.formData.processInstanceId = processInstanceIdList.join(',');
            datas.formData.assignee = datas.currentAccountId;
            datas.formData.opinion = '';
            datas.formData.sendDate = '';
            datas.formData.startUser = datas.detailDataList[0].reportFormationByUserId;
          }
        }
        // console.log(datas.detailDataList)
      },
      { deep: true }
    );

    // 确定选择
    const dialogSuccess = async () => {
      datas.loading = true;
      processExecute(datas.formData).then(res => {
        datas.loading = false;
        if (res !== false) {
          if (datas.formData.isAssent === 1) {
            switch (datas.examineStatus) {
              case 2:
                addMsg('M013');
                break;
              case 3:
                addMsg('M014');
                break;
              case 4:
                addMsg('M015');
                break;
              case 5:
                addMsg('M016');
                break;
            }
          } else {
            datas.rejectStepNames = [];
            switch (datas.examineStatus) {
              case 2:
                datas.rejectStepNames.push('审核');
                break;
              case 3:
                datas.rejectStepNames.push('签字');
                break;
              case 4:
                datas.rejectStepNames.push('盖章');
                break;
              case 5:
                datas.rejectStepNames.push('发送');
                break;
            }
            addMsg('M011');
          }
          ElMessage.success('审批成功！');
          datas.showDialog = false;
          bus.$emit('reloadReportList', true);
          context.emit('close', false);
        }
      });
    };
    // 添加消息待办
    const addMsg = eventCode => {
      // 添加消息待办
      const params = {
        eventCode: eventCode,
        receiverType: '1',
        senderName: getNameByid(datas.currentAccountId),
        receiverIds: datas.formData.assignee,
        receiverNames: getNameByid(datas.formData.assignee),
        todoStartTime: formatDate(new Date()),
        c_ids: datas.reportNos.join(','),
        c_b_samplesIdArray: datas.sampleIds.join(','),
        c_b_sampleNoArray: datas.secSampleNums.join(','),
        c_b_reportNoArray: datas.reportNos.join(','),
        c_b_rejectStepNameArray: datas.rejectStepNames.join(',')
      };
      if (eventCode === 'M011') {
        params.receiverIds = datas.formData.startUser;
        params.receiverNames = getNameByid(datas.formData.startUser);
      }
      if (eventCode === 'M016') {
        // params.eventCode = 'M017'
        // params.receiverIds = datas.formData.assignee
        // params.receiverNames = getNameByid(datas.formData.assignee)
        // addByTemp(params).then(res => {
        //   if (res !== false) {
        //     console.log(res.data)
        //     datas.rejectStepNames = []
        //   }
        // })
        params.eventCode = 'M016';
        params.receiverIds = datas.reportFormationByUserId;
        params.receiverNames = getNameByid(datas.reportFormationByUserId);
        addByTemp(params).then(res => {
          if (res !== false) {
            console.log(res.data);
            datas.rejectStepNames = [];
          }
        });
      } else {
        addByTemp(params).then(res => {
          if (res !== false) {
            console.log(res.data);
            datas.rejectStepNames = [];
          }
        });
      }
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };
    // 选择发送时间
    const changeSendTime = date => {
      // console.log(formatDate(date))
      datas.formData.sendDate = formatDate(date);
    };
    // 切换到radio
    const changeAssent = value => {
      if (value === 0) {
        datas.disableOthers = false;
      } else {
        datas.disableOthers = true;
      }
    };

    return { ...toRefs(datas), close, dialogSuccess, changeSendTime, addMsg, changeAssent };
  },
  created() {
    // this.getStandarInfoList()
  },
  methods: {}
};
</script>
<style lang="scss" scoped>
.batch-approval {
  .search {
    width: 360px;
  }
  .add-report-search {
    margin-bottom: 15px;
  }
  .fr {
    float: right;
  }

  .el-radio {
    margin-right: 0;
    background: #f4f4f5;
  }
  .sendBack.is-checked {
    background: $tes-red;
  }
  .pass.is-checked {
    background: $green;
  }
  .sendBack {
    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #fff;
    }
  }
  .pass {
    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #fff;
    }
  }
  .el-radio.is-bordered {
    border: 0;
    border-radius: 4px;
    width: 49%;
    text-align: center;
  }
  .radioGroup {
    width: 100%;
    display: flex;
    align-items: center;
    :deep(.el-radio__input) {
      display: none;
    }
  }
}
</style>
