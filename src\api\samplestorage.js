import request from '@/utils/request';

// 检测分配查询列表
export function warehousingList(data) {
  return request({
    url: '/api-orders/orders/warehousing/warehousingList',
    method: 'post',
    data
  });
}
// // 入库
export function addWarehousing(data) {
  return request({
    url: '/api-orders/orders/warehousing/addWarehousing',
    method: 'post',
    data
  });
}
// // 批量入库
export function addWarehousings(data) {
  return request({
    url: '/api-orders/orders/warehousing/batchWarehousing',
    method: 'post',
    data
  });
}

// 文件管理-上传/更新
export function uploadStorageFile(data, callback) {
  return request({
    url: '/api-orders/orders/sampletemplatefile/upload',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: function (progressEvent) {
      // 原生获取上传进度的事件
      console.log(progressEvent);
      // callback(progressEvent)
    },
    data
  });
}

// // 处理
export function handleSampleInventory(data) {
  return request({
    url: '/api-orders/orders/sampleprocessinginfo/save',
    method: 'post',
    data
  });
}

// 修改入库信息
export function updateSampleInventory(data) {
  return request({
    url: '/api-orders/orders/warehousinginfo/update',
    method: 'post',
    data
  });
}

// 样品库存查看接口
export function getWarehousinginfo(id, sid) {
  return request({
    url: '/api-orders/orders/warehousinginfo/info/' + sid + '/' + id,
    method: 'get'
  });
}
// 查询打印机列表
export function getPringList() {
  return request({
    url: `/api-orders/orders/samples/print/printers`,
    method: 'get'
  });
}
// 打印
export function printCode(data) {
  return request({
    url: `/api-orders/orders/samples/print/info`,
    method: 'post',
    data
  });
}
// 样品回库
export function warehousingReturn(data) {
  return request({
    url: `/api-orders/orders/warehousing/return`,
    method: 'post',
    data
  });
}
// 样品借出
export function warehousingLoadn(data) {
  return request({
    url: `/api-orders/orders/warehousing/loan`,
    method: 'post',
    data
  });
}
// 查看
export function warehousinginfo(data) {
  return request({
    url: `/api-orders/orders/warehousinginfo/info`,
    method: 'post',
    data
  });
}
