// import { nextTick } from 'vue'
import $ from 'jquery';
import { ElMessage } from 'element-plus';

// 获取input输入框

function getInput(el) {
  let inputEle;
  if (el.tagName !== 'INPUT') {
    inputEle = el.querySelector('input');
  } else {
    inputEle = el;
  }
  return inputEle;
}
function dispatchEvent(el, type) {
  const evt = document.createEvent('HTMLEvents');
  evt.initEvent(type, true, true);
  el.dispatchEvent(evt);
}
const focus = app => {
  app.directive('focus', {
    mounted(el) {
      const children = $(el).find('input')[0];
      $(children).focus();
    }
  });
};

const trim = app => {
  app.directive('trim', {
    mounted(el) {
      const inputEle = getInput(el);
      const handler = function (event) {
        const newVal = event.target.value.trim();
        if (event.target.value !== newVal) {
          event.target.value = newVal;
          dispatchEvent(inputEle, 'input');
        }
      };
      // 回车后过滤空格
      const keydown = function (event) {
        if (event.keyCode === 13) {
          const newVal = event.target.value.trim();
          if (event.target.value !== newVal) {
            event.target.value = newVal;
            dispatchEvent(inputEle, 'input');
          }
        }
      };
      el.inputEle = inputEle;
      el._blurHandler = handler;
      el._keyHandler = keydown;
      inputEle.addEventListener('blur', handler);
      inputEle.addEventListener('keydown', keydown);
    },
    unmounted(el) {
      const { inputEle } = el;
      inputEle.removeEventListener('blur', el._blurHandler);
      inputEle.removeEventListener('keydown', el._keyHandler);
    }
  });
};
const copyFunction = value => {
  if (navigator.clipboard && window.isSecureContext) {
    navigator.clipboard.writeText(value);
    ElMessage.closeAll();
    ElMessage.success('成功复制到粘贴板!');
  } else {
    // 创建text area
    const textArea = document.createElement('textarea');
    textArea.value = value;
    // 使text area不在viewport，同时设置不可见
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    return new Promise((resolve, reject) => {
      // 执行复制命令并移除文本框
      document.execCommand('copy') ? resolve() : reject(new Error('出错了'));
      textArea.remove();
    }).then(
      () => {
        ElMessage.closeAll();
        ElMessage.success('成功复制到粘贴板!');
      },
      () => {
        ElMessage.closeAll();
        ElMessage.error('复制失败');
      }
    );
  }
};
const copy = app => {
  app.directive('copy', {
    // 当被绑定的元素插入到DOM中时，自动触发mounted函数
    mounted(el, binding, vnode) {
      // 当用户在 <div> 元素 上右击鼠标时执行
      el.oncontextmenu = function (e) {
        // 阻止右键浏览器的默认操作
        return false;
      };
      // 绑定右键点击事件
      el.addEventListener('contextmenu', () => {
        copyFunction(el.innerText);
      });
    },

    onUnmounted(el, binding) {
      el.removeEventListener('contextmenu', () => copyFunction(el.innerText));
    }
  });
};

export default app => {
  focus(app);
  trim(app);
  copy(app);
};
