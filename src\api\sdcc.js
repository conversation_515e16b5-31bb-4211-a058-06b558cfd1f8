import request from '@/utils/request';
// sdcc查询列表
export function getList(data) {
  return request({
    url: '/api-diplomat/sdcc/order/list',
    method: 'post',
    data
  });
}
// sdcc成品检验列表
export function detailListApi(productionOrderId) {
  return request({
    url: `/api-diplomat/sdcc/production/detailList/${productionOrderId}`,
    method: 'get'
  });
}
// 获取详情tab
export function getTabsApi(materialCategoryCode) {
  return request({
    url: `/api-capabilitystd/capability/externalcapabilitycategory/findSdccInfo/${materialCategoryCode}`,
    method: 'get'
  });
}
// 获取报告
export function getReportDetail(data) {
  return request({
    url: `/api-orders/orders/report/findOrderReport`,
    method: 'post',
    data
  });
}
// 获取数据
export function getTestListProcess(data) {
  return request({
    url: `/api-orders/orders/report/findOrderReportDetail`,
    method: 'post',
    data
  });
}
// 成品检验 获取检测数据
export function getInspectionApi(data) {
  return request({
    url: `/api-diplomat/inspection-result/list`,
    method: 'post',
    data
  });
}
// 成品检验 新增编辑和生产数据
export function saveFinishProduction(data) {
  return request({
    url: `/api-diplomat/sdcc/order/save`,
    method: 'post',
    data
  });
}
// 成品检验 删除生产数据
export function deleteFinishedProduction(data) {
  return request({
    url: `/api-diplomat/sdcc/order-detail/?ids=${data}`,
    method: 'DELETE'
  });
}

// 保存测试数据
export function saveTestData(data) {
  return request({
    url: `/api-diplomat/inspection-result/save`,
    method: 'post',
    data
  });
}
// 删除测试数据
export function deleteTestData(data) {
  return request({
    url: `/api-diplomat/inspection-result/delete`,
    method: 'post',
    data
  });
}

// 原材料获取生产数据
export function getRawProductList({ productionOrderId: productionOrderId, materialSdccType: materialSdccType }) {
  return request({
    url: `/api-diplomat/sdcc/material/list/query/${productionOrderId}?materialSdccType=${materialSdccType}`,
    method: 'get'
  });
}

// 原材料检验 保存生产数据
export function saveRawProduction(data) {
  return request({
    url: `/api-diplomat/sdcc/material/save`,
    method: 'post',
    data
  });
}
// 原材料删除生产数据
export function deleteRawProduction(data) {
  return request({
    url: `/api-diplomat/sdcc/material?ids=${data}`,
    method: 'DELETE'
  });
}
// 上传SDCC
export function submitSDCC(data) {
  return request({
    url: `/api-diplomat/inspection-result/sdcc/submit`,
    method: 'post',
    data
  });
}
// 下载文件
export function downLoadFileApi({ fileId: fileId, fileName: fileName }) {
  return request({
    url: `/api-diplomat/inspection-result/download/${fileId}?fileName=${fileName}`,
    method: 'get',
    responseType: 'blob'
  });
}
