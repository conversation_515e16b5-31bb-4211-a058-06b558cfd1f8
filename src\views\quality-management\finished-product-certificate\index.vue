<template>
  <!-- 成品检验合格证 -->
  <div class="flex flex-col h-full">
    <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
      <el-form-item prop="param">
        <div style="width: 60vw">
          <CombinationQuery
            :field-list="searchFieldList"
            :field-tip="fieldTips"
            @get-single-text="getSingleText"
            @get-param-list="getParamList"
            @reset-search="reset"
          />
        </div>
      </el-form-item>
    </el-form>
    <div class="flex-1 overflow-auto">
      <div class="flex gap-5 flex-col px-6 py-5 h-full">
        <div class="page-model">
          <div class="flex flex-row pb-3">
            <el-radio-group v-model="formInline.status" size="small" @change="getList()">
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button v-for="(val, key) in radioData" :key="key" :label="key">{{ val }}</el-radio-button>
            </el-radio-group>
            <el-select
              v-model="formInline.deliveryInstitutionCode"
              class="owner-select ml-3"
              filterable
              placeholder="请选择送检部门"
              size="small"
              clearable
              @change="getList()"
            >
              <el-option v-for="(val, key) in dictionary['JHDW']?.enable" :key="key" :label="val" :value="key" />
            </el-select>
          </div>
          <el-table
            ref="singleTableRef"
            :key="tableKey"
            v-loading="listLoading"
            highlight-current-row
            :data="tableList"
            size="medium"
            fit
            border
            height="auto"
            class="dark-table base-table"
            :row-style="
              () => {
                return 'cursor: pointer';
              }
            "
            @header-dragend="drageHeader"
            @current-change="handleCurrentChange"
          >
            <template v-for="(item, index) in tableColumn" :key="index">
              <el-table-column
                :prop="item.fieldKey"
                :label="item.fieldName"
                :sortable="Number(item.isSortable) === 1"
                :width="item.isMinWidth ? '' : item.columnWidth"
                :min-width="item.isMinWidth ? item.columnWidth : ''"
                show-overflow-tooltip
              >
                <template #default="{ row }">
                  <template v-if="item.fieldType === fieldTypesEnum.Person">
                    <UserTag :name="getNameByid(row[item.fieldKey]) || row[item.fieldKey] || '--'" />
                  </template>
                  <template v-else-if="item.fieldType === fieldTypesEnum.Status">
                    <el-tag size="small" effect="dark" :type="status[item.fieldKey][row[item.fieldKey]].type">{{
                      status[item.fieldKey][row[item.fieldKey]].label
                    }}</el-tag>
                  </template>
                  <template v-else-if="item.fieldType === fieldTypesEnum.Date">
                    <span>{{ row[item.fieldKey] == '' ? '--' : formatDate(row[item.fieldKey]) }}</span>
                  </template>
                  <template v-else-if="item.fieldType === fieldTypesEnum.Custom">
                    <span v-if="item.fieldKey == 'materialCode'">
                      {{ materialCategoryAll[row[item.fieldKey]]?.name || row[item.fieldKey] || '--' }}
                    </span>
                    <template v-else-if="item.fieldKey == 'nuclearMarker'">
                      <span v-if="row.nuclearMarker">
                        {{ row.nuclearMarker.indexOf('H') > -1 ? '⚠H' : '' }}
                        {{ row.nuclearMarker.indexOf('空') > -1 ? '©' : '' }}
                      </span>
                      <span v-else>--</span>
                    </template>
                  </template>
                  <template v-else-if="item.fieldKey === 'quantityUnit'">
                    {{ dictionary?.['5']?.all?.[row.quantityUnit] }}
                  </template>
                  <span v-else>{{ row[item.fieldKey] || '--' }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <pagination
            v-show="total > 0"
            :page="listQuery.page"
            :limit="listQuery.limit"
            :total="total"
            @pagination="getList"
          />
        </div>
        <PrintTable
          :select="selectRow"
          :search-params="formInline"
          :certificate-templates="originCertificateTemplates"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, ref, toRefs, onMounted, onUnmounted } from 'vue';
import router from '@/router/index.js';
import Pagination from '@/components/Pagination';
// import ListLayout from '@/components/ListLayout';
import { formatDate } from '@/utils/formatTime';
import UserTag from '@/components/UserTag';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { getLoginInfo } from '@/utils/auth';
import { mapGetters } from 'vuex';
import CombinationQuery from '@/components/CombinationQuery';
// import TableColumnView from '@/components/TableColumnView';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';
import { getDictionary } from '@/api/user';
import PrintTable from './components/print-table.vue';
import { certificateList, getSaleCertification } from '@/api/material';
import { tableColumn } from './tableColumn';
import tableHeightManager from '@/utils/tableHeightManager';
// import performanceMonitor from '@/utils/performanceMonitor';

export default {
  name: 'FinishedProductCertificate',
  // Pagination, ListLayout, UserTag, DialogBatchDetermin,
  components: { CombinationQuery, Pagination, UserTag, PrintTable },
  setup() {
    const searchFieldList = [
      {
        fieldKey: 'salesOrderCode',
        fieldName: '销售订单号',
        fieldType: 'text'
      },
      {
        fieldKey: 'customerName',
        fieldName: '客户名称',
        fieldType: 'text'
      },
      {
        fieldKey: 'projectName',
        fieldName: '项目名称',
        fieldType: 'text'
      },
      {
        fieldKey: 'materialNo',
        fieldName: '物料编号',
        fieldType: 'text'
      },
      {
        fieldKey: 'materialDesc',
        fieldName: '物料名称',
        fieldType: 'text'
      },
      {
        fieldKey: 'companyModel',
        fieldName: '型号规格',
        fieldType: 'text'
      },
      {
        fieldKey: 'batchNo',
        fieldName: '批次',
        fieldType: 'text'
      },
      {
        fieldKey: 'productBatchNo',
        fieldName: '生产批次',
        fieldType: 'text'
      },
      {
        fieldKey: 'reelNo',
        fieldName: '客户批次号',
        fieldType: 'text'
      },
      {
        fieldKey: 'productionOrderNo',
        fieldName: '生产订单号',
        fieldType: 'text'
      },
      {
        fieldKey: 'no',
        fieldName: '申请单号',
        fieldType: 'text'
      },
      {
        fieldKey: 'secSampleNum',
        fieldName: '样品编号',
        fieldType: 'text'
      },
      {
        fieldKey: 'inspectionCode',
        fieldName: '报告编号',
        fieldType: 'text'
      }
    ];
    const state = reactive({
      singleTableRef: ref(),
      fieldTips: searchFieldList.map(x => x.fieldName).join('/'),
      selectRow: {}, // 选中的销售订单
      dialogVisible: false,
      ruleForm: ref(),
      searchFieldList: [],
      originCertificateTemplates: [],
      materialCategoryAll: {},
      listLoading: false,
      radioData: {
        1: '未打印',
        2: '已打印',
        3: '待入库',
        4: '已入库'
      },
      status: {
        submitStatus: {
          0: { type: 'warning', label: '未送检' },
          1: { type: 'success', label: '已送检' }
        },
        isDeleted: {
          0: { type: 'warning', label: '未删除' },
          1: { type: 'success', label: '已删除' }
        }
      },
      listQuery: {
        limit: 20,
        page: 1
      },
      formInline: {
        param: '',
        tableQueryParamList: []
      },
      dictionary: {
        HGZSCPD: {
          enable: {},
          all: {}
        },
        JHDW: {
          enable: {},
          all: {}
        },
        5: {
          enable: {},
          all: {}
        }
      },
      tableList: [],
      oldRow: {}, // 右侧选中的行数据（修改之前）
      tableLeft: [],
      tableSelected: [], // 表格选中的值
      dialogFormVisible: false,
      total: 0
    });

    const tableKey = ref(0);
    const tableKeyLeft = ref(0);
    const getList = query => {
      const params = { ...state.formInline };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoading = true;
      getSaleCertification(params).then(res => {
        state.listLoading = false;
        if (res) {
          state.total = res.data.data.totalCount;
          state.tableList = res.data.data.list;
          state.listQuery.page = Number(params.page);
          if (state.tableList?.length) {
            state.singleTableRef.setCurrentRow(state.tableList[0]);
          } else {
            state.selectRow = null;
          }
        }
      });
    };
    getList();
    const getDictionaryList = () => {
      Object.keys(state.dictionary).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          state.dictionary[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              state.dictionary[item].enable[optionItem.code] = optionItem.name;
            }
            state.dictionary[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    getDictionaryList();

    const handleSizeChange = val => {
      state.listQuery.limit = val;
      getList();
    };

    const reset = () => {
      state.formInline = {
        param: '',
        mateType: '',
        reviewerId: getLoginInfo().accountId, // 审核人员
        submitTime: [],
        reviewTime: [],
        ownerId: '', // 试验员
        tableQueryParamList: []
      };
      getList();
    };
    const handleCheckOpera = row => {
      router.push({
        path: '/recordReviewDetail',
        query: {
          samplesId: row.samplesId,
          capabilityId: row.capabilityId
        }
      });
    };
    const handleSelectionChange = val => {
      state.tableSelected = val;
    };
    const handleAudit = () => {};
    /** 批量判定 */
    const handleDetermin = () => {
      state.dialogVisible = true;
    };
    const handleDetail = row => {
      router.push({
        path: '/recordReview/sample/detail',
        query: {
          orderId: row.orderId,
          sampleId: row.samplesId
        }
      });
    };

    const handleCurrentChange = row => {
      state.selectRow = JSON.parse(JSON.stringify(row));
    };
    const getSingleText = val => {
      state.formInline.param = val;
      state.formInline.tableQueryParamList = [];
      getList();
    };

    const getParamList = paramList => {
      state.formInline.tableQueryParamList = paramList;
      state.formInline.param = '';
      getList();
    };
    // 编辑
    const handleEdit = index => {
      state.oldRow = JSON.parse(JSON.stringify(state.tableList[index]));
      state.tableList[index].isEdit = true;
    };
    // 入库
    const handleWarehouse = row => {
      console.log(row);
    };
    // 保存
    const handleSave = row => {
      console.log(row);
    };
    // 取消保存
    const handleCancle = index => {
      state.tableList[index] = JSON.parse(JSON.stringify(state.oldRow));
      state.tableList[index].isEdit = false;
    };

    const onUpdateColumns = columns => {
      tableKey.value = tableKey.value + 1;
      state.fieldTips = tableColumn
        .filter(item => item.isQuery == 1)
        .map(item => item.fieldName)
        .join('/');
    };
    const closeDialog = () => {
      state.dialogVisible = false;
    };
    // 获取打印模板
    const getCertificateTemplates = async () => {
      const res = await certificateList({ page: '1', limit: '-1', categoryId: '' });
      if (res) {
        state.originCertificateTemplates = res.data.data.list.filter(item => item.category === 1 && item.status === 1);
      }
    };
    onMounted(() => {
      getCertificateTemplates();

      // 注册表格高度管理，避免频繁的CSS calc重新计算
      setTimeout(() => {
        const tableElement = state.singleTableRef?.$el;
        if (tableElement) {
          tableHeightManager.registerTable('finished-product-certificate-main', tableElement, {
            offsetHeight: 47.5, // 对应原来的47.5rem
            useRem: true,
            minHeight: 200
          });
        }
      }, 100); // 延迟确保DOM已渲染

      // 添加性能监控，检测页面卡死问题
      // performanceMonitor.on('onFreeze', freezeTime => {
      //   console.warn(`检测到页面冻结 ${freezeTime.toFixed(2)}ms，可能是开发工具拖拽导致`);
      //   // 可以在这里添加用户提示或自动恢复逻辑
      // });

      // performanceMonitor.on('onLowFps', avgFps => {
      //   console.warn(`检测到低FPS: ${avgFps.toFixed(2)}，页面性能可能受到影响`);
      // });
    });

    onUnmounted(() => {
      // 清理表格高度管理
      tableHeightManager.unregisterTable('finished-product-certificate-main');

      // 清理性能监控回调
      // performanceMonitor.off('onFreeze');
      // performanceMonitor.off('onLowFps');
    });
    return {
      ...toRefs(state),
      closeDialog,
      tableColumn,
      handleEdit,
      handleSave,
      handleCancle,
      handleWarehouse,
      getSingleText,
      getParamList,
      handleCurrentChange,
      getPermissionBtn,
      handleDetail,
      drageHeader,
      getNameByid,
      handleSizeChange,
      getNamesByid,
      handleAudit,
      handleDetermin,
      reset,
      handleSelectionChange,
      handleCheckOpera,
      formatDate,
      getList,
      tableKey,
      tableKeyLeft,
      colWidth,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  },
  computed: {
    ...mapGetters(['tenantGroup', 'tenantInfo'])
  }
};
</script>
<style lang="scss" scoped>
.page-searchbar {
  background: #fff;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
}
.page-model {
  background: #fff;
  padding: 20px 20px 0 20px;
  overflow: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
}
:deep(.format-height-table2) {
  .el-table__body-wrapper {
    /* 高度由tableHeightManager动态管理，避免频繁的calc重新计算 */
    overflow-y: auto;
  }
  .el-table__fixed-body-wrapper {
    /* 固定列也需要同步高度管理 */
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  .el-table__fixed-body-wrapper::-webkit-scrollbar {
    display: none;
  }
}
</style>
