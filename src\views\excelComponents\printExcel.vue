<template>
  <div id="template" class="slot-temp box-excel" :class="{ vertical: !isTransverse, horizontal: isTransverse }">
    <BaseTemplate
      :sample-num="jsonData?.secSampleNum"
      :model-specification="jsonData?.experimentProdTypeList"
      :file-no="form?.fileNo"
      :sample-name="jsonData?.sampleName"
      :batch-no="jsonData.batchNo"
      :english-name="jsonData?.englishName"
      :capability-name="jsonData?.capabilityName"
      :head-type="templateStyle"
      :template-tail-style="templateTailStyle"
      :is-transverse="isTransverse"
      :is-dog-tag="isDogTagType"
    >
      <template #test-site>
        <select
          v-model="form.address"
          class="select-option"
          :class="{ red: formcolor.address }"
          :disabled="type !== 'edit'"
          :readonly="type !== 'edit'"
          placeholder="  "
          @click="handleHistory('address')"
        >
          <option v-for="item in testSiteList" :key="item.id" :value="item.code">{{ item.name }}</option>
        </select>
      </template>
      <template #test-time>
        <input v-model="testTime" type="text" class="input" readonly />
      </template>
      <template #test-method>
        <input
          v-model="form.method"
          type="text"
          class="input"
          :class="{ red: formcolor.method }"
          :readonly="type !== 'edit'"
          @click="handleHistory('method')"
        />
      </template>
      <template #test-standard>
        <input
          v-model="form.selectedStandardName"
          type="text"
          class="input"
          :class="{ red: formcolor.selectedStandardName }"
          :readonly="type !== 'edit'"
          @click="handleHistory('selectedStandardName')"
        />
      </template>
      <template #test-temperature>
        <input
          v-model="form.temperature"
          type="text"
          class="input"
          :class="{ red: formcolor.temperature }"
          :readonly="type !== 'edit'"
          @click="handleHistory('temperature')"
        />
      </template>
      <template #test-humidness>
        <input
          v-model="form.humidness"
          type="text"
          class="input"
          :class="formcolor.humidness ? 'red' : ''"
          :readonly="type !== 'edit'"
          @click="handleHistory('humidness')"
        />
      </template>
      <template #test-device>
        <div v-if="deviceListNew">
          <el-row v-for="(item, index) in deviceListNew" :key="index">
            <el-col :span="24">
              <el-row class="bd-b">
                <el-col :span="6" class="bd-r"> 检测设备： </el-col>
                <el-col :span="18">
                  <input v-model="item.deviceName" type="text" class="input" :readonly="true" />
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="24">
              <el-row class="bd-b">
                <el-col :span="6" class="bd-r">设备编号：</el-col>
                <el-col :span="6" class="bd-r">
                  <input v-model="item.deviceNumber" type="text" class="input" maxlength="30" :readonly="true" />
                </el-col>
                <el-col :span="5" class="bd-r">上/下次检定日期：</el-col>
                <el-col :span="7">
                  <span style="padding: 0px 5px; color: #606266">
                    {{ item.validBeginDate }}/{{ item.validEndDate }}
                  </span>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
      </template>
      <div ref="scriptRef" class="html" v-html="jsonData.excelHtml" />
      <div class="excel-bd">
        <slot />
      </div>
      <template #tester>
        <div v-if="(jsonData.status === 3 || jsonData.status === 5) && jsonData.realOwnerIds" class="inline-block">
          <div v-for="(item, index) in jsonData.realOwnerIds.split(',')" :key="index" class="inline-block">
            <img
              v-if="
                jsonData.realOwnerIdsimgs &&
                jsonData.realOwnerIdsimgs[index] &&
                jsonData.realOwnerIdsimgs[index] !== 'null'
              "
              class="qImage"
              :src="jsonData.realOwnerIdsimgs[index]"
              alt=""
              oncontextmenu="return false"
              referrerPolicy="no-referrer"
            />
            <span v-else>{{ getNameByid(item) }}</span>
          </div>
        </div>
      </template>
      <template #checker>
        <div v-if="jsonData.status === 5" class="inline-block">
          <div v-for="(item, index) in jsonData.realReviewerId.split(',')" :key="index" class="qm">
            <img
              v-if="
                jsonData.realReviewerIdimg &&
                jsonData.realReviewerIdimg[index] &&
                jsonData.realReviewerIdimg[index] !== 'null'
              "
              class="qImage"
              :src="jsonData.realReviewerIdimg[index]"
              alt=""
              oncontextmenu="return false"
              referrerPolicy="no-referrer"
            />
            <span v-else>{{ getNamesByIds(item) }}</span>
          </div>
        </div>
      </template>
      <template #test-date>
        <span v-if="jsonData.status === 3 || jsonData.status === 5">
          {{ jsonData?.completestartdate }}
        </span>
      </template>
      <template #check-date>
        <span v-if="jsonData.status === 5">
          {{ jsonData?.reviewdatetime }}
        </span>
      </template>
    </BaseTemplate>
  </div>
  <div v-show="historyData.length >= 2 && showHistory" id="showHistory" class="pop-zr">
    <el-card class="box-card">
      <h4>历史修改</h4>
      <i class="el-icon-close i-close" @click="showHistory = false" />
      <div class="pop-content">
        <el-table :data="historyData" style="width: 100%">
          <el-table-column prop="value" label="数值" width="100" />
          <el-table-column prop="createBy" label="修改人" width="80">
            <template #default="{ row }">
              {{ getNameByid(row.createBy) }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="时间" width="100" />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>
import $ from 'jquery';
import axios from 'axios';
import { useRoute } from 'vue-router';
// import '@/views/excelComponents/commonPart/excelcommon.scss';
// import './excelstyle.scss';
// import './module.scss';
import { getNameByid, getNamesByid, getNamesByIds } from '@/utils/common';
import { reactive, toRefs, watch, onMounted, onUnmounted, nextTick, ref, getCurrentInstance, computed } from 'vue';
import { getTenantConfig } from '@/utils/auth';
import pdf from '@/utils/preview-or-download-pdf';
import BaseTemplate from '@/views/excelComponents/components/BaseTemplate';
import { getXhModuleNumber, disabledXxysSelection } from './func/dynamicCore';
import { setTemplateScript } from './func/templateScript';
import { getDictionary } from '@/api/user';
import { formatDate } from '@/utils/formatTime';
import { mounted, unmounted } from '@/views/excelComponents/template-style-loader';
import { getMinioURL } from '@/utils/base-url';

export default {
  name: 'PrintExcel',
  components: { BaseTemplate },
  props: {
    jsonData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    currIndex: {
      type: Number,
      default: 0
    },
    deviceList: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['handleData', 'setImg'],
  setup(props, ctx) {
    const route = useRoute();
    const { appContext } = getCurrentInstance();
    const bus = appContext.config.globalProperties.bus;
    const scriptRef = ref();
    const state = reactive(
      {
        colorlist: [],
        currIndexPrint: 0,
        templateStyle:
          getTenantConfig().templateStyle === undefined || getTenantConfig().templateStyle === null
            ? 2
            : getTenantConfig().templateStyle,
        templateTailStyle:
          getTenantConfig().templateStyle === undefined || getTenantConfig().templateStyle === null
            ? 0
            : getTenantConfig().templateTailStyle,
        isflag: true,
        realOwnerIdsimgs: [],
        isDogTagType: false, // 是否是铭牌模板
        realReviewerIdimg: '',
        transverse: 0,
        jsonDataNew: props.jsonData,
        publicJs: '',
        module: '',
        colorupdate: true,
        type: '',
        testTime: '',
        deviceListNew: [],
        showHistory: false,
        formcolor: {},
        form: {
          address: '',
          temperature: '',
          humidness: '',
          method: '',
          selectedStandardName: ''
        },
        texJs: '',
        testSiteList: [],
        historyData: [],
        setHistoryTimer: null
      },
      { deep: true }
    );
    bus.$on('excelData', allDate => {});
    const NEW_TEMPLATE_ELEMENT_SELECTOR =
      '.template-content textarea, .template-content input, .template-content select';
    const RADIO_CHECKBOX_NAME_REGEXP = /^(radio|checkbox)$/i;
    const RADIO_CHECKBOX_PARENT_ID_REGEXP = /^[a-z\d\b]{9}_[a-z\b]+/;
    const isTransverse = computed(() => state.jsonDataNew.showType === 1);
    const { DEV } = import.meta.env;
    const templateEditorPath2 = (DEV ? 'http://*************' : '') + getMinioURL();
    watch(props, (newValue, oldValue) => {
      if (newValue && newValue.jsonData.excelHtml) {
        const itemData = JSON.parse(JSON.stringify(newValue.jsonData));
        state.jsonDataNew = itemData;
        state.jsonDataNew.excelHtml = itemData.excelHtml;
        state.transverse = state.jsonDataNew.experimentData.showType;
        state.jsonDataNew.experimentData = itemData.experimentData;
        state.currIndexPrint = newValue.currIndex;
        state.deviceListNew = state.jsonDataNew.devices; // 获取设备
        if (
          state.jsonDataNew?.completestartdate === state.jsonDataNew?.completedatetime ||
          !state.jsonDataNew?.completedatetime
        ) {
          state.testTime = formatDate(state.jsonDataNew?.completestartdate, '', true);
        } else {
          state.testTime =
            formatDate(state.jsonDataNew?.completestartdate, '', true) +
            '~' +
            formatDate(state.jsonDataNew?.completedatetime, '', true);
        }
        if (state.jsonDataNew.isSpecial) {
          state.isDogTagType = true;
        } else {
          state.isDogTagType = false;
        }
        nextTick(() => {
          setData();
        });
      }
    });
    const getPublicJs = async () => {
      const result = await axios.get(`${templateEditorPath2}/public.js`);
      state.publicJs = result.data;
      setTemplateValue();
      setExperimentData();
      setTemplateScript(scriptRef, getMinioURL(), state.publicJs);
      disabledStatus();
      handleWireCore();
      const imgs = await pdf.PageImg(state.jsonDataNew.experimentData?.showType, true, state.currIndexPrint);
      if (imgs) {
        ctx.emit('setImg', imgs);
      }
    };
    const HTMLDOMtoString = HTMLDOM => {
      const div = document.createElement('div');
      div.appendChild(HTMLDOM);
      return div.innerHTML;
    };

    const getTestSiteList = () => {
      getDictionary('SYCS').then(res => {
        state.testSiteList = res.data.data?.dictionaryoption;
      });
    };
    onMounted(() => {
      mounted();
      getTestSiteList();
      state.testSiteList = getTestSiteList();
      nextTick(() => {
        // window.MathJax.startup.defaultReady()
        $('#showHistory').hover(
          function () {
            window.clearInterval(state.setHistoryTimer);
            state.showHistory = true;
          },
          function () {
            state.setHistoryTimer = setTimeout(() => {
              state.showHistory = false;
            }, 3000);
          }
        );
      });
    });

    onUnmounted(() => {
      unmounted();
    });

    // // 初始化数据
    // const setData = async() => {
    //   getXhModuleNumber(route.name, state.jsonDataNew.coreNumber, state.jsonDataNew.experimentData)
    //   state.type = route.query.type
    //   state.jsonDataNew.method = formatMethod(state.jsonDataNew.method)
    //   state.form.method = state.jsonDataNew.method
    //   state.form.standard = state.jsonDataNew.standard
    //   // 关键字引用
    //   if (state.jsonDataNew.templateValue && (JSON.stringify(state.jsonDataNew.templateValue) !== '{}')) {
    //     for (var k in state.jsonDataNew.templateValue) {
    //       if (document.getElementById(k)) {
    //         document.getElementById(k).value = state.jsonDataNew.templateValue[k]
    //       }
    //     }
    //   }
    //   if (JSON.stringify(state.jsonDataNew.experimentData) !== '{}' && state.jsonDataNew.experimentData !== undefined) {
    //     state.deviceListNew = state.jsonDataNew.experimentData.devices
    //     if (state.jsonDataNew.experimentData.body) {
    //       const keylist = Object.keys(state.jsonDataNew.experimentData?.header)
    //       keylist.forEach(item => {
    //         if (state.jsonDataNew.experimentData?.header[item].length >= 2 && state.type === 'check') {
    //           state.formcolor[item] = true
    //         } else {
    //           state.formcolor[item] = false
    //         }
    //         state.form[item] = state.jsonDataNew.experimentData?.header[item][0].value
    //       })
    //       const Ul = document.getElementsByClassName('ipt')
    //       Array.from(Ul).forEach((item, ulIndex) => {
    //         var id = item.getAttribute('id')
    //         var isRadio = id.split('_')[1]
    //         // document.getElementById(id).onclick = showHistory()
    //         if (state.type === 'edit') {
    //           document.getElementById(id).removeAttribute('readonly')
    //           if (isRadio === 'radio') {
    //             const fileName = id.split('_')[0]
    //             $('input[name="' + fileName + '"]').attr('disabled', false)
    //           }
    //         } else {
    //           document.getElementById(id).style.backgroundColor = '#fff'
    //           if (isRadio === 'radio') {
    //             const fileName = id.split('_')[0]
    //             $('input[name="' + fileName + '"]').attr('disabled', true)
    //           }
    //           document.getElementById(id).setAttribute('readonly', 'readonly')
    //         }
    //         document.getElementById(id).addEventListener('focus', function() {
    //           if (state.jsonDataNew.experimentData.body[id].length >= 2 && state.type === 'check') {
    //             window.clearInterval(state.setHistoryTimer)
    //             state.historyData = state.jsonDataNew.experimentData.body[id]
    //             state.showHistory = true
    //             state.setHistoryTimer = setTimeout(() => {
    //               window.clearInterval(state.setHistoryTimer)
    //               state.showHistory = false
    //             }, 3000)
    //           }
    //         })
    //         var thisValue = state.jsonDataNew.experimentData.body[id]
    //         var newValue = ''
    //         if (thisValue instanceof Array) {
    //           if (thisValue.length >= 2 && state.type === 'check') {
    //             item.style.color = 'red'
    //           }
    //           newValue = thisValue[0].value
    //         }
    //         if (isRadio === 'radio') {
    //           const fileName = id.split('_')[0]
    //           $('input[name="' + fileName + '"][value="' + newValue + '"]').attr('checked', true)
    //         }
    //         if (item.tagName === 'SELECT') {
    //           item.options.length = 0
    //           var opitem = new Option()
    //           opitem.value = newValue
    //           if (newValue.split('-').length > 1) {
    //             opitem.text = newValue.split('-')[2]
    //             item.options.add(opitem, 0)
    //           } else {
    //             opitem.text = newValue
    //             item.options.add(opitem, 0)
    //           }
    //         }
    //         item.value = newValue !== null ? newValue : ' '
    //         if (ulIndex === Array.from(Ul).length - 1) {
    //           nextTick(() => {
    //             getPublicJs()
    //           })
    //         }
    //       })
    //     } else {
    //     }
    //   }
    // }

    // 初始化数据
    const setData = async () => {
      getXhModuleNumber(route.name, state.jsonDataNew.coreNumber, state.jsonDataNew.experimentData);
      setTemplateHeader();
      await getPublicJs();
    };

    // 设置模板头信息
    function setTemplateHeader() {
      state.type = route.query.type;
      if (state.jsonDataNew?.header?.selectedStandardName) {
        state.form.selectedStandardName = state.jsonDataNew?.header?.selectedStandardName[0]?.value;
      }
      state.form.fileNo = state.jsonDataNew.header?.fileNo[0]?.value;
      state.form.method = state.jsonDataNew.header?.method[0]?.value;
      state.form.temperature = state.jsonDataNew.header?.temperature[0]?.value;
      state.form.address = state.jsonDataNew.header?.address[0]?.value;
      state.form.humidness = state.jsonDataNew.header?.humidness[0]?.value;
    }

    // 设置模板关键参数值
    function setTemplateValue() {
      if (state.jsonDataNew.templateValue && JSON.stringify(state.jsonDataNew.templateValue) !== '{}') {
        for (var k in state.jsonDataNew.templateValue) {
          if (document.getElementById(k)) {
            document.getElementById(k).value = state.jsonDataNew.templateValue[k];
          }
        }
      }
    }

    // 设置试验数据
    function setExperimentData() {
      if (JSON.stringify(state.jsonDataNew.experimentData) !== '{}' && state.jsonDataNew.experimentData !== undefined) {
        const templateContainer = document.getElementById('template');
        if (templateContainer) {
          // 获取所有需要填入值的 元素
          const allFormElements = templateContainer.querySelectorAll(NEW_TEMPLATE_ELEMENT_SELECTOR);
          const inputEleCollection = templateContainer.getElementsByClassName('ipt');
          // 设置模板表头数据
          if (state.jsonDataNew.experimentData.body) {
            setTempalteFormHeader();
            if (allFormElements.length > 0) {
              setInputEleValueAndStatusByNewTemplate(allFormElements);
            }
            if (inputEleCollection?.length > 0) {
              setInputEleValueAndStatus(inputEleCollection);
            }
          }
        }

        disabledXxysSelection();
      }
    }

    function setTempalteFormHeader() {
      state.form = {
        address: '',
        temperature: '',
        humidness: '',
        method: '',
        selectedStandardName: '',
        fileNo: ''
      };
      const keylist = Object.keys(state.jsonDataNew.experimentData?.header);
      keylist.forEach(item => {
        if (state.jsonDataNew.experimentData?.header[item].length >= 2 && state.type === 'check') {
          state.formcolor[item] = true;
        } else {
          state.formcolor[item] = false;
        }
        state.form[item] = state.jsonDataNew.experimentData?.header[item][0].value;
      });
    }

    function setInputEleValueAndStatusByNewTemplate(allFormElements) {
      allFormElements.forEach(inputElement => {
        if (state.type !== 'edit') {
          inputElement.style.backgroundColor = '#fff';
          inputElement.setAttribute('disabled', '');
          inputElement.setAttribute('readonly', '');
        }
        const isRadioOrCheckbox = inputElement.type && RADIO_CHECKBOX_NAME_REGEXP.test(inputElement.type);
        const key = isRadioOrCheckbox ? inputElement.id.match(RADIO_CHECKBOX_PARENT_ID_REGEXP)?.[0] : inputElement.id;
        const valueList = state.jsonDataNew.experimentData.body[key];
        let realValue = null;
        if (valueList !== '' && valueList !== undefined) {
          if (valueList instanceof Array) {
            if (valueList.length >= 2 && state.type === 'check') {
              inputElement.style.color = 'red';
            }
            realValue = valueList[0].value;
          }
          if (isRadioOrCheckbox) {
            if (inputElement.value == realValue) {
              inputElement.checked = true;
            }
          } else {
            inputElement.value = realValue;
          }
        }
      });
    }

    function setInputEleValueAndStatus(inputEleCollection) {
      // 设置所有需要填值元素的值和状态
      Array.from(inputEleCollection).forEach(item => {
        var id = item.getAttribute('id');
        var isRadio = id.split('_')[1];
        if (state.type === 'edit') {
          document.getElementById(id).removeAttribute('readonly');
          if (isRadio === 'radio') {
            const fileName = id.split('_')[0];
            $('input[name="' + fileName + '"]').attr('disabled', false);
          }
        } else {
          document.getElementById(id).style.backgroundColor = '#fff';
          if (isRadio === 'radio') {
            const fileName = id.split('_')[0];
            $('input[name="' + fileName + '"]').attr('disabled', true);
          }
          document.getElementById(id).setAttribute('readonly', 'readonly');
          document.getElementById(id).disabled = '';
        }
        document.getElementById(id).addEventListener('click', function () {
          if (
            state.jsonDataNew.experimentData.body[id] &&
            state.jsonDataNew.experimentData.body[id].length >= 2 &&
            state.type === 'check'
          ) {
            window.clearInterval(state.setHistoryTimer);
            state.historyData = state.jsonDataNew.experimentData.body[id];
            state.showHistory = true;
            state.historyLabel = {};
            if (id.indexOf('_radio') > -1) {
              Array.from($('#' + id).children()).forEach(function (radioItem) {
                state.historyLabel[radioItem.getAttribute('value')] = radioItem.nextSibling.nodeValue;
              });
            }
            state.setHistoryTimer = setTimeout(() => {
              window.clearInterval(state.setHistoryTimer);
              state.showHistory = false;
            }, 3000);
          }
        });
        var thisValue = state.jsonDataNew.experimentData.body[id];
        var newValue = '';
        if (thisValue instanceof Array) {
          if (thisValue.length >= 2 && state.type === 'check') {
            item.style.color = 'red';
          }
          newValue = thisValue[0].value;
        }
        if (isRadio === 'radio') {
          const fileName = id.split('_')[0];
          $('input[name="' + fileName + '"][value="' + newValue + '"]').attr('checked', true);
        }
        if (item.type === 'checkbox') {
          const checkBoxValue = newValue.split('@;');
          checkBoxValue.forEach(val => {
            $('input[id="' + item.id + '"][value="' + val + '"]').attr('checked', true);
          });
        }
        if (item.tagName === 'SELECT') {
          if (state.type === 'edit') {
            if ($(item).attr('data-code') === 'XXYS') {
              if (newValue) {
                $(item).val(newValue);
              }
            } else {
              $(item).val(newValue);
            }
          } else {
            item.options.length = 0;
            var opitem = new Option();
            opitem.value = newValue;
            if (newValue.split('-').length > 1) {
              opitem.text = newValue.split('-')[2];
              item.options.add(opitem, 0);
            } else {
              opitem.text = newValue;
              item.options.add(opitem, 0);
            }
          }
        }
        if ($(item).attr('data-code') !== 'XXYS') {
          item.value = newValue !== null ? newValue : ' ';
        }
      });
    }

    function disabledStatus() {
      const inputEleCollection = document.getElementsByClassName('ipt');
      Array.from(inputEleCollection).forEach(item => {
        var id = item.getAttribute('id');
        var isRadio = id.split('_')[1];
        document.getElementById(id).style.backgroundColor = '#fff';
        if (isRadio === 'radio') {
          const fileName = id.split('_')[0];
          $('input[name="' + fileName + '"]').attr('disabled', true);
        }
        document.getElementById(id).setAttribute('readonly', 'readonly');
        document.getElementById(id).disabled = '';
      });
    }

    // 头部历史数据赋值
    const handleHistory = item => {
      if (state.type === 'check') {
        state.historyData = [];
        const listdata = state.jsonDataNew.experimentData.header[item];
        listdata?.forEach(obj => {
          const valueaa = {};
          valueaa.createTime = obj.createTime;
          valueaa.createBy = obj.createBy;
          valueaa.value = obj.value;
          state.historyData.push(valueaa);
        });
        state.showHistory = true;
      }
    };
    const setimg = () => {
      setData();
    };
    // 取模板数据
    const handleData = () => {
      const excelData = {};
      const allData = {};
      const templateContainer = document.getElementById('template');
      // new template
      const allFormElements = templateContainer.querySelectorAll(NEW_TEMPLATE_ELEMENT_SELECTOR);
      // console.log('allFormElements', allFormElements);
      allFormElements.forEach(item => {
        if (item.type && item.type.match(RADIO_CHECKBOX_NAME_REGEXP)) {
          if (item.checked) {
            const parentId = item.id.match(RADIO_CHECKBOX_PARENT_ID_REGEXP)[0];
            excelData[parentId] = item.value;
          }
        } else {
          excelData[item.id] = item.value;
        }
      });

      // old template
      const inputEleCollection = templateContainer.getElementsByClassName('ipt');
      for (var i = 0; i < inputEleCollection.length; i++) {
        if (!inputEleCollection.item(i).id.includes('moduleIndex')) {
          const id = inputEleCollection.item(i).id;
          if (id.split('_')[1] === 'radio') {
            const radioName = id.split('_')[0];
            excelData[id] = $('input[name="' + radioName + '"]:checked').val();
          } else if (inputEleCollection.item(i).type === 'checkbox') {
            var checkedValues = [];
            $('input[name="' + id + '"]:checked').each(function () {
              checkedValues.push($(this).val());
            });
            excelData[id] = checkedValues.join('@;');
          } else {
            excelData[id] = $('#' + id).val();
          }
        }
      }
      // console.log('excelData', excelData);
      state.isEdit = false;
      // return false
      allData.header = { ...state.form };
      allData.body = { ...excelData };
      ctx.emit('handleData', allData);
      bus.$emit('excelData', allData);
    };

    // 线芯自动赋值并禁用
    const handleWireCore = () => {
      if (state.type === 'edit') {
        $('input, textarea, select').change(function () {
          state.isEdit = true;
        });
      }
      const ele = document.getElementById(`sjlx_radio`) || document.getElementById(`sjxz_radio`);
      if (ele) {
        disabledXxysSelection();
        ele.addEventListener('blur', function (e) {
          if (e.target.tagName === 'INPUT') {
            setData();
          }
        });
      }
    };

    return {
      ...toRefs(state),
      handleData,
      isTransverse,
      formatDate,
      HTMLDOMtoString,
      setimg,
      getPublicJs,
      getNamesByid,
      getNamesByIds,
      scriptRef,
      handleHistory,
      getNameByid,
      setData
    };
  }
};
</script>

<style scoped lang="scss">
@import '@/styles/mixin.scss';
.slot-temp {
  font-family: Times New Roman, 'SourceHanSerifCN';
  box-sizing: border-box;
  box-shadow: 0px 4px 12px 4px rgba(0, 0, 0, 0.08), 0px 2px 4px -2px rgba(0, 0, 0, 0.16);
  filter: drop-shadow(0px 3px 8px rgba(0, 0, 0, 0.12));
  margin: 0 auto;
  padding: 50px 55px 50px 55px;
  font-size: 14px;
  font-weight: 500;
  background: #fff;

  &.vertical {
    width: 794px;
  }

  &.horizontal {
    width: 1123px;
  }
}

:deep(.el-input.is-disabled .el-input__inner) {
  color: #000;
}

:deep(.is-disabled .el-input__suffix) {
  display: none;
}
.qm {
  display: flex;
}
.qImage {
  max-height: 80px;
  max-width: 70px;
  display: block;
}

.pdf-header {
  h2,
  h3 {
    text-align: center;
  }
  .bd {
    margin: 0 auto;
  }
}

.bd {
  text-align: left;
  width: 680px;
  margin: 10px auto 0;
}

.header {
  border: 1px solid #000;
  line-height: 1.5;
  text-indent: 10px;
  margin-top: 20px;
  border-bottom: none;
}

.pop-zr {
  width: 320px;
  position: fixed;
  top: 150px;
  right: 50px;
  transition: all 0.2s ease 0s;

  .pop-content {
    max-height: 300px;
    overflow-y: auto;
  }

  h4 {
    text-align: left;
    margin-bottom: 5px;
  }

  .i-close {
    position: absolute;
    top: 10px;
    right: 5px;
    cursor: pointer;
  }
}

.print-box {
  width: 794px;
}

@media print {
  /*隐藏不打印的元素*/
  .print-box {
    display: block;
  }
  /*其他打印样式*/
}

.red {
  color: red;

  input {
    color: red !important;
  }

  :deep(.el-input__inner) {
    color: red !important;
  }

  .el-input__inner {
    color: red !important;
  }
}

.template-title {
  font-weight: bold;
  font-size: 1.5em;
  line-height: normal;
}
.english-title {
  font-size: 1em;
  margin-top: 0px;
  line-height: normal;
}
.inline-block {
  display: inline-block;
  vertical-align: top;
}
</style>
