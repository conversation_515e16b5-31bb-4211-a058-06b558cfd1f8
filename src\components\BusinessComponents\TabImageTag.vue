<template>
  <div class="Specification">
    <div class="header-search-group" :style="{ marginBottom: parentType === 1 ? '20px' : '10px' }">
      <div v-if="parentType === 1" class="search-bar">
        <!-- <el-input v-model.trim="condition" size="small" placeholder="请输入key/名称" prefix-icon="el-icon-search" clearable @clear="getList()" @keyup.enter="getList()" />
        <el-button type="primary" size="small" style="margin-left:10px;" @click="getList()">查询</el-button> -->
      </div>
      <div class="btn-group">
        <el-button
          v-if="addOrEditPermission && tableStatus !== 2"
          size="small"
          icon="el-icon-plus"
          :loading="listLoading"
          :type="parentType === 1 ? 'primary' : 'default'"
          @click="handleAddEdit()"
          @keyup.prevent
          @keydown.enter.prevent
          >新增</el-button
        >
        <el-button
          v-if="showEditBtn"
          size="small"
          :type="parentType === 1 ? 'primary' : 'default'"
          icon="el-icon-edit-outline"
          :loading="listLoading"
          @click="handleEditable()"
          @keyup.prevent
          @keydown.enter.prevent
          >编辑</el-button
        >
        <el-button
          v-if="keyEditable || keyAdd"
          size="small"
          icon="el-icon-check"
          type="primary"
          :loading="listLoading"
          @click="handleSaveEdit()"
          @keyup.prevent
          @keydown.enter.prevent
          >保存</el-button
        >
        <el-button
          v-if="keyEditable || keyAdd"
          size="small"
          icon="el-icon-close"
          :loading="listLoading"
          @click="handleCancelEdit()"
          @keyup.prevent
          @keydown.enter.prevent
          >取消</el-button
        >
      </div>
    </div>

    <el-table
      v-if="isShow"
      id="sortableList"
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="tableList"
      size="medium"
      height="auto"
      fit
      border
      highlight-current-row
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
    >
      <!-- <el-table-column label="排序" width="50px">
        <i class="tes-move iconfont" style="font-size: 12px;cursor: move;" />
      </el-table-column> -->
      <el-table-column label="编号" type="index" width="50" />
      <template v-for="(item, index) in imageTagFieldList" :key="index">
        <el-table-column
          v-if="item.checkbox && item.isHide !== 1"
          :prop="item.field"
          :label="item.name"
          :sortable="Number(item.isOrder) === 1"
          :width="item.isMinWidth ? '' : getColWidth(item.colWidth)"
          :min-width="item.isMinWidth ? getColWidth(item.colWidth) : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div v-if="item.type === 0">
              <div v-if="keyEditable || !row.id">
                <el-input v-model="row[item.field]" v-trim placeholder="请输入" />
              </div>
              <div v-else>
                {{ row[item.field] || '--' }}
              </div>
            </div>
            <div v-else>
              {{ row[item.length] || '--' }}
            </div>
          </template>
        </el-table-column>
      </template>

      <el-table-column prop="length" label="长度(px)" width="120">
        <template #default="{ row }">
          <div v-if="keyEditable || !row.id">
            <el-form :key="row.id" :model="row" :rules="lengthRules">
              <el-form-item prop="length" style="margin: 0px">
                <el-input v-model.number="row.length" placeholder="请输入" />
              </el-form-item>
            </el-form>
          </div>
          <div v-else>
            {{ row.length || '--' }}
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="width" label="宽度(px)" width="120">
        <template #default="{ row }">
          <div v-if="keyEditable || !row.id">
            <el-form :key="row.id" :model="row" :rules="widthRules">
              <el-form-item prop="width" style="margin: 0px">
                <el-input v-model.number="row.width" placeholder="请输入" />
              </el-form-item>
            </el-form>
          </div>
          <div v-else>
            {{ row.width || '--' }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" prop="operation" :width="colWidth.operationSingle">
        <template #default="{ row, $index }">
          <div v-if="deletePermission || !row.id" :key="$index">
            <span class="blue-color" @click="handleDelete(row, $index)">删除</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { reactive, ref, toRefs, watch, computed } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import Sortable from 'sortablejs';
import { getColWidth } from '@/utils/func/customTable';
import { imageTagFieldList } from './func/imageTagInfo';
import { getCapabilityImageLabel, saveCapabilityImageLabel, deleteCapabilityImageLabel } from '@/api/capability';
import { getMaterialImageLabel, saveMaterialImageLabel, deleteMaterialImageLabel } from '@/api/material';
import { ElMessage, ElMessageBox } from 'element-plus';
import { publicIsNumber } from '@/utils/validate';

export default {
  name: 'TabImageTag',
  components: {},
  props: {
    currentData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    parentType: {
      // 0: 检测项目， 1: 物资分类
      type: Number,
      default: 0
    }
  },
  setup(props) {
    // const { proxy } = getCurrentInstance()
    const state = reactive({
      tableRef: ref(),
      isShow: true,
      categoryId: '', // 物资分类id
      condition: '',
      dictionaryList: [], // 字典列表
      dialogUpload: false,
      ruleForm: ref(),
      listLoading: false,
      tableList: [],
      showSaveBtn: false,
      tableStatus: 0,
      capabilityId: props.currentData?.id,
      materialCategoryCode: props.currentData?.materialCategoryCode
    });

    const validatorLength = (rule, value, callback) => {
      if (!publicIsNumber(value)) {
        const msg = '输入的数字无效，请检查!';
        callback(new Error(msg));
        ElMessage.warning(msg);
      } else if (Number(value) > 700) {
        const msg = '为了更好的展示图片，长度最大值不能超过700!';
        callback(new Error(msg));
        ElMessage.warning(msg);
      } else if (Number(value) <= 1) {
        const msg = '长度需要大于1';
        callback(new Error(msg));
        ElMessage.warning(msg);
      } else {
        callback();
      }
    };

    const validatorWidth = (rule, value, callback) => {
      if (!publicIsNumber(value)) {
        const msg = '输入的数字无效，请检查!';
        callback(new Error(msg));
        ElMessage.warning(msg);
      } else if (Number(value) > 900) {
        const msg = '为了更好的展示图片，宽度最大值不能超过900!';
        callback(new Error(msg));
        ElMessage.warning(msg);
      } else if (Number(value) <= 1) {
        const msg = '宽度需要大于1';
        callback(new Error(msg));
        ElMessage.warning(msg);
      } else {
        callback();
      }
    };

    const lengthRules = reactive({
      length: [
        { required: true, message: '请输入长度' },
        { validator: validatorLength, trigger: 'blur' }
      ]
    });

    const widthRules = reactive({
      width: [
        { required: true, message: '请输入长度' },
        { validator: validatorWidth, trigger: 'blur' }
      ]
    });

    const tableKey = ref(0);
    const getList = () => {
      state.listLoading = true;
      state.isShow = false;
      if (props.parentType === 0) {
        getCapabilityImageLabel(props.currentData?.id).then(res => {
          if (res) {
            state.tableList = res.data.data;
          }
          state.isShow = true;
          state.listLoading = false;
        });
      } else {
        getMaterialImageLabel(props.currentData?.id).then(res => {
          if (res) {
            state.tableList = res.data.data;
          }
          state.isShow = true;
          state.listLoading = false;
        });
      }
    };

    // 行拖拽
    const rowDrop = () => {
      // 获取当前表格
      const el = document.getElementById('sortableList').querySelector('.el-table__body');
      Sortable.create(el, {
        animation: 300,
        handle: '.tes-move',
        draggable: '.el-table__row',
        ghostClass: 'ghost',
        // 拖动对象移动样式
        dragClass: 'drag',
        forceFallback: true,
        onEnd({ newIndex, oldIndex }) {
          if (oldIndex !== newIndex) {
            const currRow = state.tableList.splice(oldIndex, 1)[0];
            state.tableList.splice(newIndex, 0, currRow);
            state.tableList.forEach((value, index) => {
              value.order = index;
            });
            updateOrder(state.tableList);
          }
        }
      });
    };
    const updateOrder = tableData => {
      state.listLoading = true;
      state.isShow = false;
    };
    // 新增、编辑
    const handleAddEdit = row => {
      if (props.parentType === 0) {
        state.tableList.push({
          capabilityId: state.capabilityId,
          id: '',
          labelKey: '',
          labelName: '',
          length: '',
          width: '',
          materialCategoryCode: state.materialCategoryCode
        });
      } else {
        state.tableList.push({
          materialCategoryId: state.categoryId,
          id: '',
          labelKey: '',
          labelName: '',
          length: '',
          width: '',
          materialCategoryCode: state.materialCategoryCode
        });
      }
      state.tableStatus = 1;
      state.showSaveBtn = true;
    };

    // #region 编辑

    const handleEditable = () => {
      state.showSaveBtn = true;
      state.tableStatus = 2;
    };

    const handleCancelEdit = () => {
      state.tableList = state.tableList.filter(item => item.id);
      state.showSaveBtn = false;
      state.tableStatus = 0;
    };

    const handleSaveEdit = () => {
      let isValid = true;
      state.tableList.forEach(item => {
        if (
          !item.labelKey ||
          !item.labelName ||
          !publicIsNumber(item.length) ||
          !publicIsNumber(item.width) ||
          Number(item.length) > 700 ||
          Number(item.length) <= 1 ||
          Number(item.width) > 900 ||
          Number(item.width) <= 1
        ) {
          isValid = false;
        }
      });
      if (isValid) {
        const params = {
          entityList: state.tableList
        };
        if (props.parentType === 0) {
          state.listLoading = true;
          saveCapabilityImageLabel(params).then(res => {
            state.listLoading = false;
            if (res) {
              state.tableStatus = 0;
              ElMessage.success('保存成功!');
              state.showSaveBtn = false;
              getList();
            }
          });
        } else {
          state.listLoading = true;
          saveMaterialImageLabel(params).then(res => {
            state.listLoading = false;
            if (res) {
              state.tableStatus = 0;
              ElMessage.success('保存成功!');
              state.showSaveBtn = false;
              getList();
            } else {
              state.showSaveBtn = false;
            }
          });
        }
      } else {
        ElMessage.warning('未填写图片key/名称或不满足1<=长度<=700且1<=宽度<=900，无法保存！');
      }
    };

    const handleDelete = (row, index) => {
      if (row.id) {
        ElMessageBox({
          title: '删除图片标签',
          message: `是否确认删除图片标签${row.labelKey}-${row.labelName}？`,
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: true,
          type: 'warning'
        })
          .then(() => {
            if (props.parentType === 0) {
              deleteCapabilityImageLabel(row.id).then(res => {
                if (res) {
                  getList();
                  ElMessage.success('删除成功!');
                }
              });
            } else {
              deleteMaterialImageLabel(row.id).then(res => {
                if (res) {
                  getList();
                  ElMessage.success('删除成功!');
                }
              });
            }
          })
          .catch(() => {});
      } else {
        state.tableList.splice(index, 1);
      }
    };

    const keyEditable = computed(() => state.tableStatus === 2);
    const keyAdd = computed(() => state.tableStatus === 1);
    const showEditBtn = computed(() => !state.showSaveBtn && addOrEditPermission && state.tableList.length > 0);

    const addOrEditPermission = computed(() => {
      if (props.parentType === 0) {
        return getPermissionBtn('AddOrEditCapabilityImageLabelBtn');
      } else {
        return getPermissionBtn('AddOrEditMaterialImageLabelBtn');
      }
    });

    const deletePermission = computed(() => {
      if (props.parentType === 0) {
        return getPermissionBtn('deleteCapabilityImageLabelBtn');
      } else {
        return getPermissionBtn('deleteMaterialImageLabelBtn');
      }
    });

    // #endregion

    // #region 特殊列

    const jumpApplicationDetail = row => {};

    // #endregion

    // #region 立即执行

    // #endregion

    // #region 监听编号

    watch(
      props,
      newValue => {
        if (newValue) {
          if (newValue?.currentData?.id) {
            handleCancelEdit();
            if (props.parentType === 0) {
              state.capabilityId = props.currentData?.id;
              state.materialCategoryCode = props.currentData?.materialCategoryCode;
            } else {
              state.categoryId = props.currentData?.id;
              state.materialCategoryCode = props.currentData?.code;
            }
            getList();
          }
        }
      },
      { immediate: true }
    );

    // #endregion

    return {
      ...toRefs(state),
      updateOrder,
      rowDrop,
      handleAddEdit,
      drageHeader,
      getNameByid,
      getNamesByid,
      getPermissionBtn,
      formatDate,
      getList,
      tableKey,
      colWidth,
      keyEditable,
      keyAdd,
      handleEditable,
      handleCancelEdit,
      handleSaveEdit,
      imageTagFieldList,
      jumpApplicationDetail,
      getColWidth,
      handleDelete,
      lengthRules,
      widthRules,
      addOrEditPermission,
      deletePermission,
      showEditBtn
    };
  },
  computed: {},
  created() {}
};
</script>
<style lang="scss" scoped>
.ggljf {
  position: absolute;
  top: 0;
  left: -103px;
  display: inline-block;
}
.glzd {
  position: absolute;
  display: inline-block;
  top: 0;
  left: -88px;
}
.header-search-group {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  .search-bar {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .btn-group {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>
