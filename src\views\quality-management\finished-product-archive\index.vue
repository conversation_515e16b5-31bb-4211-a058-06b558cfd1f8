<template>
  <div class="h-full overflow-hidden layout">
    <ListLayout :has-quick-query="false">
      <template #search-bar>
        <el-form ref="editFrom" :inline="true" :model="state.queryParams" class="page-searchbar" @submit.prevent>
          <el-form-item prop="keyword" class="w-[600px]">
            <el-input
              v-model="state.queryParams.keyword"
              v-trim
              v-focus
              placeholder="请输入工程项目名称/销售订单/生产订单/公司批号/物料编号/物料描述/搜索"
              prefix-icon="el-icon-search"
              size="large"
              clearable
              @keyup.enter="handleQuery()"
            />
          </el-form-item>
          <el-form-item style="margin-left: 0">
            <el-button type="primary" size="large" @click="handleQuery()">查询</el-button>
            <el-button size="large" @click="reset">重置</el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #button-group>
        <el-button type="primary" size="large" @keyup.prevent @keydown.enter.prevent @click="onRedirectMOM()"
          >跳转到MOM</el-button
        >
        <el-button
          type="primary"
          size="large"
          @keyup.prevent
          @keydown.enter.prevent
          @click="onBatchQueryDialogVisible(true)"
        >
          批量查询</el-button
        >
      </template>
      <div class="h-full overflow-hidden flex flex-col">
        <div v-if="state.notFoundBatchNoList?.length > 0" class="flex gap-3 flex-wrap mb-4 items-center">
          <el-tooltip effect="dark" content="批次未查询到例行检测报告" placement="top">
            <el-icon color="#dc2828"> <i class="el-icon-warning" /></el-icon>
          </el-tooltip>
          <span v-for="(item, index) in state.notFoundBatchNoList" :key="index">{{ item }}</span>
          <span>批次未查询到例行检测报告</span>
        </div>
        <div class="flex-1 overflow-hidden flex flex-col">
          <el-tabs v-model="state.archiveType" @tab-click="onChangeArchiveType($event)">
            <el-tab-pane
              :label="InspectionTypeConstMapDesc[InspectionTypeConst.ROUTINE_FINISHED_PRODUCT]"
              :name="InspectionTypeConst.ROUTINE_FINISHED_PRODUCT"
            />
            <el-tab-pane
              :label="InspectionTypeConstMapDesc[InspectionTypeConst.FINISHED_PRODUCT_SAMPLING]"
              :name="InspectionTypeConst.FINISHED_PRODUCT_SAMPLING"
            />
            <el-tab-pane
              :label="InspectionTypeConstMapDesc[InspectionTypeConst.W_POINT_SAMPLING]"
              :name="InspectionTypeConst.W_POINT_SAMPLING"
            />
          </el-tabs>

          <!-- <component
            :is="componentRef"
            ref="component"
            class="h-[60vh]"
            @onGetNotFoundBatchNoList="handleGetNotFoundBatchNoList($event)"
          /> -->
          <RoutineData
            v-if="state.archiveType === InspectionTypeConst.ROUTINE_FINISHED_PRODUCT"
            ref="component"
            @onGetNotFoundBatchNoList="handleGetNotFoundBatchNoList($event)"
          />
          <FinishedProductInspectionReport
            v-else-if="state.archiveType === InspectionTypeConst.FINISHED_PRODUCT_SAMPLING"
            ref="component"
            @onGetNotFoundBatchNoList="handleGetNotFoundBatchNoList($event)"
          />
          <WPointInspectionReport
            v-else-if="state.archiveType === InspectionTypeConst.W_POINT_SAMPLING"
            ref="component"
            @onGetNotFoundBatchNoList="handleGetNotFoundBatchNoList($event)"
          />
        </div>
      </div>
    </ListLayout>
    <BatchQueryDialog
      ref="batchQueryDialogRef"
      v-model="state.batchQueryDialogVisible"
      @onQuery="onBatchQuery($event)"
    />
  </div>
</template>

<script setup>
import ListLayout from '@/components/ListLayout';
import { defineAsyncComponent, reactive, ref, shallowRef } from 'vue';
import BatchQueryDialog from '../component/batch-query/index.vue';
import { InspectionTypeConst, InspectionTypeConstMapDesc } from '@/const/inspection-type-const';

const RoutineData = defineAsyncComponent(() => import('./routine-data/index.vue'));
const FinishedProductInspectionReport = defineAsyncComponent(() =>
  import('./finished-product-inspection-report/index.vue')
);
const WPointInspectionReport = defineAsyncComponent(() => import('./wPoint-inspection-report/index.vue'));
const batchQueryDialogRef = ref();

const archiveTypeMapComponent = {
  [InspectionTypeConst.ROUTINE_FINISHED_PRODUCT]: RoutineData,
  [InspectionTypeConst.FINISHED_PRODUCT_SAMPLING]: FinishedProductInspectionReport,
  [InspectionTypeConst.W_POINT_SAMPLING]: WPointInspectionReport
};

const componentRef = shallowRef(RoutineData);
const component = ref();
let batchQueryParams = {};
const state = reactive({
  selected: [],
  list: [],
  loading: false,
  pagination: {
    page: 1,
    limit: 20
  },
  total: 0,
  queryParams: {
    keyword: ''
  },
  notFoundBatchNoList: [],
  archiveType: InspectionTypeConst.ROUTINE_FINISHED_PRODUCT,
  batchQueryDialogVisible: false
});

const onRedirectMOM = () => {
  //
};

const onBatchQueryDialogVisible = () => {
  state.batchQueryDialogVisible = true;
};

const onChangeArchiveType = ({ props: { name } }) => {
  batchQueryDialogRef.value.onSetEmpty();
  state.queryParams.keyword = '';
  batchQueryParams = {};
  setTimeout(() => {
    componentRef.value = archiveTypeMapComponent[name];
  }, 50);
  //  componentRef.value = archiveTypeMapComponent[name];
};

const handleGetNotFoundBatchNoList = notFoundBatchNoList => {
  state.notFoundBatchNoList = notFoundBatchNoList;
};

const reset = () => {
  state.queryParams.keyword = '';
  handleQuery();
};

const onBatchQuery = (params = {}) => {
  batchQueryParams = params;
  handleQuery();
};

const handleQuery = () => {
  let params = { param: state.queryParams.keyword };
  if (batchQueryParams && Object.keys(batchQueryParams).length) {
    params = { ...params, ...batchQueryParams };
  }
  component.value.query(params);
};
</script>
<style lang="scss" scoped>
.layout {
  padding-bottom: 20px;
  padding-top: 84px;
}
:deep(.page-wrapper) {
  overflow: hidden;
  height: 100%;
  .page-list-main {
    flex: 1;
    overflow: hidden;
    top: 0 !important;
    .page-default-main {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      .el-container {
        overflow: hidden;
        height: 100%;
        .page-main {
          height: 100% !important;
          overflow: hidden !important;
        }
      }
    }
  }
}
</style>
