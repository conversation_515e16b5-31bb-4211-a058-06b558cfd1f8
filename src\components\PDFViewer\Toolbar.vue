<template>
  <div ref="toolbarRef" class="toolbar flex items-center px-5 bg-bg_color">
    <button id="sidebarToggle" title="显示目录">
      <el-icon>
        <folder />
      </el-icon>
    </button>
    <button id="previous" title="上一页">
      <el-icon>
        <arrow-left-bold />
      </el-icon>
    </button>
    <input id="pageNumber" type="number" />
    <span id="numPages" class="whitespace-nowrap" />
    <button id="next" title="下一页">
      <el-icon>
        <arrow-right-bold />
      </el-icon>
    </button>
    <button id="pageRotateCcw" text title="逆时针旋转">
      <el-icon>
        <refresh-left />
      </el-icon>
    </button>
    <button id="pageRotateCw" text title="顺时针旋转">
      <el-icon>
        <refresh-right />
      </el-icon>
    </button>
    <button id="zoomOut" text title="缩小">
      <el-icon>
        <zoom-out />
      </el-icon>
    </button>
    <select id="scaleSelect" data-l10n-id="zoom">
      <option
        v-for="option in scaleOptions"
        :id="option.id"
        :key="option.id"
        :value="option.value"
        :data-l10n-id="option.l10nId"
        :data-l10n-args="option.l10nArgs"
        :hidden="option.value === 'custom'"
        :disabled="option.value === 'custom'"
      />
    </select>
    <button id="zoomIn" text title="放大">
      <el-icon>
        <zoom-in />
      </el-icon>
    </button>
    <!--    <el-button id="print">打印</el-button>-->
    <!--    <el-button id="presentationMode" text title="演示模式">-->
    <!--      <FontIcon icon="icon-yanshi" />-->
    <!--    </el-button>-->
    <button :title="isFullscreen ? '退出全屏' : '全屏'" @click="toggle">
      <el-icon>
        <full-screen />
      </el-icon>
    </button>
    <!--    <el-button id="download" text title="下载">-->
    <!--      <FontIcon icon="icon-xiazai" />-->
    <!--    </el-button>-->
  </div>
</template>

<script setup>
import {
  Folder,
  ArrowLeftBold,
  ArrowRightBold,
  RefreshLeft,
  RefreshRight,
  ZoomOut,
  ZoomIn,
  FullScreen
} from '@element-plus/icons';
// import { FullScreen } from '@element-plus/icons-vue';
// import { useFullscreen } from '@vueuse/core';
import { ref, watchEffect } from 'vue';

const scaleOptions = [
  { id: 'pageAutoOption', value: 'auto', l10nId: 'page_scale_auto', l10nArgs: '' },
  { id: 'pageActualOption', value: 'page-actual', l10nId: 'page_scale_actual', l10nArgs: '' },
  { id: 'pageFitOption', value: 'page-fit', l10nId: 'page_scale_fit', l10nArgs: '' },
  { id: 'pageWidthOption', value: 'page-width', l10nId: 'page_scale_width', l10nArgs: '' },
  { id: 'customScaleOption', value: 'custom', l10nId: '', l10nArgs: '' },
  { id: 'customScaleOption', value: '0.5', l10nId: 'page_scale_percent', l10nArgs: getScale(50) },
  { id: 'customScaleOption', value: '0.75', l10nId: 'page_scale_percent', l10nArgs: getScale(75) },
  { id: 'customScaleOption', value: '1', l10nId: 'page_scale_percent', l10nArgs: getScale(100) },
  { id: 'customScaleOption', value: '1.25', l10nId: 'page_scale_percent', l10nArgs: getScale(125) },
  { id: 'customScaleOption', value: '1.5', l10nId: 'page_scale_percent', l10nArgs: getScale(150) },
  { id: 'customScaleOption', value: '2', l10nId: 'page_scale_percent', l10nArgs: getScale(200) },
  { id: 'customScaleOption', value: '3', l10nId: 'page_scale_percent', l10nArgs: getScale(300) },
  { id: 'customScaleOption', value: '4', l10nId: 'page_scale_percent', l10nArgs: getScale(400) }
];
const toolbarRef = ref();
const fullscreenElement = ref();

// const { isFullscreen, toggle } = useFullscreen(fullscreenElement);

watchEffect(() => (fullscreenElement.value = toolbarRef.value?.parentElement));

function getScale(value) {
  return `{ "scale": ${value} }`;
}
</script>

<style scoped lang="scss">
.toolbar {
  height: 42px;
  background: rgb(255 255 255 / 80%);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 5px 5px 0 0;
  box-shadow: 0 -4px 10px 0 rgb(255 255 255 / 15%);
  backdrop-filter: blur(2px);

  & > * {
    margin-right: 5px;
  }

  & > button {
    border-radius: var(--el-border-radius-base);
    padding: 8px 15px;
    height: 32px;

    &:disabled {
      color: var(--el-font-color-disabled-base);
    }

    &:hover:not(:disabled) {
      background-color: var(--el-background-color-base);
    }
  }

  & > input,
  & > select {
    height: 28px;
    line-height: 28px;
  }
}

#pageNumber {
  width: 32px;
  text-align: center;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 5px;
  outline: none;

  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    appearance: none;
  }
}

#scaleSelect {
  padding: 0 8px;
  text-align: center;
  appearance: none;
  cursor: pointer;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 5px;
  outline: none;
}
</style>
