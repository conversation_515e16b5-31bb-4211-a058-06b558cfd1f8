<template>
  <div ref="htmlDoc" v-loading="loadingWord" class="html-contant" />
</template>

<script>
import { ref, reactive, toRefs } from 'vue';
// import mammoth from 'mammoth'
import { useRoute } from 'vue-router';
import { exportWord } from '@/api/testReport';
import { ElMessage } from 'element-plus';
import Watermark from '@/utils/watermark';
import { getLoginInfo } from '@/utils/auth';
import { formatTenantId } from '@/utils/formatJson';
import { renderAsync } from 'docx-preview';
// import $ from 'jquery'

export default {
  name: 'Preview',
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    const route = useRoute();
    const datas = reactive({
      showDrawer: false,
      htmlContant: null,
      loadingWord: false,
      fileViewIframe: ref(),
      htmlDoc: ref()
    });

    // 下载报告
    const downloadReport = row => {
      // console.log(row)
      datas.loadingWord = true;
      datas.htmlContant = null;
      exportWord(row.id, row.mateType, row.sampleId).then(res => {
        // console.log(res)
        document.title = `${row.reportNo} - cxist`;
        if (res !== false) {
          const reader = new FileReader();
          reader.addEventListener('loadend', () => {
            try {
              const resdata = JSON.parse(reader.result);
              if (resdata.code === 400) {
                ElMessage({
                  message: resdata.message,
                  type: 'error',
                  duration: 3000
                });
                setTimeout(() => {
                  window.close();
                }, 3000);
              }
            } catch (error) {
              const blob = res.data;
              // console.log(blob)
              if (blob.size === 0) {
                datas.loadingWord = false;
                ElMessage.warning('报告加载失败！size = 0');
              }
              var reader1 = new FileReader();
              reader1.addEventListener('loadend', function (e) {
                const arrayBuffer = e.currentTarget.result;
                renderAsync(arrayBuffer, datas.htmlDoc);
                const userInfo = getLoginInfo();
                const tenantInfo = formatTenantId(userInfo.tenantId);
                if (tenantInfo.isWatermark) {
                  Watermark.add(`${userInfo.nickname} ${userInfo.username}`);
                } else {
                  Watermark.remove();
                }
                datas.loadingWord = false;
              });
              reader1.readAsArrayBuffer(blob);
            }
          });
          reader.readAsText(res.data, 'utf-8');
        } else {
          datas.loadingWord = false;
        }
      });
    };

    downloadReport(route.params);

    return {
      ...toRefs(datas),
      downloadReport
    };
  },
  methods: {}
};
</script>
<style lang="scss" scoped>
.uploadBtn {
  width: 100%;
  margin-left: 10px;
  margin-bottom: 10px;
}
.html-contant {
  // height: 800px;
  overflow: hidden auto;
  // padding: 0px 10px;
  border-width: 1px !important;
  //:deep(table) {
  // width: 100% !important;
  // border-spacing: 0px;
  //// border-bottom: 0.5px solid black;
  // td {
  //   border-left: 0.5px solid black;
  //   border-top: 0.5px solid black;
  //   border-width: 1px !important;

  //   p {
  //     strong {
  //       line-height: 20px;
  //     }
  //   }
  // }
  // td:last-child {
  //   border-width: 1px;
  //   border-right: 0.5px solid black;
  // }
  // th {
  //   border: 0.5px solid black;
  //   border-bottom: 0px;
  //   border-top:1px solid black;
  // }
  //}
  :deep(ol) {
    text-align: left;
    li {
      height: 25px;
    }
  }
  :deep(.docx-wrapper) {
    background: #ffffff;
    section {
      padding: 50px 50px !important;
      header {
        border: 0px;
        table tr td {
          border: 0px;
          border-width: 0px !important;
          p.docx_4 {
            border-bottom: 0px !important;
          }
        }
      }
      article {
        p {
          line-height: 20px;
        }
      }
      footer {
        border: 0px;
        table tr td {
          border: 0px;
          border-width: 0px !important;
        }
      }
    }
  }
}
</style>
