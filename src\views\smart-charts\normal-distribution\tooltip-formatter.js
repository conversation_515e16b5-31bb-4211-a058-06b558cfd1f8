import { throttle } from 'lodash';

export const singleChartTooltipFormatter = throttle(params => {
  let title = params[0]?.axisValue;
  params.forEach(item => {
    title += `<div style="margin: 0px 0 0;line-height:1.5;">
            <div style="line-height:1.5;">
              ${item.marker}
              <span style="font-size:14px;color:#303133;font-weight:400;margin-left:2px">${item.seriesName}</span>
              <span style="margin-left:10px;font-size:14px;color:#303133;font-weight:900">${Number(
                item.value
              ).toExponential()}</span>
              <div style="clear:both"></div>
            </div>
            <div style="clear:both"></div>
          </div>`;
  });
  return title;
});
