/* eslint-disable vue/attributes-order */
<template>
  <div :class="{ hidden: hidden }" class="pagination-container">
    <el-pagination
      ref="pageRef"
      :current-page="currentPage"
      :page-size="pageSize"
      :layout="layout"
      :page-sizes="pageSizes"
      :total="total"
      v-bind="$attrs"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
import { scrollTo } from '@/utils/scroll-to';
import { reactive, toRefs, ref, onMounted, nextTick, watchEffect } from 'vue';

export default {
  name: 'Pagination',
  props: {
    total: {
      required: true,
      type: Number
    },
    page: {
      type: Number,
      default: 1
    },
    limit: {
      type: Number,
      default: 20
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 50];
      }
    },
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    background: {
      type: Boolean,
      default: true
    },
    autoScroll: {
      type: <PERSON>olean,
      default: true
    },
    hidden: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:page', 'update:limit', 'pagination'],
  setup(props, context) {
    // console.log(props)
    const pageRef = ref(null);
    const data = reactive({
      show: props.total >= props.limit
    });
    const currentPage = ref(props.page);

    const pageSize = ref(props.limit);

    watchEffect(() => {
      currentPage.value = props.page;
      pageSize.value = props.limit;
    });

    const handleSizeChange = val => {
      pageSize.value = val;
      context.emit('update:limit', val);
      context.emit('pagination', { page: currentPage.value, limit: val });
      if (props.autoScroll) {
        scrollTo(0, 800);
      }
    };

    const handleCurrentChange = val => {
      if (val) {
        currentPage.value = val;
        context.emit('update:page', val);
        context.emit('pagination', { page: val, limit: pageSize.value });
      }
      if (props.autoScroll) {
        scrollTo(0, 800);
      }
    };

    onMounted(() => {
      // consumeCurrentStatus()
      nextTick(() => {
        if (pageRef.value) {
          pageRef.value.internalCurrentPage = currentPage.value;
          pageRef.value.internalPageSize = pageSize.value;
        }
      });
    });

    return {
      ...toRefs(data),
      currentPage,
      pageSize,
      pageRef,
      handleSizeChange,
      handleCurrentChange
    };
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
.pagination-container {
  padding: 8px 0;
  text-align: right;
  :deep(.el-pagination) {
    padding: 0;
    .el-pager {
      color: #606266;
      font-size: 12px;
      font-weight: 500;
      li {
        background: none;
      }
    }
  }
  :deep(.el-pagination) {
    .btn-prev,
    .btn-next {
      background: none !important;
    }
  }
}
.pagination-container.hidden {
  display: none;
}
</style>
