<template>
  <!-- 检验策略 - 策略配置 -->
  <div class="testMaterial">
    <div class="flex-bc">
      <div class="searchInput">
        <el-input
          v-model="configcondition"
          v-trim
          v-focus
          placeholder="请输入物料分组/物料/工序进行搜索"
          clearable
          size="small"
          @keyup.enter="getTableList"
          @clear="getTableList"
        >
          <template #prefix>
            <i class="el-input__icon el-icon-search" />
          </template>
        </el-input>
        <el-button type="primary" size="small" @click="getTableList" @keyup.prevent @keydown.enter.prevent
          >查询</el-button
        >
        <el-button size="small" @click="reset">重置</el-button>
      </div>
      <div v-if="getPermissionBtn('addRules')" class="searchRight fr">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleAddUnit"
          @keyup.prevent
          @keydown.enter.prevent
          >添加规则</el-button
        >
      </div>
    </div>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
      @sort-change="handleSortChange"
    >
      <el-table-column
        label="物料分组"
        prop="materialGroupName"
        :min-width="colWidth.materialGroup"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="nowrap">{{ row.materialGroupName || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="物料" prop="materialName" :min-width="colWidth.material" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.materialName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="工序"
        prop="workingProcedureName"
        :min-width="colWidth.process"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.workingProcedureName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="检验类型" prop="inspectionTypeName" :min-width="colWidth.status" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.inspectionTypeName || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="getPermissionBtn('editRules') || getPermissionBtn('deleteRules')"
        label="操作"
        :width="colWidth.operation"
        prop="caozuo"
      >
        <template #default="{ row }">
          <span v-if="getPermissionBtn('editRules')" class="blue-color" @click="handleEdit(row)">编辑</span>
          <span v-if="getPermissionBtn('deleteRules')" class="blue-color" @click="handleDelete(row)">删除</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getTableList" />
    <el-dialog
      v-model="dialogVisible"
      :title="isAdd ? '添加规则 - ' + treeTitle : '检验策略：编辑规则 - ' + treeTitle"
      :close-on-click-modal="false"
      width="480px"
      custom-class="Material_dialog"
    >
      <el-form
        v-if="dialogVisible"
        ref="ruleForm"
        :model="formData"
        label-width="110px"
        size="small"
        class="inline-block"
        label-position="right"
      >
        <el-form-item label="物料分组：" prop="materialGroupName">
          <el-tag v-if="formData.materialGroupName" size="large" closable @close="deleteMaterialgroup"
            ><span class="tagClass">{{ formData.materialGroupName }}</span></el-tag
          >
          <el-button
            v-else
            type="primary"
            plain
            size="small"
            @click="handleMaterialgroup"
            @keyup.prevent
            @keydown.enter.prevent
            >请选择物料分组</el-button
          >
        </el-form-item>
        <el-form-item label="物料：" prop="materialName">
          <el-tag v-if="formData.materialName" closable size="large" @close="deleteMaterialItem"
            ><span class="tagClass">{{ formData.materialName }}</span></el-tag
          >
          <el-button
            v-else
            type="primary"
            plain
            size="small"
            @click="handleMaterialItem"
            @keyup.prevent
            @keydown.enter.prevent
            >请选择物料</el-button
          >
        </el-form-item>
        <el-form-item label="工序：" prop="workingProcedureName">
          <el-tag v-if="formData.workingProcedureName" size="large" closable @close="deleteProcess"
            ><span class="tagClass">{{ formData.workingProcedureName }}</span></el-tag
          >
          <el-button
            v-else
            type="primary"
            plain
            size="small"
            @click="handleProcess"
            @keyup.prevent
            @keydown.enter.prevent
            >请选择工序</el-button
          >
        </el-form-item>
        <el-form-item label="检验类型：" prop="inspectionType">
          <el-select v-model="formData.inspectionType" placeholder="请选择检验类型" multiple size="small" clearable>
            <el-option v-for="(val, key) in dictionaryAll['JYLX'].enable" :key="key" :label="val" :value="key" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button size="small" @click="dialogVisible = false">取 消</el-button>
          <el-button size="small" type="primary" @click="onSubmit">确 认</el-button>
        </span>
      </template>
    </el-dialog>
    <Process
      :dialog-visiable="dialogProcess"
      :is-add="isAdd"
      :detail-data="formData"
      @selectRow="getProcess"
      @closeDialog="closeProcess"
    />
    <MaterialItem
      :dialog-visiable="dialogMaterialItem"
      :detail-data="formData"
      :is-add="isAdd"
      @selectRow="getMaterialItem"
      @closeDialog="closeMaterialItem"
    />
    <MaterialGroup
      :dialog-visiable="dialogMaterialgroup"
      :detail-data="formData"
      :is-add="isAdd"
      @selectRow="getMaterialgroup"
      @closeDialog="closeMaterialgroup"
    />
  </div>
</template>

<script>
import { reactive, ref, watch, onMounted, getCurrentInstance, toRefs } from 'vue';
import { getMaterialList, addMaterial, deleteMaterial } from '@/api/strategy';
// import { getWls } from '@/api/mas'
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import Pagination from '@/components/Pagination';
import Process from '@/components/BusinessComponents/Process';
import MaterialGroup from '@/components/BusinessComponents/MaterialGroup';
import MaterialItem from '@/components/BusinessComponents/MaterialItem';
import { colWidth } from '@/data/tableStyle';
import { getDictionary } from '@/api/user';

export default {
  name: 'Material',
  components: { Pagination, Process, MaterialGroup, MaterialItem },
  props: {
    treeId: {
      type: String,
      default: ''
    },
    treeTitle: {
      type: String,
      default: ''
    },
    treeLength: {
      type: Number,
      default: 0
    },
    activeName: {
      type: String,
      default: ''
    },
    currentData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // const store = useStore()
    const state = reactive({
      isAdd: false,
      dialogVisible: false, // 添加规则弹出框
      dialogProcess: false, // 选择工序弹出框
      dialogMaterialgroup: false, // 选择物料组弹出框
      dialogMaterialItem: false, // 选择物料弹出框
      formData: {
        type: []
      }, // 添加规则表单信息
      detailData: {}, // 传递给详情页的内容
      versionId: '', // 版本id
      dictionaryAll: {
        JYLX: {
          enable: {},
          all: {}
        }
      },
      tableData: [],
      isAsc: '',
      orderBy: '',
      currentData: props.currentData,
      configcondition: '', // 模糊查询关键字
      total: 0,
      tableLoading: false, // 表格加载的loading
      treeTitle: '', // 选中树节点的name
      checkTreeId: '', // 选中的左侧树节点的id
      treeLength: 0, // 树节点的长度
      listQuery: {
        page: 1,
        limit: 20
      }
    });
    const tableKey = ref(0);
    watch(props, newValue => {
      state.treeLength = props.treeLength;
      state.currentData = props.currentData;
      state.checkTreeId = props.treeId;
      if (state.checkTreeId) {
        if (props.activeName === 'first') {
          proxy.getTableList();
          getDictionaryList();
        }
      } else {
        state.tableData = [];
      }
    });
    const reset = () => {
      state.configcondition = '';
      state.listQuery.page = 1;
      state.listQuery.limit = 20;
      proxy.getTableList();
    };
    // 列表排序
    const handleSortChange = data => {
      const { prop, order } = data;
      state.orderBy = prop;
      if (order === 'ascending') {
        state.isAsc = 'false';
      } else if (order === 'descending') {
        state.isAsc = 'true';
      } else {
        state.isAsc = '';
      }
      proxy.getTableList();
    };

    // 树节点编辑
    const showEditDialog = ref(false);
    const handleAddUnit = (type, row) => {
      if (state.treeLength === 0) {
        proxy.$message.warning('请先在左侧添加分类');
        return false;
      }
      state.dialogVisible = true;
      state.isAdd = true;
      state.formData = {
        inspectionType: []
      };
    };
    // 编辑、查看详情
    const handleEdit = row => {
      state.isAdd = false;
      state.formData = JSON.parse(JSON.stringify(row));
      state.formData.inspectionType = state.formData.inspectionType ? state.formData.inspectionType?.split(',') : [];
      state.dialogVisible = true;
    };
    // 删除
    const handleDelete = (row, type) => {
      proxy
        .$confirm('是否删除当前规则？', '确认删除', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          // 删除产品
          deleteMaterial({ configId: row.configId }).then(res => {
            if (res) {
              proxy.$message.success('删除成功');
              proxy.getTableList();
            }
          });
        })
        .catch(() => {});
    };
    const closeDeatilDrawer = val => {
      state.detailDrawer = false;
      proxy.getTableList();
    };
    const onSubmit = () => {
      if (!state.formData.materialGroupId && !state.formData.materialId && !state.formData.workingProcedureId) {
        proxy.$message.warning('请至少选择一项');
      } else {
        const params = {
          strategyId: state.checkTreeId,
          categoryCode: state.currentData.code,
          categoryId: state.currentData.id,
          versionId: state.versionId,
          ...state.formData,
          inspectionType: state.formData.inspectionType.toString()
        };
        addMaterial(params).then(res => {
          if (res) {
            state.dialogVisible = false;
            if (state.isAdd) {
              proxy.$message.success('添加成功');
            } else {
              proxy.$message.success('编辑成功');
            }
            proxy.getTableList();
          }
        });
      }
    };
    const getDictionaryList = () => {
      Object.keys(state.dictionaryAll).forEach(dictionaryItem => {
        getDictionary(dictionaryItem).then(res => {
          if (res) {
            state.dictionaryAll[dictionaryItem] = { enable: {}, all: {} };
            res.data.data.dictionaryoption.forEach(val => {
              if (val.status === 1) {
                state.dictionaryAll[dictionaryItem].enable[val.code] = val.name;
              }
              state.dictionaryAll[dictionaryItem].all[val.code] = val.name;
            });
          }
        });
      });
    };
    // mounted
    onMounted(() => {});
    const closeDrawer = value => {
      if (value.isRefresh) {
        proxy.getTableList();
      }
      state.unitVisiable = false;
    };
    const handleProcess = () => {
      state.dialogProcess = true;
    };
    const handleMaterialItem = () => {
      state.dialogMaterialItem = true;
    };
    const handleMaterialgroup = () => {
      state.dialogMaterialgroup = true;
    };
    const deleteProcess = () => {
      state.formData.workingProcedureId = '';
      state.formData.workingProcedureName = '';
      state.formData.workingProcedureCode = '';
    };
    const deleteMaterialItem = () => {
      state.formData.materialId = '';
      state.formData.materialName = '';
      state.formData.materialCode = '';
      if (!state.formData.materialGroupName) {
        state.formData.materialGroupId = '';
        state.formData.materialGroupNo = '';
      }
    };
    const deleteMaterialgroup = () => {
      state.formData.materialGroupId = '';
      state.formData.materialGroupNo = '';
      state.formData.materialGroupName = '';
      state.formData.materialId = '';
      state.formData.materialName = '';
      state.formData.materialCode = '';
    };
    const getProcess = value => {
      state.dialogProcess = false;
      state.formData = { ...state.formData, ...value };
    };
    const getMaterialItem = value => {
      state.dialogMaterialItem = false;
      state.formData = { ...state.formData, ...value };
    };
    const getMaterialgroup = value => {
      state.dialogMaterialgroup = false;
      if (state.formData.materialGroupId && value.materialGroupId !== state.formData.materialGroupId) {
        state.formData.materialId = '';
        state.formData.materialName = '';
        state.formData.materialCode = '';
      }
      state.formData = { ...state.formData, ...value };
    };
    const closeProcess = value => {
      state.dialogProcess = false;
    };
    const closeMaterialItem = value => {
      state.dialogMaterialItem = false;
    };
    const closeMaterialgroup = value => {
      state.dialogMaterialgroup = false;
    };
    return {
      ...toRefs(state),
      // getMas,
      deleteProcess,
      deleteMaterialItem,
      deleteMaterialgroup,
      closeProcess,
      closeMaterialItem,
      closeMaterialgroup,
      onSubmit,
      handleProcess,
      handleMaterialItem,
      handleMaterialgroup,
      getProcess,
      getMaterialItem,
      getMaterialgroup,
      handleAddUnit,
      closeDrawer,
      getPermissionBtn,
      formatDate,
      getNameByid,
      drageHeader,
      handleDelete,
      showEditDialog,
      closeDeatilDrawer,
      handleSortChange,
      handleEdit,
      tableKey,
      reset,
      colWidth
    };
  },
  created() {},
  methods: {
    getTableList(query) {
      const vm = this;
      if (vm.treeLength === 0) {
        vm.$message.warning('请先在左侧添加分类');
        return false;
      }
      const params = {
        configCondition: vm.configcondition,
        strategyId: vm.checkTreeId,
        isAsc: vm.isAsc,
        orderBy: vm.orderBy,
        tabIndex: '0'
      };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        vm.listQuery.page = query.page;
        vm.listQuery.limit = query.limit;
      } else {
        params.page = vm.listQuery.page.toString();
        params.limit = vm.listQuery.limit.toString();
      }
      vm.tableLoading = true;
      getMaterialList(params).then(res => {
        vm.tableLoading = false;
        if (res) {
          vm.tableData = res.data.data.configInfoList.list;
          vm.total = res.data.data.configInfoList.totalCount;
          vm.versionId = res.data.data.versionInfo.versionList[0].versionId;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
.testMaterial {
  text-align: left;
}
.flex-bc {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.searchInput {
  display: inline-block;
  .el-input {
    margin-right: 8px;
    width: 300px;
  }
}
.tagClass {
  max-width: 280px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  & + :deep(.el-icon-close) {
    top: -11px;
  }
}
</style>
<style lang="scss">
.Material_dialog {
  .el-dialog__title {
    overflow-wrap: anywhere;
  }
  .el-dialog__body {
    padding-bottom: 0;
  }
  .el-form .el-form-item {
    margin-bottom: 10px;
  }
}
</style>
