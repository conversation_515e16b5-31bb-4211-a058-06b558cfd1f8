<template>
  <!-- 选择部门人员弹窗 -->
  <el-dialog
    v-model="showDialog"
    custom-class="custom-dialog"
    title="选择人员"
    width="60%"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="closeDialogPage"
  >
    <div class="dialog-header">
      <div class="header-left">
        <el-input
          ref="inputRef"
          v-model="filterNickName"
          v-trim
          class="search"
          size="small"
          placeholder="请输入人员名称"
          prefix-icon="el-icon-search"
          clearable
          @keyup.enter="searchItem"
        />
        <el-button type="primary" size="small" @click="searchItem">查询</el-button>
      </div>
    </div>
    <div class="dialog-content">
      <el-row>
        <el-col :span="6">
          <div class="tree-container">
            <div class="tree-content">
              <el-tree
                ref="leftTreeRef"
                :data="treeData"
                node-key="id"
                :props="defaultProps"
                default-expand-all
                :expand-on-click-node="false"
                :highlight-current="true"
                draggable
                class="leftTree"
                @node-click="clickNode"
              >
                <template #default="{ node }">
                  <span>{{ node.label }}</span>
                </template>
              </el-tree>
            </div>
          </div>
        </el-col>
        <el-col :span="18">
          <div v-loading="loading" class="list-container">
            <el-row
              v-for="item in personList"
              :key="item.userId"
              class="item-content"
              style="width: 100%"
              @click="changeCheckBox(item)"
            >
              <el-col :span="1">
                <div class="left">
                  <el-checkbox v-model="item.checked" :disabled="item.disabled" @change="changeCheckBox(item)" />
                </div>
              </el-col>
              <el-col :span="23">
                <div class="main">
                  <div class="title" :class="{ 'title-checked': item.checked }">{{ item.nickname }}</div>
                </div>
              </el-col>
            </el-row>
            <el-empty v-if="personList.length === 0" :image="emptyImg" description="暂无数据" />
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="dialog-other">
      <div class="title">
        <label>已选人员</label>
        <!-- <el-button v-if="tags.length > 0" size="small" icon="el-icon-delete" @click="clear">清空</el-button> -->
      </div>
      <div v-if="tags.length > 0" class="select-items">
        <el-tag v-for="(tag, index) in tags" :key="tag.userId" size="small" closable @close="closeTag(tag, index)">
          {{ tag.nickname || tag.username }}
        </el-tag>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialogPage">取 消</el-button>
        <el-button v-if="isShowRecord" type="primary" @click="dialogRecord = true">调整记录</el-button>
        <el-button type="primary" @click="dialogSuccess">确定选择</el-button>
      </span>
    </template>
    <!-- 调整记录 -->
    <DialogAdjustmentRecord :dialog-visible="dialogRecord" @closeDialog="handleCloseDialog" />
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, nextTick } from 'vue';
import { getDepartTree, getMemberTable } from '@/api/departManagement';
import { formatTree } from '@/utils/formatJson';
import DialogAdjustmentRecord from '../../views/comprehensive-management/persondutytime-management/DialogAdjustmentRecord.vue';
import emptyImg from '@/assets/img/empty-data.png';
import { ElMessage } from 'element-plus';

export default {
  name: 'DialogMulDepartPerson',
  components: { DialogAdjustmentRecord },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    alreadySelect: {
      type: Object,
      default: function () {
        return {};
      }
    },
    isShowRecord: {
      type: Boolean, // 是否显示调整记录
      default: false
    }
  },
  emits: ['selectData'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    const state = reactive({
      treeNodeData: {},
      dialogRecord: false,
      isShowRecord: props.isShowRecord,
      tableData: [],
      leftTreeRef: ref(),
      inputRef: ref(),
      filterNickName: '',
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      tags: [],
      showDialog: false,
      treeData: [],
      treeDetail: [],
      personList: [],
      oldPersonList: [],
      loading: false,
      selectedItemId: ''
    });

    watch(props, newValue => {
      if (newValue.dialogVisible) {
        state.filterNickName = '';
        state.oldPersonList = [];
        state.personList = [];
        state.treeNodeData = {};
        state.tags = JSON.parse(JSON.stringify(newValue.alreadySelect));
        nextTick(() => {
          state.inputRef.focus();
        });
        state.showDialog = newValue.dialogVisible;
        getDepartTreeList();
      }
    });
    // 部门树列表
    const getDepartTreeList = () => {
      state.loading = true;
      getDepartTree({}).then(function (res) {
        if (res) {
          state.loading = false;
          const data = formatTree(res.data.data);
          state.treeData = JSON.parse(JSON.stringify(data));
          if (!state.treeNodeData.id) {
            state.treeNodeData = state.treeData[0];
          }
          if (state.treeNodeData.id) {
            getPersonList();
            nextTick(() => {
              state.leftTreeRef.setCurrentKey(state.treeNodeData.id, true);
            });
          }
        }
      });
    };
    // 获取人员列表
    const getPersonList = () => {
      state.loading = true;
      const params = {
        departmentId: state.treeNodeData.id === 'all' ? '' : state.treeNodeData.id,
        limit: '-1',
        page: '1'
      };
      getMemberTable(params).then(res => {
        state.loading = false;
        if (res) {
          const data = res.data.data.list;
          state.personList = data;
          state.personList.forEach(item => {
            if (
              state.tags.some(val => {
                return val.userId === item.userId;
              })
            ) {
              // 已经保存过
              item.checked = true;
            }
          });
          state.oldPersonList = JSON.parse(JSON.stringify(state.personList));
        }
      });
    };
    // 点击树节点
    const clickNode = data => {
      state.treeNodeData = data;
      getPersonList();
    };

    // 确定选择
    const dialogSuccess = () => {
      if (state.tags.length) {
        context.emit('selectData', state.tags);
        state.showDialog = false;
      } else {
        ElMessage.warning('请至少选择一条数据');
      }
    };
    // 取消选择
    const closeDialogPage = () => {
      context.emit('selectData', false);
      state.showDialog = false;
    };

    // 数据过滤
    const filterItem = item => {
      item.departmentId = state.treeNodeData.id;
      state.tags.push(item);
    };
    // 单行切换
    const changeCheckBox = item => {
      item.checked = !item.checked;
      const index = state.tags.findIndex(val => {
        return val.userId === item.userId;
      });
      if (index == -1) {
        // 没勾选
        state.tags.push(item);
      } else {
        // 勾选了，需要删除
        state.tags.splice(index, 1);
      }
    };
    // 关闭tags
    const closeTag = (tag, index) => {
      state.tags.splice(index, 1);
      if (tag.id || tag?.dayWorkPoints) {
        // 说明以前保存过,需要添加的删除的参数里面
        const index = state.personList.findIndex(item => item.userId === tag.userId);
        if (index !== -1) {
          state.personList[index].checked = false;
        }
      }
    };
    const searchItem = () => {
      if (state.filterNickName) {
        state.personList = [];
        state.personList = state.personList.concat(
          state.oldPersonList.filter(item => {
            return JSON.stringify(item.nickname).indexOf(state.filterNickName) !== -1;
          })
        );
      } else {
        state.personList = state.oldPersonList;
      }
    };

    const filterClear = () => {
      state.personList = state.treeDetail;
    };
    const handleCloseDialog = () => {
      state.dialogRecord = false;
    };
    return {
      ...toRefs(state),
      getPersonList,
      handleCloseDialog,
      emptyImg,
      searchItem,
      dialogSuccess,
      closeDialogPage,
      filterItem,
      clickNode,
      closeTag,
      changeCheckBox,
      filterClear
    };
  }
};
</script>
<style lang="scss">
@import '@/styles/dialog.scss';
</style>
<style lang="scss" scoped>
@import '@/styles/tree.scss';

.dialog-content {
  .tree-container {
    .tree-content {
      height: calc(100vh - 520px);
      overflow-y: auto;
      padding-left: 0;
    }
  }
  .list-container {
    height: calc(100vh - 480px);
    overflow-y: auto;
    .el-radio-group {
      width: 100%;
    }
    .item-content {
      padding-bottom: 0 !important;
      .main {
        width: 100%;
        .title {
          padding-bottom: 8px;
        }
        .item-list {
          width: 100%;
          border-left: none;
          .item-box {
            height: 16px;
            margin-right: 20px !important;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
}
</style>
