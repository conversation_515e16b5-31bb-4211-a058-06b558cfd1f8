/**
 * 本文件仅用做存储固定不变的行业术语字典
 * 如存储的字典项可能需要修改，请勿使用本文件存储，
 * 而应将其存在数据库中，使用@/api/apidictionary.js中的API调用。
 */

import inspection_icon_1 from '@/assets/img/inspection_icon_1.svg';
import inspection_icon_2 from '@/assets/img/inspection_icon_2.svg';
import inspection_icon_3 from '@/assets/img/inspection_icon_3.svg';

/**
 * 检验类型
 */
export const InspectionType = [
  { id: 1, name: '采购入库', code: '1', icon: inspection_icon_1 },
  { id: 2, name: '过程检验', code: '2', icon: inspection_icon_2 },
  { id: 3, name: '完工检验', code: '3', icon: inspection_icon_3 }
];

export const InspectionTypeAll = [
  { id: '', code: '', name: '全部' },
  { id: 1, code: '1', name: '采购入库' },
  { id: 2, code: '2', name: '过程检验' },
  { id: 3, code: '3', name: '完工检验' }
];

/**
 * 租户类型
 */
export const tenantTypeOptions = [
  { id: 1, name: '工厂实验室' },
  { id: 0, name: '第二方企业' },
  { id: 2, name: '第三方实验室' }
];
// tenantInfo.type
// type === 0, // 国家电网等甲方实验室
// type === 1, // 工厂侧实验室
// type === 2, // 第三方检测机构

/**
 * 委托类型
 */
export const taskType = [
  { id: 0, name: '委托试验', icon: inspection_icon_1 },
  { id: 1, name: '型式试验', icon: inspection_icon_2 },
  { id: 2, name: '能力验证', icon: inspection_icon_3 }
];

/**
 * 试验类型
 */
export const testType = [
  { id: 0, name: '型式试验' },
  { id: 1, name: '全性能' },
  { id: 2, name: '部分性能' }
];

/**
 * 系统常量
 */
export const _SYS = 'CX-LIMS';

/**
 * 结论类型
 */
export const reportTypes = [
  { code: 0, name: '合格' },
  { code: 1, name: '不合格' },
  { code: 2, name: '不判定' }
];
