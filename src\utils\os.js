import os from 'os';

export const getIP = () => {
  const interfaces = os.networkInterfaces();
  console.log('interfaces:', interfaces);
  for (var devName in interfaces) {
    const iface = interfaces[devName];
    console.log('iface:', iface);
    for (let i = 0; i < iface.length; i++) {
      const alias = iface[i];
      console.log('alias:', alias);
      if (
        alias.family === 'IPv4' &&
        alias.address !== '127.0.0.1' &&
        !alias.internal &&
        alias.netmask === '*************'
      ) {
        return alias.address;
      }
    }
  }
};
// 获取本机的网络ip地址
const jsonpCallback = res => {
  var ip = res.Ip;
  console.log(ip);
};

export const getIntnetIP = () => {
  var JSONP = document.createElement('script');
  JSONP.type = 'text/javascript';
  JSONP.src = 'http://chaxun.1616.net/s.php?type=ip&v=&output=json&callback=' + jsonpCallback;
  document.getElementsByTagName('head')[0].appendChild(JSONP);
};
