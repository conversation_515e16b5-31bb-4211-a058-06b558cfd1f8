import request from '@/utils/request';
import qs from 'qs';
// import { getIntnetIP } from '@/utils/os'
const postHeaders = {
  'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
};

// 获取系统ip
export function getIp() {
  return request({
    url: 'http://pv.sohu.com/cityjson?ie=utf-8',
    method: 'get'
  });
}
// 调用登录接口以后，异步往这个接口里插入数据
// 入参：
// {
//   "ie":能否用js获取用户当前的浏览器种类
//   "address"：js能否根据IP获取地址
//   }
export function saveSysLoginLog(data) {
  return request({
    url: '/api-user/user/sysloginlog/save',
    method: 'post',
    data
  });
}
// 查询日志列表
export function getSysLogList(data) {
  return request({
    url: '/api-user/user/sysloginlog/list',
    method: 'post',
    data
  });
}
// 企业管理登录日志查询日志列表
export function getLogList(data) {
  return request({
    url: '/api-user/user/sysloginlog/listTenant',
    method: 'post',
    data
  });
}

// 退出系统（登出操作）
export function removeSysToken(data) {
  data = qs.stringify(data);
  return request({
    url: '/api-uaa/oauth/remove/token',
    headers: postHeaders,
    method: 'post',
    data
  });
}
