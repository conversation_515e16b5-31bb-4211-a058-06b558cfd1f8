import request from '@/utils/request';

/**
 * 获取检测项目检测报告模板信息
 * @param {string} capabilityId
 */
export function capabilityreporttemplate(capabilityId) {
  return request({
    url: `/api-capabilitystd/capability/capabilityreporttemplate/findByCapabilityId/${capabilityId}`,
    method: 'get'
  });
}
/**
 * 保存检测项目检测报告模板信息
 */
export function reporttemplateSave(data) {
  return request({
    url: `/api-capabilitystd/capability/capabilityreporttemplate/saveOrUpdate`,
    method: 'post',
    data
  });
}

/**
 * 业务数据和模板映射关系查询
 */
export function findByCapabilityIdParamer(data) {
  return request({
    url: `/api-capabilitystd/capability_template_field_mapping/list`,
    method: 'post',
    data
  });
}
/**
 * 业务数据和模板映射关系保存
 */
export function saveParamer(data) {
  return request({
    url: `/api-capabilitystd/capability_template_field_mapping/saveOrUpdate`,
    method: 'post',
    data
  });
}
/**
 * 业务数据和模板映射关系删除
 */
export function deleteParamer(data) {
  return request({
    url: `/api-capabilitystd/capability_template_field_mapping/delete`,
    method: 'post',
    data
  });
}
