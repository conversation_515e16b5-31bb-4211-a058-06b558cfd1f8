<template>
  <div v-if="isShow" class="modelTable">
    <!-- 标准的表格 -->
    <el-table
      ref="tableRef"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      size="medium"
      highlight-current-row
      class="dark-table format-height-table base-table format-height-table2"
      @row-click="onRowClick"
      @header-dragend="drageHeader"
    >
      <el-table-column label="序号" width="70px" align="center">
        <template #default="{ $index }">
          {{ $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="标准号" prop="standardNo" min-width="120px" sortable>
        <template #default="{ row }">
          <span>{{ row.standardNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标准名称" prop="name" min-width="180px" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.name || '' }}
        </template>
      </el-table-column>
      <el-table-column label="英文名称" prop="englishName" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.englishName || '--' }}
        </template>
      </el-table-column>
      <el-table-column label="生效时间" prop="standardEffectiveDate" width="140px">
        <template #default="{ row }">
          {{ formatDate(row.standardEffectiveDate) || '' }}
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getTableList" /> -->
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch } from 'vue';
import { formatDate } from '@/utils/formatTime';
// import { saveProduct, copyProduct, updateProduct } from '@/api/testBase'
import { drageHeader } from '@/utils/formatTable';
// import { defaults } from 'codemirror'
export default {
  name: 'StandTable',
  props: {
    tableData: {
      type: Array,
      default: function () {
        return [];
      }
    },
    isShow: {
      type: Boolean,
      default: false
    },
    searchParam: {
      type: String,
      default: ''
    }
  },
  emits: ['selectData'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    watch(props, newValue => {
      state.isShow = props.isShow;
      if (state.isShow) {
        state.tableData = props.tableData;
        state.searchKey = props.searchParam;
      }
    });
    const state = reactive({
      tableData: [],
      searchKey: '',
      tableRef: ref(),
      tableLoading: false,
      isShow: false
    });
    const onRowClick = row => {
      context.emit('selectData', row);
    };
    return {
      ...toRefs(state),
      formatDate,
      drageHeader,
      onRowClick
    };
  },
  computed: {},
  created() {}
};
</script>

<style lang="scss" scoped>
.modelTable {
  margin-top: 10px;
  height: 100%;
}
:deep(.el-table.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 260px);
    overflow: auto;
  }
}
</style>
