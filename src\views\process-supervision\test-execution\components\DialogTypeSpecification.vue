<template>
  <el-dialog
    v-model="dialogVisiable"
    title="型号规格"
    :width="800"
    :close-on-click-modal="false"
    custom-class="dialog-table"
    @close="closedialog"
  >
    <el-table
      ref="multipleTableRef"
      :data="tableData"
      size="medium"
      fit
      border
      class="dark-table base-table"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column type="selection" :min-width="colWidth.checkbox" />
      <el-table-column label="型号规格" :min-width="colWidth.date" prop="hdrq">
        <template #default="{ row }">
          {{ row.typeName || '--' }}
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="medium" @click="closedialog()">取 消</el-button>
        <el-button
          :loading="submitLoading"
          size="medium"
          type="primary"
          @click="onSubmit"
          @keyup.prevent
          @keydown.enter.prevent
          >确 认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { colWidth } from '@/data/tableStyle';
import { getNameByid } from '@/utils/common';
import { ElMessage } from 'element-plus';
import { saveOrUpdateExperimentProdType } from '@/api/execution';

export default {
  name: 'DialogRecipients',
  components: {},
  props: {
    dialogShow: {
      type: Boolean,
      default: function () {
        return false;
      }
    },
    jsonData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const route = useRoute();
    const state = reactive({
      dialogVisiable: false,
      tableData: [],
      selectTable: [],
      multipleTableRef: ref(null),
      submitLoading: false
    });
    watch(props, newValue => {
      state.dialogVisiable = newValue.dialogShow;
      if (state.dialogVisiable) {
        state.tableData = [];
        state.selectTable = [];
        props.jsonData.sampleProdTypeList.forEach(item => {
          state.tableData.push({ typeName: item });
        });
        props.jsonData.experimentProdTypeList.forEach(val => {
          state.selectTable.push({ typeName: val });
          nextTick(() => {
            if (state.multipleTableRef) {
              const rowIndex = state.tableData.findIndex(item => item.typeName === val);
              state.tableData[rowIndex].isCheck = true;
              state.multipleTableRef.toggleRowSelection(state.tableData[rowIndex], true);
            }
          });
        });
      }
    });
    // 列表接口
    const closedialog = () => {
      state.dialogVisiable = false;
      context.emit('closeDialog', false);
    };
    // 仪器设备表单选中的数据
    const handleSelectionChange = value => {
      state.selectTable = value;
    };

    const handleRowClick = row => {
      const rowIndex = state.tableData.findIndex(item => item.typeName === row.typeName);
      if (rowIndex !== -1) {
        row.isCheck = !row.isCheck;
        state.multipleTableRef.toggleRowSelection(state.tableData[rowIndex], row.isCheck);
      }
    };

    const onSubmit = () => {
      state.submitLoading = true;
      // 保存的仪器设备
      if (state.selectTable.length === 0) {
        ElMessage.warning('请至少勾选一条数据');
        return false;
      }
      const params = {
        experimentId: route.query.experimentId,
        experimentProdTypeList: state.selectTable.map(item => {
          return item.typeName;
        }),
        sampleId: route.query.samplesId
      };
      saveOrUpdateExperimentProdType(params).then(res => {
        if (res) {
          ElMessage.success('操作成功');
          state.dialogVisiable = false;
          context.emit('closeDialog', params.experimentProdTypeList);
        }
        state.submitLoading = false;
      });
    };

    return {
      ...toRefs(state),
      props,
      onSubmit,
      getNameByid,
      closedialog,
      colWidth,
      handleSelectionChange,
      handleRowClick
    };
  }
};
</script>

<style lang="scss" scoped>
.dialog-table {
  .selectBh {
    width: 60%;
  }
}
</style>
