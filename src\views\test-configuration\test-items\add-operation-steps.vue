<template>
  <!-- 试验步骤新增、编辑、查看 -->
  <DetailLayout :main-offset-top="110">
    <template #page-header>
      <div class="header-flex flex-between">
        <div class="page-title">
          <span class="title">{{ itemTitle }}</span>
        </div>
        <div v-if="!disabled" class="btn-group">
          <el-button v-if="isShowDeleteBtn" :loading="detailLoading" @click="deleteOperationSteps(currentDetail)"
            >删除草稿</el-button
          >
          <el-button :loading="detailLoading" @click="saveOperationSteps">保 存</el-button>
          <el-button :loading="detailLoading" type="primary" @click="pushOperationSteps(currentDetail)"
            >发 布</el-button
          >
        </div>
      </div>
      <div class="page-header-group">
        <el-row v-loading="detailLoading" :gutter="20">
          <el-col :span="8" class="item">
            <span class="title">项目名称：</span>
            <div class="txt ellipsis">
              {{ itemName ? itemName : '--' }}
            </div>
          </el-col>
          <el-col :span="4" class="item item-version">
            <span class="title">当前版本：</span>
            <el-tag v-if="currentDetail.version" size="small">{{ currentDetail.version }}</el-tag>
            <div v-if="currentDetail.status !== 1">
              <span> 草稿 </span>
              <el-tooltip effect="dark" placement="top" content="当前为草稿状态，与已发布版本内容存在部分差别。">
                <span class="el-icon-info" />
              </el-tooltip>
            </div>
          </el-col>
          <el-col v-if="currentList.length > 0" :span="12" class="item item-version">
            <div>
              <span class="title">历史版本：</span>
              <el-tag
                v-for="item in currentList"
                :key="item.id"
                size="small"
                class="version-tag"
                :type="currentDetail.id === item.id ? '' : 'info'"
                @click="changeVersion(item)"
                >{{ item.version }}</el-tag
              >
            </div>
          </el-col>
        </el-row>
      </div>
    </template>
    <div v-loading="detailLoading" class="panel-content">
      <el-form
        ref="formRef"
        :model="currentDetail"
        class="formDataSample"
        :inline="true"
        :rules="addRules"
        :label-width="labelWidth"
        :label-position="position"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="试验方法：" prop="name">
              <el-tag v-if="disabled" size="small" type="error">{{ currentDetail.name }}</el-tag>
              <el-input v-else v-model="currentDetail.name" placeholder="请输入试验方法" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="disabled ? '附件：' : '附件上传：'">
              <div v-if="disabled && fileList.length === 0">暂无附件</div>
              <upload-files
                :file-list="fileList"
                :disabled="disabled"
                @upload="httpRequest"
                @delete-file="deleteFile"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="试验要求" prop="description">
              <div v-if="disabled">{{ currentDetail.description || '--' }}</div>
              <el-input v-else v-model="currentDetail.description" type="textarea" placeholder="请输入试验要求" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="操作步骤：" prop="step" style="margin-top: 12px">
              <MarkdownEditor
                :value="currentDetail.step"
                :toolbar="toolbar"
                :height="'calc(100vh - 350px)'"
                :disabled="disabled"
                @getContent="getContent"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </DetailLayout>
</template>

<script>
import { reactive, toRefs, ref } from 'vue';
import { useRoute } from 'vue-router';
import { formatDate } from '@/utils/formatTime';
import { getNameByid, getNamesByid } from '@/utils/common';
// import router from '@/router'
// import { getLoginInfo } from '@/utils/auth'
// import { mapGetters } from 'vuex'
import { getToken } from '@/utils/auth';
import { ElMessage, ElMessageBox } from 'element-plus';
import DetailLayout from '@/components/DetailLayout';
import MarkdownEditor from '@/components/MarkdownEditor/index.vue';
import {
  detailMethodById,
  saveOrUpdateMethod,
  deleteMethod,
  publishMethod,
  detailMethodList,
  uploadMoreMethod
} from '@/api/testItem';
import { capabilityAttachmentFileListUploadUrl } from '@/api/uploadAction';
import _ from 'lodash';
import UploadFiles from '@/components/Upload/upload-files.vue';

export default {
  name: 'AddOrUpdateOperationSteps',
  components: { DetailLayout, MarkdownEditor, UploadFiles },
  setup() {
    // const { proxy } = getCurrentInstance()
    const route = useRoute();
    const state = reactive({
      itemTitle: '新增试验方法',
      itemName: '暂无',
      itemVersion: '草稿',
      isShowDeleteBtn: false,
      currentDetail: {
        name: '',
        step: '',
        status: 0,
        capabilityId: route.params.capabilityId,
        fileIds: []
      },
      currentList: [],
      position: 'top',
      labelWidth: '110px',
      addRules: {
        name: [{ required: true, message: '请选择试验方法' }],
        step: [{ required: true, message: '请输入操作步骤' }]
      },
      toolbar:
        'formatselect | forecolor backcolor | fontselect | fontsizeselect | bullist numlist | outdent indent lineheight | undo redo | bold italic underline strikethrough subscript superscript removeformat charmap hr selectall | alignleft aligncenter alignright alignjustify | table image link pagebreak | code preview fullscreen',
      disabled: false,
      uploadURL: capabilityAttachmentFileListUploadUrl(),
      headerConfig: {
        Authorization: getToken()
      },
      fileList: [],
      oldFileList: [],
      uploadFilesRef: ref(),
      detailLoading: false,
      filesData: {},
      formRef: {},
      overLimt: false,
      isCharacter: false,
      wordCount: 0
    });

    const validateForm = () => {
      if (!state.currentDetail.name) {
        ElMessage.warning('请选择试验方法！');
        return;
      }
      if (!state.currentDetail.step) {
        ElMessage.warning('请输入操作步骤');
        return;
      }
    };

    const validateWordLimit = () => {
      const wordCountEle = document.getElementsByClassName('tox-statusbar__wordcount');
      if (wordCountEle.length > 0) {
        const wordCountContent = wordCountEle.item(0).textContent;
        if (wordCountContent.includes('字符')) {
          state.isCharacter = true;
          state.wordCount = Number(wordCountContent.split(' ')[0]);
          if (state.wordCount && state.wordCount > 20000) {
            state.overLimt = true;
          } else {
            state.overLimt = false;
          }
        } else if (wordCountContent.includes('字')) {
          state.isCharacter = false;
          state.wordCount = Number(wordCountContent.split(' ')[0]);
          if (state.wordCount && state.wordCount > 10000) {
            state.overLimt = true;
          } else {
            state.overLimt = false;
          }
        } else {
          state.overLimt = false;
          return true;
        }
      }
      if (state.overLimt) {
        if (state.isCharacter) {
          ElMessage.warning(`当前输入的字符数为${state.wordCount}, 超过最大字符数限制20000, 请检查!`);
        } else {
          ElMessage.warning(`当前输入的字符数为${state.wordCount}, 超过最大字数限制10000, 请检查!`);
        }
        return false;
      }
      return true;
    };

    // 保存
    const saveOperationSteps = () => {
      state.formRef.validate(valid => {
        if (valid) {
          if (validateWordLimit()) {
            state.detailLoading = true;
            saveOrUpdateMethod(state.currentDetail).then(res => {
              state.detailLoading = false;
              if (res !== false) {
                // console.log(res.data)
                ElMessage.success('保存成功！');
                // console.log(proxy)
                setTimeout(() => {
                  window.close();
                }, 1000);
              }
            });
          }
        } else {
          validateForm();
        }
      });
    };

    // 发布
    const pushOperationSteps = row => {
      state.formRef.validate(valid => {
        if (valid) {
          if (validateWordLimit()) {
            state.detailLoading = true;
            saveOrUpdateMethod(state.currentDetail).then(res => {
              state.detailLoading = false;
              if (res !== false) {
                state.detailLoading = true;
                publishMethod(res.data.data).then(res1 => {
                  if (res1 !== false) {
                    ElMessage.success('发布成功！');
                    setTimeout(() => {
                      window.close();
                    }, 1000);
                  }
                });
              }
            });
          }
        } else {
          validateForm();
        }
      });
    };
    // 删除草稿
    const deleteOperationSteps = row => {
      ElMessageBox({
        title: '',
        message: '是否确认删除？删除后不可恢复',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          state.detailLoading = true;
          deleteMethod(row.id).then(res => {
            state.detailLoading = false;
            if (res !== false) {
              ElMessage.success('删除成功！');
              setTimeout(() => {
                window.close();
              }, 1000);
            }
          });
          // console.log(row)
        })
        .catch(() => {});
    };
    // 操作步骤
    const getContent = value => {
      // console.log(value)
      state.currentDetail.step = value;
    };
    // 获取试验方法详情
    const getDetailMethodInfo = id => {
      if (id !== '0') {
        state.detailLoading = true;
        detailMethodById(id).then(res => {
          state.detailLoading = false;
          if (res !== false) {
            // console.log(res.data.data)
            state.currentDetail = res.data.data;
            state.fileList = state.currentDetail.fileInfo;
            state.oldFileList = JSON.parse(JSON.stringify(state.currentDetail.fileInfo));
            state.isShowDeleteBtn = state.currentDetail.status === 0 && route.params.type === 'edit';
          }
        });
        // 获取附件列表 详情接口把附件列表返回了，这边这个接口暂时就不用了
        // getAttachmentById(id).then(res => {
        //   if (res !== false) {
        //     console.log(res.data.data)
        //   }
        // })
      }
    };
    // 获取查看列表
    const getDetailMethodList = methodId => {
      return new Promise(resolve => {
        state.detailLoading = true;
        detailMethodList(methodId).then(res => {
          state.detailLoading = false;
          if (res !== false) {
            // console.log(res.data.data)
            state.currentList = res.data.data;
            resolve(state.currentList);
          } else {
            resolve([]);
          }
        });
      });
    };
    // 切换版本号
    const changeVersion = item => {
      // console.log(item)
      getDetailMethodInfo(item.id);
    };
    // 附件上传
    // 批量上传使用el-upload有点坑，这边不用下面部分可以删除 --start--
    const handleRemove = file => {
      // console.log(file)
      // console.log(state.uploadFilesRef)
      var fileList = state.uploadFilesRef.uploadFiles;
      var files = _.filter(fileList, f => {
        return file.name !== f.name;
      });
      // console.log(files)
      state.uploadFilesRef.uploadFiles = files;
    };
    const handleExceed = () => {
      ElMessage.warning('附件可以批量上传！');
    };
    const beforeUpload = file => {
      // console.log(file)
      // debugger;
      const fileSize = file.size / 1024 / 1024 < 50;
      if (!fileSize) {
        ElMessage.error('上传附件大小不能超过50M');
        return false;
      } else {
        return true;
      }
    };
    const handleUploadSuccess = (res, file, fileList) => {
      // debugger;
      if (res.code === 200) {
        // console.log(file)
        state.filesData.fileId = res.data.fileId;
        state.filesData.fileName = res.data.fileName;
        state.filesData.fileRemoteName = res.data.fileRemoteName;
      }
    };
    // --end--
    // 批量上传文件
    const httpRequest = files => {
      // console.log(files)
      uploadMoreMethod(files, callback => {
        // console.log(callback)
      }).then(res => {
        if (res !== false) {
          // console.log(res.data.data)
          state.fileList.push(...res.data.data);
          const newItem = _.xorBy(state.fileList, state.oldFileList, 'id');
          state.currentDetail.fileIds = newItem.map(n => {
            return n.id;
          });
          // console.log(state.currentDetail)
          ElMessage.success(res.data.message);
        }
      });
      // console.log(state.uploadFilesRef.uploadFiles)
      // var uploadFiles = state.uploadFilesRef.uploadFiles
      // var upData = new FormData()
      // for (let i = 0; i < uploadFiles.length; i++) {
      //   upData.append('file', uploadFiles[i].raw)
      // }
      // upData.append('contentType', 'multipart/form-data;')
    };

    const deleteFile = fileIndex => {
      state.fileList.splice(fileIndex, 1);
      ElMessage.success('删除附件成功！');
    };

    return {
      ...toRefs(state),
      formatDate,
      getNameByid,
      getNamesByid,
      saveOperationSteps,
      pushOperationSteps,
      deleteOperationSteps,
      getContent,
      getDetailMethodInfo,
      getDetailMethodList,
      changeVersion,
      handleRemove,
      handleExceed,
      beforeUpload,
      handleUploadSuccess,
      httpRequest,
      deleteFile
    };
  },
  computed: {},
  async created() {
    // console.log(this.$route.params)
    const item = this.$route.params;
    this.itemName = item.capabilityName;
    if (item.type === 'add') {
      this.itemTitle = '新增试验方法';
    } else if (item.type === 'edit') {
      this.itemTitle = '编辑试验方法';
    } else {
      this.itemTitle = '试验方法详情';
      this.disabled = true;
      this.toolbar = false;
      await this.getDetailMethodList(item.methodId);
    }
    this.getDetailMethodInfo(item.id);
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-form .el-form-item) {
  margin-bottom: 12px !important;
}
.formDataSample {
  .el-row {
    width: 100%;
  }
}
::v-deep(.page-detail-main) {
  width: 100% !important;
  margin: 0 !important;
  padding: 20px 24px;
  overflow-y: auto;
  height: calc(100vh - 110px);
}

.header-flex {
  padding-top: 10px;
}

.page-header-group {
  padding-top: 0;
  .el-col {
    text-align: left;
    margin: 4px 0;
    .title {
      height: 32px;
      line-height: 32px;
      color: $tes-font2;
    }
  }
  .item {
    display: flex;
    align-items: center;
    .txt {
      max-width: calc(100% - 90px);
      height: 32px;
      line-height: 32px;
    }
  }
  .item-version {
    .el-tag {
      margin-right: 10px;
    }
    .version-tag {
      cursor: pointer;
    }
  }
}
.panel-content {
  background: #fff;
  padding: 20px;
  .name-list {
    .el-tag {
      margin: 0 10px 10px 0;
    }
  }
  ::v-deep(.el-form .el-form-item .el-form-item__content) {
    max-width: 100% !important;
  }
}
</style>
