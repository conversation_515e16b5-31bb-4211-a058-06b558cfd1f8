<template>
  <div ref="componentRef" class="simple-virtual-select">
    <el-input
      ref="inputRef"
      v-model="displayValue"
      :placeholder="placeholder"
      :size="size"
      :disabled="disabled"
      :clearable="clearable"
      readonly
      @click="toggleDropdown"
      @clear="handleClear"
    >
      <template #suffix>
        <i class="el-icon-arrow-down select-arrow" :class="{ 'is-reverse': dropdownVisible }" />
      </template>
    </el-input>

    <!-- 下拉框 -->
    <div v-if="dropdownVisible" class="select-dropdown">
      <!-- 搜索框 -->
      <div v-if="filterable" class="select-filter" @click.stop>
        <el-input
          ref="searchInputRef"
          v-model="searchText"
          size="small"
          placeholder="搜索选项"
          prefix-icon="el-icon-search"
          clearable
          @clear="searchText = ''"
          @click.stop
        />
        <div class="search-debug">
          搜索: "{{ searchText }}" - 找到 {{ filteredOptions.length }} / {{ options.length }} 个结果
        </div>
      </div>

      <!-- 虚拟滚动列表 -->
      <div ref="listRef" class="select-list" @scroll="handleScroll">
        <div :style="{ height: totalHeight + 'px', position: 'relative' }">
          <div
            v-for="item in visibleItems"
            :key="item.value"
            class="select-option"
            :class="{
              'is-selected': item.label === modelValue,
              'is-highlighted': item.label === highlightedValue
            }"
            :style="{
              position: 'absolute',
              top: item.top + 'px',
              width: '100%',
              height: itemHeight + 'px',
              lineHeight: itemHeight + 'px'
            }"
            @click="selectOption(item)"
            @mouseenter="highlightedValue = item.label"
          >
            {{ item.label }}
            <i v-if="item.label === modelValue" class="el-icon-check option-check" />
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredOptions.length === 0" class="select-empty">
        {{ searchText ? '无匹配数据' : '暂无数据' }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';

export default {
  name: 'SimpleVirtualSelect',
  props: {
    modelValue: {
      type: [String, Number],
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    size: {
      type: String,
      default: 'small'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: false
    },
    filterable: {
      type: Boolean,
      default: true
    },
    itemHeight: {
      type: Number,
      default: 34
    },
    maxHeight: {
      type: Number,
      default: 200
    }
  },
  emits: ['update:modelValue', 'change', 'clear'],
  setup(props, { emit }) {
    const componentRef = ref(null);
    const inputRef = ref(null);
    const listRef = ref(null);
    const searchInputRef = ref(null);
    const dropdownVisible = ref(false);
    const searchText = ref('');
    const highlightedValue = ref('');
    const scrollTop = ref(0);

    // 计算显示值
    const displayValue = computed(() => {
      if (!props.modelValue) return '';
      const option = props.options.find(item => item.label === props.modelValue);
      return option ? option.label : props.modelValue;
    });

    // 搜索过滤
    const filteredOptions = computed(() => {
      if (!searchText.value) {
        return props.options;
      }

      const query = searchText.value.toLowerCase();

      const filtered = props.options.filter(option => {
        if (!option || !option.label) return false;
        const matches = option.label.toLowerCase().includes(query);
        return matches;
      });

      return filtered;
    });

    // 虚拟滚动计算
    const totalHeight = computed(() => {
      return filteredOptions.value.length * props.itemHeight;
    });

    const visibleCount = computed(() => {
      return Math.ceil(props.maxHeight / props.itemHeight) + 2;
    });

    const startIndex = computed(() => {
      return Math.max(0, Math.floor(scrollTop.value / props.itemHeight));
    });

    const endIndex = computed(() => {
      return Math.min(startIndex.value + visibleCount.value, filteredOptions.value.length);
    });

    const visibleItems = computed(() => {
      const items = filteredOptions.value.slice(startIndex.value, endIndex.value).map((option, index) => ({
        ...option,
        top: (startIndex.value + index) * props.itemHeight
      }));

      // if (items.length === 0 && filteredOptions.value.length > 0) {
      // }

      return items;
    });

    // 切换下拉框
    const toggleDropdown = () => {
      if (props.disabled) return;

      // 如果要打开下拉框，先关闭其他所有下拉框
      if (!dropdownVisible.value) {
        // 触发全局事件，关闭其他下拉框
        document.dispatchEvent(
          new CustomEvent('closeAllSelects', {
            detail: { except: componentRef.value }
          })
        );
      }

      dropdownVisible.value = !dropdownVisible.value;

      if (dropdownVisible.value) {
        nextTick(() => {
          // 强制重置滚动位置到顶部
          scrollTop.value = 0;
          if (listRef.value) {
            listRef.value.scrollTop = 0;
          }

          // 再次使用 nextTick 确保 DOM 更新完成
          nextTick(() => {
            // 如果有选中值，滚动到选中项
            if (props.modelValue) {
              scrollToSelected();
            }

            // 聚焦搜索框
            if (props.filterable && searchInputRef.value) {
              searchInputRef.value.focus();
            }
          });
        });
      } else {
        searchText.value = '';
      }
    };

    // 处理搜索输入
    const handleSearchInput = () => {
      // 重置滚动位置
      scrollTop.value = 0;
      if (listRef.value) {
        listRef.value.scrollTop = 0;
      }
    };

    // 选择选项
    const selectOption = option => {
      emit('update:modelValue', option.label);
      emit('change', option.label);

      // 关闭下拉框并重置状态
      dropdownVisible.value = false;
      searchText.value = '';

      // 重置滚动位置，为下次打开做准备
      scrollTop.value = 0;
      if (listRef.value) {
        listRef.value.scrollTop = 0;
      }
    };

    // 处理清空
    const handleClear = () => {
      emit('update:modelValue', '');
      emit('change', '');
      emit('clear');
    };

    // 处理搜索清空
    const handleSearchClear = () => {
      searchText.value = '';
      scrollTop.value = 0;
      if (listRef.value) {
        listRef.value.scrollTop = 0;
      }
    };

    // 滚动到选中项
    const scrollToSelected = () => {
      if (!props.modelValue || !listRef.value) return;

      const selectedIndex = filteredOptions.value.findIndex(option => option.label === props.modelValue);
      if (selectedIndex >= 0) {
        const targetScrollTop = selectedIndex * props.itemHeight;
        const maxScrollTop = Math.max(0, totalHeight.value - props.maxHeight);
        const finalScrollTop = Math.min(targetScrollTop, maxScrollTop);
        scrollTop.value = finalScrollTop;
        listRef.value.scrollTop = finalScrollTop;

        // 高亮选中项
        highlightedValue.value = props.modelValue;
      }
    };

    // 处理滚动
    const handleScroll = e => {
      scrollTop.value = e.target.scrollTop;
    };

    // 点击外部关闭
    const handleClickOutside = e => {
      if (!dropdownVisible.value) return;

      // 如果点击的是组件内部（包括输入框和下拉框），不关闭
      if (componentRef.value && componentRef.value.contains(e.target)) {
        return;
      }

      dropdownVisible.value = false;
      searchText.value = '';
    };

    // 监听搜索文本变化
    watch(searchText, () => {
      // 搜索时重置滚动位置到顶部
      scrollTop.value = 0;
      if (listRef.value) {
        listRef.value.scrollTop = 0;
      }
    });

    // 处理全局关闭事件
    const handleCloseAllSelects = e => {
      // 如果事件指定了例外组件，且当前组件是例外，则不关闭
      if (e.detail?.except === componentRef.value) {
        return;
      }

      if (dropdownVisible.value) {
        dropdownVisible.value = false;
        searchText.value = '';
      }
    };

    onMounted(() => {
      document.addEventListener('click', handleClickOutside);
      document.addEventListener('closeAllSelects', handleCloseAllSelects);
    });

    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside);
      document.removeEventListener('closeAllSelects', handleCloseAllSelects);
    });

    return {
      componentRef,
      inputRef,
      listRef,
      searchInputRef,
      dropdownVisible,
      searchText,
      highlightedValue,
      displayValue,
      filteredOptions,
      totalHeight,
      visibleItems,
      toggleDropdown,
      handleSearchInput,
      selectOption,
      handleClear,
      handleSearchClear,
      scrollToSelected,
      handleScroll
    };
  }
};
</script>

<style lang="scss" scoped>
.simple-virtual-select {
  position: relative;
  width: 100%;

  .select-arrow {
    transition: transform 0.3s;
    cursor: pointer;

    &.is-reverse {
      transform: rotate(180deg);
    }
  }

  .select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 9999;
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    margin-top: 2px;
    max-height: 280px;
    min-width: 200px;
    overflow: hidden;
  }

  .select-filter {
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
    background: white;

    :deep(.el-input) {
      .el-input__wrapper {
        box-shadow: 0 0 0 1px #dcdfe6 inset;

        &.is-focus {
          box-shadow: 0 0 0 1px #409eff inset;
        }
      }
    }

    .search-debug {
      font-size: 12px;
      color: #999;
      margin-top: 4px;
      background: #f8f9fa;
      padding: 4px 8px;
      border-radius: 3px;
    }
  }

  .select-list {
    height: 200px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }
  }

  .select-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
    cursor: pointer;
    color: #606266;
    font-size: 14px;

    &:hover,
    &.is-highlighted {
      background-color: #f5f7fa;
    }

    &.is-selected {
      color: #409eff;
      font-weight: 500;
      background-color: #f0f9ff;
    }

    .option-check {
      color: #409eff;
    }
  }

  .select-empty {
    padding: 20px;
    text-align: center;
    color: #999;
    font-size: 14px;
  }
}
</style>
