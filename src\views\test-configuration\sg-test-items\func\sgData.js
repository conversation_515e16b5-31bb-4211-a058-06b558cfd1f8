import { getExternalMaterialClassification } from '@/api/sg-capabilitystd';

export function getSgMaterialClassifcation() {
  return new Promise(resolve => {
    // const storageResult = window.sessionStorage.getItem('sg-material-classification')
    // if (storageResult) {
    //   resolve(JSON.parse(storageResult))
    // } else {
    getExternalMaterialClassification().then(res => {
      if (res.data.data) {
        const result = res.data.data;
        // window.sessionStorage.setItem('sg-material-classification', JSON.stringify(result))
        resolve(result);
      } else {
        resolve([]);
      }
    });
    // }
  });
}

// TODO: 新增更新物资分类接口后需要更新 sessionStorage.setItem('sg-material-classification')
