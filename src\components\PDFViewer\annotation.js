import { AnnotationType } from './enum';

const TYPE_NAME_MAP = {
  [AnnotationType.HIGHLIGHT]: 'highlight',
  [AnnotationType.POPUP]: 'popup'
};

export class Annotation {
  constructor(parameters) {
    const { id, title, contents, hasPopup, rect, annotationType, parentId } = parameters;
    this.id = this.getId(id, annotationType);
    this.title = title;
    this.titleObj = { str: title };
    this.contents = contents ?? '';
    this.contentsObj = { str: this.contents };
    this.parentId = parentId;
    this.hasPopup = hasPopup || this.isExistPopup(title, contents, annotationType);
    this.rect = rect;
    this.annotationType = annotationType;
  }

  getId(base, type) {
    return `custom-${TYPE_NAME_MAP[type]}-${base}`;
  }

  isExistPopup(title, contents, type) {
    return !title && !contents && type !== AnnotationType.POPUP;
  }
}
