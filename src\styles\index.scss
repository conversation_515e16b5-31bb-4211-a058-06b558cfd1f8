// @import "./variables.scss";
@import './element-variables.scss';
@import './intelligentChart.scss';
// @import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';
@import './layout.scss';
@import './table.scss';
@import './icon/iconfont.css';
@import './tailwindcss.css';
@import './font-barlow-result.css';
@import './font-misans-medium-result.css';
@import './font-misans-regular-result.css';

@font-face {
  font-family: 'SourceHanSerifCN';
  src: local('SourceHanSerifCN-Regular'),
    url('../assets/font/SourceHanSerifCN-Regular/SourceHanSerifCN-Regular.woff2') format('woff2');
  font-style: normal;
  font-weight: normal;
  font-display: swap;
}

@font-face {
  font-family: 'iconfont';
  src: url('./icon/iconfont.woff') format('woff'), url('./icon/iconfont.woff2') format('woff2'),
    url('./icon/iconfont.ttf') format('truetype');
}

@media screen and (max-width: 1044px) {
  body {
    overflow: auto;
  }
}

/** 初始化 **/
body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
input,
button,
textarea,
p,
blockquote,
th,
td,
form,
pre {
  margin: 0;
  padding: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
a:active,
a:hover {
  outline: 0;
}
img {
  display: inline-block;
  border: none;
  vertical-align: middle;
}
li {
  list-style: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
h1,
h2,
h3 {
  font-weight: 400;
}
h4,
h5,
h6 {
  font-size: 100%;
  font-weight: 400;
}
button,
input,
select,
textarea {
  font-size: 100%;
}
input,
button,
textarea,
select,
optgroup,
option {
  font-family: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  outline: 0;
}
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
textarea:disabled,
input:disabled {
  background: none !important;
}
select:disabled {
  color: #303133;
  opacity: 1;
}
hr {
  height: 0;
  line-height: 0;
  margin: 10px 0;
  padding: 0;
  border: none !important;
  border-bottom: 1px solid #eee !important;
  clear: both;
  overflow: hidden;
  background: none;
}
a {
  color: #303133;
  text-decoration: none;
}
a:hover {
  color: #777;
}

body {
  // min-width: 1044px;
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: 'MiSans', 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif, iconfont;
  // font-family, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif, iconfont;
  line-height: 1.6;
  color: #303133;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  line-height: 1;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mt-10 {
  margin-top: 10px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: inline-block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ' ';
    clear: both;
    height: 0;
  }
}

aside {
  background: $background-color;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  // font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif, iconfont;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  a {
    color: #337ab7;
    cursor: pointer;
    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-left {
  text-align: left;
}

.flex-1 {
  flex: 1 1 0%;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(
    90deg,
    rgba(32, 182, 249, 1) 0%,
    rgba(32, 182, 249, 1) 0%,
    rgba(33, 120, 241, 1) 100%,
    rgba(33, 120, 241, 1) 100%
  );
  .subtitle {
    font-size: 20px;
    color: #fff;
  }
  &.draft {
    background: #d0d0d0;
  }
  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;
  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  border: 1px solid #dfe6ec;
  margin-bottom: 15px;
  margin-top: 15px;
  padding: 10px;
  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

.el-date-table td.today span {
  background-color: $menuHover;
}
.el-date-table td.start-date span,
.el-date-table td.end-date span {
  background-color: $menuActiveText;
}
//common
.nowrap {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

// 字符超长的处理
.ellipsis {
  display: inline-block; //块状显示
  overflow: hidden; //隐藏文字
  text-overflow: ellipsis; //显示...
  white-space: nowrap; //不换行
}

// 换行
.word-break {
  word-break: break-all;
  word-wrap: break-word;
  height: auto;
  width: 100%;
  line-height: 20px;
}

.tag-name {
  margin-right: 4px !important;
}

.auto-width {
  width: 100%;
}

.color-red {
  color: #f56c6c;
}

.item-fu-ce {
  width: 20px;
  height: 20px;
  background: #f56c6c;
  border-radius: 2px;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  line-height: 20px;
  color: #ffffff;
  text-align: center;
}

.item-yuan {
  width: 20px;
  height: 20px;
  background: #3392e2;
  border-radius: 2px;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  line-height: 20px;
  color: #ffffff;
  text-align: center;
}

.el-backtop {
  background-color: #dcdfe6;
  &:hover {
    background-color: #dcdfe6;
  }
}

.go-back-icon {
  transform: rotate(180deg);
  color: #ffffff;
}

.tag-order {
  font-size: 14px;
  border-radius: 4px;
  background: $tes-primary2 !important;
  color: $tes-primary !important;
  border-color: $tes-primary1 !important;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
}

// nprogress 样式自定义、
#nprogress {
  .bar {
    background: $tes-primary !important;
  }
}

.tes-iconfont {
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.font-barlow {
  font-family: 'barlow';
}

// .number_font {
//   font-family: 'Misans';
//   font-variant-numeric: tabular-nums;
// }

// 加急图标
.urgent {
  font-size: 12px;
  // position: absolute;
  // left: -11px;
  // top: 5px;
  color: #f56c6c;
  background-color: #fef0f0;
  border: 1px solid #fde2e2;
  border-radius: 2px;
  padding: 2px 3px;
  display: inline-block;
  line-height: 12px;
  transform: scale(0.8);
  z-index: 999;
}

.special {
  font-size: 12px;
  // position: absolute;
  // left: -11px;
  // top: 5px;
  color: #ae009f;
  background-color: #f9e6f0;
  border: 1px solid #f1c6e0;
  border-radius: 2px;
  padding: 2px 3px;
  display: inline-block;
  line-height: 12px;
  transform: scale(0.8);
  z-index: 999;
}

.drawer-form {
  max-height: 270px;
  overflow-y: auto;
}
.drawer-form:hover {
  ::-webkit-scrollbar-thumb {
    background: #d4d7de;
  }
}
.el-collapse-item__header {
  background-color: $background-color !important;
}
//   .el-collapse-item__wrap {
//     background-color: $background-color !important;
//   }
.el-table {
  background-color: $background-color !important;
}
//   .el-menu {
//     background-color: $background-color !important;
//   }
.el-table tr {
  background-color: $background-color !important;
}
.el-tree {
  background-color: $background-color !important;
}
// .el-input__inner {
//   background-color: $background-color;
// }
.el-calendar {
  background-color: $background-color !important;
}
//   .el-radio-button__inner {
//     background-color: $background-color !important;
//   }
.el-radio-button__original-radio:checked + .el-radio-button__inner {
  background-color: $tes-primary2 !important;
  border-color: $tes-primary3;
  box-shadow: -0.067rem 0 0 0 $tes-primary3;
  color: $tes-primary !important;
}
.el-dialog {
  background-color: $background-color !important;
}
.el-message-box {
  background-color: $background-color !important;
}
.el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: $background-color !important;
}
.el-drawer {
  background-color: $background-color !important;
}
.el-descriptions__body {
  background-color: $background-color !important;
}
.el-step__description.is-finish {
  color: #303133;
}
