<template>
  <el-dialog
    v-model="showDialog"
    custom-class="code-editor"
    title="编辑表达式"
    width="60%"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <div class="wrap">
      <div class="codeList">
        <el-input v-model="inputValue" placeholder="查询" class="input" />
        <ul class="snippets-groups">
          <li>
            <ul class="snippets-categories">
              <li v-for="(value, key, index) in filteredCodeSnippetCategories" :key="index">
                <div class="category nowrap" @click="openCodeList(value)">
                  <span v-show="value.isExpanded" class="code-iconfont icon-xia pointer" />
                  <span v-show="!value.isExpanded" class="code-iconfont icon-you pointer" />
                  <el-tooltip class="item" effect="dark" :content="key" placement="top" :show-after="500">
                    <span class="list-text">
                      {{ key }}
                    </span>
                  </el-tooltip>
                  <span class="badge">{{ value.list.length }}</span>
                </div>
                <ul v-show="value.isExpanded" class="snippets-types">
                  <li v-for="(codeSnippet, index1) in value.list" :key="index1" class="snippet nowrap">
                    <el-popover placement="top" width="400" :show-after="300" trigger="hover">
                      <div :class="codeSnippet.name" class="color" v-html="`${codeSnippet.name}`" />
                      <template #reference>
                        <span class="list-text">{{ codeSnippet.name }}</span>
                      </template>
                    </el-popover>
                    <el-tooltip content="引用" placement="right" effect="light" :hide-after="0">
                      <span class="code-iconfont icon-jiantou pointer" @click="addCode(codeSnippet)" />
                    </el-tooltip>
                  </li>
                </ul>
              </li>
            </ul>
          </li>
        </ul>
      </div>
      <div class="code">
        <div class="header">
          <el-tooltip :content="'撤销上次修改'" placement="bottom" effect="light">
            <span class="code-iconfont icon-chexiao1" @click="undo()" />
          </el-tooltip>
          <el-tooltip :content="'重做上次修改'" placement="bottom" effect="light">
            <span class="code-iconfont icon-huifu1" @click="redo()" />
          </el-tooltip>
          <span class="seperator" />
          <el-tooltip :content="'注释选定文本'" placement="bottom" effect="light">
            <span class="code-iconfont icon-jia1" @click="comment()" />
          </el-tooltip>
          <el-tooltip :content="'取消对选定文本的注释'" placement="bottom" effect="light">
            <span class="code-iconfont icon-jian1" @click="uncomment()" />
          </el-tooltip>
          <span class="seperator" />
          <el-tooltip :content="'向左缩进'" placement="bottom" effect="light">
            <span class="code-iconfont icon-xiangzuosuojin" @click="indentLess()" />
          </el-tooltip>
          <el-tooltip :content="'向右缩进'" placement="bottom" effect="light">
            <span class="code-iconfont icon-xiangyousuojin" @click="indentMore()" />
          </el-tooltip>
          <el-tooltip :content="'自动格式化选定文本'" placement="bottom" effect="light">
            <span class="code-iconfont icon-wusuojin" @click="indentAuto()" />
          </el-tooltip>
          <span class="seperator" />
          <el-tooltip :content="'全部折叠'" placement="bottom" effect="light">
            <span class="code-iconfont icon-jian" @click="collapseAll()" />
          </el-tooltip>
          <el-tooltip :content="'全部展开'" placement="bottom" effect="light">
            <span class="code-iconfont icon-jia" @click="expandAll()" />
          </el-tooltip>
          <span class="seperator" />
        </div>
        <div class="center">
          <textarea id="code" class="form-control" name="code" :placeholder="placeholderData" />
        </div>
        <!-- 引入后在html界面中建立textarea标签，用于生成代码框 -->
      </div>
      <div class="item-list">
        <el-button size="small" icon="el-icon-plus" @click="selectItems()">选择关键参数</el-button>
        <div v-for="(item, index) in capabilityParaList" :key="index" class="capability-para">
          {{ item.name }}({{ item.templatekey }})
          <span
            v-clipboard:copy="item.templatekey"
            class="icon el-icon-document-copy"
            @click="copyParam(item.templatekey)"
          />
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="runJS">运 行</el-button>
        <el-button type="primary" @click="dialogSuccess">确 定</el-button>
      </span>
    </template>
    <SelectKeyParams
      :show="showItemDialog"
      :tree="treeData"
      :data="addList"
      @close="closeDialog"
      @selectData="selectData"
    />
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, nextTick, getCurrentInstance, onMounted, markRaw } from 'vue';
import 'codemirror/theme/base16-light.css';
import 'codemirror/lib/codemirror.css';
import 'codemirror/addon/hint/show-hint.css';

import './assets/css/reset.css';
import './assets/icon/iconfont.css';

import 'codemirror/addon/dialog/dialog.css';
import CodeMirror from 'codemirror/lib/codemirror';
import 'codemirror/addon/edit/matchbrackets';
import 'codemirror/addon/selection/active-line';

import 'codemirror/addon/fold/foldgutter.css';
import 'codemirror/addon/fold/foldcode';
import 'codemirror/addon/fold/brace-fold';
import 'codemirror/addon/fold/comment-fold';
import 'codemirror/addon/fold/indent-fold';
import 'codemirror/addon/fold/foldgutter';

import 'codemirror/addon/lint/lint.css';
import 'codemirror/addon/lint/lint';
import 'codemirror/addon/lint/javascript-lint';

import 'codemirror/addon/edit/closebrackets';
import 'codemirror/addon/edit/matchbrackets';

import 'codemirror/addon/hint/show-hint';
import 'codemirror/mode/javascript/javascript';
import 'codemirror/addon/comment/comment';

import 'codemirror/addon/display/placeholder';

// import 'codemirror/mode/sql/sql'

import FunctionCode from './assets/code/functionCode';
import SelectKeyParams from '@/components/BusinessComponents/SelectKeyParams';
import { getCapabilityTree } from '@/api/user';
import { formatTree } from '@/utils/formatJson';
import { ElMessage } from 'element-plus';

export default {
  name: 'CodeEditor',
  components: { SelectKeyParams },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {
          capabilityjson: [],
          javascriptmath: ''
        };
      }
    },
    list: {
      type: Array,
      default: function () {
        return [];
      }
    },
    materialCode: {
      type: String,
      default: ''
    }
  },
  emits: ['close', 'setData'],
  setup(props, context) {
    // console.log(props)
    // const route = useRoute()
    const { proxy } = getCurrentInstance();
    const datas = reactive({
      showDialog: false,
      codeList: {},
      filteredCodeSnippetCategories: {},
      inputValue: '',
      toolBar: { isLint: true },
      CodeMirrorEditor: null,
      codeValue: 'if(num > 0){ return true }',
      selection: { from: {}, to: {} },
      hintFunctionCode: [],
      hintColumnCode: [],
      placeholderData: '示例：if(num > 0){ return true }',
      showItemDialog: false,
      treeData: [],
      addList: [],
      capabilityParaList: [],
      runStatus: false
    });

    watch(
      () => props.show,
      newValue => {
        datas.showDialog = newValue;
        if (newValue) {
          datas.capabilityParaList = props.data.capabilityjson ? props.data.capabilityjson : [];
          datas.codeValue = props.data.javascriptmath ? props.data.javascriptmath : '';
          datas.addList = JSON.parse(JSON.stringify(datas.capabilityParaList));
          nextTick(() => {
            proxy.initCodeMirror();
            proxy.addIcon(FunctionCode, 'function', 'hintFunctionCode');
            datas.codeList = proxy.initCode(FunctionCode);
            datas.filteredCodeSnippetCategories = datas.codeList;
            proxy.getTree(props.materialCode);
          });
        }
      },
      { deep: true }
    );

    watch(
      () => datas.inputValue,
      val => {
        datas.filteredCodeSnippetCategories = proxy.filterCategories(datas.codeList, val);
      }
    );

    onMounted(() => {});

    // 确定
    const dialogSuccess = () => {
      // var topScript = '<script>' + datas.codeValue + '<\/script>'
      // const ele = document.createElement('script')
      //   ele.type = 'text/javascript'
      //   ele.text = script.innerHTML
      //   scriptRef.value.append(ele)
      if (datas.codeValue === '') {
        ElMessage.error('请输入表达式');
        return false;
      } else if (datas.capabilityParaList.length === 0) {
        ElMessage.error('请选择关键参数');
        return false;
      } else if (datas.runStatus === false) {
        ElMessage.error('代码未解析或解析失败，请提交正确的代码段！');
        return false;
      }

      const param = {
        capabilityjson: datas.capabilityParaList,
        javascriptmath: datas.codeValue
      };
      context.emit('setData', param);
    };
    // 取消
    const handleClose = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };

    // 选择关键参数
    const selectItems = () => {
      datas.showItemDialog = true;
    };
    // 关闭关键参数弹出框
    const closeDialog = () => {
      datas.showItemDialog = false;
    };
    // 获取关键参数
    const selectData = v => {
      datas.capabilityParaList = v;
      datas.addList = JSON.parse(JSON.stringify(datas.capabilityParaList));
    };
    // 运行
    const runJS = () => {
      const javascriptMath = datas.codeValue; // 'console.log(value); return value + xyz'
      const capabilityJson = datas.capabilityParaList; // [{ key: 'value' }, { key: 'xyz' }]
      const values = [];
      if (javascriptMath === '') {
        ElMessage.error('请输入表达式');
        return false;
      } else if (capabilityJson.length === 0) {
        ElMessage.error('请选择关键参数');
        return false;
      }
      capabilityJson.forEach((item, index) => {
        values.push(index + 1);
      });
      values.push(11);
      // 获取表达式
      const func = javascriptMath;
      const keys = [];
      if (capabilityJson.length > 0) {
        capabilityJson.forEach(json => {
          keys.push(json.templatekey);
        });
      }
      try {
        const Fn = new Function(keys.join(','), 'expValue', func);
        console.log(Fn(...values));
        ElMessage.success('运行成功');
        datas.runStatus = true;
      } catch (e) {
        ElMessage.error('运行失败:' + e.message);
        datas.runStatus = false;
      }
    };
    return {
      ...toRefs(datas),
      handleClose,
      dialogSuccess,
      runJS,
      selectItems,
      closeDialog,
      selectData
    };
  },
  computed: {
    hintCode() {
      return [].concat(this.hintFunctionCode, this.hintColumnCode);
    }
  },
  methods: {
    getTree(mateType) {
      getCapabilityTree(mateType).then(response => {
        if (response !== false) {
          const data = response.data.data;
          this.treeData = formatTree(data);
          this.treeData.unshift({
            id: '-1',
            parentId: '0',
            materialCategoryCode: data.length > 0 ? data[0].materialCategoryCode : '266013',
            name: '全部',
            order: 0,
            status: 2
          });
        }
      });
    },
    addCode(val) {
      // console.log(val)
      // console.log(this.CodeMirrorEditor)
      this.CodeMirrorEditor.replaceSelection(val.code); // 追加内容
      // this.initCursorToEnd()
    },
    openCodeList(item) {
      item.isExpanded = !item.isExpanded;
    },
    initCode(data) {
      const obj = {};
      // const arr = []
      for (let i = 0; i < data.length; i++) {
        if (!obj[data[i].category]) {
          obj[data[i].category] = {};
          obj[data[i].category].list = [];
          obj[data[i].category].isExpanded = false;
        }
        obj[data[i].category].list.push(data[i]);
      }
      return obj;
    },
    filterCategories(srcCategories, codeKeyword) {
      const filteredCategories = {};
      // let arr = []
      for (const category in srcCategories) {
        const codeSnippets = srcCategories[category].list;
        const filteredCodeSnippets = codeSnippets.filter(codeSnippet => {
          return codeSnippet.name.toLowerCase().indexOf(codeKeyword.toLowerCase()) > -1;
        });
        filteredCategories[category] = {};
        filteredCategories[category].list = filteredCodeSnippets;
        filteredCategories[category].isExpanded = true;
      }
      return filteredCategories;
    },
    undo() {
      this.CodeMirrorEditor.undo();
    },
    redo() {
      this.CodeMirrorEditor.redo();
    },
    comment() {
      if (typeof this.selection.from.line === 'undefined' || typeof this.selection.to.line === 'undefined') {
        return;
      }
      if (this.selection.from.line <= this.selection.to.line) {
        this.CodeMirrorEditor.lineComment(this.selection.from, this.selection.to);
      } else {
        this.CodeMirrorEditor.lineComment(this.selection.to, this.selection.from);
      }
    },
    uncomment() {
      if (typeof this.selection.from.line === 'undefined' || typeof this.selection.to.line === 'undefined') {
        return;
      }
      if (this.selection.from.line <= this.selection.to.line) {
        this.CodeMirrorEditor.uncomment(this.selection.from, this.selection.to);
      } else {
        this.CodeMirrorEditor.uncomment(this.selection.to, this.selection.from);
      }
    },
    indentLess() {
      this.CodeMirrorEditor.indentSelection('subtract');
    },
    indentMore() {
      this.CodeMirrorEditor.indentSelection('add');
    },
    // 自动格式化选定文本
    indentAuto() {
      this.CodeMirrorEditor.indentSelection('smart');
    },
    // 全部折叠
    collapseAll() {
      const cm = this.CodeMirrorEditor;
      cm.operation(() => {
        for (let i = cm.firstLine(), e = cm.lastLine(); i <= e; i++) {
          cm.foldCode(CodeMirror.Pos(i, 0), null, 'fold');
        }
      });
    },
    // 全部展开
    expandAll() {
      const cm = this.CodeMirrorEditor;
      cm.operation(() => {
        for (let i = cm.firstLine(), e = cm.lastLine(); i <= e; i++) {
          cm.foldCode(CodeMirror.Pos(i, 0), null, 'unfold');
        }
      });
    },
    addIcon(data, type, name) {
      // 给 hint 添加类型 icon
      // let arr = []
      for (let i = 0; i < data.length; i++) {
        const nativeItem = {
          text: data[i].code,
          displayText: data[i].name,
          className: '',
          render: (elt, self, data) => {
            const compEle = document.createElement('div');
            compEle.innerHTML = `<span class="code-iconfont icon-${type}" style='color: red;'></span>  ${data.displayText}`;
            elt.appendChild(compEle);
          }
        };
        this[name].push(nativeItem);
      }
    },
    getHints(cm, option) {
      return new Promise(accept => {
        setTimeout(() => {
          const WORD = /[\w\[\]\"\.$]+/;
          const cursor = cm.getCursor();
          const curLine = cm.getLine(cursor.line);
          var start = cursor.ch;
          const end = cursor.ch;
          while (start && WORD.test(curLine.charAt(start - 1))) {
            --start;
          }
          const curWord = start !== end && curLine.slice(start, end);
          const list = [];
          var isDefinedObj = false;
          if (curWord) {
            if (list.length === 0) {
              var dotIndex = curWord.lastIndexOf('.');
              var memberStr = curWord.slice(dotIndex + 1);
              var comList = this.hintCode;
              for (let j = 0; j < comList.length; j++) {
                if (comList[j].displayText.toLowerCase().lastIndexOf(memberStr.toLowerCase(), 0) === 0) {
                  list.push(comList[j]);
                }
              }
              isDefinedObj = true;
            }
          }
          if (isDefinedObj) {
            return accept({
              list: list,
              from: CodeMirror.Pos(cursor.line, start),
              to: CodeMirror.Pos(cursor.line, end)
            });
          } else {
            return accept({
              list: list,
              from: CodeMirror.Pos(cursor.line, start + 1),
              to: CodeMirror.Pos(cursor.line, end)
            });
          }
        }, 100);
      });
    },

    initCursorToEnd() {
      // 初始化光标位置到编辑器结尾
      var lastLineNumber = this.CodeMirrorEditor.lastLine();
      var lastCharNumber = this.CodeMirrorEditor.getLine(lastLineNumber).length;
      this.CodeMirrorEditor.setCursor(lastLineNumber, lastCharNumber);
      this.selection.from = this.selection.to = this.CodeMirrorEditor.getCursor();
    },
    initCodeMirror() {
      var that = this;
      var myTextarea = document.getElementById('code');
      this.CodeMirrorEditor = markRaw(
        CodeMirror.fromTextArea(myTextarea, {
          value: '',
          mode: 'javascript', // 编辑器语言
          // theme:'base16-light', // 不设置主题, 使用默认主题
          lineNumbers: true, // 显示行号
          smartIndent: true,
          autofocus: true,
          autoRefresh: true,
          historyEventDelay: 500,
          indentWithTabs: true,
          tabSize: 2,
          indentUnit: 4,
          line: true,
          matchBrackets: true,
          autoCloseBrackets: true,
          styleActiveLine: true,
          foldGutter: true,
          lint: false, // 代码检查
          gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers'],
          extraKeys: { 'Ctrl-Space': 'autocomplete' }, // 自定义快捷键
          // extraKeys: {"Ctrl": "autocomplete"}, // ctrl可以弹出选择项
          hintOptions: {
            completeSingle: false,
            alignWithWord: false,
            hint: this.getHints
          }
        })
      );
      // 赋值
      this.CodeMirrorEditor.setValue(this.codeValue);
      // 当输入框内容发生变化 更新 codeValue 值
      this.CodeMirrorEditor.on('change', cm => {
        // console.log('change')
        that.codeValue = cm.getValue();
        // console.log(JSON.stringify(that.codeValue))
      });

      // this.CodeMirrorEditor.on('cursorActivity', (cm) => {
      //   console.log('cursorActivity')
      //   console.log(cm)
      //   console.log(that.CodeMirrorEditor)
      //   that.CodeMirrorEditor.showHint()
      //   that.selection.to = cm.getCursor()
      //   console.log(cm.getCursor())
      //   if (that.CodeMirrorEditor.getSelections().length === 0) {
      //     that.selection.from = cm.getCursor()
      //   }
      // })
      // this.CodeMirrorEditor.on('mousedown', (cm) => {
      //   console.log('mousedown')
      //   console.log(cm)
      //   console.log(cm.getCursor())
      //   that.selection.from = cm.getCursor()
      // })
    },
    copyParam(e) {
      console.log(e);
      // const input = document.createElement('input');
      // input.setAttribute('readonly', 'readonly');
      // input.setAttribute('value', item.templatekey);
      // document.body.appendChild(input);
      //     input.setSelectionRange(0, 9999);
      //     if (document.execCommand('copy')) {
      //         document.execCommand('copy');
      //         console.log('复制成功');
      // }
      const that = this;
      this.$copyText(e).then(
        function (e) {
          that.$message.success('复制成功！');
        },
        function (e) {
          that.$message.error('复制失败！');
        }
      );
    },
    onCopyError() {
      this.$message.error('复制失败！');
    }
  }
};
</script>
<style lang="scss">
.code-editor {
  padding: 0px 10px;
  .el-dialog__body {
    .wrap {
      display: flex;
      justify-content: space-between;
      border: 1px solid #ccc;
    }

    .wrap .codeList {
      width: 210px;
    }
    .wrap .codeList .input {
      padding: 10px;
    }
    .wrap .codeList .snippets-groups {
      padding: 5px 10px;
    }
    .wrap .codeList .snippets-groups .category {
      padding: 4px 0;
      .code-iconfont {
        display: inline-block;
        padding-right: 6px;
        font-size: 12px;
      }
    }
    .wrap .codeList .snippet {
      display: flex;
      justify-content: space-between;
    }
    .wrap .codeList .snippets-types {
      padding: 0 20px;
    }
    .wrap .codeList .badge {
      color: #fff;
      background-color: #4090f7;
      padding: 0 8px;
      border-radius: 4px;
      margin-left: 8px;
    }
    .wrap .codeList li {
      margin: 5px 0;
    }
    .wrap .code {
      width: 100%;
      overflow: auto;
      height: calc(100vh - 300px);
    }
    .code:deep(.cm-comment) {
      color: #a50;
    }
    .code:deep(.cm-keyword) {
      color: #708;
    }
    .code {
      border-left: 1px solid #ccc;
    }
    .header {
      height: 28px;
      border-bottom: 1px solid #ccc;
    }
    .code .center {
      height: calc(100% - 28px);
      overflow-y: auto;
    }
    .code .code-iconfont {
      float: left;
      width: 28px;
      height: 28px;
      font-size: 14px;
      cursor: pointer;
      background-position: center;
      background-repeat: no-repeat;
      padding-top: 6px;
      text-align: center;
    }
    .seperator {
      float: left;
      width: 0;
      height: 28px;
      border-right: 1px solid #ccc;
    }
    .CodeMirror {
      height: 100% !important;
    }
    .CodeMirror pre.CodeMirror-placeholder {
      color: #999;
    }
    #code {
      touch-action: none;
    }
    .item-list {
      width: 200px;
      padding: 10px;
      border-left: 1px solid #ccc;
      overflow-y: auto;
      .el-button {
        text-align: right;
      }
      .capability-para {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 8px 0px;
        cursor: pointer;
        .icon {
          display: inline-block;
          margin-left: 10px;
        }
        &:hover {
          background: #ccc;
        }
      }
    }
  }
}
</style>
