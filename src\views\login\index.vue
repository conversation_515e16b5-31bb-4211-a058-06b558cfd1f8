<template>
  <div
    v-if="!isMk"
    class="login-container"
    :style="{ 'background-image': `url(${imgPreviewUrl?.backgroundAddr || imgDefaule.backgroundAddr})` }"
  >
    <div class="wrapper">
      <div class="headline">
        <img :src="imgPreviewUrl?.logoAddr || imgDefaule.logoAddr" alt="qms-logo" />
        <div class="phone">
          <i class="el-icon el-icon-phone" />
          <span class="content">全国服务电话 ************</span>
        </div>
      </div>
      <div class="login-box">
        <img src="@/assets/img/login.png" class="welcomeLogo" alt="login" />
        <img :src="imgPreviewUrl?.welcomeAddr || imgDefaule.welcomeAddr" class="login-img" alt="login" />
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" autocomplete="on">
          <el-form-item prop="username" :class="activeClass === 1 ? 'activeclass' : ''">
            <el-input
              ref="username"
              v-model="loginForm.username"
              placeholder="用户名"
              prefix-icon="el-icon-user"
              name="username"
              type="text"
              tabindex="1"
              autocomplete="on"
              @focus="addActiveClass(1)"
            />
          </el-form-item>
          <el-tooltip v-model="capsTooltip" content="Caps lock is On" placement="right" manual>
            <el-form-item prop="password" :class="activeClass === 2 ? 'activeclass' : ''">
              <el-input
                :key="passwordType"
                ref="password"
                v-model="loginForm.password"
                prefix-icon="el-icon-lock"
                :type="passwordType"
                placeholder="密码"
                name="password"
                tabindex="2"
                @focus="addActiveClass(2)"
                @keyup="checkCapslock"
                @blur="capsTooltip = false"
                @keyup.enter="handleLogin"
              />
            </el-form-item>
          </el-tooltip>
          <div v-if="checkServerIp()" class="other-login">
            <img src="@/assets/img/wechat.png" alt="微信登录" class="wechat" @click="dialogWX = true" />
            <img src="@/assets/img/DingTalk.png" alt="钉钉登录" class="DingTalk" @click="loginForDT" />
          </div>
          <el-button :loading="loading" type="primary" class="login-btn" @click.prevent="handleLogin">登 录</el-button>
        </el-form>
        <div class="copyright">
          <span>Copyright © 2017-{{ new Date().getFullYear() }} 程析智能. All Rights Reserved</span>
        </div>
      </div>
    </div>
    <DialogDingTalk :dialog="dialogDT" :goto-url="gotoUrl" @closeDialog="closeDialog" />
    <DialogWeChat :dialog="dialogWX" :page-type="'login'" @closeDialog="closeWXDialog" />
    <Dialog2FA
      :dialog-visible="show2FADialog"
      :username="loginForm.username"
      @close-dialog="close2FADialog"
      @verification-succeeded="verification2FA"
    />
  </div>
</template>

<script>
import { getMenuList, getLIMSConfig } from '@/utils/auth';
import { getBandApi } from '@/api/sysConfig';
import { isEnabledSecretAuth } from '@/api/userInfo';
import DialogDingTalk from '@/components/ThirdParty/DialogDingTalk.vue';
import DialogWeChat from '@/components/ThirdParty/DialogWeChat.vue';
import { getQrCodeApi } from '@/api/sysConfig';
import { ElMessageBox } from 'element-plus';
import { checkServerIp, QmsServerIps } from '@/utils/server';
import { useRoute } from 'vue-router';
import Dialog2FA from './components/Dialog2FA.vue';
import { setCurrentIp } from '@/services/loginService';
import { computed } from 'vue';
import { diyLoginStyleQuery, diyLoginStylePreview } from '@/api/login-configuring';
import backgroundAddr from '@/assets/img/login_bg.png';
import logoAddr from '@/assets/img/qms-cx.png';
import welcomeAddr from '@/assets/img/login_welcome.png';

export default {
  name: 'Login',
  components: { DialogDingTalk, DialogWeChat, Dialog2FA },
  setup(props, ctx) {},
  data() {
    const route = useRoute();
    const validateUsername = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入用户名'));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入密码'));
      } else {
        callback();
      }
    };

    const isQms = computed({
      get: () => checkServerIp(QmsServerIps)
    });
    const isMk = computed({
      get: () => route?.query?.redirect?.indexOf('mkCode=') > -1
    });
    return {
      activeClass: 0,
      isMk,
      dialogDT: false,
      dialogWX: false,
      dialogVisible: false,
      gotoUrl: '',
      imgCodeData: {}, // 登录页配置信息
      imgPreviewUrl: {}, // 预览图片地址
      imgDefaule: {
        backgroundAddr,
        logoAddr,
        welcomeAddr
      }, // 图片的默认地址
      loginForm: {
        username: '', // denghonggang
        password: '',
        grant_type: 'password'
      },
      options: [
        { name: 'system', pwd: '123qweR' },
        { name: 'denghonggang', pwd: '123qweR' },
        { name: 'shiyanyuan01', pwd: '123qweR' }
      ],
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      otherQuery: {},
      checkServerIp,
      show2FADialog: false,
      isQms
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query;
        if (query) {
          if (query?.redirect?.indexOf('mkCode=') > -1 || query.mkCode) {
            // 八益，MK系统单点登录
            this.redirect = query?.redirect?.split('?')[0] || '/home';
            this.otherQuery = this.getOtherQuery(query);
            const mkCode = query?.redirect?.match(/mkCode=([^&]+)/)?.[1] || query.mkCode;
            this.handleLoginMK(mkCode);
          } else {
            this.redirect = query.redirect;
            this.otherQuery = this.getOtherQuery(query);
            if (query.code && checkServerIp()) {
              this.getIsBand(query);
            }
          }
        }
      },
      immediate: true
    }
  },
  mounted() {
    if (this.loginForm.username === '') {
      this.$refs.username.focus();
    } else if (this.loginForm.password === '') {
      this.$refs.password.focus();
    }
    this.getLoginPageCodeInfo();
  },
  methods: {
    changeUser(v) {
      // console.log(v)
      var flag = 0;
      this.options.forEach(r => {
        if (r.name === v) {
          this.loginForm.password = r.pwd;
          flag += 1;
        }
      });
      if (flag === 0) {
        this.loginForm.password = '';
      }
    },
    checkCapslock(e) {
      const { key } = e;
      this.capsTooltip = key && key.length === 1 && key >= 'A' && key <= 'Z';
    },
    loginForDT() {
      getQrCodeApi('loginPage').then(res => {
        this.gotoUrl = res.data.data;
        this.dialogDT = true;
      });
    },
    getIsBand(query) {
      getBandApi(query).then(res => {
        if (res) {
          const data = res.data.data;
          if (!data.isBind) {
            ElMessageBox({
              title: '提示',
              message:
                '<div style="line-height: 40px; text-align: center;">当前钉钉未绑定账号，请绑定后再进行登录</div><div style="line-height: 40px; text-align: center;">绑定步骤：登录系统-个人管理-基本信息</div>',
              confirmButtonText: '确定',
              dangerouslyUseHTMLString: true,
              showCancelButton: false,
              closeOnClickModal: false
            })
              .then(() => {
                this.$router.push({
                  path: '/login'
                });
              })
              .catch(() => {});
          } else {
            this.getToken(data);
          }
        }
      });
    },
    getToken(params) {
      this.getLoginPush({ ...params, grant_type: 'openId' });
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = '';
      } else {
        this.passwordType = 'password';
      }
      this.$nextTick(() => {
        this.$refs.password.focus();
      });
    },
    addActiveClass(index) {
      this.activeClass = index;
    },
    closeDialog() {
      this.dialogDT = false;
    },
    closeWXDialog(info) {
      this.dialogWX = false;
      if (info?.isRefreshLogin) {
        this.getToken(info.detail);
      }
    },
    handleLogin() {
      var that = this;
      this.$refs.loginForm.validate(async valid => {
        if (valid) {
          that.loading = true;
          const secretAuthResponse = await isEnabledSecretAuth({ username: that.loginForm.username });
          if (secretAuthResponse && secretAuthResponse.data.data) {
            that.show2FADialog = true;
            // ElMessage.warning('需要二次验证!')
          } else {
            that.getLoginPush(that.loginForm);
          }
        } else {
          that.loading = false;
          return false;
        }
        that.loading = false;
      });
    },
    // 八益MK单点登录
    handleLoginMK(mkCode) {
      var that = this;
      that.getLoginPush({ userName: mkCode, grant_type: 'bayi' });
    },
    close2FADialog(val) {
      this.show2FADialog = false;
    },
    verification2FA(val) {
      var that = this;
      if (val) {
        that.getLoginPush(that.loginForm);
      }
    },
    getLoginPush(params) {
      const that = this;
      that.$store
        .dispatch('user/login', params)
        .then(async res => {
          if (res) {
            await setCurrentIp();
            const menuList = getMenuList();
            let homeFlag = false;
            let userFlag = false;
            if (menuList && menuList.length > 0) {
              homeFlag = menuList.some(function (menu) {
                return menu.name === 'Home' || menu.name === 'InspectionApplication';
              });
              userFlag = menuList.some(function (m) {
                return m.name === 'userManage';
              });
            }
            // this.$router.push({ path: this.redirect || '/home/<USER>', query: this.otherQuery, params: { token: '0' }})
            const { DEV, VITE_TEMPLATE_EDITOR, VITE_TEMPLATE_EDITOR_ORIGIN } = import.meta.env;
            const { origin } = window.location;
            if (this.redirect) {
              if (DEV && this.redirect.startsWith(VITE_TEMPLATE_EDITOR)) {
                window.location.href = VITE_TEMPLATE_EDITOR_ORIGIN + this.redirect;
              } else {
                window.location.href = origin + this.redirect;
              }
            } else if (homeFlag) {
              window.location.href = origin + getLIMSConfig().VITE_HOME_PAGE;
            } else if (userFlag) {
              window.location.href = origin + '/user-manage/base-info';
            } else {
              const toPath = menuList[0].children.length > 0 ? menuList[0].children[0].path : menuList[0].path;
              window.location.href = origin + toPath;
            }
            // setTimeout(function() {
            //   that.$router.replace('/user-manage/base-info')
            // })
            // this.$router.push({ path: this.redirect || '/tes-home' })
            that.loading = false;
          } else {
            that.loading = false;
          }
          // this.$router.push({ path: '/tes-home' })
        })
        .catch(() => {
          that.loading = false;
        });
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur];
        }
        return acc;
      }, {});
    },
    // 获取登录页配置信息
    getLoginPageCodeInfo() {
      diyLoginStyleQuery({ bizCode: '' }).then(res => {
        if (res) {
          this.imgCodeData = res.data.data;
          this.imgPreviewUrl = {};
          this.getImgUrl(this.imgCodeData.backgroundAddr, 'backgroundAddr');
          this.getImgUrl(this.imgCodeData.logoAddr, 'logoAddr');
          this.getImgUrl(this.imgCodeData.welcomeAddr, 'welcomeAddr');
        }
      });
    },
    // 获取登录页的预览图片
    getImgUrl(val, fieldName) {
      if (val) {
        diyLoginStylePreview({ addr: val, bizCode: '' }).then(res => {
          if (res) {
            if (res) {
              this.imgPreviewUrl[fieldName] = 'data:image/png;base64,' + res.data.data;
            }
          }
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.login-container {
  width: 100vw;
  height: 100vh;
  min-width: 1024px;
  overflow: auto;
  background: linear-gradient(to right bottom, #fff, rgba(0, 179, 138, 0.2));
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  .welcomeLogo {
    display: block;
    margin-bottom: 13px;
  }
  .wrapper {
    width: 100vw;
    height: 100vh;
    padding: 40px 60px;
    position: relative;
    .headline {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // img {
      //   width: 250px;
      // }
      .phone {
        font-size: 14px;
        .el-icon {
          color: #00b678;
        }
        .content {
          display: inline-block;
          padding-left: 10px;
          color: #606266;
        }
      }
    }
    .login-box {
      position: absolute;
      left: 10%;
      top: 24%;
      text-align: left;
      .login-img {
        margin-bottom: 99px;
      }
      .copyright {
        margin-top: 100px;
        span {
          font-size: 12px;
          color: #909399;
        }
      }
      .login-form {
        position: relative;
        width: 320px;
        .el-form-item {
          margin-bottom: 20px;
        }
        :deep(.el-input) {
          .el-input__inner {
            height: 40px;
            line-height: 40px;
          }
          .el-input__inner::-webkit-input-placeholder {
            color: #a8abb2;
          }
          .el-input__icon {
            line-height: 40px;
            color: #909399;
          }
        }
        .other-login {
          height: 40px;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          img {
            cursor: pointer;
            width: 32px;
            margin-left: 10px;
          }
        }
        .login-btn {
          width: 100%;
          height: 40px;
          padding-top: 9px;
          padding-bottom: 9px;
          margin-top: 30px;
        }
      }
    }
  }
}
</style>
