ARG BASE_IMAGE=byzan/nginx:byz-nightly

FROM $BASE_IMAGE

ENV TZ=Asia/Shanghai

# Copy default nginx config
COPY ./default.conf /etc/nginx/conf.d/default.template

# Remove default nginx website
RUN rm -rf /usr/share/nginx/html/*

COPY ./ /usr/share/nginx/html

# Frontend default env
ENV VITE_HOME_PAGE ${defaultHomePage:-/home}
ENV VITE_FLASH_TABLE http://*************:30020

# Nginx default server
ENV API_GATEWAY http://cx-main_tes-sc-gateway:8800
ENV MINIO_HOST http://cx-main_minio:9000
ENV WEBSOCKET_HOST http://cx-main_tes-message:8051
ENV MANAGE_HOST http://cx-main_web-cxist-manage
ENV QMS_HOST http://cx-main_web-cxist-qms
ENV TEMPLATE_EDITOR_HOST http://cx-main_web-cxist-raw-data-template-editor

COPY ./startup.sh /usr/local/bin
RUN chmod +x /usr/local/bin/startup.sh

ENTRYPOINT [ "startup.sh" ]

