/**
 * 统一的resize事件管理器
 * 解决多个resize监听器导致的性能问题和页面卡死
 */

// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func.apply(this, args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

class ResizeManager {
  constructor() {
    this.callbacks = new Map();
    this.isProcessing = false;
    this.lastWidth = window.innerWidth;
    this.lastHeight = window.innerHeight;

    // 使用较长的防抖时间，确保开发工具拖拽时不会频繁触发
    this.debouncedHandler = debounce(this.handleResize.bind(this), 800);

    // 绑定单一的resize事件监听器
    window.addEventListener('resize', this.debouncedHandler, { passive: true });

    // 监听页面可见性，当页面隐藏时停止处理
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
  }

  /**
   * 注册resize回调
   * @param {string} id - 唯一标识
   * @param {Function} callback - 回调函数
   * @param {Object} options - 选项
   */
  register(id, callback, options = {}) {
    this.callbacks.set(id, {
      callback,
      priority: options.priority || 0, // 优先级，数字越大优先级越高
      enabled: true,
      lastExecuted: 0,
      minInterval: options.minInterval || 0 // 最小执行间隔
    });
  }

  /**
   * 注销resize回调
   * @param {string} id - 唯一标识
   */
  unregister(id) {
    this.callbacks.delete(id);
  }

  /**
   * 启用/禁用特定回调
   * @param {string} id - 唯一标识
   * @param {boolean} enabled - 是否启用
   */
  setEnabled(id, enabled) {
    const callback = this.callbacks.get(id);
    if (callback) {
      callback.enabled = enabled;
    }
  }

  /**
   * 处理resize事件
   */
  handleResize() {
    // 如果页面不可见，跳过处理
    if (document.hidden) {
      return;
    }

    // 如果正在处理中，跳过
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      const currentWidth = window.innerWidth;
      const currentHeight = window.innerHeight;

      // 检查是否真的有尺寸变化
      if (currentWidth === this.lastWidth && currentHeight === this.lastHeight) {
        this.isProcessing = false;
        return;
      }

      const resizeInfo = {
        width: currentWidth,
        height: currentHeight,
        deltaWidth: currentWidth - this.lastWidth,
        deltaHeight: currentHeight - this.lastHeight,
        timestamp: Date.now()
      };

      // 按优先级排序执行回调
      const sortedCallbacks = Array.from(this.callbacks.entries())
        .filter(([id, config]) => config.enabled)
        .sort(([, a], [, b]) => b.priority - a.priority);

      // 分批执行回调，避免阻塞
      this.executeCallbacksBatch(sortedCallbacks, resizeInfo, 0);

      this.lastWidth = currentWidth;
      this.lastHeight = currentHeight;
    } catch (error) {
      console.error('ResizeManager error:', error);
    } finally {
      // 延迟重置处理状态，避免过快的连续调用
      setTimeout(() => {
        this.isProcessing = false;
      }, 100);
    }
  }

  /**
   * 分批执行回调
   */
  executeCallbacksBatch(callbacks, resizeInfo, index) {
    if (index >= callbacks.length) {
      return;
    }

    const batchSize = 3; // 每批处理3个回调
    const endIndex = Math.min(index + batchSize, callbacks.length);

    for (let i = index; i < endIndex; i++) {
      const [id, config] = callbacks[i];

      // 检查最小执行间隔
      if (resizeInfo.timestamp - config.lastExecuted < config.minInterval) {
        continue;
      }

      try {
        config.callback(resizeInfo);
        config.lastExecuted = resizeInfo.timestamp;
      } catch (error) {
        console.error(`ResizeManager callback error for ${id}:`, error);
      }
    }

    // 如果还有更多回调，使用requestAnimationFrame继续处理
    if (endIndex < callbacks.length) {
      requestAnimationFrame(() => {
        this.executeCallbacksBatch(callbacks, resizeInfo, endIndex);
      });
    }
  }

  /**
   * 处理页面可见性变化
   */
  handleVisibilityChange() {
    if (document.hidden) {
      // 页面隐藏时，停止所有处理
      this.isProcessing = false;
    }
  }

  /**
   * 手动触发resize处理（用于特殊情况）
   */
  trigger() {
    this.handleResize();
  }

  /**
   * 获取当前注册的回调数量
   */
  getCallbackCount() {
    return this.callbacks.size;
  }

  /**
   * 获取启用的回调数量
   */
  getEnabledCallbackCount() {
    return Array.from(this.callbacks.values()).filter(config => config.enabled).length;
  }

  /**
   * 清除所有回调
   */
  clear() {
    this.callbacks.clear();
  }

  /**
   * 销毁管理器
   */
  destroy() {
    window.removeEventListener('resize', this.debouncedHandler);
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    this.callbacks.clear();
  }
}

// 创建全局实例
const resizeManager = new ResizeManager();

export default resizeManager;

/**
 * 便捷的注册函数
 * @param {string} id - 唯一标识
 * @param {Function} callback - 回调函数
 * @param {Object} options - 选项
 */
export function onResize(id, callback, options = {}) {
  resizeManager.register(id, callback, options);

  // 返回注销函数
  return () => {
    resizeManager.unregister(id);
  };
}

/**
 * Vue 3 Composition API 钩子
 * @param {string} id - 唯一标识
 * @param {Function} callback - 回调函数
 * @param {Object} options - 选项
 */
export function useResize(id, callback, options = {}) {
  const { onMounted, onUnmounted } = require('vue');

  onMounted(() => {
    resizeManager.register(id, callback, options);
  });

  onUnmounted(() => {
    resizeManager.unregister(id);
  });

  return {
    trigger: () => resizeManager.trigger(),
    setEnabled: enabled => resizeManager.setEnabled(id, enabled)
  };
}
