<template>
  <div class="title flex flex-row justify-between items-center">
    <span>温度信息</span>
    <div>
      <span class="label">选择日期：</span>
      <el-date-picker
        v-model="selectTemperature"
        type="datetimerange"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        class="mr-2"
        @change="handleChange"
      />
      <el-button
        v-if="getPermissionBtn('ExportTemperatureInfo')"
        :loading="echartsLoading"
        type="primary"
        size="small"
        @click="handleDown"
        >导出</el-button
      >
    </div>
  </div>
  <div class="h-full"><v-chart :option="optionJSON" :width="'100%'" :height="'100%'" /></div>
</template>
<script>
import { onMounted, reactive, toRefs } from 'vue';
import { formatDateTime, calculateDaysBefore } from '@/utils/formatTime';
import VChart from '@/components/VChart';
import { getPermissionBtn } from '@/utils/common';
import { useRoute } from 'vue-router';
import { deviceBoxRdsData } from '@/api/aging-chamber';
import { exportRdsData } from '@/api/environ-monitoring';
import { ElMessage } from 'element-plus';
export default {
  name: 'EchartsTemperature',
  components: { VChart },
  props: {},
  setup(props, context) {
    const route = useRoute();
    const state = reactive({
      selectTemperature: [new Date(), new Date()],
      echartsLoading: false,
      formData: {},
      xAxisArray: [],
      temperatureList: [],
      humidnessList: [],
      optionJSON: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: params => {
            var text = `<div>${params[0].name}123123</div>`;
            params.forEach(item => {
              if (item.value !== 0) {
                text += `<div class="echartsFormatter">${item.marker}<span class="label">${item.seriesName}</span>：<span class="value">${item.value}项</span></div> `;
              }
            });
            return text;
          }
        },
        toolbox: {
          feature: {
            dataView: { show: false, readOnly: false },
            magicType: { show: false, type: ['line', 'bar'] },
            restore: { show: false }
          },
          top: '8%',
          right: '3%'
        },
        grid: {
          width: 'auto',
          top: '20%',
          bottom: '15%'
        },
        legend: {
          data: ['温度', '湿度'],
          top: '8%',
          itemGap: 20
        },
        dataZoom: [
          {
            type: 'slider',
            show: false,
            rangeMode: 'value',
            height: 15,
            bottom: 5,
            zoomLock: false,
            brushSelect: false,
            textStyle: {
              color: '#ffffff'
            }
          }
        ],
        xAxis: [
          {
            type: 'category',
            data: [],
            axisLabel: {
              interval: 0
            },
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '温度',
            alignTicks: true,
            axisLabel: {
              formatter: '{value} ℃'
            }
          },
          {
            type: 'value',
            name: '湿度',
            alignTicks: true,
            axisLabel: {
              formatter: '{value} %'
            }
          }
        ],
        series: [
          {
            name: '温度',
            type: 'line',
            data: [],
            lineStyle: {
              // 阴影部分
              shadowOffsetX: 0, // 折线的X偏移
              shadowOffsetY: 7, // 折线的Y偏移
              shadowBlur: 6, // 折线模糊
              shadowColor: 'rgba(199, 213, 231, 1)' // 折线颜色
            },
            itemStyle: {
              borderWidth: 2,
              color: '#409EFF',
              shadowOffsetX: 0, // 折线的X偏移
              shadowOffsetY: 7, // 折线的Y偏移
              shadowBlur: 6, // 折线模糊
              shadowColor: 'rgba(199, 213, 231, 1)'
            },
            emphasis: {
              itemStyle: {
                color: '#D1EDC4',
                borderColor: '#409EFF'
              }
            }
          },
          {
            name: '湿度',
            type: 'line',
            data: [],
            yAxisIndex: 1,
            lineStyle: {
              // 阴影部分
              shadowOffsetX: 0, // 折线的X偏移
              shadowOffsetY: 7, // 折线的Y偏移
              shadowBlur: 6, // 折线模糊
              shadowColor: 'rgba(231, 199, 199, 1)' // 折线颜色
            },
            itemStyle: {
              borderWidth: 2,
              color: '#F56C6C',
              shadowOffsetX: 0, // 折线的X偏移
              shadowOffsetY: 7, // 折线的Y偏移
              shadowBlur: 6, // 折线模糊
              shadowColor: 'rgba(231, 199, 199, 1)'
            },
            emphasis: {
              itemStyle: {
                color: '#D1EDC4',
                borderColor: '#F56C6C'
              }
            }
          }
        ]
      }
    });
    const getEcharts = async () => {
      state.xAxisArray = [];
      state.temperatureList = [];
      state.humidnessList = [];
      state.echartsLoading = true;
      const { data } = await deviceBoxRdsData(state.formData).finally((state.echartsLoading = false));
      if (data && data.data?.length) {
        data?.data?.forEach(item => {
          state.xAxisArray.push(item.acquisitionTime);
          state.temperatureList.push(item.pointNameValueMap.temperature);
          state.humidnessList.push(item.pointNameValueMap.humidness);
        });
        renferEcharts();
      }
    };
    const renferEcharts = () => {
      state.optionJSON.xAxis[0].data = state.xAxisArray;
      state.optionJSON.series[0].data = state.temperatureList;
      state.optionJSON.series[1].data = state.humidnessList;
    };
    const handleChange = date => {
      if (date) {
        state.formData.startDate = formatDateTime(date[0]);
        state.formData.endDate = formatDateTime(date[1]);
        getEcharts();
      } else {
        state.formData.startDate = '';
        state.formData.endDate = '';
      }
    };
    const handleDown = async () => {
      if (!state.xAxisArray.length) {
        ElMessage.warning('暂无数据');
        return false;
      }
      const params = {
        startDate: state.formData.startDate,
        endDate: state.formData.endDate,
        dataType: 1,
        deviceNumber: route.query.deviceNumber,
        deviceId: route.query.deviceId
      };
      state.echartsLoading = true;
      const { data } = await exportRdsData(params).finally((state.echartsLoading = false));
      if (data) {
        const reader = new FileReader();
        reader.addEventListener('loadend', () => {
          try {
            const resdata = JSON.parse(reader.result);
            if (resdata.code === 400) {
              ElMessage({
                message: resdata.message,
                type: 'error',
                duration: 3000
              });
            }
          } catch (error) {
            var fileName = `${route.query.boxName}.xlsx`;
            var downloadElement = document.createElement('a');
            var href = window.URL.createObjectURL(data);
            downloadElement.style.display = 'none';
            downloadElement.href = href;
            downloadElement.download = decodeURI(fileName);
            document.body.appendChild(downloadElement);
            downloadElement.click();
            document.body.removeChild(downloadElement);
            window.URL.revokeObjectURL(href);
          }
        });
        reader.readAsText(data, 'utf-8');
      }
    };
    onMounted(() => {
      state.formData = {
        id: route.query.boxId,
        startDate: calculateDaysBefore(new Date(), 3),
        endDate: formatDateTime(new Date())
      };
      getEcharts();
    });

    return {
      ...toRefs(state),
      formatDateTime,
      handleDown,
      handleChange,
      getPermissionBtn,
      getEcharts
    };
  }
};
</script>
<style lang="scss" scoped>
.title {
  font-size: 17px;
  font-weight: 500;
  margin-bottom: 10px;
}
.label {
  font-size: 14px;
  font-weight: normal;
}
</style>
