<template>
  <el-dialog
    v-model="dialogShow"
    title="复测试验"
    :close-on-click-modal="false"
    width="480px"
    custom-class="submit_dialog"
    :before-close="close"
  >
    <el-form v-if="dialogShow" ref="ruleForm" :model="formData" label-position="right" label-width="110px">
      <el-form-item label="检测项目：" prop="date">
        <div>{{ jsonData.capabilityName }}</div>
      </el-form-item>
      <el-form-item
        label="试验日期："
        prop="date"
        :rules="{ required: true, message: '请选择试验日期', trigger: 'change' }"
      >
        <el-date-picker
          v-model="formData.date"
          :clearable="false"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item
        label="试验员："
        prop="ownerId"
        :rules="{ required: true, message: '请选择试验员', trigger: 'change' }"
      >
        <el-select v-model="formData.ownerId" multiple placeholder="请选择" clearable filterable>
          <el-option
            v-for="item in getNameOptionaByid(jsonData.ownerIds)"
            :key="item.value"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="复测说明：">
        <el-input v-model="formData.retestReason" type="textarea" :rows="4" placeholder="请输入复测说明" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="handleSubmit(1)" @keyup.prevent @keydown.enter.prevent>确 认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, toRefs, ref, watch } from 'vue';
import { formatDate } from '@/utils/formatTime';
import store from '@/store';
import { getNameOptionaByid, getNamesByid, getNameByid } from '@/utils/common';
import { getLoginInfo } from '@/utils/auth';
import { retest } from '@/api/execution';
import { ElMessage } from 'element-plus';
import { addByTemp } from '@/api/messageAgent';
export default {
  name: 'RetestTest',
  props: {
    dialogResetVisiable: {
      type: Boolean,
      default: false
    },
    jsonData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closedialog'],
  setup(props, ctx) {
    const state = reactive({
      dialogShow: false,
      jsonData: props.jsonData,
      currentAccountId: getLoginInfo().accountId,
      excelInfo: {},
      formData: {
        ownerId: [],
        retestReason: '',
        date: []
      },
      userList: store.state.common.nameList
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogResetVisiable;
      if (state.dialogShow) {
        state.jsonData = newValue.jsonData;
      }
    });
    const ruleForm = ref('');
    // 提交审核事件
    const handleSubmit = () => {
      const postdata = JSON.parse(JSON.stringify(state.formData));
      ruleForm.value.validate().then(valid => {
        if (valid) {
          postdata.experimentId = state.jsonData.experimentId;
          postdata.ownerId = postdata.ownerId.toString();
          postdata.startDate = formatDate(state.formData.date[0]);
          postdata.finishDate = formatDate(state.formData.date[1]);
          retest(postdata).then(res => {
            if (res.data.code === 200) {
              ElMessage.success({
                message: '提交成功',
                type: 'success'
              });
              addMsg(postdata);
              ctx.emit('closedialog', true);
            }
          });
        }
      });
    };
    // 添加消息待办
    const addMsg = datas => {
      // 添加消息待办
      const params = {
        eventCode: 'M005',
        receiverType: '1',
        senderName: getNameByid(state.currentAccountId),
        receiverIds: datas.ownerId,
        receiverNames: getNamesByid(datas.ownerId).join(','),
        c_ids: state.jsonData.capabilityId,
        todoStartTime: formatDate(state.formData.date[0]),
        todoEndTime: formatDate(state.formData.date[1]),
        c_b_samplesIdArray: state.jsonData.samplesId,
        c_b_sampleNoArray: state.jsonData.secSampleNum,
        c_b_projectNameArray: state.jsonData.capabilityName,
        c_b_capabilityIdArray: state.jsonData.capabilityId
      };

      addByTemp(params).then(res => {
        // if (res !== false) {
        // }
      });
    };
    // 关闭弹屏
    const close = () => {
      ruleForm.value.resetFields();
      ctx.emit('closedialog', false);
    };
    return {
      ...toRefs(state),
      ruleForm,
      getNameOptionaByid,
      handleSubmit,
      close,
      addMsg
    };
  }
};
</script>
