<template>
  <!-- 配置登录页 -->
  <div v-loading="detailLoading" class="loginConfiguring">
    <div class="thumbnail">
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        :action="imgAction['backgroundAddr']"
        :data="{ bizCode: bizCode }"
        :headers="headerconfig"
        :limit="1"
        :on-change="handleChangeFile"
        :before-upload="beforeUpload"
        :on-success="
          (response, uploadFile, uploadFiles) => handleFileSuccess(response, uploadFile, uploadFiles, 'backgroundAddr')
        "
        :show-file-list="false"
      >
        <img
          :src="imgPreviewUrl?.backgroundAddr || imgDefaule.backgroundAddr"
          class="cursorPointer"
          alt=""
          @click="submitUpload"
        />
        <div v-if="isShowIcon" class="indicatorChart">
          <SvgIcon icon-class="num3" :width="60" :height="60" />
        </div>
      </el-upload>
      <div class="logoLeft">
        <el-upload
          ref="uploadLogoAddrRef"
          class="upload-demo"
          :action="imgAction['logoAddr']"
          :data="{ bizCode: bizCode }"
          :headers="headerconfig"
          :limit="1"
          :before-upload="beforeUpload"
          :on-success="
            (response, uploadFile, uploadFiles) => handleFileSuccess(response, uploadFile, uploadFiles, 'logoAddr')
          "
          :show-file-list="false"
        >
          <img
            :src="imgPreviewUrl?.logoAddr || imgDefaule.logoAddr"
            class="cursorPointer"
            alt=""
            @click="submitUpload"
          />
          <div v-if="isShowIcon" class="indicatorChart">
            <SvgIcon icon-class="num1" :width="30" :height="30" />
          </div>
        </el-upload>
      </div>
      <div class="logoCenter">
        <img src="@/assets/img/login.png" alt="" />
      </div>
      <div class="logoCenter2">
        <el-upload
          ref="uploadWelcomeAddrRef"
          class="upload-demo"
          :action="imgAction['welcomeAddr']"
          :data="{ bizCode: bizCode }"
          :headers="headerconfig"
          :limit="1"
          :before-upload="beforeUpload"
          :on-success="
            (response, uploadFile, uploadFiles) => handleFileSuccess(response, uploadFile, uploadFiles, 'welcomeAddr')
          "
          :show-file-list="false"
        >
          <img
            :src="imgPreviewUrl?.welcomeAddr || imgDefaule.welcomeAddr"
            class="cursorPointer"
            alt=""
            @click="submitUpload"
          />
          <div v-if="isShowIcon" class="indicatorChart">
            <SvgIcon icon-class="num2" :width="40" :height="40" />
          </div>
        </el-upload>
      </div>
    </div>
    <ul>
      <li>
        <el-button type="primary" size="mini" @click="isShowIcon = !isShowIcon">{{
          isShowIcon ? '隐藏预览图的指示数字' : '显示预览图的指示数字'
        }}</el-button>
      </li>
      <li>
        <SvgIcon icon-class="num1" :width="27" :height="27" />
        <el-button size="mini" @click="restoreDefault('logoAddr')">恢复默认</el-button>
        <div class="uploadTitle">建议上传127*40的图片（点击图片上传）</div>
      </li>
      <li>
        <SvgIcon icon-class="num2" :width="27" :height="27" />
        <el-button size="mini" @click="restoreDefault('welcomeAddr')">恢复默认</el-button>
        <div class="uploadTitle">建议上传304*61的图片（点击图片上传）</div>
      </li>
      <li>
        <SvgIcon icon-class="num3" :width="27" :height="27" />
        <el-button size="mini" @click="restoreDefault('backgroundAddr')">恢复默认</el-button>
        <div class="uploadTitle">建议上传1920*1080的图片（点击图片上传）</div>
      </li>
    </ul>
  </div>
</template>
<script>
import { reactive, toRefs, getCurrentInstance, ref } from 'vue';
import { getToken } from '@/utils/auth';
import {
  diyLoginStyleQuery,
  diyLoginStylePreview,
  resetBackground,
  resetLogo,
  resetWelcome
} from '@/api/login-configuring';
import {
  diyLoginStyleBackgroundUploadUrl,
  diyLoginStyleLogoUploadUrl,
  diyLoginStyleWelcomeUploadUrl
} from '@/api/uploadAction';
import SvgIcon from '@/components/SvgIcon';
import backgroundAddr from '@/assets/img/login_bg.png';
import logoAddr from '@/assets/img/qms-cx.png';
import welcomeAddr from '@/assets/img/login_welcome.png';

export default {
  name: 'LoginConfiguring',
  components: { SvgIcon },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      detailLoading: false,
      detailInfo: {},
      imgCodeData: {},
      imgDefaule: {
        backgroundAddr,
        logoAddr,
        welcomeAddr
      }, // 图片的默认地址
      imgPreviewUrl: {},
      bizCode: '',
      isShowIcon: true,
      imgAction: {
        backgroundAddr: diyLoginStyleBackgroundUploadUrl(),
        logoAddr: diyLoginStyleLogoUploadUrl(),
        welcomeAddr: diyLoginStyleWelcomeUploadUrl()
      },
      uploadRef: ref(),
      uploadLogoAddrRef: ref(),
      uploadWelcomeAddrRef: ref(),
      logo_title: '',
      headerconfig: {
        Authorization: getToken()
      }
    });
    const getImgCodeData = () => {
      state.detailLoading = true;
      diyLoginStyleQuery({ bizCode: state.bizCode }).then(res => {
        state.detailLoading = false;
        if (res) {
          if (res.data.data.id) {
            state.imgCodeData = res.data.data;
            state.imgPreviewUrl = {};
            getImgUrl(state.imgCodeData.backgroundAddr, 'backgroundAddr');
            getImgUrl(state.imgCodeData.logoAddr, 'logoAddr');
            getImgUrl(state.imgCodeData.welcomeAddr, 'welcomeAddr');
          }
        }
      });
    };
    getImgCodeData();
    const getImgUrl = (val, fieldName) => {
      if (val) {
        state.detailLoading = true;
        diyLoginStylePreview({ addr: val, bizCode: state.bizCode }).then(res => {
          state.detailLoading = false;
          if (res) {
            state.imgPreviewUrl[fieldName] = 'data:image/png;base64,' + res.data.data;
          }
        });
      }
    };
    const beforeUpload = file => {
      var fileName = '';
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      const fileSize = file.size / 1024 / 1024 < 20;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过20M');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else if (fileName !== 'png' && fileName !== 'jpeg' && fileName !== 'jpg') {
        proxy.$message.error('请上传.png、.jpg、.jpeg的文件');
        return false;
      } else {
        return true;
      }
    };
    const handleFileSuccess = (response, uploadFile, uploadFiles, fieldName) => {
      const refJson = {
        backgroundAddr: 'uploadRef',
        logoAddr: 'uploadLogoAddrRef',
        welcomeAddr: 'uploadWelcomeAddrRef'
      };
      state[refJson[fieldName]].clearFiles();
      if (response.code === 200) {
        getImgUrl(response.data, fieldName);
        proxy.$message.success(response.message);
      } else {
        proxy.$message.error(response.message);
      }
    };
    const handleChange = (file, fileList) => {
      console.log(file);
    };
    const submitUpload = () => {
      state.uploadRef.submit();
    };
    // 恢复默认
    const restoreDefault = fieldName => {
      state.detailLoading = true;
      switch (fieldName) {
        case 'backgroundAddr':
          return resetBackground(state.imgCodeData.id).then(res => {
            state.detailLoading = false;
            getImgCodeData();
          });
        case 'logoAddr':
          return resetLogo(state.imgCodeData.id).then(res => {
            state.detailLoading = false;
            getImgCodeData();
          });
        case 'welcomeAddr':
          return resetWelcome(state.imgCodeData.id).then(res => {
            state.detailLoading = false;
            getImgCodeData();
          });
      }
    };
    const handleChangeFile = () => {};
    return {
      ...toRefs(state),
      handleChange,
      handleChangeFile,
      beforeUpload,
      submitUpload,
      handleFileSuccess,
      restoreDefault
    };
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/mixin.scss';
.loginConfiguring {
  height: 100%;
  // overflow-y: hidden;
}
.thumbnail {
  margin: 0 auto;
  position: relative;
  transform: scale(0.6);
  top: -60px;
  min-height: 500px;
  img {
    width: 100%;
  }
  .logoLeft {
    top: 40px;
    left: 60px;
    position: absolute;
  }
  .logoCenter {
    position: absolute;
    left: 10%;
    top: 24%;
  }
  .logoCenter2 {
    position: absolute;
    left: 10%;
    top: 29%;
  }
}
.cursorPointer {
  cursor: pointer;
}
.indicatorChart {
  position: absolute;
  top: 50%;
  left: 50%;
  cursor: pointer;
}
.logoLeft {
  .indicatorChart {
    top: 10%;
  }
}
.logoCenter2 {
  .indicatorChart {
    top: 20%;
  }
}
.uploadTitle {
  margin: 1px 0 0 0;
  font-size: 12px;
  color: #808080;
  line-height: 16px;
}
ul {
  margin: 0;
  padding: 0;
  position: absolute;
  right: 60px;
  top: 30%;
  z-index: 999;
  text-align: left;
}
li {
  list-style: none;
  margin-bottom: 40px;
}
.svg-icon {
  vertical-align: middle;
}
</style>
