<!-- AI助手 -->
<template>
  <el-drawer
    v-model="dialogShow"
    :with-header="false"
    :modal="false"
    :before-close="handleClose"
    custom-class="drawerServe"
    :show-close="true"
    @opened="handleOpened"
  >
    <div class="drawer-container">
      <div class="drawer-header">
        <span class="title">
          <SvgIcon icon-class="ai-icon" :width="85" :height="22" />
        </span>
        <div class="header-right">
          <el-button class="clear-btn" round size="small" @click="handleDeleteHistory">
            <SvgIcon icon-class="brush" :width="16" :height="16" />
            <span class="text">清空对话</span>
          </el-button>
          <span class="close-btn el-icon el-icon-close" @click="handleClose" />
        </div>
      </div>
      <div ref="chatScroll" class="chatHistory">
        <div
          v-for="item in messageList"
          :key="item.createTime"
          :class="['messageRow', item.response_mode == 'streaming' ? 'userMssage' : '']"
        >
          <span v-if="item.response_mode == 'streaming'" class="name-icon">{{
            makePy(item.user.charAt(0))[0].toUpperCase()
          }}</span>
          <img v-else src="@/assets/img/icon-ai-avator.png" alt="" class="avatar" />
          <div class="message">
            <div v-if="chatPending && item.response_mode == 'robot' && item.query === ''" class="sk-cube-grid">
              <div class="sk-cube sk-cube1" />
              <div class="sk-cube sk-cube2" />
              <div class="sk-cube sk-cube3" />
            </div>
            <span v-else v-html="item.query" />
          </div>
        </div>
      </div>
      <div class="sendContent">
        <div ref="container" class="send-area" :class="active ? 'active' : ''" @click="toggleActive(true)">
          <el-input
            ref="textarea"
            v-model="messageInput"
            type="textarea"
            maxlength="100"
            show-word-limit
            placeholder="请输入您的问题"
            clearable
            rows="3"
            resize="none"
            @focus="focus"
            @blur="blur"
            @input="checkInput"
            @keyup.enter="sendMessage"
            @keydown.up="changeInput('up')"
            @keydown.down="changeInput('down')"
          />
          <el-button class="send-btn" round :disabled="!allowSend" @click="sendMessage">
            <SvgIcon icon-class="send2" :width="16" :height="16" />
            <span class="text">发送</span>
          </el-button>
        </div>
      </div>
    </div>
  </el-drawer>
</template>
<script>
import { reactive, toRefs, watch, ref, nextTick } from 'vue';
import SvgIcon from '@/components/SvgIcon';
import { makePy } from '@/utils/chinese-to-str';
import { getLoginInfo } from '@/utils/auth';
import 'whatwg-fetch';

export default {
  name: 'DialogIntelligentService',
  components: { SvgIcon },
  props: {
    dialogVisiable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      dialogShow: false,
      messageInput: '',
      title: '',
      container: ref(),
      allowSend: false,
      chatPending: false,
      active: false,
      chatScroll: ref(),
      conversationId: '',
      selectIndex: 0,
      RESOURCE_SECRET_KEY: 'app-drH52TuCSQbxZ7VqHRtL7OLf',
      RESOURCE_URI: 'http://*************/v1/chat-messages',
      messageList: [
        {
          response_mode: 'robot',
          query: '您好，我是LIMS系统AI助手，有什么可以帮你？'
        }
      ], // 聊天记录
      sendMessageList: [], // 发送出去的聊天记录
      textarea: ref()
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisiable;
      // if (state.dialogShow) {
      // }
    });

    const handleOpened = () => {
      if (state.textarea) {
        state.textarea.focus();
      }
    };
    const handleClose = () => {
      context.emit('closeDialog');
      new AbortController().abort();
      state.dialogShow = false;
    };

    const toggleActive = value => {
      state.active = value;
    };

    const focus = () => {
      if (!state.textarea) {
        return;
      }
      toggleActive(true);
      state.textarea.focus();
    };

    const blur = () => {
      if (!state.textarea) {
        return;
      }
      toggleActive(false);
      state.textarea.blur();
    };

    const sendMessage = () => {
      const params = {
        conversation_id: state.conversationId,
        inputs: {},
        query: state.messageInput,
        user: getLoginInfo().username,
        response_mode: 'streaming'
      };
      state.messageList.push(params);
      state.sendMessageList.push(state.messageInput);
      state.selectIndex = state.sendMessageList.length - 1;
      state.messageInput = '';
      state.messageList.push({ response_mode: 'robot', query: '' });
      setTimeout(() => {
        bottomScrollClick();
        createEventSource(params);
      });
    };
    const createEventSource = params => {
      state.chatPending = true;
      const headers = new Headers();
      headers.set('Authorization', 'Bearer ' + state.RESOURCE_SECRET_KEY);
      headers.set('Content-Type', 'application/json');
      fetch(state.RESOURCE_URI, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(params)
      }).then(response => {
        const reader = response.body?.getReader();
        const decoder = new TextDecoder();
        reader?.read().then(function processText({ done, value }) {
          if (done) {
            state.chatPending = false;
            return;
          }
          reader.read().then(processText);
          typeWriting(decoder.decode(value));
          bottomScrollClick();
        });
      });
    };
    const typeWriting = response => {
      const responseData = response.split('data: ');
      const messageFont = JSON.parse(responseData[responseData.length - 1]);
      state.conversationId = messageFont.conversation_id;
      var newMessage = '';
      if (messageFont.answer.indexOf('\n\n') > -1) {
        newMessage = messageFont.answer.replace('\n\n', '<br/>');
      } else if (messageFont.answer.indexOf('\n') > -1) {
        newMessage = messageFont.answer.replace('\n', '<br/>');
      } else {
        newMessage = messageFont.answer;
      }
      state.messageList[state.messageList.length - 1].query += newMessage;
    };
    // 滚动到底部
    const bottomScrollClick = () => {
      state.chatScroll.scrollTo({ top: state.chatScroll.scrollHeight });
    };
    // 删除聊天记录
    const handleDeleteHistory = () => {
      state.messageList = [
        {
          response_mode: 'robot',
          query: '您好，我是LIMS系统AI助手，有什么可以帮你？'
        }
      ];
      state.conversationId = '';
      state.sendMessageList = [];
      nextTick(() => {
        state.textarea.focus();
      });
    };
    // 上下键选择已发送的内容
    const changeInput = type => {
      if (type === 'up') {
        if (state.selectIndex >= 0) {
          state.messageInput = state.sendMessageList[state.selectIndex];
          state.selectIndex -= 1;
        }
      } else {
        if (state.selectIndex < state.sendMessageList.length - 1) {
          state.selectIndex += 1;
          state.messageInput = state.sendMessageList[state.selectIndex];
        }
      }
    };
    const checkInput = () => {
      state.allowSend = state.messageInput.trim() !== '';
    };

    return {
      ...toRefs(state),
      changeInput,
      bottomScrollClick,
      typeWriting,
      handleClose,
      handleOpened,
      sendMessage,
      makePy,
      focus,
      blur,
      checkInput,
      toggleActive,
      handleDeleteHistory
    };
  }
};
</script>
<style lang="scss" scoped>
.drawer-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.sendContent {
  padding-top: 20px;
  width: 100%;
  .send-area {
    position: relative;
    border-radius: 12px;
    box-sizing: border-box;
    height: 100%;
    padding: 2px;
    width: 100%;
    box-shadow: 0px 2px 8px 0px rgba(7, 185, 185, 0.16);
    &.active {
      background-image: linear-gradient(90deg, #5dc1f7, #00b678);
    }
  }
  .el-textarea {
    border: none;
    border-radius: 12px;
  }
  .send-btn {
    position: absolute;
    bottom: 15px;
    right: 15px;
    display: flex;
    align-items: center;
    background: #00b678;
    .text {
      color: #fff;
      padding-left: 6px;
    }
    &:hover {
      background: rgba($color: #00b678, $alpha: 0.7);
    }
  }
}
.messageRow {
  line-height: 20px;
  display: flex;
  margin-bottom: 15px;
  .message {
    display: flex;
    align-items: center;
    border-radius: 12px;
    border-top-left-radius: 0;
    background: #fff;
    border: 1px solid #ddf1ec;
    box-shadow: 0px 2px 4px 0px rgba(7, 185, 185, 0.08);
    padding: 12px 16px;
    max-width: 88%;
    text-align: left;
    margin-left: 10px;
    span {
      display: inline-block;
      text-align: justify;
    }
  }
  .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.04);
  }
}
.userMssage {
  flex-direction: row-reverse;
  .message {
    margin-left: 0;
    margin-right: 10px;
    border-radius: 12px;
    border-top-right-radius: 0;
    color: #fff;
    background-color: #00b678;
    box-shadow: 0px 2px 4px 0px rgba(7, 185, 185, 0.1);
    span {
      display: inline-block;
      text-align: justify;
    }
  }
  .name-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #00b678;
    width: 32px;
    height: 32px;
    font-size: 16px;
    overflow: hidden;
    color: #fff;
    border-radius: 50%;
  }
}
.chatHistory {
  flex: 1;
  position: relative;
  overflow-y: auto;
  word-break: break-all;
}
.chatHistory::-webkit-scrollbar {
  width: 0 !important;
}
.sendContent {
  :deep(.el-textarea__inner) {
    border-radius: 10px;
    border-color: #fff;
    padding-top: 10px;
    padding-bottom: 44px;
  }
  :deep(.el-input__count) {
    left: 15px;
    bottom: 10px;
    right: auto;
  }
}
:deep(.el-input-group__append) {
  background-color: #00b678;
  color: #fff;
}
.sk-cube-grid {
  display: flex;
  align-items: center;
}
.sk-cube-grid .sk-cube {
  width: 5px;
  height: 5px;
  background-color: gray;
  float: left;
  border-radius: 5px;
  margin-right: 2px;
  -webkit-animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
  animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
}
.sk-cube-grid .sk-cube1 {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}
.sk-cube-grid .sk-cube2 {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}
.sk-cube-grid .sk-cube3 {
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
}
@-webkit-keyframes sk-cubeGridScaleDelay {
  0%,
  70%,
  100% {
    -webkit-transform: scale3D(1, 1, 1);
    transform: scale3D(1, 1, 1);
  }
  35% {
    -webkit-transform: scale3D(0, 0, 1);
    transform: scale3D(0, 0, 1);
  }
}
@keyframes sk-cubeGridScaleDelay {
  0%,
  70%,
  100% {
    -webkit-transform: scale3D(1, 1, 1);
    transform: scale3D(1, 1, 1);
  }
  35% {
    -webkit-transform: scale3D(0, 0, 1);
    transform: scale3D(0, 0, 1);
  }
}
</style>
<style lang="scss">
.drawerServe {
  width: 480px !important;
  border-radius: 20px;
  right: 20px !important;
  top: 20px !important;
  bottom: 20px !important;
  height: auto !important;
  border: 1px solid #a6c8d8;
  box-shadow: 0px 4px 10px 0px rgba(19, 100, 128, 0.2);
  .el-drawer__body {
    position: relative;
    padding: 20px !important;
    background: radial-gradient(80% 61% at 100% 0%, #d0f2f4 0%, rgba(208, 242, 244, 0) 100%),
      radial-gradient(103% 54% at 0% 0%, #eeffeb 0%, rgba(238, 255, 235, 0) 100%),
      linear-gradient(180deg, #cdf6f5 17%, #e2f7ee 55%);
  }
  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 30px;
    .title {
      font-size: 18px;
      color: $tes-font;
    }
    .clear-btn {
      padding: 0.25rem 0.75rem !important;
      margin-right: 20px;
      background: rgba(0, 182, 120, 0);
      border: 1px solid rgba(0, 182, 120, 0.5);
      transition: background 150ms;
      span {
        display: inline-block;
        padding-left: 5px;
        font-size: 14px;
        color: #00b678;
      }
      &:hover {
        background: rgba(0, 182, 120, 0.1);
      }
    }
    .close-btn {
      font-size: 20px;
      color: $tes-font;
      cursor: pointer;
      &:hover {
        color: #00b678;
      }
    }
    .header-right {
      display: inline-flex;
      align-items: center;
    }
  }
}
</style>
