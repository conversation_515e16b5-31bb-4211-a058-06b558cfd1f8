<template>
  <!-- 追根溯源 -->
  <ListLayout :has-custom-header="true" :has-custom-main="true" :main-offset-top="topHeight">
    <template #page-custom-header>
      <SingleLineHeader>
        <template #left-form-group>
          <el-form
            ref="editFrom"
            label-width="110px"
            label-position="top"
            :inline="true"
            :model="searchForm"
            @submit.prevent
          >
            <el-row :gutter="10">
              <el-col :span="9">
                <el-form-item label="物资分类：" prop="materialCode">
                  <el-select
                    v-model="searchForm.materialCode"
                    filterable
                    size="small"
                    placeholder="请选择物资分类"
                    @change="changeMaterialCode"
                  >
                    <el-option v-for="item in materialList" :key="item.value" :label="item.name" :value="item.code" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="模糊搜索：" prop="key">
                  <el-input
                    ref="inputKeyRef"
                    v-model="searchForm.key"
                    size="small"
                    placeholder="请输入委托单号/申请单号/样品号/报告号"
                    controls-position="right"
                    clearable
                    :min="2"
                    :max="10"
                    @change="changeSearchKey"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
        <template #right-button-group>
          <el-button
            key="sample"
            type="text"
            size="small"
            @click="renderSampleData()"
            @keyup.prevent
            @keydown.enter.prevent
          >
            <span class="el-icon-data-analysis" />
          </el-button>
          <el-button key="cancel" size="small" @click="cancelRender()" @keyup.prevent @keydown.enter.prevent
            >清空</el-button
          >
          <el-button
            :loading="renderLoading"
            type="primary"
            size="small"
            @click="search()"
            @keyup.prevent
            @keydown.enter.prevent
            >查询</el-button
          >
        </template>
      </SingleLineHeader>
    </template>
    <template #page-custom-main>
      <el-row>
        <el-col :span="24">
          <CustomPanel :has-margin-right="true" :has-margin-bottom="true" :has-margin-left="true">
            <template #panel-content>
              <v-chart
                v-if="showChart"
                :option="treeChartOption"
                :width="'100%'"
                :height="'70vh'"
                @click="gotoDetail"
              />
              <el-empty v-else :image="emptyImg" description="暂无图表" />
            </template>
          </CustomPanel>
        </el-col>
      </el-row>
    </template>
  </ListLayout>
</template>

<script>
import { reactive, toRefs, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import router from '@/router/index.js';
import ListLayout from '@/components/ListLayout';
import SingleLineHeader from '@/components/PageComponents/SingleLineHeader';
import CustomPanel from '@/components/PageComponents/CustomPanel';
import VChart from '@/components/VChart';
import { treeData } from '../data/testData';
import emptyImg from '@/assets/img/empty-chart.png';

export default {
  name: 'TracingSource',
  components: {
    ListLayout,
    SingleLineHeader,
    CustomPanel,
    VChart
  },
  setup(props) {
    const store = useStore();
    const route = useRoute();
    console.log('store', store);
    console.log('route', route);
    const data = reactive({
      searchForm: {
        // 物资分类
        materialCode: '',
        // 模糊搜索
        key: ''
      },
      topHeight: 140,
      materialList: store.state.user.materialList,
      showChart: false,
      renderLoading: false
    });
    // 过滤审批状态颜色
    const filterExamineStatus = status => {
      // 审批状态  1、待提交2、待审核、3、待签字4、待盖章5、待发送6、已完成
      const classMap = {
        1: ['info', '待提交'],
        2: ['warning', '待审核'],
        3: ['warning', '待签字'],
        4: ['warning', '待盖章'],
        5: ['warning', '待发送'],
        6: ['success', '已完成']
      };
      return classMap[status];
    };
    const labelStatusStyle = {
      fontSize: 12,
      borderRadius: 4,
      padding: [4, 6, 2, 4]
    };
    const treeChartOption = reactive({
      tooltip: {
        trigger: 'item',
        triggerOn: 'mousemove'
      },
      series: [
        {
          type: 'tree',
          id: 0,
          name: 'tree1',
          data: [],
          left: 0,
          right: '20%',
          top: 0,
          bottom: 0,
          // symbol: 'arrow',
          symbol: 'none',
          symbolSize: 7,
          symbolRotate: -90,
          // symbolOffset: [-100, 0],
          edgeShape: 'polyline',
          edgeForkPosition: '70%',
          initialTreeDepth: 3,
          // itemStyle: {
          //   color: 'green',
          //   borderCap: 'round',
          //   borderJoin: ''
          // },
          lineStyle: {
            width: 2
          },
          label: {
            padding: [0, 8],
            backgroundColor: '#fff',
            // backgroundColor: 'red',
            position: 'inside',
            verticalAlign: 'middle',
            align: 'left',
            formatter: params => {
              console.log('params', params);
              const statusArr = filterExamineStatus(params.data.status);
              return `{label|${params.data.name}}` + (statusArr ? ` {${statusArr[0]}|${statusArr[1]}}` : '');
            },
            rich: {
              label: {
                color: store.state.settings.theme,
                fontSize: 18,
                lineHeight: 10
              },
              info: {
                color: '#FFF',
                backgroundColor: '#909399',
                borderColor: '#909399',
                ...labelStatusStyle
              },
              warning: {
                color: '#fff',
                backgroundColor: '#E6A23C',
                borderColor: '#E6A23C',
                ...labelStatusStyle
              },
              success: {
                color: '#FFF',
                backgroundColor: '#67C23A',
                borderColor: '#67C23A8',
                ...labelStatusStyle
              }
            }
          },
          leaves: {
            label: {
              padding: [0, 4],
              // backgroundColor: 'green',
              position: 'insideLeft'
            }
          },
          emphasis: {
            disabled: true,
            focus: 'none'
          },
          expandAndCollapse: false,
          animationDuration: 550,
          animationDurationUpdate: 750
        }
      ]
    });

    // 切换物资分类
    const changeMaterialCode = item => {
      console.log('changeMaterialCode item', item);
    };

    // 模糊搜索
    const changeSearchKey = value => {
      data.searchForm.key = value;
    };

    // 查询
    const search = () => {
      console.log('search');
      data.renderLoading = true;
      setTimeout(() => {
        data.renderLoading = false;
      }, 1500);
    };

    // 展示演示数据
    const renderSampleData = () => {
      console.log('renderSampleData');
      replaceRouteUrlParameter('1685942770140045323');
      data.showChart = true;
      treeChartOption.series[0].data = [treeData];
    };

    // 路由路径参数替换和添加，从详情页返回的时候保留id来查询数据显示
    const replaceRouteUrlParameter = id => {
      console.log('replaceRouteUrlParameter', route.path + '?id=' + id);
      router.push({
        path: route.path,
        query: { id }
      });
    };

    // 跳转详情页
    const gotoDetail = params => {
      console.log('gotoDetail', params);
      if (params?.data?.type) {
        const detailType = params.data.type;
        switch (detailType) {
          case 'entrust': {
            router.push({
              path: '/smart-charts/tracing-source-registration',
              query: {
                id: '1685942770140045323',
                flag: 1
              }
            });
            break;
          }

          case 'sample': {
            router.push({
              path: '/smart-charts/tracing-source-sample-deteail',
              query: {
                orderId: '1526417936054333451',
                sampleId: '1526417924821987363'
              }
            });
            break;
          }

          case 'report': {
            router.push({
              path: '/smart-charts/tracing-source-report-deteail',
              query: {
                reportId: '1681950839139336200',
                sampleId: '1677227310166831200',
                reportStage: '6'
              }
            });
            break;
          }

          case 'test-item': {
            // do someting
            break;
          }
        }
      }
    };

    const cancelRender = () => {
      data.showChart = false;
    };

    onMounted(() => {
      const id = route.query.id;
      console.log('onMounted router', router);
      console.log('onMounted route', route);
      if (id) {
        renderSampleData();
        replaceRouteUrlParameter(id);
      }
    });

    return {
      ...toRefs(data),
      emptyImg,
      treeChartOption,
      changeMaterialCode,
      changeSearchKey,
      search,
      renderSampleData,
      gotoDetail,
      cancelRender
    };
  }
};
</script>

<style lang="scss" scoped></style>
