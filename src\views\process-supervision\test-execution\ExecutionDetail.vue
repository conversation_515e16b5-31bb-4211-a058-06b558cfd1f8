<template>
  <!-- 检测执行 样品视图详情页-->
  <ListLayout
    :has-quick-query="false"
    :has-left-panel="true"
    :has-aside-padding="false"
    :aside-panel-width="asidePanelWidth"
    :aside-max-width="asidePanelWidth"
    :aside-min-width="asidePanelWidth"
    aside-height="100%"
    main-height="100%"
  >
    <template #search-bar>
      <div class="page-title">样品编号：{{ jsonData.secSampleNum }}</div>
    </template>
    <template #button-group>
      <el-button
        v-if="jsonData.isower && getPermissionBtn('MakeSample')"
        type="primary"
        plain
        size="large"
        @click="handleMakeSample()"
        >制作小样</el-button
      >
      <el-button
        v-if="jsonData.isower && getPermissionBtn('AddTestItem')"
        type="primary"
        plain
        size="large"
        icon="el-icon-plus"
        @click="handleAddItem()"
        >添加检测项目</el-button
      >
      <el-button
        v-if="jsonData.isower && getPermissionBtn('informationSupplement')"
        type="primary"
        plain
        size="large"
        @click="handleSupplement()"
        >样品信息补充</el-button
      >
      <el-button
        v-if="jsonData.isower && getPermissionBtn('templateHomeDownload')"
        type="primary"
        plain
        size="large"
        icon="el-icon-download"
        @click="downLoadTemplateHome(jsonData.secSampleNum, jsonData.mateType)"
        >模板首页下载</el-button
      >
      <el-button
        v-if="jsonData.isower && getPermissionBtn('recordAddSample')"
        size="large"
        icon="el-icon-files"
        type="primary"
        plain
        @click="dialogRecipients = true"
        >样品领用</el-button
      >
      <el-button
        v-if="jsonData.isower && tableList[activeIndex] && tableList[activeIndex].length !== 0"
        type="primary"
        icon="el-icon-collection-tag"
        @click="handleSafeguard()"
        @keyup.prevent
        @keydown.enter.prevent
        >试样分组</el-button
      >
    </template>
    <template #page-left-side>
      <div class="left-container">
        <div class="left-header">
          <div class="left-title">
            <span>检测项目</span>
            <el-popover placement="right" trigger="hover" popper-class="popover-content">
              <template #reference><i class="el-icon-info" /></template>
              <div class="content">
                <p>标识说明</p>
                <div class="popover-item"><span class="testing-icon text-copy">复</span>复测项目</div>
                <div class="popover-item"><span class="testing-icon text-origin">源</span>初始项目</div>
                <div class="popover-item"><span class="testing-icon text-change">改</span>数据修改</div>
                <div class="popover-item"><span class="testing-icon text-back">退</span>退回项目</div>
                <div class="popover-item"><span class="testing-icon text-copy">特</span>特殊项目</div>
                <div class="popover-item"><span class="testing-icon text-info">增</span>新增项目</div>
              </div>
            </el-popover>
          </div>
          <el-checkbox v-model="giveMeChecked" @change="handleFilter">仅看我的</el-checkbox>
        </div>
        <el-menu
          :default-active="activeItem?.status?.toString()"
          class="header-menu"
          size="small"
          mode="horizontal"
          @select="changeStatus"
        >
          <el-menu-item
            v-for="(item, index) in statusList"
            :key="index"
            :index="item?.value?.toString()"
            class="header-menu-item"
          >
            {{ item.label }}
            {{ item.number }}
          </el-menu-item>
        </el-menu>
        <TestingList
          ref="tableRef"
          :key="tableKey"
          :data="tableList[activeIndex]"
          item-type="project"
          :style="{ flexGrow: 1 }"
          @row-click="handleRowClick"
        />
      </div>
    </template>
    <div class="right-container">
      <div class="favorite-zone">
        <el-tooltip
          v-if="isShowRaw && getPermissionBtn('TemplateCopyAffix')"
          content="复制原始记录模板数据"
          effect="light"
          placement="left"
        >
          <el-button plain type="primary" size="small" icon="el-icon-document" @click="handleCopyValue()"
            >一键复制</el-button
          >
        </el-tooltip>
        <el-tooltip
          v-if="activeItem.status == 2"
          :content="remindType[jsonData.capabilityId] ? '修改提醒' : '添加提醒'"
          effect="light"
          placement="left"
        >
          <el-button
            plain
            size="small"
            :type="remindType[jsonData.capabilityId] ? 'warning' : 'primary'"
            :icon="remindType[jsonData.capabilityId] ? 'el-icon-message-solid' : 'el-icon-bell'"
            @click="handleRemind(jsonData, remindType[jsonData.capabilityId] ? 'edit' : 'add')"
            >{{ remindType[jsonData.capabilityId] ? '修改提醒' : '提醒' }}</el-button
          >
        </el-tooltip>
        <el-tooltip :content="jsonData.isFavorite === 1 ? '取消收藏' : '添加收藏'" effect="light" placement="left">
          <el-button
            plain
            size="small"
            type="primary"
            :icon="jsonData.isFavorite === 1 ? 'el-icon-star-on' : 'el-icon-star-off'"
            @click="handleFavorite(jsonData)"
            >{{ jsonData.isFavorite === 1 ? '取消收藏' : '收藏' }}</el-button
          >
        </el-tooltip>
      </div>
      <el-tabs v-model="activeName" tab-position="right" @tab-change="handleChangeTabs">
        <el-tab-pane label="原始记录" name="1">
          <div class="middle-container">
            <!-- 复测说明 -->
            <el-alert
              v-if="jsonData.retestSourceId !== '' && jsonData.retestReason && jsonData.status == 2"
              :title="'复测说明：' + jsonData.retestReason"
              type="error"
            />
            <!-- 退回说明 -->
            <el-alert
              v-if="jsonData.isBack === 1 && jsonData.backReason && jsonData.status == 2"
              :title="'退回原因：' + jsonData.backReason"
              type="warning"
            />
            <!-- 提醒说明 -->
            <el-alert
              v-if="remindType[jsonData.capabilityId] && activeItem.status == 2"
              :title="remindTitle"
              type="success"
            />
            <div v-if="tableList[activeIndex] && tableList[activeIndex].length !== 0" class="middle-content">
              <FirstTab
                v-if="!isShowRaw"
                :json-data="jsonData"
                :sample-collection-list="sampleCollectionList"
                @refreshDetail="handleRefreshDetail"
              />
              <FirstTabRaw
                v-if="isShowRaw"
                :json-data="jsonData"
                :left-ul="tableList[activeIndex]"
                @refreshDetail="handleRefreshDetail"
                @getList="getList"
                @changeStatus="changeStatus"
              />
            </div>
            <div v-else class="empty-box">
              <el-empty :image="emptyImg" description="暂无模板" />
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="项目检验记录" name="2">
          <SecondTab :json-data="jsonData" :active-name="activeName" />
        </el-tab-pane>
        <el-tab-pane label="操作日志" name="3">
          <OperationLogs :experiment-id="jsonData.experimentId" :active-name="activeName" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <module-safeguard
      :json-data="jsonData"
      :dialog-sheath="dialogSheath"
      @closexx="closexx"
      @save-color="saveSampleColor"
    />
    <!-- 样品领用弹出窗 -->
    <DialogRecipients
      :json-data="jsonData"
      :dialog-recipients="dialogRecipients"
      :sample-collection-list="sampleCollectionList"
      @closeRecipients="closeRecipients"
    />
    <!-- 提醒弹出窗 -->
    <el-dialog
      v-model="dialogRemind"
      :title="isAddRemind ? '新增提醒' : '编辑待提醒'"
      :close-on-click-modal="false"
      width="480px"
      custom-class="remind_dialog"
    >
      <el-form
        v-if="dialogRemind"
        ref="ruleForm"
        v-loading="dialogLoading"
        :model="formData"
        label-position="right"
        label-width="90px"
        size="small"
      >
        <el-form-item
          label="标题："
          prop="submit.title"
          :rules="{ required: true, message: '请输入标题', trigger: 'change' }"
        >
          <el-input
            v-model="formData.submit.title"
            v-trim
            size="small"
            type="text"
            maxlength="50"
            placeholder="请输入标题"
          />
        </el-form-item>
        <el-form-item
          label="试验员："
          prop="realOwnerIds"
          :rules="{ required: true, message: '请选择试验员', trigger: 'change' }"
        >
          <el-select
            v-model="formData.realOwnerIds"
            clearable
            multiple
            placeholder="请选择"
            class="submit-form-item"
            style="width: 100%"
          >
            <el-option v-for="item in nameList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="等级："
          prop="submit.level"
          :rules="{ required: true, message: '请选择等级', trigger: 'change' }"
        >
          <el-select v-model="formData.submit.level" placeholder="请选择等级" size="small" style="width: 100%">
            <el-option label="较弱" :value="0" />
            <el-option label="一般" :value="1" />
            <el-option label="重要" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="截止时间："
          prop="submit.expireTime"
          :rules="{ required: true, message: '请选择截止时间', trigger: 'change' }"
        >
          <el-date-picker
            v-model="formData.submit.expireTime"
            popper-class="no-atTheMoment"
            type="datetime"
            :disabled-date="disabledDate"
            clearable
            placeholder="请选择截止时间"
            size="small"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item
          label="提醒频率："
          prop="submit.remindTimeList"
          :rules="{ required: true, message: '请选择截止时间', trigger: 'change' }"
        >
          <el-select
            v-model="formData.submit.remindTimeList"
            multiple
            placeholder="请选择提醒频率"
            clearable
            size="small"
            style="width: 73%"
          >
            <el-option v-for="(val, key) in scheduleList" :key="key" :label="val" :value="key" />
          </el-select>
          <el-date-picker
            ref="scheduleRef"
            v-model="scheduleTime"
            type="datetime"
            popper-class="no-atTheMoment"
            clearable
            :editable="false"
            class="scheduleTime"
            @change="changeSchedule"
          />
          <span class="blue-color cursor" style="float: right; margin-right: 0" @click="addScheduleTime"
            >添加指定时间</span
          >
        </el-form-item>
        <el-form-item label="内容：" prop="submit.content">
          <el-input
            v-model="formData.submit.content"
            type="textarea"
            maxlength="100"
            :rows="2"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button size="small" :loading="dialogLoading" @click="dialogRemind = false">取 消</el-button>
          <el-button v-if="!isAddRemind" size="small" :loading="dialogLoading" type="danger" @click="deleteRemind()"
            >删 除</el-button
          >
          <el-button size="small" :loading="dialogLoading" type="primary" @click="onSubmitRemind()">确 认</el-button>
        </span>
      </template>
    </el-dialog>
    <DialogSupplement
      :show="dialogSupplement"
      :sample-id="samplesId"
      :data="{ secSampleNum: jsonData.secSampleNum, prodType: jsonData.prodType, samplesName: jsonData.samplesName }"
      :material-category-code="jsonData.mateType"
      @closeDialog="handleCloseDialog"
    />
    <!-- 添加检测项目 -->
    <AddInspectionItem
      :show="showAddItem"
      :data="alreadyList"
      :material-category-code="jsonData.mateType"
      @close="closeItemDialog"
      @selectData="selectData"
    />
    <!-- 制作小样 -->
    <DialogMakeSample
      :show="makeSampleDailog"
      :data="{ secSampleNum: jsonData.secSampleNum, prodType: jsonData.prodType, samplesName: jsonData.samplesName }"
      :sample-id="samplesId"
      :material-category-code="jsonData.mateType"
      @closeDialog="closeMakeDialog"
    />
  </ListLayout>
</template>

<script>
import { reactive, toRefs, ref, onMounted, watch, computed, getCurrentInstance } from 'vue';
import { useRoute } from 'vue-router';
import TestingList from './components/TestingList';
import FirstTab from './components/FirstTab';
import FirstTabRaw from './components/FirstTabRaw';
import SecondTab from './secondTab';
import OperationLogs from './operationLogs';
import { getDictionary } from '@/api/user';
import DialogRecipients from './DialogRecipients.vue';
import DialogSupplement from '@/components/DialogSupplement';
import DialogMakeSample from './components/dialog-make-sample';
import ModuleSafeguard from '@/components/BusinessComponents/ModuleSafeguard';
import ListLayout from '@/components/ListLayout';
import { formatDate, formatDateTime } from '@/utils/formatTime';
import {
  capabilityBysamplesId,
  experimentmongodatainfo,
  TemplateIdByexperimentId,
  favoriteItem,
  unFavoriteItem,
  getItemRemindType,
  getRemindInfo,
  saveRemindInfo,
  deleteRemindApi,
  sampleHomePrint
} from '@/api/execution';
import { getSampleCollectionList } from '@/api/sampleItemTest';
import { getNamesByid, getPermissionBtn, getNameOptionaByid } from '@/utils/common';
import { getLoginInfo } from '@/utils/auth';
// import { getCapabilityUplist } from '@/api/user'
import _ from 'lodash';
import store from '@/store';
import AddInspectionItem from '@/components/BusinessComponents/AddInspectionItem';
import { getDistributionInfoById, saveDistributionInfo } from '@/api/allocation';
import emptyImg from '@/assets/img/empty-template.png';
import { decryptCBC } from '@/utils/ASE';
export default {
  name: 'ExecutionDetail',
  components: {
    TestingList,
    FirstTab,
    DialogMakeSample,
    SecondTab,
    OperationLogs,
    DialogRecipients,
    FirstTabRaw,
    ModuleSafeguard,
    ListLayout,
    DialogSupplement,
    AddInspectionItem
  },
  setup(ctx) {
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    watch(
      () => route.query,
      () => {
        // getdata()
      }
    );
    const state = reactive({
      asidePanelWidth: 360,
      copyData: [], // 模板复制的值集合
      dialogSupplement: false,
      experimentCapabilityList: [], // 检测分配已经保存过的数据
      showAddItem: false, // 添加检测项目
      makeSampleDailog: false, // 制作小样弹出框
      remindContent: '',
      alreadyList: [],
      scheduleTime: '',
      scheduleList: {
        0: '截止时',
        15: '截止前15分钟',
        60: '截止前1小时',
        180: '截止前3小时',
        1440: '截止前1天'
      },
      sampleCollectionList: [], // 样品领用记录
      scheduleRef: ref(),
      dialogRemind: false,
      dialogLoading: false,
      isAddRemind: false,
      nameList: store.state.common.nameList,
      nameJson: {},
      disabledDate: time => {
        return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
      },
      ruleForm: ref(),
      ruleFormSupplement: ref(),
      levelJson: {}, // 级别
      levelArray: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ], // 级别
      formData: {
        realOwnerIds: [getLoginInfo().accountId],
        submit: {
          remindTimeList: []
        }
      },
      remindType: {}, // 待办状态
      remindInfo: {
        title: '',
        content: '',
        expireTime: ''
      },
      tableKey: ref(),
      tableRef: ref(),
      testipt: '',
      dialogSheath: false,
      dialogRecipients: false, // 样品领用弹出窗
      activeItem: {
        status: 2
      },
      accountId: getLoginInfo().accountId,
      leftList: [],
      samplesId: route.query.samplesId,
      capabilityId: route.query.capabilityId,
      avtivestatus: route.query.avtivestatus,
      orderId: '',
      isShowRaw: true, // 区分tab原始记录的两种情况
      activeName: '1', // 右侧tab的选中
      jsonData: {
        status: null
      },
      statusList: [
        { value: 2, label: '待提交', number: 0 },
        { value: 3, label: '待审核', number: 0 },
        { value: 5, label: '已通过', number: 0 }
      ],
      statusClass: {
        3: 'fu',
        4: 'yuan'
      },
      giveMeChecked: false,
      allTableList: [],
      tableList: []
    });
    onMounted(() => {
      // 左侧表格的高亮行
    });
    const filterName = () => {
      state.nameList.forEach(item => {
        state.nameJson[item.id] = item.username;
      });
    };
    filterName();
    const activeIndex = computed(() => {
      if (state.activeItem.status === 2) {
        return 0;
      }
      if (state.activeItem.status === 3) {
        return 1;
      }
      if (state.activeItem.status === 5) {
        return 2;
      }
      return 0;
    });
    const remindTitle = computed(() => {
      return `${state.remindInfo.title}提醒：${state.remindInfo.expireTime} 时截止${
        state.remindInfo.content ? '，内容：' : ''
      }${state.remindInfo.content}`;
    });
    // 局部刷新左边列表数据
    const getList = () => {
      capabilityBysamplesId(state.samplesId).then(res => {
        if (res.data.code === 200) {
          if (res.data.data.length > 0) {
            state.tableList = res.data.data;
            state.allTableList = JSON.parse(JSON.stringify(res.data.data));
            state.statusList[0].number = state.tableList[0].length;
            state.statusList[1].number = state.tableList[1].length;
            state.statusList[2].number = state.tableList[2].length;
            getRemindType();
            changeStatus(state.activeItem.status);
            // handleRowClick(state.activeItem)
            // state.tableRef.setCurrentRow(state.activeItem)
          }
        }
      });
    };
    // 获取待办状态
    const getRemindType = () => {
      const arrayItem = state.tableList[0];
      const params = {
        sampleId: route.query.samplesId,
        capabilityIds: []
      };
      if (arrayItem) {
        params.capabilityIds = arrayItem.map(item => {
          return item.capabilityId;
        });
        getItemRemindType(params).then(res => {
          if (res) {
            state.remindType = res.data.data;
          }
        });
      }
    };
    // 过滤仅看我的的项目
    const handleFilter = val => {
      if (val) {
        state.tableList[0] = state.allTableList[0].filter(item => {
          return (
            item.ownerIds.split(',').some(val => val === state.accountId) ||
            item.reviewerId.split(',').some(val => val === state.accountId)
          );
        });
        state.statusList[0].number = state.tableList[0].length;
        state.tableList[1] = state.allTableList[1].filter(item => {
          return (
            item.ownerIds.split(',').some(val => val === state.accountId) ||
            item.reviewerId.split(',').some(val => val === state.accountId)
          );
        });
        state.statusList[1].number = state.tableList[1].length;
        state.tableList[2] = state.allTableList[2].filter(item => {
          return (
            item.ownerIds.split(',').some(val => val === state.accountId) ||
            item.reviewerId.split(',').some(val => val === state.accountId)
          );
        });
        state.statusList[2].number = state.tableList[2].length;
        changeStatus(state.activeItem.status);
      } else {
        getList();
      }
    };
    // 刷新数据 getdata isRefresh：修改部分字段，局部刷新
    const getdata = isRefresh => {
      capabilityBysamplesId(state.samplesId).then(res => {
        if (res.data.code === 200) {
          if (res.data.data.length > 0) {
            state.tableList = res.data.data;
            state.allTableList = JSON.parse(JSON.stringify(res.data.data));
            state.statusList[0].number = state.tableList[0].length;
            state.statusList[1].number = state.tableList[1].length;
            state.statusList[2].number = state.tableList[2].length;
            getRemindType();
            if (isRefresh) {
              initRefresh();
            } else {
              initDetail();
            }
          }
        }
      });
    };
    getdata();
    const initDetail = () => {
      var allList = state.tableList[0].concat(state.tableList[1], state.tableList[2]);
      state.leftList = allList;
      if (state.capabilityId && route.query.capabilityId !== '') {
        state.activeItem = allList.find(item => {
          return item.capabilityId === state.capabilityId;
        });
        state.jsonData = JSON.parse(JSON.stringify(state.activeItem));
        handleRowClick(state.activeItem);
        state.tableRef.setCurrentRow(state.activeItem);
      } else {
        if (state.avtivestatus) {
          if (state.tableList[state.avtivestatus].length !== 0) {
            state.activeItem = state.tableList[state.avtivestatus][0];
            if (state.remindType[state.jsonData.capabilityId]) {
              getRemind(state.activeItem);
            }
            changeStatus(state.activeItem.status);
          }
        } else {
          state.activeItem = allList[0];
          handleRowClick(state.activeItem);
          state.tableRef.setCurrentRow(state.activeItem);
        }
      }
    };
    const initRefresh = () => {
      var allList = state.tableList[0].concat(state.tableList[1], state.tableList[2]);
      state.leftList = allList;
      state.activeItem = allList.find(item => {
        return item.capabilityId === state.activeItem.capabilityId;
      });
      state.tableRef.setCurrentRow(state.activeItem);
      handleRowClick(state.activeItem);
    };
    const changeStatus = val => {
      state.activeItem.status = typeof val === 'string' ? Number(val) : val;
      if (state.tableList[activeIndex.value].length !== 0) {
        handleRowClick(state.tableList[activeIndex.value][0]);
        state.tableRef.setCurrentRow(state.tableList[activeIndex.value][0]);
      }
    };
    const initDictionary = () => {
      getDictionary(13).then(res => {
        if (res) {
          state.levelArray[0].group = [];
          state.levelArray[1].group = [];
          res.data.data.dictionaryoption.forEach(item => {
            if (item.status === 1) {
              state.levelArray[0].group.push(item);
            } else {
              state.levelArray[1].group.push(item);
            }
            state.levelJson[item.code] = item.name;
          });
        }
      });
    };
    // 点击下载模板
    const handleRowClick = row => {
      state.capabilityId = row.capabilityId;
      state.jsonData = {
        ...JSON.parse(JSON.stringify(row)),
        disabled: false,
        isower: _.indexOf(row.ownerIds.split(','), state.accountId) !== -1
      };
      if (row.experimentId) {
        downTemplate(row.experimentId);
      }
      getRemind(row);
    };
    const getRemind = row => {
      getRemindInfo({ capabilityId: row.capabilityId, sampleId: row.sampleId }).then(res => {
        if (res) {
          const data = res.data.data;
          if (JSON.stringify(data) !== '{}') {
            state.formData.submit = JSON.parse(JSON.stringify(data));
            state.formData.realOwnerIds = data.testerList.map(item => {
              return item.userId;
            });
            state.remindInfo.title = data.title;
            state.remindInfo.content = data.content;
            state.remindInfo.expireTime = data.expireTime;
          }
        }
      });
    };
    // TemplateIdByexperimentId
    // 下载原始模板
    const downTemplate = experimentId => {
      TemplateIdByexperimentId(experimentId).then(async res => {
        if (res) {
          state.isShowRaw = !!res.data.data.html;
          await experimentmongodatainfo(experimentId).then(async resdata => {
            if (resdata) {
              state.jsonData = {
                ...state.jsonData,
                excelHtml: decryptCBC(res.data.data.html),
                fileNo: res.data.data.fileNo,
                showType: res.data.data.showType,
                source: res.data.data.source,
                experimentData: resdata.data.data,
                templateId: resdata.data.data.templateId,
                realReviewerIdimg: resdata.data.data.reviewerSignUrl.split(','),
                realOwnerIdsimgs: resdata.data.data.ownerSignUrls.split(',')
              };
            }
          });
        }
      });
    };
    // 线芯维护弹屏
    const handleSafeguard = () => {
      state.dialogSheath = true;
    };
    // 下载模板首页
    const downLoadTemplateHome = (secSampleNum, mateType) => {
      sampleHomePrint({ secSampleNum: secSampleNum, mateType: mateType }).then(res => {
        if (res.data.size > 81) {
          const blob = new Blob([res.data], { type: '' });
          const blobUrl = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.download = `原始记录首页.doc`;
          a.href = blobUrl;
          a.click();
          proxy.$message.success('下载附件成功');
        } else {
          proxy.$message.warning('暂无可用模板，请先上传/启用模板');
        }
      });
    };
    const closexx = () => {
      state.dialogSheath = false;
    };

    const saveSampleColor = colorList => {
      state.jsonData.experimentData.coreRecord.coreColourList = colorList;
      state.jsonData.experimentData.saveColorNumber = colorList.length;
    };

    const closeRecipients = val => {
      state.dialogRecipients = false;
      if (val) {
        getRecipientsList();
      }
    };
    const getRecipientsList = () => {
      getSampleCollectionList(route.query.samplesId).then(res => {
        if (res) {
          state.sampleCollectionList = res.data.data;
        }
      });
    };
    getRecipientsList();
    const handleChangeTabs = val => {
      state.activeName = val;
    };
    // 打开样品领用记录添加编辑弹出框
    const handleAddDialog = () => {
      state.dialogSubmit = true;
    };
    const handleRemind = (row, type) => {
      state.dialogRemind = true;
      if (type === 'add') {
        state.isAddRemind = true;
        state.formData = {
          realOwnerIds: [state.accountId],
          submit: {
            title: '【' + row.secSampleNum + '】的【' + row.capabilityName + '】等待检测',
            remindTimeList: ['15'],
            level: 1,
            capabilityName: row.capabilityName,
            capabilityId: row.capabilityId,
            sampleId: row.sampleId,
            sampleNo: row.secSampleNum
          }
        };
      } else {
        state.isAddRemind = false;
      }
    };
    // 复制原始记录模板的值
    const handleCopyValue = () => {
      state.copyData = [];
      const bodyData = [];
      const headerData = [];
      for (var keyHeader in state.jsonData.experimentData.header) {
        headerData.push(state.jsonData.experimentData.header[keyHeader][0]?.value);
      }
      for (var key in state.jsonData.experimentData.body) {
        bodyData.push(state.jsonData.experimentData.body[key][0]?.value);
      }
      if (localStorage.getItem('templateValue') && localStorage.getItem('templateValue') !== '[]') {
        state.copyData = [
          ...new Set(bodyData),
          ...new Set(headerData),
          ...JSON.parse(localStorage.getItem('templateValue'))
        ];
      } else {
        state.copyData = [...new Set(bodyData), ...new Set(headerData)];
      }
      localStorage.setItem('templateValue', JSON.stringify(state.copyData));
      proxy.$message.success('复制成功！');
    };
    const handleFavorite = row => {
      if (row.isFavorite === 0) {
        favoriteItem(row.experimentId).then(res => {
          if (res) {
            proxy.$message.success(res.data.data);
            state.jsonData.isFavorite = 1;
          }
        });
      } else {
        unFavoriteItem(row.experimentId).then(res => {
          if (res) {
            proxy.$message.success(res.data.data);
            state.jsonData.isFavorite = 0;
          }
        });
      }
    };
    const onSubmitRemind = () => {
      state.ruleForm.validate().then(valid => {
        if (valid) {
          const testerList = [];
          state.formData.realOwnerIds.forEach(item => {
            testerList.push({
              userId: item,
              userName: state.nameJson[item]
            });
          });
          const param = {
            ...state.formData.submit,
            expireTime: formatDateTime(state.formData.submit.expireTime),
            testerList: testerList
          };
          const timeDifference = new Date(param.expireTime).getTime() - new Date().getTime();
          const leave1 = timeDifference % (24 * 3600 * 1000); // 计算天数后剩余的毫秒数
          const leave2 = leave1 % (3600 * 1000); // 计算小时数后剩余的毫秒数
          const minutes = Math.floor(leave2 / (60 * 1000));
          if (minutes < 2 && state.remindInfo.expireTime !== param.expireTime) {
            proxy.$message.warning('截止时间请选择至少两分钟后');
            return false;
          }
          saveRemindInfo(param).then(res => {
            if (res) {
              proxy.$message.success(res.data.message);
              state.dialogRemind = false;
              getRemindType();
              getdata();
            }
          });
        }
      });
    };
    const deleteRemind = () => {
      proxy
        .$confirm('是否删除？删除后将不能还原。', '删除确认', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          deleteRemindApi({
            sampleId: state.formData.submit.sampleId,
            capabilityId: state.formData.submit.capabilityId
          }).then(res => {
            if (res) {
              state.dialogRemind = false;
              proxy.$message.success(res.data.message);
              getRemindType();
            }
          });
        })
        .catch(() => {});
    };
    // 添加指定日期
    const addScheduleTime = () => {
      state.scheduleRef.focus();
    };
    const changeSchedule = val => {
      if (val) {
        const data = formatDateTime(val);
        if (
          !state.formData.submit.remindTimeList.some(item => {
            return item === data;
          })
        ) {
          state.formData.submit.remindTimeList.push(data);
        }
        state.scheduleTime = '';
      }
    };
    const handleSupplement = () => {
      state.dialogSupplement = true;
    };
    const handleCloseDialog = () => {
      state.dialogSupplement = false;
    };
    // 添加检测项目
    const handleAddItem = () => {
      state.tableList.forEach(item => {
        item.forEach(val => {
          state.alreadyList.push(val);
        });
      });
      state.showAddItem = true;
    };
    // 数据采集
    const handleDataAcquisition = () => {};
    // 添加项目关闭
    const closeItemDialog = val => {
      state.showAddItem = false;
      state.alreadyList = [];
    };
    // 选中的值
    const selectData = selectData => {
      if (selectData.length) {
        getDistributionInfo(selectData);
      }
    };
    // 获取检测分配已经保存过的数据
    const getDistributionInfo = selectData => {
      getDistributionInfoById({ sampleId: route.query.samplesId }).then(res => {
        if (res) {
          state.experimentCapabilityList = [];
          res.data.data.experimentCapabilityList.forEach(item => {
            item.childList.forEach((val, index) => {
              val.sourceId = val.capabilityParaId;
              val.sourceName = val.name;
              val.sourceUnit = val.unitname;
              val.standardRequirement = val.requirement;
              val.order = index;
            });
            item.standardRequirement = item.requirement;
            item.sourceUnit = item.unitname;
            state.experimentCapabilityList.push(item);
          });
          saveSelectItem(selectData);
        }
      });
    };
    const saveSelectItem = selectData => {
      const addSelectItem = [];
      selectData.forEach((item, index) => {
        item.childList.forEach((val, i) => {
          val.order = i;
          val.sourceName = val.sourcename;
          val.sourceId = val.sourceIdStr;
        });
        addSelectItem.push({
          ...item,
          experimentCategoryId: item.experimentCategoryIdStr,
          sourceId: item.sourceIdStr,
          sourceNumber: item.sourcenumber,
          sourceName: item.sourcename,
          ownerId: state.accountId,
          isManual: 1,
          operationType: 1,
          status: 1,
          experimentStatus: 1,
          sourceCapabilityType: 'Internal',
          order: state.experimentCapabilityList.length + index,
          startDateTime: formatDate(new Date()),
          finishDateTime: formatDate(new Date())
        });
      });
      const params = {
        mateType: state.jsonData.mateType,
        sampleStatus: 2,
        sampleId: route.query.samplesId,
        sampleStandardEntityList: [],
        prodType: state.jsonData.sampleProdTypeList.toString(),
        experimentCapabilityList: [...state.experimentCapabilityList, ...addSelectItem]
      };
      saveDistributionInfo(params).then(res => {
        if (res) {
          getList();
          proxy.$message.success('添加成功！');
        }
      });
    };
    // 制作小样
    const handleMakeSample = () => {
      state.makeSampleDailog = true;
    };
    // 关闭制作小样弹出框
    const closeMakeDialog = () => {
      state.makeSampleDailog = false;
    };
    // 选择小样
    const selectMakeData = data => {
      state.makeSampleDailog = false;
    };
    // 刷新详情
    const handleRefreshDetail = row => {
      state.activeItem = row;
      getdata(true);
    };
    return {
      ...toRefs(state),
      getdata,
      handleRefreshDetail,
      handleMakeSample,
      closeMakeDialog,
      selectMakeData,
      handleCopyValue,
      getDistributionInfo,
      handleAddItem,
      handleDataAcquisition,
      closeItemDialog,
      selectData,
      handleCloseDialog,
      handleSupplement,
      getRemind,
      getRecipientsList,
      deleteRemind,
      addScheduleTime,
      changeSchedule,
      getRemindType,
      formatDateTime,
      handleFavorite,
      filterName,
      onSubmitRemind,
      getNameOptionaByid,
      handleRemind,
      initDictionary,
      emptyImg,
      activeIndex,
      remindTitle,
      handleSafeguard,
      downLoadTemplateHome,
      handleAddDialog,
      getPermissionBtn,
      getList,
      closexx,
      closeRecipients,
      changeStatus,
      getNamesByid,
      handleChangeTabs,
      handleRowClick,
      handleFilter,
      saveSampleColor
    };
  },
  computed: {},
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
:deep(.page-list-main .main-panel) {
  height: calc(100vh - 11rem);
  padding: 0 12px 0 20px;
}

.page-main {
  position: relative;
}
.itemIcon {
  position: absolute;
  right: 40px;
  top: 20px;
  z-index: 99;
}
.cursor:hover {
  cursor: pointer;
}
.icon {
  -webkit-tap-highlight-color: transparent;
  outline: none;
  border: 0;
}
.star {
  font-size: 22px;
  margin-left: 20px;
}
.starInit {
  color: $yellow;
}
.left-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  .left-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;

    .left-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: normal;
      line-height: 24px;

      .el-icon-info {
        margin-left: 6px;
        color: var(--tesPrimary);
        font-size: 18px;
        cursor: pointer;
      }
    }

    .el-button {
      padding: 7px !important;
    }
  }

  .header-menu {
    width: 100%;
    justify-content: space-between;

    .header-menu-item {
      width: 30%;
      height: 40px;
      color: inherit;
      font-size: 16px;
      line-height: 40px;
    }

    .header-menu-item.is-active {
      color: var(--tesPrimary);
      font-weight: 500;
    }
  }
}

.popover-content .popover-item {
  margin-top: 8px;
}

.right-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.right-container:deep(.el-tabs) {
  height: 100%;

  .el-tabs__header {
    margin-left: 0;
    background-color: $background-color;
  }

  .el-tabs__content,
  .el-tab-pane {
    height: 100%;
  }

  .el-tabs__item.is-active {
    background-color: var(--menuHover);
  }
}

.favorite-zone {
  position: absolute;
  right: 10px;
  bottom: 20px;
  z-index: 1;

  .el-button {
    display: block;
    width: 100px;
    margin: 10px 0 0 0;
    padding: 0 8px !important;
  }
}

.middle-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.middle-content {
  flex-grow: 1;
  overflow: hidden;
}

.el-alert {
  flex-shrink: 0;
  margin-top: 10px;

  &:first-child {
    margin-top: 0;
  }

  :deep(.el-alert__content) {
    text-align: left;
  }
}

.empty-box {
  .el-empty {
    padding: 40px 0;
  }
}

:deep(.el-container .el-main .page-main) {
  padding: 0;
  background-color: $background-color;
  position: relative;
}
</style>
<style lang="scss">
.scheduleTime.el-date-editor.el-input {
  width: 2px;
}
.no-atTheMoment .el-picker-panel {
  .el-button--text.el-picker-panel__link-btn {
    display: none;
  }
}
.scheduleTime {
  .el-input__prefix {
    display: none;
  }
  :deep(.el-input--small .el-input__inner) {
    border: 0;
  }
  .el-input__suffix {
    display: none;
  }
  .el-input__inner {
    border: 0;
    background: none;
    color: transparent;
    padding: 0;
  }
}

.testing-icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  margin-right: 8px;
  border-radius: 4px;
  border: 1px solid transparent;
  color: #fff;
  font-size: 12px;
  line-height: 1;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  &.text-copy {
    color: $tes-red;
    background-color: rgba($tes-red, 0.1);
    border-color: rgba($tes-red, 0.2);
  }
  &.text-origin {
    color: $tes-blue;
    background-color: rgba($tes-blue, 0.1);
    border-color: rgba($tes-blue, 0.2);
  }
  &.text-change {
    color: $panGreen;
    background-color: rgba($panGreen, 0.1);
    border-color: rgba($panGreen, 0.2);
  }
  &.text-back {
    color: $tes-yellow;
    background-color: rgba($tes-yellow, 0.1);
    border-color: rgba($tes-yellow, 0.2);
  }
  &.text-info {
    color: $tes-grey;
    background-color: rgba($tes-grey, 0.1);
    border-color: rgba($tes-grey, 0.2);
  }
  &.text-special {
    color: $tes-grey;
    background-color: rgba($tes-grey, 0.1);
    border-color: rgba($tes-grey, 0.2);
  }
}
</style>
