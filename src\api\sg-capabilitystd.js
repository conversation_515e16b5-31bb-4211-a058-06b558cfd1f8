import request from '@/utils/request';

// #region 国网物资分类

// 查询国网（外部）物资类型信息
export function getExternalMaterialClassification() {
  return request({
    url: '/api-capabilitystd/capability/externalmaterialclassification/findList',
    method: 'get'
  });
}

// 保存国网（外部）物资类型信息
export function saveExternalMaterialClassification(data) {
  return request({
    url: '/api-capabilitystd/capability/externalmaterialclassification/saveOrUpdate',
    method: 'post',
    data
  });
}

// #endregion

// #region 国网检测项目分类树形数据

// 国网（外部）检测项目分类树形数据
export function getExternalCapabilityCategory(materialCategoryCode) {
  return request({
    url: `/api-capabilitystd/capability/externalcapabilitycategory/listTree/${materialCategoryCode}`,
    method: 'get'
  });
}

// 保存国网（外部）检测项目分类树形数据
export function saveExternalCapabilityCategory(data) {
  return request({
    url: `/api-capabilitystd/capability/externalcapabilitycategory/saveOrUpdate`,
    method: 'post',
    data
  });
}

// 删除国网（外部）检测项目分类树形数据
export function deleteExternalCapabilityCategory(data) {
  return request({
    url: `/api-capabilitystd/capability/externalcapabilitycategory/delete`,
    method: 'delete',
    data
  });
}

// #endregion

// #region 国网检测项目

export function getExternalCapabilityList(data) {
  return request({
    url: `/api-capabilitystd/capability/externalcapability/list`,
    method: 'post',
    data
  });
}

export function getExternalCapabilityInfoById(externalCapabilityId) {
  return request({
    url: `/api-capabilitystd/capability/externalcapability/info/${externalCapabilityId}`,
    method: 'get'
  });
}

export function saveExternalCapabilityInfo(data) {
  return request({
    url: `/api-capabilitystd/capability/externalcapability/saveOrUpdate`,
    method: 'post',
    data
  });
}

export function deleteExternalCapabilityInfo(data) {
  return request({
    url: `/api-capabilitystd/capability/externalcapability/delete`,
    method: 'post',
    data
  });
}

// #endregion

// #region 国网检测项目-关键参数

// 国网（外部）关键参数
export function getKeyParamsByExternalCapabilityId(externalCapabilityId) {
  return request({
    url: `/api-capabilitystd/capability/externalcapabilitypara/findByExternalCapabilityId/${externalCapabilityId}`,
    method: 'get'
  });
}

// 保存国网（外部）关键参数
export function saveExternalKeyParam(data) {
  return request({
    url: `/api-capabilitystd/capability/externalcapabilitypara/saveOrUpdate`,
    method: 'post',
    data
  });
}

// 保存国网（外部）关键参数
export function deleteExternalKeyParam(id) {
  return request({
    url: `/api-capabilitystd/capability/externalcapabilitypara/delete/${id}`,
    method: 'delete'
  });
}

// #endregion

// #region 保存国网（外部）检测项目和系统内部检测项目关联关系信息

// 根据LIMS检测项目Id查询关键参数绑定
export function getMapKeyParamsByCapabilityId(capabilityId) {
  return request({
    url: `/api-capabilitystd/capability/externalcapabilitymap/findByCapabilityId/${capabilityId}`,
    method: 'get'
  });
}

// 根据国网项目Id查询关键参数绑定
export function getMapKeyParamsByExternalCapabilityId(externalCapabilityId) {
  return request({
    url: `/api-capabilitystd/capability/externalcapabilitymap/findByExternalCapabilityId/${externalCapabilityId}`,
    method: 'get'
  });
}

// 保存关键参数绑定
export function saveExternalKeyParamMap(data) {
  return request({
    url: `/api-capabilitystd/capability/externalcapabilitymap/saveOrUpdate`,
    method: 'post',
    data
  });
}

// 删除关键参数绑定
export function deleteExternalKeyParamMap(id) {
  return request({
    url: `/api-capabilitystd/capability/externalcapabilitymap/delete/${id}`,
    method: 'delete'
  });
}

// #endregion

// #region 国网检测项目弹窗

export function getSgCapabilityIdByMaterialCodeAndCategoryId(categoryId, materialClassificationCode) {
  return request({
    url: `/api-capabilitystd/capability/externalcapability/extUpList/${materialClassificationCode}/${categoryId}`,
    method: 'get'
  });
}

// 树分类拖动排序
export function updateOrderCategory(data) {
  return request({
    url: '/api-capabilitystd/capability/externalcapabilitycategory/updateOrder',
    method: 'post',
    data
  });
}

// #endregion
