const templateEditorPath =
  window.location.origin === 'http://localhost:9527'
    ? 'http://*************/attachmentview/tes-wasm'
    : window.location.origin + '/attachmentview/tes-wasm';
// (import.meta.env.DEV ? import.meta.env.VITE_TEMPLATE_EDITOR_ORIGIN : '') + import.meta.env.VITE_TEMPLATE_EDITOR;
const stylesheetUrls = [
  // new template styles
  `${templateEditorPath}/template-layout.css`,
  `${templateEditorPath}/template-component.css`,

  // old excel styles
  `${templateEditorPath}/old-excel-style.css`
];

export function mounted() {
  stylesheetUrls.forEach(url => {
    const linkElement = document.createElement('link');
    linkElement.rel = 'stylesheet';
    linkElement.href = url;
    document.head.appendChild(linkElement);
  });
}

export function unmounted() {
  stylesheetUrls.forEach(url => {
    const linkElement = document.head.querySelector(`link[rel="stylesheet"][href^="${url}"]`);
    if (linkElement) {
      linkElement.remove();
    }
  });
}
