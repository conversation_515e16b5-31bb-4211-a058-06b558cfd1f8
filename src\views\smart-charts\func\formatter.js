export function formatterTips(params, isNoSeries) {
  let tip = '';
  if (params && params.length > 0) {
    tip = params[0]?.name + '<br>';
    params.forEach(item => {
      if (item.seriesName.includes('率')) {
        tip += item.marker + item.seriesName + ': ' + item.value + '%<br />';
      } else {
        if (isNoSeries) {
          tip += item.marker + item.value + '<br />';
        } else {
          tip += item.marker + item.seriesName + ': ' + item.value + '<br />';
        }
      }
    });
  }
  return tip;
}
