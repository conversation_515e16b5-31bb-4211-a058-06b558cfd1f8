export function formatDateTime(time, format = 'yyyy-MM-dd hh:mm:ss') {
  let date = new Date();
  if (time) {
    date = new Date(time);
  }

  const dateObjects = {
    'M+': date.getMonth() + 1, // 月
    'd+': date.getDate(), // 日
    'h+': date.getHours(), // 时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds() // 毫秒
  };

  if (/(y{4})/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }

  for (const key in dateObjects) {
    if (new RegExp('(' + key + ')').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? dateObjects[key] : ('00' + dateObjects[key]).substr(('' + dateObjects[key]).length)
      );
    }
  }

  return format;
}
// 过滤只显示月日, date：只显示日期，month：只显示月份，不传全部显示
export function formatDateDay(time, type) {
  var date = new Date();
  if (time) {
    date = new Date(time);
  }
  let month = date.getMonth() + 1;
  if (date.getMonth() + 1 < 10) {
    month = '0' + month;
  }
  let day = date.getDate();
  if (date.getDate() < 10) {
    day = '0' + day;
  }
  let H = date.getHours();
  let M = date.getMinutes();
  let S = date.getSeconds();
  if (date.getHours() < 10) {
    H = '0' + H;
  }
  if (date.getMinutes() < 10) {
    M = '0' + M;
  }
  if (date.getSeconds() < 10) {
    S = '0' + S;
  }
  if (type === 'date') {
    return day;
  } else if (type === 'month') {
    return month;
  } else if (type === 'dayTime') {
    return `${month}-${day} ${H}:${M}:${S}`;
  } else {
    return month + '月' + day + '日';
  }
}

// 过滤年月日，包括星期几
export function formatDate(time, num, text) {
  var date = new Date();
  var time_str = '';
  var show_day = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  if (time) {
    date = new Date(time);
  } else {
    return time;
  }
  let month = date.getMonth() + 1;
  let day = date.getDate();
  if (date.getMonth() + 1 < 10) {
    month = '0' + month;
  }
  if (date.getDate() < 10) {
    day = '0' + day;
  }

  if (num === 1) {
    time_str = date.getFullYear() + '年' + month + '月' + day + '日 ' + show_day[date.getDay()];
  } else {
    if (text) {
      time_str = date.getFullYear() + '年' + month + '月' + day + '日';
    } else {
      time_str = date.getFullYear() + '-' + month + '-' + day;
    }
  }
  if (time_str === 'NaN-NaN-NaN') {
    return '';
  } else {
    return time_str;
  }
}
// 过滤显示几点几分
export function formatCalendar(time, type) {
  let timeStr;
  var date = formatDateTime(time);
  if (type === 'hour') {
    timeStr = date.split(' ')[1].split(':')[0] + ':' + date.split(' ')[1].split(':')[1];
  } else {
    timeStr = date.split(' ')[0];
  }
  return timeStr;
}

export function formatNotEmptyDate(time, num) {
  return time ? formatDate(time, num) : time;
}

// 过滤时分秒
export function formatTimes(time) {
  var date = new Date();
  if (time) {
    date = new Date(time);
  }
  let H = date.getHours();
  let M = date.getMinutes();
  let S = date.getSeconds();
  if (date.getHours() < 10) {
    H = '0' + H;
  }
  if (date.getMinutes() < 10) {
    M = '0' + M;
  }
  if (date.getSeconds() < 10) {
    S = '0' + S;
  }
  return H + ':' + M + ':' + S;
}

export function formatDateTimeName(time) {
  var date = new Date();
  if (time) {
    date = new Date(time);
  }
  let month = date.getMonth() + 1;
  let day = date.getDate();
  let H = date.getHours();
  let M = date.getMinutes();
  let S = date.getSeconds();
  if (date.getMonth() + 1 < 10) {
    month = '0' + month;
  }
  if (date.getDate() < 10) {
    day = '0' + day;
  }
  if (date.getHours() < 10) {
    H = '0' + H;
  }
  if (date.getMinutes() < 10) {
    M = '0' + M;
  }
  if (date.getSeconds() < 10) {
    S = '0' + S;
  }
  return date.getFullYear() + month + day + H + M + S;
}
// 过滤年月
export function formatYM(time) {
  var date = new Date();
  if (time) {
    date = new Date(time);
  }
  let month = date.getMonth() + 1;
  if (date.getMonth() + 1 < 10) {
    month = '0' + month;
  }
  return date.getFullYear() + '-' + month;
}
// 过滤年月日
export function formatDateFilter(time) {
  let timeResult;
  if (time.indexOf('日') > -1) {
    timeResult = time.replace(/年/g, '-').replace(/月/g, '-').split('日')[0];
    timeResult = formatDate(timeResult);
  } else {
    timeResult = time.replace(/年/g, '-').replace(/月/g, '');
    timeResult = formatYM(timeResult);
  }
  return timeResult;
}
// 过滤websocket需求时间
export function formatWebsocketTime(millisecond) {
  var date = millisecond ? new Date(new Date().getTime() + millisecond) : new Date();
  let month = date.getMonth() + 1;
  let day = date.getDate();
  let H = date.getHours();
  let M = date.getMinutes();
  let S = date.getSeconds();
  if (date.getMonth() + 1 < 10) {
    month = '0' + month;
  }
  if (date.getDate() < 10) {
    day = '0' + day;
  }
  if (date.getHours() < 10) {
    H = '0' + H;
  }
  if (date.getMinutes() < 10) {
    M = '0' + M;
  }
  if (date.getSeconds() < 10) {
    S = '0' + S;
  }
  return date.getFullYear() + '' + month + '' + day + '' + H + '' + M + '' + S;
}
// 计算几月之后的日期
export function addMonth(date, num) {
  if (!date || !num) return;
  var num1 = parseInt(num);
  var d = new Date(date);
  var thisMonth = d.getMonth() + 1;
  var thisYear = d.getFullYear();
  var thisDay = d.getDate();
  var dateStr = '';
  var addCount = thisMonth + num1;
  var diffMonthCount = parseInt(addCount / 12); // 取整
  if (thisMonth + num1 === 12 * diffMonthCount) {
    // 如果是本年
    if (thisMonth + num1 === 12) {
      diffMonthCount = 0;
    } else {
      diffMonthCount = diffMonthCount - 1;
    }
  }
  if (thisMonth + num1 > 12) {
    // 如果是大于一年
    thisYear += diffMonthCount;
  }
  thisMonth = addCount - 12 * diffMonthCount;
  if (thisMonth < 10) {
    thisMonth = '0' + thisMonth;
  }
  var thatDate = new Date(thisYear, thisMonth, 0); // 当天数为0 js自动处理为上一月的最后一天
  var thatDay = thatDate.getDate(); // 指定年月的当月最大天数
  const m1 = date.substring(5, 7);
  if (thisDay === 30 || thisDay === 31 || (m1 === '02' && thisDay >= 28)) {
    thisDay = thatDay;
    if (date) {
      const m2 = date.substring(5, 10);
      //  成立日为2月份，且期限为整年的判断闰年
      if (m2 === '02-28' && num % 12 === 0) {
        if (!(thisYear % (thisYear % 100 ? 4 : 400))) {
          thisDay = '29';
        }
      }
    }
    dateStr = thisYear + '-' + thisMonth + '-' + thisDay;
  } else {
    dateStr = addMonth2(date, num);
  }
  return dateStr;
}
function addMonth2(date, num) {
  var monthnum = 0;
  if (typeof num === 'string') {
    monthnum = parseInt(num);
  } else {
    monthnum = num;
  }
  if (typeof date === 'string') {
    date = new Date(date);
  }
  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  var day = date.getDate();
  var sumMonths = month + monthnum;
  var newyear = year + parseInt(sumMonths / 12);
  var newmonth = sumMonths % 12;
  var newday = day;
  let newmonth2 = '';
  if (newmonth < 1) {
    if (newmonth === 0) newyear--;
    newmonth2 = 0 - ((sumMonths - 1) % 12);
    newmonth = 12;
  }
  var da = new Date(newyear, newmonth2, 0);
  return newyear + '-' + newmonth + '-' + (da.getDate() < newday ? da.getDate() : newday);
}
// 计算date1和date2相差天数
export function differenceDays(date1, date2) {
  const oneDay = 24 * 60 * 60 * 1000;
  const time1 = new Date(date1).getTime();
  const time2 = new Date(date2).getTime();
  const difference = Math.abs(time1 - time2); // 相差的毫秒数
  const days = Math.floor(difference / oneDay); // 相差的天数
  return days;
}
// 计算date1和date2相差小时分钟
export function differenceTimes(date1, date2) {
  const start = new Date(date1);
  const end = new Date(date2);

  // 计算时间差（毫秒）
  const diffInMs = Math.abs(end - start);

  // 转换为小时和分钟
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const hours = Math.floor(diffInMinutes / 60);
  const minutes = diffInMinutes % 60;

  return { hours, minutes };
}
// 获取当前日期的一周日期
export function getWeekDate(date) {
  const currenDay = date.getDay();
  const weeksArray = [];
  for (var i = 0; i < 7; i++) {
    var das = formatDate(date.getTime() + 24 * 60 * 60 * 1000 * (i - ((currenDay + 6) % 7)));
    weeksArray.push(das);
  }
  return weeksArray;
}
// 获取当前日期是一年的第几周
export function getWeekNumber(date) {
  const nowDate = new Date(date);
  const startOfYear = new Date(nowDate.getFullYear(), 0, 1);
  const startOfWeek = new Date(startOfYear);
  // 计算当前时间与一年的开始周的时间差，单位为毫秒
  const diff = nowDate.getTime() - startOfWeek.getTime();
  // 计算时间差对应的周数
  const currentWeek = Math.ceil(diff / (7 * 24 * 60 * 60 * 1000));
  return currentWeek;
}

/** 计算几天前的时间 */
export function calculateDaysBefore(end, days) {
  const endDate = new Date(end);
  const daysBefore = new Date(endDate);
  daysBefore.setDate(endDate.getDate() - days);

  // 格式化为 YYYY-MM-DD HH:mm:ss
  const year = daysBefore.getFullYear();
  const month = String(daysBefore.getMonth() + 1).padStart(2, '0');
  const day = String(daysBefore.getDate()).padStart(2, '0');
  const hours = String(daysBefore.getHours()).padStart(2, '0');
  const minutes = String(daysBefore.getMinutes()).padStart(2, '0');
  const seconds = String(daysBefore.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/** 根据开始时间和分钟小时,计算结束时间 */
export function calculateEndTime(startTime, hoursToAdd, minutesToAdd) {
  const startDate = new Date(startTime.replace(' ', 'T'));

  // 2. 分别计算小时和分钟的毫秒数
  const hoursMs = hoursToAdd * 60 * 60 * 1000; // 小时转毫秒
  const minutesMs = minutesToAdd * 60 * 1000; // 分钟转毫秒

  // 3. 计算总时间戳
  const endTimestamp = startDate.getTime() + hoursMs + minutesMs;
  const endDate = new Date(endTimestamp);

  // 4. 格式化输出
  const pad = num => num.toString().padStart(2, '0');
  return `${endDate.getFullYear()}-${pad(endDate.getMonth() + 1)}-${pad(endDate.getDate())} ${pad(
    endDate.getHours()
  )}:${pad(endDate.getMinutes())}:${pad(endDate.getSeconds())}`;
}
