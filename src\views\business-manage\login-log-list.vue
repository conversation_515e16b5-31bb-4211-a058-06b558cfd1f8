<template>
  <!-- 企业管理-登录日志 -->
  <ListLayout :has-search-panel="false" :has-quick-query="false" :has-button-group="false">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="condition">
          <el-input
            v-model="formInline.condition"
            v-trim
            v-focus
            placeholder="请输入用户名/姓名"
            class="ipt-360"
            size="large"
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter="getList()"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="getList()">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table base-table format-height-table no-quick-query"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column label="用户名" prop="username" :width="colWidth.person" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.username || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="姓名" prop="nickname" :width="colWidth.person" show-overflow-tooltip>
        <template #default="{ row }">
          <UserTag :name="row.nickname || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="登录时间" prop="createTime" :min-width="colWidth.datetime" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.createTime || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="IP地址" prop="ip" :width="colWidth.orderNo">
        <template #default="{ row }">
          <span>{{ row.ip || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="登录城市" prop="address" :width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.address || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="浏览器" prop="ie" :width="colWidth.description" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.ie || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="退出时间" prop="logoutTime" :min-width="colWidth.datetime" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.logoutTime || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="在线时长" prop="onlineTime" :width="colWidth.amount" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.onlineTime || '--' }}{{ row.logexception ? '(' + row.logexception + ')' : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        prop="caozuo"
        :width="colWidth.operationSingle"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <span v-if="row.onlineflag === 1" class="blue-color" @click="handleAM(row)">登出</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
  </ListLayout>
</template>

<script>
import { reactive, toRefs, getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getLoginId, setLoginId, getLoginInfo } from '@/utils/auth';
import ListLayout from '@/components/ListLayout';
import store from '@/store';
import Pagination from '@/components/Pagination';
import UserTag from '@/components/UserTag';
import { drageHeader } from '@/utils/formatTable';
import { removeSysToken, getLogList } from '@/api/login-log';
import { loginOutApi } from '@/api/sysConfig';
import { colWidth } from '@/data/tableStyle';

export default {
  name: 'LoginLogBusiness',
  components: { Pagination, ListLayout, UserTag },
  setup() {
    const { proxy } = getCurrentInstance();
    // const store = useStore().state
    const datas = reactive({
      accountId: getLoginInfo().accountId,
      accountName: getLoginInfo().username,
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: null
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      list: [],
      formInline: {
        condition: ''
      }
    });
    // 重置
    const reset = () => {
      datas.formInline = {
        condition: ''
      };
      datas.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: null
      };
      getList();
    };
    // table排序
    const sortChange = data => {
      const { prop, order } = data;
      datas.listQuery.orderBy = prop;
      if (order === 'ascending') {
        datas.listQuery.isAsc = true;
      } else if (order === 'descending') {
        datas.listQuery.isAsc = false;
      } else {
        datas.listQuery.isAsc = null;
      }
    };
    // 登出操作
    const handleAM = (row, flag) => {
      ElMessageBox({
        title: '登出确认',
        message: '登出后将会影响当前用户的系统操作，可能会造成数据丢失，是否继续？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          loginOutApi(row.logId).then(res => {
            if (res) {
              const loginId = getLoginId();
              if (loginId === row.logId) {
                setLoginId('');
                loginOut(row, true);
              } else {
                proxy.getList();
                loginOut(row, false);
              }
            }
          });
        })
        .catch(() => {});
    };
    const loginOut = (row, isMyself) => {
      removeSysToken({ token: row.token }).then(res => {
        if (res) {
          if (isMyself) {
            logout();
          }
          ElMessage.success('登出成功！');
        }
      });
    };
    const getList = data => {
      if (data && data !== undefined) {
        datas.listQuery.page = data.page;
        datas.listQuery.limit = data.limit;
      }
      const param = Object.assign(datas.formInline, datas.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      datas.listLoading = true;
      getLogList(param).then(res => {
        datas.listLoading = false;
        if (res !== false) {
          const { data } = res.data;
          datas.list = data.list;
          datas.total = data.totalCount;
        }
      });
    };
    getList();
    const logout = async () => {
      await store.dispatch('user/resetToken');
    };
    return { ...toRefs(datas), reset, sortChange, handleAM, logout, drageHeader, loginOut, colWidth, getList };
  }
};
</script>
<style lang="scss" scoped></style>
