const watermark = {};

const setWatermark = (str1, str2) => {
  const id = '1.8989898989898989.123412416';

  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id));
  }

  // 创建一个画布
  const can = document.createElement('canvas');
  // 设置画布的长宽
  can.width = 300;
  can.height = 200;

  const cans = can.getContext('2d');
  // 旋转角度
  cans.rotate((-15 * Math.PI) / 180);
  cans.font = '16px Vedana';
  // 设置填充绘画的颜色、渐变或者模式
  cans.fillStyle = 'rgba(200, 200, 200, 0.40)';
  // 设置文本内容的当前对齐方式
  cans.textAlign = 'left';
  // 设置在绘制文本时使用的当前文本基线
  cans.textBaseline = 'Middle';
  // 在画布上绘制填色的文本（输出的文本，开始绘制文本的X坐标位置，开始绘制文本的Y坐标位置）
  cans.fillText(str1, can.width / 2, can.height / 2);
  cans.fillText(str2, can.width / 2, can.height / 2 + 20);
  const div = document.createElement('div');
  div.id = id;
  div.style.pointerEvents = 'none';
  div.style.top = '130px';
  div.style.left = '80px';
  div.style.position = 'fixed';
  div.style.zIndex = '1000';
  div.style.width = document.documentElement.clientWidth + 'px';
  div.style.height = document.documentElement.clientHeight + 'px';
  div.style.background = 'url(' + can.toDataURL('image/png') + ') left top repeat';
  document.body.appendChild(div);
  return id;
};

// 添加日期
export function getDate() {
  var d = new Date();
  var year = d.getFullYear();
  var month = d.getMonth() + 1 >= 10 ? d.getMonth() + 1 : '0' + (d.getMonth() + 1);
  var day = d.getDate() >= 10 ? d.getDate() : '0' + d.getDate();
  return year + '年' + month + '月' + day + '日';
}

// 添加水印
watermark.add = (str1, str2) => {
  str2 = getDate();
  let id = setWatermark(str1, str2);
  if (document.getElementById(id) === null) {
    id = setWatermark(str1, str2);
  }
};

// 移除水印
watermark.remove = () => {
  const id = '1.8989898989898989.123412416';
  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id));
  }
};

export default watermark;
