<template>
  <!-- 试验配置 - 国网策略 -->
  <ListLayout
    class="test-item"
    :has-quick-query="false"
    :has-left-panel="true"
    :aside-max-width="520"
    :aside-panel-width="300"
  >
    <template #search-bar>
      <div class="searchInput">
        <el-input
          v-model="searchInput"
          v-trim
          v-focus
          class="ipt-360"
          placeholder="请输入编号/名称进行搜索"
          clearable
          size="large"
          @input="searchTable()"
          @clear="searchTable()"
          @keyup.enter="searchTable()"
        >
          <template #prefix>
            <i class="el-input__icon el-icon-search" />
          </template>
        </el-input>
        <el-button type="primary" size="large" @click="searchTable()">查询</el-button>
        <el-button size="large" @click="searchTable('reset')">重置</el-button>
      </div>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('addItemSgStrategy')"
        class="fr"
        type="primary"
        size="large"
        icon="el-icon-plus"
        @click="handleAddItem"
        @keyup.prevent
        @keydown.enter.prevent
        >添加项目</el-button
      >
    </template>
    <template #page-left-side>
      <div class="tree-container">
        <div class="tree-header">
          <div class="header-input-button" style="width: 100%">
            <el-input
              v-model="filterText"
              size="small"
              placeholder="请输入名称"
              prefix-icon="el-icon-search"
              clearable
            />
          </div>
        </div>
        <div class="tree-content">
          <el-tree
            ref="treeRef"
            :data="treeData"
            node-key="id"
            :props="defaultProps"
            :expand-on-click-node="false"
            highlight-current
            draggable
            :filter-node-method="filterNode"
            class="leftTree"
            @node-click="clickNode"
          >
            <template #default="{ node }">
              <span>{{ node.label }}</span>
            </template>
          </el-tree>
        </div>
      </div>
    </template>
    <div class="topVersion textLeft">
      <div class="top item">
        <div class="version-list">
          <div>版本号：</div>
          <el-radio-group
            v-model="radioVersion"
            size="mini"
            class="scrollbar-flex-content"
            @change="handleChangeVersion"
          >
            <el-radio-button v-for="item in versionList" :key="item.id" :label="item.id">
              {{ item.versionCode }}
            </el-radio-button>
          </el-radio-group>
          <div style="margin: 0 0 0 10px">
            <el-button
              v-if="isShowVersionSwitch && getPermissionBtn('editedVersion')"
              size="mini"
              @click="handleEditVersion"
              >编辑版本</el-button
            >
            <el-button v-if="getPermissionBtn('addNewVersion')" type="primary" size="mini" @click="handleAddVersion"
              >新增版本</el-button
            >
          </div>
        </div>
      </div>
      <div class="bottom item">
        <span v-if="isShowVersionSwitch">
          默认当前版本：<el-switch
            v-model="currentVersion.isDefault"
            size="small"
            :disabled="!getPermissionBtn('editedVersion')"
            :active-value="1"
            :inactive-value="0"
            @change="handleEditSwitch"
          />
        </span>
        <div class="description textRight inlineBlock">版本描述：{{ currentVersion.description || '--' }}</div>
      </div>
    </div>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="newTableList"
      fit
      border
      height="auto"
      :size="tableSize"
      highlight-current-row
      class="dark-table test-item-table base-table"
      @header-dragend="drageHeader"
    >
      <el-table-column label="项目编号" prop="externalCapabilityCode" :width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.externalCapabilityCode || '--' }}
        </template>
      </el-table-column>
      <el-table-column label="项目名称" prop="externalCapabilityName" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.externalCapabilityName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="externalCapabilityStatus" :width="colWidth.status">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="row.externalCapabilityStatus == 1 ? 'success' : 'info'">
            {{ row.externalCapabilityStatus == 1 ? '已启用' : '已停用' }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column v-for="(val, key) in itemLevel" :key="key" prop="grade" :width="colWidth.level">
        <template #header>
          <el-checkbox
            v-if="val === 'A'"
            v-model="isCheckAll"
            :disabled="!getPermissionBtn('editSgStrategy')"
            :indeterminate="checkAStatus === 2"
            :label="val"
            :true-label="1"
            :false-label="0"
            size="small"
            @change="
              val => {
                return handleChangeAllCheck(val, 'isCheckA');
              }
            "
          />
          <el-checkbox
            v-if="val === 'B'"
            v-model="isCheckBAll"
            :disabled="!getPermissionBtn('editSgStrategy')"
            :indeterminate="checkBStatus === 2"
            :label="val"
            :true-label="1"
            :false-label="0"
            size="small"
            @change="
              val => {
                return handleChangeAllCheck(val, 'isCheckB');
              }
            "
          />
          <el-checkbox
            v-if="val === 'C'"
            v-model="isCheckCAll"
            :disabled="!getPermissionBtn('editSgStrategy')"
            :indeterminate="checkCStatus === 2"
            :label="val"
            :true-label="1"
            :false-label="0"
            size="small"
            @change="
              val => {
                return handleChangeAllCheck(val, 'isCheckC');
              }
            "
          />
        </template>
        <template #default="{ row }">
          <el-checkbox
            v-if="val === 'A'"
            v-model="row.isCheckA"
            :disabled="!getPermissionBtn('editSgStrategy')"
            :true-label="1"
            :false-label="0"
            size="small"
            @change="
              val => {
                return handleChangeCheck(val, 'isCheckA', 'isCheckAll');
              }
            "
          />
          <el-checkbox
            v-if="val === 'B'"
            v-model="row.isCheckB"
            :disabled="!getPermissionBtn('editSgStrategy')"
            :true-label="1"
            :false-label="0"
            size="small"
            @change="
              val => {
                return handleChangeCheck(val, 'isCheckB', 'isCheckBAll');
              }
            "
          />
          <el-checkbox
            v-if="val === 'C'"
            v-model="row.isCheckC"
            :disabled="!getPermissionBtn('editSgStrategy')"
            :true-label="1"
            :false-label="0"
            size="small"
            @change="
              val => {
                return handleChangeCheck(val, 'isCheckB', 'isCheckCAll');
              }
            "
          />
        </template>
      </el-table-column>
      <el-table-column
        v-if="getPermissionBtn('deleteItemSgStrategy')"
        label="操作"
        :width="70"
        prop="caozuo"
        class-name="fixed-right"
        fixed="right"
      >
        <template #default="scope">
          <span class="blue-color" @click="deleteRow(scope.row)">删除</span>
        </template>
      </el-table-column>
    </el-table>
    <template #other>
      <!-- 新增检测项目树弹出框 -->
      <el-dialog
        v-model="showEditDialog"
        :title="isAddVersion === true ? '新增版本' : '编辑版本'"
        width="400px"
        :close-on-click-modal="false"
      >
        <el-form
          v-if="showEditDialog"
          ref="formVersionRef"
          :model="formVersion"
          :rules="dialogRules"
          label-position="right"
        >
          <el-form-item label="状态：" prop="status" :label-width="formLabelWidth">
            <el-switch
              v-model="formVersion.status"
              class="inner-switch"
              :active-value="1"
              :inactive-value="0"
              :active-text="formVersion.status ? '启用' : '停用'"
            />
          </el-form-item>
          <el-form-item
            label="版本号："
            prop="versionCode"
            :label-width="formLabelWidth"
            :rules="{ required: true, message: '请输入版本号', trigger: 'change' }"
          >
            <el-input
              ref="inputSearchRef"
              v-model="formVersion.versionCode"
              maxlength="50"
              autocomplete="off"
              placeholder="请输入版本号"
            />
          </el-form-item>
          <el-form-item label="版本描述：" prop="description" :label-width="formLabelWidth">
            <el-input
              v-model="formVersion.description"
              type="textarea"
              maxlength="300"
              :rows="3"
              autocomplete="off"
              placeholder="请输入版本描述"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button :loading="dialogLoading" @click="showEditDialog = false">取 消</el-button>
            <el-button :loading="dialogLoading" type="primary" @click="submitVersion">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 添加国网检测项目 -->
      <AddSgMultiItem
        :show="showAdd"
        :data="oldTableList"
        :level="itemLevel"
        :material-select-code="currentNode.code"
        :can-select-material="false"
        @selectData="selectData"
        @close="closeDialog"
      />
    </template>
  </ListLayout>
</template>
<script>
import { reactive, ref, watch, toRefs, nextTick, getCurrentInstance } from 'vue';
import { getPermissionBtn } from '@/utils/common';
import { getDictionary } from '@/api/user';
import AddSgMultiItem from '@/components/BusinessComponents/AddSgMultiItem';
import ListLayout from '@/components/ListLayout';
import { ElMessage, ElMessageBox } from 'element-plus';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { getSgMaterialClassifcation } from '@/views/test-configuration/sg-test-items/func/sgData';
import {
  versionApi,
  findByExternalStrategyId,
  saveOrUpdate,
  externalstrategyDelete,
  saveVersion
} from '@/api/sg-sgStrategy';

export default {
  name: 'SgStrategyList',
  components: { ListLayout, AddSgMultiItem },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      newTableList: [],
      oldTableList: [],
      isAddVersion: false,
      dialogLoading: false,
      formVersion: {},
      showAdd: false,
      dialogRules: {},
      treeRef: ref(),
      inputSearchRef: ref(),
      formVersionRef: ref(),
      searchInput: '',
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      treeData: [],
      isCheckAll: 0,
      formLabelWidth: '80px',
      isCheckBAll: 0,
      isCheckCAll: 0,
      versionList: [], // 版本列
      currentVersion: {
        isDefault: 0
      }, // 当前选择的版本
      radioVersion: '', // 所选版本
      itemLevel: {}, // 等级
      tableKey: '',
      showEditDialog: false,
      listLoading: false,
      filterText: '',
      asideWidth: 240,
      tableSize: 'medium',
      isShowVersionSwitch: true,
      currentNode: {},
      checkAStatus: 0,
      checkBStatus: 0,
      checkCStatus: 0
    });
    const getDictionaryList = () => {
      getDictionary('XMDJ').then(res => {
        if (res) {
          res.data.data.dictionaryoption.forEach(item => {
            if (item.status === 1) {
              state.itemLevel[item.code] = item.name;
            }
          });
        }
      });
    };
    getDictionaryList();
    // 左侧树
    const getTreeList = () => {
      state.listLoading = true;
      getSgMaterialClassifcation().then(res => {
        state.listLoading = false;
        if (res) {
          state.treeData = res;
          if (state.treeData.length > 0) {
            state.currentNode = state.treeData[0];
            getVersionList();
            nextTick(() => {
              state.treeRef.setCurrentKey(state.currentNode.id, true);
            });
          }
        }
      });
    };
    getTreeList();
    // 获取版本列表
    const getVersionList = () => {
      state.listLoading = true;
      versionApi(state.currentNode.code).then(res => {
        state.listLoading = false;
        if (res) {
          state.versionList = res.data.data;
          state.newTableList = [];
          if (state.versionList.length > 0) {
            if (!state.radioVersion) {
              state.radioVersion = state.versionList[0].id;
              state.currentVersion = state.versionList[0];
            } else {
              state.currentVersion = state.versionList.filter(item => {
                return item.id === state.radioVersion;
              })[0];
            }
            state.isShowVersionSwitch = true;
            getTableList();
          } else {
            state.newTableList = [];
            state.oldTableList = [];
            state.isShowVersionSwitch = false;
            initCheckStatus();
          }
        }
      });
    };
    // 切换版本
    const handleChangeVersion = val => {
      state.currentVersion = state.versionList.filter(item => {
        return item.id === val;
      })[0];
      state.newTableList = [];
      getTableList();
    };
    const getTableList = () => {
      if (!state.currentVersion?.id) {
        return false;
      }
      findByExternalStrategyId(state.currentVersion.id).then(res => {
        if (res) {
          state.newTableList = res.data.data;
          state.oldTableList = res.data.data;
          initCheckStatus();
        }
      });
    };
    // 判断ABC表头选择框的状态 0 未选中, 1 全选中, 2 半选中
    const checkStatus = (type, typeAll) => {
      state[typeAll] = 0;
      const array = state.newTableList.filter(item => {
        return item[type];
      });
      if (array.length === 0) {
        return 0;
      } else if (array.length === state.newTableList.length) {
        state[typeAll] = 1;
        return 1;
      } else {
        return 2;
      }
    };
    // 初始化状态
    const initCheckStatus = () => {
      state.checkAStatus = checkStatus('isCheckA', 'isCheckAll');
      state.checkBStatus = checkStatus('isCheckB', 'isCheckBAll');
      state.checkCStatus = checkStatus('isCheckC', 'isCheckCAll');
    };
    // 列表查询
    const searchTable = type => {
      if (type === 'reset') {
        state.searchInput = '';
        getTableList();
      } else {
        if (state.searchInput) {
          state.newTableList = state.oldTableList.filter(item => {
            return (
              item.externalCapabilityCode.indexOf(state.searchInput) !== -1 ||
              item.externalCapabilityName.indexOf(state.searchInput) !== -1
            );
          });
        } else {
          state.newTableList = state.oldTableList;
        }
        initCheckStatus();
      }
    };
    // 过滤树节点
    watch(
      () => state.filterText,
      newValue => {
        state.treeRef.filter(newValue);
      }
    );
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    const clickNode = node => {
      if (node.id !== state.currentNode.id) {
        state.currentNode = node;
        state.radioVersion = '';
        state.currentVersion = {};
        getVersionList();
        state.currentVersion = {
          isDefault: 0
        };
      }
    };
    const submitVersion = () => {
      proxy.$refs['formVersionRef'].validate(valid => {
        if (valid) {
          if (!state.formVersion.id) {
            if (
              state.versionList.some(item => {
                return item.versionCode === state.formVersion.versionCode;
              })
            ) {
              proxy.$message.warning('版本号已存在！');
              return false;
            }
          }
          if (state.formVersion.status === 0) {
            state.formVersion.isDefault = 0;
          }
          state.dialogLoading = true;
          saveVersion(state.formVersion).then(res => {
            state.dialogLoading = false;
            if (res) {
              state.showEditDialog = false;
              getVersionList();
              proxy.$message.success('保存成功');
            }
          });
        } else {
          return false;
        }
      });
    };
    // 添加项目
    const handleAddItem = () => {
      if (state.versionList.length === 0) {
        proxy.$message.warning('请先增加版本号');
        return false;
      }
      state.showAdd = true;
    };
    const closeDialog = () => {
      state.showAdd = false;
    };
    const selectData = val => {
      state.showAdd = false;
      if (val.length > 0) {
        const params = [];
        val.forEach(item => {
          params.push({
            externalStrategyId: state.currentVersion.id,
            ...item
          });
        });
        onSaveOrder(params);
      }
    };
    const handleChangeAllCheck = (val, type) => {
      if (state.newTableList.length === 0) {
        return false;
      }
      state.newTableList.forEach(item => {
        item[type] = val;
      });
      onSaveOrder(state.newTableList);
    };
    const onSaveOrder = params => {
      state.listLoading = true;
      saveOrUpdate(params).then(res => {
        state.listLoading = false;
        if (res) {
          ElMessage.success('保存成功');
          state.showAdd = false;
          getTableList();
        }
      });
    };
    const handleChangeCheck = (val, type, typeAll) => {
      checkStatus(type, typeAll);
      onSaveOrder(state.newTableList);
    };
    // 删除
    const deleteRow = row => {
      ElMessageBox({
        title: '提示',
        message: '是否确认删除?',
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(() => {
          state.listLoading = true;
          externalstrategyDelete(row.id).then(res => {
            state.listLoading = false;
            if (res) {
              ElMessage.success('删除成功');
              getTableList();
            }
          });
        })
        .catch(() => {});
    };
    // 编辑版本
    const handleEditVersion = () => {
      state.showEditDialog = true;
      nextTick(() => {
        state.inputSearchRef.focus();
      });
      state.isAddVersion = false;
      state.formVersion = JSON.parse(JSON.stringify(state.currentVersion));
    };
    // 新增版本
    const handleAddVersion = () => {
      if (state.treeData.length === 0) {
        ElMessage.warning('请先选择物资分类');
        return false;
      }
      state.showEditDialog = true;
      nextTick(() => {
        state.inputSearchRef.focus();
      });
      state.isAddVersion = true;
      state.formVersion = {
        status: 1,
        isDefault: 0,
        materialClassificationCode: state.currentNode.code,
        materialClassificationId: state.currentNode.id
      };
    };
    // 切换默认版本
    const handleEditSwitch = val => {
      state.listLoading = true;
      if (val && state.currentVersion.status === 0) {
        state.currentVersion.status = 1;
      }
      saveVersion({ ...state.currentVersion, isDefault: val }).then(res => {
        state.listLoading = false;
        if (res) {
          getVersionList();
          proxy.$message.success('操作成功');
        }
      });
    };
    return {
      ...toRefs(state),
      colWidth,
      handleEditSwitch,
      handleEditVersion,
      handleAddVersion,
      deleteRow,
      initCheckStatus,
      handleChangeAllCheck,
      handleChangeCheck,
      getDictionaryList,
      selectData,
      closeDialog,
      getVersionList,
      handleAddItem,
      searchTable,
      getTreeList,
      submitVersion,
      clickNode,
      getPermissionBtn,
      getTableList,
      drageHeader,
      filterNode,
      handleChangeVersion
    };
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
.version-list {
  display: flex;
  width: 100%;
  .el-radio-group {
    flex: 1;
    overflow-x: auto;
    text-wrap: nowrap;
  }
}
.searchInput {
  display: flex;
  .el-input {
    width: 360px;
    margin-right: 10px;
  }
}
.textLeft {
  text-align: left;
}
.textRight {
  text-align: right;
}
.topVersion {
  height: 80px;
  background-color: #f5f7fa;
  padding: 5px 10px;
  border-radius: 5px;
  margin-bottom: 10px;
  .item {
    line-height: 35px;
  }
}
.description {
  width: 80%;
  padding: 0 10px 0 0;
  float: right;
}
.inlineBlock {
  display: inline-block;
}
:deep(.el-scrollbar) {
  vertical-align: bottom;
}

.tree-container .tree-content {
  height: calc(100vh - 235px);
}

.scrollbar-flex-content {
  .el-radio-button__original-radio:checked + .el-radio-button__inner {
    border-color: $tes-primary !important;
    color: $tes-primary !important;
  }
}
</style>
