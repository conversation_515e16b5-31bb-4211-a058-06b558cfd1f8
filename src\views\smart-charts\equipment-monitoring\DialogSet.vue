<template>
  <el-dialog
    v-model="dialogShow"
    title="选择设备"
    :close-on-click-modal="false"
    width="1090px"
    custom-class="DialogEquipmentSet"
    :before-close="closedialog"
  >
    <el-radio-group v-model="selectView" size="mini" @change="changeView">
      <el-radio-button label="">默认视图</el-radio-button>
      <el-radio-button v-for="(val, key) in viewList" :key="key" :label="val">
        {{ val }}
      </el-radio-button>
    </el-radio-group>
    <el-form
      ref="viewFormRef"
      :model="formDataView"
      label-width="80px"
      label-position="left"
      size="mini"
      class="formDataView"
    >
      <el-row>
        <el-col :span="6">
          <el-form-item prop="username" label="视图名称：">
            <el-input
              v-model="formDataView.username"
              :disabled="!selectView"
              maxlength="10"
              type="text"
              placeholder="请输入视图名称"
              @blur="handleBlur"
            />
          </el-form-item>
        </el-col>
        <el-col :span="7" style="margin-left: 10px">
          <el-checkbox v-model="formDataView.isForce" @change="handleCheckBox">显示</el-checkbox>
        </el-col>
      </el-row>
    </el-form>
    <el-row class="tableContent">
      <el-col :span="8" class="deviceTable">
        <el-row class="deviceTableTh deviceTableTr">
          <el-col :span="11" :offset="2">设备编号</el-col>
          <el-col :span="11">设备名称</el-col>
        </el-row>
        <el-row
          v-for="(row, index) in tableList"
          :key="row.id"
          class="deviceTableTr deviceTableLi cursorPointer"
          :class="{ activeRow: selectRow.deviceRow.id === row.id }"
        >
          <el-col :span="2"
            ><el-checkbox
              v-model="row.selected"
              @change="
                val => {
                  return handleChangeCheckBox(val, row, index, 'equipment');
                }
              "
          /></el-col>
          <el-col :span="11" @click="handleActiveRow(row, 'device')">{{ row.deviceNum || '--' }}</el-col>
          <el-col :span="11" @click="handleActiveRow(row, 'device')">{{ row.deviceName || '--' }}</el-col>
        </el-row>
      </el-col>
      <el-col :span="4" :offset="1" class="codeTable">
        <el-row class="deviceTableTh deviceTableTr">
          <el-col :span="21" :offset="2">设备码点</el-col>
        </el-row>
        <div v-if="codePointList.length">
          <el-row
            v-for="(row, index) in codePointList"
            :key="row.id"
            class="deviceTableTr deviceTableLi cursorPointer"
            :class="{ activeRow: selectRow.codePointRow.id === row.id }"
          >
            <el-col :span="2"
              ><el-checkbox
                v-model="row.selected"
                @change="
                  val => {
                    return handleChangeCheckBox(val, row, index, 'code');
                  }
                "
            /></el-col>
            <el-col :span="21" @click="handleActiveCodePoint(row)">{{ row.codeNumber || '--' }}</el-col>
          </el-row>
        </div>
        <div v-else class="nodeCodeList">
          <img src="@/assets/img/empty-data.png" alt="" />
          <span>暂无数据</span>
        </div>
      </el-col>
      <el-col :span="10" :offset="1">
        <el-card class="box-card">
          <div class="cardTitle">码点设置</div>
          <el-form
            v-if="selectRow.codePointRow.id"
            ref="codePointRef"
            :model="formData"
            :rules="codePointRules"
            label-position="top"
            size="small"
            label-width="auto"
          >
            <el-row>
              <el-col :span="11">
                <el-form-item prop="codeNumber" label="码点编号：">
                  <el-input v-model="formData.codeNumber" type="text" disabled placeholder="请输入码点编号" />
                </el-form-item>
              </el-col>
              <el-col :span="11" :offset="2">
                <el-form-item prop="codeName" label="码点名称：">
                  <el-input v-model="formData.codeName" type="text" disabled placeholder="请输入码点名称" />
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item prop="zxfs" label="展现方式：">
                  <el-select v-model="formData.zxfs" placeholder="请选择展现方式" filterable style="width: 100%">
                    <el-option :value="1" label="控制图" />
                    <el-option :value="2" label="折线图" />
                    <el-option :value="3" label="单数据" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="11" :offset="2">
                <el-form-item prop="password" label="关联订单编号：">
                  <el-input v-model="formData.password" type="text" placeholder="请输入关联订单编号" />
                </el-form-item>
              </el-col>
              <el-col v-if="formData.zxfs === 1" :span="11">
                <el-form-item
                  prop="sxpl"
                  label="刷新频率(s)："
                  :rules="{ required: true, message: '请输入刷新频率(s)', trigger: 'change' }"
                >
                  <el-input
                    v-model="formData.sxpl"
                    type="text"
                    placeholder="请输入刷新频率(s)"
                    @blur="handleBlurForm('sxpl')"
                  />
                </el-form-item>
              </el-col>
              <el-col v-if="formData.zxfs === 1" :span="11" :offset="2">
                <el-form-item
                  prop="fzs"
                  label="分组数(个)："
                  :rules="{ required: true, message: '请输入分组数(个)', trigger: 'change' }"
                >
                  <el-input
                    v-model="formData.fzs"
                    type="text"
                    placeholder="请输入分组数(个)"
                    @blur="handleBlurForm('fzs')"
                  />
                </el-form-item>
              </el-col>
              <el-col v-if="formData.zxfs === 1" :span="11">
                <el-form-item prop="kzxup" label="手动设置控制上线：">
                  <el-input v-model="formData.kzxup" type="text" placeholder="控制上线" />
                </el-form-item>
              </el-col>
              <el-col v-if="formData.zxfs === 1" :span="11" :offset="2">
                <el-form-item prop="kzxDown" label="手动设置控制下线：">
                  <el-input v-model="formData.kzxDown" type="text" placeholder="控制下线" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div v-else class="nodeCodeList">
            <img src="@/assets/img/empty-data.png" alt="" />
            <span>暂无数据</span>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <div class="dialogBtn textRight">
      <el-button @click="closedialog">取消</el-button>
      <el-button type="primary" size="small">保存视图</el-button>
      <el-button v-if="!selectView" type="primary" size="small" @click="handleSaveAs">另存为视图</el-button>
    </div>
  </el-dialog>
  <el-dialog
    v-model="dialogAddView"
    title="新增视图"
    :close-on-click-modal="false"
    width="390px"
    custom-class="DialogEquipmentSet"
    :before-close="closedialog"
  >
    <el-form
      ref="viewAddFormRef"
      :model="formDataAddView"
      label-width="80px"
      label-position="left"
      size="mini"
      class="formDataView"
    >
      <el-form-item prop="username" label="视图名称：">
        <el-input
          ref="addViewInputRef"
          v-model="formDataAddView.username"
          type="text"
          placeholder="请输入视图名称"
          @blur="handleBlur"
        />
      </el-form-item>
    </el-form>
    <div class="dialogBtn textRight">
      <el-button size="small" @click="dialogAddView = false">取消</el-button>
      <el-button type="primary" size="small" @click="submitSaveAs">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, nextTick, getCurrentInstance } from 'vue';
import { colWidth } from '@/data/tableStyle';
import { drageHeader } from '@/utils/formatTable';
export default {
  name: 'DialogSet',
  components: {},
  props: {
    dialogVisiable: {
      type: Boolean,
      default: false
    },
    deviceCodeList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // const store = useStore()
    const state = reactive({
      selectView: '', // 选择的视图
      viewName: '', // 视图名称
      equipmentSelected: [], // 勾选中的设备
      codePointSelect: [], // 勾选中的码点
      codePointList: [], // 码点列表
      tableList: [], // 设备列表
      selectRow: {
        // 选中的数据
        deviceRow: {},
        codePointRow: {}
      },
      formData: {}, // 码点设置表单
      formDataAddView: {}, // 新增视图表单
      dialogAddView: false, // 新增视图弹出窗
      codePointRules: {}, // 码点表单校验
      tableRef: ref(),
      codePointRef: ref(),
      formDataView: {},
      viewFormRef: ref(),
      viewAddFormRef: ref(),
      addViewInputRef: ref(),
      isEditView: false, // 编辑视图
      dialogShow: false,
      viewList: {
        1: '视图1',
        2: '视图2'
      }, // 视图列
      layoutDetailJson: {}, // 选中的模块
      listLoading: false
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisiable;
      if (state.dialogShow) {
        state.tableList = props.deviceCodeList;
      }
    });
    // 切换视图
    const changeView = val => {
      state.formDataView.username = val;
    };
    const handleEditView = viewKey => {
      state.isEditView = !state.isEditView;
      state.viewName = state.selectView;
    };
    // 设备选中行
    const handleActiveRow = row => {
      state.selectRow.deviceRow = row;
      state.codePointList = row.codeList;
      if (state.codePointList.length) {
        handleActiveCodePoint(state.codePointList[0]);
      } else {
        handleActiveCodePoint({});
      }
    };
    // 码点选中行
    const handleActiveCodePoint = row => {
      state.selectRow.codePointRow = row;
      state.formData = row;
      if (!row.zxfs) {
        state.formData.zxfs = 1;
        state.formData.sxpl = 60;
        state.formData.fzs = 1;
      }
    };
    const handleChangeCheckBox = (val, row, index, type) => {
      if (type === 'equipment') {
        state.equipmentList[index].selected = val;
        if (val) {
          // 选择设备
          state.equipmentSelected.push(row.id);
        } else {
          // 删除已选中的设备
          const findIndex = state.equipmentSelected.findIndex(item => {
            return item === row.id;
          });
          state.equipmentSelected.splice(findIndex, 1);
        }
      } else {
        state.codePointList[index].selected = val;
        if (val) {
          // 选择设备
          state.codePointSelect.push(row.id);
        } else {
          // 删除已选中的设备
          const findIndex = state.codePointSelect.findIndex(item => {
            return item === row.id;
          });
          state.codePointSelect.splice(findIndex, 1);
        }
      }
    };
    const handleSelectionChange = val => {
      state.equipmentSelected = val;
    };
    const closedialog = () => {
      context.emit('closeDialog', { isRefresh: false });
    };
    // 视图是否显示
    const handleCheckBox = val => {};
    // 视图名称输入框移出焦点,如果清空没有输入，则回到默认值
    const handleBlur = () => {
      if (!state.formDataView.username) {
        state.formDataView.username = state.selectView;
      }
    };
    // 另存为
    const handleSaveAs = () => {
      state.dialogAddView = true;
      nextTick(() => {
        state.addViewInputRef.focus();
      });
    };
    // 提交另存为
    const submitSaveAs = () => {
      if (state.formDataAddView.username) {
        state.dialogAddView = false;
      } else {
        proxy.$message.error('请先输入视图名称！');
      }
    };
    // 码点设置刷新频率和分组数，清空时移出焦点恢复成默认值
    const handleBlurForm = type => {
      if (!state.formData[type]) {
        state.formData[type] = type === 'sxpl' ? 60 : 1;
      }
    };
    return {
      ...toRefs(state),
      handleSaveAs,
      handleActiveRow,
      handleActiveCodePoint,
      handleBlur,
      submitSaveAs,
      handleEditView,
      props,
      handleBlurForm,
      handleCheckBox,
      changeView,
      closedialog,
      colWidth,
      drageHeader,
      handleChangeCheckBox,
      handleSelectionChange
    };
  },
  computed: {},
  created() {}
};
</script>
<style lang="scss" scoped>
.nodeCodeList {
  img {
    width: 90px;
    display: block;
    margin: 0 auto;
  }
  span {
    line-height: 20px;
    margin-bottom: 20px;
    display: inline-block;
    width: 100%;
    text-align: center;
    color: #fff;
  }
}
.dialogBtn {
  margin: 30px 0 20px 0;
}
.deviceTable,
.codeTable {
  background-color: #3f7ab6;
  border-radius: 5px;
  padding: 0 5px;
}
.textRight {
  text-align: right;
}

.cursorPointer {
  cursor: pointer;
}
.tableContent {
  // margin-top: 20px;
  color: #fff;
}
.deviceTableTr {
  line-height: 36px;
  margin-bottom: 10px;
  .el-col {
    padding: 0 10px;
  }
}
.cardTitle {
  font-size: 16px;
  line-height: 16px;
  color: #fff;
  margin-bottom: 10px;
}
.deviceTableLi {
  .el-col {
    background-color: #386eb0;
  }
  .el-col:first-child {
    text-align: center;
  }
  &:hover .el-col {
    background-color: #4280b9;
  }
  &.activeRow {
    .el-col {
      background-color: #7ad0ff;
    }
  }
}
.el-card {
  background-color: #4280b9;
  border: 0;
}
:deep(.el-card__body) {
  padding: 15px;
}
.formDataView {
  margin-top: 15px;
}
:deep(.el-checkbox__label) {
  color: #fff;
}
</style>
<style lang="scss">
.DialogEquipmentSet .el-dialog__body .el-form-item__label {
  color: #fff !important;
}
.DialogEquipmentSet {
  border-radius: 10px;
  .el-button--primary,
  .el-button--primary:focus,
  .el-button--primary:hover {
    background: #245094;
    border-color: #245094;
    color: #fff;
  }
  .el-table tr {
    background-color: #fff !important;
  }
  .el-radio-button__inner {
    background-color: transparent;
  }
  .el-radio-button__inner {
    color: #fff;
  }
  .el-radio-button__original-radio:checked + .el-radio-button__inner {
    background-color: #7ad0ff;
    border-color: #7ad0ff;
    box-shadow: 1px 0 0 0 #7ad0ff;
  }
  .el-dialog__header {
    background: linear-gradient(90deg, #245094 0%, #3f7ab6 100%);
    border-radius: 10px 10px 0 0;
  }
  .el-dialog__title {
    color: #7ad0ff;
    font-weight: 700;
  }
  .el-dialog__body {
    background: linear-gradient(180deg, #2d59a5 0%, #4280b9 100%);
    border-radius: 0 0 10px 10px;
  }
  .el-button:hover {
    color: #000;
    border-color: #7ad0ff;
    background-color: #d4edfe;
  }
}
</style>
