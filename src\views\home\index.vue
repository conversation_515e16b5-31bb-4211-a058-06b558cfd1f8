<template>
  <div class="tes-home-contant">
    <Header />
    <Main />
  </div>
</template>

<script>
import { getCurrentInstance, reactive, toRefs } from 'vue';
// import router from '@/router/index.js'
// import { constantRoutes } from '@/router'
// import { useRoute } from 'vue-router'
import Header from './Header.vue';
import Main from './Main.vue';

export default {
  name: 'TesHome',
  components: { Header, Main },
  setup() {
    const { proxy } = getCurrentInstance();
    // console.log(proxy)
    proxy.$message('message');
    // const route = useRoute()
    const datas = reactive({
      inputData: '',
      newData: 'home'
    });

    return {
      ...toRefs(datas)
    };
  },
  created() {
    // this.$message('this is home  !')
  }
};
</script>
<style lang="scss" scoped>
.tes-home-contant {
  height: 100%;
  width: 100%;
  line-height: normal;
  background: #eff1f4;
}
</style>
