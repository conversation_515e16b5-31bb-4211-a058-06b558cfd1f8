import request from '@/utils/request';

// #region 委托登记信息

/**
 * 委托登记列表
 * http://*************:8800/doc.html#/diplomat/%E5%A7%94%E6%89%98%E7%99%BB%E8%AE%B0%E4%BF%A1%E6%81%AF/listUsingGET_2
 */
export function getTaskRegistrationList(data) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg/list`,
    method: 'post',
    data
  });
}

/**
 * 通过委托id获取检测委托基本信息
 */
export function getTaskRegistrationInfo(taskId) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg/info/${taskId}`,
    method: 'get'
  });
}
// 委托报告下载
export function downLoadFile(entrustRegId) {
  return request({
    url: `/api-diplomat/diplomat/entrustregreport/download/${entrustRegId}`,
    responseType: 'blob',
    headers: { 'Content-Type': 'application/octet-stream; charset=utf-8' },
    method: 'get'
  });
}

/**
 * 保存委托登记基本信息
 */
export function saveTaskRegistrationInfo(data) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg/save`,
    method: 'post',
    data
  });
}

/**
 * 提交委托登记基本信息
 */
export function submitTaskRegistrationInfo(id) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg/submit/${id}`,
    method: 'post'
  });
}

// #endregion

// #region 委托登记技术评审

/**
 * 通过委托id查询技术评审信息
 */
export function getTechnicalReviewList(processInstanceId) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg/processHistory/${processInstanceId}`,
    method: 'get'
  });
}

/**
 * 提交委托登记基本信息
 */
export function saveTechnicalReviewInfo(data) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg/processExecute`,
    method: 'post',
    data
  });
}

// #endregion

// #region 委托登记样品信息

/**
 * 样品信息列表
 * http://*************:8800/doc.html#/diplomat/%E5%A7%94%E6%89%98%E7%99%BB%E8%AE%B0%E6%A0%B7%E5%93%81%E4%BF%A1%E6%81%AF/listUsingGET_4
 */
export function getTaskSampleList(superId) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg_sample/list/${superId}`,
    method: 'get'
  });
}

/**
 * 根据id查询样品信息
 */
export function getSampleInfoById(id) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg_sample/info/${id}`,
    method: 'get'
  });
}

/**
 * 通过id复制样品信息
 */
export function copySampleInfoById(data) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg_sample/copy`,
    method: 'post',
    data
  });
}

/**
 * 通过id删除样品信息
 */
export function deleteSampleInfoById(id) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg_sample/delete/${id}`,
    method: 'get'
  });
}

/**
 * 保存登记样品基本信息
 */
export function saveTaskSampleInfo(data) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg_sample/save`,
    method: 'post',
    data
  });
}
/**
 * 保存登记样品基本信息
 */
export function editTaskSampleInfo(data) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg_sample/repeatedly`,
    method: 'post',
    data
  });
}

/**
 * 根据国网物资分类编码和样品登记获取检测项目
 * @param {*} materialClassificationCode
 * @param {*} sampleGrade
 * @returns
 */
export function getSgTestItemByStrategy(materialClassificationCode, sampleGrade) {
  return request({
    url: `/api-capabilitystd/capability/externalstrategydetails/findByMaterialClassificationCode/${materialClassificationCode}/${sampleGrade}`,
    method: 'get'
  });
}

/**
 * 保存或更新委托登记国网检测项目
 */
export function saveTaskSampleTestItems(data) {
  return request({
    url: `/api-diplomat/diplomat/entrustregcapability/saveOrUpdate`,
    method: 'post',
    data
  });
}

/**
 * 根据国网物资分类编码和样品登记获取检测项目
 * @param {*} entrustRegSampleId
 * @returns
 */
export function getSavedTestItemsBySampleId(entrustRegSampleId) {
  return request({
    url: `/api-diplomat/diplomat/entrustregcapability/findByEntrustRegSampleId/${entrustRegSampleId}`,
    method: 'get'
  });
}

// #endregion

// #region 委托登记费用信息

/**
 * 删除费用信息
 * @param {*} id
 * @returns
 */
export function deleteFeeInfoById(id) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg_cost/delete/${id}`,
    method: 'get'
  });
}

/**
 * 删除费用信息
 * @param {*} id
 * @returns
 */
export function getFeeInfoById(id) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg_cost/info/${id}`,
    method: 'get'
  });
}

/**
 * 获取费用信息列表
 * @param {*} superId
 * @returns
 */
export function getFeeInfoListByTaskId(superId) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg_cost/list/${superId}`,
    method: 'get'
  });
}

/**
 * 保存委托登记费用信息
 * @param {*} data
 * @returns
 */
export function saveTaskFeeInfo(data) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg_cost/save`,
    method: 'post',
    data
  });
}

// #endregion

// #region 委托登记附件信息

/**
 * 上传附件
 * @param {*} superId
 * @param {*} data
 * @returns
 */
export function uploadAttachmentByTaskId(superId, data) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg_attachment/upload/${superId}`,
    method: 'post',
    data
  });
}

/**
 * 附件列表
 * @param {*} superId
 * @returns
 */
export function getAttachmentListByTaskId(superId) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg_attachment/list/${superId}`,
    method: 'get'
  });
}

/**
 * 附件下载
 * @param {*} id
 * @returns
 */
export function downloadAttachmentById(id) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg_attachment/download/${id}`,
    responseType: 'blob',
    headers: { 'Content-Type': 'application/octet-stream; charset=utf-8' },
    method: 'get'
  });
}

/**
 * 删除附件信息
 * @param {*} id
 * @returns
 */
export function deleteAttachmentListById(id) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg_attachment/delete/${id}`,
    method: 'get'
  });
}

// 查询客户信息
export function getClientDetail(id) {
  return request({
    url: `/api-diplomat/diplomat/customer/info/${id}`,
    method: 'get'
  });
}

// 地址列表
export function getClientAddress(customerId) {
  return request({
    url: `/api-diplomat/diplomat/customer/address/list/${customerId}`,
    method: 'get'
  });
}

// 联系人列表
export function getClientContacts(customerId) {
  return request({
    url: `/api-diplomat/diplomat/customer/contacts/list/${customerId}`,
    method: 'get'
  });
}

/**
 * 获取发票信息
 * @param {*} customerId
 * @returns
 */
export function getInvoiceInfo(customerId) {
  return request({
    url: `/api-diplomat/diplomat/customer/invoice/info/${customerId}`,
    method: 'get'
  });
}

/**
 * 根据id获取客户信息
 */
export function getTaskCustomerInfo(superId) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg_customer/list/${superId}`,
    method: 'get'
  });
}

/**
 * 保存客户信息
 */
export function saveTaskCustomerInfo(data) {
  return request({
    url: `/api-diplomat/diplomat/entrust_reg_customer/save`,
    method: 'post',
    data
  });
}

// #endregion
