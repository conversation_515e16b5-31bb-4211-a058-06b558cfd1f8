<template>
  <!-- 地域分布 -->
  <ListLayout :has-custom-header="true" :has-custom-main="true" :main-offset-top="topHeight">
    <template #page-custom-header>
      <SingleLineHeader>
        <template #left-form-group>
          <el-form ref="editFrom" label-width="120px" label-position="top" :model="searchForm" @submit.prevent>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item
                  label="报告日期："
                  class="date-range"
                  prop="submitTime"
                  :rules="{ required: true, message: '请选择时间范围', trigger: 'change' }"
                >
                  <el-date-picker
                    v-model="searchForm.submitTime"
                    type="daterange"
                    size="small"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :shortcuts="shortcuts"
                    @change="handleChangeTime"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  label="物资分类："
                  prop="mateType"
                  :rules="{
                    required: true,
                    message: '请选择物资分类',
                    trigger: 'change'
                  }"
                >
                  <el-select
                    v-model="searchForm.mateType"
                    class="chart-query-select"
                    placeholder="请选择物资分类"
                    size="small"
                    clearable
                  >
                    <el-option v-for="item in materialList" :key="item.code" :label="item.name" :value="item.code">{{
                      item.name
                    }}</el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  label="客户信息："
                  prop="customerType"
                  :rules="{
                    required: true,
                    message: '请选择客户信息',
                    trigger: 'change'
                  }"
                >
                  <el-select
                    v-model="searchForm.customerType"
                    class="chart-query-select"
                    placeholder="请选择客户信息"
                    size="small"
                    clearable
                  >
                    <el-option label="委托方" :value="1" />
                    <el-option label="缴款方" :value="0" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
        <template #right-button-group>
          <el-button key="sample" type="text" size="small" @click="formatterData" @keyup.prevent @keydown.enter.prevent>
            <span class="el-icon-data-analysis" />
          </el-button>
          <el-button key="cancel" size="small" @click="handleClear()" @keyup.prevent @keydown.enter.prevent
            >清空</el-button
          >
          <el-button
            :loading="renderLoading"
            type="primary"
            size="small"
            @click="getEchartsList()"
            @keyup.prevent
            @keydown.enter.prevent
            >查询</el-button
          >
        </template>
      </SingleLineHeader>
    </template>
    <template #page-custom-main>
      <el-row>
        <el-col v-loading="renderLoading" :span="24">
          <CustomPanel :has-margin-right="true" :has-margin-left="true">
            <template #panel-content>
              <MapChart :option="externalOption" :width="'100%'" :height="'72vh'" />
            </template>
          </CustomPanel>
        </el-col>
      </el-row>
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs } from 'vue';
import ListLayout from '@/components/ListLayout';
import MapChart from '@/components/Echarts/MapChart.vue';
import { formatDate } from '@/utils/formatTime';
import { past3Months } from '@/data/dateShortcuts';
import SingleLineHeader from '@/components/PageComponents/SingleLineHeader';
import CustomPanel from '@/components/PageComponents/CustomPanel';
import { getNameByid, calculator } from '@/utils/common';
import { colWidth } from '@/data/tableStyle';
import { useStore } from 'vuex';
import { getDetectionDistribution } from '@/api/regional-distribution';
import { province } from '@/data/province';
import provinceAll from '@/data/dataAll';
// import proviceLatLng from '@/data/proviceLatLng'
import emptyImg from '@/assets/img/empty-chart.png';
import emptyData from '@/assets/img/empty-data.png';

export default {
  name: 'RegionalDistribution',
  components: {
    ListLayout,
    CustomPanel,
    SingleLineHeader,
    MapChart
  },
  setup(props, context) {
    const processData = reactive({
      isAdd: false
    });
    const store = useStore().state;
    const state = reactive({
      searchForm: {
        customerType: 0,
        submitTime: [formatDate(new Date().getTime() - 3600 * 1000 * 24 * 90), formatDate(new Date())],
        startDate: formatDate(new Date().getTime() - 3600 * 1000 * 24 * 90),
        endDate: formatDate(new Date()),
        mateType: store.user.materialList[0]?.code
      },
      tableData: [],
      detailData: {
        detailList: [],
        resultList: []
      },
      isShowReturn: false,
      materialList: store.user.materialList,
      shortcuts: past3Months,
      topHeight: 140,
      renderLoading: false,
      proviceData: []
    });
    const externalOption = ref({
      tooltip: {
        trigger: 'item',
        axisPointer: {
          type: 'shadow'
        },
        formatter: params => {
          if (params.componentSubType === 'scatter') {
            let text = `<div style="text-align: left;font-size: 15px; margin-bottom: 8px;"><span style="display: inline-block;margin-right: 20px;">${params.data.name}</span><span style="float: right;">${params.data.quantity}</span></div>`;
            const dataInfo = params.data;
            dataInfo.childList.forEach(item => {
              text += `<div style="text-align: left;"><span style="display: inline-block;margin-right: 20px;">${item.name}</span><span style="float: right;">${item.quantity}</span></div>`;
            });
            return text;
          } else {
            const text = `<div style="text-align: left;font-size: 15px;><span style="display: inline-block;margin-right: 20px;">${
              params.name
            }</span><span style="float: right;">${params.value || 0}</span></div>`;
            return text;
          }
        }
      },
      backgroundColor: '#fff',
      geo: {
        show: true,
        center: [105, 36],
        map: 'china', // 使用
        roam: true,
        itemStyle: {
          areaColor: '#CCF0E8',
          borderWidth: 1, // 设置外层边框
          borderColor: '#00B38A',
          position: 'inside',
          shadowColor: '#99E1D0',
          shadowBlur: 5
        },
        scaleLimit: {
          // 所属组件的z分层，z值小的图形会被z值大的图形覆盖
          min: 1 // 最小的缩放值
        },
        label: {
          show: true,
          color: '#fff' // 省份标签字体颜色
        },
        zoom: 1.24,
        aspectScale: 0.85, // 长宽比
        emphasis: {
          itemStyle: {
            show: true,
            areaColor: '#00B38A', // 悬浮区背景
            borderWidth: 1.6,
            shadowBlur: 25
          },
          label: {
            color: '#fff',
            show: true
          }
        }
      },
      visualMap: [
        {
          left: '8%',
          type: 'continuous',
          min: 0,
          max: 0,
          seriesIndex: 0, // 指定系列数据
          text: ['最高', '最低'],
          realtime: true,
          calculable: true,
          inRange: {
            color: ['#99E1D0', '#33C2A1', '#00B38A']
          }
        }
      ],
      series: [
        {
          name: '总体分布图', // 系列名称
          type: 'map', // 图表类型
          geoIndex: 0,
          roam: true,
          data: []
        },
        {
          // 文字和标志
          type: 'scatter',
          coordinateSystem: 'geo',
          geoIndex: 0,
          data: [],
          z: 999,
          label: {
            formatter: '{c}',
            position: 'right'
          },
          zlevel: 999,
          symbolSize: 6,
          itemStyle: {
            color: '#00A17C'
          },
          symbol: 'circle'
        }
      ]
    });
    const formatterData = () => {
      const newArray = [];
      for (var key in provinceAll) {
        newArray.push({
          childList: provinceAll[key].children,
          latitude: provinceAll[key].latitude,
          longitude: provinceAll[key].longitude,
          regionState: provinceAll[key].regionState,
          quantity: provinceAll[key].quantity
        });
      }
      drawPage(newArray);
    };
    const getEchartsList = () => {
      if (
        state.searchForm.mateType === '' ||
        state.searchForm.customerType === '' ||
        state.searchForm.startDate === ''
      ) {
        return false;
      }
      const params = JSON.parse(JSON.stringify(state.searchForm));
      delete params.submitTime;
      getDetectionDistribution(params).then(res => {
        if (res) {
          const detailList = res.data.data;
          drawPage(detailList);
        }
      });
    };
    getEchartsList();
    const drawPage = detailList => {
      state.proviceData = []; // 各省份地区数量
      var maxNumber = 0; // 各省份地区数量的最大值
      var minNumber = 0; // 各省份地区数量的最小值
      detailList.forEach(item => {
        state.proviceData.push({
          name: province[item.regionState[0]], // 需要重新匹配地区名字 例如上海市不回显，
          value: item.quantity
        });
        // 散点图
        item.name = item.regionState.toString();
        item.value = [Number(item.longitude), Number(item.latitude), item.quantity, item.name];
        maxNumber = maxNumber < item.quantity ? item.quantity : maxNumber;
        minNumber = minNumber > item.quantity ? item.quantity : minNumber;
      });
      // 全国有34个省级行政区，少于34个表示有些地区没有，则最小值是0
      if (detailList.length < 34) {
        minNumber = 0;
      }
      externalOption.value.series[0].data = state.proviceData;
      externalOption.value.series[0].data = state.proviceData;
      externalOption.value.visualMap[0].min = minNumber;
      externalOption.value.visualMap[0].max = maxNumber;
      externalOption.value.series[1].data = detailList;
    };
    const handleChangeTime = val => {
      if (val?.length > 0) {
        state.searchForm.startDate = formatDate(val[0]);
        state.searchForm.endDate = formatDate(val[1]);
      } else {
        state.searchForm.startDate = '';
        state.searchForm.endDate = '';
      }
    };
    // 清空
    const handleClear = () => {
      externalOption.value.series[1].data = [];
    };
    return {
      ...toRefs(state),
      ...toRefs(processData),
      emptyImg,
      handleClear,
      formatterData,
      handleChangeTime,
      getEchartsList,
      externalOption,
      getNameByid,
      formatDate,
      emptyData,
      calculator,
      colWidth
    };
  }
};
</script>
<style lang="scss" scoped>
.panel-header-left {
  justify-content: space-between;
}
.echartspageHeader {
  background-color: $background-color;
  margin-bottom: 10px;
  padding-left: 20px;
}
</style>
