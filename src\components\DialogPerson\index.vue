<template>
  <el-dialog
    v-model="dialogVisiable"
    :title="dialogTitle"
    :close-on-click-modal="false"
    width="950px"
    top="50px"
    @close="handleClose"
  >
    <el-row style="margin-bottom: 10px">
      <el-col :span="24" style="text-align: left; line-height: 32px">
        <el-input
          ref="inputRef"
          v-model="condition"
          v-trim
          size="small"
          autocomplete="off"
          placeholder="请输入姓名/手机号"
          prefix-icon="el-icon-search"
          clearable
          style="width: 360px"
          @clear="getPersonList()"
          @keyup.enter="getPersonList()"
        />
        <el-button type="primary" size="small" style="margin-left: 10px" @click="getPersonList()">查询</el-button>
        <el-button size="small" @click="reset">重置</el-button>
      </el-col>
    </el-row>
    <el-table
      ref="tableRef"
      v-loading="dialogLoading"
      :data="tableData"
      size="medium"
      fit
      border
      height="auto"
      class="dark-table base-table format-height-table2"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" :selectable="selectable" width="55" />
      <el-table-column label="姓名" prop="nickname" min-width="140px">
        <template #default="{ row }">
          <span class="nowrap">{{ row.nickname }}</span>
        </template>
      </el-table-column>
      <el-table-column label="手机号" prop="mobile" width="140px">
        <template #default="{ row }">
          <span class="nowrap">{{ row.mobile || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="邮箱" prop="email" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.email || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="部门" prop="name" min-width="140px" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="nowrap">{{ row.name || '--' }}</span>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="small" @click="handleClose">取 消</el-button>
        <el-button type="primary" size="small" @click="onSubmit">确 认</el-button>
      </span>
    </template>
    <pagination
      v-show="total > 20"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getPersonList"
    />
  </el-dialog>
</template>
<script>
// getCurrentInstance
import { reactive, ref, toRefs, watch, nextTick } from 'vue';
import { getAllMemberList } from '@/api/roleManage';
import Pagination from '@/components/Pagination';
export default {
  name: 'DialogPerson',
  components: { Pagination },
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    dialogTitle: {
      type: String,
      default: '维护试验人员'
    },
    alreadyExist: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    const state = reactive({
      dialogLoading: false, // 弹出窗loading
      tableData: [],
      inputRef: ref(),
      dialogVisiable: false,
      dialogTitle: props.dialogTitle,
      alreadySelected: [],
      tableRef: ref(),
      selectedMembers: [],
      condition: '',
      total: 0,
      listQuery: {
        limit: 20,
        page: 1
      }
    });
    watch(props, newValue => {
      state.dialogVisiable = newValue.dialogShow;
      if (state.dialogVisiable) {
        state.condition = '';
        state.total = 0;
        state.listQuery = {
          limit: 20,
          page: 1
        };
        state.selectedMembers = [];
        state.tableData = [];
        state.alreadySelected = props.alreadyExist;
        getPersonList();
        nextTick(() => {
          state.inputRef.focus();
        });
      }
    });
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDialog', { isRefresh: false });
    };
    // 提交所选人员
    const onSubmit = () => {
      context.emit('closeDialog', { isRefresh: state.selectedMembers.length > 0, selected: state.selectedMembers });
    };
    // 获取人员列表
    const getPersonList = query => {
      const params = { condition: state.condition };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoadingAll = true;
      getAllMemberList(params).then(res => {
        state.listLoadingAll = false;
        if (res) {
          state.tableData = res.data.data.list;
          state.total = res.data.data.totalCount;
        }
      });
    };
    // 选中的成员
    const handleSelectionChange = val => {
      state.selectedMembers = val;
    };
    const reset = () => {
      state.condition = '';
      state.total = 0;
      state.listQuery = {
        limit: 20,
        page: 1
      };
      getPersonList();
    };
    const handleRowClick = row => {
      if (
        !state.alreadySelected.some(item => {
          return item === row.id;
        })
      ) {
        state.tableRef.toggleRowSelection(row);
      }
    };
    const selectable = row => {
      if (
        state.alreadySelected.some(item => {
          return item === row.id;
        })
      ) {
        return false;
      } else {
        return true;
      }
    };
    return {
      ...toRefs(state),
      onSubmit,
      handleClose,
      getPersonList,
      reset,
      selectable,
      handleSelectionChange,
      handleRowClick
    };
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 100%;
}
:deep(.el-select) {
  width: 100%;
}
</style>
