/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./index.html', './src/**/*.{vue,js}'],
  theme: {
    extend: {
      colors: {
        bg_color: 'var(--el-bg-color)',
        primary: 'var(--el-color-primary)',
        primary_light_1: 'var(--el-color-primary-light-1)',
        primary_light_2: 'var(--el-color-primary-light-2)',
        text_color_primary: 'var(--el-text-color-primary)',
        text_color_regular: 'var(--el-text-color-regular)',
        text_color_disabled: 'var(--el-text-color-disabled)'
      },
      textColor: {
        primary: 'var(--el-color-primary)',
        primaryText: 'var(--el-text-color-primary)',
        regular: 'var(--el-text-color-regular)',
        secondary: 'var(--el-text-color-secondary)',
        wechat: '#28C445',
        danger: 'var(--el-color-danger)',
        disabled: 'var(--el-text-color-disabled)'
      },
      screens: {
        sm: '1280px',
        md: '1440px',
        lg: '1600px',
        xl: '1920px'
      },
      fontSize: {
        sm: ['12px', '20px'],
        base: ['14px', '22px'],
        middle: ['16px', '24px'],
        lg: ['18px', '26px'],
        xl: ['20px', '28px'],
        xxl: ['24px', '32px'],
        '4xl': '2rem',
        '7xl': ['4.75rem', '4.75rem']
      }
    }
  },
  corePlugins: {
    preflight: false
  },
  plugins: []
};
