import request from '@/utils/request';

// 计划分类列表
export function documentPlanCategoryList(data) {
  return request({
    url: '/api-document/document/plancategory/list',
    method: 'post',
    data
  });
}

// 保存计划分类
export function plancategorySave(data) {
  return request({
    url: '/api-document/document/plancategory/saveOrUpdate',
    method: 'post',
    data
  });
}

// 删除附件
export function deleteFile(id) {
  return request({
    url: `/api-document/document/attachment/delete/${id}`,
    method: 'delete'
  });
}

// 删除附件
export function plandetailList(data) {
  return request({
    url: `/api-document/document/plandetail/list`,
    method: 'post',
    data
  });
}

// 获取详情
export function plandetailId(id) {
  return request({
    url: `/api-document/document/plandetail/info/${id}`,
    method: 'get'
  });
}

// 删除计划明细
export function deletePlanDetail(id) {
  return request({
    url: `/api-document/document/plandetail/delete/${id}`,
    method: 'delete'
  });
}

// 保存或更新计划明细信息
export function savePlanDetail(data) {
  return request({
    url: `/api-document/document/plandetail/saveOrUpdate`,
    method: 'post',
    data
  });
}
