<template>
  <el-drawer
    v-model="drawerVisiable"
    :title="titleJSON[drawerType]"
    direction="rtl"
    :before-close="handleClose"
    size="88%"
    destroy-on-close
    custom-class="page-drawer"
  >
    <DrawerLayout
      v-if="drawerVisiable"
      v-loading="drawerLoading"
      :has-left-panel="false"
      :main-offset-top="53"
      :has-button-group="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        label-position="right"
        size="small"
        label-width="110px"
        :class="{ 'detail-form': drawerType === 'check' }"
        class="form-height-auto"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item prop="inspectionNo" label="设备点检单号：">
              <span v-if="drawerType === 'check'">{{ formData.inspectionNo }}</span>
              <el-input v-else v-model="formData.inspectionNo" disabled placeholder="自动生成无需填写" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="formData.deviceNumber" prop="deviceNumber" label="仪器设备编号：">
              <div v-if="drawerType === 'check'">{{ formData.deviceNumber }}</div>
              <el-tag
                v-else
                type="primary"
                effect="dark"
                closable
                @click="handleChooseEquipment()"
                @close="handleDeleteTag()"
                >{{ formData.deviceNumber }}</el-tag
              >
            </el-form-item>
            <el-form-item
              v-else
              prop="deviceNumber"
              label="仪器设备编号："
              :rules="{ required: true, message: '请输入仪器设备编号', tigger: 'change' }"
            >
              <el-button icon="el-icon-plus" type="primary" size="mini" @click="handleChooseEquipment()"
                >选择仪器设备</el-button
              >
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="deviceName" label="仪器设备名称：">
              <span v-if="drawerType === 'check'">{{ formData.deviceName }}</span>
              <el-input v-else v-model="formData.deviceName" disabled placeholder="自动带入无需填写" />
            </el-form-item> </el-col
          ><el-col :span="8">
            <el-form-item prop="deviceModel" label="型号规格：">
              <span v-if="drawerType === 'check'">{{ formData.deviceModel }}</span>
              <el-input v-else v-model="formData.deviceModel" disabled placeholder="自动带入无需填写" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              prop="standardNo"
              label="点检标准编号："
              :rules="{ required: drawerType !== 'check', message: '请选择点检标准编号', tigger: 'change' }"
            >
              <span v-if="drawerType === 'check'">{{ formData.standardNo }}</span>
              <el-select
                v-else
                v-model="formData.standardNo"
                placeholder="请选择点检标准编号"
                clearable
                style="width: 100%"
                @change="handleChangeStandard"
              >
                <el-option v-for="(val, key) in standardListJSON" :key="key" :value="key" :label="val.standardNo">
                  <div class="selectOption">
                    <div class="optionName">{{ val.standardNo }}</div>
                    <div v-if="val.standardName" class="optionRemark">{{ val.standardName }}</div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="standardName" label="点检标准名称：">
              <span v-if="drawerType === 'check'">{{ formData.standardName }}</span>
              <el-input v-else v-model="formData.standardName" disabled placeholder="自动带入无需填写" />
            </el-form-item> </el-col
          ><el-col :span="8">
            <el-form-item
              prop="inspectionBy"
              label="点检员："
              :rules="{ required: drawerType !== 'check', message: '请选择点检员', tigger: 'change' }"
            >
              <UserTag v-if="drawerType === 'check'" :name="getNameByid(formData.inspectionBy) || '--'" />
              <el-select
                v-else
                v-model="formData.inspectionBy"
                clearable
                filterable
                placeholder="请选择点检员"
                size="small"
                style="width: 100%"
              >
                <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="inspectionTime" label="点检日期：">
              <span v-if="drawerType === 'check'">{{ formatDate(formData.inspectionTime) }}</span>
              <el-date-picker
                v-else
                v-model="formData.inspectionTime"
                type="date"
                size="small"
                placeholder="请选择点检日期"
                style="width: 100%"
                @change="changeDate"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="btnGroup">
          <el-col :span="12"><h3>点检标准明细</h3></el-col>
          <el-col :span="12" class="text-right">
            <el-button v-if="drawerType !== 'check'" type="primary" icon="el-icon-plus" size="mini" @click="handleAdd()"
              >新增</el-button
            >
          </el-col>
        </el-row>
        <el-table
          v-if="isShowTable && tableHeader.length"
          id="sortableList"
          ref="tableRef"
          :data="formData.detailList"
          class="dark-table base-table format-height-table2"
          fit
          border
          height="auto"
          size="medium"
          style="margin-top: 5px"
          @header-dragend="drageHeader"
        >
          <el-table-column
            v-if="drawerType !== 'check'"
            label="排序"
            prop="deviceNumber"
            align="center"
            :width="colWidth.serialNo"
          >
            <i class="tes-move iconfont" style="font-size: 12px; cursor: move" />
          </el-table-column>
          <el-table-column label="序号" type="index" :width="colWidth.serialNo" />
          <el-table-column
            v-for="item in tableHeader"
            :key="item.fieldId"
            :label="item.fieldName"
            :prop="item.fieldId"
            :min-width="120"
            show-overflow-tooltip
          >
            <template #default="{ row, $index }">
              <div v-if="item.fieldType === 'text'">
                <!-- 文本输入框 -->
                <el-form-item
                  v-if="drawerType !== 'check'"
                  :prop="`detailList.${$index}${[item.fieldId]}`"
                  style="margin-bottom: 0"
                  label-width="0"
                >
                  <el-input
                    v-model="row[item.fieldId]"
                    :placeholder="`请输入${item.fieldName}`"
                    maxlength="300"
                    style="width: 100%"
                    @change="
                      val => {
                        return handleChange(val, $index, item.fieldId);
                      }
                    "
                  />
                </el-form-item>
                <span v-else>{{ row[item.fieldId] || '--' }}</span>
              </div>
              <div v-if="item.fieldType === 'date'">
                <!-- 日期选择框 -->
                <el-form-item
                  v-if="drawerType !== 'check'"
                  :prop="`detailList.${$index}${[item.fieldId]}`"
                  style="margin-bottom: 0"
                  label-width="0"
                >
                  <el-date-picker
                    v-model="row[item.fieldId]"
                    type="date"
                    :placeholder="`请选择${item.fieldName}`"
                    style="width: 100%"
                    @change="
                      val => {
                        return handleEditDate(val, $index, item.fieldId);
                      }
                    "
                  />
                </el-form-item>
                <span v-else>{{ row[item.fieldId] || '--' }}</span>
              </div>
              <div v-if="item.fieldType === 'number'">
                <!-- 数字输入框 -->
                <el-form-item
                  v-if="drawerType !== 'check'"
                  :prop="`detailList.${$index}${[item.fieldId]}`"
                  :rules="{ validator: isNumberNot, validata: row[item.fieldId], trigger: 'change' }"
                  style="margin-bottom: 0"
                  label-width="0"
                >
                  <el-input
                    v-model="row[item.fieldId]"
                    :placeholder="`请输入${item.fieldName}`"
                    style="width: 100%"
                    @change="
                      val => {
                        return handleChange(val, $index, item.fieldId);
                      }
                    "
                  />
                </el-form-item>
                <span v-else>{{ row[item.fieldId] || '--' }}</span>
              </div>
              <div v-if="item.dictionaryCode">
                <!-- 字典选择框 -->
                <el-form-item
                  v-if="drawerType !== 'check'"
                  :prop="`detailList.${$index}${[item.fieldId]}`"
                  style="margin-bottom: 0"
                  label-width="0"
                >
                  <el-select
                    v-model="row[item.fieldId]"
                    :placeholder="`请选择${item.fieldName}`"
                    clearable
                    style="width: 100%"
                    @change="
                      val => {
                        return handleChange(val, $index, item.fieldId);
                      }
                    "
                  >
                    <el-option
                      v-for="(val, key) in dictionaryDataAssemble[item.dictionaryCode]"
                      :key="key"
                      :label="val"
                      :value="key"
                    />
                  </el-select>
                </el-form-item>
                <span v-else>{{ dictionaryDataAssemble[item.dictionaryCode][row.tableInfo[item.id]] || '--' }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="点检检查结果" prop="inspectionResult" :min-width="colWidth.materialGroup">
            <template #default="{ row, $index }">
              <el-form-item
                v-if="drawerType !== 'check'"
                :prop="`detailList.${$index}.inspectionResult`"
                :rules="{ required: true, message: '请选择点检检查结果', tigger: 'change' }"
                style="margin-bottom: 0"
                label-width="0"
              >
                <el-select
                  v-model="row.inspectionResult"
                  placeholder="请选择点检检查结果"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="(val, key) in dictionaryDataAssemble['DJJCJG']"
                    :key="key"
                    :label="val"
                    :value="key"
                  />
                </el-select>
              </el-form-item>
              <span v-else>{{ dictionaryDataAssemble['DJJCJG'][row.inspectionResult] || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="异常及不合格处理"
            prop="exceptionHandling"
            :min-width="colWidth.projectName"
            show-overflow-tooltip
          >
            <template #default="{ row, $index }">
              <el-form-item
                v-if="drawerType !== 'check'"
                :prop="`detailList.${$index}.exceptionHandling`"
                style="margin-bottom: 0"
                label-width="0"
              >
                <el-input
                  v-model="row.exceptionHandling"
                  type="textarea"
                  placeholder="请输入异常及不合格处理"
                  style="width: 100%"
                />
              </el-form-item>
              <span v-else>{{ row.exceptionHandling }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="drawerType !== 'check'"
            label="操作"
            prop="measurementCycle"
            :width="colWidth.operation"
          >
            <template #default="{ row, $index }">
              <span class="blue-color" @click="handleDelete($index)">删除</span>
              <span class="blue-color" @click="handleCopy(row, $index)">复制</span>
            </template>
          </el-table-column>
        </el-table>
        <el-row style="margin-top: 20px">
          <el-col :span="8">
            <el-form-item prop="inspectionResult" label="点检结果：">
              <div v-if="drawerType === 'check'">
                <span v-if="formData.inspectionResult">{{
                  dictionaryDataAssemble['DJJCJG'][formData.inspectionResult]
                }}</span>
                <span v-else>--</span>
              </div>
              <el-select
                v-else
                v-model="formData.inspectionResult"
                placeholder="请选择点检结果"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="(val, key) in dictionaryDataAssemble['DJJCJG']"
                  :key="key"
                  :label="val"
                  :value="key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="deviceStatus" label="设备状态：">
              <div v-if="drawerType === 'check'">
                <span v-if="formData.deviceStatus">{{ dictionaryDataAssemble['24'][formData.deviceStatus] }}</span>
                <span v-else>--</span>
              </div>
              <el-select
                v-else
                v-model="formData.deviceStatus"
                placeholder="请选择设备状态"
                clearable
                style="width: 100%"
              >
                <el-option v-for="(val, key) in dictionaryDataAssemble['24']" :key="key" :label="val" :value="key" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <AddSingleEquipment
        :show="drawerSelectEquipment"
        :select-info="{ deviceNumber: formData.deviceNumber, name: formData.name }"
        @close-dialog="handleSelectData"
      />
      <div class="drawer-fotter">
        <el-button v-if="drawerType !== 'check'" type="primary" :loading="drawerLoading" @click="onSubmit"
          >确认</el-button
        >
        <el-button :loading="drawerLoading" @click="handleClose">取消</el-button>
      </div>
    </DrawerLayout>
  </el-drawer>
</template>
<script>
import { watch, reactive, toRefs, ref, getCurrentInstance, nextTick } from 'vue';
import DrawerLayout from '@/components/DrawerLayout';
import UserTag from '@/components/UserTag';
import { drageHeader } from '@/utils/formatTable';
import { formatDate } from '@/utils/formatTime';
import { colWidth } from '@/data/tableStyle';
import { useStore } from 'vuex';
import { getDictionary } from '@/api/dictionary';
import Sortable from 'sortablejs';
import { isNumberNot } from '@/utils/validate';
import { getNameByid } from '@/utils/common';
import AddSingleEquipment from '@/components/BusinessComponents/AddSingleEquipment';
import { pointInspectionFieldListAll } from '@/api/spotInspectionStandard';
import { devicePointInspectionSave, devicePointInspectionInfo } from '@/api/spotInspectionEquipment';
import { pointInspectionStandardList, pointInspectionStandardInfoId } from '@/api/spotInspectionStandard';
export default {
  name: 'DrawerSpotMonad',
  components: { DrawerLayout, AddSingleEquipment, UserTag },
  props: {
    drawerShow: {
      type: Boolean,
      default: false
    },
    detailInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    drawerType: {
      type: String,
      default: ''
    }
  },
  emits: ['closeDrawer'],
  setup(props, context) {
    const store = useStore().state;
    const { proxy } = getCurrentInstance();
    const state = reactive({
      formData: {
        detailList: [{ tableInfo: {}, order: 0 }]
      },
      isShowTable: true,
      drawerSelectEquipment: false,
      userOptions: store.common.nameList,
      standardListJSON: {},
      drawerLoading: false,
      detailInfo: {},
      titleJSON: {
        check: '查看设备点检单',
        edit: '编辑设备点检单',
        add: '新增设备点检单'
      },
      drawerType: '',
      oldStandardId: '',
      oldDetailList: [],
      dictionaryDataAssemble: {}, // 字典集合
      dictionaryCodeAll: [],
      tableHeader: [],
      isEdit: false,
      drawerVisiable: false,
      formRef: ref(),
      loading: false,
      tableRef: ref(null)
    });
    watch(props, newValue => {
      state.drawerVisiable = newValue.drawerShow;
      if (state.drawerVisiable) {
        state.drawerLoading = false;
        state.standardListJSON = {};
        state.formData = {
          inspectionTime: formatDate(new Date()),
          detailList: [{ tableInfo: {}, order: 0 }]
        };
        state.tableHeader = [];
        state.drawerType = props.drawerType;
        state.detailInfo = props.detailInfo;
        getStandardList();
        if (state.detailInfo.id) {
          getDetailInfo();
        } else {
          state.oldStandardId = '';
          getTableHeader();
        }
      }
    });
    // 获取点检标准列表
    const getStandardList = () => {
      state.drawerLoading = true;
      pointInspectionStandardList({ page: '1', limit: '-1' }).then(res => {
        state.drawerLoading = false;
        if (res) {
          res.data.data.list.forEach(item => {
            state.standardListJSON[item.standardNo] = item;
          });
        }
      });
    };
    const getDetailInfo = () => {
      state.drawerLoading = true;
      devicePointInspectionInfo(state.detailInfo.id).then(res => {
        state.drawerLoading = false;
        state.formData = {
          detailList: [{ tableInfo: {}, order: 0 }]
        };
        if (res) {
          state.tableHeader = res.data.data.tableHeader;
          state.dictionaryCodeAll = state.tableHeader.map(item => item.dictionaryCode);
          getDictionaryDataAssemble([...new Set(state.dictionaryCodeAll), 'DJJCJG', '24']);
          state.formData = res.data.data;
          state.oldStandardId = res.data.data.standardId;
          state.formData.detailList.forEach(item => {
            item = Object.assign(item, item.tableInfo);
          });
          state.oldDetailList = JSON.parse(JSON.stringify(state.formData.detailList));
          nextTick(() => {
            rowDrop();
          });
        }
      });
    };
    const rowDrop = () => {
      // 获取当前表格
      const el = document.getElementById('sortableList')?.querySelector('.el-table__body-wrapper tbody');
      Sortable.create(el, {
        animation: 300,
        handle: '.tes-move',
        draggable: '.el-table__row',
        ghostClass: 'ghost',
        // 拖动对象移动样式
        dragClass: 'drag',
        forceFallback: true,
        onEnd: function (evt) {
          if (evt.oldIndex !== evt.newIndex) {
            const currRow = state.formData.detailList.splice(evt.oldIndex, 1)[0];
            state.formData.detailList.splice(evt.newIndex, 0, currRow);
            state.formData.detailList.forEach((value, index) => {
              value.order = index;
            });
            showTable();
          }
        }
      });
    };
    const showTable = () => {
      state.isShowTable = false;
      setTimeout(() => {
        state.isShowTable = true;
        nextTick(() => {
          rowDrop();
        });
      }, 0);
    };
    // 获取所有字典选项
    const getDictionaryDataAssemble = dictionaryCodeArray => {
      dictionaryCodeArray.forEach(item => {
        if (item) {
          state.dictionaryDataAssemble[item] = {};
          state.drawerLoading = true;
          getDictionary(item).then(res => {
            state.drawerLoading = false;
            if (res) {
              const data = res.data.data.dictionaryoption;
              data.forEach(val => {
                if (val.status === 1) {
                  state.dictionaryDataAssemble[item][val.code] = val.name;
                }
              });
            }
          });
        }
      });
    };
    // 获取表格头
    const getTableHeader = () => {
      state.drawerLoading = true;
      pointInspectionFieldListAll({ status: 1 }).then(res => {
        state.drawerLoading = false;
        if (res) {
          state.tableHeader = res.data.data;
          const params = {};
          state.tableHeader.forEach(item => {
            item.fieldId = item.id;
            delete item.id;
            params[item.fieldId] = '';
          });
          state.dictionaryCodeAll = state.tableHeader.map(item => item.dictionaryCode);
          getDictionaryDataAssemble([...new Set(state.dictionaryCodeAll), 'DJJCJG', '24']);
          nextTick(() => {
            rowDrop();
          });
        }
      });
    };
    const handleAdd = () => {
      state.formData.detailList.push({
        order: state.formData.detailList.length,
        tableInfo: {}
      });
    };
    const onSubmit = () => {
      proxy.$refs['formRef']
        .validate()
        .then(valid => {
          if (valid) {
            state.drawerLoading = true;
            devicePointInspectionSave({ ...state.formData, tableHeader: state.tableHeader }).then(res => {
              state.drawerLoading = false;
              if (res) {
                proxy.$message.success('保存成功！');
                context.emit('closeDrawer', true);
              }
            });
          }
        })
        .catch(error => {
          const fieldName = Object.keys(error)[0];
          proxy.$message.warning(error[fieldName][0].message);
          return false;
        });
      // context.emit('closeDrawer')
    };
    // 关闭弹出窗
    const handleClose = () => {
      context.emit('closeDrawer');
    };
    // 删除
    const handleDelete = index => {
      state.formData.detailList.splice(index, 1);
      state.formData.detailList.forEach((item, index) => {
        item.order = index;
      });
    };
    const selectChange = (selection, row) => {
      if (row && row.deviceId) {
        row.selected = !row.selected;
      }
    };
    // 格式化日期
    const changeDate = val => {
      if (val) {
        state.formData.date = formatDate(val);
      }
    };
    // 选择仪器设备
    const handleChooseEquipment = () => {
      state.drawerSelectEquipment = true;
    };
    const handleSelectData = val => {
      state.drawerSelectEquipment = false;
      if (val.deviceNumber) {
        state.formData.deviceNumber = val.deviceNumber;
        state.formData.deviceName = val.name;
        state.formData.deviceModel = val.model;
        state.formData.deviceId = val.id;
      }
    };
    const handleChange = (val, index, field) => {
      state.formData.detailList[index].tableInfo[field] = val;
    };
    // 切换点检标准
    const handleChangeStandard = val => {
      if (val) {
        state.formData.standardName = state.standardListJSON[val].standardName;
        state.formData.standardId = state.standardListJSON[val].id;
        if (state.formData.standardId === state.oldStandardId) {
          state.formData.detailList = JSON.parse(JSON.stringify(state.oldDetailList));
        } else {
          getInfoDetail(state.formData.standardId);
        }
      } else {
        state.formData.standardName = '';
      }
    };
    const getInfoDetail = id => {
      pointInspectionStandardInfoId(id).then(res => {
        if (res) {
          const { data } = res.data;
          state.formData.detailList = [];
          data.itemList.forEach(item => {
            state.formData.detailList.push({
              order: state.formData.detailList.length,
              tableInfo: item.tableInfo,
              ...item.tableInfo
            });
          });
        }
      });
    };
    // 格式化日期选择框
    const handleEditDate = (val, index, field) => {
      state.formData.detailList[index].tableInfo[field] = formatDate(val);
    };
    // 复制
    const handleCopy = row => {
      const newCopyRow = JSON.parse(JSON.stringify(row));
      delete newCopyRow.id;
      state.formData.detailList.push({ ...newCopyRow, order: state.formData.detailList.length });
    };
    // 删除已选设备
    const handleDeleteTag = () => {
      state.formData.deviceNumber = '';
      state.formData.deviceName = '';
      state.formData.deviceModel = '';
      state.formData.deviceId = '';
    };
    return {
      ...toRefs(state),
      isNumberNot,
      getNameByid,
      handleDeleteTag,
      handleChange,
      handleEditDate,
      handleChangeStandard,
      handleChooseEquipment,
      handleCopy,
      handleSelectData,
      changeDate,
      handleDelete,
      handleAdd,
      onSubmit,
      handleClose,
      formatDate,
      colWidth,
      drageHeader,
      selectChange
    };
  }
};
</script>

<style lang="scss" scoped>
h3 {
  line-height: 32px;
  margin: 0;
}
.selectOption {
  display: flex;
  .optionName {
    flex: 1;
  }
  .optionRemark {
    font-size: 12px;
    color: $tes-font3;
  }
}

.detail-form {
  .el-form-item {
    margin-bottom: 0;
  }
}

.btnGroup {
  margin-top: 20px;
  font-weight: 600;
}
::v-deep(.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 37rem) !important;
    overflow-y: auto;
  }
}
</style>
