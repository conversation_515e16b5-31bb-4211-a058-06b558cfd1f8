<template>
  <!-- 归档查询 -->
  <div class="archives-search">
    <div class="archives-search__header">
      <div class="archives-search__title">
        <span class="archives-search__title-text">归档查询</span>
        <el-tooltip
          effect="dark"
          content="支持关键字：样品编号、申请单号、委托编号、报告编号、样品名称、型号规格、盘号、批次、物料编号、物料分组、物料名称、检测项目"
          placement="top"
        >
          <SvgIcon icon-class="lightbulb-flash" :width="24" :height="24" />
        </el-tooltip>
      </div>
      <div class="archives-search__search-wrap">
        <el-input
          v-model="inputStr"
          v-focus
          v-trim
          clearable
          class="archives-search__search-select"
          placeholder="请输入关键字搜索"
          style="width: 450px"
          size="large"
          @clear="handleClear()"
        />
        <el-button class="archives-search__search-btn" type="primary" size="large" @click="handleSearch">
          <span>搜索</span>
          <i class="el-icon-search" />
        </el-button>
      </div>
    </div>
    <div class="archives-main-list">
      <ul v-loading="loading" class="archives-search__list">
        <li v-for="item in list" :key="item.id">
          <div class="archives-search__block">
            <div class="title">归档信息</div>
            <el-row class="archives-search__info-row">
              <el-col :span="8">
                <span>归档编号</span>
                <span><KeywordText :texts="item.archivalCode" /></span>
              </el-col>
              <el-col :span="8">
                <span>样品编号</span>
                <span><KeywordText :texts="item.secSampleNum" /></span>
              </el-col>
              <el-col :span="8">
                <span>报告编号</span>
                <span><KeywordText :texts="item.reportNo" /></span>
              </el-col>
            </el-row>
            <el-row class="archives-search__info-row">
              <el-col :span="8">
                <span>样品名称</span>
                <span><KeywordText :texts="item.mateName" /></span>
              </el-col>
              <el-col :span="8">
                <span>型号规格</span>
                <span><KeywordText :texts="item.prodType" /></span>
              </el-col>
            </el-row>
          </div>
          <div class="archives-search__block">
            <div class="title">原始记录</div>
            <ul class="archives-search__file-list">
              <li v-for="fileItem in item.experimentData" :key="fileItem.id" @click="handlePreview(fileItem, 50)">
                <SvgIcon icon-class="pdf-icon" class="file-icon" :width="48" :height="48" />
                <div class="file-name">
                  <KeywordText :texts="fileItem.sourceName" />
                </div>
              </li>
            </ul>
          </div>
          <div class="archives-search__block">
            <div class="title">检测报告</div>
            <ul class="archives-search__file-list2">
              <li v-for="fileItem in item.reportData" :key="fileItem.id" @click="handlePreview(fileItem)">
                <SvgIcon icon-class="pdf-icon" class="file-icon2" :width="32" :height="32" />
                <div class="file-name2">
                  <KeywordText :texts="fileItem.reportNo" />
                </div>
              </li>
            </ul>
          </div>
          <el-divider />
          <div class="archives-search__item-footer">
            <span>归档人 : <UserTag :name="item.archivalUsername" /></span>
            <span>归档日期 : {{ item.archivalDate }}</span>
            <span>归档版本 : {{ item.archivalVersion }}</span>
          </div>
        </li>
      </ul>
      <el-empty v-if="isEmpty" :image="emptyImg" description="暂无数据" />
    </div>

    <div class="archives-search__footer">
      <Pagination
        v-show="pagination.total > 0"
        :page="pagination.page"
        :limit="pagination.limit"
        :total="pagination.total"
        :page-sizes="[3, 10, 20, 30, 50]"
        @pagination="onPagination"
      />
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, onMounted } from 'vue';
import { archivalQuery } from '@/api/order';
import router from '@/router/index.js';
import SvgIcon from '@/components/SvgIcon';
import UserTag from '@/components/UserTag';
import Pagination from '@/components/Pagination';
import emptyImg from '@/assets/img/empty-data.png';

const KeywordText = {
  props: ['texts'],
  template: `
    <span
      v-for="(item, index) in texts"
      :key="index"
      :style="item.keyword ? { color: '#00b38a' } : null"
    >
      {{ item.text }}
    </span>
  `
};

export default {
  name: 'ArhivesSearch',
  components: {
    SvgIcon,
    UserTag,
    Pagination,
    KeywordText
  },
  setup() {
    const state = reactive({
      inputStr: '',
      keyword: '',
      loading: false,
      list: [],
      isEmpty: false,
      pagination: {
        page: 1,
        limit: 3,
        total: 0
      }
    });

    onMounted(() => {
      query({
        page: state.pagination.page,
        limit: state.pagination.limit
      });
    });

    const handleSearch = () => {
      state.keyword = state.inputStr;
      const params = {
        page: state.pagination.page,
        limit: state.pagination.limit,
        param: state.inputStr
      };
      query(params);
    };
    // 清空重置
    const handleClear = () => {
      state.pagination = {
        page: 1,
        limit: 3,
        total: 0
      };
      handleSearch();
    };
    const handlePreview = (fileItem, pageScale) => {
      const url = import.meta.env.DEV
        ? fileItem.fileUrl.replace(window.location.host, '*************')
        : fileItem.fileUrl;
      const newRouter = router.resolve({
        path: '/preview-file',
        query: {
          pageScale,
          fileUrl: encodeURIComponent(url)
        }
      });
      window.open(newRouter.href, '_blank');
    };

    const onPagination = pagination => {
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        param: state.keyword
      };
      query(params);
    };

    const query = async params => {
      const beforeTime = new Date().getTime();
      state.loading = true;
      const res = await archivalQuery(params);
      const afterTime = new Date().getTime();
      if (afterTime - beforeTime <= 300) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      state.loading = false;
      if (res) {
        const result = formatArchivalData(res.data.data.list);
        state.pagination.page = res.data.data.currPage;
        state.pagination.limit = res.data.data.pageSize;
        state.pagination.total = res.data.data.totalCount;
        // showDropdown()
        state.list = result;
        state.isEmpty = result.length === 0;
      }
    };

    const formatArchivalData = data => {
      return data.map(row => {
        let reportNoStr = '';
        return {
          id: row.id,
          archivalCode: setKeyword(row.archivalCode),
          secSampleNum: setKeyword(row.secSampleNum),
          mateName: setKeyword(row.mateName),
          prodType: setKeyword(row.prodType),
          experimentData: row.experimentData.map(item => ({
            id: item.id,
            sourceName: setKeyword(item.sourceName),
            sourceNumber: item.sourceNumber,
            fileUrl: item.fileUrl
          })),
          reportData: row.reportData.map(item => {
            if (item.reportNo) {
              reportNoStr = reportNoStr + (reportNoStr ? '、' : '') + item.reportNo;
            }
            return {
              id: item.id,
              reportNo: setKeyword(item.reportNo),
              fileUrl: item.fileUrl
            };
          }),
          reportNo: setKeyword(reportNoStr),
          archivalUsername: row.archivalUsername,
          archivalDate: row.archivalDate,
          archivalVersion: row.archivalVersion
        };
      });
    };

    const setKeyword = str => {
      const keyword = state.keyword;
      const keywordRegexp = new RegExp(keyword, 'g');
      if (keywordRegexp.test(str)) {
        const strArr = str
          .replace(keywordRegexp, ',${0},')
          .split(',')
          .reduce((accumulator, currentItem) => {
            if (currentItem) {
              accumulator.push(currentItem === '${0}' ? { text: keyword, keyword: true } : { text: currentItem });
            }
            return accumulator;
          }, []);
        return strArr;
      } else {
        return [{ text: str }];
      }
    };

    return {
      ...toRefs(state),
      emptyImg,
      handleClear,
      handleSearch,
      handlePreview,
      onPagination,
      query
    };
  }
};
</script>

<style lang="scss" scoped>
.archives-search {
  display: flex;
  flex-direction: column;
  position: relative;
  height: calc(100vh - 48px);
  overflow: hidden;
}

.archives-search__header {
  padding: 20px 24px;
  background-color: $background-color;
}

.archives-search__title {
  display: flex;
  justify-content: center;
  align-items: center;
}

.archives-search__title-text {
  margin-right: 10px;
  color: #3d3d3d;
  font-size: 24px;
  line-height: 32px;
}

.archives-search__search-wrap {
  margin-top: 12px;
}

.archives-search__search-select :deep(.el-input__inner) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.archives-search__search-btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.archives-search__search-btn .el-icon-search {
  margin-left: 6px;
}

.archives-main-list {
  flex: 1;
  overflow-y: auto;
  margin-top: 20px;
  margin-bottom: 76px;
}

.archives-search__list {
  list-style: none;
  padding: 0;
  margin: 0 24px;
}

.archives-search__list > li {
  padding: 20px 24px;
  background-color: $background-color;
  text-align: left;

  &:not(:last-of-type) {
    margin-bottom: 20px;
  }
}

.archives-search__block {
  margin-bottom: 20px;
}

.archives-search__block > .title {
  line-height: 24px;
  font-size: 16px;
  text-align: left;
  font-weight: 500;
  position: relative;
  // padding-left: 12px;
  padding: 13px 0 12px 12px;
  display: flex;
  align-items: center;
  &:before {
    content: ' ';
    width: 4px;
    height: 16px;
    background: #00b38a;
    position: absolute;
    left: 0;
  }
}

.archives-search__info-row span:first-child {
  display: inline-block;
  width: 80px;
  line-height: 32px;
  color: #909399;
}
.archives-search__info-row span:last-child {
  display: inline-block;
  line-height: 32px;
  color: #303133;
}

.archives-search__file-list,
.archives-search__file-list2 {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  li {
    padding: 10px;
    border-radius: 4px;
    width: 200px;
    display: flex;
    align-items: center;
    background: $tes-bgA;
    border: 1px solid $tes-border;
    &:hover {
      cursor: pointer;
      background: $tes-primary2;
      border-color: $tes-primary1;
    }
  }
}

.file-icon {
  margin-right: 8px;
}

.file-icon2 {
  margin-right: 8px;
}

.file-name,
.file-name2 {
  width: 100%;
  color: $tes-font;
  max-height: 40px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

// .file-name2 {
//   flex-grow: 1;
//   white-space: nowrap;
//   text-overflow: ellipsis;
//   overflow: hidden;
//   color: $tes-font;
//   font-size: 14px;
//   line-height: 26px;
// }

.archives-search__item-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.archives-search__item-footer > span {
  margin-left: 30px;
}

.archives-search__footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 6px 24px;
  background: #fff;
}
</style>
