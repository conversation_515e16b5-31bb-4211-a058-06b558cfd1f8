<template>
  <!-- 正态分布图 -->
  <ListLayout :has-custom-header="true" :has-custom-main="true" :main-offset-top="topHeight">
    <template #page-custom-header>
      <CollapseHeader ref="collapse" @set-main-offset="setMainOffset">
        <template #title>
          <div v-if="showCondition" class="page-title page-comple">
            <span>{{ searchForm.inspectionTypeName }}</span>
            <span v-if="searchForm.materialClassificationName" class="space-line" />
            <span>{{ searchForm.materialClassificationName }}</span>
            <span v-if="searchForm.voltageLevelName" class="space-line" />
            <span>{{ searchForm.voltageLevelName }}</span>
            <span v-if="searchForm.materialGroupName" class="space-line" />
            <span>{{ searchForm.materialGroupName }}</span>
            <span v-if="searchForm.workingProcedureName" class="space-line" />
            <span v-if="searchForm.workingProcedureName">{{ searchForm.workingProcedureName }}</span>
            <span v-if="searchForm.itemKeyParam" class="space-line" />
            <span>{{ searchForm.itemKeyParam }}</span>
            <span v-if="searchForm.key">&nbsp;&nbsp;&nbsp;&nbsp;{{ `模糊搜索：${searchForm.key}` }}</span>
          </div>
          <div v-else class="page-title">选择查询条件生成正态分布图</div>
        </template>

        <template #collapse>
          <div class="collapse-group">
            <div class="left-form-group">
              <el-form
                ref="editFrom"
                label-width="110px"
                label-position="top"
                :inline="true"
                :model="searchForm"
                @submit.prevent
              >
                <el-row :gutter="16">
                  <el-col :span="4">
                    <el-form-item label="检验类型：" prop="inspectionType">
                      <el-select
                        v-model="searchForm.inspectionType"
                        class="chart-query-select"
                        placeholder="选择检验类型"
                        size="small"
                        clearable
                        @change="changeInspectionType"
                      >
                        <el-option
                          v-for="item in inspectionTypeOptions"
                          :key="item.id"
                          :label="item.name"
                          :value="item.code"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item
                      label="物资分类："
                      prop="materialClassification"
                      :rules="{
                        required: true,
                        message: '请选择物资分类',
                        trigger: 'change'
                      }"
                    >
                      <el-select
                        v-model="searchForm.materialClassification"
                        class="chart-query-select"
                        placeholder="选择物资分类"
                        size="small"
                        clearable
                        @change="changeMaterialClassification"
                      >
                        <el-option
                          v-for="item in materialClassifications"
                          :key="item.id"
                          :label="item.name"
                          :value="item.code"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item label="电压等级：" prop="voltageLevel">
                      <el-select
                        v-model="searchForm.voltageLevel"
                        class="chart-query-select"
                        placeholder="选择电压等级"
                        size="small"
                        clearable
                        @change="changeVoltageLevel"
                      >
                        <el-option
                          v-for="item in voltageLevelOptions"
                          :key="item.id"
                          :label="item.name"
                          :value="item.code"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item label="物料分组：" prop="materialGroupName">
                      <el-tag v-if="searchForm.materialGroupName" closable @close="deleteMaterialgroup">
                        {{ searchForm.materialGroupName }}
                      </el-tag>
                      <el-button
                        v-else
                        size="small"
                        icon="el-icon-plus"
                        @click="handleMaterialgroup"
                        @keyup.prevent
                        @keydown.enter.prevent
                        >选择物料分组</el-button
                      >
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item label="工序：">
                      <el-tag v-if="searchForm.workingProcedureName" size="small" closable @close="deleteProcess">
                        <span class="query-tag">{{ searchForm.workingProcedureName }}</span>
                      </el-tag>
                      <el-button
                        v-else
                        size="small"
                        icon="el-icon-plus"
                        @click="handleProcess"
                        @keyup.prevent
                        @keydown.enter.prevent
                        >选择工序</el-button
                      >
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item
                      label="关键参数："
                      prop="itemKeyParam"
                      :rules="{
                        required: true,
                        message: '请选择关键参数',
                        trigger: 'change'
                      }"
                    >
                      <el-tag v-if="searchForm.itemKeyParam" size="small" closable @close="deleteKeyParam()">
                        <span class="query-tag">{{ searchForm.itemKeyParam }}</span>
                      </el-tag>
                      <el-button
                        v-else
                        size="small"
                        icon="el-icon-plus"
                        @click="selectItems()"
                        @keyup.prevent
                        @keydown.enter.prevent
                        >选择关键参数</el-button
                      >
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item
                      label="时间范围："
                      class="date-range"
                      prop="submitTime"
                      :rules="{
                        required: true,
                        message: '请选择时间范围',
                        trigger: 'change'
                      }"
                    >
                      <el-date-picker
                        v-model="searchForm.submitTime"
                        type="daterange"
                        size="small"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :shortcuts="shortcuts"
                        @change="handleDatePicker"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="模糊搜索：" prop="key">
                      <el-input
                        v-model="searchForm.key"
                        size="small"
                        placeholder="请输入生产订单/物料编号/物料分组/样品名称/型号规格"
                        controls-position="right"
                        clearable
                        :min="2"
                        :max="10"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            <div class="right-button-group">
              <el-button
                key="sample"
                type="text"
                size="small"
                @click="renderSampleData()"
                @keyup.prevent
                @keydown.enter.prevent
              >
                <span class="el-icon-data-analysis" />
              </el-button>
              <el-button key="cancel" size="small" @click="cancelRender()" @keyup.prevent @keydown.enter.prevent
                >取消</el-button
              >
              <el-button
                :loading="renderLoading"
                type="primary"
                size="small"
                @click="renderNormDistData()"
                @keyup.prevent
                @keydown.enter.prevent
                >生成图表</el-button
              >
            </div>
          </div>
        </template>
      </CollapseHeader>
    </template>
    <template #page-custom-main>
      <el-row>
        <el-col :span="6">
          <CustomPanel :has-margin-left="true" :has-margin-right="true" :panel-height="'100%'">
            <template #panel-title>
              <div class="panel-header-left">
                <span class="title">属性</span>
              </div>
            </template>
            <template #panel-content>
              <el-descriptions v-if="paramTable.length > 0 && showChart" :column="1" border>
                <el-descriptions-item v-for="(item, index) in paramTable" :key="index" label-align="left">
                  <template #label>
                    <div class="cell-item">
                      {{ item.label }}
                    </div>
                  </template>
                  {{ Number(item.value).toExponential() }}
                </el-descriptions-item>
              </el-descriptions>
              <div v-else class="no-data">
                <img src="@/assets/img/empty-data.png" class="nodata-img" alt="no-data" />
              </div>
            </template>
          </CustomPanel>
        </el-col>
        <el-col :span="18">
          <CustomPanel :has-margin-right="true">
            <template #panel-title>
              <div class="panel-header-left">
                <span class="title">正态分布图</span>
              </div>
            </template>
            <template #panel-content>
              <line-bar-chart v-if="showChart" :width="'100%'" :height="'45vh'" :option="normDistOption" />
              <el-empty v-else :image="emptyImg" description="暂无图表" />
            </template>
          </CustomPanel>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <CustomPanel
            :has-margin-top="true"
            :has-margin-right="true"
            :has-margin-bottom="true"
            :has-margin-left="true"
          >
            <template #panel-title>
              <div class="panel-header-left">
                <span class="title">样本数据</span>
              </div>
            </template>
            <template #panel-content>
              <!-- <el-scrollbar height="400px"> -->
              <el-table v-if="showChart" :data="sampleTable" class="dark-table">
                <el-table-column label="序号" min-width="50px" align="left">
                  <template #default="scope">{{
                    scope.$index + 1 < 10 ? '0' + (scope.$index + 1) : scope.$index + 1
                  }}</template>
                </el-table-column>
                <el-table-column label="分组" min-width="50px" align="right">
                  <template #default="{ row }">{{ Number(row.group).toExponential(6) }}</template>
                </el-table-column>
                <el-table-column label="频数" min-width="100px" align="right">
                  <template #default="{ row }">{{ Number(row.freq).toExponential() }}</template>
                </el-table-column>
                <el-table-column label="正态曲线" min-width="100px" align="right">
                  <template #default="{ row }">{{ Number(row.normDist).toExponential() }}</template>
                </el-table-column>
              </el-table>
              <el-empty v-else :image="emptyTable" description="暂无图表" />
              <!-- </el-scrollbar> -->
            </template>
          </CustomPanel>
        </el-col>
      </el-row>

      <el-row>
        <el-col>
          <div style="height: 60px; width: 100%" />
        </el-col>
      </el-row>
    </template>

    <template #other>
      <MaterialGroup
        :dialog-visiable="dialogMaterialgroup"
        :detail-data="searchForm"
        :is-add="true"
        @selectRow="getMaterialgroup"
        @closeDialog="closeMaterialgroup"
      />
      <Process
        :dialog-visiable="dialogProcess"
        :is-add="true"
        :detail-data="searchForm"
        @selectRow="getProcess"
        @closeDialog="closeProcess"
      />
      <SelectKeyParam
        :show="showItemDialog"
        :tree="treeData"
        :data="oldSelectedKeyParam"
        @close="closeDialog"
        @selected-data="selectedKeyParam"
      />
      <BottomPanel>
        <template #panel-content>
          <div style="text-align: right">
            <el-button
              :loading="downloadLoading"
              type="primary"
              size="small"
              @click="exportToExcel()"
              @keyup.prevent
              @keydown.enter.prevent
              >数据导出</el-button
            >
          </div>
        </template>
      </BottomPanel>
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, watch, nextTick, computed, onMounted, getCurrentInstance } from 'vue';
import LineBarChart from '@/components/LineBarChart';
import ListLayout from '@/components/ListLayout';
import { formatDate, formatDateTime } from '@/utils/formatTime';
import { formatTree } from '@/utils/formatJson';
import { getCapabilityTree } from '@/api/user';
import { getDictionary } from '@/api/user';
import { getKeyParamData } from '@/api/order';
import { InspectionTypeAll } from '@/data/industryTerm';
import {
  getAxisInterval,
  getAverageOfArray,
  getStandardDeviation,
  getNormDistGroup,
  getNormDistRangeArray,
  getNormDistFreqArray,
  getNormDistArray
} from '../func/calculate';
import { demoSet } from '../data/testData';
import { past3Months } from '@/data/dateShortcuts';
import { useStore } from 'vuex';
import SelectKeyParam from '@/components/BusinessComponents/SelectKeyParam';
import Process from '@/components/BusinessComponents/Process';
import MaterialGroup from '@/components/BusinessComponents/MaterialGroup';
import CollapseHeader from '@/views/smart-charts/components/CollapseHeader';
import BottomPanel from '@/components/PageComponents/BottomPanel';
import CustomPanel from '@/components/PageComponents/CustomPanel';
import { ElMessage } from 'element-plus';
import { mathAdd } from '@/utils/calc/basicMath';
import { getNameByid } from '@/utils/common';
import emptyImg from '@/assets/img/empty-chart.png';
import emptyTable from '@/assets/img/empty-table.png';
import { getPageRequestParam, setPageRequestParam } from '@/utils/auth';
import { singleChartTooltipFormatter } from './tooltip-formatter';

export default {
  name: 'NormalDistribution',
  components: {
    ListLayout,
    LineBarChart,
    SelectKeyParam,
    Process,
    MaterialGroup,
    CollapseHeader,
    BottomPanel,
    CustomPanel
  },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const store = useStore();
    const processData = reactive({
      dialogProcess: false,
      isAdd: false
    });
    const data = reactive({
      searchForm: {
        // 检验类型
        inspectionType: '',
        inspectionTypeName: '全部',
        // 物资分类
        materialClassification: store.state.user.materialList[0]?.code || '',
        materialClassificationName: store.state.user.materialList[0]?.name || '',
        // 电压等级
        voltageLevel: '',
        voltageLevelName: '全部',
        // 物料分组
        materialGroupId: '',
        materialGroupName: '',
        materialGroupNo: '',
        // 工序
        workingProcedureId: '',
        workingProcedureName: '',
        workingProcedureCode: '',
        // 关键参数
        itemKeyParam: '',
        capabilityId: '',
        capabilityParamId: '',
        capabilityParamName: '',
        templateKey: '',
        // 时间范围
        submitTime: [formatDate(new Date().getTime() - 3600 * 1000 * 24 * 90), formatDate(new Date())],
        startTime: formatDate(new Date().getTime() - 3600 * 1000 * 24 * 90),
        endTime: formatDate(new Date()),
        // 子组大小
        subGroupCount: 5
      },
      formInline: {
        param: '',
        endTime: '',
        startTime: '',
        registerUserId: '',
        type: ''
      },
      showItemDialog: false,
      oldSelectedKeyParam: {},
      treeData: [],
      capabilityKeyParam: {},
      shortcuts: past3Months,
      inspectionTypeOptions: InspectionTypeAll,
      voltageLevelOptions: [],
      dialogMaterialgroup: false,
      show: false,
      topHeight: 85,
      headerPadding: 0,
      showChart: false,
      showCondition: false,
      downloadLoading: false,
      renderLoading: false
    });
    const sampleTable = ref([]);
    const paramTable = ref([]);
    const subgroupCount = ref(5);

    // #region 计算和渲染图表

    const normDistOption = ref({
      title: {
        text: '示例折线图'
      },
      xAxis: {
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    });

    function renderChart(keyItemData = demoSet) {
      data.showChart = true;
      const count = keyItemData.length;
      const mean = getAverageOfArray(keyItemData);
      const sortedSet = JSON.parse(JSON.stringify(keyItemData)).sort((a, b) => a - b);
      const max = sortedSet[count - 1];
      const min = sortedSet[0];
      const range = max - min;
      const stdDev = getStandardDeviation(sortedSet);
      const normarDivGroup = getNormDistGroup(count, min, max);
      const idealGroup = normarDivGroup.idealGroup;
      const groupDistance = normarDivGroup.groupDistance;
      const minRange = normarDivGroup.minRange;
      const groupCount = normarDivGroup.groupCount;
      const rangeArray = getNormDistRangeArray(minRange, groupDistance, groupCount);
      const freqArray = getNormDistFreqArray(sortedSet, minRange, groupDistance, groupCount);
      const normDistArray = getNormDistArray(rangeArray, mean, stdDev);
      const groupArray = Array(groupCount)
        .fill(null)
        .map((_, h) => `${h + 1}`);
      sampleTable.value = [];
      paramTable.value = [];

      let intervalCount = normDistArray.length;
      const sortedNormDistArray = JSON.parse(JSON.stringify(normDistArray)).sort((a, b) => a - b);
      const axisInfo = getAxisInterval(
        sortedNormDistArray[0],
        sortedNormDistArray[sortedNormDistArray.length - 1],
        intervalCount
      );
      const nbMin = axisInfo.axisMin;
      let nbMax = axisInfo.axisMax;
      const nbInterval = axisInfo.axistInterval;
      intervalCount = axisInfo.intervalCount;

      const sortedFreqArray = JSON.parse(JSON.stringify(freqArray)).sort((a, b) => a - b);
      const freqAxisInfo = getAxisInterval(
        sortedFreqArray[0],
        sortedFreqArray[sortedFreqArray.length - 1],
        intervalCount
      );
      const freqMin = freqAxisInfo.axisMin;
      let freqMax = freqAxisInfo.axisMax;
      const freqInterval = freqAxisInfo.axistInterval;
      intervalCount = freqAxisInfo.intervalCount > intervalCount ? freqAxisInfo.intervalCount : intervalCount;

      nbMax = mathAdd(nbMin, nbInterval * intervalCount);
      freqMax = mathAdd(freqMin, freqInterval * intervalCount);

      for (let i = 0; i < groupCount; i++) {
        sampleTable.value.push({
          group: rangeArray[i],
          freq: freqArray[i],
          normDist: normDistArray[i]
        });
      }

      const operationList = [
        { label: '平均值', value: mean.toFixed(6) },
        { label: '最大值', value: max },
        { label: '最小值', value: min },
        { label: '极差', value: range.toFixed(6) },
        { label: '标准差', value: stdDev.toFixed(6) },
        { label: '分组数', value: idealGroup },
        { label: '分组组距', value: groupDistance.toFixed(6) },
        { label: '实际组数', value: groupCount },
        { label: '最小分组区间', value: minRange.toFixed(6) }
      ];
      paramTable.value = operationList;

      normDistOption.value = {
        color: ['#4dcaae', '#e6a23c'],
        title: {
          text: ''
        },
        grid: {
          left: '40',
          right: '40',
          bottom: '30',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          extraCssText: 'text-align:left', // 设置tooltip的自定义样式
          formatter: params => {
            return singleChartTooltipFormatter(params);
          },
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        toolbox: {
          feature: {
            // dataView: { show: true, readOnly: false },
            // magicType: { show: true, type: ['line', 'bar'] },
            // restore: { show: true },
            // saveAsImage: { show: true }
          }
        },
        legend: {
          data: ['频率直方图', '正态分布曲线'],
          itemGap: 40
        },
        xAxis: [
          {
            type: 'category',
            data: groupArray,
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '频数',
            min: freqMin,
            max: freqMax,
            interval: freqInterval,
            splitLine: {
              lineStyle: {
                color: ['#ebeef5']
              }
            },
            axisLabel: {
              margin: 1,
              formatter: function (value) {
                return value.toExponential();
              }
            }
          },
          {
            type: 'value',
            name: '正态分布值',
            min: nbMin,
            max: nbMax,
            interval: nbInterval,
            splitLine: {
              lineStyle: {
                color: ['#ebeef5']
              }
            },
            axisLabel: {
              formatter: function (value) {
                return value.toExponential();
              }
            }
          }
        ],
        series: [
          {
            name: '频率直方图',
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return value;
              }
            },
            barWidth: '50%',
            barMaxWidth: 50,
            itemStyle: {
              borderColor: '#00B38A',
              borderWidth: 2
            },
            data: freqArray
          },
          {
            name: '正态分布曲线',
            type: 'line',
            yAxisIndex: 1,
            smooth: true,
            symbol: 'circle',
            symbolSize: 10,
            itemStyle: {
              color: '#F8E3C5',
              borderWidth: 2,
              borderColor: '#E6A23C'
            },
            lineStyle: {
              color: '#E6A23C',
              width: 2,
              shadowColor: 'rgba(230, 162, 60, 0.5)',
              shadowBlur: 6,
              shadowOffsetY: 4
            },
            tooltip: {
              valueFormatter: function (value) {
                return value;
              }
            },
            data: normDistArray
          }
        ]
      };
    }

    // #endregion

    // #region 查询条件表单

    const setMainOffset = height => {
      data.topHeight = height;
    };

    const getVoltageLevel = () => {
      getDictionary(2).then(res => {
        data.voltageLevelOptions = [{ code: '', name: '全部', status: 1 }].concat(res.data.data?.dictionaryoption);
      });
    };

    const materialClassifications = computed({
      get: () => store.state.user.materialList
    });

    const closeProcess = value => {
      processData.dialogProcess = false;
    };
    const closeMaterialgroup = value => {
      data.dialogMaterialgroup = false;
    };

    const deleteProcess = () => {
      data.searchForm.workingProcedureId = '';
      data.searchForm.workingProcedureName = '';
      data.searchForm.workingProcedureCode = '';
    };

    const deleteKeyParam = () => {
      data.searchForm.itemKeyParam = '';
      data.searchForm.capabilityId = '';
      data.searchForm.capabilityParamId = '';
      data.searchForm.capabilityParamName = '';
      data.searchForm.templateKey = '';
    };

    const deleteMaterialgroup = () => {
      data.searchForm.materialGroupId = '';
      data.searchForm.materialGroupNo = '';
      data.searchForm.materialGroupName = '';
    };

    const handleProcess = () => {
      processData.dialogProcess = true;
    };

    const handleMaterialgroup = () => {
      data.dialogMaterialgroup = true;
    };

    const handleDatePicker = value => {
      if (value) {
        data.expired = false;
        data.searchForm.startTime = formatDate(value[0]);
        data.searchForm.endTime = formatDate(value[1]);
      } else {
        data.searchForm.startTime = '';
        data.searchForm.endTime = '';
      }
    };

    const getProcess = value => {
      processData.dialogProcess = false;
      if (data.searchForm.workingProcedureId && value.workingProcedureId !== data.formData.workingProcedureId) {
        data.searchForm.workingProcedureId = '';
        data.searchForm.workingProcedureName = '';
        data.searchForm.workingProcedureCode = '';
      }
      data.searchForm = { ...data.searchForm, ...value };
    };

    const getMaterialgroup = value => {
      data.dialogMaterialgroup = false;
      if (data.searchForm.materialGroupId && value.materialGroupId !== data.formData.materialGroupId) {
        data.searchForm.materialGroupId = '';
        data.searchForm.materialGroupName = '';
        data.searchForm.materialGroupNo = '';
      }
      data.searchForm = { ...data.searchForm, ...value };
    };

    // 选择关键参数
    const selectItems = () => {
      data.showItemDialog = true;
    };
    // 关闭关键参数弹出框
    const closeDialog = () => {
      data.showItemDialog = false;
    };

    // 获取关键参数
    const selectedKeyParam = val => {
      data.capabilityKeyParam = val;
      data.searchForm.capabilityId = data.capabilityKeyParam.capabilityid;
      data.searchForm.capabilityParamId = data.capabilityKeyParam.id;
      data.searchForm.capabilityParamName = data.capabilityKeyParam.name;
      data.searchForm.templateKey = data.capabilityKeyParam.templatekey;
      data.searchForm.itemKeyParam = `${data.capabilityKeyParam.name}-${data.capabilityKeyParam.templatekey}`;
      data.oldSelectedKeyParam = JSON.parse(JSON.stringify(data.capabilityKeyParam));
    };

    const getTree = mateType => {
      getCapabilityTree(mateType).then(response => {
        if (response.data.code === 200) {
          const result = response.data.data;
          data.treeData = formatTree(result);
          data.treeData.unshift({
            id: '-1',
            parentId: '0',
            materialCategoryCode:
              result.length > 0 ? result[0].materialCategoryCode : data.searchForm.materialClassification,
            name: '全部',
            order: 0,
            status: 2
          });
        }
      });
    };

    const getInspectionTypeList = () => {
      getDictionary('JYLX').then(res => {
        data.inspectionTypeOptions = [];
        res.data.data?.dictionaryoption.forEach(item => {
          if (item.status === 1) {
            data.inspectionTypeOptions.push(item);
          }
        });
        data.inspectionTypeOptions.unshift({ id: '', code: '', name: '全部', status: 1 });
      });
    };

    const changeInspectionType = value => {
      data.searchForm.inspectionTypeName = getInspectionTypeName(value);
    };

    function getInspectionTypeName(code) {
      const inspectionIndex = data.inspectionTypeOptions.findIndex(item => item.code === code);
      return inspectionIndex === -1 ? '' : data.inspectionTypeOptions[inspectionIndex].name;
    }

    const changeMaterialClassification = value => {
      materialClassifications.value.forEach(item => {
        if (item.code === value) {
          data.searchForm.materialClassificationName = item.name;
        }
      });
    };

    const changeVoltageLevel = value => {
      data.searchForm.voltageLevelName = getVoltageLevelName(value);
    };

    function getVoltageLevelName(code) {
      const voltageIndex = data.voltageLevelOptions.findIndex(item => item.code === code);
      return voltageIndex === -1 ? '' : data.voltageLevelOptions[voltageIndex].name;
    }

    function getSearchFormParams() {
      if (!data.searchForm.materialClassification) {
        ElMessage.warning({
          message: '请先选择物资分类',
          type: 'warning'
        });
        return false;
      }
      if (!data.searchForm.capabilityId) {
        ElMessage.warning({
          message: '请先选择关键参数',
          type: 'warning'
        });
        return false;
      }
      if (!data.searchForm.startTime || !data.searchForm.endTime) {
        ElMessage.warning({
          message: '请先选择时间范围',
          type: 'warning'
        });
        return false;
      }
      return {
        capabilityId: data.searchForm.capabilityId,
        capabilityParaId: data.searchForm.capabilityParamId,
        capabilityParaName: data.searchForm.capabilityParamName,
        startDate: data.searchForm.startTime,
        endDate: data.searchForm.endTime,
        mateType: data.searchForm.materialClassification,
        materialGroupNo: data.searchForm.materialGroupNo,
        productionProcedureNo: data.searchForm.workingProcedureCode,
        templateKey: data.searchForm.templateKey,
        type: data.searchForm.inspectionType,
        voltName: data.searchForm.voltageLevel,
        key: data.searchForm.key
      };
    }

    const setQueryParams = () => {
      const localStorageParams = getPageRequestParam() ? JSON.parse(getPageRequestParam()) : {};
      localStorageParams['NormalDistribution'] = data.searchForm;
      setPageRequestParam(localStorageParams);
    };
    const renderNormDistData = () => {
      const keyParamPostBody = getSearchFormParams();
      if (!keyParamPostBody) {
        return;
      }
      data.showCondition = true;
      const keyParamValueArray = [];
      data.renderLoading = true;
      getKeyParamData(keyParamPostBody)
        .then(res => {
          setQueryParams();
          if (res && res.data.code === 200 && res.data.data.length > 0) {
            res.data.data.forEach(item => {
              if (!isNaN(Number(item.capabilityParaValue))) {
                keyParamValueArray.push(Number(item.capabilityParaValue));
              }
            });
            if (keyParamValueArray.length < 2) {
              ElMessage.warning({
                message: `该关键参数的历史数据数量为${keyParamValueArray.length}, 无法生成正态分布图!`,
                type: 'warning'
              });
              return;
            } else {
              data.showChart = true;
              proxy.$refs.collapse.handleCollapse();
              renderChart(keyParamValueArray);
            }
          } else {
            ElMessage.warning({
              message: '查找不到该关键参数的历史数据!',
              type: 'warning'
            });
          }
        })
        .catch(err => {
          ElMessage.error({
            message: `${err.message}`,
            type: 'error'
          });
        })
        .finally(() => {
          data.renderLoading = false;
        });
    };

    const renderSampleData = () => {
      data.showChart = true;
      data.showCondition = false;
      proxy.$refs.collapse.handleCollapse();
      renderChart();
    };

    const cancelRender = () => {
      data.showChart = false;
      data.showCondition = false;
      proxy.$refs.collapse.handleCollapse();
    };

    // #endregion

    // #region 表格

    // #endregion

    watch(
      () => data.showItemDialog,
      newValue => {
        if (newValue) {
          data.oldSelectedKeyParam = JSON.parse(JSON.stringify(data.capabilityKeyParam));
          nextTick(() => {
            if (data.searchForm.materialClassification) {
              getTree(data.searchForm.materialClassification);
            } else {
              ElMessage.warning({
                message: '请先选择物资分类！',
                type: 'warning'
              });
            }
          });
        }
      },
      { deep: true }
    );

    const exportToExcel = () => {
      const keyParamPostBody = getSearchFormParams();
      if (!keyParamPostBody) {
        return;
      }
      data.downloadLoading = true;
      getKeyParamData(keyParamPostBody)
        .then(res => {
          if (res.data.code === 200) {
            const result = res.data.data;
            if (result.length > 0) {
              const workSheets = [];
              import('@/utils/Export2Excel').then(excel => {
                if (result.length > 0) {
                  for (let i = 0; i < result.length; i++) {
                    result[i].ownerName = `${getNameByid(result[i].ownerId)}`;
                    result[i].voltageLevelName = `${result[i].voltName ? getVoltageLevelName(result[i].voltName) : ''}`;
                  }
                  const sampleHeader = [
                    '样品编号',
                    '物料分组',
                    '试验日期',
                    '物料编号',
                    '样品名称',
                    '规格型号',
                    '电压等级',
                    '批次',
                    '盘号',
                    '实验负责人',
                    '检验对象',
                    '对象位置',
                    '对象名称',
                    '项目',
                    '关键参数',
                    '线芯分组',
                    '关键参数实际值'
                  ];
                  const sampleFilterVal = [
                    'secSampleNum',
                    'materialGroup',
                    'startDate',
                    'materialNo',
                    'sampleName',
                    'prodType',
                    'voltageLevelName',
                    'batchNo',
                    'reelNo',
                    'ownerName',
                    'experimentObject',
                    'objectPosition',
                    'objectName',
                    'capabilityName',
                    'capabilityParaName',
                    'color',
                    'capabilityParaValue'
                  ];
                  const sampleExcelData = formatExcelData(sampleFilterVal, result);
                  const sampleSheet = excel.getWorkSheet({
                    header: sampleHeader,
                    data: sampleExcelData,
                    wsName: `样品信息`
                  });
                  workSheets.push(sampleSheet);
                }
                excel.exportMultiSheetExcel(workSheets, `LIMS-正态分布图 ${formatDateTime()}`);
              });
            } else {
              ElMessage.warning({
                message: '暂无可导出的的数据!',
                type: 'warning'
              });
              return;
            }
          }
        })
        .catch(err => {
          ElMessage.error({
            message: `${err.message}`,
            type: 'error'
          });
        })
        .finally(() => {
          data.downloadLoading = false;
        });
    };

    function formatExcelData(filterVal, jsonData) {
      return jsonData.map(v =>
        filterVal.map(j => {
          return v[j];
        })
      );
    }

    onMounted(() => {
      getVoltageLevel();
      getInspectionTypeList();
      const localStorageParams = getPageRequestParam() ? JSON.parse(getPageRequestParam()) : undefined;
      if (localStorageParams?.['NormalDistribution']) {
        data.searchForm = localStorageParams['NormalDistribution'];
        renderNormDistData();
      }
    });
    return {
      ...toRefs(data),
      ...toRefs(processData),
      subgroupCount,
      paramTable,
      sampleTable,
      normDistOption,
      materialClassifications,
      renderChart,
      handleDatePicker,
      selectItems,
      closeDialog,
      selectedKeyParam,
      getTree,
      getProcess,
      closeProcess,
      deleteProcess,
      handleProcess,
      deleteMaterialgroup,
      handleMaterialgroup,
      getMaterialgroup,
      closeMaterialgroup,
      getVoltageLevel,
      deleteKeyParam,
      setMainOffset,
      renderNormDistData,
      changeInspectionType,
      changeMaterialClassification,
      changeVoltageLevel,
      renderSampleData,
      cancelRender,
      emptyImg,
      emptyTable,
      exportToExcel
    };
  }
};
</script>
<style lang="scss" scoped></style>
