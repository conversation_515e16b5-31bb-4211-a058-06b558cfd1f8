import CryptoJS from 'crypto-js';

// ECB模式加密
export function encryptECB(word, keyStr) {
  keyStr = keyStr || 'kxxkKf2ge7t3VjzHDc2cGyDaJjg4IdEHyRHwMGgncek=';
  const key = CryptoJS.enc.Base64.parse(keyStr);
  const srcs = CryptoJS.enc.Base64.parse(word);
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  });
  return encrypted.toString();
}

// ECB模式解密
export function decryptECB(word, keyStr) {
  keyStr = keyStr || 'kxxkKf2ge7t3VjzHDc2cGyDaJjg4IdEHyRHwMGgncek=';
  const key = CryptoJS.enc.Base64.parse(keyStr);
  const srcs = CryptoJS.enc.Base64.parse(word);
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  });
  return encrypted.toString();
}

// CBC模式加密
export function encryptCBC(word, keyStr, ivStr) {
  keyStr = keyStr || 'kxxkKf2ge7t3VjzHDc2cGyDaJjg4IdEHyRHwMGgncek=';
  ivStr = ivStr || 'vh/3xcge+vOKHD++IY/MjQ==';
  const key = CryptoJS.enc.Base64.parse(keyStr);
  const iv = CryptoJS.enc.Base64.parse(ivStr);
  const srcs = CryptoJS.enc.Base64.parse(word);
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });
  return encrypted.toString();
}

// CBC模式解密
export function decryptCBC(word, keyStr, ivStr) {
  if (!word) {
    return '';
  }
  keyStr = keyStr || 'kxxkKf2ge7t3VjzHDc2cGyDaJjg4IdEHyRHwMGgncek=';
  ivStr = ivStr || 'vh/3xcge+vOKHD++IY/MjQ==';
  var key = CryptoJS.enc.Base64.parse(keyStr);
  const iv = CryptoJS.enc.Base64.parse(ivStr);

  var decrypt = CryptoJS.AES.decrypt(word, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });
  return decrypt.toString(CryptoJS.enc.Utf8);
}
