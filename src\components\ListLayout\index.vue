<template>
  <div class="page-wrapper">
    <!-- 页头 固定区域 -->
    <div v-if="hasPageHeader" class="page-header" :class="{ 'page-custom-header': hasCustomHeader }">
      <!-- 适用通用列表页 -->
      <div v-if="!hasCustomHeader" class="page-header">
        <div class="page-searchbar">
          <slot name="search-bar" />
        </div>
        <!-- 右侧按钮组 -->
        <div v-if="hasButtonGroup" style="display: flex; flex-direction: row">
          <slot name="button-group" />
        </div>
      </div>
      <div v-else>
        <!-- 适用复杂的页头 -->
        <slot name="page-custom-header" />
      </div>
    </div>
    <!-- 主体 页面滚动区域 -->
    <div class="page-list-main" :style="{ top: hasPageHeader ? `${mainOffsetTop}px` : '20px' }">
      <div v-if="!hasCustomMain" class="page-default-main">
        <div v-if="hasSearchPanel" class="search-panel">
          <!-- 高级搜索展开区域 -->
          <slot name="search-panel" />
        </div>
        <el-container v-if="hasLeftPanel && !hasRightPanel" class="main-panel">
          <!-- 列表页双列布局 -->
          <el-aside v-show="showAside" :style="{ width: asideWidth + 'px' }">
            <!-- 列表页左侧区域 -->
            <div
              :class="'left-panel' + (hasAsidePadding ? ' left-panel-padding' : '')"
              :style="{ height: asideHeight }"
            >
              <slot name="page-left-side" />
            </div>
          </el-aside>
          <el-tooltip
            v-if="hasLeftPanel && hasDragHandle"
            effect="dark"
            :content="collapseTip"
            :hide-after="0"
            placement="right"
          >
            <drag-handle @widthChange="widthChange" @mouseup="collapseLeft" @mousedown="setOldAsideWidth" />
          </el-tooltip>
          <el-container :class="{ 'margin-left-20': hasLeftPanel && !hasDragHandle }">
            <!-- 列表页主体区域 -->
            <el-main>
              <!-- 列表页表格区域 -->
              <div v-if="hasQuickQuery" class="radio-content">
                <!-- 快捷查询 -->
                <slot name="radio-content" />
              </div>
              <div class="page-main" :style="{ height: mainHeight }">
                <slot />
              </div>
            </el-main>
          </el-container>
        </el-container>
        <el-container v-else-if="!hasLeftPanel && hasRightPanel" class="main-panel">
          <!-- 列表页双列布局 -->
          <el-container :class="{ 'margin-right-20 ': hasRightPanel && !hasDragHandle }">
            <!-- 列表页主体区域 -->
            <el-main>
              <!-- 列表页表格区域 -->
              <div v-if="hasQuickQuery" class="radio-content">
                <!-- 快捷查询 -->
                <slot name="radio-content" />
              </div>
              <div class="page-main" :style="{ height: mainHeight }">
                <slot />
              </div>
            </el-main>
          </el-container>
          <el-tooltip
            v-if="hasRightPanel && hasDragHandle"
            effect="dark"
            :content="collapseTip"
            :hide-after="0"
            placement="right"
          >
            <drag-handle @widthChange="rightWidthChange" @mouseup="collapseRight" @mousedown="setOldAsideWidth" />
          </el-tooltip>
          <el-aside v-show="showAside" :style="{ width: asideWidth + 'px' }">
            <!-- 列表页右侧区域 -->
            <div
              :class="'left-panel' + (hasAsidePadding ? ' left-panel-padding' : '')"
              :style="{ height: asideHeight }"
            >
              <slot name="page-right-side" />
            </div>
          </el-aside>
        </el-container>
        <el-container v-else>
          <!-- 列表页默认布局 -->
          <el-main>
            <!-- 列表页表格区域 -->
            <div v-if="hasQuickQuery" class="radio-content">
              <!-- 快捷查询 -->
              <slot name="radio-content" />
            </div>
            <!-- 主体区域 -->
            <div class="page-main" :style="{ height: mainHeight }">
              <slot />
            </div>
          </el-main>
        </el-container>
      </div>
      <div v-else class="page-custom-main">
        <!-- 适用复杂的页面主体-->
        <slot name="page-custom-main" />
      </div>
    </div>
    <slot name="other" />
  </div>
</template>
<script>
import DragHandle from '@/components/DragHandle/handle.vue';
import { reactive, toRefs } from 'vue';
export default {
  name: 'ListLayout',
  components: { DragHandle },
  props: {
    hasPageHeader: {
      type: Boolean,
      default: true
    },
    hasCustomHeader: {
      type: Boolean,
      default: false
    },
    hasCustomMain: {
      type: Boolean,
      default: false
    },
    hasQuickQuery: {
      type: Boolean,
      default: true
    },
    hasSearchPanel: {
      type: Boolean,
      default: true
    },
    hasButtonGroup: {
      type: Boolean,
      default: true
    },
    hasLeftPanel: {
      type: Boolean,
      default: false
    },
    hasRightPanel: {
      type: Boolean,
      default: false
    },
    hasDragHandle: {
      type: Boolean,
      default: true
    },
    hasAsidePadding: {
      type: Boolean,
      default: true
    },
    // 主体顶部偏移量
    mainOffsetTop: {
      type: Number,
      default: 84
    },
    asidePanelWidth: {
      type: Number,
      default: 300
    },
    asideMaxWidth: {
      type: Number,
      default: 600
    },
    asideMinWidth: {
      type: Number,
      default: 200
    },
    asideHeight: {
      type: String,
      default: 'auto'
    },
    mainHeight: {
      type: String,
      default: 'auto'
    }
  },
  setup(props, context) {
    const data = reactive({
      showAside: true,
      asideWidth: props.asidePanelWidth,
      asideMaxWidth: props.asideMaxWidth,
      asideMinWidth: props.asideMinWidth,
      oldAsideWidth: JSON.parse(JSON.stringify(props.asidePanelWidth)),
      collapseTip: props.hasLeftPanel ? '点击折叠左面板' : '点击折叠右面板'
    });
    // 拖拽边框
    const widthChange = m => {
      data.asideWidth = data.asideWidth - m;
      if (data.asideWidth < data.asideMinWidth) {
        data.asideWidth = data.asideMinWidth;
      } else {
        if (data.asideWidth >= data.asideMaxWidth) {
          data.asideWidth = data.asideMaxWidth;
        }
      }
    };
    // 拖拽边框
    const rightWidthChange = m => {
      data.asideWidth = data.asideWidth + m;
      if (data.asideWidth < data.asideMinWidth) {
        data.asideWidth = data.asideMinWidth;
      } else {
        if (data.asideWidth >= data.asideMaxWidth) {
          data.asideWidth = data.asideMaxWidth;
        }
      }
    };
    const setOldAsideWidth = () => {
      data.oldAsideWidth = JSON.parse(JSON.stringify(data.asideWidth));
    };
    const collapseLeft = () => {
      if (data.asideWidth === data.oldAsideWidth) {
        data.showAside = !data.showAside;
        data.collapseTip = data.showAside ? '点击折叠左面板' : '点击展开左面板';
      }
    };
    const collapseRight = () => {
      if (data.asideWidth === data.oldAsideWidth) {
        data.showAside = !data.showAside;
        data.collapseTip = data.showAside ? '点击折叠右面板' : '点击展开右面板';
      }
    };

    return {
      ...toRefs(data),
      widthChange,
      collapseLeft,
      collapseRight,
      setOldAsideWidth,
      rightWidthChange
    };
  }
};
</script>

<style lang="scss" scoped>
.page-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden auto;
}

.page-custom-header,
.page-custom-header > div {
  width: 100%;
}

.search-panel {
  :slotted(.el-collapse) {
    .el-collapse-item {
      .el-collapse-item__wrap {
        background: none;
        border: none;
      }

      .el-collapse-item__content {
        margin-bottom: 20px;
        padding: 20px 20px 10px;
        background: $background-color;
      }

      .el-collapse-item__header {
        display: none;
      }
    }

    .el-form-item__content {
      text-align: left;
    }

    .el-form-item__label {
      line-height: 32px;
    }

    .el-radio__input {
      display: none;
    }

    .el-radio-button--mini .el-radio-button__inner {
      padding-left: 10px;
    }

    .el-checkbox {
      margin-left: 20px;
    }

    .el-radio-button {
      display: inline-block;
      margin-left: 10px;
    }

    .el-radio-group {
      .el-radio-button.is-active {
        .el-radio-button__inner {
          color: $tes-primary;
          box-shadow: none;
        }
      }

      .el-radio-button {
        &:first-child {
          background: none;
        }
      }

      .el-radio-button__inner {
        border: 0;
        font-size: 14px;
        background: none !important;
        padding: 5px;
      }

      .date-radio {
        display: inline-block;
        margin-left: 10px;
      }

      .label-type {
        margin-right: 4px;
      }
    }
  }
}

.page-list-main {
  // margin-top: 20px;
  .el-container {
    .el-main {
      margin: 0 24px;
      background: $background-color;

      .radio-content {
        margin: 20px 20px 0;
        width: auto;

        // :slotted(.el-checkbox) {
        //   margin-left: 15px;
        // }
      }
    }

    .el-container {
      .el-main {
        margin: 0;
      }
    }
  }

  // 双列布局
  .main-panel {
    padding: 0 24px;

    .left-panel {
      background: $background-color;
      overflow: hidden;
      // height: 100%;
    }

    .left-panel-padding {
      padding: 20px 20px 10px;
    }

    .page-main {
      margin: 0 0 0 20px;
    }
  }
}

.margin-left-20 {
  margin-left: 20px;
}

.margin-right-20 {
  margin-right: 20px;
}

.el-container {
  .el-aside,
  .el-main {
    padding: 0;
    border-radius: 0;
  }

  .el-aside {
    margin: 0;
  }

  .el-main {
    .page-main {
      margin: 0;
    }
  }
}
</style>
