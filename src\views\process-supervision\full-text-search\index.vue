<template>
  <el-popover :visible="visible" trigger="manual" width="30vw" popper-class="full-text-search__popover">
    <template #reference>
      <el-input v-model="keywordStr" placeholder="全文检索" style="width: 30vw" @input="onInput">
        <template #prefix>
          <div class="full-text-search__icon">
            <i :class="loading ? 'el-icon-loading' : 'el-icon-search'" />
          </div>
        </template>
      </el-input>
    </template>
    <template #default>
      <el-scrollbar v-click-outside="onClickOutside" tag="ul" height="400px" class="full-text-search__list">
        <li v-for="row in list" :key="row.id">
          <div class="full-text-search__group-title">{{ row.archivalCode }}</div>
          <ul class="full-text-search__file-list">
            <li v-for="item in row.experimentData" :key="item.id" @click="handleDownload(item)">
              <span
                v-for="(strItem, index) in item.sourceName"
                :key="index"
                :style="strItem.keyword ? { color: 'red' } : null"
              >
                {{ strItem.text }}
              </span>
              <i class="el-icon-download" />
            </li>
          </ul>
        </li>
      </el-scrollbar>
    </template>
  </el-popover>
</template>

<script>
import { reactive, toRefs } from 'vue';
import { archivalQuery } from '@/api/order';
import { debounce } from 'lodash';
import { ClickOutside } from 'element-plus/lib/directives';

export default {
  name: 'OutsourcingManagement',
  directives: { ClickOutside },
  setup() {
    const state = reactive({
      visible: false,
      keywordStr: '',
      list: [],
      loading: false
    });

    const onInput = debounce(keyword => {
      if (keyword) {
        console.log('keyword', keyword);
        query(keyword);
      }
    }, 500);

    const query = async keyword => {
      state.loading = true;
      const res = await archivalQuery({ param: keyword });
      state.loading = false;
      if (res) {
        console.log('res', res);
        const result = formatArchivalData(res.data.data, keyword);
        // showDropdown()
        state.visible = true;
        state.list = result;
      }
    };

    const formatArchivalData = (data, keyword) => {
      return data.map(row => ({
        id: row.id,
        archivalCode: row.archivalCode,
        experimentData: row.experimentData.map(item => ({
          id: item.id,
          sourceName: setKeyword(item.sourceName, keyword),
          sourceNumber: item.sourceNumber,
          fileUrl: item.fileUrl
        }))
      }));
    };

    const setKeyword = (str, keyword) => {
      const keywordRegexp = new RegExp(keyword, 'g');
      if (keywordRegexp.test(str)) {
        const strArr = str
          .replace(keywordRegexp, ',${0},')
          .split(',')
          .reduce((accumulator, currentItem) => {
            if (currentItem) {
              accumulator.push(currentItem === '${0}' ? { text: keyword, keyword: true } : { text: currentItem });
            }
            return accumulator;
          }, []);
        return strArr;
      } else {
        return [{ text: str }];
      }
    };

    const onClickOutside = () => {
      state.visible = false;
    };

    const handleDownload = item => {
      if (item?.fileUrl) {
        window.open(item.fileUrl, '_blank');
      }
    };

    return {
      ...toRefs(state),
      onClickOutside,
      onInput,
      handleDownload
    };
  }
};
</script>

<style lang="scss" scoped>
.full-text-search__icon {
  width: 25px;
  text-align: center;
}

.full-text-search__list :deep(.el-scrollbar__view) {
  list-style: none;
  padding: 0;
  margin: 0;
}

.full-text-search__list :deep(.el-scrollbar__wrap) {
  padding-right: 12px;
}

.full-text-search__group-title {
  margin-bottom: 10px;
  color: #909399;
}

.full-text-search__file-list {
  list-style: none;
  padding: 0;
  margin: 0 0 20px 0;
}

.full-text-search__file-list li {
  position: relative;
  padding: 8px 34px 8px 14px;
  border: 1px solid transparent;
  margin: 0 4px 10px 4px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.12);
  line-height: 20px;
  text-align: left;
  transition: all 0.5s ease;
  cursor: pointer;

  &:last-child {
    margin-bottom: 0;
  }

  &.active,
  &:hover {
    // border: 1px solid var(--tesPrimary);
    box-shadow: 0px 0px 4px 0px var(--tesPrimary);
    background-color: var(--menuHover);
  }

  & > .el-icon-download {
    position: absolute;
    top: 50%;
    right: 14px;
    transform: translateY(-50%);
    font-size: 16px;
    line-height: 20px;
  }
}
</style>

<style>
.full-text-search__popover.el-popover.el-popper {
  padding-right: 0;
}
</style>
