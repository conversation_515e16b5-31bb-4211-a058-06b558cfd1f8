<template>
  <!-- 检测机构信息 -->
  <!-- 分包商信息 -->
  <el-drawer
    v-model="showDrawer"
    :title="category === 1 ? titlesSub[drawerType] : titles[drawerType]"
    direction="rtl"
    :before-close="handleClose"
    :size="750"
    destroy-on-close
    :close-on-click-modal="false"
    @opened="handleOpened"
  >
    <DrawerLayout v-loading="drawerLoading" :has-button-group="false" :has-page-header="false">
      <el-form
        ref="formRef"
        :inline="true"
        :model="formData"
        label-width="110px"
        class="form-height-auto"
        label-position="top"
      >
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="状态：" prop="isValid">
              <el-switch
                v-model="formData.isValid"
                class="inner-switch"
                :active-value="true"
                :inactive-value="false"
                :active-text="formData.isValid ? '启用' : '停用'"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="category === 0" :span="12">
            <el-form-item label="检测机构属性：" prop="type">
              <el-radio-group v-model="formData.type">
                <el-radio :label="0">内部</el-radio>
                <el-radio :label="1">外部</el-radio>
                <el-radio :label="2">检储配</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="category === 1 ? '分包商编号：' : '机构编号：'"
              prop="no"
              :rules="{
                required: true,
                message: category === 1 ? '请输入分包商编号' : '请输入机构编号',
                trigger: 'change'
              }"
            >
              <el-input
                ref="inputRef"
                v-model="formData.no"
                v-trim
                maxlength="100"
                :placeholder="category === 1 ? '请输入分包商编号' : '请输入机构编号'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="category === 1 ? '分包商名称：' : '公司名称：'"
              prop="name"
              :rules="{
                required: true,
                message: category === 1 ? '请输入分包商名称' : '请输入公司名称',
                trigger: 'change'
              }"
            >
              <el-input
                v-model="formData.name"
                maxlength="100"
                :placeholder="category === 1 ? '请输入分包商名称' : '请输入公司名称'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="英文名：" prop="enName">
              <el-input v-model="formData.enName" maxlength="100" placeholder="请输入英文名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司传真：" prop="faxNo">
              <el-input v-model="formData.faxNo" maxlength="100" placeholder="请输入公司传真" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司电话：" prop="phone" :rules="[{ validator: isPhoneMobile, tigger: 'blur' }]">
              <el-input v-model="formData.phone" maxlength="50" placeholder="请输入公司电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="盖章范围：" prop="gzfw">
              <el-checkbox-group v-model="formData.gzfw" size="small" @change="changeGzfw">
                <el-checkbox label="isCnas">CNAS</el-checkbox>
                <el-checkbox label="isCma">CMA</el-checkbox>
                <el-checkbox label="isStamp">检测专用</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="经营范围：" prop="bizScope">
              <el-input
                v-model="formData.bizScope"
                type="textarea"
                maxlength="300"
                :rows="3"
                placeholder="请输入经营范围"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注：" prop="remark">
              <el-input v-model="formData.remark" type="textarea" maxlength="300" :rows="3" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="drawer-fotter">
        <el-button type="primary" :loading="drawerLoading" @click="onSubmit">确认</el-button>
        <el-button :loading="drawerLoading" @click="handleClose">取消</el-button>
      </div>
    </DrawerLayout>
  </el-drawer>
</template>

<script>
import { ref, watch, reactive, getCurrentInstance, toRefs } from 'vue';
import { saveOrganization } from '@/api/testingApparatus';
import { isPhoneMobile } from '@/utils/validate';
import DrawerLayout from '@/components/DrawerLayout';
import store from '@/store';
import { useRoute } from 'vue-router';

export default {
  name: 'DrawerCustomerInfo',
  components: { DrawerLayout },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    drawerType: {
      type: String,
      required: true
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // 抽屉事件
    const showDrawer = ref(props.drawer);
    const route = useRoute();
    const state = reactive({
      titles: {
        add: '新增机构信息',
        edit: '编辑机构信息',
        check: '查看机构信息'
      },
      titlesSub: {
        add: '新增分包商信息',
        edit: '编辑分包商信息',
        check: '查看分包商信息'
      },
      customerStatus: {},
      inputRef: ref(),
      drawerType: '',
      drawerLoading: false,
      userList: store.state.common.nameList,
      tit: {
        1: '年',
        2: '月'
      },
      dialogTreeData: [],
      equipUnit: [],
      formRef: ref(),
      formData: {
        gzfw: []
      },
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      category: route.name === 'SubcontractorManagement' ? 1 : 0 // 1 分包商管理页面， 0 检测机构页面
    });
    const handleOpened = () => {
      if (state.inputRef) {
        state.inputRef.focus();
      }
    };
    // 关闭抽屉
    const handleClose = () => {
      showDrawer.value = false;
      context.emit('close', { isRefresh: false, isShow: false });
    };
    watch(props, newValue => {
      showDrawer.value = newValue.drawer;
      if (showDrawer.value) {
        state.drawerType = props.drawerType;
        state.customerStatus = props.customerStatus;
        if (state.drawerType === 'add') {
          state.formData = {
            gzfw: [],
            type: 0,
            isValid: true,
            category: state.category
          };
        } else {
          state.formData = JSON.parse(JSON.stringify(props.detailData));
          state.formData.gzfw = [];
          const array = ['isCnas', 'isCma', 'isStamp'];
          array.forEach(item => {
            if (state.formData[item]) {
              state.formData.gzfw.push(item);
            }
          });
        }
      }
    });
    // 确认新增
    const onSubmit = () => {
      proxy.$refs['formRef'].validate(valid => {
        if (valid) {
          state.drawerLoading = true;
          saveOrganization(state.formData).then(res => {
            state.drawerLoading = false;
            if (res) {
              context.emit('close', { isRefresh: true, isShow: false });
              proxy.$message.success('保存成功');
            }
          });
        } else {
          return false;
        }
      });
    };
    const validateNumber = (rule, value, callback) => {
      const numberReg = /^\d+$/;
      if (value === '' || value === undefined || value === null) {
        callback(new Error('请输入计量周期'));
      } else {
        if (numberReg.test(value)) {
          if (value.length > 1 || value === 0 || value === '0') {
            if (value.substring(0, 1) > 0) {
              callback();
            } else {
              callback(new Error('请输入非负整数'));
            }
          } else {
            callback();
          }
        } else {
          callback(new Error('请输入非负整数'));
        }
      }
    };
    // 盖章范围
    const changeGzfw = val => {
      const array = ['isCnas', 'isCma', 'isStamp'];
      console.log(val);
      array.forEach(item => {
        console.log(item, val.indexOf);
        if (val.indexOf(item) > -1) {
          state.formData[item] = true;
        } else {
          state.formData[item] = false;
        }
      });
      console.log(state.formData);
    };

    return {
      ...toRefs(state),
      changeGzfw,
      handleOpened,
      isPhoneMobile,
      validateNumber,
      onSubmit,
      handleClose,
      showDrawer
    };
  }
};
</script>

<style lang="scss" scoped></style>
