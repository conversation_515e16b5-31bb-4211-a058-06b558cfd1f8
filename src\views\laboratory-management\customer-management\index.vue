<template>
  <!-- 客户管理 -->
  <ListLayout :has-button-group="getPermissionBtn('addCustomer')">
    <template #search-bar>
      <div class="searchInput">
        <el-input
          v-model="condition"
          v-trim
          v-focus
          size="large"
          class="ipt-360"
          clearable
          prefix-icon="el-icon-search"
          placeholder="请输入编号/名称/描述"
          @clear="getTableList"
          @keyup.enter="getTableList"
        />
        <el-button size="large" type="primary" @click="getTableList">查询</el-button>
        <el-button size="large" @click="reset">重置</el-button>
      </div>
    </template>
    <template #button-group>
      <el-button
        size="large"
        icon="el-icon-plus"
        type="primary"
        @click="addCustomer"
        @keyup.prevent
        @keydown.enter.prevent
        >新增客户</el-button
      >
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="16">
          <el-radio-group v-model="isValid" size="small" @change="getTableList">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button :label="true">已生效</el-radio-button>
            <el-radio-button :label="false">已作废</el-radio-button>
          </el-radio-group>
        </el-col>
        <el-col :span="8" style="text-align: right">
          <TableColumnView binding-menu="CustomerManagement" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="tableList"
      size="medium"
      fit
      border
      height="auto"
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="item.fieldType === fieldTypesEnum.Person">
              <UserTag :name="row[item.fieldKey] || '--'" />
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Status">
              <el-tag v-if="row.isValid" effect="dark" size="small" type="success">生效</el-tag>
              <el-tag v-else size="small" effect="dark" type="info">作废</el-tag>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Custom">
              <span v-if="item.fieldKey === 'reckoningWay'">{{ customerStatusJson[row[item.fieldKey]] || '--' }}</span>
              <span v-if="item.fieldKey === 'credit'">{{ reputationJson[row[item.fieldKey]] || '--' }}</span>
            </template>
            <span v-else>{{ row[item.fieldKey] || '--' }}</span>
          </template>
        </el-table-column>
      </template>

      <!-- <el-table-column label="客户编号" prop="no" width="200" align="left" show-overflow-tooltip sortable>
        <template #default="{ row }">
          {{ row.no || '--' }}
        </template>
      </el-table-column>
      <el-table-column label="客户名称" prop="name" :width="colWidth.material" align="left" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.name }}
        </template>
      </el-table-column>
      <el-table-column label="公司电话" prop="phone" align="left" :width="colWidth.model" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.phone || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="默认联系人" prop="isRemeasured" align="left" :width="120">
        <template #default="{ row }">
          <UserTag :name="row.defaultContacts || '--' " />
        </template>
      </el-table-column>
      <el-table-column label="默认地址" prop="defaultAddress" align="left" :min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.defaultAddress||'--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户描述" prop="remark" :min-width="colWidth.remark" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.remark||'--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结算方式" prop="reckoningWay" :width="120" show-overflow-tooltip>
        <template #default="{ row }">
          {{ customerStatusJson[row.reckoningWay]||'--' }}
        </template>
      </el-table-column>
      <el-table-column label="客户信誉" prop="credit" align="left" :width="colWidth.amount">
        <template #default="{ row }">
          <div>{{ reputationJson[row.credit] || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="isValid" align="left" :width="colWidth.cycle">
        <template #default="{ row }">
          <el-tag v-if="row.isValid" effect="dark" size="small" type="success">生效</el-tag>
          <el-tag v-else size="small" effect="dark" type="info">作废</el-tag>
        </template>
      </el-table-column> -->
      <el-table-column label="操作" :width="colWidth.operation" fixed="right" class-name="fixed-right">
        <template #default="{ row }">
          <span class="blue-color" @click="checkRow(row)">查看</span>
          <span
            v-if="row.isValid && getPermissionBtn('cancelCustomer')"
            class="blue-color"
            @click="cancellationRow(row)"
            >作废</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getTableList"
    />
    <template #other>
      <!-- 客户所有信息抽屉 -->
      <DrawerDetail
        :detail-drawer="detailDrawer"
        :drawer-type="drawerType"
        :detail-info="detailInfo"
        :customer-status="customerStatusJson"
        :reputation="reputationJson"
        @closeDrawer="closeDetailDrawer"
      />
      <!-- 新增客户信息 -->
      <DrawerCustomerInfo
        :drawer="customerDrawer"
        :customer-status="customerStatusJson"
        :reputation="reputationJson"
        :drawer-type="customerDrawerType"
        @close="closeCustomerDrawer"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import Pagination from '@/components/Pagination';
import ListLayout from '@/components/ListLayout';
import { formatDate } from '@/utils/formatTime';
import { useStore } from 'vuex';
import UserTag from '@/components/UserTag';
import DrawerDetail from './DrawerDetail.vue';
import DrawerCustomerInfo from './DrawerCustomerInfo.vue';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { getDictionary } from '@/api/user';
import { getList, invalidCustomer } from '@/api/customerManagement';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import { colWidth } from '@/data/tableStyle';
import TableColumnView from '@/components/TableColumnView';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';

export default {
  name: 'CustomerManagement',
  components: { Pagination, ListLayout, UserTag, DrawerDetail, DrawerCustomerInfo, TableColumnView },
  setup() {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const state = reactive({
      tableRef: ref(),
      isValid: '',
      condition: '', // 列表关键字
      drawerType: '', // 详情抽屉类型
      detailDrawer: false, // 仪器设备编号抽屉
      customerDrawer: false,
      orderBy: '', // 排序字段
      isDesc: '', // 是否倒叙
      customerDrawerType: '',
      detailInfo: {},
      ruleForm: ref(),
      listLoading: false,
      statusType: {
        Fault: 'danger',
        Running: 'success',
        Scrapped: 'info'
      },
      customerStatusJson: {}, // 客户状态
      reputationJson: {}, // 客户信誉
      userOptions: store.common.nameList,
      types: [],
      listQuery: {
        limit: 20,
        page: 1
      },
      tableColumns: [],
      tableList: [],
      total: 0,
      currentAccountId: getLoginInfo().accountId
    });
    const tableKey = ref(0);
    const getTableList = query => {
      const params = { condition: state.condition, orderBy: state.orderBy };
      if (state.isValid !== '') {
        params.isValid = state.isValid;
      }
      if (state.isDesc !== '') {
        params.isDesc = state.isDesc;
      }
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoading = true;
      getList(params).then(res => {
        state.listLoading = false;
        if (res) {
          state.total = res.data.data.totalCount;
          state.tableList = res.data.data.list;
        }
      });
    };
    getTableList();
    const sortChange = column => {
      state.orderBy = column.prop;
      if (column.order === 'descending') {
        state.isDesc = true;
      } else {
        state.isDesc = false;
      }
      getTableList();
    };
    // 从字典获取结算方式
    const getDictionaryJSFS = () => {
      getDictionary('JSFS').then(res => {
        if (res) {
          const data = res.data.data.dictionaryoption;
          data.forEach(item => {
            state.customerStatusJson[item.code] = item.name;
          });
        }
      });
    };
    // 从字典获取客户信誉
    const getDictionaryKHXY = () => {
      getDictionary('KHXY').then(res => {
        if (res) {
          const data = res.data.data.dictionaryoption;
          data.forEach(item => {
            state.reputationJson[item.code] = item.name;
          });
        }
      });
    };
    getDictionaryJSFS();
    getDictionaryKHXY();
    const reset = () => {
      state.isValid = '';
      state.condition = '';
      state.orderBy = '';
      state.isDesc = '';
      getTableList();
    };
    // 列表操作
    const handleOperate = () => {};
    // 新增客户信息
    const addCustomer = () => {
      state.customerDrawer = true;
      state.customerDrawerType = 'add';
    };
    // 查看客户信息
    const checkRow = row => {
      state.detailDrawer = true;
      state.drawerType = 'check';
      state.detailInfo = row;
    };
    // 作废客户信息
    const cancellationRow = row => {
      proxy
        .$confirm('是否确认作废该客户信息？', '作废确认', {
          confirmButtonText: '确认作废',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'warning'
        })
        .then(() => {
          state.listLoading = true;
          invalidCustomer(row.id).then(function (res) {
            state.listLoading = false;
            if (res) {
              proxy.$message.success(res.data.message);
              getTableList();
            }
          });
        })
        .catch(() => {});
    };
    // 关闭弹出窗
    const closeDialog = val => {
      state.detailDrawer = false;
      if (val.isRefresh) {
        getTableList();
      }
    };
    const closeCustomerDrawer = val => {
      state.customerDrawer = false;
      if (val.isRefresh) {
        getTableList();
      }
    };
    // 切换维修结果
    const changeMateType = () => {
      getTableList();
    };
    // 关闭仪器设备弹出窗
    const closeDetailDrawer = val => {
      state.detailDrawer = false;
      if (val.isRefresh) {
        getTableList();
      }
    };
    const onUpdateColumns = columns => {
      tableKey.value = tableKey.value + 1;
      state.tableColumns = columns;
    };
    return {
      ...toRefs(state),
      sortChange,
      closeCustomerDrawer,
      getDictionaryJSFS,
      getDictionaryKHXY,
      getPermissionBtn,
      changeMateType,
      drageHeader,
      getNameByid,
      getNamesByid,
      addCustomer,
      checkRow,
      cancellationRow,
      closeDialog,
      closeDetailDrawer,
      reset,
      handleOperate,
      formatDate,
      getTableList,
      tableKey,
      colWidth,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  }
};
</script>
<style lang="scss" scoped>
.searchInput {
  display: flex;
  .el-input {
    width: 360px;
    margin-right: 10px;
  }
}
</style>
