import request from '@/utils/request';

/**
 * 获取消息模板列表
 */
export function queryMessageTemplateList(data) {
  return request({
    url: '/api-message/message/messagetemplate/list',
    method: 'post',
    data
  });
}

/**
 * 根据ID获取消息模板详情
 */
export function queryMessageTemplateDetail() {
  return request({
    url: '/api-message/message/messagetemplate/info/{id}',
    method: 'get'
  });
}

/**
 * 保存消息模板
 * @param {string} data.id // 数据id，无id为新增，id有值为更新
 * @param {string} data.eventCode // 事件编码
 * @param {string} data.eventName // 事件名称
 * @param {string} data.templateType // 模版类型，1消息 2待办
 * @param {string} data.messageTitle // 消息标题
 * @param {string} data.messageContent // 消息内容
 * @param {string} data.messageDescription // 消息描述
 * @param {string} data.targetType // 接收对象类型，权限为0，具体某人为1
 * @param {string} data.targetObjectId // 接收对象类型为权限，存roleid，为某人，存目标人的表字段名
 * @param {string} data.messageGroupId // 消息分类ID
 * @param {string} data.messageGroupKey // 消息分类Key值
 * @param {string} data.forwardUri // 跳转页面URI
 */
export function saveMessageTemplate(data) {
  return request({
    url: '/api-message/message/messagetemplate/save',
    method: 'post',
    data
  });
}

/**
 * 获取消息分组列表
 */
export function queryMessageGroupList() {
  return request({
    url: '/api-message/message/messagegroup/listAll',
    method: 'get'
  });
}
