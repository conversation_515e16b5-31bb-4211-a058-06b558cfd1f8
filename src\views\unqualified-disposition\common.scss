.register {
  margin-bottom: 20px;
  text-align: left;
}
:deep(.el-alert__title) {
  color: #303133;
  font-size: 16px;
}
:deep(.el-alert) {
  padding: 16px 20px;
  margin-left: 10px;
}
:deep(.el-alert__content) {
  padding: 0;
}
:deep(.el-alert--info.is-light) {
  background-color: #f5f7fa;
}
:deep(.el-alert--error.is-light) {
  background: #fef0f0;
  border: 1px solid #fcd3d3;
  border-radius: 4px;
}
.info-main {
  margin: 0 24px 0 24px;
  background: $background-color;
  color: #303133;
  .el-alert {
    width: auto;
    margin: 0 13px 16px;
  }
}
.marginB0 {
  .el-form-item {
    margin-bottom: 0;
  }
}
.inlineBlock {
  display: inline-block;
}
.textR {
  text-align: right;
}
.qmrq {
  color: #606266;
  font-size: 14px;
  line-height: 30px;
  padding: 10px 0 20px;
  img {
    max-width: 100%;
  }
}
.titleHeader {
  height: 48px;
  line-height: 48px;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.12);
  background-color: $background-color;
  text-align: left;
  font-size: 18px;
  font-weight: 700;
  padding-left: 24px;
  padding-right: 24px;
  margin-bottom: 8px;
}
.register-title {
  line-height: 24px;
  font-size: 16px;
  text-align: left;
  font-weight: 400;
  position: relative;
  // padding-left: 12px;
  padding: 13px 0 12px 12px;
  display: flex;
  align-items: center;
  &:before {
    content: ' ';
    width: 4px;
    height: 16px;
    background: #00b38a;
    position: absolute;
    left: 0;
  }
}
:deep(.el-form .el-form-item .el-form-item__label) {
  font-size: 14px;
  color: #909399;
  line-height: 32px;
}
:deep(.el-input--medium) {
  line-height: 32px;
}
:deep(.el-input--medium .el-input__icon) {
  line-height: 32px;
}
:deep(
    .el-input-number.is-controls-right[class*='medium'] [class*='decrease'],
    .el-input-number.is-controls-right[class*='medium'] [class*='increase']
  ) {
  line-height: 16px;
}
:deep(.el-form-item__content) {
  color: #303133;
}
:deep(.el-divider--horizontal) {
  margin: 20px 0 12px;
}
:deep(.el-divider) {
  background-color: #dcdfe6;
}
:deep(.el-alert) {
  line-height: 22px;
}
:deep(
    .el-form-item--medium .el-form-item__content,
    .el-form-item--medium .el-form-item__label,
    .el-range-editor--medium
  ) {
  line-height: 32px;
}
:deep(.el-input--medium .el-input__inner) {
  height: 32px;
  line-height: 32px;
}
.isCheck {
  :deep(.el-form .el-form-item) {
    margin-bottom: 0;
  }
}
.required-info {
  display: inline-block;
  margin-left: 10px;
  color: $tes-font2;
  font-size: 12px;
}
