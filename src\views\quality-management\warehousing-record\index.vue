<template>
  <!-- 成品入库记录 -->
  <ListLayout :has-button-group="false" :has-quick-query="true">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="param">
          <div style="width: 42vw">
            <CombinationQuery
              :field-list="searchFieldList"
              :field-tip="fieldTips"
              @get-single-text="getSingleText"
              @get-param-list="getParamList"
              @reset-search="reset"
            />
          </div>
        </el-form-item>
        <el-form-item style="margin-left: 0px">
          <el-button size="large" type="text" class="searchBtn" @click="search" @keyup.prevent @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
          /></el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" :model="formInline" label-width="110px" label-position="right">
            <el-form-item label="交货单位：">
              <el-select
                v-model="formInline.deliveryInstitution"
                placeholder="请选择交货单位"
                size="small"
                filterable
                clearable
              >
                <el-option v-for="(val, key) in dictionary['JHDW'].enable" :key="key" :label="val" :value="key" />
              </el-select>
            </el-form-item>
            <el-form-item label="收货仓库：">
              <el-select
                v-model="formInline.warehouseNo"
                placeholder="请选择收货仓库"
                size="small"
                filterable
                clearable
              >
                <el-option v-for="(val, key) in dictionary['BYCK'].enable" :key="key" :label="val" :value="key" />
              </el-select>
            </el-form-item>
            <el-form-item label="打印员：">
              <el-select v-model="formInline.printUserId" clearable filterable size="small" placeholder="请选择打印员">
                <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="8" :offset="16" class="text-right">
          <TableColumnView binding-menu="FinishWarehousingRecord" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="tableList"
      size="medium"
      fit
      border
      height="auto"
      class="dark-table test-item-table base-table format-height-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      @header-dragend="drageHeader"
    >
      <el-table-column type="expand">
        <template #default="{ row }">
          <el-row>
            <el-col :span="20" :offset="2">
              <VirtualTable
                :data="row.detailEntityList"
                :columns="columns"
                :row-height="50"
                :buffer-size="10"
                size="small"
                border
                hover
                row-key="id"
              >
                <!-- <template #status="{ rowChild }">
                  <span :style="{ color: row.status === 'active' ? 'green' : 'red' }">
                    {{ row.status }}
                  </span>
                </template> -->
              </VirtualTable>
            </el-col>
          </el-row>
        </template>
      </el-table-column>
      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="item.fieldType === fieldTypesEnum.Person">
              <template v-if="row[item.fieldKey]">
                <UserTag
                  v-for="ownerId in row[item.fieldKey].split(',')"
                  :key="ownerId"
                  :name="getNameByid(ownerId) || ownerId || '--'"
                />
              </template>
              <span v-else>--</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Status">
              <el-tag
                v-if="row[item.fieldKey] !== ''"
                size="small"
                effect="dark"
                :type="statusJson[item.fieldKey][row[item.fieldKey]]?.type"
                >{{ statusJson[item.fieldKey][row[item.fieldKey]]?.label }}</el-tag
              >
              <span v-else>--</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Date">
              <span>{{ row[item.fieldKey] == '' ? '--' : formatDate(row[item.fieldKey]) }}</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Custom">
              <div v-if="item.fieldKey == 'deliveryInstitution'">
                {{ dictionary['JHDW'].all[row[item.fieldKey]] || row[item.fieldKey] || '--' }}
              </div>
              <div v-else-if="item.fieldKey == 'warehouseNo'">
                {{ dictionary['BYCK'].all[row[item.fieldKey]] || row[item.fieldKey] || '--' }}
              </div>
              <div v-else>{{ row[item.fieldKey] }}</div>
            </template>
            <span v-else>{{ row[item.fieldKey] || '--' }}</span>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" fixed="right" :min-width="colWidth.operationSingle">
        <template #default="{ row }">
          <span class="blue-color" @click.stop="handleDownLoad(row)">导出</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, onMounted } from 'vue';
// import router from '@/router/index.js';
import { ElMessage } from 'element-plus';
import Pagination from '@/components/Pagination';
import ListLayout from '@/components/ListLayout';
import { formatDate } from '@/utils/formatTime';
import UserTag from '@/components/UserTag';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import CombinationQuery from '@/components/CombinationQuery';
import TableColumnView from '@/components/TableColumnView';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';
import { certificatewarehousereceiptList, downloadFile } from '@/api/warehousing-record';
import VirtualTable from '@/components/VirtualTable';
import { getDictionary } from '@/api/user';
import { useStore } from 'vuex';
export default {
  name: 'RawCertificate',
  components: {
    Pagination,
    ListLayout,
    UserTag,
    CombinationQuery,
    VirtualTable,
    TableColumnView
  },
  setup() {
    const store = useStore().state;
    const state = reactive({
      tableRef: ref(),
      fieldTips: '',
      showS: false,
      activeName: '0',
      dialogVisible: false,
      userOptions: store.common.nameList,
      searchFieldList: [],
      tableChild: [],
      listLoading: false,
      listQuery: {
        limit: 20,
        page: 1
      },
      dictionary: {
        JHDW: {
          enable: {},
          all: {}
        },
        BYCK: {
          enable: {},
          all: {}
        }
      },
      formInline: {
        param: '',
        tableQueryParamList: []
      },
      tableColumns: [],
      columns: [
        { key: 'materialNo', title: '编号', isMinWidth: 1, width: 260 },
        { key: 'prodType', title: '型号规格', isMinWidth: 1, width: 260 },
        { key: 'inputWarehouseUnit', title: '单位', isMinWidth: 1, width: 120 },
        { key: 'customerQuantity', title: '数量', isMinWidth: 1, width: 120 },
        { key: 'batchNo', title: '批号', isMinWidth: 1, width: 120 },
        { key: 'batchNoProperty', title: '批号属性', isMinWidth: 1, width: 240 },
        { key: 'remark', title: '备注', isMinWidth: 0, width: 300 }
      ],
      tableList: [],
      total: 0
    });
    const tableKey = ref(0);
    const tableKeyLeft = ref(0);
    const getList = query => {
      const params = { ...state.formInline };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoading = true;
      certificatewarehousereceiptList(params).then(res => {
        state.listLoading = false;
        if (res) {
          state.total = res.data.data.totalCount;
          state.tableList = res.data.data.list;
          state.listQuery.page = Number(params.page);
        }
      });
    };
    // 打开 高级搜索
    const search = () => {
      state.showS = !state.showS;
      if (state.activeName === '0') {
        state.activeName = '1';
      } else {
        state.activeName = '0';
      }
    };
    const handleSizeChange = val => {
      state.listQuery.limit = val;
      getList();
    };
    const reset = () => {
      state.formInline = {
        param: '',
        tableQueryParamList: []
      };
      getList();
    };

    const getParamList = paramList => {
      state.formInline.tableQueryParamList = paramList;
      state.formInline.param = '';
      getList();
    };
    const getSingleText = val => {
      state.formInline.param = val;
      state.formInline.tableQueryParamList = [];
      getList();
    };
    const onUpdateColumns = columns => {
      tableKey.value = tableKey.value + 1;
      state.tableColumns = columns;
      state.searchFieldList = columns.filter(item => {
        return item.isQuery == 1;
      });
      state.fieldTips = state.searchFieldList.map(item => item.fieldName).join('/');
    };
    const getDictionaryList = () => {
      Object.keys(state.dictionary).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          state.dictionary[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              state.dictionary[item].enable[optionItem.code] = optionItem.name;
            }
            state.dictionary[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    const handleDownLoad = async row => {
      state.listLoading = true;
      const { data } = await downloadFile(row.id).finally((state.listLoading = false));
      if (data) {
        window.open(data.data);
        ElMessage.success('导出成功!');
      }
    };
    onMounted(() => {
      getList();
      getDictionaryList();
    });
    return {
      ...toRefs(state),
      getSingleText,
      handleDownLoad,
      search,
      getParamList,
      getPermissionBtn,
      drageHeader,
      getNameByid,
      handleSizeChange,
      getNamesByid,
      reset,
      formatDate,
      getList,
      tableKey,
      tableKeyLeft,
      colWidth,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  }
};
</script>
<style lang="scss">
.btn-mg20 {
  margin-right: 20px;
}

.margin-right {
  margin-right: 4px;
}
.fr {
  float: right;
}

:deep(.el-radio.is-bordered + .el-radio.is-bordered) {
  margin-left: 0;
}
.child-table.el-table .el-table__body-wrapper {
  max-height: 200px !important;
}
</style>
