<template>
  <el-tag :size="size" class="tag-user">
    <span class="icon" :class="iconName" />
    <span class="name ellipsis" :style="{ 'max-width': width }">
      {{ name }}
    </span>
  </el-tag>
</template>

<script>
export default {
  name: 'UserTag',
  props: {
    size: {
      type: String,
      default: 'small'
    },
    name: {
      type: String,
      default: ''
    },
    iconName: {
      type: String,
      default: 'el-icon-user-solid'
    },
    width: {
      type: String,
      default: '72px'
    }
  }
};
</script>

<style lang="scss" scoped>
.tag-user {
  border: none;
  display: inline-flex;
  align-items: center;
  background: $user-tag-backGroundColor;
  margin-right: 4px;
  .icon {
    color: #c0c4cc;
    padding-right: 4px;
  }
  .name {
    color: $user-tag-color;
  }
}
</style>
