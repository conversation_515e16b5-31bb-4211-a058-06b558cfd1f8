<template>
  <div class="review-info">
    <div class="review-info-header">
      <el-row>
        <el-col :span="12"><div class="title">技术评审</div></el-col>
        <el-col :span="12">
          <div class="review-button">
            <!-- <el-button
              class="review-btn"
              type="primary"
              size="small"
              icon="el-icon-plus"
              @click="addReview"
              @keyup.prevent
              @keydown.enter.prevent
            >历史记录</el-button> -->
            <el-button
              v-if="currentTaskStatus === 1 && getPermissionBtn('TaskReviewBtn')"
              class="review-btn"
              type="primary"
              size="small"
              icon="el-icon-plus"
              @click="addReview()"
              @keyup.prevent
              @keydown.enter.prevent
              >合同评审</el-button
            >
            <el-button
              v-if="currentTaskStatus === 2 && getPermissionBtn('TaskReviewBtn')"
              class="review-btn"
              type="primary"
              size="small"
              icon="el-icon-plus"
              @click="addReview()"
              @keyup.prevent
              @keydown.enter.prevent
              >委托确认</el-button
            >
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="content">
      <el-steps
        :key="stepKey"
        direction="vertical"
        :space="100"
        :active="reviewListData.length - 1"
        style="margin: 0 15px"
      >
        <el-step v-for="item in reviewListData" :key="item">
          <template #title>
            <span class="name-title">{{ item.nodeName === '提交流程' ? '委托提交' : item.nodeName }}</span>
            <span class="time-title">{{ formatDateTime(item.endTime) }}</span>
          </template>
          <template #description>
            <!-- <div v-if="item.updateTime">
              <el-descriptions title="">
                <el-descriptions-item label="合同评审">{{ getNameByid(item.approveBy) }}</el-descriptions-item>
                <el-descriptions-item label="人员能力">{{ item.staffAbility ? '满足' : '不满足' }}</el-descriptions-item>
                <el-descriptions-item label="仪器设备">{{ item.devAbility ? '满足' : '不满足' }}</el-descriptions-item>
                <el-descriptions-item label="环境设备">
                  <el-tag size="small">{{ item.envAbility ? '满足' : '不满足' }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="评审意见">{{ item.approveDetail }}</el-descriptions-item>
              </el-descriptions>
            </div> -->
            <div v-if="item.endTime" class="step-desc">
              <el-form :model="item" label-width="110px">
                <el-row v-if="item.nodeName === '提交流程' || item.nodeName === '重新提交'" :gutter="20">
                  <el-col :span="6">
                    <el-form-item label="提交人：">
                      <div>{{ getNameByid(item.approveBy) }}</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="18" />
                </el-row>
                <el-row v-else :gutter="20">
                  <el-col :span="6">
                    <el-form-item label="合同评审：">
                      <div>{{ getNameByid(item.approveBy) }}</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="人员能力：">
                      <div>
                        <el-tag size="small" :type="Number(item.staffAbility) === 1 ? 'success' : 'warning'">
                          {{ Number(item.staffAbility) === 1 ? '满足' : '不满足' }}
                        </el-tag>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="仪器设备：">
                      <el-tag size="small" :type="Number(item.devAbility) === 1 ? 'success' : 'warning'">
                        {{ Number(item.devAbility) === 1 ? '满足' : '不满足' }}
                      </el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="环境设备：">
                      <el-tag size="small" :type="Number(item.envAbility) === 1 ? 'success' : 'warning'">
                        {{ Number(item.envAbility) === 1 ? '满足' : '不满足' }}
                      </el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="审批结果：">
                      <el-tag size="small" :type="Number(item.approveResult) === 1 ? 'success' : 'danger'">
                        {{ Number(item.approveResult) === 1 ? '通过' : '不通过' }}
                      </el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="18" />
                  <el-col :span="24">
                    <el-form-item label="评审意见：">
                      <div>{{ item.approveDetail }}</div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            <div v-else class="step-desc">
              <div class="name">
                {{ getNameByid(item.approveBy) || item.approveBy }}
              </div>
            </div>
          </template>
        </el-step>
      </el-steps>
      <el-empty v-if="!reviewListData || reviewListData.length === 0" :image="emptyImg" description="暂无数据" />
    </div>
    <div class="other">
      <el-dialog v-model="showFeeDialog" :title="feeTitle" width="40%">
        <template #default>
          <el-form
            ref="formRef"
            :model="currentFeeInfo"
            class="formDataSample"
            label-position="right"
            label-width="110px"
          >
            <el-row :gutter="40">
              <el-col :span="12">
                <el-form-item label="人员能力：" prop="staffAbility">
                  <el-radio-group v-model="currentFeeInfo.staffAbility">
                    <el-radio :label="1" size="large">满足</el-radio>
                    <el-radio :label="0" size="large">不满足</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="仪器设备：" prop="devAbility">
                  <el-radio-group v-model="currentFeeInfo.devAbility">
                    <el-radio :label="1" size="large">满足</el-radio>
                    <el-radio :label="0" size="large">不满足</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="环境设备：" prop="envAbility">
                  <el-radio-group v-model="currentFeeInfo.envAbility">
                    <el-radio :label="1" size="large">满足</el-radio>
                    <el-radio :label="0" size="large">不满足</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12" />
              <el-col :span="24">
                <el-form-item label="评审结论：" prop="approveResult">
                  <el-radio-group
                    id="review-result"
                    v-model="currentFeeInfo.approveResult"
                    text-color="#ffffff"
                    result-pass
                  >
                    <div id="flex-group">
                      <div class="flex-content">
                        <el-radio id="review-pass" :label="1" size="large" border>通过</el-radio>
                      </div>
                      <div id="flex-space" />
                      <div class="flex-content">
                        <el-radio id="review-refuse" :label="0" border size="large">不通过</el-radio>
                      </div>
                    </div>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="评审意见：" prop="approveDetail">
                  <el-input
                    v-if="!isCheck"
                    v-model="currentFeeInfo.approveDetail"
                    type="textarea"
                    :rows="2"
                    size="small"
                  />
                  <span v-else>
                    {{ currentFeeInfo.approveDetail || '--' }}
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="showFeeDialog = false">取消</el-button>
            <el-button type="primary" :loading="submitFeeLoading" @click="submitFeeData"> 确认 </el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch, computed } from 'vue';
// import { useStore } from 'vuex'
import { getLoginInfo } from '@/utils/auth';
// import router from '@/router/index.js'
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getTechnicalReviewList, saveTechnicalReviewInfo } from '@/api/task-registration';
import { formatDate, formatDateTime } from '@/utils/formatTime';
// import { getReviewUnitDict } from '@/api/login'
import emptyImg from '@/assets/img/empty-table.png';

export default {
  name: 'PanelReviewInfo',
  components: {},
  props: {
    taskId: {
      type: String,
      default: ''
    },
    processId: {
      type: String,
      default: ''
    },
    showDetail: {
      type: Boolean,
      default: false
    },
    reviewInfo: {
      type: Array,
      default: function () {
        return [];
      }
    },
    taskStatus: {
      type: Number,
      default: 0
    }
  },
  emits: ['setInfo'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    // console.log(proxy)
    // const store = useStore().state
    const datas = reactive({
      tableReviewKey: 'tableReviewKey',
      reviewListData: props.reviewInfo,
      addReviewTitle: '新增附件',
      showFeeDialog: false,
      feeTitle: '技术评审',
      formRef: ref(),
      isCheck: false,
      currentFeeInfo: {
        approveType: 0,
        approveResult: 1,
        devAbility: 1,
        envAbility: 1,
        staffAbility: 1,
        approveDetail: ''
      },
      costTypeList: [],
      submitFeeLoading: false,
      stepKey: 0
    });

    watch(
      () => props.taskId,
      newValue => {
        // if (newValue) {
        // }
      },
      { deep: true }
    );

    watch(
      () => props.reviewInfo,
      newValue => {
        if (newValue) {
          datas.reviewListData = props.reviewInfo;
          datas.stepKey += 1;
        }
      },
      { deep: true }
    );

    // 添加附件-打开新增附件弹出框
    const addReview = () => {
      datas.feeTitle = '技术评审';
      datas.feeInfoDetail = {};
      datas.showFeeDialog = true;
      // context.emit('setInfo', datas.reviewListData)
    };

    const reviewTask = () => {};

    // 删除附件
    const deleteReview = row => {
      console.log(row);
      ElMessageBox({
        title: '提交',
        message: '是否确认删除该附件？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          getTechnicalReviewList(row.id).then(res => {
            if (res !== false) {
              console.log(res);
              ElMessage.success('删除成功');
              context.emit('setInfo', 'delete');
            }
          });
        })
        .catch(() => {});
    };

    const submitFeeData = () => {
      // 提交费用信息
      datas.formRef.validate(valid => {
        if (valid) {
          datas.currentFeeInfo.superId = props.taskId;
          datas.currentFeeInfo.approveType = props.taskStatus - 1;
          datas.currentFeeInfo.approveBy = getLoginInfo().accountId;
          datas.submitFeeLoading = true;
          const resultParams = {
            devAbility: `${datas.currentFeeInfo.devAbility}`,
            envAbility: `${datas.currentFeeInfo.envAbility}`,
            staffAbility: `${datas.currentFeeInfo.staffAbility}`,
            isAssent: `${datas.currentFeeInfo.approveResult}`,
            opinion: datas.currentFeeInfo.approveDetail
          };
          const executeParams = {
            businessKey: props.taskId,
            processInstanceId: props.processId,
            processParameter: resultParams
          };
          saveTechnicalReviewInfo(executeParams).then(function (res) {
            datas.submitFeeLoading = false;
            if (res !== false) {
              context.emit('setInfo', datas.currentFeeInfo);
              ElMessage.success('评审成功');
              datas.currentFeeInfo.approveType = 0;
              datas.currentFeeInfo.approveResult = 1;
              datas.currentFeeInfo.devAbility = 1;
              datas.currentFeeInfo.envAbility = 1;
              datas.currentFeeInfo.approveDetail = '';
              datas.showFeeDialog = false;
            }
          });
        } else {
          return false;
        }
      });
    };

    const currentTaskStatus = computed({
      get: () => props.taskStatus
    });

    return {
      ...toRefs(datas),
      emptyImg,
      currentTaskStatus,
      getNameByid,
      formatDate,
      formatDateTime,
      addReview,
      drageHeader,
      deleteReview,
      reviewTask,
      getPermissionBtn,
      submitFeeData
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.review-info {
  .review-info-header {
    margin: 15px 0 15px 0;
    text-align: left;
    .title {
      font-size: 16px;
      font-weight: bold;
      height: 32px;
      line-height: 32px;
      text-align: left;
      display: inline-block;
      margin-right: 16px;
    }
    .review-btn {
      margin-left: 10px;
    }
    .review-button {
      text-align: right;
    }
    :deep(.el-button) {
      float: right;
    }
  }
  .content {
    background: $background-color;
    text-align: left;
    position: relative;
    .review-info-table {
      margin-bottom: 15px;
    }
  }
}

.step-desc {
  .name {
    color: #999999;
    span {
      margin-left: 5px;
    }
  }
  .desc {
    color: #999999;
    background: #f6f6f6;
    border-radius: 3px;
    padding: 5px 10px;
    max-width: 520px;
  }
  .el-textarea {
    width: 100%;
    margin-top: 10px;
  }
  .approva-radio {
    font-size: 14px;
    margin-top: 10px;
    .title {
      float: left;
      margin-right: 10px;
    }
  }
  .next-user {
    margin-top: 10px;
    height: 50px;
  }
  .send-date {
    margin: 10px 0px;
  }
}

.content {
  :deep(.el-step__description) {
    padding-right: 0%;
  }
}
.el-steps {
  .name-title {
    color: #303339;
    font-size: 14px;
    font-style: normal;
    font-weight: normal;
  }
  .time-title {
    color: #999999;
    font-size: 12px;
    margin: 0 10px;
    font-weight: 500;
  }
}

#review-result {
  width: 100%;
  #review-pass,
  #review-refuse {
    padding: 0px;
    height: 2.5rem;
    line-height: 2.5rem;
    font-weight: 540;
    width: 100%;
    text-align: center;
    :deep(.el-radio__input) {
      display: none !important;
    }
  }

  #review-refuse.is-checked,
  #review-pass.is-checked {
    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #fff;
    }
  }

  #review-refuse.is-checked {
    background-color: $tes-red;
    border-color: $tes-red !important;
    box-shadow: $tes-red -1px 0px 0px 0px;
  }

  #review-pass.is-checked {
    background-color: $green;
    border-color: $green !important;
    box-shadow: $green -1px 0px 0px 0px;
  }
  #flex-group {
    display: flex;
    flex-direction: row;
    #flex-space {
      width: 15px;
    }
    .flex-content {
      flex: auto;
    }
  }
}
</style>
