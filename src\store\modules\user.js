import { getMenu, login, logout, getUserInfo, getSysMenu } from '@/api/login';
import { getDictionary } from '@/api/user';
import { getCapabilityTree } from '@/api/user';
import { materialCategoryList } from '@/api/material';
import { saveSysLoginLog } from '@/api/login-log';
import {
  initToken,
  getToken,
  setToken,
  removeToken,
  getRefreshToken,
  setRefreshToken,
  removeRefreshToken,
  getLoginId,
  setLoginId,
  setLoginInfo,
  removeLoginInfo,
  removeMenuList,
  setMenuList,
  removePermissionRouterList,
  setSysConfig,
  setTenantConfig
} from '@/utils/auth';
import router from '@/router';
import store from '@/store';
import _ from 'lodash';
import { ElLoading, ElMessage } from 'element-plus';
import { Encrypt } from '@/utils/crypto';
import { loginOutApi } from '@/api/sysConfig';
import { bindNewPlatform } from '@/api/platform';

const state = {
  token: getToken(),
  name: '',
  accountId: '',
  loginMobile: '',
  avatar: '',
  introduction: '',
  roles: [],
  menus: [],
  area: [],
  materialList: [],
  unit: [],
  sampleUnit: [],
  tenantGroup: [],
  tenantInfo: {}
};

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token;
  },
  SET_REFTOKEN: (state, reftoken) => {
    state.reftoken = reftoken;
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction;
  },
  SET_NAME: (state, name) => {
    state.name = name;
  },
  SET_ACCOUNTID: (state, accountId) => {
    state.accountId = accountId;
  },
  SET_MOBILE: (state, loginMobile) => {
    state.loginMobile = loginMobile;
  },
  SET_MENUS: (state, menus) => {
    state.menus = menus;
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar;
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles;
  },
  SET_AREA: (state, area) => {
    state.area = area;
  },
  SET_MaterialList: (state, materialList) => {
    state.materialList = materialList;
  },
  SET_UNIT: (state, unit) => {
    state.unit = unit;
  },
  SET_SAMPLE_UNIT: (state, unit) => {
    state.sampleUnit = unit;
  },
  SET_TENANT_GROUP: (state, tenantGroup) => {
    state.tenantGroup = tenantGroup;
  },
  SET_TENANT_INFO: (state, tenantInfo) => {
    state.tenantInfo = tenantInfo;
  }
};

const actions = {
  // user login
  login({ commit }, userInfo) {
    const loading = ElLoading.service({
      lock: true,
      text: '登录中...',
      background: 'rgba(255, 255, 255, 0.5)'
    });
    var params = {};
    if (userInfo.unionId) {
      params = userInfo;
      params.grant_type = 'openId';
    } else if (userInfo.grant_type == 'bayi') {
      params = userInfo;
    } else {
      params = userInfo;
      params.grant_type = 'password';
      if (window.location.pathname !== '/uniappTemplate') {
        params.password = Encrypt(params.password);
      }
    }
    commit('SET_TOKEN', initToken);
    setToken(initToken);
    return new Promise((resolve, reject) => {
      login(params)
        .then(async response => {
          if (response) {
            const { data } = response.data;
            const thistoken = data.token_type + ' ' + data.access_token;
            // var strings = data.access_token.split('.')
            // var userInfo = JSON.parse(decodeURIComponent(escape(window.atob(strings[1].replace(/-/g, '+').replace(/_/g, '/')))))
            setRefreshToken(data.refresh_token);
            commit('SET_REFTOKEN', data.refresh_token);
            commit('SET_TENANT_INFO', { type: data.tenantType });
            commit('SET_TOKEN', thistoken);
            setToken(thistoken);
            getUserInfo().then(res => {
              if (res) {
                commit('SET_TENANT_GROUP', res.data.data.oauthClientDetails);
                localStorage.setItem('tenantGroup', JSON.stringify(res.data.data.oauthClientDetails));
                setLoginInfo({
                  username: res.data.data.username,
                  nickname: res.data.data.nickname,
                  accountId: res.data.data.id,
                  tenantId: res.data.data.currenttenantId
                });
                commit('SET_ACCOUNTID', res.data.accountId);
                setTenantConfig(res.data.data.tenantConfig);
                setSysConfig(res.data.data.sysConfig);
                getSysMenu().then(resp => {
                  commit('SET_MENUS', resp.data.data);
                  // localStorage.setItem('menuList', JSON.stringify(resp.data.data))
                  loading.setText('获取用户信息中...');
                  // store.dispatch('common/setNameList')
                  setMenuList(resp.data.data);
                  store.dispatch('permission/getcheckPermissionBtn');
                  store.dispatch('permission/getLIMSConfig');
                  const platformParams = sessionStorage.getItem('platformParams');
                  if (platformParams) {
                    // const encodeParams = encodeURIComponent(platformParams)
                    const bindParams = {
                      bindPassword: params.password,
                      bindUserId: data.id,
                      bindUsername: params.username,
                      ciphertext: platformParams
                    };
                    sessionStorage.removeItem('platformParams');
                    bindNewPlatform(bindParams).then(res => {
                      if (res) {
                        ElMessage.success('绑定成功!');
                      }
                    });
                  }
                  resolve(true);
                  // resolve(res.data.menuList)
                });
              } else {
                logout();
              }
              loading.close();
            });
            // resolve(true)
            // setReToken(data.refresh_token)
            // var TenantId = userInfo.access_tenant_id[0]
            // var TenantId =  getLoginInfo().[0].id
            // 不良品租户id
            // if (import.meta.env.MODE === 'blp') {
            //   TenantId = '66610001'
            // }
          } else {
            logout();
            resolve(false);
          }
          loading.close();
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  platformLogin({ commit }, userTokenInfo) {
    const loading = ElLoading.service({
      lock: true,
      text: '登录中...',
      background: 'rgba(255, 255, 255, 0.5)'
    });
    return new Promise((resolve, reject) => {
      const data = userTokenInfo;
      const thistoken = data.token_type + ' ' + data.access_token;
      localStorage.setItem('refresh_token', JSON.stringify(data.refresh_token));
      commit('SET_REFTOKEN', data.refresh_token);
      commit('SET_TENANT_INFO', { type: data.tenantType });
      commit('SET_TOKEN', thistoken);
      setToken(thistoken);
      getUserInfo().then(res => {
        if (res) {
          commit('SET_TENANT_GROUP', res.data.data.oauthClientDetails);
          localStorage.setItem('tenantGroup', JSON.stringify(res.data.data.oauthClientDetails));
          setLoginInfo({
            username: res.data.data.username,
            nickname: res.data.data.nickname,
            accountId: res.data.data.id,
            tenantId: res.data.data.currenttenantId
          });
          commit('SET_ACCOUNTID', res.data.accountId);
          setTenantConfig(res.data.data.tenantConfig);
          setSysConfig(res.data.data.sysConfig);
          getSysMenu().then(resp => {
            commit('SET_MENUS', resp.data.data);
            loading.setText('获取用户信息中...');
            setMenuList(resp.data.data);
            store.dispatch('permission/getcheckPermissionBtn');
            store.dispatch('permission/getLIMSConfig');
            resolve(true);
          });
        } else {
          logout();
        }
        loading.close();
      });
      loading.close();
    });
  },
  // lims直接跳转到TES
  loginLimsToTes({ commit }, token) {
    return new Promise((resolve, reject) => {
      commit('SET_TOKEN', token);
      const thistoken = 'Bearer' + ' ' + token;
      setToken(thistoken);
      var strings = token.split('.');
      var userInfo = JSON.parse(
        decodeURIComponent(escape(window.atob(strings[1].replace(/-/g, '+').replace(/_/g, '/'))))
      );
      // console.log(userInfo)
      setLoginInfo({ username: userInfo.account_name, accountId: userInfo.account_id, tenantId: userInfo.tenant_id });
      getMenu()
        .then(res => {
          commit('SET_MENUS', res.data.menuList);
          commit('SET_ACCOUNTID', res.data.accountId);
          localStorage.setItem('menuList', JSON.stringify(res.data.menuList));
          setMenuList(res.data.menuList);
          resolve(res.data.menuList);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  // 租户切换
  changeTenant({ commit }, tenantId) {
    const loading = ElLoading.service({
      lock: true,
      text: '切换租户中...',
      background: 'rgba(255, 255, 255, 0.5)'
    });
    return new Promise((resolve, reject) => {
      const loginId = getLoginId();
      loginOutApi(loginId)
        .then(async resLogin => {
          if (resLogin) {
            const reftoken = getRefreshToken();
            commit('SET_TOKEN', initToken);
            setToken(initToken);
            setLoginId('');
            login({ grant_type: 'refresh_token', refresh_token: reftoken, tenant_id: tenantId })
              .then(async response => {
                if (response) {
                  const { data } = response.data;
                  const thistoken = data.token_type + ' ' + data.access_token;
                  commit('SET_TOKEN', thistoken);
                  setToken(thistoken);
                  getUserInfo().then(res => {
                    commit('SET_TENANT_GROUP', res.data.data.oauthClientDetails);
                    commit('SET_TENANT_INFO', { type: data.tenantType });
                    setLoginInfo({
                      username: res.data.data.username,
                      nickname: res.data.data.nickname,
                      accountId: res.data.data.id,
                      tenantId: res.data.data.currenttenantId
                    });
                    commit('SET_ACCOUNTID', res.data.accountId);
                    setSysConfig(res.data.data.sysConfig);
                    setTenantConfig(res.data.data.tenantConfig);
                    const currentIP = localStorage.getItem('currentIp');
                    const param = {
                      ie: navigator.userAgent,
                      address: currentIP
                    };
                    saveSysLoginLog(param).then(res => {
                      setLoginId(res.data.data);
                    });
                    getSysMenu().then(resp => {
                      commit('SET_MENUS', resp.data.data);
                      localStorage.setItem('menuList', JSON.stringify(resp.data.data));
                      loading.setText('获取用户信息中...');
                      // store.dispatch('common/setNameList')
                      setMenuList(resp.data.data);
                      store.dispatch('permission/getcheckPermissionBtn');
                      store.dispatch('permission/getLIMSConfig');
                      resolve(true);
                    });
                  });
                } else {
                  logout();
                  resolve(false);
                }
                loading.close();
              })
              .catch(error => {
                reject(error);
              });
          } else {
            logout();
            resolve(false);
          }
        })
        .catch(error => {
          reject(error);
        });
      // TODO: lims权限登录，后面用不到就可以删除下面代码
      // login({ username: username, password: password }).then(response => {
      //   const { data } = response
      //   console.log('data')
      //   console.log(data)
      //   console.log(data.access_token)
      //   commit('SET_TOKEN', data.access_token)
      //   const thistoken = data.token_type + ' ' + data.access_token
      //   setToken(thistoken)
      //   loginAgain({ 'refresh_token': data.refresh_token, 'TenantId': tenantId }).then(res => {
      //     const { data } = res
      //     commit('SET_TOKEN', data.access_token)
      //     const thistoken = data.token_type + ' ' + data.access_token
      //     setToken(thistoken)
      //     // setReToken(data.refresh_token)
      //     //       commit('SET_TENANT_GROUP', data.items)
      //     loading.setText('重新获取菜单中...')
      //     getMenu().then(res => {
      //       commit('SET_MENUS', res.data.menuList)
      //       // console.log(res.data.menuList)
      //       commit('SET_ACCOUNTID', res.data.accountId)
      //       setLoginInfo({ 'username': username, 'password': password, 'accountId': res.data.accountId, 'tenantId': tenantId })
      //       // console.log({ 'username': username, 'password': password, 'accountId': res.data.accountId, 'tenantId': tenantId })
      //       localStorage.setItem('menuList', JSON.stringify(res.data.menuList))
      //       loading.setText('重新获取用户信息中...')
      //       store.dispatch('common/setNameList')
      //       setMenuList(res.data.menuList)
      //       store.dispatch('permission/getcheckPermissionBtn')
      //       router.push('/home')
      //       setTimeout(function() { window.location.reload() }, 300)
      //       resolve(res.data.menuList)
      //     })
      //   })
      // }).catch(error => {
      //   reject(error)
      // })
    });
  },
  clearAllStorage({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      commit('SET_TOKEN', '');
      commit('SET_ROLES', []);
      removeToken();
      removeRefreshToken();
      removeLoginInfo();
      removeMenuList();
      removePermissionRouterList();
      dispatch('tagsView/delAllViews', null, { root: true });
      resolve();
    });
  },
  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout()
        .then(() => {
          commit('SET_TOKEN', '');
          commit('SET_ROLES', []);
          removeToken();
          removeLoginInfo();
          removeMenuList();
          removePermissionRouterList();
          dispatch('tagsView/delAllViews', null, { root: true });
          router.push('/login');
          resolve();
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  // 检测项目树
  getCapabilityTree({ commit }) {
    return new Promise((resolve, reject) => {
      getCapabilityTree(0)
        .then(response => {
          if (response !== false) {
            const { data } = response;
            console.log(data);
            resolve(response.data.data);
          } else {
            resolve([]);
          }
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  // 获取16类物资
  getMaterialCategoryList({ commit }) {
    return new Promise((resolve, reject) => {
      materialCategoryList('')
        .then(response => {
          if (response !== false) {
            const { data } = response;
            // console.log(data.data)
            const materialListByP = data.data;
            const tabsData = [];
            if (materialListByP.length > 0) {
              materialListByP.forEach(m => {
                if (m.parentId === '0') {
                  tabsData.push(m);
                }
              });
            }
            const lastList = _.orderBy(tabsData, ['order']);
            commit('SET_MaterialList', lastList);
            resolve(lastList);
          } else {
            commit('SET_MaterialList', []);
            resolve([]);
          }
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  // 获取检测项目关键参数的单位
  getUnit({ commit }) {
    return new Promise((resolve, reject) => {
      getDictionary(13)
        .then(response => {
          if (response !== false) {
            const { data } = response;
            commit('SET_UNIT', data.data.dictionaryoption);
            resolve(data.data.dictionaryoption);
          } else {
            resolve([]);
          }
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  // 获取样品单位列表
  getSampleUnit({ commit }) {
    return new Promise((resolve, reject) => {
      getDictionary(5)
        .then(response => {
          if (response !== false) {
            const { data } = response;
            commit('SET_SAMPLE_UNIT', data.data.dictionaryoption);
            resolve(data.data.dictionaryoption);
          } else {
            resolve([]);
          }
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  // 获取租户列表 getTenantAccess
  // getTenantAccess({ commit }) {
  //   return new Promise((resolve, reject) => {
  //     getTenantAccess().then(res => {
  //       const { data } = res
  //       console.log('data.items')
  //       console.log(data.items)
  //       commit('SET_TENANT_GROUP', data.items)
  //       resolve(data.items)
  //     }).catch(error => {
  //       reject(error)
  //     })
  //   })
  // },
  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '');
      commit('SET_MENUS', []);
      removeToken();
      removeLoginInfo();
      removeMenuList();
      router.push(`/login`);
      resolve();
    });
  },
  resetCurrentToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '');
      commit('SET_MENUS', []);
      removeToken();
      removeLoginInfo();
      removeMenuList();
      resolve();
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
