<template>
  <div v-if="item?.meta?.visible">
    <el-sub-menu
      v-if="item.children?.length > 0 && !item.meta.showOneMenu"
      :index="resolvePath(item.path)"
      popper-append-to-body
      :class="{ 'icon-pl': !sidebar.opened }"
    >
      <template #title>
        <i
          :class="{
            [item.meta.icon]: true,
            'sub-el-icon': item.meta.icon,
            iconfont: !item.meta.icon,
            'ml-0': true,
            'mr-2': true
          }"
        />
        <span>{{ item.meta.title }}</span>
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-sub-menu>
    <template v-else>
      <el-menu-item
        v-if="item.name.startsWith('dynamic-')"
        :index="item.name"
        :class="{ 'submenu-title-noDropdown': !isNest }"
        @click="handleButtonPath(item.name)"
      >
        <template #title>
          <span class="ml-4">{{ item.meta.title }}</span>
        </template>
      </el-menu-item>
      <app-link v-else :to="resolvePath(item.path)" class="show-title">
        <el-menu-item :index="resolvePath(item.path)" :class="{ 'submenu-title-noDropdown': !isNest }">
          <i
            :class="{
              [item.meta.icon]: true,
              'sub-el-icon': item.meta.icon,
              iconfont: !item.meta.icon,
              'ml-0': true,
              'mr-2': true
            }"
          />
          <template #title>{{ item.meta.title }}</template>
        </el-menu-item>
      </app-link>
    </template>
  </div>
</template>

<script>
import { isExternal } from '@/utils/validate';
import AppLink from './AppLink';
import FixiOSBug from './FixiOSBug';
import { mapGetters } from 'vuex';
import { getPlatformUrlParams } from '@/api/platform';
import { ElMessage } from 'element-plus';
import qs from 'qs';

export default {
  name: 'SidebarItem',
  components: { AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapGetters(['sidebar'])
  },
  methods: {
    handleButtonPath(path) {
      const info = path.substring(8);
      const infoObj = qs.parse(info);
      if (infoObj?.platformId) {
        getPlatformUrlParams(infoObj.platformId, infoObj.targetPath || '').then(res => {
          if (res) {
            const { url, ciphertext } = res.data.data;
            if (url && ciphertext) {
              const params = encodeURIComponent(ciphertext);
              const fullUrl =
                url.substring(url.length - 1) === '/'
                  ? `${url.substring(0, url.length - 1)}?ciphertext=${params}`
                  : `${url}?ciphertext=${params}`;
              window.open(fullUrl, '_blank');
            }
          } else {
            ElMessage.warning('获取平台跳转参数失败!');
          }
        });
      }
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath;
      }
      if (isExternal(this.basePath)) {
        return this.basePath;
      }
      if (routePath === '/') {
        return '/home';
      }
      return routePath?.length > 0 ? routePath : this.basePath;
    }
  }
};
</script>
<style lang="scss" scoped>
.icon-pl {
  // padding-left: 8px;
  :deep(.iconfont) {
    margin-left: 4px !important;
  }
}
</style>
