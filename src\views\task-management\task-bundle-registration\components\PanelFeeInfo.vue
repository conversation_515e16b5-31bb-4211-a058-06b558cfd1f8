<template>
  <div class="sample-about">
    <div class="sample-about-header">
      <div class="title">费用信息</div>
      <el-button
        v-if="showDetail && isInvalidated === 0"
        type="primary"
        class="analysisBtn"
        size="small"
        icon="el-icon-plus"
        @click="addFee"
        @keyup.prevent
        @keydown.enter.prevent
        >新增费用</el-button
      >
    </div>
    <div class="content">
      <el-table
        v-if="feeInfoDetail && feeInfoDetail.length > 0"
        ref="tableSampleRef"
        :key="tableSampleKey"
        :data="feeInfoDetail"
        :summary-method="getSummaries"
        show-summary
        fit
        border
        class="dark-table base-table format-height-table"
        @header-dragend="drageHeader"
      >
        <el-table-column type="index" label="序号" :width="70" align="center" />
        <el-table-column label="费用类型" prop="costType" min-width="100px" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.costType || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="检测费用" prop="inspectionCost" min-width="140px" show-overflow-tooltip>
          <template #default="{ row }">
            <div>{{ row.inspectionCost || '--' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="描述" prop="remark" min-width="120px" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.remark || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="170px" prop="caozuo" align="center">
          <template #default="{ row }">
            <!-- <span class="blue-color" @click="chenckSampleIA(row)">查看</span> -->
            <span v-if="showDetail && isInvalidated === 0" class="blue-color" @click="editFeeInfo(row)">编辑</span>
            <span v-if="showDetail && isInvalidated === 0" class="blue-color" @click="deleteFeeInfo(row)">删除</span>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-if="!feeInfoDetail || feeInfoDetail.length === 0" :image="emptyImg" description="暂无数据" />
    </div>
  </div>
  <div class="other">
    <el-dialog v-model="showFeeDialog" :title="feeTitle" width="30%">
      <template #default>
        <el-form
          ref="formRef"
          :model="currentFeeInfo"
          class="formDataSample"
          label-position="right"
          label-width="110px"
        >
          <el-row :gutter="40">
            <el-col :span="24">
              <el-form-item label="费用类型：" prop="costType">
                <el-select
                  v-if="!isCheck"
                  v-model="currentFeeInfo.costType"
                  placeholder="请选择"
                  clearable
                  filterable
                  style="width: 100%"
                >
                  <el-option v-for="item in costTypeList" :key="item.code" :label="item.name" :value="item.name" />
                </el-select>
                <div v-else class="nowrap">
                  {{ currentFeeInfo.costType || '--' }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="检测费用：" prop="inspectionCost">
                <el-input-number
                  v-if="!isCheck"
                  v-model="currentFeeInfo.inspectionCost"
                  controls-position="right"
                  :step="1"
                  :min="0"
                  style="width: 100%"
                  size="small"
                />
                <span v-else> {{ currentFeeInfo.inspectionCost || '--' }} </span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="描述：" prop="remark">
                <el-input v-if="!isCheck" v-model="currentFeeInfo.remark" size="small" />
                <span v-else> {{ currentFeeInfo.remark || '--' }} </span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showFeeDialog = false">取消</el-button>
          <el-button type="primary" :loading="submitFeeLoading" @click="submitFeeData"> 确认 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch } from 'vue';
// import { useStore } from 'vuex'
// import router from '@/router/index.js'
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { ElMessage, ElMessageBox } from 'element-plus';
import { deleteFeeInfoById, saveTaskFeeInfo } from '@/api/task-registration';
import { getDictionary } from '@/api/dictionary';
import emptyImg from '@/assets/img/empty-table.png';

export default {
  name: 'FeeInfo',
  components: {},
  props: {
    taskId: {
      type: String,
      default: ''
    },
    isInvalidated: {
      type: Number,
      default: 0
    },
    showDetail: {
      type: Boolean,
      default: false
    },
    feeInfoData: {
      type: Object,
      default: function () {
        return {
          list: [],
          total: 0
        };
      }
    }
  },
  emits: ['setInfo', 'close'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    // console.log(proxy)
    // const store = useStore().state
    const datas = reactive({
      tableSampleKey: 'tableSampleKey',
      feeInfoDetail: props.feeInfoData.list,
      totalFee: props.feeInfoData.total,
      showDrawer: false,
      isInvalidated: props.isInvalidated,
      showFeeDialog: false,
      feeTitle: '新增费用',
      formRef: ref(),
      submitFeeLoading: false,
      currentFeeInfo: {
        costType: '',
        inspectionCost: 0,
        remark: '',
        superId: ''
      },
      costTypeList: [],
      isCheck: false,
      defaultCostType: ''
    });

    watch(
      () => props.feeInfoData,
      newValue => {
        if (newValue && newValue.list) {
          const list = [];
          props.feeInfoData.list.forEach(item => {
            list.push({
              costType: item.costType === 'inspection' ? '检测费' : item.costType,
              createBy: item.createBy,
              createTime: item.createTime,
              id: item.id,
              inspectionCost: Number(item.inspectionCost),
              remark: item.remark,
              superId: item.superId,
              tenantId: item.tenantId,
              updateBy: item.updateBy,
              updateTime: item.updateTime
            });
          });
          datas.feeInfoDetail = list;
          datas.totalFee = props.feeInfoData.total;
        } else {
          datas.feeInfoDetail = [];
          datas.totalFee = 0;
        }
      },
      { deep: true }
    );

    // 添加样品-打开新增样品弹出框
    const addFee = () => {
      // console.log(datas.feeInfoDetail)
      datas.feeTitle = '新增费用';
      // datas.feeInfoDetail = {}
      datas.currentFeeInfo = {
        costType: '',
        inspectionCost: 0,
        remark: '',
        superId: ''
      };
      datas.showFeeDialog = true;
      // context.emit('setInfo', datas.feeInfoDetail)
    };
    // 关闭新增样品页面弹出框
    const closeAddSampleDrawer = value => {
      datas.showDrawer = value;
      context.emit('setInfo', 'close');
    };
    // 获取新增样品数据
    const getAddSampleData = data => {
      console.log(data);
      // datas.feeInfoDetail.push(data)
      context.emit('setInfo', datas.feeInfoDetail);
    };
    // 编辑样品
    const editFeeInfo = row => {
      datas.feeTitle = '编辑费用';
      datas.currentFeeInfo = row;
      datas.showFeeDialog = true;
    };

    // 删除样品
    const deleteFeeInfo = row => {
      ElMessageBox({
        title: '提交',
        message: '是否确认删除该样品？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          deleteFeeInfoById(row.id).then(res => {
            if (res !== false) {
              ElMessage.success('删除成功');
              context.emit('setInfo', 'delete');
            }
          });
        })
        .catch(() => {});
    };

    const submitFeeData = () => {
      // 提交费用信息
      datas.formRef.validate(valid => {
        if (valid) {
          datas.currentFeeInfo.superId = props.taskId;
          if (datas.feeTitle === '新增费用') {
            datas.submitFeeLoading = true;
            saveTaskFeeInfo(datas.currentFeeInfo).then(function (res) {
              datas.submitFeeLoading = false;
              if (res !== false) {
                context.emit('setInfo', datas.currentFeeInfo);
                context.emit('close', false);
                ElMessage.success('新增成功');
                datas.showFeeDialog = false;
              }
            });
          } else {
            datas.submitFeeLoading = true;
            saveTaskFeeInfo(datas.currentFeeInfo).then(function (res) {
              datas.submitFeeLoading = false;
              if (res !== false) {
                context.emit('setInfo', datas.currentFeeInfo);
                context.emit('close', false);
                ElMessage.success('编辑成功');
                datas.showFeeDialog = false;
              }
            });
          }
        } else {
          return false;
        }
      });
    };

    const getSummaries = param => {
      const { columns, data } = param;
      // console.log(`总结:${param}`)
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          return;
        }
      });
      sums[0] = data.length + 1;
      sums[1] = '合计';
      sums[2] = datas.totalFee;
      sums[3] = '';
      sums[4] = '';

      return sums;
    };

    function getCostTypeList() {
      getDictionary('FeeType').then(res => {
        datas.costTypeList = res.data.data.dictionaryoption;
        datas.defaultCostType = datas.costTypeList.filter(item => item.status === 1)[0].code;
      });
    }

    getCostTypeList();

    return {
      ...toRefs(datas),
      emptyImg,
      submitFeeData,
      getNameByid,
      addFee,
      drageHeader,
      closeAddSampleDrawer,
      getAddSampleData,
      editFeeInfo,
      deleteFeeInfo,
      getCostTypeList,
      getSummaries,
      getPermissionBtn
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.sample-about {
  .sample-about-header {
    margin: 15px 0 15px 0;
    text-align: left;
    .title {
      font-size: 16px;
      font-weight: bold;
      height: 32px;
      line-height: 32px;
      text-align: left;
      display: inline-block;
      margin-right: 16px;
    }
    .el-button {
      float: right;
    }
  }
  .content {
    background: $background-color;
    text-align: left;
    position: relative;
    // .sample-about-table {
    //   margin-bottom: 15px;
    // }
    // :deep(.el-table__footer) {
    //   .is-hidden {
    //     display: block;
    //   }
    // }
  }
}
</style>
