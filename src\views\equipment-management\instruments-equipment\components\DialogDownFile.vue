<template>
  <el-dialog
    v-model="dialogShow"
    title="下载附件"
    :close-on-click-modal="false"
    width="480px"
    custom-class="dialog_key_equipment"
    @close="handleDialogClose"
  >
    <el-form v-if="dialogShow" ref="formRef" :model="formData" label-width="100px">
      <el-form-item
        prop="refTypeList"
        label="类型："
        style="margin: 0px"
        :rules="{ required: true, message: '请选择类型', trigger: 'change' }"
      >
        <el-select v-model="formData.refTypeList" multiple placeholder="请选择附件类型" size="small">
          <el-option v-for="(val, key) in refTypeJSON" :key="key" :label="val" :value="key" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleDialogClose()">关 闭</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, ref, toRefs, watch, getCurrentInstance } from 'vue';
// Api
import { downloadZip } from '@/api/equipment';
export default {
  name: 'DialogDownFile',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    selectTable: {
      type: Array,
      default: function () {
        return [];
      }
    },
    dictionaryAll: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      dialogLoading: false, // 弹出窗loading
      formRef: ref(),
      refTypeJSON: {},
      formData: {
        refTypeList: []
      },
      dialogShow: false
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.formData = {
          refTypeList: [],
          deviceList: props.selectTable.map(item => {
            return { id: item.id, name: item.name };
          })
        };
        state.refTypeJSON = props.dictionaryAll['SBFJLX'].all;
      }
    });
    const handleDialogClose = () => {
      state.dialogShow = false;
      context.emit('closeDialog');
    };
    const onSubmit = () => {
      state.formRef
        .validate()
        .then(async valid => {
          if (valid) {
            state.dialogLoading = true;
            const response = await downloadZip(state.formData).finally((state.dialogLoading = false));
            if (response) {
              if (response.headers['content-length']) {
                const blob = new Blob([response.data], { type: response.headers['content-type'] || 'application/zip' });
                const blobUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.download = '设备附件.zip';
                a.href = blobUrl;
                a.click();
                proxy.$message.success('下载附件成功');
                state.dialogShow = false;
                context.emit('closeDialog', true);
              } else {
                state.dialogShow = false;
                proxy.$message.error('文件不存在');
                context.emit('closeDialog', true);
              }
            }
          } else {
            return false;
          }
        })
        .catch(() => {});
    };
    return {
      ...toRefs(state),
      onSubmit,
      handleDialogClose
    };
  }
};
</script>
<style lang="scss" scoped>
.btnGroup {
  margin-bottom: 10px;
  text-align: right;
}
</style>
<style lang="scss">
.dialog_key_equipment {
  .format-height-table3 {
    .el-table__body-wrapper {
      max-height: calc(100vh - 37.5rem) !important;
      overflow-y: auto;
    }
    .el-table__fixed-body-wrapper {
      max-height: calc(100vh - 38rem) !important;
      overflow-y: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;
    }
    .el-table__fixed-body-wrapper::-webkit-scrollbar {
      display: none;
    }
  }
}
</style>
