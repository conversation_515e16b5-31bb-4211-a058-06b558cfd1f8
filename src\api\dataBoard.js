import request from '@/utils/request';
const postHeaders = {
  'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
};
// 未来15天待检样品
export function getPredictionList(data) {
  return request({
    url: '/api-orders/orders/dataDisplay/findUndoneSampleData',
    method: 'get'
  });
}
// 近15天样品完成数
export function getFinishList(data) {
  return request({
    url: '/api-orders/orders/dataDisplay/findFinishSampleData',
    method: 'get'
  });
}
// 待下达（待认领）样品
export function getIssued(data) {
  return request({
    url: '/api-orders/orders/dataDisplay/findFinishSampleData',
    method: 'get'
  });
}
// 近15天提交项目排行榜
export function getfindSubmitCapabilityData(data) {
  return request({
    url: '/api-orders/orders/dataDisplay/findSubmitCapabilityData',
    method: 'get'
  });
}
// 近15天提交报告排行榜
export function getSubmitReportData(data) {
  return request({
    url: '/api-orders/orders/dataDisplay/findSubmitReportData',
    method: 'get'
  });
}
// 近30天不合格样品
export function getFailedTestSample(data) {
  return request({
    url: '/api-orders/orders/dataDisplay/findUnqualifiedData',
    method: 'get'
  });
}
// 未完成报告
export function getUnReportList(data) {
  return request({
    url: '/api-orders/orders/dataDisplay/unReportList',
    method: 'post',
    data
  });
}
// 近30天原材料合格率\近30天半成品合格率\近30天成品合格率
export function thirtyDataListApi(data) {
  return request({
    url: '/api-orders/orders/bigTable/qualificationRate',
    method: 'get',
    headers: postHeaders,
    params: data
  });
}
// 查询看板配置
export function dashboardconfig() {
  return request({
    url: '/api-orders/orders/dashboardconfig/query',
    method: 'get'
  });
}

// 重置默认视图
export function dashboardconfigReset() {
  return request({
    url: '/api-orders/orders/dashboardconfig/reset',
    method: 'get'
  });
}

// 保存报表配置信息
export function dashboardconfigSave(data) {
  return request({
    url: '/api-orders/orders/dashboardconfig/save',
    method: 'post',
    data
  });
}
