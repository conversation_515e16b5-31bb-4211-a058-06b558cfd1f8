<template>
  <el-dialog v-model="showUnFinishedDialog" title="未完成检测样品" width="500px" :close-on-click-modal="false">
    <CustomPanel
      :has-margin-bottom="true"
      :panel-margin-width="0.8"
      :has-panel-header="false"
      panel-min-height="80px"
      class="tip-panel"
      style="background-color: rgb(237, 250, 255)"
    >
      <div class="tip-message">
        <el-row>
          <el-col :span="2" style="text-align: center"><i class="el-icon-warning warning-icon" style="" /></el-col>
          <el-col :span="22"
            ><span style="line-height: 2.571429rem; font-size: 0.9rem"
              >以下样品因已有检测数据或报告数据不能完成检测</span
            ></el-col
          >
        </el-row>
        <el-row>
          <el-col :span="2" />
          <el-col :span="22">
            <el-tag v-for="sample in unFinishedSampleList" :key="sample" type="info" effect="dark">
              {{ sample }}
            </el-tag>
          </el-col>
        </el-row>
      </div>
    </CustomPanel>
    <template #footer>
      <el-button type="primary" @click="submitUnfinishTip">确定</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, watch, toRefs } from 'vue';
import CustomPanel from '@/components/PageComponents/CustomPanel.vue';
// import { certificateDecide } from '@/api/raw-certificate';
export default {
  name: 'DialogBatchCompletion',
  components: { CustomPanel },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    unList: {
      type: Array,
      default: () => []
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      unFinishedSampleList: [],
      dialogLoading: false,
      showUnFinishedDialog: false
    });

    const cancelDialog = value => {
      context.emit('closeDialog', value);
    };

    const submitUnfinishTip = () => {
      state.showUnFinishedDialog = false;
      cancelDialog(false);
    };

    watch(
      () => props.dialogVisible,
      newValue => {
        if (newValue) {
          state.showUnFinishedDialog = props.dialogVisible;
          state.unFinishedSampleList = props.unList;
        }
      }
    );

    return {
      ...toRefs(state),
      cancelDialog,
      submitUnfinishTip
    };
  }
};
</script>
<style lang="scss" scoped>
.result-group {
  .warning-radio,
  .success-radio {
    padding: 0px;
    height: 3rem;
    line-height: 3rem;
    font-weight: 540;
    width: 100%;
    text-align: center;
    :deep(.el-radio__input) {
      display: none;
    }
  }

  .warning-radio.is-checked,
  .success-radio.is-checked {
    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #fff;
    }
  }

  .warning-radio.is-checked {
    background-color: $tes-red;
    border-color: $tes-red !important;
    box-shadow: $tes-red -1px 0px 0px 0px;
  }

  .success-radio.is-checked {
    background-color: $green;
    border-color: $green !important;
    box-shadow: $green -1px 0px 0px 0px;
  }
}
.tip-panel {
  border: 1px solid $tes-primary;
  border-radius: 10px;
  // box-shadow: 0px 0px 4px rgb(243, 73, 73);
}
.tip-panel:hover {
  box-shadow: 1px 1px 2px 1px $tes-primary4;
}

.tip-message {
  padding: 5px 10px 10px 10px;
}

.tip-text {
  margin: 0px;
  line-height: 1.25rem;
  color: $tes-font2;
}

.flex-group {
  display: flex;
  flex-direction: row;
  .flex-space {
    width: 15px;
  }
  .flex-content {
    flex: auto;
  }
}
</style>
