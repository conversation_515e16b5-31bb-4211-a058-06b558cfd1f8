import request from '@/utils/request';
import { postFormHeaders } from '@/utils/http/requestInfo';
import qs from 'qs';

// 检验单列表接口
export function getInspectionList(data) {
  return request({
    url: '/api-diplomat/diplomat/inspection/list',
    method: 'post',
    data
  });
}
// 添加检验单信息接口
export function addInspection(data) {
  return request({
    url: '/api-diplomat/diplomat/inspection/save',
    method: 'post',
    data
  });
}
// 编辑检验单
export function updateInspection(data) {
  return request({
    url: '/api-diplomat/diplomat/inspection/update',
    method: 'post',
    data
  });
}
// 取消检验单
export function cancelInspection(id) {
  return request({
    url: `/api-diplomat/diplomat/inspection/cancel/${id}`,
    method: 'post'
  });
}
// 还原检验单
export function restoreInspection(id) {
  return request({
    url: `/api-diplomat/diplomat/inspection/unCancel/${id}`,
    method: 'get'
  });
}
// 根据检验单id查询检验单详情
export function getInspectionInfo(id) {
  return request({
    url: `/api-diplomat/diplomat/inspection/info/${id}`,
    method: 'get'
  });
}
// 添加样品接口
export function addSample(data) {
  return request({
    url: '/api-diplomat/diplomat/inspectionorders/save',
    method: 'post',
    data
  });
}
// 编辑样品接口
export function updateSample(data) {
  return request({
    url: '/api-diplomat/diplomat/inspectionorders/update',
    method: 'post',
    data
  });
}
// 删除样品接口
export function deleteSample(id) {
  return request({
    url: `/api-diplomat/diplomat/inspectionorders/delete/${id}`,
    method: 'post'
  });
}
// 检验单提交
export function submitInspection(id) {
  return request({
    url: `/api-diplomat/diplomat/inspection/submitInspection/${id}`,
    method: 'post'
  });
}
// 第三方检测报告数据批量回传
export function thirdReportReturn(data) {
  return request({
    url: '/api-orders/orders/distribution/thirdDataReturn',
    method: 'post',
    data
  });
}

/**
 * 检验单复制
 * http://*************:8800/doc.html#/diplomat/inspectionapi/copyUsingPOST
 * @param {*} data
 * @returns
 */
export function copyInspection(data) {
  // data = qs.stringify(data);
  return request({
    url: `/api-diplomat/diplomat/inspection/copy`,
    // headers: postFormHeaders,
    method: 'post',
    data
  });
}

/**
 * 复制检测inspectionOrders信息
 * http://*************:8800/doc.html#/diplomat/inspectionOrdersapi/copyUsingPOST_1
 * @param {*} data
 * @returns
 */
export function copyInspectionSample(data) {
  data = qs.stringify(data);
  return request({
    url: `/api-diplomat/diplomat/inspectionorders/copy`,
    headers: postFormHeaders,
    method: 'post',
    data
  });
}
