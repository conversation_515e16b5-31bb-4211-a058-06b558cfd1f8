<template>
  <!-- 检测机构 -->
  <!-- 分包商管理 -->
  <ListLayout
    :has-button-group="
      (category === 0 && getPermissionBtn('addOrganization')) ||
      (category === 1 && getPermissionBtn('addSubcontractor'))
    "
    :has-quick-query="true"
  >
    <template #search-bar>
      <div class="searchInput">
        <el-input
          v-model="condition"
          v-trim
          v-focus
          size="large"
          class="ipt-360"
          clearable
          prefix-icon="el-icon-search"
          placeholder="请输入编号/公司名称"
          @clear="getTableList"
          @keyup.enter="getTableList"
        />
        <el-button size="large" type="primary" @click="getTableList">查询</el-button>
        <el-button size="large" @click="reset">重置</el-button>
      </div>
    </template>
    <template #button-group>
      <el-button
        size="large"
        icon="el-icon-plus"
        type="primary"
        @click="addCustomer"
        @keyup.prevent
        @keydown.enter.prevent
      >
        {{ category === 1 ? '新增分包商' : '新增机构' }}
      </el-button>
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="24" style="text-align: right">
          <TableColumnView binding-menu="TestingApparatus" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="tableList"
      size="medium"
      fit
      border
      height="auto"
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="item.fieldType === fieldTypesEnum.Person">
              <UserTag :name="getNameByid(row[item.fieldKey]) || '--'" />
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Status">
              <el-tag v-if="row[item.fieldKey]" effect="dark" size="small" type="success">启用</el-tag>
              <el-tag v-else size="small" effect="dark" type="info">停用</el-tag>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Date">
              <span>{{ formatDate(row[item.fieldKey]) || '--' }}</span>
            </template>
            <span v-else>{{ row[item.fieldKey] || '--' }}</span>
          </template>
        </el-table-column>
      </template>
      <el-table-column
        v-if="category === 0 && getPermissionBtn('associatedTenant')"
        label="操作"
        :width="colWidth.operationMultiple"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <span class="blue-color" @click="checkRow(row)">查看</span>
          <span
            class="blue-color"
            @click="
              selectRow = row;
              dialogVisiable = true;
              relevancyTenantId = row.relevancyTenantId;
            "
            >关联租户</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getTableList"
    />
    <template #other>
      <!-- 检测机构所有信息抽屉 -->
      <DrawerDetail
        :id="rowId"
        :detail-drawer="detailDrawer"
        :drawer-type="drawerType"
        @closeDrawer="closeDetailDrawer"
      />
      <!-- 新增编辑机构信息 -->
      <DrawerOrganizationInfo
        :drawer="customerDrawer"
        :detail-data="rowDetail"
        :drawer-type="customerDrawerType"
        @close="closeCustomerDrawer"
      />
      <el-dialog
        v-model="dialogVisiable"
        title="关联租户"
        :close-on-click-modal="false"
        width="420px"
        @close="handleClose"
      >
        请选择关联租户：
        <el-select v-model="relevancyTenantId" placeholder="请选择租户" size="small">
          <el-option v-for="item in tenantList" :key="item.id" :value="item.id" :label="item.clientShortName" />
        </el-select>
        <template #footer>
          <span class="dialog-footer">
            <el-button size="small" @click="handleClose">取 消</el-button>
            <el-button type="primary" size="small" @click="onSubmit">确 认</el-button>
          </span>
        </template>
      </el-dialog>
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import Pagination from '@/components/Pagination';
import ListLayout from '@/components/ListLayout';
import { formatDate } from '@/utils/formatTime';
import { useStore } from 'vuex';
import UserTag from '@/components/UserTag';
import DrawerDetail from './DrawerDetail.vue';
import DrawerOrganizationInfo from './DrawerOrganizationInfo.vue';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { getList, getListTenant, submitTenant } from '@/api/testingApparatus';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import { colWidth } from '@/data/tableStyle';
import { useRoute } from 'vue-router';
import TableColumnView from '@/components/TableColumnView';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';

export default {
  name: 'TestingApparatus',
  components: { Pagination, ListLayout, UserTag, DrawerDetail, DrawerOrganizationInfo, TableColumnView },
  setup() {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const route = useRoute();
    const state = reactive({
      tableRef: ref(),
      condition: '', // 列表关键字
      rowDetail: {},
      dialogVisiable: false, // 关联租户弹出框
      drawerType: '', // 详情抽屉类型
      detailDrawer: false, // 仪器设备编号抽屉
      customerDrawer: false,
      selectRow: {}, // 选中的行
      relevancyTenantId: '',
      tenantList: [], // 租户列表
      tenantListJson: {}, // 租户列表json结构
      orderBy: '', // 排序字段
      isDesc: '', // 是否倒叙
      customerDrawerType: '',
      rowId: '',
      ruleForm: ref(),
      listLoading: false,
      statusType: {
        Fault: 'danger',
        Running: 'success',
        Scrapped: 'info'
      },
      customerStatusJson: {}, // 客户状态
      userOptions: store.common.nameList,
      types: [],
      listQuery: {
        limit: 20,
        page: 1
      },
      tableColumns: [],
      tableList: [],
      total: 0,
      currentAccountId: getLoginInfo().accountId,
      category: route.name === 'SubcontractorManagement' ? 1 : 0 // 1 分包商管理页面， 0 检测机构页面
    });
    const tableKey = ref(0);
    const getTableList = query => {
      const params = { condition: state.condition, orderBy: state.orderBy, category: state.category };
      if (state.isDesc !== '') {
        params.isDesc = state.isDesc;
      }
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoading = true;
      getList(params).then(res => {
        state.listLoading = false;
        if (res) {
          state.total = res.data.data.totalCount;
          state.tableList = res.data.data.list;
        }
      });
    };
    getTableList();
    const sortChange = column => {
      state.orderBy = column.prop;
      if (column.order === 'descending') {
        state.isDesc = true;
      } else {
        state.isDesc = false;
      }
      getTableList();
    };
    const reset = () => {
      state.condition = '';
      state.orderBy = '';
      state.isDesc = '';
      getTableList();
    };
    const getTenantList = () => {
      getListTenant({ page: '1', limit: '-1' }).then(res => {
        if (res) {
          state.tenantList = [];
          res.data.data.list.forEach(item => {
            if (item.status === 1) {
              state.tenantList.push(item);
              state.tenantListJson[item.id] = item.clientShortName;
            }
          });
        }
      });
    };
    getTenantList();
    // 列表操作
    const handleOperate = () => {};
    // 新增信息
    const addCustomer = () => {
      state.customerDrawer = true;
      state.customerDrawerType = 'add';
    };
    // 查看信息
    const checkRow = row => {
      state.detailDrawer = true;
      state.drawerType = 'check';
      state.rowId = row.id;
    };
    // 关闭弹出窗
    const closeDialog = val => {
      state.detailDrawer = false;
      if (val.isRefresh) {
        getTableList();
      }
    };
    const closeCustomerDrawer = val => {
      state.customerDrawer = false;
      if (val.isRefresh) {
        getTableList();
      }
    };
    // 切换维修结果
    const changeMateType = () => {
      getTableList();
    };
    // 关闭仪器设备弹出窗
    const closeDetailDrawer = val => {
      state.detailDrawer = false;
      if (val.isRefresh) {
        getTableList();
      }
    };
    const handleClose = () => {
      state.dialogVisiable = false;
    };
    // 提交关联租户
    const onSubmit = () => {
      if (!state.relevancyTenantId) {
        proxy.$message.error('请先选择租户');
        return false;
      }
      const params = {
        superId: state.selectRow.id,
        relevancyTenantId: state.relevancyTenantId,
        relevancyTenantName: state.tenantListJson[state.relevancyTenantId]
      };
      submitTenant(params).then(res => {
        if (res) {
          state.dialogVisiable = false;
          proxy.$message.success('关联成功');
          getTableList();
        }
      });
    };
    const onUpdateColumns = columns => {
      const newTableColumns = [];
      columns.forEach(column => {
        if (state.category === 1 && /^(no|name)$/.test(column.fieldKey)) {
          if (column.fieldKey === 'no') {
            column.fieldName = '分包商编号';
          } else if (column.fieldKey === 'name') {
            column.fieldName = '分包商名称';
          }
          newTableColumns.push(column);
        } else if (!(state.category !== 0 && /^relevancyTenantName$/.test(column.fieldKey))) {
          newTableColumns.push(column);
        }
      });
      tableKey.value = tableKey.value + 1;
      state.tableColumns = newTableColumns;
    };
    return {
      ...toRefs(state),
      sortChange,
      getTenantList,
      handleClose,
      onSubmit,
      closeCustomerDrawer,
      getPermissionBtn,
      changeMateType,
      drageHeader,
      getNameByid,
      getNamesByid,
      addCustomer,
      checkRow,
      closeDialog,
      closeDetailDrawer,
      reset,
      handleOperate,
      formatDate,
      getTableList,
      tableKey,
      colWidth,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  }
};
</script>
<style lang="scss" scoped>
.searchInput {
  display: flex;
  .el-input {
    width: 360px;
    margin-right: 10px;
  }
}
</style>
