<template>
  <div class="table-column-view">
    <el-popover
      ref="popoverRef"
      placement="bottom"
      :width="200"
      trigger="click"
      :auto-close="0"
      popper-class="table-column-popver"
    >
      <template #reference>
        <div class="table-column-view__trigger" @mouseleave="handleLeave" @mouseenter="handleEnter">
          <SvgIcon :icon-class="columnViewUrl" :width="14" :height="14" />
          <SvgIcon :icon-class="settingColumnViewUrl" :width="4" :height="4" class="setting-column-icon" />
        </div>
      </template>
      <ul class="table-column-view__list">
        <li
          v-for="(item, index) in viewList"
          :key="index"
          class="table-column-view__list-item"
          :class="{ 'list-item-isDefalut': item.isDefault === 1 }"
          @click="changeCurrentView(item, index)"
        >
          <span>{{ item.viewName }}</span>
          <el-dropdown
            v-if="item.isFixedView !== 1"
            trigger="hover"
            :class="item.showIcon ? 'icon-show' : ''"
            class="tree-dropdown el-icon"
            placement="bottom-end"
            @visible-change="changeIcon(item.showIcon, item)"
          >
            <i class="el-icon-more" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="editView(item, index)"><i class="iconfont tes-edit" />编辑</el-dropdown-item>
                <el-dropdown-item class="color-red" @click="deleteView(item, index)"
                  ><i class="iconfont tes-delete" />删除</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </li>
      </ul>
      <div class="table-column-view__new-view-button" @keyup.prevent @keydown.enter.prevent @click="addNewView">
        <i class="el-icon-plus" />
        创建新视图
      </div>
    </el-popover>

    <!-- 新增编辑视图 -->
    <table-column-custom
      v-model="showDrawer"
      :type="editerType"
      :user-id="userId"
      :binding-menu="bindingMenu"
      :view="editerView"
      @update:view="updateView"
    />
  </div>
</template>

<script>
import { ref, reactive, toRefs, computed, onMounted, getCurrentInstance } from 'vue';
import { useStore } from 'vuex';
import { ElMessage } from 'element-plus';
import { getViewByBindingMenu, getViewByViewId, setDefaultViewById, deleteViewById } from '@/api/tableView';
// import { getViewByBindingMenu, getViewByViewId, setDefaultViewById } from '@/api/tableView'
import { getLoginInfo } from '@/utils/auth';
import { cloneDeep } from 'lodash';
import SvgIcon from '@/components/SvgIcon';
import { filterTableColumnDataByTenantType } from './tools';
import TableColumnCustom from './TableColumnCustom.vue';

export default {
  name: 'TableColumnView',
  components: { TableColumnCustom, SvgIcon },
  props: {
    bindingMenu: {
      type: String,
      default: ''
    }
  },
  emits: ['columns'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const store = useStore();
    const state = reactive({
      userId: getLoginInfo()?.accountId,
      columnViewUrl: 'column-view',
      settingColumnViewUrl: 'setting-column-view',
      currentView: {
        id: '',
        viewName: '',
        isDefault: '',
        isFixedView: 0,
        tableColumnData: []
      },
      editerView: {},
      listLoading: false,
      itemLoading: false,
      viewList: [],
      showDrawer: false,
      editerType: 'add'
    });

    const popoverRef = ref(null);

    const tenantInfo = computed(() => store.getters.tenantInfo);

    onMounted(() => {
      if (props.bindingMenu) {
        state.listLoading = true;
        getViewByBindingMenu(props.bindingMenu)
          .then(res => {
            state.listLoading = false;
            if (res && res.data.code === 200 && res.data.data.length > 0) {
              const fixedViewIndex = res.data.data.findIndex(item => item.isFixedView.toString() === '1');
              // 置顶默认视图
              const fixedViewItemList = fixedViewIndex === -1 ? [] : res.data.data.splice(fixedViewIndex, 1);
              state.viewList = fixedViewItemList.concat(res.data.data);
              const defaultIndex = state.viewList.findIndex(item => item.isDefault.toString() === '1');
              const defaultView = defaultIndex === -1 ? state.viewList[0] : state.viewList[defaultIndex];
              changeCurrentView(defaultView, defaultIndex === -1 ? 0 : defaultIndex);
            }
          })
          .catch(() => {
            state.listLoading = false;
          });
      }
    });

    const getViewConfig = (id, index) => {
      return getViewByViewId(id).then(res => {
        if (res && res.data.code === 200) {
          triggerTableColumns(res.data.data, index);
        }
      });
    };

    const setDefaultView = async id => {
      return setDefaultViewById(id).then(res => {
        if (res && res.data.code === 200) {
          state.viewList.forEach(item => {
            if (item.id === id) {
              item.isDefault = 1;
            } else {
              item.isDefault = 0;
            }
          });
          state.currentView.isDefault = 1;
        }
      });
    };

    const changeCurrentView = async (item, index) => {
      if (state.currentView.id !== item.id) {
        state.itemLoading = true;
        await setDefaultView(item.id);
      }
      state.currentView.id = item.id;
      state.currentView.viewName = item.viewName;
      state.currentView.isFixedView = item.isFixedView;
      if (item?.sysEmployeeListConfigList?.length > 0) {
        state.currentView.isDefault = item.isDefault;
        triggerTableColumns(item, index);
        state.itemLoading = false;
      } else {
        state.itemLoading = true;
        await getViewConfig(item.id, index);
        state.itemLoading = false;
      }
    };

    const triggerTableColumns = (viewData, viewListIndex) => {
      let tableColumnData = viewData.sysEmployeeListConfigList;
      if (viewData.isFixedView.toString() === '1') {
        tableColumnData = filterTableColumnDataByTenantType(tableColumnData, tenantInfo.value.type);
      }
      const columns = tableColumnData.filter(item => item.isShow === 1);
      state.currentView.tableColumnData = tableColumnData;
      state.viewList[viewListIndex].sysEmployeeListConfigList = tableColumnData;
      context.emit('columns', cloneDeep(columns));
      popoverRef.value.hide();
    };

    const updateView = viewData => {
      // state.currentView = {
      //   id: viewData.id,
      //   viewName: viewData.viewName,
      //   isDefault: viewData.isDefault,
      //   isFixedView: viewData.isFixedView,
      //   tableColumnData: viewData.sysEmployeeListConfigList
      // }
      let isNewView = true;
      state.viewList.forEach((item, index) => {
        if (viewData.isDefault) {
          if (item.id === viewData.id) {
            item.isDefault = 1;
          } else {
            item.isDefault = 0;
          }
        }
        if (item.id === viewData.id) {
          isNewView = false;
          state.viewList.splice(index, 1, viewData);
        }
      });
      if (isNewView) {
        if (viewData.isDefault) {
          state.viewList.forEach(item => {
            item.isDefault = 0;
          });
        }
        state.viewList.push(viewData);
      }
      let showColumn = state.viewList.filter(item => {
        return item.isDefault === 1;
      })[0];
      if (!showColumn) {
        showColumn = state.viewList[0];
        state.viewList[0].isDefault = 1;
      }
      const columns = showColumn.sysEmployeeListConfigList.filter(item => item.isShow === 1);
      context.emit('columns', columns);
    };

    const addNewView = () => {
      state.showDrawer = true;
      state.editerType = 'add';
      state.editerView = {
        id: state.viewList.find(item => item.isFixedView === 1)?.id,
        isDefault: '1'
      };
    };

    const editView = item => {
      state.showDrawer = true;
      state.editerType = 'edit';
      let tableColumnData = item.sysEmployeeListConfigList;
      if (item.isFixedView.toString() === '1') {
        tableColumnData = filterTableColumnDataByTenantType(tableColumnData, tenantInfo.value.type);
      }
      state.editerView = {
        id: item.id,
        isDefault: item.isDefault,
        isFixedView: item.isFixedView,
        viewName: item.viewName,
        tableColumnData: tableColumnData
      };
    };

    const deleteView = (item, index) => {
      proxy
        .$confirm(`是否删除${item.viewName}？`, {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          showClose: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        })
        .then(() => {
          deleteViewById(item.id).then(res => {
            if (res && res.data.code === 200) {
              if (state.currentView.id === item.id) {
                changeCurrentView(state.viewList[0], 0);
              }
              state.viewList.splice(index, 1);
              ElMessage.success(`${item.viewName}删除成功!`);
            }
          });
        });
    };
    const handleLeave = () => {
      state.columnViewUrl = 'column-view';
      state.settingColumnViewUrl = 'setting-column-view';
    };
    const handleEnter = () => {
      state.columnViewUrl = 'column-view-hover';
      state.settingColumnViewUrl = 'setting-column-view-hover';
    };
    const changeIcon = (command, node) => {
      if (!command) {
        node.showIcon = true;
      } else {
        node.showIcon = !node.showIcon;
      }
    };

    return {
      popoverRef,
      ...toRefs(state),
      handleLeave,
      changeIcon,
      handleEnter,
      changeCurrentView,
      updateView,
      addNewView,
      editView,
      deleteView
    };
  }
};
</script>

<style lang="scss">
.table-column-view {
  float: right;
}

.table-column-view__trigger {
  padding: 8px 8px 8px 8px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  margin-bottom: 0px;
  position: relative;
  cursor: pointer;
  &:hover {
    background-color: $tes-primary2;
    border-color: #00b38a;
  }
}

.setting-column-icon {
  position: absolute;
  right: 4px;
  bottom: 4px;
}

.table-column-view__list {
  list-style: none;
  width: 100%;
  height: 200px;
  margin: 0;
  padding: 11px 12px 11px 12px;
  overflow-y: auto;
}

.table-column-view__list-item {
  display: flex;
  justify-content: space-between;
  line-height: 17px;
  padding: 7px 6px 7px 6px;
  font-size: 14px;
  color: #606266;
  .el-icon-more {
    display: none;
  }
  cursor: pointer;
  &:hover {
    background: #f5f7fa;
    .el-icon-more {
      display: inline-block;
    }
  }
  border-radius: 4px;
}

.list-item-isDefalut {
  color: $tes-primary;
}

.table-column-view__item-button {
  flex-grow: 1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.el-button--small.table-column-view__item-delete-icon {
  margin: 0 5px;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

.table-column-view__new-view-button {
  cursor: pointer;
  padding: 10px 12px 10px 12px;
  background-color: $tes-primary2;
  color: $tes-primary;
}
.table_column_custom_checkbox {
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #606266;
  }
  .el-checkbox.is-bordered.is-checked {
    background-color: transparent;
    border-color: #dcdfe6 !important;
  }
  .el-checkbox:hover.is-bordered.is-checked {
    border-color: $tes-primary1 !important;
  }
  .el-checkbox:hover {
    background-color: $tes-primary2 !important;
    border-color: $tes-primary1 !important;
    .el-checkbox__input.is-checked + .el-checkbox__label {
      color: $tes-primary;
    }
    .el-checkbox__input + .el-checkbox__label {
      color: $tes-primary;
    }
  }
}
.table-column-popver.el-popover.el-popper {
  padding: 0;
}
</style>
