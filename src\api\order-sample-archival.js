import request from '@/utils/request';

/** 查询归档
 *  sampleType 样品类型
 *   batchNoList 批次号集合
 */
export function querySampleArchival(data) {
  return request({
    url: '/api-orders/orders/sample-archival/queryPage',
    method: 'post',
    data
  });
}

/** 原材料投料清单 */
export function queryProductionOrderRawMaterialRecord(data) {
  return request({
    url: '/api-orders/orders/sample-archival/productionOrderRawMaterialRecord',
    method: 'post',
    data
  });
}

/** 导出原材料投料清单 */
export function exportProductionOrderRawMaterialRecord(data) {
  return request({
    url: '/api-orders/orders/sample-archival/exportProductionOrderRawMaterialRecord',
    method: 'post',
    data
  });
}

export function downloadZip(data) {
  return request({
    url: '/api-orders/orders/sample-archival/download-zip',
    method: 'post',
    data,
    responseType: 'blob'
  });
}

export function exportRoutineFinishedProductInspectionReport(data) {
  return request({
    url: '/api-orders/orders/sample-archival/exportRoutineFinishedProductInspectionReport',
    method: 'post',
    data
  });
}
