<template>
  <el-dialog
    v-model="dialogShow"
    title="样品领用记录"
    :close-on-click-modal="false"
    width="990px"
    custom-class="dialogRecipients"
    :before-close="closedialog"
  >
    <div class="title">
      <!-- <span class="title-line" /> 样品领用记录 -->
      <!-- 添加的功能需要移出到detial组件页头 -->
      <el-button
        v-if="jsonData.isower"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleAddEdit('add')"
        @keyup.prevent
        @keydown.enter.prevent
        >添加样品领用</el-button
      >
    </div>
    <el-table
      v-loading="listLoading"
      :data="tableData"
      class="dark-table format-height-table"
      tooltip-effect="light"
      style="width: 100%"
    >
      <el-table-column prop="date" label="领用人" :width="colWidth.person">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.receiveOwnerId)" />
        </template>
      </el-table-column>
      <el-table-column prop="name" label="日期" :width="colWidth.date">
        <template #default="{ row }">
          <span>{{ row.receiveDate }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="型号规格" :min-width="colWidth.model" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.prodType }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="数量" :width="colWidth.amount">
        <template #default="{ row }">
          <span>{{ row.receiveNum }}{{ unitJson[row.sampleUnitId] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="备注" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="nowrap">{{ row.remark ? row.remark : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="操作" :width="colWidth.operation">
        <template #default="{ row }">
          <span class="blue-color" @click="handleAddEdit('edit', row)">编辑</span>
          <span class="blue-color" @click="handleDelete(row)">删除</span>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog v-model="dialogSubmit" :title="dialogTitle" :close-on-click-modal="false" width="480px">
      <el-descriptions :column="1">
        <el-descriptions-item label="领用人：">
          <UserTag :name="getNameByid(accountId)" />
        </el-descriptions-item>
        <el-descriptions-item label="型号规格：">{{ formData.prodType }}</el-descriptions-item>
        <el-descriptions-item label="样品名称：">{{ formData.sampleName || '--' }}</el-descriptions-item>
      </el-descriptions>
      <el-divider />
      <el-form
        v-if="dialogSubmit"
        ref="ruleForm"
        :model="formData"
        size="small"
        label-position="right"
        label-width="84px"
      >
        <el-form-item
          label="领用日期："
          prop="receiveDate"
          :rules="{ required: true, message: '请选择领用日期', trigger: 'change' }"
        >
          <el-date-picker v-model="formData.receiveDate" type="date" :clearable="false" placeholder="选择日期" />
        </el-form-item>
        <el-form-item
          label="领用数量："
          prop="receiveNum"
          :rules="{ required: true, message: '请选择领用数量', trigger: 'change' }"
        >
          <el-input-number
            v-model="formData.receiveNum"
            controls-position="right"
            :min="1"
            :step="1"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item
          label="单位："
          prop="sampleUnitId"
          :rules="{ required: true, message: '请选择领用单位', trigger: 'change' }"
        >
          <el-select v-model="formData.sampleUnitId" filterable clearable placeholder="请选择单位" style="width: 100%">
            <el-option-group v-for="item in unitArray" :key="item.label" :label="item.label">
              <el-option
                v-for="val in item.group"
                :key="val.id"
                :label="val.name"
                :value="val.code"
                :disabled="val.status !== 1"
              >
                <span style="float: left">{{ val.name }}</span>
                <span v-if="val.status !== 1" class="fr" style="color: red">已停用</span>
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="备注：">
          <el-input
            v-model="formData.remark"
            type="textarea"
            placeholder="请输入备注"
            show-word-limit
            maxlength="300"
            :rows="2"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button size="small" @click="dialogSubmit = false">取 消</el-button>
          <el-button
            size="small"
            type="primary"
            :loading="submitLoading"
            @click="onSubmit"
            @keyup.prevent
            @keydown.enter.prevent
            >确 认</el-button
          >
        </span>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { getCurrentInstance, reactive, toRefs, ref, watch } from 'vue';
// import router from '@/router/index.js'
import { getLoginInfo } from '@/utils/auth';
import { useRoute } from 'vue-router';
import UserTag from '@/components/UserTag';
import { colWidth } from '@/data/tableStyle';
import { getDictionary } from '@/api/user';
import { getSampleCollectionList, saveSample, deleteSample } from '@/api/sampleItemTest';
import { getNameByid } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
// import { useStore } from 'vuex'
// import { getSampleCollectionList } from '@/api/sample'
export default {
  name: 'DialogRecipients',
  components: {
    UserTag
  },
  props: {
    dialogRecipients: {
      type: Boolean,
      default: function () {
        return false;
      }
    },
    jsonData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    sampleCollectionList: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['closeRecipients'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // const store = useStore()
    const route = useRoute();
    const state = reactive({
      dialogSubmit: false,
      dialogShow: false,
      accountId: getLoginInfo().accountId,
      listLoading: false,
      isRefresh: false,
      dialogTitle: '添加样品领用',
      ruleForm: ref(null),
      unitJson: {},
      unitArray: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ],
      tableData: [],
      isLoaned: 0, // 是否借出
      isReceiveVerification: 0, // 是否需要领用验证
      circulationMode: 1, // 流转方式， 1：材料领用 0 单项流转
      formData: {
        receiveDate: new Date(),
        receiveNum: 0,
        samplesId: route.query.samplesId
      },
      submitLoading: false
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogRecipients;
      if (state.dialogShow) {
        state.tableData = [];
        state.formData.sampleName = props.jsonData.sampleName;
        state.isLoaned = props.jsonData.isLoaned;
        state.isReceiveVerification = props.jsonData.isReceiveVerification;
        state.circulationMode = props.jsonData.circulationMode;
        state.formData.prodType = props.jsonData.prodType;
        state.tableData = props.sampleCollectionList;
        state.listLoading = false;
        state.isRefresh = false;
        getUnitList();
      }
    });
    // 列表接口
    const getList = () => {
      state.listLoading = true;
      getSampleCollectionList(route.query.samplesId).then(res => {
        state.listLoading = false;
        if (res.data.code === 200) {
          state.tableData = res.data.data;
        }
      });
    };
    // 数量单位
    const getUnitList = () => {
      state.listLoading = true;
      getDictionary(5).then(res => {
        state.listLoading = false;
        if (res) {
          res.data.data.dictionaryoption.forEach(item => {
            if (item.status === 1) {
              state.unitArray[0].group.push(item);
            } else {
              state.unitArray[1].group.push(item);
            }
            state.unitJson[item.code] = item.name;
          });
        }
      });
    };
    // 打开样品领用记录添加编辑弹出框
    const handleAddEdit = (type, row) => {
      if (type === 'edit') {
        state.dialogSubmit = true;
        state.dialogTitle = '编辑样品领用';
        state.formData = JSON.parse(JSON.stringify(row));
      } else {
        if (state.isReceiveVerification && state.circulationMode === 0) {
          if (state.isLoaned) {
            proxy.$message.warning('当前样品已被借出，无法领用进行试验！');
            return false;
          }
          if (state.tableData.length > 0) {
            proxy
              .$confirm('当前样品已被人领用，是否线下已转移并进行重新领用？', '确认', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning'
              })
              .then(() => {
                handleReceive();
              })
              .catch(() => {});
          } else {
            handleReceive();
          }
        } else {
          handleReceive();
        }
      }
    };
    const handleReceive = () => {
      state.dialogSubmit = true;
      state.formData = {
        receiveDate: new Date(),
        receiveNum: 0,
        sampleName: props.jsonData.sampleName,
        samplesId: route.query.samplesId,
        prodType: props.jsonData.prodType,
        sampleUnitId: state.unitArray[0].group[0].code
      };
    };
    const handleDelete = row => {
      proxy
        .$confirm('是否删除？删除后将不能还原。', '删除确认', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          deleteSample(row.id).then(res => {
            if (res) {
              proxy.$message.success(res.data.message);
              state.isRefresh = true;
              getList();
            } else {
              proxy.$message.error(res.data.message);
            }
          });
        })
        .catch(() => {});
    };
    const onSubmit = () => {
      state.ruleForm.validate().then(valid => {
        if (valid) {
          state.submitLoading = true;
          const params = {
            ...state.formData,
            receiveDate: formatDate(state.formData.receiveDate)
          };
          saveSample(params).then(res => {
            if (res) {
              state.submitLoading = false;
              proxy.$message.success(res.data.message);
              state.dialogSubmit = false;
              state.isRefresh = true;
              getList();
            } else {
              proxy.$message.error(res.data.message);
            }
          });
        }
      });
    };
    const closedialog = () => {
      context.emit('closeRecipients', state.isRefresh);
    };
    return {
      ...toRefs(state),
      props,
      handleDelete,
      onSubmit,
      getList,
      getNameByid,
      handleReceive,
      getUnitList,
      handleAddEdit,
      closedialog,
      colWidth
    };
  },
  computed: {},
  created() {}
};
</script>

<style lang="scss" scoped>
.title {
  display: flex;
  align-items: center;
  color: $tes-font;
  font-size: 16px;
  text-align: left;
  margin-bottom: 10px;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
  width: 100%;
}
:deep(.el-table__body-wrapper) {
  margin-bottom: 0;
}
</style>
<style lang="scss">
.dialogRecipients .el-dialog__body {
  padding-bottom: 40px !important;
}
.dialogRecipients .format-height-table {
  .el-table__body-wrapper {
    max-height: calc(100vh - 468px);
    overflow-x: hidden !important;
  }
}
</style>
