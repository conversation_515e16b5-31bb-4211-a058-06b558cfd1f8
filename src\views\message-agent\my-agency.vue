<template>
  <!-- 我的待办 -->
  <ListLayout class="my-agency" :has-button-group="false">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="condition">
          <el-input
            v-model="formInline.condition"
            v-trim
            v-focus
            placeholder="请输入标题或内容"
            class="ipt-360"
            size="large"
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter="onSubmit"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0px">
          <el-button type="primary" size="large" @click="onSubmit">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
          <el-button class="searchBtn" type="text" size="large" @click="search" @keyup.prevent @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
          /></el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" :model="searchForm" label-width="130px" label-position="right">
            <el-form-item label="等级：">
              <el-radio-group v-model="searchForm.level" @change="changelevel">
                <el-radio lable="">不限</el-radio>
                <el-radio v-for="item in levelOptions" :key="item.id" :label="item.id">{{ item.name }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="分组：">
              <el-select
                v-model="searchForm.groupId"
                class="owner-select"
                placeholder="请选择"
                size="small"
                clearable
                @change="changecategoryName"
              >
                <el-option
                  v-for="item in categoryNameOptions"
                  :key="item.id"
                  :label="item.groupName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间：">
              <el-date-picker
                v-model="searchForm.createDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                @change="changeCreateTime"
              />
            </el-form-item>
            <el-form-item label="要求完成时间：">
              <el-date-picker
                v-model="searchForm.finishTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                @change="changeTodoTime"
              />
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-radio-group v-model="radioData" size="small" @change="changeRadio">
        <el-radio-button label="全部" />
        <el-radio-button label="未处理" />
        <el-radio-button label="已处理" />
        <el-radio-button label="已超期" />
      </el-radio-group>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table message-table base-table format-height-table"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column label="发件人" prop="senderName" min-width="90px" show-overflow-tooltip>
        <template #default="{ row }">
          <span :class="row.todoStatus === 0 ? 'no-read-weight' : ''">{{ row.senderName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标题" prop="title" min-width="140px" show-overflow-tooltip>
        <template #default="{ row }">
          <span :class="row.todoStatus === 0 ? 'no-read-weight' : ''">{{ row.title || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="待办内容" prop="content" min-width="400px" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.content" :class="row.todoStatus === 0 ? 'no-read-weight' : ''">{{ row.content }}</span>
          <span v-else>--</span>
          <!-- <span class="look-msg" @click="editDetail(row, 1)">>>查看待办</span> -->
        </template>
      </el-table-column>
      <el-table-column label="分组" prop="categoryName" min-width="200px" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.categoryName" :class="row.todoStatus === 0 ? 'no-read-weight' : ''">{{
            row.categoryName
          }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="等级" prop="level" width="100px">
        <template #default="{ row }">
          <el-tag v-if="row.level" size="small" effect="dark" :type="levelStyleClass[row.level]">
            {{ filterLevel(row.level)[0] }}</el-tag
          >
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="150px" sortable>
        <template #default="{ row }">
          <span v-if="row.createTime" :class="row.todoStatus === 0 ? 'no-read-weight' : ''">{{
            formatDate(row.createTime)
          }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="要求完成时间" prop="todoFinishedTime" width="180px" sortable>
        <template #default="{ row }">
          <span v-if="row.todoFinishedTime" :class="row.todoStatus === 0 ? 'no-read-weight' : ''">{{
            formatDate(row.todoFinishedTime)
          }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="todoStatus" width="100px">
        <template #default="{ row }">
          <span :class="row.todoStatus === 0 ? 'no-read-weight' : ''">{{
            filterTodoStatus(row.todoStatus) || '--'
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="140px" prop="caozuo" fixed="right" class-name="fixed-right">
        <template #default="{ row }">
          <span class="blue-color" @click="editDetail(row, 1)">详情</span>
          <span
            v-if="(row.todoStatus === 0 || row.todoStatus === 2) && getPermissionBtn('handleAgencyBtn')"
            class="blue-color"
            @click="editDetail(row, 2)"
            >处理</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      small
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
    <template #other>
      <!-- 消息详情 -->
      <my-message-detail :drawer="showMessageDrawer" :detail="messageDetail" :is-agency="true" @close="closeDetail" />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
import Pagination from '@/components/Pagination';
import { getMessageList, messagegroupListNum, getMessageInfo } from '@/api/messageAgent';
// import { ElMessage, ElMessageBox } from 'element-plus'
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import ListLayout from '@/components/ListLayout';
// import { useStore } from 'vuex'
// import { useRoute } from 'vue-router'
// import router from '@/router/index.js';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import MyMessageDetail from './my-message-detail.vue';
import _ from 'lodash';

export default {
  name: 'MyAgency',
  components: { Pagination, MyMessageDetail, ListLayout },
  setup() {
    const { proxy } = getCurrentInstance();
    // const store = useStore().state
    // const route = useRoute()
    // console.log(route)
    const editFrom = ref(null);
    const datas = reactive({
      currentAccountId: getLoginInfo().accountId,
      activeName: '0',
      showS: false,
      list: [],
      ids: [],
      formInline: {
        messageType: '2',
        groupId: '',
        condition: '',
        level: null,
        startTime: null,
        endTime: null,
        todoStatus: 'all',
        finishStartTime: '',
        finishEndTime: ''
      },
      searchForm: {
        level: '',
        categoryName: '',
        groupId: '',
        createDateRange: [],
        finishTime: []
      },
      listQuery: {
        page: 1,
        limit: 20
      },
      levelStyleClass: {
        0: 'danger',
        1: 'warning',
        2: 'success'
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      radioData: '全部',
      categoryNameOptions: [],
      categoryNametree: [],
      levelOptions: [
        { id: 0, name: '重要' },
        { id: 1, name: '一般' },
        { id: 2, name: '较弱' }
      ],
      showMessageDrawer: false,
      messageDetail: {},
      selectList: []
    });

    // 查询
    function onSubmit() {
      proxy.getList();
    }
    // 重置
    function reset() {
      datas.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        sort: 'DESC'
      };
      datas.searchForm = {
        level: '',
        categoryName: '',
        groupId: '',
        createDateRange: [],
        finishTime: []
      };
      datas.formInline = {
        messageType: '2',
        groupId: '',
        condition: '',
        level: '',
        startTime: null,
        endTime: null,
        todoStatus: 'all'
      };
      datas.radioData = '全部';
      proxy.getList();
    }
    // 高级搜索
    const search = () => {
      datas.showS = !datas.showS;
      if (datas.activeName === '0') {
        datas.activeName = '1';
      } else {
        datas.activeName = '0';
      }
    };

    const sortChange = data => {
      // const { prop, order } = data
      // console.log(prop)
      // console.log(order)
    };

    // 点击详情，查看消息--打开详情抽屉
    const editDetail = (row, flag) => {
      getMessageInfo({ id: row.id, messageType: '2' }).then(res => {
        if (res !== false) {
          datas.messageDetail = res.data.data;
          if (flag !== 2) {
            datas.showMessageDrawer = true;
            // router.push({ name: 'MyAgencyDetail', query: { id: row.id } });
          } else {
            jumpPage(res.data.data);
          }
        }
      });
    };
    // 点击处理跳转页面
    const jumpPage = data => {
      if (data.todomessageEntity.forwardUriList.length > 0) {
        data.todomessageEntity.forwardUriList.forEach(url => {
          const urlName = window.location.origin + url;
          window.location.href = urlName;
        });
      }
    };
    // 关闭详情抽屉
    const closeDetail = v => {
      datas.showMessageDrawer = v;
    };

    // 过滤等级
    const filterLevel = value => {
      const p = {
        0: ['重要', '#f56c6c'],
        1: ['一般', '#67c23a'],
        2: ['较弱', '']
      };
      return p[value];
    };
    // 过滤状态
    const filterTodoStatus = value => {
      const p = {
        0: '未处理',
        1: '已处理',
        2: '已超期',
        all: '全部'
      };
      return p[value];
    };
    // 切换tab
    const changeRadio = value => {
      const param = {
        全部: 'all',
        已超期: '2',
        已处理: '1',
        未处理: '0'
      };
      datas.formInline.todoStatus = param[value];
      proxy.getList();
    };
    // 切换等级
    const changelevel = v => {
      datas.formInline.level = v + '';
    };
    // 切换分组
    const changecategoryName = v => {
      datas.formInline.groupId = v + '';
    };
    // 切换时间
    const changeCreateTime = date => {
      datas.formInline.startTime = date ? formatDate(date[0]) : '';
      datas.formInline.endTime = date ? formatDate(date[1]) : '';
    };
    // 切换时间
    const changeTodoTime = date => {
      datas.formInline.finishStartTime = date ? formatDate(date[0]) : '';
      datas.formInline.finishEndTime = date ? formatDate(date[1]) : '';
    };

    return {
      editDetail,
      drageHeader,
      formatDate,
      getNameByid,
      changeRadio,
      filterLevel,
      sortChange,
      editFrom,
      ...toRefs(datas),
      filterTodoStatus,
      getPermissionBtn,
      search,
      onSubmit,
      reset,
      changelevel,
      changecategoryName,
      changeCreateTime,
      closeDetail,
      changeTodoTime,
      jumpPage
    };
  },
  created() {
    const status = this.$route.query.status;
    if (status) {
      const radioData = this.filterTodoStatus(status);
      if (radioData) {
        this.radioData = radioData;
        this.formInline.readStatus = status;
      }
    }

    this.getList();
    this.messagegroupLists();
    // 刷新列表
    this.bus.$on('reloadMyAgentList', msg => {
      this.getList();
    });
  },
  methods: {
    // 获取待办列表
    getList(data) {
      const _this = this;
      _this.listLoading = true;
      if (data && data !== undefined) {
        _this.listQuery.page = data.page;
        _this.listQuery.limit = data.limit;
      }
      const param = Object.assign(_this.formInline, _this.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      // console.log(param)
      // 列表接口
      getMessageList(param).then(res => {
        // console.log(res.data)
        if (res !== false) {
          const { data } = res.data;
          _this.list = data.list;
          _this.total = data.totalCount;
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
    },
    // 获取分组列表
    messagegroupLists() {
      var that = this;
      const params = {
        messageType: '2'
      };
      that.categoryNameOptions = [];
      messagegroupListNum(params).then(res => {
        if (res !== false) {
          // console.log(res.data.data)
          const allList = res.data.data;
          const list = res.data.data;
          var categoryNametree = [];
          if (allList.length > 0) {
            allList.forEach(opt => {
              if (opt.level === 2) {
                that.categoryNameOptions.push(opt);
              }
              if (opt.parent_id === 0) {
                categoryNametree.push(opt);
              }
              const hasitem = _.filter(list, optl => {
                res.closable = false;
                return optl.parent_id === opt.id;
              });
              if (hasitem.length > 0) {
                opt.childrens = hasitem;
              } else {
                opt.childrens = [];
              }
            });
            const newList = [];
            categoryNametree.forEach((tree, index) => {
              if (tree.childrens.length > 0) {
                tree.childrens.forEach((child, cindex) => {
                  newList.push({ list: [tree] });
                  newList[cindex].list.push(child);
                  newList[cindex].list = newList[cindex].list.concat(child.childrens);
                });
              }
            });
            that.categoryNametree = newList;
            // console.log(newList)
          }
          // console.log(that.categoryNametree)
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.my-agency {
  height: inherit;
  overflow: hidden auto;
  :deep(.el-form-item--medium .el-form-item__content) {
    line-height: 28px;
  }
  :deep(.el-form-item__label) {
    line-height: 28px;
  }
  .message-list {
    width: 100%;
    min-height: 30px;
    .list-contant {
      height: 30px;
      line-height: 30px;
      float: left;
      width: 100%;
      margin-bottom: 10px;
      .item {
        float: left;
        width: 111px;
        border-radius: 4px;
        border: 1px solid #909399;
        margin: 0px 10px 10px 0px;
      }
      .item1 {
        height: 30px;
        float: left;
        width: 111px;
        margin-right: 10px;
      }
    }
  }
  .message-table {
    width: auto;
    .el-table__body-wrapper {
      .look-msg {
        color: $tes-primary;
        cursor: pointer;
      }
    }
  }
  .no-read-weight {
    font-weight: bold;
    color: $tes-font1;
  }
}
</style>
