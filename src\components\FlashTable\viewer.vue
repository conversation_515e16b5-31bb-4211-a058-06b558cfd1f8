<template>
  <!-- flashTable -->
  <div class="h-full">
    <iframe ref="iframeRef" :src="iframeSrc" frameborder="0" width="100%" height="100%" />
  </div>
</template>
<script>
// getCurrentInstance
import { reactive, toRefs, watch, ref, onMounted, onBeforeUnmount } from 'vue';
// Data
import { COMMAND } from '@/data/flashTable';
// Utils
import { getFlashTableToken } from '@/utils/flashTable';
import { getLIMSConfig } from '@/utils/auth';
export default {
  name: 'FlashTableViewer',
  components: {},
  props: {
    option: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  emits: ['receive'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance();
    const state = reactive({
      iframeOrigin: getLIMSConfig().VITE_FLASH_TABLE,
      iframeOption: {},
      iframeSrc: ''
    });
    const iframeRef = ref(null);
    watch(props, newValue => {
      initDetail();
    });
    const initDetail = async () => {
      state.iframeSrc = `${state.iframeOrigin}/viewer?origin=${window.origin}&id=${props.option.id}&name=${
        props.option.name
      }&token=${await getFlashTableToken()}`;
      window.addEventListener('message', handleMessage);
    };
    // 发送指令
    const sendPostMessage = commandValue => {
      const postMessageOption = {
        command: commandValue,
        ...state.iframeOption
      };
      if (iframeRef.value) {
        iframeRef.value.contentWindow.postMessage(JSON.parse(JSON.stringify(postMessageOption)), state.iframeOrigin);
      }
    };
    // 初始化设置
    const initPage = () => {
      state.iframeOption = {
        data: {
          tools: {
            commentEnabled: false,
            traceEnabled: false,
            editButton: false,
            cancelButton: false,
            submitButton: false,
            submitSuccessPage: false
          }
        }
      };
      sendPostMessage(COMMAND.SET_CONFIG);
    };
    const initPage2 = () => {
      state.iframeOption = {
        data: {
          readonly: props.option.readonly
        }
      };
      sendPostMessage(COMMAND.SET_TEMPLATE_STATUS);
    };
    // 获取数据
    const getLocalData = () => {
      sendPostMessage(COMMAND.GET_DATA);
    };
    // 填充模板数据 数据id
    const setLocalData = id => {
      state.iframeOption = {
        data: { id }
      };
      sendPostMessage(COMMAND.LOAD_DATA);
    };
    // 提交数据
    const submitData = () => {
      sendPostMessage(COMMAND.SUBMIT_DATA);
    };
    const getPDF = templateList => {
      state.iframeOption = {
        data: { templateList: templateList }
      };
      sendPostMessage(COMMAND.SUBMIT_DATA);
    };
    // 接收消息
    const handleMessage = event => {
      if (event.origin !== state.iframeOrigin) {
        console.warn('收到来自未授权源的消息:', event.origin);
        return;
      }
      switch (event.data.command) {
        case COMMAND.TEMPLATE_LOADED: // 加载完毕
          initPage2();
          context.emit('receive', { command: COMMAND.TEMPLATE_LOADED, data: event.data });
          break;
        case COMMAND.TEMPLATE_CREATED: // 创建成功
          initPage();
          break;
        case COMMAND.DATA_LOADED:
          break;

        case COMMAND.GET_DATA:
          break;

        case COMMAND.SUBMIT_CALLBACK: // 保存返回
          context.emit('receive', { command: COMMAND.SUBMIT_CALLBACK, data: event.data });
          break;

        case COMMAND.BACK:
          break;

        case COMMAND.SAVE_TEMPLATE:
          break;

        case COMMAND.PUBLISH_TEMPLATE:
          break;
      }
    };
    // 组件挂载时添加消息监听
    onMounted(() => {
      // initDetail();
    });

    // 组件卸载前移除消息监听
    onBeforeUnmount(() => {
      console.log('消息监听关闭');
      window.removeEventListener('message', handleMessage);
    });
    return {
      ...toRefs(state),
      sendPostMessage,
      setLocalData,
      getLocalData,
      submitData,
      iframeRef,
      getPDF
    };
  }
};
</script>
<style lang="scss" scoped></style>
