<template>
  <el-dialog
    v-model="dialogVisible"
    title="制作小样"
    width="950px"
    :close-on-click-modal="false"
    custom-class="imgDialog"
    @close="closeDialog"
  >
    <el-form
      v-model="formDataInfo"
      v-loading="dialogLoading"
      size="small"
      label-position="right"
      label-width="80px"
      class="dialog-form form-info"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="样品编号：" prop="secSampleNum">
            <span>{{ formDataInfo.secSampleNum || '--' }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="样品名称：" prop="samplesName">
            <span>{{ formDataInfo.samplesName || '--' }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="型号规格：" prop="prodType">
            <span>{{ formDataInfo.prodType || '--' }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div v-if="tableFieldList.length" class="btn-group">
      <el-button
        v-if="formData.tableDataList.length && !isEdit && !isAdd"
        icon="el-icon-edit"
        :loading="dialogLoading"
        @click="handleEdit()"
        >编辑</el-button
      >
      <el-button v-if="isEdit" icon="el-icon-close" :loading="dialogLoading" @click="cacleEdit()">取消编辑</el-button>
      <el-button v-if="!isEdit" type="primary" icon="el-icon-plus" :loading="dialogLoading" @click="handleAdd()"
        >新增</el-button
      >
    </div>
    <el-form ref="ruleFormTable" :model="formData" :rules="rules" size="small">
      <el-table
        v-loading="dialogLoading"
        :data="formData.tableDataList"
        fit
        border
        height="auto"
        highlight-current-row
        class="detail-table dark-table base-table format-height-table2"
      >
        <el-table-column
          v-for="item in tableFieldList"
          :key="item.id"
          :prop="item.id"
          :min-width="180"
          :label="item.fieldName"
          show-overflow-tooltip
        >
          <template #default="{ row, $index }">
            <!-- 文本框 -->
            <el-form-item
              v-if="item.fieldType === '0'"
              :prop="`tableDataList.${$index}.tableInfo.${item.id}`"
              :rules="{
                required: item.isRequired === 1 && ((isEdit && row.isOld) || (isAdd && !row.isOld)),
                message: `请输入${item.fieldName}`,
                tigger: 'blur'
              }"
              style="margin-bottom: 0"
            >
              <el-input
                v-if="(isEdit && row.isOld) || (isAdd && !row.isOld)"
                v-model="row.tableInfo[item.id]"
                maxlength="300"
                :placeholder="item.fieldName"
              />
              <span v-else>{{ row.tableInfo[item.id] || '--' }}</span>
            </el-form-item>
            <!-- 数字框 -->
            <el-form-item
              v-if="item.fieldType === '1'"
              :prop="`tableDataList.${$index}.tableInfo.${item.id}`"
              :rules="{
                validator: isNumber,
                isRequire: item.isRequired === 1 && ((isEdit && row.isOld) || (isAdd && !row.isOld)),
                fieldName: item.fieldName,
                trigger: 'blur'
              }"
              style="margin-bottom: 0"
            >
              <el-input
                v-if="(isEdit && row.isOld) || (isAdd && !row.isOld)"
                v-model="row.tableInfo[item.id]"
                maxlength="30"
                :placeholder="item.fieldName"
              />
              <span v-else>{{ row.tableInfo[item.id] || '--' }}</span>
            </el-form-item>
            <!-- 日期框 -->
            <el-form-item
              v-if="item.fieldType === '2'"
              :prop="`tableDataList.${$index}.tableInfo.${item.id}`"
              :rules="{
                validator: isDate,
                isRequire: item.isRequired === 1 && ((isEdit && row.isOld) || (isAdd && !row.isOld)),
                fieldName: item.fieldName,
                trigger: 'change'
              }"
              style="margin-bottom: 0"
            >
              <el-date-picker
                v-if="(isEdit && row.isOld) || (isAdd && !row.isOld)"
                v-model="row.tableInfo[item.id]"
                type="date"
                :placeholder="item.fieldName"
                style="width: 100%"
              />
              <span v-else>{{ formatDate(row.tableInfo[item.id]) || '--' }}</span>
            </el-form-item>
            <!-- 字典框 -->
            <el-form-item
              v-if="item.fieldType === '3'"
              :prop="`tableDataList.${$index}.tableInfo.${item.id}`"
              :rules="{
                required: item.isRequired === 1 && ((isEdit && row.isOld) || (isAdd && !row.isOld)),
                message: `请选择${item.fieldName}`,
                tigger: 'blur'
              }"
              style="margin-bottom: 0"
            >
              <el-select
                v-if="(isEdit && row.isOld) || (isAdd && !row.isOld)"
                v-model="row.tableInfo[item.id]"
                filterable
                size="small"
                clearable
                :placeholder="item.fieldName"
                style="width: 100%"
              >
                <el-option
                  v-for="val in dictionaryCodeArray[item.dictionaryCode]"
                  :key="val.value"
                  :label="val.name"
                  :value="val.code"
                />
              </el-select>
              <span v-else>{{
                dictionaryCodeArray?.[item.dictionaryCode]?.[row.tableInfo[item.id]]?.name || '--'
              }}</span>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column v-if="isAdd || isEdit" label="操作" :min-width="colWidth.operationSingle" fixed="right">
          <template #default="{ row, $index }">
            <span v-if="!row.isOld && isAdd" class="blue-color" @click="handleDelete($index)">删除</span>
            <span v-if="row.isOld && isEdit" class="blue-color" @click="handleDeleteOld($index)">删除</span>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <template #footer>
      <div v-if="tableFieldList.length && (isEdit || isAdd)" class="dialog-footer">
        <el-button :loading="dialogLoading" @click="closeDialog">取 消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
          >确 认</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, getCurrentInstance, watch, ref } from 'vue';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { findUseSampleSupplementaryConfig, saveOrUpdate, findBySampleId } from '@/api/execution';
import { getDictionary } from '@/api/user';
import { formatDate } from '@/utils/formatTime';
export default {
  name: 'DialogMakeSample',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    sampleId: {
      type: String,
      default: ''
    },
    materialCategoryCode: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      formData: {
        tableDataList: []
      },
      rules: {},
      formDataInfo: {},
      oldTableData: [],
      fieldTypeJSON: {
        0: '文本',
        1: '数字',
        2: '日期',
        3: '字典'
      },
      ruleFormTable: ref(),
      tableFieldList: [],
      isAdd: false,
      isEdit: false,
      dictionaryCodeArray: {}, // 用到的字典集合
      dialogLoading: false,
      dialogVisible: false
    });
    watch(props, newValue => {
      state.dialogVisible = newValue.show;
      if (state.dialogVisible) {
        state.isAdd = false;
        state.isEdit = false;
        state.oldTableData = [];
        state.formDataInfo = props.data;
        state.formData = {
          materialCategoryCode: props.materialCategoryCode,
          configType: 1,
          sampleId: props.sampleId,
          tableDataList: []
        };
        getList();
      }
    });
    const isNumber = (rule, val, callback) => {
      if (rule.isRequire && !val && val !== 0 && val !== '0') {
        // 必填
        callback(new Error(`请输入${rule.fieldName}`));
      } else if (val !== undefined) {
        // 只要有值就判断数字类型
        if (Number(val).toString() === 'NaN' || val?.substring(0, 1) === '.') {
          callback(new Error(`请输入数字`));
        } else if (Number(val) < 0) {
          callback(new Error(`请输入非负数`));
        } else {
          callback();
        }
      } else {
        // 不必填无值可通过校验
        callback();
      }
    };
    const isDate = (rule, val, callback) => {
      if (rule.isRequire && (val === undefined || val === null)) {
        callback(new Error(`请选择${rule.fieldName}`));
      } else {
        callback();
      }
    };
    const getList = () => {
      state.dialogLoading = true;
      findUseSampleSupplementaryConfig(props.materialCategoryCode, '1').then(res => {
        state.dialogLoading = false;
        if (res) {
          state.tableFieldList = [];
          res.data.data.forEach(item => {
            state.tableFieldList.push({ ...item, fieldId: item.id });
          });
          getDetail();
          const dicArray = [
            ...new Set(
              state.tableFieldList.map(item => {
                return item.dictionaryCode;
              })
            )
          ];
          getDictionaryAll(dicArray);
        }
      });
    };
    const getDetail = () => {
      state.dialogLoading = true;
      state.formData.tableDataList = [];
      state.oldTableData = [];
      findBySampleId(props.sampleId, '1').then(res => {
        state.dialogLoading = false;
        if (res) {
          res.data.data.tableDataList.forEach(item => {
            state.formData.tableDataList.push({ ...item, isOld: true });
          });
          state.oldTableData = JSON.parse(JSON.stringify(state.formData.tableDataList));
        }
      });
    };
    const getDictionaryAll = codes => {
      codes.forEach(item => {
        if (item) {
          getDictionaryCode(item);
        }
      });
    };
    const handleAdd = () => {
      state.formData.tableDataList.push({ tableInfo: {} });
      state.isAdd = true;
    };
    const getDictionaryCode = item => {
      state.dialogLoading = true;
      getDictionary(item).then(res => {
        state.dialogLoading = false;
        if (res) {
          state.dictionaryCodeArray[item] = {};
          res.data.data.dictionaryoption.forEach(val => {
            if (val.status === 1) {
              state.dictionaryCodeArray[item][val.code] = val;
            }
          });
        }
      });
    };
    const onSubmit = () => {
      state.ruleFormTable
        .validate()
        .then(valid => {
          if (valid) {
            const params = {
              ...state.formData,
              tableHeader: state.tableFieldList
            };
            state.dialogLoading = true;
            saveOrUpdate(params).then(res => {
              state.dialogLoading = false;
              if (res) {
                proxy.$message.success('保存成功！');
                closeDialog();
              }
            });
          } else {
            return false;
          }
        })
        .catch(error => {
          const fieldName = Object.keys(error)[0];
          proxy.$message.warning(error[fieldName][0].message);
          return false;
        });
    };
    const closeDialog = () => {
      state.dialogVisible = false;
      context.emit('closeDialog', false);
    };
    // 删除行数据
    const handleDelete = index => {
      state.formData.tableDataList.splice(index, 1);
      if (state.formData.tableDataList.length === state.oldTableData.length) {
        state.isAdd = false;
      }
    };
    // 删除已经新增过的数据
    const handleDeleteOld = index => {
      state.formData.tableDataList.splice(index, 1);
    };
    // 编辑
    const handleEdit = () => {
      state.isEdit = true;
    };
    // 取消编辑
    const cacleEdit = () => {
      state.isEdit = false;
      getDetail();
      // state.formData.tableDataList = JSON.parse(JSON.stringify(state.oldTableData))
    };
    return {
      ...toRefs(state),
      drageHeader,
      handleEdit,
      handleDeleteOld,
      cacleEdit,
      isNumber,
      isDate,
      handleAdd,
      formatDate,
      colWidth,
      handleDelete,
      closeDialog,
      onSubmit
    };
  }
};
</script>

<style lang="scss" scoped>
:deep(.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 37.5rem) !important;
    overflow-y: auto;
  }
}
.btn-group {
  text-align: right;
  margin-bottom: 10px;
}
.form-info {
  background-color: #f0f2f5;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
}
.dialog-form .el-form-item {
  margin-bottom: 0;
}
</style>
