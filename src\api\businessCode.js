import request from '@/utils/request';

// 业务编码查询列表
export function getBusinessCodeList(data) {
  return request({
    url: `/api-user/rule/rule/list`,
    method: 'post',
    data
  });
}
// 检测单据是否唯一
export function checkCount(data) {
  return request({
    url: '/api-user/rule/rule/count',
    method: 'post',
    data
  });
}
// 新增业务编码
export function addRuleCode(data) {
  return request({
    url: '/api-user/rule/rule/save',
    method: 'post',
    data
  });
}
// 编辑业务编码
export function updateRuleCode(data) {
  return request({
    url: '/api-user/rule/rule/update',
    method: 'post',
    data
  });
}
// 业务编码详情
export function getDetail(data) {
  return request({
    url: '/api-user/rule/rule/find',
    method: 'post',
    data
  });
}
// 根据Id查询rule信息
export function getGenerator(id) {
  return request({
    url: `/api-user/rule/rule/idgenerator/${id}`,
    method: 'get'
  });
}
