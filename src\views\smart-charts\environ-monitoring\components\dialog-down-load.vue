<template>
  <el-dialog
    v-model="dialogVisiable"
    title="导出数据"
    :width="600"
    :close-on-click-modal="false"
    custom-class="dialog-table"
    @close="closedialog"
  >
    <div v-loading="dialogLoading" class="flex flex-row justify-center items-center">
      <span class="label">日期范围：</span>
      <el-date-picker
        v-model="timeRanges"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="medium" @click="closedialog()">取 消</el-button>
        <el-button
          :loading="dialogLoading"
          size="medium"
          type="primary"
          @click="handleDownLoad"
          @keyup.prevent
          @keydown.enter.prevent
          >导 出</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch } from 'vue';
// data
import { colWidth } from '@/data/tableStyle';
import { ElMessage } from 'element-plus';
// api
import { exportRdsData } from '@/api/environ-monitoring';
// utils
import { getNameByid } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';

export default {
  name: 'DialogDownLoad',
  components: {},
  props: {
    dialogShow: {
      type: Boolean,
      default: function () {
        return false;
      }
    },
    detailInfo: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      dialogVisiable: false,
      timeRanges: [],
      selectTable: [],
      dialogLoading: false
    });
    watch(props, newValue => {
      state.dialogVisiable = newValue.dialogShow;
      if (state.dialogVisiable) {
        state.timeRanges = [];
      }
    });
    // 取消
    const closedialog = () => {
      state.dialogVisiable = false;
      context.emit('closeDialog', false);
    };

    const handleDownLoad = async () => {
      if (!state.timeRanges.length) {
        ElMessage.warning('请选择日期范围');
        return;
      }
      const params = {
        deviceId: props.detailInfo.deviceId,
        deviceNumber: props.detailInfo.deviceNumber,
        startDate: `${formatDate(state.timeRanges[0])} 00:00:00`,
        endDate: `${formatDate(state.timeRanges[1])} 00:00:00`
      };
      const { data } = await exportRdsData(params).finally((state.dialogLoading = false));
      if (data) {
        const reader = new FileReader();
        reader.addEventListener('loadend', () => {
          try {
            const resdata = JSON.parse(reader.result);
            if (resdata.code === 400) {
              ElMessage({
                message: resdata.message,
                type: 'error',
                duration: 3000
              });
            }
          } catch (error) {
            var fileName = `${props.detailInfo.deviceName}.xlsx`;
            var downloadElement = document.createElement('a');
            var href = window.URL.createObjectURL(data);
            downloadElement.style.display = 'none';
            downloadElement.href = href;
            downloadElement.download = decodeURI(fileName);
            document.body.appendChild(downloadElement);
            downloadElement.click();
            document.body.removeChild(downloadElement);
            window.URL.revokeObjectURL(href);
          }
        });
        reader.readAsText(data, 'utf-8');
      }
    };

    return {
      ...toRefs(state),
      props,
      handleDownLoad,
      getNameByid,
      closedialog,
      colWidth
    };
  }
};
</script>

<style lang="scss" scoped>
.dialog-table {
  .selectBh {
    width: 60%;
  }
}
</style>
