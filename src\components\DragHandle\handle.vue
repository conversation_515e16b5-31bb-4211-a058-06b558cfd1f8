<template>
  <div class="x-handle relative" unselectable="on" onselectstart="return false" @mousedown="mouseDown" @click="showout">
    <div class="grab-handle" :style="{ 'background-image': 'url(' + grabImage + ')' }" />
    <!-- 添加hub遮挡住iframe，防止遇到iframe时无法正确触发mouseup事件 -->
    <div v-if="isdragging" class="w-[1500px] absolute h-full top-0 ml-4" />
  </div>
</template>

<script>
import { toRefs, reactive } from 'vue';
import grabImage from '@/assets/img/grab.svg';

export default {
  name: 'DragHandle',
  emits: ['showoutWidth', 'widthChange'],
  setup(props, context) {
    const data = reactive({
      lastX: '',
      isdragging: false
    });
    return { ...toRefs(data), grabImage };
  },
  created() {
    document.addEventListener('mouseup', this.mouseUp);
  },

  unmounted() {
    document.removeEventListener('mouseup', this.mouseUp);
  },

  methods: {
    mouseDown(event) {
      this.isdragging = true;
      document.addEventListener('mousemove', this.mouseMove);
      this.lastX = event.screenX;
    },
    mouseMove(event) {
      this.$emit('widthChange', this.lastX - event.screenX);
      this.lastX = event.screenX;
    },
    mouseUp() {
      this.isdragging = false;
      this.lastX = '';
      document.removeEventListener('mousemove', this.mouseMove);
    },
    showout(res) {
      this.$emit('showoutWidth', 0);
    }
  }
};
</script>
<style lang="scss" scoped>
.x-handle {
  margin: 0 5px;
  padding: 0;
  height: auto;
  cursor: col-resize;
  z-index: 10;
  color: #909399;
  // i {
  //   font-size: 14px;
  // }
}
.grab-handle {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 10px;
  display: block;
  background-position: center;
  background-repeat: no-repeat;
}
</style>
