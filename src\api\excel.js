import request from '@/utils/request';

// 保存模板
export function savecapabilitytemplate(data) {
  return request({
    url: '/api-capabilitystd/capability/capabilitytemplate/save',
    method: 'post',
    data
  });
}
// 修改检测项目模板信息
export function updatecapabilitytemplate(data) {
  return request({
    url: '/api-capabilitystd/capability/capabilitytemplate/update',
    method: 'post',
    data
  });
}
// 根据检测项目获取模板信息
export function getByCapabilityId(capabilityId) {
  return request({
    url: '/api-capabilitystd/capability/capabilitytemplate/findAllByCapabilityId/' + capabilityId,
    method: 'get'
    // params: capabilityId
  });
}
// 根据Id查询检测项目模板信息
export function capabilitytemplate(id) {
  return request({
    url: '/api-capabilitystd/capability/capabilitytemplate/info/' + id,
    method: 'get'
    // params: capabilityId
  });
}

// 附件下载
export function downloadExcel(data) {
  return request({
    url: '/api-capabilitystd/capability/attachment/download/',
    method: 'get',
    params: data
  });
}
// 批量打印
export function batchPrint(data) {
  return request({
    url: '/api-orders/orders/experiment/experimentdata/batchPrint',
    method: 'post',
    data
  });
}
// 检测地点
export function detectionaddress(data) {
  return request({
    url: '/api-capabilitystd/standard/detectionaddress/list',
    method: 'post',
    data
  });
}

/** 获取模板引擎模板信息 */
export function queryReleasedTemplates(capabilityId) {
  return request({
    url: `/api-capabilitystd/capability/designTemplate/capability/${capabilityId}`,
    method: 'get'
  });
}

/** 获取模板引擎未发布模板信息 */
export function queryUnreleasedTemplates(capabilityId) {
  return request({
    url: `/api-capabilitystd/capability/designTemplate/unreleasedTemplate/${capabilityId}`,
    method: 'get'
  });
}
// // 根据Id查询检测项目信息
// export function getCapabilityById(id) {
//   return request({
//     url: `/api-capabilitystd/capability/capability/info/${id}`,
//     method: 'get'
//   })
// }
// // 保存检测项目信息
// export function saveCapability(data) {
//   return request({
//     url: '/api-capabilitystd/capability/capability/save',
//     method: 'post',
//     data
//   })
// }
