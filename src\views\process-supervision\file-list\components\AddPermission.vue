<template>
  <el-dialog
    v-model="showDialog"
    custom-class="add-permission"
    title="添加权限"
    width="50%"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <el-tabs v-model="activeName">
      <el-tab-pane label="部门" name="first">
        <el-tree
          ref="departmentTreeRef"
          :data="departmentTree"
          node-key="id"
          :props="defaultDepartmentProps"
          default-expand-all
          :expand-on-click-node="false"
          :default-checked-keys="defaultDepartmentKeys"
          :highlight-current="true"
          class="tree-contant"
          show-checkbox
          @check-change="changeDepartmentNode"
        >
          <template #default="{ node }">
            <span>{{ node.label }}</span>
          </template>
        </el-tree>
      </el-tab-pane>
      <el-tab-pane label="角色" name="second">
        <div class="role">
          <div v-for="role in roleList" :key="role" class="role-list">
            <span @click="clickRole(role)">{{ role.name }}</span>
            <el-checkbox v-model="role.checked" @change="clickRole(role, 2)" />
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="员工" name="third">
        <el-tree
          ref="accountTreeRef"
          :data="accountTree"
          node-key="id"
          :props="defaultDepartmentProps"
          default-expand-all
          :expand-on-click-node="false"
          :highlight-current="true"
          class="tree-contant"
          @node-click="clickAccountNode"
        >
          <template #default="{ node }">
            <span>{{ node.label }}</span>
          </template>
        </el-tree>
        <div class="account">
          <div v-for="account in accountList" :key="account" class="account-list">
            <span @click="clickAccount(account)">{{ account.nickname }}</span>
            <el-checkbox v-model="account.checked" @change="clickAccount(account, 2)" />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <el-row>
      <el-col :span="24">
        <div class="select-title">
          <label>已选项目</label>
          <el-button size="small" @click="clear">清空</el-button>
        </div>
        <div class="select-items">
          <el-tag v-for="tag in tags" :key="tag.permissionGroupName" closable type="info" @close="closeTag(tag)">
            {{ tag.permissionGroupName || tag.permissionGroupId }}
          </el-tag>
        </div>
      </el-col>
    </el-row>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent>确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, getCurrentInstance, ref } from 'vue';
import { getRoleTree } from '@/api/roleManage';
import { getDepartTree, getMemberTable } from '@/api/departManagement';
import _ from 'lodash';
// import { ElMessage } from 'element-plus'
// import { useRoute } from 'vue-router'
import { formatTree } from '@/utils/formatJson';

export default {
  name: 'AddPermission',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    permissionData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'setInfo'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const datas = reactive({
      showDialog: false,
      options: [],
      permissionDetial: {},
      activeName: 'first',
      departmentTreeRef: ref(),
      departmentTree: [],
      defaultDepartmentProps: {
        children: 'children',
        label: 'name'
      },
      defaultDepartmentKeys: [],
      oldTags: [],
      tags: [],
      roleList: [],
      accountTree: [],
      accountList: []
    });

    watch(
      () => props.show,
      newValue => {
        if (newValue) {
          datas.showDialog = newValue;
          datas.tags = props.permissionData;
          datas.defaultDepartmentKeys = [];
          datas.roleList.forEach(role => {
            role.checked = false;
          });
          datas.accountList.forEach(role => {
            role.checked = false;
          });
          if (datas.tags.length > 0) {
            datas.tags.forEach(tag => {
              if (tag.permissionGroupType === 1) {
                datas.defaultDepartmentKeys.push(tag.permissionGroupId);
              }
              if (tag.permissionGroupType === 2) {
                datas.roleList.forEach(role => {
                  if (tag.permissionGroupId === role.id) {
                    role.checked = true;
                  }
                });
              }
              if (tag.permissionGroupType === 3) {
                datas.accountList.forEach(user => {
                  if (tag.permissionGroupId === user.id) {
                    user.checked = true;
                  }
                });
              }
            });
          }
        }
      },
      { deep: true }
    );

    // 确定选择
    const dialogSuccess = () => {
      datas.showDialog = false;
      context.emit('setInfo', datas.tags);
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };
    // 选择树-部门
    const changeDepartmentNode = (data, checked) => {
      if (checked) {
        const param = {
          permissionGroupId: data.id,
          permissionGroupName: data.name,
          permissionGroupType: 1
        };
        datas.tags.push(param);
      } else {
        _.remove(datas.tags, function (n) {
          return n.permissionGroupId === data.id;
        });
      }
    };
    // 关闭tag
    const closeTag = tag => {
      _.remove(datas.tags, function (n) {
        return n.permissionGroupId === tag.permissionGroupId;
      });
      if (tag.permissionGroupType === 1) {
        _.remove(datas.defaultDepartmentKeys, function (n) {
          return n === tag.permissionGroupId;
        });
        datas.departmentTreeRef.setChecked(tag.permissionGroupId, false, false);
      }
      if (tag.permissionGroupType === 2) {
        datas.roleList.forEach(role => {
          if (tag.permissionGroupId === role.id) {
            role.checked = false;
          }
        });
      }
      if (tag.permissionGroupType === 3) {
        datas.accountList.forEach(user => {
          if (tag.permissionGroupId === user.id) {
            user.checked = false;
          }
        });
      }
    };
    // 清空tag
    const clear = () => {
      datas.tags = [];
    };
    // 角色-click
    const clickRole = (role, flag) => {
      if (flag !== 2) {
        role.checked = !role.checked;
      }
      if (role.checked === false) {
        _.remove(datas.tags, function (n) {
          return n.permissionGroupId === role.id;
        });
      } else {
        const param = {
          permissionGroupId: role.id,
          permissionGroupName: role.name,
          permissionGroupType: 2
        };
        datas.tags.push(param);
      }
    };
    // 点击部门树
    const clickAccountNode = data => {
      proxy.getDepartmentEmployees(data.id);
    };
    // 人员-click
    const clickAccount = (account, flag) => {
      if (flag !== 2) {
        account.checked = !account.checked;
      }
      if (account.checked === false) {
        _.remove(datas.tags, function (n) {
          return n.permissionGroupId === account.id;
        });
      } else {
        const param = {
          permissionGroupId: account.id,
          permissionGroupName: account.nickname,
          permissionGroupType: 3
        };
        datas.tags.push(param);
      }
    };

    return {
      ...toRefs(datas),
      dialogSuccess,
      close,
      changeDepartmentNode,
      closeTag,
      clear,
      clickRole,
      clickAccountNode,
      clickAccount
    };
  },
  created() {
    this.getDepartments();
    this.getRoles();
  },
  methods: {
    // 部门列表
    getDepartments() {
      var that = this;
      getDepartTree({}).then(res => {
        if (res !== false) {
          that.departmentTree = formatTree(res.data.data);
          that.accountTree = formatTree(res.data.data);
          if (that.departmentTree[0]) {
            that.getDepartmentEmployees(that.departmentTree[0].id);
          }
          that.defaultDepartmentKeys = [];
        }
      });
    },
    // 获取人员列表，接口3连调
    getDepartmentEmployees(departmentId) {
      var that = this;
      const param = {
        departmentId: departmentId,
        condition: '',
        page: '1',
        limit: '9999'
      };
      getMemberTable(param).then(res => {
        if (res !== false) {
          that.accountList = res.data.data.list;
          if (that.accountList.length > 0) {
            that.accountList.forEach(user => {
              if (that.tags.length > 0) {
                const items = _.filter(that.tags, function (acc) {
                  return acc.permissionGroupId === user.id;
                });
                if (items.length > 0) {
                  user.checked = true;
                } else {
                  user.checked = false;
                }
              } else {
                user.checked = false;
              }
            });
          }
        }
      });
      // getDepartmentEmployee(departmentId).then(res => {
      //   if (res !== false) {
      //     // console.log(res.data.items)
      //     const employeeByIds = []
      //     if (res.data.items && res.data.items.length > 0) {
      //       res.data.items.forEach(item => {
      //         employeeByIds.push(item.employeeId)
      //       })
      //     }
      //     if (employeeByIds.length > 0) {
      //       getEmployeeByIds(employeeByIds.join(',')).then(res1 => {
      //         if (res1 !== false) {
      //           const accountIds = []
      //           if (res1.data && res1.data.length > 0) {
      //             res1.data.forEach(account => {
      //               accountIds.push(account.accountId)
      //             })
      //           }
      //           if (accountIds.length > 0) {
      //             getAccountByIds(accountIds.join(',')).then(res2 => {
      //               if (res2 !== false) {
      //                 // console.log(res2.data)
      //                 that.accountList = res2.data
      //                 if (that.accountList.length > 0) {
      //                   that.accountList.forEach(user => {
      //                     if (that.tags.length > 0) {
      //                       const items = _.filter(that.tags, function(acc) {
      //                         return acc.permissionGroupId === user.id
      //                       })
      //                       if (items.length > 0) {
      //                         user.checked = true
      //                       } else {
      //                         user.checked = false
      //                       }
      //                     } else {
      //                       user.checked = false
      //                     }
      //                   })
      //                 }
      //               }
      //             })
      //           }
      //         }
      //       })
      //     }
      //   }
      // })
    },
    // 获取角色列表
    getRoles() {
      var that = this;
      getRoleTree({}).then(res => {
        if (res !== false) {
          that.roleList = res.data.data;
          if (that.roleList.length > 0) {
            that.roleList.forEach(role => {
              role.checked = false;
            });
          }
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.add-permission {
  .role {
    max-height: 400px;
    overflow: auto;
    .role-list {
      height: 25px;
      line-height: 25px;
      cursor: pointer;
      padding-right: 15px;
      display: flex;
      justify-content: space-between;
      span {
        display: block;
        width: 100%;
      }
      &:hover {
        background: $tes-primary2;
      }
      .el-checkbox {
        float: right;
      }
    }
  }
  .el-tree {
    width: 40%;
    float: left;
    padding-right: 15px;
  }
  .account {
    width: 60%;
    float: right;
    max-height: 400px;
    overflow: auto;
    border: 1px solid $tes-border3;
    .account-list {
      height: 25px;
      line-height: 25px;
      cursor: pointer;
      padding-right: 15px;
      padding-left: 15px;
      display: flex;
      justify-content: space-between;
      span {
        display: block;
        width: 100%;
      }
      &:hover {
        background: $tes-primary2;
      }
      .el-checkbox {
        float: right;
      }
    }
  }
  .select-title {
    width: 100%;
    height: 50px;
    line-height: 50px;
    .el-button {
      float: right;
      margin-top: 10px;
    }
  }
  .select-items {
    height: 124px;
    padding: 4px 4px 2px 4px;
    border: 1px solid #e4e7ed;
    box-sizing: border-box;
    overflow: auto;
    border-radius: 4px;
    .el-tag {
      margin: 5px;
    }
  }
}
</style>
