<template>
  <!-- 体系记录详情 -->
  <DetailLayout v-loading="detailLoading" :main-offset-top="90">
    <template #page-header>
      <div class="header-flex justify-between">
        <div class="header-title">记录编号：{{ formData.code || '保存之后自动生成' }}</div>
        <div class="btn-group">
          <el-button
            :loading="detailLoading"
            size="large"
            icon="el-icon-back"
            @click="handleCancle()"
            @keyup.prevent
            @keydown.enter.prevent
            >返回</el-button
          >
          <el-button
            v-if="isEdit"
            :loading="detailLoading"
            type="primary"
            size="large"
            icon="el-icon-receiving"
            @click="handleSave('save')"
            @keyup.prevent
            @keydown.enter.prevent
            >保存</el-button
          >
          <el-button
            v-if="isEdit"
            :loading="detailLoading"
            type="primary"
            size="large"
            icon="el-icon-position"
            @click="handleSave('submit')"
            @keyup.prevent
            @keydown.enter.prevent
            >保存并提交</el-button
          >
        </div>
      </div>
    </template>
    <div class="page-main">
      <div class="panel-headline">
        <div class="title">基本信息</div>
      </div>
      <div class="panel-content">
        <el-form
          ref="formRef"
          :model="formData"
          :class="isEdit ? 'editForm' : ''"
          label-position="right"
          label-width="110px"
        >
          <el-row :gutter="20">
            <el-col v-for="item in pageViewGroup['info']" :key="item.fieldKey" :span="Number(item.columnWidth)">
              <el-form-item
                :label="`${item.fieldName}：`"
                :prop="item.fieldKey"
                :rules="{
                  required: item.fieldKey == 'name' || item.fieldKey == 'designTemplateId',
                  message: `请${item.fieldType == 'text' ? '输入' : '选择'}${item.fieldName}`,
                  trigger: 'change'
                }"
              >
                <!-- 输入框 -->
                <div v-if="item.fieldType == 'text'">
                  <el-input
                    v-if="isEdit"
                    v-model="formData[item.fieldKey]"
                    v-trim
                    max-length="100"
                    :placeholder="`请输入${item.fieldName}`"
                  />
                  <div v-else>{{ formData[item.fieldKey] || '--' }}</div>
                </div>
                <!-- 日期 -->
                <div v-else-if="item.fieldType == 'date'">
                  <el-date-picker
                    v-if="isEdit"
                    v-model="formData[item.fieldKey]"
                    type="date"
                    :placeholder="`请选择${item.fieldName}`"
                    style="width: 100%"
                    @change="
                      val => {
                        return handleChangeDate(val, item.fieldKey);
                      }
                    "
                  />
                  <div v-else>{{ formData[item.fieldKey] || '--' }}</div>
                </div>
                <!-- 人员 -->
                <div v-else-if="item.fieldType == 'person'">
                  <div v-if="item.fieldKey == 'reviewer'">
                    <el-select
                      v-if="isEdit"
                      v-model="formData[item.fieldKey]"
                      placeholder="请选择审批人"
                      clearable
                      filterable
                      style="width: 100%"
                      @change="handleChangeReviewer"
                    >
                      <el-option
                        v-for="userItem in userOptions"
                        :key="userItem.id"
                        :label="userItem.name"
                        :value="userItem.id"
                      />
                    </el-select>
                    <UserTag v-else :name="getNameByid(formData[item.fieldKey]) || formData[item.fieldKey] || '--'" />
                  </div>
                  <div v-if="item.fieldKey == 'approval'">
                    <el-select
                      v-if="isEdit"
                      v-model="formData[item.fieldKey]"
                      :placeholder="formData.reviewer ? `请选择${item.fieldName}` : '请先选择审批人'"
                      clearable
                      :disabled="!formData.reviewer"
                      filterable
                      style="width: 100%"
                    >
                      <el-option
                        v-for="userItem in userOptions"
                        :key="userItem.id"
                        :label="userItem.name"
                        :value="userItem.id"
                      />
                    </el-select>
                    <UserTag v-else :name="getNameByid(formData[item.fieldKey]) || formData[item.fieldKey] || '--'" />
                  </div>
                </div>
                <!-- 类型 -->
                <div v-else-if="item.fieldType == 'custom'">
                  <template v-if="item.fieldKey == 'designTemplateType'">
                    <el-select
                      v-if="isEdit"
                      v-model="formData[item.fieldKey]"
                      placeholder="请先选择模板"
                      clearable
                      disabled
                      filterable
                      style="width: 100%"
                    >
                      <el-option v-for="(val, key) in templateTypeJSON" :key="key" :label="val" :value="key" />
                    </el-select>
                    <div v-else>{{ templateTypeJSON[formData[item.fieldKey]] || formData[item.fieldKey] || '--' }}</div>
                  </template>
                  <template v-if="item.fieldKey == 'designTemplateId'">
                    <el-select
                      v-if="isEdit"
                      v-model="formData[item.fieldKey]"
                      placeholder="请先选择模板"
                      clearable
                      filterable
                      style="width: 100%"
                      @change="handleChangeTemplate"
                    >
                      <el-option v-for="(val, key) in templateAll" :key="key" :label="val.name" :value="key" />
                    </el-select>
                    <div v-else>
                      {{ templateAll[formData[item.fieldKey]]?.name || formData[item.fieldKey] || '--' }}
                    </div>
                  </template>
                  <template v-if="item.fieldKey == 'file'">
                    <el-upload
                      :file-list="attachmentList"
                      :action="uploadAction"
                      :headers="headerconfig"
                      :accept="accept"
                      :auto-upload="true"
                      :before-upload="beforeUpload"
                      :on-success="handleFileSuccess"
                      :on-preview="handlePreview"
                      :on-remove="handleRemove"
                    >
                      <el-button type="primary" size="small">上传附件</el-button>
                    </el-upload>
                  </template>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-divider />
      <div class="panel-headline">
        <div class="title">体系记录</div>
      </div>
      <!-- 模板 -->
      <div :class="isEdit ? 'flash-table' : 'flash-table-check'">
        <FlashTableViewer
          v-if="formData.designTemplateId"
          ref="flashViewRef"
          :option="templateOption"
          @receive="handleReceive"
        />
        <div v-else class="no-data flex h-full flex-col justify-center items-center">
          <img src="@/assets/img/empty-data.png" alt="" style="width: 200px" />
          <span class="no-data-title">请先选择模板</span>
        </div>
      </div>
    </div>
  </DetailLayout>
</template>

<script>
// Basic
import { reactive, toRefs, ref, onMounted, nextTick } from 'vue';
import { useStore } from 'vuex';
import router from '@/router/index.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRoute } from 'vue-router';

// Utils
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { formatViewData } from '@/utils/formatJson';
import { getLoginInfo, getToken } from '@/utils/auth';

// components
import DetailLayout from '@/components/DetailLayout';
import UserTag from '@/components/UserTag';
import FlashTableViewer from '@/components/FlashTable/viewer.vue';
import { documentAttachmentUploadUrl } from '@/api/uploadAction';

// Api
import { getViewByBindingMenu } from '@/api/tableView';
import { documentPlanCategoryList, deleteFile } from '@/api/planManagement';
import { downloadById } from '@/api/maintenanceRecordList';
import { getQualitySystem, findReleaseData, saveOrUpdate, processSubmit, processExecute } from '@/api/systemRecord';

// Data
import { COMMAND } from '@/data/flashTable';

export default {
  name: 'DetailSystemRecord',
  components: { DetailLayout, FlashTableViewer, UserTag },
  setup() {
    const route = useRoute();
    const store = useStore().state;
    const state = reactive({
      userOptions: store.common.nameList,
      templateOption: {},
      attachmentList: [],
      accept: '.png, .jpg, .jpeg, .pdf, .xlsx, .doc, .docx',
      uploadAction: documentAttachmentUploadUrl(),
      flashViewRef: ref(),
      headerconfig: {
        Authorization: getToken()
      },
      currentAccountId: getLoginInfo().accountId,
      formRef: ref(),
      designId: '',
      submitType: '',
      templateTypeJSON: {},
      templateAll: {},
      pageViewGroup: {
        info: {}
      },
      detailLoading: false,
      isEdit: false,
      formData: {
        attachmentList: []
      }
    });
    // 获取发布后模板
    const getDesigntemplate = async () => {
      state.detailLoading = true;
      const response = await findReleaseData().finally((state.detailLoading = false));
      if (response) {
        state.templateAll = {};
        response.data.data.forEach(item => {
          state.templateAll[item.id] = item;
        });
      }
    };
    // 获取模板类型
    const initType = async () => {
      state.detailLoading = true;
      const response = await documentPlanCategoryList({ page: '1', limit: '-1' }).finally(
        (state.detailLoading = false)
      );
      if (response) {
        state.templateTypeJSON = {};
        response.data.data.forEach(item => {
          state.templateTypeJSON[item.code] = item.name;
        });
      }
    };
    const handleCancle = () => {
      router.push({
        path: '/systemManagement/systemRecord'
      });
    };
    // 设置模板值
    const setTemplateValue = () => {
      state.flashViewRef.setLocalData(state.formData.templateDataId);
    };
    // 查询详情
    const initDetail = async () => {
      if (route.query.type == 'add') {
        state.isEdit = true;
        state.option = {};
      } else {
        state.detailLoading = true;
        const response = await getQualitySystem(route.query.id).finally((state.detailLoading = false));
        if (response) {
          state.formData = response.data.data;
          state.attachmentList = state.formData.attachmentList;
          state.isEdit =
            getPermissionBtn('EditSystemRecord') &&
            state.formData.status == 1 &&
            state.formData.createBy == state.currentAccountId;
          setTemplateOption();
        }
      }
    };
    onMounted(() => {
      getDesigntemplate();
      initType();
      initDetail();
    });
    const getDetailView = async () => {
      state.detailLoading = true;
      const res = await getViewByBindingMenu('DetailSystemRecord');
      state.detailLoading = false;
      if (res) {
        state.pageViewGroup = res.data.data[0]
          ? formatViewData(res.data.data[0]?.sysEmployeeListConfigList)
          : {
              info: {}
            };
      }
    };
    getDetailView();
    const handleChangeReviewer = val => {
      if (!val) {
        state.formData.approval = '';
      }
    };
    const handleReceive = res => {
      switch (res.command) {
        case COMMAND.TEMPLATE_LOADED: // 加载完毕
          state.detailLoading = false;
          if (state.formData.templateDataId) {
            setTemplateValue();
          }
          break;
        case COMMAND.SUBMIT_CALLBACK: // 提交成功返回数据id
          state.formData.templateDataId = res.data.data;
          if (state.submitType == 'save') {
            onSave();
          } else {
            submitInfo();
          }
          break;
      }
    };
    const handleSave = type => {
      state.submitType = type;
      state.formRef.validate(async valid => {
        if (valid) {
          if (state.submitType == 'save') {
            // 保存
            state.detailLoading = true;
            state.flashViewRef.submitData();
          } else {
            // 保存并提交
            onSubmit();
          }
        } else {
          return false;
        }
      });
    };
    // 保存
    const onSave = async () => {
      state.detailLoading = true;
      const response = await saveOrUpdate(state.formData).finally((state.detailLoading = false));
      if (response) {
        ElMessage.success('保存成功！');
        router.push({
          path: '/systemManagement/systemRecord'
        });
      }
    };
    const onSubmit = async () => {
      ElMessageBox({
        title: '提示',
        message: '是否确认保存并提交？',
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(async () => {
          state.flashViewRef.submitData();
        })
        .catch(() => {});
    };
    const submitInfo = async isUpload => {
      state.detailLoading = true;
      const response = await saveOrUpdate(state.formData).finally((state.detailLoading = false));
      if (response) {
        if (!isUpload) {
          state.formData.id = response.data.data;
          state.detailLoading = true;
          const response2 = state.formData.processInstanceId
            ? await processExecute({
                isAssent: 1,
                processInstanceId: state.formData.processInstanceId,
                businessKey: state.formData.id
              }).finally((state.listLoading = false))
            : await processSubmit(state.formData.id).finally((state.detailLoading = false));
          if (response2) {
            ElMessage.success('提交成功！');
            router.push({
              path: '/systemManagement/systemRecord'
            });
          }
        }
      }
    };
    // 格式化日期
    const handleChangeDate = (val, fieldName) => {
      if (val) {
        state.formData[fieldName] = formatDate(val);
      }
    };
    // 切换模板
    const handleChangeTemplate = val => {
      if (val) {
        state.detailLoading = true;
        state.formData.designTemplateType = state.templateAll[val]?.templateType;
        state.formData.designTemplateVersion = state.templateAll[val]?.templateVersion;
        setTemplateOption();
      } else {
        state.formData.designTemplateId = '';
        state.formData.designTemplateType = '';
        state.formData.designTemplateVersion = '';
      }
    };
    // 设置模板option
    const setTemplateOption = () => {
      nextTick(() => {
        state.templateOption = {
          name: state.templateAll[state.formData.designTemplateId]?.name,
          id: state.formData.designTemplateId,
          readonly: state.isEdit ? 0 : 1
        };
      });
    };
    const beforeUpload = file => {
      var fileName = '';
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      const fileSize = file.size / 1024 / 1024 < 20;
      if (!fileSize) {
        ElMessage.error('上传附件大小不能超过20M');
        return false;
      } else if (
        fileName !== 'png' &&
        fileName !== 'jpg' &&
        fileName !== 'jpeg' &&
        fileName !== 'pdf' &&
        fileName !== 'xlsx' &&
        fileName !== 'doc' &&
        fileName !== 'docx'
      ) {
        ElMessage.error(`仅支持${state.accept}文件扩展名`);
        return false;
      } else if (file.size === 0) {
        ElMessage.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };
    /** 附件上传成功 */
    const handleFileSuccess = (res, file, uploadFiles) => {
      if (res.code == 200) {
        state.formData.attachmentList.push({
          name: file.name,
          fileName: file.name,
          fileId: res.data[0]
        });
        if (state.formData.status == 4) {
          submitInfo(true);
        }
      }
    };
    /** 预览 */
    const handlePreview = async file => {
      state.detailLoading = true;
      const response = await downloadById(file.id).finally((state.detailLoading = false));
      if (response) {
        const blob = new Blob([response.data], { type: '' });
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = `${file.name}`;
        a.href = blobUrl;
        a.click();
        ElMessage.success('下载附件成功');
      }
    };
    const handleRemove = file => {
      const fileId = file.fileId || file?.response?.data[0];
      if (fileId) {
        state.detailLoading = true;
        deleteFile(fileId).then(res => {
          state.detailLoading = false;
          if (res) {
            state.formData.attachmentList = state.formData.attachmentList.filter(item => item.fileId != fileId);
            ElMessage.success('附件删除成功！');
            submitInfo(true);
            return true;
          } else {
            return false;
          }
        });
      }
    };
    const handleChangeFile = (uploadFile, uploadFiles) => {};
    return {
      ...toRefs(state),
      onSave,
      handleRemove,
      beforeUpload,
      handlePreview,
      handleSave,
      handleCancle,
      handleReceive,
      handleFileSuccess,
      setTemplateOption,
      handleChangeFile,
      handleChangeTemplate,
      handleChangeReviewer,
      formatDate,
      handleChangeDate,
      getNameByid,
      getPermissionBtn
    };
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item__error) {
  padding: 0;
}
:deep(.el-icon-close-tip) {
  display: none !important;
}
.header-title {
  font-size: 20px;
}
.flash-table {
  height: 57vh;
}
.flash-table-check {
  height: 59vh;
}
.no-data-title {
  color: $tes-font3;
}
:deep(.editForm.el-form .el-form-item) {
  margin-bottom: 13px !important;
}
:deep(.el-form .el-form-item) {
  margin-bottom: 0 !important;
}
:deep(.el-divider--horizontal) {
  margin: 10px 0 20px 0;
}
</style>
