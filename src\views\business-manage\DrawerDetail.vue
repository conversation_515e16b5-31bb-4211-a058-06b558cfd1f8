<template>
  <!-- 部门管理员工详情 -->
  <el-drawer
    v-if="showDrawer"
    v-model="showDrawer"
    title="员工详情"
    direction="rtl"
    :before-close="handleClose"
    size="88%"
    destroy-on-close
    custom-class="equipmentDetail"
  >
    <DrawerLayout
      v-loading="detailLoading"
      :has-left-panel="false"
      :has-button-group="getPermissionBtn('editEquipment')"
    >
      <template #drawer-title>
        <span>{{ detailData.nickname || '名称' }}</span>
        <el-tag size="small" :type="tagType[detailData.status]">{{ jobType[detailData.beOnTheJob] }}</el-tag>
      </template>
      <template #button-group>
        <el-button size="small" @click="editDetail" @keyup.prevent @keydown.enter.prevent>编辑员工</el-button>
      </template>
      <el-form class="isCheck drawer-form" label-width="115px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="用户名称：" prop="deviceCategoryId">
              <UserTag :name="detailData.username || '--'" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="姓名：" prop="nickname">
              <UserTag :name="detailData.nickname || '--'" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="部门：" prop="departmentName">
              <div>{{ detailData.departmentName || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="在职状态：" prop="beOnTheJob">
              <div>{{ dictionaryAll['jobType'][detailData.beOnTheJob]?.name || detailData.beOnTheJob || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="角色分配：" prop="roleName">
              <div>{{ detailData.roleName || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检验类型：" prop="inspectionType">
              <div v-if="detailData.inspectionType">
                <span v-for="(item, index) in detailData.inspectionType.split(',')" :key="item">
                  {{ dictionaryAll['JYLX'][item]?.name
                  }}<span v-if="index !== detailData.inspectionType.split(',').length - 1">，</span>
                </span>
              </div>
              <span v-else>--</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工号：" prop="jobNumber">
              <div>{{ detailData.jobNumber || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="手机号：" prop="mobile">
              <div>{{ detailData.mobile || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="邮箱：" prop="email">
              <div>{{ detailData.email || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证号：" prop="identityCard">
              <div>{{ detailData.identityCard || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别：" prop="gender">
              <div>{{ dictionaryAll['sex'][detailData.gender]?.name || detailData.gender || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出生日期：" prop="birthday">
              <div>{{ detailData.birthday || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入职日期：" prop="entryDate">
              <div>{{ detailData.entryDate || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最高学历：" prop="degree">
              <div>
                {{ dictionaryAll['highestDegree'][detailData.degree]?.name || detailData.degree || '--' }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="英文名称：" prop="englishname">
              <div>{{ detailData.englishname || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="姓名拼音：" prop="transcription">
              <div>{{ detailData.transcription || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="公司邮箱：" prop="companyEmail">
              <span>{{ detailData.companyEmail || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="公司电话：" prop="companyMobile">
              <div>{{ detailData.companyMobile || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工龄（年）：" prop="seniority">
              <div>{{ detailData.seniority || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="毕业院校：" prop="graduatedSchool">
              <div>{{ detailData.graduatedSchool || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="专业：" prop="major">
              <div>{{ detailData.major || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="职称：" prop="technicalTitle">
              <div>{{ detailData.technicalTitle || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="家庭地址：" prop="homeAddress">
              <div>{{ detailData.homeAddress || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="紧急联系人：" prop="emergencyContact">
              <div>{{ detailData.emergencyContact || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="紧急联系人电话：" prop="emergencyContactPhone">
              <div>{{ detailData.emergencyContactPhone || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工作简历：" prop="workResume">
              <div v-if="detailData.workResume?.length">
                <span v-for="item in detailData.workResume" :key="item.id" class="link" @click="handleDownLoad(item)">
                  {{ item.fileName }}；
                </span>
              </div>
              <span v-else>--</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="人员上岗证书：" prop="jobCertificate">
              <div v-if="detailData.jobCertificate?.length">
                <div
                  v-for="item in detailData.jobCertificate"
                  :key="item.id"
                  class="link"
                  @click="handleDownLoad(item)"
                >
                  {{ item.fileName }}；
                </div>
              </div>
              <span v-else>--</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs v-model="activeName" class="marginTop">
        <el-tab-pane label="可检项目" name="1">
          <DetailEquipmentItems
            :id="detailData.id"
            :user-id="detailData.userId"
            type="depart"
            :list="tableDataItem"
            @handleResh="handleReshItem"
          />
        </el-tab-pane>
        <el-tab-pane label="人员培训" name="2">
          <DetailPersonTrain :person-id="detailData.userId" />
        </el-tab-pane>
      </el-tabs>
    </DrawerLayout>
    <DrawerDepart
      :drawer="unitVisiable"
      type="edit"
      :tree-data="treeData"
      :dictionary="dictionaryAll"
      :detail-data="detailData"
      @close="closeDrawer"
    />
  </el-drawer>
</template>
<script>
import { ref, watch, reactive, getCurrentInstance, toRefs } from 'vue';
import { getLoginInfo } from '@/utils/auth';
import DrawerLayout from '@/components/DrawerLayout';
import { getUserItemApi, getMemberInfo, downloadById } from '@/api/departManagement';
import { getPermissionBtn } from '@/utils/common';
import { getNameByid } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { formatDate } from '@/utils/formatTime';
import UserTag from '@/components/UserTag';
import DrawerDepart from './DrawerDepart';
import { colWidth } from '@/data/tableStyle';
import DetailEquipmentItems from '@/components/ItemComponents/DetailEquipmentItems';
import DetailPersonTrain from './depart-manage/components/DetailPersonTrain.vue';
export default {
  name: 'DrawerDepartDetail',
  components: { DrawerLayout, UserTag, DrawerDepart, DetailEquipmentItems, DetailPersonTrain },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    dictionary: {
      type: Object,
      default: function () {
        return {};
      }
    },
    userId: {
      type: String,
      default: ''
    },
    treeData: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['close', 'refresh'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      activeName: '1',
      drawerDepartType: 'edit', // 新增编辑员工弹出框类型
      detailData: {
        workResume: [],
        jobCertificate: []
      },
      dictionaryAll: {
        sex: {},
        highestDegree: {},
        jobType: {},
        JYLX: {}
      },
      tableDataItem: [], // 项目列表
      treeData: [], // 分类树
      unitVisiable: false,
      tableLoading: false,
      jobType: {
        1: '在职',
        0: '离职',
        2: '外部用户'
      },
      tagType: {
        1: 'success',
        0: 'info',
        2: 'warning'
      },
      pageViewGroup: {
        personInfo: {},
        personTrain: {}
      },
      detailLoading: false, // 详情页的loading
      isRefresh: true,
      accountId: getLoginInfo().accountId, // 当前登录人的id
      listLoading: false,
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      }
    });
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    const showDrawer = ref(props.drawer);
    const handleClose = () => {
      if (state.isModified) {
        proxy
          .$confirm('确认离开当前页面吗？离开后数据不可恢复', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false
          })
          .then(() => {
            showDrawer.value = false;
            state.isModified = false;
            state.detailData = {
              workResume: [],
              jobCertificate: []
            };
            context.emit('close', false);
          })
          .catch(() => {});
      } else {
        showDrawer.value = false;
        state.detailData = {
          workResume: [],
          jobCertificate: []
        };
        context.emit('close', false);
      }
    };
    watch(props, newValue => {
      showDrawer.value = newValue.drawer;
      if (showDrawer.value) {
        state.treeData = props.treeData;
        state.dictionaryAll = props.dictionary || {
          sex: {},
          highestDegree: {},
          jobType: {},
          JYLX: {}
        };
        state.activeName = '1';
        initDetail();
        getUserItem();
      }
    });
    // 获取关联项目
    const getUserItem = () => {
      getUserItemApi(props.userId).then(res => {
        if (res) {
          state.tableDataItem = res.data.data;
        }
      });
    };
    // 查询详情
    const initDetail = () => {
      getMemberInfo(props.userId).then(res => {
        if (res) {
          state.detailData = res.data.data;
        }
      });
    };
    const closeDrawer = value => {
      if (value.isRefresh) {
        initDetail();
        context.emit('refresh', true);
      }
      state.unitVisiable = false;
    };
    const editDetail = () => {
      state.unitVisiable = true;
    };
    const handleReshItem = () => {
      getUserItem();
    };
    // 下载
    const handleDownLoad = async file => {
      state.detailLoading = true;
      const response = await downloadById(file.id).finally((state.detailLoading = false));
      if (response) {
        const blob = new Blob([response.data], { type: '' });
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = `${file.name}`;
        a.href = blobUrl;
        a.click();
        proxy.$message.success('下载附件成功');
      }
    };
    return {
      ...toRefs(state),
      initDetail,
      handleDownLoad,
      getUserItem,
      handleReshItem,
      drageHeader,
      colWidth,
      closeDrawer,
      getPermissionBtn,
      editDetail,
      filterNode,
      getNameByid,
      formatDate,
      handleClose,
      showDrawer
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/tree.scss';
.link {
  color: $tes-primary;
  cursor: pointer;
}
.equipmentDetail {
  .ssfl {
    width: 300px;
    :deep(.el-input__suffix) {
      display: none;
    }
  }
  :slotted(.drawer-wrapper .drawer-title) {
    margin-bottom: 0 !important;
  }
}

.marginTop {
  margin-top: 35px;
}
:deep(.el-input.is-disabled .el-input__inner) {
  background: $background-color;
  border: 0;
  color: #606266;
  padding: 0;
  cursor: text;
}
:deep(.el-result) {
  padding: 0;
  flex-direction: row;
  justify-content: flex-start;
}
:deep(.el-result__icon svg) {
  width: 14px;
  height: 14px;
  position: relative;
  top: 2px;
}
:deep(.el-result__extra) {
  margin-top: 0;
  color: $green;
  .error {
    color: $red;
  }
}
.overflowHidden {
  :deep(.el-form-item__label) {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.isCheck {
  margin-top: 12px;
  padding: 10px 20px;
  background: #f5f7fa;
  border-radius: 3px;
}
</style>
<style lang="scss">
.equipmentDetail {
  .el-drawer__close-btn {
    padding-right: 10px;
  }
  .ssfl {
    width: 50%;
    .el-input__suffix {
      display: none;
    }
  }
}
.equipmentDetail .el-drawer__header {
  padding: 20px 40px 0px !important;
}
.equipmentDetail .el-drawer__body {
  padding: 20px 40px !important;
  // background: #f0f2f5;
}
.equipmentDetail {
  .drawer-content {
    height: auto !important;
  }
}
</style>
