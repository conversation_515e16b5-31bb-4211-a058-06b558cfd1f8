// 获取近几天日期
export function getDay(day) {
  var today = new Date();
  var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
  today.setTime(targetday_milliseconds);
  var tYear = today.getFullYear();
  var tMonth = today.getMonth();
  var tDate = today.getDate();
  tMonth = doHandleMonth(tMonth + 1);
  tDate = doHandleMonth(tDate);
  return tYear + '-' + tMonth + '-' + tDate;
}

export function doHandleMonth(month) {
  var m = month;
  if (month.toString().length === 1) {
    m = '0' + month;
  }
  return m;
}
// 获取本月日期
export function getMonthDay() {
  var firstDate = new Date();
  var startDate =
    firstDate.getFullYear() +
    '-' +
    (firstDate.getMonth() + 1 < 10 ? '0' : '') +
    (firstDate.getMonth() + 1) +
    '-' +
    '01';
  var date = new Date();
  var currentMonth = date.getMonth();
  var nextMonth = ++currentMonth;
  var nextMonthFirstDay = new Date(date.getFullYear(), nextMonth, 1);
  var oneDay = 1000 * 60 * 60 * 24;
  var lastDate = new Date(nextMonthFirstDay - oneDay);
  var endDate =
    lastDate.getFullYear() +
    '-' +
    (lastDate.getMonth() + 1 < 10 ? '0' : '') +
    (lastDate.getMonth() + 1) +
    '-' +
    (lastDate.getDate() < 10 ? '0' : '') +
    lastDate.getDate();
  return [startDate, endDate];
}
// 获取本周日期
export function getCurrentWeek() {
  var dateArr = [];
  var now = new Date();
  var nowDayOfWeek = now.getDay();
  if (nowDayOfWeek === 0) {
    nowDayOfWeek = 7;
  }
  var start = getDay(1 - nowDayOfWeek);
  dateArr.push(start);
  var end = getDay(7 - nowDayOfWeek);
  dateArr.push(end);
  return dateArr;
}
