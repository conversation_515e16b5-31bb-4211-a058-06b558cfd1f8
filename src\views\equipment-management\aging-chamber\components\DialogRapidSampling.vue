<template>
  <el-dialog v-model="dialogShow" title="快速取样" :close-on-click-modal="false" :width="1250" @close="handleClose()">
    <div v-loading="dialogLoading">
      <el-form label-position="left" :inline="true" size="small">
        <el-row>
          <el-col :span="8" :offset="16" class="text-right">
            <el-button
              type="primary"
              plain
              :loading="dialogLoading"
              :disabled="selectRow.length == 0"
              size="small"
              @click="handleExtraction()"
              @keyup.prevent
              @keydown.enter.prevent
              >批量取出</el-button
            >
          </el-col>
        </el-row>
      </el-form>
      <el-table
        ref="tableRef"
        :data="tableList"
        class="dark-table base-table format-height-table2"
        fit
        border
        highlight-current-row
        size="medium"
        height="auto"
        style="margin-top: 5px"
        :row-style="
          () => {
            return 'cursor: pointer';
          }
        "
        @header-dragend="drageHeader"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" :width="colWidth.checkbox" align="center" fixed="left" />
        <el-table-column
          label="样品信息"
          prop="sampleName"
          :min-width="colWidth.name"
          align="left"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ row.sampleName || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="试样数量" prop="sampleCount" :width="100">
          <template #default="{ row }">
            <span>{{ row.sampleCount || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="试样温度℃" prop="sampleTemperature" :width="colWidth.status" align="center">
          <template #default="{ row }">
            <span>{{ row.sampleTemperature }}</span>
          </template>
        </el-table-column>
        <el-table-column label="放置时间" prop="startDateTime" :min-width="220" show-overflow-tooltip>
          <template #default="{ row }">
            <div>{{ row.startDateTime }} ~ {{ row.endDateTime }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="放置周期"
          prop="periodHour"
          align="left"
          :min-width="colWidth.date"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span v-if="row.periodHour">{{ row.periodHour }}小时</span>
            <span v-if="row.periodMinute">{{ row.periodMinute }}分钟</span>
          </template>
        </el-table-column>
        <el-table-column label="放样人" prop="createBy" :width="colWidth.people">
          <template #default="{ row }">
            <UserTag :name="getNameByid(row.createBy) || row.createBy || '--'" />
          </template>
        </el-table-column>
        <el-table-column label="已放置时间" prop="startDateTime" :width="colWidth.dateranger">
          <template #default="{ row }">
            <span>{{ formateTimeDiff(row.startDateTime) || '--' }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>
<script>
// getCurrentInstance
import { reactive, ref, toRefs, watch } from 'vue';
import { useStore } from 'vuex';
import { getNameByid } from '@/utils/common';
import UserTag from '@/components/UserTag';
import { ElMessage, ElMessageBox } from 'element-plus';
import { sampleList, sampleOut } from '@/api/aging-chamber';
import { formatDate, differenceTimes } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
export default {
  name: 'DialogRapidSampling',
  components: { UserTag },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    selectInfo: {
      type: Object,
      default: () => {}
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    const store = useStore().state;
    const state = reactive({
      dialogLoading: false, // 弹出窗loading
      selectRow: [],
      tableList: [],
      dialogShow: false,
      ruleForm: ref(),
      deviceInfo: {}, // 已经选择的设备
      nameList: store.common.nameList,
      tableRef: ref(null)
    });
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisible;
      if (state.dialogShow) {
        state.selectRow = [];
        state.deviceInfo = props.selectInfo;
        getTableList();
      }
    });
    const getTableList = async () => {
      state.dialogLoading = true;
      const { data } = await sampleList({ boxId: state.deviceInfo.id, sampleStatus: 0 }).finally(
        (state.dialogLoading = false)
      );
      if (data) {
        state.tableList = data.data;
      }
    };
    /** 批量取出 */
    const handleExtraction = () => {
      const notReachedList = state.selectRow
        .filter(item => {
          return new Date(item.endDateTime).getTime() > new Date().getTime();
        })
        .map(item => item.sampleName);
      if (notReachedList.length) {
        ElMessageBox({
          title: '是否取出当前样品',
          message: '当前样品未到达放置结束时间，是否确认提前取出<br/>' + notReachedList.join('，'),
          confirmButtonText: '确定',
          dangerouslyUseHTMLString: true,
          showCancelButton: false,
          closeOnClickModal: false,
          type: 'warning'
        })
          .then(() => {
            onExtraction();
          })
          .catch(() => {});
      } else {
        onExtraction();
      }
    };
    /** 批量取出放样 */
    const onExtraction = async () => {
      ElMessageBox({
        title: '批量取出',
        message: '是否确认取出？',
        confirmButtonText: '确定',
        showCancelButton: false,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(async () => {
          state.dialogLoading = true;
          const params = state.selectRow.map(item => item.id);
          const { data } = await sampleOut(params).finally((state.dialogLoading = false));
          if (data) {
            ElMessage.success('操作成功！');
            handleClose(true);
          }
        })
        .catch(() => {});
    };
    const formateTimeDiff = startDate => {
      const { hours, minutes } = differenceTimes(startDate, new Date());
      return `${hours}小时${minutes}分钟`;
    };
    // 关闭弹出窗
    const handleClose = isRefresh => {
      context.emit('closeDialog', isRefresh);
    };
    const handleSelectionChange = val => {
      state.selectRow = val;
    };
    // 判断是否可以勾选
    const rowSelectable = (row, index) => {
      return true;
    };
    const handleRowClick = row => {
      state.tableRef.toggleRowSelection(
        row,
        !state.selectRow.some(item => {
          return row.id === item.id;
        })
      );
    };
    return {
      ...toRefs(state),
      rowSelectable,
      getNameByid,
      handleClose,
      handleExtraction,
      handleSelectionChange,
      getTableList,
      formatDate,
      colWidth,
      drageHeader,
      formateTimeDiff,
      handleRowClick
    };
  }
};
</script>
<style lang="scss" scoped>
::v-deep(.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 37.5rem) !important;
    overflow-y: auto;
  }
}
</style>
