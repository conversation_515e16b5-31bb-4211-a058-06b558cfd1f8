<template>
  <el-dialog
    v-model="showDialog"
    custom-class="code-editor"
    title="编辑"
    width="70%"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <textarea ref="myCodeRef" v-model="code" class="textarea" />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="dialogSuccess">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, nextTick } from 'vue';
// import { ElMessage } from 'element-plus'
// import { useRoute } from 'vue-router'
import * as CodeMirror from 'codemirror/lib/codemirror.js';
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/material-darker.css';
import 'codemirror/mode/javascript/javascript.js';
import 'codemirror/mode/clike/clike.js';
import 'codemirror/mode/vue/vue.js';
import 'codemirror/addon/scroll/simplescrollbars.css';
import 'codemirror/addon/scroll/simplescrollbars.js';
import 'codemirror/addon/hint/show-hint.js';
import 'codemirror/addon/hint/show-hint.css';
import 'codemirror/addon/hint/javascript-hint.js';
import 'codemirror/addon/hint/html-hint.js';
import 'codemirror/addon/hint/anyword-hint.js';
import 'codemirror/addon/hint/css-hint.js';
import 'codemirror/addon/hint/show-hint.css';
import 'codemirror/addon/fold/markdown-fold.js';
import 'codemirror/addon/fold/foldgutter.css';
import 'codemirror/addon/fold/foldcode.js';
import 'codemirror/addon/fold/brace-fold.js';
import 'codemirror/addon/selection/active-line.js';
import 'codemirror/addon/lint/lint.js';
import 'codemirror/addon/lint/lint.css';
import 'codemirror/addon/lint/javascript-lint.js';

export default {
  name: 'CodeEditor',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    data: {
      type: String,
      default: ''
    }
  },
  emits: ['close', 'setData'],
  setup(props, context) {
    // console.log(props)
    // const route = useRoute()
    const datas = reactive({
      showDialog: false,
      code: '',
      editorText: null,
      myCodeRef: ref()
    });

    watch(
      () => props.show,
      newValue => {
        console.log(newValue);
        datas.showDialog = newValue;
        if (newValue) {
          datas.code = props.data;
          nextTick(() => {
            init();
          });
        }
      },
      { deep: true }
    );

    // 加载配置项
    const init = () => {
      console.log(datas.myCodeRef);
      datas.editorText = CodeMirror.fromTextArea(datas.myCodeRef, {
        value: datas.code,
        mode: { name: 'javascript', globalVars: true },
        indentWithTabs: true,
        smartIndent: true,
        lineNumbers: true,
        matchBrackets: true,
        autoRefresh: true,
        theme: 'material-darker',
        lineWrapping: true,
        extraKeys: { Ctrl: 'autocomplete' }, // 自定义快捷键
        Autofocus: true,
        styleActiveLine: true,
        hintOptions: {
          // 自定义提示选项
          tables: {
            users: ['name', 'score', 'birthDate'],
            countries: ['name', 'population', 'size'],
            score: ['zooao']
          }
        }
      });
      // 可选,挂载一下监听事项
      // datas.editorText.on('change', (cm) => {
      //   datas.code = cm.getValue()
      //   console.log(datas.code)
      // })
      datas.editorText.on('keypress', function () {
        datas.editorText.showHint();
      });
      // datas.editorText.setSize('600', '600')
      console.log(datas.editorText);
    };
    // 数据刷新
    const refresh = () => {
      nextTick(() => {
        datas.editorText.refresh();
      });
    };
    // 更新数据
    const setValue = value => {
      datas.editorText.setValue(value);
      refresh();
    };
    // 确定
    const dialogSuccess = () => {
      var topScript = '<script>' + datas.code + '<\/script>';
      // const ele = document.createElement('script')
      //   ele.type = 'text/javascript'
      //   ele.text = script.innerHTML
      //   scriptRef.value.append(ele)
      context.emit('setData', topScript);
    };
    // 取消
    const handleClose = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };

    return {
      ...toRefs(datas),
      handleClose,
      init,
      refresh,
      setValue,
      dialogSuccess
    };
  },
  methods: {}
};
</script>
<style lang="scss">
.code-editor {
  padding: 0px 10px;
  .el-dialog__body {
    .CodeMirror-lines {
      line-height: 15px;
    }
  }
}
</style>
