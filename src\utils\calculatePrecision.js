function publicIsInteger(obj) {
  return Math.floor(obj) === obj;
}
function publicToInteger(floatNum) {
  var ret = { times: 1, num: 0 };
  if (publicIsInteger(floatNum)) {
    ret.num = floatNum;
    return ret;
  }
  var strfi = floatNum + '';
  var dotPos = strfi.indexOf('.');
  var len = strfi.substr(dotPos + 1).length;
  var times = Math.pow(10, len);
  var intNum = parseInt(floatNum * times + 0.5, 10);
  ret.times = times;
  ret.num = intNum;
  return ret;
}
function publicOperation(a, b, op, n) {
  var o1 = publicToInteger(a);
  var o2 = publicToInteger(b);
  var n1 = o1.num;
  var n2 = o2.num;
  var t1 = o1.times;
  var t2 = o2.times;
  var max = t1 > t2 ? t1 : t2;
  var result = null;
  switch (op) {
    case 'add':
      if (t1 === t2) {
        // 两个小数位数相同
        result = n1 + n2;
      } else if (t1 > t2) {
        // o1 小数位 大于 o2
        result = n1 + n2 * (t1 / t2);
      } else {
        // o1 小数位 小于 o2
        result = n1 * (t2 / t1) + n2;
      }
      return result / max;
    case 'subtract':
      if (t1 === t2) {
        result = n1 - n2;
      } else if (t1 > t2) {
        result = n1 - n2 * (t1 / t2);
      } else {
        result = n1 * (t2 / t1) - n2;
      }
      return result / max;
    case 'multiply':
      result = (n1 * n2) / (t1 * t2);
      if (n) {
        return toFixed(result, n);
      } else {
        return result;
      }
    case 'divide':
      result = (n1 / n2) * (t2 / t1);
      // 如果除数为0，返回/,防止出错
      if (b === 0) {
        return '/';
      } else {
        if (n) {
          return toFixed(result, n);
        } else {
          return result;
        }
      }
  }
}
function publicCheckNum(value) {
  if (!isNaN(Number(value))) {
    const charArray = value.toString().split('');
    if (charArray[charArray.length - 1] === '0') {
      if (charArray[0] === '.') {
        const oldCharLength = charArray.length;
        let numString = Number(value).toString();
        const zeroCount = oldCharLength - numString.length + 1;
        for (let i = 0; i < zeroCount; i++) {
          numString = `${numString}0`;
        }
        return numString;
      } else {
        return value;
      }
    } else {
      return Number(value);
    }
  }
}

// 加减乘除的四个方法
export function publicAdd(a, b) {
  return publicOperation(publicCheckNum(a), publicCheckNum(b), 'add');
}

export function publicSubtract(a, b) {
  return publicOperation(publicCheckNum(a), publicCheckNum(b), 'subtract');
}

export function publicMultiply(a, b, n) {
  return publicOperation(publicCheckNum(a), publicCheckNum(b), 'multiply', n);
}
export function publicDivide(a, b, n) {
  return publicOperation(publicCheckNum(a), publicCheckNum(b), 'divide', n);
}
// 保留几位小数 n 四舍五入的小数，d 需要保留的位数
function toFixed(n, d) {
  var s = n + '';
  if (!d) d = 0;
  if (s.indexOf('.') === -1) s += '.';
  s += new Array(d + 1).join('0');
  if (new RegExp('^(-|\\+)?(\\d+(\\.\\d{0,' + (d + 1) + '})?)\\d*$').test(s)) {
    // eslint-disable-next-line
    var s = '0' + RegExp.$2;
    var pm = RegExp.$1;
    var a = RegExp.$3.length;
    var b = true;
    if (a === d + 2) {
      a = s.match(/\d/g);
      if (parseInt(a[a.length - 1]) > 4) {
        for (var i = a.length - 2; i >= 0; i--) {
          a[i] = parseInt(a[i]) + 1;
          if (a[i] === 10) {
            a[i] = 0;
            b = i !== 1;
          } else break;
        }
      }
      s = a.join('').replace(new RegExp('(\\d+)(\\d{' + d + '})\\d$'), '$1.$2');
    }
    if (b) s = s.substring(1);
    return (pm + s).replace(/\.$/, '');
  }
  return this + '';
}
/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}
