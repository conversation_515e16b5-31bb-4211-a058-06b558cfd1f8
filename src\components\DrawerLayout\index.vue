<template>
  <div class="drawer-wrapper">
    <!-- 页头 固定区域 -->
    <div v-if="hasPageHeader">
      <!-- 适用通用列表页 -->
      <div class="drawer-header">
        <div class="drawer-title">
          <slot name="drawer-title" />
        </div>
        <!-- 右侧按钮组 -->
        <div v-if="hasButtonGroup">
          <slot name="button-group" />
        </div>
      </div>
    </div>
    <!-- 主体 页面滚动区域 -->
    <div
      class="drawer-content"
      :style="{ height: hasPageHeader ? 'calc(100% - ' + `${mainOffsetTop}px` + ')' : '100%' }"
    >
      <el-container v-if="hasLeftPanel" class="drawer-main-panel">
        <!-- 列表页双列布局 -->
        <el-aside v-show="showAside" :style="{ width: asideWidth + 'px' }">
          <!-- 列表页左侧区域 -->
          <div class="tree-container">
            <slot name="page-left-side" />
          </div>
        </el-aside>
        <el-tooltip
          v-if="hasLeftPanel && hasDragHandle"
          effect="dark"
          :content="collapseTip"
          :hide-after="0"
          placement="right"
        >
          <drag-handle @widthChange="widthChange" @mouseup="collapseLeft" @mousedown="setOldAsideWidth" />
        </el-tooltip>
        <el-container :class="{ 'margin-left-20': hasLeftPanel && !hasDragHandle }">
          <!-- 列表页主体区域 -->
          <el-main>
            <div class="drawer-main">
              <slot />
            </div>
          </el-main>
        </el-container>
      </el-container>
      <div v-else class="drawer-main">
        <slot />
      </div>
    </div>
    <slot name="other" />
  </div>
</template>
<script>
import DragHandle from '@/components/DragHandle/handle.vue';
import { reactive, toRefs } from 'vue';
export default {
  name: 'DrawerLayout',
  components: { DragHandle },
  props: {
    hasPageHeader: {
      type: Boolean,
      default: true
    },
    hasButtonGroup: {
      type: Boolean,
      default: true
    },
    hasLeftPanel: {
      type: Boolean,
      default: false
    },
    hasDragHandle: {
      type: Boolean,
      default: false
    },
    mainOffsetTop: {
      type: Number,
      default: 51
    },
    asidePanelWidth: {
      type: Number,
      default: 300
    },
    asideMaxWidth: {
      type: Number,
      default: 600
    },
    asideMinWidth: {
      type: Number,
      default: 200
    }
  },
  setup(props, context) {
    const data = reactive({
      showAside: true,
      asideWidth: props.asidePanelWidth,
      asideMaxWidth: props.asideMaxWidth,
      asideMinWidth: props.asideMinWidth,
      oldAsideWidth: JSON.parse(JSON.stringify(props.asidePanelWidth)),
      collapseTip: '点击折叠左面板'
    });
    // 拖拽边框
    const widthChange = m => {
      data.asideWidth = data.asideWidth - m;
      if (data.asideWidth < data.asideMinWidth) {
        data.asideWidth = data.asideMinWidth;
      } else {
        if (data.asideWidth >= data.asideMaxWidth) {
          data.asideWidth = data.asideMaxWidth;
        }
      }
    };
    const setOldAsideWidth = () => {
      data.oldAsideWidth = JSON.parse(JSON.stringify(data.asideWidth));
    };
    const collapseLeft = () => {
      if (data.asideWidth === data.oldAsideWidth) {
        data.showAside = !data.showAside;
        data.collapseTip = data.showAside ? '点击折叠左面板' : '点击展开左面板';
      }
    };

    return {
      ...toRefs(data),
      widthChange,
      collapseLeft,
      setOldAsideWidth
    };
  }
};
</script>

<style lang="scss" scoped>
.drawer-main {
  height: 100%;
  display: flex;
  flex-direction: column;
  :deep(.form-height-auto) {
    flex: 1 1 0%;
    overflow-y: auto;
    overflow-x: hidden;
  }
}
.drawer-wrapper {
  height: 100%;
  overflow: hidden auto;
  .drawer-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    :deep(.el-tag) {
      margin-left: 10px;
    }
  }
  .drawer-title {
    display: flex;
    align-items: center;
    gap: 10px;
    line-height: 26px;
    font-size: 18px;
    font-weight: 700;
    // margin-bottom: 15px;
  }
  .tree-container {
    height: 100%;
    padding: 20px;
    background: $background-color;
    padding-right: 15px;
  }
  .drawer-content {
    .el-container {
      .el-main {
        margin: 0 24px;
      }
      .el-aside {
        margin: 0 !important;
      }

      .el-container {
        .el-main {
          margin: 0;
        }
      }
    }

    // 双列布局
    .main-panel {
      padding: 0 24px;

      .left-panel {
        background: $background-color;
        padding: 20px 20px 10px;
        overflow: hidden;
      }

      .page-main {
        margin: 0 0 0 20px;
      }
    }
  }

  .margin-left-20 {
    margin-left: 20px;
  }

  .el-container {
    height: 100%;
    .el-aside,
    .el-main {
      padding: 0;
      border-radius: 0;
    }

    .el-main {
      .page-main {
        margin: 0;
      }
    }
  }
}
:deep(.el-form-item__error) {
  padding-top: 0;
}
:deep(.el-input--medium .el-input__inner) {
  height: 32px;
  line-height: 32px;
}
:deep(.el-select--medium) {
  width: 100%;
  line-height: 32px;
}
:deep(.el-input-number--medium) {
  line-height: 32px;
}
:deep(.el-form-item--medium .el-form-item__content) {
  line-height: 32px;
}
:deep(.el-form-item--medium .el-form-item__label) {
  line-height: 32px;
}
:deep(.el-range-editor--medium) {
  line-height: 32px;
}
:deep(.isCheck .el-form-item) {
  margin-bottom: 0;
}
:deep(.el-form .el-form-item .el-form-item__label) {
  color: #909399;
}
:deep(.el-input--medium) {
  line-height: 32px;
}
:deep(.el-input--medium .el-input__icon) {
  line-height: 32px;
}
:deep(.el-tabs__header) {
  margin-bottom: 20px;
}
:deep(.el-tabs__nav-wrap::after) {
  background-color: #e4e7ed;
}
:deep(.el-tabs__item) {
  font-size: 16px;
}
</style>
