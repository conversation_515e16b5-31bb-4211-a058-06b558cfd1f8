import request from '@/utils/request';

// 列表
export function certificatewarehousereceiptList(data) {
  return request({
    url: '/api-orders/orders/certificatewarehousereceipt/list',
    method: 'post',
    data
  });
}

// 下载
export function downloadFile(certificateWarehouseReceiptId) {
  return request({
    url: `/api-orders/orders/certificatewarehousereceipt/getExportDataUrl/${certificateWarehouseReceiptId}`,
    method: 'post'
  });
}
